Type.registerNamespace('sale.eshoporder.eshopplatformcheck');

sale.eshoporder.eshopplatformcheck.OrderGatherInfoAction = function () {
    sale.eshoporder.eshopplatformcheck.OrderGatherInfoAction.initializeBase(this);
};

sale.eshoporder.eshopplatformcheck.OrderGatherInfoAction.prototype = {
    _commonAction: null,
    context: function (cb) {
          cb({});
    },

    initialize: function () {
        sale.eshoporder.eshopplatformcheck.OrderGatherInfoAction.callBaseMethod(this, 'initialize');
        if (this._commonAction == null) {
            this._commonAction = new sale.jarvis.DeliverBill.common.CommonAction();
        }
        var form = this.get_form();
        var params = form.get_pageParams();
        // 新的客户网店没有默认atype，这里不处理默认值
        // form.edAType.set_value(params.atypeId);
        // form.edAType.set_text(params.atypeName);
        var myDate = new Date();
        myDate.setTime(myDate.getTime());
        form.billDate.set_value(myDate);
        form.hfOrderInfo.set_value(params.orderInfo);
        form.hfOtypeId.set_value(params.otypeId);
        form.hfBtypeId.set_value(params.btypeId);
        /*    var list = [
                {title: '选择收款订单数', value: params.orderCount, cname: 'bg1'},
                {title: '订单应收金额合计', value: params.gatherTotal.toFixed(2), cname: 'bg2'},
            ];
            form.lvGatherBoard.dataBind(list);*/
        var AtypeId= this._commonAction.getLocalStorageDataByType(this,$user.profileid+'gatherinfo_edAtypeId', 0);
        var AtypeName= this._commonAction.getLocalStorageDataByType(this,$user.profileid+'gatherinfo_edAtypeName', "");
        var form=this.get_form();
        form.edAType.set_value(AtypeId);
        form.edAType.set_text(AtypeName);
        this.doCheckChange();

    },
    doItemRender: function(sender, args) {
        var data = args.get_data();
        var item = args.get_item();
        var index = args.get_index();
        if (index == 2) {
            item.value.addCssClass('cha');
        } else if (index == 3) {
            item.value.set_text(item.value.get_text() + '%');
        }
    },
    doAtypeChange:function(){
        var form=this.get_form();
        var atypeId=form.edAType.get_value();
        var atypeName=form.edAType.get_text();
        this._commonAction.setLocalStorageData(this, $user.profileid+'gatherinfo_edAtypeId', atypeId);
        this._commonAction.setLocalStorageData(this, $user.profileid+'gatherinfo_edAtypeName', atypeName);
    },

    doAtypeSelectorInit: function (sender) {
        var mode = "00001";
        var url = "/jxc/baseinfo/selector/AtypeSelector.gspx";
        // if ("0" == selectItem.processtype) {
        //     mode = "0000400003";
        // }
        var parameter = {
            showadd: false,
            parTypeId: mode
        };
        var filterStr = sender.get_text();
        if (filterStr && sender.get_textChanged()) {
            parameter.text = filterStr;
        }
        sender.set_selectorPageParams(parameter);
        sender.set_selectorPage(url);
    },
    doEnterPress: function (sender, eventArgs) {
        if (sender.get_value() && !sender.get_textChanged()) {
            eventArgs.set_cancel(true);
        }
    },
    doAtypeInfoSelect: function (sender, eventArgs) {
        var selector = eventArgs.get_form();
        var result = selector.selectedData;
        if (result == null) {
            Sys.UI.MessageBox.alert("没有选择任何科目信息");
            return;
        }
        var form = sender.get_form();
        form.edAType.set_value(result.id);
        form.edAType.set_text(result.fullname);
     },
    doCheckChange:function()
    {
        var form = this.get_form();
        var orderList=form.hfOrderInfo.get_value();
        var checkWay = form.dropAmount.get_value();
        var gatherTotal = 0, orderCount = 0;
        for (var i=0;i<orderList.length;i++)
        {
            var itemOrder=orderList[i];
            if (checkWay == 0)
            {
                itemOrder.gatherTotal = Number((itemOrder.disedTaxedTotal + itemOrder.orderBuyerFreightFee + itemOrder.ptypeServiceFee
                    - itemOrder.gatheredTotal).toFixed(2));
            }
            if (checkWay == 1)
            {
                itemOrder.gatherTotal = itemOrder.nationalSubsidyTotal;
            }
            if (checkWay == 2)
            {
                itemOrder.gatherTotal = Number((itemOrder.disedTaxedTotal + itemOrder.orderBuyerFreightFee + itemOrder.ptypeServiceFee
                    - itemOrder.gatheredTotal -itemOrder.nationalSubsidyTotal).toFixed(2));
            }
            if (itemOrder.gatherTotal <= 0)
            {
                continue;
            }
            gatherTotal +=itemOrder.gatherTotal;
            orderCount++;
        }
        if (orderCount < orderList.length)
        {
            $common.showInfo("部分无收款金额的订单被过滤");
        }
        var list = [
            {title: '选择收款订单数', value: orderCount, cname: 'bg1'},
            {title: '订单应收余额合计', value: gatherTotal.toFixed(2), cname: 'bg2'},
        ];
        form.lvGatherBoard.dataBind(list);
    },

    doSave: function (sender) {
        var form = sender.get_form();
        form.saveData();
         var orderList=form.hfOrderInfo.get_value();
        var btypeId=form.hfBtypeId.get_value();
        var otypeId=form.hfOtypeId.get_value();
        var atypeId=form.edAType.get_value();
        var billDate=form.billDate.get_value();
        var gatherList = new  Array();
         for (var i=0;i<orderList.length;i++)
        {
            var itemOrder=orderList[i];
            if (itemOrder.gatherTotal <= 0)
            {
                continue;
            }
            itemOrder.btypeId = btypeId;
            itemOrder.atypeId = atypeId;
            itemOrder.otypeId = otypeId;
            itemOrder.billDate = billDate;
            gatherList.push(itemOrder);
        }
         if (gatherList ==null|| gatherList.length ==0)
         {
             $common.showError("没有需要收款的订单");
             return;
         }
         var startObj =null;
        if(window.$startTiming){
            startObj = $startTiming('新对账结算->订单对账->原始订单收款');
         }
        $common.ajax({
            url: "/sale/eshoporder/platformcheck/doSaleOrderGather",
            data: {orderList: gatherList,},
            async: true,
            waiting:'正在处理,请稍等',
            router: 'ngp',
            //waiting:500,
            success: function (res) {
                if (res.code != "200") {
                    $common.alertError("收款失败:"+!res.message ? "未知异常" : res.message);
                } else {
                    if(res.data)
                    {
                        $common.alertError("收款失败:"+ res.data);
                        return;
                    }
                    $common.showOk("收款成功");
                    form.doOk();
                    return;
                }
            },
            error: function (res) {
                $common.alertError("收款失败:"+!res.message ? "未知异常" : res.message);
            },
        });
    },

    doClose: function (sender) {
        this.get_form().close(true);
    },

    dispose: function () {
        sale.eshoporder.eshopplatformcheck.OrderGatherInfoAction.callBaseMethod(this, 'dispose');
    }
}
sale.eshoporder.eshopplatformcheck.OrderGatherInfoAction.registerClass('sale.eshoporder.eshopplatformcheck.OrderGatherInfoAction', Sys.UI.PageAction);
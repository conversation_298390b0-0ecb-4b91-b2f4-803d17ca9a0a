<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.PlSalePeriodMapper">

    <!--时段字段-->
    <sql id="PlSalePeriod_Column_List">
        psp
        .
        id
        ,psp.profile_id,psp.period_group_id,
        psp.period_name,psp.period_start_time,psp.period_end_time,
        psp.period_days,psp.remark,psp.deleted,
        psp.create_time,psp.update_time,psp.creator_id
    </sql>

    <resultMap id="BaseResultMap" type="com.wsgjp.ct.sale.biz.eshoporder.entity.salesperiod.PlSalePeriod">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="profileId" column="profile_id" jdbcType="BIGINT"/>
        <result property="periodGroupId" column="period_group_id" jdbcType="BIGINT"/>
        <result property="periodName" column="period_name" jdbcType="VARCHAR"/>
        <result property="periodStartTime" column="period_start_time" jdbcType="TIMESTAMP"/>
        <result property="periodEndTime" column="period_end_time" jdbcType="TIMESTAMP"/>
        <result property="periodDays" column="period_days" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,profile_id,period_group_id,
        period_name,period_start_time,period_end_time,
        period_days,remark,deleted,
        create_time,update_time,creator_id
    </sql>

    <!--    批量新增或修改-->
    <insert id="addBatch">
        INSERT INTO
        pl_sale_period(id,profile_id,period_group_id,period_name,period_start_time,period_end_time,period_days,remark,deleted,create_time,update_time,creator_id)
        values
        <foreach collection="list" separator="," item="item">
            (
            #{item.id},#{item.profileId},#{item.periodGroupId},#{item.periodName},#{item.periodStartTime},#{item.periodEndTime},#{item.periodDays},#{item.remark},#{item.deleted},#{item.createTime},#{item.updateTime},#{item.creatorId}
            )
        </foreach>
    </insert>

    <!--    根据时段ID删除时段-->
    <delete id="deleteById">
        delete
        from pl_sale_period
        where profile_id = #{arg1}
          and period_group_id = #{arg0}
    </delete>

    <!--    查询时段组的所有时段-->
    <select id="listPlSalePeriod" resultMap="BaseResultMap">
        SELECT
        <include refid="PlSalePeriod_Column_List"/>
        FROM pl_sale_period psp WHERE psp.period_group_id=#{arg0} and psp.profile_id=#{arg1}
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<Page xmlns="Craba.UI" Title="${title}"
      MinWidth="670"
      MinHeight="${minHeight}"
      CssClass='pd0'
      DataSource="${datasource}"
      ActionType="sale.product.EditRuleFormAction, sale/eshoporder/product/EditRuleForm.js">
    <FlexColumn>
        <FlowPanel CssClass="plr10" ItemLabelWidth="120" LayoutDirection="Vert" Enabled="${canEdit}">
            <FlowPanel CssClass="pd0" Visible="${!isDefault}">
                <Label Text="*" FontColor="#e43a1c" CssStyle="vertiacl-align:middle"/>
                <Label Text="同步规则名称"/>
            </FlowPanel>
            <TextEdit DataField="ruleName" Visible="${!isDefault}" CssClass='ml10' Width="463" required="${!isDefault}"/>
            <FlowPanel>
                <Label Text="* " FontColor="#e43a1c" CssStyle="vertiacl-align:middle"/>
                <Label Text="同步库存"/>
            </FlowPanel>
            <DropDownCheckBoxList ID="drKtype"
                                  SelectOnly="true" Required="true"
                                  NullDisplayText="请选择同步仓库"
                                  DropDownStyle="DropDownMultiSearch" CssClass='ml10'
                                  DataSource="${ktypeList}"
                                  DataField="ktypeIds"
                                  DataTextField="fullname"
                                  DataValueField="id"
                                  Width="463"/>
            <FlowPanel CssClass="pd0 pt10">
                <Label Text="* " FontColor="#e43a1c" CssStyle="vertiacl-align:middle"/>
                <Label Text="库存同步公式"/>
            </FlowPanel>
            <FlowPanel CssClass="pd0 ml10">
                <Label Text="库存数量 = 可销售库存 * "/>
                <NumberEdit NumberType="PositiveInt" MaxValue="9999999999"
                            ShowLabel="true" Width="100"
                            DefaultValue="100"
                            MinValue="0"
                            DataField="calculate.percentage"
                            ID="txtPercentage"
                            ShowCalculator="false"
                            NullDisplayText="输入比例"/>
                <Label Text="% "/>
                <DropDownEdit ID="drCalculateMode" DropDownStyle="DropDownList" Hint="计算方式"
                              Width="40" Required="true"
                              OnChange="doTypeChange" DataTextField="text" DataValueField="id"
                              DataField="calculate.calculateMode">
                    <ListItem Text="+" Value="0"/>
                    <ListItem Text="-" Value="1"/>
                    <ListItem Text="*" Value="2"/>
                    <ListItem Text="/" Value="3"/>
                </DropDownEdit>
                <NumberEdit NumberType="PositiveInt"
                            ID="txtVirtualQty"
                            MaxValue="9999999999"
                            ShowLabel="true"
                            Width="100"
                            Hint="虚拟库存量"
                            DataField="calculate.ruleCalQty"
                            MinValue="0"
                            ShowCalculator="false"/>
            </FlowPanel>
            <FlexColumn CssClass="mb0 pt10">
                <CheckBox Text="0库存自动同步" DataField="zeroQtySyncEnabled" CssClass="mb0"/>
                <Label Text="开启后，当库存为0时会同步到网店，可能会导致商品下架，请谨慎选择" FontColor="grey"/>
            </FlexColumn>
            <Button Text="展开更多配置" RightIcon="aicon-down" Visible="${!showMore}" CssClass="NoBorder"
                    CssStyle="color:#2288fc;border:0px" OnClick="showHideMore" ID="btnShowMore"
                    OnRightIconClick="showHideMore"/>
            <FlowPanel ID="fpMore" CssClass="plr0" ItemLabelWidth="120" LayoutDirection="Vert" Visible="${showMore}">
                <FlexColumn CssClass="mb0">
                    <CheckBox Text="分仓同步库存" DataField="warehouseStockSyncEnabled" OnClick="doWarehouseSyncClick"
                              CssClass="mb0"/>
                    <Label OnClick="doWarehouseTipClick" IsMemo="true" FontColor="grey" Text="${warehouseTip}"/>
                </FlexColumn>
                <CheckBox Text="时效库存同步" DataField="eshopMultiStockSyncEnabled" OnClick="doMultiSyncClick"
                          Visible="${isSupportMultiSync}"/>
                <Grid AllowConfig="false"
                      ID="multiStockGrid"
                      CssClass='FlexAuto'
                      ReadOnly="false"
                      Visible="${eshopMultiStockSyncEnabled}"
                      DefaultRowCount="1"
                      DataSource="${multiList}"
                      OnCellBeginEdit='doMultiStockCellBeginEdit'>
                    <IconColumn Name='icon' DataField='icon' OnButtonClick='doIconColumnClick' Width='55'
                                Caption='操作' ListItems='增加行=aicon-zengjia,删除行=aicon-jian' DisplayNull='true'/>
                    <DropDownColumn Caption="时效类型" DataSource="${allowMultiTypes}" DataValueField="platformMultiId"
                                    DataTextField="platformMultiName"
                                    DataField="platformMultiId" MaxWidth="0" Width="200" OnChange="doMultiTimeTypeChanged"/>
                    <DropDownColumn Caption="同步类型" DataField="syncType" Width="80"
                                    OnChange="onSyncTypeChange" ListItems="0=按公式,1=固定数量"/>
                    <SelectorColumn Caption="同步库存公式" SelectOnly="true" Width="200" AllowDelIcon='false'
                                    DataField="formula" Icon='aicon-bianji4'/>
                    <NumberColumn Caption="同步数量" NumberType="PositiveFloat" DefaultValue="0"
                                  DataField="syncTimeQty" AllowSort="false" DecimalPrecision="14" MinValue="0"
                                  OnChange="doSyncTimeQtyChanged"/>
                </Grid>
            </FlowPanel>
            <Button ID="btnHideMore" Text="收起更多配置" RightIcon="aicon-up" Visible="${showMore}" CssClass="NoBorder"
                    CssStyle="color:#2288fc" OnClick="showHideMore" OnRightIconClick="showHideMore"/>
        </FlowPanel>
    </FlexColumn>
    <HBlock CssClass='BottomBlock FlexRight margin0' Visible="${canEdit}">
        <Block CssClass='Flex1'/>
        <Button ID="btnSave" OnClick="doSave" CssClass="SpecialButton" Text="保存"/>
        <CancelButton Text="关闭"/>
    </HBlock>

    <PopupBlock ID="formulaPopup">
        <FlexColumn CssClass='FlexAuto FlexColumn' Width="376">
            <Label Text='同步库存公式' CssClass='StockRuleTopMenuCaption'/>
            <FlowPanel ID="formulaForm">
                <Label Text="可销售库存"/>
                <Label Text="*"/>
                <NumberEdit NumberType="PositiveInt"
                            MaxValue="999999"
                            DefaultValue="100"
                            ShowLabel="true" Width="110"
                            ID="txtTimePercentage" DataField="calculate.percentage" ShowCalculator="false"/>
                <Label Text="%"/>
                <DropDownEdit DropDownStyle="DropDownList" Hint="计算方式" SelectedIndex="0" Width="40"
                              ID="timeDeVirtualQtyType" OnChange="doTimeQtyChange"
                              DataField="calculate.calculateMode">
                    <ListItem Text="+" Value="0"/>
                    <ListItem Text="-" Value="1"/>
                    <ListItem Text="*" Value="2"/>
                    <ListItem Text="/" Value="3"/>
                </DropDownEdit>
                <NumberEdit NumberType="PositiveInt"
                            MaxValue="999999"
                            MinValue="0"
                            DefaultValue="0"
                            OnChange="doTimeQtyChange" DecimalScale="0" ShowLabel="true"
                            Width="110"
                            Hint="虚拟库存量"
                            DataField="calculate.ruleCalQty" ID="txtTimeVirtualQty" ShowCalculator="false"/>
            </FlowPanel>
            <FlowPanel ID="formulaQtyForm" Visible="false">
                <Label Text="固定数量:"/>
                <NumberEdit NumberType="PositiveInt" MaxValue="999999" MinValue="0" DefaultValue="0"
                            ShowLabel="true" Width="110"
                            ID="neRuleCalQty" DataField="fixQty" ShowCalculator="false"/>
            </FlowPanel>
            <FlowPanel CssClass="BottomBlock">
                <Block CssClass='Flex1'/>
                <Button Text="确定" CssClass="SpecialButton" OnClick="doSaveFormula"/>
            </FlowPanel>
        </FlexColumn>
    </PopupBlock>

    <Style>
        .Button.NormalLine .RightIcon{
        border-color:white;
        border-width:0px;
        margin-left:0px;
        }
        .FlowPanel .FlowItem {
        margin-bottom:0px;
        margin-right:0px;
        }
    </Style>
</Page>
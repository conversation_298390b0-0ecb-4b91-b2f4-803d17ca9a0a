Type.registerNamespace('sale.eshoporder.eshopsaleorder');

sale.eshoporder.eshopsaleorder.EshopOrderAutoSubmitConfigAction = function () {
    sale.eshoporder.eshopsaleorder.EshopOrderAutoSubmitConfigAction.initializeBase(this);
};

sale.eshoporder.eshopsaleorder.EshopOrderAutoSubmitConfigAction.prototype = {
    oldData:null,
    context: function (cb) {
        cb();
    },
    initialize: function EshopOrderAutoSubmitConfigAction$initialize() {
        sale.eshoporder.eshopsaleorder.EshopOrderAutoSubmitConfigAction.callBaseMethod(this, 'initialize');
        var from = this.get_form();
        var rsp = $common.ajaxSync({
            url: "sale/eshoporder/order/getEshopOrderAutoSubmitConfig" ,
            data: null,
            type: 'post',
            router: 'ngp'
        });
        this.oldData = rsp.data;
        from.submitNowAfterDownload.set_value(!rsp.data ? true : rsp.data.submitNowAfterDownload);
        from.submitTimeAfterPay.set_value(!rsp.data ? false : rsp.data.submitTimeAfterPay);
        from.minuteTime.set_value(!rsp.data ? null : rsp.data.minuteTime);
    },

    doSave: function (sender) {
        var startObj = null;
        if (window.$startTiming) {
            startObj = $startTiming('网店->平台原始订单池->自动提交设置');
        }
        var form = this.get_form();
        var data = form.saveData();
        var rsp = $common.ajaxSync({
            url: "sale/eshoporder/order/saveEshopOrderAutoSubmitConfig" ,
            data: data,
            type: 'post',
            router: 'ngp'
        });
        if (rsp.code != 200) {
            $common.showError("保存失败," + rsp.message);
            return;
        }
        $common.showOk("保存成功");
        if (!(this.oldData && this.oldData.submitNowAfterDownload == data.submitNowAfterDownload)){
            $analysisCloud.Util.addPubSystemLog("自动提交设置：修改【是否下载订单后立即自动提交】为【" +
                (data.submitNowAfterDownload ? "是" : "否") + "】");
        }
        if (!(this.oldData && this.oldData.submitTimeAfterPay == data.submitTimeAfterPay)){
            $analysisCloud.Util.addPubSystemLog("自动提交设置：修改【是否订单付款后提交】为【" +
                (data.submitTimeAfterPay ? "是" : "否") + "】");
        }
        if (!(this.oldData && this.oldData.minuteTime == data.minuteTime)){
            $analysisCloud.Util.addPubSystemLog("自动提交设置：修改【付款后分钟数】为【" +
                data.minuteTime + "】");
        }
        form.doOk();
        if (startObj != null) {
            startObj.endTiming();// 触发上报
        }
    },

    onSubmitConfigClick:function ( sender){
        var form = this.get_form();
        if (form.submitNowAfterDownload.get_checked()) {
            form.submitTimeAfterPay.set_checked(false)
            return;
        }
        else if(form.submitTimeAfterPay.get_checked()){
            form.submitNowAfterDownload.set_checked(false)
            return;
        }
    },

    dispose: function () {
        sale.eshoporder.eshopsaleorder.EshopOrderAutoSubmitConfigAction.callBaseMethod(this, 'dispose');
    }
};
sale.eshoporder.eshopsaleorder.EshopOrderAutoSubmitConfigAction.registerClass('sale.eshoporder.eshopsaleorder.EshopOrderAutoSubmitConfigAction', Sys.UI.PageAction);
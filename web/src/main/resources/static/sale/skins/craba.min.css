@font-face{font-family:"iconfont";src:url("iconfont.eot?t=1752810445724");src:url("iconfont.eot?t=1752810445724#iefix") format('embedded-opentype'),url('data:application/x-font-woff2;charset=utf-8;base64,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') format('woff2'),url("iconfont.woff?t=1752810445724") format('woff'),url("iconfont.ttf?t=1752810445724") format('truetype'),url("iconfont.svg?t=1752810445724#iconfont") format('svg')}.iconfont{font-family:"iconfont" !important;font-size:16px;font-style:normal;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.aicon-hudong1:before{content:"\e64b"}.aicon-hudong4:before{content:"\e87e"}.aicon-jiaohuanweizhi:before{content:"\e649"}.aicon-bitianxiangguanli:before{content:"\e62e"}.aicon-tuding:before{content:"\e608"}.aicon-moreunfold:before{content:"\e9ff"}.aicon-less:before{content:"\e6a5"}.aicon-check:before{content:"\e81d"}
.aicon-duigou1:before{content:"\e78a"}.aicon-dash:before{content:"\e81f"}.aicon-gengduo:before{content:"\e6eb"}.aicon-jian:before{content:"\e613"}.aicon-gengduotianchong:before{content:"\e6f3"}.aicon-daoru:before{content:"\e65e"}.aicon-daorudingdan:before{content:"\e766"}.aicon-play-circle:before{content:"\e784"}.aicon-setting:before{content:"\e78f"}.aicon-close-circle:before{content:"\e77f"}.aicon-up-circle:before{content:"\e9fc"}.aicon-question-circle:before{content:"\e785"}.aicon-down-circle:before{content:"\e781"}.aicon-plus-circle:before{content:"\e783"}.aicon-minus-circle:before{content:"\e782"}.aicon-drag1:before{content:"\e669"}.aicon-paixu:before{content:"\e7d7"}
.aicon-meiyuan:before{content:"\e70e"}.aicon-fujian:before{content:"\e60c"}.aicon-meiyuan1:before{content:"\e6c5"}.aicon-huifu:before{content:"\e7e6"}.aicon-chexiao:before{content:"\e7e5"}.aicon-table:before{content:"\e7dd"}.aicon-ziti:before{content:"\e7e3"}.aicon-skin:before{content:"\e7da"}.aicon-xinshouyindao11:before{content:"\e7d9"}.aicon-link:before{content:"\e665"}.aicon-rukuguanli:before{content:"\e651"}.aicon-renminbi:before{content:"\e714"}.aicon-fuzhi:before{content:"\e7df"}.aicon-jianqie:before{content:"\e7e2"}.aicon-niantie:before{content:"\e7e1"}.aicon-yinyong:before{content:"\e716"}.aicon-dianhua:before{content:"\e6ce"}.aicon-jiaxingshoucangtianchong:before{content:"\e6f5"}
.aicon-jiesuo:before{content:"\e675"}.aicon-fenxiangtianchong:before{content:"\e6f2"}.aicon-xuanzhuan:before{content:"\e7b6"}.aicon-youjian:before{content:"\e7b1"}.aicon-sousuotianchong:before{content:"\e701"}.aicon-wenjianjia-:before{content:"\e7b5"}.aicon-qingchu:before{content:"\e674"}.aicon-yanjing:before{content:"\e722"}.aicon-shengyin:before{content:"\e6db"}.aicon-shanchu6:before{content:"\e6d9"}.aicon-lipintianchong:before{content:"\e6f8"}.aicon-qingkong:before{content:"\e7be"}.aicon-shanchutianchong:before{content:"\e6fc"}.aicon-qian1:before{content:"\e6ea"}.aicon-shangpin:before{content:"\e7bc"}.aicon-xiaojiantou:before{content:"\e7c5"}.aicon-feijichangtianchong:before{content:"\e6f0"}
.aicon-tianjiayonghu:before{content:"\e6dd"}.aicon-shezhi2:before{content:"\e6da"}.aicon-liwu:before{content:"\e6e9"}.aicon-ico-jrcwsf:before{content:"\e7b3"}.aicon-jinbi2:before{content:"\e7a2"}.aicon-shurutianchong:before{content:"\e71d"}.aicon-yidong:before{content:"\e7b9"}.aicon-fenggepitchon:before{content:"\e6c1"}.aicon-dianhua6:before{content:"\e7a0"}.aicon-fenggeshezhi:before{content:"\e6c4"}.aicon-fenxiang2:before{content:"\e73d"}.aicon-zhengquetishi:before{content:"\e768"}.aicon-bianji5:before{content:"\e736"}.aicon-saoma:before{content:"\e74a"}.aicon-file:before{content:"\e7e0"}.aicon-wenjianjia:before{content:"\e75d"}.aicon-kefu3:before{content:"\e79d"}.aicon-Group-:before{content:"\e6cc"}
.aicon-wendang5:before{content:"\e75a"}.aicon-kefu2:before{content:"\e773"}.aicon-diannao:before{content:"\e739"}.aicon-diannao1:before{content:"\e79e"}.aicon-shoujichongzhi1:before{content:"\e789"}.aicon-kefu1:before{content:"\e72a"}.aicon-bangzhu:before{content:"\e79a"}.aicon-yujing:before{content:"\e774"}.aicon-tuichu:before{content:"\e6be"}.aicon-shangchuan:before{content:"\e74f"}.aicon-yonghu:before{content:"\e76a"}.aicon-bangding:before{content:"\e776"}.aicon-wenjian:before{content:"\e6cd"}.aicon-yibantishi1:before{content:"\e765"}.aicon-shirenrenzheng:before{content:"\e752"}.aicon-shengyin1:before{content:"\e751"}.aicon-shouye1:before{content:"\e6cb"}.aicon-wendang:before{content:"\e68f"}
.aicon-gongju:before{content:"\e67f"}.aicon-xiazai4:before{content:"\e686"}.aicon-shouye2:before{content:"\e6a2"}.aicon-xinshouyindao:before{content:"\e6b5"}.aicon-denglumima-baise:before{content:"\e6a1"}.aicon-iconset0120:before{content:"\e68c"}.aicon-refresh_icon:before{content:"\e672"}.aicon-yulan:before{content:"\e6bc"}.aicon-fankui:before{content:"\e6aa"}.aicon-banben:before{content:"\e69d"}.aicon-guestbook:before{content:"\e6b2"}.aicon-shijian4:before{content:"\e67a"}.aicon-denglu1:before{content:"\e69e"}.aicon-gongju2:before{content:"\e682"}.aicon-fankui1:before{content:"\e6b0"}.aicon-tool:before{content:"\e680"}.aicon-wendang1:before{content:"\e69c"}
.aicon-wendang4:before{content:"\e6a4"}.aicon-xiazai:before{content:"\e683"}.aicon-iconset0121:before{content:"\e68d"}.aicon-xinshoubangzhu:before{content:"\e71e"}.aicon-xiazai2:before{content:"\e790"}.aicon-wodefankui:before{content:"\e6af"}.aicon-wendang2:before{content:"\e69f"}.aicon-yulan1:before{content:"\e6bd"}.aicon-zhuce2:before{content:"\e69b"}.aicon-tishikong:before{content:"\e636"}.aicon-add1:before{content:"\e609"}.aicon-pifu:before{content:"\e691"}.aicon-shezhi1:before{content:"\e65c"}.aicon-pifu1:before{content:"\e60b"}.aicon-dayin:before{content:"\e666"}.aicon-pifu2:before{content:"\e667"}.aicon-chaxun:before{content:"\e64a"}.aicon-caiwuguanli1:before{content:"\e668"}
.aicon-bianji:before{content:"\e60e"}.aicon-tishi2:before{content:"\e611"}.aicon-tishi1:before{content:"\e606"}.aicon-tishi663:before{content:"\e663"}.aicon-yincang:before{content:"\e660"}.aicon-suoding1:before{content:"\e676"}.aicon-shezhi:before{content:"\e65b"}.aicon-tishi:before{content:"\e657"}.aicon-z:before{content:"\e673"}.aicon-tishi3:before{content:"\e68a"}.aicon-radio-checked:before{content:"\e650"}.aicon-chuyidong:before{content:"\e610"}.aicon-dayinhetong:before{content:"\e61e"}.aicon-xianshiyuanma:before{content:"\e6de"}.aicon-suoding:before{content:"\e65f"}.aicon-shanchu:before{content:"\e637"}.aicon-sanjiaoleft:before{content:"\e6b6"}.aicon-sortdown:before{content:"\e64e"}
.aicon-sortup:before{content:"\e64f"}.aicon-shujuyichang:before{content:"\e8db"}.aicon-bianji1:before{content:"\e638"}.aicon-tishi5:before{content:"\e9fb"}.aicon-bianji11:before{content:"\e617"}.aicon-del2:before{content:"\e616"}.aicon-tishi4:before{content:"\e738"}.aicon-zhushi:before{content:"\e603"}.aicon-jianpan1:before{content:"\e61c"}.aicon-icon--:before{content:"\e76e"}.aicon-bianji2:before{content:"\e618"}.aicon-shangjiantou:before{content:"\e600"}.aicon-bianji3:before{content:"\e688"}.aicon-jianpan:before{content:"\e648"}.aicon-bianji4:before{content:"\e692"}.aicon-user:before{content:"\e7af"}.aicon-export:before{content:"\e792"}.aicon-up:before{content:"\e7ef"}
.aicon-save:before{content:"\e793"}.aicon-team:before{content:"\e7b0"}.aicon-mima2:before{content:"\e60f"}.aicon-right:before{content:"\e7ed"}.aicon-down:before{content:"\e7f0"}.aicon-shanchu3:before{content:"\e615"}.aicon-mima:before{content:"\e607"}.aicon-check-circle:before{content:"\e77d"}.aicon-addteam:before{content:"\e7ae"}.aicon-x:before{content:"\e61b"}.aicon-verticalright:before{content:"\e7eb"}.aicon-left:before{content:"\e7ee"}.aicon-verticalleft:before{content:"\e7ec"}.aicon-yuanmakaifa:before{content:"\e639"}.aicon-edit-square:before{content:"\e791"}.aicon-totop:before{content:"\e7fd"}.aicon-arrowleft:before{content:"\e7f7"}.aicon-indent:before{content:"\e806"}
.aicon-arrowdown:before{content:"\e7f8"}.aicon-colum-height:before{content:"\e7fa"}.aicon-menu:before{content:"\e808"}.aicon-fall:before{content:"\e802"}.aicon-download:before{content:"\e7ff"}.aicon-arrowright:before{content:"\e7f5"}.aicon-align-left:before{content:"\e80d"}.aicon-align-center:before{content:"\e80c"}.aicon-align-right:before{content:"\e80b"}.aicon-swap:before{content:"\e803"}.aicon-stock:before{content:"\e804"}.aicon-vertical-align-botto:before{content:"\e7fb"}.aicon-fullscreen-exit:before{content:"\e7f2"}.aicon-orderedlist:before{content:"\e80a"}.aicon-vertical-align-top:before{content:"\e7fe"}.aicon-upload:before{content:"\e7f9"}.aicon-unorderedlist:before{content:"\e809"}
.aicon-outdent:before{content:"\e807"}.aicon-rise:before{content:"\e805"}.aicon-fullscreen:before{content:"\e7f1"}.aicon-arrowup:before{content:"\e7f6"}.aicon-underline:before{content:"\e818"}.aicon-pic-center:before{content:"\e80e"}.aicon-drag:before{content:"\e842"}.aicon-pic-right:before{content:"\e80f"}.aicon-italic:before{content:"\e81a"}.aicon-bold:before{content:"\e811"}.aicon-font-colors:before{content:"\e812"}.aicon-infomation:before{content:"\e815"}.aicon-column-width:before{content:"\e81c"}.aicon-desktop:before{content:"\e843"}.aicon-bg-colors:before{content:"\e826"}.aicon-strikethrough:before{content:"\e817"}.aicon-font-size:before{content:"\e814"}
.aicon-plus:before{content:"\e823"}.aicon-number:before{content:"\e819"}.aicon-code:before{content:"\e81b"}.aicon-close1:before{content:"\e820"}.aicon-question:before{content:"\e822"}.aicon-pic-left:before{content:"\e810"}.aicon-line-height:before{content:"\e816"}.aicon-zhuanru:before{content:"\e644"}.aicon-fenxiang:before{content:"\e624"}.aicon-close:before{content:"\e69a"}.aicon-add:before{content:"\e6b9"}.aicon-duigou:before{content:"\e629"}.aicon-close-circle-fill:before{content:"\e84c"}.aicon-down-circle-fill:before{content:"\e84a"}.aicon-dingdan:before{content:"\e647"}.aicon-tishishuoming:before{content:"\e64c"}.aicon-wode:before{content:"\e630"}.aicon-fenlei:before{content:"\e632"}
.aicon-shuoming:before{content:"\e62a"}.aicon-shouye:before{content:"\e631"}.aicon-jiantouxia:before{content:"\e62f"}.aicon-jiantoushang:before{content:"\e62c"}.aicon-up-circle-fill:before{content:"\e84e"}.aicon-xiaoxi:before{content:"\e633"}.aicon-question-circle-fill:before{content:"\e851"}.aicon-dingyue:before{content:"\e63f"}.aicon-back:before{content:"\e697"}.aicon-more:before{content:"\e6a7"}.aicon-Dollar-circle-fill:before{content:"\e853"}.aicon-image-fill:before{content:"\e860"}.aicon-minus-circle-fill:before{content:"\e84b"}.aicon-plus-circle-fill:before{content:"\e850"}.aicon-quanping:before{content:"\e628"}.aicon-kefu:before{content:"\e627"}.aicon-check-circle-fill:before{content:"\e848"}
.aicon-home:before{content:"\e867"}.aicon-jiantou:before{content:"\e8cb"}.aicon-huanyihuan:before{content:"\e653"}.aicon-shuzhi:before{content:"\e8a2"}.aicon-shugan:before{content:"\e89f"}.aicon-shuye:before{content:"\e8a0"}.aicon-shangpinwei:before{content:"\e88e"}.aicon-fenleiliebiao:before{content:"\e895"}.aicon-manjian:before{content:"\e896"}.aicon-fenleidaohang:before{content:"\e897"}.aicon-shangpinlanmu:before{content:"\e898"}.aicon-fuwenben:before{content:"\e899"}.aicon-yushou:before{content:"\e88f"}.aicon-zhekou:before{content:"\e890"}.aicon-xianshihuodong:before{content:"\e891"}.aicon-shipin1:before{content:"\e7de"}.aicon-shangpinxinxiliu:before{content:"\e893"}
.aicon-zengpin:before{content:"\e894"}.aicon-jingangwei:before{content:"\e88d"}.aicon-tupian:before{content:"\e88c"}.aicon-bt:before{content:"\e87d"}.aicon-gonggao_huaban:before{content:"\e7d8"}.aicon-paixu2:before{content:"\e86e"}.aicon-fujianshangchuan:before{content:"\e868"}.aicon-daochu1:before{content:"\e869"}.aicon-bangzhu1:before{content:"\e86a"}.aicon-shouqi:before{content:"\e86b"}.aicon-shaixuan:before{content:"\e86c"}.aicon-shuaxin1:before{content:"\e86d"}.aicon-cclose:before{content:"\e866"}.aicon-queshengye_zanwushuju:before{content:"\e63b"}.aicon-chaxunwushuju:before{content:"\e66a"}.aicon-zhankaishouqi:before{content:"\e864"}.aicon-rili:before{content:"\e7ba"}
.aicon-qingkong1:before{content:"\e662"}.aicon-kejian:before{content:"\e656"}.aicon-bukejian:before{content:"\e659"}.aicon-shizhong1:before{content:"\e719"}.aicon-jiantou2:before{content:"\e74b"}.aicon-jiantou1:before{content:"\e7b8"}.aicon-fenlei1:before{content:"\e62d"}.aicon-filter:before{content:"\e634"}.aicon-daochu:before{content:"\e646"}.aicon-xiayi:before{content:"\e620"}.aicon-shangyi:before{content:"\e621"}.aicon-zengjia:before{content:"\e612"}.aicon-bianji6:before{content:"\e619"}.aicon-shanchu1:before{content:"\e61f"}.aicon-fukucunyujingshengcheng:before{content:"\e87b"}.aicon-anquankucunyujingshengcheng:before{content:"\e87c"}.aicon-xiaoshouchukudan:before{content:"\e7ca"}
.aicon-yingshoukuanchaxun:before{content:"\e7cb"}.aicon-xiaoshoudingdan:before{content:"\e7cc"}.aicon-shoukuandan:before{content:"\e7cd"}.aicon-caigou:before{content:"\e7cf"}.aicon-kucunchaxun1:before{content:"\e7d0"}.aicon-zhanghuyuechaxun:before{content:"\e7d1"}.aicon-xiaoshoudingdanguanli:before{content:"\e7d2"}.aicon-caigourukudan1:before{content:"\e7d3"}.aicon-xiaoshoumaolitongji:before{content:"\e82a"}.aicon-fukuandan:before{content:"\e877"}.aicon-yingfukuanchaxun:before{content:"\e87a"}.aicon-xiaoshouckd:before{content:"\e876"}.aicon-menu-shezhi:before{content:"\e86f"}.aicon-caigourukudan:before{content:"\e870"}.aicon-caigourkcx:before{content:"\e871"}.aicon-kucunchaxun:before{content:"\e872"}
.aicon-shangpindangan:before{content:"\e873"}.aicon-wanglai:before{content:"\e874"}.aicon-xiaoshouth:before{content:"\e875"}.aicon-xsckcx:before{content:"\e878"}.aicon-caigoutuihuo:before{content:"\e879"}.aicon-baobeiduiying2:before{content:"\e85a"}.aicon-shezhiSKUbianhao:before{content:"\e85b"}.aicon-qichuyingshouyingfu:before{content:"\e85c"}.aicon-baobeidabiao:before{content:"\e85d"}.aicon-qichushouzhizhanghu:before{content:"\e85e"}.aicon-xinzengshangpindaorukucun:before{content:"\e85f"}.aicon-shouquanwangdian:before{content:"\e861"}.aicon-shezhidayinmoban:before{content:"\e862"}.aicon-xiazaibaobei2:before{content:"\e863"}.aicon-qichukaizhang:before{content:"\e865"}.aicon-caigoudingdanshenhe:before{content:"\e839"}
.aicon-dingdanruku:before{content:"\e83a"}.aicon-caigoudanjuchaxun:before{content:"\e83b"}.aicon-quehuobuhuocaigou:before{content:"\e83c"}.aicon-fuhuokuan:before{content:"\e83d"}.aicon-wancheng:before{content:"\e83e"}.aicon-xinzengcaigoudingdan:before{content:"\e83f"}.aicon-xinzengcaigoutuihuodan:before{content:"\e840"}.aicon-xinzengcaigouhuanhuodan:before{content:"\e841"}.aicon-yufukuan:before{content:"\e858"}.aicon-xinzengcaigourukudan:before{content:"\e859"}.aicon-anhuozhaodan:before{content:"\e65a"}.aicon-ercifenjian:before{content:"\e7c2"}.aicon-peihuodengji:before{content:"\e7c3"}.aicon-baoguochengzhong:before{content:"\e7c4"}.aicon-zhengdanyanhuo:before{content:"\e7c7"}.aicon-piliangyanhuo:before{content:"\e7c8"}
.aicon-saomiaoyanhuo:before{content:"\e7ce"}.aicon-weifahuodingdanchaxun:before{content:"\e7db"}.aicon-yishenhedingdan:before{content:"\e7dc"}.aicon-yifahuodingdanchaxun:before{content:"\e832"}.aicon-dadanpeihuo:before{content:"\e833"}.aicon-piliangchengzhong:before{content:"\e834"}.aicon-chuangjianboci:before{content:"\e835"}.aicon-chaidanyanhuo:before{content:"\e836"}.aicon-bocifahuo:before{content:"\e837"}.aicon-saomiaofahuo:before{content:"\e838"}.aicon-weiyanhuodingdan:before{content:"\e6c6"}.aicon-yisaomiaoshangpin:before{content:"\e6c7"}.aicon-daichengzhongbaoguoshu:before{content:"\e6c8"}.aicon-qq:before{content:"\e6ca"}.aicon-baobeiduiying:before{content:"\e713"}.aicon-dingdancelveguanli:before{content:"\e7bf"}
.aicon-kucuntongbu:before{content:"\e7c1"}.aicon-xiazaibaobei:before{content:"\e7c9"}.aicon-xiaoshoujigouyunyingtongji:before{content:"\e7e8"}.aicon-mobanzhongxin:before{content:"\e81e"}.aicon-shangpinjinxiaocunbiandongtongji:before{content:"\e828"}.aicon-kucunzhuangkuangbiao:before{content:"\e829"}.aicon-peizhizidongxiazaidingdan:before{content:"\e82b"}.aicon-shougongxiazaidingdan:before{content:"\e82c"}.aicon-yuandanguanli:before{content:"\e82d"}.aicon-zhouqigouguanli:before{content:"\e82e"}.aicon-baojingxinxi:before{content:"\e82f"}.aicon-yushouguanli:before{content:"\e830"}.aicon-dingdanshenhe:before{content:"\e831"}
:root{--mainColor:#2288fc;--hoverColor:#21c8ff;--lightColor:#eff6fe}html,body{padding:0;margin:0}html{height:100%}body{display:flex;flex-direction:column;height:100%;background-color:#fff}body,input,select,textarea,button{color:#323232;font-size:12px;font-family:system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,"Apple Color Emoji","Segoe UI Emoji",Segoe UI Symbol,"Noto Color Emoji","arial",pingfang sc,"微软雅黑","宋体"}form{margin:0}img{border:0}textarea{resize:none}textarea.ResizeBoth{resize:both}textarea.ResizeWidth{resize:horizontal}textarea.ResizeHeight{resize:vertical}*{outline:0;box-sizing:border-box;-webkit-font-smoothing:antialiased}fieldset{border:1px solid #ddd}::-ms-clear{display:none}::-ms-reveal{display:none}::selection{background-color:#d2ebff}.FloatLeft{float:left}.FloatRight{float:right}.overflowauto,.oauto{overflow:auto !important}.overhidden{overflow:hidden !important}
.ovisible{overflow:visible !important}.clear{clear:both}.clear.FloatLeft{float:none;width:100%;overflow:hidden;height:0}.clearnone{clear:none}.link.normal,a.normal:link,a.normal:visited{color:inherit}a:link,.link,a.LabelLink:link,a.LabelLink:visited,a:visited{text-decoration:none;cursor:pointer}a:link,.link,a.LabelLink:link,a.LabelLink:visited,a:visited{color:#2288fc;color:var(--mainColor)}a:hover,.link:hover,.link.normal:hover,a.normal:hover,a.LabelLink:hover{color:#21c8ff;color:var(--hoverColor);text-decoration:none}.LinkItem{text-decoration:underline !important}.LinkHover:hover{text-decoration:underline !important}table{border-spacing:0}ol,ul{margin:0;padding-inline-start:15px}h1{font-size:20pt;line-height:24pt}.lh20{line-height:20px !important}.height20{height:20px !important}.lh16{line-height:16px !important}.height16{height:16px !important}h2{font-size:16pt;line-height:20pt}p{-webkit-margin-before:0;-webkit-margin-after:0;margin:0}.pointer{cursor:pointer !important}.fpDragItem,.dragCursor{cursor:grabbing !important;user-select:none}
.cursorArrow,.Buttons.cursorArrow .Button{cursor:default !important}*[disabled="true"],.disabled{color:#a0a0a0 !important;cursor:not-allowed !important}.GridBodyCell .disabled a,.GridBodyCell.disabled .icon{color:#a0a0a0 !important;cursor:not-allowed !important}input[type="radio"],input[type="checkbox"],.MenuRoot,.Button,.MenuItem,.FormCaptionButton,.RowNoConfigIcon{cursor:pointer}.ellipsis,.Label.ellipsis,.FlowPanel .FlowLabel,.TreeView.NoWrapEllipsis .TextBlock,.FlowPanel .Label.ellipsis,.RadioButton label{display:inline-block;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;word-wrap:normal;word-break:keep-all}#openwin{display:none}.aicon-up-circle-fill,.bicon-tuoyuanxing,.aicon-gengduotianchong,.aicon-down-circle-fill{color:#999}.RowNoConfigIcon,[class^="aicon-"]:before,[class*=" aicon-"]:before,[class^="aicon-"]:after,[class*=" aicon-"]:after{font-family:"iconfont" !important;font-size:16px;font-style:normal;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;-webkit-text-stroke-width:0;text-decoration:inherit;width:auto;height:auto;line-height:inherit;speak:none}
a [class^="aicon-"],a [class*=" aicon-"]{display:inline-block}.ErrMeesge{font-size:13px;color:red}.Label[class^="aicon-"],.Label[class*=" aicon-"]{display:inline-flex;cursor:default}.Label[class^="aicon-"]:before,.Label[class*=" aicon-"]:before{margin-right:5px}.EditDelButton{position:absolute;display:none;cursor:pointer;width:14px;height:14px;line-height:14px;border-radius:8px;color:#cecece;text-align:center}.EditDelButton:before{font-size:14px;display:block}.BackTopShape{position:fixed;display:none;z-index:9;bottom:10px;color:#888;cursor:pointer;right:5px;background-color:rgba(0,0,0,0.2);border:1px solid #eee;padding:10px}.BackTopShape:before{content:"\e600";font-size:20px}.BackTopShape:hover{color:#333}.Button.Label{margin-top:0}.BreakWord{word-wrap:break-word !important;word-break:break-all !important;white-space:pre-wrap !important;height:auto !important;line-height:18px !important}
.KeepWord{white-space:nowrap !important;word-wrap:normal !important;word-break:keep-all !important}.LayoutGroupBorder .LayoutFirstItemNoLabel{width:3px}.LayoutItemControlVSpace .LayoutFirstItemNoLabel{width:0}.Label{word-wrap:break-word;word-break:break-all;white-space:pre-wrap}.Babel{margin-top:0;line-height:28px;height:28px;display:inline-block}.TextLabel{width:118px;height:24px;border-width:0;padding:1px;text-indent:3px;background-color:#f6f6f6}.HSpacer{width:5px;font-size:1px;line-height:1px;padding:0;margin:0;overflow:hidden;display:inline-block;flex-shrink:0}.VSpacer{height:5px;overflow:hidden;padding:0;margin:0;flex-shrink:0}.LayoutItemControl .HSpacer{width:0}.NormalBorder{border:1px solid #ddd;border-radius:4px}.Border{border:1px solid #ddd;background-color:#fff;border-radius:2px}.NoneBorder{border:none !important;box-shadow:none !important}.Panel{border-color:#ddd}.SDIPanel{border-top:1px solid #ddd;box-shadow:1px 2px 2px #fff;padding:5px}.ToolTip{border:1px solid #eee;box-shadow:1px 2px 2px #fff;background-color:#f2fdff;padding:5px}
.hidden{visibility:hidden}.NodeEditPopup.Popup{min-width:300px}.NodeEditPopup.Popup.p-top{border-radius:4px;background-color:#fff;padding:10px 20px}.NodeEditPopup .Caption{font-size:16px;line-height:40px;display:block;font-weight:bold}.trTop{-webkit-transition:top .3s ease-in-out;-moz-transition:top .3s ease-in-out;transition:top .3s ease-in-out}._editor_bin{position:fixed;bottom:10px;right:10px;width:1px;height:20px;font-size:1px;line-height:1px;overflow:hidden;padding:0;margin:0;opacity:0;-webkit-user-select:text}.AlignCenter{text-align:center;margin:auto}.AlignCenter table{margin:auto}.ConfigButtonsBlock{margin-left:10px;display:flex;flex-direction:column;overflow-y:auto}.ConfigButtonsBlock .Button{min-width:85px;display:block;margin-bottom:6px;display:block}.ConfigButtonsBlock .ConfigCloseButton{margin:0}.MustCharLeft:before{content:"*";color:#e43a1c;vertical-align:middle;padding-right:3px;line-height:normal;text-decoration:none;font-size:15px;font-weight:bold;font-family:serif}
.MustCharRight:after{content:"*";color:#e43a1c;vertical-align:middle;padding-left:3px;line-height:normal;text-decoration:none;font-size:15px;font-family:serif;font-weight:bold}.ToolTip{display:inline-block}.IMSpacer{padding:0 2px}.jsErrorTitle{color:red;line-height:30px}.jsErrorTips{display:block;line-height:30px;margin-bottom:10px}.LinkLabel{color:#00f;cursor:pointer}.HintBox{cursor:default;position:absolute;left:50%;top:0;opacity:0;background-color:#333;color:#fff;padding:5px 25px 5px 10px;line-height:20px;word-wrap:break-word;word-break:break-all;white-space:pre-wrap;display:inline-block;cursor:pointer;border-radius:4px}.HintBox.TitleHint{max-width:1000px;max-height:460px;z-index:99999999 !important;padding:0;pointer-events:none}.HintBox.WhiteHint{box-shadow:0 2px 6px #bbb;color:#333;background-color:#fff}.HintBox.WhiteHint .iconfont{color:#fff}.HintBox.TitleHint .TitleContent{padding:10px;max-width:1000px;max-height:450px;overflow-y:auto}.HintBox.TitleHint:after{display:none}
.HintBox .iconfont{position:absolute;color:#333;line-height:normal}.HintBox .arrowTop{left:15%;bottom:-9px}.HintBox .arrowRight{left:-8px;top:10%}.HintBox .arrowBottom{left:15%;top:-9px}.HintBox .arrowLeft{right:-8px;top:10%}.HintBox .arrowTop:before{content:"\e6b6";transform:rotate(-90deg);display:block}.HintBox .arrowRight:before{content:"\e6b6"}.HintBox .arrowBottom:before{content:"\e600"}.HintBox .arrowLeft:before{content:"\e6b6";transform:rotate(180deg);display:block}.HintBlock{position:absolute;left:-10000px;top:-10000px;border-radius:6px;padding:10px;background-color:rgba(51,51,51,0.9);color:#fff;border:1px solid #555;box-shadow:0 0 5px #ddd}.HintBlock .arrow{position:absolute;top:-12px;left:10px;color:#555}.HintBlock .arrow.bottom:before{content:"\e6b6";transform:rotate(-90deg);display:block}.HintBlock .arrow.right{left:auto;right:10px}.HintBlock .arrow.bottom{top:auto;bottom:-13px}.OnlyTextTips.showClose:after,.HintBox:after{content:"\e69a";font-family:"iconfont" !important;font-weight:normal;color:#fff;cursor:pointer;position:absolute;right:10px;top:50%;transform:translateY(-50%)}
.OnlyTextTips.showClose:after{color:#999}.HintBox:after{top:15px;right:7px}.mw120{max-width:120px}.minw120{min-width:120px !important}.minw150{min-width:150px !important}.minw200{min-width:200px !important}.TextTips{transform:translateX(-50%)}.TextTips,.OnlyTextTips{position:fixed;top:-50px;left:50%;z-index:999;font-size:13px;font-weight:normal;border:0;background-color:#e8f3ff;border:1px solid #1a87ff;padding:10px 30px 10px 16px;line-height:22px;color:#333;border-radius:2px;word-wrap:break-word;word-break:break-all;min-width:10px;max-width:80%;width:auto;overflow:hidden;align-items:center;display:flex;-webkit-transition:top .2s linear;-moz-transition:top .2s linear;transition:top .2s linear}.OnlyTextTips.showClose{padding-right:40px}.OnlyTextTips.remember{padding-right:110px}.OnlyTextTips.remember .remb{position:absolute;right:30px;top:50%;transform:translateY(-50%)}.OnlyTextTips.remember:after{color:#2288fc;color:var(--mainColor)}.TextTips:hover:after,.OnlyTextTips.showClose:hover:after,.HintBox:hover:after{color:#f44336}
.TextTips.noClose:after{display:none}.TextTips.noClose{padding-right:20px}.OnlyTextTips{max-width:100%}.OnlyTextTips a,.OnlyTextTips a:visited{text-decoration:none}.OnlyTextTips.HasTarget{position:relative;left:auto;top:auto;min-width:100px;max-width:inherit;z-index:auto;margin:10px 0;box-shadow:none;border:0;border-radius:2px;flex-shrink:0}.OnlyTextTips.HasTarget{background-color:#fbf4ed}.OnlyTextTips.Info.HasTarget{background-color:#f1f5ff}.OnlyTextTips.Ok.HasTarget{background-color:#def7e4}.f12.OnlyTextTips{font-size:12px}.Info:before,.Alert.Info .MessageContent:before{color:#1890ff}.Ok:before,.Alert.Ok .MessageContent:before{color:#6fc544}.Warn:before,.Alert.Warn .MessageContent:before{color:#fe7803}.Error:before,.Alert.Error .MessageContent:before{color:#e85854}.TextTips:before,.OnlyTextTips:before{font-size:18px;color:#1890ff;margin:0 10px 0 0}.OnlyTextTips.Ok,.TextTips.Ok{border-color:#0bb76d;background-color:#e8ffea}.OnlyTextTips.Ok:before,.TextTips.Ok:before{color:#0bb76d}.OnlyTextTips.Warn,.TextTips.Warn{border-color:#ff7805;background-color:#fff7e8}
.OnlyTextTips.Warn:before,.TextTips.Warn:before{color:#ff7805}.OnlyTextTips.Error,.TextTips.Error{border-color:#ff391e;background-color:#ffece8}.OnlyTextTips.Error:before,.TextTips.Error:before{color:#ff391e}.Title{font-size:14px;font-weight:300;display:inline-block}#GlobalToolTip{border:1px solid #000;background:#333;z-index:9999999;line-height:30px;text-align:center;padding:0 15px;color:#fff;border-radius:7px;box-shadow:0 0 7px #ccc;position:absolute;left:43%;top:43%;display:none;opacity:.9}.VTop{vertical-align:top}.DisabledOverlay{background-color:#fff;position:absolute;left:0;top:0;width:100%;height:100%;cursor:not-allowed}.InlineBlock{display:inline-block}.RadiusButton{border-radius:4px;-webkit-border-radius:4px;-moz-border-radius:4px}.Shadow{box-shadow:2px 2px 2px #ccc;-webkit-box-shadow:2px 2px 2px #ccc;-moz-box-shadow:2px 2px 2px #ccc}.PanelConfigPage .Grid{margin:5px 5px 5px 10px}.LayoutGroupContainer .Button{height:20px;margin-bottom:4px;line-height:20px}.StatusBar{border-top:1px solid #eee;height:22px;line-height:22px}
.StatusBarHandle{width:10px;float:left;height:10px}.HtmlEditorHtml{border:0;padding:10px;line-height:26px;font-size:16px;background-color:#000;color:#fff;flex:auto}.HtmlEditorHtml::selection{background-color:#2288fc}.HtmlEditorForm .LayoutItemControl{padding-bottom:5px}.FormBorder .LayoutItemLabel{line-height:25px}.hideposition{position:absolute !important;left:-10000px;top:-10000px;width:100%}.GridPreviewImg{width:400px}.ExportForm{width:0;height:0;overflow:hidden}.NewHtmlEditor{border:1px solid #99caf4}.NewHtmlEditor.focus{border-color:#64a8e2}.PopupOverlay{position:fixed;left:0;top:0;width:100%;height:100%;z-index:9997}.CenterOverlay{background-color:rgba(3,3,3,0.5)}.Edit,.SkinButton,.Button,.RightIcon{-webkit-transition:all .1s linear;-moz-transition:all .1s linear;transition:all .1s linear}.Edit.AutoWidth{transition:none;min-width:20px}.Button.hasRight{transition-property:background-color,border-color,color}.ButtonEdit.SelectOnly{cursor:pointer}.EditBlock.HideButton .SkinButton{cursor:default}
.EditBlock.HideButton .SkinButton:before{display:none}.Button{border-radius:2px;border:1px solid #ddd;text-align:center;background-color:#f6f6f6;height:28px;line-height:26px;padding:0 8px;display:inline-block;white-space:nowrap;overflow:hidden;flex-shrink:0;color:#333}.Button,.Button.SecondButton{border-color:#cbcbcb;padding:0 14px;border-radius:2px;background-color:#fff}.Button.disabled,.Button.disabled.hasRight,.Button.disabled:hover,.Button.disabled.hasRight:hover{cursor:not-allowed;color:#c5c5c5 !important;background-color:#fafafa !important;border-color:#ddd !important;font-weight:normal;text-decoration:none}.Button.SpecialButton.disabled,.Button.SpecialButton.disabled:hover{background-color:#f2f2f2 !important}.Button:before{margin:0 4px 0 0;vertical-align:bottom}.Button.icontop{height:44px;line-height:22px;padding:0 10px}.Button.icontop:before{margin:0;display:block;text-align:center}.Button.iconright:before{float:right;margin:0 0 0 5px}.Button.SpeedButton{border:0;background-color:transparent;padding:0}
.Button.SpeedButton:hover{color:#2288fc;background-color:transparent}.Button.SpeedButton:focus,.Button.icononly:focus{box-shadow:none}.Button.LineButton{color:#323232;background-color:#fff;border-color:#d0d0d0}.Button.LineButton:hover{background-color:#fff;border-color:#4f81bd;color:#4f81bd}.SpeedButton.icononly{padding:0}.Button.SpeedButton.disabled,.Button.SpeedButton.disabled:hover{color:#999;background-color:transparent !important}.Button.SpeedButton.icontop{height:44px;line-height:22px;padding:0 10px}.Button.icononly{padding:0 5px;min-width:30px}.Button.icononly:before{margin:0}.Button.SpeedButton,.SpeedButton.icononly{min-width:20px}.icononly.SpeedButton:hover,.icononly.SpeedButton.active{background-color:transparent;border:0;color:#00b7f3}.Button>img{margin-right:4px;vertical-align:text-bottom}.Button:hover,.Button.hover,.Button.SecondButton:hover,.Button.SecondButton.hover,.Button.hasRight:hover,.Button.hasRight.ShowDrop,.Buttons .Button:hover,.CombEditor .Button:hover{background-color:#fff;border-color:#2288fc;color:#2288fc}
.Button.SpecialButton:hover,.Button.SpecialButton.hover,.Button.SpecialButton:active{background-color:#54a4ff;border-color:#54a4ff;color:#fff}.Button.SpecialButton.rightHover.HasLine,.Button.SpecialButton{background-color:#2288fc;border-color:#2288fc;color:#fff}.Button.Button50{border-radius:4px;height:50px;line-height:50px;min-width:110px;font-size:16px}.Button.Button40{border-radius:4px;height:40px;line-height:38px;min-width:90px;font-size:14px}.Button.Button30{border-radius:4px;height:30px;line-height:28px;min-width:auto}.Button.Button20{border-radius:4px;height:20px;line-height:18px;min-width:auto;padding:0 5px}.Button.BorderButton{color:#2288fc;background-color:#fff;border-color:#2288fc;color:var(--mainColor);border-color:var(--mainColor)}.Button:active,.Button.BorderButton:active,.Button.BorderButton:hover{border-color:#2288fc;color:#2288fc;color:var(--mainColor);border-color:var(--mainColor);box-shadow:none;background-color:#eff6fe;background-color:var(--lightColor)}.Button.HintButton{color:#2288fc;color:var(--mainColor);background-color:#f7faff;border-color:#e4efff}
.Button.HintButton:active,.Button.HintButton:hover{border-color:#2288fc;border-color:var(--mainColor);color:#2288fc;color:var(--mainColor);background-color:#eff6fe;background-color:var(--lightColor)}.Button.BorderButton.NoBorder{border:0}.Button.SpeedButton.BorderButton:hover,.Button.BorderButton.NoBorder:hover{color:#00b7f3;background-color:transparent !important}.Button.NoBorder,.SpeedButton.Button{padding:0 2px;box-shadow:none;background-color:transparent !important}.Button.PrimaryButton{color:#fff;background-color:#337ab7;border-color:#337ab7}.Button.PrimaryButton:hover{background-color:#286090;border-color:#286090;color:#fff}.Button.PrimaryButton.disabled,.PrimaryButton.disabled:hover{color:#eee;background-color:#7fa1bd;border-color:#8db3d4}.Button.SuccessButton.rightHover.HasLine,.Button.hasRight.SuccessButton.ShowDrop,.Button.SuccessButton{color:#fff;background-color:#0ebe5c;border-color:#0ebe5c}.Button.SuccessButton:hover,.SuccessButton:hover{background-color:#399239;border-color:#399239;color:#fff}
.Button.SuccessButton.disabled,.SuccessButton.disabled:hover{color:#eee;background-color:#89da89;border-color:#7fd47f}.Button.SuccessButton.HasLine .RightIcon{border-left-color:#68d36c}.Button.SuccessButton.HasLine:hover .RightIcon{border-color:#0ebe5c;background-color:#0ebe5c}.Button.SuccessButton.HasLine .RightIcon:hover{background-color:#399239;border-color:#399239}.Button.InfoButton{color:#fff;background-color:#5bc0de;border-color:#46b8da}.Button.InfoButton:hover,.InfoButton:hover{background-color:#31b0d5;border-color:#31b0d5;color:#fff}.Button.InfoButton.disabled,.InfoButton.disabled:hover{color:#eee;background-color:#99cbda;border-color:#99cbda}.Button.WarnButton{color:#fff;background-color:#f1ad28;border-color:#f1ad28}.Button.WarnButton:hover,.WarnButton:hover{background-color:#ce7a0a;border-color:#ce7a0a;color:#fff}.Button.WarnButton.disabled,.WarnButton.disabled:hover{color:#eee;background-color:#ecc894;border-color:#ecc894}.Button.DangerButton{color:#fff;background-color:#e85854;border-color:#e85854}
.Button.DangerButton:hover,.DangerButton:hover{background-color:#c9302c;border-color:#c9302c;color:#fff}.Button.DangerButton.disabled,.DangerButton.disabled:hover{color:#eee;background-color:#f1a2a0;border-color:#f1a2a0}.Button.HasLoading{padding:0 24px}.Button .LoadingFor{position:absolute;right:-5px;top:0}.Button .ProgressBar{position:absolute;left:0;top:0;width:100%;height:100%;z-index:0;opacity:.5;border-radius:0;border-width:0}.Button .ProgressBar .ProgressValue{border-radius:0}.SwitchButton.hasText{width:auto;padding:0 10px;height:22px;line-height:22px;border-radius:12px;text-align:left;background-color:#ffedd2;color:#fa7e00}.SwitchButton.hasText .SwitchBlock{background-color:#fa9c00;left:3px;top:2px;margin:0 !important}.SwitchButton.hasText .SwitchText{text-align:left;padding-left:15px}.SwitchButton.hasText.checked{background-color:#ddf1dc;color:#3ba33d}.SwitchButton.hasText.checked .SwitchBlock{background-color:#2ca72e;left:3px;margin-left:0}.SwitchButton{color:#fff;border:0;width:44px;min-width:44px;height:22px;line-height:22px;border-radius:12px;background-color:#bfbfbf;cursor:pointer;position:relative;display:inline-block}
.SwitchButton .SwitchBlock{border:0;left:2px;top:2px;width:18px;height:18px;border-radius:50%;background-color:#fff;position:absolute;transition:all .2s linear;box-sizing:content-box}.SwitchButton.ShowButton,.SwitchButton.ShowButton:hover{width:auto;min-width:auto}.SwitchButton.ShowButton .SwitchText{margin-left:23px;margin-right:8px}.SwitchButton.ShowButton.checked .SwitchText{margin-left:8px;margin-right:23px}.FormBorder .SwitchButton.noText{width:32px;min-width:32px;height:16px;line-height:16px}.FormBorder .SwitchButton.noText .SwitchBlock{top:1.5px;width:13px;height:13px}.FormBorder .SwitchButton.noText.checked .SwitchBlock{margin-left:-15px}.SwitchButton.checked{background-color:#2288fc}.SwitchButton.checked:hover{background-color:#54a4ff}.SwitchButton.checked .SwitchBlock{left:100%;margin-left:-20px}.SwitchButton.disabled{background-color:#ddd;cursor:not-allowed}.SwitchButton.checked.disabled{background-color:#54a4ff}.SwitchButton.disabled .SwitchBlock{background-color:#f2f2f2}.SwitchButton.readonly{cursor:default}
.SwitchButton.Strong{min-width:60px;width:60px;height:30px;line-height:30px;border-radius:20px}.SwitchButton.Strong .SwitchBlock{width:22px;height:22px;border-radius:11px;top:4px;left:4px}.SwitchButton.Strong.checked .SwitchBlock{left:100%;margin-left:-25px}.Grid .SwitchButton{vertical-align:middle}.BadgeButton{position:relative;overflow:visible}.BadgeButton.Button{margin-right:18px}.BadgeButton .BadgeValue{z-index:1;position:absolute;top:-15px;left:90%;border-radius:10px;padding:0 4px;text-align:center;line-height:20px;color:#fff;background-color:#fb7070;cursor:default}.BadgeButton .BadgeDot{z-index:1;position:absolute;top:-6px;right:-6px;border-radius:4px;width:8px;height:8px;background-color:#fb7070}.BadgeButton.disabled .BadgeValue,.BadgeButton.disabled .BadgeDot{background-color:#f9a7a7}.Buttons{display:flex;flex-wrap:wrap}.Buttons .Button{margin:0 6px 0 0;float:left}.Buttons .Button:last-child{margin-right:0}.HorzButtons .Button{float:left}.HorzButtons .Button,.VertButtons .Button{margin:0;border-radius:0;border:0;height:28px;line-height:28px;padding:0 10px}
.VertButtons{width:90px}.VertButtons .Button{clear:both;display:block;width:100%;padding:0}.Selector.Buttons .Button span,.Normal.Buttons .Button.active:after{position:absolute;right:-3px;bottom:0;font-family:"iconfont" !important;font-size:18px;font-style:normal;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:inherit;speak:none;font-weight:bold}.Normal.Buttons,.Selector.Buttons{background-color:#fff}.Border.Buttons{border:0}.Buttons .Button.active{border-color:#2288fc;background-color:#2288fc;color:#fff}.Border.Buttons .Button.active,.Normal.Buttons .Button.active{border-color:#2288fc;background-color:#fff;color:#2288fc;position:relative}.Normal.Buttons .Button.active:after{position:absolute;right:0;bottom:0;margin:0;z-index:2;content:"\e78a";color:#fff;font-size:11px;font-family:"iconfont" !important;width:0;height:0;border-color:transparent transparent #2288fc transparent;border-style:solid;border-width:0 0 16px 16px;display:flex;justify-content:right;padding-bottom:1px;line-height:25px}
.TabNormal.Buttons{background-color:#fff;border-bottom:1px solid #eee;padding-left:10px}.TabNormal.Buttons .Button{border:0;border-bottom:2px solid #fff;border-radius:0;height:40px;line-height:40px;margin:0;background-color:#fff;padding:0 15px;color:#333}.TabNormal.Buttons .Button:hover,.TabNormal.Buttons .Button.active{border-bottom:2px solid #2288fc;background-color:#fff;box-shadow:none;color:#2288fc}.NoBorder.Buttons{background-color:transparent}.NoBorder.Buttons .Button{border:0;border-radius:0;margin:0;padding:0 6px;color:#333;background-color:transparent}.NoBorder.Buttons .Button:hover,.NoBorder.Buttons .Button.active{box-shadow:none;color:#2288fc;background-color:transparent}.Buttons .Button.active.disabled{background-color:#2288fc !important;border-color:#2288fc !important}.Buttons.Selector .Button{display:flex;position:relative;overflow:visible;margin-right:10px}.Buttons.Selector .Button:after{content:"\e616";font-family:"iconfont";margin-left:5px;font-size:16px;font-weight:normal;position:absolute;right:-8px;top:-8px;background-color:rgb(0 0 0 /0.5);color:#fff;border-radius:50%;width:16px;height:16px;line-height:16px}
.Buttons.Selector.HoverRemove .Button:after{display:none}.Buttons.Selector .HideRemoveIcon.Button:after{display:none !important}.Buttons.Selector.HoverRemove .Button:hover::after{display:block}.ButtonEdit.Buttons{border-right:0;height:auto;min-height:30px;display:flex;text-indent:0;overflow:hidden;flex-wrap:nowrap}.ButtonEdit.Buttons.Wrap{flex-wrap:wrap}.ButtonEdit.Buttons .Button{cursor:default;position:relative;margin:3px 0 auto 5px;background-color:#ecf5ff;line-height:22px;height:22px;padding-right:25px;padding-left:5px;color:#56a5ff;border-color:#cae4ff;border-width:1px}.ButtonEdit.Buttons.ReadOnly .Button{padding-right:5px}.ButtonEdit.Buttons.ReadOnly .Button span{display:none}.Labels.EditBlock .ButtonEdit.Buttons{border:0;background-color:transparent}.Labels.EditBlock .SkinButton{display:none}.Normal.Labels.EditBlock .Button,.Normal.Labels.EditBlock .Button:hover{background-color:transparent;border:0;margin:0;align-self:center;color:inherit;padding:0}.alignNormal{align-self:normal}
.AlignNormal{align-items:normal}.Normal.Labels.EditBlock .Button:first-child:before{display:none}.Normal.Labels.EditBlock .Button:before{content:'、';margin:0}.Buttons.Multi{padding:3px 0}.Buttons.Multi .Button{margin-top:3px;margin-bottom:3px}.Buttons.Group.FlexAuto .Button{flex:auto;padding:0 3px}.Buttons.Group .Button{margin:0;line-height:26px;border-color:#e1e1e1;margin-left:-1px;border-radius:0}.Buttons.Group .Button:hover{border-color:#e1e1e1}.Buttons.Group .Button:first-child{margin-left:0;border-radius:4px 0 0 4px}.Buttons.Group .Button:last-child{border-radius:0 4px 4px 0}.Buttons.Group .Button.active{background-color:transparent;border-color:#2288fc;color:#2288fc;z-index:2}.ButtonEdit.Buttons .Button:hover{background-color:#57b9ff;color:#fff}.ButtonEdit.Buttons .Button span{top:2px;right:4px;width:16px;height:16px;line-height:16px;overflow:hidden;border-radius:8px;cursor:pointer;z-index:2}.ButtonEdit.Buttons .Button span:after{content:"\e616";position:absolute;left:-1px;top:0}
.ButtonEdit.Buttons .Button span:hover{background-color:#fafafa;color:#2288fc}.ButtonEdit.Buttons.disabled .Button{padding:0 10px;background-color:#eee;color:#aaa;border-color:#ccc;cursor:not-allowed}.ButtonEdit.Buttons.disabled .Button span{display:none !important}.ButtonEdit.Buttons.Selector .Button:after{display:none}.EditBlock .ArrowCombo.aicon-down-circle{color:#aaa;width:32px}.Buttons.Vert{flex-direction:column;display:flex}.Buttons.Vert .Button{border:0;border-bottom:1px solid #ccc;border-radius:0;line-height:36px;height:36px}.ImageCell{text-align:center;min-height:16px}.Button.hasRight{padding-right:0 !important}.Button .RightIcon{width:30px;display:inline-flex;justify-content:center;vertical-align:top;align-items:center;margin-left:5px;text-align:center}.Button.hasClick .RightIcon:hover{color:#2288fc;background-color:#eff6fe;background-color:var(--lightColor)}.Button.disabled .RightIcon:hover{background-color:#efefef;color:#bbb}.Button.hasRight.HideRightIcon{border-right-width:1px;padding-right:5px !important}
.Button.hasRight.HideRightIcon .RightIcon{display:none}.Button.HasLine .RightIcon{position:relative;margin-left:14px;border-radius:0 2px 2px 0}.Button.HasLine .RightIcon:after,.CombEditor .Button:after{content:" ";border-left:1px solid #ddd;position:absolute;top:0;left:0;height:100%}.Button.SpecialButton.HasLine .RightIcon:after,.CombEditor .Button.SpecialButton:after{border-color:#0978f5}.Button.SpecialButton.HasLine:hover .RightIcon:after{border-color:#54a4ff}.Button.SpecialButton.HasLine .RightIcon{background-color:#2288fc}.Button.SpecialButton.ShowDrop .ArrowCombo:before,.Button.SuccessButton.ShowDrop .ArrowCombo:before,.Button.SpecialButton .ArrowCombo,.Button.SuccessButton .ArrowCombo{color:#fff}.Button.SpecialButton.hasClick .RightIcon:hover{background-color:#0978f5;color:#fff}.Button.SuccessButton.hasClick .RightIcon:hover{background-color:#399239;color:#fff}.CombEditor{border:1px solid #e1e1e1;border-radius:4px;position:relative;overflow:hidden;display:flex}
.CombEditor .EditBlock{margin:0 !important}.CombEditor .EditBlock .SkinButton{border:0;width:18px}.CombEditor .Edit{border:0;text-indent:6px;margin:0}.CombEditor .Button{position:relative;background-color:#fff;padding:0 5px;color:#333;border-radius:0;border-width:0;text-indent:0}.CombButton{display:flex}.CombButton .Button{display:inline-block;vertical-align:top;position:relative;z-index:1}.CombButton .Button:hover{z-index:2}.CombButton .Button.hasRight{border-top-right-radius:0;border-bottom-right-radius:0}.CombButton .Button.hasRight.SpecialButton{border-right-color:rgba(255,255,255,0.3)}.CombButton .CombRightButton{padding:0 5px;min-width:30px;border-top-left-radius:0;border-bottom-left-radius:0;margin-left:-1px}.CombButton .CombRightButton.SpecialButton{border-left-color:#0978f5}.CombButton .CombRightButton .RightIcon{padding:0;margin:0}.CombButton .CombRightButton.hasRight{padding:0}.CombButton .Button.CombRightButton.ShowDrop{border-color:#ccc}.CombButton .CombLeftButton{border-top-right-radius:0;border-bottom-right-radius:0}
.CombButton .CombRightButton:focus{box-shadow:none}.Button.SpecialButton .RightIcon.enabled{color:#fff}.Button .RightIcon.enabled{background-color:#fff;cursor:pointer;color:#333}.Edit{margin:0;text-indent:6px;padding:0;width:120px;min-width:10px;height:28px;line-height:28px;text-align:left;border:1px solid #e1e1e1;background-color:#fff;text-overflow:ellipsis}.ColorEdit{padding:3px;line-height:22px;background-clip:content-box}.TextArea{width:118px;padding:5px;height:120px;border:1px solid #e1e1e1;text-indent:0;flex-shrink:0;background-color:#fff;overflow:auto;line-height:24px}.FlexBlock>.TextArea{display:flex;width:auto;height:auto;flex-shrink:1;flex-grow:1;flex-basis:auto}.EditBlock .Edit:hover{border-color:#c9c9c9}.Edit.vert{height:56px;line-height:26px}.TextArea.FlexAuto{flex-grow:1 !important}.EditBlock.HasRequire .Edit,.EditBlock.HasRequire .SkinButton,.Edit.HasRequire,.HtmlEditor.HasRequire,.TextArea.HasRequire,.FileEditor.HasRequire,.CombEditor.HasRequire,.Edit.HasRequire:focus{border-color:#e43a1c !important}
.ArrowCombo{color:#333}.ArrowCombo:before,.aicon-moreunfold:before,.aicon-jiantou2:before,.aicon-more:before{-webkit-transition:all .1s linear;-moz-transition:all .1s linear;transition:all .1s linear}.ShowDrop .ArrowCombo:before,.ShowPopup .ArrowCombo:before,.ShowPopup.ArrowCombo:before{transform:rotate(180deg);color:#2288fc;display:block}.NBIcon.expand:before{transform:rotate(90deg);margin-top:-2px;display:block}.Edit.hover,.Edit:hover,.TextArea:hover,.EditBlock:hover .SkinButton,.EditBlock:hover .Edit,.EditBlock:hover .SkinButtonBlock{border-color:#00b7f3}.TextEdit:focus,.TextArea:focus,.EditBlock.active .SkinButton,.EditBlock.active .Edit,.EditBlock.active .SkinButtonBlock{border-color:#00b7f3}.TextEdit:focus,.TextArea:focus,.EditBlock.active{box-shadow:0 0 0 2px rgba(34,136,252,0.1)}.TextEdit.NoBorder:focus,.TextArea.NoBorder:focus,.EditBlock.NoBorder.active{box-shadow:none}.EditBlock .SkinButton:hover{color:#2288fc}.EditBlock[class*="icon-"]:before{position:absolute;top:1px;left:1px;width:20px;background-color:#eee;line-height:26px;text-align:center}
.EditBlock[class*="icon-"] .Edit{padding-left:20px}div.EditBlock{display:inline-flex;line-height:normal;position:relative;overflow:hidden}.EditBlock .Edit,.Grid .EditBlock .ButtonEdit{border-top-right-radius:0;border-bottom-right-radius:0}.EditBlock textarea,.EditBlock input{border-right-width:0;display:inline-block}.EditBlock textarea{text-indent:0;padding-left:5px;overflow:hidden}.EditBlock .SkinButton{flex-shrink:0;width:23px;border:1px solid #e1e1e1;text-align:center;border-left-width:0;justify-content:center;margin:0;cursor:pointer;display:inline-flex;align-items:center;background-color:#fff}.EditBlock .SkinButtonBlock{overflow:hidden;border:1px solid #e1e1e1;border-left-width:0;display:flex;flex-direction:column;justify-content:center;background-color:#fff;border-radius:0 4px 4px 0}.EditBlock .SkinButtonBlock .SkinButton{border:0;display:block;width:22px;line-height:10px}.EditBlock .SkinButtonBlock .SkinButton:hover{background-color:#eee}.EditBlock .SkinButtonBlock .SkinButton:before{font-size:12px;line-height:11px;font-weight:normal}
.Edit.disabled,.Edit.disabled:hover,.TextArea.disabled,.TextArea.disabled:hover,.EditBlock.disabled,.EditBlock.disabled:hover,.EditBlock.disabled input,.EditBlock.disabled .SkinButtonBlock,.EditBlock.disabled .SkinButton,.EditBlock.disabled:hover .SkinButton,.EditBlock.disabled:hover .Edit,.EditBlock.disabled:hover .SkinButtonBlock{box-shadow:none;border-color:#eee !important;background-color:#f6f6f6;color:inherit;cursor:not-allowed}.EditBlock.disabled .SkinButtonBlock .SkinButton,.EditBlock.disabled:hover .SkinButtonBlock .SkinButton{background-color:inherit}.EditBlock.stepBlock{border:1px solid #e1e1e1;border-radius:4px}.EditBlock.stepBlock input{text-align:center;text-indent:0;border-radius:0;border:0}.EditBlock .SkinButtonBlock .SkinButton:before{font-size:13px}.EditBlock .SkinButton.stepNum{background-color:#fafafa;color:#666;border:0}.EditBlock .SkinButton.icon-text-button{width:38px;background-color:#f5f5f5;border:1px solid #ddd;cursor:default;justify-content:center;align-items:center}
.EditBlock .SkinButton.icon-text-button .icon-text-display{padding:0 6px;line-height:inherit;font-size:12px;color:#666;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ImageEdit{display:flex;align-items:center;justify-content:center;border-right-width:0}.requiredIcon{position:relative}.requiredIcon:before{position:absolute;right:22px;color:#00b7f3;top:0;line-height:26px}.FileEditor{height:28px;width:118px;line-height:28px;overflow:hidden;text-indent:5px;position:relative;color:#999;background:#fcfcfc;border:1px solid #e1e1e1;overflow:hidden;display:inline-flex}.FileEdit{width:220px;height:28px;opacity:0;filter:alpha(opacity=0);padding:0;font-size:0;flex-shrink:0}.FileEditor input{cursor:pointer;position:absolute;right:0;top:0;z-index:1;opacity:0;filter:alpha(opacity=0);width:100%;left:0}.FileEditor:hover{color:#444;background:#eee;border-color:#ccc;text-decoration:none}.FileEditor .FileButton{flex-shrink:0;padding:0 5px;color:#444}.FileEditor .FileName{flex:auto}
.SoftKeyboardPopup{border-spacing:1px}.SoftKeyboardButton{width:40px;height:22px;text-align:center;font-size:14px;font-family:"Arial";cursor:pointer}.SoftKeyboard_Number{background:#fafafa}.SoftKeyboard_Letter{background:#e1e1e1}.SoftKeyboard_Symbol{background:#ccc}.SoftKeyboard_FN{font-size:12px;background:#cce4ee}.SoftKeyboard_OK{font-size:12px;background:#cce4ee}.SoftKeyboard_Line{height:3px;line-height:0;font-size:0;overflow:hidden}.SoftKeyboardButton:hover{background:#00b7f3;color:#fff}.SoftKeyboard_Line:hover{background:0}.CalculatorPopup{font-size:18px;border:0;position:absolute;height:184px;width:200px;overflow:hidden;left:-1000px;top:-1000px;display:inline-flex;text-align:center}.CalculatorPopup .Btn{text-align:center;padding:0;width:50px;height:36px;line-height:36px;border-right:1px solid #eff2f5;border-bottom:1px solid #eff2f5;float:left;margin:0;cursor:pointer;overflow:hidden;color:#363636}.CalculatorPopup .Btn.opt{font-weight:bold;background-color:#f5f5f5;color:#2288fc}
.CalculatorPopup .Btn.equalsign{height:74px;background-color:#2288fc;color:#fff;line-height:74px}.CalculatorPopup .Btn.aicon-qingchu:before{font-size:20px}.CalculatorPopup .Btn:hover{background-color:#2288fc;color:#fff}.CalculatorPopup .ctorLeft{width:150px;flex-shrink:0;display:flex;flex-wrap:wrap}.CalculatorPopup .ctorRight{width:50px}.CalculatorPopup .ctorRight>*{float:none;display:block;color:#2288fc;background-color:#f5f5f5}.CalculatorHeader{height:40px;border:1px solid #eee;border-radius:6px;margin:5px;background-color:#fafafa;line-height:20px;text-align:right;padding:0 5px}.MenuBar{background-color:#374979;height:36px;line-height:36px;color:#fff;overflow:hidden;text-align:left;flex-shrink:0;display:flex;overflow:hidden}.MenuBar .MenuRoots{display:flex;overflow:hidden;flex-grow:2}.MenuBar .MenuRoot{white-space:nowrap;padding:0 8px;flex-shrink:0;display:flex;align-items:center}.MenuBar .MenuRoot.disabled,.MenuBar .MenuRoot.disabled:hover{background-color:inherit;cursor:not-allowed;color:#a0a0a0}
.MenuBar .MenuRootImage{display:inline-block;text-align:center}.MenuBar .MenuRootImage:before{width:30px}.MenuBar .MenuRootImage img{margin-right:8px}.MenuBar .MenuRootCaption{display:inline-block}.MenuBar .MenuRoot.aicon-children:after{content:"\e9ff";display:inline-block;margin-left:3px;font-size:12px}.MenuBar .MenuRootActive,.MenuBar .MenuRoot:hover{background-color:#00b7f3;color:#fff}.MenuBar.MenuItemVert{height:auto;line-height:normal}.MenuBar.MenuItemVert .MenuRoot{text-align:center;padding:5px 18px 5px 18px;flex-direction:column}.MenuBar.MenuItemVert .MenuRootImage{width:auto;height:24px;margin-bottom:3px;display:block}.MenuBar.MenuItemVert .MenuRootImage:before{margin:0}.MenuBar.MenuItemVert .MenuRoot .MenuRootCaption{display:block}.MenuBar.MenuItemVert .MenuRoot.aicon-children:after{display:none}.MenuBar.vert{flex-direction:column;flex-basis:auto;flex-shrink:0;flex-grow:0;height:auto;line-height:46px}.MenuBar.vert .MenuRoots{flex-direction:column}
.MenuBar.vert .MenuRoot{border-bottom:1px solid #666;padding:0;display:flex}.MenuBar.vert .MenuRootCaption{flex-shrink:1;flex-grow:1;flex-basis:auto}.MenuBar.vert .MenuRootImage{width:32px;text-align:center;align-self:center}.MenuBar.vert .MenuRootImage:before{margin:0}.MenuBar.vert .MenuRoot.aicon-children:after{content:"\e7b8";margin:0 5px 0 0;float:right}.MenuBar.vert.MenuItemVert .MenuRoot{border-bottom:1px solid #136fb7;line-height:30px;text-align:center;padding:10px 0}.MenuBar.vert.MenuItemVert .MenuRootImage{width:auto;display:block;margin:0}.MenuBar.vert.MenuItemVert .MenuRoot .MenuRootCaption{display:block}.MenuBar.vert.MenuItemVert .MenuRoot.aicon-children:after{display:none}.MenuBar.hasScroll{flex-shrink:1;overflow:hidden}.MenuBar.CFlatMenuBar{background-color:inherit;border:0;color:inherit}.MenuBar.TreeBar{overflow:auto;flex-direction:column;overflow-x:hidden}.MenuBar.TreeBar .MenuRoots{overflow:initial}.BrowserIE .MenuBar.TreeBar .MenuRoots{overflow:visible}
.MenuBar.TreeBar .Menu{position:inherit;background-color:#2288fc;box-shadow:none;border:0;flex-direction:column;flex-shrink:0;border-radius:0}.MenuBar.TreeBar .Menu .MenuImage{margin-left:10px}.MenuBar.TreeBar .Menu .Menu .MenuImage{margin-left:20px}.MenuBar.TreeBar .Menu .Menu .Menu .MenuImage{margin-left:40px}.MenuBar.TreeBar .Menu .ChildBlock{border-right:0}.MenuBar.TreeBar .MenuSeperator{background-color:#3889d0}.MenuBar.TreeBar .MenuRoot.aicon-children:after{content:"\e9ff";margin-right:10px}.MenuBar.TreeBar .MenuRoot.Show.aicon-children:after{content:"\e6a5"}.MenuBar.TreeBar .MenuItem .aicon-children:before{content:"\e9ff";margin-right:10px}.MenuBar.TreeBar .MenuItem.Show .aicon-children:before{content:"\e6a5"}.MenuBar.TreeBar .MenuRoot:hover,.MenuBar.TreeBar .MenuItem:hover,.MenuBar.TreeBar .MenuItem.active{background-color:#00b7f3;color:#fff}.Menu{display:flex;top:-10000px;left:-1000px;position:absolute;flex-direction:column;flex-wrap:wrap;background-color:#fefefe;border:1px solid #eee;white-space:nowrap;cursor:default;box-shadow:1px 1px 5px #ccc}
.RootMenu.ShowMenuPopup{max-height:98%;max-width:98%}.Menu.Horz{flex-direction:row}.RootMenu.Menu.Horz.GroupHorz{flex-direction:row}.Menu .ChildBlock{border-right:1px solid #dedede;display:flex;flex-direction:column}.Menu .MenuItem{white-space:nowrap;line-height:34px;height:34px;overflow:hidden;display:flex;flex-shrink:0}.Menu .MenuItem.selected{font-weight:bold}.Menu .MenuItem>*,.Menu .MenuItem:before{float:left}.Menu .MenuItem:hover{background-color:#00b7f3;color:#fff}.Menu .MenuItem.disabled,.Menu .MenuItem.disabled:hover,.Menu .MenuItem.disabled:hover .MenuShortcut{color:#a0a0a0;background-color:inherit !important;cursor:not-allowed}.Menu .MenuImage{width:30px;height:inherit;text-align:right;display:flex;align-items:center;justify-content:center}.Menu .MenuCaption{padding:0 5px;text-align:left;flex-shrink:1;flex-grow:1;flex-basis:auto}.Menu .MenuMore,.Menu .MenuNoMore{width:30px;text-align:center}.Menu .MenuMore.aicon-children:before{content:"\e7b8";font-size:13px}
.Menu .MenuShortcut{padding-right:5px;float:right;color:#999}.Menu .MenuSeperator{background-color:#e1e1e1;height:1px}.Menu .MenuItem.MenuText{font-size:14px;cursor:default;font-weight:500;color:#2288fc;color:var(--mainColor)}.Menu .MenuItem.MenuText .MenuNoMore{display:none}.Menu .MenuItem.MenuText .MenuImage{width:2px}.Menu .MenuItem.MenuText .MenuImage:before{display:none}.Menu .MenuItem.MenuText:hover{background-color:inherit;color:#00b7f3}.RootMenu.hasArrow{margin-top:16px}.RootMenu.hasArrow.vert{margin-left:22px;margin-top:0}.RootMenu.hasArrow .Arrow{content:" ";display:inline-block;width:14px;height:14px;position:absolute;background-color:#fff;box-shadow:-5px 5px 6px #eee;cursor:default;left:10%;top:-6px;transform:rotate(135deg)}.RootMenu.hasArrow.vert .Arrow{left:-7px;top:20px;transform:rotate(45deg)}.CToolMenuBar.MenuBar{background:#fff;height:28px;line-height:28px;color:inherit;border:1px solid #ddd}.CToolMenuBar .MenuRootActive{background-color:#00b7f3}
.CToolMenu.Menu{border:1px solid #ddd;border-top:2px solid #00b7f3;background-color:#fbfbfb}.CToolMenu.Menu .MenuItem.disabled,.CToolMenu.Menu .MenuItem.disabled .MenuCaption{background-color:inherit;color:#a0a0a0}.NormalMenu.Menu{text-align:left;overflow:hidden}.NormalMenu.Menu .MenuItem .MenuImage img{max-height:22px;margin-left:5px}.PrintMenu.Menu{min-width:120px}.PrintMenu.Menu .MenuItem .MenuImage{width:30px}.PrintMenu.Menu .MenuItem .MenuImage img{height:28px;margin-top:3px}.MDIDocument,.MDIDocument body{overflow:hidden}.MDIDocument body{display:flex !important}.MDIPanel{width:auto;overflow:hidden;flex-shrink:1;flex-grow:1;flex-basis:auto;display:flex;flex-direction:column;margin:0 !important}.MDIBar{display:flex;overflow:hidden;cursor:default;flex-shrink:0;height:36px;line-height:36px;position:relative}.MDITabCaptions{display:flex;flex:auto;overflow:hidden}.MDITabCaption{flex-shrink:0;float:left;border:1px solid #e6e6e6;border-bottom:0;border-radius:4px 4px 0 0;margin-right:4px;display:inline-flex;position:relative;cursor:default;padding:0 22px;color:#313131;background-color:#fff;font-size:14px}
.MDITabCaption.active{z-index:1;background-color:#fafafa;color:#2288fc;color:var(--mainColor);border-color:#d9d9d9}.MDITabCaption:hover{color:#2288fc;color:var(--mainColor);background-color:#dde5ff}.MDITabCaption:before{font-weight:normal}.MDITabCaption .TabCloseButton{cursor:pointer;text-align:center;display:none;color:#666;position:absolute;top:50%;width:18px;height:18px;line-height:18px;margin-top:-10px;right:3px;color:#9d9d9d;font-weight:bold}.MDITabCaption .TabCloseButton:hover{color:#1351ca}.MDITabCaption .TabCloseButton:before{font-size:12px}.MDITabCaption.active .TabCloseButton,.MDITabCaption:hover .TabCloseButton{display:block}.MDINavIcons{align-self:flex-end;flex:100 0 auto}.MDIChilds{clear:both;overflow:hidden;display:flex;flex-grow:1;flex-shrink:0;flex-basis:0px;position:relative}.MDIChild{flex-grow:1;flex-shrink:0;flex-basis:0;display:flex;overflow:auto}.MDIChild>.PageBlock{padding:5px;overflow:hidden;flex-basis:0;min-height:350px}
.MDIInnerBlock{padding:0 2px 0 2px;display:flex;align-items:center;flex-shrink:0}.MDITabCaption.aicon-suoding1{padding-left:4px}.MDITabCaption.aicon-suoding1:before{margin-right:2px}.MDINavIcons .Button{margin:2px 3px}.InsertSpacer{transition:width .2s linear;border:1px dashed #ccc;border-radius:4px 4px 0 0;margin:0 5px;flex-shrink:0}.notrans{transition:none}.minH400{min-height:400px}.minH450{min-height:450px}.minH500{min-height:500px}.minH550{min-height:550px}.minH600{min-height:600px}.PageControl{display:flex;flex-grow:1;flex-basis:auto;flex-shrink:1;flex-direction:column;overflow:hidden}.TabHeader{position:relative;display:flex;align-items:center;flex-shrink:0;line-height:30px}.TabHeader .TabCaptions{overflow:hidden;flex-shrink:1;flex-grow:1;flex-basis:auto}.CaptionLeft .TabHeader .TabCaptions{flex-grow:0}.CaptionLeft .TabHeader .TabSpace,.CaptionLeft .TabHeader .TabContainer{flex-grow:1}.TabHeader .TabCaption{border:1px solid #ddd;border-bottom-width:0;display:inline-flex;background-color:#fafafa;float:left;margin:0 5px 0 0;padding:0 10px;cursor:pointer;border-radius:4px 4px 0 0}
.TabHeader .TabCaption .Button{margin-right:5px}.TabHeader .TabCaptionText{display:inline-block;padding:0 3px}.TabHeader .TabCaption:hover{border-color:#00b7f3;background-color:#00b7f3;color:#fff}.TabHeader .TabCaption.active{border-color:#2288fc;background-color:#2288fc;color:#fff}.TabHeader .TabSpace{float:right;overflow:hidden;align-items:center;display:flex}.TabContainer{overflow:hidden;display:flex;margin-left:10px;flex-shrink:0;align-items:center}.TabContainer .ChkBoxStyle{height:22px;line-height:22px}.TabContainer .ChkBoxStyle input{left:0;top:3px}.TabContainer .Button,.TabContainer .Edit,.TabContainer .Label{height:24px;line-height:24px;margin-top:0}.TabContainer .LayoutItemControl .RadioButtonList{margin-top:0}.TabContainer .LayoutItemControl .RadioButtonList.Border{margin-top:0}.TabBody{border:1px solid #ddd;clear:both;min-width:100px;overflow:hidden;position:relative}.TabPage{padding:10px;overflow:auto}.TabPage.Hided{position:absolute !important;left:-10000px;top:-10000px;width:100%;height:100%}
.TabBodyNoHeader{border:0;overflow:hidden}.BrowserIE .TabPage{overflow-x:hidden}.PageControl.Normal .TabCaptions{height:30px;line-height:30px}.PageControl.Normal .TabHeader{border:0}.PageControl.Normal .TabHeader .TabCaption.active{color:#fff}.PageControl.Normal .TabCaption{background-color:#fff;border-color:#fff;margin:0 2px 0 0}.PageControl.Normal .TabCaption:hover{color:#fff;background-color:#00b7f3}.PageControl.Normal .TabCaption.active{background-color:#2288fc;border-color:#2288fc}.TabRadius>.TabHeader .TabCaption{border-radius:6px 6px 0 0}.TabRadius.TabBottom>.TabHeader .TabCaption{border-radius:0 0 6px 6px}.TabRadius.TabLeft>.TabHeader .TabCaption{border-radius:6px 0 0 6px}.TabRadius.TabRight>.TabHeader .TabCaption{border-radius:0 6px 6px 0}.TabBottom>.TabHeader{align-items:flex-start}.TabBottom>.TabHeader .TabCaption{border-top-width:0;border-bottom-width:1px}.TabLeft{flex-direction:row}.TabLeft>.TabHeader{align-items:flex-start;display:flex;border:0}.TabLeft>.TabHeader .TabSpace{display:none}
.TabLeft>.TabHeader .TabCaptions{display:flex;flex-direction:column;height:auto;padding-left:0}.TabLeft>.TabHeader .TabCaption{margin:5px 0;border-right:0;background-color:#fafafa}.TabLeft>.TabBody{clear:inherit}.PageControl.TabVert>.TabHeader{width:30px;height:auto;padding-top:5px;border:0}.PageControl.TabVert>.TabHeader .TabCaptions{width:30px}.PageControl.TabVert>.TabHeader .TabCaption{padding:5px 0;border:1px solid #dee6ee;border-right:0}.PageControl.TabVert>.TabHeader .TabCaption:hover{color:#333;background-color:#eee}.PageControl.TabVert>.TabHeader .TabCaption.active{background-color:#2288fc;color:#fff;border-color:#2288fc}.PageControl.TabVert>.TabHeader .TabCaptionText{width:12px;margin:auto;text-align:center;line-height:20px;padding:0}.TabRight{flex-direction:row-reverse}.TabRight>.TabHeader{align-items:flex-start;display:flex}.TabRight>.TabHeader .TabSpace{display:none}.TabRight>.TabHeader .TabCaptions{display:flex;flex-direction:column;height:auto;padding-left:0}.TabRight>.TabHeader .TabCaption{margin:5px 0;border-left:0;border-bottom-width:1px}
.TabRight>.TabBody{clear:inherit}.PageControl.MaxTab>.TabHeader{line-height:46px}.PageControl.MaxTab>.TabHeader .TabCaptions{padding-left:10px;margin-bottom:0}.FormBorder .PageControl.MaxTab>.TabHeader .TabCaptions{padding-left:20px}.PageControl.MaxTab>.TabHeader .TabCaption{font-size:14px;margin-right:40px;padding:0;border:0;border-radius:0;border-bottom:2px solid #fff;background-color:transparent}.PageControl.MaxTab>.TabHeader .TabCaption.active{border-color:#2288fc;color:#2288fc}.PageControl.MaxTab2>.TabHeader{line-height:30px;padding-top:10px}.PageControl.MaxTab2>.TabHeader .TabCaptions{padding-left:10px}.PageControl.MaxTab2>.TabHeader .TabCaption{background-color:#fff;margin:0 4px 0 0;font-size:13px}.PageControl.MaxTab2>.TabHeader .TabCaption.active{background-color:#2288fc;border-color:#2288fc;color:#fff}.PageControl.FlexTab>.TabHeader .TabCaptions{justify-content:space-around;display:flex}.PageControl.FlexTab>.TabHeader .TabCaption{margin:0 5px 0 0;flex:auto;justify-content:center}
.PageControl.FlexTab>.TabHeader .TabCaption:last-child{margin-right:0}.FormBorder{background-color:#fff;min-width:380px;position:absolute;left:40%;top:-20000px;display:flex;flex-direction:column;box-shadow:0 0 10px #444;max-height:100%;max-width:100%}.FormBorder.HideCaption .FormCaption{display:none}.FormCaption{display:flex;align-items:center;background-color:#2288fc;line-height:36px;height:36px;flex-shrink:0;color:#fff;overflow:hidden;position:relative}.FormCaption .FormCaptionText{font-size:14px;text-indent:0;padding-left:10px;padding-right:80px;white-space:nowrap;word-wrap:normal;word-break:keep-all;overflow:hidden;text-overflow:ellipsis}.FormCaption .FormCaptionText.hasSub{display:flex}.FormCaption .FormCaptionText:before{margin-right:5px}.UISkinIcon,.FormCaption .FormCaptionButton{border-radius:4px;text-align:center;width:22px;height:26px;line-height:26px;margin-right:20px}.UISkinIcon:hover,.FormCaption .FormCaptionButton:hover{color:#fff;background-color:#21c8ff;background-color:var(--hoverColor)}
.FormCaption .FormCaptionButton.max{right:42px}.FormCaption .FormCaptionButton.min1{right:42px}.FormCaption .FormCaptionButton.min2{right:77px}.FormCaption .FormCaptionButton.aicon-close:hover{background-color:#fb7070}.FormSubCaption{display:inline-block;text-indent:10px;font-size:12px;color:#eee;vertical-align:bottom}.FormContent{display:flex;flex-shrink:1;flex-grow:1;flex-basis:auto;flex-direction:column;overflow:hidden}.ErrBody{padding:10px}.FormOverlay{background-color:#333;position:absolute;left:0;top:0;width:100%;height:100%}.rangeOverlay{background-color:#fafafa}.FormEnabled{background-color:transparent}.InfoForm{border-color:#ccc;box-shadow:2px 2px 12px #444}.InfoForm .FormCaption{background-color:#eaeaea;color:#333;border-bottom:1px solid #ddd}.InfoForm .FormCaption .FormCaptionText{text-shadow:none}.FormBorder .FormContent .PageBlock,.FormBorder .FormContent .CustomBlock{padding:5px;overflow:auto}.FormBorder .FormContent .IFrameModal.PageBlock{padding:0}.FormBorder.FlowAuto .FormContent .PageBlock>.FlexBlock{overflow:inherit;flex-shrink:0}
.BrowserIE .FormBorder.FlowAuto .FormContent .PageBlock>.FlexBlock{overflow:hidden}.PageBlock.FlexScroll{padding:10px 0}.PageBlock.FlexScroll>.FlexBlock.oauto{padding:10px 20px}.FormBorder .FormContent .PageBlock>.FlexBlock.oauto{flex-shrink:1}.FormBorder.RightBottom{min-width:0;min-height:0;left:100% !important;width:100px !important;height:100px !important;overflow:hidden;opacity:.5}.FormBorder.RightTop{min-width:0;min-height:0;left:100% !important;top:-50px !important;width:100px !important;height:100px !important;overflow:hidden;opacity:.5}.FormBorder.RightTop .ResizerLine,.FormBorder.RightBottom .ResizerLine,.FormBorder.RightTop .FResizer,.FormBorder.RightBottom .FResizer,.FormBorder.RightTop .FormCaption,.FormBorder.RightBottom .FormCaption,.FormBorder.RightTop .FormContent,.FormBorder.RightBottom .FormContent{visibility:hidden}.UISkinIcon{position:absolute;right:72px;top:6px;cursor:pointer}.UISkinIcon:before{font-size:20px}.ResizeMode.FormBorder{overflow:visible}.ResizerLine{position:absolute;left:-3px;top:-3px;display:none;width:100%;height:100%;z-index:1;border-width:3px;border-style:solid;border-color:#555;background-color:rgba(255,255,255,0.2)}
.FResizer{position:absolute;display:block;width:12px;height:12px;border:0;font-size:0;overflow:hidden;background-color:Transparent;background-image:none}.FResizer.FLeftTop{left:-6px;top:-6px;z-index:3}.FResizer.FLeftBottom{left:-6px;bottom:-6px;z-index:3}.FResizer.FRightTop{right:-6px;top:-6px;z-index:3}.FResizer.FRightBottom{right:1px;bottom:1px;z-index:3;width:10px;height:10px}.FResizer.FLeft{left:-6px;top:0;width:10px;height:100%;z-index:2}.FResizer.FTop{left:0;top:-6px;width:100%;height:10px;z-index:2}.FResizer.FRight{right:-6px;top:0;width:10px;height:98%;z-index:2}.FResizer.FBottom{left:0;bottom:-6px;width:98%;height:10px;z-index:2}.FormBorder.FullWindow .ResizerLine,.FormBorder.FullWindow .FResizer{display:none}.FormBorder.FullWindow,.FormBorder.FullWindow .FormCaption{border-radius:0;box-shadow:none}.MessageBox{min-width:310px;max-width:1000px;background-color:#fff;text-align:left}.MessageBox .FormCaption{line-height:40px}.MessageBox .FormCaption .FormCaptionButton{top:8px}.MessageBox .FormContent .Button{min-width:50px;margin:0 5px;font-size:13px;padding:0 15px}
.MessageBox .FormContent{padding:0;overflow:hidden}.MessageBox .MessageContent{line-height:40px;padding:30px 30px 20px 30px;overflow:auto;display:flex;flex-grow:1;flex-basis:auto;justify-content:center}.MessageBox .MessageBoxText{font-size:14px;text-align:left;word-wrap:break-word;word-break:break-all;-ms-word-break:break-all}.MessageBox .MessageContent:before{font-size:22px;margin-right:5px;flex-shrink:0}.MessageBox .BottomButtons{padding:10px 10px 15px 15px;background-color:#efefef;border-top:1px solid #ddd;text-align:right;border-radius:0 0 6px 6px}.MessageBox .ExceptionText{white-space:pre-wrap}.MessageBox .ExceptionNo{color:#ccc}.MessageBox .PromptEdit{display:block;width:100%;margin:10px 0 0 0}.GridBlock{flex-grow:1;flex-basis:auto;flex-shrink:1;display:flex;flex-direction:column;overflow:hidden;min-height:50px}.Grid{flex-grow:1;flex-basis:100px;flex-shrink:1;display:flex;flex-direction:column;border:1px solid #ddd;overflow:hidden;position:relative}
.Grid .GridHeaderBar{overflow:hidden;flex-shrink:0;position:relative;background-color:#f2f2f2}.Grid .CaptionVert{text-align:center}.Grid .CaptionVert .GridHeaderCaption{margin:0 auto;text-align:center;white-space:inherit;word-wrap:break-word;word-break:break-all;word-spacing:10px;letter-spacing:10px;width:20px}.Grid .GridHeader{color:#3e3e3e;cursor:default;min-width:100%;table-layout:fixed}.Grid .GridHeaderRow{height:30px}.Grid .MultiHeader .GridHeaderRow{height:22px}.Grid .GridHeaderCaption{position:relative;overflow:hidden;border-color:#dee6ee;border-right-width:1px;border-right-style:solid;border-bottom-width:1px;border-bottom-style:solid}.Grid .GridHeaderCaptionText{padding:0 5px;white-space:nowrap;text-overflow:ellipsis;overflow:hidden;display:flex;align-items:center;justify-content:center}.Grid .GridHeaderCaptionText table{margin:auto}.Grid .GridHeaderCaptionText:before{margin-right:4px;font-weight:normal}.Grid .ActiveColumn{background-color:#e5e5f1;color:#000}.Grid .NoArrow{color:red;font-family:arial}
.Grid .GridBody{flex-grow:1;flex-basis:auto;flex-shrink:1;background-color:#fff;overflow:auto;color:#333}.Grid .GridBody .GridTable{table-layout:fixed;border-color:transparent;min-height:5px}.Grid.ShowSpacer .GridBody .GridTable{min-width:100%}.NoHeader .Grid .GridTable{border-top:1px solid #eee}.Grid .GridBodyRow{height:30px}.Grid.RowHeight35 .GridBodyRow{height:35px}.Grid.RowHeight40 .GridBodyRow{height:40px}.Grid.RowHeight45 .GridBodyRow{height:45px}.Grid.RowHeight50 .GridBodyRow{height:50px}.UseEven .GridBodyRow:nth-child(even)>td{background-color:#f9fafc}.Grid .GridBodyRow:hover>td,.Grid .GridBodyRow.hover>td{background-color:#eef2fe;color:#222}.Grid .GridBodyRow.active>td{background-color:#fffee4;color:#222}.Grid .GridBodyRow.active:hover>td{background-color:#feeeb5}.Grid .GridBodyRow[selected="true"]>td{background-color:#fdf7df}.Grid .DefaultEditor.GridBodyCell{box-shadow:inset 0 0 0 1px #ccc;cursor:pointer}.Grid .GridBodyRow:hover .DefaultEditor.GridBodyCell{box-shadow:inset 0 0 0 1px #2288fc}
.Grid .DefaultEditor.SelectorCell .GridBodyCellText{margin-right:55px}.Grid.draging{user-select:none}.Grid.draging .GridBodyRow:hover>td,.Grid.draging .GridBodyRow.hover>td{background-color:#fff}.Grid .GridBodyRow .SelectorButton{position:absolute;right:5px;top:50%;margin-top:-11px;padding:0 10px;display:none;font-size:12px}.BrowserIE .Grid .GridBodyRow .SelectorButton{margin-top:5px}.Grid .GridBodyRow .SelectorButton.Button,.Grid .GridBodyRow .SelectorButton.icon{cursor:pointer;line-height:22px;height:22px}.Grid .GridBodyRow .SelectorButton.icon{right:3px;padding:0;color:#666}.Grid .GridBodyRow .SelectorButton .Button,.Grid .GridBodyRow .SelectorButton .icon{cursor:pointer;margin-left:5px;line-height:22px;height:22px}.Grid .GridBodyRow:hover .SelectorCell .SelectorButton{display:flex}.Grid .GridBodyRow:hover .SelectorCell.HideButton .SelectorButton{display:none}.SelectorGrid .GridBodyRow:hover .SelectorButton{display:none}.SelectorGrid .GridBodyRow:hover .DefaultEditor.SelectorButton{display:flex}
.Grid .GridBodyRow .SelectorCell{position:relative;overflow:hidden;text-overflow:ellipsis}.BrowserIE .Grid .SelectorCell .nulltext,.BrowserIE .Grid .SelectorCell .GridBodyCellText{min-height:30px;line-height:30px;display:inline}.BrowserIE .Grid .SelectorCell .MergeCell .nulltext,.BrowserIE .Grid .SelectorCell .MergeCell .GridBodyCellText{display:flex}.Line18 .Grid .GridBodyCellText{line-height:18px}.Grid .CellEdit{padding:0;text-indent:6px;border:0}.Grid .activeEditor{background-color:#fff;padding:0;border:1px solid #00b7f3;overflow:hidden;box-shadow:0 0 2px 1px #94c4fb}.Grid .activeNumber input{box-shadow:inset 0 0 2px 1px #94c4fb}.Grid .GridBodyCell,.Grid .GridBodyRowNoCell,.Grid .GridDetailTd{border-right:1px solid #e9e9e9;border-bottom:1px solid #e9e9e9;cursor:default}.GridBlock.AutoHeight.NoBorder.OuterBorder .Grid .GridBodyRow:last-child td,.GridBlock.AutoHeight .Grid .GridBodyRow:last-child td{border-bottom-width:0}.GridBlock.AutoHeight.NoBorder .Grid .GridBodyRow:last-child td{border-bottom-width:1px}
.Grid .GridFooterCell{overflow-x:hidden;border-right:1px solid #e9e9e9}.Grid .GridFooterCellText{white-space:nowrap;padding:0 3px;height:30px;line-height:30px}.Grid .CellSpacer,.Grid.Stretch .GridFilterBar.Normal .GridFilterBlock:last-child,.Grid.Stretch .GridHeaderBar.Normal .GridHeaderCaption:nth-last-child(2),.Grid.Stretch .GridFooterBar.Normal .GridFooterCell:nth-last-child(2),.Grid.ShowSpacer.Stretch .GridBody.Normal .GridBodyCell:nth-last-child(2),.Grid.HideSpacer.Stretch .GridBody.Normal .GridBodyCell:nth-last-child(1){border-right-width:0}.Grid .CellSpacer{padding:0 !important}.Grid .GridBodyCell .SkinButtonBlock{border:0}.Grid .GridBodyCell img{vertical-align:middle;margin:5px}.Grid .ChkBoxStyle input{top:4px;left:0}.Grid .GridBodyCell .ProgressBar{height:20px;margin:auto 5px;width:auto}.Grid .GridBodyCell .ProgressValue{height:100%}.Grid .ProgressBar .ProgressText,.Grid .ProgressBar .ProgressHighText{line-height:12px;word-break:keep-all;word-spacing:normal;white-space:nowrap}.Grid .GridBody .GridDetailTd{padding:2px 0 2px 2px;min-height:50px}
.Grid .GridBody .MoreText{margin:10px auto;text-align:center;color:#666;cursor:pointer;flex-shrink:0}.GridBodyCellText{padding:3px 5px;min-height:15px;word-wrap:break-word;word-break:break-all;white-space:pre-wrap}.Grid .GridBodyCellText.HasClick:hover{text-decoration:underline;cursor:pointer}.Grid .GridBodyCellText[unselectable="on"]{cursor:default}.Grid .GridBodyRowNoCell{background-color:#f6fcff}.Grid .GridBodyRowNoCellText{min-height:15px;text-align:center}.Grid .GridFooterBar{overflow:hidden;flex-shrink:0;background-color:#fefbe5}.Grid .GridFooter{table-layout:fixed;min-width:100%;cursor:default;border-top:1px solid #ddd}.Grid .checkShape{color:#00b7f3}.Grid .checkShape.index0:before{content:"\e820";font-weight:bold;color:#fe020e}.Grid .checkShape.index1:before{content:"\e81d";font-weight:bold;color:#2fc33d}.Grid .delShape{cursor:pointer;color:#666;font-size:16px;display:inline}.Grid .delShape:before{content:"\e61f"}
.Grid .delShape:hover{color:red}.Grid .GridBodyCell .nulltext{color:#ababab;padding:0 6px;width:100%}.Grid.NoData .GridBody{display:flex;flex-direction:column;justify-content:center}.Grid.NoData .GridBody .EmptyBlock{display:flex;flex-direction:column;align-self:center;justify-content:center;align-items:center}.Grid.NoData .GridBody .GridBodyRow{visibility:hidden}.Grid.NoData .ScrollBarLine{flex-shrink:0}.maxShape{cursor:pointer;display:inline-block}.maxShape:hover{color:#00b7f3}.maxShape:before{content:"\e7f1";font-size:18px}.Grid .GridDetailTd .PageBlock{min-height:initial}.Grid .EditBlock.stepBlock{margin:0 5px;height:26px;line-height:26px}.Grid .EditBlock.stepBlock .CellEdit{background-color:#fff;text-align:center;text-indent:0;border-radius:0;width:auto;flex:auto;overflow:hidden}.Grid .MergeCell .EditBlock .EditBlock{padding:0;margin:0}.ColResizer{cursor:col-resize;background-color:transparent;position:absolute}.ColResizer.row{cursor:row-resize}
.ColSplitter{background-color:#2288fc;background-color:var(--mainColor);position:absolute}.GridDragDiv{border:1px solid #aaa;background:#eee;padding:8px;position:absolute;left:-1000px;top:-1000px;z-index:1000}.GridHintNoWrapCell{position:absolute;visibility:hidden;left:-1000px;top:-1000px;text-indent:1px}.Grid.NoWordWrap .GridBodyCellText,.Grid.NoWordWrap .GridDynamicButtonBody a{overflow:hidden;text-overflow:ellipsis;white-space:pre;word-wrap:normal;word-break:keep-all;max-width:100%}.Grid.NoWordWrap .GridDynamicButtonBody.NoWrap{display:flex}.Grid.NoWordWrap .GridDynamicButtonBody._horz{overflow:hidden;padding:0}.Grid .GridDynamicButtonBody .aicon{cursor:pointer}.Grid .RowNoConfigIcon{display:inline-block;margin:auto}.Grid .RowNoConfigIcon:before{content:"\e65b";font-size:18px;font-weight:normal}.Grid .RowNoConfigIcon:hover{color:#00b7f3}.GridBodyRowNoCellText{overflow:hidden;text-overflow:ellipsis;white-space:pre;word-wrap:normal;word-break:keep-all}.GridFilterBar{height:30px;overflow:hidden;background-color:#f9f9f9;flex-shrink:0;border-bottom:1px solid #dee6ee}
.GridFilterBar .GridFilterXBar{height:30px;min-width:100%;display:flex}.GridFilterBar .GridFilterBlock{padding:0 4px;position:relative;height:30px;width:50px;border-right:1px solid #dee6ee;overflow:hidden;text-align:center;flex-shrink:0;display:flex;align-items:center}.GridFilterBar .GridFilterBlock>*{flex:auto;width:auto}.GridFilterBar .GridFilterBlock .Edit{height:22px;line-height:22px;flex-basis:auto;flex-grow:1;flex-shrink:1}.GridFilterBar .GridFilterBlock .EditBlock{height:22px}.GridFilterBar .GridFilterBlock .ButtonEdit{margin:0}.GridFilterBar .GridFilterBlock .LayoutItemControl .Edit{margin-top:0}.GridFilterBar .GridFilterBlock .Button{height:22px;line-height:22px}.GridFilterBar .GridFilterLineSpacer{margin:auto;color:#666}.GridFilterBar .GridFilterIcon{width:100%;height:28px;display:block;line-height:28px;cursor:pointer}.GridFilterBar .IntervalBlock{border-radius:1px;display:flex;align-items:center;border:1px solid #e1e1e1;background-color:#fff;height:22px;width:100%;overflow:hidden}
.GridFilterBar .IntervalBlock .Edit{border:0;margin:0;text-align:center;text-indent:0;width:auto !important;flex-basis:auto;flex-grow:1;flex-shrink:1}.GridFilterBar .TextColumn:before{position:absolute;left:8px;top:50%;margin-top:-6px;font-size:13px}.GridFilterBar .TextColumn input{padding-left:20px;text-indent:0}.GridFilterBar .SkinButton:before{font-size:13px}.GridFilterBar .ArrowCombo:before{font-size:15px}.GridFilterBar .Edit,.GridFilterBar .EditBlock,.GridFilterBar .TextArea,.GridFilterBar .EditBlock .SkinButton{border-radius:1px}.GridExpandCell{display:flex}.GridExpandCell .text{overflow:hidden;text-overflow:ellipsis;white-space:pre}.GridExpandCell .level0{margin-left:5px}.GridExpandCell .level1{margin-left:22px}.GridExpandCell .level2{margin-left:39px}.GridExpandCell .level3{margin-left:56px}.GridExpandCell .level4{margin-left:71px}.GridExpandCell .level5{margin-left:86px}.GridExpandCell .level6{margin-left:105px}.GridExpandCell .level7{margin-left:120px}.GridExpandCell .level8{margin-left:135px}
.GridExpandCell .level9{margin-left:150px}.GridExpandCell .level10{margin-left:165px}.GridBodyCell .aicon-xiaojiantou{cursor:pointer;vertical-align:top;display:inline-block}.GridBodyCell .aicon-xiaojiantou.margin0{margin:0}.GridBodyCell .aicon-xiaojiantou.collapsed:before{transform:rotate(-90deg)}.GridBodyCell .aicon-xiaojiantou:before{display:inline-block;color:#999;transition:all .2s linear}.FiscalCaption{line-height:24px;text-align:center}.FiscalHeader{border-top:1px solid #ccc;text-align:right;overflow:hidden;display:flex}.FiscalFooter{overflow:hidden;display:flex}.LessNumber{color:red}.GridBodyFiscalText{height:29px;display:flex}.GridBodyFiscalText .FiscalBlock{line-height:30px;height:30px}.GridFooterCellText .FiscalBlock{line-height:20px;height:20px}.FiscalBlock{float:left;line-height:20px;flex-shrink:0;flex-basis:0;flex-grow:1;text-align:center;border-right:1px solid #efefef}.FiscalBlock.fc0{border-right-color:transparent}.FiscalBlock.fc2{border-right-color:#edb9b9}
.FiscalBlock.fc5{border-right-color:#a7d9f5}.FiscalBlock.fc8{border-right-color:#a7d9f5}.FiscalBlock.fc10{border-right-color:#edb9b9}.FiscalBlock .CellEdit{text-indent:0;text-align:center}.BrowserIE .FiscalGrid .Grid .SelectorCell .nulltext,.BrowserIE .FiscalGrid .Grid .SelectorCell .GridBodyCellText{line-height:normal;min-height:auto;padding:0 0 0 5px}.CellIcons{text-align:center}.CellIcons div{display:inline-block;width:18px;text-align:center;line-height:22px;height:22px;margin-right:4px;cursor:pointer}.Grid .GridDynamicButtonBody .aicon:hover,.CellIcons div:hover{color:#00b7f3}.CellIcons div:last-child{margin-right:0}.GridDynamicButtonBody{padding:4px 2px;word-wrap:break-word;display:flex;flex-wrap:wrap}.Grid .LinkColumn,.GridDynamicButtonBody a,.GridDynamicButtonBody a:link,.GridDynamicButtonBody a:visited{color:#2288fc;cursor:pointer;text-align:center}.Grid .LinkColumn:hover,.GridDynamicButtonBody a:hover{color:#00b7f3}.Grid .IconRight .GridHeaderCaptionText{display:inline-flex;flex-direction:row-reverse;align-items:center;width:100%;justify-content:center}
.Grid .IconRight .GridHeaderCaptionText:before{margin-right:0;margin-left:4px;cursor:pointer}.Grid .IconRight .GridHeaderCaptionText:hover:before{color:#2288fc}.Grid .IconAbsRight .GridHeaderCaptionText{position:relative;line-height:20px}.Grid .IconAbsRight .GridHeaderCaptionText:before{position:absolute;right:10px;top:0;margin:0;cursor:pointer}.Grid .IconAbsRight .GridHeaderCaptionText:hover:before{color:#2288fc}.GridDynamicButtonBody._vert{flex-direction:column}.GridDynamicButtonBody ._vert{display:block;line-height:16px;margin-bottom:4px}.GridDynamicButtonBody ._vert:last-child{margin-bottom:0}.GridDynamicButtonBody ._horz{display:inline-block;padding:0 5px;line-height:16px}.GridDynamicButtonBody.Normal a{background-color:#eee;padding:0 5px;text-decoration:none}.GridDynamicButtonBody.Normal a:hover{background-color:#00b7f3;color:#fff}.GridDynamicButtonBody.Normal a.disabled:hover{background-color:#eee}.GridDynamicButtonBody.NormalText a{text-decoration:none;color:#333}.GridDynamicButtonBody.NormalText a:hover{color:#00b7f3}
.GridDynamicButtonBody.LinkText a:hover{text-decoration:underline}.GridDynamicButtonBody.Normal ._vert{margin:5px;display:block;line-height:24px;border-radius:3px}.GridDynamicButtonBody.Normal ._horz{margin:0 3px;line-height:24px;border-radius:3px}.GridDynamicButtonBody.NoUnderline a{text-decoration:none}.Grid .MergeCell .GridBodyCellText,.Grid .MergeCell .CellIcons,.Grid .MergeCell .GridBodyCellData,.Grid .MergeCell .MergeSWCell,.Grid .MergeCell .stepBlock,.Grid .MergeCell .ChkBoxStyle,.Grid .MergeCell .GridDynamicButtonBody{border-bottom:1px solid #e9e9e9;display:flex;align-items:center;height:35px !important;line-height:normal !important;min-height:auto !important;padding:0 5px}.Grid .MergeCell .CellIcons{justify-content:center}.Grid .MergeCell .stepBlock{margin:0}.Grid .MergeCell .ChkBoxStyle.CellChk:before{display:none}.Grid .MergeCell .stepBlock .CellEdit,.Grid .MergeCell .stepBlock .stepNum{height:26px;line-height:26px}.MergeCell .stepBlock{border:0}.Grid .MergeCell .ChkBoxStyle{justify-content:center}
.Grid .Number .MergeCell .GridBodyCellText{justify-content:flex-end}.Grid .MergeCell .ChkBoxStyle input{top:50%;left:50%;transform:translate(-50%,-50%)}.Grid .MergeCell .stepBlock:last-child,.Grid .MergeCell .ChkBoxStyle:last-child,.Grid .MergeCell .CellIcons:last-child,.Grid .MergeCell .GridBodyCellText:last-child,.Grid .MergeCell .GridBodyCellData:last-child,.Grid .MergeCell .MergeSWCell:last-child,.Grid .MergeCell .GridDynamicButtonBody:last-child{border-bottom:0}.Grid .MergeSWCell{display:flex;justify-content:center}.Grid .MergeFooter{text-align:left}.Grid .MergeCell .ImgData{display:flex;justify-content:center;align-items:center}.Grid .SkinButton,.Grid .iconfont{color:#2288fc;font-weight:normal}.GridBody .SkinButton{border-width:0}.DynamicFilter{padding-top:3px}.DynamicFilter .EditBlock{margin-top:0;margin-right:5px}.GridBody .DynamicCell .SkinButton{border:1px solid #c9c9c9;border-left:0}.Grid .GridHeaderBar .GridHeader .GridHeaderCaption.focus{border-color:#2288fc;background-color:#2288fc;color:#fff}
.Grid .GridFilterBar .GridFilterBlock.focus,.Grid .GridHeaderBar .GridHeader .GridHeaderCaption.focus,.Grid .GridBody .GridBodyCell.focus{border-left:1px solid #2288fc;border-right:1px solid #2288fc}.Grid .GridBody .GridBodyRow:last-child .GridBodyCell.focus{border-bottom:1px solid #2288fc}.Pager{border:1px solid #ddd;background-color:#fff;height:30px;line-height:30px;overflow:hidden;overflow-x:auto;flex-shrink:0;display:flex;align-items:center}.PagerXBar.XBar{bottom:0;height:5px;z-index:1}.PagerXBar.XBar .BarValue{height:3px}.PagerBottom{border-width:0 1px 1px 1px}.PagerTop{border-width:1px 1px 0 1px}.PagerButtons{display:flex;border:1px solid #ddd;border-radius:8px;background-color:#f6f6f6;height:22px;line-height:22px;padding:0 10px;overflow:hidden}.PagerButton{padding:0 8px;cursor:pointer}.PagerButton:hover{background-color:#ddd}.PagerButton.active{background-color:#2576b0;color:#fff}.Pager .PagerContent{padding:0 5px;display:flex;flex-shrink:0;align-items:center}.Pager .PagerContent .Button{height:inherit;line-height:inherit;background-color:inherit;color:#333;padding:0 5px}
.Pager .PagerContent .Button.disabled{color:#bbb;border-color:#eee}.Pager .PagerContent .Button.disabled:hover{background-color:inherit;border-color:#eee}.Pager .PagerContent .Edit{height:22px;line-height:22px}.Pager .PagerContent .EditBlock{height:22px;line-height:22px}.Pager .PagerContent .pageSize :first-child{margin-right:3px}.Pager .PagerContent .pageSize :last-child{margin-left:3px}.Pager .PagerContent .dropPageIndex :first-child{margin:0 3px}.Pager .PagerContent .dropPageIndex :last-child{margin:0 3px}.PagerSummaryText{padding-right:5px}.Pager_homePage{margin-left:16px}.Pager_homePage:before{content:"\e7eb"}.Pager_lastPage:before{content:"\e7ec"}.Pager_prevPage:before{content:"\e7ee"}.Pager_nextPage:before{content:"\e7ed"}.Pager_refresh{margin-right:10px !important}.Pager_refresh:before{content:"\e672";font-weight:bold;font-size:14px}.Pager .GotoBlock{display:flex;align-items:center;margin-left:10px}
.Pager .GotoEdit{width:40px;text-align:center;margin:0 3px;text-indent:0}.Pager .InnerBlock{padding-left:6px;display:flex;flex-shrink:0}.Pager .InnerBlock .FlowPanel{padding:0;margin-bottom:0}.Pager .InnerBlock .FlowPanel .FlowItem{margin-bottom:0}.Pager .InnerBlock td{height:26px}.Pager .InnerBlock .Label{padding:0;margin-top:0;line-height:2}.LazyPager .Pager_refresh{display:none}.Pager .InnerBlock .Button,.InnerBlock .Edit,.InnerBlock .EditBlock,.Pager .InnerBlock .FlowPanel .FlowLabel{height:22px;line-height:22px}.Grid .GridBody .EditBlock{display:flex}.GridBody .Edit,.GridBody .EditBlock,.GridBody .TextArea{border-radius:0}.ButtonEdit.PagerEveryPageEdit{width:52px;text-align:center;text-indent:0}.ButtonEdit.PagerDropPageEdit{width:52px;text-align:center;text-indent:0}.PopupArrow{position:absolute;width:0;height:0;border-width:8px;border-style:solid;border-bottom-color:transparent;border-top-color:transparent;border-right-color:transparent;border-left-color:transparent}.Popup{min-width:90px;min-height:20px;background-color:#f9f9f9;opacity:0;position:absolute;border:1px solid #ddd;padding:10px;max-height:100%;max-width:100%}
.Border.Popup{background-color:#f9f9f9}.Popup.overflow{overflow:scroll;overflow-x:hidden}.Popup.p-top{border-top:3px solid #fff;padding-top:7px}.Popup.p-top .PopupArrow{top:-19px;border-bottom-color:#fff}.Popup.p-bottom{border-bottom:3px solid #fff;padding-bottom:7px}.Popup.p-bottom .PopupArrow{bottom:-19px;border-top-color:#fff}.Popup.p-left{border-left:3px solid #fff}.Popup.p-left .PopupArrow{border-right-color:#fff}.Popup.p-right{border-right:3px solid #fff}.Popup.p-right .PopupArrow{margin-left:3px;border-left-color:#fff}.Popup.noArrow{border:1px solid #ddd}.Popup.noArrow .PopupArrow{display:none}.Popup.opacity.Step{transition:opacity .4s linear,left .4s linear,top .4s linear;border-radius:20px;padding:20px}.Popup.Step input{width:0;height:0;border:0;margin:0;padding:0;overflow:hidden;font-size:0}.Popup.Step .Content{min-width:150px;font-size:14px}.Popup.Step .Bottom{margin-top:30px;display:flex}.Popup.Step .Bottom .NextBtn{margin-left:10px}.Popup.Step .StepNums{display:flex;flex:auto;align-items:center;margin-right:15px}
.Popup.Step .StepNums .item{margin:0 3px;display:block;width:4px;height:4px;border-radius:2px;background-color:#ccc}.Popup.Step .StepNums .item.cur{width:6px;height:6px;border-radius:6px;background-color:#2288fc}.FormOverlay.Step{background-color:rgba(255,255,255,0);box-shadow:0 0 0 9999px rgba(0,0,0,0.5);z-index:1;border-radius:6px}.CenterOverlay .FormOverlay.Step{display:none}.Popup.Embed{opacity:1;max-width:none;max-height:none}.UI_ListView{overflow:auto;flex-shrink:1;position:relative;flex-grow:1;flex-basis:auto;background-color:#fff;border:1px solid #eee;border-radius:0}.UI_ListItem{float:left;overflow:hidden}.UI_ListView.ListVert{border:0;background:inherit;display:flex;flex-direction:column}.UI_ListView.ListVert .UI_ListItem{float:none;display:block;line-height:36px;height:36px;text-indent:5px;cursor:default;flex-shrink:0}.UI_ListView.ListVert .UI_ListItem:hover{background-color:#2288fc;color:#fff}.UI_ListView .DataBlock>*{margin-right:5px}.UI_ListView .DataBlock>*:last-child{margin-right:0}
.DataText{word-wrap:break-word;word-break:break-all}.ListPager{justify-content:flex-end}.DataText:before{margin-right:5px}.UI_ListView .UI_ListItem.selected{background-color:#ecf2ff;color:#2288fc}.UI_ListView .UI_ListItem.ViewDragBlock{border:1px dashed #ccc;background-color:#fafafa}.UI_ListView .UI_ListItem.ViewMoveItem{position:absolute !important;z-index:99999;margin:0;opacity:.8;background-color:#fff;filter:alpha(opacity=80);cursor:grabbing}.UI_ListView .UI_ListItem.CurDrop{box-shadow:0 0 5px #ccc}.NoBorder{border:none !important}.EditBlock.NoBorder .Edit{border:0}.EditBlock.NoBorder .SkinButton{border:0}.WhiteBorder{border-color:transparent !important;transition:none}.EditBlock.WhiteBorder .Edit{border-color:transparent !important}.EditBlock.WhiteBorder .SkinButton{border-color:transparent !important}.ProgressBar{width:100%;height:18px;background-color:#e0e0e0;border-radius:8px;position:relative;border:4px solid #e0e0e0}.ProgressBar.Animate .ProgressValue,.Grid .ProgressBar.Animate .ProgressValue,.ProgressBar .ProgressValue{background-image:linear-gradient(90deg,#47bdfe 0,#057cff 93%,#0087d5 100%);height:100%;width:0;border-radius:8px;position:absolute;left:0;top:0;z-index:0}
.ProgressBar .ProgressText,.ProgressBar .ProgressHighText{position:relative;z-index:1;margin:auto;width:100%;height:100%;text-align:center;top:0;display:flex;justify-content:center;align-items:center;color:#333}.ProgressBar .ProgressHighText{color:#fff}.ProgressBar.ValueFloat span{position:absolute;left:0;top:-40px;border-radius:8px;width:auto;min-width:26px;height:30px;display:inline-block;line-height:30px !important;text-align:center;background-color:#191919;transition:left .2s linear;word-break:keep-all;word-wrap:normal;white-space:nowrap;padding:0 5px;color:#fff}.ProgressBar.ValueFloat span:after{content:"";border-top:10px solid #191919;border-left:10px solid transparent;border-right:10px solid transparent;position:absolute;bottom:-6px;left:8px}.ProgressBar.BlackBg,.GridBodyCell.BlackBg .ProgressBar{background-color:#262626;border-color:#262626}.ProgressBar.BlackBg .ProgressText,.GridBodyCell.BlackBg .ProgressBar .ProgressText{color:#fff}.ProgressBar.RedValue .ProgressValue,.GridBodyCell.RedValue .ProgressBar .ProgressValue{background-color:#d9534f;background-image:none}
.ProgressBar.GreenValue .ProgressValue,.GridBodyCell.GreenValue .ProgressBar .ProgressValue{background-color:#5cb85c;background-image:none}.ProgressBar.YellowValue .ProgressValue,.GridBodyCell.YellowValue .ProgressBar .ProgressValue{background-color:#f0ad4e;background-image:none}.ProgressBar.hasItems.hasText{height:1px;margin-bottom:30px;margin-top:16px}.ProgressBar.hasItems.hasText.hasClick .Item{cursor:pointer}.ProgressBar.hasItems.hasText.hasClick .Item:hover{color:#2288fc}.ProgressBar.hasItems .ProgressText{display:flex;justify-content:space-around;position:relative}.ProgressBar.hasItems .Item{display:block;line-height:24px;margin:0;counter-increment:number;display:flex;flex-direction:column;align-items:center;color:#aaa}.ProgressBar.hasItems .Item:before{content:counter(number);width:24px;height:24px;line-height:24px;border-radius:50%;background-color:#fff;color:#9c9c9c;border:1px solid #9c9c9c;margin-top:20px}.ProgressBar.hasItems.ShowCheck .ProgressText .active.aicon-check:before{content:'\e81d';background-color:#fff;border-color:#2288fc;color:#2288fc}
.ProgressBar.hasItems.ShowCheck .ProgressText .active.aicon-check{color:#333}.ProgressBar.hasItems .Item.active{color:#2288fc}.ProgressBar.hasItems .Item.active:before{background-color:#2288fc;border-color:#2288fc;color:#fff}.BetweenBar.ProgressBar.hasItems .ProgressText{justify-content:space-between}.BetweenBar.ProgressBar.hasItems .Item:first-child{align-items:flex-start}.BetweenBar.ProgressBar.hasItems .Item{align-items:center}.BetweenBar.ProgressBar.hasItems .Item:last-child{align-items:flex-end}.Grid .ProgressBar.ok .ProgressValue{background-color:#4ebd7d}.ImageCropperBox{display:inline-block;border:0;border:0;user-select:none;-moz-user-select:none;-ms-user-select:none;-webkit-user-select:none}.ImageCropper{border:1px solid #666;position:relative;user-select:none;-moz-user-select:none;-ms-user-select:none;-webkit-user-select:none}.ImageCropper_DragHandle{border:1px dashed #fff;width:80px;height:80px;top:0;left:0;cursor:grabbing;position:absolute}.ImageCropperDrag{position:absolute;background:#fff;border:1px solid #333;width:6px;height:6px;z-index:500;font-size:0;opacity:.5;filter:alpha(opacity=50)}
.ImageCropperDragLeft{left:-4px;top:50%;margin-top:-4px;cursor:ew-resize}.ImageCropperDragRight{right:-4px;top:50%;margin-top:-4px;cursor:ew-resize}.ImageCropperDragUp{top:-4px;left:50%;margin-left:-4px;cursor:ns-resize}.ImageCropperDragDown{bottom:-4px;left:50%;margin-left:-4px;cursor:ns-resize}.ImageCropperDragLeftUp{left:-4px;top:-4px;cursor:nw-resize}.ImageCropperDragRightUp{right:-4px;top:-4px;cursor:ne-resize}.ImageCropperDragRightDown{right:-4px;bottom:-4px;background-color:#00f;cursor:nw-resize}.ImageCropperDragLeftDown{left:-4px;bottom:-4px;cursor:ne-resize}.ImageCropperDragHide{border:0;border:0;background-color:transparent;filter:alpha(opacity=0);cursor:grabbing}.ImageCropperDragDisable{border:0;border:0;background-color:transparent;filter:alpha(opacity=0);cursor:default}.ImageCropper_DragBaseImg{filter:url(blur.svg#blur);-webkit-filter:blur(2px);-moz-filter:blur(2px);-ms-filter:blur(2px);filter:blur(2px);filter:progid:DXImageTransform.Microsoft.Blur(PixelRadius=2,MakeShadow=false)}
.FlowPanel .FlowCaption,.FlexBlock .FlexCaption,.LayoutGroupCaption{text-indent:5px;line-height:32px;height:32px;background-color:#eff3f6;flex-shrink:0}.FlowPanel{margin-bottom:5px;padding:0;overflow:hidden;flex-shrink:0;display:flex;flex-wrap:wrap;align-content:flex-start;position:relative}.FlowPanel .CustomControl .FlowPanel{padding-top:0}.FlowPanel .CustomControl .FlowPanel .FlowItem{margin-bottom:0}.FlowPanel.vert{flex-direction:column;flex-wrap:nowrap;padding-right:0}.FlowPanel .FlowItem{margin:0 8px 5px 0;float:left;display:flex;align-items:center;flex-shrink:0}.FlowPanel .FlowItem:last-child{margin-right:0}.FlowPanel .FlowItem:last-child .SpeedButton{padding-right:0}.FlowPanel.MB0 .FlowItem{margin-bottom:0 !important}.FlowPanel.Margin10 .FlowItem{margin:0 0 10px 0 !important}.FlowPanel.Margin15 .FlowItem{margin:0 0 15px 0 !important}.FlowPanel.Margin20 .FlowItem{margin:0 0 20px 0 !important}.FlowPanel.vert>.FlowItem{clear:both;float:none;margin-right:0}.FlowPanel.Border .FlowItem{margin:0 3px 5px 5px}
.FlowPanel.Border{border-color:#dee6ee;padding-top:5px;text-align:left;box-shadow:none;border-radius:1px}.FlowPanel.Border.shadow{box-shadow:0 0 6px #ccc}.FlowPanel.hasCaption{padding-top:0}.FlowPanel .FlowCaption{width:100%;clear:both;margin-bottom:5px}.FlowPanel .FlowCaption:before{margin-right:5px}.FlowPanel .FlowLabel.MustCharLeft,.FlowPanel .FlowLabel.MustCharRight{display:flex;align-items:center;justify-content:right;flex-shrink:0}.LayoutItemLabel .MustCharLeft{display:flex;align-items:center}.FlowPanel .FlowLabel.MustCharLeft{justify-content:left}.FlowPanel .FlowLabel{margin-right:5px;text-align:right}.FlowPanel.MorePanel{display:block}.FlowPanel.MorePanel .FlowLabel{width:70px}.FlowPanel .LayoutItemLabel{line-height:28px}.FlowPanel .Label{display:inline-flex;align-items:center;height:28px;line-height:28px;cursor:default}.FlowPanel>.HSpacer{height:28px}.FlowPanel>.VSpacer{height:28px;clear:both;width:1px;float:none}.FlowPanel .FlowItem .HBlock{display:inline-block;padding:0}.FlowPanel .FlowItem .LayoutGroupBorder .HBlock{display:block}
.FlowPanel .NoMargin{height:28px}.FlowPanel .Flex1.FlexAuto{height:28px;align-items:center}.FlowPanel.HasColSpan.VertItem .FlowItem{padding-right:10px}.FlowPanel.HasColSpan.VertItem.vert .FlowItem{padding-right:0}.FlowPanel.HasColSpan.LabelLeft .FlowLabel{padding-left:10px}.LabelRight .Label,.LabelRight .FlowLabel{justify-content:right}.LabelLeft .Label,.LabelLeft .FlowLabel{justify-content:left}.LabelCenter .Label,.LabelCenter .FlowLabel{justify-content:center}.LabelTop{align-self:flex-start}.CustomControl{flex-shrink:0}.FlowPanel.FieldSet{position:relative;padding:20px 0 5px 0;overflow:visible;margin-top:14px;border-radius:4px 4px 0 0;-webkit-border-radius:4px 4px 0 0}.FlowPanel.FieldSet .FlowCaption{padding:0 5px;color:inherit;background-color:#fff;position:absolute;top:-9px;left:10px;width:auto;height:auto;line-height:normal;font-weight:bold;margin:0}.FlowPanel.FieldSet .FlowItem{display:inline-flex;float:none;vertical-align:top}.FlowPanel.OverHide,.FlowPanel.OverHide.Border{border:0;overflow:hidden;height:0 !important;padding:0}
.FlexRow{flex-direction:row !important}.FlexBlock{display:flex;flex-shrink:1;flex-grow:1;flex-basis:auto;overflow:hidden}.FlexBlock.vert .FlexBlock{width:auto}.FlexBlock>*,.FlexRow>*{margin-right:5px}.FlexBlock>*:last-child,.FlexRow>*:last-child{margin-right:0}.FlexBlock.vert{display:flex;flex-direction:column;overflow-y:auto}.FlexBlock.vert>*,.FlexBlock.FlexVert>*,.FlexBlock.hasCaption>*{margin-right:0;margin-bottom:5px}.FlexBlock.imb10.vert>*,.FlexBlock.imb10.FlexVert>*,.FlexBlock.imb10.hasCaption>*{margin-right:0;margin-bottom:10px}.FlexBlock.FlexVert>*:last-child,.FlexBlock.vert>*:last-child,.FlexBlock.hasCaption>*:last-child{margin-bottom:0}.FlexBlock>.HBlock{padding:0}.FlexBlock.FlexCenter,.UI_ListView.FlexCenter,.FlexCenter{display:flex;-webkit-align-items:center;align-items:center;-webkit-justify-content:center;justify-content:center}.FlexWrap{display:flex;flex-wrap:wrap}.FlexNoWrap{display:flex;flex-wrap:nowrap}.FlexSpace{display:flex;justify-content:space-between}
.FlexAround{display:flex;justify-content:space-around}.FlexRight{display:flex;justify-content:flex-end}.FlexShrink0{flex-shrink:0 !important}.FlexShrink1{flex-shrink:1 !important}.SelfCenter{align-self:center}.FlexBlock.hasCaption{flex-direction:column}.FlexBlock.Border.hasCaption .FlexCaption{border:0}.FlexBlock.Border{box-shadow:none}.FlexBlock .FlexCaption{width:100%;flex-shrink:0;border:1px solid #dee6ee;border-bottom:0;margin-bottom:0}.MDIChild.Hided,.FlexBlock.Hided{display:none}.MDIChild.Hided.PageLoading{display:flex;position:absolute;left:-10000px;top:-10000px}.CustomBlock,.PageBlock{flex-shrink:1;flex-grow:1;flex-basis:auto;display:flex;flex-direction:column;position:relative}.PageBlock.Horz{flex-direction:row}.PageBlock.VMiddle{justify-content:center}.PageBlock.VBottom{justify-content:flex-end}.PageBlock.HCenter{align-items:center}.PageBlock.HRight{align-items:flex-end}.FlexWidth{flex-grow:1;flex-basis:auto;flex-shrink:1;display:flex}
.FlexColumn{flex-direction:column !important;overflow-y:scroll}.CustomControl.FlexColumn{overflow-y:auto}.FlexVert{display:flex !important;flex-direction:column !important}.FlexFixed{flex:none;display:flex;flex-direction:column}.ListViewBlock{flex-grow:1;flex-basis:auto;flex-shrink:1;display:flex;flex-direction:column;overflow:hidden}.ListViewBlock .ListView{flex-grow:1;flex-basis:auto;flex-shrink:1}.FlexClear{flex-shrink:1;flex-grow:1;flex-basis:auto;display:block}.WidthAuto{width:auto !important}.Flex1{flex:1 0 auto;width:auto !important;margin:0}.FlexGrow1{flex:1 !important}.FlexGrow2{flex:2 !important}.FlexGrow3{flex:3 !important}.Block.Flex1{margin:0}.Flex2{flex:2 0 auto;width:auto !important}.Flex3{flex:3 0 auto;width:auto !important}.Flex4{flex:4 0 auto;width:auto !important}
.Flex5{flex:5 0 auto;width:auto !important}.TabBody,.TabBodyNoHeader{display:flex;flex-shrink:1;flex-grow:1;flex-basis:auto}.TabPage{display:flex;flex-shrink:1;flex-grow:1;flex-basis:auto;flex-direction:column}.TabPage>*{margin-bottom:5px}.TabPage>*:last-child{margin-bottom:0}.FlowPanel .HBlock.FlexAuto,.HBlock.FlexAuto{display:flex;height:28px;flex-shrink:1;padding:0;align-items:center}.HBlock.FlexAuto>*{margin:0 5px 0 0}.TextArea.FlexAuto{height:auto}.Button.FlexAuto{justify-content:center}iframe{border:0}body>iframe{display:none}body.draging iframe{pointer-events:none}body.draging{user-select:none}.PageBlock iframe,.IFrame{border:0;flex-grow:1;flex-basis:auto;flex-shrink:1;display:flex;width:auto;height:auto}.BrowserIE .FlexBlock iframe,.BrowserIE .IFrame,.BrowserIE .TabPage iframe{width:100%;height:100%}.HBlock{padding:5px 0 0 0;overflow:hidden;flex-shrink:0}.Block{flex-shrink:0}.CustomBlock.Block{flex-shrink:1}
.VBlock{flex-shrink:0}.Block.Border,.VBlock.Border,.HBlock.Border{padding-top:5px;padding-bottom:5px;box-shadow:none;padding-left:5px;margin-bottom:5px}.HBlock.Border{padding-bottom:0}.HBlock>*,.Block.Border>*{margin:0 5px 5px 0;display:inline-block;vertical-align:top}.VBlock>*{display:block;margin-bottom:5px;margin-right:5px}.HBlock>*:last-child{margin-right:0}.VBlock>*:last-child{margin-bottom:0}.HBlock>.FlowPanel{margin:0;display:flex}.HBlock>.HBlock,.HBlock>.FlexBlock{margin-bottom:0}.HBlock>a,.HBlock>.Label{line-height:28px}.HBlock>.linkbutton{line-height:28px}.FlexAuto{flex-grow:1 !important;flex-basis:auto;flex-shrink:1;display:flex;width:auto;min-width:10px}.Flex0{flex-grow:0 !important;flex-shrink:0 !important;flex-basis:auto !important}.FlexGrow{flex-grow:1 !important;flex-shrink:1 !important;flex-basis:auto !important}.Flex50{flex-basis:50% !important}.NoMargin .HBlock>*,.MDIInnerBlock .HBlock>*,.TabContainer .HBlock>*,.TabContainer .Block.Border>*,.FlowItem .HBlock>*,.InnerBlock .HBlock>*,.FlowItem .Block.Border>*{margin-bottom:0}
.NoMargin .HBlock,.MDIInnerBlock .HBlock,.TabContainer .HBlock,.InnerBlock .HBlock{padding:0}.ColorPopup{position:absolute;overflow:hidden;left:-2000px;top:-2000px;background-color:#fff;width:302px}.ColorPopup .Title{display:block;background-color:#dcdcdc;text-indent:10px;line-height:30px;border-bottom:1px solid #ddd}.ColorPopup .TSBlock{display:flex}.ColorPopup .BZBlock{display:flex}.ColorPopup .ZJBlock{display:flex;height:30px}.ColorPopup .BodyBlock{display:flex;flex-wrap:wrap;margin:5px 0;width:310px}.ColorPopup .BodyBlock .ColorItem{margin:0 5px;border-bottom:0}.ColorBlock{border:1px solid #ccc;overflow:hidden}.ColorBlock .ColorPopup{position:relative;width:auto;left:0;top:0;box-shadow:2px 0 3px #eee}.ColorItem{cursor:pointer;display:block;width:20px;height:20px;margin:5px;overflow:hidden;flex-shrink:0}.Calendar{display:flex;border:1px solid #eee;flex-shrink:0}.Calendar .CalendarMain{flex:auto}.Border.CalendarMain{left:-2000px;top:-2000px;padding:1px}.Calendar .CalendarMain .CalendarBody{border-bottom:0}
.CalendarMain{display:inline-block;background-color:#fff}.CalendarMain .CalendarTitle{line-height:40px;height:40px;position:relative;display:flex;border-bottom:1px solid #e7e7e7}.CalendarMain .CalendarTitle .Year,.CalendarMain .CalendarTitle .Month{width:auto;margin:0;color:#333;font-weight:bold;font-size:14px}.CalendarMain .CalendarTitle .Year{margin-right:5px}.CalendarMain .CalendarTitle span{width:30px;text-align:center;cursor:pointer;display:inline-block;vertical-align:middle;color:#9d9d9d}.CalendarMain .CalendarTitle span:hover{color:#2288fc}.CalendarMain .YearArrow1{transform:rotate(180deg)}.CalendarMain .DateArrow1{transform:rotate(180deg)}.CalendarMain .CalendarBody{position:relative;border-bottom:1px solid #e7e7e7}.CalendarMain .CalendarBody .Border{border-radius:0}.CalendarMain .WeekRow{background-color:#2288fc;color:#fff;overflow:hidden;display:flex}.CalendarMain .WeekCell{width:24px;height:32px;flex-grow:1;line-height:32px;margin:0 6px;text-align:center;flex-shrink:0;cursor:default}
.CalendarMain .DayRow{display:block;clear:both;overflow:hidden;display:flex}.CalendarMain .DayCell{border:1px solid transparent;flex-grow:1;width:24px;height:24px;line-height:22px;margin:6px;flex-shrink:0;color:#bbb;text-align:center;cursor:pointer}.CalendarMain .DayCell.mtCurr{color:#373737}.CalendarMain .DayCell.mtCurr.selected{background-color:#2288fc;color:#fff;border-radius:2px}.CalendarMain .DayCell.dis{color:#eee;cursor:not-allowed;background-color:inherit}.CalendarMain .DayCell.dis:hover{background-color:inherit}.CalendarMain .DayCell.Today{border-color:#2288fc;border-radius:2px}.CalendarMain .TodayButton{width:100%;height:34px;line-height:34px;border-radius:0;background-color:#fafafa;color:#2288fc;border:0;clear:both;box-shadow:none;padding:0}.CalendarMain .DateTimeBody{display:flex}.CalendarMain .DateTimeBody .CalendarBody{border:0;flex:auto}.CalendarMain .DateTimeBody .TodayButton{border-top:1px solid #e7e7e7;height:50px;line-height:50px;background-color:#fff}
.CalendarMain .TodayButton:hover{background-color:#2288fc;color:#fff}.CalendarMain .timeBlock{margin-top:39px}.CalendarMain .timeBlock .TimePopup{border-right:0;border-bottom:0;min-width:160px}.CalendarMain .timeBlock .TimePopup .TimeBody{height:249px}.CalendarRange .RangeBody{display:flex}.CalendarRange .RangeBody.kind1 .CalendarBody{border-bottom:0}.CalendarRange .CalendarMain .CalendarTitle{border-bottom:1px solid #e4e4f2}.CalendarRange .CalendarMain .CalendarBody{border:0;padding:0 5px}.CalendarRange .BottomButtons{padding:12px 5px 12px 10px;display:flex;align-items:center;border-top:1px solid #e4e4f2}.CalendarRange .BottomButtons .Button{margin:0 5px}.CalendarRange .LeftButtons{padding-top:10px;display:flex;flex-direction:column;border-right:1px solid #e4e4f2}.CalendarRange .LeftButtons .Button{background-color:transparent;border:0;margin:0;height:30px;line-height:30px;text-align:left;padding:0 20px}.CalendarRange .LeftButtons .Button:hover{color:#2288fc}.CalendarRange .CalendarMain .DayCell:hover{background-color:#fff;color:#bbb}
.CalendarRange .calendar1{margin-right:10px}.CalendarRange .CalendarMain .DayCell.mtCurr:hover,.CalendarMain .DayCell:hover{background-color:#f1f1f1;border-radius:2px}.CalendarRange .tBody{border-bottom:1px solid #e4e4f2;padding:7px 0;display:flex;justify-content:center;align-items:center;color:#aaa}.CalendarRange .tBody .SkinButton{border:0}.CalendarRange .tBody .aicon-rili{display:none}.CalendarRange .tBody .split{margin:0 40px;display:inline-block}.CalendarRange .tBody .EditBlock input{text-align:center;text-indent:0;border:0}.CalendarRange .cBody{display:flex}.CalendarRange .CalendarMain .WeekCell{width:30px;margin:0}.CalendarRange .CalendarMain .DayCell{cursor:default;margin:2px 0;width:30px;height:30px;line-height:28px}.CalendarRange .CalendarMain .DayCell.mtCurr{cursor:pointer;color:#333}.CalendarRange .CalendarMain .DayCell.selected{background-color:transparent;color:#373737;border-radius:2px}.CalendarRange .CalendarMain .DayRow .DayCell.mtCurr.start,.CalendarRange .CalendarMain .DayRow .DayCell.mtCurr.end{background-color:#2288fc;background-color:var(--mainColor);color:#fff;font-weight:bold;border-radius:2px}
.CalendarRange .CalendarMain .DayCell.inRange{background-color:#e6f7ff;color:#555}.CalendarRange .RangeDay{font-weight:bold;color:#2288fc}.MonthPopup{border:1px solid #eee;display:inline-block;text-align:center}.MonthPopupMain{margin:auto;overflow:hidden;display:flex;justify-content:center}.MonthPopup .YearBlock{float:left;width:150px;margin:0 3px;padding:0 0 10px 0}.MonthPopup .YearArrowRow{line-height:28px;display:flex;justify-content:space-between;margin-top:5px}.MonthPopup .VLine{float:left;background-color:#ccc;width:1px;overflow:hidden;padding:0}.MonthPopup .MonthBlock{float:left;width:150px;margin:0 3px;padding:10px 0}.MonthPopup.hasYear .MonthBlock{margin-top:25px}.MonthPopup .YearArrowRow .DateArrow{cursor:pointer;display:block;width:50px;margin:0 15px}.MonthPopup .YearArrowRow .DateArrow:hover{background-color:#00b7f3;color:#fff}.MonthPopup .Button{margin-top:10px}.MonthPopup ._td{display:inline-block;width:40px;cursor:pointer;line-height:40px;text-align:center;margin:5px}.MonthPopup ._td:hover{color:#00b7f3}
.MonthPopup ._td.selected{color:#fff;background-color:#77bff9;border-radius:4px}.MonthPopup ._td.disabled{cursor:not-allowed;color:#a0a0a0}.MonthPopup .Edit{margin:auto;margin-top:10px;width:70px;text-align:center;padding:0}.MonthPopup .DateArrow{margin:5px 0;cursor:pointer}.CalendarMain .MonthPopup.Border{background-color:#fafafa}.CalendarMain .MonthPopup.Border{border:0;left:0;top:-100%;padding:0 1px}.CalendarMain .MonthPopup.hasYear .MonthBlock,.CalendarMain .MonthPopup.hasYear .YearBlock{margin-top:0;padding-top:5px}.TreeBlock{display:flex;background-color:#fff;border:1px solid #ddd;flex-direction:column;overflow:hidden;flex-grow:1;flex-shrink:1;flex-basis:auto}.TreeView{padding:0 5px;overflow:auto;outline:0;flex-shrink:1;flex-grow:1;flex-basis:auto;background-color:#fff}.TreeNode{display:flex;line-height:26px;cursor:default;position:relative;border-radius:4px}.TreeNode .ChkBoxStyle{height:auto;line-height:auto}.TreeNode.dragChild{box-shadow:inset 0 0 0 1px #2288fc}
.TreeNode.selected,.TreeNode:hover{color:#2288fc;background-color:#e6f2ff}.TreeNode.disabled:hover,.TreeNode.disabled{background-color:inherit;color:#aaa}.TreeView.AllowClick .TreeNode{cursor:pointer}.TreeView.AllowDrag .TreeNode{cursor:grabbing}.TreeBlock.NoWrap .TextBlock{word-break:keep-all;word-wrap:normal;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.TreeView .NodeEdit{height:22px;line-height:22px;margin-top:2px}.TreeView .Children0{overflow:hidden}.TreeView.ShowColumns{display:flex;overflow:hidden;border:0}.TreeView.ShowColumns .Children0{overflow:auto;flex:auto;flex:auto;flex-basis:0}.TreeView.ShowColumns .Children0:nth-child(n+2){border-left:1px solid #eee}.TreeView.ShowColumns .TreeNode{padding:0 6px}.TreeBlock .TreeToolBar{display:flex;flex-direction:column;position:relative}.TreeBlock .Buttons{background-color:#f6f6f6;display:block;height:28px;position:relative}.TreeBlock .Buttons .Button{border:0;float:none;margin:0;border-radius:0;color:#000;background-color:inherit}
.TreeBlock .Buttons .Button:before{margin:0}.TreeBlock .Buttons .Button.active{background-color:inherit;color:#000}.TreeBlock .Buttons .Button:hover{background-color:#ccc}.TreeBlock .SearchBar{position:relative;display:flex}.TreeBlock .TreeToolBar .Edit{margin:5px;padding-left:15px}.TreeBlock .TreeToolBar .aicon-chaxun{position:absolute;left:8px;top:11px;margin:0;cursor:pointer}.TreeBlock.ShowEditor .TreeToolBar .Buttons,.TreeBlock.ShowToolBar .TreeToolBar .SearchBar{display:none}.TreeNode .LevelBlock{flex-shrink:0}.NodeLevel0{width:0}.NodeLevel1{width:20px}.NodeLevel2{width:40px}.NodeLevel3{width:60px}.NodeLevel4{width:80px}.NodeLevel5{width:100px}.NodeLevel6{width:120px}.NodeLevel7{width:140px}.NodeLevel8{width:160px}.NodeLevel9{width:180px}.NodeLevel10{width:200px}.NodeLevel11{width:220px}.NodeLevel12{width:240px}.NodeLevel13{width:260px}.NodeLevel14{width:280px}.NodeLevel15{width:300px}.NodeLevel16{width:320px}.NodeLevel17{width:340px}.NodeLevel18{width:360px}.TreeNode .ExpandBlock{width:20px;height:30px;text-align:center;flex-shrink:0;cursor:pointer}
.TreeNode .ExpandBlock:before{color:#999;display:inline-block;transition:all .2s linear}.TreeNode .ExpandBlock.expand:before{content:"\e7c5"}.TreeNode .ExpandBlock.collapsed:before{content:"\e7c5";transform:rotate(-90deg)}.TreeNode .TextBlock{padding-right:2px;word-break:break-word;word-wrap:break-word;white-space:pre-wrap}.TreeDragBlock{pointer-events:none;cursor:grabbing;position:absolute;display:none;color:#2288fc;box-shadow:0 0 2px 4px #eee;border-radius:4px}.nopointerev{pointer-events:none}.TreeDragBlock .TreeNode{padding-right:18px;cursor:grabbing;background-color:#fff}.TreeDragBlock .LevelBlock{display:none}.DragLine{pointer-events:none;height:2px;background-color:#2288fc;position:absolute;z-index:999}.DragLine:before{content:" ";position:absolute;left:0;top:-3px;width:0;height:0;border-left:4px solid #2288fc;border-bottom:4px solid transparent;border-top:4px solid transparent}.DragLine.vert:before{transform:rotate(90deg);left:-1px;top:-2px}
.TreeNode .EditorBlock{display:flex;position:absolute;right:0;top:0;background-color:#eee}.TreeNode .EditorBlock .SkinButton{padding:0 3px;cursor:pointer;color:#888;font-weight:normal;height:26px}.TreeNode .EditorBlock .SkinButton:hover{color:#fff;background-color:#2288fc}.NodeFadeIn,.NodeFadeOut{-webkit-animation-duration:.2s;animation-duration:.2s;-webkit-animation-fill-mode:both;animation-fill-mode:both;-webkit-animation-play-state:paused;animation-play-state:paused;-webkit-animation-timing-function:ease-in-out;animation-timing-function:ease-in-out}.NodeFadeIn{-webkit-animation-name:transitionDropIn;animation-name:transitionDropIn;-webkit-animation-play-state:running;animation-play-state:running;opacity:0}.NodeFadeOut{-webkit-animation-name:transitionDropOut;animation-name:transitionDropOut;-webkit-animation-play-state:running;animation-play-state:running}.DropStyle,.TreeBlock.DropStyle,.GridBlock.DropStyle{box-shadow:0 0 8px rgba(121,176,237,0.8);cursor:grabbing}.TreeView.ShowLine .ExpandBlock{background-image:url("ui/tree/node0.gif");background-position:0 center;background-repeat:no-repeat}
.TreeView.ShowLine .TreeNode:last-child .ExpandBlock{background-image:url("ui/tree/none.gif");background-position:0 center;background-repeat:no-repeat}.TreeView.ShowLine .iconfont.ExpandBlock.expand,.TreeView.ShowLine .iconfont.ExpandBlock.collapsed{background-image:url("ui/tree/node.gif");background-position:0 center;background-repeat:no-repeat}.TreeView.ShowLine .ChildrenBlock *:nth-last-child(2) .iconfont.ExpandBlock.collapsed,.TreeView.ShowLine .ChildrenBlock *:nth-last-child(2) .iconfont.ExpandBlock.expand{background-image:url("ui/tree/last.gif");background-position:0 center;background-repeat:no-repeat}.TreeView.ShowLine>:nth-last-child(1)>:nth-last-child(2) .ExpandBlock.expand,.TreeView.ShowLine>:nth-last-child(1)>:nth-last-child(2) .ExpandBlock.collapsed{background-image:url("ui/tree/last.gif");background-position:0 center;background-repeat:no-repeat}.TreeView.ShowLine .Children0>.TreeNode.hasChild:first-child .iconfont.ExpandBlock{background-image:url("ui/tree/first.gif")}
.TreeView.ShowLine .Children0>.TreeNode:first-child .iconfont.ExpandBlock{background-image:url("ui/tree/firstNo.gif")}.TreeView.ShowLine .ChildrenBlock{background-image:url("ui/tree/line.gif");background-repeat:repeat-y}.TreeView.ShowLine .Children1{background-position:0 0}.TreeView.ShowLine .Children2{background-position:20px 0}.TreeView.ShowLine .Children3{background-position:40px 0}.TreeView.ShowLine .Children4{background-position:60px 0}.TreeView.ShowLine .Children5{background-position:80px 0}.TreeView.ShowLine .Children6{background-position:100px 0}.TreeView.ShowLine .Children7{background-position:120px 0}.TreeView.ShowLine .Children8{background-position:140px 0}.TreeView.ShowLine .ChildrenBlock:last-child{background-image:none}.Splitter{display:flex;border-width:0;border-style:solid;border-color:#ddd;flex-shrink:0;color:#2288fc;border-color:#dfdfe8}.VSplitter{cursor:ew-resize;width:10px;justify-content:flex-start;align-items:center}.VSplitter.VLeft{border-left-width:1px}.VSplitter.VRight{border-right-width:1px;justify-content:flex-end}
.VSplitter .Shape{flex-direction:column;width:14px;padding:8px 0;text-align:center;background:linear-gradient(-90deg,#e7f9ff 0,#fff 100%);border:1px solid #dfdfe8;border-left-width:0;height:50px}.VSplitter.hasText .Shape{height:auto;width:18px}.HSplitter{cursor:ns-resize;height:10px;justify-content:center;align-items:flex-start}.HSplitter.HTop,.HSplitter.HBottom{border-top-width:1px;margin-bottom:0}.HSplitter .Shape{padding:0 8px;height:14px;background:linear-gradient(180deg,#e7f9ff 0,#fff 100%);border:1px solid #dfdfe8;border-top-width:0;width:50px}.HSplitter.hasText .Shape{width:auto;height:18px}.HSplitter.hasText .Shape{flex-direction:row-reverse;letter-spacing:2px}.VSplitter.collapsed,.VSplitter.nodrag,.HSplitter.nodrag{cursor:default;border:0}.HSplitter.collapsed{cursor:default;height:19px}.HSplitter.collapsed:hover{border-color:#ddd}.Splitter .Shape{overflow:hidden;flex-shrink:0;display:flex;align-items:center;justify-content:center;cursor:pointer;z-index:3}.Splitter .Shape:hover{background-color:#2288fc;background-image:none;border-color:#2288fc;color:#fff}
.Splitter .Shape:before{font-size:11px}.VSplitter.hasText .Shape:before{margin-bottom:3px}.HSplitter.hasText .Shape:before{margin-left:3px}.VSplitter.collapsed .Shape:before,.VSplitter.VRight .Shape:before{transform:rotate(-180deg)}.VSplitter.VRight.collapsed .Shape:before{transform:none}.VSplitter.VLeft.collapsed .Shape{position:absolute;left:0}.VSplitter.VRight.collapsed .Shape{position:absolute;right:0}.HSplitter.HBottom.collapsed .Shape:before,.HSplitter.HTop .Shape:before{transform:rotate(90deg)}.HSplitter.HTop.collapsed .Shape:before,.HSplitter.HBottom .Shape:before{transform:rotate(-90deg)}.VSplitter.VLeft .Shape{border-radius:0 3px 3px 0}.VSplitter.VRight .Shape{border-radius:3px 0 0 3px;border-right-width:0;border-left-width:1px}.HSplitter.HBottom .Shape,.HSplitter.HTop .Shape{border-radius:0 0 3px 3px}.HSplitter.HTop .Shape{margin-top:-1px}.VSplitter.collapsed{width:0}.VSplitter.collapsed{margin:0 !important}.HSplitter.noShape{border-top-width:1px;border-bottom-width:0;margin:10px 0 5px 0;position:relative}
.HSplitter.noShape:hover .Shape{border-color:#2288fc}.HSplitter.noShape .Shape{cursor:ns-resize;border:1px solid #ddd;background-color:#fafafa;border-radius:4px;width:32px;height:14px;top:-7px;position:absolute;font-size:8px;font-style:normal}.VSplitter.noShape{border-left-width:1px;border-right-width:0;margin:0 5px 0 5px;position:relative}.VSplitter.noShape .Shape{cursor:ew-resize;border:1px solid #ddd;background-color:#fafafa;border-radius:4px;width:14px;height:32px;left:-7px;position:absolute}.VSplitter.noShape.collapsed,.HSplitter.noShape.collapsed{display:none}.HSplitter.prevGrid{margin-top:-6px}.HSplitter.prevGrid0{margin-top:-1px}.HSplitter.prevGrid,.HSplitter.prevGrid0{z-index:3;height:5px}.HSplitter.nextTab{margin-bottom:6px}.HSplitter.nextTab.collapsed{margin-bottom:6px}.HSplitter.prevGrid.collapsed .Shape,.HSplitter.prevGrid0.collapsed .Shape{border-radius:3px 3px 0 0;margin-top:-14px;border-top-width:1px}.HSplitter.prevGrid.collapsed.hasText .Shape,.HSplitter.prevGrid0.collapsed.hasText .Shape{margin-top:-18px}
.Splitter:hover,.Splitter.hover{border-color:#2288fc}.CheckBoxList,.RadioButtonList{overflow:auto;box-shadow:none;display:inline-flex;flex-shrink:0;align-items:center;flex-wrap:wrap}.RadioButtonList.Vert,.CheckBoxList.Vert{align-items:flex-start;flex-direction:column}.RadioButtonList .clear,.CheckBoxList .clear{width:100%}.CheckBoxList .ChkBoxStyle,.RadioButtonList .ChkBoxStyle{margin-right:10px}.CheckBoxList.item20 .ChkBoxStyle,.RadioButtonList.item20 .ChkBoxStyle{margin-right:20px}.CheckBoxList.item30 .ChkBoxStyle,.RadioButtonList.item30 .ChkBoxStyle{margin-right:30px}.RadioButtonList.Vert .VertItem,.CheckBoxList.Vert .VertItem{margin-right:0;width:100%;height:28px;line-height:28px}.RadioButtonList.Vert .VertItem:last-child{margin-bottom:0}.RadioButtonList.Border,.CheckBoxList.Border{padding-left:5px}.RadioButtonList.Vert.Border,.CheckBoxList.Vert.Border{padding-left:0}.RadioButtonList.Vert.Border,.CheckBoxList.Vert.Border{padding-left:0}.RadioButtonList.Vert.Border .VertItem,.CheckBoxList.Vert.Border .VertItem{padding:0 5px}
.CheckBoxList.Border{margin-bottom:0}.CheckBox.Border,.CheckBoxList.Border .ChkBoxStyle{margin-bottom:5px;height:36px;line-height:34px;border:1px solid #ccc;border-radius:4px;padding:0 10px 0 10px}.ChkBoxStyle{flex-shrink:0;position:relative;display:inline-flex;align-items:center;height:28px;line-height:28px}.Grid .ChkBoxStyle{height:21px;line-height:21px}.ChkBoxStyle:hover:before{border-color:#2288fc}.ChkBoxStyle:before{flex-shrink:0;white-space:nowrap;text-align:center;display:inline-block;content:".";color:#fff;background-color:#fff;margin:0 5px 0 0;width:12px;height:12px;line-height:12px;font-weight:normal;font-size:12px;border:1px solid #ccc}.ChkBoxStyle.dnone{display:none}.ChkBoxStyle.disabled input{visibility:hidden}.ChkBoxStyle input{padding:0;margin:0;width:16px;height:16px;position:absolute;left:0;top:5px;opacity:0;filter:alpha(opacity=0)}.ChkBoxStyle label{flex:auto;display:inline-block;font-weight:normal;word-break:keep-all;white-space:pre;cursor:pointer}
.chk_label{cursor:pointer}.ChkBoxStyle img{vertical-align:middle}.ChkBoxStyle.disabled label{cursor:default}.ChkBoxStyle.checked:before{background-color:#2288fc;border-color:#2288fc;content:"\e81d";color:#fff;font-family:"iconfont" !important}.ChkBoxStyle.checked:hover:before{box-shadow:0 0 0 2px #e2f0ff}.ChkBoxStyle.disabled:before{border-color:#ccc !important;background-color:#eee !important;color:#eee}.ChkBoxStyle.checked.disabled:before{border-color:#eee;content:"\e81d";color:#2288fc;font-family:"iconfont" !important}.ChkBoxStyle.half:before{border-color:#2288fc;content:"-";color:#2288fc;background-color:#fff}.CheckBox.NormalBorder{padding-left:10px}.CheckBox.NormalBorder input{left:10px}.OnlyChk.ChkBoxStyle:before{margin:0;padding:0}.RadioButtonList .ChkBoxStyle:before,.RadioButton.ChkBoxStyle:before{content:" ";border-radius:50%;border-width:1px;background-color:#fff}
.RadioButtonList .ChkBoxStyle.disabled:before,.RadioButton.ChkBoxStyle.disabled:before{content:" ";background-color:#eee;border-color:#ccc}.RadioButtonList .ChkBoxStyle.checked:before,.RadioButton.ChkBoxStyle.checked:before{content:" ";background-color:#fff;border-color:#2288fc;width:6px;height:6px;border-width:4px}.RadioButtonList .ChkBoxStyle:hover:before,.RadioButton.ChkBoxStyle:hover:before{color:#2288fc}.RadioButtonList .ChkBoxStyle.checked.disabled:before,.RadioButton.ChkBoxStyle.checked.disabled:before{content:" ";background-color:#2288fc;border-color:#ccc;width:5px;height:5px;border-width:5px}.DropPopup{position:absolute;left:-2000px;top:-2000px;display:flex;flex-direction:column}.DropPopup .DropItem{display:block;text-indent:10px;white-space:nowrap;cursor:default;text-overflow:ellipsis;overflow:hidden}.DropPopup .DropItem img{vertical-align:middle}.DropItem.normal{background-color:#fff;color:#000}.DropItem.selected{background-color:#e0efff;color:#2288fc}
.DropItem.normal:hover{background-color:#eff1f6}.DropItem.disabled,.DropItem.disabled:hover{background-color:transparent}.CheckBoxListPopup{position:absolute;left:-2000px;top:-2000px;max-width:90%;display:flex;flex-direction:column}.CheckBoxListPopup .clear{width:100%}.CheckBoxListPopup.Vert .DropContainer{display:flex;flex-direction:column;flex-wrap:nowrap;flex-shrink:1}.CheckBoxListPopup .ChkBoxStyle{padding:0 5px}.CheckBoxListPopup .VertItem{flex-shrink:0}.CheckBoxListPopup .VertItem:hover{background-color:#fef5e7;color:#fe9f13}.CheckBoxListPopup .Button{align-self:flex-start;margin:9px;min-width:60px}.DropContainer{display:flex;flex-wrap:wrap;overflow:auto;flex:1 1 auto;border-top:1px solid #eee}.DropPopup .DropContainer{flex-direction:column;flex-wrap:nowrap;position:relative}.DropSearchEdit{margin:5px;min-width:20px;flex:0 0 auto;padding-left:22px;width:auto}.DropSearchIcon{margin:0;position:absolute;left:10px;top:5px;line-height:30px;height:30px}
.DropBottomBlock{border-top:1px solid #eee;align-items:center;justify-content:center;display:flex}.CheckBoxListPopup .ChkBoxStyle,.DropPopup .DropItem{line-height:30px;height:30px;flex-shrink:0}.CheckBoxListPopup img{max-height:30px}.CheckBoxListPopup .ChkBoxStyle input{left:9px;top:7px}.colorItem{width:16px;height:16px;display:inline-block;vertical-align:middle;margin-right:5px}.LayoutGroupBorder{border:1px solid #ddd;margin-bottom:5px;text-align:left;border-radius:1px;-moz-border-radius:1px;-webkit-border-radius:1px;flex-shrink:0;overflow:hidden}.LayoutGroupBlock{flex-shrink:0}.LayoutBlock{flex-shrink:0;margin:5px 0}.NoMargin{margin:0}.NoMargin .LayoutBlock{margin:0}.NoMargin .LayoutGroupVSpacer{display:none}.NoMargin .HBlock{padding:0}.LayoutGroupBorder .LayoutGroupBorder{border-top-style:solid}.LayoutGroupVSpacer{height:4px}.LayoutItemLabelVSpace{padding-top:3px}.LayoutItemControlVSpace{padding-top:3px}.LayoutItemLabel{white-space:nowrap;padding-left:8px;padding-right:8px;line-height:32px}
.LayoutItemControl{padding-right:6px}.LayoutItemControl .Label{line-height:28px;margin-top:0}.LayoutItemNoLabel{padding-top:6px;padding-right:0}.LayoutFirstItemNoLabel{padding-left:0;padding-top:3px;padding-right:0}.NavBar{border:1px solid #ddd;background-color:#fff;display:flex;flex-direction:column;flex-shrink:0;position:relative}.NavBar .NBGroupHeader{cursor:pointer;color:#333;line-height:30px;background-color:#eff3f6;overflow:hidden}.NavBar .NBCaptionText{text-indent:10px;float:left}.NavBar .NBCaptionText:before{margin-right:8px;width:20px}.NavBar .NBIcon{color:#777;margin-right:4px;float:right}.NavBar.noIcon .NBIcon{display:none}.NavBar .NBGroupBody{overflow:hidden;padding:5px;-webkit-transition:height .1s linear;-moz-transition:height .1s linear;transition:height .1s linear}.NavBar .NBGroupBody.collapsed{padding:0;height:0}.NavBar .NavGroup.expand{flex:auto}.NavBar.Normal{border-top-width:1px}.NavBar.Normal .NBGroupHeader{background-color:#fff;border:0;position:relative;cursor:pointer}
.NavBar .NBGroupHeader:hover{color:#2288fc;background-color:#edf4fe}.NavBar.Normal .NBCaptionText{padding-left:20px}.NavBar.Normal .NBIcon{position:absolute;left:10px}.NavBar.GroupMargin10{background-color:transparent}.NavBar.GroupMargin10 .NavGroup{margin-bottom:10px;background-color:#fff;border-radius:4px}.NavBar.GroupMargin10 .NavGroup:last-child{margin-bottom:0}.ToolBar{height:36px;line-height:36px;border:1px solid #ccc;background-color:#eff3f6;overflow:hidden;flex-shrink:0}.ToolBarNoBottomLine{border-bottom-style:none}.CustomToolBarRows{display:flex;flex-wrap:wrap}.ToolBarNoTopLine{border-top-style:none}.ToolBarText{float:left}.ToolBarHandle{width:0;height:34px;float:left;margin:0}.ToolButton{float:left;cursor:pointer;text-align:center;height:36px;padding:0 8px}.ToolButton img{margin-top:4px;width:16px;height:16px}.ToolSpeedText{white-space:nowrap;vertical-align:middle}.ToolSeperator{float:left;width:1px;height:23px;background-color:#ccc;margin:0 5px;margin-top:6px;box-shadow:0 0 4px #ccc}
.ToolButton:hover{background-color:#bbdcf5}.YBar{right:0;top:0;width:8px;height:100%}.YBar.small{width:4px}.XBar{left:0;bottom:0;width:100%;height:8px}.XBar.small{height:4px}.XBar .BarValue{height:100%;width:100px}.YBar .BarValue{height:100px;width:100%}.CrabaScrollBar{position:absolute;background-color:rgb(100 100 100 / .2);border-radius:6px;z-index:2;display:none}.CrabaScrollBar .BarValue{position:absolute;opacity:.4;background-color:#888;border-radius:6px;z-index:3}.CrabaScrollBar:hover .BarValue,.CrabaScrollBar.drag .BarValue{opacity:.8;background-color:#666}::-webkit-scrollbar-thumb{background-color:#ccc;border-radius:6px}::-webkit-scrollbar-thumb:hover{background-color:#999}::-webkit-scrollbar{width:8px;height:8px}::-webkit-scrollbar-track{background-color:rgba(150,150,150,0.1);border-radius:6px}::-webkit-scrollbar-button{display:none}.ListBox{background-color:#fff;border:1px solid #ddd;width:118px;cursor:default;box-shadow:none;border-radius:2px;overflow:auto}.ListBox img{width:10px;height:10px}
.ListBox.disabled .BoxItem,.ListBox .BoxItem.disabled{color:#a0a0a0;cursor:not-allowed}.ListBox .BoxItem{line-height:25px;text-indent:5px;cursor:default;color:#333;border-bottom:1px solid #fff}.ListBox .BoxItem:before{margin-right:5px}.ListBox .BoxItem:hover{background-color:#eee}.ListBox .BoxItem.active{background-color:#fefae3}.ListBox.NoWrap .BoxItem{word-break:keep-all;word-wrap:normal;white-space:nowrap;display:inline-flex;min-width:100%}.ListBox.Dot .BoxItem{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;word-wrap:normal;word-break:keep-all}.FlexBlock>.ListBox{display:flex;flex-shrink:1;flex-grow:1;flex-basis:auto;width:auto;flex-direction:column}.ArrowRight,.CalendarMain .ArrowRight{border-left:6px solid #777;border-right:6px solid transparent;border-top:6px solid transparent;border-bottom:6px solid transparent;font-size:0;padding:0;overflow:hidden;display:inline-block;width:0;height:0;float:none;background:0}.ArrowLeft{border-right:6px solid #777;border-left:6px solid transparent;border-top:6px solid transparent;border-bottom:6px solid transparent;font-size:0;padding:0;overflow:hidden;display:inline-block;width:0;height:0}
.ArrowLeft:hover{border-right-color:#00b7f3}.ArrowRight:hover{border-left-color:#00b7f3}.ArrowDown,.Button.ArrowDown{border-right:6px solid transparent;border-left:6px solid transparent;border-top:6px solid #777;border-bottom:6px solid transparent;font-size:0;padding:0;overflow:hidden;display:inline-block;width:0;height:0}.ArrowUp{border-right:6px solid transparent;border-left:6px solid transparent;border-top:6px solid transparent;border-bottom:6px solid #777;font-size:0;padding:0;overflow:hidden;display:inline-block;width:0;height:0}.HtmlEditorItemBorder{cursor:pointer;border-bottom:1px solid #b0daeb}.HtmlEditorListElement{text-align:center;line-height:2;padding:0 10px;cursor:pointer;border-bottom:1px solid #b0daeb}.HtmlEditorListElement:hover{border-color:#74bbd8;background-color:#fdfcf4}.HtmlEditor{flex:auto;border:1px solid #d9d6d1;overflow:hidden;display:flex;flex-direction:column;min-height:150px}.HtmlEditor table{border-spacing:1px}.HtmlEditor.ShowFull{position:fixed;left:0;top:0;width:100% !important;height:100% !important;background-color:#fff}
.HtmlEditor .ToolBar{height:auto;border:0;background-image:none;background-color:#f9f9f8;padding:1px}.HtmlEditor .ToolBar,.HtmlEditor .HtmlEditorBody{border:0}.HtmlEditor .HtmlEditorBody{border-top:1px solid #d9d6d1;overflow:auto;font-size:14px;line-height:normal;padding:10px;flex:auto}.HtmlEditor .DisabledOverlay{width:100% !important;height:100% !important}.HtmlArea:before{cursor:pointer;margin:20px;font-size:40px}.HtmlEditor.Normal .insertSwf,.HtmlEditor.Normal .undo,.HtmlEditor.Normal .redo,.HtmlEditor.Normal .imgJustify,.HtmlEditor.Normal .ToolSeperator,.HtmlEditor.Normal .preview,.HtmlEditor.Normal .insertFace,.HtmlEditor.Normal .togglePara,.HtmlEditor.Normal .insertTextArea,.HtmlEditor.Normal .fontName,.HtmlEditor.Normal .paste,.HtmlEditor.Normal .cut,.HtmlEditor.Normal .copy,.HtmlEditor.Normal .insertHr,.HtmlEditor.Normal .insertImg,.HtmlEditor.Normal .insertTable,.HtmlEditor.Normal .paraFormat{display:none}.HtmlEditor .ToolButton{font-weight:normal;color:#111}
.HtmlEditor .ToolButton:before{font-size:20px}.transition1{-webkit-transition:all .1s linear;-moz-transition:all .1s linear;transition:all .1s linear}.transition2{-webkit-transition:all .2s linear;-moz-transition:all .2s linear;transition:all .2s linear}.transition3,.Popup.transition3{-webkit-transition:all .3s linear;-moz-transition:all .3s linear;transition:all .3s linear}.transition4{-webkit-transition:all .4s linear;-moz-transition:all .4s linear;transition:all .4s linear}.transition5{-webkit-transition:all .5s linear;-moz-transition:all .5s linear;transition:all .5s linear}.transition6{-webkit-transition:all .6s linear;-moz-transition:all .6s linear;transition:all .6s linear}.transition10{-webkit-transition:all 1s linear;-moz-transition:all 1s linear;transition:all 1s linear}.opacity{opacity:1;-webkit-transition:opacity .4s linear;-moz-transition:opacity .4s linear;transition:opacity .4s linear}input::placeholder,textarea::placeholder{font-size:12px !important;color:#bbb}input::-webkit-input-placeholder,textarea::-webkit-input-placeholder{font-size:12px !important;color:#bbb}
input:-moz-placeholder,textarea:-moz-placeholder{font-size:12px !important;color:#bbb}input:-ms-input-placeholder,textarea:-ms-input-placeholder{font-size:12px !important;color:#bbb}.placeholder24::-webkit-input-placeholder{font-size:24px !important;font-weight:none;text-indent:18px;font-weight:500}.placeholder24:-moz-placeholder{font-size:24px !important;font-weight:none;text-indent:18px;font-weight:500}.placeholder24:-ms-input-placeholder{font-size:24px !important;font-weight:none;text-indent:18px;font-weight:500}.placeholder24::placeholder{font-size:24px !important;font-weight:none;text-indent:18px;font-weight:500}input::-webkit-input-safebox-button{display:none}#sogou_secure_inputs_container{display:none}.dselect{user-select:none;-moz-user-select:none;-ms-user-select:none;-webkit-user-select:none}.ATag{display:inline-block;line-height:22px;padding:0 5px;cursor:pointer}.ErrMessage{color:red}.dnone{display:none !important}.dblock{display:block !important}.dinline{display:inline-block}
.dinlineflex{display:inline-flex !important}.dflex{display:flex !important}.dflex1{display:flex}.dflexv{display:flex !important;align-items:center}.vcenter{align-items:center}.hcenter{justify-content:center !important}.hleft{justify-content:flex-start !important}.dflexv1{display:flex;align-items:center}.dflexnone{flex:none !important}.dflex0{flex:0}.dflexHorz{display:flex}.dflexHorz>*{margin-right:5px}.dflexHorz:last-child{margin-right:0}.posrel{position:relative}.posabs{position:absolute}.posfixed{position:fixed}.w100s{width:100% !important}.height10{height:10px}.height22{height:22px}.height24{height:24px}.height26{height:26px}.height28{height:28px}.height30{height:30px}.height32{height:32px}.height34{height:34px}.height40{height:40px}.height50{height:50px}.height120{height:120px}.height200{height:200px}.height300{height:300px}.height400{height:400px}.width80{width:80px}.width100{width:100px}.width120{width:120px}.width150{width:150px}.width200{width:200px}
.lh22{line-height:22px}.lh24{line-height:24px}.lh26{line-height:26px}.lh28{line-height:28px}.lh30{line-height:30px}.lh32{line-height:32px}.lh34{line-height:34px}.mr0{margin-right:0 !important}.mb0{margin-bottom:0 !important}.pr0{padding-right:0 !important}.pr5{padding-right:5px !important}.pl5{padding-left:5px}.pl10{padding-left:10px !important}.pl20{padding-left:20px !important}.pl35{padding-left:35px !important}.pr10{padding-right:10px !important}.margin0{margin:0 !important}.margin10{margin:10px !important}.margin20{margin:20px !important}.margin0tree{margin:0}.margin0tree>.TreeBlock{margin:0}.margin5{margin:5px}.marginAuto{margin:auto}.mt20{margin-top:20px !important}.ml10{margin-left:10px !important}.mr20{margin-right:20px !important}.mb10{margin-bottom:10px !important}.mb20{margin-bottom:20px !important}.mt30{margin-top:30px !important}.mt40{margin-top:40px !important}.ml20{margin-left:20px !important}.ml15{margin-left:15px !important}.ml5{margin-left:5px !important}.mt10{margin-top:10px !important}
.mb5{margin-bottom:5px !important}.mt5{margin-top:5px !important}.mtb5{margin-top:5px !important;margin-bottom:5px !important}.mtb10{margin-top:10px !important;margin-bottom:10px !important}.mts6{margin-top:6px !important}.mr5{margin-right:5px !important}.mr10{margin-right:10px !important}.pdl5{padding-left:5px !important}.pdt5{padding-top:5px !important}.pd0{padding:0 !important}.pt20{padding-top:20px !important}.pt10{padding-top:10px !important}.pd10{padding:10px !important}.pd20{padding:20px !important}.pd30{padding:30px !important}.pdt0{padding-top:0 !important}.pdb0{padding-bottom:0 !important}.plr0{padding-left:0 !important;padding-right:0 !important}.pltr10{padding-left:10px !important;padding-top:10px !important;padding-right:10px !important}.plr5{padding-left:5px !important;padding-right:5px !important}.plr10{padding-left:10px !important;padding-right:10px !important}.plr15{padding-left:15px !important;padding-right:15px !important}.plr20{padding-left:20px !important;padding-right:20px !important}
.ptb10{padding-top:10px !important;padding-bottom:10px !important}.ptb20{padding-top:20px !important;padding-bottom:20px !important}.mlr10{margin-left:10px !important;margin-right:10px !important}.mlr15{margin-left:15px !important;margin-right:15px !important}.FlowPanel.vert.plr10{padding-right:10px}.redcolor{color:red}.bluecolor{color:#00f}.maincolor{color:#2288fc}.ccc{color:#ccc}.aaa{color:#aaa}.bbb{color:#bbb}.c111{color:#111}.c222{color:#222}.c333{color:#333}.c444{color:#444}.c555{color:#555}.c666{color:#666}.c777{color:#777}.c888{color:#888}.c999{color:#999}.bold,.Bold,.ChkBoxStyle.bold label{font-weight:bold}.radius4{border-radius:4px !important}.radius6{border-radius:6px !important}.radius8{border-radius:8px !important}.radius10{border-radius:10px !important}.radius12{border-radius:12px !important}.radius14{border-radius:14px !important}.radius15{border-radius:15px !important}.radius50p{border-radius:50% !important}.tright{text-align:right !important}.tleft{text-align:left !important}
.tcenter{text-align:center !important}.tshadow{text-shadow:1px 1px 2px #aaa}.shadow{box-shadow:0 1px 2px #ccc}.shadownone{box-shadow:none}.aicon-uploader{border:1px dashed #ccc;padding:0;color:#ccc;border-radius:4px;display:flex;align-items:center;justify-content:center;cursor:pointer;position:relative;flex-shrink:0;overflow:hidden;text-indent:0}.fileIcon{position:relative;text-indent:0}.aicon-uploader:hover{border-color:#bbb;color:#aaa}.aicon-uploader:before{font-size:40px;content:"\e6b9"}.fileIcon .FileEditor,.fileIcon .FileEdit,.fileIcon form,.aicon-uploader .ButtonOpacity,.aicon-uploader form{position:absolute;top:0;left:0;width:100%;height:100%;cursor:pointer}.fileIcon .FileEditor,.aicon-uploader .FileEditor{background-color:transparent;border:0;width:100%;height:100%}.fileIcon .FileButton,.fileIcon .FileName,.HideName .FileName,.uploader.Button .FileEditor .FileName,.aicon-uploader .FileEditor .FileButton,.aicon-uploader .FileEditor .FileName{display:none !important}
.aicon-uploader .Label{line-height:normal;height:auto}.uploader.Button{padding:0}.uploader.Button .FileEditor{color:inherit;padding:0 10px;text-indent:0;border:0;background-color:transparent;width:100% !important;height:auto !important;line-height:inherit !important}.uploader.Button .FileButton{width:auto;color:inherit}.uploader.Button .FileEdit{cursor:pointer;width:100%}.HintForm{background-color:#fff;min-width:200px;max-width:600px;min-height:70px;position:fixed;right:-200px;bottom:1px;border:1px solid #2881c7;z-index:99999}.HintCaption{line-height:24px;text-indent:10px;background-color:#2881c7;color:#fff}.HintClose{position:absolute;right:0;top:0;width:24px;height:24px;background-color:#288fc2;text-align:center;line-height:24px;color:#fff}.HintClose:hover{color:#fff;background-color:#fb7070}.HintContent{line-height:30px;text-indent:12px;padding:5px}.Loading{position:absolute;left:-1000px;top:-1000px;padding:0 20px;height:54px;line-height:54px;text-align:center;white-space:nowrap;word-wrap:normal;word-break:keep-all;border:1px solid #555;background-color:#555;color:#fff;border-radius:8px;box-shadow:0 0 6px #bbb;display:inline-block}
.LoadingIcon{display:inline-block;-webkit-animation:LoadingIconSnake .8s infinite linear;animation:LoadingIconSnake .8s infinite linear;margin:0 10px}.LoadingIcon:before{content:"\e653"}.WaitingBlock{position:relative}.WaitingBlock .ProgressBarFor{width:50%}.WaitingBlock .LoadingFor,.WaitingBlock .ProgressBarFor{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);z-index:2}.WaitingBlock .LoadingFor{display:flex;align-items:center;width:100%;justify-content:center}.WaitingBlock .LoadingMask{position:absolute;width:100%;height:100%;z-index:9999;left:0;top:0;background-color:rgba(200,200,200,0.2)}#LoadingFirst{position:absolute;left:45.5%;top:46.5%}.ECharts{flex-shrink:1;flex-grow:1;flex-basis:auto;overflow:hidden}.ECharts:first-child{height:100%;width:100%}.DropPopup.Border,.CheckBoxListPopup.Border,.CalendarMain.Border,.ColorPopup.Border,.CalculatorPopup.Border{border-top-left-radius:0;border-top-right-radius:0}.Popup,.ColorPopup.Border,.TimePopup,.SelfDropPopup,.CalendarRange.Border,.CalculatorPopup.Border,.MonthPopup.Border,.CalendarMain.Border,.CheckBoxListPopup.Border,.DropPopup.Border{box-shadow:0 2px 10px 0 #e0e0e0}
.DrpFadeIn,.DrpFadeOut{animation-duration:.3s;animation-fill-mode:both;animation-play-state:paused;animation-timing-function:ease-in-out}.DrpFadeIn{animation-name:transitionDropIn;animation-play-state:running;opacity:0}.DrpFadeOut{animation-name:transitionDropOut;animation-play-state:running}.DrpOrange{will-change:top,left;transform-origin:center top 0}.DrpUp{will-change:bottom,left;transform-origin:center bottom 0}.Popup.PrintMenuPopup{text-align:center;width:220px;background-color:#eaeaea;box-shadow:0 0 8px #333}.PrintMenuPopup .Button{display:block;margin:8px auto;width:180px;height:34px;line-height:34px;border-radius:4px}.PrintMenuPopup .PrintButton .Button{width:160px;border-radius:4px 0 0 4px}.PrintMenuPopup .PrintButton .RightIcon.Button{border-radius:0 4px 4px 0}.PrintMenuPopup .Label.version{color:#aaa}.BrowserCheckContent{text-align:center;width:700px}.BrowserCheckContent .Title{margin:20px auto;text-align:center;font-size:22px;font-weight:bold;display:block}.BrowserCheckContent .Title p{line-height:20px;margin:5px auto;font-size:14px;display:block}
.BrowserCheckContent .Tips{margin:0 auto;text-align:left;font-size:14px;color:#aaa}.BrowserCheckContent .Lists{margin:25px auto 35px auto;text-align:center}.BrowserCheckContent .icon{position:relative;border-radius:6px;width:80px;height:80px;display:inline-block;margin:0 5px;background-image:url("ui/icons/browsers.jpg");background-repeat:no-repeat;overflow:visible}.BrowserCheckContent .icon:hover{box-shadow:0 0 8px #ccc}.BrowserCheckContent .icon .info{width:120px;display:none;position:absolute;left:0;top:85px;line-height:24px;color:#666;text-align:left}.BrowserCheckContent .icon.bwsgjp .info{width:640px}.BrowserCheckContent .icon .info .name{display:block;color:#2288fc;font-weight:bold}.BrowserCheckContent .icon:hover .info{display:block}.BrowserCheckContent .b360{background-position:0 0}.BrowserCheckContent .bsougou{background-position:-80px 0}.BrowserCheckContent .bjisu{background-position:-160px 0}.BrowserCheckContent .bqq{background-position:-240px 0}.BrowserCheckContent .bliebao{background-position:-320px 0}
.BrowserCheckContent .bwsgjp{background-position:-400px 0}.CaptionVert{text-align:center}.CaptionVert .GridHeaderCaptionText{margin:0 auto;text-align:center;line-height:24px;white-space:inherit;word-wrap:break-word;word-break:break-all;word-spacing:10px;letter-spacing:10px;width:20px}.Grid .CellChk.vert{display:flex;flex-direction:column;justify-content:center}.Grid .CellChk.horz{display:flex;justify-content:center}.Grid .CellChk.left{display:flex;justify-content:center}.Grid .CellChk .ChkBoxStyle{margin:3px 5px}.Grid .CellChk .ChkBoxStyle label{flex:none}.ImageViewer{position:relative;width:100%;height:100%}.ImageViewer .Button.Arrow{z-index:2;margin:0;position:absolute;top:50%;height:60px;width:60px;line-height:60px;border-radius:30px;margin-top:-30px;opacity:.8}.ImageViewer .Button.LeftArrow{left:5px}.ImageViewer .Button.RightArrow{right:5px}.ImageViewer .Button.CloseBtn{opacity:.8;z-index:3;position:absolute;width:60px;height:60px;line-height:60px;border-radius:30px;right:5px;top:5px;margin:0}
.ImageViewer .Button.CloseBtn:before{margin:0;position:absolute;top:0;left:20px;font-size:20px}.ImageViewer .Button.CloseBtn:hover{border-color:#fb7070;background-color:#fb7070;color:#fff}.ImageViewer .ViewBody{position:relative;display:flex;flex-shrink:1;flex-grow:1;margin:0;flex-basis:auto;align-items:center;justify-content:center;height:100%;overflow:hidden}.ImageViewer.autoSize .ViewBody{overflow:auto;height:calc(100% - 55px)}.ImageViewer img,.ImageViewer embed{position:absolute;z-index:1;opacity:1;transition:opacity .2s linear}.ImageViewer embed.pdf{width:100% !important;height:100%}.ImageViewer embed.txt{width:50% !important;height:80%;background-color:#fff}.ImageViewer img.opacity{opacity:.3}.ImageViewer img.drag{cursor:grabbing}.ImageViewer .ControlBlock{line-height:50px;position:absolute;bottom:0;right:0;z-index:5;height:50px;padding-left:10px;background-color:#000;color:#fff;display:flex;align-items:center}.ImageViewer .ControlBlock .aicon{margin-left:20px;cursor:pointer}
.ImageViewer .ControlBlock .aicon:before{font-size:20px}.ImageViewer .CheckBox input{margin:0 5px;width:20px;height:20px;vertical-align:middle}.ImageViewer .text{position:absolute;top:0;color:#fff;font-size:14px;width:100%;text-align:center;line-height:50px}.ImageViewer .title{z-index:2;background-color:rgba(0,0,0,0.5);font-size:20px}.ImageViewer .ButtonsDiv{position:absolute;bottom:0;left:0;z-index:4;width:100%;background-color:rgba(0,0,0,0.7);height:50px;display:flex;align-items:center;justify-content:center}.ImageViewer .ButtonsDiv .btn{border-radius:10px;height:20px;line-height:20px;background-color:#eee;padding:0 7px;text-align:center;margin:0 5px;cursor:pointer}.ImageViewer .ButtonsDiv .dot{line-height:20px;margin:0 7px;color:#fff}.ImageViewer .ButtonsDiv .btn.cur,.ImageViewer .ButtonsDiv .btn:hover{background-color:#2288fc;color:#fff}.ImageViewer .ButtonsDiv .Flex1{display:none}.ImageViewer .ButtonsDiv .PagerContent{padding:0 10px;display:flex;align-items:center;background-color:#fff;border-radius:4px}
.ImageViewer .ButtonsDiv .PagerContent .Button{margin:0 5px}.ImageViewer .max.aicon{cursor:pointer;position:absolute;right:10px;bottom:0;line-height:40px;z-index:5;color:#fff}.ImageViewer .aicon:hover{color:#2288fc}.AbsViewer{position:absolute;left:0;top:0;z-index:99999;background-color:rgba(0,0,0,0.8)}.ImageViewer.Only .LeftArrow,.ImageViewer.Only .RightArrow,.ImageViewer.Only .ButtonsDiv{display:none}.ImageViewer.Only .ControlBlock{background-color:transparent}.TraceDump{text-align:left;position:fixed;bottom:5px;right:5px;width:600px;height:600px;max-height:100%;max-width:100%;z-index:99999;background-color:rgba(0,0,0,0.9);color:#fff;border-radius:8px;border:1px solid #000;box-shadow:0 0 8px #444;white-space:nowrap;transition:all .1s linear;padding-top:40px}.TraceDump b{color:#f70a58}.TraceDump.ShowFull{width:50%;height:100%;right:0;bottom:0;top:auto !important;background-color:#000;border-radius:0}.TraceDump .btnBlock{opacity:.9;position:absolute;top:0;left:0;width:100%;display:flex;justify-content:flex-end;background-color:#000;cursor:grab}
.TraceDump .btn{line-height:30px;height:30px;border-radius:4px;text-align:center;font-size:12px;cursor:pointer;display:block;padding:0 10px;margin:5px}.TraceDump .TraceContent{overflow:auto;width:100%;height:100%;padding:50px 5px 5px 5px;line-height:24px;word-break:break-word;word-wrap:break-word;white-space:pre-wrap}.TraceDump .closeBtn{background-color:#e91e63}.TraceDump .clearBtn{background-color:#555}.TraceDump .maxBtn{background-color:#2288fc}.TraceDump .copyBtn{background-color:#ff7164}.TraceDump .btn:hover{background-color:#00b7f3}.Popup.PrintMenuPopup .ChkBoxStyle{display:block;margin:8px auto;width:100px}a.TipsLink,.TipsText{color:#2288fc}.PrintErrMsg{color:#666;line-height:32px}.PrintErrorBox.MessageBox .FormCaption{border-bottom-width:1px}.PrintErrorBox .bicon{font-weight:normal;font-size:12px;background-repeat:no-repeat;height:40px;margin:0 20px;background-size:40px;background-position:center top;padding-top:40px}.PrintErrorBox .iconNew{background-image:url("ui/print/new.png")}
.PrintErrorBox .iconOld{background-image:url("ui/print/old.png")}.PrintErrorBox .Title1{font-size:16px;line-height:60px;text-align:center;width:700px}.PrintErrorBox .Title2{font-size:15px;font-weight:bold;line-height:40px;display:block}.PrintErrorBox .ListDot span{display:flex;line-height:30px;align-items:center;font-size:12px}.PrintErrorBox .ListDot span:before{content:" ";width:5px;height:5px;border-radius:2.5px;background-color:#a4a4a4;margin-right:10px}.PrintErrorBox .downLink{position:absolute;right:10px;top:25px}.PrintErrorBox .Item{position:relative;padding:5px 15px;margin-bottom:20px;margin-left:15px;border-radius:2px}.PrintErrorBox .Item:hover{background-color:#ecf2ff}.PrintErrorBox .BottomButtons{display:none}.PrintErrorBox ._Bottom{margin:0 auto;padding:20px 0;display:flex;align-items:center;justify-content:space-around}.PrintErrorBox ._Progress{position:absolute;left:0;top:75px;height:300px;border-left:1px solid #bbb}.PrintErrorBox ._Progress .icon{border:1px solid #bbb;border-radius:9px;width:18px;height:18px;line-height:18px;text-align:center;font-size:12px;position:absolute;left:-10px;top:0;background:#fff;box-shadow:0 0 5px 6px #fff;color:#bbb}
.PrintErrorBox .MessageBoxText{position:relative}.PrintErrorBox ._Progress .icon:nth-child(2){top:130px}.PrintErrorBox ._Progress .icon:nth-child(3){top:290px}.SearchBlock{position:relative;padding:0;display:flex;flex-shrink:0;height:34px}.SearchBlock .SearchButton{position:absolute;right:0;top:0;border-radius:16px;padding:0 8px !important}.SearchBlock .SearchEdit{text-indent:12px;border-radius:4px;flex-grow:1;margin:0;flex-basis:auto;flex-shrink:0;display:flex;width:auto !important;padding-right:32px}.MovieBg{position:relative}.MovieBg:after{content:"";position:absolute;top:0;left:-5%;height:100%;width:50px;background:#fff;z-index:0;opacity:.2;transform:skew(-25deg);animation:movieBg 1s linear infinite;animation-play-state:running}.MaxContent .GridBlock{flex-grow:1 !important;height:auto !important;width:auto !important}.MaxContent .GridBlock .Grid{flex-grow:1 !important;height:auto !important;width:auto !important}.SkinUIPage{padding:20px 30px 15px 30px}
.SkinUIPage .FlowPanel{padding:20px 5px 15px 0}.UIHtmlBox{position:absolute;right:-1000px;bottom:-1000px;width:100px;height:100px;overflow:hidden}.DragBlock{display:inline-block;position:fixed;right:20px;bottom:20px;z-index:10}.DragBlock>.dnone{display:inline-block}.ListHeader.ListViewBlock{height:40px;flex-shrink:0;flex-grow:0;margin-bottom:0}.ListHeader .UI_ListView{border:1px solid #ddd;background-color:#eaeaea;line-height:40px;overflow:hidden}.ListHeader .UI_ListView .UI_ListItem{width:120px;border-right:1px solid #ddd;text-indent:10px}.ListHeader .UI_ListView .UI_ListItem:last-child{border-right:0}.ListBody{margin:0}.ListBody .UI_ListView{border:1px solid #ddd;border-top:0}.ListBody img{width:30px;height:30px;margin-top:5px;margin-left:10px;overflow:hidden;border-radius:15px;cursor:pointer}.ListBody .UI_ListItem{display:flex;float:none;line-height:40px;border-bottom:1px solid #ddd;flex-wrap:wrap}.ListBody .UI_ListItem:hover{background-color:#eee}.ListBody .UI_ListItem .DataText{width:120px;display:block;border-right:1px solid #ddd;text-indent:5px}
.ListBody .UI_ListItem .DataText:last-child{border-right:0}.ListBody .ListPager{border:1px solid #ddd;border-top:0}.ListBody .ListCaption{display:block;flex-shrink:0;text-indent:10px;line-height:40px;background-color:#f3ebd4;color:#000;width:100%}.Workflow.DropStyle{border-color:#337ab7}.Workflow.ShowGrid{background:url("ui/icons/grid.png") repeat}.Workflow{border:1px solid #ccc;background-color:#fff;position:relative;overflow:auto;user-select:none}.Workflow .WorkItem{margin:0;padding:0 15px;flex-shrink:0;z-index:5;position:absolute;height:50px;display:flex;flex-direction:row;align-items:center;cursor:pointer;word-break:keep-all;box-shadow:0 2px 6px 0 rgba(70,81,121,0.09);border-radius:6px;border:1px solid #e9e9e9;background-color:#fff;font-size:13px}.Workflow .WorkItem:before{width:24px;height:24px;background-color:#2288fc;background-color:var(--mainColor);border-radius:4px;margin-right:7px;text-align:center;line-height:24px;color:#fff}.Workflow .WorkItem.vert{flex-direction:column;height:auto;padding:10px}
.Workflow .WorkItem.vert:before{margin-bottom:5px;margin-right:0}.Workflow .WorkItem.noshadow{box-shadow:none}.Workflow .WorkItem.IconRadius{border:0;background-color:transparent;box-shadow:none;height:auto}.Workflow .WorkItem.height40{height:40px}.Workflow .WorkItem.height30{height:30px}.Workflow .WorkItem.IconRadius:before{width:50px;height:50px;line-height:50px;border-radius:50%;background-color:#2288fc;background-color:var(--mainColor);text-align:center;color:#fff;font-size:22px;margin-bottom:10px}.Workflow .WorkItem.IconRadius:hover:before{background-color:#2288fc;background-color:var(--mainColor);color:#fff}.Workflow .WorkItem .WorkResizer{position:absolute;left:-5px;top:-5px;width:calc(100% + 10px);height:calc(100% + 10px);border-color:#2288fc;border:1px solid var(--mainColor)}.Workflow .WorkItem:hover{border-color:#2288fc}.Workflow .WorkItem.text{width:auto;height:auto;line-height:normal;border:0;box-shadow:none;background-color:transparent;padding:0;font-size:12px;cursor:default}
.Workflow .WorkItem.menuIcon:after{margin-left:15px;content:"\e9ff";font-family:'iconfont' !important}.Workflow .WorkItem.link{cursor:pointer}.Workflow .WorkItem.text:hover{box-shadow:none}.Workflow .WorkResizer .FResizer{border-color:#2288fc;border:1px solid var(--mainColor)}.Workflow.NoResize .WorkResizer .DotResizer{display:none}.Workflow.NoResize .Fixed .WorkResizer .DotResizer,.Workflow.NoResize .Group .WorkResizer .DotResizer{display:block}.Workflow.NoLine .WorkResizer .LineResizer{display:none}.Workflow .FResizer.FRightBottom{right:-6px;bottom:-6px}.Workflow .FResizer.FLeft{top:50%;width:10px;height:10px;margin-top:-5px;border-radius:5px}.Workflow .FResizer.FBottom{left:50%;width:10px;margin-left:-5px;border-radius:5px}.Workflow .FResizer.FTop{left:50%;width:10px;margin-left:-5px;border-radius:5px}.Workflow .FResizer.FRight{top:50%;height:10px;border-radius:5px;margin-top:-5px}.Workflow .CrossLine{display:none;position:absolute;left:0;top:0;overflow:hidden}
.Workflow.ShowLine .CrossLine{display:block}.Workflow .CrossLine.Horz{width:100%;height:0;border-top:1px dashed #337ab7}.Workflow .CrossLine.Vert{height:100%;width:0;border-left:1px dashed #337ab7}.Workflow .WorkLine{z-index:3;position:absolute;width:0;height:0;user-select:none;margin:0}.Workflow .WorkLine .Line{width:100%;height:100%;margin:0;padding:0}.Workflow .WorkLine .Text{line-height:30px;padding:0 10px;position:absolute;z-index:4;color:#555;word-break:keep-all}.WorkColorPopup{box-shadow:0 0 7px #aaa}.WorkColorPopup .ColorPopup{cursor:grabbing}.Workflow.Ani .WorkItem,.Workflow.Ani .WorkLine{transition:left,top .3s linear}.Workflow .WorkItem.Group{background:#f6fbff;border-radius:4px;border:1px dashed #cdddff;z-index:1;min-height:80px;cursor:default;box-shadow:none}.Workflow .WorkItem.solidBorder{border-style:solid}.Workflow .ItemText{text-align:center}.Workflow .WorkItem.dashed{border:0;border-bottom:1px dashed #d8e2ff;height:auto;line-height:normal}.Workflow .WorkItem.solid{border:0;border-bottom:1px solid #d8e2ff;height:auto;line-height:normal}
.Workflow .WorkItem.Group .ItemText{position:absolute;top:-20px}.Workflow .WorkItem.Group.List{background-color:#fff;border:1px solid #e3edf9}.Workflow .WorkItem.Group.InnerText .ItemText{top:20px}.Workflow .WorkItem.Group .ItemText,.Workflow .WorkItem.Group.InnerText .ItemText{left:50%;transform:translateX(-50%)}.Workflow .WorkItem.Group.List .ItemText{top:10px;left:auto;transform:none}.Workflow .WorkItem.itemDot{border-radius:0;color:#2288fc;background-color:transparent;border:0;border-bottom:1px solid #fff;height:22px;line-height:22px;padding:0 5px;width:auto;min-width:auto;flex-direction:row;box-shadow:none}.Workflow .WorkItem.itemDot:hover{border-color:#2288fc;background-color:transparent}.Workflow .WorkItem.itemDot:before{content:"";background-color:#2288fc;width:5px;height:5px;border-radius:50%;overflow:hidden;text-decoration:none;margin-right:5px}.Workflow .WorkItem.itemDot.disabled{background-color:transparent;border-color:#fff}.Workflow .SelectRect{border:1px dashed #ccc;position:absolute;background-color:#eee}
.Workflow .WorkResizer .Add{display:none;position:absolute;border-radius:50%;width:30px;height:30px;border-color:#2288fc;color:#2288fc;border:1px solid var(--mainColor);color:var(--mainColor);background-color:#fff;align-items:center;justify-content:center}.Workflow .WorkResizer .Add.right{display:none;top:50%;left:100%;margin-left:20px;margin-top:-15px}.Workflow .WorkResizer .Add.bottom{top:100%;left:50%;margin-top:12px;margin-left:-35px}.Workflow .WorkResizer .Add.del{top:100%;left:50%;margin-top:12px;margin-left:5px}.Workflow .WorkResizer .Add:hover{background-color:#2288fc;background-color:var(--mainColor);color:#fff}.Workflow.Radius .Add{display:flex}.Workflow.Radius.RightIcon .Add.right{display:flex}.Workflow.Radius .WorkItem.HasNext.HasPrev{border-radius:4px;width:auto;height:60px;background-color:#eee;color:#333;padding:0 20px;border-color:#ccc}.Workflow.Radius .WorkItem.HasPrev,.Workflow.Radius .WorkItem.HasNext{border-radius:50%;width:80px;height:80px;background-color:#2288fc;background-color:var(--mainColor);border-color:#2288fc;border-color:var(--mainColor);color:#fff;padding:0}
.Workflow.Radius .WorkItem.HasPrev{background-color:#e85854;border-color:#e85854}.Workflow.Radius .WorkLine .Text{border:1px solid #31b0d5;color:#31b0d5}.Workflow.disabled .WorkItem .ItemText,.Workflow.disabled .WorkLine .Text,.Workflow .WorkItem.disabled .ItemText,.Workflow .WorkLine.disabled .Text{color:#aaa !important}.Workflow.disabled .WorkItem,.Workflow .WorkItem.disabled{background-color:#f4f8fd !important;color:#999 !important;border-color:#eee}.Workflow.disabled .WorkItem:before,.Workflow .WorkItem.disabled:before{opacity:45%}.Workflow .WorkLine.textVert .Text{width:20px;word-break:break-all;line-height:20px}.WorkChart{user-select:inherit}.WorkChart .WorkChartItem{padding:0 10px;box-shadow:1px 1px 3px #eee;max-width:120px;cursor:pointer;position:absolute;border:1px solid #eee;background-color:#fafafa;min-width:80px;text-align:center;line-height:40px;border-radius:4px}.WorkChart .WorkChartItem:hover{background-color:#2288fc;border-color:#2288fc;background-color:var(--mainColor);border-color:var(--mainColor);color:#fff}
.WorkChart .WorkChartLine{position:absolute}.WorkChart.disabled .WorkChartItem,.WorkChart .WorkChartItem.disabled{cursor:default !important;border-color:#bbb;background-color:#ddd !important;color:#aaa !important}.AlertPopup.Popup{min-width:300px;padding:10px 20px;background:#fff}.AlertPopup .Label{display:block;line-height:46px;font-size:14px}.AlertPopup .Title{font-size:16px;font-weight:bold}.BorderBottom{border-bottom:1px solid #e1e1e1 !important;padding-bottom:5px}.BorderRight{border-right:1px solid #e1e1e1 !important}.BorderLeft{border-left:1px solid #e1e1e1 !important}.BorderTop{border-top:1px solid #e1e1e1 !important}.PageBlock.pd0,.TabPage.pd0{padding:0 !important}.PageBlock.ptb0,.TabPage.ptb0{padding-top:0 !important;padding-bottom:0 !important}.PageBlock.plr0,.TabPage.plr0{padding-left:0 !important;padding-right:0 !important}.prl10{padding-left:10px;padding-right:10px}.pb10{padding-bottom:10px}.pb20{padding-bottom:20px}.pb5{padding-bottom:5px}.BottomBlock,.FlowPanel.BottomBlock,.FlexBlock .BottomBlock{display:flex;border-top:1px solid #e1e1e1;padding:10px 20px 5px 20px;z-index:1}
.BottomBlock .Button{min-width:90px;line-height:38px;height:40px;font-size:14px;padding:0 16px}.FormBorder .BottomBlock{padding:15px 20px 10px 20px;margin-top:15px}.Popup .BottomBlock .Button,.FormBorder .BottomBlock .Button{min-width:50px;line-height:30px;height:30px;font-size:13px;padding:0 16px}.childmb0>*{margin-bottom:0}.TimePopup{min-width:120px}.TimePopup .TimeBody{display:flex;border-bottom:1px solid #e7e7e7;height:200px;overflow:hidden}.TimePopup .TimeBody .Block{border-right:1px solid #e7e7e7;overflow:hidden;flex-grow:1}.TimePopup .TimeBody .Block:last-child{border-right:0}.TimePopup .TimeBody .item{line-height:24px;height:24px;text-align:center;cursor:pointer}.TimePopup .TimeBody .item.selected,.TimePopup .TimeBody .item:hover{background-color:#eff6fe;background-color:var(--lightColor)}.TimePopup .TimeBody .item.selected{color:#2288fc}.TimePopup .Bottom{display:flex;justify-content:space-between;padding:10px}.TimeDroped .EditBlock{display:none}.TimeDroped .TimePopup{box-shadow:none;border:1px solid #e7e7e7}
@keyframes LoadingIconSnake{0%{transform:rotate(0)}100%{transform:rotate(360deg)}}@keyframes transitionDropIn{0%{opacity:0;transform:scaleY(0.8)}to{opacity:1;transform:scaleY(1)}}@keyframes transitionDropOut{0%{opacity:1;transform:scaleY(1)}to{opacity:0;transform:scaleY(0.8)}}@keyframes movieBg{1%{left:0}50%{left:50%}100%{left:200%}}@keyframes progress-bar-stripes{from{background-position:40px 0}to{background-position:0 0}}@keyframes colors{from{color:#ff7046}to{color:#fff}}@-moz-document url-prefix(){.MDIChild .FormBorder.MessageBox .MessageBoxText{word-wrap:normal;word-break:keep-all;white-space:nowrap}.BrowserFirefox .FlowPanel.vert{overflow:visible}}.iconm5::before{margin-right:5px}.font12,.icon12:before{font-size:12px !important}.font13,.icon13:before{font-size:13px !important}.font14,.icon14:before{font-size:14px !important}.font15,.icon15:before{font-size:15px !important}.font16,.icon16:before{font-size:16px !important}
.font17,.icon17:before{font-size:17px !important}.font18,.icon18:before{font-size:18px !important}.font19,.icon19:before{font-size:19px !important}.font20,.icon20:before{font-size:20px !important}.font21,.icon21:before{font-size:21px !important}.font22,.icon22:before{font-size:22px !important}.font23,.icon23:before{font-size:23px !important}.font24,.icon24:before{font-size:24px !important}.font25,.icon25:before{font-size:25px !important}.HomeSearchBar.SearchBlock{position:fixed;z-index:4;top:-1000px;left:0;width:300px;padding:2px 0 2px 5px;align-items:center}.HomeSearchBar.SearchBlock .SearchEdit{border:0;background-color:#fff;border-radius:14px;height:30px !important}.HomeSearchBar.SearchBlock .SearchEdit:focus{box-shadow:inset 0 0 2px #000}.HomeSearchBar.SearchBlock .SearchButton{border:0;padding:0;right:4px;top:1px;border-radius:50%;height:30px;color:#444;background-color:transparent}.HomeSearchPopup{padding:0;width:300px;z-index:2}.HomeSearchPopup .Grid{border:0}.HomeSearchPopup .GridBody{overflow-x:hidden}
.HomeSearchPopup .NoHeader .Grid .GridTable{border:0}.HomeSearchPopup .GridBodyRow{cursor:pointer}.HomeSearchPopup .GridBodyCellText{padding-left:12px}.HomeSearchPopup::before{position:absolute;z-index:10;left:50%;top:50%;transform:translate(-50%,-50%);font-size:44px !important;color:#ccc}.SearchBarOverlay{position:fixed;z-index:3;right:0;top:0;width:100%;height:60px;background-color:rgba(0,0,0,0.5)}.FormDargSize{position:absolute;bottom:5px;right:5px;display:none;font-weight:bold}.reverseIcon{flex-direction:row-reverse;display:inline-flex !important}.reverseIcon:before{margin-left:5px;margin-right:0}.NewMonthPopupMain ._td{padding:0 5px;width:auto;line-height:28px}.NewMonthPopupMain .YearArrow,.NewMonthPopupMain .YearArrow1{cursor:pointer}.NewMonthPopupMain .YearArrow{margin-right:12px}.NewMonthPopupMain .YearArrow1{margin-left:12px;transform:rotate(-180deg)}.NewMonthPopupMain .YearArrow:hover,.NewMonthPopupMain .YearArrow1:hover{color:#2486fc}.NewMonthPopupMain .MonthBlock .YearArrowRow{height:31px}
.NewMonthPopupMain .YearBlock .Edit{display:none}.NewMonthPopupMain .YearBlock,.NewMonthPopupMain .MonthBlock{margin:0;padding:0}.NewMonthPopupMain .MonthBlock ._td{margin:5px 0}.NewMonthPopupMain .MonthBlock{width:83px}.NewMonthPopupMain .YearBlock{width:230px;border-right:1px solid #e7e7e7}.NewMonthPopupMain .YearBlock ._td{margin:5px 18px}.NewMonthPopupMain .MonthBlock,.NewMonthPopupMain .YearBlock{border-bottom:1px solid #e7e7e7}.NewMonthPopupMain .YearArrowRow{border-bottom:1px solid #e7e7e7}.FooterBlock{display:flex;justify-content:flex-end}.FooterBlock .Button{margin:10px}.CrabaTextArea{position:relative;flex-grow:1;display:flex}.CrabaTextArea textarea{width:100%;word-break:break-all;padding-bottom:10px}.InputCount{color:#909399;position:absolute;font-size:12px;line-height:14px;bottom:10px;right:10px;background-color:rgba(246,246,246,0.3)}.ListLabel .Label{display:block;line-height:22px;margin:0;counter-increment:number;height:auto}.ListLabel .Label:before{content:counter(number) ". ";margin-right:3px}
.SkinColor,.SkinColorIcon:before{color:#2288fc !important;color:var(--mainColor) !important}.SkinColor:hover,.SkinColorHover:hover{color:#2288fc !important;color:var(--mainColor) !important}.SkinLightBg,.SkinLightBgHover:hover{background-color:#eaf2ff !important}.SkinBorder,.SkinBorderHover:hover{border-color:#2288fc !important;border-color:var(--mainColor) !important}.SkinBg,.SkinBgHover:hover{background-color:#2288fc !important;background-color:var(--mainColor) !important}.SkinButton.aicon-bg-colors{color:#666}.tipLabel{color:#999 !important;cursor:default;font-size:12px}.DropContainer .nulltext{height:100%;display:flex;justify-content:center;align-items:center;color:#8e939f}.ListViewGrid .ListViewBody.ListViewBlock,.NormalList.ListViewBlock{flex-grow:0;margin:0}.ListViewGrid .ListViewBody .UI_ListView,.NormalList .UI_ListView{display:flex;flex-direction:column;border:0;overflow-x:hidden}.ListViewGrid .ListViewBody .UI_ListView .UI_ListItem,.NormalList .UI_ListView .UI_ListItem{flex-shrink:0;flex-grow:0;display:flex;line-height:30px;height:30px;align-items:center;cursor:default;border:0;padding:0 10px;border-radius:4px;position:relative}
.ListViewGrid .ListViewBody .UI_ListView .UI_ListItem{border-bottom:1px solid #e6e8f0;border-radius:0;margin:0;line-height:50px;height:50px}.ListViewGrid .ListViewBody .UI_ListView .UI_ListItem:hover,.NormalList .UI_ListView .UI_ListItem:hover{background-color:#ecf2ff}.ListViewGrid .ListViewBody .UI_ListView .UI_ListItem.selected,.NormalList .UI_ListView .UI_ListItem.selected{background-color:transparent;color:inherit}.ListViewGrid .ListViewBody .UI_ListView .UI_ListItem:hover,.ListViewGrid .ListViewBody .UI_ListView .UI_ListItem.active,.NormalList .UI_ListView .UI_ListItem:hover,.NormalList .UI_ListView .UI_ListItem.active{background-color:#ecf2ff;color:#2288fc}.NormalList.Multi .UI_ListView .UI_ListItem.selected{background-color:#ecf2ff}.NormalList.Multi .UI_ListView .UI_ListItem.selected:after{content:"\e78a";font-family:"iconfont" !important;font-style:normal;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;color:#2288fc;position:absolute;right:10px;top:50%;font-size:16px;font-weight:bold;transform:translate(0,-50%)}
.ListViewGrid{border:1px solid #e6e8f0}.ListViewGrid .ListViewHeader{flex-grow:0;flex-shrink:0;background-color:#f5f6f8;display:flex;height:36px;margin:0;padding:0 10px;font-size:14px}.ListViewGrid .ListViewHeader .Label{margin:0;line-height:36px}.ListViewGrid .ListViewHeader .UI_ListView{border:0;overflow:hidden;background:transparent}.ListViewGrid .ListViewHeader .UI_ListItem{height:36px;line-height:36px;margin:0}.ListViewGrid .UI_ListView .HBlock{padding:0;display:flex}.ListViewGrid .UI_ListView .HBlock>*{margin-bottom:0}.ListViewGrid .ListPager{border:0;border-top:0}.ListViewGrid .UI_ListView .SpeedButton{padding:0}.ListViewGrid.NoBorder .ListViewBody .UI_ListItem{border:0;height:40px;line-height:40px}.ListViewBottom .Button{margin-left:10px;margin-bottom:0;font-size:13px;height:50px;line-height:50px}.ListButtons{flex-shrink:0}.ListButtons .UI_ListView{display:flex;flex-wrap:wrap;border:0;background-color:transparent}.ListButtons .UI_ListItem{cursor:pointer;height:30px;line-height:30px;padding:0 10px;display:flex;align-items:center}
.ListButtons .UI_ListItem:hover{background-color:#ecf2ff;color:#2288fc}.Grid td.GridBodyCell.colorBg{animation:colorBg 1s ease-in-out}.colorBg2{animation:colorBg 1s ease-in-out}.highlight{animation:borderColor 1s ease-in-out}.highlight.TabPage{animation:colorBg 1s ease-in-out}@keyframes colorBg{0%{background-color:#2288fc;background-color:var(--mainColor)}100%{background-color:#fff}}@keyframes borderColor{0%{border-color:#2288fc;border-color:var(--mainColor)}100%{border-color:#fff}}.shake{animation:shake 800ms ease-in-out}.shakeBg{animation:shakeBg 600ms ease-in-out}@keyframes shake{10%,90%{transform:translate3d(-2px,0,0)}20%,80%{transform:translate3d(+3px,0,0)}30%,70%{transform:translate3d(-6px,0,0)}40%,60%{transform:translate3d(+6px,0,0)}50%{transform:translate3d(-6px,0,0)}}@keyframes shakeBg{0{box-shadow:0 0 1px 1px #2288fc;box-shadow:0 0 1px 1px var(--mainColor)}50%{box-shadow:0 0 3px 3px #2288fc;box-shadow:0 0 3px 3px var(--mainColor)}100%{box-shadow:0 0 1px 1px #2288fc;box-shadow:0 0 1px 1px var(--mainColor)}
}.ConfigPopup .FlowPanel{padding:20px 20px 20px 50px}.ConfigPopup .FlowItem{margin-bottom:15px}.ConfigPopup .BottomBlock{margin-top:20px}.ConfigPopup .Label{margin:0 15px;color:#999}.PageControl.TabCenter>.TabHeader{line-height:34px}.PageControl.TabCenter>.TabHeader .TabCaptions{display:flex;align-items:center}.PageControl.TabCenter>.TabHeader .TabCaption{background-color:#f9f9f9;border-radius:4px 4px 0 0;font-size:14px;color:#000;margin:0 4px 0 0;padding:0 20px;border:1px solid #f0f0f0;border-bottom:0}.PageControl.TabCenter>.TabHeader .TabCaption.active{color:#fff;background-color:#2288fc}.PageControl.TabCenter>.TabHeader .TabCaptions{justify-content:center}.PageControl.Left>.TabHeader .TabCaptions{justify-content:flex-start}.PageControl.Right>.TabHeader .TabCaptions{justify-content:flex-end}.PageControl.TabNumber>.TabHeader .TabCaption:before{content:counter(number);background-color:#c8c8c8;display:flex;align-items:center;justify-content:center;align-self:center;color:#fff;height:14px;width:14px;font-size:12px;border-radius:2px;margin-right:6px;font-weight:700}
.PageControl.TabNumber>.TabHeader .TabCaption{counter-increment:number;transition:all .3s}.PageControl.TabCenter.TabNumber>.TabHeader .TabCaption.active:before{background-color:#fff;color:#2288fc}.Grid .GridBodyCell .GridBodyCellText.VertText{writing-mode:vertical-lr;height:auto !important;letter-spacing:2px;width:100%;justify-content:center}.Grid .OnlyIcon .GridBodyCellText{display:none}.Grid .GridBodyRow .GridBodyCell.dragSelect{background-color:#adcef4 !important}.HoverScroll,.HoverScroll .UI_ListView{overflow:hidden !important}.HoverScroll:hover,.HoverScroll .UI_ListView:hover{overflow:auto !important}.ChildMargin10>*{margin-bottom:10px !important}.ChildMargin10 :last-child{margin-bottom:0 !important}.TreeView.TileContainer .ChildrenBlock.Children0{flex:unset;flex-basis:unset}.TileContainer>.ChildrenBlock .selected{position:relative}.TileContainer>.ChildrenBlock .selected::before{content:'';position:absolute;top:0;left:0;height:100%;width:2px;background-color:#2188fc}
.TileContent .Children1>.TreeNode,.TileContent .Children1>.TreeNode:hover{background-color:#f5f5f5}.TileContent .Children1>.TreeNode{font-weight:600}.TileContent .Children2>.TreeNode{background-color:unset}.TileContent .Children2{display:flex;flex-wrap:wrap;padding:8px 0}.TileContent .TreeNode:hover{background-color:unset;color:unset}.TileContent .TreeNode.selected{color:unset}.AllowMoveElt .topBar,.AllowMoveElt .dragable{cursor:move}.AllowMoveElt.Notification.moveing,.AllowMoveElt.Popup.moveing{transition:none}.Notification{display:flex;flex-direction:column;width:330px;border-radius:8px;border:1px solid #ebeef5;position:fixed;background-color:#fff;box-shadow:0 0 12px rgba(0,0,0,.12);transition:all .3s;overflow:hidden;z-index:9999}.Notification .Notification_icon{margin-right:10px;height:40px;overflow:hidden}.Notification .Notification_icon::before{font-size:22px}.Notification .topBar{margin:0;display:flex;height:40px;overflow:hidden;align-items:center;line-height:40px;padding:0 26px 0 10px}
.Notification .topBar .caption{font-weight:bold;font-size:16px}.Notification_content{padding:5px 30px 15px 30px;color:#606266;font-size:14px;line-height:22px;margin:0;flex:auto}.Notification.Notification_left{left:16px}.Notification.Notification_right{right:16px}.Notification_close{position:absolute;top:18px;right:15px;cursor:pointer}.Notification_close:hover::after{color:#e15241}.Notification_close::after{content:"\e69a";font-family:"iconfont" !important;font-weight:normal;font-size:16px;color:#909399;font-style:normal}.Notification.transtion_right{right:0;transform:translate(100%)}.Notification.transtion_left{left:0;transform:translate(-100%)}.maxWidth500{max-width:500px}.maxWidthP50{max-width:50%}.HideEditorButton .EditBlock .SkinButton{display:none}.HideEditorButton .EditBlock .Edit{border-right-width:1px;border-top-right-radius:4px;border-bottom-right-radius:4px}.Grid .ImageListCell img{margin:0 3px}.ProgressCircle{position:relative}
.ProgressCircle .progress-circle-bg{stroke:#f3f3f3;stroke-width:6px;fill-opacity:0}.ProgressCircle .progress-circle-value{transition:all .6s ease;stroke:#2288fc;stroke-width:6px;stroke-linecap:round;fill-opacity:0}.ProgressCircle .ProgressText{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);display:block;font-size:18px;color:#666;font-family:tahoma}.ProgressCircle.ok .progress-circle-value{stroke:#4ebd7d}.ProgressCircle.ok .ProgressText{color:#4ebd7d}.ProgressCircle .ProgressText:before{font-size:30px}.GridBodyCell .CellFlowPanel.FlowPanel{padding:0;justify-content:center}.GridBodyCell .CellFlowPanel .FlowItem{margin:0;margin-right:5px}.GridBodyCell .CellFlowPanel .SkinButton{border-width:1px;border-left:0}.GridBodyCell.HideCheckBox .ChkBoxStyle{display:none}.rate-group,.rate-item,.rate-text{display:inline-block}.rate-group{list-style-type:none;margin:0;padding:0;display:flex;align-items:center}.rate-group .rate-item{margin-right:8px;cursor:pointer;color:#f0f0f0}.rate-div,.rate-div .rate-star::before{font-size:20px}
.rate-star{display:inline-block;transition:all .2s}.rate-item:hover .rate-star,.rate-readonly .rate-star-full.rate-item:hover .rate-star{transform:scale(1.2)}.rate-readonly .rate-item:hover .rate-star{transform:scale(1)}.rate-star-full,.rate-star-full .rate-star::before{color:#f5dc4d}.rate-group.rate-readonly .rate-item{cursor:auto}.PagerTotalCount{padding-right:6px}.Preview500400{width:450px !important;height:400px !important}.Preview400300{width:400px !important;height:300px !important}.Preview100100{width:100px !important;height:100px !important}.DropImages{display:flex;align-items:center;border-right:0}.DropImageEdit img,.CellDropImages img,.DropImages img{margin:0 5px;max-height:30px}.CellDropImages,.DropImageEdit{display:flex;flex-wrap:wrap;justify-content:center;align-items:center}.MiniBox{position:fixed;z-index:99999;right:10px;bottom:0;width:200px;background-color:#fff;border-radius:8px 8px 0 0;overflow:hidden;border:1px solid #eee;box-shadow:0 0 10px 3px #ddd}.MiniBox .item{border-bottom:1px solid #ddd;padding:0 10px}
.MiniBox .item:last-child{border-bottom:0}.MiniBox .item .Label,.MiniBox .item .Button{line-height:40px;height:40px}.FormBorder.mini{left:100%;top:100%;min-width:10px;width:10px;height:10px;z-index:999999}.MiniBox .topBar{line-height:40px;height:40px;background-color:#2288fc;border-radius:8px 8px 0 0;color:#fff;padding:0 10px;cursor:move}.MiniBox .ArrowCombo:before{color:#fff !important;cursor:pointer}.Pager.child{border:0}.MergeCell.tcenter .GridBodyCellText{text-align:center;justify-content:center}.MergeCell.tright .GridBodyCellText{text-align:right;justify-content:flex-end}.DragGroups{position:relative;display:flex;flex-direction:column}.DragGroups>*{margin-bottom:10px}.DragGroups *:last-child{margin-bottom:0}.DragGroups.allowDrag .DragItem,.DragGroups.allowDrag .DragItem .FlowLabel{cursor:move}.ViewMoveItem{pointer-events:none}.DragItem.ViewMoveItem{position:absolute !important;z-index:99999;margin:0;opacity:.8;background-color:#fff;box-shadow:0 0 5px #aaa;filter:alpha(opacity=80);cursor:grabbing;border-radius:4px;overflow:hidden}
.DragItem.ViewMoveItem.EveryItem{padding:10px}.DragGroups .DragItem.ViewMoveItem{min-width:90%}.DragGroups .DragItem.CurDrop{box-shadow:0 0 5px #2288fc}.GridBlock.HideBorder .Grid{border:0}.GridBlock.HideBorder .GridHeaderBar{background-color:#fff}.GridBlock.HideBorder .GridHeader{border-bottom:0}.GridBlock.HideBorder .GridHeader .GridHeaderRow .GridHeaderCaption{border-width:0}.GridBlock.HideBorder .GridHeader.MultiHeader .GridHeaderRow .GridHeaderCaption{border-width:1px}.GridBlock.HideBorder .RowNoConfigIcon{width:100%}.GridBlock.HideBorder .MultiHeader .RowNoConfigIcon,.GridBlock.HideBorder .GridHeader.MultiHeader .GridHeaderCaptionText{border-right:0}.GridBlock.HideBorder .GridFooter{border:0}.GridBlock.HideBorder .GridFooterCell{border-right:0}.GridBlock.HideBorder .GridFooterCellText{height:16px;line-height:16px;border-right:1px solid #e6e8f0}.GridBlock.HideBorder .GridFilterBar{border-bottom:0}.GridBlock.HideBorder .Pager{border:0}.GridBlock.HideBorder .GridHeaderCaptionText{text-align:left}
.GridBlock.HideBorder .GridBodyCell,.GridBlock.HideBorder .GridBodyRowNoCell{border:0}.GridBlock.TableGrid .GridHeaderBar{background-color:#fff}.GridBlock.NoBorder .Grid{border-width:0}.GridBlock.NoBorder.OuterBorder .Grid{border-width:1px}.GridBlock.NoBorder .GridHeaderBar{border-radius:4px}.GridBlock.NoBorder.OuterBorder .GridHeaderBar{border-radius:0}.GridBlock.NoBorder .GridHeader{border-bottom:0}.GridBlock.NoBorder .GridHeader .GridHeaderRow .GridHeaderCaption{border-width:0}.GridBlock.NoBorder .GridHeader.MultiHeader .GridHeaderRow .GridHeaderCaption{border-width:1px}.Grid .AlignLeft.GridHeaderCaption{text-align:left}.Grid .AlignRight.GridHeaderCaption{text-align:right}.GridBlock.NoBorder .RowNoConfigIcon{width:100%}.GridBlock.NoBorder .RowNoConfigIcon,.GridBlock.NoBorder .GridHeader .GridHeaderCaptionText{border-right:1px solid #c3c1d9}.GridBlock.NoBorder.OuterBorder .RowNoConfigIcon,.GridBlock.NoBorder.OuterBorder .GridHeader .GridHeaderCaptionText{border-right:0}.GridBlock.NoBorder .MultiHeader .RowNoConfigIcon,.GridBlock.NoBorder .GridHeader.MultiHeader .GridHeaderCaptionText{border-right:0}
.GridBlock.NoBorder .GridFooter{border:0}.GridBlock.NoBorder .GridFooterCell{border-right:0}.GridBlock.NoBorder .GridFooterCellText{height:16px;line-height:16px;border-right:1px solid #e6e8f0}.GridBlock.NoBorder .GridFilterBar{border-bottom:0}.GridBlock.NoBorder .Pager{border:0}.GridBlock.NoBorder .GridFilterBlock,.GridBlock.NoBorder .GridBodyCell,.GridBlock.NoBorder .GridBodyRowNoCell,.GridBlock.NoBorder .GridDetailTd{border-right:0;border-bottom-color:#e8eaf1}.GridBlock.NoBorder .GridBodyRow:hover>td{background-color:#ecf2ff}*{scrollbar-width:thin;scrollbar-color:#c1c1c1 #eee}.HideStepButton .SkinButton{display:none}.HideStepButton .EditBlock.stepBlock{border:0}.HideStepButton .EditBlock.stepBlock .CellEdit{border:0;background-color:transparent}.FlowPanel.HasColSpan{padding:0}.FlowPanel.HasColSpan .FlowItem,.FlowPanel.HasColSpan .FlexBlock.clear{margin:0 0 10px 0}.FlowPanel.HasColSpan .FlowItem.MoreItem{padding-left:10px}.FlowPanel.HasColSpan.ColSpan2>.FlowItem.ColSpan2,.FlowPanel.HasColSpan.ColSpan2>.FlowItem.ColSpan2,.FlowPanel.HasColSpan.ColSpan2>.FlowItem.ColSpan3,.FlowPanel.HasColSpan.ColSpan2>.FlowItem.ColSpan4,.FlowPanel.HasColSpan.ColSpan2>.FlowItem.ColSpan5,.FlowPanel.HasColSpan.ColSpan3>.FlowItem.ColSpan3,.FlowPanel.HasColSpan.ColSpan3>.FlowItem.ColSpan4,.FlowPanel.HasColSpan.ColSpan3>.FlowItem.ColSpan5,.FlowPanel.HasColSpan.ColSpan4>.FlowItem.ColSpan4,.FlowPanel.HasColSpan.ColSpan4>.FlowItem.ColSpan5,.FlowPanel.HasColSpan.ColSpan5>.FlowItem.ColSpan5,.FlowPanel.HasColSpan.ColSpan6>.FlowItem.ColSpan6,.FlowPanel.HasColSpan.ColSpan7>.FlowItem.ColSpan7,.FlowPanel.HasColSpan.ColSpan8>.FlowItem.ColSpan8,.FlowPanel.HasColSpan.ColSpan9>.FlowItem.ColSpan9,.FlowPanel.HasColSpan.ColSpan10>.FlowItem.ColSpan10{width:100%}
.FlowPanel.HasColSpan.ColSpan2>.FlowItem{width:50%}.FlowPanel.HasColSpan.ColSpan3>.FlowItem{width:33.33%}.FlowPanel.HasColSpan.ColSpan3>.FlowItem.ColSpan2{width:66.66%}.FlowPanel.HasColSpan.ColSpan4>.FlowItem{width:25%}.FlowPanel.HasColSpan.ColSpan4>.FlowItem.ColSpan2{width:50%}.FlowPanel.HasColSpan.ColSpan4>.FlowItem.ColSpan3{width:75%}.FlowPanel.HasColSpan.ColSpan5>.FlowItem{width:20%}.FlowPanel.HasColSpan.ColSpan5>.FlowItem.ColSpan2{width:40%}.FlowPanel.HasColSpan.ColSpan5>.FlowItem.ColSpan3{width:60%}.FlowPanel.HasColSpan.ColSpan5>.FlowItem.ColSpan4{width:80%}.FlowPanel.HasColSpan.ColSpan6>.FlowItem{width:16.66%}.FlowPanel.HasColSpan.ColSpan6>.FlowItem.ColSpan2{width:33.33%}.FlowPanel.HasColSpan.ColSpan6>.FlowItem.ColSpan3{width:50%}.FlowPanel.HasColSpan.ColSpan6>.FlowItem.ColSpan4{width:66.66%}.FlowPanel.HasColSpan.ColSpan6>.FlowItem.ColSpan5{width:83.34%}.FlowPanel.HasColSpan.ColSpan7>.FlowItem{width:14.28%}.FlowPanel.HasColSpan.ColSpan7>.FlowItem.ColSpan2{width:28.57%}.FlowPanel.HasColSpan.ColSpan7>.FlowItem.ColSpan3{width:42.85%}
.FlowPanel.HasColSpan.ColSpan7>.FlowItem.ColSpan4{width:57.14%}.FlowPanel.HasColSpan.ColSpan7>.FlowItem.ColSpan5{width:71.42%}.FlowPanel.HasColSpan.ColSpan7>.FlowItem.ColSpan6{width:85.71%}.FlowPanel.HasColSpan.ColSpan8>.FlowItem{width:12.5%}.FlowPanel.HasColSpan.ColSpan8>.FlowItem.ColSpan2{width:25%}.FlowPanel.HasColSpan.ColSpan8>.FlowItem.ColSpan3{width:37.5%}.FlowPanel.HasColSpan.ColSpan8>.FlowItem.ColSpan4{width:50%}.FlowPanel.HasColSpan.ColSpan8>.FlowItem.ColSpan5{width:62.5%}.FlowPanel.HasColSpan.ColSpan8>.FlowItem.ColSpan6{width:75%}.FlowPanel.HasColSpan.ColSpan8>.FlowItem.ColSpan7{width:87.5%}.FlowPanel.HasColSpan.ColSpan9>.FlowItem{width:11.11%}.FlowPanel.HasColSpan.ColSpan9>.FlowItem.ColSpan2{width:22.22%}.FlowPanel.HasColSpan.ColSpan9>.FlowItem.ColSpan3{width:33.33%}.FlowPanel.HasColSpan.ColSpan9>.FlowItem.ColSpan4{width:44.44%}.FlowPanel.HasColSpan.ColSpan9>.FlowItem.ColSpan5{width:55.55%}.FlowPanel.HasColSpan.ColSpan9>.FlowItem.ColSpan6{width:66.66%}.FlowPanel.HasColSpan.ColSpan9>.FlowItem.ColSpan7{width:77.77%}
.FlowPanel.HasColSpan.ColSpan9>.FlowItem.ColSpan8{width:88.88%}.FlowPanel.HasColSpan.ColSpan10>.FlowItem{width:10%}.FlowPanel.HasColSpan.ColSpan10>.FlowItem.ColSpan2{width:20%}.FlowPanel.HasColSpan.ColSpan10>.FlowItem.ColSpan3{width:30%}.FlowPanel.HasColSpan.ColSpan10>.FlowItem.ColSpan4{width:40%}.FlowPanel.HasColSpan.ColSpan10>.FlowItem.ColSpan5{width:50%}.FlowPanel.HasColSpan.ColSpan10>.FlowItem.ColSpan6{width:60%}.FlowPanel.HasColSpan.ColSpan10>.FlowItem.ColSpan7{width:70%}.FlowPanel.HasColSpan.ColSpan10>.FlowItem.ColSpan8{width:80%}.FlowPanel.HasColSpan.ColSpan10>.FlowItem.ColSpan9{width:90%}.Grid .GridBodyCell .HideBack.ProgressBar{background-color:transparent}.HtmlContent{display:flex;display:flex;min-height:60px;flex:1;min-width:1px}.HtmlContent .HtmlEditorBody{flex:1}.HtmlContent .HtmlPreview{display:none;flex:1;border-top:1px solid #d9d6d1;min-width:1px;margin-left:-1px;border-left:1px solid #d1d5da;box-sizing:border-box;background-color:#fafbfc;padding:10px;font-size:14px;overflow-y:scroll;scrollbar-width:none;-ms-overflow-style:none}
.HtmlContent .HtmlPreview::-webkit-scrollbar{display:none}.GridBlock.HoverGrid .GridBodyCell{position:relative}.Grid .RowButtons{position:absolute;right:0;top:50%;transform:translate(0,-50%);display:flex;z-index:9}.Grid .RowButtons .RowBtn{margin-right:5px;cursor:pointer;background-color:rgba(255,255,255,0.9);color:#2288fc;font-weight:bold;width:20px;height:20px;overflow:hidden;text-align:center;line-height:20px;border-radius:50%}.Grid .RowButtons .RowBtn:before{font-size:12px}.Grid .RowButtons .RowBtn:hover{background-color:#2288fc;color:#fff}.HideMoreButton .MoreItem{display:none}.spliterMask{z-index:9999999;position:absolute;left:0;top:0;background-color:rgba(255,255,255,0.01);user-select:none;width:100%;height:100%}.Grid .UserHeaderBlock{display:flex;justify-content:space-between;align-items:center}.Grid .UserHeaderBefore{display:flex;justify-content:center;align-items:center;flex-direction:row-reverse}.Grid .GridHeaderCaption .sortIcon:before{font-size:11px;margin-left:3px;color:#888;font-weight:normal}
.ButtonView{flex:none;overflow:visible;margin:0}.ButtonView .UI_ListView{overflow:visible;background-color:transparent;border:0;flex-wrap:wrap;display:flex;flex:none}.ButtonView .UI_ListItem{margin-right:5px;position:relative;cursor:pointer;border:1px solid #ddd;background-color:#fff;line-height:26px;margin-bottom:6px;padding:0 5px 0 10px;display:flex;border-radius:3px;justify-content:center;overflow:visible}.ButtonView .UI_ListItem:hover{border-color:#2288fc}.ButtonView .UI_ListItem .value{margin-left:2px;font-weight:600;word-break:keep-all}.ButtonView .UI_ListView .UI_ListItem.selected{border-color:#2288fc;color:#333;background-color:#fff}.RedView .UI_ListItem.selected:after,.ButtonView .UI_ListItem.selected:after{position:absolute;right:0;bottom:0;margin:0;z-index:2;content:"\e78a";font-weight:bold;color:#fff;font-size:11px;font-family:"iconfont" !important;width:0;height:0;border-color:transparent transparent #2288fc transparent;border-style:solid;border-width:0 0 16px 16px;display:flex;justify-content:right;padding-bottom:1px;line-height:25px;font-style:normal;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-decoration:inherit;speak:none}
.ButtonView.view3 .UI_ListItem{background-color:#eef6ff;border-color:#eef6ff}.ButtonView.view3 .UI_ListItem:hover,.ButtonView.view3 .UI_ListItem.selected{border-color:#2288fc !important}.hoverBtns .btnsBlock,.ButtonView .UI_ListItem .btns{display:none;position:absolute;right:-8px;top:-8px;z-index:4}.hoverBtns .btnsBlock{right:5px;top:5px}.hoverBtns .UI_ListItem:hover .btnsBlock,.ButtonView .UI_ListItem:hover .btns{display:flex}.btnsBlock .icon,.ButtonView .UI_ListItem .btns .DataText{display:block;width:12px;height:12px;line-height:12px;text-align:center;border-radius:50%;background-color:#aaaeb4;color:#fff}.btnsBlock .icon:before,.ButtonView .UI_ListItem .btns .DataText:before{font-size:12px;margin:0}.btnsBlock .icon:hover,.ButtonView .UI_ListItem .btns .DataText:hover{background-color:#898c91}.ButtonView.List>.UI_ListView{flex-direction:column}.ButtonView.List>.UI_ListView>.UI_ListItem{width:100%;align-items:center}.kuohao:before{content:'(';margin:0 0 0 3px}.kuohao:after{content:')';margin-right:5px}
.ButtonView .UI_ListItem a.DataText{line-height:24px}.BorderLine{border-bottom:1px solid #aaa}.Grid.ShowFooter .XBar{bottom:32px}.Button.NormalLine:hover{overflow:visible}.Button.NormalLine:hover .RightIcon{border-left-color:#2288fc}.Button.NormalLine .RightIcon{z-index:2;border:1px solid #cbcbcb;margin-top:-1px;margin-right:-1px}.Button.NormalLine .RightIcon:after{display:none}.Button.NormalLine.rightHover .RightIcon:hover,.Button.NormalLine.rightHover .RightIcon.enabled:hover{border-color:#2288fc;background-color:#fff}.Button.NormalLine.rightHover:hover{border-color:#cbcbcb;color:#333}.Button.NormalLine.disabled .RightIcon,.Button.NormalLine.disabled .RightIcon:hover{color:#aaa;border-color:#ddd;background-color:#fafafa}.NotBold{font-weight:normal !important}.TabCaptions .configBtn{height:18px;line-height:18px;vertical-align:middle;align-self:center}.PgConfigPopup.Popup{min-width:200px;padding:0}.PgConfigPopup .bottom{border-top:1px solid #dcdcdc;padding:10px 10px 5px 10px}.PgConfigPopup .title{font-weight:600;font-size:16px;padding:0 10px;line-height:40px;background-color:#fafafa;cursor:move}
.PgConfigPopup .vbtitle{margin:0 0 0 10px;line-height:24px;color:gray;display:block}.PgConfigPopup.FL .ChkBoxStyle::before{font-size:18px;width:auto;height:auto;box-shadow:none}.PgConfigPopup.FL .ChkBoxStyle.disabled:before{background-color:inherit !important;color:#aaa}.PgConfigPopup.FL .ChkBoxStyle:before{background-color:inherit;border:0;color:#2288fc;color:var(--mainColor);font-family:"iconfont" !important;content:"\e656"}.PopupShowAll.PgConfigPopup.FL .ChkBoxStyle:before{content:"\e659"}.PopupShowAll.PgConfigPopup.FL .ChkBoxStyle.checked:before{content:"\e656";background-color:inherit}.PgConfigPopup.FL{display:flex;flex-direction:column;width:350px;overflow:hidden;max-height:100%}.PgConfigPopup.FL .NormalList{flex:auto;overflow:auto}.PgConfigPopup.FL .UI_ListView{background-color:transparent}.PgConfigPopup.FL .NormalList .UI_ListItem.selected:after{display:none}.PgConfigPopup .UI_ListItem{margin-bottom:5px}
.PgConfigPopup .UI_ListItem:last-child{margin-bottom:0}.PgConfigPopup .FlowEditing .UI_ListItem .cbox{display:none}.PgConfigPopup .FlowEditing .UI_ListItem:hover .cbox{display:block}.PgConfigPopup.FL .SearchBlock{margin:10px 10px 0 10px}.ListBlockItem .UI_ListView{display:flex;border:0}.ListBlockItem.Wrap .UI_ListView{flex-wrap:wrap}.ListBlockItem .UI_ListItem{background:linear-gradient(180deg,#ecf2ff 0,#f7fbfe 100%);flex-direction:column;border:1px solid #e1e1e1;border-radius:6px;display:flex;flex:1 1 1px;margin:0 10px 0 0;padding:10px}.ListBlockItem.Wrap .UI_ListItem{margin-bottom:10px}.ListBlockItem .UI_ListItem:last-child{margin-right:0}.ListBlockItem.NoBg .UI_ListItem{background:0}.ListBlockItem.NoBorder .UI_ListItem{border:0}.ProgressBar.Horz.hasItems .Item{flex-direction:row;background-color:#fff;padding:0 12px}.ProgressBar.Horz.hasItems .Item:before{margin-top:0;margin-right:6px}.Buttons.lheight22 .Button{height:22px;line-height:20px;border-radius:2px;padding:0 5px}
.SkinButton.aicon-rili:before{color:#8c8c8c;font-size:12px}.FlowPanel .FlowItem.shakeBg{z-index:3}.Popup.PanelPopupMenu{min-width:150px}.Popup.PanelPopupMenu .FlowPanel{display:flex;flex-direction:column;align-content:normal}.Popup.PanelPopupMenu .FlowItem{flex:auto;margin:0 0 10px 0}.Popup.PanelPopupMenu .Button{flex:auto}.Popup.PanelPopupMenu .RightIcon{float:right}.height100p{height:100% !important}.iconm0:before{margin:0}.FlowItem>.Border .ChkBoxStyle{height:28px;line-height:28px;margin-bottom:0}.CheckBoxList.Border.Vert .ChkBoxStyle{margin-bottom:5px}.Button.MoreButton{position:relative;padding-right:30px !important;overflow:visible}.Button .RightIcon.moreRightBtn{border:1px solid #cbcbcb;margin:0;z-index:1;position:absolute;right:-1px;top:-1px}.Button.MoreButton:hover .moreRightBtn{border-left-color:#2288fc}.Button .RightIcon.moreRightBtn:hover{border-color:#2288fc}.HideMoreIcon .moreRightBtn{display:none}.HideMoreIcon .Button.MoreButton{padding-right:inherit !important}
.Button.MoreButton.HasLine .RightIcon:after{display:none}.Button.MoreButton.HasLine .RightIcon.ArrowCombo{border:0}.UI_ListView .UI_ListItem.NoSelect{cursor:default}.UI_ListView .UI_ListItem.NoSelect:hover{border-color:#ddd}.NormalList.lh40 .UI_ListView .UI_ListItem{height:40px;line-height:40px}.NormalList.lh50 .UI_ListView .UI_ListItem{height:50px;line-height:50px}.BottomBlock .CombButton .Button{min-width:auto;margin-right:0 !important;padding:0 10px}#fl_hidden_body{display:none}.FileEditor.Button.OnlyButton{padding:0;text-indent:0}.FileEditor.Button.OnlyButton .FileName{display:none}.Button .FileEdit{border:0;background-color:transparent;position:absolute;left:0;top:0;width:100%}.FileEditor.Button.OnlyButton .FileEdit{width:auto}.FileEditor.Button.SpecialButton .FileButton{color:#fff}.EditSearchPopup .Grid,.EditSearchPopup .Pager{border:0}.GridFilterBlock .ButtonEdit.Buttons{min-height:auto}.GridFilterBlock .ButtonEdit.Buttons .Button{height:18px;line-height:16px;margin-top:1px;padding-right:18px}
.GridFilterBlock .ButtonEdit.Buttons .Button span{top:0;right:3px;width:14px;height:18px;line-height:17px}.GridFilterBar .GridFilterBlock .ChkBoxStyle .Edit{height:16px;line-height:normal}.GridBlock.HideBorder .GridFilterBar .GridFilterBlock{border-right-width:0}.Button.WrapText{height:auto;line-height:22px;font-size:12px}.Button.WrapText.hasRight{display:flex}.HideButtonWrap .Button40.WrapText.hasRight .RightIcon,.Button.WrapText.hasRight .RightIcon{line-height:38px}.Button.WrapText p{line-height:9px;padding-bottom:7px;font-size:11px}.HideButtonWrap .Button.WrapText{height:28px;line-height:26px}.HideButtonWrap .Button40.WrapText{height:40px !important;line-height:38px !important}.HideButtonWrap .Button.WrapText .RightIcon{line-height:28px}.HideButtonWrap .Button.WrapText p{display:none}.LazyGrid .GridBody{position:relative}.LazyGrid .GridBody .GridTable{will-change:transform;backface-visibility:hidden}.LazyGrid .GridBody .GridScrollAreaBottom{position:absolute;top:0;left:0;width:100%;z-index:-1}
.ProgressBar.Modal .close{display:none;position:absolute;right:-90px;top:-9px}.ProgressBar.Modal.allowClose .close{display:block}.ProgressBar.Modal span{background-color:rgba(255,255,255,0.5);color:#333 !important;font-size:14px;top:-30px;word-break:keep-all}.ProgressBar.Modal span:after{display:none}.ProgressOverlay{background-color:#fff}.HtmlEditor .insertSwf:before{font-size:16px}.zindex10000{z-index:10000 !important}.GridBlock.Flex0{max-width:100%}.aicon-jiantou:before{font-size:9px}.bgwhite{background-color:#fff}.boxwhite{background:#fff;border-radius:6px;padding:10px}.child-margin-h15>*{margin-right:15px}.child-margin-h10>*{margin-right:10px}.child-margin-h5>*{margin-right:5px}.child-margin-h5>*:last-child,.child-margin-h10>*:last-child,.child-margin-h15>*:last-child{margin-right:0}.child-margin-v10>*{margin-bottom:10px !important}.child-margin-v10>*:last-child{margin-bottom:0 !important}.child-margin-v15>*{margin-bottom:15px}.child-margin-v15>*:last-child{margin-bottom:0}.FormCaption .FormCaptionButton.refresh_btn{right:125px}
.HBlock.MB0>*{margin-bottom:0}.lh40{line-height:40px !important}.FlowPanel.VertItem .FlowItem{flex-direction:column;align-items:inherit;margin-bottom:7px}.FlowPanel.VertItem .FlowLabel{line-height:24px;display:flex;justify-content:left}.FlowPanel.VertItem .FlowItem:last-child{margin-bottom:0}.FlowItem:empty{display:none}.PageBlock.FlexBlock{flex-direction:row}.FormBorder.FlexBlock{overflow:hidden;overflow-y:hidden}.RootMenu.hasTool .Tool{position:absolute;right:0;top:0;background-color:#fafafa;padding:10px;height:100%;border-radius:0 10px 10px 0}.RootMenu.hasTool .Tool .Button{width:36px;height:36px;line-height:36px;padding:0;border-radius:8px}.RootMenu.hasTool .Tool .BadgeButton .BadgeValue{font-size:12px}.RootMenu.Menu.hasTool.ShowMenuPopup{padding-right:60px}.RootMenu.Menu.hasTool.GroupHorz .LayoutButton:before{transform:rotate(90deg);display:block}.MenuBarLine.DragLine{background-color:#fff}.MenuBarLine.DragLine:before{border-left-color:#fff}.EveryItem{position:absolute;background-color:#ddd}
.MenuRoot.EveryItem{background-color:#22366c;color:#fff;display:flex;align-items:center;font-weight:bold;padding:10px 15px;border-radius:6px;font-size:14px;z-index:9999;overflow:hidden;width:auto !important}.MenuRoot.EveryItem .MenuRootCaption{word-break:keep-all;flex-shrink:0}.MenuRoot.EveryItem .MenuRootImage{margin-right:10px}.MenuBar.draging .MenuRootActive,.MenuBar.draging .MenuRoot.Show,.MenuBar.draging .MenuRoot:hover{background-color:inherit;color:inherit;cursor:grabbing}.MorePanel .ButtonEdit.Buttons.FlexAuto{min-width:120px}.AllowDragRowNo .GridBodyRowNoCell{cursor:grabbing}.PanelLabelEdit,.ColumnCaptionEdit{position:absolute;transform:translateY(-50%);transition:none}._GroupBlock{min-width:120px;margin-right:10px;flex:auto}._GroupBlock .Title{line-height:36px;font-weight:bold;text-indent:10px;border-bottom:1px solid #eee;margin-bottom:5px}._GroupBlock .NormalList .UI_ListView .UI_ListItem{line-height:28px;height:28px;cursor:pointer;margin-bottom:3px;padding-right:40px}
._GroupBlock .NormalList .UI_ListView .UI_ListItem.active{color:inherit}._GroupBlock .NormalList.ListViewBlock{flex-grow:1}._GroupBlock .NormalList .UI_ListView{padding:0 10px 10px 10px;max-height:310px}._VbConfigMenu ._TopSearchBlock{margin:15px 5px 10px 10px}._VbConfigMenu .SearchEdit{border-radius:10px;width:90px}.MenuItem.MoreButton{justify-content:center}.GridHeaderCaption.ViewMoveItem{line-height:30px;height:30px !important;min-width:100px;text-align:center;padding:0 10px;font-weight:bold;border-radius:6px;word-break:keep-all;z-index:999;width:auto !important;position:absolute !important;color:#2288fc;color:var(--mainColor)}.GridHeaderCaption.ViewMoveItem .sortIcon,.GridHeaderCaption.ViewMoveItem>*{display:none}.GridHeaderCaption.ViewMoveItem .GridHeaderCaptionText{display:block}.RootMenu .menuEditBlock{position:absolute;right:5px;top:4px}.RootMenu .menuEditBlock .Button{color:inherit}.RootMenu .MenuItem{position:relative;z-index:1}.RootMenu .MenuItem .Edit{align-self:center}.configMenu .MenuItem.MenuText .MenuNoMore{display:block}
.RootMenu.Menu.configMenu .MenuMore,.RootMenu.Menu.configMenu .MenuNoMore{width:60px}.RootMenu .MenuText .iconbkj,.RootMenu .MenuText .iconsc,.RootMenu.FavMenu .MenuItem .iconbkj{display:none}.Menu.FavMenu.RootMenu.hasTool{padding-right:10px}.RootMenu.FavMenu .Tool{display:none !important}@media(max-height:800px){.RootMenu.MenuBar.vert .MenuRoot{height:32px;line-height:32px;font-size:13px}.RootMenu.Menu .MenuItem{height:30px;line-height:30px;font-size:12px}.RootMenu .menuEditBlock{top:0}}.FlowPanel .FlexAuto.FlowItem{overflow:hidden;flex-shrink:1}.Grid.StickyGrid .sticky{background-color:#fff;z-index:1}.Grid.StickyGrid .RowNoCell,.Grid.StickyGrid .GridBodyRowNoCell{position:sticky;z-index:1;left:0}.Grid.StickyGrid .sticky.last{box-shadow:4px 0 5px -1px #ddd}.Grid.StickyGrid .sticky.first{box-shadow:-4px 0 5px -1px #ddd}.Grid.StickyGrid .GridHeaderBar .sticky,.Grid.StickyGrid .GridHeaderBar .RowNoCell{background-color:#f2f2f2}.Grid.StickyGrid .GridFilterBar .sticky,.Grid.StickyGrid .GridFilterBar .RowNoCell{background-color:#f9f9f9}
.Grid.StickyGrid .GridFooterBar .sticky,.Grid.StickyGrid .GridFooterBar .RowNoCell{background-color:#fefbe5}.Workflow .WorkItem.colorYellow{background:#ff943e;color:#fff}.Workflow .WorkItem.colorBlue{background:#3189ff;color:#fff}.Workflow .WorkItem.colorGreen{background:#00c9ac;color:#fff}.Workflow .WorkItem.colorDark{background:#5f71ef;color:#fff}.Workflow .WorkItem.colorOrg{background:#fc6010;color:#fff}.Workflow .WorkItem.itemBlue:before{background-color:#45a1ff}.Workflow .WorkItem.itemDark:before{background-color:#5f71ef}.Workflow .WorkItem.itemGreen:before{background-color:#37bd4f}.Workflow .WorkItem.itemOrg:before{background-color:#ff8945}.Workflow .WorkItem.itemSky:before{background-color:#35c9c9}.Workflow .WorkItem.iconColorYellow:before{background:#ff943e}.Workflow .WorkItem.iconColorBlue:before{background:#3189ff}.Workflow .WorkItem.iconColorGreen:before{background:#00c9ac}.Workflow .WorkItem.iconColorDark:before{background:#5f71ef}.Workflow .WorkItem.iconColorOrg:before{background:#fc6010}
.SvgBox{pointer-events:none;position:absolute;left:0;top:0;width:100%;height:100%}.Workflow.draging{cursor:move}.Workflow.draging .WorkLine{pointer-events:none}.FlowPanel.ShowMoreRight .rightIconBtn{position:absolute;right:0}.dashed{border-style:dashed}.bgfa{background-color:#fafafa}.bgfff{background-color:#fff}.MorePanel .clear{clear:unset}.FlowEditing{position:relative}.FlowEditing:before{content:' ';z-index:10;pointer-events:none;position:absolute;width:calc(100% - 2px);height:calc(100% - 2px);left:0;top:0;border:1px dashed var(--mainColor);box-shadow:inset 0 0 4px var(--mainColor)}.FlowEditing.FlowPanel{overflow:visible !important}.DragMode{position:relative;overflow:visible}.FlowEditing .FlowItem{position:relative;overflow:visible !important}.DragMode .ItemToolBar{position:absolute;top:0;left:0;width:100%;height:100%;z-index:2;cursor:grabbing;display:none}.FlowEditing .FlowItem:hover .ItemToolBar{display:block;border:1px solid var(--mainColor);box-shadow:0 0 3px var(--mainColor)}
.draging.DragMode .FlowItem:hover .ItemToolBar{display:none}.FlowEditing.MorePanel .ItemToolBar .aicon-bukejian:before{content:"\e656"}.DragMode .ItemToolBar .btns{position:absolute;right:0;top:-5px}.DragMode .ItemToolBar .btns .Button{width:20px;height:20px;line-height:20px;text-align:center;border-radius:50%;border:0;box-shadow:1px 3px 6px 1px rgba(0,0,0,0.15);padding:0;min-width:auto;overflow:hidden}.DragMode .ItemToolBar .btns .Button:hover{background-color:var(--mainColor);color:#fff}.DragMode .ItemToolBar .btns .Button::before{font-size:14px}.HideModifyButton .editIcon{display:none}.FlowPanel.vert .FlowItem.MoreItem{justify-content:flex-end}.FlowItem.ViewMoveItem{z-index:9999;border-radius:4px;background-color:rgba(100,100,100,0.2);box-shadow:0 0 3px 1px #ddd;padding:5px}.FlowItem.ViewMoveItem .ItemToolBar{display:none}.DragSpacer{border-left-color:#2288fc;border-left-color:var(--mainColor)}.RootMenu .MenuText .moreIcon{align-self:center;font-size:12px}.sticky{position:sticky !important;z-index:1}
.bottom{bottom:0}.top{top:0}.EveryItem.fpDragItem{border-radius:4px;padding:10px;z-index:999}.EveryItem.fpDragItem .cbox{display:none}.HelpContent{font-size:14px;font-weight:bold;color:var(--mainColor);line-height:34px}.HelpContent img{display:block;max-width:1000px;max-height:600px}.GridHelp img{width:600px;height:230px}.FlowHelp img{width:700px;height:280px}.PgConfigPopup.CurDrop,.FlowEditing.CurDrop{box-shadow:0 0 5px var(--mainColor)}
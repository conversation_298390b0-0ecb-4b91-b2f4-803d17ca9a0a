<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.AccGoodsStockBatchlifeMapper">
    <resultMap id="BaseResultMap" type="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.AccGoodsStockBatchlife">
        <constructor>
            <idArg column="id" javaType="java.math.BigInteger" jdbcType="BIGINT"/>
            <arg column="profile_id" javaType="java.math.BigInteger" jdbcType="BIGINT"/>
            <arg column="ktype_id" javaType="java.math.BigInteger" jdbcType="BIGINT"/>
            <arg column="ptype_id" javaType="java.math.BigInteger" jdbcType="BIGINT"/>
            <arg column="batchno" javaType="java.lang.String" jdbcType="VARCHAR"/>
            <arg column="produce_date" javaType="java.util.Date" jdbcType="DATE"/>
            <arg column="expire_date" javaType="java.util.Date" jdbcType="DATE"/>
            <arg column="qty" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>
            <arg column="sub_qty" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>
            <arg column="create_time" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
            <arg column="update_time" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        </constructor>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
    id, profile_id, ktype_id, ptype_id, batchno, produce_date, expire_date, qty, sub_qty, 
    create_time, update_time
  </sql>
    <select id="selectByExample" parameterType="com.wsgjp.ct.sale.biz.eshoporder.example.AccGoodsStockBatchlifeExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        'true' as QUERYID,
        <include refid="Base_Column_List"/>
        from acc_goodsstock_batchlife
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by #{orderByClause}
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.math.BigInteger" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from acc_goodsstock_batchlife
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.math.BigInteger">
    delete from acc_goodsstock_batchlife
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <delete id="deleteByExample" parameterType="com.wsgjp.ct.sale.biz.eshoporder.example.AccGoodsStockBatchlifeExample">
        delete from acc_goodsstock_batchlife
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.AccGoodsStockBatchlife">
    insert into acc_goodsstock_batchlife (id, profile_id, ktype_id, 
      ptype_id, batchno, produce_date, 
      expire_date, qty, sub_qty, 
      create_time, update_time)
    values (#{id,jdbcType=BIGINT}, #{profileId,jdbcType=BIGINT}, #{ktypeId,jdbcType=BIGINT}, 
      #{ptypeId,jdbcType=BIGINT}, #{batchno,jdbcType=VARCHAR}, #{produceDate,jdbcType=DATE}, 
      #{expireDate,jdbcType=DATE}, #{qty,jdbcType=DECIMAL}, #{subQty,jdbcType=DECIMAL}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
    <insert id="insertSelective" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.AccGoodsStockBatchlife">
        insert into acc_goodsstock_batchlife
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="profileId != null">
                profile_id,
            </if>
            <if test="ktypeId != null">
                ktype_id,
            </if>
            <if test="ptypeId != null">
                ptype_id,
            </if>
            <if test="batchno != null">
                batchno,
            </if>
            <if test="produceDate != null">
                produce_date,
            </if>
            <if test="expireDate != null">
                expire_date,
            </if>
            <if test="qty != null">
                qty,
            </if>
            <if test="subQty != null">
                sub_qty,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="profileId != null">
                #{profileId,jdbcType=BIGINT},
            </if>
            <if test="ktypeId != null">
                #{ktypeId,jdbcType=BIGINT},
            </if>
            <if test="ptypeId != null">
                #{ptypeId,jdbcType=BIGINT},
            </if>
            <if test="batchno != null">
                #{batchno,jdbcType=VARCHAR},
            </if>
            <if test="produceDate != null">
                #{produceDate,jdbcType=DATE},
            </if>
            <if test="expireDate != null">
                #{expireDate,jdbcType=DATE},
            </if>
            <if test="qty != null">
                #{qty,jdbcType=DECIMAL},
            </if>
            <if test="subQty != null">
                #{subQty,jdbcType=DECIMAL},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.wsgjp.ct.sale.biz.eshoporder.example.AccGoodsStockBatchlifeExample"
            resultType="java.lang.Long">
        select count(*) from acc_goodsstock_batchlife
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        update acc_goodsstock_batchlife
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=BIGINT},
            </if>
            <if test="record.profileId != null">
                profile_id = #{record.profileId,jdbcType=BIGINT},
            </if>
            <if test="record.ktypeId != null">
                ktype_id = #{record.ktypeId,jdbcType=BIGINT},
            </if>
            <if test="record.ptypeId != null">
                ptype_id = #{record.ptypeId,jdbcType=BIGINT},
            </if>
            <if test="record.batchno != null">
                batchno = #{record.batchno,jdbcType=VARCHAR},
            </if>
            <if test="record.produceDate != null">
                produce_date = #{record.produceDate,jdbcType=DATE},
            </if>
            <if test="record.expireDate != null">
                expire_date = #{record.expireDate,jdbcType=DATE},
            </if>
            <if test="record.qty != null">
                qty = #{record.qty,jdbcType=DECIMAL},
            </if>
            <if test="record.subQty != null">
                sub_qty = #{record.subQty,jdbcType=DECIMAL},
            </if>
            <if test="record.createTime != null">
                create_time = #{record.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.updateTime != null">
                update_time = #{record.updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update acc_goodsstock_batchlife
        set id = #{record.id,jdbcType=BIGINT},
        profile_id = #{record.profileId,jdbcType=BIGINT},
        ktype_id = #{record.ktypeId,jdbcType=BIGINT},
        ptype_id = #{record.ptypeId,jdbcType=BIGINT},
        batchno = #{record.batchno,jdbcType=VARCHAR},
        produce_date = #{record.produceDate,jdbcType=DATE},
        expire_date = #{record.expireDate,jdbcType=DATE},
        qty = #{record.qty,jdbcType=DECIMAL},
        sub_qty = #{record.subQty,jdbcType=DECIMAL},
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
        update_time = #{record.updateTime,jdbcType=TIMESTAMP}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.AccGoodsStockBatchlife">
        update acc_goodsstock_batchlife
        <set>
            <if test="profileId != null">
                profile_id = #{profileId,jdbcType=BIGINT},
            </if>
            <if test="ktypeId != null">
                ktype_id = #{ktypeId,jdbcType=BIGINT},
            </if>
            <if test="ptypeId != null">
                ptype_id = #{ptypeId,jdbcType=BIGINT},
            </if>
            <if test="batchno != null">
                batchno = #{batchno,jdbcType=VARCHAR},
            </if>
            <if test="produceDate != null">
                produce_date = #{produceDate,jdbcType=DATE},
            </if>
            <if test="expireDate != null">
                expire_date = #{expireDate,jdbcType=DATE},
            </if>
            <if test="qty != null">
                qty = #{qty,jdbcType=DECIMAL},
            </if>
            <if test="subQty != null">
                sub_qty = #{subQty,jdbcType=DECIMAL},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.AccGoodsStockBatchlife">
    update acc_goodsstock_batchlife
    set profile_id = #{profileId,jdbcType=BIGINT},
      ktype_id = #{ktypeId,jdbcType=BIGINT},
      ptype_id = #{ptypeId,jdbcType=BIGINT},
      batchno = #{batchno,jdbcType=VARCHAR},
      produce_date = #{produceDate,jdbcType=DATE},
      expire_date = #{expireDate,jdbcType=DATE},
      qty = #{qty,jdbcType=DECIMAL},
      sub_qty = #{subQty,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
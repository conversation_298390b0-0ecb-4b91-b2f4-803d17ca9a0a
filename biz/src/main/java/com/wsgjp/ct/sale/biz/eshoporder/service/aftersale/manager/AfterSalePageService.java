package com.wsgjp.ct.sale.biz.eshoporder.service.aftersale.manager;

import bf.datasource.page.PageDevice;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import bf.datasource.page.Sort;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.shade.com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.wsgjp.ct.bill.core.handle.entity.*;
import com.wsgjp.ct.bill.core.handle.entity.BillEntity;
import com.wsgjp.ct.bill.core.handle.entity.dao.BillRelation;
import com.wsgjp.ct.bill.core.handle.entity.enums.*;
import com.wsgjp.ct.bill.core.handle.entity.enums.BillCreateType;
import com.wsgjp.ct.bill.core.handle.entity.enums.BillPostState;
import com.wsgjp.ct.bill.core.handle.service.BillCoreService;
import com.wsgjp.ct.common.enums.core.entity.BaseMarkBigDataEntity;
import com.wsgjp.ct.common.enums.core.entity.MarkData;
import com.wsgjp.ct.common.enums.core.enums.BaseOrderMarkEnum;
import com.wsgjp.ct.common.enums.core.enums.FullLinkStatusEnum;
import com.wsgjp.ct.common.enums.core.enums.QtyChangeSourceType;
import com.wsgjp.ct.pm.service.PermissionValiateService;
import com.wsgjp.ct.record.sheet.core.entity.BillNumberEntity;
import com.wsgjp.ct.record.sheet.core.entity.QueryBillNumberEntity;
import com.wsgjp.ct.record.sheet.core.entity.request.BillDetailUpdateRequest;
import com.wsgjp.ct.record.sheet.core.entity.request.OrderBillUpdateRequest;
import com.wsgjp.ct.record.sheet.core.service.BillDetailUpdateService;
import com.wsgjp.ct.record.sheet.core.service.BillNumberCoreService;
import com.wsgjp.ct.sale.biz.api.baseinfo.BaseInfoApi;
import com.wsgjp.ct.sale.biz.api.baseinfo.pojo.BtypeDeliveryinfoDto;
import com.wsgjp.ct.sale.biz.api.request.baseinfo.BtypeDeliverInfoParamsDto;
import com.wsgjp.ct.sale.biz.api.response.GeneralResponse;
import com.wsgjp.ct.sale.biz.api.ydh.YdhApi;
import com.wsgjp.ct.sale.biz.bifrost.service.impl.BifrostEshopOrderServiceImpl;
import com.wsgjp.ct.sale.biz.eshoporder.api.response.GetRefundDeliverBillResponse;
import com.wsgjp.ct.sale.biz.eshoporder.api.response.deliver.GetRefundDeliverBillRequest;
import com.wsgjp.ct.sale.biz.eshoporder.constant.NameConstantEnum;
import com.wsgjp.ct.sale.biz.eshoporder.constant.RefundConstantEnum;
import com.wsgjp.ct.sale.biz.eshoporder.constant.StringConstantEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.order.OrderCreateType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.refund.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.GoodsBatchEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.GoodsSerialEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.check.BilCheckResultResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.check.BillCheckResult;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.base.QueryBaseTypeParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.order.EshopOrderMarkParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.BaseDateResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.BaseResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.ManualOrderResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.UnConfirmRefundResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.stock.EshopOrderSaleQtyChange;
import com.wsgjp.ct.sale.biz.eshoporder.impl.ProcessLoggerImpl;
import com.wsgjp.ct.sale.biz.eshoporder.log.EshopRefundOrderSysLog;
import com.wsgjp.ct.sale.biz.eshoporder.manage.refund.RefundCheckInMergeManage;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.*;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.aftersale.AfterSaleMapper;
import com.wsgjp.ct.sale.biz.eshoporder.service.aftersale.manager.request.AfterSaleListQueryRequest;
import com.wsgjp.ct.sale.biz.eshoporder.service.baseinfo.EshopOrderBaseInfoService;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopService;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopSaleOrderBuilder;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopSaleOrderManualService;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopSaleOrderNotifyService;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopSaleOrderService;
import com.wsgjp.ct.sale.biz.eshoporder.service.receiver.EshopBuyerService;
import com.wsgjp.ct.sale.biz.eshoporder.service.refund.EshopOrderEshopRefundService;
import com.wsgjp.ct.sale.biz.eshoporder.service.refund.EshopRefundReceiveCheckInService;
import com.wsgjp.ct.sale.biz.eshoporder.service.refund.EshopRefundSaver;
import com.wsgjp.ct.sale.biz.eshoporder.service.refund.genbill.helper.NotifyOrderHelper;
import com.wsgjp.ct.sale.biz.eshoporder.service.refund.genbill.helper.QtyChangeHelper;
import com.wsgjp.ct.sale.biz.eshoporder.service.refund.impl.EshopRefundReceiveCheckInServiceImpl;
import com.wsgjp.ct.sale.biz.eshoporder.service.refund.notify.NotifyOrderCatch;
import com.wsgjp.ct.sale.biz.eshoporder.service.refund.util.RefundDetailCalculateUtil;
import com.wsgjp.ct.sale.biz.eshoporder.service.refund.util.RefundMarkUtil;
import com.wsgjp.ct.sale.biz.eshoporder.support.DeliverApi;
import com.wsgjp.ct.sale.biz.eshoporder.util.*;
import com.wsgjp.ct.sale.biz.jarvis.common.enumMapping.EnumMappingUtil;
import com.wsgjp.ct.sale.biz.jarvis.dto.BillDeliverDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.deliverbillupdate.RefundDeliverBill;
import com.wsgjp.ct.sale.biz.jarvis.dto.deliverbillupdate.RefundDeliverBillRequest;
import com.wsgjp.ct.sale.biz.jarvis.dto.upload.UploadBillEnclosureDTO;
import com.wsgjp.ct.sale.biz.jarvis.entity.BigData;
import com.wsgjp.ct.sale.biz.jarvis.entity.BillEnclosure;
import com.wsgjp.ct.sale.biz.jarvis.entity.query.BillDeliverQueryParams;
import com.wsgjp.ct.sale.biz.jarvis.entity.refund.RefundAndDeliverMappingEntity;
import com.wsgjp.ct.sale.biz.jarvis.mapper.BigDataMapper;
import com.wsgjp.ct.sale.biz.jarvis.mapper.BillEnclosureMapper;
import com.wsgjp.ct.sale.biz.jarvis.mapper.JarvisEshopRefundMapper;
import com.wsgjp.ct.sale.biz.jarvis.open.JarvisOpenApi;
import com.wsgjp.ct.sale.biz.jarvis.open.dto.*;
import com.wsgjp.ct.sale.biz.jarvis.service.refund.JarvisEshopRefundService;
import com.wsgjp.ct.sale.biz.jarvis.service.upload.UploadBillEnclosureService;
import com.wsgjp.ct.sale.biz.refund.refund.entity.RefundDuty;
import com.wsgjp.ct.sale.biz.refund.refund.entity.request.GetHistoryRefundNumbersRequest;
import com.wsgjp.ct.sale.biz.refund.refund.entity.request.GetReportDataRequest;
import com.wsgjp.ct.sale.biz.refund.refund.entity.request.NotifyRefundSendStateRequest;
import com.wsgjp.ct.sale.biz.refund.refund.entity.request.UpdateRefundDutyRequest;
import com.wsgjp.ct.sale.biz.refund.refund.entity.response.CheckCanReProcessResponse;
import com.wsgjp.ct.sale.biz.shopsale.common.FeignResult;
import com.wsgjp.ct.sale.common.config.EshopOrderCommonConfig;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.enums.RefundPlatformOperation;
import com.wsgjp.ct.sale.common.enums.eshoporder.OrderOpreateType;
import com.wsgjp.ct.sale.common.enums.eshoporder.ReSendStateEnum;
import com.wsgjp.ct.sale.common.enums.eshoporder.ReturnState;
import com.wsgjp.ct.sale.common.enums.publish.CheckinState;
import com.wsgjp.ct.sale.platform.dto.order.EshopOrderEntity;
import com.wsgjp.ct.sale.platform.dto.refund.EshopRefundOrderEntity;
import com.wsgjp.ct.sale.platform.entity.request.order.CloseOrderRequest;
import com.wsgjp.ct.sale.platform.entity.request.order.DecryptionOpenUidRequest;
import com.wsgjp.ct.sale.platform.entity.response.order.CloseOrderResponse;
import com.wsgjp.ct.sale.platform.entity.response.order.DecryptionOpenUidResponse;
import com.wsgjp.ct.sale.platform.enums.*;
import com.wsgjp.ct.sale.platform.factory.taobao.TaobaoConfig;
import com.wsgjp.ct.sale.platform.utils.BeanUtils;
import com.wsgjp.ct.sale.sdk.financecheck.entity.FinanceCheckChangeEntity;
import com.wsgjp.ct.sale.sdk.financecheck.enums.CheckSubjectEnum;
import com.wsgjp.ct.sale.sdk.financecheck.enums.FinanceCheckChangeSourceEnum;
import com.wsgjp.ct.sale.sdk.mapper.FinanceCheckMapper;
import com.wsgjp.ct.sale.sdk.stock.biz.StockChangeService;
import com.wsgjp.ct.sale.sdk.stock.enums.StockChangeTypeEnum;
import com.wsgjp.ct.sale.sdk.stock.parameter.StockChangeQueueDto;
import com.wsgjp.ct.sis.client.SisClient;
import com.wsgjp.ct.sis.client.common.EncrypAndDecrypSenceEnum;
import com.wsgjp.ct.sis.client.common.SensitiveFieldEnum;
import com.wsgjp.ct.sis.client.entity.DicInfo;
import com.wsgjp.ct.sis.client.entity.DicRequest;
import com.wsgjp.ct.sis.client.entity.EncryptFullAdapter;
import com.wsgjp.ct.sis.client.entity.EncryptInfo;
import com.wsgjp.ct.support.business.Money;
import com.wsgjp.ct.support.business.MoneyUtils;
import com.wsgjp.ct.support.business.pubsystemlog.PubSystemLogService;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.global.GlobalConfig;
import com.wsgjp.ct.support.global.entity.SysGlobalConfig;
import com.wsgjp.ct.support.log.service.LogService;
import ngp.idgenerator.UId;
import ngp.starter.web.base.GeneralResult;
import ngp.utils.*;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
public class AfterSalePageService {
    private final EshopOrderEshopRefundMapper refundMapper;

    private final AfterSaleMapper afterSaleMapper;
    private final BillEnclosureMapper billEnclosureMapper;
    private final BillNumberCoreService billSvc;
    private final EshopOrderBaseInfoService baseSvc;
    private final EshopOrderBuyerMapper eshopOrderBuyerMapper;
    private final EshopBuyerService buyerService;
    private final UploadBillEnclosureService uploadBillEnclosureService;
    private final EshopRefundReceiveCheckInMapper eshopRefundReceiveCheckInMapper;
    private final EshopRefundBillReleationMapper eshopRefundBillReleationMapper;
    private final RefundCheckInMergeManage refundCheckInMergeManage;
    private final NotifyOrderHelper notifyOrderHelper;
    private final DeliverApi deliverApi;
    private final BaseKtypeMapper baseKtypeMapper;
    private final StockChangeService stockChangeService;
    private final EshopSaleOrderMapper orderMapper;
    private final JarvisOpenApi jarvisOpenApi;
    private static final Logger logger = LoggerFactory.getLogger(EshopOrderEshopRefundService.class);
    private final EshopBizMapper eshopBizMapper;
    private final FinanceCheckMapper financeCheckMapper;
    private final EshopSaleOrderManualService eshopSaleOrderManualService;
    private final EshopRefundReceiveCheckInMapper checkInMapper;
    private final BifrostEshopOrderServiceImpl bifrostEshopOrderService;
    private final BigDataMapper bigDataMapper;
    private final YdhApi ydhApi;
    private final BillCoreService billCoreService;
    Pattern p = Pattern.compile("\\s*");
    private static final String SYSTEM = "系统";
    // 定义空字符串常量
    private static final String EMPTY_STRING = "";

    @Autowired
    @Lazy
    private JarvisEshopRefundMapper jarvisEshopRefundMapper;

    @Autowired
    private EshopRefundReceiveCheckInService receiveCheckInService;

    @Autowired
    @Lazy
    private QtyChangeHelper qtyChangeHelper;

    @Autowired
    @Lazy
    private JarvisEshopRefundService jarvisEshopRefundService;

    private Map<BillPostState, String> inoutMapping;

    private final BillDetailUpdateService billDetailUpdateService;


    public AfterSalePageService(EshopOrderEshopRefundMapper refundMapper, AfterSaleMapper afterSaleMapper, BillEnclosureMapper billEnclosureMapper, BillNumberCoreService billSvc, EshopOrderBaseInfoService baseSvc, EshopOrderBuyerMapper eshopOrderBuyerMapper, EshopBuyerService buyerService, UploadBillEnclosureService uploadBillEnclosureService, EshopRefundReceiveCheckInMapper eshopRefundReceiveCheckInMapper, EshopRefundBillReleationMapper eshopRefundBillReleationMapper, RefundCheckInMergeManage refundCheckInMergeManage, NotifyOrderHelper notifyOrderHelper, DeliverApi deliverApi, BaseKtypeMapper baseKtypeMapper, StockChangeService stockChangeService, EshopSaleOrderMapper orderMapper, JarvisOpenApi jarvisOpenApi, EshopBizMapper eshopBizMapper, FinanceCheckMapper financeCheckMapper, EshopSaleOrderManualService eshopSaleOrderManualService, EshopRefundReceiveCheckInMapper checkInMapper, BifrostEshopOrderServiceImpl bifrostEshopOrderService, BigDataMapper bigDataMapper, YdhApi ydhApi, BillCoreService billCoreService, BillDetailUpdateService billDetailUpdateService) {
        this.refundMapper = refundMapper;
        this.afterSaleMapper = afterSaleMapper;
        this.billEnclosureMapper = billEnclosureMapper;
        this.billSvc = billSvc;
        this.baseSvc = baseSvc;
        this.eshopOrderBuyerMapper = eshopOrderBuyerMapper;
        this.buyerService = buyerService;
        this.uploadBillEnclosureService = uploadBillEnclosureService;
        this.eshopRefundReceiveCheckInMapper = eshopRefundReceiveCheckInMapper;
        this.eshopRefundBillReleationMapper = eshopRefundBillReleationMapper;
        this.refundCheckInMergeManage = refundCheckInMergeManage;
        this.notifyOrderHelper = notifyOrderHelper;
        this.deliverApi = deliverApi;
        this.baseKtypeMapper = baseKtypeMapper;
        this.stockChangeService = stockChangeService;
        this.orderMapper = orderMapper;
        this.jarvisOpenApi = jarvisOpenApi;
        this.eshopBizMapper = eshopBizMapper;
        this.financeCheckMapper = financeCheckMapper;
        this.eshopSaleOrderManualService = eshopSaleOrderManualService;
        this.checkInMapper = checkInMapper;
        this.bifrostEshopOrderService = bifrostEshopOrderService;
        this.bigDataMapper = bigDataMapper;
        this.ydhApi = ydhApi;
        this.billCoreService = billCoreService;
        this.billDetailUpdateService = billDetailUpdateService;
        inoutMapping = new HashMap<>();
        inoutMapping.put(BillPostState.UNCONFIRMED, "待审核");
        inoutMapping.put(BillPostState.CONFIRM_WAIT, "待审核");
        inoutMapping.put(BillPostState.CONFIRM_COMPLETED, "待出库");
        inoutMapping.put(BillPostState.PROCESS_COMPLETED, "待出库");
        inoutMapping.put(BillPostState.STOCK_INOUT_PART, "已出库");
        inoutMapping.put(BillPostState.STOCK_INOUT_COMPLETED, "已出库");
        inoutMapping.put(BillPostState.STOCK_POST_FAILED, "核算失败");
        inoutMapping.put(BillPostState.STOCK_POST_FAILED_PROCESSED, "核算失败已处理");
        inoutMapping.put(BillPostState.ACCOUNTING_COMPLETED, "核算成功");
    }

    // 定义硬编码值为类常量
    private static final int DELIVERY_TYPE = 1;


    public PageResponse<EshopRefundEntity> queryAfterSaleList(@RequestBody PageRequest<AfterSaleListQueryRequest> parameter, boolean needDetails) {
        try {
            // todo zsh 还有筛选的查询 还有商品的查询
            // 排序
            ensureSorting(parameter);
            AfterSaleListQueryRequest param = parameter.getQueryParams();
            // 处理参数 查询补全参数条件
            handleParam(param);
            // 处理系统查询需要的条件
            handleSystemQuery(parameter, param);
            PageDevice.initPage(parameter, false);
            PageDevice.initPageWithCount(parameter, false);
            // 查询售后单列表数据
            List<EshopRefundEntity> data = needDetails ? queryAfterSaleListWithDetail(param) : queryAfterSaleListWithNoDetail(param);
            return returnAfterSaleList(parameter, data);
        } catch (Exception e) {
            logger.error("售后单查询异常:", e);
        }
        return null;
    }


    public int queryAfterSaleListCount(PageRequest<AfterSaleListQueryRequest> parameter) {
        parameter.setSorts(null);
        AfterSaleListQueryRequest param = parameter.getQueryParams();
        // 处理参数 查询补全参数条件
        handleParam(param);
        // 处理系统查询需要的条件
        handleSystemQuery(parameter, param);
        PageDevice.initPage(parameter, true);
        PageDevice.initPageWithCount(parameter, true);

        // 查询售后单数量
        return refundMapper.queryAfterSaleListCount(param);
    }

    public List<EshopRefundEntity> queryAfterSaleListWithDetail(AfterSaleListQueryRequest refundParameter) {
        List<EshopRefundEntity> refundList = refundMapper.queryAfterSaleList(refundParameter);
        if (refundList == null || refundList.isEmpty()) {
            refundList = new ArrayList<>();
        }

        List<BigInteger> refundVchcodes = refundList.stream().map(EshopRefundEntity::getId).collect(Collectors.toList());
        int pageSize = 1000;
        int size = refundVchcodes.size();
        int pageCount = size % pageSize == 0 ? size / pageSize : size / pageSize + 1;
        List<EshopRefundFreight> refundFreights = new ArrayList<>(size);
        List<EshopRefundApplyDetail> refundApplyDetails = new ArrayList<>(size);
        List<EshopRefundApplyDetailCombo> refundApplyDetailCombos = new ArrayList<>(size);
        List<EshopRefundDetailSerialNo> refundDetailSerialNos = new ArrayList<>(size);
        List<EshopOrderMarkEntity> refundMarks = new ArrayList<>(size);
        List<EshopRefundBillReleationEntity> orderReleationList = new ArrayList<>(size);
        BigInteger profileId = CurrentUser.getProfileId();
        for (int i = 0; i < pageCount; i++) {
            List<BigInteger> splitRefundVchcodes = refundVchcodes.stream().skip((long) i * pageSize).limit(pageSize).collect(Collectors.toList());
            List<EshopRefundFreight> eshopRefundFreightsNew = refundMapper.queryRefundFreightLists(profileId, splitRefundVchcodes);
            if (!eshopRefundFreightsNew.isEmpty()) {
                refundFreights.addAll(eshopRefundFreightsNew);
            }
            List<EshopRefundApplyDetail> eshopRefundApplyDetailsNew = refundMapper.queryRefundDetailListByVchcodes(profileId, splitRefundVchcodes, null);
            if (!eshopRefundApplyDetailsNew.isEmpty()) {
                refundApplyDetails.addAll(eshopRefundApplyDetailsNew);
            }
            List<EshopRefundApplyDetailCombo> comboRowsNewveCheckInDetailsNew = refundMapper.queryRefundComboDetailListByVchcodes(profileId, splitRefundVchcodes);
            if (!comboRowsNewveCheckInDetailsNew.isEmpty()) {
                refundApplyDetailCombos.addAll(comboRowsNewveCheckInDetailsNew);
            }
            List<EshopRefundDetailSerialNo> eshopRefundDetailSerialNosNew = refundMapper.queryRefundDetailSerialInfosByVchcodes(profileId, splitRefundVchcodes, NameConstantEnum.REFUND_APPLY_DETAIL_SERIALNO.getName());
            if (!eshopRefundDetailSerialNosNew.isEmpty()) {
                refundDetailSerialNos.addAll(eshopRefundDetailSerialNosNew);
            }
            EshopOrderMarkParameter markParameter = new EshopOrderMarkParameter();
            markParameter.setProfileId(profileId);
            markParameter.setEshopOrderIds(splitRefundVchcodes);
            List<EshopOrderMarkEntity> marks = orderMapper.queryEshopOrderMark(markParameter);
            if (!marks.isEmpty()) {
                refundMarks.addAll(marks);
            }
            List<EshopRefundBillReleationEntity> releationList = eshopRefundBillReleationMapper.getOrderReleationListBatch(splitRefundVchcodes, profileId);
            if (!releationList.isEmpty()) {
                orderReleationList.addAll(releationList);
            }

        }
        // 获取淘宝配置信息
        TaobaoConfig taoBaoConfig = getTaoBaoConfig();
        List<BillInfoForRefund> billInfoForRefunds;
        if (refundParameter.isDeliverFreightVisible()) {
            billInfoForRefunds = buildDeliverFreightNew(refundList);
        } else {
            billInfoForRefunds = new ArrayList<>();
        }
        List<RefundDuty> refundDuties = new ArrayList<>();
        List<BigInteger> refundDutyIdList = new ArrayList<>();
        Map<BigInteger, RefundDuty> refundDutyMap = new HashMap<>();
        if (refundParameter.isRefundDutyVisible()) {
            refundList.forEach(x -> {
                refundDutyIdList.addAll(x.getRefundDutyIdsList());
            });
            if (!refundDutyIdList.isEmpty()) {
                refundDuties = refundMapper.getRefundDutyEntityByIds(profileId, refundDutyIdList);
            }
            for (RefundDuty x : refundDuties) {
                refundDutyMap.put(x.getId(), x);
            }
        }
        for (EshopRefundEntity item : refundList) {
            if (refundParameter.isDeliverFreightVisible() && !billInfoForRefunds.isEmpty()) {
                buildDeliverInfo(item, billInfoForRefunds, item.getId());
            }
            List<EshopRefundFreight> freights = refundFreights.stream().filter(f -> f.getRefundOrderId().equals(item.getId())).collect(Collectors.toList());
            if (!freights.isEmpty()) {
                List<String> nos = freights.stream().map(EshopRefundFreight::getFreightNo).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
                item.setFreightNo(StringUtils.join(nos, ","));
                item.setFreightName(StringUtils.join(freights.stream().map(EshopRefundFreight::getFreightName).filter(StringUtils::isNotEmpty).collect(Collectors.toList()), ","));
                item.setFreightBtypeId(freights.get(0).getFreightBtypeId());
            }
            List<EshopRefundApplyDetail> details = refundApplyDetails.stream().filter(d -> {
                if (d.getCostPeriod() == 0) {
                    d.setCostPeriod(null);
                }
                return d.getRefundOrderId().equals(item.getId());
            }).collect(Collectors.toList());
            List<EshopRefundApplyDetailCombo> comboRowList = refundApplyDetailCombos.stream().filter(c -> c.getRefundOrderId().equals(item.getId())).collect(Collectors.toList());
            List<EshopRefundDetailSerialNo> snList = refundDetailSerialNos.stream().filter(c -> c.getRefundOrderId().equals(item.getId())).collect(Collectors.toList());
            List<EshopRefundApplyDetail> refundDetails = queryRefundDetails2(details, comboRowList, snList);
            item.setRefundApplyDetails(refundDetails);
            buildRefundDutyNew(item, refundDutyMap);
            setPlatformPicUrlIfNotEmpty(item);
            getMarks(item, refundMarks);
            getProcessTradeOrderId(item, orderReleationList);
            getInfoNew(item, taoBaoConfig);
        }
        buildOrderProcessStates(refundParameter.getProfileId(), refundList);
        return refundList;
    }

    public List<EshopRefundEntity> queryAfterSaleListWithNoDetail(AfterSaleListQueryRequest refundParameter) {
        List<EshopRefundEntity> refundList = refundMapper.queryAfterSaleList(refundParameter);
        if (refundList == null || refundList.isEmpty()) {
            refundList = new ArrayList<>();
        }

        List<BigInteger> refundVchcodes = refundList.stream().map(EshopRefundEntity::getId).collect(Collectors.toList());
        int pageSize = 1000;
        int size = refundVchcodes.size();
        int pageCount = size % pageSize == 0 ? size / pageSize : size / pageSize + 1;
        List<EshopOrderMarkEntity> refundMarks = new ArrayList<>(size);
        List<EshopRefundBillReleationEntity> orderReleationList = new ArrayList<>(size);
        BigInteger profileId = CurrentUser.getProfileId();
        for (int i = 0; i < pageCount; i++) {
            List<BigInteger> splitRefundVchcodes = refundVchcodes.stream().skip((long) i * pageSize).limit(pageSize).collect(Collectors.toList());
            EshopOrderMarkParameter markParameter = new EshopOrderMarkParameter();
            markParameter.setProfileId(profileId);
            markParameter.setEshopOrderIds(splitRefundVchcodes);
            List<EshopOrderMarkEntity> marks = orderMapper.queryEshopOrderMark(markParameter);
            if (!marks.isEmpty()) {
                refundMarks.addAll(marks);
            }
            List<EshopRefundBillReleationEntity> releationList = eshopRefundBillReleationMapper.getOrderReleationListBatch(splitRefundVchcodes, profileId);
            if (!releationList.isEmpty()) {
                orderReleationList.addAll(releationList);
            }
        }
        // 获取淘宝配置信息
        TaobaoConfig taoBaoConfig = getTaoBaoConfig();
        List<BillInfoForRefund> billInfoForRefunds;
        if (refundParameter.isDeliverFreightVisible()) {
            billInfoForRefunds = buildDeliverFreightNew(refundList);
        } else {
            billInfoForRefunds = new ArrayList<>();
        }
        List<RefundDuty> refundDuties = new ArrayList<>();
        List<BigInteger> refundDutyIdList = new ArrayList<>();
        Map<BigInteger, RefundDuty> refundDutyMap = new HashMap<>();
        if (refundParameter.isRefundDutyVisible()) {
            refundList.forEach(x -> {
                refundDutyIdList.addAll(x.getRefundDutyIdsList());
            });
            if (!refundDutyIdList.isEmpty()) {
                refundDuties = refundMapper.getRefundDutyEntityByIds(profileId, refundDutyIdList);
            }
            for (RefundDuty x : refundDuties) {
                refundDutyMap.put(x.getId(), x);
            }
        }
        for (EshopRefundEntity item : refundList) {
            if (refundParameter.isDeliverFreightVisible() && !billInfoForRefunds.isEmpty()) {
                buildDeliverInfo(item, billInfoForRefunds, item.getId());
            }
            buildRefundDutyNew(item, refundDutyMap);
            setPlatformPicUrlIfNotEmpty(item);
            getMarks(item, refundMarks);
            getProcessTradeOrderId(item, orderReleationList);
            getInfoNew(item, taoBaoConfig);
        }
        buildOrderProcessStates(refundParameter.getProfileId(), refundList);
        return refundList;
    }

    private static TaobaoConfig getTaoBaoConfig() {
        TaobaoConfig config = null;
        try {
            config = BeanUtils.getBean(TaobaoConfig.class);
        } catch (Exception e) {
            logger.error("获取TaoBaoConfig失败,失败原因: {}", e.getMessage());
        }
        return config;
    }

    @NotNull
    private static PageResponse<EshopRefundEntity> returnAfterSaleList(PageRequest<AfterSaleListQueryRequest> parameter, List<EshopRefundEntity> data) {
        PageResponse<EshopRefundEntity> response = new PageResponse<>();
        response.setList(data);
        response.setTotal(0L);
        response.setPageIndex(parameter.getPageIndex());
        response.setPageSize(parameter.getPageSize());
        return response;
    }

    private static void handleSystemQuery(PageRequest<AfterSaleListQueryRequest> parameter, AfterSaleListQueryRequest param) {
        CommonUtil.initLimited(param);
        param.setAllowEtypeIds(PermissionValiateService.getLimitEtypeIds());
        if (!param.getAllowEtypeIds().contains(BigInteger.ZERO)) {
            param.getAllowEtypeIds().add(BigInteger.ZERO); // 避免重复添加，提高性能
        }
    }

    private void handleParam(AfterSaleListQueryRequest param) {
        //批量查询类型为 【售后单号/订单编号】
        if (param.getRefundBatchQueryType() != null && param.getRefundBatchQueryType().equals(AfterSaleBatchQueryTypeEnum.REFUND_ORDER_NUMBER)) {
            ArrayList<String> list = keyWordHandle(param.getKeyword());
            if (!list.isEmpty()) {
                param.setTradeRefundOrderNumbers(list);
                param.setTradeOrderIds(list);
            }
        }
        //批量查询类型为【退货物流/原单物流】【原发货物流公司】
        if (param.getRefundBatchQueryType() != null && (param.getRefundBatchQueryType().equals(AfterSaleBatchQueryTypeEnum.FREIGHT_NO) || param.getRefundBatchQueryType().equals(AfterSaleBatchQueryTypeEnum.SEND_FREIGHT_NAME))) {
            List<BigInteger> detailIds = getDetailIds(param);
            if (detailIds != null && !detailIds.isEmpty()) {
                param.setSourceDetailIds(detailIds);
            }
            ArrayList<String> list = keyWordHandle(param.getKeyword());
            param.setFreightNumbers(list);
        }
        // 批量查询类型【买家账号查询】 或者筛选包含手机号和买家账号
        if ((param.getRefundBatchQueryType() != null && (param.getRefundBatchQueryType().equals(AfterSaleBatchQueryTypeEnum.BUYER_ACCOUNT)) || (null != param.getFilter() && (StringUtils.isNotBlank(param.getFilter().getCustomerReceiverMobile()) || StringUtils.isNotBlank(param.getFilter().getCustomerShopAccount()))))) {
            getDicInfos(param);
        }
        //  批量查询类型【父级订单编号】
        if (param.getRefundBatchQueryType() != null && (param.getRefundBatchQueryType().equals(AfterSaleBatchQueryTypeEnum.PLATFORM_PARENT_ORDER_ID))) {
            ArrayList<String> list = keyWordHandle(param.getKeyword());
            if (!list.isEmpty()) {
                param.setParentTradeOrderIds(list);
            }
        }
        //  批量查询类型【退货物流公司】
        if (param.getRefundBatchQueryType() != null && (param.getRefundBatchQueryType().equals(AfterSaleBatchQueryTypeEnum.RETURN_FREIGHT_NAME))) {
            ArrayList<String> list = keyWordHandle(param.getKeyword());
            if (!list.isEmpty()) {
                param.setFreightCompanyNames(list);
            }
        }
        //  批量查询类型【网店商品信息查询】
        if (param.getRefundBatchQueryType() != null && (param.getRefundBatchQueryType().equals(AfterSaleBatchQueryTypeEnum.PLATFORM_PTYPE_INFO))) {
            ArrayList<String> list = keyWordHandle(param.getKeyword());
            if (!list.isEmpty()) {
                param.setBabyInfos(list);
            }
        }


    }

    private List<BigInteger> getDetailIds(AfterSaleListQueryRequest param) {
        GetDetailIdsForRefundRequest request = new GetDetailIdsForRefundRequest();
        request.setProfileId(CurrentUser.getProfileId());
        String keyword = param.getKeyword();
        ArrayList<String> list = keyWordHandle(keyword);
        if (list.isEmpty()) {
            return Collections.emptyList();
        }
        if (param.getRefundBatchQueryType() != null && param.getRefundBatchQueryType().equals(AfterSaleBatchQueryTypeEnum.FREIGHT_NO)) {
            request.setFreightBillNos(list);
        } else if (param.getRefundBatchQueryType() != null && param.getRefundBatchQueryType().equals(AfterSaleBatchQueryTypeEnum.SEND_FREIGHT_NAME)) {
            request.setCompanyNames(list);
        }
        return jarvisOpenApi.getDetailIdsForRefund(request);
    }

    private ArrayList<String> keyWordHandle(String keyword) {
        if (keyword == null || keyword.isEmpty()) {
            return new ArrayList<>();
        }
        keyword = keyword.replace("，", ",");
        keyword = keyword.replace(" ", ",");
        keyword = keyword.replace("\n", ",");
        keyword = keyword.replace("\t", ",");
        keyword = keyword.replace("\r", ",");
        return new ArrayList<>(Arrays.asList(keyword.split(",")));
    }

    private void getDicInfos(AfterSaleListQueryRequest queryRefundParameter) {
        Set<Integer> shopTypeSet = new HashSet<>();
        List<BigInteger> eshopIds = new ArrayList<>();
        if (queryRefundParameter.getOtypeIds() != null && !queryRefundParameter.getOtypeIds().isEmpty()) {
            List<EshopInfo> eshopInfoList = eshopBizMapper.getEshopInfoByIds(CurrentUser.getProfileId(), queryRefundParameter.getOtypeIds());
            Iterator var5 = eshopInfoList.iterator();
            while (var5.hasNext()) {
                EshopInfo shop = (EshopInfo) var5.next();
                if (shop.getEshopType() != null) {
                    shopTypeSet.add(shop.getEshopType().getCode());
                }
                eshopIds.add(shop.getOtypeId());
            }
        }
        if (StringUtils.isNotBlank(queryRefundParameter.getKeyword())) {
            List<String> keyWords = new ArrayList<>();
            List<String> tradeIds = new ArrayList<>();

            if (null != queryRefundParameter.getRefundBatchQueryType() && queryRefundParameter.getRefundBatchQueryType().isSensitive()) {
                ArrayList<String> list = keyWordHandle(queryRefundParameter.getKeyword());
                if (list.isEmpty()) {
                    return;
                }
                for (String sensitive : list) {
                    List<DicInfo> dicInfos;
                    DicRequest request = new DicRequest();
                    request.setField(queryRefundParameter.getRefundBatchQueryType().getSensitiveFieldEnum());
                    request.setValue(sensitive);
                    request.setEshopIds(eshopIds);
                    request.setStartTime(queryRefundParameter.getBeginTime());
                    request.setEndTime(queryRefundParameter.getEndTime());
                    request.setContainsJd(false);
                    dicInfos = SisClient.getDicInfosByTime(request);
                    keyWords.addAll(Optional.ofNullable(dicInfos).orElse(Lists.newArrayList()).stream().filter(x -> CollectionUtils.isNotEmpty(x.getValues())).map(DicInfo::getValue).distinct().collect(Collectors.toList()));
                    List<List<String>> collect = null;
                    if (dicInfos != null) {
                        collect = dicInfos.stream().map(DicInfo::getTradeIds).collect(Collectors.toList());
                    }
                    if (collect != null) {
                        for (List<String> ids : collect) {
                            if (CollectionUtils.isNotEmpty(ids)) {
                                tradeIds.addAll(ids);
                            }
                        }
                    }
                }
                List<String> collect = tradeIds.stream().distinct().collect(Collectors.toList());
                queryRefundParameter.setTradeOrderIds(collect);
                queryRefundParameter.setBatchQueryEncryptKeyList(list);
                queryRefundParameter.setBatchQueryKeyList(keyWords);
            }
        }
        // todo  手机号
        RefundManageFilter filter = queryRefundParameter.getFilter();
        if (null != filter && StringUtils.isNotBlank(filter.getCustomerReceiverMobile())) {
            DicRequest request = new DicRequest();
            request.setField(SensitiveFieldEnum.BUYER_MOBILE);
            request.setValue(filter.getCustomerReceiverMobile());
            request.setEshopIds(eshopIds);
            request.setStartTime(queryRefundParameter.getBeginTime());
            request.setEndTime(queryRefundParameter.getEndTime());
            request.setContainsJd(false);
            List<DicInfo> dicInfos = SisClient.getDicInfosByTime(request);
            if (!dicInfos.isEmpty()) {
                List<String> dicValueList = dicInfos.stream().filter(Objects::nonNull).filter(item -> {
                    return item.getValues() != null && !item.getValues().isEmpty();
                }).map(DicInfo::getValue).collect(Collectors.toList());
                queryRefundParameter.getFilter().setCustomerReceiverMobileDics(dicValueList);
            }
        }

        if (null != filter && StringUtils.isNotBlank(filter.getCustomerShopAccount())) {
            DicRequest request = new DicRequest();
            request.setField(SensitiveFieldEnum.BUYER_ACCOUNT);
            request.setValue(filter.getCustomerShopAccount());
            request.setEshopIds(eshopIds);
            request.setStartTime(queryRefundParameter.getBeginTime());
            request.setEndTime(queryRefundParameter.getEndTime());
            request.setContainsJd(false);
            List<DicInfo> dicInfos = SisClient.getDicInfosByTime(request);
            List<String> dicValueList = Optional.ofNullable(dicInfos).orElse(Lists.newArrayList()).stream().map(DicInfo::getValue).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
            queryRefundParameter.getFilter().setCustomerShopAccountDics(dicValueList);
        }
    }

    private void ensureSorting(PageRequest<AfterSaleListQueryRequest> parameter) {
        if (parameter.getSorts() == null || parameter.getSorts().isEmpty()) {
            Sort sort = new Sort();
            sort.setDataField("refundCreateTime");
            sort.setAscending(true);
            sort.setSysCase(false);
            parameter.setSorts(Collections.singletonList(sort));
        }
        for (Sort sort : parameter.getSorts()) {
            if (sort.getDataField().equals("orderTimeTags")) {
                sort.setDataField("orderTimeTag");
                sort.setAscending(!sort.getAscending());
            }
        }
    }

    private List<BillInfoForRefund> buildDeliverFreightNew(List<EshopRefundEntity> list) {
        try {
            if (list.isEmpty()) {
                return Collections.emptyList();
            }
            List<BigInteger> sourceDetailIds = refundMapper.getSourceDetailIdsBatch(list.stream().map(EshopRefundEntity::getId).collect(Collectors.toList()), CurrentUser.getProfileId());
            if (sourceDetailIds.isEmpty()) {
                return Collections.emptyList();
            }
            sourceDetailIds = sourceDetailIds.stream().filter(item -> {
                return item != null && item.compareTo(BigInteger.ZERO) != 0;
            }).collect(Collectors.toList());
            List<BillInfoForRefund> billInfoForRefund = jarvisOpenApi.getBillInfoForRefund(sourceDetailIds);
            if (billInfoForRefund.isEmpty()) {
                return Collections.emptyList();
            }
            return billInfoForRefund;
        } catch (Exception e) {
            // 异常处理逻辑（示例）
            System.err.println("An error occurred while building deliver freight info: " + e.getMessage());
            // 可以根据具体情况决定是否要将异常向上抛出，或者记录日志等。
            return Collections.emptyList();
        }
    }

    private void buildDeliverInfo(EshopRefundEntity item, List<BillInfoForRefund> billInfoForRefunds, BigInteger refundId) {
        List<BillInfoForRefund> filteredList = billInfoForRefunds.stream().filter(bill -> bill.getRefundOrderId().compareTo(refundId) == 0).filter(bill -> !bill.getFreightBillNo().isEmpty() && bill.getFreightBtypeName() != null).collect(Collectors.toList());
        filteredList = filteredList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(p -> p.getFreightBtypeName() + ";" + p.getFreightBillNo()))), ArrayList::new));
        StringBuilder deliverFreightCompany = new StringBuilder();
        StringBuilder deliverFreightNumber = new StringBuilder();
        appendCommaSeparatedValues(deliverFreightCompany, deliverFreightNumber, filteredList, BillInfoForRefund::getFreightBtypeName, BillInfoForRefund::getFreightBillNo);
        item.setDeliverFreightCompany(deliverFreightCompany.toString());
        item.setDeliverFreightNumber(deliverFreightNumber.toString());
    }

    private void appendCommaSeparatedValues(StringBuilder firstBuilder, StringBuilder secondBuilder, List<BillInfoForRefund> list, Function<BillInfoForRefund, String> firstValueGetter, Function<BillInfoForRefund, String> secondValueGetter) {
        boolean isFirst = true;
        for (BillInfoForRefund bill : list) {
            String firstValue = firstValueGetter.apply(bill);
            String secondValue = secondValueGetter.apply(bill);
            if (isFirst) {
                isFirst = false;
            } else {
                firstBuilder.append(",");
                secondBuilder.append(",");
            }
            firstBuilder.append(firstValue);
            secondBuilder.append(secondValue);
        }
    }

    public List<EshopRefundApplyDetail> queryRefundDetails2(List<EshopRefundApplyDetail> refundDetailList, List<EshopRefundApplyDetailCombo> comboRows, List<EshopRefundDetailSerialNo> eshopRefundDetailSerialNos) {
        if (refundDetailList != null && !refundDetailList.isEmpty()) {
            refundDetailList.forEach(item -> {
                if (StringUtils.isNotEmpty(item.getPlatformPicUrl())) {
                    item.setPicUrl(item.getPlatformPicUrl());
                }
                // parameter.setId(item.getId());
                List<EshopRefundDetailSerialNo> serialNos = eshopRefundDetailSerialNos.stream().filter(c -> c.getDetailId().equals(item.getDetailId())).collect(Collectors.toList());
                if (!serialNos.isEmpty()) {
                    item.setSerialNoList(serialNos);
                    // 导出不会触发get方法取对应的值，这里重新设置一下
                    item.setSnNoStr(item.getSnNoStr());
                }
            });
        }
        if (comboRows == null || comboRows.isEmpty()) {
            return refundDetailList;
        }
        return filterRefundDetailsNEW(refundDetailList, comboRows);
    }

    private List<EshopRefundApplyDetail> filterRefundDetailsNEW(List<EshopRefundApplyDetail> refundDetailList, List<EshopRefundApplyDetailCombo> comboRows) {
        // 使用Optional简化空值检查
        List<EshopRefundApplyDetail> effectiveRefundDetailList = Optional.ofNullable(refundDetailList).orElse(Collections.emptyList());
        List<EshopRefundApplyDetailCombo> effectiveComboRows = Optional.ofNullable(comboRows).orElse(Collections.emptyList());

        // 调用convertComboRows方法，并处理可能的异常
        try {
            List<EshopRefundApplyDetail> comboDetails = convertComboRows(effectiveRefundDetailList, effectiveComboRows);
            List<EshopRefundApplyDetail> result = new ArrayList<>();
            if (!comboDetails.isEmpty()) {
                result.addAll(comboDetails);
            }
            List<EshopRefundApplyDetail> normalDetail = effectiveRefundDetailList.stream().filter(x -> x.getComboRowId().longValue() == 0).collect(Collectors.toList());
            if (!normalDetail.isEmpty()) {
                result.addAll(normalDetail);
            }
            return result;
        } catch (Exception e) {
            // 根据实际情况处理异常，例如记录日志或返回空列表
            // 此处为示例，实际应用中应根据具体情况进行处理
            System.err.println("Error processing refund details: " + e.getMessage());
            return Collections.emptyList();
        }
    }

    private List<EshopRefundApplyDetail> convertComboRows(List<EshopRefundApplyDetail> refundDetails, List<EshopRefundApplyDetailCombo> comboRows) {
        List<EshopRefundApplyDetail> comboDetails = new ArrayList<>();
        if (comboRows == null || comboRows.isEmpty()) {
            return comboDetails;
        }
        for (EshopRefundApplyDetailCombo combo : comboRows) {
            EshopRefundApplyDetail comboRow = combo.toRefundDetail();
            List<EshopRefundApplyDetail> details = refundDetails.stream().filter(x -> x.getComboRowId().equals(combo.getId())).collect(Collectors.toList());
            if (details.isEmpty()) {
                continue;
            }
            EshopRefundApplyDetail refundDetail = details.get(0);
            comboRow.setComboId(combo.getComboId());
            comboRow.setPlatformFullName(refundDetail.getPlatformFullName());
            comboRow.setPlatformPropertiesName(refundDetail.getPlatformPropertiesName());
            comboRow.setPlatformNumId(refundDetail.getPlatformNumId());
            comboRow.setPlatformSkuId(refundDetail.getPlatformSkuId());
            comboRow.setPlatformXcode(refundDetail.getPlatformXcode());
            comboRow.setPlatformPicUrl(refundDetail.getPlatformPicUrl());
            comboRow.setPicUrl(refundDetail.getPlatformPicUrl());

            comboDetails.add(comboRow);
            comboDetails.addAll(details);
        }
        return comboDetails;
    }

    private void buildRefundDutyNew(EshopRefundEntity item, Map<BigInteger, RefundDuty> refundDutyMap) {
        // 增加对item的null检查
        if (item == null) {
            return;
        }
        if (item.getRefundDutyIdsList() != null && !item.getRefundDutyIdsList().isEmpty()) {
            try {
                // 使用Stream API进行优化
                String dutyString = item.getRefundDutyIdsList().stream().map(refundDutyMap::get).filter(Objects::nonNull) // 过滤null值
                        .map(RefundDuty::getRefundDuty).filter(d -> !d.isEmpty()) // 确保不添加空字符串
                        .collect(Collectors.joining(",")); // 拼接字符串

                if (!dutyString.isEmpty()) {
                    item.setRefundDuties(dutyString);
                }
            } catch (Exception e) {
                // 异常处理：可以按需记录日志或进行其他处理
                logger.error("Error building refund duties: ", e);
                // 如果你有特定的日志框架，请替换上面的注释代码
            }
        }
    }

    public void setPlatformPicUrlIfNotEmpty(EshopRefundEntity refundEntity) {
        if (Objects.isNull(refundEntity)) {
            logger.warn("Refund entity is null.");
            return;
        }
        List<EshopRefundApplyDetail> refundDetails = refundEntity.getRefundDetails();
        if (Objects.isNull(refundDetails) || refundDetails.isEmpty()) {
            logger.warn("Refund details are null or empty.");
            return;
        }
        // 优化：避免对每个detail都执行get和set，先过滤出需要处理的detail
        refundDetails.stream().filter(x -> StringUtils.isNotEmpty(x.getPlatformPicUrl())).forEach(x -> x.setPicUrl(x.getPlatformPicUrl()));
    }

    public void getMarks(EshopRefundEntity refund, List<EshopOrderMarkEntity> marks) {
        List<EshopOrderMarkEntity> refundMarks = marks.stream().filter(item -> item.getOrderId() != null && item.getOrderId().compareTo(refund.getId()) == 0).collect(Collectors.toList());
        buildRefundMark(refund, refundMarks, refund.getRefundTimeMarks());
    }

    private void buildRefundMark(EshopRefundEntity item, List<EshopOrderMarkEntity> refundMarks, List<EshopOrderMarkEntity> refundTimeMarks) {
        if (refundMarks == null) {
            refundMarks = new ArrayList<>();
        }
        if (refundTimeMarks == null) {
            refundTimeMarks = new ArrayList<>();
        }
        // 优化：只调用一次 System.currentTimeMillis()
        long currentTimeMillis = System.currentTimeMillis();

        // 提取公共逻辑，减少重复代码
        RefundTiming refundTiming = item.getRefundTiming();
        if (refundTiming.getId().compareTo(BigInteger.ZERO) == 0) {
            return;
        }
        List<Long> times = new ArrayList<>();
        List<Long> otherTime = new ArrayList<>();
        // 审核超时
        if (item.getRefundTiming().getSysPromisedConfirmTime() != null && System.currentTimeMillis() > item.getRefundTiming().getSysPromisedConfirmTime().getTime() && item.getConfirmState().equals(RefundAuditStatus.WAIT_AUDIT) && !RefundTypeEnum.REFUND_ONLY_ON_SALE.equals(item.getRefundTypeEnum()) && !item.getRefundState().equals(RefundStatus.NONE) && !item.getRefundState().equals(RefundStatus.SELLER_REFUSE) && !item.getRefundState().equals(RefundStatus.CANCEL) && !item.getRefundState().equals(RefundStatus.SUCCESS)) {
            refundMarks.add(buildMark(item.getProfileId(), item.getId(), 200073));
            long l = currentTimeMillis - refundTiming.getSysPromisedConfirmTime().getTime();
            times.add(l);
        }
        // 退货收货超时
        if (!RefundTypeEnum.MONEY_ONLY.equals(item.getRefundTypeEnum()) && !RefundTypeEnum.REFUND_ONLY_ON_SALE.equals(item.getRefundTypeEnum()) && item.getRefundTiming().getPromisedReceiveTime() != null && System.currentTimeMillis() > item.getRefundTiming().getPromisedReceiveTime().getTime() && item.getReceiveState().equals(RefundReceiveStatus.WAITING_RECEIVE) && !item.getRefundState().equals(RefundStatus.NONE) && !item.getRefundState().equals(RefundStatus.SELLER_REFUSE) && !item.getRefundState().equals(RefundStatus.CANCEL) && !item.getRefundState().equals(RefundStatus.SUCCESS)) {
            refundMarks.add(buildMark(item.getProfileId(), item.getId(), 200074));
            long l = currentTimeMillis - refundTiming.getPromisedReceiveTime().getTime();
            times.add(l);
        }
        // 线上同意退款超时
        if (item.getCreateType() != null && item.getCreateType().equals(RefundCreateTypeEnum.DOWNLOAD_CREATE) && !RefundTypeEnum.OMIT_SEND_GOODS.equals(item.getRefundTypeEnum()) && !RefundTypeEnum.RESEND_GOODS.equals(item.getRefundTypeEnum()) && !RefundTypeEnum.EXCHANGE_GOODS.equals(item.getRefundTypeEnum()) && item.getRefundTiming().getPromisedConfirmTime() != null && System.currentTimeMillis() > item.getRefundTiming().getPromisedConfirmTime().getTime() && !item.getRefundState().equals(RefundStatus.NONE) && !item.getRefundState().equals(RefundStatus.SELLER_REFUSE) && !item.getRefundState().equals(RefundStatus.CANCEL) && !item.getRefundState().equals(RefundStatus.SUCCESS)) {
            refundMarks.add(buildMark(item.getProfileId(), item.getId(), 200075));
            long l = currentTimeMillis - refundTiming.getPromisedConfirmTime().getTime();
            times.add(l);
        }
        // 补发超时
        if (item.getRefundTiming().getPromisedDeliverTime() != null && System.currentTimeMillis() > item.getRefundTiming().getPromisedDeliverTime().getTime() && item.getRefundProcessState().equals(RefundSaleProcessEnum.WAIT_PROCESS) && !item.getRefundState().equals(RefundStatus.NONE) && !item.getRefundState().equals(RefundStatus.SELLER_REFUSE) && !item.getRefundState().equals(RefundStatus.CANCEL) && !item.getRefundState().equals(RefundStatus.SUCCESS)) {
            refundMarks.add(buildMark(item.getProfileId(), item.getId(), 200076));
            long l = currentTimeMillis - refundTiming.getPromisedDeliverTime().getTime();
            times.add(l);
        }
        // 线上同意退货超时
        if (item.getCreateType() != null && item.getCreateType().equals(RefundCreateTypeEnum.DOWNLOAD_CREATE) && !RefundTypeEnum.MONEY_ONLY.equals(item.getRefundTypeEnum()) && !RefundTypeEnum.REFUND_ONLY_ON_SALE.equals(item.getRefundTypeEnum()) && !RefundTypeEnum.RESEND_GOODS.equals(item.getRefundTypeEnum()) && !RefundTypeEnum.OMIT_SEND_GOODS.equals(item.getRefundTypeEnum()) && item.getRefundTiming().getPromisedAgreeTime() != null && System.currentTimeMillis() > item.getRefundTiming().getPromisedAgreeTime().getTime() && item.getPlatformReturnState().equals(PlatformReturnState.NONE) && item.getRefundState().equals(RefundStatus.WAIT_SELLER_AGREE) && !item.getRefundState().equals(RefundStatus.NONE) && !item.getRefundState().equals(RefundStatus.SELLER_REFUSE) && !item.getRefundState().equals(RefundStatus.CANCEL) && !item.getRefundState().equals(RefundStatus.SUCCESS)) {
            refundMarks.add(buildMark(item.getProfileId(), item.getId(), 200077));
            long l = currentTimeMillis - refundTiming.getPromisedAgreeTime().getTime();
            times.add(l);
        }
        if (item.getCreateType() != null && item.getCreateType().equals(RefundCreateTypeEnum.DOWNLOAD_CREATE) && !item.getRefundState().equals(RefundStatus.SUCCESS) && !item.getRefundState().equals(RefundStatus.CANCEL)) {
            if (!times.isEmpty()) {
                List<Long> collect = times.stream().sorted().collect(Collectors.toList());
                Long time = collect.get(collect.size() - 1);
                refundTimeMarks.add(buildMarkForBeyondTime(item.getProfileId(), item.getId(), time));
                item.setRefundTimeMarks(refundTimeMarks);
            } else {
                // 没有超时的时候 就去计算即将超时
                long time1 = refundTiming.getSysPromisedConfirmTime().getTime() - currentTimeMillis;
                long time2 = refundTiming.getPromisedReceiveTime().getTime() - currentTimeMillis;
                long time3 = refundTiming.getPromisedConfirmTime().getTime() - currentTimeMillis;
                long time4 = refundTiming.getPromisedDeliverTime().getTime() - currentTimeMillis;
                long time5 = refundTiming.getPromisedAgreeTime().getTime() - currentTimeMillis;
                otherTime.add(time1);
                otherTime.add(time2);
                otherTime.add(time3);
                otherTime.add(time4);
                otherTime.add(time5);
                List<Long> collect = otherTime.stream().sorted().collect(Collectors.toList());
                Long time = collect.get(0);
                if (time <= 259200000 && time > 0) {
                    refundTimeMarks.add(buildMarkForRemainTime(item.getProfileId(), item.getId(), time));
                    item.setRefundTimeMarks(refundTimeMarks);
                }
            }
        }
        item.setRefundMarks(refundMarks);
    }

    private EshopOrderMarkEntity buildMark(BigInteger profileId, BigInteger id, int code) {
        EshopOrderMarkEntity item = new EshopOrderMarkEntity();
        item.setId(UId.newId());
        item.setOrderId(id);
        item.setProfileId(profileId);
        item.setOrderType(OrderSourceType.Refund);
        item.setMarkTarget(OrderMarkType.Main);
        item.setMarkCode(BigInteger.valueOf(code));
        item.setBubble(BaseOrderMarkEnum.getBaseOrderMarkEnumByCode(code).getBubble());
        item.setShowType(MarkShowType.All);
        item.setCreateType(MarkCreateType.DownloadSysCalc);
        return item;
    }

    private EshopOrderMarkEntity buildMarkForRemainTime(BigInteger profileId, BigInteger id, Long time) {
        EshopOrderMarkEntity item = new EshopOrderMarkEntity();
        item.setId(UId.newId());
        item.setOrderId(id);
        item.setProfileId(profileId);
        item.setOrderType(OrderSourceType.Refund);
        item.setMarkTarget(OrderMarkType.Main);
        item.setMarkCode(BigInteger.valueOf(BaseOrderMarkEnum.REMAIN_TIME.getCode()));
        item.setBubble(BaseOrderMarkEnum.REMAIN_TIME.getBubble() + formatDuring(time));
        item.setName(BaseOrderMarkEnum.REMAIN_TIME.getBubble() + formatDuring(time));
        item.setShowType(MarkShowType.All);
        item.setCreateType(MarkCreateType.DownloadSysCalc);
        return item;
    }

    public String formatDuring(long mss) {
        long days = mss / (1000 * 60 * 60 * 24);
        long hours = (mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60);
        long minutes = (mss % (1000 * 60 * 60)) / (1000 * 60);
        long seconds = (mss % (1000 * 60)) / 1000;
        return days + "天" + hours + "时" + minutes + "分" + seconds + "秒";

    }

    private EshopOrderMarkEntity buildMarkForBeyondTime(BigInteger profileId, BigInteger id, Long time) {
        EshopOrderMarkEntity item = new EshopOrderMarkEntity();
        item.setId(UId.newId());
        item.setOrderId(id);
        item.setProfileId(profileId);
        item.setOrderType(OrderSourceType.Refund);
        item.setMarkTarget(OrderMarkType.Main);
        item.setMarkCode(BigInteger.valueOf(BaseOrderMarkEnum.ALREADY_BEYOND_TIME.getCode()));
        item.setBubble(BaseOrderMarkEnum.ALREADY_BEYOND_TIME.getBubble() + formatDuring(time));
        item.setName(BaseOrderMarkEnum.ALREADY_BEYOND_TIME.getBubble() + formatDuring(time));
        item.setShowType(MarkShowType.All);
        item.setCreateType(MarkCreateType.DownloadSysCalc);
        return item;
    }

    private void getProcessTradeOrderId(EshopRefundEntity item, List<EshopRefundBillReleationEntity> relatioList) {
        try {
            List<EshopRefundBillReleationEntity> orderReleationList = relatioList.stream().filter(relation -> relation.getRefundOrderId() != null && relation.getRefundOrderId().compareTo(item.getId()) == 0).collect(Collectors.toList());
            if (!orderReleationList.isEmpty()) {
                item.setProcessTradeOrderId(orderReleationList.get(0).getBillNumber());
            }
        } catch (Exception ex) {
            logger.error("获取出库单号错误");
        }
    }

    private void getInfoNew(EshopRefundEntity item, TaobaoConfig finalConfig) {
        // 确保profileId非空以避免NullPointerException
        BigInteger profileId = item.getProfileId();
        // 这个东西使用很少 并且返回实体只有string不好区分 所以暂时不改
        // 对confirmEtypeId的边界条件处理
        if (RefundAuditStatus.AUDITED.equals(item.getConfirmState()) && item.getConfirmEtypeId().compareTo(BigInteger.ZERO) == 0) {
            item.setConfirmEtypeName(SYSTEM);
        }
        if (finalConfig != null && finalConfig.getAppKey() != null) {
            item.setAppKey(finalConfig.getAppKey());
        }
    }

    private void buildOrderProcessStates(BigInteger profileId, List<EshopRefundEntity> refundList) {
        if (ObjectUtils.isEmpty(refundList)) {
            return;
        }
        List<String> tradeIdList = refundList.stream().map(EshopRefundEntity::getTradeOrderId).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(tradeIdList)) {
            return;
        }
        List<OrderProcessStatesEntity> processStatesEntityList = queryOrderProcessStates(profileId, tradeIdList);
        if (CollectionUtils.isEmpty(processStatesEntityList)) {
            return;
        }
        Map<String, OrderProcessStatesEntity> orderProcessStatesByTradeOrderId = processStatesEntityList.stream().collect(Collectors.toMap(OrderProcessStatesEntity::getTradeOrderId, Function.identity(), (key1, key2) -> key2));
        updateRefundListWithProcessStates(refundList, orderProcessStatesByTradeOrderId);
    }

    private List<OrderProcessStatesEntity> queryOrderProcessStates(BigInteger profileId, List<String> tradeIdList) {
        try {
            // 假设 refundMapper.queryOrderPorcessStatesByorderIdList 已经正确处理异常
            return refundMapper.queryOrderPorcessStatesByorderIdList(profileId, tradeIdList);
        } catch (Exception e) {
            // 根据实际情况处理异常，例如记录日志
            logger.error("查询订单处理状态失败", e);
            return Collections.emptyList();
        }
    }

    private void updateRefundListWithProcessStates(List<EshopRefundEntity> refundList, Map<String, OrderProcessStatesEntity> orderProcessStatesByTradeOrderId) {
        List<BigInteger> deliverVchcodes = new ArrayList<>();
        refundList.forEach(it -> {
            deliverVchcodes.addAll(it.getRefundApplyDetails().stream().filter(x -> x.getSourceVchcode() != null && !x.getSourceVchcode().equals(BigInteger.ZERO)).map(EshopRefundApplyDetail::getSourceVchcode).distinct().collect(Collectors.toList()));
        });

        Map<String, GetRefundDeliverBillResponse.Data> deliverMap = getRefundDeliverDataNew(deliverVchcodes);

        refundList.forEach(it -> {
            String tradeId = it.getTradeOrderId();
            OrderProcessStatesEntity orderProcessStatesEntity = orderProcessStatesByTradeOrderId.get(tradeId);
            if (orderProcessStatesEntity != null) {
                TradeProcessStateEnum processState = orderProcessStatesEntity.getProcessState();
                it.setTradeProcessState(processState);
            }

            GetRefundDeliverBillResponse.Data d = deliverMap.get(tradeId);
            if (d != null) {
                it.setBillSellerMemo(d.getSellerMemo());
                it.setBillRemark(d.getSysMemo());
                it.setBuyMessage(d.getBuyMessage());
            }
        });
    }

    private Map<String, GetRefundDeliverBillResponse.Data> getRefundDeliverDataNew(List<BigInteger> deliverVchcodes) {
        // 初始化一个空的返回Map，以应对可能的早返回情况
        Map<String, GetRefundDeliverBillResponse.Data> deliverMap = new HashMap<>();
        // 检查输入列表是否为空，避免无意义的API调用
        if (CollectionUtils.isEmpty(deliverVchcodes)) {
            return deliverMap;
        }
        GetRefundDeliverBillRequest request = new GetRefundDeliverBillRequest();
        request.setProfileId(CurrentUser.getProfileId());
        request.setVchcodes(deliverVchcodes);

        // 使用try-catch处理API调用可能抛出的异常
        try {
            GeneralResult<GetRefundDeliverBillResponse> response = deliverApi.getRefundDeliverBill(request);
            // 如果你的API响应类没有这样的方法，你需要根据实际情况进行调整
            GetRefundDeliverBillResponse responseData = response.getData();
            if (responseData == null || CollectionUtils.isEmpty(responseData.getData())) {
                return deliverMap;
            }
            // 使用Java 8的Streams API来优化Map的构建过程
            deliverMap = responseData.getData().stream().collect(Collectors.toMap(GetRefundDeliverBillResponse.Data::getTradeOrderId, Function.identity()));
        } catch (Exception e) {
            // 根据业务需求处理异常，例如记录日志、抛出自定义异常等
            // 这里仅打印异常堆栈，实际应用中可能需要更详细的处理
            logger.error("获取交易单信息异常: {}", e.getMessage(), e);
        }
        return deliverMap;
    }

    public BaseResponse updateRefundDuty(UpdateRefundDutyRequest request) {
        BaseResponse baseResponse = new BaseResponse();
        if (request.noNeedUpdate()) {
            baseResponse.setSuccess(true);
            baseResponse.setMessage("无需更新");
            return baseResponse;
        }
        request.setProfileId(CurrentUser.getProfileId());
        Integer i = afterSaleMapper.batchEditRefund(request);

        if (i > 0) {
            baseResponse.setSuccess(true);
            baseResponse.setMessage("批量修改售后单成功");
        } else {
            baseResponse.setSuccess(false);
            baseResponse.setMessage("批量售后单失败");
        }
        if (request.isRefundDutyIdsCheck() && request.getRefundDutyIds() != null && !request.getRefundDutyIdsList().isEmpty()) {
            List<String> dutyNames = refundMapper.getRefundDutyByIds(request);
            for (BigInteger vchcode : request.getVchcodes()) {
                SysLogUtil.add(SysLogUtil.buildRefundLog(vchcode, "【修改售后责任方】,修改为：" + String.join(",", dutyNames), RefundOrderOperateTypeEnum.MODIFY_DUTY));
            }
        }
        if (request.isRefundReasonCheck() && request.getReasonId() != null) {
            for (BigInteger vchcode : request.getVchcodes()) {
                SysLogUtil.add(SysLogUtil.buildRefundLog(vchcode, "【修改售后原因】,修改为：" + request.getReason(), RefundOrderOperateTypeEnum.MODIFY_DUTY));
            }
        }
        if (request.isRefundRemarkCheck() && request.getRemark() != null) {
            for (BigInteger vchcode : request.getVchcodes()) {
                SysLogUtil.add(SysLogUtil.buildRefundLog(vchcode, "【修改售后备注】,修改为：" + request.getRemark(), RefundOrderOperateTypeEnum.MODIFY_DUTY));
            }
        }
        if (request.isEdCommentCheck() && request.getMemo() != null) {
            for (BigInteger vchcode : request.getVchcodes()) {
                SysLogUtil.add(SysLogUtil.buildRefundLog(vchcode, "【修改附加说明】,修改为：" + request.getMemo(), RefundOrderOperateTypeEnum.MODIFY_DUTY));
            }
        }
        if (request.isRefundKtypeCheck() ) {
            for (BigInteger vchcode : request.getVchcodes()) {
                SysLogUtil.add(SysLogUtil.buildRefundLog(vchcode, "【修改仓库】,修改为：" + request.getKtypeName(), RefundOrderOperateTypeEnum.MODIFY_DUTY));
            }
        }
        return baseResponse;
    }



}

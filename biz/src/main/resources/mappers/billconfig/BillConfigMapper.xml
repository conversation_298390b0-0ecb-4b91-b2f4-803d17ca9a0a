<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wsgjp.ct.sale.biz.jarvis.mapper.BillSettingMapper">


    <select id="list" resultType="com.wsgjp.ct.sale.biz.jarvis.bill.config.PubBillSettings">
        SELECT setting_key, vchtype,
               business_type, order_sale_mode, setting_value,
               custom_type, control_type, stoped
        FROM pub_bill_settings
        WHERE profile_id=#{profileId}
        <foreach collection="param.settingKey" separator="," close=")" open="and setting_key in(" item="item">
            #{item}
        </foreach>
    </select>
</mapper>
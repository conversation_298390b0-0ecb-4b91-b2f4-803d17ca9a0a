<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.StockCalculateMapper">
    <select id="getStockPreRecordDetailList" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select pre.profile_id, pre.source_id as sourceId, 101 as sourceType,1 as recordType,
        pre.ptype_id,pre.sku_id, pre.id as sourceDetailId,
        pre.trade_order_id as tradeId,pre.ktype_id,pre.qty,
        pre.rule_id,pre.batchno,pre.batch_price,pre.produce_date,pre.expire_date
        from stock_pre_record_order_detail pre
        left join pl_eshop_sale_order o on pre.profile_id=o.profile_id and pre.eshop_id=o.otype_id and
        pre.trade_order_id=o.trade_order_id
        where pre.profile_id=#{profileId}
        and pre.trade_state = 2
        and pre.sku_id>0
        and pre.refund_state=0
        and o.id is null
        and o.deleted =0
        and pre.source_id in
        <foreach collection="sourceIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <sql id="sale_formToWhere">
        from pl_eshop_sale_order_detail pesd
        left join pl_eshop_sale_order peso on pesd.profile_id = peso.profile_id and peso.id = pesd.eshop_order_id
        left join pl_eshop_sale_order gifto on peso.profile_id = gifto.profile_id and peso.otype_id = gifto.otype_id
                 and peso.trade_order_id=gifto.platform_parent_order_id and peso.order_sale_type =6 and gifto.order_sale_type = 7
        left join pl_eshop_sale_order_detail giftod on giftod.profile_id = gifto.profile_id and giftod.eshop_order_id = gifto.id and giftod.platform_ptype_id=pesd.platform_ptype_id
        where pesd.profile_id=#{profileId}
        and pesd.process_state=0
        and peso.business_type!=204
        and peso.business_type!=205
        and peso.business_type!=208
        and peso.deleted=0
        and pesd.deleted=0
        and pesd.mapping_state=1
        and pesd.local_refund_state !=4
        and pesd.deliver_required=1
        and peso.self_delivery_mode!=3
        and pesd.sku_id > 0
        and pesd.qty != ifnull(giftod.qty,0)
        and pesd.platform_detail_trade_state in
        <foreach collection="saleOrderStatusList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </sql>

    <select id="getSaleOrderUse" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select peso.id as sourceId,1 as sourceType,1 as recordType,
        (pesd.qty - ifnull(giftod.qty,0)) as qty,
        pesd.profile_id,
        pesd.sku_id,
        pesd.ktype_id,
        true as saleOccupiedType,
        pesd.platform_detail_trade_state as tradeState,
        pesd.id as sourceDetailId,
        2 as saleOccupiedType,
        1 as sendOccupiedType
        <include refid="sale_formToWhere"/>
        and pesd.sku_id in
        <foreach collection="skuIdList" index="index" item="skuId" open="(" separator="," close=")">
            #{skuId}
        </foreach>
        order by pesd.create_time limit #{pageIndex},#{pageSize}
    </select>

    <select id="getSaleOrderUseCount" resultType="java.lang.Integer">
        select count(1)
        <include refid="sale_formToWhere"/>
        and pesd.sku_id in
        <foreach collection="skuIdList" index="index" item="skuId" open="(" separator="," close=")">
            #{skuId}
        </foreach>
    </select>

    <select id="getSaleOrderRecordList" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select pesd.profile_id, peso.id as sourceId, 1 as sourceType,1 as recordType,
        pesd.ptype_id,pesd.sku_id,pesd.id as sourceDetailId,
        peso.trade_order_id as tradeId,peso.ktype_id,(pesd.qty - ifnull(giftod.qty,0)) as qty,
        pesd.stock_sync_rule_id as ruleId,
        pesd.batchno,pesd.expire_date,pesd.produce_date,pesd.batch_price,1 as summaryType,
        pesd.platform_detail_trade_state as tradeState,
        false as draftState,
        9802 as  vchType,
        true as saleOccupiedType,
        1 as sendOccupiedType,peso.platform_parent_order_id as parentOrderId
        <include refid="sale_formToWhere"/>
        and pesd.eshop_order_id in
        <foreach collection="sourceIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <sql id="advance_where">
        where todp.profile_id=#{profileId}
        and todp.process_state=0
        and todp.deleted=0
        and toc.business_type!=204
        and toc.business_type!=205
        and toc.business_type!=208
        and todp.local_refund_state!=4
        and todp.deliver_required=1
        and todp.mapping_state=1
        and todp.is_sale_qty_need_to_occupy = 0
        and toc.self_delivery_mode!=3
        and todc.sku_id in
        <foreach collection="skuIdList" index="index" item="skuId" open="(" separator="," close=")">
            #{skuId}
        </foreach>
        and todp.platform_detail_trade_state in(0,1,2,6,7)
    </sql>

    <select id="getAdvanceOrderUse" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select todp.vchcode as sourceId,2 as sourceType,1 as recordType,
        todc.qty,
        todp.profile_id,
        todc.sku_id,
        toc.ktype_id,
        todc.detail_id as sourceDetailId,
        todp.platform_detail_trade_state as tradeState,
        2 as saleOccupiedType,
        1 as sendOccupiedType
        from td_orderbill_detail_platform todp
        left join td_orderbill_detail_core todc on todp.profile_id = todc.profile_id and todp.detail_id=todc.detail_id
        left join td_orderbill_platform top on todp.profile_id=top.profile_id and todp.vchcode=top.vchcode
        left join td_orderbill_core toc on todp.profile_id=toc.profile_id and todp.vchcode=toc.vchcode
        <include refid="advance_where"/>
        order by toc.bill_date limit #{pageIndex},#{pageSize}
    </select>

    <select id="getAdvanceOrderUseCount" resultType="java.lang.Integer">
        select count(1) from td_orderbill_detail_platform todp
        left join td_orderbill_detail_core todc on todp.profile_id = todc.profile_id and todp.detail_id=todc.detail_id
        left join td_orderbill_platform top on todp.profile_id=top.profile_id and todp.vchcode=top.vchcode
        left join td_orderbill_core toc on todp.profile_id=toc.profile_id and todp.vchcode=toc.vchcode
        <include refid="advance_where"/>
    </select>

    <select id="getAdvanceRecordList" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select toc.vchcode as sourceId,2 as sourceType,1 as recordType,toc.profile_id,
        todc.qty,toc.ktype_id,todc.sku_id,todc.ptype_id,todc.detail_id as sourceDetailId,
        todp.stock_sync_rule_id as ruleId,top.trade_id,
        tods.batch_price,tods.batchno,tods.produce_date,tods.expire_date,5 as summaryType,
        todp.platform_detail_trade_state as tradeState,
        false as draftState,
        toc.vchtype as  vchType,
        2 as saleOccupiedType,
        1 as sendOccupiedType
        from td_orderbill_detail_platform todp
        left join td_orderbill_detail_core todc on todp.profile_id = todc.profile_id and
        todp.detail_id=todc.detail_id
        left join td_orderbill_detail_serialno tods on todc.profile_id=tods.profile_id and tods.detail_id=todc.detail_id
        left join td_orderbill_platform top on todp.profile_id=top.profile_id and todp.vchcode=top.vchcode
        left join td_orderbill_core toc on todp.profile_id=toc.profile_id and todp.vchcode=toc.vchcode
        left join td_orderbill_timing tot on todp.profile_id=tot.profile_id and todp.vchcode=tot.vchcode
        where toc.profile_id = #{profileId}
        and todp.process_state=0
        and toc.deleted=0
        and toc.business_type!=204
        and toc.business_type!=205
        and toc.business_type!=208
        and todp.local_refund_state!=4
        and todp.deliver_required=1
        and todp.is_sale_qty_need_to_occupy =0
        and toc.self_delivery_mode!=3
        and todc.sku_id>0
        and (`toc`.order_sale_type != 3 OR (`toc`.order_sale_type=3 AND `toc`.platform_parent_order_id !=''))
        and todp.vchcode in
        <foreach collection="sourceIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="!profileIsOldVersion">
            and todp.platform_detail_trade_state in (0,1,2,6,7)
        </if>
        <if test="profileIsOldVersion">
            <if test="errBusinessOrderEnabled and nonPaidOrderEnabled">
                and todp.platform_detail_trade_state in (0,1,2,6,7)
            </if>
            <if test="!errBusinessOrderEnabled and nonPaidOrderEnabled">
                and todp.platform_detail_trade_state in (1,2,6,7)
            </if>
            <if test="errBusinessOrderEnabled and !nonPaidOrderEnabled">
                and todp.platform_detail_trade_state in (0,2,6,7)
            </if>
            <if test="!errBusinessOrderEnabled and !nonPaidOrderEnabled">
                and todp.platform_detail_trade_state in (2,6,7)
            </if>
        </if>
        <if test="cyclePurchaseOccupyEnabled">
            <if test="cyclePurchaseTimeType == 0 and cyclePurchaseOccupyStartTime!= null and cyclePurchaseOccupyEndTime!=null">
                AND `tot`.plan_send_time between #{cyclePurchaseOccupyStartTime} and #{cyclePurchaseOccupyEndTime}
            </if>
        </if>

    </select>

    <sql id="deliver_sql">
        where a.profile_id=#{profileId}
        and tc.deleted=0
        and b.refund_state!=2
        and tc.business_type != 204
        and tc.business_type!=205
        and tc.business_type!=208
        and td.stock_occupy_type=0
        and tc.post_state <![CDATA[<]]> 600
        <if test="skuIdList!=null and skuIdList.size()>0">
            and td.sku_id in
            <foreach collection="skuIdList" separator="," open="(" close=")" index="index" item="item">
                #{item}
            </foreach>
        </if>
        and b.trade_state in
        <foreach collection="statusList" index="index" item="state" open="(" separator="," close=")">
            #{state}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </sql>

    <select id="getDeliverBillUse" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select tc.profile_id,a.vchcode as sourceId,4 as sourceType,1 as recordType,
        (-1*(td.qty-td.inout_qty)) as qty,tc.bill_number as tradeId,
        td.sku_id,
        td.ptype_id,
        b.stock_sync_rule_id as ruleId,
        ifnull(rule.rule_type,0) as ruleType,
        td.ktype_id
        from td_bill_deliver a
        left join td_bill_core tc on a.profile_id=tc.profile_id and a.vchcode=tc.vchcode
        left join td_bill_detail_deliver b on a.profile_id=b.profile_id and a.vchcode=b.vchcode
        left join td_bill_detail_core td on b.profile_id=td.profile_id and b.vchcode=td.vchcode and
        b.detail_id=td.detail_id
        left join pl_eshop_stock_sync_rule rule on rule.profile_id=a.profile_id and rule.id=b.stock_sync_rule_id and rule.deleted=0
        left join acc_vchtype vc on tc.vchtype = vc.vchtype and vc.profile_id = tc.profile_id
        <include refid="deliver_sql"/>
        order by a.create_time limit #{pageIndex},#{pageSize}
    </select>

    <select id="getDeliverBillUseCount" resultType="java.lang.Integer">
        select count(1) from td_bill_deliver a
        left join td_bill_core tc on a.profile_id=tc.profile_id and a.vchcode=tc.vchcode
        left join td_bill_detail_deliver b on a.profile_id=b.profile_id and a.vchcode=b.vchcode
        left join td_bill_detail_core td on b.profile_id=td.profile_id and b.vchcode=td.vchcode and
        b.detail_id=td.detail_id
        left join pl_eshop_stock_sync_rule rule on rule.profile_id=a.profile_id and rule.id=b.stock_sync_rule_id and rule.deleted=0
        left join acc_vchtype vc on tc.vchtype = vc.vchtype and vc.profile_id = tc.profile_id
        <include refid="deliver_sql"/>
    </select>

    <sql id="bill_core">
        where tbc.profile_id = #{profileId}
        and tbc.post_state <![CDATA[<]]> 600 and tbc.post_state != -100
        and tbc.business_type!=204
        and tbc.business_type!=205
        and tbc.business_type!=208
        and tbdc.stock_occupy_type=0
        and tbc.deleted=0
        and tbdc.sku_id>0
        and tbc.process_type != 1
        <if test="skuIdList!=null and skuIdList.size()>0">
            and tbdc.sku_id in
            <foreach collection="skuIdList" separator="," open="(" close=")" index="index" item="item">
                #{item}
            </foreach>
        </if>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        <if test="profileIsOldVersion">
            and tbdc.inout_type in
            <foreach collection="allowInOutBills" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        and tbdc.ktype_point_type = 0
        and (-1*(tbdc.qty- tbdc.inout_qty))!=0
        and  (tdwt.vchcode is null or tdwt.self_delivery_mode != 4 or tdwt.full_link_status <![CDATA[<]]> 80 )
    </sql>

    <select id="getBillCoreUseCount" resultType="java.lang.Integer">
        select count(1)
        from td_bill_core tbc
        left join td_bill_deliver tdb on tdb.profile_id=tbc.profile_id and tbc.vchcode=tdb.vchcode
        left join td_bill_detail_core tbdc on tbc.vchcode = tbdc.vchcode and tbdc.profile_id = tbc.profile_id
        left join acc_vchtype vc on tbc.vchtype = vc.vchtype and vc.profile_id = tbc.profile_id
        left join td_bill_warehouse_task tdwt on tdwt.profile_id=tbc.profile_id and tdwt.vchcode = tbc.vchcode
        <include refid="bill_core"/>
    </select>

    <select id="getBillCoreUse" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select tbc.profile_id,tbc.vchcode as sourceId,
        tbdc.detail_id as sourceDetailId,
        (-1*(tbdc.qty- tbdc.inout_qty)) as qty,
        tbdc.sku_id,tbdc.ptype_id,tbdc.ktype_id,
        4 as sourceType,
        1 as recordType,
        true as saleOccupiedType,
        if(tbc.post_state>=500, 2, 1) as sendOccupiedType
        from td_bill_core tbc
        left join td_bill_deliver tdb on tdb.profile_id=tbc.profile_id and tbc.vchcode=tdb.vchcode
        left join td_bill_detail_core tbdc on tbc.vchcode = tbdc.vchcode and tbdc.profile_id = tbc.profile_id
        left join acc_vchtype vc on tbc.vchtype = vc.vchtype and vc.profile_id = tbc.profile_id
        left join td_bill_warehouse_task tdwt on tdwt.profile_id=tbc.profile_id and tdwt.vchcode = tbc.vchcode
        <include refid="bill_core"/>
        order by tbc.create_time
        limit #{pageIndex},#{pageSize}
    </select>

    <select id="getDeliverRecordList" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select tdb.profile_id, tdb.vchcode as sourceId,4 as sourceType,1 as recordType,tbdd.detail_id as sourceDetailId,
        tc.bill_number as trade_id,
        (-1*(tbdc.qty-tbdc.inout_qty)) as qty,
        tbdd.stock_sync_rule_id as ruleId,
        tbdc.ptype_id,tbdc.sku_id,tbdc.ktype_id,
        tbdc.cost_id,tbdb.produce_date,tbdb.expire_date,tbdb.batchno,tbdb.batch_price,3 as summaryType
        from td_bill_deliver tdb
        left join td_bill_core tc on tdb.profile_id = tc.profile_id and tdb.vchcode = tc.vchcode
        left join td_bill_detail_deliver tbdd on tdb.profile_id = tbdd.profile_id and tdb.vchcode = tbdd.vchcode
        left join td_bill_detail_core tbdc on tdb.profile_id = tbdc.profile_id and tdb.vchcode = tbdc.vchcode and
        tbdd.detail_id=tbdc.detail_id
        left join td_bill_detail_batch tbdb on tbdb.profile_id=tbdc.profile_id and tbdb.detail_id=tbdc.detail_id and
        tbdb.vchcode=tbdc.vchcode
        left join acc_vchtype vc on tc.vchtype = vc.vchtype and vc.profile_id = tc.profile_id
        where tdb.profile_id = #{profileId}
        and tc.deleted=0
        and tc.business_type!=204
        and tc.business_type!=205
        and tc.business_type!=208
        and tbdc.stock_occupy_type=0
        and tbdd.refund_state!=2
        and tbdc.sku_id>0
        and tc.post_state <![CDATA[<]]> 600
        and tdb.vchcode in
        <foreach collection="sourceIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="errBusinessOrderEnabled and nonPaidOrderEnabled">
            and tbdd.trade_state in (0,1,2,3,4,6,7)
        </if>
        <if test="!errBusinessOrderEnabled and nonPaidOrderEnabled">
            and tbdd.trade_state in (1,2,3,4,6,7)
        </if>
        <if test="errBusinessOrderEnabled and !nonPaidOrderEnabled">
            and tbdd.trade_state in (0,2,3,4,6,7)
        </if>
        <if test="!errBusinessOrderEnabled and !nonPaidOrderEnabled">
            and tbdd.trade_state in (2,3,4,6,7)
        </if>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </select>

    <select id="getBillCoreRecordList" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select tbc.profile_id, tbc.vchcode as sourceId,4 as sourceType,1 as recordType,tbdc.detail_id as sourceDetailId,
        tbc.bill_number as tradeId,
        (-1*(tbdc.qty-tbdc.inout_qty)) as qty,0 as ruleId,
        tbdc.ptype_id,tbdc.sku_id,tbdc.ktype_id,
        tbdc.cost_id,tbdb.produce_date,tbdb.expire_date,tbdb.batchno,tbdb.batch_price,
        case when tbc.vchtype = 1000 then 6
        when tbc.vchtype = 3000 then 4
        when tbc.vchtype = 2000 then 2
        else 5 end as summaryType,
        2 as tradeState,
        if(tbc.post_state=0, true, false) as draftState,
        tbc.vchtype as vchType,
        true as saleOccupiedType,
        if(tbc.post_state>=500, 2, 1) as sendOccupiedType
        from td_bill_core tbc
        left join td_bill_deliver tdb on tdb.profile_id=tbc.profile_id and tbc.vchcode=tdb.vchcode
        left join td_bill_detail_core tbdc on tbc.vchcode = tbdc.vchcode and tbdc.profile_id = tbc.profile_id
        left join td_bill_detail_batch tbdb on tbdb.profile_id=tbdc.profile_id and tbdb.detail_id=tbdc.detail_id and
        tbdb.vchcode=tbdc.vchcode
        left join td_bill_warehouse_task tdwt on tdwt.profile_id=tbc.profile_id and tdwt.vchcode = tbc.vchcode
        where tbc.profile_id = #{profileId}
        and tbc.post_state <![CDATA[<]]> 600 and tbc.post_state != -100
        and tbc.business_type!=204
        and tbc.business_type!=205
        and tbc.business_type!=208
        and tbdc.stock_occupy_type=0
        and tbc.deleted=0
        and tbdc.sku_id>0
        and tbc.process_type != 1
        and tbc.vchcode in
        <foreach collection="sourceIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and tbc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        <if test="profileIsOldVersion">
            and tbdc.inout_type in
            <foreach collection="allowInOutBills" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        and tbdc.ktype_point_type = 0
        and  (tdwt.vchcode is null or  tdwt.self_delivery_mode != 4  or tdwt.full_link_status <![CDATA[<]]> 80 )
    </select>

    <select id="getAccountingBackRecordList" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select tdb.profile_id, tdb.vchcode as sourceId,8 as sourceType,1 as recordType,
        tc.bill_number as trade_id,
        (-1*(tbdc.qty-tbdc.inout_qty)) as qty,
        tbdd.stock_sync_rule_id as ruleId,
        tbdc.ptype_id,tbdc.sku_id,tbdc.ktype_id,
        tbdc.cost_id,tbdb.produce_date,tbdb.expire_date,tbdb.batchno
        from td_bill_deliver tdb
        left join td_bill_core tc on tdb.profile_id = tc.profile_id and tdb.vchcode = tc.vchcode
        left join td_bill_deliver_state tbds on tdb.profile_id=tbds.profile_id and tbds.vchcode=tdb.vchcode
        left join td_bill_detail_deliver tbdd on tdb.profile_id = tbdd.profile_id and tdb.vchcode = tbdd.vchcode
        left join td_bill_detail_core tbdc on tdb.profile_id = tbdc.profile_id and tdb.vchcode = tbdc.vchcode and
        tbdd.detail_id = tbdc.detail_id
        left join td_bill_detail_batch tbdb on tbdb.profile_id=tdb.profile_id and tbdb.detail_id=tbdc.detail_id and
        tbdb.vchcode=tdb.vchcode
        left join acc_vchtype vc on tc.vchtype = vc.vchtype and vc.profile_id = tc.profile_id
        where tdb.profile_id = #{profileId}
        and tc.deleted=0
        and tbdc.sku_id>0
        and tbdd.stock_sync_rule_id>0
        and tc.business_type!=204
        and tc.business_type!=205
        and tc.business_type!=208
        and tbdd.refund_state!=2
        and tbdc.ktype_point_type=0
        and tbdc.stock_occupy_type=0
        and tc.post_state <![CDATA[<]]> 600
        and tdb.vchcode in
        <foreach collection="sourceIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="errBusinessOrderEnabled and nonPaidOrderEnabled">
            and tbdd.trade_state in (0,1,2,3,4,6,7)
        </if>
        <if test="!errBusinessOrderEnabled and nonPaidOrderEnabled">
            and tbdd.trade_state in (1,2,3,4,6,7)
        </if>
        <if test="errBusinessOrderEnabled and !nonPaidOrderEnabled">
            and tbdd.trade_state in (0,2,3,4,6,7)
        </if>
        <if test="!errBusinessOrderEnabled and !nonPaidOrderEnabled">
            and tbdd.trade_state in (2,3,4,6,7)
        </if>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </select>

    <select id="getOrderBillCoreUse" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select tbc.profile_id,tbc.vchcode as sourceId,
        <if test="!billOutSaleRecordEnabled and !billInSaleRecordEnabled">
            CASE WHEN tbdc.inout_type = 0 THEN (tbdc.qty + ifnull(todc.inout_qty,0)) ELSE -1*(tbdc.qty - ifnull(todc.inout_qty,0))  END
        </if>
        <if test="!billOutSaleRecordEnabled and billInSaleRecordEnabled">
            CASE WHEN tbdc.inout_type = 0 THEN (tbdc.qty + ifnull(todc.inout_qty,0)) ELSE -1*(tbdc.qty- tbdc.reference_qty)  END
        </if>
        <if test="billOutSaleRecordEnabled and !billInSaleRecordEnabled">
            CASE WHEN tbdc.inout_type = 0 THEN (tbdc.qty- tbdc.reference_qty) ELSE -1*(tbdc.qty - ifnull(todc.inout_qty,0)) END
        </if>
        <if test="billOutSaleRecordEnabled and billInSaleRecordEnabled">
            CASE WHEN tbdc.inout_type = 0 THEN (tbdc.qty- tbdc.reference_qty) ELSE -1*(tbdc.qty- tbdc.reference_qty) END
        </if>
        as qty,
        5 as sourceType,1 as recordType,
        tbdc.sku_id,tbdc.ptype_id,tbdc.ktype_id,
        0 as ruleId,0 as ruleType
        from td_orderbill_core tbc
        left join td_orderbill_detail_core tbdc on tbc.vchcode = tbdc.vchcode and tbdc.profile_id = tbc.profile_id
        left join td_orderbill_detail_complete todc on tbdc.profile_id=todc.profile_id and todc.detail_id=tbdc.detail_id and
        todc.vchcode=tbdc.vchcode
        where tbc.profile_id = #{profileId}
        and tbc.orderover_state in (0,3)
        and tbc.post_state = 500
        and tbc.vchtype  in
        <foreach collection="orderBillVchtypes" index="index" item="vchtype" open="(" separator="," close=")">
            #{vchtype}
        </foreach>
        and tbdc.sku_id in
        <foreach collection="skuIdList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="!billOutSaleRecordEnabled and !billInSaleRecordEnabled">
            and if(tbdc.inout_type = 0, tbdc.qty + ifnull(todc.inout_qty,0) > 0, tbdc.qty - ifnull(todc.inout_qty,0) > 0)
        </if>
        <if test="!billOutSaleRecordEnabled and billInSaleRecordEnabled">
            and if(tbdc.inout_type = 0, tbdc.qty + ifnull(todc.inout_qty,0) > 0, tbdc.qty - tbdc.reference_qty > 0)
        </if>
        <if test="billOutSaleRecordEnabled and !billInSaleRecordEnabled">
            and if(tbdc.inout_type = 0, tbdc.qty- tbdc.reference_qty > 0, tbdc.qty - ifnull(todc.inout_qty,0) > 0)
        </if>
        <if test="billOutSaleRecordEnabled and billInSaleRecordEnabled">
            and if(tbdc.inout_type = 0, tbdc.qty-tbdc.reference_qty > 0, tbdc.qty-tbdc.reference_qty > 0)
        </if>
        order by tbc.create_time
        limit #{pageIndex},#{pageSize}
    </select>


    <select id="getOrderBillSendRecordCoreUseCount" resultType="java.lang.Integer">
        select count(1)
        from td_orderbill_core tbc
        left join td_orderbill_detail_core tbdc on tbc.vchcode = tbdc.vchcode and tbdc.profile_id = tbc.profile_id
        where tbc.profile_id = #{profileId}
        and tbc.orderover_state in (0,3)
        and tbc.post_state = 500
        and tbc.vchtype = 9001
        and tbdc.sku_id in
        <foreach collection="skuIdList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getOrderBillSendRecordCoreUse" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select tbc.profile_id,tbc.vchcode as sourceId,
        if(tbdc.qty - tbdc.reference_qty  - sum(if(t.vchcode  is null,0,tbdc2.qty)) &lt; 0,0,tbdc.qty - tbdc.reference_qty - sum(if(t.vchcode  is null,0,tbdc2.qty))) as qty,
        5 as sourceType,2 as recordType,
        tbdc.sku_id,tbdc.ptype_id,tbdc.ktype_id,
        0 as ruleId,0 as ruleType
        from td_orderbill_core tbc
        left join td_orderbill_detail_core tbdc on tbc.vchcode = tbdc.vchcode and tbdc.profile_id = tbc.profile_id
        left JOIN td_bill_detail_core tbdc2
        on tbdc.detail_id = tbdc2.source_detail_id AND tbdc2.ptype_id = tbdc.ptype_id  and tbdc.profile_id = tbdc2.profile_id
        LEFT JOIN td_bill_core t ON t.vchcode = tbdc2.vchcode AND t.profile_id = tbdc2.profile_id and t.post_state &lt; 500
        where tbc.profile_id = #{profileId}
        and tbc.orderover_state in (0,3)
        and tbc.post_state = 500
        and tbc.vchtype = 9001
        and tbdc.sku_id in
        <foreach collection="skuIdList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        group  by tbdc.detail_id
        order by tbc.create_time
        limit #{pageIndex},#{pageSize}
    </select>

    <select id="getOrderBillCoreUseCount" resultType="java.lang.Integer">
        select count(1)
        from td_orderbill_core tbc
        left join td_orderbill_detail_core tbdc on tbc.vchcode = tbdc.vchcode and tbdc.profile_id = tbc.profile_id
        left join td_orderbill_detail_batch tdds on tdds.profile_id=tbdc.profile_id and tdds.detail_id=tbdc.detail_id
        left join td_orderbill_detail_complete todc on tbdc.profile_id=todc.profile_id and todc.detail_id=tbdc.detail_id and
        todc.vchcode=tbdc.vchcode
        where tbc.profile_id = #{profileId}
        and tbc.orderover_state in (0,3)
        and tbc.post_state = 500
        and tbc.vchtype  in
        <foreach collection="orderBillVchtypes" index="index" item="vchtype" open="(" separator="," close=")">
            #{vchtype}
        </foreach>
        and tbdc.sku_id in
        <foreach collection="skuIdList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="!billOutSaleRecordEnabled and !billInSaleRecordEnabled">
            and if(tbdc.inout_type = 0, tbdc.qty + ifnull(todc.inout_qty,0) > 0, tbdc.qty - ifnull(todc.inout_qty,0) > 0)
        </if>
        <if test="!billOutSaleRecordEnabled and billInSaleRecordEnabled">
            and if(tbdc.inout_type = 0, tbdc.qty + ifnull(todc.inout_qty,0) > 0, tbdc.qty - tbdc.reference_qty > 0)
        </if>
        <if test="billOutSaleRecordEnabled and !billInSaleRecordEnabled">
            and if(tbdc.inout_type = 0, tbdc.qty- tbdc.reference_qty > 0, tbdc.qty - ifnull(todc.inout_qty,0) > 0)
        </if>
        <if test="billOutSaleRecordEnabled and billInSaleRecordEnabled">
            and if(tbdc.inout_type = 0, tbdc.qty-tbdc.reference_qty > 0, tbdc.qty-tbdc.reference_qty > 0)
        </if>
    </select>

    <select id="getOrderBillRecordList"  resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select  obc.profile_id, obc.vchcode as sourceId,5 as sourceType,obdc.detail_id as sourceDetailId,
        obc.bill_number as tradeId,
        obdc.ptype_id,obdc.sku_id,obdc.ktype_id,
        tddb.batch_price,tddb.produce_date,tddb.batchno,tddb.expire_date,
        case when obc.vchtype = 9000 then 6
        when obc.vchtype = 9001 then 4
        when obc.vchtype = 9005 then 2
        else 5 end as summaryType,
        obc.vchtype as vchType,
        if(obdc.inout_type = 0, 1, 3) as recordType,
        obdc.qty as qty,
        obdc.reference_qty as referenceQty,
        cp.inout_qty as inoutQty,
        2 as tradeState,
        true as saleOccupiedType
        from td_orderbill_core obc
         left join td_orderbill_detail_core obdc on obc.vchcode = obdc.vchcode and obdc.profile_id = obc.profile_id
         left join td_orderbill_detail_batch tddb on tddb.profile_id=obdc.profile_id and tddb.detail_id=obdc.detail_id
         left join td_orderbill_detail_complete cp on obdc.profile_id=cp.profile_id and cp.detail_id=obdc.detail_id
        where obc.profile_id = #{profileId}
          and obc.orderover_state in (0,3)
          and obc.post_state = 500
          and obc.vchcode in
        <foreach collection="sourceIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getOrderBillUseList"  resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select  obc.profile_id, obc.vchcode as sourceId,5 as sourceType,obdc.detail_id as sourceDetailId,
        obc.bill_number as tradeId,
        obdc.ptype_id,obdc.sku_id,obdc.ktype_id,
        obc.vchtype as vchType,
        if(obdc.inout_type = 0, 1, 3) as recordType,
        obdc.qty as qty,
        obdc.reference_qty as referenceQty,
        cp.inout_qty as inoutQty,
        2 as tradeState,
        true as saleOccupiedType
        from td_orderbill_core obc
        left join td_orderbill_detail_core obdc on obc.vchcode = obdc.vchcode and obdc.profile_id = obc.profile_id
        left join td_orderbill_detail_complete cp on obdc.profile_id=cp.profile_id and cp.detail_id=obdc.detail_id
        where obc.profile_id = #{profileId}
        and obc.orderover_state in (0,3)
        and obc.post_state = 500
        and obdc.sku_id in
        <foreach collection="skuIdList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getOrderBillCordRecordList" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select tbc.profile_id, tbc.vchcode as sourceId,5 as sourceType,1 as recordType,tbdc.detail_id as sourceDetailId,
        tbc.bill_number as tradeId,
        <if test="!billOutSaleRecordEnabled and !billInSaleRecordEnabled">
            CASE WHEN tbdc.inout_type = 0 THEN (tbdc.qty + ifnull(todc.inout_qty,0)) ELSE -1*(tbdc.qty - ifnull(todc.inout_qty,0))  END
        </if>
        <if test="!billOutSaleRecordEnabled and billInSaleRecordEnabled">
            CASE WHEN tbdc.inout_type = 0 THEN (tbdc.qty + ifnull(todc.inout_qty,0)) ELSE -1*(tbdc.qty- tbdc.reference_qty)  END
        </if>
        <if test="billOutSaleRecordEnabled and !billInSaleRecordEnabled">
            CASE WHEN tbdc.inout_type = 0 THEN (tbdc.qty- tbdc.reference_qty) ELSE -1*(tbdc.qty - ifnull(todc.inout_qty,0)) END
        </if>
        <if test="billOutSaleRecordEnabled and billInSaleRecordEnabled">
            CASE WHEN tbdc.inout_type = 0 THEN (tbdc.qty- tbdc.reference_qty) ELSE -1*(tbdc.qty- tbdc.reference_qty) END
        </if>
        as qty,
        tbdc.ptype_id,tbdc.sku_id,tbdc.ktype_id,
        tdds.batch_price,tdds.produce_date,tdds.batchno,tdds.expire_date,
        case when tbc.vchtype = 9000 then 6
        when tbc.vchtype = 9001 then 4
        when tbc.vchtype = 9005 then 2
        else 5 end as summaryType
        from td_orderbill_core tbc
        left join td_orderbill_detail_core tbdc on tbc.vchcode = tbdc.vchcode and tbdc.profile_id = tbc.profile_id
        left join td_orderbill_detail_batch tdds on tdds.profile_id=tbdc.profile_id and tdds.detail_id=tbdc.detail_id
        left join td_orderbill_detail_complete todc on tbdc.profile_id=todc.profile_id and todc.detail_id=tbdc.detail_id and
        todc.vchcode=tbdc.vchcode
        where tbc.profile_id = #{profileId}
        and tbc.orderover_state in (0,3)
        and tbc.post_state = 500
        and tbc.vchtype  in
        <foreach collection="orderBillVchtypes" index="index" item="vchtype" open="(" separator="," close=")">
            #{vchtype}
        </foreach>
        and tbc.vchcode in
        <foreach collection="sourceIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="!billOutSaleRecordEnabled and !billInSaleRecordEnabled">
            and if(tbdc.inout_type = 0, tbdc.qty + ifnull(todc.inout_qty,0) > 0, tbdc.qty - ifnull(todc.inout_qty,0) > 0)
        </if>
        <if test="!billOutSaleRecordEnabled and billInSaleRecordEnabled">
            and if(tbdc.inout_type = 0, tbdc.qty + ifnull(todc.inout_qty,0) > 0, tbdc.qty - tbdc.reference_qty > 0)
        </if>
        <if test="billOutSaleRecordEnabled and !billInSaleRecordEnabled">
            and if(tbdc.inout_type = 0, tbdc.qty- tbdc.reference_qty > 0, tbdc.qty - ifnull(todc.inout_qty,0) > 0)
        </if>
        <if test="billOutSaleRecordEnabled and billInSaleRecordEnabled">
            and if(tbdc.inout_type = 0, tbdc.qty-tbdc.reference_qty > 0, tbdc.qty-tbdc.reference_qty > 0)
        </if>
    </select>

    <select id="getOrderBillCordSendRecordList" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select tbc.profile_id, tbc.vchcode as sourceId,5 as sourceType,2 as recordType,tbdc.detail_id as sourceDetailId,
        tbc.bill_number as tradeId,
        if(tbdc.qty - tbdc.reference_qty  - sum(if(t.vchcode  is null,0,tbdc2.qty)) &lt; 0,0,tbdc.qty - tbdc.reference_qty - sum(if(t.vchcode  is null,0,tbdc2.qty))) AS qty,
        tbdc.ptype_id,tbdc.sku_id,tbdc.ktype_id,
        tdds.batch_price,tdds.produce_date,tdds.batchno,tdds.expire_date
        from td_orderbill_core tbc
        left join td_orderbill_detail_core tbdc on tbc.vchcode = tbdc.vchcode and tbdc.profile_id = tbc.profile_id
        left join td_orderbill_detail_batch tdds on tdds.profile_id=tbdc.profile_id and tdds.detail_id=tbdc.detail_id
        left JOIN td_bill_detail_core tbdc2
        on tbdc.detail_id = tbdc2.source_detail_id AND tbdc2.ptype_id = tbdc.ptype_id  and tbdc.profile_id = tbdc2.profile_id
        LEFT JOIN td_bill_core t ON t.vchcode = tbdc2.vchcode AND t.profile_id = tbdc2.profile_id and t.post_state &lt; 500
        where tbc.profile_id = #{profileId}
        and tbc.orderover_state in (0,3)
        and tbc.post_state = 500
        and tbc.vchtype = 9001
        and tbc.vchcode in
        <foreach collection="sourceIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        group  by tbdc.detail_id
    </select>

    <select id="getNeedFixSkuIdList" resultType="java.math.BigInteger">
        select s.id as skuId
        from base_ptype_sku s
                 left join base_ptype bp on s.ptype_id = bp.id and s.profile_id = bp.profile_id
        where s.profile_id = #{profileId}
          and s.stoped = 0
          and bp.stoped = 0
          and bp.deleted = 0
        order by s.id
        limit #{pageIndex},#{pageSize}
    </select>

    <select id="getNeedFixSkuIdCount" resultType="java.lang.Integer">
        select count(1)
        from base_ptype_sku s
                 left join base_ptype bp on s.ptype_id = bp.id and s.profile_id = bp.profile_id
        where s.profile_id = #{profileId}
          and s.stoped = 0
          and bp.stoped = 0
          and bp.deleted = 0
    </select>

    <select id="getNeedFixSkuIdListCount" resultType="java.lang.Integer">
        select count(1)
        from base_ptype_sku s
                 left join base_ptype bp on s.ptype_id = bp.id and s.profile_id = bp.profile_id
        where s.profile_id = #{profileId}
          and s.stoped = 0
          and bp.stoped = 0
          and bp.deleted = 0
    </select>

    <select id="getNeedFixSkuIdListByPage" resultType="java.math.BigInteger">
        select s.id as skuId
        from base_ptype_sku s
                 left join base_ptype bp on s.ptype_id = bp.id and s.profile_id = bp.profile_id
        where s.profile_id = #{profileId}
          and s.stoped = 0
          and bp.stoped = 0
          and bp.deleted = 0
        limit #{pageStart}, #{pageSize}
    </select>

    <!--索引-->
    <select id="getSaleQtyChangeIndex" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.CalculateIndex">
        select id, change_type, change_index, update_time
        from stock_record_index
        where id in (1001, 1002, 1003,1004,1005,1006)
    </select>

    <insert id="initSaleQtyChangeIndex">
        insert into stock_record_index(id, profile_id, change_type, change_index)
        values (1001, 0, 1001, 0),
               (1002, 0, 1002, 0),
               (1003, 0, 1003, 0),
               (1004, 0, 1004, 0),
               (1005, 0, 1005, 0),
               (1006, 0, 1006, 0)
    </insert>
    <insert id="initSaleQtyChangeRecalcIndex">
        insert into stock_record_index(id, profile_id, change_type, change_index)
        values (1004, 0, 1004, 0),
               (1005, 0, 1005, 0),
               (1006, 0, 1006, 0)
    </insert>


    <insert id="insertNewChangeIndex">
        insert into stock_record_index(id, profile_id, change_type, change_index)
        values (#{id}, 0, #{changeType}, #{changeIndex})
    </insert>

    <insert id="insertReCalculateLog">
        insert into stock_qty_recalculate_log
        (id, type_id, profile_id, ktype_id, sku_id, record_qty, calculate_qty,source_id,source_type)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},#{item.typeId},#{item.profileId},#{item.ktypeId},#{item.skuId},#{item.recordQty},#{item.calculateQty},#{item.sourceId},#{item.sourceType})
        </foreach>
    </insert>

    <insert id="saveSysData">
        replace into sys_data(id, profile_id, sub_name, sub_value, `description`, last_modify_etype_id)
        values (#{id}, #{profileId}, #{subName}, #{subValue}, #{description}, 0)
    </insert>

    <update id="updateChangeIndex">
        update stock_record_index
        set change_index=#{lastChangeIndex}
        where id = #{id}
          and change_index = #{changeIndex}
    </update>

    <update id="updateQueueFinish">
        update
        <if test="!recalc">
            stock_change_order_queue
        </if>
        <if test="recalc">
            stock_change_order_queue_recalc
        </if>
        set finished=#{finished}
        where id in
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!--<update id="repairSaleRecord">
        update stock_record_qty r
            left join(select profile_id, sku_id, ktype_id,
                             sum(if(stock_occupy_type in(20,21,22,23) and record_type=1, qty, 0)) as qty,
                             sum(if(stock_occupy_type in(20,21,22,23) and record_type=3, qty, 0)) as transQty,
                             sum(if(stock_occupy_type in(12,22), qty, 0)) as sendQty,
                             sum(if(stock_occupy_type in(13,23), qty, 0)) as sendLackQty
                      from stock_record_order_detail
                      where profile_id = #{profileId}
                      group by profile_id, sku_id, ktype_id) tmp on tmp.profile_id = r.profile_id and
                                                                    tmp.ktype_id = r.ktype_id and tmp.sku_id = r.sku_id
            set r.sale_qty=ifnull(tmp.qty,0), r.trans_qty=ifnull(tmp.transQty,0), r.send_qty=ifnull(tmp.sendQty,0), r.send_qty_lack=ifnull(tmp.sendLackQty,0)
        where r.profile_id = #{profileId}
          and (r.sale_qty != ifnull(tmp.qty, 0) or r.trans_qty != ifnull(tmp.transQty,0));
    </update>-->

    <update id="repairSaleRecord">
        update stock_record_qty_sale r
            left join (select sum(qty) as qty, profile_id, sku_id, ktype_id
            from stock_record_order_detail
            where profile_id = #{profileId}
            and record_type = 1
            group by profile_id, sku_id, ktype_id) tmp on tmp.profile_id = r.profile_id and
            tmp.ktype_id = r.ktype_id and tmp.sku_id = r.sku_id
            set r.qty=ifnull(tmp.qty, 0)
        where r.profile_id = #{profileId}
          and r.qty != ifnull(tmp.qty, 0)
    </update>


    <update id="repairBatchSendRecord">
        update stock_record_qty_send_batch r
            left join (select sum(qty) as qty, profile_id, hash_mark, record_type
                       from stock_record_order_detail
                       where profile_id = #{profileId}
                         and record_type = 2
                         and hash_mark != ''
                       group by profile_id, hash_mark) tmp on tmp.profile_id = r.profile_id and tmp.hash_mark = r.hash_mark
        set r.qty=ifnull(tmp.qty, 0)
        where r.profile_id = #{profileId}
          and r.qty != ifnull(tmp.qty, 0)
    </update>

    <insert id="resetBatchSendRecord">
        delete from stock_record_qty_send_batch where profile_id=#{profileId};
        insert into stock_record_qty_send_batch (id, profile_id, ptype_id, sku_id, cost_id, ktype_id, qty, batchno, produce_date, expire_date, hash_mark, batch_price)
            select id,profile_id,ptype_id,sku_id,cost_id,ktype_id, sum(qty), batchno, produce_date, expire_date, hash_mark, batch_price
             from stock_record_order_detail where profile_id=#{profileId} and record_type=2 and ifnull(hash_mark,'')!=''
             group by profile_id,hash_mark;
    </insert>

    <update id="repairBatchSaleRecord">
        update stock_record_qty_sale_batch r
            left join (select sum(qty) as qty, profile_id, hash_mark, record_type
                       from stock_record_order_detail
                       where profile_id = #{profileId}
                         and record_type = 1
                         and hash_mark != ''
                       group by profile_id, hash_mark) tmp on tmp.profile_id = r.profile_id and tmp.hash_mark = r.hash_mark
        set r.qty=ifnull(tmp.qty, 0)
        where r.profile_id = #{profileId}
          and r.qty != ifnull(tmp.qty, 0)
    </update>

    <update id="updateQueueUnique">
        update
        <if test="!recalc">
            stock_change_order_queue
        </if>
        <if test="recalc">
            stock_change_order_queue_recalc
        </if>
            stock_change_order_queue
        set queue_hash=null
        where id in
        <foreach collection="list" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updateQueueUniqueByLimit">
        update
        <if test="!recalc">
            stock_change_order_queue
        </if>
        <if test="recalc">
            stock_change_order_queue_recalc
        </if>
        set queue_hash=null
        where id > #{pageIndex} limit #{pageLimit}
    </update>

    <!--    <insert id="resetBatchSaleRecord">
            delete from stock_record_qty_batch where profile_id=#{profileId};
            insert into stock_record_qty_batch (id, profile_id, ptype_id, sku_id, ktype_id, batchno, produce_date, expire_date, hash_mark, batch_price,sale_qty,trans_qty,send_qty,send_qty_lack)
            select id,profile_id,ptype_id,sku_id,ktype_id,  batchno, produce_date, expire_date, hash_mark, batch_price,
                   sum(if(stock_occupy_type in(20,21,22,23) and record_type=1, qty, 0)) as qty,
                   sum(if(stock_occupy_type in(20,21,22,23) and record_type=3, qty, 0)) as transQty,
                   sum(if(stock_occupy_type in(12,22), qty, 0)) as sendQty,
                   sum(if(stock_occupy_type in(13,23), qty, 0)) as sendLackQty
            from stock_record_order_detail where profile_id=#{profileId}  and ifnull(hash_mark,'')!=''
            group by profile_id,hash_mark;
        </insert>-->

    <insert id="resetBatchSaleRecord">
        delete from stock_record_qty_sale_batch where profile_id=#{profileId};
        insert into stock_record_qty_sale_batch (id, profile_id, ptype_id, sku_id, ktype_id, qty, batchno, produce_date, expire_date, hash_mark, batch_price)
        select id,profile_id,ptype_id,sku_id,ktype_id, sum(qty), batchno, produce_date, expire_date, hash_mark, batch_price
        from stock_record_order_detail where profile_id=#{profileId} and record_type=1 and ifnull(hash_mark,'')!=''
        group by profile_id,hash_mark;
    </insert>
    <!--操作占用表-->
    <select id="getQtyRecordDetailList" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select profile_id, source_id, source_detail_id, source_type,record_type, sku_id, ktype_id, qty, hash_mark, rule_hash_mark, stock_occupy_type
        from stock_record_order_detail
        where profile_id=#{profileId}
        and sku_id in
        <foreach open="(" close=")" collection="skuIdList" separator="," index="index" item="item">
            #{item}
        </foreach>
        limit #{pageIndex},#{pageSize}
    </select>

    <select id="getQtyRecordDetailCount" resultType="java.lang.Integer">
        select count(1) from stock_record_order_detail where profile_id=#{profileId}
        and sku_id in
        <foreach open="(" close=")" collection="skuIdList" separator="," index="index" item="item">
            #{item}
        </foreach>
    </select>

<!--    <select id="getSaleRecordForCalculate" resultType="java.math.BigInteger">
        select distinct sku_id
        from stock_record_qty
        where profile_id = #{profileId}
          and update_time > date_sub(now(), interval 10 day)
    </select>-->

    <select id="getSaleRecordForCalculate" resultType="java.math.BigInteger">
        select distinct sku_id
        from stock_record_qty_sale
        where profile_id = #{profileId}
          and update_time > date_sub(now(), interval 10 day)
    </select>

    <select id="getSysData" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.SysData">
        select id, profile_id, sub_name, sub_value
        from sys_data
        where profile_id = #{profileId}
          and sub_name = #{subName}
        limit 1
    </select>

    <select id="getBillInOutRecordList" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select tbid.profile_id,
        tbid.inout_id        as sourceId,
        9                    as sourceType,
        2                    as recordType,
        tbid.inout_detail_id as
        sourceDetailId,
        tbir.inout_number    as trade_id,
        (-1 * tbid.qty)      as qty,
        0                    as ruleId,
        tbid.ptype_id,
        tbid.sku_id,
        tbid.ktype_id,
        tbdc.cost_id,
        tbdb.produce_date,
        tbdb.expire_date,
        tbdb.batchno,
        tbdc.batch_price,
        tbc.vchcode          as bill_vchcode,
        tbid.detail_id as bill_detail_id
        from td_bill_inout_detail tbid
        left join td_bill_detail_deliver tbdd on tbid.vchcode = tbdd.vchcode and tbid.detail_id = tbdd.detail_id and tbdd.profile_id = tbid.profile_id
        left join td_bill_inout_record tbir on tbid.profile_id = tbir.profile_id and tbir.inout_id = tbid.inout_id
        left join td_bill_warehouse_task tdwt on tdwt.profile_id=tbid.profile_id and tdwt.warehouse_task_id =
        tbir.inout_id
        left join td_bill_detail_core tbdc on tbdc.detail_id = tbid.detail_id and tbdc.profile_id = tbid.profile_id
        left join td_bill_core tbc on tbc.profile_id = tbdc.profile_id and tbc.vchcode = tbdc.vchcode
        left join td_bill_detail_batch tbdb
        on tbdb.profile_id = tbid.profile_id and tbdb.inout_detail_id = tbid.inout_detail_id
        where tbid.profile_id = #{profileId}
        and tbir.inout_state != 100
        and tbir.record_type not in (3,13)
        and tbid.ktype_point_type = 0
        and tbc.business_type != 205
        and tbc.business_type != 204
        and tbc.deleted=0
        and tbc.business_type != 208
        and (tdwt.vchcode is null or tdwt.self_delivery_mode != 4  or tdwt.full_link_status <![CDATA[<]]> 80 )
        and (( tbdd.trade_state !=5 and tbdd.refund_state !=2) or tbdd.detail_id is null)
        and tbid.inout_id in
        <foreach collection="sourceIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and tbc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        <if test="!newSendQtyCalcEnabled">
            and (tbc.deliver_process_type != 3 OR tbir.inout_state != 60)
        </if>
    </select>

    <select id="getBillInOutUse" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select tbir.inout_id as sourceId,9 as sourceType,2 as recordType,
        (-1*tbid.qty) as qty,
        tbir.inout_number as trade_id,
        tbir.profile_id,
        tbid.sku_id,
        tbid.ktype_id,
        tbdc.cost_id,
        tbdb.produce_date,tbdb.expire_date,tbdb.batchno,tbdc.batch_price
        from td_bill_inout_detail tbid
        left join td_bill_inout_record tbir on tbid.profile_id=tbir.profile_id and tbir.inout_id=tbid.inout_id
        left join td_bill_detail_core tbdc on tbdc.detail_id=tbid.detail_id and tbdc.profile_id=tbid.profile_id
        left join td_bill_core tbc on tbc.profile_id = tbdc.profile_id and tbc.vchcode = tbdc.vchcode
        left join td_bill_detail_batch tbdb on tbdb.profile_id=tbid.profile_id and
        tbdb.inout_detail_id=tbid.inout_detail_id
        left join td_bill_warehouse_task tdwt on tdwt.profile_id=tbid.profile_id and tdwt.warehouse_task_id =
        tbir.inout_id
        left join acc_vchtype vc on tbc.vchtype = vc.vchtype and vc.profile_id = tbc.profile_id
        left join td_bill_detail_deliver tbdd on tbid.vchcode = tbdd.vchcode and tbid.detail_id = tbdd.detail_id and tbdd.profile_id = tbid.profile_id
        where tbid.profile_id = #{profileId}
        and tbir.inout_state != 100
        and tbir.record_type not in (3,13)
        and tbc.business_type!=204
        and tbc.business_type!=205
        and tbc.business_type!=208
        and tbid.ktype_point_type=0
        and (tdwt.vchcode is null or tdwt.self_delivery_mode != 4  or tdwt.full_link_status <![CDATA[<]]> 80 )
        and (( tbdd.trade_state !=5 and tbdd.refund_state !=2) or tbdd.detail_id is null)
        and tbid.sku_id in
        <foreach collection="skuIdList" index="index" item="skuId" open="(" separator="," close=")">
            #{skuId}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        <if test="!newSendQtyCalcEnabled">
            and (tbc.deliver_process_type != 3 OR tbir.inout_state != 60)
        </if>
        order by tbir.create_time limit #{pageIndex},#{pageSize}
    </select>

    <select id="getBillInOutUseCount" resultType="java.lang.Integer">
        select count(1)
        from td_bill_inout_detail tbid
        left join td_bill_inout_record tbir on tbid.profile_id = tbir.profile_id and tbir.inout_id = tbid.inout_id
        left join td_bill_warehouse_task tdwt on tdwt.profile_id=tbid.profile_id and tdwt.warehouse_task_id =
        tbir.inout_id
        left join td_bill_detail_core tbdc on tbdc.detail_id=tbid.detail_id and tbdc.profile_id=tbid.profile_id
        left join td_bill_core tbc on tbc.profile_id = tbdc.profile_id and tbc.vchcode = tbdc.vchcode
        left join acc_vchtype vc on tbc.vchtype = vc.vchtype and vc.profile_id = tbc.profile_id
        left join td_bill_detail_deliver tbdd on tbid.vchcode = tbdd.vchcode and tbid.detail_id = tbdd.detail_id and tbdd.profile_id = tbid.profile_id
        where tbid.profile_id = #{profileId}
        and tbir.inout_state != 100
        and tbir.record_type not in (3,13)
        and tbc.business_type!=204
        and tbc.business_type!=205
        and tbc.business_type!=208
        and tbid.ktype_point_type=0
        and (tdwt.vchcode is null or tdwt.self_delivery_mode != 4  or tdwt.full_link_status <![CDATA[<]]> 80 )
        and (( tbdd.trade_state !=5 and tbdd.refund_state !=2) or tbdd.detail_id is null)
        and tbid.sku_id in
        <foreach collection="skuIdList" index="index" item="skuId" open="(" separator="," close=")">
            #{skuId}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        <if test="!newSendQtyCalcEnabled">
            and (tbc.deliver_process_type != 3 OR tbir.inout_state != 60)
        </if>
    </select>

    <select id="getWmsInRecordList" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        SELECT a.profile_id,a.vchcode AS sourceId,10 AS sourceType,2 AS recordType,
        b.erp_order_code AS trade_id,
        (-1 * (a.qty-a.reality_qty)) AS qty ,0 AS ruleId,
        a.ptype_id,a.sku_id,b.ktype_id,
        tbdc.cost_id,a.produce_date,a.expire_date,a.batchno,tbdc.batch_price
        FROM `td_wms_bill_detail_in` a
        JOIN `td_wms_bill_core` b ON a.profile_id=b.profile_id AND a.vchcode=b.vchcode
        LEFT JOIN td_bill_core tbc ON tbc.profile_id=a.profile_id AND tbc.vchcode=a.vchcode
        LEFT JOIN td_bill_detail_core tbdc ON tbdc.detail_id=a.detail_id AND tbdc.profile_id=a.profile_id
        left join acc_vchtype vc on tbc.vchtype = vc.vchtype and vc.profile_id = tbc.profile_id
        WHERE a.profile_id=#{profileId}
        AND b.wms_status!=2
        AND b.wms_status!=3
        AND tbc.business_type!=205
        AND tbc.business_type!=204
        AND tbc.business_type!=208
        AND a.vchcode in
        <foreach collection="sourceIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </select>


    <select id="queryMaxQueueId" resultType="java.math.BigInteger">
        select max(id)
        from stock_change_order_queue
    </select>

    <select id="queryMinQueueId" resultType="java.math.BigInteger">
        select min(id)
        from stock_change_order_queue
        where create_time >= #{startDate}
    </select>

    <select id="getWmsOutRecordList" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.QueryRecordDetailParameter">
        SELECT a.profile_id,a.vchcode AS sourceId,11 AS sourceType,2 AS recordType, b.erp_order_code AS
        trade_id,a.detail_id as sourceDetailId,
        (a.qty-a.reality_qty) AS qty ,0 AS ruleId,
        a.ptype_id,a.sku_id,b.ktype_id,
        tbdc.cost_id,a.produce_date,a.expire_date,a.batchno,tbdc.batch_price
        FROM `td_wms_bill_detail_out` a
        JOIN `td_wms_bill_core` b ON a.profile_id=b.profile_id AND a.vchcode=b.vchcode
        LEFT JOIN td_bill_core tbc ON tbc.profile_id=a.profile_id AND tbc.vchcode=a.vchcode
        LEFT JOIN td_bill_detail_core tbdc ON tbdc.detail_id=a.detail_id AND tbdc.profile_id=a.profile_id
        left join acc_vchtype vc on tbc.vchtype = vc.vchtype and vc.profile_id = tbc.profile_id
        WHERE a.profile_id=#{profileId}
        AND b.wms_status!=2
        AND b.wms_status!=3
        AND tbc.business_type!=205
        AND tbc.business_type!=204
        AND tbc.business_type!=208
        AND a.vchcode IN
        <foreach collection="sourceIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </select>

    <select id="getWmsInUse" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.stock.QueryStockInUseParameter">
        SELECT a.profile_id,a.vchcode AS sourceId,10 AS sourceType,2 AS recordType,
        b.erp_order_code AS trade_id,
        (-1 * (a.qty-a.reality_qty)) AS qty ,0 AS ruleId,
        a.ptype_id,a.sku_id,b.ktype_id,
        tbdc.cost_id,a.produce_date,a.expire_date,a.batchno,tbdc.batch_price
        FROM `td_wms_bill_detail_in` a
        JOIN `td_wms_bill_core` b ON a.profile_id=b.profile_id AND a.vchcode=b.vchcode
        LEFT JOIN td_bill_core tbc ON tbc.profile_id=a.profile_id AND tbc.vchcode=a.vchcode
        LEFT JOIN td_bill_detail_core tbdc ON tbdc.detail_id=a.detail_id AND tbdc.profile_id=a.profile_id
        left join acc_vchtype vc on tbc.vchtype = vc.vchtype and vc.profile_id = tbc.profile_id
        WHERE a.profile_id=#{profileId}
        AND b.wms_status!=2
        AND b.wms_status!=3
        AND tbc.business_type!=205
        AND tbc.business_type!=204
        AND tbc.business_type!=208
        AND a.sku_id in
        <foreach collection="skuIdList" index="index" item="skuId" open="(" separator="," close=")">
            #{skuId}
        </foreach>
        left join acc_vchtype vc on tbc.vchtype = vc.vchtype and vc.profile_id = tbc.profile_id
        order by b.create_time limit #{pageIndex},#{pageSize}
    </select>

    <select id="getWmsInUseCount" resultType="java.lang.Integer"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.stock.QueryStockInUseParameter">
        SELECT count(1)
        FROM `td_wms_bill_detail_in` a
        JOIN `td_wms_bill_core` b ON a.profile_id=b.profile_id AND a.vchcode=b.vchcode
        LEFT JOIN td_bill_core tbc ON tbc.profile_id=a.profile_id AND tbc.vchcode=a.vchcode
        left join acc_vchtype vc on tbc.vchtype = vc.vchtype and vc.profile_id = tbc.profile_id
        WHERE a.profile_id=#{profileId}
        AND b.wms_status!=2
        AND b.wms_status!=3
        AND tbc.business_type!=205
        AND tbc.business_type!=204
        AND tbc.business_type!=208
        AND a.sku_id in
        <foreach collection="skuIdList" index="index" item="skuId" open="(" separator="," close=")">
            #{skuId}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </select>

    <select id="getWmsOutUse" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.stock.QueryStockInUseParameter">
        SELECT a.profile_id,a.vchcode AS sourceId,11 AS sourceType,2 AS recordType, b.erp_order_code AS trade_id,
        (a.qty-a.reality_qty) AS qty ,0 AS ruleId,
        a.ptype_id,a.sku_id,b.ktype_id,
        tbdc.cost_id,a.produce_date,a.expire_date,a.batchno,tbdc.batch_price
        FROM `td_wms_bill_detail_out` a
        JOIN `td_wms_bill_core` b ON a.profile_id=b.profile_id AND a.vchcode=b.vchcode
        LEFT JOIN td_bill_core tbc ON tbc.profile_id=a.profile_id AND tbc.vchcode=a.vchcode
        LEFT JOIN td_bill_detail_core tbdc ON tbdc.detail_id=a.detail_id AND tbdc.profile_id=a.profile_id
        left join acc_vchtype vc on tbc.vchtype = vc.vchtype and vc.profile_id = tbc.profile_id
        WHERE a.profile_id=#{profileId}
        AND b.wms_status!=2
        AND b.wms_status!=3
        AND tbc.business_type!=205
        AND tbc.business_type!=204
        AND tbc.business_type!=208
        AND b.order_type!=5
        AND a.sku_id in
        <foreach collection="skuIdList" index="index" item="skuId" open="(" separator="," close=")">
            #{skuId}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        order by b.create_time limit #{pageIndex},#{pageSize}
    </select>

    <select id="getWmsOutUseCount" resultType="java.lang.Integer"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.stock.QueryStockInUseParameter">
        SELECT count(1)
        FROM `td_wms_bill_detail_out` a
        JOIN `td_wms_bill_core` b ON a.profile_id=b.profile_id AND a.vchcode=b.vchcode
        LEFT JOIN td_bill_core tbc ON tbc.profile_id=a.profile_id AND tbc.vchcode=a.vchcode
        left join acc_vchtype vc on tbc.vchtype = vc.vchtype and vc.profile_id = tbc.profile_id
        WHERE a.profile_id=#{profileId}
        AND b.wms_status!=2
        AND b.wms_status!=3
        AND b.order_type!=5
        AND tbc.business_type!=205
        AND tbc.business_type!=204
        AND tbc.business_type!=208
        AND a.sku_id in
        <foreach collection="skuIdList" index="index" item="skuId" open="(" separator="," close=")">
            #{skuId}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </select>

    <select id="getWmsWarehouseTaskOutUse" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.stock.QueryStockInUseParameter">
        SELECT a.profile_id,a.vchcode AS sourceId,11 AS sourceType,2 AS recordType, b.erp_order_code AS trade_id,
        (a.qty-a.reality_qty) AS qty ,0 AS ruleId,
        a.ptype_id,a.sku_id,b.ktype_id,
        tbdc.cost_id,a.produce_date,a.expire_date,a.batchno,tbdc.batch_price
        FROM `td_wms_bill_detail_out` a
        JOIN `td_wms_bill_core` b ON a.profile_id=b.profile_id AND a.vchcode=b.vchcode
        LEFT JOIN td_bill_warehouse_task task ON task.profile_id=a.profile_id AND task.warehouse_task_id=a.vchcode
        LEFT JOIN td_bill_warehouse_task_detail task_detail ON task_detail.warehouse_task_detail_id=a.detail_id AND task_detail.profile_id=a.profile_id
        LEFT JOIN td_bill_detail_core tbdc ON tbdc.detail_id=task_detail.detail_id AND tbdc.profile_id=task_detail.profile_id
        left join td_bill_core tbc on tbdc.profile_id = tbc.profile_id and tbdc.vchcode = tbc.vchcode
        left join acc_vchtype vc on tbc.vchtype = vc.vchtype and vc.profile_id = tbc.profile_id
        WHERE a.profile_id=#{profileId}
        AND b.wms_status!=2
        AND b.wms_status!=3
        AND b.order_type =5
        AND tbc.business_type!=205
        AND tbc.business_type!=204
        AND tbc.business_type!=208
        AND a.sku_id in
        <foreach collection="skuIdList" index="index" item="skuId" open="(" separator="," close=")">
            #{skuId}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        order by b.create_time limit #{pageIndex},#{pageSize}
    </select>

    <select id="getWmsWarehouseTaskOutUseCount" resultType="java.lang.Integer"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.stock.QueryStockInUseParameter">
        SELECT count(1)
        FROM `td_wms_bill_detail_out` a
        JOIN `td_wms_bill_core` b ON a.profile_id=b.profile_id AND a.vchcode=b.vchcode
        LEFT JOIN td_bill_warehouse_task task ON task.profile_id=a.profile_id AND task.warehouse_task_id=a.vchcode
        LEFT JOIN td_bill_warehouse_task_detail task_detail ON task_detail.warehouse_task_detail_id=a.detail_id AND task_detail.profile_id=a.profile_id
        LEFT JOIN td_bill_detail_core tbdc ON tbdc.detail_id=task_detail.detail_id AND tbdc.profile_id=task_detail.profile_id
        left join td_bill_core tbc on tbdc.profile_id = tbc.profile_id and tbdc.vchcode = tbc.vchcode
        left join acc_vchtype vc on tbc.vchtype = vc.vchtype and vc.profile_id = tbc.profile_id
        WHERE a.profile_id=#{profileId}
        AND b.wms_status!=2
        AND b.wms_status!=3
        AND b.order_type =5
        AND tbc.business_type!=205
        AND tbc.business_type!=204
        AND tbc.business_type!=208
        AND a.sku_id in
        <foreach collection="skuIdList" index="index" item="skuId" open="(" separator="," close=")">
            #{skuId}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </select>



    <select id="getAccountingRecordList" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select tdb.profile_id, tdb.vchcode as sourceId,4 as sourceType,1 as recordType,
        tc.bill_number as trade_id,
        (-1*(tbdc.qty-tbdc.inout_qty)) as qty,
        tbdd.stock_sync_rule_id as ruleId,
        tbdc.ptype_id,tbdc.sku_id,tbdc.ktype_id,
        tbdc.cost_id,tbdb.produce_date,tbdb.expire_date,tbdb.batchno,tbdc.batch_price
        from td_bill_deliver tdb
        left join td_bill_core tc on tdb.profile_id = tc.profile_id and tdb.vchcode = tc.vchcode
        left join td_bill_detail_deliver tbdd on tdb.profile_id = tbdd.profile_id and tdb.vchcode = tbdd.vchcode
        left join td_bill_detail_core tbdc on tdb.profile_id = tbdc.profile_id and tdb.vchcode = tbdc.vchcode and
        tbdd.detail_id=tbdc.detail_id
        left join td_bill_detail_batch tbdb on tbdb.profile_id=tbdc.profile_id and tbdb.detail_id=tbdc.detail_id and
        tbdb.vchcode=tbdc.vchcode
        left join acc_vchtype vc on tc.vchtype = vc.vchtype and vc.profile_id = tc.profile_id
        where tdb.profile_id = #{profileId}
        and tc.deleted=0
        and tc.business_type!=204
        and tc.business_type!=205
        and tc.business_type!=208
        and tbdc.stock_occupy_type=0
        and tbdd.refund_state!=2
        and tbdd.stock_sync_rule_id>0
        and tbdc.sku_id>0
        and tdb.vchcode in
        <foreach collection="sourceIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="errBusinessOrderEnabled and nonPaidOrderEnabled">
            and tbdd.trade_state in (0,1,2,3,4,6,7)
        </if>
        <if test="!errBusinessOrderEnabled and nonPaidOrderEnabled">
            and tbdd.trade_state in (1,2,3,4,6,7)
        </if>
        <if test="errBusinessOrderEnabled and !nonPaidOrderEnabled">
            and tbdd.trade_state in (0,2,3,4,6,7)
        </if>
        <if test="!errBusinessOrderEnabled and !nonPaidOrderEnabled">
            and tbdd.trade_state in (2,3,4,6,7)
        </if>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </select>

    <delete id="deleteSysData">
        delete
        from sys_data
        where profile_id = #{profileId}
          and sub_name = #{subName}
    </delete>

    <select id="getSysDataList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.SysData">
        select id, profile_id, sub_name, sub_value from sys_data
        where profile_id in
        <foreach collection="profileIds" separator="," open="(" close=")" index="index" item="profileId">
            #{profileId}
        </foreach>
        and sub_name = #{subName}
    </select>

    <select id="queryPreStockDetails" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select pre.profile_id, od.source_id, od.source_type, od.record_type,od.id,
               od.ptype_id,od.sku_id,od.source_detail_id,
               od.trade_id,od.ktype_id,od.qty,
               od.rule_id,od.batch_price,od.batchno,od.expire_date,od.produce_date,1 as summaryType
        from stock_pre_record_order_detail pre
        left join pl_eshop_sale_order s on s.profile_id=pre.profile_id and s.otype_id=pre.eshop_id and s.trade_order_id=pre.trade_order_id and s.deleted=0
        left join stock_record_order_detail od on od.profile_id=pre.profile_id and od.source_id=pre.source_id and pre.id=od.source_detail_id
        where pre.profile_id=#{profileId} and s.id in
        <foreach collection="sourceIds" separator="," open="(" close=")" index="index" item="id">
            #{id}
        </foreach>
        and od.source_type=101
        and od.id>0
    </select>

    <select id="queryOldPreStockDetails" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select profile_id, source_id, source_type, record_type,id,
        ptype_id,sku_id,source_detail_id,
        trade_id,ktype_id,qty,
        rule_id,batch_price,batchno,expire_date,produce_date,summary_type
        from stock_record_order_detail
        where profile_id=#{profileId}
        and create_time >= DATE_ADD(now(),interval -300 day)
        and create_time <![CDATA[<=]]> DATE_ADD(now(),interval -10 minute)
        and source_id not in
        <foreach collection="sourceIds" separator="," open="(" close=")" index="index" item="id">
            #{id}
        </foreach>
        and source_type=101
    </select>
    <select id="getDeliverSendRecordList"
            resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select tdb.profile_id, tdb.vchcode as sourceId,4 as sourceType,2 as recordType,tbdd.detail_id as sourceDetailId,
        tc.bill_number as trade_id,
        (-1*(tbdc.qty-tbdc.inout_qty)) as qty,
        tbdd.stock_sync_rule_id as ruleId,
        tbdc.ptype_id,tbdc.sku_id,tbdc.ktype_id,
        tbdc.cost_id,tbdb.produce_date,tbdb.expire_date,tbdb.batchno,tbdb.batch_price
        from td_bill_deliver tdb
        left join td_bill_core tc on tdb.profile_id = tc.profile_id and tdb.vchcode = tc.vchcode
        left join td_bill_assinfo tba on tba.profile_id=tdb.profile_id and tba.vchcode=tdb.vchcode
        left join td_bill_detail_deliver tbdd on tdb.profile_id = tbdd.profile_id and tdb.vchcode = tbdd.vchcode
        left join td_bill_detail_core tbdc on tdb.profile_id = tbdc.profile_id and tdb.vchcode = tbdc.vchcode and
        tbdd.detail_id=tbdc.detail_id
        left join td_bill_detail_batch tbdb on tbdb.profile_id=tbdc.profile_id and tbdb.detail_id=tbdc.detail_id and
        tbdb.vchcode=tbdc.vchcode
        left join acc_vchtype vc on tc.vchtype = vc.vchtype and vc.profile_id = tc.profile_id
        where tdb.profile_id = #{profileId}
        and tc.deleted=0
        and tc.business_type!=204
        and tc.business_type!=205
        and tc.business_type!=208
        and tbdc.stock_occupy_type=0
        and tbdd.refund_state!=2
        and tbdc.sku_id>0
        and tbdd.send_qty_lock_status=1
        and (tc.post_state <![CDATA[<]]> 500 or (tc.post_state=500 and  tba.deliver_state=0))
        and tdb.vchcode in
        <foreach collection="sourceIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="errBusinessOrderEnabled and nonPaidOrderEnabled">
            and tbdd.trade_state in (0,1,2,3,4,6,7)
        </if>
        <if test="!errBusinessOrderEnabled and nonPaidOrderEnabled">
            and tbdd.trade_state in (1,2,3,4,6,7)
        </if>
        <if test="errBusinessOrderEnabled and !nonPaidOrderEnabled">
            and tbdd.trade_state in (0,2,3,4,6,7)
        </if>
        <if test="!errBusinessOrderEnabled and !nonPaidOrderEnabled">
            and tbdd.trade_state in (2,3,4,6,7)
        </if>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </select>
    <select id="getWarehouseTaskSendRecordList" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select task.profile_id, task.warehouse_task_id as sourceId,12 as sourceType,2 as recordType,tbdd.detail_id as sourceDetailId,
        tc.bill_number as trade_id,
        (-1*(tbdc.qty-tbdc.inout_qty)) as qty,
        tbdd.stock_sync_rule_id as ruleId,
        tbdc.ptype_id,tbdc.sku_id,tbdc.ktype_id,
        tbdc.cost_id,tbdb.produce_date,tbdb.expire_date,tbdb.batchno,tbdb.batch_price
        from td_bill_warehouse_task task
        left join td_bill_warehouse_task_detail detail_task on detail_task.warehouse_task_id = task.warehouse_task_id and detail_task.profile_id = task.profile_id
        left join td_bill_detail_deliver tbdd on detail_task.profile_id = tbdd.profile_id and detail_task.detail_id = tbdd.detail_id
        left join td_bill_detail_core tbdc on detail_task.profile_id = tbdc.profile_id and detail_task.detail_id = tbdc.detail_id
        left join td_bill_core tc on tbdc.profile_id = tc.profile_id and tbdc.vchcode = tc.vchcode
        left join td_bill_detail_batch tbdb on tbdb.profile_id=tbdc.profile_id and tbdb.detail_id=tbdc.detail_id and tbdb.vchcode=tbdc.vchcode
        left join acc_vchtype vc on tc.vchtype = vc.vchtype and vc.profile_id = tc.profile_id
        where task.profile_id = #{profileId}
        and tc.deleted=0
        and task.deleted=0
        and task.business_type!=204
        and task.business_type!=205
        and task.business_type!=208
        and tbdc.stock_occupy_type=0
        and tbdd.refund_state!=2
        and tbdc.sku_id>0
        and tbdd.send_qty_lock_status=1
        and task.wholesale_bill != 1
--         and task.warehouse_state=1
        and task.full_link_status <include refid="com.wsgjp.ct.sale.biz.jarvis.mapper.warehouseTask.WarehouseTaskMapper.wait_state"></include>
        and task.warehouse_task_id in
        <foreach collection="sourceIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="errBusinessOrderEnabled and nonPaidOrderEnabled">
            and tbdd.trade_state in (0,1,2,3,4,6,7)
        </if>
        <if test="!errBusinessOrderEnabled and nonPaidOrderEnabled">
            and tbdd.trade_state in (1,2,3,4,6,7)
        </if>
        <if test="errBusinessOrderEnabled and !nonPaidOrderEnabled">
            and tbdd.trade_state in (0,2,3,4,6,7)
        </if>
        <if test="!errBusinessOrderEnabled and !nonPaidOrderEnabled">
            and tbdd.trade_state in (2,3,4,6,7)
        </if>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </select>


    <select id="getWarehouseTaskSaleRecordList" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select task.profile_id, task.warehouse_task_id as sourceId,12 as sourceType,1 as recordType,
        tbdd.detail_id as sourceDetailId,tc.bill_number as trade_id,
        (-1*(tbdc.qty-tbdc.inout_qty)) as qty,
        tbdd.stock_sync_rule_id as ruleId,
        tbdc.ptype_id,tbdc.sku_id,tbdc.ktype_id,
        tbdc.cost_id,tbdb.produce_date,tbdb.expire_date,tbdb.batchno,tbdb.batch_price,3 as summaryType
        from td_bill_warehouse_task task
        left join td_bill_warehouse_task_detail task_detail on task_detail.profile_id = task.profile_id and
        task_detail.warehouse_task_id = task.warehouse_task_id
        left join td_bill_detail_deliver tbdd on task_detail.profile_id = tbdd.profile_id and task_detail.detail_id =
        tbdd.detail_id
        left join td_bill_detail_core tbdc on task_detail.profile_id = tbdc.profile_id and task_detail.detail_id =
        tbdc.detail_id
        left join td_bill_core tc on tbdc.profile_id = tc.profile_id and tbdc.vchcode = tc.vchcode
        left join td_bill_detail_batch tbdb on task_detail.profile_id=tbdb.profile_id and
        task_detail.detail_id=tbdb.detail_id
        left join acc_vchtype vc on tc.vchtype = vc.vchtype and vc.profile_id = tc.profile_id
        where task.profile_id = #{profileId}
        and tc.deleted=0
        and tbdc.deleted = 0
        and task.business_type!=204
        and task.business_type!=205
        and task.business_type!=208
        and tbdc.stock_occupy_type=0
        and tbdd.refund_state!=2
        and tbdc.sku_id>0
        and tc.post_state <![CDATA[<]]> 600
        and tc.order_sale_mode = 5
        and (task.self_delivery_mode != 4 or task.full_link_status <![CDATA[<]]> 80)
        and (tbdc.qty-tbdc.inout_qty)!=0
        and task.wholesale_bill != 1
        and task.warehouse_task_id in
        <foreach collection="sourceIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="errBusinessOrderEnabled and nonPaidOrderEnabled">
            and tbdd.trade_state in (0,1,2,3,4,6,7)
        </if>
        <if test="!errBusinessOrderEnabled and nonPaidOrderEnabled">
            and tbdd.trade_state in (1,2,3,4,6,7)
        </if>
        <if test="errBusinessOrderEnabled and !nonPaidOrderEnabled">
            and tbdd.trade_state in (0,2,3,4,6,7)
        </if>
        <if test="!errBusinessOrderEnabled and !nonPaidOrderEnabled">
            and tbdd.trade_state in (2,3,4,6,7)
        </if>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and (tbdc.qty-tbdc.inout_qty)!=0
    </select>

    <select id="getWarehouseTaskRecordList" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select task.profile_id,
            task.warehouse_task_id as sourceId,
             tbdd.detail_id as sourceDetailId,tc.bill_number as trade_id,
            (-1*(tbdc.qty-tbdc.inout_qty)) as qty,
            tbdd.stock_sync_rule_id as ruleId,
            tbdc.ptype_id,
            tbdc.sku_id,
            tbdc.ktype_id,
            tbdb.produce_date,tbdb.expire_date,tbdb.batchno,tbdb.batch_price,
            3 as summaryType,
            12 as sourceType,
            1 as recordType,
            tc.vchtype as vchType,
            true as saleOccupiedType,
            if(tbdd.send_qty_lock_status=1, 2, if(tc.post_state=500 or tc.post_state=550, 2, 1)) as sendOccupiedType,
            false as draftState,
            tbdd.trade_state,
            tc.post_state
        from td_bill_warehouse_task task
         left join td_bill_warehouse_task_detail twtd on twtd.profile_id = task.profile_id and twtd.warehouse_task_id = task.warehouse_task_id
         left join td_bill_detail_deliver tbdd on twtd.profile_id = tbdd.profile_id and twtd.detail_id = tbdd.detail_id
         left join td_bill_detail_core tbdc on twtd.profile_id = tbdc.profile_id and twtd.detail_id =tbdc.detail_id
        left join td_bill_core tc on tbdc.profile_id = tc.profile_id and tbdc.vchcode = tc.vchcode
        left join td_bill_detail_batch tbdb on twtd.profile_id=tbdb.profile_id and twtd.detail_id=tbdb.detail_id
        where  task.profile_id = #{profileId}
        and tc.deleted=0
        and tbdc.deleted = 0
        and task.business_type!=204
        and task.business_type!=205
        and task.business_type!=208
        and tbdc.stock_occupy_type=0
        and tbdd.refund_state!=2
        and tbdd.trade_state in (0,1,2,3,4,6,7)
        and tbdc.sku_id>0
        and tc.post_state <![CDATA[<]]> 600
        and task.full_link_status <![CDATA[<]]> 80
        and (tbdc.qty-tbdc.inout_qty)!=0
        and task.warehouse_task_id in
        <foreach collection="sourceIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and tc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </select>


    <sql id="WarehouseTaskUse_where">
        where task.profile_id = #{profileId}
        and tc.deleted=0
        and tbdc.deleted = 0
        and task.business_type!=204
        and task.business_type!=205
        and task.business_type!=208
        and tbdc.stock_occupy_type=0
        and tbdd.refund_state!=2
        and tbdd.trade_state in (0,1,2,3,4,6,7)
        and tbdc.sku_id>0
        and tc.post_state <![CDATA[<]]> 600
        and task.full_link_status <![CDATA[<]]> 80
        and (tbdc.qty-tbdc.inout_qty)!=0
        and tc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and tbdc.sku_id in
        <foreach collection="skuIdList" index="index" item="skuId" open="(" separator="," close=")">
            #{skuId}
        </foreach>

    </sql>

    <select id="getWarehouseTaskUseList" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select task.profile_id,
        task.warehouse_task_id as sourceId,
        tbdd.detail_id as sourceDetailId,
        tc.bill_number as trade_id,
        (-1*(tbdc.qty-tbdc.inout_qty)) as qty,
        tbdd.stock_sync_rule_id as ruleId,
        tbdc.ptype_id,tbdc.sku_id,tbdc.ktype_id,
        3 as summaryType,
        12 as sourceType,
        1 as recordType,
        tc.vchtype as vchType,
        true as saleOccupiedType,
        if(tbdd.send_qty_lock_status=1, 2, if(tc.post_state=500 or tc.post_state=550, 2, 1)) as sendOccupiedType,
        false as draftState,
        tbdd.trade_state,
        tc.post_state
        from td_bill_warehouse_task task
        left join td_bill_warehouse_task_detail twtd on twtd.profile_id = task.profile_id and twtd.warehouse_task_id = task.warehouse_task_id
        left join td_bill_detail_deliver tbdd on twtd.profile_id = tbdd.profile_id and twtd.detail_id = tbdd.detail_id
        left join td_bill_detail_core tbdc on twtd.profile_id = tbdc.profile_id and twtd.detail_id =tbdc.detail_id
        left join td_bill_core tc on tbdc.profile_id = tc.profile_id and tbdc.vchcode = tc.vchcode
        <include refid="WarehouseTaskUse_where"/>
        order by task.create_time limit #{pageIndex},#{pageSize}
    </select>

    <select id="getWarehouseTaskUseCount" resultType="int">
        select count(1)
        from td_bill_warehouse_task task
        left join td_bill_warehouse_task_detail twtd on twtd.profile_id = task.profile_id and twtd.warehouse_task_id = task.warehouse_task_id
        left join td_bill_detail_deliver tbdd on twtd.profile_id = tbdd.profile_id and twtd.detail_id = tbdd.detail_id
        left join td_bill_detail_core tbdc on twtd.profile_id = tbdc.profile_id and twtd.detail_id =tbdc.detail_id
        left join td_bill_core tc on tbdc.profile_id = tc.profile_id and tbdc.vchcode = tc.vchcode
        <include refid="WarehouseTaskUse_where"/>
    </select>


    <select id="getWarehouseSaleQtyTaskUseCount" resultType="java.lang.Integer">
        select count(1)
        from td_bill_warehouse_task task
        left join td_bill_warehouse_task_detail task_detail on task_detail.profile_id = task.profile_id and task_detail.warehouse_task_id = task.warehouse_task_id
        left join td_bill_detail_deliver tbdd on task_detail.profile_id = tbdd.profile_id and task_detail.detail_id = tbdd.detail_id
        left join td_bill_detail_core tbdc on task_detail.profile_id = tbdc.profile_id and task_detail.detail_id = tbdc.detail_id
        left join td_bill_core tc on tbdc.profile_id = tc.profile_id and tbdc.vchcode = tc.vchcode
        left join td_bill_detail_batch tbdb on task_detail.profile_id=tbdb.profile_id and task_detail.detail_id=tbdb.detail_id
        left join acc_vchtype vc on tc.vchtype = vc.vchtype and vc.profile_id = tc.profile_id
        <include refid="WarehouseSaleQtyTask_sql"/>
    </select>
    <select id="getWarehouseSaleQtyTaskUse" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select tc.profile_id,task.warehouse_task_id as sourceId,12 as sourceType,1 as recordType,
        (-1*(tbdc.qty-tbdc.inout_qty)) as qty,tc.bill_number as tradeId,
        tbdc.sku_id,
        tbdc.ptype_id,
        tbdd.stock_sync_rule_id  as ruleId,
        ifnull(rule.rule_type,0) as ruleType,
        tbdc.ktype_id
        from td_bill_warehouse_task task
        left join td_bill_warehouse_task_detail task_detail on task_detail.profile_id = task.profile_id and task_detail.warehouse_task_id = task.warehouse_task_id
        left join td_bill_detail_deliver tbdd on task_detail.profile_id = tbdd.profile_id and task_detail.detail_id = tbdd.detail_id
        left join td_bill_detail_core tbdc on task_detail.profile_id = tbdc.profile_id and task_detail.detail_id = tbdc.detail_id
        left join td_bill_core tc on tbdc.profile_id = tc.profile_id and tbdc.vchcode = tc.vchcode
        left join td_bill_detail_batch tbdb on task_detail.profile_id=tbdb.profile_id and task_detail.detail_id=tbdb.detail_id
        left join pl_eshop_stock_sync_rule rule on rule.profile_id=tbdd.profile_id and rule.id=tbdd.stock_sync_rule_id and rule.deleted=0
        left join acc_vchtype vc on tc.vchtype = vc.vchtype and vc.profile_id = tc.profile_id
        <include refid="WarehouseSaleQtyTask_sql"/>
        order by task.create_time limit #{pageIndex},#{pageSize}
    </select>
    <sql id="WarehouseSaleQtyTask_sql">
        where task.profile_id=#{profileId}
        and tc.deleted=0
        and tbdc.deleted = 0
        and task.business_type!=204
        and task.business_type!=205
        and task.business_type!=208
        and tbdc.stock_occupy_type=0
        and tbdd.refund_state!=2
        and tbdc.sku_id>0
        and tc.post_state <![CDATA[<]]> 600
        and tc.order_sale_mode = 5
        and (task.self_delivery_mode != 4 or task.full_link_status <![CDATA[<]]> 80)
        and task.wholesale_bill != 1
        <if test="skuIdList!=null and skuIdList.size()>0">
            and tbdc.sku_id in
            <foreach collection="skuIdList" separator="," open="(" close=")" index="index" item="item">
                #{item}
            </foreach>
        </if>
        and tbdd.trade_state in
        <foreach collection="statusList" index="index" item="state" open="(" separator="," close=")">
            #{state}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </sql>

    <select id="getWarehouseSendQtyTaskUseCount" resultType="java.lang.Integer">
        select count(1)
        from td_bill_warehouse_task task
        left join td_bill_warehouse_task_detail detail_task on detail_task.warehouse_task_id = task.warehouse_task_id and detail_task.profile_id = task.profile_id
        left join td_bill_detail_deliver tbdd on detail_task.profile_id = tbdd.profile_id and detail_task.detail_id = tbdd.detail_id
        left join td_bill_detail_core tbdc on detail_task.profile_id = tbdc.profile_id and detail_task.detail_id = tbdc.detail_id
        left join td_bill_core tc on tbdc.profile_id = tc.profile_id and tbdc.vchcode = tc.vchcode
        left join td_bill_detail_batch tbdb on tbdb.profile_id=tbdc.profile_id and tbdb.detail_id=tbdc.detail_id and tbdb.vchcode=tbdc.vchcode
        left join pl_eshop_stock_sync_rule rule on rule.profile_id=tbdd.profile_id and rule.id=tbdd.stock_sync_rule_id and rule.deleted=0
        left join acc_vchtype vc on tc.vchtype = vc.vchtype and vc.profile_id = tc.profile_id
        <include refid="WarehouseSendQtyTask_sql"/>
    </select>

    <select id="getWarehouseSendQtyTaskUse" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        select task.profile_id, task.warehouse_task_id as sourceId,12 as sourceType,2 as recordType,tbdd.detail_id as sourceDetailId,
        (-1*(tbdc.qty-tbdc.inout_qty)) as qty,tc.bill_number as trade_id,
        tbdd.stock_sync_rule_id as ruleId, ifnull(rule.rule_type,0) as ruleType,
        tbdc.ptype_id,tbdc.sku_id,tbdc.ktype_id,
        tbdc.cost_id,tbdb.produce_date,tbdb.expire_date,tbdb.batchno,tbdb.batch_price
        from td_bill_warehouse_task task
        left join td_bill_warehouse_task_detail detail_task on detail_task.warehouse_task_id = task.warehouse_task_id and detail_task.profile_id = task.profile_id
        left join td_bill_detail_deliver tbdd on detail_task.profile_id = tbdd.profile_id and detail_task.detail_id = tbdd.detail_id
        left join td_bill_detail_core tbdc on detail_task.profile_id = tbdc.profile_id and detail_task.detail_id = tbdc.detail_id
        left join td_bill_core tc on tbdc.profile_id = tc.profile_id and tbdc.vchcode = tc.vchcode
        left join td_bill_detail_batch tbdb on tbdb.profile_id=tbdc.profile_id and tbdb.detail_id=tbdc.detail_id and tbdb.vchcode=tbdc.vchcode
        left join pl_eshop_stock_sync_rule rule on rule.profile_id=tbdd.profile_id and rule.id=tbdd.stock_sync_rule_id and rule.deleted=0
        left join acc_vchtype vc on tc.vchtype = vc.vchtype and vc.profile_id = tc.profile_id
        <include refid="WarehouseSendQtyTask_sql"/>
        order by task.create_time limit #{pageIndex},#{pageSize}
    </select>
    <select id="getWmsOutRecordTaskList"
            resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail">
        SELECT a.profile_id,a.vchcode AS sourceId,11 AS sourceType,2 AS recordType, b.erp_order_code AS
        trade_id,a.detail_id as sourceDetailId,
        (a.qty-a.reality_qty) AS qty ,0 AS ruleId,
        a.ptype_id,a.sku_id,b.ktype_id,
        tbdc.cost_id,a.produce_date,a.expire_date,a.batchno,tbdc.batch_price
        FROM `td_wms_bill_detail_out` a
        JOIN `td_wms_bill_core` b ON a.profile_id=b.profile_id AND a.vchcode=b.vchcode
        LEFT JOIN td_bill_core tbc ON tbc.profile_id=a.profile_id AND tbc.vchcode=a.vchcode
        LEFT JOIN td_bill_detail_core tbdc ON tbdc.detail_id=a.detail_id AND tbdc.profile_id=a.profile_id
        left join acc_vchtype vc on tbc.vchtype = vc.vchtype and vc.profile_id = tbc.profile_id
        WHERE a.profile_id=#{profileId}
        AND b.wms_status!=2
        AND b.wms_status!=3
        AND b.order_type!=5
        AND tbc.business_type!=205
        AND tbc.business_type!=204
        AND tbc.business_type!=208
        AND a.vchcode IN
        <foreach collection="sourceIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        union
        SELECT a.profile_id,a.vchcode AS sourceId,11 AS sourceType,2 AS recordType, b.erp_order_code AS
        trade_id,a.detail_id as sourceDetailId,
        (a.qty-a.reality_qty) AS qty ,0 AS ruleId,
        a.ptype_id,a.sku_id,b.ktype_id,
        tbdc.cost_id,a.produce_date,a.expire_date,a.batchno,tbdc.batch_price
        FROM `td_wms_bill_detail_out` a
        JOIN `td_wms_bill_core` b ON a.profile_id=b.profile_id AND a.vchcode=b.vchcode
        LEFT JOIN td_bill_warehouse_task task ON task.profile_id=a.profile_id AND task.warehouse_task_id=a.vchcode
        LEFT JOIN td_bill_warehouse_task_detail task_detail ON task_detail.warehouse_task_detail_id=a.detail_id AND task_detail.profile_id=a.profile_id
        LEFT JOIN td_bill_detail_core tbdc ON tbdc.detail_id=task_detail.detail_id AND tbdc.profile_id=task_detail.profile_id
        left join td_bill_core tbc on tbdc.profile_id = tbc.profile_id and tbdc.vchcode = tbc.vchcode
        left join acc_vchtype vc on tbc.vchtype = vc.vchtype and vc.profile_id = tbc.profile_id
        WHERE a.profile_id=#{profileId}
        AND b.wms_status!=2
        AND b.wms_status!=3
        AND b.order_type =5
        AND tbc.business_type!=205
        AND tbc.business_type!=204
        AND tbc.business_type!=208
        AND a.vchcode IN
        <foreach collection="sourceIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </select>

    <select id="getNeedFixSkuIdListBySkuIdAsc" resultType="java.math.BigInteger">
        select s.id as skuId
        from base_ptype_sku s
                 left join base_ptype bp on s.ptype_id = bp.id and s.profile_id = bp.profile_id
        where s.profile_id = #{profileId} and s.id>#{skuId}
          and s.stoped = 0
          and bp.stoped = 0
          and bp.deleted = 0
        order by s.id asc
        limit #{pageSize}
    </select>
    <select id="getEshopIdByPlatformRowId" resultType="java.math.BigInteger">
        select eshop_id from pl_eshop_product_sku where profile_id=#{profileId} and id=#{rowId}
    </select>

    <sql id="WarehouseSendQtyTask_sql">
        where task.profile_id = #{profileId}
        and tc.deleted=0
        and task.deleted=0
        and task.business_type!=204
        and task.business_type!=205
        and task.business_type!=208
        and tbdc.stock_occupy_type=0
        and tbdd.refund_state!=2
        and tbdc.sku_id>0
        and tbdd.send_qty_lock_status=1
--         and task.warehouse_state=1
        and task.full_link_status <include refid="com.wsgjp.ct.sale.biz.jarvis.mapper.warehouseTask.WarehouseTaskMapper.wait_state"></include>
        and task.wholesale_bill != 1
        <if test="skuIdList!=null and skuIdList.size()>0">
            and tbdc.sku_id in
            <foreach collection="skuIdList" separator="," open="(" close=")" index="index" item="item">
                #{item}
            </foreach>
        </if>
        and tbdd.trade_state in
        <foreach collection="statusList" index="index" item="state" open="(" separator="," close=")">
            #{state}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </sql>


    <insert id="insertPreRecordDetails">
        insert ignore into stock_pre_record_order_detail
            (id, profile_id, eshop_id, trade_order_id, trade_order_detail_id, source_id, ptype_id,
            sku_id, ktype_id, qty, trade_state, refund_state,
            rule_id, batchno, batch_price, produce_date, expire_date, hash_mark)
        values
        <foreach collection="details" separator="," item="item">
            (#{item.id},#{item.profileId},#{item.eshopId},#{item.tradeOrderId},#{item.tradeOrderDetailId},#{item.sourceId},#{item.ptypeId}
            ,#{item.skuId},#{item.ktypeId},#{item.qty},#{item.tradeState},#{item.refundState},
            #{item.ruleId},#{item.batchno},#{item.batchPrice},#{item.produceDate},#{item.expireDate},#{item.hashMark})
        </foreach>
    </insert>

    <select id="getGiftOrderParentId" resultType="java.math.BigInteger">
        select id from pl_eshop_sale_order
        where profile_id = #{profileId} and order_sale_type = 6
        and trade_order_id in
        <foreach collection="tradeIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>
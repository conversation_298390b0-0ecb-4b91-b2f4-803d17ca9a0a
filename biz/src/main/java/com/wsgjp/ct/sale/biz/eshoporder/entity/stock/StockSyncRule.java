package com.wsgjp.ct.sale.biz.eshoporder.entity.stock;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.eshoporder.constant.StringConstantEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Stock;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleTargetTypeEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleType;
import com.wsgjp.ct.sale.common.enums.publish.ProtectDaysUnitEnum;
import com.wsgjp.ct.sale.platform.enums.EshopWareHouseType;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class StockSyncRule extends StockSyncRuleBase {
    private BigInteger ptypeId;
    private BigInteger skuId;
    private BigInteger unitId;
    private BigDecimal unitRate;
    private String eshopName;
    private BigInteger otypeId;
    private ShopType eshopType;

    private String warehouseCode;
    private String warehouseName;
    private EshopWareHouseType warehouseType;
    private List<StockRuleDetail> stockRuleDetailList;
    private List<StockRuleDetail> relationLockDetailList;
    /**
     * 时效规则列表
     */
    private List<MultiStockSyncDetail> multiStockSyncDetailList;
    private List<Stock> ktypeDataSource;
    private String ptypeName;
    private Boolean batchEnabled;
    private int protectDays;
    private ProtectDaysUnitEnum protectDaysUnit;
    private String formula;
    private BigInteger frozenQty;
    private BigInteger frozenSaleQty;
    private String ktypeIds;
    private String ktypeNames;
    private Date lastSyncTime;
    private String frozenQtyStr;
    private BigDecimal ruleCalQty;


    private BigDecimal thresholdForShare;
    private BigDecimal sysnForShare;

    private BigDecimal thresholdForLock;
    private BigDecimal sysnForLock;
    /*
    * 分仓同步规则，新版本分仓同步功能使用
    * */
    private List<StockSyncRule> storeRuleLsit;

    public ShopType getEshopType() {
        return eshopType;
    }

    public void setEshopType(ShopType eshopType) {
        this.eshopType = eshopType;
    }

    public BigDecimal getSysnForShare() {
        if (sysnForShare == null) {
            sysnForShare = getCalculate().getSysnForShare();
        }
        return sysnForShare;
    }

    public void setSysnForShare(BigDecimal sysnForShare) {
        this.sysnForShare = sysnForShare;
    }

    public BigDecimal getSysnForLock() {
        if (sysnForLock == null) {
            sysnForLock = getCalculate().getSysnForLock();
        }
        return sysnForLock;
    }

    public void setSysnForLock(BigDecimal sysnForLock) {
        this.sysnForLock = sysnForLock;
    }

    public BigDecimal getThresholdForShare() {
        if (thresholdForShare == null) {
            thresholdForShare = getCalculate().getThresholdForShare();
        }
        return thresholdForShare;
    }

    public void setThresholdForShare(BigDecimal thresholdForShare) {
        this.thresholdForShare = thresholdForShare;
    }

    public BigDecimal getThresholdForLock() {
        if (thresholdForLock == null) {
            thresholdForLock = getCalculate().getThresholdForLock();
        }
        return thresholdForLock;
    }

    public void setThresholdForLock(BigDecimal thresholdForLock) {
        this.thresholdForLock = thresholdForLock;
    }


    public String getPtypeName() {
        return ptypeName;
    }

    public void setPtypeName(String ptypeName) {
        this.ptypeName = ptypeName;
    }

    public Date getLastSyncTime() {
        return lastSyncTime;
    }

    public void setLastSyncTime(Date lastSyncTime) {
        this.lastSyncTime = lastSyncTime;
    }

    public BigInteger getSkuId() {
        return skuId;
    }

    public void setSkuId(BigInteger skuId) {
        this.skuId = skuId;
    }

    public String getFrozenStr() {
        if (stockRuleDetailList == null || stockRuleDetailList.size() == 0) {
            return "";
        }
        if (getRuleType().equals(StockRuleType.FrozenStock)) {
            List<String> append = new ArrayList<>(stockRuleDetailList.size());
            stockRuleDetailList.forEach(item -> {
                String str = String.format("%s:%d", item.getFullname(), item.getFrozenQty().intValue());
                append.add(str);
            });
            return StringUtils.join(append, ";");
        }
        return "";
    }

    public String getKtypeNames() {
        if (ktypeNames != null && !"".equals(ktypeNames)) {
            return ktypeNames;
        }
        if (CollectionUtils.isEmpty(stockRuleDetailList)) {
            return "";
        }
        List<String> names = stockRuleDetailList.stream().map(StockRuleDetail::getFullname).collect(Collectors.toList());
        return StringUtils.join(names, ",");
    }

    public String getBatchInfo() {
        if (CollectionUtils.isEmpty(stockRuleDetailList)) {
            return "";
        }
        List<String> batchList = new ArrayList<>();
        for (StockRuleDetail detail : stockRuleDetailList) {
            if (StringUtils.isEmpty(detail.getBatchno()) && detail.getExpireDate() == null && detail.getExpireDate() == null) {
                continue;
            }
            List<String> itemList = new ArrayList<>();
            if (StringUtils.isNotEmpty(detail.getBatchno())) {
                itemList.add(String.format("批次号: %s", detail.getBatchno()));
            }
            if (detail.getProduceDate() != null) {
                itemList.add(String.format("生产日期: %s", DateUtils.formatDate(detail.getProduceDate())));
            }
            if (detail.getExpireDate() != null) {
                itemList.add(String.format("过期时间: %s", DateUtils.formatDate(detail.getExpireDate())));
            }
            if (itemList.size() == 0) {
                continue;
            }
            batchList.add(StringUtils.join(itemList, StringConstantEnum.COMMA.getSymbol()));
        }
        if (batchList.size() == 0) {
            return "";
        }
        return StringUtils.join(batchList, ",");
    }

    public String getFormula() {
        getRuleCronString();
        return formula;
    }

    public Calculate getCalculate() {
        String ruleCron = getRuleCron();
        if (StringUtils.isNotEmpty(ruleCron)) {
            return JsonUtils.toObject(ruleCron, Calculate.class);
        }
        return new Calculate();
    }

    private void getRuleCronString() {
        Calculate calculate = getCalculate();
        StockRuleType ruleType = getRuleType();
        StockRuleTargetTypeEnum targetType = getTargetType();
        String ruleName = getRuleName();
        String targetTypeName = getTargetTypeName(ruleName);
        formula = "";
        if (calculate == null || calculate.getRuleCalQty() == null) {
            return;
        }
        if (ruleType.equals(StockRuleType.SharedInventory)) {
            formula = String.format("可销售库存*%s%s%s%s", calculate.getPercentage().stripTrailingZeros().toPlainString(), "%", calculate.getCalculateMode().getName(), calculate.getRuleCalQty().stripTrailingZeros().toPlainString());
        } else if (ruleType.equals(StockRuleType.FrozenStock)) {
            formula = String.format("总锁库余量*%s%s", calculate.getPercentage().stripTrailingZeros().toPlainString(), "%");
        } else if (ruleType.equals(StockRuleType.VirtualStock)) {
            formula = String.format("虚拟库存数量:%s", calculate.getRuleCalQty().stripTrailingZeros().toPlainString());
        } else if (ruleType.equals(StockRuleType.FixedStockNumber)) {
            formula = String.format("固定数量:%s", calculate.getRuleCalQty().stripTrailingZeros().toPlainString());
        }
        if (targetType != null && StringUtils.isNotEmpty(targetType.getName()) && !StockRuleTargetTypeEnum.ALL.equals(targetType)) {
            formula = String.format("%s；同步公式：【%s】", targetTypeName, formula);
        }
    }

    private String getTargetTypeName(String ruleName) {
        StockRuleTargetTypeEnum targetType = getTargetType();
        if (targetType == null || StringUtils.isEmpty(targetType.getName()) || StockRuleTargetTypeEnum.ALL.equals(targetType)) {
            return "";
        }
        String targetTypeName;
        switch (targetType) {
            case NORMAL:
                targetTypeName = StringUtils.isNotEmpty(ruleName) && !ruleName.contains("【默认规则】") && !ruleName.contains("【网店默认库存同步规则】")
                        ? "例外库存同步规则" : ruleName;
                targetTypeName = StringUtils.isEmpty(targetTypeName) ? "网店默认库存同步规则" : targetTypeName;
                break;
            case WAREHOUSE:
                targetTypeName = StringUtils.isNotEmpty(ruleName) && !ruleName.contains("【默认规则】")  && !ruleName.contains("【网店默认库存同步规则】")
                        ? "例外分仓库存同步规则" : ruleName;
                targetTypeName = StringUtils.isEmpty(targetTypeName) ? "网店默认分仓库存同步规则" : targetTypeName;
                break;
            case LADDER:
                targetTypeName = StringUtils.isNotEmpty(ruleName) && !ruleName.contains("【默认规则】")  && !ruleName.contains("【网店默认库存同步规则】")
                        ? "例外阶梯库存同步规则" : ruleName;
                targetTypeName = StringUtils.isEmpty(targetTypeName) ? "网店默认阶梯库存同步规则" : targetTypeName;
                break;
            default:
                targetTypeName = targetType.getName();
                break;
        }
        if (StringUtils.isNotEmpty(ruleName) && StringUtils.isNotEmpty(targetTypeName) && !ruleName.equals(targetTypeName)) {
            targetTypeName = String.format("%s：【%s】", targetTypeName, ruleName);
        }
        return targetTypeName;
    }

    public String getEshopName() {
        return eshopName;
    }

    public void setEshopName(String eshopName) {
        this.eshopName = eshopName;
    }

    public BigInteger getOtypeId() {
        return otypeId;
    }

    public void setOtypeId(BigInteger otypeId) {
        this.otypeId = otypeId;
    }

    public String getKtypeIds() {
        if (StringUtils.isEmpty(ktypeIds)) {
            return "";
        }
        return ktypeIds;
    }

    public void setKtypeIds(String ktypeIds) {
        this.ktypeIds = ktypeIds;
    }

    public List<StockRuleDetail> getStockRuleDetailList() {
        if (stockRuleDetailList == null) {
            if (getRuleType().equals(StockRuleType.VirtualStock)) {
                StockRuleDetail defaultDetail = new StockRuleDetail();
                defaultDetail.setRuleId(getId());
                defaultDetail.setFrozenQty(getCalculate().getRuleCalQty());
                defaultDetail.setProfileId(getProfileId());
                defaultDetail.setPtypeId(getPtypeId());
                defaultDetail.setSkuId(getSkuId());
                defaultDetail.setUnitRate(getUnitRate());
                defaultDetail.setPcategory(getPcategory().getCode());
                stockRuleDetailList = new ArrayList<>();
                stockRuleDetailList.add(defaultDetail);
            }
        }
        return stockRuleDetailList;
    }

    public void setStockRuleDetailList(List<StockRuleDetail> stockRuleDetailList) {
        this.stockRuleDetailList = stockRuleDetailList;
    }

    public List<BigInteger> getKtypeIdList() {
        if (CollectionUtils.isNotEmpty(getStockRuleDetailList())) {
            return stockRuleDetailList.stream().map(StockRuleDetail::getKtypeId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
        List<BigInteger> ktypeIdList = new ArrayList<>();
        if (StringUtils.isEmpty(getKtypeIds())) {
            return ktypeIdList;
        }
        String[] strings = getKtypeIds().split(",");
        for (String s : strings) {
            ktypeIdList.add(new BigInteger(s));
        }
        return ktypeIdList;
    }

    public BigInteger getPtypeId() {
        return ptypeId;
    }

    public void setPtypeId(BigInteger ptypeId) {
        this.ptypeId = ptypeId;
    }

    public List<Stock> getKtypeDataSource() {
        return ktypeDataSource;
    }

    public void setKtypeDataSource(List<Stock> ktypeDataSource) {
        this.ktypeDataSource = ktypeDataSource;
    }

    public void setKtypeNames(String ktypeNames) {
        this.ktypeNames = ktypeNames;
    }

    public BigInteger getFrozenQty() {
        StockRuleType ruleType = getRuleType();
        if (ruleType.equals(StockRuleType.SharedInventory) || ruleType.equals(StockRuleType.FixedStockNumber)) {
            return null;
        }
        if (stockRuleDetailList != null && stockRuleDetailList.size() > 0) {
            long sum = stockRuleDetailList.stream().mapToLong(x -> x.getFrozenQty().longValue()).sum();
            return BigInteger.valueOf(sum);
        }
        return BigInteger.valueOf(getCalculate().getRuleCalQty().longValue());
    }

    public BigInteger getFrozenSaleQty() {
        StockRuleType ruleType = getRuleType();
        if (ruleType.equals(StockRuleType.SharedInventory) || ruleType.equals(StockRuleType.FixedStockNumber)) {
            return null;
        }
        if (stockRuleDetailList != null && stockRuleDetailList.size() > 0) {
            if (ruleType.equals(StockRuleType.FrozenStock) || ruleType.equals(StockRuleType.VirtualStock)) {
                return BigInteger.valueOf(stockRuleDetailList.stream().mapToLong(x -> x.getFrozenSaleQty().longValue()).sum());
            }
        }
        return frozenQty;
    }


    public BigInteger getUnitId() {
        if (unitId == null) {
            return BigInteger.ZERO;
        }
        return unitId;
    }

    public void setUnitId(BigInteger unitId) {
        this.unitId = unitId;
    }

    public BigDecimal getUnitRate() {
        if (unitRate == null || unitRate.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ONE;
        }
        return unitRate;
    }

    public void setUnitRate(BigDecimal unitRate) {
        this.unitRate = unitRate;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getWarehouseName() {
        return warehouseName;
    }

    public void setWarehouseName(String warehouseName) {
        this.warehouseName = warehouseName;
    }

    public static StockSyncRule doClone(StockSyncRule rule) {
        return JsonUtils.toObject(JsonUtils.toJson(rule), StockSyncRule.class);
    }

    public EshopWareHouseType getWarehouseType() {
        return warehouseType;
    }

    public void setWarehouseType(EshopWareHouseType warehouseType) {
        this.warehouseType = warehouseType;
    }

    public String getFrozenQtyStr() {
        if (frozenQtyStr == null && getFrozenQty() != null) {
            return String.format("%s(余%s)", getFrozenQty(), getFrozenSaleQty());
        }
        return frozenQtyStr;
    }

    public void setFrozenQtyStr(String frozenQtyStr) {
        this.frozenQtyStr = frozenQtyStr;
    }

    public List<StockRuleDetail> getRelationLockDetailList() {
        return relationLockDetailList;
    }

    public void setRelationLockDetailList(List<StockRuleDetail> relationLockDetailList) {
        this.relationLockDetailList = relationLockDetailList;
    }

    public Boolean getBatchEnabled() {
        if (batchEnabled == null) {
            batchEnabled = false;
        }
        return batchEnabled;
    }

    public void setBatchEnabled(Boolean batchEnabled) {
        this.batchEnabled = batchEnabled;
    }

    public int getProtectDays() {
        return protectDays;
    }

    public void setProtectDays(int protectDays) {
        this.protectDays = protectDays;
    }

    public ProtectDaysUnitEnum getProtectDaysUnit() {
        return protectDaysUnit;
    }

    public void setProtectDaysUnit(ProtectDaysUnitEnum protectDaysUnit) {
        this.protectDaysUnit = protectDaysUnit;
    }

    public List<MultiStockSyncDetail> getMultiStockSyncDetailList() {
        return multiStockSyncDetailList;
    }

    public void setMultiStockSyncDetailList(List<MultiStockSyncDetail> multiStockSyncDetailList) {
        this.multiStockSyncDetailList = multiStockSyncDetailList;
    }

    public List<StockSyncRule> getStoreRuleLsit() {
        return storeRuleLsit;
    }

    public void setStoreRuleLsit(List<StockSyncRule> storeRuleLsit) {
        this.storeRuleLsit = storeRuleLsit;
    }
}

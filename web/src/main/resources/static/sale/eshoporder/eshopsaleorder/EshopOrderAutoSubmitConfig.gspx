<?xml version="1.0" encoding="UTF-8" ?>
<Page xmlns="Craba.UI" Title="自动提交设置"
      ActionType="sale.eshoporder.eshopsaleorder.EshopOrderAutoSubmitConfigAction,sale/eshoporder/eshopsaleorder/EshopOrderAutoSubmitConfig.js"
      CssClass="pd0" AllowResize="false">

    <Style>
         .p:before{content:" ";width:6px;height:6px;border-radius:3px;background-color:#ccc;display:inline-block;margin-right:10px;vertical-align:middle;}
        .tipMsg{background-color:#F0F7FF;border:2px solid #D2E4FF;padding:10px;border-radius:5px;}
    </Style>

    <FlexColumn ID="config"  CssClass="plr20 pt10">
        <VBlock CssClass="tipMsg">
            <Label Text='自动提交的订单包含：已付款的订单、货到付款的订单、月结的订单。' />
            <Label Text='平台发货/代销/不走发货流程的订单，平台发货后，才会自动提交。' />
        </VBlock>

        <HBlock  CssClass="dflexv1" >
            <Label Text='订单自动提交时间：' />
        </HBlock>
        <HBlock>
            <HSpacer Width="30"/>
            <RadioButton  ID="submitNowAfterDownload" Text="下载订单后立即自动提交" DataField="submitNowAfterDownload" OnClick="onSubmitConfigClick"/>
            <HBlock  CssClass="dflexv1" >
                <HSpacer Width="30"/>
                <RadioButton ID="submitTimeAfterPay" Text="订单付款" DataField="submitTimeAfterPay" OnClick="onSubmitConfigClick"/>
                <NumberEdit ID="minuteTime" FormatNumber="true"  MinValue="5" MaxValue="9999999" DataField="minuteTime" NumberType="PositiveInt" Width="60" />
                <Label Text='分钟后，提交' />
            </HBlock>
        </HBlock>



    </FlexColumn >
    <HBlock CssClass="BottomBlock FlexRight">
        <HBlock >
            <Block CssClass='Flex1'/>
            <Button Text="保存" CssClass="SpecialButton" OnClick="doSave"/>
            <CancelButton Text='关闭' ID="cancel"/>
        </HBlock>
    </HBlock>
</Page>
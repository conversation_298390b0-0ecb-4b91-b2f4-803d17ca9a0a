<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopOrderBaseInfoMapper">
    <select id="getAtypeList" parameterType="java.math.BigInteger"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Atype">
        select *
        from base_atype
        where profile_id = #{profileId}
          and deleted = 0
          and stoped = 0
    </select>

    <select id="getBtypeList" parameterType="java.math.BigInteger"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Btype">
        select *
        from base_btype
        where profile_id = #{profileId}
        and deleted = 0
        and stoped = 0
        <if test="freighted!=null and freighted>=0">
            and freighted = #{freighted}
        </if>
    </select>

    <select id="getBtypeById" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Btype">
        select b.*, bbe.price_level
        from base_btype b
                 left join base_btype_extend bbe on bbe.profile_id = b.profile_id and bbe.btype_id = b.id
        where b.id = #{btypeId}
          and b.profile_id = #{profileId}
    </select>

    <select id="getBaseBtypeById" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Btype">
        select *
        from base_btype
        where id = #{btypeId}
          and profile_id = #{profileId}
    </select>

    <select id="getBtypeDeliverInfoById" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Btype">
        SELECT b.id,
               bd.`province`  as `buyer.province`,
               bd.`city`      as `buyer.city`,
               bd.`district`  as `buyer.district`,
               bd.`street`    as `buyer.street`,
               bd.`address`   as `buyer.address`,
               bd.`telephone` as `buyer.telephone`,
               bd.`cellphone` as `buyer.cellphone`,
               bd.`people`    as `buyer.people`
        FROM base_btype b
                 LEFT JOIN base_btype_deliveryinfo bbd ON bbd.profile_id = b.profile_id AND bbd.btype_id = b.id
                 LEFT JOIN base_deliveryinfo bd ON bd.profile_id = bbd.profile_id AND bbd.id = bd.id

        WHERE b.id = #{btypeId}
          AND b.profile_id = #{profileId}
          AND bbd.defaulted = 1
    </select>

    <select id="getFreightBtypeByCodeOrName" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Btype">
        select *
        from base_btype
        where profile_id = #{profileId} and freighted=1
        <if test="code!=null and name!=null">
            and (
            <if test="code!=''">
                usercode=#{code}
            </if>
            <if test="name!='' and code!=''">
                or fullname=#{name}
            </if>
            <if test="name!='' and code==''">
                fullname=#{code}
            </if>
            )
        </if>
        limit 1
    </select>
    <select id="getBtypeByName" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Btype">
        select b.*, bbe.price_level
        from base_btype b
                 left join base_btype_extend bbe on bbe.profile_id = b.profile_id and bbe.btype_id = b.id
        where b.fullname = #{btypeName}
          and b.profile_id = #{profileId}
          and b.deleted = 0
          and b.stoped = 0
          and b.classed = 0
    </select>

    <select id="getEtypeById" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Etype">
        select *
        from base_etype
        where profile_id = #{profileId}
          and id = #{etypeId}
    </select>

    <select id="getUnitPrice" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.UnitPrice">
        select id, profile_id, ptype_id, unit_code, unit_name, unit_rate, barcode,
               ptype_length, ptype_width, ptype_height, ptype_volume, ptype_length_unit, ptype_weight, ptype_weight_unit
        from base_ptype_unit
        where profile_id = #{profileId}
          and id = #{unitId}
    </select>

    <select id="findBaseUnitPrice" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.UnitPrice">
        select *
        from base_ptype_unit
        where profile_id = #{profileId}
          and ptype_id = #{ptypeId}
          and unit_code = 1
    </select>

    <select id="getBaseUnitList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.UnitPrice">
        select * from base_ptype_unit where profile_id=#{profileId} and unit_code=1
        <if test="ptypeIds!=null">
            and ptype_id in
            <foreach collection="ptypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
    </select>

    <select id="getPtypeXcode" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.PtypeXcode">
        select sku.ptype_id, sku.id as skuId, px.xcode, bar.fullbarcode as fullBarcode
        from base_ptype_sku sku
        left join base_ptype_xcode px on sku.id = px.sku_id
        and sku.profile_id = px.profile_id
        and px.unit_id = #{unitId}
        and px.defaulted = 1
        left join base_ptype_fullbarcode bar on bar.profile_id = sku.profile_id
        and bar.sku_id = sku.id
        and bar.unit_id = #{unitId}
        and bar.defaulted = 1
        where sku.profile_id = #{profileId}
        <if test="skuId!=null and skuId>0">
            and sku.id = #{skuId}
        </if>
        <if test="xcode!=null and xcode!=''">
            and px.xcode=#{xcode}
        </if>
        limit 1
    </select>

    <select id="getSinglePtypeXcode" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.PtypeXcode">
        select *
        from base_ptype_xcode
        where profile_id = #{profileId}
          and ptype_id = #{ptypeId}
          and unit_id = #{unitId}
          and sku_id = #{skuId}
          and xcode = #{xcode}
        limit 1
    </select>

    <select id="getPtypeXcodeList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.PtypeXcode">
        select px.ptype_id, px.sku_id, px.xcode, bar.fullbarcode as fullBarcode,bpu.unit_rate,px.unit_id
        from base_ptype_xcode px
        left join base_ptype p on p.id=px.ptype_id and p.profile_id=px.profile_id
        left join base_ptype_fullbarcode bar on bar.profile_id = px.profile_id
        and bar.sku_id = px.sku_id
        and bar.unit_id = px.unit_id
        and bar.defaulted = 1
        left join base_ptype_unit bpu on bpu.profile_id=px.profile_id and bpu.id=px.unit_id
        where px.profile_id = #{profileId} and p.stoped=0 and p.deleted=0
        <if test="skuIdList!=null and skuIdList.size()>0">
            and px.sku_id in
            <foreach collection="skuIdList" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="ptypeId!=null and ptypeId!=''">
            and px.ptype_id=#{ptypeId}
        </if>
        <if test="skuId!=null and skuId!=''">
            and px.sku_id=#{skuId}
        </if>
    </select>

    <select id="getPtypeSku" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.PtypeSkuEntity">
        select *
        from base_ptype_sku sku
        where sku.profile_id = #{profileId} and ptype_id=#{ptypeId}
        <if test="propvalueName1!=null">
            AND sku.propvalue_name1=#{propvalueName1}
        </if>
        <if test="propvalueName2!=null">
            AND sku.propvalue_name2=#{propvalueName2}
        </if>
        <if test="propvalueName3!=null">
            AND sku.propvalue_name3=#{propvalueName3}
        </if>
        <if test="propvalueName4!=null">
            AND sku.propvalue_name4=#{propvalueName4}
        </if>
        <if test="propvalueName5!=null">
            AND sku.propvalue_name5=#{propvalueName5}
        </if>
        <if test="propvalueName6!=null">
            AND sku.propvalue_name6=#{propvalueName6}
        </if>
    </select>

    <select id="getStockList" parameterType="java.math.BigInteger"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Stock">
        select *
        from base_ktype
        where profile_id = #{profileId}
        and deleted = 0
        and scategory=0 and classed=0 and stoped=0
        <if test="ids!=null">
            and id IN
            <foreach collection="ids" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
    </select>

    <select id="getStock" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Stock">
        select *
        from base_ktype
        where profile_id = #{profileId}
          and id = #{id}
          and deleted = 0
    </select>

    <select id="getCurrencyList" parameterType="java.math.BigInteger"
            resultType="java.util.Currency">
        select *
        from base_currency
        where profile_id = #{profileId}
          and stoped = 0
    </select>

    <select id="getEtypeList" parameterType="java.math.BigInteger"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Etype">
        select *
        from base_etype
        where profile_id = #{profileId}
          and stoped = 0
          and deleted = 0
    </select>

    <select id="getAdminEtype" parameterType="java.math.BigInteger"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Etype">
        select *
        from base_etype
        where profile_id = #{profileId}
          and stoped = 0
          and deleted = 0
        and sysid=1 limit 1
    </select>

    <select id="getPropList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Prop">
        select *
        from base_prop
        where profile_id = #{profileId}
    </select>

    <select id="getPropListById" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Prop">
        select *
        from base_prop
        where profile_id = #{profileId} and id =#{Id}
    </select>

    <select id="getPropListByName" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Prop">
        select *
        from base_prop
        where profile_id = #{profileId}
        and deleted = 0
        and stoped = 0
          <if test="propName != null and propName.size()>0">
          and prop_name in
        <foreach collection="propName" close=")" open="(" separator="," item="prop">
            #{prop}
        </foreach>
          </if>
    </select>

    <select id="getEnablePropList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Prop">
        select *
        from base_prop
        where profile_id = #{profileId}
          and deleted = 0
          and stoped = 0
    </select>

    <select id="getPropValueListByPropId" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.PropValueName">
        select *
        from base_propvalue
        where profile_id = #{profileId}
          <if test="id != null">
              and id in
              <foreach collection="id" close=")" open="(" separator="," item="propId">
                  #{propId}
              </foreach>
          </if>
    </select>

    <select id="getPropValueList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.PropValueName">
        select *
        from base_propvalue
        where profile_id = #{profileId}
          and propvalue_name = #{propvalueName}
          and prop_id = #{propId}
    </select>

    <select id="getPropValueByNameList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.PropValueName">
        select id,profile_id,prop_id,propvalue_name,barcode,memo,rowindex,stoped,deleted,create_time,update_time
        from base_propvalue
        where profile_id = #{profileId} and propvalue_name in
        <foreach collection="propvalueName" close=")" open="(" separator="," item="name">
            #{name}
        </foreach>
    </select>

    <select id="getPropValueByNameAndPropId" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.PropValueName">
        select id,
               profile_id,
               prop_id,
               propvalue_name,
               barcode,
               memo,
               rowindex,
               stoped,
               deleted,
               create_time,
               update_time
        from base_propvalue
        where profile_id = #{profileId}
          and propvalue_name = #{propvalueName}
          and prop_id = #{propId}
          and deleted = 0
          limit 1
    </select>

    <select id="queryCombo" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.BasePtypeCombo">
        select combo_id,
               profile_id,
               total,
               need_print_name,
               combo_type,
               single_ptype,
               need_split_detail,
               unique_mark,
               logistics_print_name,
               goods_print_name,
               create_time,
               update_time
        from base_ptype_combo
        where profile_id = #{profileId}
    </select>

    <select id="queryComboByComBoId" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.BasePtypeCombo">
        select combo_id,
               profile_id,
               total,
               need_print_name,
               combo_type,
               single_ptype,
               need_split_detail,
               unique_mark,
               logistics_print_name,
               goods_print_name
        from base_ptype_combo
        where profile_id = #{profileId}
        <if test="comboIds!=null and comboIds.size()>0">
            AND combo_id IN
            <foreach collection="comboIds" close=")" open="(" separator="," item="comboId">
            #{comboId}
            </foreach>
        </if>
    </select>

    <select id="queryComboDetail" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.ComboDetail">
        select detail.id,
               detail.combo_id,
               detail.profile_id,
               detail.ptype_id,
               detail.sku_id,
               detail.unit_id,
               detail.qty,
               detail.price,
               detail.total,
               detail.necessary_sku,
               detail.stoped,
               detail.gifted,
               detail.create_time,
               detail.update_time,
               bu.unit_rate
        from base_ptype_combo_detail detail
                 left join base_ptype_unit bu on bu.profile_id=detail.profile_id and bu.id=detail.unit_id
        where detail.profile_id = #{profileId}
          and detail.combo_id = #{comboId}
    </select>

    <select id="getComboDetails" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.ComboDetail">
        select co.*,p.usercode as comboCode,p.fullname as comboName,
        (case when (temp.labelfieldValue = '代销') then true else false end)  as saleProxyLabel,
        bu.unit_rate, un.id as baseUnitId,bpx.xcode,p.pcategory,dp.tax_rate as taxRate
        from base_ptype_combo_detail co
        left join base_ptype p on p.profile_id=co.profile_id and p.id=co.combo_id and p.pcategory=2
        left join base_ptype dp on dp.profile_id=co.profile_id and dp.id=co.ptype_id
        left join base_ptype_unit bu on bu.profile_id=co.profile_id and bu.id=co.unit_id
        left join base_ptype_xcode bpx on bpx.profile_id=co.profile_id and co.sku_id=bpx.sku_id and
        co.unit_id=bpx.unit_id and bpx.defaulted=1
        left join base_ptype_unit un on un.profile_id=co.profile_id and un.ptype_id=co.ptype_id and un.unit_code=1
        left join (select cdlp.profile_id      as profileId,
        cdlp.resource_id     as ptypeId,
        clv.labelfield_value as labelfieldValue
        from cf_data_label_ptype cdlp
        left join Cf_Labelfield_Value clv
        on clv.id = cdlp.labelfield_value_id
        and clv.profile_id = cdlp.profile_id
        where clv.labelfield_value = '代销'
        and cdlp.profile_id = #{profileId} ) temp on temp.profileId = co.profile_id and temp.ptypeId = co.ptype_id
        where co.profile_id=#{profileId}
        <if test="ptypeId!=null">
            and co.combo_id=#{ptypeId}
        </if>
        <if test="skuId!=null">
            and co.sku_id=#{skuId}
        </if>
        <if test="ptypeIdList!=null and ptypeIdList.size()>0">
            AND co.combo_id IN
            <foreach collection="ptypeIdList" close=")" open="(" separator="," item="pid">
                #{pid}
            </foreach>
        </if>
        <if test="skuIdList!=null and skuIdList.size()>0">
            AND co.sku_id IN
            <foreach collection="skuIdList" close=")" open="(" separator="," item="sid">
                #{sid}
            </foreach>
        </if>
        <if test="!needVirtual">
            and dp.pcategory !=1
        </if>
        order by co.id asc
    </select>

    <select id="getPtypeList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.Ptype">
        select * from base_ptype where profile_id=#{profileId}
        <if test="onlyQueryCombo!=null and onlyQueryCombo==true">
            and pcategory=2
        </if>
        <if test="ptypeIds!=null and ptypeIds.size()>0">
            AND id IN
            <foreach collection="ptypeIds" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getComboPtype" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.Ptype">
        select * from base_ptype where profile_id=#{profileId} and usercode=#{usercode} and pcategory=2 and stoped=0 and deleted=0
    </select>


    <select id="getPtypeById" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.Ptype">
        select b.*,pic.pic_url as platformPicUrl
        from base_ptype b
        left join base_ptype_pic pic on pic.ptype_id = b.id and pic.profile_id = b.profile_id
        where b.profile_id = #{profileId}
          AND b.id = #{ptypeId} limit 1
    </select>

    <select id="getComboByBarCode" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.Ptype">
        select b.*,pic.pic_url as platformPicUrl,
               xcode.xcode as xcode,
               unit.id as unitId,
               unit.unit_name as unitName
        from base_ptype b
                 left join base_ptype_pic pic on pic.ptype_id = b.id and pic.profile_id = b.profile_id
                 left join base_ptype_xcode xcode on xcode.ptype_id = b.id and xcode.profile_id = b.profile_id and xcode.info_type=1 and xcode.defaulted=1
                 left join base_ptype_unit unit on unit.ptype_id = b.id and pic.profile_id = b.profile_id
        where b.profile_id = #{profileId} and b.pcategory = 2
          AND b.barcode = #{barCode}
    </select>

    <select id="getPtype" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.Ptype">
        select *
        from base_ptype
        where profile_id = #{profileid}
          and usercode = #{usercode}
          and fullname = #{fullname}
          and deleted = 0
          and classed = 0
          and pcategory in (0, 1)

    </select>

    <select id="getPtypeByFullname" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.Ptype">
        select *
        from base_ptype
        where profile_id = #{profileid}
          and fullname = #{fullname}
          and deleted = 0
--           and stoped = 0
          and classed = 0
        order by create_time DESC
    </select>

    <select id="getPtypeInfoByUsercode" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.Ptype">
        select *
        from base_ptype
        where profile_id = #{profileid}
          and usercode like concat('%',#{usercode},'%')
          and deleted = 0
          and stoped = 0
          and classed = 0
        order by create_time DESC
    </select>

    <select id="getPtypeByuserCode" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.Ptype">
        select *
        from base_ptype
        where profile_id = #{profileid}
          and usercode = #{usercode}
          and deleted = 0
          and classed = 0
          and pcategory in (0, 1)

    </select>

    <select id="getPtypeByuserCodeAndCategory" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.Ptype">
        select *
        from base_ptype
        where profile_id = #{profileid}
          and usercode = #{usercode}
          and deleted = 0
          and classed = 0
    </select>

    <select id="getPtypeByuserCodeAll" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.Ptype">
        select *
        from base_ptype
        where profile_id = #{profileid}
          and usercode = #{usercode}
          and deleted = 0
          and stoped = 0
          and pcategory = 2
          and classed = 0
    </select>

    <select id="getOtypeClassList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.OtypeClass">
        select *, 0 as pid
        from base_otypeclass
        where profile_id = #{profileId}
    </select>

    <insert id="addOtypeClass" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.OtypeClass">
        INSERT INTO base_otypeclass (profile_id, id, classname, btype_id, create_time)
        VALUES (#{otypeClass.profileId}, #{otypeClass.id}, #{otypeClass.classname}, #{otypeClass.btypeId},
                #{otypeClass.createTime});
    </insert>

    <insert id="addSysData">
        insert into sys_data(id, profile_id, sub_name, sub_value, `description`)
        VALUES (#{id}, #{profileId}, #{subName}, #{subValue}, #{description})
        ON DUPLICATE KEY UPDATE sub_value=#{subValue},`description`=#{description}
    </insert>

    <update id="modifyOtypeClass" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.OtypeClass">
        UPDATE base_otypeclass
        SET classname   = #{otypeClass.classname},
            update_time = #{otypeClass.updateTime}
        WHERE id = #{otypeClass.id}
          and profile_id = #{otypeClass.profileId};
    </update>
    <select id="checkDuiplicateOtypeClass" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.OtypeClass"
            resultType="java.lang.Boolean">
        select count(0) > 0
        from base_otypeclass
        where classname = #{otypeClass.classname}
          and profile_id = #{otypeClass.profileId};
    </select>


    <delete id="deleteOtypeClass" parameterType="java.math.BigInteger">
        delete
        from base_otypeclass
        where profile_id = #{profileId}
          and id = #{Id};
    </delete>

    <select id="getMaxOtypeOrderIndex" parameterType="java.math.BigInteger" resultType="java.lang.Integer">
        select max(rowindex)
        from base_otype
        where profile_id = #{profileId}
          and deleted = 0
          and stoped = 0
    </select>
    <select id="getOtypeOrderIndex" parameterType="java.math.BigInteger" resultType="java.lang.Integer">
        select rowindex
        from base_otype
        where profile_id = #{profileId}
          and id = #{Id}
          and deleted = 0
          and stoped = 0
    </select>

    <select id="getSingleXcode" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.PtypeXcode">
        SELECT
        `id`,
        `ptype_id`,
        `sku_id`,
        `unit_id`,
        `xcode`
        FROM
        `base_ptype_xcode` WHERE profile_id=#{profileId}
        <if test="xcode!=null and xcode!=''">
            and xcode=#{xcode}
        </if>
        <if test="skuId!=null and skuId>0">
            and sku_id=#{skuId}
        </if>
        <if test="unitId!=null and unitId>0">
            and unit_id=#{unitId}
        </if>
    </select>

    <select id="queryOrderStrategyConfig"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.OrderStrategyConfig">
        select c.*
        from pl_bill_strategy_config c
        where profile_id = #{profileId}
          and strategy_type = 50
          and enabled = 1

    </select>
    <select id="getAtype" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Atype">
        select * from base_atype where profile_id=#{profileId}
        <if test="typeId!=null and typeId!=''">
            and typeid=#{typeId}
        </if>
        <if test="fullname!=null and fullname!=''">
            and fullname=#{fullname}
        </if>
        <if test="atypeId!=null and atypeId>0">
            and id=#{atypeId}
        </if>
        limit 1
    </select>

    <select id="getAtypeByFullname" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Atype">
        select *
        from base_atype
        where profile_id = #{profileId}
          and fullname = #{fullname}
--           and typeid LIKE '0000400003%'
          and deleted = 0
          and stoped = 0
          and classed = 0
    </select>

    <update id="changeOtypeOrderIndex" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Otype">
        <if test="otype.changeOrderIndexType==1">
            update base_otype set
            rowindex=rowindex-1
            where rowindex = #{otype.rowindex} and profile_id=#{otype.profileId} and id = #{otype.id} and
            deleted=0;
            update base_otype set
            rowindex=rowindex+1
            where rowindex = #{otype.rowindex}-1 and profile_id=#{otype.profileId} and id != #{otype.id} and
            deleted=0;
        </if>
        <if test="otype.changeOrderIndexType==2">
            update base_otype set
            rowindex=rowindex+1
            where rowindex = #{otype.rowindex} and profile_id=#{otype.profileId} and id = #{otype.id} and
            deleted=0;
            update base_otype set
            rowindex=rowindex-1
            where rowindex = #{otype.rowindex}+1 and profile_id=#{otype.profileId} and id != #{otype.id} and
            deleted=0;
        </if>
        <if test="otype.changeOrderIndexType==3">
            update base_otype set
            rowindex=rowindex+1
            where rowindex &lt; #{otype.rowindex} and profile_id=#{otype.profileId} and id != #{otype.id} and
            deleted=0;
            update base_otype set
            rowindex=0
            where profile_id=#{otype.profileId} and id = #{otype.id} and deleted=0;
        </if>
        <if test="otype.changeOrderIndexType==4">
            update base_otype set
            rowindex=rowindex-1
            where rowindex &gt; #{otype.rowindex} and profile_id=#{otype.profileId} and id != #{otype.id} and
            deleted=0;
            update base_otype set
            rowindex=#{otype.maxIndex}
            where profile_id=#{otype.profileId} and id = #{otype.id} and deleted=0;
        </if>
    </update>

    <update id="changeOtypeOrderOneIndex">
        update base_otype
        set rowindex=#{otype.rowindex}
        where profile_id = #{otype.profileId}
          and id = #{otype.id}
          and deleted = 0;
    </update>
    <update id="updateSysData" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.SysData">
        update sys_data
        set sub_value = #{subValue}
        where profile_id = #{profileId}
          and sub_name = #{subName}
    </update>

    <delete id="deleteOtypes" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.EshopParameter">
        update base_otype set deleted=1 where profile_id=#{params.profileId} and
        id in
        <foreach collection="params.otypeIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteOtype">
        delete
        from base_otype
        where profile_id = #{profileId}
          and id = #{otypeId}
    </delete>

    <delete id="softDeleteOtype">
        update base_otype
        set deleted=1
        where profile_id = #{profileId}
          and id = #{otypeId}
    </delete>

    <select id="queryBasePtypeByPtypeId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.BasePtypeProp">
        SELECT bp.id, bp.profile_id, bp.ptype_id, bp.prop_id, bp.rowindex
        FROM base_ptype_prop bp
        WHERE ptype_id = #{ptypeId}
          AND profile_id = #{profileId}
        ORDER BY rowindex ASC
    </select>
    <select id="getBaseBarcode" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.BaseBarcode">
        select id as barcodeId, profile_id, pcategory
        from base_ptype
        where profile_id = #{profileId}
          and barcode = #{barcode}
        limit 1
    </select>
    <select id="getSkuBarcode" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.BaseBarcode">
        select id as barcodeId, profile_id, 0 as pcategory
        from base_ptype_fullbarcode
        where profile_id = #{profileId}
          and fullbarcode = #{barcode}
        limit 1
    </select>

    <select id="getEtypeByName" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Etype">
        select *
        from base_etype
        where profile_id = #{profileId}
          and fullname = #{etypeName}
          AND stoped = 0
          AND deleted = 0
        limit 1
    </select>

    <select id="getStockByName"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Stock">
        select *
        from base_ktype
        where profile_id = #{profileId}
          and fullname = #{stockName}
          and deleted = 0
          and stoped = 0
          and classed=0
        limit 1
    </select>

    <select id="getBaseKtypeListByProfileId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Stock">
        select *
        from base_ktype bk
        <if test="param.ktypeLimited">
            inner join base_limit_scope bls on bls.profile_id=bk.profile_id and object_type=2 and
            bk.id=bls.object_id and bls.etype_id=#{param.etypeId}
        </if>
        where bk.profile_id = #{param.profileId}
        and bk.deleted = 0
        and bk.stoped = 0
        and bk.classed = 0
        and bk.scategory in (0,1,2)
    </select>

    <select id="getBaseKtypeListByProfileIdForRefund"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Stock">
        select *
        from base_ktype bk
        <if test="param.ktypeLimited">
            inner join base_limit_scope bls on bls.profile_id=bk.profile_id and object_type=2 and
            bk.id=bls.object_id and bls.etype_id=#{param.etypeId}
        </if>
        where bk.profile_id = #{param.profileId}
        and bk.deleted = 0
        and bk.stoped = 0
        and bk.classed = 0
        and bk.scategory in (0,1,2)
    </select>

    <select id="getAdminBaseKtypeByProfileId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Stock">
        select *
        from base_ktype bk
        where bk.profile_id = #{profileId}
        and bk.deleted = 0
        and bk.stoped = 0
        and bk.sysrow =1 limit 1
    </select>

    <select id="getSysData" resultType="java.lang.String">
        select sub_value
        from sys_data
        where profile_id = #{profileId}
          and sub_name = #{subName}
    </select>
    <select id="getPtypeSkuByPtypeId" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.PtypeSkuEntity">
        select *
        from base_ptype_sku sku
        where sku.profile_id = #{profileId}
          and ptype_id = #{ptypeId}
    </select>

    <select id="getNoSkuByPtypeId" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.PtypeSkuEntity">
        select *
        from base_ptype_sku sku
        where sku.profile_id = #{profileId}
        and ptype_id = #{ptypeId}
        <if test="propertise !=null and propertise!=''">
            and propvalue_name1=#{propertise}
        </if>
        limit 1
    </select>
    <select id="getEtype" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Etype">
        select e.*, d.fullname as dtypeName
        from base_etype e
        left join base_dtype d on e.profile_id = d.profile_id and e.dtype_id = d.id
        where e.profile_id = #{profileId}
        <if test="typeId !=null and typeId!=''">
            and e.id = #{typeId}
        </if>
        <if test="mobile !=null and mobile!=''">
            and (e.mobile = #{mobile}
            <if test="fullname !=null and fullname!=''">
                or e.fullname=#{fullname}
            </if>
            )
        </if>
        <if test="ignoreDelete=true">
            and e.deleted=false
        </if>
        <if test="userCode!=null and userCode!=''">
            and e.usercode=#{userCode}
        </if>
        limit 1

    </select>

    <select id="getFreightTemplates" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.LogisticsTemplate">
        SELECT
        temp.id as template_id,
        temp.`profile_id`,
        temp.template_name,
        lg.token_id as eshopId,
        lg.freight_btype_id,
        btype.fullname as freight_btype_name
        FROM `td_template` temp
        LEFT JOIN `td_logistics_template` lg ON temp.`id`=lg.`template_id` AND temp.`profile_id`=lg.`profile_id`
        LEFT JOIN pl_eshop es on es.profile_id=temp.profile_id and es.otype_id=lg.token_id
        LEFT JOIN td_logistics_account_token ac on lg.token_id=ac.id and temp.profile_id=ac.profile_id
        LEFT JOIN base_btype btype on btype.profile_id=temp.profile_id and btype.id=lg.freight_btype_id
        WHERE temp.profile_id=#{profileId} AND temp.`deleted`=0 and template_type=0
        <if test="btypeId!=null and btypeId>0">
            lg.freight_btype_id=#{btypeId}
        </if>
    </select>
    <select id="getStockLimited" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Stock">
        select *
        from base_ktype b
        <if test="ktypeLimited">
            inner join base_limit_scope bls on bls.profile_id=b.profile_id and object_type=2 and
            b.id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where b.profile_id = #{profileId}
        and b.deleted = 0
        and b.stoped = 0 and b.classed=0
    </select>

    <select id="getDefaultStock" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Stock">
        select *
        from base_ktype b
        where b.profile_id = #{profileId}
        and b.deleted = 0
        and b.stoped = 0 and b.classed=0 and b.sysrow=1 limit 1
    </select>
    <select id="getBillCount" resultType="java.lang.Integer">
        select count(*)
        from acc_bill_assinfo
        where profile_id = #{profileId}
          and source_id = #{vchcode}
    </select>
    <select id="checkAtypeDeleted" resultType="boolean">
        select count(0) > 0
        from base_atype
        where profile_id = #{profileId}
          and id = #{id}
          and deleted = 0
          and stoped = 0
    </select>

    <select id="queryGoodsStock" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.GoodsStock">
        select *
        from acc_goodsstock_detail
        where profile_id = #{profileId}
          and sku_id = #{skuId}
          and ktype_id = #{ktypeId}
    </select>

    <select id="queryGoodsStockList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.GoodsStock">
        select *
        from acc_goodsstock_detail
        where profile_id = #{profileId}
        <if test="skuId!=null">
            and sku_id=#{skuId}
        </if>
        <if test="ktypeId!=null">
            and ktype_id=#{ktypeId}
        </if>
        <if test="skuIdList!=null and skuIdList.size()>0">
            and sku_id in
            <foreach collection="skuIdList" close=")" open="(" separator="," item="skuId">
                #{skuId}
            </foreach>
        </if>
        <if test="ktypeIdList!=null and ktypeIdList.size()>0">
            and ktype_id in
            <foreach collection="ktypeIdList" close=")" open="(" separator="," item="kId">
                #{kId}
            </foreach>
        </if>
    </select>

    <select id="batchQueryComboDetail" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.ComboDetail">
        select id,
        combo_id,
        profile_id,
        ptype_id,
        sku_id,
        unit_id,
        qty,
        price,
        total,
        necessary_sku,
        stoped,
        gifted,
        create_time,
        update_time
        from base_ptype_combo_detail
        where profile_id = #{profileId}
        <if test="comboIds!=null and comboIds.size()>0">
            and combo_id in
            <foreach collection="comboIds" close=")" open="(" separator="," item="comboId">
                #{comboId}
            </foreach>
        </if>
    </select>
    <select id="getPtypeSkuByPtypeIdsCount" resultType="java.lang.Integer">
        select count(*)
        from base_ptype_sku
        where profile_id = #{profileId} and stoped=0 and deleted=0
        <if test="ptypeIds!=null and ptypeIds.size()>0">
            and ptype_id in
            <foreach collection="ptypeIds" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="getPtypeSkuByPtypeIds" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.PtypeSkuEntity">
        select id,ptype_id,profile_id
        from base_ptype_sku
        where profile_id = #{profileId} and stoped=0 and deleted=0
        <if test="ptypeIds!=null and ptypeIds.size()>0">
            and ptype_id in
            <foreach collection="ptypeIds" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="getPtypeByPtypeIdLimit1" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.Ptype">
        select *
        from base_ptype t1
        where t1.profile_id = #{profileId}
          and t1.id = #{ptypeId}
        limit 1
    </select>
    <select id="getPtypeSkuBySkuIdLimit1"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.PtypeSkuEntity">
        select t1.*
        from base_ptype_sku t1
        where t1.profile_id = #{profileId}
          and t1.id = #{skuId}
        limit 1
    </select>
    <select id="getAllStockList" parameterType="java.math.BigInteger"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Stock">
        select *
        from base_ktype
        where profile_id = #{profileId}
        and deleted = 0 and stoped=0
        <if test="ids!=null and ids.size()>0">
            and id IN
            <foreach collection="ids" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
    </select>

    <select id="getOpenXcodeEshopList" resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select e.otype_id, e.fullname, e.profile_id, e.eshop_type
        from pl_eshop e
        where e.profile_id = #{profileId}
          and e.stoped = 0
          and e.deleted = 0
    </select>
    <select id="getFreightBtypeByName" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Btype">
        select *
        from base_btype
        where profile_id = #{profileId}
          and freighted = 1
          and fullname = #{name}
        limit 1
    </select>

    <select id="getPayWaysByFullName" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.PayWays">
        select id,fullname,profile_id,create_time,update_time,deleted from base_payways where profile_id=#{profileId} and fullname=#{fullname} and deleted=0 limit 1
    </select>

    <select id="getEtypeByOrderBtype" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Etype">
    select
    a.profile_id as profileId,a.id as id,a.fullname as fullname,
    d.id as dtypeId ,d.fullname as dtypeName
    from  base_btype_extend b
    left join base_etype a on a.id=b.etype_id and a.profile_id=#{profileId}
    left join base_dtype d on d.profile_id=b.profile_id and a.dtype_id=d.id
    where b.profile_id=#{profileId} and b.btype_id=#{btypeId}
    </select>

    <select id="getDtypeByEtypeId" resultType="com.wsgjp.ct.baseinfo.core.dao.entity.Dtype">
        select d.* from base_dtype d
        left join base_etype e on d.profile_id=e.profile_id and d.id=e.dtype_id
        where d.profile_id=#{profileId} and e.id=#{etypeId} and d.deleted=0
    </select>

    <select id="getBtypeIdFromMappingByDistributorName" resultType="java.math.BigInteger">
        select btype_id from pl_eshop_platform_btype_mapping where profile_id=#{profileId} and platform_btype_name=#{fullname} and deleted=0 limit 1
    </select>

    <select id="getBtypeIdFromMappingByAi" resultType="java.math.BigInteger">
        select btype_id from pl_eshop_platform_btype_mapping where  profile_id=#{profileId} and platform_btype_unique_id=#{ai} and deleted=0 limit 1
    </select>

    <select id="getBtypeIdFromMappingByUniqueId" resultType="java.lang.Boolean">
        select 1 from pl_eshop_platform_btype_mapping where  profile_id=#{profileId} and platform_btype_unique_id=#{uniqueId} and deleted=0 limit 1
    </select>

    <select id="getBtypeIdByFullName" resultType="java.math.BigInteger">
        select id from base_btype where  profile_id=#{profileId} and fullname=#{fullname} and deleted=0 and stoped=0 and freighted=0 and classed=0 limit 1;
    </select>
    <select id="getSysDataPO" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.SysData">
        select *
        from sys_data
        where profile_id = #{profileId}
          and sub_name = #{subName}
    </select>

    <select id="getSenderByOtypeId" resultType="com.wsgjp.ct.sale.biz.jarvis.entity.Sender">
        select bd.people    as senderName,
                    bd.cellphone as senderMobile,
                    bd.telephone as senderPhone,
                    bd.address   as senderAddress,
                    bd.province  as senderProvince,
                    bd.city      as senderCity,
                    bd.district  as senderDistrict,
                    bd.street    as senderTown
            from base_deliveryinfo bd
         left join base_otype bo on bd.id = bo.deliver_id and bd.profile_id = bo.profile_id
        where bd.profile_id = #{profileId}
        and bo.id = #{otypeId}
        limit 1;
    </select>

    <select id="checkQuote" resultType="boolean">
         select count(0) > 0 from ${table} where profile_id=#{profileId} and ${column} = #{id} limit 1
    </select>

    <select id="checkDetailQuoteByAdvance" resultType="boolean">
        SELECT count(0) > 0 FROM td_orderbill_detail_core c JOIN td_orderbill_detail_platform p ON c.profile_id=p.profile_id AND c.detail_id=p.detail_id
        WHERE c.profile_id=#{profileId} AND c.${column}=#{id} limit 1
    </select>

    <select id="checkMainQuoteByAdvance" resultType="boolean">
        SELECT count(0) > 0 FROM td_orderbill_core c JOIN td_orderbill_platform p ON c.profile_id=p.profile_id AND c.vchcode=p.vchcode
        WHERE c.profile_id=#{profileId} AND c.${column}=#{id} limit 1
    </select>
    <select id="getComboDetailsForStockSync"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.ComboDetail">
        select co.*,p.usercode as comboCode,p.fullname as comboName,
        (case when (temp.labelfieldValue = '代销') then true else false end)  as saleProxyLabel,
        bu.unit_rate, un.id as baseUnitId,bpx.xcode,p.pcategory,dp.tax_rate as taxRate
        from base_ptype_combo_detail co
        left join base_ptype p on p.profile_id=co.profile_id and p.id=co.combo_id and p.pcategory=2
        left join base_ptype dp on dp.profile_id=co.profile_id and dp.id=co.ptype_id
        left join base_ptype_unit bu on bu.profile_id=co.profile_id and bu.id=co.unit_id
        left join base_ptype_xcode bpx on bpx.profile_id=co.profile_id and co.sku_id=bpx.sku_id and
        co.unit_id=bpx.unit_id and bpx.defaulted=1
        left join base_ptype_unit un on un.profile_id=co.profile_id and un.ptype_id=co.ptype_id and un.unit_code=1
        left join pl_eshop_ptype_stock_sync_rule_config rc on rc.profile_id = co.profile_id and rc.sku_id=co.sku_id and rc.unit_id=co.unit_id
        left join (select cdlp.profile_id      as profileId,
        cdlp.resource_id     as ptypeId,
        clv.labelfield_value as labelfieldValue
        from cf_data_label_ptype cdlp
        left join Cf_Labelfield_Value clv
        on clv.id = cdlp.labelfield_value_id
        and clv.profile_id = cdlp.profile_id
        where clv.labelfield_value = '代销'
        and cdlp.profile_id = #{profileId} ) temp on temp.profileId = co.profile_id and temp.ptypeId = co.ptype_id
        where co.profile_id=#{profileId}   and rc.sku_id is null
        <if test="ptypeId!=null">
            and co.combo_id=#{ptypeId}
        </if>
        <if test="skuId!=null">
            and co.sku_id=#{skuId}
        </if>
        <if test="ptypeIdList!=null and ptypeIdList.size()>0">
            AND co.combo_id IN
            <foreach collection="ptypeIdList" close=")" open="(" separator="," item="pid">
                #{pid}
            </foreach>
        </if>
        <if test="skuIdList!=null and skuIdList.size()>0">
            AND co.sku_id IN
            <foreach collection="skuIdList" close=")" open="(" separator="," item="sid">
                #{sid}
            </foreach>
        </if>
    </select>

    <select id="getMarkListByProfileId" resultType="com.wsgjp.ct.common.enums.core.entity.OrderMarkInfo">
        SELECT id AS code ,mark_name AS name,  mark_color AS color FROM base_user_mark WHERE profile_id=#{profileId}
    </select>

    <select id="getBtypeDeliverInfoByIds" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Btype">
        SELECT b.id,
               bd.`province`  as `buyer.province`,
               bd.`city`      as `buyer.city`,
               bd.`district`  as `buyer.district`,
               bd.`street`    as `buyer.street`,
               bd.`address`   as `buyer.address`,
               bd.`telephone` as `buyer.telephone`,
               bd.`cellphone` as `buyer.cellphone`,
               bd.`people`    as `buyer.people`
        FROM base_btype b
                 LEFT JOIN base_btype_deliveryinfo bbd ON bbd.profile_id = b.profile_id AND bbd.btype_id = b.id
                 LEFT JOIN base_deliveryinfo bd ON bd.profile_id = bbd.profile_id AND bbd.id = bd.id

        WHERE b.id in
        <foreach collection="btypeIds" close=")" open="(" separator="," item="key">
            #{key}
        </foreach>
          AND b.profile_id = #{profileId}
          AND bbd.defaulted = 1
    </select>
    <select id="getGoodStockBatchFirst"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderDetailBatch">
        SELECT id,
               profile_id,
               ktype_id,
               ptype_id,
               sku_id,
               batchno,
               produce_date,
               expire_date,
               qty,
               sub_qty,
               cost_id,
               create_time,
               update_time,
               price,
               total,
               batch_time,
               batch_hash,
               batch_price
        FROM acc_goodsstock_batch
        WHERE profile_id = #{profileId}
          AND ktype_id = #{ktypeId}
          AND batchno = #{batchno}
          AND ptype_id = #{ptypeId}
          AND sku_id = #{skuId}
        LIMIT 1
    </select>

    <select id="findUnit" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.UnitPrice">
        select *
        from base_ptype_unit
        where profile_id = #{profileId}
          and ptype_id = #{ptypeId}
        AND id=#{unitId}
        limit 1
    </select>

    <select id="getPubBillSettings" resultType="com.wsgjp.ct.sale.biz.jarvis.bill.config.PubBillSettings">
        SELECT setting_key, vchtype,
        business_type, order_sale_mode, setting_value,
        custom_type, control_type, stoped
        FROM pub_bill_settings
        WHERE profile_id=#{profileId} and setting_key in
        <foreach collection="keys" separator="," close=")" open="(" item="key">
            #{key}
        </foreach>
    </select>
</mapper>
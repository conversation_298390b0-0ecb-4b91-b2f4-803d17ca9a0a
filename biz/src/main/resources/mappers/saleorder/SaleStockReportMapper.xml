<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.SaleStockReportMapper">
    <select id="list" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.stock.SaleStockReportDto">
        select * from (
        <include refid="listDataQuery" />
        ) t
        where 1= 1
        <include refid="query-filter-or">
            <property name="_list" value="request.queryParams.filter"/>
        </include>
        <include refid="qtyFilterQuery" />
    </select>

    <select id="listSummary"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.stock.SaleStockReportDto">
        select
        sum(inventory_inner_qty) as inventory_inner_qty,
        sum(inventory_distribution_qty) as inventory_distribution_qty,
        sum(inventory_trans_qty) as inventory_trans_qty,
        sum(inventory_usage_sale_qty) as inventory_usage_sale_qty,
        sum(saleable_qty) as saleable_qty
        from (
        <include refid="listDataQuery" />
        ) t
        where 1= 1
        <include refid="query-filter-or">
            <property name="_list" value="request.queryParams.filter"/>
        </include>
        <include refid="qtyFilterQuery" />
    </select>

    <sql id="listDataQuery">
        SELECT
        bp.fullname AS p_fullname,
        bp.id as ptypeId,
        bps.id as sku_id,
        bp.sku_price,
        bp.usercode,
        bps.prop_names,
        bps.propvalue_names,
        bp.shortname,
        <choose>
            <when test="request.queryParams.ptypeType == true or request.queryParams.skuType == true">
                bpf.fullbarcode,
            </when>
            <when test="request.queryParams.comboType == true">
                bp.barcode as fullbarcode,
            </when>
        </choose>
        bp.standard,
        bp.ptype_type,
        bb.brand_name,
        ai.ktype_id,
        <if test="request.queryParams.batched == 0">
            <if test="request.queryParams.ptypeType == true or request.queryParams.comboType == true">
                ifnull(
                CASE
                bp.sku_price
                WHEN 0 THEN
                avg( ppri.retail_price )
                WHEN 1 THEN
                SUM( skupri.retail_price ) / ( SUM( CASE WHEN skupri.retail_price > 0 THEN 1 ELSE 0 END ) ) ELSE 0
                END,0) AS retail_price,
                bpp.pic_url,
                0 as skuName,
            </if>
            <if test="request.queryParams.skuType == true">
                ifnull(
                case bp.sku_price when 0 then avg(ppri.retail_price) when 1 then skupri.retail_price end,0
                ) as retail_price,
                case bps.pic_url when '' then bpp.pic_url else bps.pic_url end as pic_url,
                case bps.pic_url when '' then 0 else 1 end as skuName,
            </if>
            position.position,
            IFNULL(sum(ai.record_qty),0) / pu.unit_rate as inventory_usage_sale_qty,
            IFNULL(sum( CASE ai.ktype_point_type WHEN 0 THEN ai.qty END ),0)/ pu.unit_rate
            <if test="request.queryParams.calcTransQtyOfSaleableQty == true">
                + IFNULL( sum( CASE ai.ktype_point_type WHEN 1 THEN ai.qty END ), 0 )/ pu.unit_rate
            </if>
            - IFNULL(sum(ai.record_qty),0)/ pu.unit_rate  AS saleable_qty,
        </if>
        <if test="request.queryParams.batched == 1">
            case bp.sku_price when 0 then avg(ppri.retail_price) when 1 then skupri.retail_price end as retail_price,
            case bps.pic_url when '' then bpp.pic_url else bps.pic_url end as pic_url,
            case bps.pic_url when '' then 0 else 1 end as skuName,
            ai.batchno,
            ai.produce_date,
            ai.expire_date,
            CASE WHEN bp.batchenabled=0 THEN null
            when ai.produce_date is not null and ai.expire_date is not null then
            CONCAT(bp.protect_days_view,case when bp.protect_days_unit = 0 then '天'
            when bp.protect_days_unit = 1 then '周' when bp.protect_days_unit = 2 then '个月' when bp.protect_days_unit = 3 then '年' end)
            end as sell_by_date,
            IFNULL(sum(ai.recordQty),0) / pu.unit_rate as inventory_usage_sale_qty,
            IFNULL(sum( CASE ai.ktype_point_type WHEN 0 THEN ai.qty END ),0)/ pu.unit_rate
            <if test="request.queryParams.calcTransQtyOfSaleableQty == true">
                + IFNULL( sum( CASE ai.ktype_point_type WHEN 1 THEN ai.qty END ), 0 )/ pu.unit_rate
            </if>
            - IFNULL(sum(ai.recordQty),0) / pu.unit_rate AS saleable_qty,
            IF(bp.batchenabled = 1 and bp.cost_mode = 1, ai.batch_price, null) as batch_price,
        </if>
        bp.ptype_area,
        bp.memo as ptype_memo,
        CASE
        bp.stoped
        WHEN 1 THEN
        '停用' ELSE '启用'
        END AS STATUS,
        pu.unit_name,
        IFNULL( sum( CASE ai.ktype_point_type WHEN 0 THEN ai.qty END ),0) / pu.unit_rate AS inventory_inner_qty,
        IFNULL( sum( CASE ai.ktype_point_type WHEN 312 THEN ai.qty END ), 0 ) / pu.unit_rate AS inventory_distribution_qty,
        IFNULL( sum( CASE ai.ktype_point_type WHEN 1 THEN ai.qty END ), 0 ) / pu.unit_rate AS inventory_trans_qty,
        <include refid="pub_custom_field_baseinfo_ptype_column"/>
        FROM
        base_ptype bp
        <include refid="pub_custom_field_baseinfo_ptype_leftjoin">
            <property name="table-alias" value="bp"/>
        </include>
        LEFT JOIN base_ptype_pic bpp ON bp.profile_id = bpp.profile_id
        AND bp.id = bpp.ptype_id
        AND bpp.rowindex = 1
        LEFT JOIN base_ptype_sku bps ON bp.profile_id = bps.profile_id
        AND bp.id = bps.ptype_id
        LEFT JOIN base_ptype_unit pu ON pu.ptype_id = bp.id
        AND pu.profile_id = bp.profile_id
        LEFT JOIN base_ptype_fullbarcode bpf ON bps.profile_id = bpf.profile_id
        AND pu.id = bpf.unit_id and bps.id = bpf.sku_id
        AND bpf.defaulted = 1
        LEFT JOIN base_brandtype bb ON bp.profile_id = bb.profile_id
        AND bp.brand_id = bb.id
        LEFT JOIN base_ptype_price ppri ON ppri.profile_id = bp.profile_id
        AND ppri.unit_id = pu.id
        AND ppri.ptype_id = bp.id
        AND ppri.sku_id = 0
        AND bp.sku_price = 0
        LEFT JOIN base_ptype_price skupri ON skupri.profile_id = bp.profile_id
        AND skupri.unit_id = pu.id
        AND skupri.sku_id = bps.id
        AND bp.sku_price = 1
        <include refid="limit-ptype">
            <property name="limit-alias-id" value="bp.id"/>
            <property name="limit-alias-profileid" value="bp.profile_id"/>
        </include>
        <if test="request.queryParams.batched == 1">
            left join (
            select
            "" as batchno,
            NULL AS produce_date,
            NULL AS expire_date,
            sum(aid.qty) AS qty,
            0 as recordQty,
            0 as batch_price,
            aid.sku_id,
            aid.ptype_id,
            aid.profile_id,
            ktype_id,
            ktype_point_id,
            ktype_point_type,
            0 as sale_out_qty,0 as bill_out_qty,0 as deliver_out_qty,0 as trans_out_qty,0 as other_out_qty,0 as purc_in_qty,0 as trans_in_qty,
            0 as other_in_qty
            from
            base_ptype bp right join  acc_inventory_detail aid ON bp.id = aid.ptype_id
            AND bp.id = aid.ptype_id and bp.batchenabled = 0
            AND bp.profile_id = aid.profile_id
            WHERE
            bp.profile_id = #{profileId}
            <if test="request.queryParams != null and request.queryParams.ktypeIds != null">
                and ktype_id in
                <foreach collection="request.queryParams.ktypeIds" separator="," close=")" open="(" item="ktypeId">
                    #{ktypeId}
                </foreach>
            </if>
            GROUP BY
            aid.ptype_id,
            aid.sku_id,
            aid.ktype_point_id,
            aid.ktype_point_type
            UNION
            select
            batchno,
            produce_date,
            expire_date,
            qty  AS qty,
            0 as recordQty,
            batch_price,
            sku_id,
            ptype_id,
            profile_id,
            ktype_id,
            ktype_point_id,
            ktype_point_type,
            0 as sale_out_qty,0 as bill_out_qty,0 as deliver_out_qty,0 as trans_out_qty,0 as other_out_qty,0 as purc_in_qty,0 as trans_in_qty,
            0 as other_in_qty
            from
            acc_inventory_batch
            WHERE
            profile_id = #{profileId}
            <if test="request.queryParams != null and request.queryParams.ktypeIds != null">
                and ktype_id in
                <foreach collection="request.queryParams.ktypeIds" separator="," close=")" open="(" item="ktypeId">
                    #{ktypeId}
                </foreach>
            </if>
            UNION ALL
            SELECT
            s.batchno,
            s.produce_date,
            s.expire_date,
            0 AS qty,
            s.qty as recordQty,
            s.batch_price,
            s.sku_id,
            s.ptype_id,
            s.profile_id,
            s.ktype_id,
            0 as ktype_point_id,
            0 as ktype_point_type,
            summ.sale_out_qty,summ.bill_out_qty,summ.deliver_out_qty,summ.trans_out_qty,summ.other_out_qty,summ.purc_in_qty,summ.trans_in_qty,
            summ.other_in_qty
            FROM
            stock_record_qty_sale_batch s
            LEFT JOIN stock_sale_qty_source_summary summ on s.profile_id = summ.profile_id and s.ktype_id = summ.ktype_id
            and s.ptype_id = summ.ptype_id and s.sku_id = summ.sku_id
            WHERE
            s.profile_id = #{profileId} and s.qty != 0 and s.ktype_id <![CDATA[ > ]]> 0
            <if test="request.queryParams != null and request.queryParams.ktypeIds != null">
                and s.ktype_id in
                <foreach collection="request.queryParams.ktypeIds" separator="," close=")" open="(" item="ktypeId">
                    #{ktypeId}
                </foreach>
            </if>
            ) ai
            on bp.profile_id = ai.profile_id and bp.id = ai.ptype_id and bps.id = ai.sku_id
        </if>
        <if test="request.queryParams.batched == 0">
            LEFT JOIN (
            select
            ptype_id,sku_id,0 as record_qty ,sum(qty) as qty,profile_id,ktype_id,ktype_point_id,ktype_point_type,
            0 as sale_out_qty,0 as bill_out_qty,0 as deliver_out_qty,0 as trans_out_qty,0 as other_out_qty,0 as purc_in_qty,0 as trans_in_qty,
            0 as other_in_qty
            from acc_inventory_detail where profile_id = #{profileId}
            <if test="request.queryParams != null and request.queryParams.ktypeIds != null">
                and ktype_id in
                <foreach collection="request.queryParams.ktypeIds" separator="," close=")" open="(" item="ktypeId">
                    #{ktypeId}
                </foreach>
            </if>
            GROUP BY
            <if test="request.queryParams.skuType == true">
                ptype_id,sku_id,
            </if>
            <if test="request.queryParams.ptypeType == true or request.queryParams.comboType == true">
                ptype_id,
            </if>
            ktype_point_id,ktype_point_type
            union all
            select s.ptype_id,s.sku_id,sum(s.qty) as record_qty,0 as qty,s.profile_id,s.ktype_id,0 as ktype_point_id,0 as ktype_point_type,
            summ.sale_out_qty,summ.bill_out_qty,summ.deliver_out_qty,summ.trans_out_qty,summ.other_out_qty,summ.purc_in_qty,summ.trans_in_qty,
            summ.other_in_qty
            from stock_record_qty_sale s
            LEFT JOIN stock_sale_qty_source_summary summ on s.profile_id = summ.profile_id and s.ktype_id = summ.ktype_id
            and s.ptype_id = summ.ptype_id and s.sku_id = summ.sku_id
            where s.profile_id = #{profileId} and s.qty != 0 and s.ktype_id <![CDATA[ > ]]> 0
            <if test="request.queryParams != null and request.queryParams.ktypeIds != null">
                and s.ktype_id in
                <foreach collection="request.queryParams.ktypeIds" separator="," close=")" open="(" item="ktypeId">
                    #{ktypeId}
                </foreach>
            </if>
            GROUP BY
            <if test="request.queryParams.skuType == true">
                s.ptype_id,s.sku_id
            </if>
            <if test="request.queryParams.ptypeType == true or request.queryParams.comboType == true">
                s.ptype_id
            </if>
            ) ai ON bp.id = ai.ptype_id and bps.id = ai.sku_id
            AND bp.profile_id = ai.profile_id

            LEFT JOIN (select bp.id, bp.fullname, bp.profile_id, group_concat(DISTINCT position) as `position`
            <if test="request.queryParams.skuType == true">
                ,tppr.sku_id
            </if>
            from base_ptype bp
            left join td_ptype_position_relation tppr
            on bp.profile_id = tppr.profile_id and bp.id = tppr.ptype_id
            left join base_ktype_position bkp
            on bp.profile_id = bkp.profile_id and tppr.position_id = bkp.id
            where bp.profile_id = #{profileId}
            <if test="request.queryParams != null and request.queryParams.ktypeIds != null">
                and bkp.ktype_id in
                <foreach collection="request.queryParams.ktypeIds" separator="," close=")" open="(" item="ktypeId">
                    #{ktypeId}
                </foreach>
            </if>
            group by
            <if test="request.queryParams.skuType == true">
                tppr.sku_id
            </if>
            <if test="request.queryParams.ptypeType == true or request.queryParams.comboType == true">
                tppr.ptype_id
            </if>) as `position`
            on `position`.id = bp.id and `position`.profile_id = bp.profile_id
            <if test="request.queryParams.skuType == true">
                and bps.id = position.sku_id
            </if>

        </if>
        <!--<if test="request.queryParams != null and request.queryParams.ktypeIds != null">
            and ai.ktype_id in
            <foreach collection="request.queryParams.ktypeIds" separator="," close=")" open="(" item="ktypeId">
                #{ktypeId}
            </foreach>
        </if>-->
        WHERE
        <include refid="baseQuery" />
        <if test="request.queryParams.batched == 0">
            GROUP BY
            <if test="request.queryParams.skuType == true">
                bps.id
            </if>
            <if test="request.queryParams.ptypeType == true or request.queryParams.comboType == true">
                bp.id
            </if>
        </if>
        <if test="request.queryParams.batched == 1">
            GROUP BY
            bps.ptype_id,
            bps.id ,
            ai.batchno,
            ai.produce_date,
            ai.expire_date,
            ai.batch_price
        </if>
        order by bp.create_time desc
    </sql>

    <sql id="baseQuery">
        bp.profile_id = #{profileId}
        <include refid="ptype-storage-filter">
            <property name="ptype" value="bp"/>
        </include>
        <include refid="limit-where-ptype">
            <property name="limit-alias-id" value="bp.id"/>
        </include>
        <choose>
            <when test="request.queryParams.partypeid == '00000'">
                and bp.partypeid = #{request.queryParams.partypeid}
            </when>
            <when test="request.queryParams.partypeid != null and request.queryParams.partypeid != ''">
                and bp.typeid like concat(#{request.queryParams.partypeid},'%')
            </when>
        </choose>
        <if test="request.queryParams.filterValue !=null and  request.queryParams.filterValue!=''">
            <bind name="filter" value="'%'+request.queryParams.filterValue+'%'"></bind>
            <if test="request.queryParams.filterKey == 'quick'.toString()">
                and (bp.fullname like #{filter}
                or bp.memo like #{filter}
                or bp.usercode like #{filter}
                <choose>
                    <when test="request.queryParams.ptypeType == true or request.queryParams.skuType == true">
                        or bpf.fullbarcode like #{filter}
                    </when>
                    <when test="request.queryParams.comboType == true">
                        or bp.barcode like #{filter}
                    </when>
                </choose>
                <if test="request.queryParams.ptypeType == true or request.queryParams.comboType == true" >
                    <if test="unitids != null and unitids.size()>0">
                        or pu.id in
                        <foreach collection="unitids" index="index" item="item" open="(" separator="," close=")">
                            #{item.id}
                        </foreach>
                    </if>
                </if>
                <if test="request.queryParams.skuType == true || request.queryParams.batched == 1">
                    <if test="unitids != null and unitids.size()>0">
                        or (pu.id,bps.id) in
                        <foreach collection="unitids" item="item" separator="," open="(" close=")">
                            (#{item.id},#{item.skuId})
                        </foreach>
                    </if>
                </if>
                )
            </if>
            <if test="request.queryParams.filterKey == 'skuxcode'.toString()">
                <if test="request.queryParams.ptypeType == true or request.queryParams.comboType == true" >
                    <if test="unitids != null and unitids.size()>0">
                        and pu.id in
                        <foreach collection="unitids" index="index" item="item" open="(" separator="," close=")">
                            #{item.id}
                        </foreach>
                    </if>
                </if>
                <if test="request.queryParams.skuType == true || request.queryParams.batched == 1">
                    <if test="unitids != null and unitids.size()>0">
                        and (pu.id,bps.id) in
                        <foreach collection="unitids" item="item" separator="," open="(" close=")">
                            (#{item.id},#{item.skuId})
                        </foreach>
                    </if>
                </if>
            </if>
            <if test="request.queryParams.filterKey == 'fullname'.toString()">
                and bp.fullname like #{filter}
            </if>
            <if test="request.queryParams.filterKey == 'usercode'.toString()">
                and bp.usercode like #{filter}
            </if>
            <if test="request.queryParams.filterKey == 'memo'.toString()">
                and bp.memo like #{filter}
            </if>
            <if test="request.queryParams.filterKey == 'sku'.toString()">
                <if test="request.queryParams.ptypeType == true" >
                    <if test="unitids != null and unitids.size()>0">
                        and pu.id in
                        <foreach collection="unitids" index="index" item="item" open="(" separator="," close=")">
                            #{item.id}
                        </foreach>
                    </if>
                </if>
                <if test="request.queryParams.comboType == true" >
                    <if test="unitids != null and unitids.size()>0">
                        and bp.id in
                        <foreach collection="unitids" index="index" item="item" open="(" separator="," close=")">
                            #{item.id}
                        </foreach>
                    </if>
                </if>
                <if test="request.queryParams.skuType == true || request.queryParams.batched == 1">
                    <if test="unitids != null and unitids.size()>0">
                        and (pu.id,bps.id) in
                        <foreach collection="unitids" item="item" separator="," open="(" close=")">
                            (#{item.id},#{item.skuId})
                        </foreach>
                    </if>
                </if>
            </if>
            <if test="request.queryParams.filterKey == 'standard'.toString()">
                and bp.standard like #{filter}
            </if>
            <if test="request.queryParams.filterKey == 'ptypeType'.toString()">
                and bp.ptype_type like #{filter}
            </if>
            <if test="request.queryParams.filterKey == 'ptypeArea'.toString()">
                and bp.ptype_area like #{filter}
            </if>
            <if test="request.queryParams.filterKey == 'brandName'.toString()">
                and bb.brand_name like #{filter}
            </if>
        </if>
        <if test="request.queryParams != null and request.queryParams.comboType != true and request.queryParams.ptypeId != null">
            and bp.id = #{request.queryParams.ptypeId}
        </if>
        <if test="request.queryParams != null and request.queryParams.comboType == true and request.queryParams.comboId != null">
            and bp.id = #{request.queryParams.comboId}
        </if>
        <if test="request.queryParams != null and request.queryParams.comboType != true and request.queryParams.showSkuStop != null and request.queryParams.showSkuStop != -1">
            and bps.stoped = #{request.queryParams.showSkuStop}
        </if>
        <if test="request.queryParams != null and request.queryParams.stopTypePtype != null and  request.queryParams.stopTypePtype != -1">
            and bp.stoped = #{request.queryParams.stopTypePtype}
        </if>
        <if test="request.queryParams !=null and request.queryParams.unitCode == 1">
            and pu.unit_code=1
        </if>
        <if test="request.queryParams !=null and request.queryParams.comboType != true and request.queryParams.unitCode > 1">
            and pu.unit_code=#{request.queryParams.unitCode,jdbcType=INTEGER}
        </if>
    </sql>

    <sql id="query-filter-or">
        <include refid="query-filter">
            <property name="_list" value="${_list}"/>
            <property name="prefix" value="and"/>
            <property name="relation" value="and"/>
        </include>
    </sql>

    <sql id="limit-ptype">
        <if test="@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-id}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-profileid}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isLimited('ptype')">
            left join base_ptype_limit_scope _blsPtype on ${limit-alias-id} = _blsPtype.ptype_id and ${limit-alias-profileid} =
            _blsPtype.profile_id  and _blsPtype.etype_id = ${@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@getEtypeId()}
        </if>
    </sql>
    <sql id="query-filter">
        <if test="${_list} != null ">
            <trim prefix=" and (" suffix=")">
                <foreach collection="${_list}" item="item" separator="${relation}">
                    <choose>


                        <when test="item.magicQuery != null and item.magicQuery.size() != 0">
                            <trim prefix="(" suffix=")">
                                <foreach collection="item.magicQuery" item="magic_item" separator="or">
                                    ${magic_item.dataField} like concat('%',#{magic_item.value},'%')
                                </foreach>
                            </trim>
                        </when>
                        <when test="item.dataField == 'red_post_time'">
                            ${_pttb}.redbill_state=1 and ${_pttb}.apc.update_time between  #{item.value1} and  #{item.value2}
                        </when>
                        <when test="item.dataField == 'ptype_lable_id_list'">
                            EXISTS (
                            SELECT 1 FROM cf_data_label_ptype cdlp
                            WHERE cdlp.labelfield_value_id in
                            <foreach collection="item.value" item="lblId" index="index" open="(" close=")" separator=",">
                                #{lblId}
                            </foreach>
                            AND cdlp.profile_id=#{profileId,jdbcType=BIGINT} AND cdlp.resource_id=p.id
                            )
                        </when>
                        <when test="item.dataField == 'btype_lable_id_list'">
                            EXISTS (
                            SELECT 1 FROM cf_data_label_btype cdlp
                            WHERE cdlp.labelfield_value_id in
                            <foreach collection="item.value" item="lblId" index="index" open="(" close=")" separator=",">
                                #{lblId}
                            </foreach>
                            AND cdlp.profile_id=#{profileId,jdbcType=BIGINT} AND cdlp.resource_id=b.id
                            )
                        </when>
                        <when test="(item.dataField == 'cuse1.fullname' ||item.dataField == 'cuse2.fullname'||item.dataField == 'cuse3.fullname'||item.dataField == 'cuse4.fullname'||item.dataField == 'cuse5.fullname')
                                    and query.enabledBillCustomEtype==false">
                        </when>



                        <when test="item.type == 0">
                            ${item.dataField} like concat('%',#{item.value},'%')
                        </when>
                        <when test="item.type == 1">
                            <trim prefixOverrides="and" prefix="(" suffix=")">
                                <if test="item.value1 != null ">
                                    ${item.dataField} >= #{item.value1}
                                </if>
                                <if test="item.value2 != null ">
                                    and ${item.dataField} <![CDATA[<=]]> #{item.value2}
                                </if>
                            </trim>
                        </when>
                        <when test="item.type == 2"> ${item.dataField} = #{item.value}</when>
                    </choose>
                </foreach>
            </trim>
        </if>
    </sql>

    <sql id="ptype-storage-filter">
        <choose>
            <when test="request.queryParams.ptypeType == true or request.queryParams.skuType == true">
                and ${ptype}.pcategory not in (1,2)
            </when>
            <when test="request.queryParams.comboType == true">
                and ${ptype}.pcategory in (2)
            </when>
        </choose>
        and ${ptype}.deleted=0 and ${ptype}.classed=0
    </sql>
    <sql id="pub_custom_field_baseinfo_ptype_column">
        pcfb.custom_head01 as ptype_custom_head01,pcfb.custom_head02 as ptype_custom_head02,pcfb.custom_head03 as ptype_custom_head03,
        pcfb.custom_head04 as ptype_custom_head04,pcfb.custom_head05 as ptype_custom_head05,pcfb.custom_head06 as ptype_custom_head06,
        pcfb.custom_head07 as ptype_custom_head07,pcfb.custom_head08 as ptype_custom_head08,pcfb.custom_head09 as ptype_custom_head09,
        pcfb.custom_head10 as ptype_custom_head10,pcfb.custom_head11 as ptype_custom_head11,pcfb.custom_head12 as ptype_custom_head12,
        pcfb.custom_head13 as ptype_custom_head13,pcfb.custom_head14 as ptype_custom_head14
    </sql>

    <sql id="pub_custom_field_baseinfo_ptype_leftjoin">
        LEFT JOIN pub_custom_field_baseinfo pcfb
        ON pcfb.sub_type=5001 AND pcfb.profile_id=#{profileId}
        and pcfb.xtype_id=${table-alias}.id
    </sql>
    <sql id="limit-where-ptype">
        <if test="@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-id}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isLimited('ptype')">
            and (_blsPtype.ptype_id is not null or  ${limit-alias-id} =0)
        </if>
    </sql>

    <select id="getUnitCode" resultType="com.wsgjp.ct.sale.biz.analysiscloud.dto.finance.BaseUnitCode" parameterType="integer">
        select u2.id,sku_id from base_ptype_unit u2
        join
        (select f.* from base_ptype_unit u
        left join base_ptype_fullbarcode f
        on u.id=f.unit_id and u.ptype_id=f.ptype_id and u.profile_id=f.profile_id
        where f.profile_id=#{profileId}
        <if test="fullbarcode!=null and  fullbarcode!=''">
            and fullbarcode like concat('%',#{fullbarcode},'%')
        </if>) a
        on a.ptype_id=u2.ptype_id and a.profile_id=u2.profile_id
        where unit_code=1 and a.profile_id=#{profileId}
    </select>

    <select id="getComboId" resultType="com.wsgjp.ct.sale.biz.analysiscloud.dto.finance.BaseUnitCode" >
        select id from base_ptype where profile_id=#{profileId}  and barcode like concat('%',#{fullbarcode},'%')
    </select>

    <select id="getSkuXcodeUnitCode" resultType="com.wsgjp.ct.sale.biz.analysiscloud.dto.finance.BaseUnitCode" parameterType="integer">
        select u2.id,sku_id from base_ptype_unit u2
        join
        (select f.* from base_ptype_unit u
        left join base_ptype_xcode f
        on u.id=f.unit_id and u.ptype_id=f.ptype_id and u.profile_id=f.profile_id
        where f.profile_id=#{profileId}
        <if test="skuXcode!=null and  skuXcode!=''">
            and xcode like concat('%',#{skuXcode},'%')
        </if>) a
        on a.ptype_id=u2.ptype_id and a.profile_id=u2.profile_id
        where unit_code=1 and a.profile_id=#{profileId}
    </select>

    <select id="getBtypePtypeUnitCode" resultType="com.wsgjp.ct.sale.biz.analysiscloud.dto.finance.BaseUnitCode" parameterType="integer">
        select ifnull(u.id,0) as `id` from base_ptype_btype_relation bpbr
        left join base_btype bb on bpbr.profile_id=bb.profile_id and bpbr.btype_id=bb.id
        left join base_ptype_unit u on bpbr.profile_id=u.profile_id and bpbr.ptype_id=u.ptype_id and u.unit_code=1
        where bpbr.profile_id=#{profileId}
        <if test="btypeName!=null and  btypeName!=''">
            and bb.fullname like concat('%',#{btypeName},'%')
        </if> group by u.id
    </select>

    <select id="getLabelsPtypeUnitCode" resultType="com.wsgjp.ct.sale.biz.analysiscloud.dto.finance.BaseUnitCode" parameterType="integer">
        select ifnull(u.id,0) as `id` from cf_data_label_ptype cdlp
        left join cf_labelfield_value clv on cdlp.profile_id=clv.profile_id and cdlp.labelfield_value_id=clv.id
        left join base_ptype_unit u on cdlp.profile_id=u.profile_id and cdlp.resource_id=u.ptype_id and u.unit_code=1
        where cdlp.profile_id=#{profileId}
        <if test="labels!=null and  labels!=''">
            and clv.labelfield_value like concat('%',#{labels},'%')
        </if> group by u.id
    </select>

    <select id="listPtypeUnits" resultType="com.wsgjp.ct.sale.biz.analysiscloud.entity.base.UnitPrice">
        select ptype_id,ptype_id as id,unit_code,unit_name,unit_rate from base_ptype_unit where profile_id =#{profileId} and
        ptype_id in
        <foreach collection="ptypeIds" item="pid" separator="," open="(" close=")" index="index">
            #{pid}
        </foreach>
    </select>

    <sql id="qtyFilterQuery">
        <if test="request.queryParams !=null and request.queryParams.comboType != true and request.queryParams.qtyFilter == 1">
            and inventory_inner_qty != 0
        </if>
        <if test="request.queryParams !=null and request.queryParams.comboType != true and request.queryParams.qtyFilter == 2">
            and saleable_qty != 0
        </if>
        <if test="request.queryParams !=null and request.queryParams.comboType != true and request.queryParams.qtyFilter == 3">
            and inventory_inner_qty = 0
        </if>
        <if test="request.queryParams !=null and request.queryParams.comboType != true and request.queryParams.qtyFilter == 4">
            and saleable_qty = 0
        </if>
    </sql>

</mapper>
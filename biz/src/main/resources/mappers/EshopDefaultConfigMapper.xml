<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopDefaultConfigMapper">

    <insert id="addEshopDefaultConfig">
        INSERT INTO pl_eshop_default_config (id, profile_id, auth_expire_notify_days) value (#{id}, #{profileId}, #{authExpireNotifyDays})
    </insert>
    <update id="updateEshopDefaultConfig">
        update pl_eshop_default_config set auth_expire_notify_days = #{authExpireNotifyDays} where profile_id = #{profileId}
    </update>
    <select id="getEshopDefaultConfigContByProfileId" resultType="java.lang.Integer">
        select COUNT(*) from pl_eshop_default_config where profile_id = #{profileId}
    </select>
    <select id="getEshopDefaultConfigByProfileId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.EshopDefaultConfigRequest">
        select *
        from pl_eshop_default_config
        where profile_id = #{profileId}
    </select>
</mapper>
Type.registerNamespace('sale.shopsale.control');

sale.shopsale.control.BasePromotionWholesaleAction = function () {
    sale.shopsale.control.BasePromotionWholesaleAction.initializeBase(this);
};

sale.shopsale.control.BasePromotionWholesaleAction.prototype = {
    context: function (cb) {
        this.dataSource = this.get_property('PromotionInfo');// 父页面的childParams
        this.otypeList = $promotion.getPromotionFilterType(0, -1);
        this.btypeLabValueList = $shopsalecommon.getBtypeLabValueList();
        this.btypeClassList = $shopsalecommon.getBtypeClassList();
        cb({
            "listItem": [
                {id: 1, text: '全部', status: true},
                {id: 2, text: '指定往来单位', status: true},

            ],
            "filterTypesOList": this.otypeList,
            "filterTypesBLabValueList": this.btypeLabValueList,
            "filterTypeBClassList": this.btypeClassList
        });
    },

    initialize: function BasePromotionWholesaleAction$initialize() {
        sale.shopsale.control.BasePromotionWholesaleAction.callBaseMethod(this, 'initialize');
        //根据促销类型加载其他空间显示
        if (this.get_rootForm().promotionRuleSetCC) {
            this.get_rootForm().promotionRuleSetCC.memberRightsPanel.set_visible(false);

            if (this.get_root().setRbPromotionMethodList) {
                this.get_root().setRbPromotionMethodList(false)
            }
        }
        this.showRangType();
        if (this.dataSource.rangTypeWholeSale == 2) {
            this.btypeSelect();
        }
    },


    //保存检查促销基本新是否有效
    checkValid: function (promotionInfo) {
        if ($shopsalecommon.compareTime(promotionInfo.startTime, promotionInfo.endTime)) {
            $common.showTips("有效开始时间不能大于结束时间");
            return false;
        }

        if (promotionInfo.mode == 1 && $shopsalecommon.compareTime(new Date().toLocaleDateString(), promotionInfo.startDate)) {
            $common.showTips("促销开始日期不能小于当前日期");
            return false;
        }
        var checkResult = $common.ajaxSync({
            url: "sale/shopsale/promotion/checkPromotionIn", data: promotionInfo, router: 'ngp'
        });
        if (checkResult && checkResult.data) {
            $common.showTips("促销名称已存在");
            return false;
        }

        return true;
    },
    doNodeEdit: function (sender) {
        if (this.hasEvent("change")) {
            this.event("change", sender);
        }
        var form = this.get_form();
        var data = form.saveData(true);
        this.showRangType();
        if (data.rangTypeWholeSale == 2) {
            this.btypeSelect();
        }
    },

    showRangType: function (sender) {
        var form = this.get_form();
        var data = form.saveData(true);
        form.batchSet(['btypeId', 'filterOTypeLabValue', 'filterOTypeClass', 'edBType'], 'set_visible', false);
        if (data.rangTypeWholeSale == 2) {
            form.batchSet(['btypeId', 'filterOTypeLabValue'], 'set_visible', true);
        }
    },

    btypeSelect: function (sender) {
        var form = this.get_form();
        var date = form.saveData(true);
        var index = date.btypeRangType;
        form.batchSet(['filterOTypeLabValue', 'filterOTypeClass', 'edBType'], 'set_visible', false);

        if (index == 7) {
            form.batchSet(['filterOTypeLabValue'], 'set_visible', true);
        } else if (index == 6) {
            form.batchSet(['filterOTypeClass'], 'set_visible', true);
        } else if (index == 4) {
            form.batchSet(['edBType'], 'set_visible', true);
        }

    },

    //往来单位选框
    onBtypeInit: function (sender) {
        var url = "/jxc/baseinfo/selector/BtypeSelector.gspx";
        var filterStr = sender.get_text();
        var parameter = {
            filtertype: 'quick',
            showadd: true,
            btypetype: 'nofreight',
            bcategory: 0,
            multiselect: true
        };
        if (filterStr && sender.get_textChanged()) {
            parameter.filtervalue = filterStr;
        }
        sender.set_selectorPageParams(parameter);
        sender.set_selectorPage(url);
    },
    doBaseInfoSelect: function (sender, eventArgs) {
        var selector = eventArgs.get_form();
        var result = selector.selectedDataList;
        var etypeIds = "";
        var etyepNames = "";

        if (!result) {
            sender.set_value("");
            sender.set_text("");
            return;
        }

        for (var i = 0; i < result.length; i++) {
            etypeIds += result[i].id + ",";
            etyepNames += result[i].fullname + ",";
        }
        etypeIds = etypeIds.substring(0, etypeIds.length - 1);
        etyepNames = etyepNames.substring(0, etyepNames.length - 1);
        eventArgs.set_focusNext(false);
        if (sender.get_value() === etypeIds) {
            return;
        }
        sender.set_value(etypeIds);
        sender.set_text(etyepNames);
    },

    doDataBind: function (sender) {
        var form = this.get_form();
        var dataSource = form.get_dataSource()
        if (dataSource.btypeRangType == 4) {
            form.edBType.set_value({
                rangValues: dataSource.rangValues,
                filterNames: dataSource.filterNames
            });
        }

    },

    buidlRangeTypes: function (promotionInfo) {
        var rangValueList = [];
        //判空
        if (promotionInfo.rangValues == null || promotionInfo.rangValues.length == 0) return;
        for (var i = 0; i < promotionInfo.rangValues.length; i++) {
            rangValueList.push({
                "rangeValueType": promotionInfo.btypeRangType,
                "promotionId": promotionInfo.id,
                "vipType": 0,
                "rangId": promotionInfo.rangValues[i]
            });
        }
        promotionInfo.rangValueList = rangValueList;
    },

    //构建过滤条件
    buildRangValues: function (promotionInfo) {
        if (promotionInfo.btypeRangType === 4 && promotionInfo.rangTypeWholeSale == 2) {
            if (typeof promotionInfo.rangValues === 'string') {
                if (!(promotionInfo.rangValues === "" || promotionInfo.rangValues === null || promotionInfo.rangValues === undefined)) {
                    promotionInfo.rangValues = promotionInfo.rangValues.split(",");
                } else {
                    $common.showTips("往来单位不能为空！");
                    return false;
                }
            } else if (promotionInfo.rangValues != null && typeof promotionInfo.rangValues === 'object' && !Object.isUndefinedOrNull(promotionInfo.rangValues.rangValues)) {
                promotionInfo.rangValues = promotionInfo.rangValues.rangValues;
            } else {
                $common.showTips("往来单位不能为空！");
                return false;
            }
        }
        this.buidlRangeTypes(promotionInfo);
        return true;
    },
    dispose: function () {
        sale.shopsale.control.BasePromotionWholesaleAction.callBaseMethod(this, 'dispose');
    }
};
sale.shopsale.control.BasePromotionWholesaleAction.registerClass('sale.shopsale.control.BasePromotionWholesaleAction', Sys.UI.PageAction);
package com.wsgjp.ct.sale.biz.eshoporder.service.order;

import bf.datasource.aspect.PageAspect;
import bf.datasource.page.PageDevice;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.alibaba.fastjson.JSONObject;
import com.wsgjp.ct.common.enums.core.enums.BaseOrderMarkEnum;
import com.wsgjp.ct.common.enums.core.enums.FullLinkStatusEnum;
import com.wsgjp.ct.common.enums.core.enums.QtyChangeSourceType;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.pm.service.PermissionValiateService;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopOrderService;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopPluginService;
import com.wsgjp.ct.sale.biz.bifrost.util.EshopUtils;
import com.wsgjp.ct.sale.biz.eshoporder.api.PtypeApi;
import com.wsgjp.ct.sale.biz.eshoporder.api.request.MalDownloadTaskEntity;
import com.wsgjp.ct.sale.biz.eshoporder.config.EshopOrderConst;
import com.wsgjp.ct.sale.biz.eshoporder.config.SaleBizConfig;
import com.wsgjp.ct.sale.biz.eshoporder.config.sysdata.AutoSubmitConfig;
import com.wsgjp.ct.sale.biz.eshoporder.config.sysdata.EshopOrderSysDataConfig;
import com.wsgjp.ct.sale.biz.eshoporder.dao.EshopAdvanceSaleOrderDao;
import com.wsgjp.ct.sale.biz.eshoporder.dao.EshopSaleOrderDao;
import com.wsgjp.ct.sale.biz.eshoporder.dto.order.CheckOrderDelete;
import com.wsgjp.ct.sale.biz.eshoporder.entity.QueryMap;
import com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderDetail;
import com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderDetailCombo;
import com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceSubmitConfig;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Stock;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.SysData;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.UnitPrice;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.order.DownloadType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopPlatformStoreMapping;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.BatchModifyOrderRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.BatchOrderParams;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.DeletedOrderParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.PartialRefreshParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMark;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.order.EshopOrderMarkParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.order.OrderMarkBigDataEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.OrderRelationResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.SellerMemoResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.TradeMsg;
import com.wsgjp.ct.sale.biz.eshoporder.impl.ProcessLoggerImpl;
import com.wsgjp.ct.sale.biz.eshoporder.log.EshopAdvanceOrderSysLog;
import com.wsgjp.ct.sale.biz.eshoporder.log.EshopSaleOrderSysLog;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.*;
import com.wsgjp.ct.sale.biz.eshoporder.service.baseinfo.EshopOrderBaseInfoService;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopService;
import com.wsgjp.ct.sale.biz.eshoporder.service.refund.EshopOrderEshopRefundService;
import com.wsgjp.ct.sale.biz.eshoporder.support.DeliverApi;
import com.wsgjp.ct.sale.biz.eshoporder.util.CommonUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.SaleOrderExStatusAsyncHandleHelper;
import com.wsgjp.ct.sale.biz.eshoporder.util.SysLogUtil;
import com.wsgjp.ct.sale.biz.jarvis.dto.BillDeliverDetailDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.deliverbillupdate.DeliverBillUpdateResult;
import com.wsgjp.ct.sale.biz.jarvis.dto.deliverbillupdate.SaleOrderSelectDeliverBillRequest;
import com.wsgjp.ct.sale.biz.jarvis.dto.deliverbillupdate.SaleOrderSelectDeliverBillResponse;
import com.wsgjp.ct.sale.biz.jarvis.entity.query.BillDeliverQueryParams;
import com.wsgjp.ct.sale.biz.jarvis.open.JarvisOpenApi;
import com.wsgjp.ct.sale.biz.jarvis.service.DeliverService;
import com.wsgjp.ct.sale.common.config.EshopOrderCommonConfig;
import com.wsgjp.ct.sale.common.constant.SyncOrderConst;
import com.wsgjp.ct.sale.common.constant.SysDataConst;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.entity.order.TmcOrderMessage;
import com.wsgjp.ct.sale.common.enums.*;
import com.wsgjp.ct.sale.common.enums.eshoporder.AdvanceOrderOperateTypeEnum;
import com.wsgjp.ct.sale.common.enums.eshoporder.ConsumerType;
import com.wsgjp.ct.sale.common.enums.eshoporder.OrderOpreateType;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.common.notify.entity.DeliverBillDetailTradeStatusDTO;
import com.wsgjp.ct.sale.common.notify.entity.NotifyMainAndDetailChangeDto;
import com.wsgjp.ct.sale.common.notify.entity.SellerMemo;
import com.wsgjp.ct.sale.common.notify.request.DeliverBillUpdateRequest;
import com.wsgjp.ct.sale.platform.dto.order.entity.EshopSaleOrderFreight;
import com.wsgjp.ct.sale.platform.dto.plugin.DropDownPlugin;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.entity.request.order.CloseOrderRequest;
import com.wsgjp.ct.sale.platform.entity.request.order.ModifyMemoRequest;
import com.wsgjp.ct.sale.platform.entity.request.plugin.CommonRequest;
import com.wsgjp.ct.sale.platform.entity.response.order.CloseOrderResponse;
import com.wsgjp.ct.sale.platform.entity.response.order.ModifyOrderMemoResponse;
import com.wsgjp.ct.sale.platform.enums.SellerFlag;
import com.wsgjp.ct.sale.platform.enums.TradeTypeEnum;
import com.wsgjp.ct.sale.platform.feature.order.EshopOrderMemoFeature;
import com.wsgjp.ct.sale.platform.sdk.mapper.EshopNotifyMapper;
import com.wsgjp.ct.sale.sdk.stock.biz.StockChangeService;
import com.wsgjp.ct.sale.sdk.stock.enums.StockChangeTypeEnum;
import com.wsgjp.ct.sale.sdk.stock.parameter.StockChangeQueueDto;
import com.wsgjp.ct.sis.client.SisClient;
import com.wsgjp.ct.sis.client.common.SensitiveFieldEnum;
import com.wsgjp.ct.sis.client.entity.DicInfo;
import com.wsgjp.ct.sis.client.entity.DicRequest;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.dao.mapper.SysDataMapper;
import com.wsgjp.ct.support.global.GlobalConfig;
import com.wsgjp.ct.support.redis.factory.CacheType;
import com.wsgjp.ct.support.thread.ThreadPool;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import ngp.idgenerator.UId;
import ngp.mq.MqSendResult;
import ngp.mq.SysMq;
import ngp.redis.RedisPoolFactory;
import ngp.starter.web.base.GeneralResult;
import ngp.utils.*;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019-12-31 15:28
 */
@Service
//@Slf4j
public class EshopSaleOrderService extends OrderServiceBase {
    private final EshopSaleOrderMapper orderMapper;
    private final EshopSaleOrderRelationService relationSvc;
    private final EshopOrderBaseInfoService eshopOrderBaseInfoService;
    private final PtypeApi baseApi;
    private final EshopService eshopService;
    private final EshopOrderEshopRefundService refundService;
    private static final Logger logger = LoggerFactory.getLogger(EshopSaleOrderService.class);
    private final BifrostEshopOrderService eshopOrderService;
    private final EshopSaleOrderPlatformService platformService;
    private final BifrostEshopPluginService eshopPluginService;
    private final RedisPoolFactory redisPoolFactory;
    private final EshopOrderCommonConfig eshopOrderCommonConfig;
    private final StockChangeService stockChangeService;
    private final EshopOrderEshopRefundMapper refundMapper;
    private final EshopSaleOrderSaver orderSaver;
    private final JarvisOpenApi jarvisOpenApiImpl;
    private final SaleBizConfig bizConfig;
    private final EshopProductMapper productMapper;
    private final EshopSaleOrderNotifyMapper notifyMapper;
    private final DeliverService deliverService;
    private final SysDataMapper sysDataMapper;
    private final EshopBizMapper bizMapper;

    public EshopSaleOrderService(EshopSaleOrderMapper orderMapper,
                                 EshopSaleOrderRelationService relationSvc, EshopSaleOrderDao orderDao,
                                 EshopOrderBaseInfoService eshopOrderBaseInfoService,
                                 PtypeApi baseApi, EshopService eshopService,
                                 EshopOrderEshopRefundService refundService,
                                 BifrostEshopOrderService eshopOrderService,
                                 EshopSaleOrderPlatformService platformService,
                                 BifrostEshopPluginService eshopPluginService,
                                 RedisPoolFactory redisPoolFactory, EshopOrderCommonConfig eshopOrderCommonConfig,
                                 StockChangeService stockChangeService,
                                 EshopOrderEshopRefundMapper returnMapper, EshopSaleOrderSaver orderSaver,
                                 JarvisOpenApi jarvisOpenApiImpl, SaleBizConfig bizConfig,
                                 EshopProductMapper productMapper, EshopSaleOrderNotifyMapper notifyMapper,
                                 DeliverService deliverService, SysDataMapper sysDataMapper, EshopBizMapper bizMapper) {
        super(orderDao);
        this.orderMapper = orderMapper;
        this.relationSvc = relationSvc;
        this.eshopOrderBaseInfoService = eshopOrderBaseInfoService;
        this.baseApi = baseApi;
        this.eshopService = eshopService;
        this.refundService = refundService;
        this.eshopOrderService = eshopOrderService;
        this.platformService = platformService;
        this.eshopPluginService = eshopPluginService;
        this.redisPoolFactory = redisPoolFactory;
        this.eshopOrderCommonConfig = eshopOrderCommonConfig;
        this.stockChangeService = stockChangeService;
        this.refundMapper = returnMapper;
        this.orderSaver = orderSaver;
        this.jarvisOpenApiImpl = jarvisOpenApiImpl;
        this.bizConfig = bizConfig;
        this.productMapper = productMapper;
        this.notifyMapper = notifyMapper;
        this.deliverService = deliverService;
        this.sysDataMapper = sysDataMapper;
        this.bizMapper = bizMapper;
    }


    public PageResponse<EshopSaleOrderEntity> queryListOrdersPage(@RequestBody PageRequest<QueryOrderParameter> pageParameter, boolean isSimpleInfo, boolean realTotal) {
        QueryOrderParameter parameter = pageParameter.getQueryParams();
        CommonUtil.initLimited(parameter);
        parameter.setAllowEtypeIds(PermissionValiateService.getLimitEtypeIds());
        parameter.getAllowEtypeIds().add(BigInteger.ZERO);
        parameter.setAllowOtypeIds(PermissionValiateService.getLimitOtypeIds());
        parameter.getAllowOtypeIds().add(BigInteger.ZERO);
//        parameter.setAllowKtypeIds(PermissionValiateService.getLimitKtypeIds());
//        parameter.getAllowKtypeIds().add(BigInteger.ZERO);
        parameter.setInsertUnRelationByNormalOrder(1 == GlobalConfig.get(EshopOrderSysDataConfig.class).getInsertUnRelationByNormalOrder());
        parameter.setQueryCyclePurchaseMainOrder(true);
        if ("refund".equals(parameter.getPageFrom())) {
            List<BigInteger> refundOtypeIds = orderMapper.getRefundOtypeIdsNotDaiYunYin(CurrentUser.getProfileId());
            parameter.setOtypeIds(refundOtypeIds);
        }
        getDicInfos(parameter);
        getFilterDicInfos(parameter);
        //初始化查询参数另外的接口处理
        PageAspect.initSortQuery(pageParameter, QueryMap.SaleQueryMap.saleOrderQueryMap);
        PageDevice.initPageWithCount(pageParameter, false);
        PageResponse<EshopSaleOrderEntity> response = new PageResponse<>();
        response.setPageSize(pageParameter.getPageSize());
        response.setPageIndex(pageParameter.getPageIndex());
        if (!isSimpleInfo) {
            List<EshopSaleOrderEntity> list = quertOnlyOrderList(parameter);
            response.setList(list);
        } else {
            List<EshopSaleOrderEntity> simpleList = quertOnlyOrderListSimpleInfo(parameter);
            response.setList(simpleList);
        }
        int tempTotal = (response.getPageSize() * response.getPageIndex()) + 1;
        response.setTotal(tempTotal);
        if (realTotal) {
            int total = querySaleOrderListCount(pageParameter.getQueryParams());
            response.setTotal(total);
        }
        return response;
    }

    public List<String> getSaleOrderTradeIds(QueryOrderParameter parameter) {
        return orderMapper.getSaleOrderTradeIds(parameter);
    }

    public PageResponse<EshopSaleOrderEntity> queryListOrdersForSelect(@RequestBody PageRequest<QueryOrderParameter> pageParameter, boolean isSimpleInfo, boolean realTotal) {
        QueryOrderParameter parameter = pageParameter.getQueryParams();
        CommonUtil.initLimited(parameter);
        parameter.setAllowEtypeIds(PermissionValiateService.getLimitEtypeIds());
        parameter.getAllowEtypeIds().add(BigInteger.ZERO);
        parameter.setAllowOtypeIds(PermissionValiateService.getLimitOtypeIds());
        parameter.getAllowOtypeIds().add(BigInteger.ZERO);
        parameter.setAllowKtypeIds(PermissionValiateService.getLimitKtypeIds());
        parameter.getAllowKtypeIds().add(BigInteger.ZERO);
        if ("refund".equals(parameter.getPageFrom())) {
            List<BigInteger> refundOtypeIds = orderMapper.getRefundOtypeIdsNotDaiYunYin(CurrentUser.getProfileId());
            parameter.setOtypeIds(refundOtypeIds);
        }
        //初始化查询参数另外的接口处理
        PageDevice.initPageWithCount(pageParameter, false);
        PageResponse<EshopSaleOrderEntity> response = new PageResponse<>();
        response.setPageSize(pageParameter.getPageSize());
        response.setPageIndex(pageParameter.getPageIndex());

        List<EshopSaleOrderEntity> simpleList = queryOrderListForSelect(parameter);
        response.setList(simpleList);
        int tempTotal = (response.getPageSize() * response.getPageIndex()) + 1;
        response.setTotal(tempTotal);
        if (realTotal) {
            int total = querySaleOrderListForSelectCount(pageParameter.getQueryParams());
            response.setTotal(total);
        }
        return response;
    }

    public List<EshopSaleOrderEntity> quertOrderList(QueryOrderParameter parameter) {
        return querySaleOrderList(parameter);
    }

    public List<EshopSaleOrderEntity> quertOnlyOrderList(QueryOrderParameter parameter) {
        return queryOnlySaleOrderList(parameter);
    }

    public List<EshopSaleOrderEntity> quertOrderListSimpleInfo(QueryOrderParameter parameter) {
        return orderDao.querySaleOrderListSimple(parameter);
    }

    public List<EshopSaleOrderEntity> quertOnlyOrderListSimpleInfo(QueryOrderParameter parameter) {
        return orderDao.queryOnlySaleOrderListSimple(parameter);
    }

    public List<EshopSaleOrderEntity> queryOrderListForSelect(QueryOrderParameter parameter) {
        return orderDao.querySaleOrderListForSelect(parameter);
    }

    public int querySaleOrderListCount(QueryOrderParameter parameter) {
        CommonUtil.initLimited(parameter);
        parameter.setAllowEtypeIds(PermissionValiateService.getLimitEtypeIds());
        parameter.getAllowEtypeIds().add(BigInteger.ZERO);
        parameter.setAllowOtypeIds(PermissionValiateService.getLimitOtypeIds());
        parameter.getAllowOtypeIds().add(BigInteger.ZERO);
        parameter.setQueryCyclePurchaseMainOrder(true);
        getDicInfos(parameter);
        getFilterDicInfos(parameter);
        parameter.setInsertUnRelationByNormalOrder(1 == GlobalConfig.get(EshopOrderSysDataConfig.class).getInsertUnRelationByNormalOrder());
        return orderMapper.querySaleOrderCount(parameter);
    }

    public List<BigInteger> querySaleOrderListIds(QueryOrderParameter parameter) {
        CommonUtil.initLimited(parameter);
        parameter.setAllowEtypeIds(PermissionValiateService.getLimitEtypeIds());
        parameter.getAllowEtypeIds().add(BigInteger.ZERO);
        parameter.setAllowOtypeIds(PermissionValiateService.getLimitOtypeIds());
        parameter.getAllowOtypeIds().add(BigInteger.ZERO);
        parameter.setAllowKtypeIds(PermissionValiateService.getLimitKtypeIds());
        parameter.getAllowKtypeIds().add(BigInteger.ZERO);
        getDicInfos(parameter);
        getFilterDicInfos(parameter);
        parameter.setInsertUnRelationByNormalOrder(1 == GlobalConfig.get(EshopOrderSysDataConfig.class).getInsertUnRelationByNormalOrder());
        return orderMapper.querySaleOrderIds(parameter);
    }

    public int querySaleOrderListForSelectCount(QueryOrderParameter parameter) {
        CommonUtil.initLimited(parameter);
        parameter.setAllowEtypeIds(PermissionValiateService.getLimitEtypeIds());
        parameter.getAllowEtypeIds().add(BigInteger.ZERO);
        parameter.setAllowOtypeIds(PermissionValiateService.getLimitOtypeIds());
        parameter.getAllowOtypeIds().add(BigInteger.ZERO);
        parameter.setAllowKtypeIds(PermissionValiateService.getLimitKtypeIds());
        parameter.getAllowKtypeIds().add(BigInteger.ZERO);
        return orderMapper.querySaleOrderForSelectCount(parameter);
    }


    public List<EshopSaleOrderEntity> partialRefreshOrderList(List<PartialRefreshParameter> parameterList) {
        QueryOrderParameter parameter = new QueryOrderParameter();
        parameter.setEshopOrderIdList(parameterList.stream().map(PartialRefreshParameter::getId).collect(Collectors.toList()));
        if (parameter.getEshopOrderIdList() == null || parameter.getEshopOrderIdList().size() == 0) {
            return null;
        }
        CommonUtil.initPageDevice(parameter);
        List<EshopSaleOrderEntity> orderEntityList = querySaleOrderList(parameter);
        if (orderEntityList == null) {
            return null;
        }
        orderEntityList.forEach(item -> {
            Optional<PartialRefreshParameter> refreshParameter =
                    parameterList.stream().filter(p -> p.getId().equals(item.getId())).findFirst();
            refreshParameter.ifPresent(partialRefreshParameter -> item.setSelected(partialRefreshParameter.isSelected()));
        });
        return orderEntityList;
    }

    public List<EshopSaleOrderEntity> quertOrderVchcodeList(QueryOrderParameter parameter) {
        return querySaleOrderList(parameter);
    }

    public List<DeliverBillEntity> getDeliverbills(QueryOrderParameter parameter) {
        return orderMapper.getDeliverbill(parameter);
    }

    public List<EshopSaleOrderDetail> queryOrdersDetails(QueryOrderDetailParameter parameter) {
        List<EshopSaleOrderDetail> orderDetailList = queryOrderDetails(parameter);
        List<EshopSaleDetailCombo> comboRows = queryOrderDetailComboRows(parameter);
        if (comboRows == null || comboRows.isEmpty()) {
            buildUnitName(orderDetailList);
            return orderDetailList;
        }
        List<EshopSaleOrderDetail> detailList = filterOrderDetails(orderDetailList, comboRows);
        buildUnitName(detailList);
        return detailList;
    }

    public List<EshopSaleOrderFreight> queryOrderFreights(QueryOrderDetailParameter parameter) {
        return orderMapper.getAllEshopOrderFreight(CurrentUser.getProfileId(), parameter.getEshopOrderId());
    }

    public void buildUnitName(List<EshopSaleOrderDetail> details) {
        if (details == null || details.size() == 0) {
            return;
        }
        try {
//            List<BigInteger> ptypeIds = details.stream().map(EshopSaleOrderDetail::getPtypeId).distinct().collect
//            (Collectors.toList());
//            Map<BigInteger, List<UnitPrice>> units = baseApi.listPtypesUnit(ptypeIds).getData();
            for (EshopSaleOrderDetail detail : details) {
                detail.setUnitRelation(detail.getUnitBaseRelation());
//                if (detail.isComboRow()) {
//                    continue;
//                }
//                if (units == null || units.size() <= 0) {
//                    continue;
//                }
//                if (!units.containsKey(detail.getPtypeId())) {
//                    continue;
//                }
//                List<UnitPrice> unit = units.get(detail.getPtypeId());
//                if (unit == null || unit.size() <= 1) {
//                    continue;
//                }
//                Collections.sort(unit);
//                detail.setUnitRelation("");
//                BigDecimal unitRate = unit.get(0).getUnitRate();
//                List<String> relationList = new ArrayList<>();
//                for (UnitPrice unitPrice : unit) {
//                    BigDecimal rate = MoneyUtils.divide(unitRate, unitPrice.getUnitRate(), Money.Discount);
//                    relationList.add(String.format("%s%s", rate.stripTrailingZeros().toPlainString(), unitPrice
//                    .getUnitName()));
//                }
//                detail.setUnitRelation(String.join("=", relationList));
            }
        } catch (RuntimeException ex) {
            logger.error(String.format("getPtypesUnit error:%s", ex.getMessage()), ex);
        }
    }

    public List<EshopSaleOrderDetail> queryOrderDetails(QueryOrderDetailParameter detailParameter) {
        return orderDao.querySaleOrderDetails(detailParameter);
    }

    public List<EshopSaleDetailCombo> queryOrderDetailComboRows(QueryOrderDetailParameter detailParameter) {
        return orderDao.queryComboRows(detailParameter);
    }

    public String doInitUnRelationOrderDetails(QueryOrderDetailParameter detailParameter) {
        if (detailParameter == null) {
            return "";
        }
        if (detailParameter.getEshopOrderIds() != null && !detailParameter.getEshopOrderIds().isEmpty()) {
            return relationSvc.doInsertTempNewUnRelationDetails(detailParameter);
        }
        return "";
    }

    public List<OrderRelationOperation> queryUnRelationOrderDetails(QueryOrderDetailParameter detailParameter) {
        //开启快速对应忽略 退款的(整单所有明细)、取消、交易关闭 订单之后
        //应该查询 临时对应表中 has_normal_order为1
        //原单手工提交对应的情况下，应该要忽略账套配置，允许查询这个网店商品
        if (detailParameter.getSubmit()) {
            detailParameter.setInsertUnRelationByNormalOrder(-1);
        }
        return relationSvc.queryUnRelationOrderDetails(detailParameter);
    }

    /**
     * 临时对应主要逻辑函数
     *
     * @param details
     * @return
     */
    public OrderRelationResponse notifyOrderToRelation(List<OrderRelationOperation> details, OrderRelationTask task) {
        logger.error(String.format("profileId：%s，临时对应接收参数：%s", CurrentUser.getProfileId(), JsonUtils.toJson(details)));
        OrderRelationResponse response = new OrderRelationResponse();
        try {
            long sTime = System.currentTimeMillis();
            response = relationSvc.notifyOrderToRelation(details, task);
            task.AppendMsg(String.format("临时对应总耗时：%s ms", System.currentTimeMillis() - sTime));
        } catch (RuntimeException ex) {
            response.setSuccess(false);
            response.setErrorMsg(ex.getMessage());
        }
        return response;
    }

    public List<EshopSaleOrderEntity> queryOrderList(BigInteger otypeId, List<String> tradeIds) {
        QueryOrderParameter parameter = new QueryOrderParameter();
        parameter.setTradeOrderIds(tradeIds);
        List<BigInteger> otypeIdList = new ArrayList<>();
        otypeIdList.add(otypeId);
        parameter.setOtypeIds(otypeIdList);
        CommonUtil.initPageDevice(parameter);
        return querySaleOrderList(parameter);
    }

    public List<EshopSaleOrderEntity> queryOrderListWithDetails(BigInteger otypeId, List<String> tradeIds) {
        List<EshopSaleOrderEntity> orderList = queryOrderList(otypeId, tradeIds);
        if (orderList == null || orderList.size() == 0) {
            return orderList;
        }
        orderList.forEach(order -> {
            QueryOrderDetailParameter param = new QueryOrderDetailParameter();
            param.setEshopOrderId(order.getId());
            List<EshopSaleOrderDetail> details = queryOrderDetails(param);
            if (details != null) {


                order.setOrderDetails(details);
            }
            List<EshopSaleDetailCombo> combos = queryOrderDetailComboRows(param);
            if (combos != null) {
                order.setDetailCombos(combos);
            }
        });
        return orderList;
    }

    public EshopSaleOrderEntity queryOrderSimpleWithDetails(BigInteger otypeId, String tradeId) {
        QueryOrderParameter parameter = new QueryOrderParameter();
        parameter.setTradeOrderId(tradeId);
        parameter.setOtypeIds(Arrays.asList(otypeId));
        return orderDao.getSimpleOrderWithDetail(parameter);
    }


    private List<EshopSaleOrderEntity> querySaleOrderList(QueryOrderParameter queryOrderParameter) {
        return orderDao.querySaleOrderList(queryOrderParameter);
    }

    private List<EshopSaleOrderEntity> queryOnlySaleOrderList(QueryOrderParameter queryOrderParameter) {
        return orderDao.queryOnlySaleOrderList(queryOrderParameter);
    }

    public EshopSaleOrderInvoiceInfo getOrderDecryptInvoice(EshopSaleOrderInvoiceInfo invoice) {
        return orderDao.getOrderDecryptInvoice(invoice);
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean deleteEshopSaleOrder(DeletedOrderParameter request, EshopSaleOrderNotifyService notifyService) {
        boolean result = orderMapper.deleteSaleOrder(request) > 0;
        if (!result) {
            throw new RuntimeException("删除失败,订单已提交");
        }
        QueryOrderParameter queryOrderParameter = new QueryOrderParameter();
        queryOrderParameter.setEshopOrderIdList(request.getEshopOrderIds());
        queryOrderParameter.setProfileId(request.getProfileId());
        List<EshopSaleOrderEntity> orderList = getSimpleOrderInfo(queryOrderParameter);
        if (CollectionUtils.isEmpty(orderList)) {
            return true;
        }
        for (EshopSaleOrderEntity item : orderList) {
            notifyService.receiveSaleOrderQtyChangeNotify(item, QtyChangeSourceType.DELETE);
            SysLogUtil.add(SysLogUtil.buildLog(item, OrderOpreateType.DELETE_ORDER, ""));
        }
        return true;
    }

    public void deleteSaleOrderSendToCold(QueryOrderParameter parameter) {
        orderMapper.deleteSaleOrderSendToCold(parameter);
    }

    public void deleteSaleOrderDetailSendToCold(QueryOrderDetailParameter parameter) {
        orderMapper.deleteSaleOrderDetailSendToCold(parameter);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateEShopOrderRemark(BigInteger profileId, BigInteger vchcode, String message) {
        orderMapper.updateEshopOrderRemark(profileId, vchcode, message);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateEshopOrderSellerMemo(BigInteger profileId, Integer flageId, BigInteger vchcode, String message) {
        orderMapper.updateEshopOrderSellerMemo(profileId, vchcode, message, flageId);
    }


    public EshopSaleOrderEntity getSimpleOrder(QueryOrderParameter parameter) {
        return orderDao.getSimpleOrder(parameter);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateOrderFlag(BigInteger profileId, BigInteger vchcode, int flagId) {
        orderMapper.updateOrderFlag(profileId, vchcode, flagId);
    }

    public void getDicInfos(QueryOrderParameter queryOrderParameter) {
        try {
            if (StringUtils.isEmpty(queryOrderParameter.getKeyWord())) {
                return;
            }
            if (queryOrderParameter.getFilterKeyType() != null  && queryOrderParameter.getFilterKeyType().isSensitive()) {
                buildParameterDicInfos(queryOrderParameter,queryOrderParameter.getFilterKeyType().getSensitiveFieldEnum());
            }
            if (queryOrderParameter.getFilterKeyTypeNew() != null  && queryOrderParameter.getFilterKeyTypeNew().isSensitive()) {
                buildParameterDicInfos(queryOrderParameter,queryOrderParameter.getFilterKeyTypeNew().getSensitiveFieldEnum());
            }
        } catch (RuntimeException ex) {
            // 处理JsonUtil.toJson(queryOrderParameter)，class com.wsgjp.ct.sale.biz.jarvis.dto.BillDeliverDetailDTO
            // declares multiple JSON fields named stockSyncRuleId
            logger.error(String.format("加密系统处理失败：%s，请求参数:%s", ex, JsonUtils.toJson(queryOrderParameter)));
        }
    }

    private void buildParameterDicInfos(QueryOrderParameter queryOrderParameter,SensitiveFieldEnum keyType) {
        List<String> keyWords = new ArrayList<>();
        List<String> tradeIdWords = new ArrayList<>();
        for (String sensitive : queryOrderParameter.getKeyArrays()) {
            DicRequest request = new DicRequest();
            request.setField(keyType);
            request.setValue(sensitive);
            request.setEshopIds(queryOrderParameter.getOtypeIds());
            request.setStartTime(queryOrderParameter.getBeginTime());
            request.setEndTime(queryOrderParameter.getEndTime());
            request.setContainsJd(queryOrderParameter.isContanisJdQuery());
            List<DicInfo> dicInfos = SisClient.getDicInfosByTime(request);
            for (DicInfo item : dicInfos) {
                if (item == null || CollectionUtils.isEmpty(item.getValues()) || item.getValue() == null) {
                    continue;
                }
                keyWords.add(item.getValue());
                if (item.getTradeIds() == null || item.getTradeIds().isEmpty()) {
                    continue;
                }
                tradeIdWords.addAll(item.getTradeIds());
            }
        }
        queryOrderParameter.setKeyWord(StringUtils.join(keyWords, ","));
        queryOrderParameter.setSisTradeOrderIds(tradeIdWords);

    }


    public void getFilterDicInfos(QueryOrderParameter parameter) {
        try {
            EshopOrderFilterParameter filterParameter = parameter.getFilterParameter();
            if (filterParameter == null) {
                return;
            }
            List<String> keyWords = new ArrayList<>();
            List<String> tradeIdWords = new ArrayList<>();

            String customerShopAccount = filterParameter.getCustomerShopAccount();
            if (StringUtils.isNotEmpty(customerShopAccount)) {
                DicRequest request = new DicRequest();
                request.setField(SensitiveFieldEnum.BUYER_ACCOUNT);
                request.setValue(customerShopAccount);
                request.setEshopIds(parameter.getOtypeIds());
                request.setStartTime(parameter.getBeginTime());
                request.setEndTime(parameter.getEndTime());
                request.setContainsJd(false);
                List<DicInfo> dicInfos = SisClient.getDicInfosByTime(request);
                dicInfos.forEach(item -> {
                            if (item.getValue() == null) {
                                return;
                            }
                            keyWords.add(item.getValue());
                            if (item.getTradeIds() == null || item.getTradeIds().size() == 0) {
                                return;
                            }
                            tradeIdWords.addAll(item.getTradeIds());
                        }
                );

                filterParameter.setKeyWord(StringUtils.join(keyWords, ","));
                filterParameter.setSisTradeOrderIds(tradeIdWords);
                filterParameter.setCustomerShopAccount("");
                filterParameter.setEnableDicAccount(true);
            }
        } catch (RuntimeException ex) {
            logger.error(String.format("加密系统处理失败：%s，请求参数:%s", ex, JsonUtils.toJson(parameter)));
        }
    }

    public SaleOrderPriceControl getPriceControl() {
        List<OrderStrategyConfig> configList = eshopOrderBaseInfoService.queryStrategyConfig();
        if (configList == null || configList.size() == 0) {
            return new SaleOrderPriceControl();
        }
        SaleOrderPriceControl control = new SaleOrderPriceControl();
        for (OrderStrategyConfig config : configList) {
            if (control.getStrategyCode() == 0) {
                control.setStrategyCode(config.getStrategyCode());
                control.setControlType(config.getControlType());
            } else if (control.getStrategyCode() < config.getStrategyCode()) {
                control.setStrategyCode(config.getStrategyCode());
                control.setControlType(config.getControlType());
            }
        }
        return control;
    }

    public List<EshopSaleOrderEntity> queryListOrdersByVchcodes(GetSaleOrderInfoRequest parameter) {
        return orderMapper.queryListOrdersByVchcodes(parameter);
    }

    public List<EshopSaleOrderEntity> queryListOrdersByVchcodesForJXC(GetSaleOrderInfoRequest parameter) {
        return orderMapper.queryListOrdersByVchcodesForJXC(parameter);
    }


    /**
     * 查询首页网店订单数据
     *
     * @param request 请求参数
     * @return EshopSaleOrderResult
     */
    public void findEshopOrderResultForMainPageOrderData(MainPageSaleOrderQueryParameter request,
                                                         EshopSaleOrderResult result) {
        initMainPageQueryParam(request);
        //1.平台下载并且已完成付款所有订单数之和 与 未付款所有订单数之和（网店订单数据）
        //2需要排除数据：全部退成、取消订单、交易关闭
        EshopSaleOrderResult findyfkEshopOrder = orderMapper.findYfkWfkEshopOrder(request);
        if (null == findyfkEshopOrder) {
            findyfkEshopOrder = new EshopSaleOrderResult();
        }
        result.setYfkOrderCount(findyfkEshopOrder.getYfkOrderCount());
        result.setYfkAmount(findyfkEshopOrder.getYfkAmount());
        result.setWfkOrderCount(findyfkEshopOrder.getWfkOrderCount());
        result.setWfkAmount(findyfkEshopOrder.getWfkAmount());

        //1.订单创建方式为下载生成之外的其他订单。比如手工新增、导入、售后生成订单数之和（网店订单数据）
        EshopSaleOrderResult findOtherEshopOrder = orderMapper.findOtherEshopOrder(request);
        if (null == findOtherEshopOrder) {
            findOtherEshopOrder = new EshopSaleOrderResult();
        }
        result.setOtherOrderCount(findOtherEshopOrder.getOtherOrderCount());
        result.setOtherAmount(findOtherEshopOrder.getOtherAmount());
    }

    /**
     * 查询首页今日待办事项数据
     *
     * @param request 请求参数
     */
    public void findEshopOrderResultForMainPageAgencyMatters(MainPageSaleOrderQueryParameter request,
                                                             EshopSaleOrderResult result) {
        initMainPageQueryParam(request);
        //原始订单待提交订单数量=>待提交（待办事项）
        EshopSaleOrderResult findEshopOrderNoSubmit;
        if (PermissionValiateService.isOldVersion()){
            findEshopOrderNoSubmit = orderMapper.findEshopOrderNoSubmitAll(request);
        }else {
            findEshopOrderNoSubmit = orderMapper.findEshopOrderNoSubmitAllNew(request);
        }
        result.setWaitSubmit(findEshopOrderNoSubmit.getWaitSubmit());

        //统计未对应的原始订单 =》 商品未对应订单数量（待办事项）
        EshopSaleOrderResult findEshopOrderUnRelation = orderMapper.findEshopOrderUnRelation(request);
        result.setUnRelation(findEshopOrderUnRelation.getUnRelation());

        //统计仓库未对应的原始订单（待办事项）
        EshopSaleOrderResult findEshopOrderNoKtype = orderMapper.findEshopOrderNoKtype(request);
        result.setNoKtype(findEshopOrderNoKtype.getNoKtype());

        QueryRefundParameter refundParameter = buildRefundParam();
        //1.待审核的售后单 =》 售后待处理（待办事项）
        //2需要排除数据：售后类型：未发-仅退款
        result.setWaitRefund(refundService.queryRefundListCount(refundParameter));
    }

    /**
     * 查询首页今日异常关注的数据
     *
     * @param request 请求参数
     */
    public void findEshopSaleOrderResultForMainPageAbnormalAttention(MainPageSaleOrderQueryParameter request,
                                                                     EshopSaleOrderResult result) {
        initMainPageQueryParam(request);
        QueryRefundParameter refundParameter = buildRefundParam();

        //统计发生了仅退款和退款退货原始订单数量=》退款单数量（异常关注）
        EshopSaleOrderResult findEshopOrderHasRefund = orderMapper.findEshopOrderHasRefund(request);
        result.setHasRefund(findEshopOrderHasRefund.getHasRefund());

        //标记已超时并且未完成对应处理的售后单
        refundParameter.setRefundTypes(null);
        List<BigInteger> markType = new ArrayList<>();
        //《1》未完成审核并且标记了审核超时售后单
        markType.add(new BigInteger("200073"));
        //《2》未完成生单并入库并且标记退货收货超时售后单
        markType.add(new BigInteger("200074"));
        //《3》未生成补发单并且标记了补发超时的售后单
        markType.add(new BigInteger("200076"));
        refundParameter.setMarkType(markType);
        refundParameter.setAuditStatus(null);
        int overTimeCountRefund = refundService.queryRefundListCount(refundParameter);
        result.setOverTimeCountRefund(overTimeCountRefund);
    }

    public void setResultUrl(EshopSaleOrderResult result, MainPageSaleOrderQueryParameter request) {
        if (null == result || null == request) {
            return;
        }
        result.setYfkOrderCountUrl(getUrlTimeParam(result.getYfkOrderCountUrl(), request));
        result.setWfkOrderCountUrl(getUrlTimeParam(result.getWfkOrderCountUrl(), request));
        result.setOtherOrderCountUrl(getUrlTimeParam(result.getOtherOrderCountUrl(), request));
    }

    private String getUrlTimeParam(String url, MainPageSaleOrderQueryParameter request) {
        if (StringUtils.isEmpty(url)) {
            return url;
        }
        String formatStr = "%s&beginTime=%s";
        if (null != request.getBeginTime()) {
            url = String.format(formatStr, url, DateUtils.formatDate(request.getBeginTime(),
SysDataConst.DATE_TIME_PATTERN));
        }
        return url;
    }


    private void initMainPageQueryParam(MainPageSaleOrderQueryParameter request) {
        CommonUtil.initLimited(request);
        request.setProfileId(CurrentUser.getProfileId());
        request.setKtypeLimited(false);
        request.setInsertUnRelationByNormalOrder(1 == GlobalConfig.get(EshopOrderSysDataConfig.class).getInsertUnRelationByNormalOrder());
    }


    @NotNull
    private static QueryRefundParameter buildRefundParam() {
        QueryRefundParameter refundParameter = new QueryRefundParameter();
        refundParameter.setRefundOperationType(RefundOperationType.CREATE);
        refundParameter.setRefundTypes(Arrays.asList(0, 1, 2, 3, 4, 6, 7));
        refundParameter.setTimeType(QueryRefundTimeType.CREATE_TIME);
        refundParameter.setBeginTime(CommonUtil.getAnyDayBeginTime(29));
        refundParameter.setEndTime(CommonUtil.getTodayLastTime());
        refundParameter.setAuditStatus(Collections.singletonList(RefundAuditStatus.WAIT_AUDIT));
        refundParameter.setBatchQueryType(RefundBatchQueryTypeEnum.REFUND_NUMBER);
        refundParameter.setRefundNoDetail(RefundNoDetail.HAS_DETAILS);
        refundParameter.setTableName("pl_eshop_sale_order `order`");
        refundParameter.setRefundStatuses(Arrays.asList(1, 2, 3, 4, 5));
        refundParameter.setDeleted(RefundDeleteStateEnum.NotDelete);
        refundParameter.setRefundStatuses(Arrays.asList(1, 2, 3, 5));
        return refundParameter;
    }

    /**
     * 检查订单是否可以关闭
     *
     * @param request
     * @return {@link CloseOrderResponse}
     */
    public CloseOrderResponse checkCloseOrder(CloseOrderRequest request) {
        CloseOrderResponse response = new CloseOrderResponse();
        response.setSuccess(true);
        if (null == request) {
            return response;
        }
        if (StringUtils.isNotEmpty(request.getCloseOrderFrom()) && "refund".equals(request.getCloseOrderFrom())) {
            List<String> errorList = new ArrayList<>();
            BillDeliverQueryParams req = new BillDeliverQueryParams();
            req.setOrgId(Collections.singletonList(request.getShopId()));
            req.setTradeIds(request.getTradeIdList());
            List<BillDeliverDetailDTO> dtos = jarvisOpenApiImpl.queryDeliverDetailsByTradeOrderId(req);
            if (CollectionUtils.isNotEmpty(dtos)) {
                Map<String, List<BillDeliverDetailDTO>> map =
                dtos.stream().collect(Collectors.groupingBy(BillDeliverDetailDTO::getTradeOrderId));
                for (Map.Entry<String, List<BillDeliverDetailDTO>> entry : map.entrySet()) {
                    String tradeOrderId = entry.getKey();
                    List<BillDeliverDetailDTO> dto = entry.getValue();
                    //任意明细 系统处理状态在待出入库之后，取消失败
                    //任意明细 出入库核算失败，取消订单也应失败
                    boolean closeOrderByDeliverDetails =
                     dto.stream().anyMatch(d -> d.getPostStateCode() > 500 || (d.getPostStateCode() == 500 && d.getPostFailure() > 0));
                    if (closeOrderByDeliverDetails) {
                        errorList.add(tradeOrderId);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(errorList)) {
                response.setErrorList(errorList);
                response.setMessage("存在已出入库或出入库失败的交易单，不允许重新补发或者取消补发！");
                response.setSuccess(false);
            } else {
                response.setSuccess(true);
            }
        }
        return response;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateOrderRefundState(UpdateOrderRefundStateRequest request) {
        if (null == request || StringUtils.isEmpty(request.getTradeOrderId()) ||
                null == request.getShopId() || BigInteger.ZERO.compareTo(request.getShopId()) == 0 ||
                null == request.getUpdateOrderRefundType()) {
            throw new RuntimeException(String.format("profileId:%s，updateOrderRefundState invalid parameter",
                CurrentUser.getProfileId()));
        }
        try {
            logger.error(String.format("profileId:%s，updateOrderRefundState:%s", CurrentUser.getProfileId(),
             JsonUtils.toJson(request)));
            QueryOrderParameter queryOrderParameter = new QueryOrderParameter();
            List<BigInteger> otypeIds = new ArrayList<>();
            otypeIds.add(request.getShopId());
            queryOrderParameter.setTradeOrderIds(Collections.singletonList(request.getTradeOrderId()));
            queryOrderParameter.setOtypeIds(otypeIds);
            EshopSaleOrderEntity localOrder = doUpdateOrderRefundState(queryOrderParameter, request);
            EshopAdvanceOrderEntity localAdvance = doUpdateAdvanceRefundState(queryOrderParameter, request);
            doOrderNotifyDeliver(localOrder, queryOrderParameter);
            doAdvanceNotifyDeliver(localAdvance, queryOrderParameter);
        } catch (Exception ex) {
            String msg = String.format("帐套【%s】售后提前通知,错误信息%s", CurrentUser.getProfileId(), ex.getMessage());
            throw new RuntimeException(msg, ex);
        }
    }

    private void doAdvanceNotifyDeliver(EshopAdvanceOrderEntity localAdvance, QueryOrderParameter queryOrderParameter) {
        if (null == localAdvance) {
            return;
        }
        EshopAdvanceSaleOrderDao advanceSaleOrderDao = GetBeanUtil.getBean(EshopAdvanceSaleOrderDao.class);
        List<EshopAdvanceOrderDetail> advanceDetails = localAdvance.getDetail();
        if (CollectionUtils.isEmpty(advanceDetails)) {
            return;
        }
        List<EshopAdvanceOrderDetail> details =
                advanceDetails.stream().filter(o -> DetailProcessState.Submit == o.getPlatform().getProcessState()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(details)) {
            EshopAdvanceOrderEntity notifyAdvance = getSimpleAdvanceInfo(queryOrderParameter).get(0);
            advanceSaleOrderDao.doNotifyRefundByOrderState(localAdvance, notifyAdvance);
        }
    }

    private void doOrderNotifyDeliver(EshopSaleOrderEntity localOrder, QueryOrderParameter queryOrderParameter) {
        if (null == localOrder) {
            return;
        }
        EshopSaleOrderNotifyService notifyService = GetBeanUtil.getBean(EshopSaleOrderNotifyService.class);
        List<EshopSaleOrderDetail> orderDetails = localOrder.getOrderDetails();
        if (orderDetails == null || orderDetails.isEmpty()) {
            return;
        }
        List<EshopSaleOrderDetail> details =
            orderDetails.stream().filter(o -> DetailProcessState.Submit == o.getProcessState()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(details)) {
            EshopSaleOrderEntity notifyOrder = getSimpleOrderInfo(queryOrderParameter).get(0);
            notifyService.doNotifyRefundByOrderState(localOrder, notifyOrder);
        }
    }

    private EshopAdvanceOrderEntity doUpdateAdvanceRefundState(QueryOrderParameter queryOrderParameter,
     UpdateOrderRefundStateRequest request) {
        List<EshopAdvanceOrderEntity> orderList = getSimpleAdvanceInfo(queryOrderParameter);
        if (orderList == null || orderList.isEmpty()) {
            return null;
        }
        EshopAdvanceOrderEntity order = orderList.get(0);
        EshopAdvanceOrderEntity localOrder = CommonUtil.deepCopy(order, EshopAdvanceOrderEntity.class);
        List<EshopAdvanceOrderSysLog> logList = new ArrayList<>();
        boolean updateMainRefund = CollectionUtils.isEmpty(request.getOidList());
        List<EshopAdvanceOrderDetail> details = order.getDetail();
        for (EshopAdvanceOrderDetail detail : details) {
            if (updateMainRefund || request.getOidList().contains(detail.getPlatform().getTradeOrderDetailId())) {
                detail.getPlatform().setLocalRefundState(request.getRefundState());
            }
            notifyMapper.modifyAdvanceDetailState(detail);
        }
        EshopAdvanceSaleOrderDao advanceDao = GetBeanUtil.getBean(EshopAdvanceSaleOrderDao.class);
        advanceDao.buildOrderRefundMentionType(order);
        notifyMapper.modifyAdvanceState(order);
        if (localOrder.getPlatform().getLocalRefundState() != order.getPlatform().getLocalRefundState()) {
            logList.add(SysLogUtil.buildAdvanceOrderLog(order,
            request.getUpdateOrderRefundType().getAdvanceOrderOperateType(),
                    String.format("%s-售后状态变更，由【%s】变更为【%s】", request.getUpdateOrderRefundType().getName(),
                            localOrder.getPlatform().getLocalRefundState().getName(),
                            order.getPlatform().getLocalRefundState().getName())));
        }
        advanceDao.doMarkStockAdd(localOrder);
        if (CollectionUtils.isNotEmpty(logList)) {
            logList.forEach(SysLogUtil::add);
        }
        return localOrder;
    }

    private EshopSaleOrderEntity doUpdateOrderRefundState(QueryOrderParameter queryOrderParameter,
     UpdateOrderRefundStateRequest request) {
        List<EshopSaleOrderEntity> orderList = getSimpleOrderInfo(queryOrderParameter);
        if (orderList == null || orderList.isEmpty()) {
            throw new RuntimeException("订单尚未流入系统");
        }
        EshopSaleOrderEntity order = orderList.get(0);
        EshopSaleOrderEntity localOrder = CommonUtil.deepCopy(order, EshopSaleOrderEntity.class);
        List<EshopSaleOrderSysLog> logList = new ArrayList<>();
        boolean updateMainRefund = CollectionUtils.isEmpty(request.getOidList());
        List<EshopSaleOrderDetail> details = order.getOrderDetails();
        for (EshopSaleOrderDetail detail : details) {
            if (updateMainRefund || request.getOidList().contains(detail.getTradeOrderDetailId())) {
                detail.setLocalRefundState(request.getRefundState());
            }
            notifyMapper.modifyDetailState(detail);
        }
        EshopSaleOrderBuilder builder = EshopSaleOrderBuilder.getSingleton();
        builder.buildOrderRefundMentionType(order);
        notifyMapper.modifyOrderState(order);
        if (localOrder.getLocalRefundState() != order.getLocalRefundState()) {
            logList.add(SysLogUtil.buildLog(order, request.getUpdateOrderRefundType().getOrderOpreateType(),
                    String.format("%s-售后状态变更，由【%s】变更为【%s】", request.getUpdateOrderRefundType().getName(),
                            localOrder.getLocalRefundState().getName(),
                            order.getLocalRefundState().getName())));
        }
        markQtyChangeByClose(orderList);
        if (CollectionUtils.isNotEmpty(logList)) {
            logList.forEach(SysLogUtil::add);
        }
        return localOrder;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateOrderByTmcRequest(UpdateOrderByTmcRequest request) {
        if (null == request || StringUtils.isEmpty(request.getTradeOrderId()) ||
                null == request.getShopId() || BigInteger.ZERO.compareTo(request.getShopId()) == 0 ||
                null == request.getConsumerType()) {
            return;
        }
        try {
            CommonUtil.doLogByEnable(logger, LogLevelEnum.ERROR, String.format("profileId:%s,updateOrderByTmcRequest" +
             ":%s", CurrentUser.getProfileId(), JsonUtils.toJson(request)));
            QueryOrderParameter queryOrderParameter = new QueryOrderParameter();
            List<BigInteger> otypeIds = new ArrayList<>();
            otypeIds.add(request.getShopId());
            queryOrderParameter.setTradeOrderIds(Collections.singletonList(request.getTradeOrderId()));
            queryOrderParameter.setOtypeIds(otypeIds);
            EshopSaleOrderEntity order = doUpdateOrderByTmc(queryOrderParameter, request);
            EshopAdvanceOrderEntity advance = doUpdateAdvanceOrderByTmc(queryOrderParameter, request);
            List<DeliverBillUpdateRequest> changeInfoList = new ArrayList<>();
            if (null == advance && !ProcessState.NoSubmit.equals(order.getProcessState())) {
                addChangeInfo(changeInfoList, getMemoChangeInfo(request, order));
                addChangeInfo(changeInfoList, getTradeSateChange(request, order));
            }
            if (null != advance && !ProcessState.NoSubmit.equals(advance.getSubmitSendState())) {
                addChangeInfo(changeInfoList, getAdvanceMemoChangeInfo(request, advance));
                addChangeInfo(changeInfoList, getAdvanceTradeSateChange(request, advance));
            }
            if (CollectionUtils.isNotEmpty(changeInfoList)) {
                GeneralResult<DeliverBillUpdateResult> notify =
                 GetBeanUtil.getBean(DeliverApi.class).notify(changeInfoList);
                if (notify.getCode() != 200) {
                    logger.error(String.format("通知发货单变更报错：%s", notify.getMessage()));
                    GetBeanUtil.getBean(DeliverApi.class).notify(changeInfoList);
                }
                doInsertChange(changeInfoList);
            }
            sendHuiBiaoMsg(Collections.singletonList(order.getId()));
            sendHuiBiaoMsg(Collections.singletonList(advance.getVchcode()));
        } catch (Exception ex) {
            String msg = String.format("帐套【%s】根据TMC修改订单报错,错误信息%s", CurrentUser.getProfileId(), ex.getMessage());
            logger.error(msg, ex);
            throw new RuntimeException(msg, ex);
        }
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void closeOrder(CloseOrderRequest request) {
        CloseOrderResponse response = checkCloseOrder(request);
        List<String> tradeIdList = request.getTradeIdList();
        if (!response.getSuccess()) {
            List<String> errorList = response.getErrorList();
            if (CollectionUtils.isNotEmpty(tradeIdList) && CollectionUtils.isNotEmpty(errorList)) {
                //过滤不支持关闭的订单
                tradeIdList.removeIf(errorList::contains);
            }
        }
        if (request.getTradeIdList().isEmpty()) {
            return;
        }
        try {
            QueryOrderParameter queryOrderParameter = new QueryOrderParameter();
            List<BigInteger> otypeIds = new ArrayList<>();
            otypeIds.add(request.getShopId());
            queryOrderParameter.setTradeOrderIds(request.getTradeIdList());
            queryOrderParameter.setOtypeIds(otypeIds);
            doCloseSaleOrder(queryOrderParameter);
            doCloseAdvanceOrder(queryOrderParameter);
        } catch (Exception ex) {
            String msg = String.format("帐套【%s】关闭订单报错,错误信息%s", CurrentUser.getProfileId(), ex.getMessage());
            logger.error(msg, ex);
            throw new RuntimeException(msg, ex);
        }
    }

    private void doCloseSaleOrder(QueryOrderParameter queryOrderParameter) {
        List<EshopSaleOrderEntity> orderList = getSimpleOrderInfo(queryOrderParameter);
        if (orderList == null || orderList.size() == 0) {
            return;
        }
        orderMapper.closeOrder(orderList);
        orderMapper.closeOrderDetail(orderList);
        clearOrderMarksById(orderList.stream().map(EshopSaleOrderEntity::getId).collect(Collectors.toList()),
         BaseOrderMarkEnum.NO_PAY);
        markQtyChangeByClose(orderList);
        orderList.forEach(item -> SysLogUtil.add(SysLogUtil.buildLog(item, OrderOpreateType.CLOSE_ORDER, "")));
    }

    private EshopSaleOrderEntity doUpdateOrderByTmc(QueryOrderParameter queryOrderParameter,
     UpdateOrderByTmcRequest request) {
        List<EshopSaleOrderEntity> orderList = getSimpleOrderInfo(queryOrderParameter);
        if (orderList == null || orderList.isEmpty()) {
            throw new RuntimeException("订单尚未流入系统");
        }
        EshopSaleOrderEntity order = orderList.get(0);
        List<EshopSaleOrderSysLog> logList = new ArrayList<>();
        switch (request.getConsumerType()) {
            case DIRECT_UPDATE_MEMO:
                orderMapper.updateOrderMemoAndFlagByTmc(order.getProfileId(), order.getId(), request.getSellerMemo(),
                 request.getSellerFlag());
                if (order.getSellerFlag() != request.getSellerFlag()) {
                    logList.add(SysLogUtil.buildLog(order, OrderOpreateType.TMC_AUTO_UPDATE, String.format("原旗帜： %s," +
 "新旗帜： %s", order.getSellerFlag().getName(), request.getSellerFlag().getName())));
                }
                if (!order.getSellerMemo().equals(request.getSellerMemo())) {
                    logList.add(SysLogUtil.buildLog(order, OrderOpreateType.TMC_AUTO_UPDATE, String.format("原卖家备注： " +
"%s,新卖家备注： %s", order.getSellerMemo(), request.getSellerMemo())));
                }
                break;
            case DIRECT_UPDATE_TRADE_STATUS:
                orderMapper.updateOrderTradeStateByTmc(order.getProfileId(), order.getId(), request.getTradeStatus(),getNeedUpdateTradeFinishTime(order,request.getTradeStatus()));
                orderMapper.updateOrderDetailByTmc(order.getProfileId(), order.getId(), request.getTradeStatus());
                if (!TradeStatus.WAIT_BUYER_PAY.equals(request.getTradeStatus())) {
                    clearOrderMarksById(Collections.singletonList(order.getId()), BaseOrderMarkEnum.NO_PAY);
                }
                if (!order.getLocalTradeState().equals(request.getTradeStatus())) {
                    logList.add(SysLogUtil.buildLog(order, OrderOpreateType.TMC_AUTO_UPDATE, String.format("原线上交易状态： " +
                     "%s,新线上交易状态： %s", order.getLocalTradeState().getName(), request.getTradeStatus().getName())));
                }
                break;
            default:
                break;
        }
        markQtyChangeByClose(orderList);
        if (CollectionUtils.isNotEmpty(logList)) {
            logList.forEach(SysLogUtil::add);
        }
        return order;
    }

    private Date getNeedUpdateTradeFinishTime(EshopSaleOrderEntity order,TradeStatus tradeStatus) {
        Date tradeFinishTime = order.getTradeFinishTime();
        if (!CommonUtil.isInvalidDate(tradeFinishTime)){
            return tradeFinishTime;
        }
        if (TradeStatus.TRADE_FINISHED == tradeStatus){
            return new Date();
        }
        return tradeFinishTime;
    }

    /**
     * 删除订单上的标记（包括主表明细）
     *
     * @param ids  id
     * @param mark 标记
     */
    private void clearOrderMarksById(List<BigInteger> ids, BaseOrderMarkEnum mark) {
        if (ids == null || ids.size() == 0) {
            return;
        }
        BigInteger profileId = CurrentUser.getProfileId();
        EshopOrderMarkParameter parameter = new EshopOrderMarkParameter();
        parameter.setEshopOrderIds(ids);
        parameter.setMarkCodes(Collections.singletonList(BigInteger.valueOf(mark.getCode())));
        parameter.setProfileId(profileId);
        parameter.setMarkTarget(-1);
        orderMapper.deleteEshopOrderMark(parameter);
    }

    private void addChangeInfo(List<DeliverBillUpdateRequest> changeInfoList, DeliverBillUpdateRequest changeInfo) {
        if (changeInfo == null) {
            return;
        }
        changeInfoList.add(changeInfo);
    }


    private void doCloseAdvanceOrder(QueryOrderParameter queryOrderParameter) {
        List<EshopAdvanceOrderEntity> orderList = getSimpleAdvanceInfo(queryOrderParameter);
        if (orderList == null || orderList.size() == 0) {
            return;
        }
        orderMapper.closeAdvanceOrder(orderList);
        orderMapper.closeAdvanceOrderDetail(orderList);
        clearOrderMarksById(orderList.stream().map(EshopAdvanceOrderEntity::getVchcode).collect(Collectors.toList()),
         BaseOrderMarkEnum.NO_PAY);
        GetBeanUtil.getBean(EshopAdvanceSaleOrderDao.class).doMarkStockAdd(orderList.get(0));
    }

    private EshopAdvanceOrderEntity doUpdateAdvanceOrderByTmc(QueryOrderParameter queryOrderParameter,
     UpdateOrderByTmcRequest request) {
        List<EshopAdvanceOrderEntity> orderList = getSimpleAdvanceInfo(queryOrderParameter);
        if (orderList == null || orderList.size() == 0) {
            return null;
        }
        EshopAdvanceOrderEntity order = orderList.get(0);
        List<EshopAdvanceOrderSysLog> logList = new ArrayList<>();
        switch (request.getConsumerType()) {
            case DIRECT_UPDATE_MEMO:
                orderMapper.updateAdvanceOrderMemoAndFlagByTmc(order.getProfileId(), order.getVchcode(),
 request.getSellerMemo(), request.getSellerFlag());
                if (order.getPlatform().getSellerFlag() != request.getSellerFlag()) {
                    logList.add(SysLogUtil.buildAdvanceOrderLog(order, AdvanceOrderOperateTypeEnum.TMC_AUTO_UPDATE,
                     String.format("原旗帜： %s,新旗帜： %s", order.getPlatform().getSellerFlag().getName(),
request.getSellerFlag().getName())));
                }
                if (!order.getPlatform().getSellerMemo().equals(request.getSellerMemo())) {
                    logList.add(SysLogUtil.buildAdvanceOrderLog(order, AdvanceOrderOperateTypeEnum.TMC_AUTO_UPDATE,
                     String.format("原卖家备注： %s,新卖家备注： %s", order.getPlatform().getSellerMemo(),
                      request.getSellerMemo())));
                }
                break;
            case DIRECT_UPDATE_TRADE_STATUS:
                orderMapper.updateAdvanceOrderTradeStateByTmc(order.getProfileId(), order.getVchcode(),
                 request.getTradeStatus());
                orderMapper.updateAdvanceOrderDetailByTmc(order.getProfileId(), order.getVchcode(),
                 request.getTradeStatus());
                if (!TradeStatus.WAIT_BUYER_PAY.equals(request.getTradeStatus())) {
                    clearOrderMarksById(Collections.singletonList(order.getVchcode()), BaseOrderMarkEnum.NO_PAY);
                }
                if (!order.getPlatform().getLocalTradeState().equals(request.getTradeStatus())) {
                    logList.add(SysLogUtil.buildAdvanceOrderLog(order, AdvanceOrderOperateTypeEnum.TMC_AUTO_UPDATE,
                     String.format("原线上交易状态： %s,新线上交易状态： %s", order.getPlatform().getLocalTradeState().getName(),
request.getTradeStatus().getName())));
                }
                break;
            default:
                break;
        }
        GetBeanUtil.getBean(EshopAdvanceSaleOrderDao.class).doMarkStockAdd(order);
        if (CollectionUtils.isNotEmpty(logList)) {
            logList.forEach(SysLogUtil::add);
        }
        return order;
    }

    public List<EshopSaleOrderEntity> getSimpleOrderInfo(QueryOrderParameter queryOrderParameter) {
        boolean paramValid =
                (queryOrderParameter.getEshopOrderIdList() != null && !queryOrderParameter.getEshopOrderIdList().isEmpty()) || (queryOrderParameter.getTradeOrderIds() != null && !queryOrderParameter.getTradeOrderIds().isEmpty());
        if (!paramValid) {
            return new ArrayList<>();
        }
        List<EshopSaleOrderEntity> orderList = orderMapper.querySimpleOrderList(queryOrderParameter);
        if (orderList == null || orderList.size() == 0) {
            return orderList;
        }
        List<BigInteger> vchcodes = orderList.stream().map(EshopSaleOrderEntity::getId).collect(Collectors.toList());
        QueryOrderDetailParameter detailParameter = new QueryOrderDetailParameter();
        detailParameter.setEshopOrderIds(vchcodes);

        Map<BigInteger, List<EshopSaleOrderDetail>> detailsMap = new HashMap<>();
        Map<BigInteger, List<EshopSaleDetailCombo>> comboRowMap = new HashMap<>();
        List<EshopSaleOrderDetail> details = orderMapper.querySimpleDetails(detailParameter);
        if (details != null && details.size() > 0) {
            detailsMap = details.stream().collect(Collectors.groupingBy(EshopSaleOrderDetail::getEshopOrderId));
        }
        List<EshopSaleDetailCombo> comboRows = orderMapper.querySimpleComboRow(detailParameter);
        if (comboRows != null && comboRows.size() > 0) {
            comboRowMap = comboRows.stream().collect(Collectors.groupingBy(EshopSaleDetailCombo::getEshopOrderId));
        }
        for (EshopSaleOrderEntity itemOrder : orderList
        ) {
            BigInteger vchcode = itemOrder.getId();
            if (detailsMap.containsKey(vchcode)) {
                itemOrder.setOrderDetails(detailsMap.get(vchcode));
            }
            if (comboRowMap.containsKey(vchcode)) {
                itemOrder.setDetailCombos(comboRowMap.get(vchcode));
            }
        }
        return orderList;
    }

    private List<EshopAdvanceOrderEntity> getSimpleAdvanceInfo(QueryOrderParameter queryOrderParameter) {
        List<EshopAdvanceOrderEntity> advanceList = orderMapper.querySimpleAdvanceOrderList(queryOrderParameter);
        if (advanceList == null || advanceList.size() == 0) {
            return advanceList;
        }
        List<BigInteger> vchcodes =
            advanceList.stream().map(EshopAdvanceOrderEntity::getVchcode).collect(Collectors.toList());
        QueryOrderDetailParameter detailParameter = new QueryOrderDetailParameter();
        detailParameter.setEshopOrderIds(vchcodes);
        Map<BigInteger, List<EshopAdvanceOrderDetail>> detailsMap = new HashMap<>();
        Map<BigInteger, List<EshopAdvanceOrderDetailCombo>> comboRowMap = new HashMap<>();
        List<EshopAdvanceOrderDetail> details = orderMapper.querySimpleAdvanceDetails(detailParameter);
        if (details != null && details.size() > 0) {
            detailsMap = details.stream().collect(Collectors.groupingBy(EshopAdvanceOrderDetail::getVchcode));
        }
        List<EshopAdvanceOrderDetailCombo> comboRows = orderMapper.queryAdvanceSimpleComboRow(detailParameter);
        if (comboRows != null && comboRows.size() > 0) {
            comboRowMap = comboRows.stream().collect(Collectors.groupingBy(EshopAdvanceOrderDetailCombo::getVchcode));
        }
        for (EshopAdvanceOrderEntity itemOrder : advanceList) {
            BigInteger vchcode = itemOrder.getVchcode();
            if (detailsMap.containsKey(vchcode)) {
                itemOrder.setDetail(detailsMap.get(vchcode));
            }
            if (comboRowMap.containsKey(vchcode)) {
                itemOrder.setCombo(comboRowMap.get(vchcode));
            }
        }
        return advanceList;
    }

    private void markQtyChangeByClose(List<EshopSaleOrderEntity> orderList) {
        for (EshopSaleOrderEntity order : orderList) {
            QueryOrderDetailParameter parameter = new QueryOrderDetailParameter();
            parameter.setEshopOrderId(order.getId());
            List<EshopSaleOrderDetail> details = new ArrayList<>();
            if (order.getOrderSaleType().equals(TradeTypeEnum.NORMAL)) {
                details = orderMapper.querySimpleDetails(parameter);
            }
            if (details == null || details.size() == 0) {
                continue;
            }
            doMarkQtyChangeByCloseNew(order, details);
        }
    }

    private void doMarkQtyChangeByCloseNew(EshopSaleOrderEntity order, List<EshopSaleOrderDetail> details) {
        if (order == null || ProcessState.Submit.equals(order.getProcessState())) {
            return;
        }
        List<StockChangeQueueDto> list = new ArrayList<>();
        for (EshopSaleOrderDetail detail : details) {
            QtyChangeSourceType sourceType = order.getOrderSaleType() == TradeTypeEnum.NORMAL ?
             QtyChangeSourceType.ORDER_CLOSE : QtyChangeSourceType.ADVANCE_CLOSE;
            StockChangeQueueDto stockChangeQueueDto = new StockChangeQueueDto();
            stockChangeQueueDto.setProfileId(CurrentUser.getProfileId());
            stockChangeQueueDto.setSourceId(detail.getEshopOrderId());
            stockChangeQueueDto.setSourceType(StockChangeTypeEnum.SALE_ORDER);
            stockChangeQueueDto.setSourceOperation(sourceType.getName());
            list.add(stockChangeQueueDto);
        }
        stockChangeService.batchInsertChange(list);
    }

    public List<EshopSaleOrderEntity> queryAllSaleOrderFields(QueryOrderParameter parameter) {
        return orderDao.queryAllSaleOrderFields(parameter);
    }

    public List<OrderSubmitBatchDetailEntity> getOrderSubmitBatchDetailOids(BigInteger profileId, BigInteger vchcode,
     List<BigInteger> submitBatchIds
    ) {
        if (submitBatchIds == null || submitBatchIds.size() == 0) {
            return null;
        }
        List<OrderSubmitBatchDetailEntity> batchDetails = orderMapper.queryOrderSubmitBatchDetail(profileId, vchcode,
        submitBatchIds);
        if (batchDetails == null || batchDetails.size() == 0) {
            return null;
        }
        return batchDetails;
    }


    public SellerMemoResponse batchModifyBuyerMeessage(BatchModifyOrderRequest request,
     ProcessLoggerImpl processLogger) {
        SellerMemoResponse result = new SellerMemoResponse();
        result.setSuccess(true);
        result.setMessages(new ArrayList<>());
        processLogger.appendMsg("开始修改订单备注信息");
        try {
            if (StringUtils.isNotEmpty(request.getMessage())) {
                request.setMessage(HttpUtils.htmlDecode(request.getMessage()));
            }
            List<BatchOrderParams> paramsList = request.getOrderParamsList();
            Map<BigInteger, List<BatchOrderParams>> collect =
                    paramsList.stream()
                            .filter(params -> params.getEshopId().compareTo(BigInteger.ZERO) > 0)
                            .collect(Collectors.groupingBy(BatchOrderParams::getEshopId));
            ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.MODIFY_BUYER_MSG);
            for (Map.Entry<BigInteger, List<BatchOrderParams>> entry : collect.entrySet()) {
                List<BatchOrderParams> needs = entry.getValue();
                EshopInfo eshop = eshopService.getEshopInfoById(request.getProfileId(), entry.getKey());
//                EshopFactory entity = null;
                threadPool.submitTaskList(need -> {
                    OrderSellerFlag flag = OrderSellerFlag.values()[need.getFlagId()];
                    String message = request.getMessage();
                    String replace = "覆盖";
                    if (request.isClearFlag()) {
                        flag = OrderSellerFlag.WHITE;
                    }
                    if (!request.isClearFlag() && request.getFlagId() > 0) {
                        flag = OrderSellerFlag.values()[request.getFlagId()];
                    }
                    if (request.isFormat()) {
                        message = String.format("%s%s%s", need.getSellerMemo(),
                                StringUtils.isEmpty(need.getSellerMemo()) ? "" : ",", request.getMessage());
                        replace = "追加";
                    }
                    if (message.length() > 500) {
                        throw new RuntimeException("卖家备注超过系统允许的最大长度");
                    }
                    QueryOrderParameter parameter = new QueryOrderParameter();
                    parameter.setEshopOrderIdList(Collections.singletonList(need.getEshopOrderId()));
                    parameter.setProfileId(CurrentUser.getProfileId());
                    parameter.setEshopOrderId(need.getEshopOrderId());
                    List<EshopSaleOrderEntity> orderList = getSimpleOrderInfo(parameter);
                    if (orderList == null || orderList.size() == 0) {
                        throw new RuntimeException("系统异常没有找到订单，请稍后重试！");
                    }
                    EshopSaleOrderEntity orderEntity = orderList.get(0);
                    String changeLog = String.format("原卖家备注:%s,%s,后卖家备注:%s;原旗帜:%s,改为:%s",
                            orderEntity.getSellerMemo(), replace,
                            message,
                            orderEntity.getSellerFlag().getEmptyName(), flag.getEmptyName());
                    if (need.isOnline()) {
                        String msg = "";
                        try {
                            boolean isimplement = EshopUtils.isFeatureSupported(EshopOrderMemoFeature.class,
                             eshop.getEshopType());
                            if (!isimplement) {
                                throw new RuntimeException(String.format("%s不支持同步备注至后台",
                                eshop.getEshopType().getName()));
                            }
                            ModifyMemoRequest modifyMemoRequest = new ModifyMemoRequest();
                            modifyMemoRequest.setShopId(eshop.getOtypeId());
                            modifyMemoRequest.setFlag(SellerFlag.values()[flag.getCode()]);
                            modifyMemoRequest.setMemo(message);
                            modifyMemoRequest.setTradeId(need.getTradeOrderId());
                            modifyMemoRequest.setClearFlag(request.isClearFlag());
                            modifyMemoRequest.setFormat(request.isFormat());
                            modifyMemoRequest.setShopId(entry.getKey());
                            modifyMemoRequest.setShopType(eshop.getEshopType());
                            ModifyOrderMemoResponse response = eshopOrderService.modifyOrderMemo(modifyMemoRequest);
//                            response = feature.modifyOrderMemo(memoRequest);
//                            eshopService
                            //todo  成功就修改数据库的 记录日志 通知发货单
                            if (response.isTruncation()) {
                                msg = msg + "平台同步限制，自动截断";
                                changeLog = changeLog + "平台同步限制，自动截断";
                            }
                            if (!response.getSuccess()) {
                                msg = String.format("同步备注至后台失败：%s", response.getMessage());
                            }
                        } catch (Exception ex) {
                            msg = String.format("同步备注至后台失败：%s", ex.getMessage());
                        }
                        if (!StringUtils.isEmpty(msg)) {
                            result.setSuccess(false);
                            TradeMsg taskMsg = new TradeMsg(need.getTradeOrderId(), msg);
                            result.getMessages().add(taskMsg);
                            processLogger.appendMsg(String.format("单号：%s ,%s", need.getTradeOrderId(), msg));
                        }
                    }
                    GetBeanUtil.getBean(EshopSaleOrderService.class).updateEshopOrderSellerMemo(request.getProfileId(), flag.getCode(), need.getEshopOrderId(), message);
                    orderEntity.setSellerMemo(message);
                    orderEntity.setSellerFlag(flag);

                    EshopSaleOrderNotifyService notifyService = GetBeanUtil.getBean(EshopSaleOrderNotifyService.class);
                    notifyService.notifyLocal(orderEntity);
                    SysLogUtil.add(SysLogUtil.buildLog(orderEntity, OrderOpreateType.MODIFY_BUYER_MESSAGE, changeLog));
                    return null;
                }, needs);
            }
        } catch (RuntimeException ex) {
            processLogger.appendMsg(String.format("备注信息修改失败，原因：%s", ex.getMessage().replace("java.util.concurrent" +
             ".ExecutionException: java.lang.RuntimeException:", "")));
            result.setSuccess(false);
            result.setMessage(ex.getMessage());
        }
        processLogger.appendMsg("备注信息修改结束");
        return result;
    }

    //大促模式订单下载走工具，非大促订单下载走站点，复用代码
    public List<MalDownloadTaskEntity> mulDownloadOrder(EshopSaleOrderDownloadTask task) {
        List<MalDownloadTaskEntity> eshopTaskList = new ArrayList<>();
        if (task == null || task.getOtypeIds() == null || task.getOtypeIds().size() == 0) {
            return eshopTaskList;
        }
        task.setExecuteTime(new Date());
        for (BigInteger item : task.getOtypeIds()) {
            if (item.equals(BigInteger.ZERO)) {
                continue;
            }
            EshopInfo eshopInfo = eshopService.getEshopInfoById(CurrentUser.getProfileId(), item);
            task.setEshopInfo(eshopInfo);
            task.setOtypeId(item);
            MalDownloadTaskEntity itemEshop = checkAndInitItemTaskInfo(task.getStatus(), eshopInfo.getEshopType());
            itemEshop.setOtypeId(item);
            itemEshop.setOtypeName(eshopInfo.getFullname());
            if (itemEshop.getCompleted()) {
                eshopTaskList.add(itemEshop);
                continue;
            }
            try {
                EshopSaleOrderDownloadTask itemTask = buildDownloadTask(task, itemEshop.getTaskId());
                platformService.doDownloadOrder(itemTask);
            } catch (RuntimeException ex) {
                buildErrorTaskInfo(itemEshop, ex.getMessage());
            }
            eshopTaskList.add(itemEshop);
        }
        buildDownloadOrderRedisCache(eshopTaskList, null);
        buildDownloadOrderParamCache(task);
        return eshopTaskList;
    }

    private void buildDownloadOrderParamCache(EshopSaleOrderDownloadTask task) {
        EshopSaleOrderDownloadTask copyTask = new EshopSaleOrderDownloadTask();
        BeanUtils.copyProperties(task, copyTask);
        copyTask.setEshopInfo(null);
        copyTask.setBeginTimeStr(task.getBeginTime() == null ? "" : DateUtils.formatDateTime(task.getBeginTime()));
        copyTask.setEndTimeStr(task.getEndTime() == null ? "" : DateUtils.formatDateTime(task.getEndTime()));
        copyTask.setBeginTime(null);
        copyTask.setEndTime(null);
        StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
        String keyStr = "";
        if (copyTask.getDownloadType().equals(DownloadType.BY_TIME)) {
            keyStr = "mulDownloadOrderParam" + CurrentUser.getProfileId() + CurrentUser.getEmployeeId();
        } else if (copyTask.getDownloadType().equals(DownloadType.BY_TRADE_ID)) {
            keyStr = "downloadOrderByIdParam" + CurrentUser.getProfileId() + CurrentUser.getEmployeeId();
        }
        String valueStr = JsonUtils.toJson(copyTask);
        template.opsForValue().set(keyStr, valueStr, eshopOrderCommonConfig.getExpireTime(), TimeUnit.MINUTES);
        logger.info(String.format("下载订单参数存入redis缓存，账套:%s，职员:%s，写入数据:%s", CurrentUser.getProfileId(),
         CurrentUser.getEmployeeId(), valueStr));
    }

    //下载订单之后将task存入redis，用户在失效时间之内打开下载订单，都会读取缓存
    private void buildDownloadOrderRedisCache(List<MalDownloadTaskEntity> eshopTaskList, String taskId) {
        StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
        String keyStr = (null == eshopTaskList ?
         "downloadOrderById" + CurrentUser.getProfileId() + CurrentUser.getEmployeeId() :
 "mulDownloadOrder" + CurrentUser.getProfileId() + CurrentUser.getEmployeeId());
        String valueStr = (null == eshopTaskList ? taskId : JsonUtils.toJson(eshopTaskList));
        template.opsForValue().set(keyStr, valueStr, eshopOrderCommonConfig.getExpireTime(), TimeUnit.MINUTES);
        logger.info(String.format("下载订单存入redis缓存，账套:%s，职员:%s，写入数据:%s", CurrentUser.getProfileId(),
         CurrentUser.getEmployeeId(), valueStr));
    }

    public String downloadOrderById(EshopSaleOrderDownloadTask task) {
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        task.setProcessLogger(processLogger);
        task.setTaskId(taskId);
        task.setDownloadType(DownloadType.BY_TRADE_ID);
        if (task.getEshopInfo() == null) {
            EshopInfo eshopInfo = eshopService.getEshopInfoById(CurrentUser.getProfileId(), task.getOtypeId());
            task.setEshopInfo(eshopInfo);
        }
        platformService.downloadSaleOrderByTid(task);
        buildDownloadOrderRedisCache(null, taskId);
        buildDownloadOrderParamCache(task);
        return taskId;
    }

    private MalDownloadTaskEntity checkAndInitItemTaskInfo(TradeStatus status, ShopType eshopType) {
        MalDownloadTaskEntity itemEshop = new MalDownloadTaskEntity();
        itemEshop.setTaskId(UId.newId().toString());
        try {
            CommonRequest commonRequest = new CommonRequest();
            commonRequest.setShopType(eshopType);
            List<DropDownPlugin> statusList = eshopPluginService.orderDownloadSupport(commonRequest);
            if (statusList == null || statusList.size() == 0) {
                return buildErrorTaskInfo(itemEshop, "该网店不支持订单下载");
            }
            List<Integer> tradeStatus = statusList.stream().map(DropDownPlugin::getValue).collect(Collectors.toList());
            if (tradeStatus.size() == 0 || !tradeStatus.contains(status.getCode())) {
                return buildErrorTaskInfo(itemEshop, String.format("该网店不支持\"%s\"状态的订单进行下载", status.getName()));
            }
        } catch (RuntimeException ex) {
            logger.error(String.format("checkAndInitItemTaskInfo error: %s", ex.getMessage()), ex);
            return buildErrorTaskInfo(itemEshop, String.format("网店数据错误，不能建立下单任务 错误原因: %s", ex.getMessage()));
        }
        return itemEshop;
    }

    private EshopSaleOrderDownloadTask buildDownloadTask(EshopSaleOrderDownloadTask task, String taskId) {
        EshopSaleOrderDownloadTask itemTask = new EshopSaleOrderDownloadTask();
        itemTask.setTaskId(taskId);
        itemTask.setBeginTime(task.getBeginTime());
        itemTask.setEndTime(task.getEndTime());
        itemTask.setPageNo(task.getPageNo());
        itemTask.setLastDownloadTime(task.getLastDownloadTime());
        itemTask.setOtypeId(task.getOtypeId());
        itemTask.setStatus(task.getStatus());
        itemTask.setEshopInfo(task.getEshopInfo());
        itemTask.setProcessLogger(task.getProcessLogger());
        itemTask.setFilterStr(task.getFilterStr());
        itemTask.setFilterType(task.getFilterType());
        itemTask.setDownloadType(task.getDownloadType());
        itemTask.setTradeFrom(task.getTradeFrom());
        itemTask.setOtypeIds(task.getOtypeIds());
        itemTask.setExecuteTime(task.getExecuteTime());
        return itemTask;
    }

    private MalDownloadTaskEntity buildErrorTaskInfo(MalDownloadTaskEntity itemEshop, String errorMssage) {
        itemEshop.setCompleted(true);
        itemEshop.setSuccess(false);
        itemEshop.setProgress(100);
        itemEshop.setMessage(errorMssage);
        itemEshop.setAllMessage(errorMssage);
        return itemEshop;
    }

    public EshopInfo getEshopById(BigInteger eshopId) {
        return eshopService.getEshopInfoById(CurrentUser.getProfileId(), eshopId);
    }

    public boolean havaOrderNo(BigInteger id) {
        EshopOrderMarkParameter eshopOrderMarkParameter = new EshopOrderMarkParameter();
        eshopOrderMarkParameter.setEshopOrderId(id);
        eshopOrderMarkParameter.setProfileId(CurrentUser.getProfileId());
        eshopOrderMarkParameter.setMarkCodes(Collections.singletonList(BigInteger.valueOf(BaseOrderMarkEnum.DD_BIC.getCode())));
        List<EshopOrderMarkEntity> eshopOrderMarkEntities = orderMapper.queryEshopOrderMark(eshopOrderMarkParameter);
        if (null != eshopOrderMarkEntities && !eshopOrderMarkEntities.isEmpty()) {
            String bigData = eshopOrderMarkEntities.get(0).getBigData();
            bigData = bigData.replace("'", "");
            OrderMarkBigDataEntity orderMarkBigDataEntity = JSONObject.parseObject(bigData,
                    OrderMarkBigDataEntity.class);
            if (null == orderMarkBigDataEntity) {
                return false;
            }
            return StringUtils.isNotEmpty(orderMarkBigDataEntity.getOrderCode());
        }
        return true;
    }

    private void sendHuiBiaoMsg(List<BigInteger> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        try {
            logger.info("原单删除/恢复订单徽标发送消息，profileid:{}，ids: {}", CurrentUser.getProfileId(), ids);
            SaleOrderExStatusAsyncHandleHelper.notify(ids);
        } catch (Exception e) {
            logger.error("原单删除/恢复订单错误徽标发送消息，profileid:{}，id: {}，原因:{}", CurrentUser.getProfileId(), ids,
        e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteOrder(List<BigInteger> ids,OrderOpreateType opreateType) {
        if (CollectionUtils.isEmpty(ids) || null == opreateType) {
            return;
        }
        BigInteger profileId = CurrentUser.getProfileId();
        orderMapper.deleteOrRecoveryEshopOrderByIds(profileId, ids, 1);
        orderMapper.deleteOrRecoveryEshopOrderDetailByIds(profileId, ids, 1);
        List<EshopOrderMarkEntity> orderMarks = new ArrayList<>();
        ids.forEach(id -> {
            EshopOrderMarkEntity item = new EshopOrderMarkEntity();
            item.setId(UId.newId());
            item.setOrderId(id);
            item.setDetailId(BigInteger.ZERO);
            item.setProfileId(profileId);
            item.setOrderType(OrderSourceType.SaleOrder);
            item.setMarkTarget(OrderMarkType.Main);
            item.setMarkCode(BigInteger.valueOf(BaseOrderMarkEnum.DELETED_LOGIC.getCode()));
            item.setBubble("操作删除了的原始订单");
            item.setShowType(MarkShowType.All);
            item.setCreateType(MarkCreateType.DownloadSysCalc);
            orderMarks.add(item);
        });
        orderMapper.insertEshopOrderMark(orderMarks);
        cleanUnRelationDetailWhenDeletedOrder(profileId, ids);
        orderDao.doMarkStockAddNew(ids, QtyChangeSourceType.DELETE);
        buildLog(ids, profileId, "订单删除",opreateType);
        sendHuiBiaoMsg(ids);
    }

    private void cleanUnRelationDetailWhenDeletedOrder(BigInteger profileId, List<BigInteger> ids) {
        if (!bizConfig.getCleanUnRelationDetailWhenDeletedOrder()) {
            return;
        }
        QueryOrderDetailParameter detailParameter = new QueryOrderDetailParameter();
        detailParameter.setProfileId(profileId);
        detailParameter.setEshopOrderIds(ids);
        List<EshopSaleOrderDetail> detailList = orderMapper.querySimpleDetails(detailParameter);
        if (CollectionUtils.isEmpty(detailList)) {
            return;
        }
        Map<String, List<EshopSaleOrderDetail>> collect =
detailList.stream().collect(Collectors.groupingBy(EshopSaleOrderDetail::getUniqueId));
        if (collect.isEmpty()) {
            return;
        }
        for (Map.Entry<String, List<EshopSaleOrderDetail>> entry : collect.entrySet()) {
            String key = entry.getKey();
            List<EshopSaleOrderDetail> orderDetails = entry.getValue();
            EshopProductMark mark = new EshopProductMark();
            mark.setProfileId(profileId);
            mark.setEshopId(orderDetails.get(0).getOtypeId());
            mark.setUniqueId(key);
            mark.setMarkCode(EshopProductMarkEnum.UN_RELATION_ORDER.getCode());
            productMapper.deleteProductMarkByMarkCode(mark);
        }
    }

    private void buildLog(List<BigInteger> ids, BigInteger profileId, String msg,OrderOpreateType opreateType) {
        QueryOrderParameter param = new QueryOrderParameter();
        param.setProfileId(profileId);
        ids.forEach(id -> {
            param.setEshopOrderId(id);
            EshopSaleOrderEntity simpleOrder = orderDao.getSimpleOrder(param);
            SysLogUtil.add(SysLogUtil.buildLog(simpleOrder, opreateType, msg));
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteRecovery(List<BigInteger> ids,OrderOpreateType opreateType) {
        if (CollectionUtils.isEmpty(ids) || null == opreateType) {
            return;
        }
        BigInteger profileId = CurrentUser.getProfileId();
        orderMapper.deleteOrRecoveryEshopOrderByIds(profileId, ids, 0);
        orderMapper.deleteOrRecoveryEshopOrderDetailByIds(profileId, ids, 0);
        EshopOrderMarkParameter parameter = new EshopOrderMarkParameter();
        parameter.setProfileId(profileId);
        parameter.setEshopOrderIds(ids);
        List<BigInteger> markCodes = new ArrayList<>();
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.DELETED_LOGIC.getCode()));
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.DELIVER_CANCEL.getCode()));
        parameter.setMarkCodes(markCodes);
        orderMapper.deleteEshopOrderMark(parameter);
        QueryOrderDetailParameter detailParameter = new QueryOrderDetailParameter();
        detailParameter.setProfileId(profileId);
        detailParameter.setEshopOrderIds(ids);
        List<EshopSaleOrderDetail> detailList = orderMapper.querySimpleDetails(detailParameter);
        if (CollectionUtils.isNotEmpty(detailList)) {
            for (EshopSaleOrderDetail detail : detailList) {
                if (detail.getDeleted() == 1 || detail.isMappingState()) {
                    continue;
                }
                relationSvc.insertUnRelationItem(detail, null, null, null);
            }
        }
        orderDao.doMarkStockAddNew(ids, QtyChangeSourceType.INSERT);
        buildLog(ids, profileId, "撤销删除订单",opreateType);
        sendHuiBiaoMsg(ids);
    }

    private void doInsertChange(List<DeliverBillUpdateRequest> changeInfoList) {
        for (DeliverBillUpdateRequest changeInfo : changeInfoList) {
            try {
                GetBeanUtil.getBean(EshopAdvanceSaleOrderMapper.class).insertChangeInfo(changeInfo);
            } catch (Exception ex) {
                logger.error(String.format("保存订单【%s】变更数据报错：%s", changeInfo.getTradeOrderId(), ex.getMessage()), ex);
            }
        }
    }

    private boolean buildAndCheckTradeStateChangeDetail(NotifyMainAndDetailChangeDto dto,
     EshopAdvanceOrderEntity local, UpdateOrderByTmcRequest request) {
        List<EshopAdvanceOrderDetail> details = local.getDetail();
        if (details == null || details.size() == 0) {
            return false;
        }
        List<DeliverBillDetailTradeStatusDTO> dtoDetails = new ArrayList<>();
        for (EshopAdvanceOrderDetail detail : details) {
            DeliverBillDetailTradeStatusDTO detailDto = new DeliverBillDetailTradeStatusDTO();
            detailDto.setTradeState(request.getTradeStatus());
            detailDto.setOrderDetailId(detail.getDetailId());
            detailDto.setOid(detail.getPlatform().getTradeOrderDetailId());
            dtoDetails.add(detailDto);
        }
        dto.setDetails(dtoDetails);
        return true;
    }

    private DeliverBillUpdateRequest getAdvanceTradeSateChange(UpdateOrderByTmcRequest request,
        EshopAdvanceOrderEntity local) {
        if (!ConsumerType.DIRECT_UPDATE_TRADE_STATUS.equals(request.getConsumerType())) {
            return null;
        }
        if (local.getPlatform().getLocalTradeState().equals(request.getTradeStatus())) {
            return null;
        }
        BigInteger vchcode = local.getVchcode();
        NotifyMainAndDetailChangeDto dto = new NotifyMainAndDetailChangeDto();
        dto.setTradeState(request.getTradeStatus());
        dto.setVchcode(vchcode);
        dto.setTradeId(local.getPlatform().getTradeId());
        dto.setPayTime(local.getPlatform().getTradePayTime());
        dto.setFinishTime(local.getPlatform().getTradeFinishTime());
        buildAndCheckTradeStateChangeDetail(dto, local, request);
        DeliverBillUpdateRequest info = buildBaseChangeInfo(local);
        info.setSubChangeType(UpdateTypeEnum.TRADE_STATE);
        info.setContent(JsonUtils.toJson(dto));
        info.setChangeType(UpdateBillTypeEnum.BILL);
        info.setCustomer(CustomerTypeEnum.DELIVER_BILL);
        return info;
    }

    private DeliverBillUpdateRequest buildBaseChangeInfo(EshopAdvanceOrderEntity order) {
        DeliverBillUpdateRequest changeInfo = new DeliverBillUpdateRequest();
        changeInfo.setId(UId.newId());
        changeInfo.setProfileId(CurrentUser.getProfileId());
        changeInfo.setTradeOrderId(order.getPlatform().getTradeId());
        changeInfo.setEshopOrderId(order.getVchcode());
        changeInfo.setOtypeId(order.getOtypeId());
        changeInfo.setModifyTime(new Date());
        changeInfo.setOid("");
        changeInfo.setProducer(ProducerTypeEnum.PRE_SALE_ORDER);
        return changeInfo;
    }

    private DeliverBillUpdateRequest buildMemoChangeInfo(EshopAdvanceOrderEntity local,
     UpdateOrderByTmcRequest request) {
        DeliverBillUpdateRequest info = buildBaseChangeInfo(local);
        SellerMemo sellerMemo = new SellerMemo();
        sellerMemo.setBuyerMessage(request.getBuyerMessage());
        sellerMemo.setSellerFlag(request.getSellerFlag().getCode());
        sellerMemo.setSellerMemo(request.getSellerMemo());
        sellerMemo.setSellerFlagMemo(local.getPlatform().getPlatformExtend().getSellerFlagMemo());
        info.setContent(JsonUtils.toJson(sellerMemo));
        info.setSubChangeType(UpdateTypeEnum.SELLER_MEMO);
        info.setChangeType(UpdateBillTypeEnum.BILL);
        info.setCustomer(CustomerTypeEnum.DELIVER_BILL);
        info.setProfileId(CurrentUser.getProfileId());
        return info;
    }

    private DeliverBillUpdateRequest getAdvanceMemoChangeInfo(UpdateOrderByTmcRequest request,
     EshopAdvanceOrderEntity local) {
        if (!ConsumerType.DIRECT_UPDATE_MEMO.equals(request.getConsumerType())) {
            return null;
        }
        boolean memo = !local.getPlatform().getSellerMemo().equals(request.getSellerMemo());
        boolean flag = !local.getPlatform().getSellerFlag().equals(request.getSellerFlag());
        if (memo || flag) {
            return buildMemoChangeInfo(local, request);
        }
        return null;
    }

    private DeliverBillUpdateRequest getTradeSateChange(UpdateOrderByTmcRequest request, EshopSaleOrderEntity order) {
        if (!ConsumerType.DIRECT_UPDATE_TRADE_STATUS.equals(request.getConsumerType())) {
            return null;
        }
        if (request.getTradeStatus().equals(order.getLocalTradeState())) {
            return null;
        }
        BigInteger vchcode = order.getId();
        NotifyMainAndDetailChangeDto dto = new NotifyMainAndDetailChangeDto();
        dto.setTradeState(request.getTradeStatus());
        dto.setVchcode(vchcode);
        dto.setTradeId(order.getTradeOrderId());
        dto.setPayTime(order.getTradePayTime());
        dto.setFinishTime(order.getTradeFinishTime());
        buildTradeStateChangeDetail(dto, order, request);
        DeliverBillUpdateRequest info = buildBaseChangeInfo(order);
        info.setSubChangeType(UpdateTypeEnum.TRADE_STATE);
        info.setContent(JsonUtils.toJson(dto));
        info.setChangeType(UpdateBillTypeEnum.BILL);
        info.setCustomer(CustomerTypeEnum.DELIVER_BILL);
        return info;
    }

    private void buildTradeStateChangeDetail(NotifyMainAndDetailChangeDto dto, EshopSaleOrderEntity online,
     UpdateOrderByTmcRequest request) {
        List<EshopSaleOrderDetail> details = online.getOrderDetails();
        if (details == null || details.size() == 0) {
            return;
        }
        List<DeliverBillDetailTradeStatusDTO> dtoDetails = new ArrayList<>();
        for (EshopSaleOrderDetail detail : details) {
            if (detail.getProcessState().compareTo(DetailProcessState.Submit) != 0) {
                continue;
            }
            DeliverBillDetailTradeStatusDTO detailDto = new DeliverBillDetailTradeStatusDTO();
            detailDto.setTradeState(request.getTradeStatus());
            detailDto.setOrderDetailId(detail.getId());
            detailDto.setOid(detail.getTradeOrderDetailId());
            dtoDetails.add(detailDto);
        }
        if (dtoDetails.size() == 0) {
            return;
        }
        dto.setDetails(dtoDetails);
    }

    private DeliverBillUpdateRequest getMemoChangeInfo(UpdateOrderByTmcRequest request, EshopSaleOrderEntity order) {
        if (!ConsumerType.DIRECT_UPDATE_MEMO.equals(request.getConsumerType())) {
            return null;
        }
        boolean memo = !order.getSellerMemo().equals(request.getSellerMemo());
        boolean flag = !order.getSellerFlag().equals(request.getSellerFlag());
        if (memo || flag) {
            return buildMemoChangeInfo(request, order);
        }
        return null;
    }

    @NotNull
    private DeliverBillUpdateRequest buildMemoChangeInfo(UpdateOrderByTmcRequest request, EshopSaleOrderEntity order) {
        DeliverBillUpdateRequest info = buildBaseChangeInfo(order);
        SellerMemo sellerMemo = new SellerMemo();
        sellerMemo.setBuyerMessage(request.getBuyerMessage());
        sellerMemo.setSellerFlag(request.getSellerFlag().getCode());
        sellerMemo.setSellerMemo(request.getSellerMemo());
        sellerMemo.setSellerFlagMemo(order.getExtend().getSellerFlagMemo());
        info.setContent(JsonUtils.toJson(sellerMemo));
        info.setSubChangeType(UpdateTypeEnum.SELLER_MEMO);
        info.setChangeType(UpdateBillTypeEnum.BILL);
        info.setCustomer(CustomerTypeEnum.DELIVER_BILL);
        return info;
    }

    private DeliverBillUpdateRequest buildBaseChangeInfo(EshopSaleOrderEntity order) {
        DeliverBillUpdateRequest changeInfo = new DeliverBillUpdateRequest();
        changeInfo.setId(UId.newId());
        changeInfo.setProfileId(order.getProfileId());
        changeInfo.setTradeOrderId(order.getTradeOrderId());
        changeInfo.setEshopOrderId(order.getId());
        changeInfo.setOtypeId(order.getOtypeId());
        changeInfo.setModifyTime(new Date());
        changeInfo.setOid("");
        changeInfo.setProducer(ProducerTypeEnum.SALE_ORDER);
        return changeInfo;
    }

    /**
     * 全渠道门店对应  /平台仓库对应 之后更新原始订单
     *
     * @param mappings
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateOrdersByStoreMapping(List<EshopPlatformStoreMapping> mappings) {
        if (CollectionUtils.isEmpty(mappings)) {
            return;
        }
        BigInteger profileId = CurrentUser.getProfileId();
        mappings.forEach(m -> {
            BigInteger eshopId = m.getEshopId();
            BigInteger ktypeId = m.getKtypeId();
            if (null == eshopId || BigInteger.ZERO.compareTo(eshopId) == 0 || null == ktypeId || BigInteger.ZERO.compareTo(ktypeId) == 0) {
                return;
            }
            String platformStoreStockId = m.getPlatformStoreStockId();
            if (StringUtils.isEmpty(platformStoreStockId)) {
                return;
            }
            //查询未对明细
            List<EshopSaleOrderDetail> details = orderMapper.queryOrderDetailByPlatformStockId(profileId, eshopId,
             platformStoreStockId);
            if (CollectionUtils.isEmpty(details)) {
                return;
            }
            //对应明细
            orderMapper.updateOrderDetailKtypeId(profileId, eshopId, ktypeId, platformStoreStockId);
            QueryOrderParameter parameter = new QueryOrderParameter();
            List<BigInteger> eshopOrderIds =
             details.stream().map(EshopSaleOrderDetail::getEshopOrderId).distinct().collect(Collectors.toList());
            List<BigInteger> detailIds =
             details.stream().map(EshopSaleOrderDetail::getId).distinct().collect(Collectors.toList());
            //标记删除
            EshopOrderMarkParameter markParameter = new EshopOrderMarkParameter();
            markParameter.setProfileId(profileId);
            markParameter.setEshopOrderDetailIds(detailIds);
            markParameter.setEshopOrderIds(eshopOrderIds);
            markParameter.setMarkCodes(Collections.singletonList(BigInteger.valueOf(BaseOrderMarkEnum.KTYPE_UN_RELATION.getCode())));
            markParameter.setMarkTarget(1);
            orderMapper.deleteEshopOrderMark(markParameter);
            parameter.setEshopOrderIds(eshopOrderIds);
            //主表仓库重算
            CommonUtil.initPageDevice(parameter);
            List<EshopSaleOrderEntity> orderEntityList = orderMapper.querySaleOrderList(parameter);
            if (CollectionUtils.isEmpty(orderEntityList)) {
                return;
            }
            EshopSaleOrderBuilder builder = EshopSaleOrderBuilder.getSingleton();
            for (EshopSaleOrderEntity order : orderEntityList) {
                EshopSaleOrderEntity oldOrder = CommonUtil.deepCopy(order, EshopSaleOrderEntity.class);
                QueryOrderDetailParameter detailParameter = new QueryOrderDetailParameter();
                detailParameter.setEshopOrderId(order.getId());
                List<EshopSaleOrderDetail> detailList = orderMapper.querySimpleDetails(detailParameter);
                if (CollectionUtils.isEmpty(detailList)) {
                    continue;
                }
                order.setOrderDetails(detailList);
                BigInteger detailKtypeId = detailList.get(0).getKtypeId();
                BigInteger localKtypeId = order.getKtypeId();
                if (detailKtypeId.compareTo(localKtypeId) != 0) {
                    Stock localKtype = eshopOrderBaseInfoService.getStockInfo(profileId, localKtypeId);
                    Stock onlineKtype = eshopOrderBaseInfoService.getStockInfo(profileId, detailKtypeId);
                    order.setKtypeId(detailKtypeId);
                    builder.buildDeliverType(order.getDeliverType(),order);
                    orderMapper.updateOrderKtype(profileId, order.getId(), detailKtypeId,order.getDeliverType().getCode(),order.getDeliverProcessType().getCode());
                    if (BigInteger.ZERO.compareTo(detailKtypeId) != 0) {
                        //标记删除
                        EshopOrderMarkParameter mark = new EshopOrderMarkParameter();
                        mark.setProfileId(profileId);
                        mark.setEshopOrderIds(Collections.singletonList(order.getId()));
                        mark.setMarkCodes(Collections.singletonList(BigInteger.valueOf(BaseOrderMarkEnum.KTYPE_UN_RELATION.getCode())));
                        orderMapper.deleteEshopOrderMark(mark);
                    }
                    SysLogUtil.add(SysLogUtil.buildLog(order, OrderOpreateType.STORE_MAPPING, String.format("原仓库： %s,新仓库： %s", null == localKtype ? "" : localKtype.getFullname(), null == onlineKtype ? "" : onlineKtype.getFullname())));
                    if (oldOrder.getDeliverType() != order.getDeliverType()){
                        SysLogUtil.add(SysLogUtil.buildLog(order, OrderOpreateType.STORE_MAPPING, String.format("原发货方： %s,新发货方： %s", null == oldOrder.getDeliverType() ? "" : oldOrder.getDeliverType().getDesc(), null == order.getDeliverType() ? "" : order.getDeliverType() .getDesc())));
                    }
                    if (oldOrder.getDeliverType() != order.getDeliverType()){
                        SysLogUtil.add(SysLogUtil.buildLog(order, OrderOpreateType.STORE_MAPPING, String.format("原发货流程方式： %s,新发货流程方式： %s", null == oldOrder.getDeliverProcessType() ? "" : oldOrder.getDeliverProcessType().getName(), null == order.getDeliverProcessType() ? "" : order.getDeliverProcessType() .getName())));
                    }
                    SaleOrderExStatusAsyncHandleHelper.notify(Collections.singletonList(order.getId()));
                }
            }
        });

    }

    public boolean checkOrderDetailUnitError(BigInteger ptypeId, BigInteger unitId) {
        if (null == ptypeId || BigInteger.ZERO.compareTo(ptypeId) == 0 ||
                null == unitId || BigInteger.ZERO.compareTo(unitId) == 0) {
            return false;
        }
        UnitPrice unit = eshopOrderBaseInfoService.findUnit(ptypeId, unitId);
        //禁用非基本浮动单位
        if (null == unit || 1 == unit.getUnitType()) {
            return true;
        }
        return false;
    }

    public String getSysDataInfo(String key) {
        String value = sysDataMapper.getSysData(CurrentUser.getProfileId(), key);
        if (StringUtils.isEmpty(value)) {
            value = "";
        }
        return value;
    }

    public List<EshopCycleOrderEntity> getCycleOrder(QueryOrderParameter param) {
        BigInteger profileId = CurrentUser.getProfileId();
        BigInteger otypeId = param.getOtypeIds().get(0);
        String tradeOrderId = param.getTradeOrderId();
        QueryOrderParameter newParam = new QueryOrderParameter();
        newParam.setOtypeIds(Collections.singletonList(otypeId));
        newParam.setPlatformParentOrderIds(Collections.singletonList(tradeOrderId));
        newParam.setQueryCyclePurchaseMainOrder(false);
        List<EshopSaleOrderEntity> cyclePurchaseOrders = orderMapper.queryAllSaleOrderFields(newParam);
        if (CollectionUtils.isEmpty(cyclePurchaseOrders)) {
            return null;
        }
        cyclePurchaseOrders.sort((o1, o2) -> {
            // 自定义比较逻辑，例如按多个字段排序
            return o1.getExtend().getCurrentPeriodNum() - o2.getExtend().getCurrentPeriodNum();
        });
        List<EshopCycleOrderEntity> result = new ArrayList<>();
        EshopAdvanceSaleOrderMapper advanceSaleOrderMapper = GetBeanUtil.getBean(EshopAdvanceSaleOrderMapper.class);
        for (EshopSaleOrderEntity cyclePurchaseOrder : cyclePurchaseOrders) {
            EshopCycleOrderEntity entity = new EshopCycleOrderEntity();
            entity.setPeriodFrequency(cyclePurchaseOrder.getExtend().getPeriodFrequency());
            entity.setPlanSignTime(cyclePurchaseOrder.getTiming().getPlanSignTime());
            entity.setPlanSendTime(cyclePurchaseOrder.getTiming().getPlanSendTime());
            entity.setLocalRefundState(cyclePurchaseOrder.getLocalRefundState().getName());
            entity.setLocalTradeState(cyclePurchaseOrder.getLocalTradeState().getName());

            QueryAdvanceOrderParameter advanceParam = new QueryAdvanceOrderParameter();
            advanceParam.setOtypeIds(Collections.singletonList(otypeId));
            advanceParam.setTradeOrderIds(Collections.singletonList(cyclePurchaseOrder.getTradeOrderId()));
            advanceParam.setQueryCyclePurchaseType(2);
            List<EshopAdvanceOrderEntity> advanceOrderEntities =
            advanceSaleOrderMapper.queryAllAdvanceOrderFields(advanceParam);
            BigInteger orderId = BigInteger.ZERO;
            if (CollectionUtils.isNotEmpty(advanceOrderEntities) && null != advanceOrderEntities.get(0) &&
                    null != advanceOrderEntities.get(0).getVchcode() && BigInteger.ZERO.compareTo(advanceOrderEntities.get(0).getVchcode()) != 0) {
                orderId = advanceOrderEntities.get(0).getVchcode();
            }
            List<SaleOrderSelectDeliverBillResponse> delivers =
             deliverService.getDeliverBillDetailsByOrderId(profileId, cyclePurchaseOrder.getOtypeId(), orderId, null);
            if (CollectionUtils.isNotEmpty(delivers)) {
                FullLinkStatusEnum fullLinkStatus = null;
                StringBuilder freightNumber = new StringBuilder();
                StringBuilder taskNumber = new StringBuilder();
                Date sendTime = null;
                for (SaleOrderSelectDeliverBillResponse deliver : delivers) {
                    if (null == deliver) {
                        continue;
                    }
                    if (null == fullLinkStatus || deliver.getFullLinkStatus().getCode() < fullLinkStatus.getCode()) {
                        fullLinkStatus = deliver.getFullLinkStatus();
                    }
                    if (StringUtils.isNotEmpty(deliver.getFreightNumber())) {
                        freightNumber.append(deliver.getFreightNumber());
                        freightNumber.append(";");
                    }
                    if (StringUtils.isNotEmpty(deliver.getTaskNumber())) {
                        taskNumber.append(deliver.getTaskNumber());
                        taskNumber.append(";");
                    }
                    if (null == sendTime || (null != deliver.getSendTime() && sendTime.after(deliver.getSendTime()))) {
                        sendTime = deliver.getSendTime();
                    }
                }
                entity.setFullLinkLinkShow(null == fullLinkStatus ? "" : fullLinkStatus.getDesc());
                entity.setFreightBTypeName(delivers.get(0).getFreightBTypeName());
                entity.setFreightNumber(CommonUtil.removeLastChar(freightNumber.toString(), ";"));
                entity.setTaskNumber(CommonUtil.removeLastChar(taskNumber.toString(), ";"));
                entity.setSendTime(sendTime);
            }
            result.add(entity);
        }
        return result;
    }

    public List<EshopSaleOrderEntity> getCyclePurchaseSubOrders(QueryOrderParameter param) {
        if (null == param || CollectionUtils.isEmpty(param.getOtypeIds()) || CollectionUtils.isEmpty(param.getPlatformParentOrderIds())) {
            return null;
        }
        param.setQueryCyclePurchaseMainOrder(false);
        List<EshopSaleOrderEntity> cyclePurchaseOrders = orderMapper.queryAllSaleOrderFields(param);
        if (CollectionUtils.isEmpty(cyclePurchaseOrders)) {
            return null;
        }
        EshopSaleOrderService orderService = GetBeanUtil.getBean(EshopSaleOrderService.class);
        QueryOrderDetailParameter queryOrderDetailParameter = new QueryOrderDetailParameter();
        queryOrderDetailParameter.setEshopOrderIds(cyclePurchaseOrders.stream().map(EshopSaleOrderEntity::getId).distinct().collect(Collectors.toList()));
        //批量查询明细
        List<EshopSaleOrderDetail> detailList = orderService.queryOrderDetails(queryOrderDetailParameter);
        if (CollectionUtils.isEmpty(detailList)) {
            return null;
        }
        List<EshopSaleDetailCombo> eshopSaleDetailCombos =
         orderService.queryOrderDetailComboRows(queryOrderDetailParameter);
        Map<BigInteger, List<EshopSaleOrderDetail>> map =
         detailList.stream().collect(Collectors.groupingBy(EshopSaleOrderDetail::getEshopOrderId));
        Map<BigInteger, List<EshopSaleDetailCombo>> collect =
         eshopSaleDetailCombos.stream().collect(Collectors.groupingBy(EshopSaleDetailCombo::getEshopOrderId));
        for (EshopSaleOrderEntity purchaseOrder : cyclePurchaseOrders) {
            orderDao.buildCycleOrderAvanceId(purchaseOrder);
            BigInteger id = purchaseOrder.getId();
            List<EshopSaleOrderDetail> details = map.get(id);
            List<EshopSaleDetailCombo> detailCombos = collect.get(id);
            if (CollectionUtils.isEmpty(details)) {
                continue;
            }
            purchaseOrder.setOrderDetails(details);
            purchaseOrder.setDetailCombos(detailCombos);
        }
        return cyclePurchaseOrders;
    }

    public boolean checkCycleOrderSubmit() {
        AutoSubmitConfig config = GlobalConfig.get(AutoSubmitConfig.class);
        return config.getEnableCycleOrderSubmit() || bizConfig.getEnableCycleOrderSubmit();
    }

    public boolean checkUnRelationProduct() {
        return Boolean.TRUE.equals(productMapper.checkUnRelationProduct(CurrentUser.getProfileId()));
    }

    public CheckOrderDelete checkDeleteOrder(List<EshopSaleOrderEntity> orders) {
        List<BigInteger> eshopOrderIds = new ArrayList<>();
        List<CheckOrderDelete.TipMsg> tipMsgs = new ArrayList<>();
        for (EshopSaleOrderEntity order : orders) {
            TradeTypeEnum orderSaleType = order.getOrderSaleType();
            //预售
            //已提交不允许删除
            if ((TradeTypeEnum.ADVANCE_FORWARD_SALE == orderSaleType ||
                    TradeTypeEnum.ADVANCE_FORWARD_SALE_BY_STOCK == orderSaleType)){
                if (ProcessState.Submit == order.getProcessState()){
                    tipMsgs.add(new CheckOrderDelete.TipMsg(order.getTradeOrderId(),"原始订单已经提交预售订单管理，订单已被过滤"));
                }else {
                    eshopOrderIds.add(order.getId());
                }
                continue;
            }
            //周期购
            if (TradeTypeEnum.CYCLE_PURCHASE == orderSaleType){
                if (ProcessState.Submit == order.getProcessState()){
                    tipMsgs.add(new CheckOrderDelete.TipMsg(order.getTradeOrderId(),"周期购订单已经提交预售订单管理，订单已被过滤"));
                }else {
                    eshopOrderIds.add(order.getId());
                    QueryOrderParameter newParam = new QueryOrderParameter();
                    newParam.setOtypeIds(Collections.singletonList(order.getOtypeId()));
                    newParam.setPlatformParentOrderIds(Collections.singletonList(order.getTradeOrderId()));
                    newParam.setQueryCyclePurchaseMainOrder(false);
                    List<EshopSaleOrderEntity> cyclePurchaseOrders = orderMapper.queryAllSaleOrderFields(newParam);
                    if (CollectionUtils.isNotEmpty(cyclePurchaseOrders)){
                        eshopOrderIds.addAll(cyclePurchaseOrders.stream().map(EshopSaleOrderBaseEntity::getId).distinct().collect(Collectors.toList()));
                    }
                }
                continue;
            }
            //送礼物订单
            if (TradeTypeEnum.VIRTUAL_GIFT_MAIN == orderSaleType){
                eshopOrderIds.add(order.getId());
                QueryOrderParameter newParam = new QueryOrderParameter();
                newParam.setOtypeIds(Collections.singletonList(order.getOtypeId()));
                newParam.setPlatformParentOrderIds(Collections.singletonList(order.getTradeOrderId()));
                List<EshopSaleOrderEntity> subOrders = orderMapper.queryAllSaleOrderFields(newParam);
                orderDao.BuildEshopOrderMainMark(subOrders, false);
                if (CollectionUtils.isNotEmpty(subOrders)){
                    Map<Boolean, List<EshopSaleOrderEntity>> grouped = subOrders.stream().collect(Collectors.partitioningBy(s -> s.getProcessState() == ProcessState.NoSubmit));
                    List<EshopSaleOrderEntity> submitSubOrders = grouped.get(false);
                    List<EshopSaleOrderEntity> noSubmitSubOrders = grouped.get(true);
                    //全部未提交 或者 已提交的订单标记了发货取消
                    if (CollectionUtils.isEmpty(submitSubOrders) ||
                            submitSubOrders.stream().allMatch(s->s.getOrderMarks().stream().anyMatch(m->m.getMarkCode().compareTo(BigInteger.valueOf(BaseOrderMarkEnum.DELIVER_CANCEL.getCode())) == 0))){
                        eshopOrderIds.addAll(subOrders.stream().map(EshopSaleOrderBaseEntity::getId).collect(Collectors.toList()));
                    }else {
                        tipMsgs.add(new CheckOrderDelete.TipMsg(order.getTradeOrderId(),"送礼物订单对应的收礼物子单不全满足删除条件，订单已被过滤"));
                    }
                }
                continue;
            }
            if (ProcessState.NoSubmit == order.getProcessState() ||
                    order.getOrderMarks().stream().anyMatch(m->m.getMarkCode().compareTo(BigInteger.valueOf(BaseOrderMarkEnum.DELIVER_CANCEL.getCode())) == 0)){
                eshopOrderIds.add(order.getId());
                continue;
            }
            if (order.getDeleted() == 0){
                tipMsgs.add(new CheckOrderDelete.TipMsg(order.getTradeOrderId(),"原始订单已经提交，订单已被过滤"));
                continue;
            }
        }
        return new CheckOrderDelete(eshopOrderIds, tipMsgs);
    }

    public void doSendTmc(EshopSaleOrderEntity item) {
        try {
            if(getQicCodeDelaySendTmcLock(item)){
                return;
            }
            EshopNotifyMapper mapper = GetBeanUtil.getBean(EshopNotifyMapper.class);
            int hasSendCount = mapper.queryMessageChangeCount(item.getProfileId(), item.getTradeOrderId(), item.getOtypeId(), TMCType.SUBMIT_CREATE_TMC.getCode());
            if (bizConfig.getQicCodeByDelayTmcMaxRetryCount() == hasSendCount || bizConfig.getQicCodeByDelayTmcMaxRetryCount() < hasSendCount){
                return;
            }
            SysMq sysMq = GetBeanUtil.getBean(SysMq.class);
            TmcOrderMessage tmcMsg = new TmcOrderMessage();
            tmcMsg.setEshopId(item.getOtypeId());
            tmcMsg.setProfileId(item.getProfileId());
            tmcMsg.setTradeId(item.getTradeOrderId());
            tmcMsg.setCreateTime(new Date());
            List<MqSendResult<TmcOrderMessage>> results = sysMq.sendDelayTime(Collections.singletonList(tmcMsg), SyncOrderConst.TmcTopIcName, bizConfig.getQicCodeByDelayTmc());
            if (CollectionUtils.isEmpty(results)){
                return;
            }
            for (MqSendResult<TmcOrderMessage> mqSendResult: results) {
                TmcOrderMessage message = mqSendResult.getMessage();
                if (message == null) {
                    continue;
                }
                EshopNotifyChange notify = new EshopNotifyChange();
                notify.setId(UId.newId());
                notify.setProfileId(item.getProfileId());
                notify.setTradeOrderId(item.getTradeOrderId());
                notify.setEshopId(item.getOtypeId());
                notify.setType(TMCType.SUBMIT_CREATE_TMC);
                notify.setContent("");
                if (mqSendResult.isSuccess()) {
                    notify.setMessage(String.format("MQ发送成功，MessageId：%s", mqSendResult.getMessageId()));
                }else {
                    notify.setMessage(String.format("MQ发送失败，失败原因：%s", mqSendResult.getErrorMsg()));
                }
                mapper.insertEshopNotifyChangeBySubmit(notify);
            }
//            disposeQicCodeDelaySendTmcLock(item);
        }catch (Exception e){
            CommonUtil.doLogByEnable(logger, LogLevelEnum.ERROR,String.format("profileId:%s,tradeOrderId:%s, no qic order send tmc error",item.getProfileId(),item.getTradeOrderId()));
            disposeQicCodeDelaySendTmcLock(item);
        }
    }

    private boolean getQicCodeDelaySendTmcLock(EshopSaleOrderEntity item) {
        try{
            String key = String.format("%s_%s_%s", item.getProfileId(), item.getOtypeId(), item.getTradeOrderId());
            String md5Key = String.format("QIC_CODE_DELAY_SEND_TMC_%s", Md5Utils.md5(key));
            StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
            //返回true，表示键md5Key原本不存在，键值对<md5Key, key>已经被成功设置。如果返回false，则意味着键已经存在
            // true 获取到了锁
            // false 没有获取到锁
            Boolean ifAbsent = template.opsForValue().setIfAbsent(md5Key, key, bizConfig.getQicCodeByDelayTmcLockTime(), TimeUnit.MINUTES);
            return ifAbsent == null || !ifAbsent;
        } catch (Exception e) {
            logger.error("qicCodeDelaySendTmcError，profileId:{}，tradeOrderId:{}", CurrentUser.getProfileId(),item.getTradeOrderId());
            return false;
        }
    }

    public void disposeQicCodeDelaySendTmcLock(EshopSaleOrderEntity item) {
        String key = String.format("%s_%s_%s", item.getProfileId(), item.getOtypeId(), item.getTradeOrderId());
        String md5Key = String.format("QIC_CODE_DELAY_SEND_TMC_%s", Md5Utils.md5(key));
        try {
            StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
            template.delete(md5Key);
        } catch (Exception ex) {
            logger.error("qicCodeDelaySendTmcDisposeLockError，profileId:{}，key:{}", CurrentUser.getProfileId(),key);
            StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
            template.delete(md5Key);
        }
    }

    public List<SaleOrderSelectDeliverBillResponse>  getSaleOrderBillByOrderId(SaleOrderSelectDeliverBillRequest request) {
        List<SaleOrderSelectDeliverBillResponse> response = new ArrayList<>();
        List<SaleOrderSelectDeliverBillResponse> deliverInfo = deliverService.getDeliverBillDetailsByOrderId(CurrentUser.getProfileId(), request.getOtypeId(), request.getOrderId(), request.getTradeOrderId());
        if (!CollectionUtils.isEmpty(deliverInfo))
        {
            response.addAll(deliverInfo);
        }
        if (request.getOrderId() != null && request.getOrderId().compareTo(BigInteger.ZERO) > 0) {
            PlatformCheckMapper checkMapper = GetBeanUtil.getBean(PlatformCheckMapper.class);
            List<SaleOrderSelectDeliverBillResponse> gatherInfo = checkMapper.querySaleOrderGatherInfo(CurrentUser.getProfileId(), Arrays.asList(request.getOrderId()));
            if (!CollectionUtils.isEmpty(gatherInfo)) {
                response.addAll(gatherInfo);
            }
        }
        return response;
    }

    public EshopOrderAutoSubmitConfig getEshopOrderAutoSubmitConfig(){
        BigInteger profileId = CurrentUser.getProfileId();
        String key = "ESHOP_ORDER_AUTO_SUBMIT_CONFIG";
        List<String> sysData = bizMapper.getSysData(profileId, key);
        if (CollectionUtils.isEmpty(sysData)){
            return null;
        }
        String subValue = sysData.get(0);
        return JsonUtils.toObject(subValue,EshopOrderAutoSubmitConfig.class);
    }

    public void saveEshopOrderAutoSubmitConfig(EshopOrderAutoSubmitConfig config){
        if (null == config){
            return;
        }
        BigInteger profileId = CurrentUser.getProfileId();
        String key = "ESHOP_ORDER_AUTO_SUBMIT_CONFIG";
        List<String> sysData = bizMapper.getSysData(profileId, key);
        if (CollectionUtils.isEmpty(sysData)){
            //新增
            SysData data = new SysData();
            data.setId(UId.newId());
            data.setProfileId(profileId);
            data.setSubName(key);
            data.setSubValue(JsonUtils.toJson(config));
            data.setDescription("原单自动提交设置");
            bizMapper.addSysData(data);
            return;
        }
        bizMapper.modifySysData(profileId,key,JsonUtils.toJson(config));
    }
}

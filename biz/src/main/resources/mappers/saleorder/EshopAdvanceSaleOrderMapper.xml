<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopAdvanceSaleOrderMapper">
    <sql id="query_columns">
        `core`.vchcode,
        `core`.otype_id,
        `core`.profile_id,
        `core`.create_type,
        `core`.create_etype_id as 'core.create_etype_id',
        `core`.platform_parent_order_id,
        `core`.business_type,
        `core`.order_source_type,
        `core`.deliver_type,
        `core`.create_time,
        `core`.update_time,
        `core`.currency_bill_total,
        `core`.buyer_id,
        `core`.btype_id,
        `core`.ktype_id,
        `core`.dtype_id,
        `core`.memo,
        `core`.submit_send_state as 'submit_send_state',
        `core`.order_sale_type,
        `core`.self_delivery_mode,
        `core`.summary,
        `platform`.trade_id as 'platform.trade_id',
		`platform`.platform_trade_state as 'platform.platform_trade_state',
		`platform`.local_trade_state as 'platform.local_trade_state',
		`platform`.local_refund_process_state as 'platform.local_refund_process_state',
		`platform`.seller_flag as 'platform.seller_flag',
		`platform`.platform_stock_id as 'platform.platform_stock_id',
		`platform`.platform_store_id as 'platform.platform_store_id',
		`platform`.platform_store_code as 'platform.platform_store_code',
		`platform`.re_send_state as 'platform.re_send_state',
		`platform`.pay_time_type as 'platform.pay_time_type',
		`platform`.trade_create_time as 'platform.trade_create_time',
		`platform`.group_header_name as 'platform.group_header_name',
		`platform`.trade_modified_time as 'platform.trade_modified_time',
		`platform`.trade_finish_time as 'platform.trade_finish_time',
		`platform`.trade_pay_time as 'platform.trade_pay_time',
		`platform`.modified_time as 'platform.modified_time',
		`platform`.platform_freight_name as 'platform.platform_freight_name',
		`platform`.platform_freight_code as 'platform.platform_freight_code',
		`platform`.customer_expected_freight_name as 'platform.customer_expected_freight_name',
		`platform`.customer_expected_freight_code as 'platform.customer_expected_freight_code',
		`platform`.trade_total as 'platform.trade_total',
		`platform`.buyer_trade_total as 'platform.buyer_trade_total',
		`platform`.buyer_unpaid_total as 'platform.buyer_unpaid_total',
		`platform`.buyer_paid_total as 'platform.buyer_paid_total',
        `platform`.mapping_state as 'platform.mapping_state',
        `platform`.order_deliver_required as 'platform.order_deliver_required',
        `platform`.seller_memo as 'platform.seller_memo',
        `platform`.buyer_message as 'platform.buyer_message',
        `platform`.pay_no as 'platform.pay_no',
        `platform`.unique_mark as 'platform.unique_mark',
        `platform`.platform_order_preferential_total as 'platform.platform_order_preferential_total',
        `platform`.local_freight_name as 'platform.local_freight_name',
        `platform`.local_freight_code as 'platform.local_freight_code',
        `platform`.local_freight_bill_no as 'platform.local_freight_bill_no',
        `platform`.local_refund_state as 'platform.local_refund_state',
        platform.export_count as 'platform.export_count',
        `platform`.salesman as 'platform.salesman',
        `platform`.platform_ptype_preferential_total as 'platform.platform_ptype_preferential_total',
        `platform`.installation_service_provider as 'platform.installation_service_provider',
        IF(TO_DAYS(`platform`.collect_time)=TO_DAYS('1990-01-01'),'' ,`platform`.collect_time) AS  'platform.collectTime',
        `platform`.collect_customer as 'platform.collect_customer',
        `platform`.gather_status as 'platform.gather_status',
        `platform`.payment_mode as 'platform.payment_mode',
        `platform`.platform_business_mark as 'platform.platform_business_mark',
        `platform`.hold_time as 'platform.hold_time',
        `platform`.submit_batch_id as 'platform.submitBatchId',
        `platform`.purchase_total as 'platform.purchaseTotal',
        `platformExtend`.vchcode as 'platform.platformExtend.vchcode',
        `platformExtend`.profile_id as 'platform.platformExtend.profile_id',
        `platformExtend`.pickup_code as 'platform.platformExtend.pickup_code',
        `platformExtend`.platform_dispatcher_name as 'platform.platformExtend.platform_dispatcher_name',
        `platformExtend`.platform_dispather_mobile as 'platform.platformExtend.platform_dispather_mobile',
        `platformExtend`.logistics_status as 'platform.platformExtend.logistics_status',
        `platformExtend`.confirm_status as 'platform.platformExtend.confirm_status',
        `platformExtend`.real_buyer_id as 'platform.platformExtend.real_buyer_id',
        `platformExtend`.pick_up_address_id as 'platform.platformExtend.pick_up_address_id',
        `platformExtend`.platform_qc_result as 'platform.platformExtend.platform_qc_result',
        `platformExtend`.platform_identify_result as 'platform.platformExtend.platform_identify_result',
        `platformExtend`.flow_channel as 'platform.platformExtend.flow_channel',
        `platformExtend`.mall_deduction_fee as 'platform.platformExtend.mall_deduction_fee',
        `platformExtend`.seller_flag_memo as 'platform.platformExtend.seller_flag_memo',
        `platformExtend`.group_title as 'platform.platformExtend.group_title',
        `platformExtend`.group_no as 'platform.platformExtend.group_no',
        `platformExtend`.take_goods_code as 'platform.platformExtend.take_goods_code',
        `platformExtend`.platform_quality_org_id as 'platform.platformExtend.platform_quality_org_id',
        `platformExtend`.platform_quality_org_name as 'platform.platformExtend.platform_quality_org_name',
        `platformExtend`.platform_quality_warehouse_code as 'platform.platformExtend.platform_quality_warehouse_code',
        `platformExtend`.platform_quality_warehouse_name as 'platform.platformExtend.platform_quality_warehouse_name',
        `platformExtend`.anchor_order_preferential_total as 'platform.platformExtend.anchor_order_preferential_total',
        `platformExtend`.platform_order_subsidy_total as 'platform.platformExtend.platform_order_subsidy_total',
        `platformExtend`.sale_period_num as 'platform.platformExtend.sale_period_num',
        `platformExtend`.current_period_num as 'platform.platformExtend.current_period_num',
        `platformExtend`.national_subsidy_total as 'platform.platformExtend.national_subsidy_total',
        if(`platform`.distribution_dised_taxed_total = 0,`core`.currency_bill_total,`platform`.distribution_dised_taxed_total) as 'platform.distribution_dised_taxed_total',
        `assinfo`.currency_ptype_preferential_total as 'assinfo.currency_ptype_preferential_total',
		`assinfo`.currency_order_buyer_freight_fee as 'assinfo.currency_order_buyer_freight_fee',
		`assinfo`.currency_ptype_service_fee as 'assinfo.currency_ptype_service_fee',
		`assinfo`.currency_advance_total as 'assinfo.currency_advance_total',
		(`core`.currency_bill_total + `assinfo`.currency_order_buyer_freight_fee + `assinfo`.currency_ptype_service_fee) as 'platform.customerPayment',
        `assinfo`.currency_order_preferential_allot_total as 'assinfo.currency_order_preferential_allot_total',
        `assinfo`.currency_ptype_commission_total as 'assinfo.currency_ptype_commission_total',
        invoice.invoice_state AS 'invoice.invoice_state',
        invoice.invoice_required AS 'invoice.invoice_required',
        invoice.invoice_type AS 'invoice.invoice_type',
        invoice.invoice_code AS 'invoice.invoice_code',
        invoice.invoice_remark AS 'invoice.invoice_remark',
        invoice.invoice_category AS 'invoice.invoice_category',
        invoice.invoice_title AS 'invoice.invoice_title',
        invoice.invoice_company AS 'invoice.invoice_company',
        invoice.invoice_register_addr AS 'invoice.invoice_register_addr',
        invoice.invoice_register_phone AS 'invoice.invoice_register_phone',
        invoice.invoice_bank AS 'invoice.invoice_bank',
        invoice.invoice_bank_account AS 'invoice.invoice_bank_account',
        invoice.secret_id AS 'invoice.secret_id',
        timing.timing_type AS 'timing.timing_type',
        `timing`.promised_collect_time AS 'timing.promised_collect_time',
        `timing`.promised_sign_time AS 'timing.promised_sign_time',
        `timing`.send_time AS 'timing.send_time',
        `timing`.plan_send_time  AS 'timing.plan_send_time',
        `timing`.plan_sign_time  AS 'timing.plan_sign_time',
        `timing`.sign_time  AS 'timing.sign_time',
        `timing`.promised_sign_startTime  AS 'timing.promised_sign_startTime',
        `buyer`.customer_receiver AS 'eshopBuyer.customer_receiver',
		`buyer`.customer_id_card AS 'eshopBuyer.customer_id_card',
		`buyer`.customer_id_card_name AS 'eshopBuyer.customer_id_card_name',
		`buyer`.customer_receiver_phone AS 'eshopBuyer.customer_receiver_phone',
		`buyer`.customer_receiver_mobile AS 'eshopBuyer.customer_receiver_mobile',
		`buyer`.customer_receiver_zip_code AS 'eshopBuyer.customer_receiver_zip_code',
		`buyer`.customer_email AS 'eshopBuyer.customer_email',
		`buyer`.customer_receiver_country AS 'eshopBuyer.customer_receiver_country',
		`buyer`.customer_receiver_province AS 'eshopBuyer.customer_receiver_province',
		`buyer`.customer_receiver_city AS 'eshopBuyer.customer_receiver_city',
		`buyer`.customer_receiver_district AS 'eshopBuyer.customer_receiver_district',
		`buyer`.customer_receiver_address AS 'eshopBuyer.customer_receiver_address',
		`buyer`.customer_receiver_full_address AS 'eshopBuyer.customer_receiver_full_address',
		`buyer`.customer_shop_account AS 'eshopBuyer.customer_shop_account',
		`buyer`.customer_pay_account AS 'eshopBuyer.customer_pay_account',
		`buyer`.customer_receiver_town AS 'eshopBuyer.customer_receiver_town',
		`buyer`.di AS 'eshopBuyer.di',
        `rbi`.customer_receiver AS 'platform.platformExtend.realBuyer.customer_receiver',
		`rbi`.customer_id_card AS 'platform.platformExtend.realBuyer.customer_id_card',
		`rbi`.customer_id_card_name AS 'platform.platformExtend.realBuyer.customer_id_card_name',
		`rbi`.customer_receiver_phone AS 'platform.platformExtend.realBuyer.customer_receiver_phone',
		`rbi`.customer_receiver_mobile AS 'platform.platformExtend.realBuyer.customer_receiver_mobile',
		`rbi`.customer_receiver_zip_code AS 'platform.platformExtend.realBuyer.customer_receiver_zip_code',
		`rbi`.customer_email AS 'platform.platformExtend.realBuyer.customer_email',
		`rbi`.customer_receiver_country AS 'platform.platformExtend.realBuyer.customer_receiver_country',
		`rbi`.customer_receiver_province AS 'platform.platformExtend.realBuyer.customer_receiver_province',
		`rbi`.customer_receiver_city AS 'platform.platformExtend.realBuyer.customer_receiver_city',
		`rbi`.customer_receiver_district AS 'platform.platformExtend.realBuyer.customer_receiver_district',
		`rbi`.customer_receiver_address AS 'platform.platformExtend.realBuyer.customer_receiver_address',
		`rbi`.customer_receiver_full_address AS 'platform.platformExtend.realBuyer.customer_receiver_full_address',
		`rbi`.customer_shop_account AS 'platform.platformExtend.realBuyer.customer_shop_account',
		`rbi`.customer_pay_account AS 'platform.platformExtend.realBuyer.customer_pay_account',
		`rbi`.customer_receiver_town AS 'platform.platformExtend.realBuyer.customer_receiver_town',
		`rbi`.di AS 'platform.platformExtend.realBuyer.di',
        `puai`.customer_receiver AS 'platform.platformExtend.selfPickUpInfo.customer_receiver',
		`puai`.customer_id_card AS 'platform.platformExtend.selfPickUpInfo.customer_id_card',
		`puai`.customer_id_card_name AS 'platform.platformExtend.selfPickUpInfo.customer_id_card_name',
		`puai`.customer_receiver_phone AS 'platform.platformExtend.selfPickUpInfo.customer_receiver_phone',
		`puai`.customer_receiver_mobile AS 'platform.platformExtend.selfPickUpInfo.customer_receiver_mobile',
		`puai`.customer_receiver_zip_code AS 'platform.platformExtend.selfPickUpInfo.customer_receiver_zip_code',
		`puai`.customer_email AS 'platform.platformExtend.selfPickUpInfo.customer_email',
		`puai`.customer_receiver_country AS 'platform.platformExtend.selfPickUpInfo.customer_receiver_country',
		`puai`.customer_receiver_province AS 'platform.platformExtend.selfPickUpInfo.customer_receiver_province',
		`puai`.customer_receiver_city AS 'platform.platformExtend.selfPickUpInfo.customer_receiver_city',
		`puai`.customer_receiver_district AS 'platform.platformExtend.selfPickUpInfo.customer_receiver_district',
		`puai`.customer_receiver_address AS 'platform.platformExtend.selfPickUpInfo.customer_receiver_address',
		`puai`.customer_receiver_full_address AS 'platform.platformExtend.selfPickUpInfo.customer_receiver_full_address',
		`puai`.customer_shop_account AS 'platform.platformExtend.selfPickUpInfo.customer_shop_account',
		`puai`.customer_pay_account AS 'platform.platformExtend.selfPickUpInfo.customer_pay_account',
		`puai`.customer_receiver_town AS 'platform.platformExtend.selfPickUpInfo.customer_receiver_town',
		`puai`.di AS 'platform.platformExtend.selfPickUpInfo.di',
        `eshop`.fullname AS 'otype_name',
		`eshop`.eshop_type AS 'shopType',
        `otype`.ocategory,
		`etype`.fullname AS 'etype_name',
		`etype`.id AS 'etype_id',
		`ktype`.fullname AS 'ktype_name',
		`btype`.id AS 'btypeId',
		`btype`.fullname AS 'btype_name',
        `smap`.platform_store_name AS 'platform.platform_stock_name',
        `supplier`.id as 'supplierId',
		`supplier`.fullname as 'supplier_name',
        `distribution`.profile_id as 'distributionBuyer.profile_id',
        `distribution`.distribution_buyer_trade_id as 'distributionBuyer.distribution_buyer_trade_id',
        `distribution`.vchcode as 'distributionBuyer.vchcode',
        `distribution`.create_time as 'distributionBuyer.create_time',
        `distribution`.update_time as 'distributionBuyer.update_time'
    </sql>

    <sql id="advance_jointable">
		LEFT JOIN pl_eshop eshop ON `eshop`.profile_id = `core`.profile_id AND  `eshop`.otype_id= `core`.otype_id
		LEFT JOIN base_otype `otype` ON `otype`.profile_id = `core`.profile_id AND  `otype`.id= `core`.otype_id
		LEFT JOIN pl_buyer buyer ON `buyer`.buyer_id=`core`.buyer_id AND `buyer`.profile_id=`core`.profile_id
		LEFT JOIN base_btype btype ON btype.id=`core`.btype_id AND btype.profile_id=`core`.profile_id
		LEFT JOIN base_ktype ktype ON ktype.id=`core`.ktype_id AND ktype.profile_id=`core`.profile_id
		LEFT JOIN base_etype etype ON etype.id=`core`.etype_id AND etype.profile_id=`core`.profile_id
		LEFT JOIN td_orderbill_timing timing ON timing.profile_id=`core`.profile_id AND timing.vchcode=`core`.vchcode
		LEFT JOIN td_orderbill_invoice_info invoice ON invoice.profile_id=`core`.profile_id AND invoice.vchcode=`core`.vchcode
		LEFT JOIN td_orderbill_platform platform ON platform.profile_id=`core`.profile_id AND platform.vchcode=`core`.vchcode
		LEFT JOIN td_orderbill_platform_extend platformExtend ON platformExtend.profile_id=`core`.profile_id AND platformExtend.vchcode=`core`.vchcode
		LEFT JOIN td_orderbill_distribution_buyer distribution ON distribution.profile_id=`core`.profile_id AND distribution.vchcode=`core`.vchcode
		LEFT JOIN pl_buyer rbi ON `rbi`.buyer_id=`platformExtend`.real_buyer_id AND `rbi`.profile_id=`platformExtend`.profile_id
		LEFT JOIN pl_buyer puai ON `puai`.buyer_id=`platformExtend`.pick_up_address_id AND `puai`.profile_id=`platformExtend`.profile_id
		LEFT JOIN base_btype supplier ON supplier.id = core.supplier_id AND supplier.profile_id = core.profile_id
        LEFT JOIN pl_eshop_platform_store_mapping smap ON smap.platform_store_stock_id = `platform`.platform_stock_id AND smap.profile_id = `platform`.profile_id AND smap.eshop_id = `core`.otype_id
        LEFT JOIN td_orderbill_assinfo assinfo on assinfo.vchcode = core.vchcode and assinfo.profile_id = core.profile_id
    </sql>

    <sql id="common-query-mark">
        <choose>
            <when test="markCondition==0">
                IN
                ( SELECT order_id FROM `pl_eshop_order_mark` AS mark
                WHERE mark.order_id = `core`.vchcode AND mark.profile_id=`core`.profile_id
                AND mark.`mark_code` IN
                <foreach collection="marks" close=")" open="(" separator="," item="mark">
                    #{mark}
                </foreach>
                )
            </when>
            <when test="markCondition==1">
                NOT IN
                (SELECT order_id FROM
                `pl_eshop_order_mark` AS mark
                WHERE mark.profile_id=`core`.profile_id AND mark.order_id = `core`.vchcode
                and mark.mark_code in
                <foreach collection="marks" close=")" open="(" separator="," item="item">
                    #{item}
                </foreach>
                )
            </when>
            <when test="markCondition==2">
                IN
                (SELECT order_id FROM
                pl_eshop_order_mark AS mark
                WHERE mark.profile_id=`core`.profile_id AND mark.order_id = `core`.vchcode
                and mark.mark_code in
                <foreach collection="marks" close=")" open="(" separator="," item="item">
                    #{item}
                </foreach>
                group by mark.order_id
                HAVING COUNT(DISTINCT mark.mark_code) = #{markCount})
                and
                (EXISTS
                (SELECT 1 FROM pl_eshop_order_mark AS mark
                WHERE mark.profile_id=`core`.profile_id AND  mark.order_id = `core`.vchcode
                HAVING COUNT(DISTINCT mark.mark_code) = #{markCount})
                )
            </when>
        </choose>
    </sql>

    <select id="queryAdvanceSaleOrderList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderEntity">
        SELECT 0 selected,
        <include refid="query_columns"/>
        FROM  ${tableName}
        <include refid="advance_jointable"/>
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=`core`.profile_id and bls.object_type=3 and
            `core`.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        <if test="ktypeLimited">
            inner join base_limit_scope kls on kls.profile_id=`core`.profile_id and kls.object_type=2 and
            `core`.ktype_id=kls.object_id and kls.etype_id=#{etypeId}
        </if>
        WHERE `core`.profile_id=#{profileId}
        <if test="allowEtypeIds!=null and allowEtypeIds.size()>0 and etypeLimited">
            and `core`.etype_id in
            <foreach collection="allowEtypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="submitBatchId != null">
            and `platform`.submit_batch_id=#{submitBatchId}
        </if>
        <if test="eshopOrderIdList!=null and eshopOrderIdList.size()>0">
            AND `core`.vchcode IN
            <foreach collection="eshopOrderIdList" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="tradeOrderId!=null and tradeOrderId!=''">
            and platform.trade_id=#{tradeOrderId}
        </if>
        <if test="processState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.ProcessState@Submit">
            AND `core`.submit_send_state=1 AND `platform`.mapping_state=1
        </if>
        <if test="processState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.ProcessState@NoSubmit">
            AND `core`.submit_send_state=0
            AND `platform`.mapping_state=1 AND `platform`.order_deliver_required=1
            AND `platform`.local_trade_state!=5
        </if>

          <include refid="advance_query_new"/>

        <if test="orderSaleType !=null and orderSaleType.size() > 0 ">
            and `core`.order_sale_type IN
            <foreach collection="orderSaleType" close=")" open="(" separator="," item="simpleType">
                #{simpleType}
            </foreach>
        </if>
        <if test="intSellerFlag !=null and intSellerFlag>0">
            AND `platform`.seller_flag=#{intSellerFlag}
        </if>
        <if test="filterKeyType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyType@Trade_ID and keyArrays!=null and keyArrays.size()>0">
            and trade_id in
            <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="filterKeyType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyType@PICK_UP_CODE and keyArrays!=null and keyArrays.size()>0">
            and `platformExtend`.pickup_code in
            <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="filterKeyType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyType@BIC_CODE and keyArrays!=null and keyArrays.size()>0">
            AND EXISTS(select 1 from td_orderbill_detail_platform
            where profile_id = #{profileId}  and vchcode = `core`.vchcode and
            <foreach collection="keyArrays" close=")" open="(" separator="or" item="key">
                verify_code like concat ('%',#{key},'%')
            </foreach>)
        </if>
        <if test="filterKeyType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyType@DISTRIBUTION_BUYER_TRADE_ID and keyArrays!=null and keyArrays.size()>0">
            AND (
            EXISTS(select 1 from td_orderbill_distribution_buyer mark
            where profile_id = #{profileId}  and vchcode = `core`.vchcode and
            <foreach collection="keyArrays" close=")" open="(" separator="or" item="key">
                distribution_buyer_trade_id like concat ('%',#{key},'%')
            </foreach>)
            OR
            EXISTS(select 1 from td_orderbill_detail_distribution_buyer
            where profile_id = #{profileId}  and vchcode = `core`.vchcode and
            <foreach collection="keyArrays" close=")" open="(" separator="or" item="key">
                distribution_buyer_trade_detail_id like concat ('%',#{key},'%')
            </foreach>)
            )
        </if>
        <if test="filterKeyType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyType@PLATFORM_PRODUCT_NAME and keyArrays!=null and keyArrays.size()>0">
            AND EXISTS(SELECT 1 FROM `td_orderbill_detail_platform` AS esd
            WHERE esd.vchcode = `core`.vchcode and esd.profile_id=#{profileId} AND esd.platform_product_name in
            <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>)
        </if>
        <if test="filterKeyType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyType@PLATFORM_XCODE and keyArrays!=null and keyArrays.size()>0">
            AND EXISTS(SELECT 1 FROM `td_orderbill_detail_platform` AS esd
            WHERE esd.vchcode = `core`.vchcode and esd.profile_id=#{profileId} AND esd.platform_xcode in
            <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>)
        </if>
        <if test="filterKeyType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyType@PLATFORM_PROPERTIES_NAME and keyArrays!=null and keyArrays.size()>0">
            AND EXISTS(SELECT 1 FROM `td_orderbill_detail_platform` AS esd
            WHERE esd.vchcode = `core`.vchcode and esd.profile_id=#{profileId} AND esd.platform_properties_name in
            <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>)
        </if>
        <if test="filterKeyType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryFilterKeyType@TAKE_GOOD_CODE and keyArrays!=null and keyArrays.size()>0">
            AND platformExtend.take_goods_code like CONCAT('%',#{keyArrays[0]},'%')
        </if>
        <if test="advanceTimeType != null and advanceTimeType == 0">
            AND `platform`.trade_create_time between #{beginTime} and #{endTime}
        </if>
        <if test="advanceTimeType != null and advanceTimeType == 1">
            AND `platform`.trade_pay_time between #{beginTime} and #{endTime}
        </if>
        <if test="advanceTimeType != null and advanceTimeType == 2">
            AND `core`.create_time between #{beginTime} and #{endTime}
        </if>
        <if test="advanceTimeType != null and advanceTimeType == 3">
            AND `timing`.plan_send_time between #{beginTime} and #{endTime}
        </if>
        <if test="queryCyclePurchaseType==0">
            AND (`core`.order_sale_type != 3 OR (`core`.order_sale_type=3 AND `core`.platform_parent_order_id=''))
        </if>
        <if test="queryCyclePurchaseType==1">
            AND `core`.order_sale_type = 3  AND `core`.platform_parent_order_id != ''
        </if>
       <if test="advanceFilterTypes !=null and advanceFilterTypes.size() > 0 ">
            AND ( 1!=1
            <foreach collection="advanceFilterTypes" close="" open="" separator="" item="advanceFilterType">
                <include refid="advance_order_quick_filter_or"/>
            </foreach>
            )
        </if>
        <if test="filterParameter != null">
             <include refid="advance_order_quick_filter"/>
            <include refid="order_filter"/>
        </if>
        <if test="filterParameter == null or (filterParameter != null and filterParameter.defaultSort)">
            order by `platform`.trade_pay_time desc
        </if>
    </select>

    <sql id="advance_order_quick_filter">
         <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@REFUNDING">
            AND `platform`.local_refund_state=1
        </if>
        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@PART_REFUNDED">
            AND `platform`.local_refund_state=3
        </if>
        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@EXCHANGING_REPLENISHING">
            AND `platform`.re_send_state=1
        </if>
        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@EXCHANGED_REPLENISHED">
            AND `platform`.re_send_state=2
        </if>
        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@NORMAL_ORDER">
            AND `platform`.mapping_state=1
            AND `platform`.local_trade_state IN(2,3,7)
            AND `platform`.local_refund_state=0
        </if>
        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@UNKNOWN_STATE">
            AND `platform`.local_trade_state=0
        </if>
        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@OTHER">
            AND `platform`.local_trade_state=5
        </if>
        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@FAIL_ORDER">
            AND (`platform`.local_trade_state not in (2,3,7)
            OR `platform`.mapping_state=0 OR local_refund_state>0)
        </if>
        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@PAID_TAIL_MONEY">
            AND `platform`.local_trade_state=2 AND `platform`.local_refund_state =0
        </if>
        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@PAID_FRONT_MONEY">
            AND `platform`.local_trade_state=7 AND `platform`.local_refund_state =0
        </if>
        <!--<if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@NO_PAY_TAIL_MONEY">-->
            <!--AND `advance`.custom_trade_status=5-->
        <!--</if>-->
        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@NO_PAY">
            AND `platform`.local_trade_state=1
        </if>

        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@PART_REFUNDING">
            AND `platform`.local_refund_state =2
        </if>
        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@REFUND_SUCCESS">
            AND `platform`.local_refund_state=4
        </if>
        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@TRANSACTION_CLOSURE">
            AND `platform`.local_trade_state=5
        </if>

    </sql>

     <sql id="advance_order_quick_filter_or">
         <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@REFUNDING">
            OR (`platform`.local_refund_state=1)
        </if>
        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@PART_REFUNDED">
            OR (`platform`.local_refund_state=3)
        </if>
        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@EXCHANGING_REPLENISHING">
            OR (`platform`.re_send_state=1)
        </if>
        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@EXCHANGED_REPLENISHED">
            OR (`platform`.re_send_state=2)
        </if>
        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@NORMAL_ORDER">
            OR (`platform`.mapping_state=1
            AND `platform`.local_trade_state IN(2,3,7)
            AND `platform`.local_refund_state=0)
        </if>
        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@UNKNOWN_STATE">
            OR (`platform`.local_trade_state=0)
        </if>
        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@OTHER">
            OR (`platform`.local_trade_state=5)
        </if>
        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@FAIL_ORDER">
            OR ((`platform`.local_trade_state not in (2,3,7)
            OR `platform`.mapping_state=0 OR local_refund_state>0))
        </if>
        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@PAID_TAIL_MONEY">
            OR (`platform`.local_trade_state=2)
        </if>
        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@PAID_FRONT_MONEY">
            OR (`platform`.local_trade_state=7)
        </if>
        <!--<if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@NO_PAY_TAIL_MONEY">-->
            <!--AND `advance`.custom_trade_status=5-->
        <!--</if>-->
        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@NO_PAY">
            OR (`platform`.local_trade_state=1)
        </if>

        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@PART_REFUNDING">
            OR (`platform`.local_refund_state =2)
        </if>
        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@REFUND_SUCCESS">
            OR (`platform`.local_refund_state=4)
        </if>
        <if test="advanceFilterType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QuickAdvanceFilterType@TRANSACTION_CLOSURE">
            OR (`platform`.local_trade_state=5)
        </if>

    </sql>

    <sql id="order_filter">
        <if test="filterParameter.intSellerFlag !=null and filterParameter.intSellerFlag>0 ">
            AND `platform`.seller_flag=#{filterParameter.intSellerFlag}
        </if>
        <if test="filterParameter.mentionValue !=null ">
            AND `core`.vchcode IN ( SELECT order_id FROM `pl_eshop_order_mark` AS mark
            WHERE mark.order_id = `core`.vchcode AND mark.profile_id=`core`.profile_id
            AND mark.`mark_code` = #{filterParameter.mentionValue})
        </if>
        <if test="filterParameter.tradeOrderId != null and filterParameter.tradeOrderId != ''">
            AND platform.trade_id like CONCAT('%',#{filterParameter.tradeOrderId},'%')
        </if>
        <if test="filterParameter.orderSourceType != null">
            AND core.order_source_type=#{filterParameter.orderSourceType}
        </if>
        <if test="filterParameter.payTimeType != null">
            AND platform.pay_time_type=#{filterParameter.payTimeType}
        </if>
        <if test="filterParameter.reSendState != null">
            AND platform.re_send_state=#{filterParameter.reSendState}
        </if>
        <if test="filterParameter.businessType != null">
            AND core.business_type=#{filterParameter.businessType}
        </if>
        <if test="filterParameter.localTradeState != null">
            AND platform.local_trade_state=#{filterParameter.localTradeState}
        </if>
        <if test="filterParameter.localRefundState != null">
            AND platform.local_refund_state=#{filterParameter.localRefundState}
        </if>
        <if test="filterParameter.buyerMessage != null and filterParameter.buyerMessage != ''">
            AND platform.buyer_message like CONCAT('%',#{filterParameter.buyerMessage},'%')
        </if>
        <if test="filterParameter.distributionBuyerTradeId != null and filterParameter.distributionBuyerTradeId != ''">
            AND distribution.distribution_buyer_trade_id like CONCAT('%',#{filterParameter.distributionBuyerTradeId},'%')
        </if>
        <if test="filterParameter.groupTitle != null and filterParameter.groupTitle != ''">
            AND platformExtend.group_title like CONCAT('%',#{filterParameter.groupTitle},'%')
        </if>
        <if test="filterParameter.sellerMemo != null and filterParameter.sellerMemo != ''">
            AND platform.seller_memo like CONCAT('%',#{filterParameter.sellerMemo},'%')
        </if>
        <if test="filterParameter.memo != null and filterParameter.memo != ''">
            AND `core`.memo like CONCAT('%',#{filterParameter.memo},'%')
        </if>
        <if test="filterParameter.platformParentOrderId != null">
            AND `core`.platform_parent_order_id  like CONCAT('%',#{filterParameter.platformParentOrderId},'%')
        </if>
        <if test="filterParameter.otypeName != null and filterParameter.otypeName != ''">
            AND eshop.fullname like CONCAT('%',#{filterParameter.otypeName},'%')
        </if>
        <if test="filterParameter.otypeId != null">
            AND core.otype_id = #{filterParameter.otypeId}
        </if>
        <if test="filterParameter.processState != null">
            AND core.submit_send_state=#{filterParameter.processState}
        </if>
        <if test="filterParameter.orderSaleType != null">
            AND core.order_sale_type = #{filterParameter.orderSaleType}
        </if>
        <if test="filterParameter.ocategory != null">
            AND otype.ocategory=#{filterParameter.ocategory}
        </if>
        <if test="filterParameter.btypeName != null and filterParameter.btypeName != ''">
            AND btype.fullname like CONCAT('%',#{filterParameter.btypeName},'%')
        </if>
        <if test="filterParameter.ktypeName != null and filterParameter.ktypeName != ''">
            AND ktype.fullname like CONCAT('%',#{filterParameter.ktypeName},'%')
        </if>
        <if test="filterParameter.deliverType != null">
            AND core.deliver_type=#{filterParameter.deliverType}
        </if>
        <if test="filterParameter.invoiceRequired != null">
            AND invoice.invoice_required=#{filterParameter.invoiceRequired}
        </if>
        <if test="filterParameter.customerShopAccount != null and filterParameter.customerShopAccount != ''">
            AND buyer.customer_shop_account like CONCAT('%',#{filterParameter.customerShopAccount},'%')
        </if>
        <if test="filterParameter.fullBuyerInfo != null and filterParameter.fullBuyerInfo != ''">
            AND
            CONCAT(
            IFNULL(buyer.`customer_shop_account`,''),
            " ",
            IFNULL(buyer.`customer_receiver`,''),
            " ",
            IFNULL(buyer.`customer_receiver_mobile`,''),
            " ",
            IFNULL(buyer.`customer_receiver_phone`,''),
            " ",
            IFNULL(buyer.`customer_receiver_province`,''),
            " ",
            IFNULL(`customer_receiver_city`,''),
            " ",
            IFNULL(buyer.`customer_receiver_district`,''),
            " ",
            IFNULL(buyer.`customer_receiver_town`,''),
            " ",
            IFNULL(buyer.`customer_receiver_address`,'')
            )
            LIKE CONCAT('%', #{filterParameter.fullBuyerInfo}, '%')
        </if>
        <if test="filterParameter.createType != null">
            AND core.create_type=#{filterParameter.createType}
        </if>
        <if test="filterParameter.collectCustomer != null and filterParameter.collectCustomer != ''">
            AND platform.collect_customer like CONCAT('%',#{filterParameter.collectCustomer},'%')
        </if>
        <if test="filterParameter.salesman != null and filterParameter.salesman != ''">
            AND platform.salesman like CONCAT('%',#{filterParameter.salesman},'%')
        </if>
        <if test="filterParameter.paymentMode != null">
            AND platform.payment_mode=#{filterParameter.paymentMode}
        </if>
        <if test="filterParameter.etypeName != null and filterParameter.etypeName != ''">
            AND etype.fullname like CONCAT('%',#{filterParameter.etypeName},'%')
        </if>
        <if test="filterParameter.localFreightName != null and filterParameter.localFreightName != ''">
            AND platform.local_freight_name like CONCAT('%',#{filterParameter.localFreightName},'%')
        </if>
        <if test="filterParameter.localFreightBillNo != null and filterParameter.localFreightBillNo != ''">
            AND platform.local_freight_bill_no like CONCAT('%',#{filterParameter.localFreightBillNo},'%')
        </if>
        <if test="filterParameter.customerExpectedFreightName != null and filterParameter.customerExpectedFreightName != ''">
            AND platform.customer_expected_freight_name like CONCAT('%',#{filterParameter.customerExpectedFreightName},'%')
        </if>
        <if test="filterParameter.remark != null and filterParameter.remark != ''">
            AND core.memo like CONCAT('%',#{filterParameter.remark},'%')
        </if>
        <if test="filterParameter.platformStockId != null and filterParameter.platformStockId != ''">
            AND platform.platform_store_id like CONCAT('%',#{filterParameter.platformStockId},'%')
        </if>
        <if test="filterParameter.platformStockName != null and filterParameter.platformStockName != ''">
            AND `smap`.platform_store_name like CONCAT('%',#{filterParameter.platformStockName},'%')
        </if>
        <if test="filterParameter.exportState != null">
            <if test="filterParameter.exportState == 0">
                AND platform.export_count = 0
            </if>
            <if test="filterParameter.exportState != 0">
                AND platform.export_count &gt; 0
            </if>
        </if>
        <if test="filterParameter.distributionDisedTaxedMinTotal != null">
            AND if(`platform`.distribution_dised_taxed_total = 0,`core`.currency_bill_total,`platform`.distribution_dised_taxed_total) &gt;= #{filterParameter.distributionDisedTaxedMinTotal}
        </if>
        <if test="filterParameter.distributionDisedTaxedMaxTotal != null">
            AND if(`platform`.distribution_dised_taxed_total = 0,`core`.currency_bill_total,`platform`.distribution_dised_taxed_total) &lt;= #{filterParameter.distributionDisedTaxedMaxTotal}
        </if>
        <if test="filterParameter.buyerTradeMinTotal != null">
            AND platform.buyer_trade_total &gt;= #{filterParameter.buyerTradeMinTotal}
        </if>
        <if test="filterParameter.buyerTradeMaxTotal != null">
            AND platform.buyer_trade_total &lt;= #{filterParameter.buyerTradeMaxTotal}
        </if>
        <if test="filterParameter.buyerPaidMinTotal != null">
            AND platform.buyer_paid_total &gt;= #{filterParameter.buyerPaidMinTotal}
        </if>
        <if test="filterParameter.buyerPaidMaxTotal != null">
            AND platform.buyer_paid_total &lt;= #{filterParameter.buyerPaidMaxTotal}
        </if>
        <if test="filterParameter.buyerUnpaidMinTotal != null">
            AND platform.buyer_unpaid_total &gt;= #{filterParameter.buyerUnpaidMinTotal}
        </if>
        <if test="filterParameter.buyerUnpaidMaxTotal != null">
            AND platform.buyer_unpaid_total &lt;= #{filterParameter.buyerUnpaidMaxTotal}
        </if>
        <if test="filterParameter.currencyBillTotalMin != null">
            AND core.currency_bill_total &gt;= #{filterParameter.currencyBillTotalMin}
        </if>
        <if test="filterParameter.currencyBillTotalMax != null">
            AND core.currency_bill_total &lt;= #{filterParameter.currencyBillTotalMax}
        </if>
        <if test="filterParameter.disedTaxedMinTotal != null">
            AND core.currency_bill_total &gt;= #{filterParameter.disedTaxedMinTotal}
        </if>
        <if test="filterParameter.disedTaxedMaxTotal != null">
            AND core.currency_bill_total &lt;= #{filterParameter.disedTaxedMaxTotal}
        </if>
        <if test="filterParameter.advanceTotalMinTotal != null">
            AND assinfo.currency_advance_total &gt;= #{filterParameter.advanceTotalMinTotal}
        </if>
        <if test="filterParameter.advanceTotalMaxTotal != null">
            AND assinfo.currency_advance_total &lt;= #{filterParameter.advanceTotalMaxTotal}
        </if>
<!--        <if test="filterParameter.taxMinTotal != null">-->
<!--            AND advance.tax_total &gt;= #{filterParameter.taxMinTotal}-->
<!--        </if>-->
<!--        <if test="filterParameter.taxMaxTotal != null">-->
<!--            AND advance.tax_total &lt;= #{filterParameter.taxMaxTotal}-->
<!--        </if>-->
        <if test="filterParameter.sellerPreferentialMinTotal != null">
            AND assinfo.currency_order_preferential_allot_total &gt;= #{filterParameter.sellerPreferentialMinTotal}
        </if>
        <if test="filterParameter.sellerPreferentialMaxTotal != null">
            AND assinfo.currency_order_preferential_allot_total &lt;= #{filterParameter.sellerPreferentialMaxTotal}
        </if>
        <if test="filterParameter.platformPreferentialMinTotal != null">
            AND platform.platform_ptype_preferential_total &gt;= #{filterParameter.platformPreferentialMinTotal}
        </if>
        <if test="filterParameter.platformPreferentialMaxTotal != null">
            AND platform.platform_ptype_preferential_total &lt;= #{filterParameter.platformPreferentialMaxTotal}
        </if>
        <if test="filterParameter.buyerFreightMinFee != null">
            AND assinfo.currency_order_buyer_freight_fee &gt;= #{filterParameter.buyerFreightMinFee}
        </if>
        <if test="filterParameter.buyerFreightMaxFee != null">
            AND assinfo.currency_order_buyer_freight_fee &lt;= #{filterParameter.buyerFreightMaxFee}
        </if>
        <if test="filterParameter.serviceMinFee != null">
            AND assinfo.currency_ptype_service_fee &gt;= #{filterParameter.serviceMinFee}
        </if>
        <if test="filterParameter.serviceMaxFee != null">
            AND assinfo.currency_ptype_service_fee &lt;= #{filterParameter.serviceMaxFee}
        </if>
        <if test="filterParameter.promisedSignStartTimeBeginTime != null and filterParameter.promisedSignStartTimeEndTime != null">
            AND timing.promised_sign_startTime between #{filterParameter.promisedSignStartTimeBeginTime} and #{filterParameter.promisedSignStartTimeEndTime}
        </if>
        <if test="filterParameter.anchorOrderPreferentialTotalMinTotal != null">
            AND `platformExtend`.anchor_order_preferential_total &gt;= #{filterParameter.anchorOrderPreferentialTotalMinTotal}
        </if>
        <if test="filterParameter.anchorOrderPreferentialTotalMaxTotal != null">
            AND `platformExtend`.anchor_order_preferential_total &lt;= #{filterParameter.anchorOrderPreferentialTotalMaxTotal}
        </if>
        <if test="filterParameter.platformOrderSubsidyTotalMinTotal != null">
            AND `platformExtend`.platform_order_subsidy_total &gt;= #{filterParameter.platformOrderSubsidyTotalMinTotal}
        </if>
        <if test="filterParameter.platformOrderSubsidyTotalMaxTotal != null">
            AND `platformExtend`.platform_order_subsidy_total &lt;= #{filterParameter.platformOrderSubsidyTotalMaxTotal}
        </if>
        <if test="filterParameter.planSendBeginTime != null and filterParameter.planSendEndTime != null">
            AND timing.plan_send_time between #{filterParameter.planSendBeginTime} and #{filterParameter.planSendEndTime}
        </if>
        <if test="filterParameter.planSignTimeBeginTime != null and filterParameter.planSignTimeEndTime != null">
            AND timing.plan_sign_time between #{filterParameter.planSignTimeBeginTime} and #{filterParameter.planSignTimeEndTime}
        </if>
        <if test="filterParameter.signBeginTime != null and filterParameter.signEndTime != null">
            AND timing.sign_time between #{filterParameter.signBeginTime} and #{filterParameter.signEndTime}
        </if>
        <if test="filterParameter.promisedCollectBeginTime != null and filterParameter.promisedCollectEndTime != null">
            AND timing.promised_collect_time between #{filterParameter.promisedCollectBeginTime} and #{filterParameter.promisedCollectEndTime}
        </if>
        <if test="filterParameter.promisedSignBeginTime != null and filterParameter.promisedSignEndTime != null">
            AND timing.promised_sign_time between #{filterParameter.promisedSignBeginTime} and #{filterParameter.promisedSignEndTime}
        </if>
        <if test="filterParameter.sendBeginTime != null and filterParameter.sendEndTime != null">
            AND timing.send_time between #{filterParameter.sendBeginTime} and #{filterParameter.sendEndTime}
        </if>
        <if test="filterParameter.createBeginTime != null and filterParameter.createEndTime != null">
            AND core.create_time between #{filterParameter.createBeginTime} and #{filterParameter.createEndTime}
        </if>
        <if test="filterParameter.tradeCreateBeginTime != null and filterParameter.tradeCreateEndTime != null">
            AND platform.trade_create_time between #{filterParameter.tradeCreateBeginTime} and #{filterParameter.tradeCreateEndTime}
        </if>
        <if test="filterParameter.tradePayBeginTime != null and filterParameter.tradePayEndTime != null">
            AND platform.trade_pay_time between #{filterParameter.tradePayBeginTime} and #{filterParameter.tradePayEndTime}
        </if>
        <if test="filterParameter.tradeFinishBeginTime != null and filterParameter.tradeFinishEndTime != null">
            AND platform.trade_finish_time between #{filterParameter.tradeFinishBeginTime} and #{filterParameter.tradeFinishEndTime}
        </if>
        <if test="filterParameter.collectBeginTime != null and filterParameter.collectEndTime != null">
            AND platform.collect_time between #{filterParameter.collectBeginTime} and #{filterParameter.collectEndTime}
        </if>
         <if test="filterParameter.shopTypes != null">
            AND eshop.eshop_sale_platform = #{filterParameter.shopTypes.code}
        </if>
        <if test="filterParameter.groupHeaderName != null and filterParameter.groupHeaderName != ''">
            AND `platform`.group_header_name like CONCAT('%',#{filterParameter.groupHeaderName},'%')
        </if>
        <if test="filterParameter.sellerFlagMemo != null and filterParameter.sellerFlagMemo != ''">
            AND `platformExtend`.seller_flag_memo like CONCAT('%',#{filterParameter.sellerFlagMemo},'%')
        </if>
        <if test="filterParameter.confirmStatus != null">
            AND `platformExtend`.confirm_status = #{filterParameter.confirmStatus}
        </if>
        <if test="filterParameter.logisticsStatus != null">
            AND `platformExtend`.logistics_status = #{filterParameter.logisticsStatus}
        </if>
        <if test="filterParameter.platformQcResult != null">
            AND `platformExtend`.platform_qc_result = #{filterParameter.platformQcResult}
        </if>
        <if test="filterParameter.platformIdentifyResult != null">
            AND `platformExtend`.platform_identify_result = #{filterParameter.platformIdentifyResult}
        </if>
        <if test="filterParameter.flowChannel != null and filterParameter.flowChannel != ''">
            AND `platformExtend`.flow_channel like CONCAT('%',#{filterParameter.flowChannel},'%')
        </if>
        <if test="filterParameter.nationalSubsidyMinTotal != null">
            AND `platformExtend`.national_subsidy_total &gt;= #{filterParameter.nationalSubsidyMinTotal}
        </if>
        <if test="filterParameter.nationalSubsidyMaxTotal != null">
            AND `platformExtend`.national_subsidy_total &lt;= #{filterParameter.nationalSubsidyMaxTotal}
        </if>
    </sql>

    <sql id="advance_query_new">
        <if test="otypeIds!=null and otypeIds.size() > 0">
            AND `core`.otype_id IN
            <foreach collection="otypeIds" close=")" open="(" separator="," item="otypeId">
                #{otypeId}
            </foreach>
        </if>
        <if test="createType !=null and createType.size() > 0 ">
            and `core`.create_type in
            <foreach collection="createType" close=")" open="(" separator="," item="createTypes">
                #{createTypes}
            </foreach>
        </if>
        <if test="businessTypes!=null and businessTypes.size()>0">
            AND `core`.business_type  IN
            <foreach collection="businessTypes" close=")" open="(" separator="," item="type">
                #{type}
            </foreach>
        </if>
        <if test="tradeStatus !=null and tradeStatus.size() > 0 ">
            and ( `platform`.local_trade_state in
            <foreach collection="tradeStatus" close=")" open="(" separator="," item="simpleType">
                #{simpleType}
            </foreach>
            <if test="deliverSendState !=null and  deliverSendState==@com.wsgjp.ct.sale.biz.eshoporder.api.enums.ProcessStatusEnum@SENDED">
                OR `platform`.deliver_Send_State=35
            </if>
            )
        </if>
        <if test="refundStates !=null and refundStates.size() > 0 ">
            and `platform`.local_refund_state in
            <foreach collection="refundStates" close=")" open="(" separator="," item="simpleType">
                #{simpleType}
            </foreach>
        </if>
        <if test="deleteState !=null and deleteState.size() > 0 ">
            and `core`.deleted in
            <foreach collection="deleteState" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="exportState !=null and exportState.size() > 0 ">
            <if test="exportState.size() ==1 and exportState[0]==0 ">
                and `platform`.export_count = 0
            </if>
            <if test="exportState.size() ==1 and exportState[0]==1 ">
                and `platform`.export_count > 0
            </if>
        </if>
        <if test="processStates !=null and processStates.size() > 0 ">
            and `core`.submit_send_state in
            <foreach collection="processStates" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="ptypeType==0">
            <if test="skuIds !=null and skuIds.size()>0">
                and (
                EXISTS (SELECT 1 FROM `td_orderbill_detail_core` WHERE profile_id=`core`.profile_id AND vchcode=`core`.vchcode AND sku_id in
                <foreach collection="skuIds" close=")" open="(" separator="," item="key">
                    #{key}
                </foreach>)
                OR (EXISTS (SELECT 1 FROM `td_orderbill_detail_combo` WHERE profile_id=`core`.profile_id AND vchcode=`core`.vchcode AND combo_id in
                <foreach collection="skuIds" close=")" open="(" separator="," item="key">
                    #{key}
                </foreach>))
                )
            </if>
        </if>
        <if test="ptypeType==1">
            <if test="ptypeLabelIds !=null and ptypeLabelIds.size()>0">
                and (
                EXISTS (SELECT 1 FROM `td_orderbill_detail_core` AS pesod
                left join cf_data_label_ptype label on label.resource_id=pesod.ptype_id and label.profile_id=pesod.profile_id
                WHERE pesod.profile_id=#{profileId} and pesod.`vchcode` = `core`.vchcode and label.labelfield_value_id in
                <foreach collection="ptypeLabelIds" close=")" open="(" separator="," item="labelId">
                    #{labelId}
                </foreach>)
                OR
                EXISTS (SELECT 1 FROM `td_orderbill_detail_combo` AS combo
                left join cf_data_label_ptype label on label.resource_id=combo.combo_id and label.profile_id=combo.profile_id
                WHERE combo.profile_id=#{profileId} and combo.`vchcode` = `core`.vchcode and label.labelfield_value_id in
                <foreach collection="ptypeLabelIds" close=")" open="(" separator="," item="labelId">
                    #{labelId}
                </foreach>)
                )
            </if>
        </if>
        <if test="ptypeType==2">
            <if test="ptypeClassIds !=null and ptypeClassIds.size()>0">
                and (
                EXISTS (SELECT 1 FROM `td_orderbill_detail_core` AS pesod
                left join base_ptype ptype on ptype.id=pesod.ptype_id and ptype.profile_id=pesod.profile_id
                WHERE pesod.profile_id=#{profileId} and pesod.`vchcode` = `core`.vchcode  and ptype.partypeid in
                <foreach collection="ptypeClassIds" close=")" open="(" separator="," item="classId">
                    #{classId}
                </foreach>)
                or
                EXISTS (SELECT 1 FROM `td_orderbill_detail_combo` AS combo
                left join base_ptype ptype on ptype.id=combo.combo_id and ptype.profile_id=combo.profile_id
                WHERE combo.profile_id=#{profileId} and combo.`vchcode` = `core`.vchcode and ptype.partypeid in
                <foreach collection="ptypeClassIds" close=")" open="(" separator="," item="classId">
                    #{classId}
                </foreach>)
                )
            </if>
        </if>
        <if test="freightName !=null and freightName!=''">
            and `platform`.local_freight_name LIKE CONCAT('%',#{freightName},'%')
        </if>
        <if test="freightBillNo !=null and freightBillNo!=''">
            and `platform`.local_freight_bill_no =#{freightBillNo}
        </if>
        <if test="flagConditionForModify==0">
            <if test="marks !=null and marks.size()>0">
                AND `core`.vchcode
                <include refid="common-query-mark"/>
            </if>
            <if test="intSellerFlags !=null and intSellerFlags.size() > 0 ">
                and `platform`.seller_flag in
                <foreach collection="intSellerFlags" close=")" open="(" separator="," item="intSellerFlag">
                    #{intSellerFlag}
                </foreach>
            </if>
        </if>
        <if test="flagConditionForModify==1">
            <if test="marks !=null and marks.size()>0 and (intSellerFlags ==null or intSellerFlags.size() == 0) ">
                AND `core`.vchcode
                <include refid="common-query-mark"/>
            </if>
            <if test="intSellerFlags !=null and intSellerFlags.size() > 0 and( marks ==null or  marks.size()==0)">
                AND `platform`.seller_flag in
                <foreach collection="intSellerFlags" close=")" open="(" separator="," item="intSellerFlag">
                    #{intSellerFlag}
                </foreach>
            </if>
            <if test="intSellerFlags !=null and intSellerFlags.size() > 0  and marks !=null and marks.size()>0">
                AND (
                `core`.vchcode
                <include refid="common-query-mark"/>
                OR
                `platform`.seller_flag in
                <foreach collection="intSellerFlags" close=")" open="(" separator="," item="intSellerFlag">
                    #{intSellerFlag}
                </foreach>
                )
            </if>
        </if>
        <if test="buyerMessageCondition==1">
            AND `platform`.buyer_message=''
        </if>
        <if test="buyerMessageCondition==2">
            AND `platform`.buyer_message  LIKE CONCAT('%',#{buyerMessage},'%')
        </if>
        <if test="buyerMessageCondition==3 and buyerMessage!=null and buyerMessage!=''">
            AND `platform`.buyer_message NOT LIKE CONCAT('%',#{buyerMessage},'%')
        </if>
        <if test="sellerMemoCondition==1">
            AND `platform`.seller_memo=''
        </if>
        <if test="sellerMemoCondition==2">
            AND `platform`.seller_memo LIKE CONCAT('%',#{sellerMemo},'%')
        </if>
        <if test="sellerMemoCondition==3 and sellerMemo!=null and sellerMemo!=''">
            AND `platform`.seller_memo NOT LIKE CONCAT('%',#{sellerMemo},'%')
        </if>
        <if test="memoCondition==1">
            AND `core`.memo=''
        </if>
        <if test="memoCondition==2">
            AND `core`.memo LIKE CONCAT('%',#{memo},'%')
        </if>
        <if test="memoCondition==3 and memo!=null and memo!=''">
            AND `core`.memo NOT LIKE CONCAT('%',#{memo},'%')
        </if>
        <if test="platformQualityOrgName!=null and platformQualityOrgName!=''">
            AND `platformExtend`.platform_quality_org_name  LIKE CONCAT('%',#{platformQualityOrgName},'%')
        </if>
    </sql>

     <sql id="query_advance_detail_assinfo">
        da.detail_id as `assinfo.detail_id`,
        da.vchcode as `assinfo.vchcode`,
        da.profile_id as `assinfo.profile_id`,
        da.unit_id as `assinfo.unit_id`,
        da.unit_rate as `assinfo.unit_rate`,
        da.unit_name as `assinfo.unit_name`,
        da.unit_qty as `assinfo.unit_qty`,
        da.currency_buyer_price as `assinfo.currency_buyer_price`,
        da.currency_price as `assinfo.currency_price`,
        da.currency_total as `assinfo.currency_total`,
        da.currency_dised_price as `assinfo.currency_dised_price`,
        da.currency_dised_total as `assinfo.currency_dised_total`,
        da.currency_tax_total as `assinfo.currency_tax_total`,
        da.currency_dised_taxed_price as `assinfo.currency_dised_taxed_price`,
        da.currency_dised_taxed_total as `assinfo.currency_dised_taxed_total`,
        da.currency_dised_initial_price as `assinfo.currency_dised_initial_price`,
        da.currency_dised_initial_total as `assinfo.currency_dised_initial_total`,
        da.currency_tax_rate as `assinfo.currency_tax_rate`,
        da.currency_tax_total as `assinfo.currency_tax_total`,
        da.currency_ptype_preferential_total as `assinfo.currency_ptype_preferential_total`,
        da.currency_order_preferential_allot_total as `assinfo.currency_order_preferential_allot_total`,
        da.ptype_commission_total as `assinfo.ptype_commission_total`,
        da.discount as `assinfo.discount`,
    </sql>

    <sql id="query_advance_detail_timing">
        timing.detail_id as `timing.detail_id`,
        timing.vchcode as `timing.vchcode`,
        timing.profile_id as `timing.profile_id`,
        timing.promised_send_time as `timing.promised_send_time`,
        timing.create_time as `timing.create_time`,
        timing.update_time as `timing.update_time`,
    </sql>

    <sql id="query_advance_detail_platform">
        dp.detail_id as `platform.detail_id`,
        dp.vchcode as `platform.vchcode`,
        dp.profile_id as `platform.profile_id`,
        dp.platform_store_id as `platform.platform_store_id`,
        dp.platform_num_id as `platform.platform_num_id`,
        dp.platform_sku_id as `platform.platform_sku_id`,
        dp.platform_properties_name as `platform.platform_properties_name`,
        dp.platform_xcode as `platform.platform_xcode`,
        dp.platform_pic_url as `platform.platform_pic_url`,
        dp.platform_product_name as `platform.platform_product_name`,
        dp.create_time as `platform.create_time`,
        dp.update_time as `platform.update_time`,
        dp.mapping_state as `platform.mapping_state`,
        dp.trade_order_detail_id as `platform.trade_order_detail_id`,
        dp.eshop_order_id as `platform.eshop_order_id`,
        dp.order_sale_type as `platform.order_sale_type`,
        dp.platform_detail_trade_state as `platform.platform_detail_trade_state`,
        dp.process_state as `platform.process_state`,
        dp.platform_store_code as `platform.platform_store_code`,
        dp.platform_stock_id as `platform.platform_stock_id`,
        dp.platform_stock_code as `platform.platform_stock_code`,
        dp.trade_total as `platform.trade_total`,
        dp.trade_price as `platform.trade_price`,
        dp.refund_total as `platform.refund_total`,
        dp.refund_qty as `platform.refund_qty`,
        dp.ptype_service_fee as `platform.ptype_service_fee`,
        dp.local_mapping_mark as `platform.local_mapping_mark`,
        dp.deliver_required as `platform.deliver_required`,
        dp.seller_memo as `platform.seller_memo`,
        dp.buyer_message as `platform.buyer_message`,
        dp.attribute as `platform.attribute`,
        dp.stock_sync_rule_id as `platform.stock_sync_rule_id`,
        dp.deleted as `platform.deleted`,
        dp.local_refund_process_state as `platform.local_refund_process_state`,
        dp.local_refund_state as `platform.local_refund_state`,
        dp.remark as `platform.remark`,
        dp.is_sale_qty_need_to_occupy as `platform.is_sale_qty_need_to_occupy`,
        dp.platform_ptype_preferential_total as `platform.platform_ptype_preferential_total`,
        dp.platform_order_preferential_total as `platform.platform_order_preferential_total`,
        dp.combo_share_scale as `platform.combo_share_scale`,
        dp.re_send_state as `platform.re_send_state`,
        dp.trade_order_detail_id as `platform.trade_order_detail_id`,
        dp.display_custom_info as `platform.display_custom_info`,
        dp.platform_qc_result as `platform.platform_qc_result`,
        dp.platform_qc_result_desc as `platform.platform_qc_result_desc`,
        dp.platform_identify_result as `platform.platform_identify_result`,
        dp.platform_identify_result_desc as `platform.platform_identify_result_desc`,
        dp.verify_code as `platform.verify_code`,
        dp.flow_channel as `platform.flow_channel`,
        dp.mall_deduction_fee as `platform.mall_deduction_fee`,
        dp.mall_deduction_rate as `platform.mall_deduction_rate`,
        dp.anchor_order_preferential_total as `platform.anchor_order_preferential_total`,
        dp.anchor_ptype_preferential_total as `platform.anchor_ptype_preferential_total`,
        dp.platform_order_subsidy_total as `platform.platform_order_subsidy_total`,
        dp.platform_ptype_subsidy_total as `platform.platform_ptype_subsidy_total`,
        dp.national_subsidy_total as `platform.national_subsidy_total`,
         `smap`.platform_store_name AS `platform.platform_stock_name`,
    </sql>

    <select id="queryAdvanceSaleOrderDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderDetail">
        select 0 AS selected, d.*,od.btype_id,od.ktype_id,
         <include refid="query_advance_detail_platform"/>
        <include refid="query_advance_detail_assinfo"/>
        <include refid="query_advance_detail_timing"/>
        bp.fullname as ptypeName,bp.pcategory,bp.ptype_type,bp.ptype_area,bp.shortname as ptypeShortName,bp.usercode as ptypeCode,
        bp.ptype_width,bp.ptype_length,bp.ptype_height,bp.weight as ptypeWeight,bp.batchenabled,bp.snenabled,
        bp.length_unit,bp.weight_unit,bp.standard as ptypeStandard,bp.memo as ptypeMemo,bp.propenabled as propenabled,bp.sub_unit as subUnit,
        <if test="submitNotNeedXcode == false">
            bpx.xcode as xcode,
        </if>
        bpf.fullbarcode as barcode,
        bps.prop_name1,bps.propvalue_name1,bps.prop_id1,bps.propvalue_id1,
        bps.prop_name2,bps.propvalue_name2,bps.prop_id2,bps.propvalue_id2,
        bps.prop_name3,bps.propvalue_name3,bps.prop_id3,bps.propvalue_id3,
        bps.prop_name4,bps.propvalue_name4,bps.prop_id4,bps.propvalue_id4,
        bps.prop_name5,bps.propvalue_name5,bps.prop_id5,bps.propvalue_id5,
        bps.prop_name6,bps.propvalue_name6,bps.prop_id6,bps.propvalue_id6,
        unit.unit_name as unitName,unit.unit_rate as unitRate,
        bsunit.unit_name as baseUnitName,brand.brand_name,
        (CASE WHEN bps.pic_url='' THEN pic.pic_url ELSE bps.pic_url END) pic_url,o.ocategory,
        custom.sub_type AS 'customField.subType',
        custom.custom_head01 AS 'customField.customHead01',
        custom.custom_head02 AS 'customField.customHead02',
        custom.custom_head03 AS 'customField.customHead03',
        custom.custom_head04 AS 'customField.customHead04',
        custom.custom_head05 AS 'customField.customHead05',
        custom.custom_head06 AS 'customField.customHead06',
        custom.custom_head07 AS 'customField.customHead07',
        custom.custom_head08 AS 'customField.customHead08',
        custom.custom_head09 AS 'customField.customHead09',
        custom.custom_head10 AS 'customField.customHead10',
        custom.custom_head11 AS 'customField.customHead11',
        custom.custom_head12 AS 'customField.customHead12',
        custom.custom_head13 AS 'customField.customHead13',
        custom.custom_head14 AS 'customField.customHead14',
        `distribution`.profile_id AS 'distribution.profile_id',
        `distribution`.detail_id AS 'distribution.detailId',
        `distribution`.eshop_id AS 'distribution.eshop_id',
        `distribution`.vchcode AS 'distribution.vchcode',
        `distribution`.buyer_price AS 'distribution.buyer_price',
        `distribution`.buyer_total AS 'distribution.buyer_total',
        `distribution`.buyer_discount AS 'distribution.buyer_discount',
        `distribution`.buyer_dised_initial_price AS 'distribution.buyer_dised_initial_price',
        `distribution`.buyer_dised_initial_total AS 'distribution.buyer_dised_initial_total',
        `distribution`.buyer_ptype_preferential_total AS 'distribution.buyer_ptype_preferential_total',
        `distribution`.buyer_order_preferential_allot_total AS 'distribution.buyer_order_preferential_allot_total',
        IF( `od`.business_type != 203, d.dised_taxed_price, `distribution`.buyer_dised_taxed_price) AS 'distribution.buyer_dised_taxed_price',
        IF( `od`.business_type != 203, d.dised_taxed_total, `distribution`.buyer_dised_taxed_total) AS 'distribution.buyer_dised_taxed_total',
        `distribution`.buyer_tax_rate AS 'distribution.buyer_tax_rate',
        `distribution`.buyer_tax_total AS 'distribution.buyer_tax_total',
        `distribution`.buyer_dised_price AS 'distribution.buyer_dised_price',
        `distribution`.buyer_dised_total AS 'distribution.buyer_dised_total',
        `distribution`.distribution_buyer_trade_detail_id AS 'distribution.distribution_buyer_trade_detail_id',
        dpurchase.vchcode as `purchase.vchcode`,
        dpurchase.profile_id as `purchase.profile_id`,
        dpurchase.detail_id as `purchase.detailId`,
        dpurchase.purchase_price as `purchase.purchase_price`,
        dpurchase.purchase_total as `purchase.purchase_total`,
        dpurchase.purchase_tax_rate as `purchase.purchase_tax_rate`,
        dpurchase.purchase_tax_total as `purchase.purchase_tax_total`,
        dpurchase.purchase_dised_price as `purchase.purchase_dised_price`,
        dpurchase.purchase_dised_total as `purchase.purchase_dised_total`,
        dpurchase.purchase_discount as `purchase.purchase_discount`,
        dpurchase.purchase_dised_taxed_price as `purchase.purchase_dised_taxed_price`,
        dpurchase.purchase_dised_taxed_total as `purchase.purchase_dised_taxed_total`,
        `liveBroadcast`.profile_id as 'liveBroadcast.profile_id',
        `liveBroadcast`.eshop_id as 'liveBroadcast.eshop_id',
        `liveBroadcast`.vchcode as 'liveBroadcast.vchcode',
        `liveBroadcast`.detail_id as 'liveBroadcast.detail_id',
        `liveBroadcast`.platform_anchor_name as 'liveBroadcast.platformAnchorName',
        `liveBroadcast`.platform_anchor_id as 'liveBroadcast.platformAnchorId',
        `liveBroadcast`.platform_anchor_name as 'liveBroadcast.platformAnchorName',
        `liveBroadcast`.platform_live_room_id as 'liveBroadcast.platformLiveRoomId',
        `liveBroadcast`.live_brodcast_session_id as 'liveBroadcast.liveBrodcastSessionId',
        `liveBroadcast`.create_time as 'liveBroadcast.createTime',
        `liveBroadcast`.update_time as 'liveBroadcast.updateTime',
        `batch`.id as 'batch.id',
        `batch`.vchcode as 'batch.vchcode',
        `batch`.detail_id as 'batch.detailId',
        `batch`.profile_id as 'batch.profileId',
        `batch`.batchno as 'batch.batchno',
        `batch`.produce_date as 'batch.produceDate',
        `batch`.expire_date as 'batch.expireDate',
        `batch`.qty as 'batch.qty',
        `batch`.sub_qty as 'batch.subQty',
        `batch`.unit_qty as 'batch.unitQty',
        `batch`.batch_price as 'batch.batchPrice',
        `batch`.create_time as 'batch.createTime',
        `batch`.update_time as 'batch.updateTime',
        bk.fullname as ktypeName
        from ${tableName}
        left join td_orderbill_detail_platform dp on dp.profile_id=d.profile_id and dp.vchcode=d.vchcode and dp.detail_id = d.detail_id
        left join td_orderbill_detail_assinfo da on da.profile_id=d.profile_id and da.vchcode=d.vchcode and da.detail_id = d.detail_id
        left join td_orderbill_core od on od.profile_id=d.profile_id and od.vchcode=d.vchcode
        left join td_orderbill_platform odp on odp.profile_id=d.profile_id and odp.vchcode=od.vchcode
        left join base_otype o on o.profile_id=d.profile_id and o.id=od.otype_id
        left join base_ptype bp on bp.profile_id=d.profile_id and d.ptype_id=bp.id
        <if test="submitNotNeedXcode == false">
            left join base_ptype_xcode bpx on bpx.profile_id=d.profile_id and bpx.sku_id=d.sku_id and bpx.unit_id=da.unit_id  and bpx.ptype_id=d.ptype_id and
            bpx.defaulted=1
        </if>
        left join base_ptype_fullbarcode bpf on bpf.profile_id=d.profile_id and bpf.ptype_id=d.ptype_id and
        bpf.sku_id=d.sku_id and bpf.unit_id=da.unit_id and bpf.defaulted=1
        left join base_ptype_pic pic on pic.profile_id = d.profile_id and d.ptype_id = pic.ptype_id and pic.rowindex = 1
        left join base_ptype_sku bps on bps.profile_id=d.profile_id and bps.id=d.sku_id
        left join base_ptype_unit unit on unit.profile_id=d.profile_id and unit.id=da.unit_id
        left join base_ptype_unit bsunit on bsunit.profile_id=d.profile_id and bsunit.ptype_id=d.ptype_id and
        bsunit.unit_code=1
        left join `base_brandtype` brand on bp.profile_id = brand.profile_id and bp.brand_id = brand.id
        LEFT JOIN pub_custom_field_baseinfo custom ON custom.sub_type=5001 AND custom.xtype_id=bp.id AND custom.profile_id=bp.profile_id
        left join td_orderbill_detail_distribution_buyer distribution on distribution.profile_id= d.profile_id
        and distribution.detail_id = d.detail_id
        left join td_orderbill_detail__purchase dpurchase on dpurchase.profile_id= d.profile_id
        and dpurchase.detail_id = d.detail_id
        left join td_orderbill_detail_live_broadcast liveBroadcast on liveBroadcast.profile_id=d.profile_id and liveBroadcast.detail_id=d.detail_id
        left join td_orderbill_detail_batch batch on batch.profile_id=d.profile_id and batch.detail_id=d.detail_id
        left join td_orderbill_detail_serialno serialno on serialno.profile_id=d.profile_id and serialno.detail_id=d.detail_id
        left join td_orderbill_detail_timing timing on timing.profile_id=d.profile_id and timing.detail_id=d.detail_id
        LEFT JOIN pl_eshop_platform_store_mapping smap ON smap.platform_store_stock_id = dp.platform_stock_id AND smap.profile_id = dp.profile_id  AND smap.eshop_id = od.otype_id
        left join base_ktype bk on bk.profile_id=d.profile_id and bk.id=d.ktype_id
        where d.profile_id=#{profileId}
        <if test="eshopOrderId!=null">
            And d.vchcode=#{eshopOrderId}
        </if>
        <if test="eshopOrderIds!=null and eshopOrderIds.size>0">
            And d.vchcode IN
            <foreach collection="eshopOrderIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@NotMapping">
            AND dp.mapping_state=0
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@Mapping">
            AND dp.mapping_state=1
        </if>
        <if test="waitAudit==true">
            AND dp.mapping_state=1 AND dp.local_refund_state!=4  and dp.deliver_required=1 and d.ptype_id>0
        </if>
        <if test="waitSendAudit==true">
            AND dp.mapping_state=1  and dp.deliver_required=1
        </if>
    </select>

    <select id="queryAdvanceSaleOrderDetailSerialnos" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderDetailSerialno">
        select *
        from td_orderbill_detail_serialno
        where profile_id = #{profileId}
          and vchcode = #{vchcode}
    </select>

    <select id="queryAdvanceSaleOrderDetailGiftRelation" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderDetailGiftRelation">
        select *
        from td_orderbill_detail_gift_relation
        where profile_id = #{profileId}
          and source_vchcode = #{vchcode}
    </select>

    <select id="queryAdvanceSaleOrderComboDetails" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderDetailCombo">
        select pl.*,
               ptype.fullname                         as ptypeName,
               ptype.id                               as ptypeId,
               ptype.shortname                        as ptypeShortName,
               ptype.usercode                         as xcode,
               ptype.barcode,
               ptype.weight_unit,
               ptype.weight                           as ptypeWeight,
               ptype.memo                             as ptypeMemo,
               ptype.propenabled                      as propenabled,
               if(o.create_type = 1, '', pic.pic_url) as picUrl,
               custom.sub_type AS 'customField.subType',
               custom.custom_head01 AS 'customField.customHead01',
                custom.custom_head02 AS 'customField.customHead02',
                custom.custom_head03 AS 'customField.customHead03',
                custom.custom_head04 AS 'customField.customHead04',
                custom.custom_head05 AS 'customField.customHead05',
                custom.custom_head06 AS 'customField.customHead06',
                custom.custom_head07 AS 'customField.customHead07',
                custom.custom_head08 AS 'customField.customHead08',
                custom.custom_head09 AS 'customField.customHead09',
                custom.custom_head10 AS 'customField.customHead10',
                custom.custom_head11 AS 'customField.customHead11',
                custom.custom_head12 AS 'customField.customHead12',
                custom.custom_head13 AS 'customField.customHead13',
                custom.custom_head14 AS 'customField.customHead14',
                `distribution`.profile_id AS 'distribution.profile_id',
                `distribution`.combo_row_id AS 'distribution.combo_row_id',
                `distribution`.vchcode AS 'distribution.vchcode',
                `distribution`.combo_id AS 'distribution.combo_id',
                `distribution`.trade_order_detail_id AS 'distribution.trade_order_detail_id',
                `distribution`.buyer_price AS 'distribution.buyer_price',
                `distribution`.buyer_total AS 'distribution.buyer_total',
                `distribution`.buyer_discount AS 'distribution.buyer_discount',
                `distribution`.buyer_dised_initial_price AS 'distribution.buyer_dised_initial_price',
                `distribution`.buyer_dised_initial_total AS 'distribution.buyer_dised_initial_total',
                `distribution`.buyer_ptype_preferential_total AS 'distribution.buyer_ptype_preferential_total',
                `distribution`.buyer_order_preferential_allot_total AS 'distribution.buyer_order_preferential_allot_total',
               IF( `o`.business_type != 203, pl.dised_taxed_price, `distribution`.buyer_dised_taxed_price) AS 'distribution.buyer_dised_taxed_price',
               IF( `o`.business_type != 203, pl.dised_taxed_total, `distribution`.buyer_dised_taxed_total) AS 'distribution.buyer_dised_taxed_total',
                `distribution`.buyer_tax_rate AS 'distribution.buyer_tax_rate',
                `distribution`.buyer_tax_total AS 'distribution.buyer_tax_total',
                `distribution`.buyer_dised_price AS 'distribution.buyer_dised_price',
                `distribution`.buyer_dised_total AS 'distribution.buyer_dised_total',
                `distribution`.distribution_buyer_trade_detail_id AS 'distribution.distribution_buyer_trade_detail_id',
                dpurchase.vchcode as `purchase.vchcode`,
               dpurchase.profile_id as `purchase.profile_id`,
               dpurchase.combo_row_id as `purchase.combo_row_id`,
               dpurchase.purchase_price as `purchase.purchase_price`,
               dpurchase.purchase_total as `purchase.purchase_total`,
               dpurchase.purchase_tax_rate as `purchase.purchase_tax_rate`,
               dpurchase.purchase_tax_total as `purchase.purchase_tax_total`,
               dpurchase.purchase_dised_price as `purchase.purchase_dised_price`,
               dpurchase.purchase_dised_total as `purchase.purchase_dised_total`,
               dpurchase.purchase_discount as `purchase.purchase_discount`,
               dpurchase.purchase_dised_taxed_price as `purchase.purchase_dised_taxed_price`,
               dpurchase.purchase_dised_taxed_total as `purchase.purchase_dised_taxed_total`,
                liveBroadcast.profile_id as `liveBroadcast.profileId`,
                liveBroadcast.eshop_id as `liveBroadcast.eshopId`,
                liveBroadcast.vchcode as `liveBroadcast.vchcode`,
                liveBroadcast.orderbill_detail_combo_row_id as `liveBroadcast.orderbillDetailComboRowId`,
                liveBroadcast.platform_anchor_name as `liveBroadcast.platformAnchorName`,
                liveBroadcast.platform_anchor_id as `liveBroadcast.platformAnchorId`,
                liveBroadcast.platform_anchor_name as `liveBroadcast.platformAnchorName`,
                liveBroadcast.platform_live_room_id as `liveBroadcast.platformLiveRoomId`,
                liveBroadcast.live_brodcast_session_id as `liveBroadcast.liveBrodcastSessionId`,
                liveBroadcast.create_time as `liveBroadcast.createTime`,
                liveBroadcast.update_time as `liveBroadcast.updateTime`
        from td_orderbill_detail_combo pl
                 left join td_orderbill_core o on o.profile_id = pl.profile_id and o.vchcode = pl.vchcode
                 left join base_ptype ptype on ptype.profile_id = pl.profile_id and pl.combo_id = ptype.id
                 left join base_ptype_pic pic on pic.profile_id = pl.profile_id and pl.combo_id = pic.ptype_id and pic.rowindex = 1
                 LEFT JOIN pub_custom_field_baseinfo custom ON custom.sub_type=5001 AND custom.xtype_id=ptype.id
                                                                   AND custom.profile_id=ptype.profile_id
                 left join td_orderbill_detail_combo_distribution_buyer distribution on distribution.profile_id= pl.profile_id
                            and distribution.combo_row_id = pl.id
                 left join td_orderbill_detail_combo_purchase dpurchase on dpurchase.profile_id= pl.profile_id  and dpurchase.combo_row_id = pl.id
                left join td_orderbill_detail_combo_live_broadcast liveBroadcast on liveBroadcast.profile_id=pl.profile_id and liveBroadcast.orderbill_detail_combo_row_id=pl.id
        where pl.profile_id = #{profileId}
          and pl.vchcode = #{eshopOrderId}
    </select>

    <insert id="insertAdvanceSaleOrder">
            insert into td_orderbill_core (profile_id,vchcode,create_etype_id,etype_id,buyer_id,
                                                        ktype_id,btype_id,dtype_id,self_delivery_mode,create_type,
                                                        business_type,order_sale_type,deliver_type,submit_send_state,order_preferential_allot_total,
                                                        currency_bill_total,memo,summary,balance_btype_id,commission_btype_id,
                                                        create_time,update_time,otype_id,supplier_id,ptype_preferential_total,
                                                        bill_total,tax_total,ptype_commission_total,ptype_service_fee,deleted,bill_date,
                                                        eshop_order_id,order_source_type,platform_parent_order_id,sender_id,deliver_process_type)
            value
                    (#{profileId}, #{vchcode}, #{createEtypeId}, #{etypeId}, #{buyerId},
                    #{ktypeId}, #{btypeId}, #{dtypeId}, #{selfDeliveryMode}, #{createType},
                    #{businessType}, #{orderSaleType}, #{deliverType}, #{submitSendState}, #{orderPreferentialAllotTotal},
                    #{currencyBillTotal}, #{memo}, #{summary}, #{balanceBtypeId}, #{commissionBtypeId},
                    #{createTime}, #{updateTime}, #{otypeId}, #{supplierId}, #{ptypePreferentialTotal},
                    #{billTotal}, #{taxTotal}, #{ptypeCommissionTotal}, #{ptypeServiceFee}, #{deleted},#{billDate},
                    #{eshopOrderId},#{orderSourceType},#{platformParentOrderId},#{senderId},#{deliverProcessType})
    </insert>

    <insert id="insertSaleOrderAdvancePlatorm">
        INSERT INTO td_orderbill_platform (`trade_id`,  `salesman`, `pay_time_type`, `local_trade_status`,
                                                                `local_refund_process_state`,`local_refund_state`, `seller_flag`,`platform_store_id`,`platform_store_code`,
                                                                `platform_stock_id`,`platform_stock_code`, `trade_create_time`,`trade_modified_time`,`trade_finish_time`,
                                                                `trade_pay_time`,`modified_time`, `local_freight_name`,`local_freight_code`,`local_freight_bill_no`,
                                                                `platform_freight_name`,`platform_freight_code`, `customer_expected_freight_name`,`customer_expected_freight_code`,
                                                                `trade_total`,`buyer_trade_total`,
                                                                `buyer_paid_total`,`buyer_unpaid_total`, `mapping_state`,`seller_memo`,`buyer_message`,
                                                                `pay_no`,`unique_mark`, `platform_order_preferential_total`,`platform_ptype_preferential_total`,`receive_address_type`,
                                                                `receive_address_id`,`distribution_dised_taxed_total`, `distribution_tax_total`,`platform_trade_state`,`local_trade_state`,
                                                                `order_deliver_required`,`platform_distributor_name`, `mode_of_payment`,`merchant_payment_account`,`payway_id`,
                                                                `Installation_service_provider`,`collect_customer`, `hold_time`,`collect_time`,`gather_status`,
                                                                `payment_mode`,`platform_business_mark`, `export_count`,`submit_batch_id`,`group_header_name`,
                                                                `purchase_total`,vchcode,profile_id,re_send_state,eshop_id)
            value (#{tradeId}, #{salesman}, #{payTimeType}, #{localTradeStatus},
                        #{localRefundProcessState},#{localRefundState},#{sellerFlag},#{platformStoreId},#{platformStoreCode},
                        #{platformStockId},#{platformStockCode},#{tradeCreateTime},#{tradeModifiedTime},#{tradeFinishTime},
                        #{tradePayTime},#{modifiedTime},#{localFreightName},#{localFreightCode},#{localFreightBillNo},
                        #{platformFreightName},#{platformFreightCode},#{customerExpectedFreightName},#{customerExpectedFreightCode},
                       #{tradeTotal},#{buyerTradeTotal},
                        #{buyerPaidTotal},#{buyerUnpaidTotal},#{mappingState},#{sellerMemo},#{buyerMessage},
                        #{payNo},#{uniqueMark},#{platformOrderPreferentialTotal},#{platformPtypePreferentialTotal},#{receiveAddressType},
                        #{receiveAddressId},#{distributionDisedTaxedTotal},#{distributionTaxTotal},#{platformTradeState},#{localTradeState},
                        #{orderDeliverRequired},#{platformDistributorName},#{modeOfPayment},#{merchantPaymentAccount},#{paywayId},
                        #{installationServiceProvider},#{collectCustomer},#{holdTime},#{collectTime},#{gatherStatus},
                        #{paymentMode},#{platformBusinessMark},#{exportCount},#{submitBatchId},#{groupHeaderName},
                        #{purchaseTotal},#{vchcode},#{profileId},#{reSendState},#{eshopId})
    </insert>

    <insert id="insertSaleOrderAdvancePlatormExtend">
        insert into td_orderbill_platform_extend(vchcode, profile_id, pickup_code, platform_dispatcher_name,
                                                 platform_dispather_mobile, logistics_status, confirm_status, create_time,
                                                 update_time,real_buyer_id,pick_up_address_id,platform_qc_result,platform_identify_result,flow_channel,mall_deduction_fee,seller_flag_memo,
                                                 group_title,group_no,take_goods_code,platform_quality_org_id,platform_quality_org_name,platform_quality_warehouse_code,platform_quality_warehouse_name,
                                                 anchor_order_preferential_total,platform_order_subsidy_total,sale_period_num,current_period_num,`national_subsidy_total`)
            value (#{vchcode}, #{profileId}, #{pickupCode}, #{platformDispatcherName},
                   #{platformDispatherMobile},#{logisticsStatus},#{confirmStatus},#{createTime},#{updateTime},#{realBuyerId},#{pickUpAddressId},#{platformQcResult},#{platformIdentifyResult},#{flowChannel},#{mallDeductionFee},#{sellerFlagMemo},
                   #{groupTitle},#{groupNo},#{takeGoodsCode},#{platformQualityOrgId},#{platformQualityOrgName},#{platformQualityWarehouseCode},#{platformQualityWarehouseName},
                   #{anchorOrderPreferentialTotal},#{platformOrderSubsidyTotal},#{salePeriodNum},#{currentPeriodNum},#{nationalSubsidyTotal})
    </insert>


    <insert id="insertSaleOrderAdvanceAssInfo">
        INSERT INTO td_orderbill_assinfo (`vchcode`,`profile_id`, `currency_ptype_preferential_total`, `currency_order_preferential_allot_total`, `currency_tax_total`,
                                                        `currency_ptype_commission_total`,`currency_ptype_service_fee`,`currency_order_buyer_freight_fee`, `currency_total`,`currency_advance_total`)
            value (#{vchcode}, #{profileId}, #{currencyPtypePreferentialTotal}, #{currencyOrderPreferentialAllotTotal}, #{currencyTaxTotal},
                        #{currencyPtypeCommissionTotal},#{currencyPtypeServiceFee},#{currencyOrderBuyerFreightFee},#{currencyTotal},#{currencyAdvanceTotal})
    </insert>

    <insert id="insertAdvanceOrderDistributionBuyer">
        insert into td_orderbill_distribution_buyer
        (profile_id,distribution_buyer_trade_id,vchcode,create_time,update_time)
        values (#{profileId},#{distributionBuyerTradeId}, #{vchcode,}, #{createTime}, #{updateTime})
    </insert>

    <insert id="insertAdvanceOrderFreight">
        insert into td_orderbill_freight
        (id,profile_id,vchcode,freight_no,freight_name,freight_btype_id,freight_code,freight_intercept_status,create_time)
        values(#{id},#{profileId},#{vchcode},#{freightNo},#{freightName},#{freightBtypeId},#{freightCode},#{freightInterceptStatus},#{createTime})
    </insert>

    <insert id="batchInsertAdvanceOrderFreight">
        insert into td_orderbill_freight(id,profile_id,vchcode,freight_no,freight_name,freight_btype_id,freight_code,freight_intercept_status,create_time)
        values
        <foreach collection="list" item="freight" separator=",">
            (#{freight.id}, #{freight.profileId}, #{freight.vchcode}, #{freight.freightNo}, #{freight.freightName}, #{freight.freightBtypeId}, #{freight.freightCode},
            #{freight.freightInterceptStatus}, #{freight.createTime})
        </foreach>
    </insert>


    <insert id="insertAdvanceOrderTiming">
        insert into td_orderbill_timing
        (id,vchcode, profile_id, cn_service, plan_send_time, send_time, system_timing, timing_type,promised_collect_time,promised_sign_time,create_time,update_time,plan_sign_time,promised_sync_freight_time,sign_time,promised_sign_startTime)
        values (#{id},#{vchcode}, #{profileId}, #{cnService}, #{planSendTime}, #{sendTime}, #{systemTiming}, #{timingType},#{promisedCollectTime}, #{promisedSignTime}, #{createTime},#{updateTime},#{planSignTime},#{promisedSyncFreightTime},#{signTime},#{promisedSignStartTime})
    </insert>

    <insert id="insertAdvanceSaleOrderDetail">
        INSERT INTO td_orderbill_detail_core
        (`profile_id`, `detail_id`, `vchcode`, `ktype_id`, `ptype_id`,
         `sku_id`,`qty`, `sub_qty`, `tax_rate`,
         `tax_total`,`dised_taxed_price`,`dised_taxed_total`, `dised_price`, `dised_total`,
         `combo_detail_id`,`bill_date`,`gift` ,`dised_initial_price`,`dised_initial_total`)
        values (#{profileId},#{detailId},#{vchcode},#{ktypeId},#{ptypeId},
                    #{skuId},#{qty},#{subQty},#{taxRate},
                    #{taxTotal},#{disedTaxedPrice},#{disedTaxedTotal},#{disedPrice},#{disedTotal},
                     #{comboDetailId},#{billDate},#{gift},#{disedInitialPrice},#{disedInitialTotal})
    </insert>

    <insert id="insertAdvanceOrderDetailPlatform">
        INSERT INTO td_orderbill_detail_platform
        (`vchcode`, `detail_id`, `trade_order_detail_id`, `eshop_order_id`, `order_sale_type`,`profile_id`,
         `platform_num_id`,`platform_product_name`,`platform_xcode`, `platform_pic_url`, `platform_sku_id`,
         `platform_properties_name`,`process_state`,`platform_store_code`, `platform_store_id`, `platform_stock_id`,
         `platform_stock_code`,`trade_total`,`trade_price`, `refund_total`, `refund_qty`,
         `ptype_service_fee`,`local_mapping_mark`,`mapping_state`, `deliver_required`, `seller_memo`,
         `buyer_message`,`stock_sync_rule_id`,`local_refund_process_state`, `local_refund_state`, `remark`,
         `is_sale_qty_need_to_occupy`,`platform_ptype_preferential_total`,`platform_order_preferential_total`, `combo_share_scale`,platform_detail_trade_state,re_send_state,display_custom_info,
         `platform_qc_result`,`platform_qc_result_desc`,`platform_identify_result`,`platform_identify_result_desc`,`verify_code`,`flow_channel`,`mall_deduction_rate`,`mall_deduction_fee`,
         `platform_order_subsidy_total`,`platform_ptype_subsidy_total`,`anchor_order_preferential_total`,`anchor_ptype_preferential_total`,`national_subsidy_total`)
        values (#{vchcode},#{detailId},#{tradeOrderDetailId},#{eshopOrderId},#{orderSaleType},#{profileId},
                    #{platformNumId},#{platformProductName},#{platformXcode},#{platformPicUrl},#{platformSkuId},
                    #{platformPropertiesName},#{processState},#{platformStoreCode},#{platformStoreId},#{platformStockId},
                    #{platformStockCode},#{tradeTotal},#{tradePrice},#{refundTotal},#{refundQty},
                    #{ptypeServiceFee},#{localMappingMark},#{mappingState},#{deliverRequired},#{sellerMemo},
                    #{buyerMessage},#{stockSyncRuleId},#{localRefundProcessState},#{localRefundState},#{remark},
                    #{isSaleQtyNeedToOccupy},#{platformPtypePreferentialTotal},#{platformOrderPreferentialTotal},#{comboShareScale},#{platformDetailTradeState},#{reSendState},#{displayCustomInfo},
                #{platformQcResult},#{platformQcResultDesc},#{platformIdentifyResult},#{platformIdentifyResultDesc},#{verifyCode},#{flowChannel},#{mallDeductionRate},#{mallDeductionFee},
                #{platformOrderSubsidyTotal},#{platformPtypeSubsidyTotal},#{anchorOrderPreferentialTotal},#{anchorPtypePreferentialTotal},#{nationalSubsidyTotal})
    </insert>

    <insert id="insertAdvanceOrderDetailAssinfo">
         INSERT INTO td_orderbill_detail_assinfo
        (`vchcode`, `detail_id`, `unit_qty`, `currency_price`, `currency_total`,`discount`,`profile_id`,
         `currency_ptype_preferential_total`,`currency_order_preferential_allot_total`,`ptype_commission_total`,`unit_id`)
        values (#{vchcode},#{detailId},#{unitQty},#{currencyPrice},#{currencyTotal},#{discount},
                    #{profileId},#{currencyPtypePreferentialTotal},#{currencyOrderPreferentialAllotTotal},#{ptypeCommissionTotal},#{unitId})
     </insert>

    <insert id="insertAdvanceSaleOrderInvoice">
        insert into td_orderbill_invoice_info
        (`id`, `profile_id`, `vchcode`, `invoice_required`, `invoice_type`,  `invoice_state`,
         `invoice_title`, `invoice_code`,  invoice_remark, invoice_category,
         invoice_company,invoice_register_addr,invoice_register_phone,invoice_bank,invoice_bank_account,secret_id)
            value
            (#{id}, #{profileId}, #{vchcode}, #{invoiceRequired}, #{invoiceType},  #{invoiceState},
             #{invoiceTitle}, #{invoiceCode}, #{invoiceRemark}, #{invoiceCategory},
             #{invoiceCompany},#{invoiceRegisterAddr},#{invoiceRegisterPhone},#{invoiceBank},#{invoiceBankAccount},#{secretId})
    </insert>

    <insert id="insertAdvanceDetailComboRow">
    insert into td_orderbill_detail_combo
        (profile_id, id, vchcode, combo_id, trade_order_detail_id,
            qty, trade_price, trade_total, currency_price,currency_total,
            discount, currency_order_preferential_allot_total, dised_taxed_price, dised_taxed_total,
            tax_total, dised_price, dised_total, memo, ptype_commission_total,
            ptype_service_fee, deliver_required, seller_memo, buyer_message, platform_pic_url,
            platform_ptype_preferential_total, platform_order_preferential_total,bill_date,currency_ptype_preferential_total,`mall_deduction_rate`,`mall_deduction_fee`,
         `platform_order_subsidy_total`,`platform_ptype_subsidy_total`,`gift`,`national_subsidy_total`)
            value
            (#{profileId}, #{id}, #{vchcode}, #{comboId}, #{tradeOrderDetailId},
                #{qty}, #{tradePrice},#{tradeTotal}, #{currencyPrice}, #{currencyTotal},
                #{discount},#{currencyOrderPreferentialAllotTotal},#{disedTaxedPrice},  #{disedTaxedTotal},
                #{taxTotal}, #{disedPrice}, #{disedTotal},#{memo},#{ptypeCommissionTotal},
                #{ptypeServiceFee}, #{deliverRequired}, #{sellerMemo},#{buyerMessage},#{platformPicUrl},
             #{platformPtypePreferentialTotal},#{platformOrderPreferentialTotal},#{billDate},#{currencyPtypePreferentialTotal},
             #{mallDeductionRate},#{mallDeductionFee},#{platformOrderSubsidyTotal},#{platformPtypeSubsidyTotal},#{gift},#{nationalSubsidyTotal})
     </insert>

    <select id="queryAllAdvanceOrderFields"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryAdvanceOrderParameter"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderEntity">
        <include refid="queryAllAdvanceOrderFieldsSelectSql"/>
        <include refid="queryAllAdvanceOrderFieldsJoinSql"/>
        <include refid="queryAllAdvanceOrderFieldsParamSql"/>
        <if test="hasForwardSaleByStockAdvance != null and hasForwardSaleByStockAdvance == true">
            UNION
            <include refid="queryAllAdvanceOrderFieldsSelectSql"/>
            <include refid="queryAllAdvanceOrderFieldsJoinSql"/>
            <include refid="queryAllAdvanceOrderFieldsParamByStockAdvanceSql"/>
        </if>
        order by 'platform.trade_pay_time' desc
    </select>

    <select id="queryHasSubmitAdvanceOrderId" resultType="java.math.BigInteger">
        SELECT p.vchcode FROM td_orderbill_platform p
                       JOIN td_orderbill_core c on p.profile_id=c.profile_id AND p.vchcode = c.vchcode
                       where p.profile_id=#{profileId} and p.eshop_id=#{eshopId} and p.trade_id =#{tradeId} and c.submit_send_state=1 limit 1
    </select>

    <sql id="queryAllAdvanceOrderFieldsSelectSql">
        SELECT `core`.vchcode,
               `core`.otype_id,
               `core`.profile_id,
               `core`.etype_id,
               `core`.create_type,
               `core`.create_etype_id,
               `core`.business_type,
               `core`.order_source_type,
               `core`.deliver_type,
               `core`.platform_parent_order_id,
               `core`.create_time,
               `core`.update_time,
               `core`.tax_total,
               `core`.buyer_id,
               `core`.btype_id,
               `core`.ktype_id,
               `core`.currency_bill_total,
               `core`.memo,
               `core`.dtype_id,
               `core`.submit_send_state,
               `core`.order_sale_type,
               `core`.self_delivery_mode,
               `core`.summary,
               `core`.eshop_order_id,
               `core`.supplier_id AS 'core.supplierId',
               `core`.balance_btype_id,
               `core`.commission_btype_id,
               `core`.sender_id,
               `core`.deliver_process_type,
               `platform`.trade_id as 'platform.trade_id',
               `platform`.seller_flag as 'platform.seller_flag',
               `platform`.platform_trade_state as 'platform.platform_trade_state',
        `platform`.local_trade_state as 'platform.local_trade_state',
        `platform`.local_refund_process_state as 'platform.local_refund_process_state',
        `platform`.seller_flag as 'platform.seller_flag',
               `platform`.platform_store_id as 'platform.platform_store_id',
               `platform`.platform_store_code as 'platform.platform_store_code',
               `platform`.pay_time_type as 'platform.pay_time_type',
               `platform`.trade_create_time as 'platform.trade_create_time',
               `platform`.trade_modified_time as 'platform.trade_modified_time',
               `platform`.trade_finish_time as 'platform.trade_finish_time',
               `platform`.trade_pay_time as 'platform.trade_pay_time',
               `platform`.modified_time as 'platform.modified_time',
               `platform`.local_freight_name as 'platform.local_freight_name',
               `platform`.local_freight_code as 'platform.local_freight_code',
               `platform`.local_freight_bill_no as 'platform.local_freight_bill_no',
               `platform`.platform_freight_name as 'platform.platform_freight_name',
               `platform`.platform_freight_code as 'platform.platform_freight_code',
               `platform`.customer_expected_freight_name as 'platform.customer_expected_freight_name',
               `platform`.customer_expected_freight_code as 'platform.customer_expected_freight_code',
               `platform`.trade_total as 'platform.trade_total',
               `platform`.buyer_trade_total as 'platform.buyer_trade_total',
               `platform`.buyer_unpaid_total as 'platform.buyer_unpaid_total',
               `platform`.buyer_paid_total as 'platform.buyer_paid_total',
               `platform`.platform_ptype_preferential_total as 'platform.platform_ptype_preferential_total',
               `platform`.mapping_state as 'platform.mapping_state',
               `platform`.order_deliver_required as 'platform.order_deliver_required',
               `platform`.seller_memo as 'platform.seller_memo',
               `platform`.buyer_message as 'platform.buyer_message',
               `platform`.pay_no as 'platform.pay_no',
               `platform`.unique_mark as 'platform.unique_mark',
               `platform`.local_refund_state as 'platform.local_refund_state',
               `platform`.salesman as 'platform.salesman',
               `platform`.platform_ptype_preferential_total as 'platform.platform_ptype_preferential_total',
               `platform`.platform_stock_id as 'platform.platform_stock_id',
               `platform`.platform_stock_code as 'platform.platform_stock_code',
               `platform`.vchcode                        AS 'platform.vchcode',
               `platform`.export_count                   AS 'platform.export_count',
               `platform`.profile_id                     AS 'platform.profile_id',
               `platform`.create_time                    AS 'platform.create_time',
               `platform`.update_time                    AS 'platform.update_time',
               `platform`.Installation_service_provider  AS 'platform.Installation_service_provider',
               `platform`.collect_time                   AS 'platform.collect_time',
               `platform`.collect_customer               AS 'platform.collect_customer',
               `platform`.gather_status                  AS 'platform.gather_status',
               `platform`.payment_mode                   AS 'platform.payment_mode',
               `platform`.platform_business_mark         AS 'platform.platform_business_mark',
               `platform`.hold_time                      AS 'platform.hold_time',
               `platform`.submit_batch_id as 'platform.submitBatchId',
               `platform`.purchase_total AS 'platform.purchaseTotal',
               `platform`.platform_order_preferential_total AS 'platform.platform_order_preferential_total',
               `platform`.re_send_state AS 'platform.re_send_state',
               `platform`.group_header_name AS 'platform.group_header_name',
               `platformExtend`.vchcode as 'platform.platformExtend.vchcode',
               `platformExtend`.profile_id as 'platform.platformExtend.profile_id',
               `platformExtend`.pickup_code as 'platform.platformExtend.pickup_code',
               `platformExtend`.platform_dispatcher_name as 'platform.platformExtend.platform_dispatcher_name',
               `platformExtend`.platform_dispather_mobile as 'platform.platformExtend.platform_dispather_mobile',
               `platformExtend`.logistics_status as 'platform.platformExtend.logistics_status',
               `platformExtend`.confirm_status as 'platform.platformExtend.confirm_status',
               `platformExtend`.real_buyer_id as 'platform.platformExtend.real_buyer_id',
               `platformExtend`.pick_up_address_id as 'platform.platformExtend.pick_up_address_id',
               `platformExtend`.platform_qc_result as 'platform.platformExtend.platform_qc_result',
               `platformExtend`.platform_identify_result as 'platform.platformExtend.platform_identify_result',
               `platformExtend`.flow_channel as 'platform.platformExtend.flow_channel',
               `platformExtend`.mall_deduction_fee as 'platform.platformExtend.mall_deduction_fee',
               `platformExtend`.seller_flag_memo as 'platform.platformExtend.seller_flag_memo',
               `platformExtend`.group_title as 'platform.platformExtend.group_title',
               `platformExtend`.group_no as 'platform.platformExtend.group_no',
               `platformExtend`.take_goods_code as 'platform.platformExtend.take_goods_code',
               `platformExtend`.platform_quality_org_id as 'platform.platformExtend.platform_quality_org_id',
               `platformExtend`.platform_quality_org_name as 'platform.platformExtend.platform_quality_org_name',
               `platformExtend`.platform_quality_warehouse_code as 'platform.platformExtend.platform_quality_warehouse_code',
               `platformExtend`.platform_quality_warehouse_name as 'platform.platformExtend.platform_quality_warehouse_name',
               `platformExtend`.platform_order_subsidy_total as 'platform.platformExtend.platform_order_subsidy_total',
               `platformExtend`.anchor_order_preferential_total as 'platform.platformExtend.anchor_order_preferential_total',
        `platformExtend`.sale_period_num as 'platform.platformExtend.sale_period_num',
        `platformExtend`.current_period_num as 'platform.platformExtend.current_period_num',
        `platformExtend`.national_subsidy_total as 'platform.platformExtend.national_subsidy_total',
               `assinfo`.currency_order_buyer_freight_fee as 'assinfo.currency_order_buyer_freight_fee',
               `assinfo`.currency_ptype_service_fee as 'assinfo.currency_ptype_service_fee',
               `assinfo`.currency_order_preferential_allot_total as 'assinfo.currency_order_preferential_allot_total',
               `assinfo`.currency_ptype_commission_total as 'assinfo.currency_ptype_commission_total',
               `assinfo`.currency_advance_total as 'assinfo.currency_advance_total',
               if(`platform`.distribution_dised_taxed_total = 0,`core`.currency_bill_total,`platform`.distribution_dised_taxed_total) as distribution_dised_taxed_total,
               `timing`.vchcode         AS 'timing.vchcode',
               `timing`.profile_id      AS 'timing.profile_id',
               `timing`.cn_service      AS 'timing.cn_service',
               `timing`.plan_send_time  AS 'timing.plan_send_time',
               `timing`.send_time       AS 'timing.send_time',
               `timing`.system_timing   AS 'timing.system_timing',
               `timing`.create_time     AS 'timing.create_time',
               `timing`.update_time     AS 'timing.update_time',
               `timing`.plan_sign_time     AS 'timing.plan_sign_time',
               `timing`.timing_type     AS 'timing.timing_type',
               `timing`.promised_collect_time     AS 'timing.promisedCollectTime',
               `timing`.promised_sign_time     AS 'timing.promisedSignTime',
               `timing`.sign_time     AS 'timing.signTime',
               `timing`.promised_sign_startTime     AS 'timing.promisedSignStartTime',
        `timing`.promised_sync_freight_time     AS 'timing.promisedSyncFreightTime',
               `invoice`.id                      AS 'invoice.id',
               `invoice`.profile_id              AS 'invoice.profile_id',
               `invoice`.vchcode                 AS 'invoice.vchcode',
               `invoice`.invoice_required        AS 'invoice.invoice_required',
               `invoice`.invoice_type            AS 'invoice.invoice_type',
               `invoice`.invoice_total           AS 'invoice.invoice_total',
               `invoice`.invoice_state           AS 'invoice.invoice_state',
               `invoice`.invoice_title           AS 'invoice.invoice_title',
               `invoice`.invoice_code            AS 'invoice.invoice_code',
               `invoice`.invoice_special_info    AS 'invoice.invoice_special_info',
               `invoice`.invoice_remark          AS 'invoice.invoice_remark',
               `invoice`.create_time             AS 'invoice.create_time',
               `invoice`.update_time             AS 'invoice.create_time',
               `invoice`.invoice_company         AS 'invoice.invoice_company',
               `invoice`.invoice_register_addr   AS 'invoice.invoice_register_addr',
               `invoice`.invoice_register_phone  AS 'invoice.invoice_register_phone',
               `invoice`.invoice_bank            AS 'invoice.invoice_bank',
               `invoice`.invoice_bank_account    AS 'invoice.invoice_bank_account',
               `invoice`.invoice_category    AS 'invoice.invoice_category',
               `invoice`.secret_id    AS 'invoice.secret_id',
               `config`.process_type,
               `eshop`.eshop_type as shopType,
               `distribution`.profile_id AS 'distributionBuyer.profile_id',
               `distribution`.distribution_buyer_trade_id AS 'distributionBuyer.distribution_buyer_trade_id',
               `distribution`.vchcode AS 'distributionBuyer.vchcode',
               `distribution`.create_time AS 'distributionBuyer.create_time',
               `distribution`.update_time AS 'distributionBuyer.update_time',
               `smap`.platform_store_name AS 'platform.platform_stock_name',
               `qic`.id as 'platform.platformExtend.qicConfig.id',
               `qic`.profile_id as 'platform.platformExtend.qicConfig.profile_id',
               `qic`.eshop_id as 'platform.platformExtend.qicConfig.eshop_id',
               `qic`.update_time as 'platform.platformExtend.qicConfig.update_time',
               `qic`.create_time as 'platform.platformExtend.qicConfig.create_time',
               `qic`.platform_quality_org_id as 'platform.platformExtend.qicConfig.platform_quality_org_id',
               `qic`.platform_quality_org_name as 'platform.platformExtend.qicConfig.platform_quality_org_name',
               `qic`.platform_quality_warehouse_code as 'platform.platformExtend.qicConfig.platform_quality_warehouse_code',
               `qic`.platform_quality_warehouse_name as 'platform.platformExtend.qicConfig.platform_quality_warehouse_name',
               `qic`.platform_quality_status as 'platform.platformExtend.qicConfig.platform_quality_status',
               `qic`.platform_quality_refund_interception_code as 'platform.platformExtend.qicConfig.platform_quality_refund_interception_code',
               `qic`.platform_quality_receive_address as 'platform.platformExtend.qicConfig.platform_quality_receive_address',
               `qic`.platform_quality_btype_code as 'platform.platformExtend.qicConfig.platform_quality_btype_code',
               `qic`.platform_quality_btype_name as 'platform.platformExtend.qicConfig.platform_quality_btype_name',
               `qic`.platform_quality_btype_insure as 'platform.platformExtend.qicConfig.platform_quality_btype_insure',
               `qic`.platform_quality_btype_insure_total as 'platform.platformExtend.qicConfig.platform_quality_btype_insure_total',
               `qic`.platform_quality_btype_insure_type as 'platform.platformExtend.qicConfig.platform_quality_btype_insure_type',
               `qic`.platform_quality_btype_backup_code as 'platform.platformExtend.qicConfig.platformQualityBtypeCodeBackup',
               `qic`.platform_quality_btype_backup_name as 'platform.platformExtend.qicConfig.platformQualityBtypeNameBackup',
               `qic`.platform_quality_btype_backup_insure as 'platform.platformExtend.qicConfig.platform_quality_btype_backup_insure',
               `qic`.platform_quality_btype_backup_insure_total as 'platform.platformExtend.qicConfig.platform_quality_btype_backup_insure_total',
               `qic`.platform_quality_btype_backup_insure_type as 'platform.platformExtend.qicConfig.platform_quality_btype_backup_insure_type',
               `qic`.platform_quality_comment as 'platform.platformExtend.qicConfig.platform_quality_comment',
               `qic`.platform_quality_btype_product as 'platform.platformExtend.qicConfig.platform_quality_btype_product',
               `qic`.platform_quality_btype_backup_product as 'platform.platformExtend.qicConfig.platformQualityBtypeBackupProduct'
        FROM ${tableName}
    </sql>

    <sql id="queryAllAdvanceOrderFieldsJoinSql">
        LEFT JOIN td_orderbill_assinfo assinfo ON assinfo.profile_id=`core`.profile_id AND assinfo.vchcode=`core`.vchcode
        LEFT JOIN td_orderbill_timing timing ON timing.profile_id=`core`.profile_id AND timing.vchcode=`core`.vchcode
        LEFT JOIN td_orderbill_invoice_info invoice ON invoice.profile_id=`core`.profile_id AND invoice.vchcode=`core`.vchcode
        LEFT JOIN td_orderbill_platform platform ON platform.profile_id=`core`.profile_id AND platform.vchcode=`core`.vchcode
        LEFT JOIN td_orderbill_platform_extend platformExtend ON platformExtend.profile_id=`core`.profile_id AND platformExtend.vchcode=`core`.vchcode
        LEFT JOIN td_orderbill_distribution_buyer distribution ON distribution.profile_id=`core`.profile_id AND distribution.vchcode=`core`.vchcode
        LEFT JOIN pl_eshop eshop ON eshop.otype_id = `core`.otype_id AND eshop.profile_id = `core`.profile_id
        LEFT JOIN pl_eshop_config config ON config.eshop_id = `core`.otype_id AND config.profile_id = `core`.profile_id
        LEFT JOIN pl_eshop_platform_store_mapping smap ON smap.platform_store_stock_id = `platform`.platform_stock_id AND smap.eshop_id = `core`.otype_id AND smap.profile_id = `core`.profile_id
        LEFT JOIN pl_eshop_qic_config qic ON qic.eshop_id = `core`.otype_id AND qic.profile_id = `core`.profile_id AND platformExtend.platform_qc_result=5  AND config.platform_quality_status=1
    </sql>

    <sql id="queryAllAdvanceOrderFieldsParamSql">
        WHERE `core`.profile_id=#{profileId}
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@Mapping">
            AND `platform`.mapping_state=1
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@NotMapping">
            AND `platform`.mapping_state=0
        </if>
        <if test="autoSubmitQuery!=null and autoSubmitQuery ">
            AND `core`.submit_send_state=0
            AND  `platform`.local_trade_state IN(1,2) AND `platform`.order_deliver_required=1
            AND `platform`.local_refund_state=0
            <if test="hasForwardSaleByStockAdvance != null and hasForwardSaleByStockAdvance == false">
                <if test="advanceTimeType != null and advanceTimeType == 1">
                    AND `platform`.trade_pay_time between #{beginTime} and #{endTime}
                </if>
                <if test="advanceTimeType != null and advanceTimeType == 2">
                    AND `core`.create_time between #{beginTime} and #{endTime}
                </if>
                <if test="advanceTimeType != null and advanceTimeType == 3">
                    AND `timing`.plan_send_time between #{beginTime} and #{endTime}
                </if>
            </if>
        </if>
        <if test="autoSubmitQuery!=null and !autoSubmitQuery ">
            <if test="advanceTimeType != null and advanceTimeType == 1">
                AND `platform`.trade_pay_time between #{beginTime} and #{endTime}
            </if>
            <if test="advanceTimeType != null and advanceTimeType == 2">
                AND `core`.create_time between #{beginTime} and #{endTime}
            </if>
            <if test="advanceTimeType != null and advanceTimeType == 3">
                AND `timing`.plan_send_time between #{beginTime} and #{endTime}
            </if>
        </if>
        <if test="eshopOrderId!='' and eshopOrderId!=null">
            AND `core`.vchcode=#{eshopOrderId}
        </if>
        <if test="tradeOrderIds!=null and tradeOrderIds.size()>0">
            AND `platform`.trade_id IN
            <foreach collection="tradeOrderIds" close=")" open="(" separator="," item="tid">
                #{tid}
            </foreach>
        </if>
        <if test="otypeIds!=null and otypeIds.size() > 0">
            AND `core`.otype_id IN
            <foreach collection="otypeIds" close=")" open="(" separator="," item="otypeId">
                #{otypeId}
            </foreach>
        </if>
        <if test="platformParentOrderIds!=null and platformParentOrderIds.size() > 0">
            AND `core`.platform_parent_order_id IN
            <foreach collection="platformParentOrderIds" close=")" open="(" separator="," item="platformParentOrderId">
                #{platformParentOrderId}
            </foreach>
        </if>
        <if test="tradeStatus !=null and tradeStatus.size() > 0 ">
            and ( `platform`.local_trade_state in
            <foreach collection="tradeStatus" close=")" open="(" separator="," item="simpleType">
                #{simpleType}
            </foreach>
            <if test="deliverSendState !=null and  deliverSendState==@com.wsgjp.ct.sale.biz.eshoporder.api.enums.ProcessStatusEnum@SENDED">
                OR `platform`.deliver_Send_State=35
            </if>
            )
        </if>
        <if test="submitBatchId != null">
            and `platform`.submit_batch_id=#{submitBatchId}
        </if>
        <if test="refundStates !=null and refundStates.size() > 0 ">
            and `platform`.local_refund_state in
            <foreach collection="refundStates" close=")" open="(" separator="," item="simpleType">
                #{simpleType}
            </foreach>
        </if>
        <if test="processState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.ProcessState@Submit">
            AND `core`.submit_send_state=1
        </if>
        <if test="processState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.ProcessState@NoSubmit">
            AND `core`.submit_send_state=0
            AND  `platform`.order_deliver_required=1
            AND `platform`.local_trade_state!=5
        </if>
        <if test="eshopOrderIdList!=null and eshopOrderIdList.size()>0">
            AND `core`.vchcode IN
            <foreach collection="eshopOrderIdList" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="deleted != null ">
            AND `core`.deleted= #{deleted}
        </if>
    </sql>

    <sql id="queryAllAdvanceOrderFieldsParamByStockAdvanceSql">
        WHERE `core`.profile_id=#{profileId}
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@Mapping">
            AND `platform`.mapping_state=1
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@NotMapping">
            AND `platform`.mapping_state=0
        </if>
        <if test="autoSubmitQuery!=null and autoSubmitQuery ">
            AND `core`.submit_send_state=0
            AND  `platform`.local_trade_state IN(1,2) AND `platform`.order_deliver_required=1
            AND `platform`.local_refund_state=0
        </if>
        AND `core`.create_time between #{beginTime} and #{endTime}
        AND (EXISTS
        (SELECT 1 FROM pl_eshop_order_mark AS mark
        WHERE mark.profile_id=`core`.profile_id AND  mark.order_id = `core`.vchcode AND mark.mark_code=10000018))
        <if test="eshopOrderId!='' and eshopOrderId!=null">
            AND `core`.vchcode=#{eshopOrderId}
        </if>
        <if test="tradeOrderIds!=null and tradeOrderIds.size()>0">
            AND `platform`.trade_id IN
            <foreach collection="tradeOrderIds" close=")" open="(" separator="," item="tid">
                #{tid}
            </foreach>
        </if>
        <if test="otypeIds!=null and otypeIds.size() > 0">
            AND `core`.otype_id IN
            <foreach collection="otypeIds" close=")" open="(" separator="," item="otypeId">
                #{otypeId}
            </foreach>
        </if>
        <if test="tradeStatus !=null and tradeStatus.size() > 0 ">
            and ( `platform`.local_trade_state in
            <foreach collection="tradeStatus" close=")" open="(" separator="," item="simpleType">
                #{simpleType}
            </foreach>
            <if test="deliverSendState !=null and  deliverSendState==@com.wsgjp.ct.sale.biz.eshoporder.api.enums.ProcessStatusEnum@SENDED">
                OR `platform`.deliver_Send_State=35
            </if>
            )
        </if>
        <if test="submitBatchId != null">
            and `platform`.submit_batch_id=#{submitBatchId}
        </if>
        <if test="refundStates !=null and refundStates.size() > 0 ">
            and `platform`.local_refund_state in
            <foreach collection="refundStates" close=")" open="(" separator="," item="simpleType">
                #{simpleType}
            </foreach>
        </if>
        <if test="processState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.ProcessState@Submit">
            AND `core`.submit_send_state=1
        </if>
        <if test="processState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.ProcessState@NoSubmit">
            AND `core`.submit_send_state=0
            AND  `platform`.deliver_required=1
            AND `platform`.local_trade_state!=5
        </if>
        <if test="eshopOrderIdList!=null and eshopOrderIdList.size()>0">
            AND `core`.vchcode IN
            <foreach collection="eshopOrderIdList" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="deleted != null ">
            AND `core`.deleted= #{deleted}
        </if>
    </sql>

    <select id="queryAllAdvanceOrderDetailFields"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderDetail">
        select
        `d`.*,
        `unit`.unit_name as unitName,
        `unit`.unit_rate as unitRate
        from ${tableName}
        left join td_orderbill_detail_assinfo da on da.profile_id=d.profile_id and da.detail_id = d.detail_id
        left join base_ptype_unit unit on unit.profile_id=d.profile_id and unit.id=da.unit_id
        left join td_orderbill_detail_platform dp on dp.profile_id=d.profile_id and dp.vchcode=d.vchcode and dp.detail_id=d.detail_id
        where d.profile_id=#{profileId}
        <if test="advanceOrderId!=null">
            And d.vchcode=#{advanceOrderId}
        </if>
        <if test="advanceOrderIds!=null and advanceOrderIds.size>0">
            And d.vchcode IN
            <foreach collection="advanceOrderIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@NotMapping">
            AND dp.mapping_state=0
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@Mapping">
            AND dp.mapping_state=1
        </if>
        <if test="waitAudit==true">
            AND dp.mapping_state=1 AND dp.local_refund_state!=4  and dp.deliver_required=1 and d.ptype_id>0
        </if>
        <if test="waitSendAudit==true">
            AND dp.mapping_state=1  and dp.deliver_required=1
        </if>
        ORDER BY d.combo_detail_id ASC
    </select>

    <select id="queryAllAdvanceOrderComboFields" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderDetailCombo">
        select `pl`.*
        from td_orderbill_detail_combo pl
        where pl.profile_id = #{profileId}
          and pl.vchcode = #{advanceOrderId}
    </select>

    <update id="updateAdvanceOrderSellerMemo">
        update td_orderbill_platform
        set seller_memo=#{message},
            seller_flag=#{flageId}
        WHERE profile_id = #{profileId}
          and vchcode = #{id}
    </update>

    <select id="queryAdvanceOrderFullInfo"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderEntity">
        select od.*,e.fullname as otypeName,bo.ocategory,
        e.eshop_type as shopType,abb.pr_total as btypePrTotal,ec.btype_generate_type,
        be.fullname as etypeName,bk.fullname as ktypeName,
        bb.fullname as btypeName,
        bd.fullname as departmentName,
        `timing`.advance_order_id         AS 'timing.advance_order_id',
        `timing`.profile_id      AS 'timing.profile_id',
        `timing`.cn_service      AS 'timing.cn_service',
        `timing`.plan_send_time  AS 'timing.plan_send_time',
        `timing`.send_time       AS 'timing.send_time',
        `timing`.system_timing   AS 'timing.system_timing',
        `timing`.create_time     AS 'timing.create_time',
        `timing`.update_time     AS 'timing.update_time',
        `timing`.timing_type     AS 'timing.timing_type'
        from td_orderbill_core od
        left join td_orderbill_timing timing on timing.profile_id=od.profile_id and timing.vchcode=od.vchcode
        left join td_orderbill_platform platform on platform.profile_id=od.profile_id and platform.vchcode=od.vchcode
        left join pl_eshop e on e.profile_id=od.profile_id and e.otype_id=od.otype_id
        left join base_otype bo on bo.profile_id=od.profile_id and bo.id=od.otype_id
        left join base_etype be on be.profile_id=od.profile_id and be.id=od.etype_id
        left join base_ktype bk on bk.profile_id=od.profile_id and bk.id=od.ktype_id
        left join base_btype bb on bb.profile_id=od.profile_id and bb.id=od.btype_id
        left join base_dtype bd on bd.profile_id=od.profile_id and bd.id=od.dtype_id
        left join acc_btype_balance abb on abb.profile_id=od.profile_id and abb.btype_id=od.btype_id
        left join pl_eshop_config ec on ec.profile_id=od.profile_id and ec.eshop_id=od.otype_id
        where od.profile_id=#{profileId}
        <if test="tradeOrderId!=null and tradeOrderId!=''">
            and platform.trade_id=#{tradeOrderId}
        </if>
        <if test="advanceOrderId!=null and advanceOrderId!=0">
            and od.vchcode=#{advanceOrderId}
        </if>
        <if test="otypeIds!=null and otypeIds.size()>0">
            AND `platform`.otype_id IN
            <foreach collection="otypeIds" close=")" open="(" separator="," item="otypeid">
                #{otypeid}
            </foreach>
        </if>
        limit 1
    </select>

    <select id="queryOrderInvoice" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderInvoiceInfo">
        select *
        from td_orderbill_invoice_info
        where profile_id = #{profileId}
          and vchcode = #{advanceOrderId}
    </select>

    <select id="queryOrderDetailDistribution"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderDetailDistributionBuyer">
        select *
        from td_orderbill_detail_distribution_buyer
        where profile_id = #{profileId}
          and detail_id = #{detailId}
    </select>

    <update id="modifySellerMemo"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.params.BatchOrderParams">
      update td_orderbill_platform
        set
        seller_memo=#{sellerMemo},
        seller_flag=#{flagId} ,modified_time=now()
        where profile_id=#{profileId} and vchcode=#{eshopOrderId}
    </update>

    <update id="modifySellerMemoBySaleVchcode"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.params.BatchOrderParams">
        update td_orderbill_platform p
            left join td_orderbill_core c on c.profile_id=p.profile_id and c.vchcode = p.vchcode
        set
            p.seller_memo=#{sellerMemo},
            p.seller_flag=#{flagId} ,p.modified_time=now()
        where p.profile_id=#{profileId} and c.eshop_order_id = #{eshopOrderId}
    </update>

    <update id="updateAdvanceSaleOrderExpectDeliverTime">
            update td_orderbill_timing
            set plan_send_time=#{timing.planSendTime}
            where profile_id=#{profileId} and vchcode=#{vchcode}
    </update>

    <update id="updateAdvanceSaleOrderExpectTradeType">
            update td_orderbill_core
            set order_sale_type=#{orderSaleType}
            where profile_id=#{profileId}
        <if test="idList!=null and idList.size>0">
            And vchcode IN
            <foreach collection="idList" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
    </update>
    <update id="updateAdvanceSaleOrderDetailTradeType">
        update td_orderbill_detail_platform
        set order_sale_type=#{orderSaleType}
        where profile_id=#{profileId}
        <if test="advanceOrderIdList!=null and advanceOrderIdList.size>0">
            And vchcode IN
            <foreach collection="advanceOrderIdList" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
    </update>

    <update id="updateSaleOrderExpectTradeType">
        update pl_eshop_sale_order
        set order_sale_type=#{orderSaleType}
        where profile_id=#{profileId}
        <if test="idList!=null and idList.size>0">
            And id IN
            <foreach collection="idList" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
    </update>

    <update id="updateSaleOrderDetailTradeType">
        update pl_eshop_sale_order_detail
        set trade_type=#{tradeType}
        where profile_id=#{profileId}
        <if test="advanceOrderIdList!=null and advanceOrderIdList.size>0">
            And advance_order_id IN
            <foreach collection="advanceOrderIdList" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
    </update>

    <select id="queryAdvanceOrderDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderDetail">
        select 0 AS selected, d.*,od.btype_id,od.ktype_id,
        bp.fullname as ptypeName,bp.ptype_type,bp.ptype_area,bp.shortname as ptypeShortName,bp.usercode as ptypeCode,
        bp.ptype_width,bp.ptype_length,bp.ptype_height,bp.weight as ptypeWeight,bp.batchenabled,bp.snenabled,
        bp.length_unit,bp.weight_unit,bp.standard as ptypeStandard,bp.memo as ptypeMemo,
        bpx.xcode as xcode,bpf.fullbarcode as barcode,
        bps.prop_name1,bps.propvalue_name1,bps.prop_id1,bps.propvalue_id1,
        bps.prop_name2,bps.propvalue_name2,bps.prop_id2,bps.propvalue_id2,
        bps.prop_name3,bps.propvalue_name3,bps.prop_id3,bps.propvalue_id3,
        bps.prop_name4,bps.propvalue_name4,bps.prop_id4,bps.propvalue_id4,
        bps.prop_name5,bps.propvalue_name5,bps.prop_id5,bps.propvalue_id5,
        bps.prop_name6,bps.propvalue_name6,bps.prop_id6,bps.propvalue_id6,
        unit.unit_name as unitName,unit.unit_rate as unitRate,
        bsunit.unit_name as baseUnitName,
        (CASE WHEN bps.pic_url='' THEN pic.pic_url ELSE bps.pic_url END) pic_url,o.ocategory
        from ${tableName}
        left join td_orderbill_detail_assinfo da on da.profile_id=d.profile_id and da.detail_id = d.detail_id
        left join td_orderbill_core core on core.profile_id=d.profile_id and core.vchcode = d.vchcode
        left join td_orderbill_platform cp on cp.profile_id=d.profile_id and cp.vchcode = d.vchcode
        left join base_otype o on o.profile_id=d.profile_id and o.id=cp.otype_id
        left join pl_eshop_sale_order od on od.profile_id=d.profile_id and od.id=core.eshop_order_id
        left join base_ptype bp on bp.profile_id=d.profile_id and d.ptype_id=bp.id
        left join base_ptype_xcode bpx on bpx.profile_id=d.profile_id and bpx.sku_id=d.sku_id and bpx.unit_id=da.unit_id and  bpx.ptype_id=d.ptype_id and
        bpx.defaulted=1
        left join base_ptype_pic pic
        on pic.profile_id = d.profile_id and d.ptype_id = pic.ptype_id and pic.rowindex = 1
        left join base_ptype_sku bps on bps.profile_id=d.profile_id and bps.id=d.sku_id
        left join base_ptype_fullbarcode bpf on bpf.profile_id=d.profile_id and bpf.ptype_id=d.ptype_id and
        bpf.sku_id=d.sku_id and bpf.unit_id=da.unit_id and bpf.defaulted=1
        left join base_ptype_unit unit on unit.profile_id=d.profile_id and unit.id=da.unit_id
        left join base_ptype_unit bsunit on bsunit.profile_id=d.profile_id and bsunit.ptype_id=d.ptype_id and
        bsunit.unit_code=1
        where d.profile_id=#{profileId}
        <if test="advanceOrderId!=null">
            And d.vchcode=#{advanceOrderId}
        </if>
        <if test="advanceOrderIds!=null and advanceOrderIds.size>0">
            And d.vchcode IN
            <foreach collection="advanceOrderIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@NotMapping">
            AND cp.mapping_state=0
        </if>
        <if test="mappingState==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingState@Mapping">
            AND cp.mapping_state=1
        </if>
        <if test="waitAudit==true">
            AND cp.mapping_state=1 AND cp.local_refund_state!=4  and cp.deliver_required=1 and d.ptype_id>0
        </if>
        <if test="waitSendAudit==true">
            AND cp.mapping_state=1  and cp.deliver_required=1
        </if>
        ORDER BY d.combo_detail_id ASC
    </select>

    <select id="queryComboRow" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderDetailCombo">
        select pl.*,
               ptype.fullname                         as ptypeName,
               ptype.id                               as ptypeId,
               ptype.shortname                        as ptypeShortName,
               ptype.usercode                         as xcode,
               ptype.barcode,
               ptype.weight_unit,
               ptype.weight                           as ptypeWeight,
               ptype.memo                             as ptypeMemo,
               if(o.create_type = 1, '', pic.pic_url) as picUrl,
                distribution.profile_id AS 'distribution.profile_id',
                distribution.vchcode AS 'distribution.vchcode',
                distribution.combo_row_id AS 'distribution.combo_row_id',
                distribution.trade_order_detail_id AS 'distribution.trade_order_detail_id',
                distribution.combo_id AS 'distribution.combo_id',
                distribution.buyer_price AS 'distribution.buyer_price',
                distribution.buyer_total AS 'distribution.buyer_total',
                distribution.buyer_discount AS 'distribution.buyer_discount',
                distribution.buyer_dised_initial_price AS 'distribution.buyer_dised_initial_price',
                distribution.buyer_dised_initial_total AS 'distribution.buyer_dised_initial_total',
                distribution.buyer_ptype_preferential_total AS 'distribution.buyer_ptype_preferential_total',
                distribution.buyer_order_preferential_allot_total AS 'distribution.buyer_order_preferential_allot_total',
                distribution.buyer_dised_taxed_total AS 'distribution.buyer_dised_taxed_total',
                distribution.buyer_dised_taxed_price AS 'distribution.buyer_dised_taxed_price',
                distribution.buyer_tax_rate AS 'distribution.buyer_tax_rate',
                distribution.buyer_tax_total AS 'distribution.buyer_tax_total',
                distribution.buyer_dised_price AS 'distribution.buyer_dised_price',
                distribution.update_time AS 'distribution.update_time',
                distribution.create_time AS 'distribution.create_time',
                distribution.distribution_buyer_trade_detail_id AS 'distribution.distribution_buyer_trade_detail_id',
                liveBroadcast.profile_id as `liveBroadcast.profileId`,
                liveBroadcast.eshop_id as `liveBroadcast.eshopId`,
                liveBroadcast.vchcode as `liveBroadcast.vchcode`,
                liveBroadcast.orderbill_detail_combo_row_id as `liveBroadcast.orderbillDetailComboRowId`,
                liveBroadcast.platform_anchor_name as `liveBroadcast.platformAnchorName`,
                liveBroadcast.platform_anchor_id as `liveBroadcast.platformAnchorId`,
                liveBroadcast.platform_anchor_name as `liveBroadcast.platformAnchorName`,
                liveBroadcast.platform_live_room_id as `liveBroadcast.platformLiveRoomId`,
                liveBroadcast.live_brodcast_session_id as `liveBroadcast.liveBrodcastSessionId`,
                liveBroadcast.create_time as `liveBroadcast.createTime`,
                liveBroadcast.update_time as `liveBroadcast.updateTime`
        from td_orderbill_detail_combo pl
                 left join td_orderbill_core o on o.profile_id = pl.profile_id and o.vchcode = pl.vchcode
                 left join td_orderbill_detail_combo_distribution_buyer distribution on distribution.profile_id = pl.profile_id and distribution.vchcode = pl.vchcode and distribution.combo_row_id= pl.id
                 left join td_orderbill_detail_combo_live_broadcast liveBroadcast on liveBroadcast.profile_id = pl.profile_id and liveBroadcast.vchcode = pl.vchcode and liveBroadcast.orderbill_detail_combo_row_id= pl.id
                 left join base_ptype ptype on ptype.profile_id = pl.profile_id and pl.combo_id = ptype.id
                 left join base_ptype_pic pic on pic.profile_id = pl.profile_id and pl.combo_id = pic.ptype_id and pic.rowindex = 1
        where pl.profile_id = #{profileId}
        <if test="eshopOrderId!=null">
            And pl.vchcode = #{eshopOrderId}
        </if>
        <if test="eshopOrderIds!=null and eshopOrderIds.size>0">
            And pl.vchcode IN
            <foreach collection="eshopOrderIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
    </select>

    <select id="getEtypeNameById" resultType="java.lang.String">
        select fullname
        from base_etype
        where profile_id = #{profileId}
          and id = #{etypeId}
        limit 1
    </select>

    <update id="updateAdvanceOrderInvoice">
        update td_orderbill_invoice_info
        set invoice_type=#{invoiceType},
            invoice_required=#{invoiceRequired},
            invoice_total=#{invoiceTotal},
            invoice_state=#{invoiceState},
            invoice_title=#{invoiceTitle},
            invoice_code=#{invoiceCode},
            invoice_special_info=#{invoiceSpecialInfo},
            invoice_remark=#{invoiceRemark},
            invoice_company=#{invoiceCompany},
            invoice_register_addr=#{invoiceRegisterAddr},
            invoice_register_phone=#{invoiceRegisterPhone},
            invoice_bank=#{invoiceBank},
            invoice_bank_account=#{invoiceBankAccount}
        where profile_id = #{profileId}
          and vchcode = #{advanceOrderId}
    </update>

    <update id="updateAdvanceOrder">
        update td_orderbill_core
        set create_etype_id=#{createEtypeId},
        etype_id=#{etypeId},
        buyer_id=#{buyerId},
        ktype_id=#{ktypeId},
        btype_id=#{btypeId},
        dtype_id=#{dtypeId},
        self_delivery_mode=#{selfDeliveryMode},
        create_type=#{createType},
        business_type=#{businessType},
        order_sale_type=#{orderSaleType},
        deliver_type=#{deliverType},
        submit_send_state=#{submitSendState},
        currency_order_preferential_allot_total=#{orderPreferentialAllotTotal},
        currency_bill_total=#{currencyBillTotal},
        otype_id=#{otypeId},
        supplier_id=#{supplierId},
        memo=#{memo},
        summary=#{summary},
        balance_btype_id=#{balanceBtypeId},
        commission_btype_id=#{commissionBtypeId}
        where profile_id=#{profileId} and vchcode=#{advanceOrderId}
    </update>

    <update id="updateAdvanceOrderPlatform">
        update td_orderbill_platform
        set  `trade_id`=#{tradeId},
        `salesman`=#{salesman},
        `pay_time_type`=#{payTimeType},
        `local_trade_status`=#{localTradeStatus},
        `local_refund_process_state`=#{localRefundProcessState},
        `local_refund_state`=#{localRefundState},
        `seller_flag`=#{sellerFlag},
        `platform_store_id`=#{platformStoreId},
        `platform_store_code`=#{platformStoreCode},
         `platform_stock_id`=#{platformStockId},
         `platform_stock_code`=#{platformStockCode},
         `trade_create_time`=#{tradeCreateTime},
         `trade_modified_time`=#{tradeModifiedTime},
         `trade_finish_time`=#{tradeFinishTime},
         `trade_pay_time`=#{tradePayTime},
         `modified_time`=#{modifiedTime},
         `local_freight_name`=#{localFreightName},
         `local_freight_code`=#{localFreightCode},
         `local_freight_bill_no`=#{localFreightBillNo},
         `platform_freight_name`=#{platformFreightName},
         `platform_freight_code`=#{platformFreightCode},
         `customer_expected_freight_name`=#{customerExpectedFreightName},
         `customer_expected_freight_code`=#{customerExpectedFreightCode},
         `ptype_preferential_total`=#{ptypePreferentialTotal},
         `ptype_commission_total`=#{ptypeCommissionTotal},
         `ptype_service_fee`=#{ptypeServiceFee},
         `order_buyer_freight_fee`=#{orderBuyerFreightFee},
         `trade_total`=#{tradeTotal},
         `buyer_trade_total`=#{buyerTradeTotal},
         `buyer_paid_total`=#{buyerPaidTotal},
         `buyer_unpaid_total`=#{buyerUnpaidTotal},
         `mapping_state`=#{mappingState},
         `seller_memo`=#{sellerMemo},
         `buyer_message`=#{buyerMessage},
         `memo`=#{memo},
         `pay_no`=#{payNo},
         `unique_mark`=#{uniqueMark},
         `platform_order_preferential_total`=#{platformOrderPreferentialTotal},
         `platform_ptype_preferential_total`=#{platformPtypePreferentialTotal},
         `receive_address_type`=#{receiveAddressType},
         `receive_address_id`=#{receiveAddressId},
         `distribution_dised_taxed_total`=#{distributionDisedTaxedTotal},
         `distribution_tax_total`=#{distributionTaxTotal},
         `platform_trade_state`=#{platformTradeState},
         `local_trade_state`=#{localTradeState},
         `order_deliver_required`=#{orderDeliverRequired},
         `platform_distributor_name`=#{platformDistributorName},
         `mode_of_payment`=#{modeOfPayment},
         `merchant_payment_account`=#{merchantPaymentAccount},
         `payway_id`=#{paywayId},
         `Installation_service_provider`=#{installation_service_provider},
         `collect_customer`=#{collectCustomer},
         `export_count`=#{exportCount},
         `collect_time`=#{collectTime},
         `gather_status`=#{gatherStatus},
         `payment_mode`=#{paymentMode},
         `platform_business_mark`=#{platformBusinessMark},
         `hold_time`=#{holdTime},
         `submit_batch_id`=#{submitBatchId},
         `group_header_name`=#{groupHeaderName},
         `purchase_total`=#{purchaseTotal}
        where profile_id=#{profileId} and vchcode=#{advanceOrderId}
    </update>

    <update id="updateAdvanceOrderExtend">
        update pl_eshop_sale_order_advance_extend
        set  `order_number`=#{orderNumber},
        `otype_id`=#{otypeId},
        `deliver_send_state`=#{deliverSendState},
        `installation_service_provider`=#{installationServiceProvider},
        `collect_time`=#{collectTime},
        `collect_customer`=#{collectCustomer},
        `payment_mode`=#{paymentMode},
        `hold_time`=#{holdTime},
        `platform_required`=#{platformRequired},
        `platform_json`=#{platformJson},
         `purchase_total`=#{purchaseTotal}
        where profile_id=#{profileId} and advance_order_id=#{advanceOrderId}
    </update>

    <update id="updateAdvanceOrderTiming">
        update td_orderbill_timing
        set `cn_service`=#{cnService},
        `send_time`=#{sendTime},
        `plan_send_time` =#{planSendTime},
        `plan_sign_time` =#{planSignTime},
        `system_timing`=#{systemTiming},
        `timing_type`=#{timingType},
         `promised_collect_time`=#{promisedCollectTime},
        `promised_sign_time`=#{promisedSignTime},
        `sign_time`=#{signTime},
        `promised_sign_startTime`=#{promisedSignStartTime}
        where profile_id=#{profileId} and vchcode=#{vchcode}
    </update>

    <update id="updateAdvanceOrderDetail">
        update td_orderbill_detail_core
        set
        `ktype_id`=#{ktypeId},
        `ptype_id`=#{ptypeId},
        `sku_id`=#{skuId},
        `unit`=#{unit},
        `qty`=#{qty},
        `sub_qty`=#{subQty},
        `tax_rate`=#{taxRate},
        `tax_total`=#{taxTotal},
        dised_taxed_price=#{disedTaxedPrice},
        dised_taxed_total=#{disedTaxedTotal},
        dised_price=#{disedPrice},
        dised_total=#{disedTotal},
        combo_detail_id=#{comboDetailId}
        where profile_id=#{profileId} and detail_id=#{detailId}
    </update>

    <update id="updateOrderComboRow">
        update td_orderbill_detail_combo
        set
        combo_id=#{comboId},
        trade_order_detail_id=#{tradeOrderDetailId},
        qty=#{qty},
        trade_price=#{tradePrice},
        trade_total=#{tradeTotal},
        currency_price=#{currencyPrice},
        currency_total=#{currencyTotal},
        discount=#{discount},
        currency_preferential_total=#{currencyPtypePreferentialTotal},
        currency_order_preferential_allot_total=#{currencyOrderPreferentialAllotTotal},
        dised_taxed_price=#{disedTaxedPrice},
        dised_taxed_total=#{disedTaxedTotal},
        tax_total=#{taxTotal},
        dised_price=#{disedPrice},
        `dised_total`     = #{disedTotal},
        `memo`     = #{memo},
        `ptype_commission_total`     = #{ptypeCommissionTotal},
        `ptype_service_fee`     = #{ptypeServiceFee},
        `deliver_required`     = #{deliverRequired},
        `seller_memo`     = #{sellerMemo},
        `buyer_message`     = #{buyerMessage},
        `platform_pic_url`     = #{platformPicUrl},
        `platform_ptype_preferential_total`     = #{platformPtypePreferentialTotal},
        `platform_order_preferential_total`     = #{platformOrderPreferentialTotal},
        `mall_deduction_rate` = #{mallDeductionRate},
        `mall_deduction_fee` = #{mallDeductionFee}
        where profile_id=#{profileId} and vchcode=#{advanceOrderId} and id = #{advanceOrderComboRowId}
    </update>



    <select id="querySimpleAdvanceOrder"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderEntity">
        select `core`.vchcode,`platform`.local_trade_state as 'platform.local_trade_state',
                `platform`.local_refund_state as 'platform.local_refund_state',
                `platform`.trade_id as 'platform.trade_id',
                `core`.otype_id ,
                `platform`.submit_batch_id as 'platform.submit_batch_id',
               `core`.deleted,`core`.submit_send_state,`core`.order_sale_type,
               `timing`.plan_send_time  AS 'timing.plan_send_time'
        from td_orderbill_core `core`
                 LEFT JOIN td_orderbill_timing timing ON timing.profile_id=`core`.profile_id AND timing.vchcode=`core`.vchcode
                 LEFT JOIN td_orderbill_platform platform ON core.profile_id=`platform`.profile_id AND platform.vchcode=`core`.vchcode
        where `core`.profile_id=#{profileId}
          AND `core`.vchcode = #{advanceOrderId}
          limit 1
    </select>
    <select id="querySimpleAdvanceDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderDetail">
        select d.*,
                dp.stock_sync_rule_id as 'platform.stock_sync_rule_id',
                dp.trade_order_detail_id as  'platform.trade_order_detail_id',
                da.unit_qty as  'assinfo.unit_qty',
                dp.process_state as 'platform.process_state',
                dp.platform_detail_trade_state as 'platform.platform_detail_trade_state',
                dp.deliver_required as 'platform.deliver_required'
        from td_orderbill_detail_core d
        left join td_orderbill_detail_platform dp on dp.profile_id=d.profile_id and dp.detail_id=d.detail_id
        left join td_orderbill_detail_assinfo da on da.profile_id=d.profile_id and da.detail_id=d.detail_id
        left join td_orderbill_core core  on core.profile_id=d.profile_id and core.vchcode=d.vchcode
        where d.profile_id=#{profileId}
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            and d.vchcode IN
            <foreach collection="eshopOrderIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="eshopOrderId!=null">
            and d.vchcode =#{eshopOrderId}
        </if>
        <if test="saleOrderId!=null and saleOrderId !=0">
            and core.eshop_order_id =#{saleOrderId}
        </if>
    </select>
    <select id="querySimpleAdvanceOrderList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderEntity">
        select core.vchcode,platform.local_trade_state as 'platform.local_trade_state',platform.local_refund_state as 'platform.local_refund_state',
               core.deleted,core.submit_send_state,core.order_sale_type,core.eshop_order_id
        from td_orderbill_core core
        LEFT JOIN td_orderbill_platform platform ON core.profile_id=`platform`.profile_id AND platform.vchcode=`core`.vchcode
        where core.profile_id=#{profileId}
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            and core.vchcode IN
            <foreach collection="eshopOrderIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
    </select>

    <update id="updateAdvancePaidTotal">
        update td_orderbill_platform
        set `buyer_trade_total`=#{buyerTradeTotal},
        `buyer_unpaid_total`= #{buyerUnpaidTotal},
        `buyer_paid_total`= #{buyerPaidTotal},
        `local_trade_state` = #{localTradeState},
        `trade_pay_time` = #{tradePayTime}
        where profile_id=#{profileId} and vchcode=#{id}
    </update>

    <update id="updateSaleOrderTradeStatus">
        update td_orderbill_platform
        set `buyer_trade_total`=#{buyerTradeTotal},
            `buyer_paid_total`= #{buyerPaidTotal},
            `buyer_unpaid_total`= #{buyerUnpaidTotal},
        `local_trade_state` = #{localTradeState},
        `trade_pay_time` = #{tradePayTime}
        where profile_id=#{profileId} and id=#{id}
    </update>

    <update id="updateAdvanceDetailTradeStatus">
        update pl_eshop_sale_order_advance_detail
        set `platform_detail_trade_state` = #{tradeState}
        where profile_id=#{profileId} and advance_order_id=#{advanceOrderId}
    </update>

    <update id="updateSaleOrderDetailTradeStatus">
        update pl_eshop_sale_order_detail
        set `platform_detail_trade_state` = #{tradeState}
        where profile_id=#{profileId} and eshop_order_id=#{eshopOrderId}
    </update>



    <delete id="deleteAdvanceSaleOrder">
       delete
       from td_orderbill_core
       where profile_id=#{profileId}
       and  vchcode = #{id}
    </delete>

    <delete id="deleteAdvanceSaleOrderPlatform">
       delete
       from td_orderbill_platform
       where profile_id=#{profileId}
       and  vchcode = #{advanceOrderId}
    </delete>

    <delete id="deleteAdvanceSaleOrderPlatformExtend">
        delete
        from td_orderbill_platform_extend
        where profile_id=#{profileId}
          and  vchcode = #{advanceOrderId}
    </delete>

    <delete id="deleteAdvanceSaleOrderInvoice">
       delete
       from td_orderbill_invoice_info
       where profile_id=#{profileId}
       and  vchcode = #{advanceOrderId}
    </delete>

        <delete id="deleteAdvanceSaleOrderAssinfo">
       delete
       from td_orderbill_assinfo
       where profile_id=#{profileId}
       and  vchcode = #{advanceOrderId}
    </delete>

    <delete id="deleteAdvanceSaleOrderDistributionBuyer">
        delete
        from td_orderbill_distribution_buyer
        where profile_id=#{profileId}
          and  vchcode = #{advanceOrderId}
    </delete>

    <delete id="deleteAdvanceSaleOrderFreight">
        delete
        from td_orderbill_freight
        where profile_id=#{profileId}
          and  vchcode = #{advanceOrderId}
    </delete>

    <delete id="deleteAdvanceSaleOrderTiming">
        delete
        from td_orderbill_timing
        where profile_id=#{profileId}
          and  vchcode = #{advanceOrderId}
    </delete>

    <delete id="deleteAdvanceComboRow">
        delete
        from td_orderbill_detail_combo
        where profile_id = #{profileId}
          and vchcode = #{advanceOrderId}
    </delete>

        <delete id="deleteAdvanceComboRowDistribution">
        delete
        from td_orderbill_detail_combo_distribution_buyer
        where profile_id = #{profileId}
          and vchcode = #{advanceOrderId}
    </delete>


    <delete id="deleteAdvanceDetails">
        delete
        from td_orderbill_detail_core
        where profile_id = #{profileId}
           and vchcode = #{advanceOrderId}
    </delete>

    <delete id="deleteAdvanceDetailPlatform">
        delete
        from td_orderbill_detail_platform
        where profile_id = #{profileId}
           and vchcode = #{advanceOrderId}
    </delete>

        <delete id="deleteAdvanceDetailAssinfo">
        delete
        from td_orderbill_detail_assinfo
        where profile_id = #{profileId}
           and vchcode = #{advanceOrderId}
    </delete>

    <delete id="deleteAdvanceDetailDistribution">
        delete
        from td_orderbill_detail_distribution_buyer
        where profile_id = #{profileId}
        and vchcode = #{advanceOrderId}
    </delete>
    <delete id="deleteAdvanceSaleOrderDetails">
        delete from td_orderbill_detail_core
        where profile_id=#{profileId}
        and vchcode =#{advanceOrderId}
        <if test="detailIds!=null and detailIds.size()>0">
            AND id IN
            <foreach collection="detailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <select id="querySimpleAdvanceOrderByVchcode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderEntity">
        select core.vchcode,platform.local_trade_state as 'platform.local_trade_state',
        platform.local_refund_state as 'platform.local_refund_state',
        core.deleted,
        platform.process_state as 'platform.process_state',
        platform.order_sale_type as 'platform.order_sale_type',
        platform.seller_flag as 'platform.seller_flag',
               core.otype_id as 'otype_id',
               platform.trade_id as 'platform.trade_id',
               platform.local_trade_state  as 'platform.local_trade_state'
        from td_orderbill_core core
            left join td_orderbill_platform platform on core.profile_id=platform.profile_id and core.vchcode=platform.vchcode
        where  core.profile_id = #{profileId}
          AND core.vchcode = #{id}
          limit 1
    </select>

    <update id="updateOrderFlag">
        update td_orderbill_platform
        set seller_flag=#{flagId}
        WHERE profile_id = #{profileId}
          and vchcode = #{id}
    </update>

    <insert id="insertChangeInfo">
        insert into pl_eshop_sale_order_change_info
        (id, profile_id, trade_order_id, change_type, content, modify_time, eshop_order_id, otype_id,trade_order_detail_id,sub_change_type,producer,customer,unique_key)
        values (#{id}, #{profileId}, #{tradeOrderId}, #{changeType}, #{content}, #{modifyTime}, #{eshopOrderId}, #{otypeId}, #{oid}
        , #{subChangeType}, #{producer}, #{customer}, #{uniqueKey})
    </insert>

    <select id="querySimpleEshopAdvanceOrderList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderEntity">
        SELECT `core`.vchcode,
        `core`.order_sale_type,
        `core`.deleted,
        `core`.submit_send_state as 'submit_send_state',
        `core`.buyer_id,
        `core`.profile_id,
        `core`.otype_id as 'otype_id',
        `core`.platform_parent_order_id as 'platform_parent_order_id',
        `platform`.local_trade_state as 'platform.local_trade_state',
        `platform`.local_refund_state as 'platform.local_refund_state',
        `platform`.profile_id as 'platform.profile_id',
        `platform`.trade_id as 'platform.trade_id',
        `platform`.seller_memo as 'platform.seller_memo',
        `platform`.seller_flag as 'platform.seller_flag',
        `platform`.submit_batch_id AS 'platform.submit_batch_id',
         `platform`.local_freight_name as 'platform.local_freight_name',
        `platform`.local_freight_code as 'platform.local_freight_code',
        `platform`.local_freight_bill_no AS 'platform.local_freight_bill_no',
        `platform`.buyer_message AS 'platform.buyer_message',
        `timing`.vchcode AS 'timing.vchcode',
        `timing`.plan_send_time AS 'timing.plan_send_time',
        `timing`.send_time AS 'timing.send_time',
        `timing`.promised_collect_time AS 'timing.promised_collect_time',
        `timing`.promised_sign_time AS 'timing.promised_sign_time',
        `timing`.plan_sign_time AS 'timing.plan_sign_time',
        `timing`.promised_sign_startTime AS 'timing.promised_sign_startTime',
        `assinfo`.currency_order_buyer_freight_fee AS 'assinfo.currency_order_buyer_freight_fee',
          `invoice`.invoice_title as 'invoice.invoice_title',
        `invoice`.invoice_state AS 'invoice.invoice_state',
         `invoice`.invoice_code as 'invoice.invoice_code',
        `invoice`.invoice_category as 'invoice.invoice_category',
        `invoice`.invoice_type AS 'invoice.invoice_type',
        `invoice`.invoice_remark AS 'invoice.invoice_remark',
        `invoice`.invoice_required AS 'invoice.invoice_required',
        `platformExtend`.vchcode as 'platform.platformExtend.vchcode',
        `platformExtend`.profile_id as 'platform.platformExtend.profile_id',
        `platformExtend`.pickup_code as 'platform.platformExtend.pickup_code',
        `platformExtend`.platform_dispatcher_name as 'platform.platformExtend.platform_dispatcher_name',
        `platformExtend`.platform_dispather_mobile as 'platform.platformExtend.platform_dispather_mobile',
        `platformExtend`.logistics_status as 'platform.platformExtend.logistics_status',
        `platformExtend`.confirm_status as 'platform.platformExtend.confirm_status',
        `platformExtend`.real_buyer_id as 'platform.platformExtend.real_buyer_id',
        `platformExtend`.pick_up_address_id as 'platform.platformExtend.pick_up_address_id',
        `platformExtend`.seller_flag_memo as 'platform.platformExtend.seller_flag_memo',
        `buyer`.buyer_id AS 'eshopBuyer.buyer_id',
        `buyer`.customer_receiver AS 'eshopBuyer.customer_receiver',
        `buyer`.customer_id_card AS 'eshopBuyer.customer_id_card',
        `buyer`.customer_id_card_name AS 'eshopBuyer.customer_id_card_name',
        `buyer`.customer_receiver_phone AS 'eshopBuyer.customer_receiver_phone',
        `buyer`.customer_receiver_mobile AS 'eshopBuyer.customer_receiver_mobile',
        `buyer`.customer_receiver_zip_code AS 'eshopBuyer.customer_receiver_zip_code',
        `buyer`.customer_email AS 'eshopBuyer.customer_email',
        `buyer`.customer_receiver_country AS 'eshopBuyer.customer_receiver_country',
        `buyer`.customer_receiver_province AS 'eshopBuyer.customer_receiver_province',
        `buyer`.customer_receiver_city AS 'eshopBuyer.customer_receiver_city',
        `buyer`.customer_receiver_district AS 'eshopBuyer.customer_receiver_district',
        `buyer`.customer_receiver_address AS 'eshopBuyer.customer_receiver_address',
        `buyer`.customer_receiver_full_address AS 'eshopBuyer.customer_receiver_full_address',
        `buyer`.customer_shop_account AS 'eshopBuyer.customer_shop_account',
        `buyer`.customer_receiver_town AS 'eshopBuyer.customer_receiver_town',
        `buyer`.di AS 'eshopBuyer.di',
        `buyer`.ai AS 'eshopBuyer.ai',
        `buyer`.ri AS 'eshopBuyer.ri',
        `buyer`.mi AS 'eshopBuyer.mi',
        `buyer`.addri AS 'eshopBuyer.addri',
        `buyer`.profile_id AS 'eshopBuyer.profileId',
        `buyer`.otype_id AS 'eshopBuyer.otypeId',
        `buyer`.open_receiver_id AS 'eshopBuyer.openReceiverId',
        `buyer`.open_address_id AS 'eshopBuyer.openAddressId',
        `buyer`.open_address_id_2 AS 'eshopBuyer.openAddressId2',
        `eshop`.eshop_type AS shopType
        FROM td_orderbill_core `core`
        LEFT JOIN td_orderbill_timing timing ON timing.profile_id=`core`.profile_id AND timing.vchcode=`core`.vchcode
        LEFT JOIN td_orderbill_platform platform ON platform.profile_id=`core`.profile_id AND platform.vchcode=`core`.vchcode
        LEFT JOIN td_orderbill_assinfo assinfo ON assinfo.profile_id=`core`.profile_id AND assinfo.vchcode=`core`.vchcode
        LEFT JOIN td_orderbill_invoice_info invoice ON invoice.profile_id=`core`.profile_id AND invoice.vchcode=`core`.vchcode
        LEFT JOIN td_orderbill_platform_extend platformExtend ON platformExtend.profile_id=`core`.profile_id AND platformExtend.vchcode=`core`.vchcode
        LEFT JOIN pl_buyer buyer ON `buyer`.buyer_id = `core`.buyer_id AND `buyer`.profile_id = `core`.profile_id
        LEFT JOIN pl_eshop eshop ON `eshop`.otype_id = `core`.otype_id AND `eshop`.profile_id = `core`.profile_id
        WHERE `core`.profile_id=#{profileId}

        <if test="eshopOrderId != null and eshopOrderId != ''">
            AND `core`.vchcode=#{eshopOrderId}
        </if>

        <if test="saleOrderId != '' and saleOrderId != null">
            AND `core`.eshop_order_id = #{saleOrderId}
        </if>

        <if test="submitBatchId != null">
            and `platform`.submit_batch_id=#{submitBatchId}
        </if>
        <if test=" tradeOrderId != null and tradeOrderId != '' ">
            AND `platform`.trade_id=#{tradeOrderId}
        </if>
        <if test="otypeIds !=null and otypeIds.size()>0">
            and `platform`.otype_id IN
            <foreach collection="otypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
    </select>
    <select id="querySimpleAdvanceDetailBySaleOrderId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderDetail">
        select * from td_orderbill_detail_core
        where profile_id = #{profileId}
          and vchcode = #{advanceOrderId}
    </select>
    <select id="queryUnRelationAdvanceDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderDetail">
        select d.*,
        from td_orderbill_detail_core d
        left join td_orderbill_detail_platform platform on d.profile_id=platform.profile_id and d.detail_id=platform.detail_id
        left join td_orderbill_platform o on d.profile_id=o.profile_id and d.vchcode = o.vchcode
        where d.profile_id=#{profileId} and platform.mapping_state=0
        <if test="eshopId!=null">
            and o.otype_id=#{eshopId}
        </if>
        <if test="platformNumId!=null and platformNumId.size() > 0 ">
            and platform.platform_ptype_id in
            <foreach collection="platformNumId" close=")" open="(" separator="," item="num_id">
                #{num_id}
            </foreach>
        </if>
        <if test="platformProperties!=null and platformProperties.size() > 0 ">
            and platform.platform_properties_name in
            <foreach collection="platformProperties" close=")" open="(" separator="," item="prop">
                #{prop}
            </foreach>
        </if>
        <if test="platformSkuInfo!=null and platformSkuInfo!=''">
            and (platform.platform_product_name like CONCAT('%',#{platformSkuInfo},'%')
            or platform.platform_xcode like CONCAT('%',#{platformSkuInfo},'%'))
        </if>
        <if test="detailIds!=null and detailIds.size()>0">
            and d.detail_id in
            <foreach collection="detailIds" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="advanceOrderIds!=null and advanceOrderIds.size()>0">
            and d.vchcode in
            <foreach collection="advanceOrderIds" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="querySimpleOrder"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderEntity">
        select od.* from td_orderbill_core od
        left join td_orderbill_platform on p.profile_id = od.profile_id and p.vchcode and od.vchcode
        where od.profile_id = #{profileId}
        <if test="advanceOrderId!=null and advanceOrderId!=0">
            and od.vchcode=#{advanceOrderId}
        </if>
        <if test="tradeOrderId!=null and tradeOrderId!=''">
            and p.trade_id=#{tradeOrderId}
        </if>
        <if test="otypeIds!=null and otypeIds.size() > 0">
            AND `p`.otype_id IN
            <foreach collection="otypeIds" close=")" open="(" separator="," item="otypeId">
                #{otypeId}
            </foreach>
        </if>
    </select>

    <update id="updateAdvanceOrderMentionType">
--         update pl_eshop_sale_order_advance
--         set mention_type=#{mentionType}
--         where profile_id=#{profileId} and vchcode=#{vchcode}
    </update>

    <update id="updateAdvanceOrderDetailMentionType">
--         update pl_eshop_sale_order_advance_detail
--         set
--             mention_type=#{mentionType}
--         where profile_id=#{profileId} and id=#{detailId}
    </update>

    <update id="updateAdvanceOrderDetailNotify">
        update td_orderbill_detail_platform
        set

            local_refund_process_state=#{localRefundProcessState},
            local_refund_state=#{localRefundState}
        where profile_id=#{profileId} and vchcode=#{detailId}
    </update>


    <update id="updateAdvanceOrderSpecify">
        update td_orderbill_core
        set
            buyer_id=#{buyerId}
        where profile_id=#{profileId} and vchcode=#{vchcode}
    </update>

    <update id="updateAdvanceOrderPlatformSpecify">
            update td_orderbill_platform
            set
            buyer_message=#{buyerMessage},
            seller_flag=#{sellerFlag},
            seller_memo=#{sellerMemo},
            local_trade_state=#{localTradeState},
            trade_pay_time=#{tradePayTime},
            trade_finish_time=#{tradeFinishTime},
            local_refund_state=#{localRefundState}
        where profile_id=#{profileId} and vchcode=#{vchcode}
    </update>

    <update id="updateAdvanceOrderPlatformExtendSpecify">
        update td_orderbill_platform_extend
        set
            pickup_code=#{pickupCode},
            platform_dispatcher_name=#{platformDispatcherName},
            platform_dispather_mobile=#{platformDispatherMobile},
            logistics_status=#{logisticsStatus},
            confirm_status=#{confirmStatus},
            real_buyer_id=#{realBuyerId},
            pick_up_address_id=#{pickUpAddressId},
        platform_qc_result=#{platformQcResult},
            platform_identify_result=#{platformIdentifyResult},
            flow_channel=#{flowChannel},
            mall_deduction_fee = #{mallDeductionFee},
        seller_flag_memo=#{sellerFlagMemo},
            group_title=#{groupTitle},
            platform_quality_org_id=#{platformQualityOrgId},
            platform_quality_org_name=#{platformQualityOrgName},
            platform_quality_warehouse_code=#{platformQualityWarehouseCode},
            platform_quality_warehouse_name=#{platformQualityWarehouseName},
            anchor_order_preferential_total=#{anchorOrderPreferentialTotal},
            platform_order_subsidy_total=#{platformOrderSubsidyTotal},
            national_subsidy_total=#{nationalSubsidyTotal}
        where profile_id=#{profileId} and vchcode=#{vchcode}
    </update>

        <update id="updateAdvanceOrderAssinfoSpecify">
         update td_orderbill_assinfo
            set
            currency_order_buyer_freight_fee=#{currencyOrderBuyerFreightFee}
        where profile_id=#{profileId} and vchcode=#{vchcode}
        </update>

    <update id="updateAdvanceOrderInvoiceSpecify">
        update td_orderbill_invoice_info
        set
            invoice_state=#{invoiceState},
            invoice_code=#{invoiceCode},
            invoice_title=#{invoiceTitle},
            invoice_type=#{invoiceType},
            invoice_remark=#{invoiceRemark},

            invoice_required=#{invoiceRequired},
            invoice_total=#{invoiceTotal},
            invoice_category=#{invoiceCategory},
            invoice_special_info=#{invoiceSpecialInfo},
            invoice_company=#{invoiceCompany},
            invoice_register_addr=#{invoiceRegisterAddr},
            invoice_register_phone=#{invoiceRegisterPhone},
            invoice_bank=#{invoiceBank},
            invoice_bank_account=#{invoiceBankAccount},

            invoice_required=#{invoiceRequired}
        where profile_id = #{profileId} and vchcode = #{vchcode}
    </update>
    <update id="updateAdvanceOrderDetailTradeType">
        update td_orderbill_detail_platform
        set
            order_sale_type=#{orderSaleType}
        where profile_id=#{profileId} and vchcode=#{detailId}
    </update>

    <update id="updateAdvanceDetailinfo">
        update td_orderbill_detail_platform
        set
        local_refund_process_state=#{platform.localRefundProcessState},
        platform_detail_trade_state=#{platform.platformDetailTradeState},
        local_refund_state=#{platform.localRefundState},
        platform_qc_result=#{platform.platformQcResult},
        platform_qc_result_desc=#{platform.platformQcResultDesc},
        platform_identify_result=#{platform.platformIdentifyResult},
        platform_identify_result_desc=#{platform.platformIdentifyResultDesc},
        verify_code=#{platform.verifyCode},
        flow_channel=#{platform.flowChannel},
        mall_deduction_rate=#{platform.mallDeductionRate},
        mall_deduction_fee=#{platform.mallDeductionFee},
        anchor_ptype_preferential_total=#{platform.anchorPtypePreferentialTotal},
        anchor_order_preferential_total=#{platform.anchorOrderPreferentialTotal},
        platform_order_subsidy_total=#{platform.platformOrderSubsidyTotal},
        platform_ptype_subsidy_total=#{platform.platformPtypeSubsidyTotal},
        national_subsidy_total=#{platform.nationalSubsidyTotal}
        where profile_id=#{profileId} and vchcode=#{vchcode} and detail_id=#{detailId}
    </update>

    <update id="updateAdvanceOrderDetailPlatformTradeState">
        update td_orderbill_detail_platform
        set
            platform_detail_trade_state=#{platformDetailTradeState}
        where profile_id=#{profileId} and vchcode=#{vchcode} and detail_id=#{detailId}
    </update>

    <update id="updateAdvanceOrderPlatformTradeState">
        update td_orderbill_platform
        set
            local_trade_state=#{localTradeState},
            trade_pay_time=#{tradePayTime},
            trade_finish_time=#{tradeFinishTime}
        where profile_id=#{profileId} and vchcode=#{vchcode}
    </update>

    <insert id="insertAdvanceDetailDistribution">
        insert into td_orderbill_detail_distribution_buyer
        (profile_id,vchcode,detail_id,eshop_id,buyer_price,buyer_total,buyer_discount,buyer_dised_initial_price,buyer_dised_initial_total,
         buyer_ptype_preferential_total,buyer_order_preferential_allot_total,buyer_dised_taxed_price,buyer_dised_taxed_total,buyer_tax_rate,
         buyer_tax_total,buyer_dised_price,buyer_dised_total,distribution_buyer_trade_detail_id)
        values (#{profileId}, #{vchcode}, #{detailId}, #{eshopId}, #{buyerPrice}, #{buyerTotal}, #{buyerDiscount},
                #{buyerDisedInitialPrice}, #{buyerDisedInitialTotal}, #{buyerPtypePreferentialTotal}, #{buyerOrderPreferentialAllotTotal},
                #{buyerDisedTaxedPrice}, #{buyerDisedTaxedTotal}, #{buyerTaxRate}, #{buyerTaxTotal}, #{buyerDisedPrice},
                #{buyerDisedTotal},#{distributionBuyerTradeDetailId})
    </insert>

      <insert id="insertAdvanceDetailPurchase">
        INSERT INTO td_orderbill_detail__purchase (`vchcode`, `profile_id`, `detail_id`, `purchase_price`, `purchase_total`,
                                                         `purchase_tax_rate`, `purchase_tax_total`, `purchase_dised_price`,
                                                         `purchase_dised_total`, `purchase_discount`, `purchase_dised_taxed_price`,
                                                         `purchase_dised_taxed_total`, `create_time`,
                                                         `update_time`)
            value (#{vchcode}, #{profileId}, #{detailId}, #{purchasePrice}, #{purchaseTotal}, #{purchaseTaxRate}, #{purchaseTaxTotal},
            #{purchaseDisedPrice}, #{purchaseDisedTotal}, #{purchaseDiscount}, #{purchaseDisedTaxedPrice},
            #{purchaseDisedTaxedTotal}, #{createTime},  #{updateTime})
        ON DUPLICATE KEY
        UPDATE `detail_id`=#{detailId},
            `profile_id`=#{profileId}
    </insert>

    <insert id="insertAdvanceComboDetailDistribution">
        insert into td_orderbill_detail_combo_distribution_buyer
        (`profile_id`, `combo_row_id`,`vchcode`,`combo_id`,`trade_order_detail_id`,buyer_price,buyer_total,buyer_discount,buyer_dised_initial_price,buyer_dised_initial_total,
         buyer_ptype_preferential_total,buyer_order_preferential_allot_total,buyer_dised_taxed_price,buyer_dised_taxed_total,buyer_tax_rate,
         buyer_tax_total,buyer_dised_price,buyer_dised_total,distribution_buyer_trade_detail_id)
        values (#{profileId},#{comboRowId}, #{vchcode},  #{comboId},  #{tradeOrderDetailId}, #{buyerPrice}, #{buyerTotal}, #{buyerDiscount},
                #{buyerDisedInitialPrice}, #{buyerDisedInitialTotal}, #{buyerPtypePreferentialTotal}, #{buyerOrderPreferentialAllotTotal},
                #{buyerDisedTaxedPrice}, #{buyerDisedTaxedTotal}, #{buyerTaxRate}, #{buyerTaxTotal}, #{buyerDisedPrice},
                #{buyerDisedTotal},#{distributionBuyerTradeDetailId})
    </insert>

    <insert id="batchInsertAdvanceComboDetailDistribution">
        insert into td_orderbill_detail_combo_distribution_buyer
        (`profile_id`,`vchcode`,`combo_id`,`trade_order_detail_id`,buyer_price,buyer_total,buyer_discount,buyer_dised_initial_price,buyer_dised_initial_total,
        buyer_ptype_preferential_total,buyer_order_preferential_allot_total,buyer_dised_taxed_price,buyer_dised_taxed_total,buyer_tax_rate,
        buyer_tax_total,buyer_dised_price,buyer_dised_total)
        values
        <foreach collection="list" item="item" separator=",">
            (#{profileId}, #{vchcode},  #{comboId},  #{tradeOrderDetailId}, #{buyerPrice}, #{buyerTotal}, #{buyerDiscount},
            #{buyerDisedInitialPrice}, #{buyerDisedInitialTotal}, #{buyerPtypePreferentialTotal}, #{buyerOrderPreferentialAllotTotal},
            #{buyerDisedTaxedPrice}, #{buyerDisedTaxedTotal}, #{buyerTaxRate}, #{buyerTaxTotal}, #{buyerDisedPrice},
            #{buyerDisedTotal})
        </foreach>
    </insert>

    <insert id="insertAdvanceComboRowPurchase">
        INSERT INTO td_orderbill_detail_combo_purchase (
             profile_id, combo_id, trade_order_detail_id, vchcode, purchase_price, purchase_total, purchase_tax_rate, purchase_tax_total,
             purchase_dised_price,purchase_dised_total, purchase_discount, purchase_dised_taxed_price,
             purchase_dised_taxed_total, create_time,
             update_time, combo_row_id)
            value ( #{profileId}, #{comboId}, #{tradeOrderDetailId},#{vchcode}, #{purchasePrice}, #{purchaseTotal}, #{purchaseTaxRate}, #{purchaseTaxTotal},
            #{purchaseDisedPrice}, #{purchaseDisedTotal}, #{purchaseDiscount}, #{purchaseDisedTaxedPrice},
            #{purchaseDisedTaxedTotal}, #{createTime},  #{updateTime},#{comboRowId})
        ON DUPLICATE KEY
        UPDATE `combo_row_id`=#{comboRowId},
            `profile_id`=#{profileId}
    </insert>

    <insert id="batchInsertAdvanceComboRowPurchase">
        insert into td_orderbill_detail_combo_purchase
        (profile_id, combo_id, trade_order_detail_id, vchcode, purchase_price, purchase_total, purchase_tax_rate, purchase_tax_total, purchase_dised_price,
         purchase_dised_total, purchase_discount, purchase_dised_taxed_price,  purchase_dised_taxed_total, create_time,
         update_time, combo_row_id)
        values
            (#{item.profileId}, #{item.comboId}, #{item.tradeOrderDetailId}, #{item.vchcode},
             #{item.purchasePrice}, #{item.purchaseTotal},
             #{item.purchaseTaxRate}, #{item.purchaseTaxTotal}, #{item.purchaseDisedPrice}, #{item.purchaseDisedTotal}, #{item.purchaseDiscount},
             #{item.purchaseDisedTaxedPrice},
             #{item.purchaseDisedTaxedTotal}, #{item.createTime}, #{item.updateTime},
             #{item.comboRowId})
            ON DUPLICATE KEY
        UPDATE `combo_row_id`=#{comboRowId},
            `profile_id`=#{profileId}
    </insert>

    <delete id="deleteAdvanceDetailPurchase">
        delete from td_orderbill_detail__purchase
        where profile_id = #{profileId}
        <if test="advanceOrderId!=null and advanceOrderId!=0">
            and vchcode = #{advanceOrderId}
        </if>
    </delete>

    <delete id="deleteAdvanceDetailGiftRelation">
        delete from td_orderbill_detail_gift_relation
        where profile_id = #{profileId}
        <if test="advanceOrderId!=null and advanceOrderId!=0">
            and source_vchcode = #{advanceOrderId}
        </if>
    </delete>

    <delete id="deleteAdvanceComboRowPurchase">
        delete from td_orderbill_detail_combo_purchase
        where profile_id = #{profileId}
        <if test="advanceOrderId!=null and advanceOrderId!=0">
            and vchcode = #{advanceOrderId}
        </if>
    </delete>

    <insert id="insertAdvanceDetailLiveBroadcast">
        insert into td_orderbill_detail_live_broadcast(profile_id, eshop_id, vchcode, detail_id,
                                                     platform_anchor_id, platform_anchor_name, platform_live_room_id,
                                                     live_brodcast_session_id, create_time, update_time)
        VALUES (#{profileId}, #{eshopId}, #{vchcode}, #{detailId},
                #{platformAnchorId}, #{platformAnchorName}, #{platformLiveRoomId},
                #{liveBrodcastSessionId}, #{createTime}, #{updateTime})
            ON DUPLICATE KEY
        UPDATE `detail_id`=#{detailId},
            `profile_id`=#{profileId}
    </insert>

    <insert id="insertAdvanceComboDetailLiveBroadcast">
            insert into td_orderbill_detail_combo_live_broadcast(profile_id, eshop_id, vchcode,
                                                            orderbill_detail_combo_row_id, platform_anchor_id,
                                                            platform_anchor_name, platform_live_room_id,
                                                            live_brodcast_session_id, create_time, update_time)
        VALUES (#{profileId}, #{eshopId}, #{vchcode}, #{orderbillDetailComboRowId},
                #{platformAnchorId}, #{platformAnchorName}, #{platformLiveRoomId},
                #{liveBrodcastSessionId}, #{createTime}, #{updateTime})
            ON DUPLICATE KEY
        UPDATE `orderbill_detail_combo_row_id`=#{orderbillDetailComboRowId},
            `profile_id`=#{profileId}
    </insert>

    <insert id="batchInsertAdvanceDetailSerialno">
        insert into td_orderbill_detail_serialno(id, vchcode, detail_id, profile_id, bill_date, otype_id, btype_id, ktype_id,
                                         ptype_id, etype_id, inout_type, snno, sn1, sn2, sn_memo, batchno, create_time,
                                         update_time, produce_date, expire_date,batch_price) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.vchcode},  #{item.detailId},  #{item.profileId}, #{item.billDate}, #{item.otypeId}, #{item.btypeId}, #{item.ktypeId},
            #{item.ptypeId}, #{item.etypeId}, #{item.inoutType},#{item.snno}, #{item.sn1}, #{item.sn2}, #{item.snMemo}, #{item.batchno}, #{item.createTime},
            #{item.updateTime}, #{item.produceDate}, #{item.expireDate}, #{item.batchPrice})
        </foreach>
    </insert>

    <insert id="batchInsertAdvanceDetailGiftRelation">
        insert into td_orderbill_detail_gift_relation(id, profile_id, vchcode, detail_id, source_vchcode, source_detail_id, create_time, update_time) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.profileId},  #{item.vchcode},  #{item.detailId}, #{item.sourceVchcode}, #{item.sourceDetailId}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>

    <insert id="insertAdvanceDetailSerialno">
        insert into td_orderbill_detail_serialno(id, vchcode, detail_id, profile_id, bill_date, otype_id, btype_id, ktype_id,
                                                 ptype_id, etype_id, inout_type, snno, sn1, sn2, sn_memo, batchno, create_time,
                                                 update_time, produce_date, expire_date,batch_price)
        VALUES (#{id}, #{vchcode}, #{detailId}, #{profileId},#{billDate}, #{otypeId}, #{btypeId}, #{ktypeId},
                #{ptypeId}, #{etypeId}, #{inoutType}, #{snno},#{sn1}, #{sn2}, #{snMemo}, #{batchno},#{createTime},
                #{updateTime}, #{produceDate}, #{expireDate}, #{batchPrice})
        ON DUPLICATE KEY
            UPDATE `id`=#{id},
                   `profile_id`=#{profileId}
    </insert>

    <insert id="insertAdvanceDetailBatch">
        insert into td_orderbill_detail_batch(id, vchcode, detail_id, profile_id, batchno, produce_date, expire_date, qty,
                                              sub_qty, unit_qty, batch_price, create_time, update_time)
        VALUES (#{id}, #{vchcode}, #{detailId}, #{profileId},#{batchno}, #{produceDate}, #{expireDate}, #{qty},
                #{subQty}, #{unitQty}, #{batchPrice}, #{createTime},#{updateTime})
        ON DUPLICATE KEY
            UPDATE `id`=#{id},
                   `profile_id`=#{profileId}
    </insert>

    <insert id="insertAdvanceDetailTiming">
        insert into td_orderbill_detail_timing( detail_id, vchcode, profile_id, promised_send_time, create_time, update_time)
        VALUES (#{detailId}, #{vchcode}, #{profileId}, #{promisedSendTime},#{createTime}, #{updateTime})
        ON DUPLICATE KEY
            UPDATE `detail_id`=#{detailId},
                   `profile_id`=#{profileId}
    </insert>


    <select id="getBaseUserMarkList" resultType="com.wsgjp.ct.sale.biz.jarvis.dto.BillDeliverMarkDTO">
        SELECT * FROM base_user_mark WHERE profile_id=#{profileId} AND id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getAllAdvanceOrderFreight"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderFreight">
        select * from td_orderbill_freight where profile_id=#{profileId} and vchcode=#{vchcode}
    </select>

    <select id="getAdvanceOrderFreight"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderFreight">
        select f.*,p.trade_id from td_orderbill_freight f
                 join td_orderbill_platform p on f.profile_id=p.profile_id and f.vchcode=p.vchcode
                 where f.profile_id=#{profileId} and p.eshop_id=#{otypeId}
        <if test="tradeOrderList!=null and tradeOrderList.size() > 0 ">
            and p.trade_id in
            <foreach collection="tradeOrderList" close=")" open="(" separator="," item="prop">
                #{prop}
            </foreach>
        </if>
    </select>

    <select id="getAdvancepOrderFreight"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderFreight">
        select * from td_orderbill_freight where profile_id=#{profileId} and vchcode=#{eshopOrderId}
                                                    and freight_no=#{freightNo} and freight_name =#{freightName}
    </select>

    <select id="checkHasForwardSaleByStockAdvance" resultType="java.lang.Boolean">
        SELECT COUNT(0)>0 FROM  pl_eshop_order_mark WHERE profile_id=#{profileId} AND mark_code=10000018 LIMIT 1
    </select>
</mapper>
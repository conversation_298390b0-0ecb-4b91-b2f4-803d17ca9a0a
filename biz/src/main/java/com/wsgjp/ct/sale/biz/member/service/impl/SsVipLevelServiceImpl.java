package com.wsgjp.ct.sale.biz.member.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wsgjp.ct.framework.enums.AssertsChangeType;
import com.wsgjp.ct.framework.enums.AssertsSourceOperation;
import com.wsgjp.ct.framework.enums.AssertsSourceType;
import com.wsgjp.ct.pm.service.PermissionValiateService;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.biz.member.common.EquityValueType;
import com.wsgjp.ct.sale.biz.member.common.PermissionShopSale;
import com.wsgjp.ct.sale.biz.member.mapper.*;
import com.wsgjp.ct.sale.biz.member.model.dto.card.GetCardRequest;
import com.wsgjp.ct.sale.biz.member.model.dto.card.GiveCardToVipRequest;
import com.wsgjp.ct.sale.biz.member.model.dto.card.VipLevelRequest;
import com.wsgjp.ct.sale.biz.member.model.dto.vip.*;
import com.wsgjp.ct.sale.biz.member.model.entity.card.SsCard;
import com.wsgjp.ct.sale.biz.member.model.entity.card.SsCardTemplate;
import com.wsgjp.ct.sale.biz.member.model.entity.rights.MemberEquityValue;
import com.wsgjp.ct.sale.biz.member.model.entity.rights.SsEquityValueDetail;
import com.wsgjp.ct.sale.biz.member.model.entity.vip.*;
import com.wsgjp.ct.sale.biz.member.model.vo.card.VipGetCard;
import com.wsgjp.ct.sale.biz.member.service.*;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.base.BaseInfoLog;
import com.wsgjp.ct.sale.biz.shopsale.service.StoreService;
import com.wsgjp.ct.sale.biz.wx.service.NgpWxOpenService;
import com.wsgjp.ct.sale.sdk.pos.biz.MemberAssertsChangeService;
import com.wsgjp.ct.sale.sdk.pos.entity.asserts.MemberAssert;
import com.wsgjp.ct.sale.sdk.pos.entity.asserts.MemberAssertsChange;
import com.wsgjp.ct.sale.sdk.pos.entity.asserts.VipAsserts;
import com.wsgjp.ct.support.context.CurrentUser;
import ngp.idgenerator.UId;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 会员等级管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-11
 */
@Service
public class SsVipLevelServiceImpl implements ISsVipLevelService {

    private static Logger log = LoggerFactory.getLogger(SsVipLevelServiceImpl.class);

    @Autowired
    SsVipLevelMapper mapper;

    @Autowired
    private ISsCardTemplateService ssCardTemplateService;

    @Autowired
    private SsCardMapper ssCardMapper;

    @Autowired
    private SsVipMapper ssVipMapper;

    @Autowired
    private SsVipRechargeLevelMapper ssVipRechargeLevelMapper;

    @Autowired
    private MemberAssertsChangeService memberAssertsChangeService;

    @Autowired
    private ISsVipAssertsChangeService vipAssertsChangeService;

    private final NgpWxOpenService wxOpenService;

    public SsVipLevelServiceImpl(@Lazy NgpWxOpenService wxOpenService) {
        this.wxOpenService = wxOpenService;
    }

    @Override
    public SelectLevelResponseDTO getDefaultVipLevel() {
        SelectLevelRequestDTO request = new SelectLevelRequestDTO();
        request.setPage(1);
        request.setPageSize(1);
        request.setVipType(0);
        PageInfo<SelectLevelResponseDTO> vipLevelList = getAllSsVipLevelList(request);
        List<SelectLevelResponseDTO> list = vipLevelList.getList();
        if (list == null || list.isEmpty()) {
            throw new RuntimeException("还未创建任何免费会员等级，请联系管理员添加");
        }
        return list.get(0);
    }

    @Override
    public PageInfo<SelectLevelResponseDTO> getAllSsVipLevelList(SelectLevelRequestDTO requsetParam) {
        PageHelper.startPage(requsetParam.getPage(), requsetParam.getPageSize());
        if (requsetParam.getVipName() != null && !Objects.equals(requsetParam.getVipName(), "")) {
            requsetParam.setVipNameList(Arrays.asList(requsetParam.getVipName().split(",|，")));
        }
        List<SelectLevelResponseDTO> pageList = getVipLevelList(requsetParam);
        return new PageInfo<>(pageList);
    }

    @Override
    public List<SelectLevelResponseDTO> getVipLevelList(SelectLevelRequestDTO requsetParam) {
        requsetParam.setProfileId(CurrentUser.getProfileId());
        List<SelectLevelResponseDTO> levelList = mapper.getAllSsVipLevelList(requsetParam);
        //查询付费会员关联的付费规则
        if (requsetParam.getVipType() == null || requsetParam.getVipType() == 1) {
            List<SelectLevelResponseDTO> sVipList = levelList.stream().filter(level -> level.getVipType() == 1).collect(Collectors.toList());
            if (!sVipList.isEmpty()) {
                List<BigInteger> sVipLevelIds = sVipList.stream().map(SsVipLevel::getId).collect(Collectors.toList());
                Map<BigInteger, List<SsVipLevelRule>> ruleGroupByLevelId = mapper.selectVipLevelRuleByLevelIds(sVipLevelIds, CurrentUser.getProfileId()).stream().collect(Collectors.groupingBy(SsVipLevelRule::getVipLevelId));
                for (SelectLevelResponseDTO vipLevel : sVipList) {
                    vipLevel.setLevelRule(ruleGroupByLevelId.get(vipLevel.getId()));
                }
            }
        }
        return levelList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public BigInteger insertSsVipLevel(VipLevelRequest requsetParam) {
        requsetParam.setProfileId(CurrentUser.getProfileId());

        checkLevelRule(requsetParam);

        SsVipLevel levelExit = mapper.getIsExitVipNameData(requsetParam);
        if (levelExit != null) {
            throw new RuntimeException("会员等级重复");
        }

        // 取出最大值 ,要判断vip_level ， 还有 upgrade_value
        SsVipLevel level = mapper.getMaxSsVipLevel(CurrentUser.getProfileId(), requsetParam.getVipType());
        if (level != null) {
            requsetParam.setVipLevel(level.getVipLevel() + 1);
            if (requsetParam.getVipType() != 1) {
                if (level.getUpgradeValue() >= requsetParam.getUpgradeValue()) {
                    throw new RuntimeException("所需成长值需大于上一个等级的成长值。小于下一个等级等级的成长值");
                }
            }
        } else {
            // 第一条数据
            requsetParam.setVipLevel(1);
        }

        requsetParam.setId(UId.newId());
        requsetParam.setProfileId(CurrentUser.getProfileId());
        BigInteger modelId = insertOrUpdateLevelCard(requsetParam, 0, null);
        requsetParam.setModelId(modelId);
        //插入付费会员等级规则
        insertLevelRule(requsetParam);
        mapper.insertSsVipLevel(requsetParam);
        return requsetParam.getId();
    }

    /**
     * 检查付费会员规则
     *
     * @param requsetParam
     */
    private void checkLevelRule(VipLevelRequest requsetParam) {
        //付费会员
        if (requsetParam.getVipType() == 1) {
            if (requsetParam.getLevelRule() == null || requsetParam.getLevelRule().isEmpty()) {
                throw new RuntimeException("付费规则为空");
            }
            for (int i = 0; i < requsetParam.getLevelRule().size(); i++) {
                SsVipLevelRule item1 = requsetParam.getLevelRule().get(i);
                if (item1.getValidity() < 0) {
                    throw new RuntimeException("等级有效期不得小于0");
                }
                if (item1.getLevelPrice().compareTo(BigDecimal.ZERO) < 0) {
                    throw new RuntimeException("等级价格不得小于0");
                }
                for (int j = i + 1; j < requsetParam.getLevelRule().size(); j++) {
                    SsVipLevelRule item2 = requsetParam.getLevelRule().get(j);
                    if (item1.getValidity().equals(item2.getValidity())) {
                        throw new RuntimeException("存在相同有效期的付费规则");
                    }
                }
            }
        }
    }

    @Transactional
    public void insertLevelRule(VipLevelRequest requsetParam) {
        if (requsetParam.getVipType() != 1 || requsetParam.getId() == null) return;
        for (SsVipLevelRule rule : requsetParam.getLevelRule()) {
            rule.setId(UId.newId());
            rule.setProfileId(CurrentUser.getProfileId());
            rule.setVipLevelId(requsetParam.getId());
        }
        //先删除该会员等级关联的档位
        mapper.deleteRulesByLevelId(requsetParam.getId(), CurrentUser.getProfileId());
        mapper.insertLevelRules(requsetParam.getLevelRule());
    }

    @Override
    @Transactional
    public Boolean updateSsVipLevel(VipLevelRequest requsetParam) {
        requsetParam.setProfileId(CurrentUser.getProfileId());

        checkLevelRule(requsetParam);

        SsVipLevel levelExit = mapper.getIsExitVipNameData(requsetParam);
        if (levelExit != null && !levelExit.getId().equals(requsetParam.getId())) {
            throw new RuntimeException("会员等级重复");
        }

        SsVipLevel ssVipLevel = new SsVipLevel();
        ssVipLevel.setId(requsetParam.getId());
        ssVipLevel.setProfileId(requsetParam.getProfileId());
        SsVipLevel vipLevelCurrent = mapper.getCurrentVipLevel(ssVipLevel);

        //校验成长值
        if (requsetParam.getVipType() != 1) {
            // 取出前后等级, 判断成长值知否在他们之间
            Integer before = vipLevelCurrent.getVipLevel() - 1;
            Integer after = vipLevelCurrent.getVipLevel() + 1;
            List<Integer> listBetweend = new ArrayList<Integer>();
            if (before >= 1) {
                listBetweend.add(before);
            }
            if (after >= 1) {
                listBetweend.add(after);
            }
            Map mapParam = new HashMap();
            mapParam.put("list", listBetweend);
            mapParam.put("profileId", CurrentUser.getProfileId());
            mapParam.put("vipType", requsetParam.getVipType());
            ArrayList<SsVipLevel> levelList = mapper.getBeforeAndAfterForSsVipLevel(mapParam);
            if (levelList.size() == 2) {
                // 中间数据
                SsVipLevel levelBefore = levelList.get(0);
                SsVipLevel levelAfter = levelList.get(1);
                if (levelBefore.getUpgradeValue() >= requsetParam.getUpgradeValue() || levelAfter.getUpgradeValue() <= requsetParam.getUpgradeValue()) {
                    throw new RuntimeException("所需成长值需大于上一个等级的成长值。小于下一个等级等级的成长值");
                }
            } else if (levelList.size() == 1) {
                SsVipLevel level = levelList.get(0);
                //
                if (vipLevelCurrent.getVipLevel() == 1) {
                    // 更新的是第一条数据 ，level 是第二个数据
                    if (level.getUpgradeValue() <= requsetParam.getUpgradeValue()) {
                        throw new RuntimeException("所需成长值需大于上一个等级的成长值。小于下一个等级等级的成长值");
                    }
                } else {
                    // 更新的是最后一条数据 ，level 是倒数第二条
                    if (level.getUpgradeValue() >= requsetParam.getUpgradeValue()) {
                        throw new RuntimeException("所需成长值需大于上一个等级的成长值。小于下一个等级等级的成长值");
                    }
                }
            }
        }

        // 修改等级权益的时候是将相关权益删除，卡包不删除
        BigInteger modelId = insertOrUpdateLevelCard(requsetParam, 1, vipLevelCurrent.getCardTemplateId());
        requsetParam.setModelId(modelId);
        //插入付费会员等级规则
        insertLevelRule(requsetParam);
        return mapper.updateSsVipLevel(requsetParam);
    }

    public BigInteger insertOrUpdateLevelCard(VipLevelRequest requsetParam, Integer type, BigInteger modelId) {
        SsCardTemplate ssCardTemplate = new SsCardTemplate();
        ssCardTemplate.setCardType(0);
        if (0 == type) {
            // 新增
            // 会员等级权益卡增加
            // 卡包
            ssCardTemplate.setProfileId(CurrentUser.getProfileId());
            ssCardTemplate.setFullname(requsetParam.getVipName());
            ssCardTemplate.setValidType(2);
            ssCardTemplate.setValidValue("");
            ssCardTemplate.setComment("");
            ssCardTemplate.setSuperposition(0);
        } else {
            ssCardTemplate = ssCardTemplateService.getCardTemplate(modelId);
            ssCardTemplate.setFullname(requsetParam.getVipName());
        }
        // 权益
        List<MemberEquityValue> memberEquityValues = new ArrayList<>();
        if (requsetParam.getScoreMultiple()) {
            List<SsEquityValueDetail> detailList = new ArrayList<>();
            // 权益价值
            MemberEquityValue memberEquityValue = new MemberEquityValue();
            memberEquityValue.setValueType(EquityValueType.scoreMultiple.name());
            memberEquityValue.setFullName(requsetParam.getScoreMultipleRmb() + "元" + requsetParam.getScoreMultipleScore() + "积分");
            memberEquityValues.add(memberEquityValue);
            // 权益明细
            SsEquityValueDetail equityValueDetail = new SsEquityValueDetail();
            equityValueDetail.setValueCondition(new BigDecimal(requsetParam.getScoreMultipleRmb()));
            equityValueDetail.setValueDetail(new BigDecimal(requsetParam.getScoreMultipleScore()));
            detailList.add(equityValueDetail);
            memberEquityValue.setDetailList(detailList);
        }
        if (requsetParam.getDiscount()) {
            List<SsEquityValueDetail> detailList = new ArrayList<>();
            // 权益价值
            MemberEquityValue memberEquityValue = new MemberEquityValue();
            memberEquityValue.setValueType(EquityValueType.discount.name());
            memberEquityValue.setFullName("消费折扣" + requsetParam.getDiscountZk() + "折");
            memberEquityValues.add(memberEquityValue);
            // 权益明细
            SsEquityValueDetail equityValueDetail = new SsEquityValueDetail();
            equityValueDetail.setValueDetail(new BigDecimal(requsetParam.getDiscountZk()));
            equityValueDetail.setValueCondition(BigDecimal.valueOf(0));
            equityValueDetail.setPtypeRang(requsetParam.getPtypeRang());
            if (requsetParam.getPtypeRang() == 1 || requsetParam.getPtypeRang() == 3
                    || requsetParam.getPtypeRang() == 4 || requsetParam.getPtypeRang() == 5) {
                equityValueDetail.setPtypeList(requsetParam.getPtypeList());
            }
            detailList.add(equityValueDetail);
            memberEquityValue.setDetailList(detailList);

        }
        if (requsetParam.getMail()) {
            // 权益价值
            MemberEquityValue memberEquityValue = new MemberEquityValue();
            memberEquityValue.setValueType(EquityValueType.mail.name());
            memberEquityValue.setFullName("包邮");
            memberEquityValues.add(memberEquityValue);
        }
        if (requsetParam.getPriorityGoods()) {
            // 权益价值
            MemberEquityValue memberEquityValue = new MemberEquityValue();
            memberEquityValue.setValueType(EquityValueType.priorityGoods.name());
            memberEquityValue.setFullName("优先发货");
            memberEquityValues.add(memberEquityValue);

        }
        //升级赠送积分
        if (requsetParam.getScoreGift() != null && requsetParam.getScoreGift()) {
            List<SsEquityValueDetail> detailList = new ArrayList<>();
            // 权益价值
            MemberEquityValue memberEquityValue = new MemberEquityValue();
            memberEquityValue.setValueType(EquityValueType.scoreGift.name());
            memberEquityValue.setFullName("升级送" + requsetParam.getScoreGiftData() + "积分");
            // 权益明细
            SsEquityValueDetail equityValueDetail = new SsEquityValueDetail();
            equityValueDetail.setValueDetail(new BigDecimal(requsetParam.getScoreGiftData()));
            equityValueDetail.setValueCondition(BigDecimal.valueOf(0));
            detailList.add(equityValueDetail);
            memberEquityValue.setDetailList(detailList);
            memberEquityValues.add(memberEquityValue);
        }
        //升级赠送卡券
        if (requsetParam.getCardGift() != null && requsetParam.getCardGift()) {
            List<SsEquityValueDetail> detailList = new ArrayList<>();
            // 权益价值
            MemberEquityValue memberEquityValue = new MemberEquityValue();
            memberEquityValue.setValueType(EquityValueType.cardGift.name());
            memberEquityValue.setFullName("升级送卡券");
            // 权益明细
            SsEquityValueDetail equityValueDetail = new SsEquityValueDetail();
            equityValueDetail.setValueCondition(BigDecimal.valueOf(0));
            equityValueDetail.setValueDetail(BigDecimal.valueOf(0));
            equityValueDetail.setValueString(requsetParam.getCardGiftList());

            detailList.add(equityValueDetail);
            memberEquityValue.setDetailList(detailList);
            memberEquityValues.add(memberEquityValue);
        }
        //开卡赠送积分
        if (requsetParam.getOpenCardScoreGift() != null && requsetParam.getOpenCardScoreGift()) {
            List<SsEquityValueDetail> detailList = new ArrayList<>();
            // 权益价值
            MemberEquityValue memberEquityValue = new MemberEquityValue();
            memberEquityValue.setValueType(EquityValueType.openCardScoreGift.name());
            memberEquityValue.setFullName("开卡送" + requsetParam.getOpenCardScoreGiftData() + "积分");
            // 权益明细
            SsEquityValueDetail equityValueDetail = new SsEquityValueDetail();
            equityValueDetail.setValueDetail(new BigDecimal(requsetParam.getOpenCardScoreGiftData()));
            equityValueDetail.setValueCondition(BigDecimal.valueOf(0));
            detailList.add(equityValueDetail);
            memberEquityValue.setDetailList(detailList);
            memberEquityValues.add(memberEquityValue);
        }
        //开卡赠送卡券
        if (requsetParam.getOpenCardGift() != null && requsetParam.getOpenCardGift()) {
            List<SsEquityValueDetail> detailList = new ArrayList<>();
            // 权益价值
            MemberEquityValue memberEquityValue = new MemberEquityValue();
            memberEquityValue.setValueType(EquityValueType.openCardGift.name());
            memberEquityValue.setFullName("开卡送卡券");
            // 权益明细
            SsEquityValueDetail equityValueDetail = new SsEquityValueDetail();
            equityValueDetail.setValueCondition(BigDecimal.valueOf(0));
            equityValueDetail.setValueDetail(BigDecimal.valueOf(0));
            equityValueDetail.setValueString(requsetParam.getOpenCardGiftList());

            detailList.add(equityValueDetail);
            memberEquityValue.setDetailList(detailList);
            memberEquityValues.add(memberEquityValue);
        }
        ssCardTemplate.setMemberEquityValues(memberEquityValues);
        return ssCardTemplateService.insertOrUpdateCardTemplate(ssCardTemplate);
    }

    @Override
    public boolean stopSsVipLevel(SsVipLevel requsetParam) {

        if (requsetParam.getStoped() && !PermissionValiateService.validate(PermissionShopSale.MEMBER_LEVEL_STOP)) {
            throw new RuntimeException("您无停用会员等级的权限，请联系管理员授权");
        }
        if (!requsetParam.getStoped() && !PermissionValiateService.validate(PermissionShopSale.MEMBER_LEVEL_OPEN)) {
            throw new RuntimeException("您无启用会员等级的权限，请联系管理员授权");
        }

        requsetParam.setProfileId(CurrentUser.getProfileId());
        // 取出最大值 ,要判断vip_level ， 还有 upgrade_value
        SsVipLevel level = mapper.getCurrentVipLevel(requsetParam);
        if (level.getVipLevel() == 1 && level.getVipType() != 1) {
            throw new RuntimeException("第一条等级不能停用");
        }
        if (level.getVipPersonCount() != null && level.getVipPersonCount() != 0) {
            throw new RuntimeException("当前等级下还有会员，不能停用");
        }
        if (requsetParam.isCheckVipRecharge() && requsetParam.getStoped()) {
            List<BigInteger> rechargeByLevelId = ssVipRechargeLevelMapper.getRechargeByLevelId(requsetParam.getId(), CurrentUser.getProfileId());
            if (rechargeByLevelId != null && rechargeByLevelId.size() != 0) {
                throw new RuntimeException("当前等级在充值策略中使用，停用等级不影响充值策略的应用");
            }
        }
        return mapper.stopSsVipLevel(requsetParam);
    }

    @Override
    public SsVipLevel selectVipLevelById(BigInteger id) {
        SsVipLevel requestParam = new SsVipLevel();
        requestParam.setId(id);
        requestParam.setProfileId(CurrentUser.getProfileId());
        return mapper.getCurrentVipLevel(requestParam);
    }

    @Override
    public SsCardTemplate getCardByLevelId(BigInteger levelId) {
        SsVipLevel ssVipLevel = mapper.getCardTemplateIdByLevelId(levelId, CurrentUser.getProfileId());
        return ssCardTemplateService.getCardTemplate(ssVipLevel.getCardTemplateId());
    }

    @Override
    public SsVipLevelAssessPeriod getLevelAssessPeriod() {
        return mapper.getLevelAssessPeriod(CurrentUser.getProfileId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void saveOrUpdateLevelAssessPeriod(SsVipLevelAssessPeriod ssVipLevelAssessPeriod) {
        // 数据校验
        checkData(ssVipLevelAssessPeriod);
        // 开启时新增数据，同时变更会员评估时间
        if (ssVipLevelAssessPeriod.getLevelAssessPeriod() == 1) {
            // 开启时又分新增和修改两种情况
            if (ssVipLevelAssessPeriod.getId() == null) {
                // 新增
                ssVipLevelAssessPeriod.setId(UId.newId());
                ssVipLevelAssessPeriod.setProfileId(CurrentUser.getProfileId());
                mapper.insertLevelAssessPeriod(ssVipLevelAssessPeriod);
                // 修改现有会员的评估时间
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DATE, ssVipLevelAssessPeriod.getPeriod());
                mapper.updateVipLevelAssess(CurrentUser.getProfileId(), calendar.getTime());
            } else {
                // 这里有个特殊情况，有些客户可能存在之前没开启但是表里有数据，属于历史遗留问题导致的脏数据，这部分数据有id但是也要修改现有会员的评估时间
                SsVipLevelAssessPeriod levelAssessPeriod = getLevelAssessPeriod();
                if (levelAssessPeriod != null && levelAssessPeriod.getLevelAssessPeriod() == 0) {
                    // 修改现有会员的评估时间
                    Calendar calendar = Calendar.getInstance();
                    calendar.add(Calendar.DATE, ssVipLevelAssessPeriod.getPeriod());
                    mapper.updateVipLevelAssess(CurrentUser.getProfileId(), calendar.getTime());
                }

                // 修改，修改时不会变更会员的评估时间
                ssVipLevelAssessPeriod.setProfileId(CurrentUser.getProfileId());
                mapper.updateLevelAssessPeriod(ssVipLevelAssessPeriod);
            }
        } else {
            // 关闭时删除数据，同时将会员评估时间置空
            mapper.deleteLevelAssessPeriod(CurrentUser.getProfileId());
            // 把所有会员的评估时间置空
            mapper.updateVipLevelAssess(CurrentUser.getProfileId(), null);
        }
    }

    private void checkData(SsVipLevelAssessPeriod ssVipLevelAssessPeriod) {
        if (ssVipLevelAssessPeriod.getLevelAssessPeriod() == null) {
            throw new RuntimeException("等级评估开关不能为空");
        }
        if (ssVipLevelAssessPeriod.getLevelAssessPeriod() != 0 && ssVipLevelAssessPeriod.getLevelAssessPeriod() != 1) {
            throw new RuntimeException("等级评估开关只能为开或关");
        }
        if (ssVipLevelAssessPeriod.getLevelAssessPeriod() == 1 && ssVipLevelAssessPeriod.getPeriod() == null) {
            throw new RuntimeException("等级评估周期不能为空");
        }
        if (ssVipLevelAssessPeriod.getLevelAssessPeriod() == 1 && (30 > ssVipLevelAssessPeriod.getPeriod() || 365 < ssVipLevelAssessPeriod.getPeriod())) {
            throw new RuntimeException("等级评估周期最大允许填入365天，最小允许填写30天");
        }
    }

    @Override
    public void levelAssessPeriod(BigInteger profileId) {
        // 查询会员等级评估周期，按账套为单位，且只查询开启的
        List<SsVipLevelAssessPeriod> list = new ArrayList<>();
        try {
            list = mapper.getLevelAssessPeriodList(profileId);
        } catch (Exception e) {
            log.error("账套id【{}】没有会员等级评估周期", profileId);
            return;
        }
        for (SsVipLevelAssessPeriod ssVipLevelAssessPeriod : list) {
            try {
                changeLevelAssessPeriod(ssVipLevelAssessPeriod, profileId);
            } catch (Exception e) {
                log.error("会员等级评估周期账套id：" + ssVipLevelAssessPeriod.getProfileId() + " 异常，异常原因：" + e.getMessage());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void vipLevelUpdate(BigInteger vipId) {
        SsVip ssVip = ssVipMapper.selectVipById(vipId, CurrentUser.getProfileId());
        Integer addVipPoint = ssVip.getGrowthValue();
        // 会员当前等级
        SsVipLevel currentLevel = mapper.selectVipLevelByVipId(CurrentUser.getProfileId(), vipId);
        boolean canUpdate = false;
        SsVipLevel vipNextLevel = null;
        //只有免费会员会升降级
        if (currentLevel != null && currentLevel.getVipType() == 0) {
            //查看成长值变动后，该成长值应该在哪个等级范围内
            vipNextLevel = mapper.getVipNextLevel(ssVip, addVipPoint);
            // vipNextLevel不为空，且不是当前等级，就变动
            // 下面这个是升降级都支持的场景，这个版本先不处理，这个版本先只能升级
            // canUpdate = Objects.nonNull(vipNextLevel) && !Objects.equals(vipNextLevel.getId(), ssVip.getLevelId());
            canUpdate = Objects.nonNull(vipNextLevel) && !Objects.equals(vipNextLevel.getId(), ssVip.getLevelId()) && vipNextLevel.getVipLevel() > currentLevel.getVipLevel();
        }
        if (canUpdate) {
            // 等级不匹配，变动等级
            // 查询开没开启等级评估周期
            SsVipLevelAssessPeriod levelAssessPeriod = mapper.getLevelAssessPeriod(CurrentUser.getProfileId());
            if (levelAssessPeriod != null && levelAssessPeriod.getLevelAssessPeriod() == 1) {
                // 开启
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DATE, levelAssessPeriod.getPeriod());
                ssVipMapper.updateVipLevel(vipId, vipNextLevel.getId(), addVipPoint, calendar.getTime(), 1, ssVip.getProfileId());
            } else {
                ssVipMapper.updateVipLevel(vipId, vipNextLevel.getId(), addVipPoint, null, 1, ssVip.getProfileId());
            }
            // 看是升级还是降级，只有升级才送权益，降级暂且不处理（但是要变更等级权益卡）
            GiveCardToVipRequest cardToVipRequest = new GiveCardToVipRequest();
            List<BigInteger> vip = new ArrayList<>();
            vip.add(vipId);
            List<BigInteger> cards = new ArrayList<>();
            cards.add(vipNextLevel.getCardTemplateId());

            // 会员升级赠送的权益
            if (vipNextLevel.getVipLevel() > currentLevel.getVipLevel()) {
                SsCardTemplate vipLevelCard = ssCardTemplateService.getCardTemplate(vipNextLevel.getCardTemplateId());
                MemberAssertsChange vipAssertsChangeDto = new MemberAssertsChange();
                vipAssertsChangeDto.setMemo("会员升级");
                vipAssertsChangeDto.setSourceOperation(AssertsSourceOperation.VIP_UPGRADE);
                vipAssertsChangeDto.setOperationSource(AssertsSourceType.PC);
                List<VipAsserts> vipAssertsList = new ArrayList<>();
                VipAsserts vipAssert = new VipAsserts();
                vipAssert.setVipId(vipId);
                List<MemberAssert> vipAsserts = new ArrayList<>();
                List<SsCardTemplate> sendCards = new ArrayList<>();
                for (MemberEquityValue memberEquityValue : vipLevelCard.getMemberEquityValues()) {
                    if (memberEquityValue.getValueType().equals(EquityValueType.cardGift.name())) {
                        //升级赠送卡券
                        sendCards = JsonUtils.toList(memberEquityValue.getDetailList().get(0).getValueString(), SsCardTemplate.class);
                    } else if (memberEquityValue.getValueType().equals(EquityValueType.scoreGift.name())) {
                        //升级赠送积分
                        vipAsserts.add(MemberAssert.createData(0, memberEquityValue.getDetailList().get(0).getValueDetail(), AssertsChangeType.UPGRADE_GIFT));
                    }
                }
                if (sendCards != null) {

                    for (SsCardTemplate map : sendCards) {
                        vipAsserts.add(MemberAssert.createData(4,
                                BigDecimal.ONE,
                                VipAssertsBillDetailDto.getTypedMemo(4, BigDecimal.ONE),
                                null,
                                map.getId(),
                                AssertsChangeType.UPGRADE_GIFT));
                    }
                }
                vipAssert.setVipAssert(vipAsserts);
                vipAssertsList.add(vipAssert);
                vipAssertsChangeDto.setVipAsserts(vipAssertsList);
                memberAssertsChangeService.vipAssertsChange(vipAssertsChangeDto);
            }

            cardToVipRequest.setVipIds(vip);
            cardToVipRequest.setCardIds(cards);
            cardToVipRequest.setComment("会员等级变动");
            ssCardTemplateService.giveCardToVip(cardToVipRequest);
        }
    }

    @Override
    public SelectLevelResponseDTO getVipLevelByLevelId(BigInteger levelId) {
        BigInteger profileId = CurrentUser.getProfileId();
        SelectLevelResponseDTO level = new SelectLevelResponseDTO();
        SsVipLevel ssVipLevel = mapper.getVipLevelByLevelId(levelId, profileId);
        BeanUtils.copyProperties(ssVipLevel, level);
        List<SsVipLevelRule> levelRules = mapper.selectVipLevelRuleByLevelId(levelId, profileId);
        level.setLevelRule(levelRules);
        return level;
    }

    @Override
    public SsVipLevel getMaxVipLevel(Integer vipType) {
        return mapper.getMaxSsVipLevel(CurrentUser.getProfileId(), vipType);
    }


    public void changeLevelAssessPeriod(SsVipLevelAssessPeriod ssVipLevelAssessPeriod, BigInteger profileId) {
        // 查询该账套下所有到评估日的会员
        List<SsVip> vipList = mapper.getVipByProfileIdAndLevelAssessPeriod(ssVipLevelAssessPeriod.getProfileId(), new Date());
        List<BigInteger> changeIds = GetBeanUtil.getBean(SsVipLevelServiceImpl.class).changeVipLevel(vipList, ssVipLevelAssessPeriod, "等级评估周期重新定级");
        // 等级变更后同步微信会员
        if (changeIds.isEmpty()) {
            return;
        }
        try {
            vipAssertsChangeService.wxVipSyncList(changeIds, profileId);
        } catch (Exception e) {
            log.error("等级评估周期-同步微信会员出错：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public List<BigInteger> changeVipLevel(List<SsVip> vipList, SsVipLevelAssessPeriod ssVipLevelAssessPeriod, String memo) {
        List<BigInteger> changeIds = new ArrayList<>();
        if (vipList != null && !vipList.isEmpty()) {
            for (SsVip ssVip : vipList) {
                // 重新定级
                // 判断会员当前成长值所符合的等级
                SsVipLevel ssVipLevel = mapper.getVipLevelBefore(ssVip.getGrowthValue(), ssVip.getProfileId());
                // 查看会员当前等级和查出来的等级是否相同
                if (ssVipLevel != null && ssVipLevel.getId() != null && !Objects.equals(ssVipLevel.getId(), ssVip.getLevelId())) {
                    // 不同-降级
                    ssVip.setLevelId(ssVipLevel.getId());
                    ssVip.setGrowthValue(0);

                    // 更新等级权益卡
                    sendLevelCard(ssVip.getId(), ssVip.getProfileId(), ssVipLevel.getCardTemplateId(), ssVipLevel.getId(), memo);

                    changeIds.add(ssVip.getId());
                } else {
                    // 相同-保级（保级不需要变更等级权益卡）
                    ssVip.setGrowthValue(0);
                }
                // 等级评估周期开启的情况下才赋值
                if (ssVipLevelAssessPeriod != null && ssVipLevelAssessPeriod.getLevelAssessPeriod() == 1) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.add(Calendar.DATE, ssVipLevelAssessPeriod.getPeriod());
                    ssVip.setNextLevelAssessTime(calendar.getTime());
                }
                // 目前只涉及到免费会员的等级变动，没有有效期，所以直接为空
                ssVip.setValidDate(null);
                int i = mapper.updateVip(ssVip);
                if (i != 1) {
                    throw new RuntimeException("修改会员等级失败，会员id=" + ssVip.getId());
                }
            }
        }
        return changeIds;
    }

    @Override
    public Integer getFreeLevel() {
        return mapper.getFreeLevel(CurrentUser.getProfileId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void changeVipLevelToSpecifiedLevel(ChangeVipLevelDTO request) {
        Assert.isTrue(request != null, "参数不能为空");
        Assert.isTrue(request.getVipId() != null && !request.getVipId().equals(BigInteger.ZERO), "会员id不能为空");
        Assert.isTrue(request.getLevelId() != null && !request.getLevelId().equals(BigInteger.ZERO), "等级id不能为空");
        // 校验会员是否存在
        SsVip ssVip = ssVipMapper.selectVipById(request.getVipId(), CurrentUser.getProfileId());
        Assert.isTrue(ssVip != null, "会员不存在");
        // 校验等级是否存在，是否停用
        SsVipLevel ssVipLevel = mapper.selectVipLevelById(CurrentUser.getProfileId(), request.getLevelId());
        Assert.isTrue(ssVipLevel != null, "会员等级不存在");
        Assert.isTrue(!ssVipLevel.getStoped(), "该等级已停用");
        SsVipLevel oldLevel = mapper.selectVipLevelById(CurrentUser.getProfileId(), ssVip.getLevelId());
        // 校验等级是否发生变更
        if (request.getLevelId().equals(ssVip.getLevelId())) {
            // 等级未发生变动，返回
            return;
        }
        // 修改会员等级及等级权益卡
        ssVip.setLevelId(request.getLevelId());
        mapper.updateVip(ssVip);
        // 更新等级权益卡
        sendLevelCard(ssVip.getId(), ssVip.getProfileId(), ssVipLevel.getCardTemplateId(), ssVipLevel.getId(), "修改会员等级");
        // 记录变更日志
        String message = String.format("手动变更会员等级：由【%s】改为【%s】", oldLevel.getVipName(), ssVipLevel.getVipName());
        saveLog(request.getVipId(), message);

        vipAssertsChangeService.wxVipAsync(ssVip.getId(), CurrentUser.getProfileId());
    }

    public void saveLog(BigInteger vipId, String message) {
        BaseInfoLog log = new BaseInfoLog();
        log.setObjectType("vip");
        log.setObjectId(vipId);
        log.setBody(message);
        try {
            GetBeanUtil.getBean(StoreService.class).saveLog(log);
        } catch (Exception e) {

        }
    }

    @Override
    public void sendLevelCard(BigInteger vipId, BigInteger profileId, BigInteger cardTemplateId, BigInteger levelId, String memo) {
        // 更新等级权益卡
        if (cardTemplateId != null) {
            // 发放等级权益卡需要把之前的等级权益卡全删除
            GetCardRequest request = new GetCardRequest();
            List<Integer> cardType = new ArrayList<>();
            cardType.add(0);
            request.setCardType(cardType);
            request.setVipId(vipId);
            request.setProfileId(profileId);
            List<VipGetCard> cardByVipId = ssCardMapper.getCardByVipId(request);
            if (cardByVipId.size() == 1) {
                // 更新modelId
                SsCard ssCard = ssCardMapper.getCardById(cardByVipId.get(0).getId(), profileId);
                ssCard.setCardTemplateId(cardTemplateId);
                ssCard.setComment(memo);
                ssCardMapper.updateCard(ssCard);
            } else if (cardByVipId.size() > 1) {
                // 保留创建时间最近的一条，其他的删除
                ssCardTemplateService.deleteCardByMemberId(vipId, 0, cardByVipId.get(0).getId());
                // 更新modelId
                SsCard ssCard = ssCardMapper.getCardById(cardByVipId.get(0).getId(), profileId);
                ssCard.setCardTemplateId(cardTemplateId);
                ssCard.setComment(memo);
                ssCardMapper.updateCard(ssCard);
            }
        } else {
            throw new RuntimeException("等级数据异常，等级id=" + levelId);
        }
    }

    @Override
    public List<SsVipLevelRule> selectVipLevelRuleByLevelId(BigInteger levelId) {
        return mapper.selectVipLevelRuleByLevelId(levelId, CurrentUser.getProfileId());
    }

    @Override
    public void payLevel(BigInteger profileId) {
        // 付费会员降为免费会员
        // 先看有没有免费等级，没有不做处理
        int freeLevelCount = 0;
        try {
            freeLevelCount = mapper.getFreeLevel(profileId);
        } catch (Exception e) {
            log.error("账套id【{}】没有会员等级表", profileId);
            return;
        }
        if (freeLevelCount == 0) {
            return;
        }
        try {
            // 查账套下有没有要到期的付费会员
            List<SsVip> vips = mapper.getExpirePaidVip(profileId);
            Assert.isTrue(vips != null && !vips.isEmpty(), "没有到期的付费会员");
            // 等级评估周期
            SsVipLevelAssessPeriod levelAssessPeriod = mapper.getLevelAssessPeriod(profileId);
            List<BigInteger> changeIds = GetBeanUtil.getBean(SsVipLevelServiceImpl.class).changeVipLevel(vips, levelAssessPeriod, "付费会员降为免费会员");

            // 等级变更后同步微信会员
            if (changeIds.isEmpty()) {
                return;
            }
            try {
                vipAssertsChangeService.wxVipSyncList(changeIds, profileId);
            } catch (Exception e) {
                log.error("等级评估周期-同步微信会员出错：" + e.getMessage());
            }
        } catch (Exception e) {
            log.error("付费会员降为免费会员异常，账套id：" + profileId + "，异常原因：" + e.getMessage());
        }
    }

    @Override
    public List<String> getLevelNameList(List<BigInteger> ids, BigInteger profileId) {
        return mapper.getLevelNameList(ids, profileId);
    }

    @Override
    public Integer growthCompute(VipGrowthCompute compute) {
        if (compute.getMode() == 1) {
            return mapper.getGrowthFromAssertBill(compute.getVipId(), compute.getVchcode(), CurrentUser.getProfileId());
        } else {
            Integer growth = vipAssertsChangeService.growthCompute(compute.getVipId(), compute.getMoney(), compute.getType());
            Integer accumulatedGrowth = vipAssertsChangeService.accumulatedGrowthCompute(compute.getVipId(), compute.getType(), compute.isAllowSaleChangeBill());
            return growth + accumulatedGrowth;
        }
    }
}

package com.wsgjp.ct.sale.biz.shopsale.model.entity.store;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigInteger;

/**
 * @program: sale
 * @author: tanglan
 * @create: 2022/12/8
 * @description: 支付方式
 **/
public class StorePayway {

    @ApiModelProperty(value = "id")
    private BigInteger id;

    @ApiModelProperty(value = "账套id")
    private BigInteger profileId;

    @ApiModelProperty(value = "机构id")
    private BigInteger otypeId;

    @ApiModelProperty(value = "支付方式id")
    private BigInteger paywayId;
    @ApiModelProperty(value = "支付方式编号")
    private String usercode;
    @ApiModelProperty(value = "支付方式名称")
    private String paywayName;
    @ApiModelProperty(value = "支付方式类型:0=现金,1=银行转账,2=聚合支付,3=预存款,4=其他第三方,5=通企付,6=淘淘谷,7=汇付,8=汇付 9=微信b2b")
    private int paywayType;

    @ApiModelProperty(value = "支付平台：1=淘淘谷，4=汇付")
    private int platformPaytype;
    
    @ApiModelProperty(value = "收款账户id")
    private String atypeId;
    @ApiModelProperty(value = "收款账户名称")
    private String atypeFullname;
    @ApiModelProperty(value = "收款账户编号")
    private String ausercode;


    @ApiModelProperty(value = "聚合支付支付类型：1 淘淘谷")
    private int payType;

    @ApiModelProperty(value = "聚合支付支付配置内容")
    private String content;
    @ApiModelProperty(value = "支付备注")
    private String memo;


    public int getPlatformPaytype() {
        return platformPaytype;
    }

    public void setPlatformPaytype(int platformPaytype) {
        this.platformPaytype = platformPaytype;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getOtypeId() {
        return otypeId;
    }

    public void setOtypeId(BigInteger otypeId) {
        this.otypeId = otypeId;
    }

    public BigInteger getPaywayId() {
        return paywayId;
    }

    public void setPaywayId(BigInteger paywayId) {
        this.paywayId = paywayId;
    }

    public String getPaywayName() {
        return paywayName;
    }

    public void setPaywayName(String paywayName) {
        this.paywayName = paywayName;
    }


    public String getAtypeId() {
        return atypeId;
    }

    public void setAtypeId(String atypeId) {
        this.atypeId = atypeId;
    }

    public String getAtypeFullname() {
        return atypeFullname;
    }

    public void setAtypeFullname(String atypeFullname) {
        this.atypeFullname = atypeFullname;
    }

    public int getPayType() {
        return payType;
    }

    public void setPayType(int payType) {
        this.payType = payType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getPaywayType() {
        return paywayType;
    }

    public void setPaywayType(int paywayType) {
        this.paywayType = paywayType;
    }


    public String getUsercode() {
        return usercode;
    }

    public void setUsercode(String usercode) {
        this.usercode = usercode;
    }

    public String getAusercode() {
        return ausercode;
    }

    public void setAusercode(String ausercode) {
        this.ausercode = ausercode;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }
}

package com.wsgjp.ct.sale.biz.eshoporder.service.order;

import com.wsgjp.ct.baseinfo.core.dao.entity.Dtype;
import com.wsgjp.ct.bill.core.handle.entity.*;
import com.wsgjp.ct.bill.core.handle.entity.BillEntity;
import com.wsgjp.ct.bill.core.handle.entity.enums.*;
import com.wsgjp.ct.bill.core.handle.service.BillCoreService;
import com.wsgjp.ct.common.enums.core.entity.MarkData;
import com.wsgjp.ct.common.enums.core.enums.BaseOrderMarkEnum;
import com.wsgjp.ct.record.sheet.core.entity.BillNumberEntity;
import com.wsgjp.ct.record.sheet.core.entity.QueryBillNumberEntity;
import com.wsgjp.ct.record.sheet.core.service.BillNumberCoreService;
import com.wsgjp.ct.sale.biz.api.baseinfo.BaseInfoApi;
import com.wsgjp.ct.sale.biz.api.baseinfo.pojo.BtypeDeliveryinfoDto;
import com.wsgjp.ct.sale.biz.eshoporder.api.AccountingApi;
import com.wsgjp.ct.sale.biz.eshoporder.api.request.PostBillRequest;
import com.wsgjp.ct.sale.biz.eshoporder.api.response.PostBillResponse;
import com.wsgjp.ct.sale.biz.eshoporder.config.EshopOrderConst;
import com.wsgjp.ct.sale.biz.eshoporder.config.ServiceConfig;
import com.wsgjp.ct.sale.biz.eshoporder.config.sysdata.EshopOrderSysDataConfig;
import com.wsgjp.ct.sale.biz.eshoporder.dao.EshopSaleOrderDao;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.LogLevelEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingMark;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.OrganizationType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.ProcessState;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.order.DownloadType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.order.OrderCreateType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.order.UpdateOrderTypeEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.EshopSaleOrderDownloadTask;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryBuyerRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryOrderDetailParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryOrderParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.order.EshopOrderMarkParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.ManualOrderResponse;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.*;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopService;
import com.wsgjp.ct.sale.biz.eshoporder.service.receiver.EshopBuyerService;
import com.wsgjp.ct.sale.biz.eshoporder.submit.impl.EshopSaleOrderAutoSubmit;
import com.wsgjp.ct.sale.biz.eshoporder.util.*;
import com.wsgjp.ct.sale.common.enums.eshoporder.OrderOpreateType;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.dto.order.EshopOrderEntity;
import com.wsgjp.ct.sale.platform.dto.order.entity.EshopOrderDetailFreight;
import com.wsgjp.ct.sale.platform.dto.order.entity.EshopSaleOrderFreight;
import com.wsgjp.ct.sale.platform.enums.PayType;
import com.wsgjp.ct.sale.platform.enums.PaymentMode;
import com.wsgjp.ct.sale.platform.enums.RefundStatus;
import com.wsgjp.ct.sale.platform.enums.TradeTypeEnum;
import com.wsgjp.ct.support.business.Money;
import com.wsgjp.ct.support.business.MoneyUtils;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.global.GlobalConfig;
import com.wsgjp.ct.support.log.service.LogService;
import com.wsgjp.ct.support.thread.ThreadPool;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.IpUtils;
import ngp.utils.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

import static com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopSaleOrderRelationService.buildOrderDetailMarksByEshopConfig;


/**
 * <AUTHOR>
 * @date 2020/2/12 0012 17:28
 */
@Service
public class EshopSaleOrderManualService extends OrderServiceBase {

	private final EshopSaleOrderDao orderDao;
	private final EshopSenderMapper delivererMapper;
	private final EshopBuyerService buyerService;
	private final BillNumberCoreService billSvc;
	private final BillCoreService billCoreService;
	private final AccountingApi accountingApi;
	private final EshopOrderBaseInfoMapper baseMapper;
	private final EshopOrderPtypeMapper eshopOrderPtypeMapper;
	private final EshopService eshopService;
	private  final BaseInfoApi baseInfoApi;
	private final ServiceConfig serviceConfig;
	private final EshopSaleOrderNotifyService notifyService;
	private final EshopSaleOrderSaver orderSaver;


	private static final Logger logger = LoggerFactory.getLogger(EshopSaleOrderManualService.class);

	public EshopSaleOrderManualService(
			EshopSaleOrderDao orderDao,
			EshopSenderMapper senderMapper,
			EshopBuyerService buyerService,
			BillCoreService billCoreService,
			AccountingApi accountingApi,
			EshopOrderBaseInfoMapper baseMapper,
			BillNumberCoreService billSvc,
			EshopOrderPtypeMapper eshopOrderPtypeMapper,
			BaseInfoApi baseInfoApi,
			EshopService eshopService,
			ServiceConfig serviceConfig,
			EshopSaleOrderNotifyService notifyService,
			EshopSaleOrderSaver orderSaver) {
		super(orderDao);
		this.orderDao = orderDao;
		this.delivererMapper = senderMapper;
		this.buyerService = buyerService;
		this.billCoreService = billCoreService;
		this.accountingApi = accountingApi;
		this.baseMapper = baseMapper;
		this.billSvc = billSvc;
		this.eshopService = eshopService;
		this.eshopOrderPtypeMapper = eshopOrderPtypeMapper;
		this.baseInfoApi = baseInfoApi;
		this.serviceConfig = serviceConfig;
		this.notifyService = notifyService;
		this.orderSaver = orderSaver;
	}

	public ManualOrderResponse modifyOrder(EshopSaleOrderEntity order, Otype otype) {
		ManualOrderResponse response = new ManualOrderResponse();
		try {
			initOrderTradeId(order);
			checkSaleOrder(order);
			buildEshopSaleOrder(order, otype);
			BtypeDeliveryinfoDto dto = buildSaveDeliverInfoDto(order);
			buildAndSaveBuyer(order);
			buildAndSaveSender(order);
			buildAndSaveDeliverInfo(order,dto);
			StatusAndMarkRebuild(order,otype);
			response = saveBill(order);
			List<EshopSaleOrderEntity> localOrders = getLocalOrders(order, otype);
			EshopSaleOrderDownloadTask task = initDownloadTask();
			orderSaver.doModifyOrder(Collections.singletonList(order), localOrders, task);
			doSubmit(order);
			buildResponse(order, response);
//			buildSubmitDestination(order,response);
			sendLogo(order);
			return response;
		} catch (Exception ex) {
			response.setSuccess(false);
			response.setMessage(String.format("编辑订单失败:%s", ex.getMessage()));
			logger.error(response.getMessage(), ex);
			return response;
		}
	}

	private void buildResponse(EshopSaleOrderEntity order, ManualOrderResponse response) {
		response.setDeliverRequired(order.isOrderDeliverRequired());
		response.setVchcode(order.getId());
		response.setTradeOrderId(order.getTradeOrderId());
	}

	@NotNull
	private EshopSaleOrderDownloadTask initDownloadTask() {
		EshopSaleOrderDownloadTask task = new EshopSaleOrderDownloadTask();
		task.setDownloadType(DownloadType.BY_MUL_EDIT);
		return task;
	}

	private List<EshopSaleOrderEntity> getLocalOrders(EshopSaleOrderEntity order, Otype otype) {
		QueryOrderParameter orderParam = new QueryOrderParameter();
		orderParam.setEshopOrderId(order.getId());
		orderParam.setProfileId(CurrentUser.getProfileId());
		List<EshopSaleOrderEntity> localOrders = GetBeanUtil.getBean(EshopSaleOrderService.class).queryOrderList(otype.getId(), Collections.singletonList(order.getTradeOrderId()));
		if (CollectionUtils.isEmpty(localOrders)){
			throw new RuntimeException(String.format("订单编号：%s,未查询到本地订单",order.getTradeOrderId()));
		}
		return localOrders;
	}

	private void sendLogo(EshopSaleOrderEntity order) {
		//徽标
		try {
			logger.info("编辑订单徽标发送消息，profileid:{}，vchcode: {}", CurrentUser.getProfileId(), Collections.singletonList(order.getId()));
			SaleOrderExStatusAsyncHandleHelper.notify(Collections.singletonList(order.getId()));
		} catch (Exception e) {
			logger.error("编辑订单徽标发送消息，profileid:{}，vchcode: {}，原因:{}", CurrentUser.getProfileId(), Collections.singletonList(order.getId()), e.getMessage());
			e.printStackTrace();
		}
	}

	public ManualOrderResponse insertSaleOrder(EshopSaleOrderEntity order, Otype otype) {
		ManualOrderResponse response = new ManualOrderResponse();
		try {
			buildInitSaleOrder(order);
			initOrderTradeId(order);
			checkSaleOrder(order);
			buildEshopSaleOrder(order, otype);
			BtypeDeliveryinfoDto dto = buildSaveDeliverInfoDto(order);
			if(!OrderCreateType.FROM_ORDER_REFUND.equals(order.getCreateType())){
				buildAndSaveBuyer(order);
			}
			order.getExtend().setRealBuyerId(order.getBuyerId());
			buildAndSaveSender(order);
			buildAndSaveDeliverInfo(order,dto);
			response = saveBill(order);
			StatusAndMarkRebuild(order, otype);
			orderDao.doBatchInsertOrderAndRetry(Collections.singletonList(order),false);
			SysLogUtil.add(SysLogUtil.buildLog(order.getTradeOrderId(), order.getId(), order.getOtypeId(), order.getLocalTradeState(), order.getProcessState(), "", order.getRefundState(),
					(order.getCreateType().equals(OrderCreateType.IMPORT) || order.getCreateType().equals(OrderCreateType.FROM_ORDER_REFUND)) ?
							(order.getCreateType().equals(OrderCreateType.IMPORT) ? OrderOpreateType.IMPORT_ORDER : OrderOpreateType.REFUND_MANUAL) :
							OrderOpreateType.MANUAL));
			doSubmit(order);
			//徽标
			try {
				logger.info("手工新增徽标发送消息，profileid:{}，vchcode: {}", CurrentUser.getProfileId(), Collections.singletonList(order.getId()));
				SaleOrderExStatusAsyncHandleHelper.notify(Collections.singletonList(order.getId()));
			} catch (Exception e) {
				logger.error("手工新增徽标发送消息，profileid:{}，vchcode: {}，原因:{}", CurrentUser.getProfileId(), Collections.singletonList(order.getId()), e.getMessage());
			}
			buildResponse(order, response);
//			buildSubmitDestination(order,response);
			return response;
		} catch (Exception ex) {
			String error = String.format("订单保存失败:%s", ex.getMessage());
			logger.error(error, ex);
			response.setMessage(error);
			response.setSuccess(false);
			return response;
		}
	}

	private  BtypeDeliveryinfoDto buildSaveDeliverInfoDto(EshopSaleOrderEntity order) {
		if (order.getEshopBuyer() == null) {
			throw new RuntimeException("订单的EshopBuyer不能为空！");
		}
		BtypeDeliveryinfoDto dto = new BtypeDeliveryinfoDto();
		dto.setId(UId.newId());
		dto.setDeliverytype(0);
		dto.setProvince(order.getEshopBuyer().getCustomerReceiverProvince());
		dto.setCity(order.getEshopBuyer().getCustomerReceiverCity());
		dto.setDistrict(order.getEshopBuyer().getCustomerReceiverDistrict());
		dto.setStreet(order.getEshopBuyer().getCustomerReceiverTown());
		dto.setReceiverAddress(order.getEshopBuyer().getCustomerReceiverAddress());
		dto.setReceiverPeople(order.getEshopBuyer().getCustomerReceiver());
		dto.setReceiverTelephone(order.getEshopBuyer().getCustomerReceiverMobile());
		dto.setReceiverCellphone(order.getEshopBuyer().getCustomerReceiverMobile());
		dto.setBtypeId(order.getBtypeId());
		return dto;
	}

	private void buildAndSaveDeliverInfo(EshopSaleOrderEntity order,BtypeDeliveryinfoDto dto) {
		try{
			if (null == order || null == dto){
				return;
			}
			if (order.isSaveBtypeAddress()){
				EshopBuyer buyer = order.getEshopBuyer();
				if (null == buyer){
					return;
				}
				QueryBuyerRequest request = new QueryBuyerRequest();
				request.setProfileId(CurrentUser.getProfileId());
				request.setOtypeId(order.getOtypeId());
				request.setCustomerReceiverProvince(dto.getProvince());
				request.setCustomerReceiverCity(dto.getCity());
				request.setCustomerReceiverDistrict(dto.getDistrict());
				request.setCustomerReceiverTown(dto.getStreet());
				request.setCustomerReceiverAddress(dto.getReceiverAddress());
				request.setCustomerReceiver(dto.getReceiverPeople());
				request.setCustomerReceiverMobile(dto.getReceiverTelephone());
				request.setBtypeId(order.getBtypeId());
				EshopBuyer eshopBuyer = GetBeanUtil.getBean(EshopOrderBuyerMapper.class).queryBuyerByDeliverInfo(request);
				if (null!= eshopBuyer){
					return;
				}
				dto.setDeliveryinfoId(buyer.getBuyerId());
				baseInfoApi.saveDeliverInfo(dto);
			}
		}catch(Exception e){
			CommonUtil.doLogByEnable(logger, LogLevelEnum.ERROR, String.format("保存收货地址失败,原因:%s", e.getMessage()),e);
		}
	}

	/**
	 * 构建提交去向
	 *
	 * @param order    订单
	 * @param response 响应
	 */
	private void buildSubmitDestination(EshopSaleOrderEntity order, ManualOrderResponse response) {
		EshopSaleOrderEntity orderEntity = GetBeanUtil.getBean(EshopSaleOrderMapper.class).querySaleOrderSubmitDestination(order.getProfileId(), order.getOtypeId(), order.getId());
		if (null == orderEntity || null == response){
			return;
		}
		if (ProcessState.NoSubmit.equals(orderEntity.getProcessState())){
			response.setSubmitDestination(-1);
			return;
		}
		if (ProcessState.Submit.equals(orderEntity.getProcessState())){
			response.setSubmitDestination(orderEntity.getProcessType().getCode());
		}
	}

	private void StatusAndMarkRebuild(EshopSaleOrderEntity order, Otype otype) {
		order.getOrderDetails().forEach(x -> {
			x.setPlatformDetailTradeState(order.getLocalTradeState());
			BuildOrderDetailMark(x);
			if (x.getOrderSaleType().equals(TradeTypeEnum.ADVANCE_FORWARD_SALE) || x.getOrderSaleType().equals(TradeTypeEnum.ADVANCE_FORWARD_SALE_BY_STOCK)) {
				//手工开的预售单，默认成【预售-按计划发】
				EShopOrderMarkUtil.doBuildAndAddToMarkList(x.getOrderDetailMarks(), order.getId(), x.getId(), order.getProfileId(), BaseOrderMarkEnum.FORWARD_SALE);
			}
			if (eshopOrderPtypeMapper.getSalePorxyLabel(order.getProfileId(),x.getPtypeId()) > 0) {
				x.setBusinessType(BillBusinessType.SaleProxy);
				EShopOrderMarkUtil.doBuildAndAddToMarkList(x.getOrderDetailMarks(), x.getEshopOrderId(), x.getId(), order.getProfileId(), BaseOrderMarkEnum.SALE_PROXY);
			}
			buildOrderDetailMarksByEshopConfig(x, otype, x.getOrderDetailMarks());
		});
		buildRefundCreateOrderMark(order);
		BuildOrderMainMark(order);
//		//根据标记构建主表和明细的控制标记
//		EshopSaleOrderBuilder builder = EshopSaleOrderBuilder.getSingleton();
//		builder.buildControllerMark(order,false);
	}

	/**
	 * 售后补发生成的原单标记计算
	 * @param order
	 */
	private void buildRefundCreateOrderMark(EshopSaleOrderEntity order) {
		if (OrderCreateType.FROM_ORDER_REFUND != order.getCreateType()){
			return;
		}
		String platformParentOrderId = order.getPlatformParentOrderId();
		if (StringUtils.isEmpty(platformParentOrderId)){
			return;
		}
		EshopOrderMarkParameter parameter = new EshopOrderMarkParameter();
		parameter.setProfileId(CurrentUser.getProfileId());
		parameter.setOtypeId(order.getOtypeId());
		parameter.setTradeOrderId(platformParentOrderId);
		List<BigInteger> markCodes = new ArrayList<>();
		markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.HSQ_DOU.getCode()));
		markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.HSQ_TAO.getCode()));
		markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.HSQ_JD.getCode()));
		markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.HSQ_VIDEO.getCode()));
		markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.HSQ_ZAN.getCode()));
		markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.HSQ_SI.getCode()));
		markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.HSQ_MEITUAN.getCode()));
		markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.HSQ_XIAOHONGSHU.getCode()));
		markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.HSQ_DANGDANG.getCode()));
		markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.HSQ_SUNING.getCode()));
		markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.HSQ_DAvDIAN.getCode()));
		markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.HSQ_XINGYUN.getCode()));
		markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.HSQ_ALIC2M.getCode()));
		markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.HSQ_TBMC.getCode()));
		markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.HSQ_VIP.getCode()));
		markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.HSQ_DW.getCode()));
		markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.HSQ_TMSM.getCode()));
		markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.HSQ_HSQ.getCode()));
		markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.OMNI_ORDER.getCode()));
		markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.HSQ_PING.getCode()));
		markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.HSQ_KUAI.getCode()));
		parameter.setMarkCodes(markCodes);
		List<EshopOrderMarkEntity> marks = orderDao.getEshopOrderMarksByTradeOrderId(parameter);
		if (CollectionUtils.isEmpty(marks)){
			return;
		}
		List<MarkData> markDataList = order.getMarkDataList();
		for (EshopOrderMarkEntity mark : marks) {
			if (null == mark){
				continue;
			}
			MarkData markData = new MarkData();
			markData.setOrderMarkEnum(BaseOrderMarkEnum.getBaseOrderMarkEnumByCode(mark.getMarkCode()));
			markData.setBubble(mark.getBubble());
			markData.setBigData(mark.getBigData());
			markDataList.add(markData);
		}
		return;
	}

	public ManualOrderResponse buildModifyOrder(EshopSaleOrderEntity order, Otype otype) {
		ManualOrderResponse response = new ManualOrderResponse();
		try {
			initOrderTradeId(order);
			//checkSaleOrder(order);
			buildEshopSaleOrder(order, otype);
			buildAndSaveBuyer(order);
			buildAndSaveSender(order);
			response.setDeliverRequired(order.isOrderDeliverRequired());
			//徽标
			try {
				logger.info("编辑预售单徽标发送消息，profileid:{}，vchcode: {}", CurrentUser.getProfileId(), Collections.singletonList(order.getId()));
				SaleOrderExStatusAsyncHandleHelper.notify(Collections.singletonList(order.getId()));
			} catch (Exception e) {
				logger.error("编辑预售单徽标发送消息，profileid:{}，vchcode: {}，原因:{}", CurrentUser.getProfileId(), Collections.singletonList(order.getId()), e.getMessage());
				e.printStackTrace();
			}
			return response;
		} catch (Exception ex) {
			response.setSuccess(false);
			response.setMessage(String.format("构建订单失败:%s", ex.getMessage()));
			logger.error(response.getMessage(), ex);
			return response;
		}
	}

	private void doSubmit(EshopSaleOrderEntity order) {
		if (1 == GlobalConfig.getUserConfig(EshopOrderSysDataConfig.class).getDisEnableAutoSubmitAfterSave()){
			return;
		}
		if (null != serviceConfig && serviceConfig.isOrderManualSubmitUseThreadPool() && !OrderCreateType.FROM_ORDER_REFUND.equals(order.getCreateType())){
			ThreadPool test = ThreadPoolFactory.build(EshopOrderConst.ORDER_MANUAL_SUBMIT_THREAD_NAME);
			test.executeAsync(x -> {
				doSubmitOrder(order);
			}, null);
		}else {
			doSubmitOrder(order);
		}
	}

	private void doSubmitOrder(EshopSaleOrderEntity order) {
		try {
			List<String> list = new ArrayList<>();
			list.add(order.getTradeOrderId());
			QueryOrderParameter parameter = new QueryOrderParameter();
			parameter.setTradeOrderIds(list);
			parameter.setProcessState(ProcessState.NoSubmit);
			EshopSaleOrderAutoSubmit orderSubmit = new EshopSaleOrderAutoSubmit(null, parameter);
			orderSubmit.submit(null);
		} catch (Exception ex) {
			logger.error(String.format("profileId%s,tradeOrderId:%s,saveOrderSubmitError", order.getProfileId(), order.getTradeOrderId()), ex);
		}
	}

	private ManualOrderResponse saveBill(EshopSaleOrderEntity order) {
		ManualOrderResponse response = new ManualOrderResponse();
		List<OrderPayment> payments = order.getPayments();
		if (payments == null || payments.size() == 0 || order.getPrTotalSettle().compareTo(BigDecimal.ZERO) <= 0) {
			return response;
		}
		if (order.getPayTimeType().equals(PayType.CASH_ON_DELIVERY) && order.getOrderSaleType() == TradeTypeEnum.NORMAL) {
			return response;
		}
		try {
			BillEntity bill = buildBill(order);
			billCoreService.saveBill(bill);
			addBillLog(bill);
			PostBillResponse postBill = postBill(bill);
			if (!postBill.getData().getSuccess()) {
				throw new RuntimeException(String.format("订单【%s】过账出错，错误原因：%s", order.getTradeOrderId(), postBill.getData().getMessage()));
			}
			return response;
		} catch (Exception e) {
			logger.error(String.format("订单【%s】生成收款单失败:%s", order.getTradeOrderId(), e.getMessage()), e);
			response.setMessage(String.format("生成收款单失败:%s", e.getMessage()));
			rollBackAdvanceInfo(order);
			return response;
		}
	}

	private PostBillResponse postBill(BillEntity bill) {
		PostBillRequest request = new PostBillRequest();
		request.setVchcode(bill.getVchcode());
		request.setAsync(false);
		PostBillResponse resp = accountingApi.PostBill(request);
		if (resp.getData() != null && !resp.getData().getSuccess()) {
			DoDeleteBill(bill.getVchcode());
			throw new RuntimeException(String.format("单据过账出错，错误原因：%s", resp.getData().getMessage()));
		}
		return resp;
	}

	private void DoDeleteBill(BigInteger vchcode) {
		PostBillRequest request = new PostBillRequest();
		request.setAsync(false);
		request.setVchcode(vchcode);
		accountingApi.DeleteBill(request);
	}

	private BillEntity buildBill(EshopSaleOrderEntity order) {
		try {
			BigInteger employeeId = CurrentUser.getEmployeeId();
			QueryBillNumberEntity param = new QueryBillNumberEntity();
			param.setProfileId(order.getProfileId());
			param.setBusinessType(0);
			param.setIntVchtype(Vchtypes.ReceiptBill.getCode());
			param.setDate(new Date());
			BillNumberEntity genNumber = billSvc.getBillNumber(param);
			BillEntity bill = new BillEntity();
			bill.setVchcode(UId.newId());
			bill.setProfileId(order.getProfileId());
			bill.setVchtype(Vchtypes.ReceiptBill);
			bill.setOtypeId(order.getOtypeId());
			//bill.setSourceId(order.getVchcode());
			bill.setProcessType(BillProcessType.NONE);
			bill.setConfirmType(BillConfirmType.NONE);
			bill.setBusinessType(BigInteger.ZERO);
			bill.setBillNumber(genNumber.getVchtypeNumber());
			bill.setBillDate(order.getCreateTime());
			bill.setPaymentDate(order.getCreateTime());
			if (order.getBtypeId() == null || order.getBtypeId().longValue() == 0) {
				throw new RuntimeException("订单往来单位不存在");
			}
			bill.setBtypeId(order.getBtypeId());
			bill.setEtypeId(employeeId);
			bill.setCreateEtypeId(employeeId);
			if (order.getEtypeId() == null) {
				bill.setEtypeId(employeeId);
			}
			bill.setKtypeId(BigInteger.ZERO);
			bill.setDtypeId(order.getDtypeId());
			bill.setBuyerId(order.getBuyerId());
			bill.setCreateType(BillCreateType.FROM_SALE_ORDER_BILL);
			bill.setDeliverType(com.wsgjp.ct.bill.core.handle.entity.enums.BillDeliverType.DELIVER_BY_LOGISTICS);
			bill.setPostState(BillPostState.DELIVERY_COMPLETED);
			bill.setSummary(String.format("手工新增预售订单生成收款单:订单编号：%s", order.getTradeOrderId()));
			buildBillAccount(order, bill);
			buildBillBalanceDetail(order, bill);
			buildBillTotal(bill);
			buildBillBalanceInfoList(order,bill);
			return bill;
		} catch (Exception ex) {
			throw new RuntimeException("构建收款单报错:" + ex.getMessage(), ex);
		}
	}

	private void buildBillBalanceInfoList(EshopSaleOrderEntity order, BillEntity bill) {
		BigDecimal payTotal = BigDecimal.valueOf(bill.getBillAccountList().stream().mapToDouble(x -> x.getTotal().doubleValue()).sum());
		List<BillBalanceInfo> billBalanceInfoList = new ArrayList<>();
		BillBalanceInfo billBalanceInfo = new BillBalanceInfo();
		billBalanceInfo.setBalanceBtypeId(order.getBtypeId());
		billBalanceInfo.setId(UId.newId());
		billBalanceInfo.setReverse(false);
		billBalanceInfo.setCurrencyBalanceTotal(payTotal);
		billBalanceInfo.setCurrencyBalanceRemain(payTotal.subtract(payTotal));
		billBalanceInfoList.add(billBalanceInfo);
		bill.setBillBalanceInfoList(billBalanceInfoList);
	}

	private void buildBillTotal(BillEntity bill) {
		BigDecimal total = BigDecimal.ZERO;
		List<BillAccount> accountList = bill.getBillAccountList();
		if (accountList != null && accountList.size() > 0) {
			for (BillAccount acc : accountList) {
				total = MoneyUtils.add(total, acc.getTotal(), Money.Total);
			}
		}
		bill.setBillTotal(total);
		bill.setDisedTaxedTotal(total);
		bill.setCurrencyBillTotal(total);
		bill.setCurrencyDisedTaxedTotal(total);

	}

	private void buildBillAccount(EshopSaleOrderEntity order, BillEntity bill) {
		List<BillAccount> accountList = new ArrayList<>();
		List<BillAccount> billAccounts = doBuildBillAccountList(order, bill);
		if (billAccounts.size() > 0) {
			accountList.addAll(billAccounts);
		}
		bill.setBillAccountList(accountList);
	}


	private List<BillAccount> doBuildBillAccountList(EshopSaleOrderEntity order, BillEntity bill) {
		List<OrderPayment> payments = order.getPayments();
		List<BillAccount> accountList = new ArrayList<>();
		for (OrderPayment payment : payments) {
			if (payment.getTotal() == null || payment.getTotal().doubleValue() == 0) {
				continue;
			}
			BillAccount account = new BillAccount();
			account.setVchcode(bill.getVchcode());
			account.setAtypeId(payment.getAtypeId());
			account.setTotal(payment.getTotal());
			account.setId(UId.newId());
			account.setBtypeId(order.getBtypeId());
			account.setAccountDetailType(BillAccountDetailType.ACCOUNT_DETAILS);
			account.setSourceAccountId(BigInteger.ZERO);
			account.setShareType(0);
			account.setDebit(BigDecimal.ZERO);
			account.setCredit(BigDecimal.ZERO);
			account.setMemo("新增预售订单收定金生成收款单");
			accountList.add(account);
		}
		return accountList;
	}

	private void buildBillBalanceDetail(EshopSaleOrderEntity order, BillEntity bill) {
		BigDecimal payTotal = BigDecimal.valueOf(bill.getBillAccountList().stream().mapToDouble(x -> x.getTotal().doubleValue()).sum());
		List<BillBalanceDetail> detailList = new ArrayList<>();
		BillBalanceDetail detail = new BillBalanceDetail();
		//detail.setBusinessVchcode(bill.getSourceId());
		detail.setId(UId.newId());
		detail.setReverse(false);
		detail.setVchcode(bill.getVchcode());
		detail.setProfileId(order.getProfileId());
		detail.setBtypeId(order.getBtypeId());
		detail.setBalanceBusinessType(BalanceBusinessType.ORIGINAL_ORDER_TOTAL);
		detail.setDetailType(BalanceDetailType.ESHOP_ORDER_SETTLEMENT);
		detail.setBillDate(order.getCreateTime());
		detail.setCurrencyPaymentTotal(payTotal);
		detail.setBusinessVchcode(order.getId());
		//detail.setCurrencyAdvanceTotal(order.getPrTotalSettle());
		detailList.add(detail);
		bill.setBillBalanceDetailList(detailList);
	}

	private void addBillLog(BillEntity bill) {
		BillLog log = new BillLog();
		log.setVchcode(UId.newId());
		log.setId(UId.newId());
		log.setProfileId(bill.getProfileId());
		log.setVchcode(bill.getVchcode());
		log.setVchtype(bill.getVchtype().getVchtype());
		log.setContent("由新增预售订单时结算生成");
		log.setEtypeId(bill.getEtypeId());
		log.setEtypeName(SysLogUtil.getLogEtypeName());
		log.setUserIp(CurrentUser.getClientIp());
		log.setServerIp(IpUtils.getLocalHostIp());
		log.setSource("预售订单保存");
		log.setRequestUrl("eshoporder/eshopsaleorder/Edit.gspx");
		LogService.add(log);
	}

	public void buildInitSaleOrder(EshopSaleOrderEntity order) {
		order.setProfileId(CurrentUser.getProfileId());
		order.setCreatorId(CurrentUser.getEmployeeId());
		if (null !=order.getDate()){
			order.setCreateTime(order.getDate());
		}else {
			order.setCreateTime(new Date());
			order.setDate(new Date());
		}
		order.setTradePayTime(new Date());
		order.setId(UId.newId());
		order.setMappingState(true);
		order.setOrderDeliverRequired(true);
		order.setDeliverType(BillDeliverType.DELIVER_BY_LOGISTICS);
	}

	public void initOrderTradeId(EshopSaleOrderEntity order) {
		if (order.getTradeOrderId() != null && !"".equals(order.getTradeOrderId())) {
			return;
		}
		QueryBillNumberEntity param = new QueryBillNumberEntity();
		param.setProfileId(order.getProfileId());
		param.setBusinessType(0);
		param.setIntVchtype(Vchtypes.OriginalSaleOrder.getCode());
		param.setDate(new Date());
		BillNumberEntity genNumber = billSvc.getBillNumber(param);

		String tradeId = "";
		for (int i = 0; i < 10; i++) {
			tradeId = genNumber.getVchtypeNumber();
			EshopSaleOrderEntity exist = orderDao.querySaleOrderByTid(tradeId, order.getOtypeId());
			if (exist == null) {
				break;
			}
		}
		order.setTradeOrderId(tradeId);

	}

	public String getInitTradeId(BigInteger otypeId) {
		QueryBillNumberEntity param = new QueryBillNumberEntity();
		param.setProfileId(CurrentUser.getProfileId());
		param.setBusinessType(0);
		param.setIntVchtype(Vchtypes.OriginalSaleOrder.getCode());
		param.setDate(new Date());
		BillNumberEntity genNumber = billSvc.getBillNumber(param);

		String tradeOrderId = "";
		for (int i = 0; i < 10; i++) {
			tradeOrderId = genNumber.getVchtypeNumber();
			EshopSaleOrderEntity exist = orderDao.querySaleOrderByTid(tradeOrderId, otypeId);
			if (exist == null) {
				break;
			}
		}
		return tradeOrderId;
	}

	private void checkSaleOrder(EshopSaleOrderEntity order) {
		try {
			String tradeId = order.getTradeOrderId();
			if (tradeId == null || "".equals(tradeId)) {
				throw new RuntimeException("订单编号不能为空");
			}
			EshopSaleOrderEntity exist = orderDao.querySaleOrderByTid(tradeId, order.getOtypeId());
			if (exist != null && !exist.getId().equals(order.getId())) {
				throw new RuntimeException("订单编号不能重复");
			}
		} catch (Exception ex) {
			throw new RuntimeException(ex.getMessage(), ex);
		}
	}

	public void buildEshopSaleOrder(EshopSaleOrderEntity order, Otype otype) {
		try {
			order.setDeleted(0);
			order.setLocalTradeState(TradeStatus.WAIT_SELLER_SEND_GOODS);
			order.setProcessState(ProcessState.NoSubmit);
			order.setRefundState(RefundStatus.NONE);
			order.setTradePayTime(order.getTradeCreateTime());
			order.setModifiedTime(new Date());
			buildPayType(order);
			buildInvoiceInfo(order);
			buildOrderDetails(order);
			buildTiming(order,otype);
			buildOrderExtend(order);
			buildOrderDistribution(order);
			calculateOrderPrice(order);
			doShareBuyerFreightFee(order);
			buildBtypeInfo(order, otype);
			buildDtypeInfo(order);
			buildAdvanceInfo(order);
			buildDeliverProcessType(order,otype);
			buildDeliverType(order,otype);
			if (otype.getOcategory().equals(OrganizationType.DYY_SHOP)) {
				order.setBusinessType(BillBusinessType.None);
			}
			clearDistributionPriceAndPurchasePrice(order);
			order.setOcategory(otype.getOcategory().getCode());
			buildOrderMention(order);
			order.setUniqueMark(order.toUniqueMark());
			buildOrderFreight(order);
		} catch (Exception ex) {
			ex.printStackTrace();
			throw new RuntimeException(String.format("订单构建失败:%s", ex.getMessage()));
		}
	}

	private static void buildOrderFreight(EshopSaleOrderEntity order) {
		List<EshopSaleOrderFreight> freightList = new ArrayList<>();
		EshopSaleOrderFreight freight = new EshopSaleOrderFreight();
		freight.setId(UId.newId());
		freight.setProfileId(order.getProfileId());
		freight.setEshopOrderId(order.getId());
		freight.setFreightCode(order.getLocalFreightCode());
		freight.setFreightNo(order.getLocalFreightBillNo());
		freight.setFreightName(order.getLocalFreightName());
		freight.setCreateTime(new Date());
		freightList.add(freight);
		order.setFreights(freightList);
		order.getOrderDetails().forEach(x->
		{
			EshopOrderDetailFreight detailFreight=new EshopOrderDetailFreight();
			detailFreight.setCreateType(0);
			detailFreight.setFreightBillNo(order.getLocalFreightBillNo());
			detailFreight.setFreightCode(order.getLocalFreightCode());
			detailFreight.setFreightName(order.getLocalFreightName());
			detailFreight.setOtypeId(order.getOtypeId());
			detailFreight.setId(UId.newId());
			detailFreight.setProfileId(order.getProfileId());
			detailFreight.setTradeOrderDetailId(x.getTradeOrderDetailId());
			detailFreight.setTradeOrderId(order.getTradeOrderId());
			detailFreight.setCreateTime(new Date());
			x.setFreightList(Collections.singletonList(detailFreight));
		});
	}

	private void buildOrderDistribution(EshopSaleOrderEntity order) {
		EshopSaleOrderDistributionBuyer distributionBuyer = new EshopSaleOrderDistributionBuyer();
		distributionBuyer.setProfileId(order.getProfileId());
		distributionBuyer.setEshopOrderId(order.getId());
		order.setDistribution(distributionBuyer);
	}

	private void buildDtypeInfo(EshopSaleOrderEntity order) {
		if (null == order || order.getEtypeId().equals(BigInteger.ZERO)){
			return;
		}
		Etype etype = baseMapper.getEtypeById(order.getProfileId(), order.getEtypeId());
		if (null == etype){
			return;
		}
		if(null == order.getDtypeId() || order.getDtypeId().compareTo(BigInteger.ZERO) == 0){
			order.setDtypeId(etype.getDtypeId());
		}
	}

	private void clearDistributionPriceAndPurchasePrice(EshopSaleOrderEntity order) {
		if (null == order){
			return;
		}
		BillBusinessType businessType = order.getBusinessType();
		List<EshopSaleOrderDetail> orderDetails = order.getOrderDetails();
		List<EshopSaleDetailCombo> detailCombos = order.getDetailCombos();
		if (!(businessType.equals(BillBusinessType.SaleDistribution) || businessType.equals(BillBusinessType.SaleProxy))){
			order.getExtend().setSupplierId(BigInteger.ZERO);
			order.getExtend().setSupplierName("");
			order.setDistributionDisedTaxedTotal(BigDecimal.ZERO);
			order.getExtend().setPurchaseTotal(BigDecimal.ZERO);
			orderDetails.forEach(o->{
				//o.setDistributionBalanceTaxedPrice(BigDecimal.ZERO);
				//o.setDistributionBalanceTaxedTotal(BigDecimal.ZERO);
				if (null != o.getPurchase()) {
					o.getPurchase().setPurchasePrice(BigDecimal.ZERO);
					o.getPurchase().setPurchaseTotal(BigDecimal.ZERO);
					o.getPurchase().setPurchaseDisedPrice(BigDecimal.ZERO);
					o.getPurchase().setPurchaseDisedTotal(BigDecimal.ZERO);
					o.getPurchase().setPurchaseDisedTaxedPrice(BigDecimal.ZERO);
					o.getPurchase().setPurchaseDisedTaxedTotal(BigDecimal.ZERO);
				}
			});
			detailCombos.forEach(d->{
				//d.setDistributionBalanceTaxedPrice(BigDecimal.ZERO);
				//d.setDistributionBalanceTaxedTotal(BigDecimal.ZERO);
				if (null != d.getPurchase()) {
					d.getPurchase().setPurchasePrice(BigDecimal.ZERO);
					d.getPurchase().setPurchaseTotal(BigDecimal.ZERO);
					d.getPurchase().setPurchaseDisedPrice(BigDecimal.ZERO);
					d.getPurchase().setPurchaseDisedTotal(BigDecimal.ZERO);
					d.getPurchase().setPurchaseDisedTaxedPrice(BigDecimal.ZERO);
					d.getPurchase().setPurchaseDisedTaxedTotal(BigDecimal.ZERO);
				}
			});
			return;
		}
		if (businessType.equals(BillBusinessType.SaleDistribution)){
			order.getExtend().setSupplierId(BigInteger.ZERO);
			order.getExtend().setSupplierName("");
			order.getExtend().setPurchaseTotal(BigDecimal.ZERO);
			orderDetails.forEach(o->{
				if (null != o.getPurchase()) {
					o.getPurchase().setPurchasePrice(BigDecimal.ZERO);
					o.getPurchase().setPurchaseTotal(BigDecimal.ZERO);
					o.getPurchase().setPurchaseDisedPrice(BigDecimal.ZERO);
					o.getPurchase().setPurchaseDisedTotal(BigDecimal.ZERO);
					o.getPurchase().setPurchaseDisedTaxedPrice(BigDecimal.ZERO);
					o.getPurchase().setPurchaseDisedTaxedTotal(BigDecimal.ZERO);
				}
			});
			detailCombos.forEach(d->{
				if (null != d.getPurchase()) {
					d.getPurchase().setPurchasePrice(BigDecimal.ZERO);
					d.getPurchase().setPurchaseTotal(BigDecimal.ZERO);
					d.getPurchase().setPurchaseDisedPrice(BigDecimal.ZERO);
					d.getPurchase().setPurchaseDisedTotal(BigDecimal.ZERO);
					d.getPurchase().setPurchaseDisedTaxedPrice(BigDecimal.ZERO);
					d.getPurchase().setPurchaseDisedTaxedTotal(BigDecimal.ZERO);
				}
			});
			return;
		}
		if (businessType.equals(BillBusinessType.SaleProxy)){
			order.setDistributionDisedTaxedTotal(BigDecimal.ZERO);
			orderDetails.forEach(o->{
				//o.setDistributionBalanceTaxedPrice(BigDecimal.ZERO);
				//o.setDistributionBalanceTaxedTotal(BigDecimal.ZERO);
			});
			detailCombos.forEach(d->{
				//d.setDistributionBalanceTaxedPrice(BigDecimal.ZERO);
				//d.setDistributionBalanceTaxedTotal(BigDecimal.ZERO);
			});
			return;
		}
	}

	public void buildBtypeInfoForImport(EshopSaleOrderEntity order, Otype otype) {
		order.setPayBtypeId(otype.getBtypeId());
		order.setCommissionBtypeId(BigInteger.ZERO);
		if (order.getBtypeId().compareTo(BigInteger.ZERO) != 0)
		{
			return;
		}
		String shopAccount = null;
		EshopBuyer buyer = order.getEshopBuyer();
		if (buyer != null) {
			shopAccount = order.getEshopBuyer().getCustomerShopAccount();
		}
		EshopSaleOrderBuilder builder = EshopSaleOrderBuilder.getSingleton();
		builder.getOrCreateBtype(shopAccount, order, otype);
	}

	public void buildBtypeInfo(EshopSaleOrderEntity order, Otype otype) {
		int btypeGenerateType = otype.getBtypeGenerateType();
		//当选择为【跟店铺所属平台结算，取默认往来单位】，结算单位取网店资料的默认往来单位。
		if (0 == btypeGenerateType){
			order.setPayBtypeId(otype.getBtypeId());
		}
		//当选择为【跟销售方结算，取订单上的销售往来单位】，结算单位取开单的往来单位。
		if (1 == btypeGenerateType){
			order.setPayBtypeId(order.getBtypeId());
		}
		order.setCommissionBtypeId(BigInteger.ZERO);
		if (order.getBtypeId().compareTo(BigInteger.ZERO) > 0) {
			return;
		}
		EshopSaleOrderBuilder builder = EshopSaleOrderBuilder.getSingleton();
		builder.getOrderBtype(order, otype,null);
	}

	public void buildTiming(EshopSaleOrderEntity order,Otype otype) {
		EshopSaleOrderTiming timing = order.getTiming();
		if (timing == null) {
			timing = new EshopSaleOrderTiming();
		}
		timing.setProfileId(order.getProfileId());
		timing.setEshopOrderId(order.getId());
		if (otype.getPromisedCollectDuration()>0 && otype.getEshopConfig().getPromisedCollectDurationConfig()==0) {
			timing.setPromisedCollectTime(DateUtils.addHours(order.getTradeCreateTime(),otype.getPromisedCollectDuration()));
		}
		if (otype.getDeliverDuration()>0) {
			timing.setSendTime(DateUtils.addHours(order.getTradePayTime(),otype.getDeliverDuration()));
		}
		if (otype.getPromisedSignDuration()>0) {
			timing.setPromisedSignTime(DateUtils.addHours(order.getTradeCreateTime(),otype.getPromisedSignDuration()));
		}
		if (otype.getPromisedSyncFreightDuration()>0) {
			timing.setPromisedSyncFreightTime(DateUtils.addHours(order.getTradeCreateTime(),otype.getPromisedSyncFreightDuration()));
		}
		order.setTiming(timing);
	}

	public void buildPayType(EshopSaleOrderEntity order) {
		if (order.getCashRequired() && order.getOrderSaleType().equals(TradeTypeEnum.NORMAL)) {
			order.setPayTimeType(PayType.CASH_ON_DELIVERY);
		} else {
			order.setPayTimeType(PayType.SCENE_SETTLEMENT);
		}
	}

	public void buildOrderMention(EshopSaleOrderEntity order) {
		if (Integer.valueOf(2).equals(order.getOcategory())) {
			order.setDeliverType(BillDeliverType.NO_DELIVER);
			order.setOrderDeliverRequired(false);
		}
		if(BillBusinessType.SaleProxy.equals(order.getBusinessType())){
			order.setDeliverType(BillDeliverType.MANUFACTURER);
		}
	}

	public void buildAndSaveSender(@NotNull EshopSaleOrderEntity order) {
		EshopSenderInfo sender = order.getSenderInfo();
		if (sender == null) {
			return;
		}
		if ("".equals(sender.getSenderName())) {
			return;
		}
		sender.setProfileId(order.getProfileId());
		try{
			EshopSenderInfo existSender = delivererMapper.getSenderByKey(sender.getProfileId(), sender.getHashKey());
			if (existSender == null) {
				BigInteger id = UId.newId();
				sender.setId(id);
				order.setSenderId(id);
				delivererMapper.insertSender(sender);
			} else {
				sender.setId(existSender.getId());
				order.setSenderId(existSender.getId());
				delivererMapper.modifySender(sender);
			}
		}catch (Exception e) {
			String error = String.format(
					"保存寄件信息【%s】报错", sender.getSenderName());
			logger.error(error, e);
			throw new RuntimeException(error, e);
		}

	}

	public void buildInvoiceInfo(EshopSaleOrderEntity order) {
		EshopSaleOrderInvoiceInfo invoiceInfo = order.getInvoiceInfo();
		if (invoiceInfo == null) {
			invoiceInfo = new EshopSaleOrderInvoiceInfo();
		}
		BigInteger invoiceId = UId.newId();
		invoiceInfo.setId(invoiceId);
		invoiceInfo.setProfileId(order.getProfileId());
		invoiceInfo.setEshopOrderId(order.getId());
		EshopSaleOrderBuilder.getSingleton().doEncryptInvoice(invoiceInfo);
	}

	public void buildOrderDetails(@NotNull EshopSaleOrderEntity order) {
		List<EshopSaleOrderDetail> details = order.getOrderDetails();
		if (details == null || details.size() == 0) {
			throw new RuntimeException("订单明细不能为空");
		}
		List<EshopSaleOrderDetail> comboRows =
				details.stream().filter(PtypeSku::isComboRow).collect(Collectors.toList());
		Map<BigInteger, BigInteger> comboRowIdMap = new HashMap<>();
		buildComboRow(order, comboRows, comboRowIdMap);
		List<EshopSaleOrderDetail> newDetails = new ArrayList<>();
		for (EshopSaleOrderDetail detail : details) {
			if (detail.getQty().compareTo(BigDecimal.ZERO) == 0){
				throw new RuntimeException("订单明细数量不能为0");
			}
			if (GetBeanUtil.getBean(EshopSaleOrderService.class).checkOrderDetailUnitError(detail.getPtypeId(), detail.getUnit())){
				throw new RuntimeException("订单明细单位不能为浮动单位");
			}
			if (detail.isComboRow()) {
				continue;
			}
			BigInteger comboRowId = detail.getComboRowId();
			detail.setDeleted(0);
			detail.setOrderSaleType(order.getOrderSaleType());
			detail.setPlatformDetailTradeState(order.getLocalTradeState());
//			detail.setSubQty(detail.getUnitQty());
			doBuildDetail(order, detail);
			buildOrderDetailExtend(detail);
			buildOrderDetailBatch(detail);
			buildOrderDetailSerialno(detail);
			if (comboRowId != null && comboRowId.longValue() > 0) {
				detail.setComboRowId(comboRowIdMap.get(comboRowId));
				detail.setTradeOrderDetailId(detail.getComboRowId().toString());
			}
			newDetails.add(detail);
		}
		order.setOrderDetails(newDetails);
	}

	private void buildOrderDetailSerialno(EshopSaleOrderDetail detail) {
		List<EshopSaleOrderDetailSerialno> serialNos = detail.getSerialNos();
		if (CollectionUtils.isEmpty(serialNos)) {
			return;
		}
		serialNos.forEach(s->{
			s.setCreateTime(new Date());
			s.setUpdateTime(new Date());
			s.setProfileId(detail.getProfileId());
			s.setId(UId.newId());
			s.setEshopOrderId(detail.getEshopOrderId());
			s.setEshopOrderDetailId(detail.getId());
			s.setQty(detail.getQty());
			s.setUnitQty(detail.getUnitQty());
			s.setSubQty(detail.getSubQty());
		});
	}

	private void buildOrderDetailBatch(EshopSaleOrderDetail detail) {
		EshopSaleOrderDetailBatch batch = detail.getBatch();
		if (null == batch){
			return;
		}
		batch.setCreateTime(new Date());
		batch.setUpdateTime(new Date());
		batch.setProfileId(detail.getProfileId());
		batch.setId(UId.newId());
		batch.setEshopOrderId(detail.getEshopOrderId());
		batch.setEshopOrderDetailId(detail.getId());
		batch.setQty(detail.getQty());
		batch.setUnitQty(detail.getUnitQty());
		batch.setSubQty(detail.getSubQty());
	}


	private void doBuildDetail(@NotNull EshopSaleOrderEntity order, EshopSaleOrderDetail detail) {
		detail.setEshopOrderId(order.getId());
		detail.setId(UId.newId());
		detail.setMappingState(true);
		detail.setDeliverRequired(true);
		if (detail.getComboRowId() != null && detail.getComboRowId().longValue() > 0) {
			detail.setLocalMappingMark(MappingMark.COMBO);
		} else {
			detail.setLocalMappingMark(MappingMark.SKU);
			detail.setTradeOrderDetailId(StringUtils.isEmpty(detail.getTradeOrderDetailId())  ? UId.newId().toString() : detail.getTradeOrderDetailId());
		}
		detail.setOtypeId(order.getOtypeId());
		detail.setKtypeId(order.getKtypeId());
		detail.setProfileId(order.getProfileId());
		detail.setQty(MoneyUtils.multiply(detail.getUnitQty(), detail.getUnitRate(), Money.Qty));
		//detail.setTaxedPrice(MoneyUtils.divide(detail.getTaxedTotal(), detail.getQty(), Money.Price));
		//detail.setDisedTaxedPrice(MoneyUtils.divide(detail.getDisedTaxedTotal(), detail.getQty(), Money.Price));
		detail.setOrderPreferentialAllotTotal(detail.getOrderPreferentialAllotTotal());
		detail.setComboShareScale(detail.getComboShareScale());
		if (null == detail.getPurchase()){
			EshopSaleOrderDetailPurchaseEntity purchaseEntity = new EshopSaleOrderDetailPurchaseEntity();
			purchaseEntity.setEshopOrderId(detail.getEshopOrderId());
			purchaseEntity.setProfileId(detail.getProfileId());
			purchaseEntity.setEshopOrderDetailId(detail.getId());
		}else {
			detail.getPurchase().setEshopOrderId(detail.getEshopOrderId());
			detail.getPurchase().setProfileId(detail.getProfileId());
			detail.getPurchase().setEshopOrderDetailId(detail.getId());
		}
	}

	public void buildComboRow(EshopSaleOrderEntity order, List<EshopSaleOrderDetail> comboRows,
			Map<BigInteger, BigInteger> comboRowIdMap) {
		if (comboRows == null || comboRows.size() == 0) {
			return;
		}
		for (EshopSaleOrderDetail detail : comboRows) {
			EshopSaleDetailCombo comboRow = new EshopSaleDetailCombo();
			comboRow.setProfileId(order.getProfileId());
			comboRow.setTradeTotal(detail.getTradeTotal());
			comboRow.setTradePrice(detail.getTradePrice());
			comboRow.setPreferentialTotal(detail.getPreferentialTotal());
			comboRow.setDisedTaxedTotal(detail.getDisedTaxedTotal());
			comboRow.setDisedTaxedPrice(detail.getDisedTaxedPrice());
			comboRow.setPrice(detail.getPrice());
			comboRow.setPtypePreferentialTotal(detail.getPtypePreferentialTotal());
			comboRow.setOrderPreferentialAllotTotal(detail.getOrderPreferentialAllotTotal());
			if (detail.getTotal().compareTo(BigDecimal.ZERO) != 0) {
				comboRow.setDiscount(MoneyUtils.divide(detail.getDisedInitialTotal(), detail.getTotal(), Money.Discount));
			}
			comboRow.setTotal(detail.getTotal());
			comboRow.setDisedInitialPrice(detail.getDisedInitialPrice());
			comboRow.setDisedInitialTotal(detail.getDisedInitialTotal());
			comboRow.setDisedPrice(detail.getDisedPrice());
			comboRow.setDisedTotal(detail.getDisedTotal());
			comboRow.setEshopOrderId(order.getId());
			comboRow.setCreateTime(new Date());
			comboRow.setQty(detail.getQty());
			comboRow.setEshopOrderDetailComboRowId(UId.newId());
			comboRow.setComboId(detail.getPtypeId());
			comboRow.setPtypeServiceFee(detail.getPtypeServiceFee());
			comboRow.setTaxTotal(detail.getTaxTotal());
			comboRow.setBuyerMessage(detail.getBuyerMessage());
			comboRow.setSellerMemo(detail.getSellerMemo());
			comboRow.setRemark(detail.getRemark());
			comboRow.setTradeOrderDetailId(comboRow.getEshopOrderDetailComboRowId().toString());
			comboRow.setTradeState(order.getLocalTradeState());
			comboRow.setPurchase(new EshopSaleOrderComboPurchaseEntity());
			comboRow.getPurchase().setPurchasePrice(detail.getPurchase().getPurchasePrice());
			comboRow.getPurchase().setPurchaseTotal(detail.getPurchase().getPurchaseTotal());
			comboRow.getPurchase().setPurchaseDisedPrice(detail.getPurchase().getPurchasePrice());
			comboRow.getPurchase().setPurchaseDisedTotal(detail.getPurchase().getPurchaseTotal());
			comboRow.getPurchase().setPurchaseDisedTaxedPrice(detail.getPurchase().getPurchasePrice());
			comboRow.getPurchase().setPurchaseDisedTaxedTotal(detail.getPurchase().getPurchaseTotal());
			comboRow.getPurchase().setProfileId(order.getProfileId());
			comboRow.getPurchase().setEshopOrderId(order.getId());
			comboRow.getPurchase().setEshopOrderDetailId(comboRow.getEshopOrderDetailComboRowId());
			if (order.getDetailCombos() == null) {
				order.setDetailCombos(new ArrayList<>());
			}
			order.getDetailCombos().add(comboRow);
			comboRowIdMap.put(detail.getId(), comboRow.getEshopOrderDetailComboRowId());
		}
	}

	public void calculateOrderPrice(@NotNull EshopSaleOrderEntity order) {
		List<EshopSaleOrderDetail> orderDetails = order.getOrderDetails();
		if (orderDetails == null || orderDetails.size() == 0) {
			return;
		}
		BigDecimal serviceFee = BigDecimal.ZERO;
		BigDecimal taxTotal = BigDecimal.ZERO;
		BigDecimal taxedTotal = BigDecimal.ZERO;
		BigDecimal ptypePreferentialTotal = BigDecimal.ZERO;
		BigDecimal tradeTotal = BigDecimal.ZERO;
		BigDecimal disedTaxedTotal = BigDecimal.ZERO;
		for (EshopSaleOrderDetail detail : orderDetails) {
			if (detail.isComboRow()) {
				continue;
			}
			serviceFee = MoneyUtils.add(detail.getPtypeServiceFee(), serviceFee, Money.Total);
			taxTotal = MoneyUtils.add(detail.getTaxTotal(), taxTotal, Money.Total);
			taxedTotal = MoneyUtils.add(detail.getTotal(), taxedTotal, Money.Total);
			ptypePreferentialTotal = MoneyUtils.add(detail.getPtypePreferentialTotal(), ptypePreferentialTotal, Money.Total);
			tradeTotal = MoneyUtils.add(detail.getTradeTotal(), tradeTotal, Money.Total);
			disedTaxedTotal = MoneyUtils.add(detail.getDisedTaxedTotal(), disedTaxedTotal, Money.Total);
		}
		order.setPtypeServiceFee(serviceFee);
		order.setTaxTotal(taxTotal);
		order.setTaxedTotal(taxedTotal);
		order.setPtypePreferentialTotal(ptypePreferentialTotal);
		order.setDisedTaxedTotal(disedTaxedTotal);
		order.setTradeTotal(tradeTotal);
		BigDecimal payment = order.getCustomerPayment();//MoneyUtils.add(order.getBuyerFreightFee(), order.getDisedTaxedTotal(), Money.Total);
		order.setBuyerTradeTotal(payment);
		if (order.getPayTimeType().equals(PayType.CASH_ON_DELIVERY)) {
			order.setBuyerUnpaidTotal(payment);
		} else {
			order.setBuyerPaidTotal(payment);
		}
		//buildOrderDistribution(order);
	}

	public void buildAndSavePickUpInfo(EshopSaleOrderEntity order) {
		EshopBuyer pickUpInfo = order.getExtend().getSelfPickUpInfo();
		if (pickUpInfo == null) {
			return;
		}
		try {
			pickUpInfo.setBuyerId(UId.newId());
			pickUpInfo.setOtypeId(order.getOtypeId());
			pickUpInfo.setProfileId(order.getProfileId());
			pickUpInfo.setEshopType(order.getShopType());
			pickUpInfo.setTradeId(order.getTradeOrderId());
			buyerService.saveBuyer(pickUpInfo,order.getScene());
			order.getExtend().setPickUpAddressId(pickUpInfo.getBuyerId());
		} catch (Exception ex) {
			String error = String.format(
					"保存自提点信息【%s】报错", pickUpInfo.getCustomerShopAccount());
			logger.error(error, ex);
			throw new RuntimeException(error, ex);
		}
	}


	public void buildAndSaveBuyer(EshopSaleOrderEntity order) {
		EshopBuyer buyer = order.getEshopBuyer();
		if (buyer == null) {
			throw new RuntimeException("订单的EshopBuyer不能为空！");
		}
		try {
			buyer.setBuyerId(UId.newId());
			buyer.setOtypeId(order.getOtypeId());
			buyer.setProfileId(order.getProfileId());
			buyer.setEshopType(order.getShopType());
			buyer.setTradeId(order.getTradeOrderId());
			buyerService.saveBuyer(buyer,order.getScene());
			order.setBuyerId(buyer.getBuyerId());
			order.setReceiveAddressId(buyer.getBuyerId());
			order.getExtend().setRealBuyerId(buyer.getBuyerId());
		} catch (Exception ex) {
			String error = String.format(
					"保存买家信息【%s】报错", buyer.getCustomerShopAccount());
			logger.error(error, ex);
			throw new RuntimeException(error, ex);
		}
	}

	public EshopSaleOrderEntity initCreateOrder(BigInteger btypeId) {
		EshopSaleOrderEntity orderEntity = new EshopSaleOrderEntity();
		//orderEntity.setCreateTime(new Date());
		orderEntity.setModifiedTime(new Date());
		orderEntity.setPayTimeType(PayType.SCENE_SETTLEMENT);
		orderEntity.setDeliverType(BillDeliverType.DELIVER_BY_LOGISTICS);
		BigInteger profileId = CurrentUser.getProfileId();
		BigInteger employeeId = CurrentUser.getEmployeeId();
		//系统配置:开单默认经手人
		if ("1".equals(eshopService.getSysData(profileId, "recordsheetBillEnabledDefaultEtype", "0"))) {
			//优先获取往来单位所属经手人，其次取当前操作员
			if (!(null == btypeId || BigInteger.ZERO.compareTo(btypeId) == 0)){
				Etype etypeByOrderBtype = baseMapper.getEtypeByOrderBtype(profileId, btypeId);
				orderEntity.setEtypeId(etypeByOrderBtype.getId());
				orderEntity.setEfullname(etypeByOrderBtype.getFullname());
				orderEntity.setDtypeId(etypeByOrderBtype.getDtypeId());
				orderEntity.setDfullname(etypeByOrderBtype.getDtypeName());
			}else {
				orderEntity.setEtypeId(employeeId);
				orderEntity.setEfullname(baseMapper.getEtypeById(profileId,employeeId).getFullname());
				Dtype dtypeByEtypeId = baseMapper.getDtypeByEtypeId(profileId, employeeId);
				if (null != dtypeByEtypeId){
					orderEntity.setDtypeId(dtypeByEtypeId.getId());
					orderEntity.setDfullname(dtypeByEtypeId.getFullname());
				}
			}
        }
		return orderEntity;
	}

	public EshopSaleOrderEntity initModifyOrder(BigInteger vchcode) {
		if (vchcode == null || vchcode.longValue() == 0) {
			throw new IllegalArgumentException("未指定需要编辑的订单ID");
		}
		EshopSaleOrderEntity saleOrder = orderDao.querySaleOrderByVchcode(vchcode);
		filterOrderBaseInfo(saleOrder);
		filterOrderDetails(saleOrder);
		return saleOrder;
	}


	private void filterOrderBaseInfo(EshopSaleOrderEntity saleOrder) {
		try {
			BigInteger profileId = saleOrder.getProfileId();
			BigInteger vchcode = saleOrder.getId();
			decryptBuyer(saleOrder);
			saleOrder.setLocalTradeState(TradeStatus.WAIT_SELLER_SEND_GOODS);
			saleOrder.setInvoiceInfo(orderDao.queryOrderInvoice(profileId, vchcode));
			//saleOrder.setSenderInfo(delivererMapper.getSenderById(profileId, delivererId));
		} catch (Exception ex) {
			throw new RuntimeException("构建订单主表报错:" + ex.getMessage(), ex);
		}
	}

	private void filterOrderDetails(EshopSaleOrderEntity saleOrder) {
		try {
			QueryOrderDetailParameter parameter = new QueryOrderDetailParameter();
			parameter.setEshopOrderId(saleOrder.getId());
			List<EshopSaleOrderDetail> orderDetails = orderDao.querySaleOrderDetails(parameter);
			/**
			 * 手工编辑订单不加载已经删除的明细
			 */

			List<EshopSaleDetailCombo> comboRows = orderDao.queryComboRows(parameter);
			buildDetailsNecessarySku(orderDetails,comboRows);
			if (orderDetails == null || orderDetails.isEmpty()) {
				return;
			}
			List<EshopSaleOrderDetail> result = filterOrderDetails(orderDetails, comboRows);
			result=result.stream().filter(x-> x.getOrderDetailMarks().stream().noneMatch(p->p.getMarkCode().equals(new BigInteger("91390003")))).collect(Collectors.toList());
			saleOrder.setOrderDetails(result);
		} catch (Exception ex) {
			throw new RuntimeException("构建订单明显报错:" + ex.getMessage(), ex);
		}
	}

	private void buildDetailsNecessarySku(List<EshopSaleOrderDetail> orderDetails, List<EshopSaleDetailCombo> comboRows) {
		try {
			if (comboRows == null || comboRows.size() == 0 || orderDetails == null || orderDetails.size() == 0) {
				return;
			}
			List<BigInteger> comboIds = comboRows.stream().map(EshopSaleDetailCombo::getComboId).collect(Collectors.toList());
			if (comboIds.size() == 0) {
				return;
			}
			List<ComboDetail> combos = baseMapper.batchQueryComboDetail(CurrentUser.getProfileId(), comboIds);
			if (combos == null || combos.size() == 0) {
				return;
			}
			Map<BigInteger, List<ComboDetail>> comboMap = combos.stream().collect(Collectors.groupingBy(ComboDetail::getComboId));
			Map<BigInteger, List<EshopSaleOrderDetail>> orderDetailMap = orderDetails.stream().filter(x -> x.getComboRowId() != null && x.getComboRowId().compareTo(BigInteger.ZERO) > 0
			).collect(Collectors.groupingBy(EshopSaleOrderDetail::getComboRowId));

			for (EshopSaleDetailCombo itemCombo : comboRows
			) {
				List<ComboDetail> itemComboDetail = comboMap.get(itemCombo.getComboId());
				List<EshopSaleOrderDetail> itemOrderDetails = orderDetailMap.get(itemCombo.getEshopOrderDetailComboRowId());
				for (EshopSaleOrderDetail itemDetail : itemOrderDetails
				) {
					Optional<ComboDetail> comboOption = itemComboDetail.stream().filter(x -> x.getSkuId().equals(itemDetail.getSkuId())
							&& x.getPtypeId().equals(itemDetail.getPtypeId())
							&& x.getUnitId().equals(itemDetail.getUnit())
					).findAny();
					if (!comboOption.isPresent()) {
						continue;
					}
					itemDetail.setNecessarySku(comboOption.get().getNecessarySku());
				}
			}
		} catch (Exception ex) {
			throw new RuntimeException("构建订单明细NecessarySku报错:" + ex.getMessage(), ex);
		}
	}

	private void decryptBuyer(EshopSaleOrderEntity saleOrder) {
		EshopBuyer buyer = buyerService.decryptBuyer(saleOrder.getProfileId(), saleOrder.getBuyerId());
		saleOrder.setEshopBuyer(buyer);
	}

	public void buildOrderExtend(EshopSaleOrderEntity orderEntity) {
		EshopSaleOrderExtendEntity extend = new EshopSaleOrderExtendEntity(orderEntity);
//		extend.setCollectTime(extend.getDefaultCollectTime());
		if (orderEntity.getCashRequired() && orderEntity.getOrderSaleType().equals(TradeTypeEnum.NORMAL)) {
			extend.setPaymentMode(PaymentMode.COD);
		}
		extend.setPurchaseTotal(extend.getPurchaseTotal());
		extend.setHoldTime(extend.getHoldTime());
		orderEntity.setExtend(extend);
	}

	private void buildOrderDetailExtend(EshopSaleOrderDetail orderDetail) {
		EshopSaleOrderDetailExtend extend = new EshopSaleOrderDetailExtend(orderDetail);
		extend.setPurchasePrice(orderDetail.getExtend().getPurchasePrice());
		extend.setPurchaseTotal(orderDetail.getExtend().getPurchaseTotal());
		extend.setId(UId.newId());
        orderDetail.getDisedTaxedTotal();
        extend.setGift(BigDecimal.ZERO.compareTo(orderDetail.getDisedTaxedTotal()) == 0);
		orderDetail.setExtend(extend);
	}

	private void buildAdvanceInfo(EshopSaleOrderEntity order) {
		if (order.getOrderSaleType().equals(TradeTypeEnum.NORMAL)) {
			return;
		}
		BigDecimal tradePayment = order.getCustomerPayment();
		BigDecimal prTotalSettle = order.getPrTotalSettle();
		order.setBuyerTradeTotal(order.getCustomerPayment());
		//需求20865，手工开单构建成已付款
		order.setBuyerPaidTotal(tradePayment);
		order.setBuyerUnpaidTotal(BigDecimal.ZERO);
		order.setLocalTradeState(TradeStatus.WAIT_SELLER_SEND_GOODS);
//		if (tradePayment.compareTo(BigDecimal.ZERO) == 0) {
//			order.setBuyerPaidTotal(BigDecimal.ZERO);
//			order.setBuyerUnpaidTotal(BigDecimal.ZERO);
//			order.setLocalTradeState(TradeStatus.WAIT_SELLER_SEND_GOODS);
//		} else if (prTotalSettle.compareTo(BigDecimal.ZERO) == 0) {
//			order.setLocalTradeState(TradeStatus.WAIT_SELLER_SEND_GOODS);
//			order.setBuyerPaidTotal(BigDecimal.ZERO);
//			order.setBuyerUnpaidTotal(tradePayment);
//		} else if (prTotalSettle.compareTo(tradePayment) < 0) {
//			order.setLocalTradeState(TradeStatus.WAIT_SELLER_SEND_GOODS);
//			order.setBuyerPaidTotal(prTotalSettle);
//			order.setBuyerUnpaidTotal(MoneyUtils.subtract(tradePayment, prTotalSettle, Money.Total));
//		} else if (prTotalSettle.compareTo(tradePayment) == 0) {
//			order.setLocalTradeState(TradeStatus.WAIT_SELLER_SEND_GOODS);
//			order.setBuyerPaidTotal(prTotalSettle);
//			order.setBuyerUnpaidTotal(BigDecimal.ZERO);
//		}
	}

	private boolean rollBackAdvanceInfo(EshopSaleOrderEntity order) {
		order.setLocalTradeState(order.getCustomerPayment().compareTo(BigDecimal.ZERO) == 0 ? TradeStatus.WAIT_SELLER_SEND_GOODS : TradeStatus.WAIT_BUYER_PAY);
		order.setBuyerTradeTotal(order.getCustomerPayment());
		order.setBuyerPaidTotal(BigDecimal.ZERO);
		order.setBuyerUnpaidTotal(order.getCustomerPayment());
		order.setTradePayTime(null);
		return true;
	}

	public void doShareBuyerFreightFee(@NotNull EshopSaleOrderEntity order) {
		EshopSaleOrderBuilder builder = EshopSaleOrderBuilder.getSingleton();
		builder.doShareBuyerFreightFee(order);
	}

	public void buildDeliverType(@NotNull EshopSaleOrderEntity order,Otype otype) {
		EshopSaleOrderBuilder builder = EshopSaleOrderBuilder.getSingleton();
		builder.buildDeliverType(BillDeliverType.DELIVER_BY_LOGISTICS,order);
	}

	public void buildDeliverProcessType(@NotNull EshopSaleOrderEntity order,Otype otype) {
		EshopSaleOrderBuilder builder = EshopSaleOrderBuilder.getSingleton();
		builder.buildOrderDeliverProcessType(order);
	}

	public void BuildOrderMainMark(@NotNull EshopSaleOrderEntity order) {
		EshopSaleOrderBuilder builder = EshopSaleOrderBuilder.getSingleton();
		EshopOrderEntity apiOrder = null;
		if(CollectionUtils.isNotEmpty(order.getMarkDataList())){
			apiOrder = new EshopOrderEntity();
			apiOrder.setMarkDataList(order.getMarkDataList());
		}
		builder.BuildOrderMainMark(apiOrder,order, null);
		builder.buildControllerMark(order, false);
	}

	public void BuildOrderDetailMark(@NotNull EshopSaleOrderDetail detail) {
		EshopSaleOrderBuilder builder = EshopSaleOrderBuilder.getSingleton();
		builder.BuildOrderDetailMark(null,detail,null);
	}

	public Btype getFreightBtypeName(BigInteger profileId, String name) {
		return baseMapper.getFreightBtypeByName(profileId, name);
	}
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopBizMapper">

    <select id="getEshopInfoByOtypeId" parameterType="java.math.BigInteger"
            resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select * from pl_eshop
        <where>
            profile_id = #{profileId} and otype_id = #{otypeId}
        </where>
    </select>

    <select id="getEshopInfoByGroupId" resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select * from pl_eshop
        <where>
            profile_id = #{profileId} and main_eshop=1 and group_id = #{groupId}
        </where>
    </select>
    <select id="getEshopBranchInfoByGroupId" resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select e.*,o.stoped,o.stoped as enabled  from pl_eshop e
        left join base_otype o on o.profile_id=e.profile_id and o.id = e.otype_id
        <where>
            e.profile_id = #{profileId} and e.main_eshop=0 and e.group_id = #{groupId} and e.deleted = 0 and o.deleted = 0
            and  exists (select 1 from pl_eshop_group g where  g.profile_id=#{profileId} and g.group_id=#{groupId})
        </where>
    </select>

    <select id="getEshopBranchInfoByMainOtypeId" resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select e.*,o.stoped,o.stoped as enabled  from pl_eshop e
        left join base_otype o on o.profile_id=e.profile_id and o.id = e.otype_id
        <where>
            e.profile_id = #{profileId} and e.main_eshop=0
            and e.group_id = (select group_id from pl_eshop where profile_id=#{profileId} and otype_id=#{otypeId})
            and e.deleted = 0 and o.deleted = 0
        </where>
    </select>


    <select id="getEshopInfoByFullname" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Otype">
        select * from base_otype
        <where>
            profile_id = #{profileId} and fullname = #{fullname} and deleted = 0
        </where>
    </select>

    <select id="getEshopInfoByFullnameAndShopAccount" resultType="java.lang.Boolean">
        select count(*)>0 from pl_eshop
        <where>
            profile_id = #{profileId} and fullname = #{fullname} and eshop_account = #{shopaccount} and deleted = 0
        </where>
    </select>

    <select id="getOtypeRowindexCount" resultType="java.lang.Boolean">
        select count(*)>0 from base_otype
        <where>
            profile_id = #{profileId} and rowindex = #{rowindex} and deleted = 0
        </where>
    </select>

    <select id="getEshopInfoByShopAccount" resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select * from pl_eshop
        <where>
            profile_id = #{profileId} and eshop_account = #{eshopAccount} and deleted = 0
        </where>
    </select>

    <select id="getOtypeList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Otype">
        SELECT bo.*,'' AS operation, e.eshop_type,e.eshop_type as shopType,e.eshop_account,k.fullname as
        ktypeName,e.token,e.refresh_token,e.token_r1expire_in,
        ifnull(e.refresh_token_expire_in,e.token_expire_in) as refresh_token_expire_in,
        e.eshop_sale_platform,ec.mapping_type,ec.btype_generate_type,
        bb.fullname as btypeName, abb.pr_total as btypePrTotal,asyc.download_begin_time as
        firstDownloadTime,asyc.last_download_end_time as lastDownloadTime,ec.auto_sync_order_enabled as
        autoSync,ec.muti_select_appkey as
        mutiSelectAppkey,e.token,e.token_expire_in,e.online_eshop_id,e.app_key,e.expire_notice,ec.process_type,ec.deliver_process_type,ec.tmc_enabled,
        ec.ptype_auto_upload_enabled,ec.btype_auto_upload_enabled,ec.ktype_auto_upload_enabled,ec.invoice_upload_enabled,ec.platform_eshop_sn_type,
        e.refund_sys_promised_confirm_duration,e.refund_promised_confirm_duration,ec.tmall_special_sale,
        e.refund_promised_agree_duration,e.refund_promised_deliver_duration,e.refund_promised_receive_duration,e.mention_deliver_duration,e.main_eshop,e.group_id,
        asyc.last_download_success_end_time as
        lastDownloadSuccessEndTime,ec.deliver_process_usetype,ec.use_platform_skuId_as_xcode,ec.real_stock_qty_enabled,ec.auto_report_stock_qty_enabled
        FROM base_otype bo
        LEFT JOIN pl_eshop e on e.otype_id = bo.id and e.profile_id = bo.profile_id
        LEFT JOIN pl_eshop_config ec on ec.eshop_id =e.otype_id and ec.profile_id=bo.profile_id
        left join base_ktype k on bo.profile_id=k.profile_id and bo.ktype_id=k.id
        left join base_btype bb on bb.profile_id=bo.profile_id and bb.id=bo.btype_id
        left join acc_btype_balance abb on abb.profile_id=bo.profile_id and abb.btype_id=bo.btype_id
        left join pl_eshop_sale_order_sync_condition asyc on asyc.eshop_id =bo.id and asyc.task_type=0 and
        asyc.profile_id=bo.profile_id
        <if test="param.otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=bo.profile_id and object_type=3 and
            bo.id=bls.object_id and bls.etype_id=#{param.etypeId}
        </if>

        <where>
            bo.profile_id=#{param.profileId} and bo.deleted=0 and bo.classed = 0 and e.fullname is not null
            <if test="list!=null">
                and bo.ocategory in
                <foreach collection="list" index="index" item="list" open="(" separator="," close=")">
                    #{list}
                </foreach>
            </if>
            <if test="param.eshopId!=null and param.eshopId!=0">
                AND e.otype_id =#{param.eshopId}
            </if>
            <if test="param.eshopIds!=null and param.eshopIds.size()>0">
                AND e.otype_id in
                <foreach collection="param.eshopIds" index="index" item="item" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.ocategory!=null and param.ocategory!=@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.OrganizationType@ALL">
                AND bo.ocategory= #{param.ocategory}
            </if>
            <if test="param.deliverProcessType!=null">
                AND ec.deliver_process_type= #{param.deliverProcessType}
            </if>
            <if test="param.fullname!=null and param.fullname!=''">
                AND bo.fullname like CONCAT('%',#{param.fullname},'%')
            </if>
            <if test="param.eshopAccount!=null and param.eshopAccount!=''">
                AND e.eshop_account like CONCAT('%',#{param.eshopAccount},'%')
            </if>
            <if test="param.checkAccountType!=null">
                AND bo.check_account_type = #{param.checkAccountType}
            </if>
            <if test="param.eshopAuthMark!=null and param.eshopAuthMark!=-1 ">
                AND e.is_auth =#{param.eshopAuthMark}
            </if>
            <if test="param.autoSync!=null and param.autoSync!=-1 ">
                AND ec.auto_sync_order_enabled =#{param.autoSync}
            </if>
            <if test="param.tmcEnable!=null and param.tmcEnable!=-1 ">
                AND ec.tmc_enabled =#{param.tmcEnable}
            </if>
            <if test="param.shopTypeInt!=null and param.shopTypeInt>=0">
                AND e.eshop_type = #{param.shopTypeInt}
            </if>
            <if test="param.memo!=null and param.memo!=''">
                AND bo.memo like CONCAT('%',#{param.memo},'%')
            </if>
            <if test="param.deliverDuration!=null">
                AND bo.deliver_duration = #{param.deliverDuration}
            </if>
            <!--            <if test="param.typeid!=null and param.typeid!=''">-->
            <!--                and bo.typeid like CONCAT(#{param.typeid},'%')-->
            <!--            </if>-->
            <if test="param.partypeid!=null and param.partypeid!=''">
                and bo.partypeid like CONCAT(#{param.partypeid},'%')
            </if>

            <if test="param.eshopSalePlatform!=null and param.eshopSalePlatform>=0">
                and e.eshop_sale_platform=#{param.eshopSalePlatform}
            </if>
            <if test="param.platformInfo!=null and param.platformInfo!=''">
                and (e.fullname like "%"#{param.platformInfo}"%" or e.eshop_account like "%"#{param.platformInfo}"%")
            </if>
            <if test="param.noplatform!=null">
                and e.eshop_sale_platform !=#{param.noplatform}
            </if>
            <if test="param.stoped!=null">
                and bo.stoped = #{param.stoped}
            </if>
            <if test="param.shopTypes!=null and param.shopTypes.size()>0">
                and e.eshop_type in
                <foreach collection="param.shopTypes" index="index" item="shopType" open="(" separator=","
                         close=")">
                    #{shopType}
                </foreach>
            </if>
            <if test="param.independentCheck != null">
                and bo.independent_check = #{param.independentCheck}
            </if>
            <if test="param.fullNameFilter != null and param.fullNameFilter !=''">
                and bo.fullname like CONCAT('%',#{param.fullNameFilter},'%')
            </if>
        <if test="param.notAllowShopTypes!=null and param.notAllowShopTypes.size()>0">
            and e.eshop_type not in
            <foreach collection="param.notAllowShopTypes" index="index" item="shopType" open="(" separator=","
                     close=")">
                #{shopType}
            </foreach>
        </if>
        </where>

        <if test="param.orderByCreateTime != null and param.orderByCreateTime">
            ORDER BY e.create_time DESC
        </if>

        <!--        <if test="param.orderField!=null and param.orderField!=''">-->
        <!--            ORDER BY #{param.orderField} DESC-->
        <!--        </if>-->

        <!--        <if test="param.pageSize>0">-->
        <!--            limit #{param.page},#{param.pageSize}-->
        <!--        </if>-->
    </select>

    <select id="getOtypePageListCount" resultType="java.lang.Integer">
        SELECT count(bo.id)
        FROM base_otype bo
        LEFT JOIN pl_eshop e on e.otype_id = bo.id and e.profile_id = bo.profile_id
        LEFT JOIN pl_eshop_config ec on ec.eshop_id =e.otype_id and ec.profile_id=bo.profile_id
        left join base_ktype k on bo.profile_id=k.profile_id and bo.ktype_id=k.id
        left join base_btype bb on bb.profile_id=bo.profile_id and bb.id=bo.btype_id
        left join acc_btype_balance abb on abb.profile_id=bo.profile_id and abb.btype_id=bo.btype_id
        left join pl_eshop_sale_order_sync_condition asyc on asyc.eshop_id =bo.id and asyc.task_type=0 and
        asyc.profile_id=bo.profile_id
        <if test="param.otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=bo.profile_id and object_type=3 and
            bo.id=bls.object_id and bls.etype_id=#{param.etypeId}
        </if>

        <where>
            bo.profile_id=#{param.profileId} and bo.deleted=0 and bo.classed = 0 and e.fullname is not null
            <if test="list!=null">
                and bo.ocategory in
                <foreach collection="list" index="index" item="list" open="(" separator="," close=")">
                    #{list}
                </foreach>
            </if>
            <if test="param.eshopId!=null and param.eshopId!=0">
                AND e.otype_id =#{param.eshopId}
            </if>
            <if test="param.eshopIds!=null">
                <foreach collection="param.eshopIds" index="index" item="item" open=" and e.otype_id in (" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <if test="param.ocategory!=null and param.ocategory!=@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.OrganizationType@ALL">
                AND bo.ocategory= #{param.ocategory}
            </if>
            <if test="param.deliverProcessType!=null">
                AND ec.deliver_process_type= #{param.deliverProcessType}
            </if>
            <if test="param.fullname!=null and param.fullname!=''">
                AND bo.fullname like CONCAT('%',#{param.fullname},'%')
            </if>
            <if test="param.eshopAccount!=null and param.eshopAccount!=''">
                AND e.eshop_account like CONCAT('%',#{param.eshopAccount},'%')
            </if>
            <if test="param.checkAccountType!=null">
                AND bo.check_account_type = #{param.checkAccountType}
            </if>
            <if test="param.eshopAuthMark!=null and param.eshopAuthMark!=-1 ">
                AND e.is_auth =#{param.eshopAuthMark}
            </if>
            <if test="param.autoSync!=null and param.autoSync!=-1 ">
                AND ec.auto_sync_order_enabled =#{param.autoSync}
            </if>
            <if test="param.tmcEnable!=null and param.tmcEnable!=-1 ">
                AND ec.tmc_enabled =#{param.tmcEnable}
            </if>
            <if test="param.shopTypeInt!=null and param.shopTypeInt>=0">
                AND e.eshop_type = #{param.shopTypeInt}
            </if>
            <if test="param.memo!=null and param.memo!=''">
                AND bo.memo like CONCAT('%',#{param.memo},'%')
            </if>
            <if test="param.deliverDuration!=null">
                AND bo.deliver_duration = #{param.deliverDuration}
            </if>
            <if test="param.partypeid!=null and param.partypeid!=''">
                and bo.partypeid like CONCAT(#{param.partypeid},'%')
            </if>

            <if test="param.eshopSalePlatform!=null and param.eshopSalePlatform>=0">
                and e.eshop_sale_platform=#{param.eshopSalePlatform}
            </if>
            <if test="param.platformInfo!=null and param.platformInfo!=''">
                and (e.fullname like "%"#{param.platformInfo}"%" or e.eshop_account like "%"#{param.platformInfo}"%")
            </if>
            <if test="param.noplatform!=null">
                and e.eshop_sale_platform !=#{param.noplatform}
            </if>
            <if test="param.stoped!=null">
                and bo.stoped = #{param.stoped}
            </if>
            <if test="param.shopTypes!=null and param.shopTypes.size()>0">
                and e.eshop_type in
                <foreach collection="param.shopTypes" index="index" item="shopType" open="(" separator=","
                         close=")">
                    #{shopType}
                </foreach>
            </if>
            <if test="param.independentCheck != null">
                and bo.independent_check = #{param.independentCheck}
            </if>
            <if test="param.fullNameFilter != null and param.fullNameFilter !=''">
                and bo.fullname like CONCAT('%',#{param.fullNameFilter},'%')
            </if>
            <if test="param.notAllowShopTypes!=null and param.notAllowShopTypes.size()>0">
                and e.eshop_type not in
                <foreach collection="param.notAllowShopTypes" index="index" item="shopType" open="(" separator=","
                         close=")">
                    #{shopType}
                </foreach>
            </if>
        </where>

        <if test="param.orderByCreateTime != null and param.orderByCreateTime">
            ORDER BY e.create_time DESC
        </if>
    </select>

    <select id="getOtypeListBack" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Otype">
        SELECT bo.*,'' AS operation, e.eshop_type,e.eshop_account,k.fullname as
        ktypeName,e.token_r1expire_in,e.eshop_sale_platform,ec.mapping_type,ec.btype_generate_type,
        bb.fullname as btypeName, abb.pr_total as btypePrTotal,asyc.download_begin_time as
        firstDownloadTime,asyc.last_download_end_time as lastDownloadTime,ec.auto_sync_order_enabled as
        autoSync,e.token_expire_in,ec.muti_select_appkey as mutiSelectAppkey,ec.use_platform_skuId_as_xcode
        FROM base_otype bo
        LEFT JOIN pl_eshop e on e.otype_id = bo.id and e.profile_id = bo.profile_id
        LEFT JOIN pl_eshop_config ec on ec.eshop_id =e.otype_id and ec.profile_id=bo.profile_id
        left join base_ktype k on bo.profile_id=k.profile_id and bo.ktype_id=k.id
        left join base_btype bb on bb.profile_id=bo.profile_id and bb.id=bo.btype_id
        left join acc_btype_balance abb on abb.profile_id=bo.profile_id and abb.btype_id=bo.btype_id
        left join pl_eshop_sale_order_sync_condition asyc on asyc.eshop_id =bo.id and asyc.task_type=0
        <if test="param.otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=bo.profile_id and object_type=3 and
            bo.id=bls.object_id and bls.etype_id=#{param.etypeId}
        </if>

        <where>
            bo.profile_id=#{param.profileId} and bo.deleted=0 and bo.classed = 0 and e.fullname is not null
            <if test="list!=null">
                and bo.ocategory in
                <foreach collection="list" index="index" item="list" open="(" separator="," close=")">
                    #{list}
                </foreach>
            </if>
            <if test="param.ocategory!=null and param.ocategory!=@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.OrganizationType@ALL">
                AND bo.ocategory= #{param.ocategory}
            </if>
            <if test="param.fullname!=null and param.fullname!=''">
                AND bo.fullname like CONCAT('%',#{param.fullname},'%')
            </if>
            <if test="param.eshopAccount!=null and param.eshopAccount!=''">
                AND e.eshop_account like CONCAT('%',#{param.eshopAccount},'%')
            </if>

            <if test="param.checkAccountType!=null">
                AND bo.check_account_type = #{param.checkAccountType}
            </if>
            <if test="param.shopTypeInt!=null and param.shopTypeInt>=0">
                AND e.eshop_type = #{param.shopTypeInt}
            </if>
            <if test="param.memo!=null and param.memo!=''">
                AND bo.memo like "%"#{param.memo}"%"
            </if>
            <if test="param.deliverDuration!=null">
                AND bo.deliver_duration = #{param.deliverDuration}
            </if>
            <!--            <if test="param.typeid!=null and param.typeid!=''">-->
            <!--                and bo.typeid like CONCAT(#{param.typeid},'%')-->
            <!--            </if>-->
            <if test="param.partypeid!=null and param.partypeid!=''">
                and bo.partypeid like CONCAT(#{param.partypeid},'%')
            </if>

            <if test="param.eshopSalePlatform!=null and param.eshopSalePlatform>=0">
                and e.eshop_sale_platform=#{param.eshopSalePlatform}
            </if>
            <if test="param.platformInfo!=null and param.platformInfo!=''">
                and (e.fullname like "%"#{param.platformInfo}"%" or e.eshop_account like "%"#{param.platformInfo}"%")
            </if>
            <if test="param.noplatform!=null">
                and e.eshop_sale_platform !=#{param.noplatform}
            </if>
            <if test="param.stoped!=null">
                and bo.stoped = #{param.stoped}
            </if>
            <if test="param.shopTypes!=null and param.shopTypes.size()>0">
                and e.eshop_type in
                <foreach collection="param.shopTypes" index="index" item="shopType" open="(" separator=","
                         close=")">
                    #{shopType}
                </foreach>
            </if>
            <if test="param.independentCheck != null">
                and bo.independent_check = #{param.independentCheck}
            </if>
        </where>
        order by bo.rowindex desc

        <!--        <if test="param.pageSize>0">-->
        <!--            limit #{param.page},#{param.pageSize}-->
        <!--        </if>-->
    </select>

    <select id="getOtypeListCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM base_otype bo
        LEFT JOIN pl_eshop e on e.otype_id = bo.id and e.profile_id = bo.profile_id
        left join base_ktype k on bo.profile_id=k.profile_id and bo.ktype_id=k.id
        <if test="param.otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=bo.profile_id and object_type=3 and
            bo.id=bls.object_id and bls.etype_id=#{param.etypeId}
        </if>
        <where>
            bo.profile_id=#{param.profileId} and bo.deleted=0 and bo.classed = 0
            <if test="list!=null">
                and bo.ocategory in
                <foreach collection="list" index="index" item="list" open="(" separator="," close=")">
                    #{list}
                </foreach>
            </if>
            <if test="param.ocategory!=null and param.ocategory!=@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.OrganizationType@ALL">
                AND bo.ocategory= #{param.ocategory}
            </if>
            <if test="param.fullname!=null and param.fullname!=''">
                AND bo.fullname like CONCAT('%',#{param.fullname},'%')
            </if>
            <if test="param.checkAccountType!=null">
                AND bo.check_account_type = #{param.checkAccountType}
            </if>
            <if test="param.shopTypeInt!=null and param.shopTypeInt>=0">
                AND e.eshop_type = #{param.shopTypeInt}
            </if>
            <if test="param.memo!=null and param.memo!=''">
                AND bo.memo = #{param.memo}
            </if>
            <if test="param.deliverDuration!=null">
                AND bo.deliver_duration = #{param.deliverDuration}
            </if>
            <!--            <if test="param.typeid!=null and param.typeid!=''">-->
            <!--                and bo.typeid like CONCAT(#{param.typeid},'%')-->
            <!--            </if>-->
            <if test="param.partypeid!=null and param.partypeid!=''">
                and bo.partypeid like CONCAT(#{param.partypeid},'%')
            </if>
            <if test="param.eshopSalePlatform!=null and param.eshopSalePlatform>=0">
                and e.eshop_sale_platform=#{param.eshopSalePlatform}
            </if>
            <if test="param.platformInfo!=null and param.platformInfo!=''">
                and (e.fullname like "%"#{param.platformInfo}"%" or e.eshop_account like "%"#{param.platformInfo}"%")
            </if>
            <if test="param.noplatform!=null">
                and e.eshop_sale_platform !=#{param.noplatform}
            </if>
            <if test="param.stoped!=null">
                and bo.stoped = #{param.stoped}
            </if>
            <if test="param.shopTypes!=null and param.shopTypes.size()>0">
                and e.eshop_type in
                <foreach collection="param.shopTypes" index="index" item="shopType" open="(" separator=","
                         close=")">
                    #{shopType}
                </foreach>
            </if>
            <if test="param.independentCheck != null">
                and bo.independent_check = #{param.independentCheck}
            </if>
        </where>

    </select>

    <select id="getOrganizationById"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Otype">
        SELECT bo.*,
               ''                      AS operation,
               e.eshop_type,
               e.eshop_type            as shopType,
               ec.mapping_type,
               ec.auto_shelf_on,
               ec.btype_generate_type,
               e.deliver_duration,
               e.promised_collect_duration,
               e.promised_sign_duration,
               ec.deliver_process_type as deliverProcessType,
               refund_sys_promised_confirm_duration,
               refund_promised_confirm_duration,
               refund_promised_agree_duration,
               refund_promised_deliver_duration,
               refund_promised_receive_duration,
               mention_deliver_duration,
               e.promised_sync_freight_duration,
               ec.plan_send_time_duration,
               ec.use_price_strategy,
               ec.reissiue_sync_freight
        FROM base_otype bo
                 LEFT JOIN pl_eshop e on e.otype_id = bo.id and e.profile_id = bo.profile_id
                 left JOIN pl_eshop_config ec on ec.eshop_id = e.otype_id and ec.profile_id = bo.profile_id
        where bo.profile_id = #{profileId}
          and bo.id = #{otypeId}
          and bo.deleted = 0
    </select>

    <select id="getOrganizationByBtypeId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Otype">
        SELECT bo.*,
               ''                      AS operation,
               e.eshop_type,
               e.eshop_type            as shopType,
               ec.mapping_type,
               ec.auto_shelf_on,
               ec.btype_generate_type,
               e.deliver_duration,
               e.promised_collect_duration,
               e.promised_sign_duration,
               ec.deliver_process_type as deliverProcessType,
               refund_sys_promised_confirm_duration,
               refund_promised_confirm_duration,
               refund_promised_agree_duration,
               refund_promised_deliver_duration,
               refund_promised_receive_duration,
               mention_deliver_duration,
               e.promised_sync_freight_duration
        FROM base_otype bo
                 LEFT JOIN pl_eshop e on e.otype_id = bo.id and e.profile_id = bo.profile_id
                 left JOIN pl_eshop_config ec on ec.eshop_id = e.otype_id and ec.profile_id = bo.profile_id
        where bo.profile_id = #{profileId}
          and bo.btype_id = #{btypeId}
          and bo.deleted = 0
        <if test="shopType!= null ">
            and e.eshop_type = #{shopType}
        </if>
        limit 1
    </select>

    <select id="getOrganizationByFullName"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Otype">
        SELECT bo.*,
               ''                      AS operation,
               e.eshop_type,
               e.eshop_type            as shopType,
               ec.mapping_type,
               ec.auto_shelf_on,
               ec.btype_generate_type,
               e.deliver_duration,
               e.promised_collect_duration,
               e.promised_sign_duration,
               ec.deliver_process_type as deliverProcessType,
               refund_sys_promised_confirm_duration,
               refund_promised_confirm_duration,
               refund_promised_agree_duration,
               refund_promised_deliver_duration,
               refund_promised_receive_duration,
               mention_deliver_duration,
               e.promised_sync_freight_duration
        FROM base_otype bo
                 LEFT JOIN pl_eshop e on e.otype_id = bo.id and e.profile_id = bo.profile_id
                 left JOIN pl_eshop_config ec on ec.eshop_id = e.otype_id and ec.profile_id = bo.profile_id
        where bo.profile_id = #{profileId}
          and e.fullname = #{fullname}
          and bo.deleted = 0
    </select>

    <select id="getOrganizationByToken"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Otype">
        SELECT bo.*,
               ''                      AS operation,
               e.eshop_type,
               e.eshop_type            as shopType,
               ec.mapping_type,
               ec.auto_shelf_on,
               ec.btype_generate_type,
               e.deliver_duration,
               e.promised_collect_duration,
               e.promised_sign_duration,
               ec.deliver_process_type as deliverProcessType,
               refund_sys_promised_confirm_duration,
               refund_promised_confirm_duration,
               refund_promised_agree_duration,
               refund_promised_deliver_duration,
               refund_promised_receive_duration,
               mention_deliver_duration
        FROM base_otype bo
                 LEFT JOIN pl_eshop e on e.otype_id = bo.id and e.profile_id = bo.profile_id
                 left JOIN pl_eshop_config ec on ec.eshop_id = e.otype_id and ec.profile_id = bo.profile_id
        where bo.profile_id = #{profileId}
          and e.token = #{token}
          and bo.deleted = 0
    </select>

    <select id="queryEShopPageInfo" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.response.EShopPageInfo">
        SELECT e.otype_id,e.profile_id,e.eshop_sale_platform,e.eshop_type,e.fullname,e.eshop_account,e.online_eshop_id,e.
        token,e.app_key,e.app_secret,e.refresh_token,e.token_expire_in,e.token_r1expire_in,e.refresh_token_expire_in,e.
        refresh_time,e.create_time,e.modify_time,e.has_token_expired,e.has_exception,e.deleted,e.update_time,e.
        vendor_id,e.platform_eshop_id,e.last_upload_business_time,e.is_auth,e.expire_notice,e.deliver_duration,e.
        promised_collect_duration,e.promised_sign_duration,e.subscribe_logistics,e.refund_sys_promised_confirm_duration,e.
        refund_promised_confirm_duration,e.refund_promised_agree_duration,e.refund_promised_deliver_duration,e.
        refund_promised_receive_duration,e.mention_deliver_duration,e.promised_sync_freight_duration,e.group_id,e.
        main_eshop,e.check_auth_type,e.take_up_auth_count, bo.usercode,bo.typeid,bo.partypeid, bo.fullname as otypeFullname, bo.ocategory, bo.ktype_id, bo.btype_id, bo.memo,
        bo.atype_id, ec.ag_enabled as agEnabled,ec.tmc_enabled as tmcEnabled,
        bo.currency_id, bo.deliver_duration as deliverDuration, bd.people as senderName, bd.telephone as phone,
        bd.cellphone as mobile ,ec.platform_bats_quality_status,ec.platform_bats_quality_default_info_status,
        bd.address, bd.province, bd.city,ec.platform_quality_status as platformQualityStatus,
        bd.district,bd.street,bd.secret_id as deliverInfoDecryptId,bo.check_account_type,bo.rowindex,bo.independent_check as independentCheck,
        ec.independent_accounting_enabled,ec.mapping_type,ec.btype_generate_type,ec.platform_eshop_sn_type,
        ec.hold_minutes,ec.muti_select_appkey as mutiSelectAppkey,ec.process_type,ec.deliver_process_type as
        sendProcessWay,ec.promised_collect_duration_config,ec.tmall_special_sale,ec.match_local_same_btype_enable,
        ec.ptype_auto_upload_enabled,ec.btype_auto_upload_enabled,ec.ktype_auto_upload_enabled,ec.invoice_upload_enabled,ec.auto_shelf_on,
        e.eshop_type, e.eshop_type as eshopTypeInt,bo.store_type,bo.business_type,ba.fullname as atypeName,ec.store_process_upload,ec.download_order_type as downloadOrderType,
        bk.fullname as ktypeName,ec.process_refund_online,ec.need_message_check,ec.auto_create_btype_enabled as autoCreateBtypeEnabled,
        ec.process_refund_online,ec.need_message_check,ec.deliver_process_usetype,ec.platfrom_config,ec.is_sku_memo_desired,ec.self_delivery_mode,ec.use_platform_skuId_as_xcode,
        ec.plan_send_time_duration,ec.use_price_strategy,ec.reissiue_sync_freight,bo.stoped
        FROM base_otype bo
        LEFT JOIN pl_eshop e ON bo.id=e.otype_id AND bo.profile_id=e.profile_id
        LEFT JOIN pl_eshop_config ec on e.profile_id=ec.profile_id and ec.eshop_id=e.otype_id
        LEFT JOIN base_atype ba on ba.profile_id=bo.profile_id and ba.id=bo.atype_id
        LEFT JOIN base_ktype bk on bk.profile_id=bo.profile_id and bk.id=bo.ktype_id
        left JOIN base_deliveryinfo bd on bd.id=bo.deliver_id and bd.profile_id = bo.profile_id
        <where>
            bo.profile_id=#{param.profileId} and e.deleted=0 and bo.classed = 0 and bo.id=#{param.otypeId}
        </where>
    </select>
    <select id="queryEShopByShopAccount" resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        SELECT e.*, ec.mapping_type
        from pl_eshop e
                 left join pl_eshop_config ec on ec.profile_id = e.profile_id and ec.eshop_id = e.otype_id
        where e.profile_id = #{profileId}
          and e.eshop_account = #{shopAcconut}
          and e.deleted = 0
    </select>

    <select id="queryEShopInfoByOrgId"
            parameterType="java.math.BigInteger"
            resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select otype_id, profile_id, eshop_sale_platform, eshop_type, fullname, eshop_account, online_eshop_id, token,
        app_key, app_secret, refresh_token, token_expire_in, token_r1expire_in, refresh_token_expire_in, refresh_time,
        create_time, modify_time, has_token_expired, has_exception, deleted, stoped, update_time, vendor_id,
        platform_eshop_id, last_upload_business_time, is_auth, expire_notice, deliver_duration,
        promised_collect_duration, promised_sign_duration, subscribe_logistics,main_eshop,
        refund_sys_promised_confirm_duration,refund_promised_confirm_duration,refund_promised_agree_duration,refund_promised_deliver_duration,refund_promised_receive_duration,mention_deliver_duration,promised_sync_freight_duration
        from pl_eshop
        <where>
            profile_id=#{profileId} and otype_id =#{orgId}
        </where>
    </select>
    <select id="queryBranchEshop"
            parameterType="java.math.BigInteger"
            resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select e.*, o.stoped as enabled
        from pl_eshop e
                 left join base_otype o on o.profile_id = e.profile_id and o.id = e.otype_id
        where e.profile_id = #{profileId}
          and main_eshop = 0 and e.deleted = 0 and o.deleted = 0 and o.stoped=0
          and group_id in (select group_id from pl_eshop where profile_id = #{profileId} and otype_id = #{shopId})
        and group_id != ''

    </select>

    <!--update by cw 2020-07-10-->
    <select id="getEshopInfoById"
            resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select
        eshop.*,config.rds_enabled,config.mall_type,config.check_account_type,config.rds_apply_time,config.rds_ready_time,config.mapping_type,config.ag_enabled,config.platform_eshop_sn_type,config.muti_select_appkey
        as mutiSelectAppkey,config.btype_generate_type,config.rds_name,config.sku_memo_mode_state,config.is_sku_memo_desired,config.use_platform_skuId_as_xcode,config.platfrom_config
        from pl_eshop eshop
        left join pl_eshop_config config on config.profile_id=eshop.profile_id and config.eshop_id=eshop.otype_id
        <where>
            eshop.profile_id=#{profileId} and eshop.otype_id =#{shopId}
        </where>
    </select>

    <select id="getEshopInfoByIds"
            resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select
        eshop.*,config.rds_enabled,config.rds_apply_time,config.rds_ready_time,config.mapping_type,config.ag_enabled,config.platform_eshop_sn_type,config.muti_select_appkey
        as mutiSelectAppkey,config.btype_generate_type,config.rds_name,config.use_platform_skuId_as_xcode
        from pl_eshop eshop
        left join pl_eshop_config config on config.profile_id=eshop.profile_id and config.eshop_id=eshop.otype_id
        where eshop.profile_id=#{profileId}
        <if test="shopIds!= null and shopIds.size()>0">
        and eshop.otype_id in
            <foreach collection="shopIds" index="index" item="shopId" open="(" separator="," close=")">
                #{shopId}
            </foreach>
        </if>

    </select>

    <select id="getAllEshopList"
            resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select
        bo.id as
        otypeId,bo.profile_id,bo.classed,eshop.eshop_sale_platform,eshop.eshop_type,bo.fullname,eshop.eshop_account,eshop.online_eshop_id,eshop.token,eshop.app_key,
        eshop.app_secret,eshop.refresh_token,eshop.token_expire_in,eshop.token_r1expire_in,eshop.refresh_token_expire_in,eshop.refresh_time,eshop.create_time,eshop.modify_time,
        eshop.has_token_expired,eshop.has_exception,eshop.deleted,eshop.update_time,eshop.vendor_id,eshop.platform_eshop_id,eshop.refund_sys_promised_confirm_duration,eshop.refund_promised_confirm_duration,
        eshop.refund_promised_agree_duration,eshop.refund_promised_deliver_duration,eshop.refund_promised_receive_duration,eshop.mention_deliver_duration,
        config.muti_select_appkey as mutiSelectAppkey,
        config.rds_enabled,auto_sync_order_enabled,auto_sync_stock_enabled,msg_notify_enabled,ag_enabled,tmc_enabled,bo.ocategory,bo.stoped,eshop.last_upload_business_time,
        config.ptype_auto_upload_enabled,config.btype_auto_upload_enabled,config.ktype_auto_upload_enabled,config.invoice_upload_enabled,config.rds_name,config.use_platform_skuId_as_xcode
        from base_otype bo
        left join pl_eshop eshop on eshop.otype_id= bo.id AND eshop.`profile_id`=bo.`profile_id`
        left join pl_eshop_config config on config.profile_id=bo.profile_id and
        config.eshop_id=bo.id

        <if test="param.otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=bo.profile_id and object_type=3 and
            bo.id=bls.object_id and bls.etype_id=#{param.etypeId}
        </if>
        <where>
            bo.profile_id=#{param.profileId} and bo.deleted =0 and bo.classed = 0
            <if test="otypeId!=null and otypeId>0">
                and bo.id=#{otypeId}
            </if>
            <if test="list!=null">
                and bo.ocategory in
                <foreach collection="list" index="index" item="list" open="(" separator="," close=")">
                    #{list}
                </foreach>
            </if>
            <if test="param.platformTypes!=null and param.platformTypes.size()>0">
                and eshop.eshop_sale_platform in
                <foreach collection="param.platformTypes" index="index" item="platformType" open="(" separator=","
                         close=")">
                    #{platformType}
                </foreach>
            </if>
            <if test="param.queryStop==false">
                and bo.stoped =0
            </if>
            <if test="param.notAllowShopTypes!=null and param.notAllowShopTypes.size()>0">
                and eshop.eshop_type not in
                <foreach collection="param.notAllowShopTypes" index="i" item="curType"  open="(" separator="," close=")">
                    #{curType}
                </foreach>
            </if>

            <if test="param.notAllowOCategorys!=null and param.notAllowOCategorys.size()>0">
                and bo.ocategory not in
                <foreach collection="param.notAllowOCategorys" index="i" item="category"  open="(" separator="," close=")">
                    #{category}
                </foreach>
            </if>
        </where>
        ORDER BY bo.rowindex
    </select>

    <select id="getEshopByShopType" resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select pe.* from pl_eshop pe
        left join base_otype bo on bo.id=pe.otype_id and bo.deleted=0
        <if test="param.otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=bo.profile_id and object_type=3 and
            bo.id=bls.object_id and bls.etype_id=#{param.etypeId}
        </if>
        <where>
            pe.profile_id=#{param.profileId} and pe.eshop_sale_platform=#{shopType} and pe.deleted=0 and bo.ocategory in
            <foreach collection="list" index="index" item="list" open="(" separator="," close=")">
                #{list}
            </foreach>
            <if test="param.queryStop==false">
                and bo.stoped =0
            </if>
        </where>
        ORDER BY bo.rowindex
    </select>
    <select id="getEffectiveJdongEshop" resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select pe.* from pl_eshop pe
        left join base_otype bo on bo.id=pe.otype_id and bo.deleted=0
        <where>
            pe.profile_id=#{param.profileId} and pe.deleted=0 and bo.stoped =0 and pe.token_expire_in > now()
            <if test="param.platformTypes!=null and param.platformTypes.size()>0">
                and pe.eshop_sale_platform in
                <foreach collection="param.platformTypes" index="index" item="platformType" open="(" separator=","
                         close=")">
                    #{platformType}
                </foreach>
            </if>
        </where>
    </select>

    <select id="getEshopByShopTypes" resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select pe.fullname,pe.otype_id as id,pe.otype_id,pe.main_eshop,pe.eshop_sale_platform,pe.eshop_type, bo.stoped
        FROM pl_eshop pe
        LEFT JOIN pl_eshop_config config ON config.profile_id=pe.profile_id AND config.eshop_id=pe.otype_id
        LEFT JOIN base_otype bo ON bo.id= pe.otype_id AND bo.`profile_id`=pe.`profile_id` AND bo.deleted=0
        <if test="param.otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=bo.profile_id and object_type=3 and
            bo.id=bls.object_id and bls.etype_id=#{param.etypeId}
        </if>
        <where>
            pe.profile_id=#{param.profileId} and pe.deleted=0
            <if test="param.platformTypes!=null and param.platformTypes.size()>0">
                and pe.eshop_sale_platform in
                <foreach collection="param.platformTypes" index="index" item="platformType" open="(" separator=","
                         close=")">
                    #{platformType}
                </foreach>
            </if>

            <if test="param.ocategorys!=null and param.ocategorys.size()>0">
                and bo.ocategory in
                <foreach collection="param.ocategorys" index="index" item="ocategory" open="(" separator="," close=")">
                    #{ocategory}
                </foreach>
            </if>

            <if test="param.notAllowOCategorys!=null and param.notAllowOCategorys.size()>0">
                and bo.ocategory not in
                <foreach collection="param.notAllowOCategorys" index="index" item="category" open="(" separator="," close=")">
                    #{category}
                </foreach>
            </if>

            <if test="param.shopTypes!=null and param.shopTypes.size()>0">
                and pe.eshop_type in
                <foreach collection="param.shopTypes" index="index" item="shopType" open="(" separator=","
                         close=")">
                    #{shopType}
                </foreach>
            </if>
            <if test="param.queryStop!=null and param.queryStop==false">
                and bo.stoped =0
            </if>
            <if test="param.shopType!=null">
                and pe.eshop_type = #{param.shopType}
            </if>
            <if test="param.notAllowShopTypes!=null and param.notAllowShopTypes.size()>0">
                and pe.eshop_type not in
                <foreach collection="param.notAllowShopTypes" index="i" item="curType"  open="(" separator="," close=")">
                    #{curType}
                </foreach>
            </if>
            <if test="param.eshopIds!=null and param.eshopIds.size()>0">
                and pe.otype_id in
                <foreach collection="param.eshopIds" index="i" item="id"  open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        ORDER BY bo.rowindex DESC
    </select>


    <insert id="insertEShop">
        INSERT INTO pl_eshop (otype_id, profile_id, eshop_type, fullname, eshop_account, online_eshop_id, token,
                              app_key,
                              app_secret, refresh_token, token_expire_in,
                              refresh_token_expire_in, refresh_time, create_time, has_token_expired, has_exception,
                              deleted,
                              stoped, eshop_sale_platform, vendor_id, platform_eshop_id, token_r1expire_in, is_auth,
                              deliver_duration, promised_collect_duration, promised_sign_duration, subscribe_logistics,
                              refund_sys_promised_confirm_duration, refund_promised_confirm_duration,
                              refund_promised_agree_duration, refund_promised_deliver_duration,
                              refund_promised_receive_duration, mention_deliver_duration,
                              promised_sync_freight_duration,main_eshop,group_id)

        VALUES (#{otypeId}, #{profileId}, #{eshopType}, #{fullname}, #{eshopAccount}, #{onlineEshopId}, #{token},
                #{appKey}, #{appSecret}, #{refreshToken}, #{tokenExpireIn}, #{refreshTokenExpireIn}, #{refreshTime},
                #{createTime}, 0, 0, 0, #{stoped}, #{eshopSalePlatform},
                #{vendorId}, #{platformEshopId}, #{tokenR1ExpireIn}, #{isAuth}, #{deliverDuration},
                #{promisedCollectDuration}, #{promisedSignDuration}, #{subscribeLogistics},
                #{refundSysPromisedConfirmDuration}, #{refundPromisedConfirmDuration}, #{refundPromisedAgreeDuration},
                #{refundPromisedDeliverDuration}, #{refundPromisedReceiveDuration}, #{mentionDeliverDuration},
                #{promisedSyncFreightDuration},#{mainEshop},#{groupId}) ON DUPLICATE KEY
        UPDATE `eshop_type` = #{eshopType},
            `fullname`=#{fullname},
            `eshop_account`=#{eshopAccount},
            `app_key`=#{appKey},
            `app_secret`=#{appSecret},
            `eshop_sale_platform`=#{eshopSalePlatform},
            `deliver_duration`=#{deliverDuration},
            `promised_collect_duration`=#{promisedCollectDuration},
            `promised_sign_duration`=#{promisedSignDuration},
            `subscribe_logistics`=#{subscribeLogistics},
            `refund_sys_promised_confirm_duration`=#{refundSysPromisedConfirmDuration},
            `refund_promised_confirm_duration`=#{refundPromisedConfirmDuration},
            `refund_promised_agree_duration`=#{refundPromisedAgreeDuration},
            `refund_promised_deliver_duration`=#{refundPromisedDeliverDuration},
            `refund_promised_receive_duration`=#{refundPromisedReceiveDuration},
            `mention_deliver_duration`=#{mentionDeliverDuration},
            `main_eshop`=#{mainEshop},
            `group_id`=#{groupId},
            `promised_sync_freight_duration`=#{promisedSyncFreightDuration};
    </insert>

    <update id="updateEShop">
        UPDATE pl_eshop
        <trim prefix="set" suffixOverrides=",">
            eshop_type = #{eshopType},
            fullname = #{fullname},
            eshop_account = #{eshopAccount},
            <if test="appKey!=null and appKey!=''">
                app_key = #{appKey},
            </if>
            <if test="appSecret!=null  and appSecret!=''">
                app_secret = #{appSecret},
            </if>
            <if test="modifyTime!=null">
            modify_time = #{modifyTime},
            </if>
            <if test="eshopSalePlatform!=null">
            eshop_sale_platform = #{eshopSalePlatform},
            </if>
            <if test="isAuth!=null">
            is_auth = #{isAuth},
            </if>
            <if test="deliverDuration!=null">
            `deliver_duration`=#{deliverDuration},
            </if>
            <if test="promisedCollectDuration!=null">
            `promised_collect_duration`=#{promisedCollectDuration},
            </if>
            <if test="promisedSignDuration!=null">
            `promised_sign_duration`=#{promisedSignDuration},
            </if>
            <if test="subscribeLogistics!=null">
            `subscribe_logistics`=#{subscribeLogistics},
            </if>
            <if test="refundSysPromisedConfirmDuration!=null">
            `refund_sys_promised_confirm_duration`=#{refundSysPromisedConfirmDuration},
            </if>
            <if test="refundPromisedConfirmDuration!=null">
            `refund_promised_confirm_duration`=#{refundPromisedConfirmDuration},
            </if>
            <if test="refundPromisedAgreeDuration!=null">
            `refund_promised_agree_duration`=#{refundPromisedAgreeDuration},
            </if>
            <if test="refundPromisedDeliverDuration!=null">
            `refund_promised_deliver_duration`=#{refundPromisedDeliverDuration},
            </if>
            <if test="refundPromisedReceiveDuration!=null">
            `refund_promised_receive_duration`=#{refundPromisedReceiveDuration},
            </if>
            <if test="mentionDeliverDuration!=null and mentionDeliverDuration!=0">
            `mention_deliver_duration`=#{mentionDeliverDuration},
            </if>
            <if test="promisedSyncFreightDuration!=null">
            `promised_sync_freight_duration`=#{promisedSyncFreightDuration},
            </if>
            <if test="tokenR1ExpireIn!=null">
                token_r1expire_in=#{tokenR1ExpireIn},
            </if>
            <if test="tmallSpecialSale!=null">
                token_expire_in=#{tokenExpireIn},
            </if>
            <if test="tokenExpireIn!=null">
                token_expire_in=#{tokenExpireIn},
            </if>
            <if test="token!=null and token!=''">
                token = #{token},
            </if>
            <if test="refreshToken!=null and refreshToken!=''">
                refresh_token = #{refreshToken},
            </if>
            <if test="vendorId!=null and vendorId!=''">
                vendor_id = #{vendorId},
            </if>
            <if test="onlineEshopId!=null and onlineEshopId!=''">
                online_eshop_id = #{onlineEshopId},
            </if>
            <if test="platformEshopId!=null">
                platform_eshop_id = #{platformEshopId},
            </if>
            <if test="lastUploadBusinessTime != null">
                last_upload_business_time = #{lastUploadBusinessTime},
            </if>
        </trim>
        WHERE otype_id = #{otypeId} and profile_id = #{profileId}
    </update>

    <update id="deleteEShop" parameterType="java.math.BigInteger">

        UPDATE pl_eshop
        SET deleted=1
        WHERE otype_id = #{id}
          and profile_id = #{profileId}
    </update>
    <update id="stopEShop" parameterType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        UPDATE pl_eshop
        SET
        <if test="stoped==true">
            stoped=1
        </if>
        <if test="stoped==false">
            stoped=0
        </if>
        WHERE otype_id = #{otypeId} and profile_id=#{profileId} ;
    </update>
    <update id="updateEshopPtypeRefreshTime">
        select 1;
    </update>
    <update id="signEshopPtypeDownload">
        select 1;
    </update>

    <select id="getEshopConfigById" parameterType="java.math.BigInteger"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopConfig">
        select *,c.deliver_process_type as sendProcessWay
        from pl_eshop_config c
        where c.profile_id = #{profileId}
          and c.eshop_id = #{shopId}
    </select>

    <select id="getEshopConfigByIds" parameterType="java.math.BigInteger"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopConfig">
        select *
        from pl_eshop_config
        where profile_id = #{profileId} and eshop_id in
            <foreach collection="shopIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
    </select>

    <select id="getEshopStockSyncDefaultRule" parameterType="java.math.BigInteger"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncRuleBase">
        select *
        from pl_eshop_stock_sync_default_rule
        where profile_id = #{profileId}
          and eshop_id = #{shopId}
    </select>

    <select id="getEshopOrderSyncCondition"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopOrderSyncCondition">
        select *
        from pl_eshop_sale_order_sync_condition
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
    </select>

    <select id="getEshopCondition"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopOrderSyncCondition">
        select *
        from pl_eshop_product_sync_condition
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
    </select>

    <insert id="insertEShopConfig">
        INSERT INTO pl_eshop_config (profile_id, eshop_id, rds_enabled, rds_apply_time, auto_sync_order_enabled,
                                     auto_sync_stock_enabled, msg_notify_enabled, ag_enabled,
                                     tmc_enabled, mall_type, check_account_type, btype_generate_type, mapping_type,
                                     xcode_mapping_required,
                                     independent_accounting_enabled, platform_eshop_sn_type, hold_minutes,
                                     muti_select_appkey, process_type, deliver_process_type,promised_collect_duration_config,tmall_special_sale,is_sku_memo_desired,reissiue_sync_freight)
        VALUES (#{config.profileId}, #{config.eshopId}, 0, null, #{config.autoSyncOrderEnabled},
                #{config.autoSyncStockEnabled}, #{config.msgNotifyEnabled},
                #{config.agEnabled}, #{config.tmcEnabled}, #{config.mallType},
                #{config.checkAccountType}, #{config.btypeGenerateType}, #{config.mappingType},
                #{config.xcodeMappingRequired}, #{config.independentAccountingEnabled},
                #{config.platformEshopSnType}, #{config.holdMinutes}, #{config.mutiSelectAppkey}, ifnull(#{config.processType},0),
                ifnull(#{config.sendProcessWay},0),#{config.promisedCollectDurationConfig},#{config.tmallSpecialSale},#{config.isSkuMemoDesired},#{config.reissiueSyncFreight});
    </insert>

    <insert id="insertEShopConfigCopy">
        INSERT INTO pl_eshop_config (profile_id, eshop_id, rds_enabled, rds_apply_time, auto_sync_order_enabled,
                                     auto_sync_stock_enabled, msg_notify_enabled, ag_enabled,
                                     tmc_enabled, mall_type, check_account_type, btype_generate_type, mapping_type,
                                     xcode_mapping_required,
                                     independent_accounting_enabled, platform_eshop_sn_type, hold_minutes,
                                     muti_select_appkey, process_type, deliver_process_type,promised_collect_duration_config,tmall_special_sale,auto_shelf_on,
                                     platfrom_config,process_refund_online,deliver_process_usetype,auto_create_btype_enabled,is_sku_memo_desired,self_delivery_mode,reissiue_sync_freight)
        VALUES (#{config.profileId}, #{config.eshopId}, 0, null, #{config.autoSyncOrderEnabled},
                #{config.autoSyncStockEnabled}, #{config.msgNotifyEnabled},
                #{config.agEnabled}, #{config.tmcEnabled}, #{config.mallType},
                #{config.checkAccountType}, #{config.btypeGenerateType}, #{config.mappingType},
                #{config.xcodeMappingRequired}, #{config.independentAccountingEnabled},
                #{config.platformEshopSnType}, #{config.holdMinutes}, #{config.mutiSelectAppkey}, ifnull(#{config.processType},0),
                ifnull(#{config.sendProcessWay},0),#{config.promisedCollectDurationConfig},#{config.tmallSpecialSale},#{config.autoShelfOn},
                #{config.platformConfig},#{config.processRefundOnline},#{config.deliverProcessUsetype},#{config.autoCreateBtypeEnabled},#{config.isSkuMemoDesired},#{config.selfDeliveryMode},#{config.reissiueSyncFreight});
    </insert>

    <update id="updateEshopConfig">
        UPDATE pl_eshop_config
        <trim prefix="set" suffixOverrides=",">
            <if test="config.mallType!=null">
            mall_type = #{config.mallType},
            </if>
            promised_collect_duration_config = #{config.promisedCollectDurationConfig},
            <if test="config.checkAccountType!=null">
                check_account_type = #{config.checkAccountType},
            </if>
            <if test="config.btypeGenerateType!=null">
                btype_generate_type = #{config.btypeGenerateType},
            </if>
            <if test="config.mappingType!=null">
                mapping_type = #{config.mappingType},
            </if>
            <if test="config.independentAccountingEnabled!=null">
                independent_accounting_enabled = #{config.independentAccountingEnabled},
            </if>
            <if test="config.agEnabled!=null">
                ag_enabled= #{config.agEnabled},
            </if>
            <if test="config.tmcEnabled!=null">
                tmc_enabled=#{config.tmcEnabled},
            </if>
            <if test="config.sendProcessWay!=null">
                deliver_process_type=#{config.sendProcessWay},
            </if>
            <if test="config.processType!=null">
                process_type=#{config.processType},
            </if>
            <if test="config.xcodeMappingRequired!=null">
                xcode_mapping_required =#{config.xcodeMappingRequired},
            </if>
            <if test="config.platformEshopSnType!=null and config.platformEshopSnType!=''">
                platform_eshop_sn_type = #{config.platformEshopSnType},
            </if>
            <if test="config.holdMinutes!=null">
                hold_minutes = #{config.holdMinutes},
            </if>
            <if test="config.autoCreateBtypeEnabled!=null">
                auto_create_btype_enabled = #{config.autoCreateBtypeEnabled},
            </if>
            <if test="config.isSkuMemoDesired!=null">
                is_sku_memo_desired = #{config.isSkuMemoDesired},
            </if>
            <if test="config.autoShelfOn!=null">
                auto_shelf_on = #{config.autoShelfOn},
            </if>
            <if test="config.autoShelfOn!=null">
                match_local_same_btype_enable=#{config.matchLocalSameBtypeEnable},
            </if>
            <if test="config.processRefundOnline!=null">
                process_refund_online=#{config.processRefundOnline},
            </if>
            <if test="config.needMessageCheck!=null">
                need_message_check=#{config.needMessageCheck},
            </if>
            <if test="config.mutiSelectAppkey!=null">
                muti_select_appkey = #{config.mutiSelectAppkey},
            </if>
            <if test="config.selfDeliveryMode!=null">
                self_delivery_mode = #{config.selfDeliveryMode},
            </if>
            <if test="config.deliverProcessUsetype!=null">
                deliver_process_usetype = #{config.deliverProcessUsetype}
            </if>

        </trim>
        WHERE eshop_id = #{config.eshopId} and profile_id=#{config.profileId};
    </update>
    <update id="updateEshopAccount">
        update pl_eshop
        set eshop_account = #{onlineShopAccount}
        WHERE profile_id= #{profileId} AND otype_id = #{eshopId}
    </update>

    <update id="updateEshopAppKey">
        update pl_eshop
        set app_key = #{appKey}
        WHERE profile_id= #{profileId} AND otype_id = #{eshopId}
    </update>

    <update id="updateEshopToken">
        update pl_eshop
        <trim prefix="set" suffixOverrides=",">
            <if test="appKey!=null and appKey!=''">
                app_key = #{appKey},
            </if>
            <if test="appSecret!=null and appSecret!=''">
                app_secret = #{appSecret},
            </if>
            token= #{token},
            <if test="refreshToken!=null and refreshToken!=''">
                refresh_token = #{refreshToken},
            </if>
            is_auth=#{isAuth},
            expire_notice=0,
            has_token_expired= #{hasTokenExpired},
            <if test="expiresIn!=null">
                token_expire_in = #{expiresIn},
            </if>
            <if test="reExpiresIn!=null">
                refresh_token_expire_in = #{reExpiresIn},
            </if>
            <if test="r1ExpireIn!=null">
                token_r1expire_in = #{r1ExpireIn},
            </if>
            <if test="onlineShopId!=null">
                online_eshop_id = #{onlineShopId},
            </if>
        </trim>
        WHERE profile_id= #{profileId} AND otype_id = #{eshopId}
    </update>

    <update id="updateEshopTokenByShopType">
        update pl_eshop
        <trim prefix="set" suffixOverrides=",">
            app_key = #{appKey},
            app_secret = #{appSecret},
            token= #{token},
            is_auth=#{isAuth},
            expire_notice=0,
            <if test="refreshToken!=null">
                refresh_token = #{refreshToken},
            </if>
            <if test="expiresIn!=null">
                token_expire_in = #{expiresIn},
            </if>
            <if test="reExpiresIn!=null">
                refresh_token_expire_in = #{reExpiresIn},
            </if>
            <if test="r1ExpireIn!=null">
                token_r1expire_in = #{r1ExpireIn},
            </if>
            <if test="onlineShopId!=null">
                online_eshop_id = #{onlineShopId},
            </if>
        </trim>
        WHERE profile_id= #{profileId} and online_eshop_id = #{onlineShopId}
        <if test="shareShopTypes != null and shareShopTypes.size()>0">
            and eshop_type in
            <foreach collection="shareShopTypes" item="shareShopType" index="index" open="(" separator="," close=")">
                #{shareShopType}
            </foreach>
        </if>
    </update>

    <update id="updateRdsStatus">
        UPDATE pl_eshop_config
        SET rds_enabled= #{rdsEnabled},
            rds_apply_time= #{rdsApplyTime},
            rds_ready_time = #{rdsReadyTime}
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
    </update>

    <update id="updateEshopNoticeById">
        UPDATE pl_eshop
        SET expire_notice = #{expireNotice}
        where profile_id = #{profileId}
          and otype_id = #{shopId}
    </update>
    <select id="getAuthrazitedShopCount" resultType="java.lang.Integer">
        select count(1)
        from pl_eshop
        where profile_id = #{param.profileId}
          and deleted = 0
          AND ((eshop_type not in (27, 66) AND (token != '' OR app_secret != '' OR app_key != '')) OR
               (eshop_type in (27, 66) AND token != ''))
          AND eshop_type != 36
          and otype_id != #{param.otypeId}
          AND has_token_expired = false
    </select>
    <select id="queryEshopPageInfoList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.response.EShopPageInfo">
        SELECT e.*, bo.usercode, bo.fullname as otypeFullname, bo.ocategory, bo.ktype_id, bo.btype_id, bo.memo,
        bo.atype_id,
        bo.currency_id, bd.people as senderName, bd.telephone as mobile,
        bd.cellphone as phone , bd.address, bd.province, bd.city,
        bd.district,bd.street,bo.check_account_type,
        bo.rowindex,ec.independent_accounting_enabled,ec.mapping_type
        FROM base_otype bo
        LEFT JOIN pl_eshop e ON bo.id=e.otype_id AND bo.profile_id=e.profile_id
        LEFT JOIN pl_eshop_config ec on e.profile_id=ec.profile_id and ec.eshop_id=e.otype_id
        left JOIN base_deliveryinfo bd on bd.id=bo.deliver_id and bd.profile_id = bo.profile_id
        <where>
            bo.profile_id=#{profileId} and e.deleted=0
        </where>
    </select>

    <update id="cancelAuth" parameterType="java.math.BigInteger">
        UPDATE pl_eshop
        SET token='',
            refresh_token='',
            app_key='',
            app_secret='',
            online_eshop_id='',
            token_expire_in=null,
            token_r1expire_in=null,
            refresh_token_expire_in=null,
            modify_time=now()
        WHERE otype_id = #{otypeId}
          and profile_id = #{profileId};
    </update>

    <update id="updateAuthStatus">
        UPDATE pl_eshop
        SET is_auth = #{isAuth}
        where profile_id = #{profileId}
          and otype_id = #{eshopId}
    </update>

    <update id="encryptTokenInfo">
        UPDATE pl_eshop
        SET token         = #{token},
            refresh_token = #{refreshToken}
        where profile_id = #{profileId}
          and otype_id = #{eshopId}
    </update>

    <update id="cancelEShopAuth">
        UPDATE pl_eshop
        SET token='',refresh_token='',app_key='',app_secret='',online_eshop_id='',
        token_expire_in=null,token_r1expire_in=null,refresh_token_expire_in=null,modify_time=now(),is_auth = 1
        WHERE profile_id=#{params.profileId} and otype_id in
        <foreach collection="params.otypeIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="cancelEShopAuthForEleme">
        UPDATE pl_eshop
        SET token='',refresh_token='',app_key='',app_secret='',
        token_expire_in=null,token_r1expire_in=null,refresh_token_expire_in=null,modify_time=now(),is_auth = 1
        WHERE profile_id=#{params.profileId} and otype_id in
        <foreach collection="params.otypeIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="updateEshopMatchType">
        update pl_eshop_config
        set mapping_type=#{mappingType}
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
    </update>

    <update id="updateEshopRealStockQtyEnabled">
        update pl_eshop_config
        set real_stock_qty_enabled=0,
            auto_report_stock_qty_enabled=0
        where profile_id = #{profileId}
        and eshop_id in
        <foreach collection="eshopIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="updateEshopSkuMemoDesired">
        update pl_eshop_config
        set sku_memo_mode_state=#{skuMemoModeState}
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
    </update>
    <update id="updateEshopConfigSyncEnabled"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopConfig">
        UPDATE pl_eshop_config
        <set>
            auto_sync_stock_enabled=#{autoSyncStockEnabled}
        </set>
        WHERE eshop_id = #{eshopId} and profile_id=#{profileId};
    </update>
    <update id="modifyEshopCondition">
        update pl_eshop_sale_order_sync_condition
        <set>
            <trim prefix="" suffixOverrides=",">
                <if test="lastDownloadBeginTime!=null">
                    last_download_begin_time=#{lastDownloadBeginTime},
                </if>
                <if test="lastDownloadEndTime!=null">
                    last_download_end_time=#{lastDownloadEndTime},
                </if>
                <if test="lastDownloadSuccessBeginTime!=null">
                    last_download_success_begin_time =#{lastDownloadSuccessBeginTime},
                </if>
                <if test="lastDownloadSuccessEndTime!=null">
                    last_download_success_end_time =#{lastDownloadSuccessEndTime},
                </if>
                <if test="lastDownloadFailBeginTime!=null">
                    last_download_fail_begin_time =#{lastDownloadFailBeginTime},
                </if>
                <if test="lastDownloadFailEndTime!=null">
                    last_download_fail_end_time =#{lastDownloadFailEndTime},
                </if>
                <if test="lastDownloadErrorMessage!=null">
                    last_download_error_message=#{lastDownloadErrorMessage},
                </if>
                <if test="lastDownloadExecTime!=null">
                    last_download_exec_time=#{lastDownloadExecTime},
                </if>
                <if test="sliceTime!=null">
                    slice_time = #{sliceTime},
                </if>
                <if test="increaseSliceTime!=null">
                    increase_slice_time = #{increaseSliceTime},
                </if>
                <if test="fullSliceTime!=null">
                    full_slice_time = #{fullSliceTime}
                </if>
            </trim>
        </set>
        where eshop_id=#{eshopId}
        and profile_id=#{profileId}
        and task_type=#{taskType}
    </update>

    <select id="checkDuplicateNameOtype" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Otype">
        select *
        from base_otype o
        left join base_deliveryinfo de on de.profile_id = o.profile_id and de.id = o.deliver_id
        where o.profile_id = #{profileId}
          and o.fullname = #{fullname}
          and o.deleted = 0
          and o.classed = 0
    </select>

    <select id="checkDuplicateOtype" resultType="java.lang.Boolean">
        select count(0) > 0
        from base_otype
        where profile_id = #{pageInfo.profileId}
          and id != #{pageInfo.otypeId}
          and fullname = #{pageInfo.otypeFullname}
          and deleted = 0
          and classed = 0
    </select>

    <select id="getMaxOtypeOrderIndex" resultType="java.math.BigInteger" parameterType="java.math.BigInteger">
        select ifnull(max(rowindex), 0)
        from base_otype
        where profile_id = #{profileId}
    </select>
    <select id="queryEshopStoreTree" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.EshopStoreTreeData">
        select pm.eshop_id                as pid,
               pm.id,
               pm.platform_store_name     as fullname,
               pm.eshop_id,
               pm.profile_id,
               pe.eshop_type,
               pm.platform_store_stock_id as usercode,
               pec.mapping_type           as openXcode,
               pm.type                    as warehouseType
        from pl_eshop_platform_store_mapping pm
                 left join pl_eshop pe on pe.profile_id = pm.profile_id and pe.otype_id = pm.eshop_id
                 left join pl_eshop_config pec on pec.profile_id = pm.profile_id and pec.eshop_id = pm.eshop_id
        WHERE pm.profile_id = #{profileId}
          and pm.eshop_id = #{otypeId}
          and pe.deleted = 0
          and pe.stoped = 0
          and pe.deleted = 0
          and pe.stoped = 0
          and pm.deleted = 0
    </select>

    <select id="queryEshopStoreTreeNew" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.EshopStoreTreeData">
        select 0 as pid, e.otype_id as id, e.fullname, e.otype_id as eshopId, e.profile_id, e.eshop_type, '' as
        usercode,ec.mapping_type as openXcode
        FROM base_otype bo
        LEFT JOIN pl_eshop e on e.otype_id = bo.id and e.profile_id = bo.profile_id
        LEFT JOIN pl_eshop_config ec on ec.eshop_id =e.otype_id and ec.profile_id=bo.profile_id
        <if test="param.otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=bo.profile_id and object_type=3 and
            bo.id=bls.object_id and bls.etype_id=#{param.etypeId}
        </if>
        where
        bo.profile_id=#{param.profileId} and bo.deleted=0 and bo.stoped=0 and bo.classed = 0
        <if test="list!=null">
            and bo.ocategory in
            <foreach collection="list" index="index" item="list" open="(" separator="," close=")">
                #{list}
            </foreach>
        </if>
        order by rowindex
    </select>


    <delete id="deleteEShops" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.EshopParameter">
        update pl_eshop set deleted=1 where profile_id=#{params.profileId} and
        otype_id in
        <foreach collection="params.otypeIds" index="index" item="item" open="(" separator="," close=")">
            #{item}

        </foreach>
    </delete>


    <select id="getEshopIsAuth" resultType="integer">
        SELECT count(1)
        from pl_eshop
        where (LENGTH(trim(app_key)) > 0 or LENGTH(trim(token)) > 0 or LENGTH(trim(app_secret)) > 0)
          and otype_id = #{otypeId}
          and now() &lt;= token_r1expire_in
    </select>

    <select id="getEshopIsAuthByExpireTime" resultType="boolean">
        SELECT count(1) > 0
        from pl_eshop
        where otype_id = #{otypeId}
          and profile_id = #{profielId}
          and now() &lt;= token_expire_in
    </select>


    <select id="getEshopIsAuthByToken" resultType="boolean">
        SELECT count(1) > 0
        from pl_eshop
        where otype_id = #{otypeId}
          and profile_id = #{profielId}
          and token != ''
    </select>

    <select id="getEshopIsAuthByOnlineShopId" resultType="boolean">
        SELECT count(1) > 0
        from pl_eshop
        where otype_id = #{otypeId}
          and profile_id = #{profielId}
          and online_eshop_id != ''
    </select>

    <select id="getEshopIsAuthByAppkey" resultType="boolean">
        SELECT count(1) > 0
        from pl_eshop
        where otype_id = #{otypeId}
          and profile_id = #{profielId}
          and app_key != ''
    </select>


    <update id="updateEshopAutoOrder">
        update pl_eshop_config
        set auto_sync_order_enabled=#{auto}
        where profile_id = #{profielId}
          and eshop_id = #{eshopId}
    </update>

    <select id="getEshopConfig" resultType="integer">
        select auto_sync_order_enabled
        from pl_eshop_config
        where profile_id = #{profielId}
          and eshop_id = #{eshopId}
    </select>

    <insert id="insertOrderSync">
        insert into pl_eshop_sale_order_sync_condition (id, profile_id, eshop_id, download_begin_time, task_type)
        values (#{id}, #{profielId}, #{eshopId}, #{beginTime}, #{type})
    </insert>

    <select id="getCountOrderSync" resultType="integer">
        SELECT count(1)
        from pl_eshop_sale_order_sync_condition
        where eshop_id = #{eshopId}
          and profile_id = #{profielId}
    </select>

    <update id="updateOrderSync">
        UPDATE pl_eshop_sale_order_sync_condition
        set download_begin_time=#{beginTime}
        where eshop_id = #{eshopId}
          and profile_id = #{profielId}
    </update>

    <update id="updateOrderSyncSliceTime">
        UPDATE pl_eshop_sale_order_sync_condition
        set slice_time=#{sliceTime},
            increase_slice_time=#{sliceTime},
            full_slice_time=#{sliceTime}
        where eshop_id = #{eshopId}
          and profile_id = #{profielId}
          and task_type = 0
    </update>

    <select id="checkEshopQuoted" resultType="boolean"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.EshopQuoteRequest">
        select count(0)>0 from ${request.tableName} where profile_id=#{request.profileId}
        <if test="request.checkType!=null and request.checkType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.EshopQuoteCheckType@OTYPEID">
            and otype_id=#{request.otypeId}
        </if>
        <if test="request.checkType!=null and request.checkType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.EshopQuoteCheckType@ESHOPID">
            and eshop_id=#{request.otypeId}
        </if>
    </select>

    <select id="checkPtypeAndEshop" resultType="boolean"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.EshopQuoteRequest">
        select count(0) > 0
        from pl_eshop_product_sku_mapping
        where eshop_id = #{request.otypeId}
          and profile_id = #{request.profileId}
          and ptype_id != 0
    </select>
    <select id="getpifaOrgList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Otype">
        SELECT bo.*,'' AS operation, e.eshop_type,e.eshop_account,k.fullname as
        ktypeName,e.token_r1expire_in,e.eshop_sale_platform,ec.mapping_type,ec.btype_generate_type,
        bb.fullname as btypeName, abb.pr_total as btypePrTotal,asyc.download_begin_time as firstDownloadTime
        FROM base_otype bo
        LEFT JOIN pl_eshop e on e.otype_id = bo.id and e.profile_id = bo.profile_id
        LEFT JOIN pl_eshop_config ec on ec.eshop_id =e.otype_id and ec.profile_id=bo.profile_id
        left join base_ktype k on bo.profile_id=k.profile_id and bo.ktype_id=k.id
        left join base_btype bb on bb.profile_id=bo.profile_id and bb.id=bo.btype_id
        left join acc_btype_balance abb on abb.profile_id=bo.profile_id and abb.btype_id=bo.btype_id
        left join pl_eshop_sale_order_sync_condition asyc on asyc.eshop_id =bo.id and asyc.task_type=0
        <if test="param.otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=bo.profile_id and object_type=3 and
            bo.id=bls.object_id and bls.etype_id=#{param.etypeId}
        </if>
        <where>
            bo.profile_id=#{param.profileId} and bo.deleted=0
            <if test="list!=null">
                and bo.ocategory in
                <foreach collection="list" index="index" item="list" open="(" separator="," close=")">
                    #{list}
                </foreach>
            </if>
            <if test="param.ocategory!=null and param.ocategory!=@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.OrganizationType@ALL">
                AND bo.ocategory= #{param.ocategory}
            </if>
            <if test="param.fullname!=null and param.fullname!=''">
                AND bo.fullname like CONCAT('%',#{param.fullname},'%')
            </if>
            <if test="param.checkAccountType!=null">
                AND bo.check_account_type = #{param.checkAccountType}
            </if>
            <if test="param.shopTypeInt!=null and param.shopTypeInt>=0">
                AND e.eshop_type = #{param.shopTypeInt}
            </if>
            <if test="param.memo!=null and param.memo!=''">
                AND bo.memo like "%"#{param.memo}"%"
            </if>
            <if test="param.deliverDuration!=null">
                AND bo.deliver_duration = #{param.deliverDuration}
            </if>
            <if test="param.partypeid!=null and param.partypeid!=''">
                and bo.partypeid like CONCAT(#{param.partypeid},'%')
            </if>
            <if test="param.eshopSalePlatform!=null and param.eshopSalePlatform>=0">
                and e.eshop_sale_platform=#{param.eshopSalePlatform}
            </if>
            <if test="param.platformInfo!=null and param.platformInfo!=''">
                and (e.fullname like "%"#{param.platformInfo}"%" or e.eshop_account like "%"#{param.platformInfo}"%")
            </if>

        </where>
        <if test="param.orderField!=null and param.orderField!=''">
            ORDER BY ${param.orderField}
        </if>

        <if test="param.pageSize>0">
            limit #{param.page},#{param.pageSize}
        </if>
    </select>

    <select id="getPifaOrgListCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM base_otype bo
        LEFT JOIN pl_eshop e on e.otype_id = bo.id and e.profile_id = bo.profile_id
        left join base_ktype k on bo.profile_id=k.profile_id and bo.ktype_id=k.id
        <if test="param.otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=bo.profile_id and object_type=3 and
            bo.id=bls.object_id and bls.etype_id=#{param.etypeId}
        </if>
        <where>
            bo.profile_id=#{param.profileId} and bo.deleted=0
            <if test="list!=null">
                and bo.ocategory in
                <foreach collection="list" index="index" item="list" open="(" separator="," close=")">
                    #{list}
                </foreach>
            </if>
            <if test="param.ocategory!=null and param.ocategory!=@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.OrganizationType@ALL">
                AND bo.ocategory= #{param.ocategory}
            </if>
            <if test="param.fullname!=null and param.fullname!=''">
                AND bo.fullname like CONCAT('%',#{param.fullname},'%')
            </if>
            <if test="param.checkAccountType!=null">
                AND bo.check_account_type = #{param.checkAccountType}
            </if>
            <if test="param.shopTypeInt!=null and param.shopTypeInt>=0">
                AND e.eshop_type = #{param.shopTypeInt}
            </if>
            <if test="param.memo!=null and param.memo!=''">
                AND bo.memo = #{param.memo}
            </if>
            <if test="param.deliverDuration!=null">
                AND bo.deliver_duration = #{param.deliverDuration}
            </if>
            <if test="param.partypeid!=null and param.partypeid!=''">
                and bo.partypeid like CONCAT(#{param.partypeid},'%')
            </if>
            <if test="param.eshopSalePlatform!=null and param.eshopSalePlatform>=0">
                and e.eshop_sale_platform=#{param.eshopSalePlatform}
            </if>

        </where>

    </select>
    <select id="queryEshopPageInfoListLimit"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.response.EShopPageInfo">
        SELECT e.*, bo.usercode, bo.fullname as otypeFullname, bo.ocategory, bo.ktype_id, bo.btype_id, bo.memo,
        bo.atype_id,
        bo.currency_id,bd.people as senderName, bd.telephone as mobile, bd.cellphone as phone ,
        bd.address, bd.province, bd.city,
        bd.district,bd.street,bo.check_account_type,
        bo.rowindex,ec.independent_accounting_enabled,ec.mapping_type,epsc.auto_refresh_product_enabled,ec.auto_save_mapping_relation
        FROM base_otype bo
        LEFT JOIN pl_eshop e ON bo.id=e.otype_id AND bo.profile_id=e.profile_id
        LEFT JOIN pl_eshop_config ec on e.profile_id=ec.profile_id and ec.eshop_id=e.otype_id
        LEFT JOIN pl_eshop_product_sync_condition epsc on epsc.profile_id=e.profile_id and epsc.eshop_id=e.otype_id
        left JOIN base_deliveryinfo bd on bd.id=bo.deliver_id and bd.profile_id = bo.profile_id
        <if test="param.otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=e.profile_id and object_type=3 and
            bo.id=bls.object_id and bls.etype_id=#{param.etypeId}
        </if>
        <where>
            bo.profile_id=#{param.profileId} and e.deleted=0 and bo.stoped=0 and bo.deleted=0
            <if test="param.notAllowOCategorys!=null and param.notAllowOCategorys.size()>0">
                and bo.ocategory not in
                <foreach collection="param.notAllowOCategorys" index="index" item="list" open="(" separator="," close=")">
                    #{list}
                </foreach>
            </if>
        </where>
        ORDER BY rowindex
    </select>


    <select id="checkBtypeIn" resultType="boolean">
        SELECT count(1) > 0
        from base_ktype
        where profile_id = #{profielId}
          and id = #{ktypeId}
          and deleted = 0
          and stoped = 0
    </select>
    <select id="getStockRuleAllEshopList" resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select
        eshop.*,rule.ktype_ids,rule.rule_cron,
        config.rds_enabled,auto_sync_order_enabled,auto_sync_stock_enabled,msg_notify_enabled,ag_enabled,tmc_enabled,bo.ocategory
        from pl_eshop eshop
        left join pl_eshop_config config on config.profile_id=eshop.profile_id and
        config.eshop_id=eshop.otype_id
        left join base_otype bo on bo.id= eshop.otype_id AND bo.`profile_id`=eshop.`profile_id` and bo.deleted=0 and
        bo.stoped=0
        left join `pl_eshop_stock_sync_default_rule` rule on rule.eshop_id=eshop.otype_id and
        rule.profile_id=eshop.profile_id
        <if test="param.otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=bo.profile_id and object_type=3 and
            bo.id=bls.object_id and bls.etype_id=#{param.etypeId}
        </if>
        <where>
            eshop.profile_id=#{param.profileId} and eshop.deleted =0
            <if test="otypeId!=null and otypeId>0">
                and eshop.otype_id=#{otypeId}
            </if>
            <if test="param.syncStockType>-1">
                and config.auto_sync_stock_enabled=#{param.syncStockType}
            </if>
            <if test="param.ktypeIds!=null and param.ktypeIds.size()>0">
                and(rule.ktype_ids='' OR rule.`ktype_ids` IS NULL
                <foreach item="item" index="index" open="or" collection="param.ktypeIds" separator="or">
                    FIND_IN_SET(#{item},rule.ktype_ids)
                </foreach>
                )
            </if>
            and bo.ocategory in (0,2)
        </where>
        order by bo.rowindex
    </select>

    <select id="getMorenEshopId" resultType="java.math.BigInteger">
        select id
        from base_otype
        where fullname = '默认网店'
          and profile_id = #{profielId}
    </select>

    <select id="getEshopByFullname" resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select *
        from pl_eshop
        where fullname = #{fullname}
          and profile_id = #{profileId}
    </select>

    <update id="updatePlEshopConfig">
        update pl_eshop_config
        set btype_generate_type = #{btypeGenerateType}
        where eshop_id = #{id}
          and profile_id = #{profileId}
    </update>

    <update id="updatePlEshopConfigByWhere">
        update pl_eshop_config
        set auto_refresh_product_enabled = #{autoRefreshProductEnabled}
        where eshop_id = #{eshopId}
          and profile_id = #{profielId}
    </update>

    <update id="updateProductStatus">
        update pl_eshop_product set platform_stock_state = #{status} where eshop_id = #{eshopId} and
        profile_id=#{profielId}
        <if test="numidList!=null and numidList.size()>0">
            and platform_num_id in
            <foreach collection="numidList" index="index" item="numid" open="(" separator="," close=")">
                #{numid}
            </foreach>
        </if>
    </update>
    <update id="updatePlEshopConfigAG">
        update pl_eshop_config
        set ag_enabled = #{agEnabled}
        where eshop_id = #{id}
          and profile_id = #{profileId}
    </update>
    <update id="moveTop">
        update base_otype
         set rowindex = (select v.m
                        from (select if(max(rowindex) = 0, 1, max(rowindex) + 1) as m
                              from base_otype
                              where profile_id = #{profileId}) v)
        where id = #{id}
    </update>
    <update id="moveBottom">
        update base_otype
        set rowindex = (select v.m
                        from (select if(min(rowindex) = 0, 1, min(rowindex) - 1) as m
                              from base_otype
                              where profile_id = #{profileId}) v)
        where id = #{id}
    </update>

    <update id="updateRowIndex">
        update base_otype
        set rowindex = #{arg1}
        where id = #{arg0}
    </update>
    <update id="updateEShopBusinessUpdateTime">
        UPDATE pl_eshop
        <trim prefix="set" suffixOverrides=",">
            modify_time = #{modifyTime},
            <if test="lastUploadBusinessTime != null">
                last_upload_business_time = #{lastUploadBusinessTime},
            </if>
        </trim>
        WHERE otype_id = #{otypeId} and profile_id = #{profileId}
    </update>
    <update id="updateTmcStatus">
        UPDATE pl_eshop_config
        SET tmc_enabled= #{tmcEnabled}
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
    </update>
    <update id="updateEshopPageConfig">
        UPDATE pl_eshop_config
        set ptype_auto_upload_enabled=#{config.ptypeAutoUploadEnabled},
        btype_auto_upload_enabled=#{config.btypeAutoUploadEnabled},
        ktype_auto_upload_enabled=#{config.ktypeAutoUploadEnabled},
        auto_shelf_on=#{config.autoShelfOn},
        ag_enabled=#{config.agEnabled},
        promised_collect_duration_config=#{config.promisedCollectDurationConfig},
        invoice_upload_enabled=#{config.invoiceUploadEnabled},
        store_process_upload=#{config.storeProcessUpload},
        process_refund_online=#{config.processRefundOnline},
        need_message_check=#{config.needMessageCheck},
        deliver_process_type=#{config.sendProcessWay},
        deliver_process_usetype=#{config.deliverProcessUsetype},
        is_sku_memo_desired = #{config.isSkuMemoDesired},
        auto_create_btype_enabled=#{config.autoCreateBtypeEnabled},
        store_process_upload=#{config.storeProcessUpload},
        platfrom_config=#{config.platformConfig},
        download_order_type=#{config.downloadOrderType},
        match_local_same_btype_enable=#{config.matchLocalSameBtypeEnable},
        <if test="config.skuMemoModeState!= null">
            sku_memo_mode_state=#{config.skuMemoModeState},
        </if>
            self_delivery_mode=#{config.selfDeliveryMode},
            tmall_special_sale=#{config.tmallSpecialSale},
        platform_bats_quality_status=#{config.platformBatsQualityStatus},
        platform_bats_quality_default_info_status=#{config.platformBatsQualityDefaultInfoStatus},
        use_price_strategy = #{config.usePriceStrategy},
        platform_quality_status=#{config.platformQualityStatus},
        plan_send_time_duration=#{config.planSendTimeDuration},
        reissiue_sync_freight = #{config.reissiueSyncFreight}
        WHERE eshop_id = #{config.eshopId}
        and profile_id = #{config.profileId};
    </update>


    <select id="getEshopInfoByEshopIdList"
            resultType="com.wsgjp.ct.common.enums.core.enums.ShopType">
        select eshop_type from pl_eshop eshop
        <where>
            profile_id=#{profileId}
            <if test="otypeList!=null and otypeList.size()>0">
                and otype_id in
                <foreach collection="otypeList" index="index" item="otypeId" open="(" separator="," close=")">
                    #{otypeId}
                </foreach>
                group by eshop_type
            </if>
        </where>
    </select>
    <select id="getSameAuthShopCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM pl_eshop
        WHERE profile_id = #{profileId}
          AND eshop_type = #{shopType}
          AND online_eshop_id = #{onlineShopId}
          AND otype_id != #{eshopId}
          and deleted = 0
          and stoped = 0
    </select>
    <select id="getFirstOtype" resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select pe.*, ec.mapping_type
        from base_otype bo
        left join pl_eshop pe on pe.profile_id = bo.profile_id and pe.otype_id = bo.id
        left join pl_eshop_config ec on bo.profile_id = ec.profile_id and bo.id = ec.eshop_id
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=bo.profile_id and object_type=3 and
            bo.id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where bo.profile_id = #{profileId}
        and bo.deleted = 0
        and bo.stoped = 0
        and bo.classed = 0
        and bo.ocategory in (0, 1, 2)
        <if test="platformTypes!=null and platformTypes.size()>0">
            and pe.eshop_sale_platform in
            <foreach collection="platformTypes" index="index" item="platformType" open="(" separator="," close=")">
                #{platformType}
            </foreach>
        </if>
        order by bo.rowindex
        limit 1
    </select>
    <select id="getEshopExcludeEshop" resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select *
        from pl_eshop
        where profile_id = #{profileId}
          and app_secret = #{appSecret}
          and otype_id != #{otypeId}
          and deleted = 0
          and stoped = 0
    </select>
    <select id="getSysData" resultType="java.lang.String">
        select sub_value from sys_data
        <where>
            profile_id = #{profileId}
            and
            sub_name =#{subName}
        </where>
    </select>
    <update id="modifySysData">
        update sys_data set sub_value = #{subValue}
        <where>
            profile_id = #{profileId}
            and
            sub_name =#{subName}
        </where>
    </update>
    <insert id="addSysData">
        insert into sys_data (id, profile_id, sub_name, sub_value, description)
        values (#{id}, #{profileId}, #{subName}, #{subValue}, #{description})
    </insert>
    <insert id="saveEshopGroup">
        insert into pl_eshop_group (id, profile_id, group_id, chain_store_fullname, store_id, group_type, auth_type,
                                   use_par_product_relation)
        values (#{id}, #{profileId}, #{groupId}, #{chainStoreFullname}, #{storeId}, #{groupType}, #{authType},
                #{useParProductRelation})
    </insert>
    <select id="checkGenerateAccounting" resultType="java.lang.Boolean">
        select count(1) > 0
        from acc_postbill_core
        where profile_id = #{profileId}
          and otype_id = #{otypeId}
    </select>
    <select id="getOrgParTypeList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Otype">
        select
        fullname,id,typeid,partypeid
        from base_otype
        <where>
            profile_id = #{profileId}
            AND classed = 1
            and deleted = 0
        </where>
    </select>

    <select id="getEshopByStandardApi"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.response.standardApi.otype.OtypeStandardApiResponse">
        select bo.id as otypeId, bo.fullname as fullname, bo.stoped as stopped
        from base_otype bo
                 left join pl_eshop pe on bo.profile_id = pe.profile_id and bo.id = pe.otype_id
        where bo.deleted = 0
          and pe.eshop_type = 999
          and bo.profile_id = #{profileId}
    </select>
    <select id="getRowIndex" resultType="java.math.BigInteger">
        select rowindex
        from base_otype
        where id = #{arg0}
    </select>
    <select id="stoppedEshopByStandardApi" resultType="java.lang.Boolean">
        select ifnull(stoped, true)
        from base_otype
        where profile_id = #{profileId}
          and id = #{otypeId}
          and deleted = 0
    </select>

    <select id="queryEshopByOnlineEshopIdAndShopType" resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select * from pl_eshop
        where profile_id = #{eshopInfo.profileId}
        and deleted = 0
        <if test="shopTypes != null">
            and eshop_type in
            <foreach item="shopType" collection="shopTypes" index="index" open="(" separator="," close=")">
                #{shopType}
            </foreach>
        </if>
        <if test="shopTypes == null">
            and eshop_type = #{eshopInfo.eshopType}
        </if>
        <if test="eshopInfo.onlineEshopId != null and eshopInfo.onlineEshopId !=''">
            and online_eshop_id = #{eshopInfo.onlineEshopId}
        </if>
        <if test="eshopInfo.onlineEshopId == null or eshopInfo.onlineEshopId ==''">
            and app_secret = #{eshopInfo.appSecret}
        </if>
    </select>

    <select id="queryEshopByOnlineEshopId" resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select * from pl_eshop
        where profile_id = #{profileId}
          and deleted = 0
          and online_eshop_id = #{onlineEshopId}
    </select>


    <select id="queryEshopByOnlineFullNameAndShopType" resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select * from pl_eshop
        where profile_id = #{eshopInfo.profileId}
        and deleted = 0
        <if test="shopTypes != null">
            and eshop_type in
            <foreach item="shopType" collection="shopTypes" index="index" open="(" separator="," close=")">
                #{shopType}
            </foreach>
        </if>
        <if test="shopTypes == null">
            and eshop_type = #{eshopInfo.eshopType}
        </if>
        and fullname = #{eshopInfo.fullname}
    </select>

    <select id="queryStrategy" resultType="com.wsgjp.ct.sale.biz.jarvis.entity.strategy.StrategyConfigBaseEntity">
        SELECT main.*, detail.strategy_value
        FROM td_deliver_strategy main
                 LEFT JOIN td_deliver_strategy_detail detail
                           ON main.`profile_id` = detail.`profile_id` AND main.id = detail.`strategy_id`
        WHERE main.profile_id = 780100252278579201
          AND parent_id = 0
          AND strategy_type = 2
    </select>
    <select id="getProcessType" resultType="com.wsgjp.ct.bill.core.handle.entity.enums.DeliverProcessTypeEnum">
        select deliver_process_type
        from pl_eshop eshop
                 left join pl_eshop_config config
                           on eshop.profile_id = config.profile_id and eshop.otype_id = config.eshop_id
        where eshop.otype_id = #{otypeId}
          and eshop.profile_id = #{profileId}
    </select>

    <select id="queryEshopTypeById" resultType="com.wsgjp.ct.common.enums.core.enums.ShopType">
        select eshop_type
        from pl_eshop
        where profile_id = #{profileId}
          and otype_id = #{shopId}
    </select>
    <select id="queryEshopStoreForWarehouseStock"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.EshopStoreTreeData">
        select pm.eshop_id                as pid,
               pm.id,
               pm.platform_store_name     as fullname,
               pm.eshop_id,
               pm.profile_id,
               pe.eshop_type,
               pm.platform_store_stock_id as usercode,
               pec.mapping_type           as openXcode,
               pm.type                    as warehouseType
        from pl_eshop_platform_store_mapping pm
                 left join pl_eshop pe on pe.profile_id = pm.profile_id and pe.otype_id = pm.eshop_id
                 left join pl_eshop_config pec on pec.profile_id = pm.profile_id and pec.eshop_id = pm.eshop_id
        WHERE pm.profile_id = #{profileId}
          and pm.eshop_id = #{otypeId}
          and pe.deleted = 0
          and pe.stoped = 0
          and pe.deleted = 0
          and pe.stoped = 0
          and (pm.type = 0 or (pm.type = 1 and pm.platform_store_type = 3))
          and pm.deleted = 0
    </select>

    <select id="getOtypeListForBiz" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Otype">
        select profile_id,
               id,
               usercode,
               fullname,
               ocategory,
               store_type,
               business_type,
               ktype_id,
               btype_id,
               check_account_type,
               currency_id,
               atype_id
        from base_otype
        where profile_id = #{profileId}
          and deleted = 0
          and classed = 0
          and stoped = 0
          and ocategory in (0, 1, 2, 3, 6)
    </select>

    <select id="getAllEshopInfo" resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select otype_id,
               profile_id,
               eshop_sale_platform,
               eshop_type,
               fullname,
               eshop_account,
               online_eshop_id,
               token,
               app_key,
               app_secret,
               refresh_token,
               token_expire_in,
               token_r1expire_in,
               refresh_token_expire_in,
               refresh_time,
               create_time,
               modify_time,
               has_token_expired,
               has_exception,
               deleted,
               stoped,
               update_time,
               vendor_id,
               platform_eshop_id,
               last_upload_business_time,
               is_auth,
               expire_notice,
               deliver_duration,
               promised_collect_duration,
               promised_sign_duration,
               subscribe_logistics,
               refund_sys_promised_confirm_duration,
               refund_promised_confirm_duration,
               refund_promised_agree_duration,
               refund_promised_deliver_duration,
               refund_promised_receive_duration,
               mention_deliver_duration
        from pl_eshop
        where profile_id = #{profileId}
          and deleted = 0
          and stoped = 0
    </select>

    <select id="getAllEshopConfig" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopConfig">
        select profile_id,
               eshop_id,
               rds_enabled,
               rds_apply_time,
               auto_sync_order_enabled,
               auto_sync_stock_enabled,
               msg_notify_enabled,
               ag_enabled,
               tmc_enabled,
               mall_type,
               check_account_type,
               btype_generate_type,
               mapping_type,
               xcode_mapping_required,
               independent_accounting_enabled,
               create_time,
               update_time,
               hold_minutes,
               platform_eshop_sn_type,
               switch_state,
               export_enabled,
               expire_export_time,
               auto_refresh_product_enabled,
               muti_select_appkey,
               process_type,
               deliver_process_type,
               ptype_auto_upload_enabled,
               btype_auto_upload_enabled,
               ktype_auto_upload_enabled,
               invoice_upload_enabled,
               auto_shelf_on,
               rds_ready_time,
               rds_check_time,
               rds_name,
               real_stock_qty_enabled,
               auto_report_stock_qty_enabled,
               use_platform_skuId_as_xcode
        from pl_eshop_config
        where profile_id = #{profileId}
    </select>

    <select id="getAllSyncCondition" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopOrderSyncCondition">
        select id,
               eshop_id,
               profile_id,
               download_begin_time,
               last_download_begin_time,
               last_download_end_time,
               last_download_exec_time,
               last_download_success_begin_time,
               last_download_success_end_time,
               last_download_fail_begin_time,
               last_download_fail_end_time,
               last_download_error_message,
               task_type,
               slice_time,
               increase_slice_time,
               full_slice_time
        from pl_eshop_sale_order_sync_condition
        where profile_id = #{profileId}
    </select>

    <select id="queryEshopCountByShopType" resultType="int">
        select count(1) from pl_eshop where profile_id=#{profileId} and eshop_type=#{shopType} and token!='' AND deleted=0
    </select>

    <select id="queryEshopCountByAppkey" resultType="int">
        select count(*) from pl_eshop where profile_id=#{profileId} and eshop_type=#{shopType} and app_key=#{appKey} AND deleted=0
    </select>
    <select id="queryEshopInfoByAppkey" resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select * from pl_eshop where profile_id=#{profileId} and eshop_type=#{shopType} and app_key=#{appKey} AND deleted=0
    </select>

    <select id="getEshopListByShopType" resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select e.*
        from pl_eshop e
        <if test="employeeId!=null">
            inner join base_limit_scope bls on bls.profile_id=e.profile_id and object_type=3 and
            e.otype_id=bls.object_id and bls.etype_id=#{employeeId}
        </if>
        where e.profile_id=#{profileId}
          and e.eshop_type=#{shopType}
          and e.token!=''
          AND e.deleted=0
        AND e.token_expire_in>now()
    </select>

    <select id="queryOrderCountByEshopId" resultType="int">
        select count(1) from pl_eshop_sale_order
        where profile_id=#{profileId}
        and otype_id=#{eshopId}
        and local_trade_state in(0,1,2,3,4,5,6,7)
        and trade_create_time <![CDATA[>=]]> #{beginTime}
        and deleted=0
    </select>

    <select id="queryDeletedEshopIds" resultType="java.math.BigInteger">
        select otype_id from pl_eshop where profile_id=#{profileId} and deleted=1
    </select>
    <select id="queryEshopByOtypeIdAndShopType" resultType="com.wsgjp.ct.sale.common.entity.EshopInfo">
        select * from pl_eshop
        where profile_id = #{eshopInfo.profileId}
        and deleted = 0
        <if test="shopTypes != null">
            and eshop_type in
            <foreach item="shopType" collection="shopTypes" index="index" open="(" separator="," close=")">
                #{shopType}
            </foreach>
        </if>
        <if test="shopTypes == null">
            and eshop_type = #{eshopInfo.eshopType}
        </if>
        <if test="eshopInfo.otypeId != null and eshopInfo.otypeId !=''">
            and otype_id = #{eshopInfo.otypeId}
        </if>
    </select>

    <update id="expireEshopAuth">
        update pl_eshop
        set has_token_expired=1, token_expire_in=now(), token_r1expire_in=now()
        where profile_id=#{profileId} and otype_id=#{eshopId}
    </update>

    <update id="updateEshopTokenSimple">
        update pl_eshop set token=#{token}
        where profile_id=#{profileId} and otype_id=#{eshopId}
    </update>

    <update id="autoSaveMappingRelation">
        update pl_eshop_config
        set auto_save_mapping_relation = #{pageInfo.autoSaveMappingRelation}
        where profile_id=#{pageInfo.profileId} and eshop_id=#{pageInfo.otypeId}
    </update>

    <update id="updateRealStockQtyEnabled">
        update pl_eshop_config
        set real_stock_qty_enabled        = #{realStockQtyEnabled},
            auto_report_stock_qty_enabled = #{autoReportStockQtyEnabled},
            auth_doc_link              = #{jdAuthDocLink},
            auth_doc_type              = #{jdAuthDocType}
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
    </update>
    <select id="queryEshopAtypeInfo" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Atype">
        select a.id ,a.fullname ,a.usercode,a.typeid  from base_otype o
        join base_atype a  on o.profile_id = a.profile_id and o.atype_id = a.id
        where o.profile_id=#{profileId} and o.id = #{otypeId}
    </select>

</mapper>
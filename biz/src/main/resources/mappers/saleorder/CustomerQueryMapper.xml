<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.CustomerQueryMapper">

    <insert id="save" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.params.CustomerQuery">
        INSERT INTO `pl_customer_query`
        (`id`,
         `etype_id`,
         `query_code`,
         `title`,
         `profile_id`,
         alway_show,
         sort_index)
            VALUE
            (#{id},
             #{etypeId},
             #{queryCode},
             #{title},
             #{profileId},
             #{alwayShow},
             #{sortIndex}
                )
        ON DUPLICATE KEY UPDATE title=#{title},
                                alway_show =#{alwayShow},
                                sort_index= #{sortIndex}
    </insert>

    <delete id="delete">
        delete from pl_customer_query where profile_id=#{profileId} and id=#{id}
    </delete>

    <delete id="deleteDetail">
        delete from pl_customer_query_detail where profile_id=#{profileId} and query_id=#{queryId}
    </delete>
    <delete id="deleteDetailBatch">
        delete from pl_customer_query_detail where profile_id=#{profileId} and query_id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insert">
        INSERT INTO pl_customer_query_detail
        (id,profile_id,query_id,field_value,field_text)
        VALUES
        <foreach item="detail" collection="details" separator=",">
            (#{detail.id},#{detail.profileId},#{detail.queryId},#{detail.fieldValue},#{detail.fieldText})
        </foreach>
    </insert>

    <select id="list" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.params.CustomerQuery">
        select * from `pl_customer_query` where profile_id=#{profileId} and etype_id=#{etypeId}
         and query_code=#{queryCode} order by sort_index
    </select>

    <select id="listDetails" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.params.CustomerQueryDetail">
        select * from pl_customer_query_detail where profile_id=#{profileId} and query_id in
        <foreach item="queryId" collection="queryIds" open="(" separator="," close=")">
            #{queryId}
        </foreach>
    </select>

    <update id="updateTitle">
        update `pl_customer_query` set title=#{title} where profile_id=#{profileId} and id=#{queryId}
    </update>

        <insert id="replaceQueryConfig">
        replace into pl_customer_query(id, etype_id, query_code, profile_id)
        values (#{queryId},#{etypeId},#{queryCode},#{profileId});
    </insert>

    <insert id="insertQueryConfigDetails">
            insert into pl_customer_query_detail(id, profile_id, query_id, field_value, field_text)
        values (#{id},#{profileId},#{queryId},#{queryConfig},#{configJson});
    </insert>
    <insert id="saveBatch" parameterType="list">
        INSERT INTO `pl_customer_query`
        (`id`,
        `etype_id`,
        `query_code`,
        `title`,
        `profile_id`,
        alway_show,
        sort_index)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id},
            #{item.etypeId},
            #{item.queryCode},
            #{item.title},
            #{item.profileId},
            #{item.alwayShow},
            #{item.sortIndex})
        </foreach>
        ON DUPLICATE KEY UPDATE title=values(title),
        alway_show =values(alway_show),
        sort_index= values(sort_index)
    </insert>

    <select id="queryPageConfigByPageName"
            resultType="com.wsgjp.ct.sale.biz.jarvis.entity.DeliverPageQueryConfig">
        select tdcq.id, tdcqd.field_text as 'configJson',tdcq.query_code as 'queryCode'
        from pl_customer_query tdcq
                 left join pl_customer_query_detail tdcqd
                           on tdcq.profile_id = tdcqd.profile_id and tdcq.id = tdcqd.query_id
        where tdcq.profile_id = #{profileId}
          and tdcq.etype_id = #{etypeId}
          and tdcq.query_code = #{queryCode}
        limit 1;
    </select>
    <select id="getForTitle" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.params.CustomerQuery">
        select *
        from pl_customer_query
        where title = #{title}
          and profile_id = #{profileId}
          and query_code = 'eshop_refund_query'
    </select>
</mapper>

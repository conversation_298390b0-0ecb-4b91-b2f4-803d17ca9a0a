<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.StockSyncMapper">
    <sql id="whereSql">
        where pepm.profile_id=#{profileId} and pepm.eshop_id=#{eshopId} and ifnull(tmp.platform_num_id,'')!=''
        <if test="fullName !=null and fullName!=''">
            and pepm.platform_fullname like CONCAT('%',#{fullName},'%')
        </if>
        <if test="warehouseCode!=null and warehouseCode!=''">
            and (pepm.warehouse_code=#{warehouseCode} or pepm.warehouse_code='' or pepm.warehouse_code is null)
        </if>
        <if test="platformNumIds!=null and platformNumIds.size()>0">
            and pepm.platform_num_id in
            <foreach collection="platformNumIds" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="stockState!=null and stockState==@com.wsgjp.ct.sale.platform.enums.StockState@OnSale">
            and pepm.platform_stock_state='1'
        </if>
        <if test="stockState!=null and stockState==@com.wsgjp.ct.sale.platform.enums.StockState@InStock">
            and pepm.platform_stock_state='2'
        </if>
        <if test="dStockState!=null and dStockState==@com.wsgjp.ct.sale.platform.enums.StockState@OnSale">
            and pepm.platform_stock_state='1'
        </if>
        <if test="dStockState!=null and dStockState==@com.wsgjp.ct.sale.platform.enums.StockState@InStock">
            and pepm.platform_stock_state='2'
        </if>
    </sql>

    <sql id="joinSql">
        JOIN (
        select ps.platform_num_id from pl_eshop_product_sku ps
        left join pl_eshop_product_sku_expand exp on ps.profile_id =exp.profile_id and ps.eshop_id = exp.eshop_id and ps.unique_id=exp.unique_id
        left join base_ptype_xcode bpx on bpx.profile_id=ps.profile_id and bpx.xcode=ps.platform_xcode and bpx.unit_id not in(select id from base_ptype_unit where profile_id=ps.profile_id and unit_type=1)
        left join base_ptype bp on bp.profile_id=bpx.profile_id and bp.id= bpx.ptype_id
        <if test="queryTypeInt==1 and productInfoList!=null and productInfoList.size()>0">
            LEFT JOIN base_ptype_combo_detail co on co.profile_id = bp.profile_id and co.combo_id = bp.id
            LEFT JOIN base_ptype_sku sku ON sku.id = co.sku_id AND co.profile_id=sku.profile_id
        </if>
        <if test="(queryTypeInt==0 and productInfoList!=null and productInfoList.size()>0) or (platformXcode!=null and platformXcode!='')">
            LEFT JOIN pl_eshop_product pp ON ps.profile_id = pp.profile_id AND pp.eshop_id=ps.eshop_id AND pp.platform_num_id = ps.platform_num_id
        </if>
        <if test="warehouseSyncEnabled!=null or allowAutoSync!=null">
            left join pl_eshop_product_sku_rule_config psrcfg on psrcfg.profile_id=ps.profile_id
            and psrcfg.platform_num_id=ps.platform_num_id and psrcfg.platform_properties=ps.platform_properties_name
        </if>
        left join pl_eshop_stock_sync_abstract_log alog on alog.profile_id=ps.profile_id and alog.eshop_id=ps.eshop_id
        and alog.platform_num_id=ps.platform_num_id and alog.platform_sku_id=ps.platform_sku_id and alog.target_type = #{targetType}
        <if test="targetType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleTargetTypeEnum@WAREHOUSE">
            and alog.warehouse_code=#{warehouseCode}
        </if>
        left join pl_eshop_product_mark mark on mark.profile_id=ps.profile_id and mark.eshop_id=ps.eshop_id and mark.unique_id=ps.unique_id and mark.mark_code=1014
        left join pl_eshop_product_mark mark2 on mark2.profile_id=ps.profile_id and mark2.eshop_id=ps.eshop_id and mark2.unique_id=ps.unique_id and mark2.mark_code=1012
        where ps.profile_id=#{profileId} and ps.eshop_id=#{eshopId}
        and ps.storage_type=0
        AND ps.platform_xcode!=''
        AND bp.id>0
        AND bp.usercode!=''
        AND bp.pcategory=2
        AND bp.stoped=0
        AND bp.deleted=0
        AND exp.mapping_type=1
        <if test="stopMarkShow == false">
            and mark2.id is null
        </if>
         <if test="stopMarkShow == true">
            and mark2.id is not null
        </if>
        and mark.id is null
        <if test="warehouseSyncEnabled!=null">
            and if(ifnull(psrcfg.state, 0) = 0, true, false)=#{warehouseSyncEnabled} and
            psrcfg.warehouse_code=#{warehouseCode}
        </if>
        <if test="allowAutoSync!=null">
            and if(ifnull(psrcfg.state, 0) = 0, true, false)=#{allowAutoSync}
        </if>
        <if test="ptypeName!=null and ptypeName!=''">
            and bp.fullname like CONCAT('%',#{ptypeName},'%')
        </if>
        <if test="xcode!=null and xcode!=''">
            and bpx.xcode like CONCAT('%',#{xcode},'%')
        </if>
        <if test="platformPropertiesName!=null and platformPropertiesName!=''">
            and ps.platform_properties_name like CONCAT('%',#{platformPropertiesName},'%')
        </if>
        <if test="platformXcode!=null and platformXcode!=''">
            and (ps.platform_xcode like CONCAT('%',#{platformXcode},'%') or pp.platform_xcode like CONCAT('%',#{platformXcode},'%'))
        </if>
        <if test="queryTypeInt==1 and productInfoList!=null and productInfoList.size()>0">
            and
            <foreach collection="productInfoList" index="index" item="p" open="(" separator="or" close=")">
                bp.fullname like CONCAT('%',#{p},'%')
                or bp.usercode like CONCAT('%',#{p},'%')
                or sku.propvalue_names like CONCAT('%',#{p},'%')
                or bpx.xcode like CONCAT('%',#{p},'%')
            </foreach>
        </if>
        <if test="queryTypeInt==0 and productInfoList!=null and productInfoList.size()>0">
            and
            <foreach collection="productInfoList" index="index" item="p" open="(" separator="or" close=")">
                pp.platform_fullname like CONCAT('%',#{p},'%') or pp.platform_xcode like CONCAT('%',#{p},'%') or
                ps.platform_xcode like CONCAT('%',#{p},'%') or ps.platform_properties_name like CONCAT('%',#{p},'%')
            </foreach>
        </if>
        <if test="platformNumIds!=null and platformNumIds.size()>0">
            and ps.platform_num_id in
            <foreach collection="platformNumIds" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="platformSkuIds!=null and platformSkuIds.size()>0">
            and ps.platform_sku_id in
            <foreach collection="platformSkuIds" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="receSyncState!=null">
            and ifnull(alog.sync_status, 0)=#{receSyncState}
        </if>
        union
        select ps.platform_num_id from pl_eshop_product_sku ps
        left join pl_eshop_product_sku_expand exp on ps.profile_id =exp.profile_id and ps.eshop_id = exp.eshop_id and ps.unique_id=exp.unique_id
        <if test="(queryTypeInt==0 and productInfoList!=null and productInfoList.size()>0) or (platformXcode!=null and platformXcode!='')">
            LEFT JOIN pl_eshop_product pp ON ps.profile_id = pp.profile_id AND pp.eshop_id=ps.eshop_id AND pp.platform_num_id = ps.platform_num_id
        </if>
        left join base_ptype_xcode bpx on bpx.profile_id=ps.profile_id and bpx.xcode=ps.platform_xcode and bpx.unit_id not in(select id from base_ptype_unit where profile_id=ps.profile_id and unit_type=1)
        left join base_ptype bp on bp.profile_id=bpx.profile_id and bpx.ptype_id=bp.id
        LEFT JOIN base_ptype_sku sku ON sku.id = bpx.sku_id AND bp.profile_id = sku.profile_id
        <if test="warehouseSyncEnabled!=null or allowAutoSync!=null">
            left join pl_eshop_product_sku_rule_config psrcfg on psrcfg.profile_id=ps.profile_id
            and psrcfg.platform_num_id=ps.platform_num_id and psrcfg.platform_properties=ps.platform_properties_name
        </if>
        left join pl_eshop_stock_sync_abstract_log alog on alog.profile_id=ps.profile_id and alog.eshop_id=ps.eshop_id
        and alog.platform_num_id=ps.platform_num_id and alog.platform_sku_id=ps.platform_sku_id and alog.target_type = #{targetType}
        <if test="targetType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleTargetTypeEnum@WAREHOUSE">
            and alog.warehouse_code=#{warehouseCode}
        </if>
        left join pl_eshop_product_mark mark on mark.profile_id=ps.profile_id and mark.eshop_id=ps.eshop_id and mark.unique_id=ps.unique_id and mark.mark_code=1014
        left join pl_eshop_product_mark mark2 on mark2.profile_id=ps.profile_id and mark2.eshop_id=ps.eshop_id and mark2.unique_id=ps.unique_id and mark2.mark_code=1012
        where ps.profile_id=#{profileId}
        and ps.storage_type=0
        and ps.eshop_id=#{eshopId}
        and bpx.xcode!=''
        and bp.id>0
        and ps.platform_xcode!=''
        and bp.pcategory=0
        and bp.stoped=0
        and bp.deleted=0
        <if test="stopMarkShow == false">
            and mark2.id is null
        </if>
        <if test="stopMarkShow == true">
            and mark2.id is not null
        </if>
        and mark.id is null
        AND exp.mapping_type=1
        <if test="ptypeName!=null and ptypeName!=''">
            and bp.fullname like CONCAT('%',#{ptypeName},'%')
        </if>
        <if test="xcode!=null and xcode!=''">
            and bpx.xcode like CONCAT('%',#{xcode},'%')
        </if>
        <if test="warehouseSyncEnabled!=null">
            and if(ifnull(psrcfg.state, 0) = 0, true, false)=#{warehouseSyncEnabled} and psrcfg.warehouse_code=#{warehouseCode}
        </if>
        <if test="allowAutoSync!=null">
            and if(ifnull(psrcfg.state, 0) = 0, true, false)=#{allowAutoSync}
        </if>
        <if test="platformPropertiesName!=null and platformPropertiesName!=''">
            and ps.platform_properties_name like CONCAT('%',#{platformPropertiesName},'%')
        </if>
        <if test="platformXcode!=null and platformXcode!=''">
            and (ps.platform_xcode like CONCAT('%',#{platformXcode},'%')
            or pp.platform_xcode like CONCAT('%',#{platformXcode},'%'))
        </if>
        <if test="queryTypeInt==1 and productInfoList!=null and productInfoList.size()>0">
            and
            <foreach collection="productInfoList" index="index" item="p" open="(" separator="or" close=")">
                bp.fullname like CONCAT('%',#{p},'%')
                or bp.usercode like CONCAT('%',#{p},'%')
                or bpx.xcode like CONCAT('%',#{p},'%')
                or sku.propvalue_names like CONCAT('%',#{p},'%')
            </foreach>
        </if>
        <if test="queryTypeInt==0 and productInfoList!=null and productInfoList.size()>0">
            and
            <foreach collection="productInfoList" index="index" item="p" open="(" separator="or" close=")">
                pp.platform_fullname like CONCAT('%',#{p},'%') or pp.platform_xcode like CONCAT('%',#{p},'%') or
                ps.platform_xcode like CONCAT('%',#{p},'%') or ps.platform_properties_name like CONCAT('%',#{p},'%')
            </foreach>
        </if>
        <if test="platformNumIds!=null and platformNumIds.size()>0">
            and ps.platform_num_id in
            <foreach collection="platformNumIds" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="platformSkuIds!=null and platformSkuIds.size()>0">
            and ps.platform_sku_id in
            <foreach collection="platformSkuIds" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="receSyncState!=null">
            and ifnull(alog.sync_status, 0)=#{receSyncState}
        </if>
        union
        SELECT ps.platform_num_id FROM pl_eshop_product_sku_mapping map
        LEFT JOIN pl_eshop_product_sku ps ON map.profile_id = ps.profile_id AND map.eshop_id=ps.eshop_id AND map.unique_id = ps.unique_id
        left join pl_eshop_product_sku_expand exp on ps.profile_id =exp.profile_id and ps.eshop_id = exp.eshop_id and ps.unique_id=exp.unique_id
        <if test="(queryTypeInt==0 and productInfoList!=null and productInfoList.size()>0) or (platformXcode!=null and platformXcode!='')">
            LEFT JOIN pl_eshop_product pp ON ps.profile_id = pp.profile_id AND pp.eshop_id=ps.eshop_id AND pp.platform_num_id = ps.platform_num_id
        </if>
        LEFT JOIN base_ptype_xcode bpx on bpx.profile_id=ps.profile_id and bpx.unit_id=map.unit_id and bpx.sku_id=map.sku_id and bpx.defaulted=1
        LEFT JOIN base_ptype_sku sku ON sku.id = map.sku_id AND sku.profile_id = map.profile_id
        LEFT JOIN base_ptype bp on bp.profile_id=ps.profile_id and bp.id=map.ptype_id
        LEFT JOIN pl_eshop_product_sku_rule_config psrcfg on psrcfg.profile_id=ps.profile_id
        and psrcfg.platform_num_id=ps.platform_num_id and psrcfg.platform_properties=ps.platform_properties_name
        left join pl_eshop_stock_sync_abstract_log alog on alog.profile_id=ps.profile_id and alog.eshop_id=ps.eshop_id
        and alog.platform_num_id=ps.platform_num_id and alog.platform_sku_id=ps.platform_sku_id and alog.target_type = #{targetType}
        <if test="targetType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleTargetTypeEnum@WAREHOUSE">
            and alog.warehouse_code=#{warehouseCode}
        </if>
        left join pl_eshop_product_mark mark on mark.profile_id=ps.profile_id and mark.eshop_id=ps.eshop_id and mark.unique_id=ps.unique_id and mark.mark_code=1014
        left join pl_eshop_product_mark mark2 on mark2.profile_id=ps.profile_id and mark2.eshop_id=ps.eshop_id and mark2.unique_id=ps.unique_id and mark2.mark_code=1012
        where ps.profile_id=#{profileId}
        and ps.eshop_id=#{eshopId}
        and ps.storage_type=0
        and bp.stoped=0
        AND bp.pcategory!=2
        and bp.deleted=0
        and bp.id>0
        <if test="stopMarkShow == false">
            and mark2.id is null
        </if>
        <if test="stopMarkShow == true">
            and mark2.id is not null
        </if>
        and mark.id is null
        AND exp.mapping_type=0
        <if test="warehouseSyncEnabled!=null">
            and if(ifnull(psrcfg.state, 0) = 0, true, false)=#{warehouseSyncEnabled}
        </if>
        <if test="allowAutoSync!=null">
            and if(ifnull(psrcfg.state, 0) = 0, true, false)=#{allowAutoSync}
        </if>
        <if test="ptypeName!=null and ptypeName!=''">
            and bp.fullname like CONCAT('%',#{ptypeName},'%')
        </if>
        <if test="xcode!=null and xcode!=''">
            and bpx.xcode like CONCAT('%',#{xcode},'%')
        </if>
        <if test="platformPropertiesName!=null and platformPropertiesName!=''">
            and ps.platform_properties_name like CONCAT('%',#{platformPropertiesName},'%')
        </if>
        <if test="platformXcode!=null and platformXcode!=''">
            and (ps.platform_xcode like CONCAT('%',#{platformXcode},'%') or pp.platform_xcode like
            CONCAT('%',#{platformXcode},'%'))
        </if>
        <if test="queryTypeInt==1 and productInfoList!=null and productInfoList.size()>0">
            and
            <foreach collection="productInfoList" index="index" item="p" open="(" separator="or" close=")">
                bp.fullname like CONCAT('%',#{p},'%') or bp.usercode like CONCAT('%',#{p},'%') or bpx.xcode like CONCAT('%',#{p},'%')
            </foreach>
        </if>
        <if test="queryTypeInt==0 and productInfoList!=null and productInfoList.size()>0">
            and
            <foreach collection="productInfoList" index="index" item="p" open="(" separator="or" close=")">
                pp.platform_fullname like CONCAT('%',#{p},'%') or pp.platform_xcode like CONCAT('%',#{p},'%')
                or ps.platform_xcode like CONCAT('%',#{p},'%') or ps.platform_properties_name like CONCAT('%',#{p},'%')
            </foreach>
        </if>
        <if test="platformNumIds!=null and platformNumIds.size()>0">
            and ps.platform_num_id in
            <foreach collection="platformNumIds" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="platformSkuIds!=null and platformSkuIds.size()>0">
            and ps.platform_sku_id in
            <foreach collection="platformSkuIds" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="receSyncState!=null">
            and ifnull(alog.sync_status, 0)=#{receSyncState}
        </if>
        union
        select ps.platform_num_id from pl_eshop_product_sku_mapping map
        left join pl_eshop_product_sku ps on map.profile_id = ps.profile_id AND map.eshop_id=ps.eshop_id and map.unique_id=ps.unique_id
        left join pl_eshop_product_sku_expand exp on ps.profile_id =exp.profile_id and ps.eshop_id = exp.eshop_id and ps.unique_id=exp.unique_id
        <if test="(queryTypeInt==0 and productInfoList!=null and productInfoList.size()>0) or (platformXcode!=null and platformXcode!='')">
            LEFT JOIN pl_eshop_product pp ON ps.profile_id = pp.profile_id AND pp.eshop_id=ps.eshop_id AND pp.platform_num_id = ps.platform_num_id
        </if>
        left join base_ptype bp on bp.profile_id=ps.profile_id and bp.id=map.ptype_id
        left join base_ptype_xcode bpx1 on bpx1.profile_id=ps.profile_id and bpx1.ptype_id=map.ptype_id and bpx1.defaulted=1
        left join base_ptype_combo_detail co on co.profile_id = bp.profile_id and co.combo_id = bp.id
        LEFT JOIN base_ptype_sku sku ON sku.id = co.sku_id AND bp.profile_id = sku.profile_id
        LEFT JOIN base_ptype_xcode bpx on bpx.profile_id=sku.profile_id and bpx.sku_id=co.sku_id and bpx.unit_id=co.unit_id
        left join pl_eshop_product_sku_rule_config psrcfg on psrcfg.profile_id=ps.profile_id
        and psrcfg.platform_num_id=ps.platform_num_id and psrcfg.platform_properties=ps.platform_properties_name
        left join pl_eshop_stock_sync_abstract_log alog on alog.profile_id=ps.profile_id and alog.eshop_id=ps.eshop_id
        and alog.platform_num_id=ps.platform_num_id and alog.platform_sku_id=ps.platform_sku_id and alog.target_type = #{targetType}
        <if test="targetType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleTargetTypeEnum@WAREHOUSE">
            and alog.warehouse_code=#{warehouseCode}
        </if>
        left join pl_eshop_product_mark mark on mark.profile_id=ps.profile_id and mark.eshop_id=ps.eshop_id and mark.unique_id=ps.unique_id and mark.mark_code=1014
        left join pl_eshop_product_mark mark2 on mark2.profile_id=ps.profile_id and mark2.eshop_id=ps.eshop_id and mark2.unique_id=ps.unique_id and mark2.mark_code=1012
        where ps.profile_id=#{profileId}
        and ps.storage_type=0
        and ps.eshop_id=#{eshopId}
        and bp.stoped=0
        and bp.deleted=0
        AND bp.pcategory=2
        and bp.id>0
        <if test="stopMarkShow == false">
            and mark2.id is null
        </if>
        <if test="stopMarkShow == true">
            and mark2.id is not null
        </if>
        and mark.id is null
        AND exp.mapping_type=0
        <if test="warehouseSyncEnabled!=null">
            and if(ifnull(psrcfg.state, 0) = 0, true, false)=#{warehouseSyncEnabled}
        </if>
        <if test="allowAutoSync!=null">
            and if(ifnull(psrcfg.state, 0) = 0, true, false)=#{allowAutoSync}
        </if>
        <if test="ptypeName!=null and ptypeName!=''">
            and bp.fullname like CONCAT('%',#{ptypeName},'%')
        </if>
        <if test="xcode!=null and xcode!=''">
            and bpx1.xcode like CONCAT('%',#{xcode},'%')
        </if>
        <if test="platformPropertiesName!=null and platformPropertiesName!=''">
            and ps.platform_properties_name like CONCAT('%',#{platformPropertiesName},'%')
        </if>
        <if test="platformXcode!=null and platformXcode!=''">
            and (ps.platform_xcode like CONCAT('%',#{platformXcode},'%') or pp.platform_xcode like CONCAT('%',#{platformXcode},'%'))
        </if>
        <if test="queryTypeInt==1 and productInfoList!=null and productInfoList.size()>0">
            and
            <foreach collection="productInfoList" index="index" item="p" open="(" separator="or" close=")">
                bp.fullname like CONCAT('%',#{p},'%')
                or bp.usercode like CONCAT('%',#{p},'%')
                or bpx1.xcode like CONCAT('%',#{p},'%')
                or sku.propvalue_names like CONCAT('%',#{p},'%')
            </foreach>
        </if>
        <if test="queryTypeInt==0 and productInfoList!=null and productInfoList.size()>0">
            and
            <foreach collection="productInfoList" index="index" item="p" open="(" separator="or" close=")">
                pp.platform_fullname like CONCAT('%',#{p},'%') or pp.platform_xcode like CONCAT('%',#{p},'%')
                or ps.platform_xcode like CONCAT('%',#{p},'%') or ps.platform_properties_name like CONCAT('%',#{p},'%')
            </foreach>
        </if>
        <if test="platformNumIds!=null and platformNumIds.size()>0">
            and ps.platform_num_id in
            <foreach collection="platformNumIds" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="platformSkuIds!=null and platformSkuIds.size()>0">
            and ps.platform_sku_id in
            <foreach collection="platformSkuIds" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="receSyncState!=null">
            and ifnull(alog.sync_status, 0)=#{receSyncState}
        </if>
        )tmp on tmp.platform_num_id=pepm.platform_num_id
    </sql>

    <select id="queryStockSyncPageDataCount" resultType="java.lang.Integer">
        select count(1)
        from pl_eshop_product pepm
        <include refid="joinSql"/>
        <include refid="whereSql"/>
    </select>


    <select id="queryStockSyncPageData"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncManagePageData">
        select 0 as pid,pepm.id as id, pepm.eshop_id ,pepm.profile_id,pepm.platform_pic_url as
        platformPicUrl,
        pepm.platform_num_id,pepm.platform_fullname as fullName,pepm.platform_xcode as
        platform_xcode,pepm.platform_xcode as pmplatformXcode,pepm.default_sku_id as platformDefaultSkuId,
        pepm.has_properties, 'true' as mainProduct,'' as platformPropertiesName,'3' as receSyncState,pepm.platform_stock_state as stock_state
        from pl_eshop_product pepm
        <include refid="joinSql"/>
        <include refid="whereSql"/>
        order by pepm.platform_fullname
    </select>

    <select id="queryStockSyncPageDataLimit"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncManagePageData">
        select 0 as pid,pepm.id as id, pepm.eshop_id ,pepm.profile_id,pepm.platform_pic_url as
        platformPicUrl,
        pepm.platform_num_id,pepm.platform_fullname as fullName,pepm.platform_xcode as
        platform_xcode,pepm.platform_xcode as pmplatformXcode,pepm.default_sku_id as platformDefaultSkuId,
        pepm.has_properties, 'true' as mainProduct,'' as platformPropertiesName,'3' as
        receSyncState,pepm.platform_stock_state as stock_state
        from pl_eshop_product pepm
        <include refid="joinSql"/>
        <include refid="whereSql"/>
        order by pepm.platform_fullname
        limit #{pageNum}, #{pageSize}
    </select>

    <select id="queryStockSyncPageDataDetail" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncManagePageData">
        <include refid="normal"/>
        union
        <include refid="xcodeCombo"/>
        union
        <include refid="xcodeNormal"/>
    </select>

    <sql id="normal">
        select pepsm.id as id,pepm.id as pid, pepsm.eshop_id ,pepsm.profile_id,
        pepsm.platform_num_id,pepsm.platform_sku_id,pepsm.platform_properties_name, '' as fullName,
        pepsm.platform_json, pepsm.platform_xcode,pepm.platform_xcode as pmplatformXcode,pepsm.platform_pic_url,
        pepm.default_sku_id as platformDefaultSkuId,false as hasProperties,
        false as mainProduct,bsu.id as baseUnitId,
        sku2.id as skuId,bpu.id as unitId,bp.id as ptypeId,bpu.unit_rate,bpu.unit_name,
        bp.fullname as ptypeName,bp.pcategory,
        psrcfg.rule_id,
        if(ifnull(psrcfg.state,0) = 2, false, true) as allowAutoSync,
        if(ifnull(psrcfg.state,0) = 1, false, true) as warehouseSyncEnabled,
        bpx.xcode as xcode,
        bp.batchenabled as batchEnabled,bp.protect_days,
        bp.protect_days_unit,
        CONCAT_WS(':',
        IF(IFNULL(sku2.propvalue_name1,'')='', NULL, sku2.propvalue_name1),
        IF(IFNULL(sku2.propvalue_name2,'')='', NULL, sku2.propvalue_name2),
        IF(IFNULL(sku2.propvalue_name3,'')='', NULL, sku2.propvalue_name3),
        IF(IFNULL(sku2.propvalue_name4,'')='', NULL, sku2.propvalue_name4),
        IF(IFNULL(sku2.propvalue_name5,'')='', NULL, sku2.propvalue_name5),
        IF(IFNULL(sku2.propvalue_name6,'')='', NULL, sku2.propvalue_name6)) as localProps,
        exp.mapping_type,alog.sync_status as 'receSyncState',pepm.platform_stock_state as stock_state,case bp.pcategory when 2 then bp.usercode else pf.fullbarcode end as fullbarcode
        from pl_eshop_product_sku pepsm
        left join pl_eshop_product pepm on pepsm.eshop_id = pepm.eshop_id and pepsm.profile_id = pepm.profile_id and
        pepsm.platform_num_id = pepm.platform_num_id
        left join pl_eshop_product_sku_mapping map on map.profile_id=pepm.profile_id and map.unique_id=pepsm.unique_id
        left join pl_eshop_product_sku_expand exp on pepsm.profile_id =exp.profile_id and pepsm.eshop_id = exp.eshop_id and pepsm.unique_id=exp.unique_id
        left join base_ptype bp on bp.profile_id=pepsm.profile_id and bp.id=map.ptype_id
        left join base_ptype_combo_detail combo on combo.profile_id = bp.profile_id and combo.combo_id = bp.id
        LEFT JOIN base_ptype_sku sku1 ON sku1.id = combo.sku_id AND bp.profile_id = sku1.profile_id
        LEFT JOIN base_ptype_sku sku2 ON sku2.id = map.sku_id AND bp.profile_id = sku2.profile_id
        left join base_ptype_xcode bpx on bpx.profile_id=pepsm.profile_id and bpx.sku_id= map.sku_id and bpx.unit_id=map.unit_id and bpx.defaulted=1
        left join base_ptype_xcode bpx1 on bpx1.profile_id=combo.profile_id and bpx1.sku_id= combo.sku_id and bpx1.unit_id=combo.unit_id
        left join base_ptype_unit bpu on bpu.profile_id=pepsm.profile_id and bpu.ptype_id=map.ptype_id and bpu.id = map.unit_id
        left join base_ptype_unit bsu on bsu.profile_id=pepsm.profile_id and bsu.ptype_id=map.ptype_id and bsu.unit_code=1
        left join pl_eshop_product_sku_rule_config psrcfg on psrcfg.profile_id=pepsm.profile_id and psrcfg.target_type=#{targetType}
        and psrcfg.platform_num_id=pepsm.platform_num_id and psrcfg.platform_properties=pepsm.platform_properties_name
        <if test="targetType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleTargetTypeEnum@WAREHOUSE">
            and psrcfg.warehouse_code=#{warehouseCode}
        </if>
        left join pl_eshop_stock_sync_abstract_log alog on alog.profile_id=pepsm.profile_id and alog.eshop_id=pepsm.eshop_id
             and alog.platform_num_id=pepsm.platform_num_id and alog.platform_sku_id=pepsm.platform_sku_id and alog.target_type = #{targetType}
        <if test="targetType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleTargetTypeEnum@WAREHOUSE">
            and alog.warehouse_code=#{warehouseCode}
        </if>
        left join pl_eshop_product_mark mark on mark.profile_id=pepsm.profile_id and mark.eshop_id=pepsm.eshop_id and mark.unique_id=pepsm.unique_id and mark.mark_code=1014
        left join pl_eshop_product_mark mark2 on mark2.profile_id=pepsm.profile_id and mark2.eshop_id=pepsm.eshop_id and mark2.unique_id=pepsm.unique_id and mark2.mark_code=1012
        left join base_ptype_fullbarcode pf on pf.profile_id=bpx.profile_id and pf.sku_id=map.sku_id and pf.unit_id =
        bpu.id and pf.defaulted=1
        where pepsm.profile_id=#{profileId}
        and pepsm.eshop_id=#{eshopId}
        and pepsm.storage_type=0
        and bp.stoped=0
        and bp.deleted=0
        AND exp.mapping_type=0
        and bp.id>0
        <if test="stopMarkShow == false">
            and mark2.id is null
        </if>
        <if test="stopMarkShow == true">
            and mark2.id is not null
        </if>
        and mark.id is null
        <if test="platformNumIds!=null and platformNumIds.size()>0">
            and pepsm.platform_num_id in
            <foreach collection="platformNumIds" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="platformSkuIds!=null and platformSkuIds.size()>0">
            and pepsm.platform_sku_id in
            <foreach collection="platformSkuIds" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="queryTypeInt==0 and productInfoList!=null and productInfoList.size()>0">
            and
            <foreach collection="productInfoList" index="index" item="p" open="(" separator="or" close=")">
                pepsm.platform_xcode like CONCAT('%',#{p},'%')
                or pepsm.platform_properties_name like CONCAT('%',#{p},'%')
                or pepm.platform_xcode like CONCAT('%',#{p},'%')
                or pepm.platform_fullname like CONCAT('%',#{p},'%')
            </foreach>
        </if>
        <if test="queryTypeInt==1 and productInfoList!=null and productInfoList.size()>0">
            and
            <foreach collection="productInfoList" index="index" item="p" open="(" separator="or" close=")">
                bp.fullname like CONCAT('%',#{p},'%')
                or bp.usercode like CONCAT('%',#{p},'%')
                or bpx.xcode like CONCAT('%',#{p},'%')
                or bpx1.xcode like CONCAT('%',#{p},'%')
                or sku1.propvalue_names like CONCAT('%',#{p},'%')
                or sku2.propvalue_names like CONCAT('%',#{p},'%')
            </foreach>
        </if>
        <if test="platformPropertiesName!=null and platformPropertiesName!=''">
            and pepsm.platform_properties_name like CONCAT('%',#{platformPropertiesName},'%')
        </if>
        <if test="platformXcode!=null and platformXcode!=''">
            and (pepsm.platform_xcode like CONCAT('%',#{platformXcode},'%') or pepm.platform_xcode like CONCAT('%',#{platformXcode},'%'))
        </if>
        <if test="ptypeName!=null and ptypeName!=''">
            and bp.fullname like CONCAT('%',#{ptypeName},'%')
        </if>
        <if test="xcode!=null and xcode!=''">
            and bpx.xcode like CONCAT('%',#{xcode},'%')
        </if>
        <if test="warehouseSyncEnabled!=null">
            and if(ifnull(psrcfg.state, 0) = 0, true, false)=#{warehouseSyncEnabled}
        </if>
        <if test="allowAutoSync!=null">
            and if(ifnull(psrcfg.state, 0) = 0, true, false)=#{allowAutoSync}
        </if>
        <if test="receSyncState!=null">
            and ifnull(alog.sync_status, 0)=#{receSyncState}
        </if>
    </sql>

    <sql id="xcodeCombo">
        select pepsm.id as id,pepm.id as pid, pepsm.eshop_id ,pepsm.profile_id,
        pepsm.platform_num_id,pepsm.platform_sku_id,pepsm.platform_properties_name, '' as fullName,
        pepsm.platform_json, pepsm.platform_xcode,pepm.platform_xcode as pmplatformXcode,pepsm.platform_pic_url,
        pepm.default_sku_id as platformDefaultSkuId,false as hasProperties,
        false as mainProduct,0 as baseUnitId, 0 as skuId,0 as unitId,bp.id as ptypeId,1 as unit_rate,'' as unit_name,
        bp.fullname as ptypeName,bp.pcategory, pessr.id as ruleId,
        if(ifnull(config.state,0) = 2, false, true) as allowAutoSync,
        if(ifnull(config.state,0) = 1, false, true) as warehouseSyncEnabled,
        bpx.xcode as xcode,
        bp.batchenabled as batchEnabled,bp.protect_days,
        bp.protect_days_unit,
        '' as localProps,exp.mapping_type,alog.sync_status as 'receSyncState',pepm.platform_stock_state as stock_state,bp.usercode as fullbarcode
        from pl_eshop_product_sku pepsm
        left join pl_eshop_product pepm on pepsm.eshop_id = pepm.eshop_id and pepsm.profile_id = pepm.profile_id and pepsm.platform_num_id = pepm.platform_num_id
        left join pl_eshop_product_sku_expand exp on pepsm.profile_id =exp.profile_id and pepsm.eshop_id = exp.eshop_id and pepsm.unique_id=exp.unique_id
        left join base_ptype_xcode bpx on bpx.profile_id=pepsm.profile_id and bpx.xcode= pepsm.platform_xcode
        left join base_ptype bp on bp.profile_id=pepsm.profile_id and bp.id=bpx.ptype_id
        left join base_ptype_combo_detail combo on combo.combo_id = bp.id and  bp.profile_id = combo.profile_id
        left join base_ptype_sku sku on sku.id = combo.sku_id and sku.profile_id = combo.profile_id
        left join pl_eshop_product_sku_rule_config config on pepm.profile_id = config.profile_id  and pepm.eshop_id=config.eshop_id and
        config.platform_num_id=pepsm.platform_num_id and config.platform_properties=pepsm.platform_properties_name and config.target_type = #{targetType}
        <if test="targetType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleTargetTypeEnum@WAREHOUSE">
            and config.warehouse_code=#{warehouseCode}
        </if>
        <choose>
            <when test="targetType!=@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleTargetTypeEnum@NORMAL">
                left join pl_eshop_stock_sync_rule pessr on pessr.profile_id = config.profile_id and pessr.id=config.rule_id and pessr.deleted=0
            </when>
            <otherwise>
                left join pl_eshop_stock_sync_rule pessr on pepsm.platform_xcode = pessr.xcode and
                pessr.profile_id=pepsm.profile_id and pessr.target_type=0 and pessr.pcategory=2  and pessr.deleted=0
            </otherwise>
        </choose>
        left join pl_eshop_stock_sync_abstract_log alog on alog.profile_id=pepsm.profile_id and alog.eshop_id=pepsm.eshop_id
        and alog.platform_num_id=pepsm.platform_num_id and alog.platform_sku_id=pepsm.platform_sku_id and alog.target_type = #{targetType}
        <if test="targetType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleTargetTypeEnum@WAREHOUSE">
            and alog.warehouse_code=#{warehouseCode}
        </if>
        left join pl_eshop_product_mark mark on mark.profile_id=pepsm.profile_id and mark.eshop_id=pepsm.eshop_id and mark.unique_id=pepsm.unique_id and mark.mark_code=1014
        left join pl_eshop_product_mark mark2 on mark2.profile_id=pepsm.profile_id and mark2.eshop_id=pepsm.eshop_id and mark2.unique_id=pepsm.unique_id and mark2.mark_code=1012
        where pepsm.profile_id=#{profileId}
        and pepsm.eshop_id=#{eshopId}
        and pepsm.storage_type=0
        and pepsm.platform_xcode!=''
        and bp.stoped=0
        and bp.deleted=0
        and bp.pcategory=2
        and bp.id>0
        and exp.mapping_type=1
        <if test="stopMarkShow == false">
            and mark2.id is null
        </if>
        <if test="stopMarkShow == true">
            and mark2.id is not null
        </if>
        and mark.id is null
        <if test="platformNumIds!=null and platformNumIds.size()>0">
            and pepsm.platform_num_id in
            <foreach collection="platformNumIds" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="platformSkuIds!=null and platformSkuIds.size()>0">
            and pepsm.platform_sku_id in
            <foreach collection="platformSkuIds" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="queryTypeInt==0 and productInfoList!=null and productInfoList.size()>0">
            and
            <foreach collection="productInfoList" index="index" item="p" open="(" separator="or" close=")">
                pepsm.platform_xcode like CONCAT('%',#{p},'%')
                or pepsm.platform_properties_name like CONCAT('%',#{p},'%')
                or pepm.platform_xcode like CONCAT('%',#{p},'%')
                or pepm.platform_fullname like CONCAT('%',#{p},'%')
            </foreach>
        </if>
        <if test="queryTypeInt==1 and productInfoList!=null and productInfoList.size()>0">
            and
            <foreach collection="productInfoList" index="index" item="p" open="(" separator="or" close=")">
                bp.fullname like CONCAT('%',#{p},'%')
                or bp.usercode like CONCAT('%',#{p},'%')
                or sku.propvalue_names like CONCAT('%',#{p},'%')
                or bpx.xcode like CONCAT('%',#{p},'%')
            </foreach>
        </if>
        <if test="platformPropertiesName!=null and platformPropertiesName!=''">
            and pepsm.platform_properties_name like CONCAT('%',#{platformPropertiesName},'%')
        </if>
        <if test="platformXcode!=null and platformXcode!=''">
            and (pepsm.platform_xcode like CONCAT('%',#{platformXcode},'%') or pepm.platform_xcode like
            CONCAT('%',#{platformXcode},'%'))
        </if>
        <if test="ptypeName!=null and ptypeName!=''">
            and bp.fullname like CONCAT('%',#{ptypeName},'%')
        </if>
        <if test="xcode!=null and xcode!=''">
            and bpx.xcode like CONCAT('%',#{xcode},'%')
        </if>
        <if test="warehouseSyncEnabled!=null">
            and if(ifnull(config.state, 0) = 0, true, false)=#{warehouseSyncEnabled}
        </if>
        <if test="allowAutoSync!=null">
            and if(ifnull(config.state, 0) = 0, true, false)=#{allowAutoSync}
        </if>
        <if test="receSyncState!=null">
            and ifnull(alog.sync_status, 0)=#{receSyncState}
        </if>
    </sql>

    <sql id="xcodeNormal">
        select pepsm.id as id,pepm.id as pid,pepsm.eshop_id ,pepsm.profile_id,
        pepsm.platform_num_id,pepsm.platform_sku_id,pepsm.platform_properties_name, '' as fullName,
        pepsm.platform_json,pepsm.platform_xcode,pepm.platform_xcode as pmplatformXcode,pepsm.platform_pic_url,
        pepm.default_sku_id as platformDefaultSkuId, false as hasProperties,  false as mainProduct,
        bsu.id as baseUnitId, bps.id as skuId,bpu.id as unitId,
        bp.id as ptypeId,bpu.unit_rate,bpu.unit_name, bp.fullname as ptypeName,bp.pcategory,
        pessr.id as rule_id,
        if(ifnull(config.state,0) = 2, false, true) as allowAutoSync,
        if(ifnull(config.state,0) = 1, false, true) as warehouseSyncEnabled,
        bpx.xcode,
        bp.batchenabled as batchEnabled,bp.protect_days,
        bp.protect_days_unit,
        CONCAT_WS(':',
        IF(IFNULL(bps.propvalue_name1,'')='', NULL, bps.propvalue_name1),
        IF(IFNULL(bps.propvalue_name2,'')='', NULL, bps.propvalue_name2),
        IF(IFNULL(bps.propvalue_name3,'')='', NULL, bps.propvalue_name3),
        IF(IFNULL(bps.propvalue_name4,'')='', NULL, bps.propvalue_name4),
        IF(IFNULL(bps.propvalue_name5,'')='', NULL, bps.propvalue_name5),
        IF(IFNULL(bps.propvalue_name6,'')='', NULL, bps.propvalue_name6)) as localProps,
        exp.mapping_type,alog.sync_status as 'receSyncState',pepm.platform_stock_state as stock_state,pf.fullbarcode
        from pl_eshop_product_sku pepsm
        left join pl_eshop_product pepm on pepsm.eshop_id = pepm.eshop_id and pepsm.profile_id = pepm.profile_id and pepsm.platform_num_id = pepm.platform_num_id
        left join pl_eshop_product_sku_expand exp on pepsm.profile_id =exp.profile_id and pepsm.eshop_id = exp.eshop_id and pepsm.unique_id=exp.unique_id
        left join base_ptype_xcode bpx on bpx.profile_id=pepsm.profile_id and bpx.xcode = pepsm.platform_xcode
        left join base_ptype bp on bp.profile_id=pepsm.profile_id and bp.id=bpx.ptype_id
        left join base_ptype_sku bps on pepsm.profile_id = bps.profile_id and bpx.sku_id=bps.id and bpx.ptype_id=bps.ptype_id
        left join base_ptype_unit bpu on bpu.profile_id=pepsm.profile_id and bpu.ptype_id=bpx.ptype_id and bpu.id=bpx.unit_id
        left join base_ptype_unit bsu on bsu.profile_id=pepsm.profile_id and bsu.ptype_id=bpx.ptype_id and bsu.unit_code=1
        left join pl_eshop_product_sku_rule_config config on pepm.profile_id = config.profile_id  and pepm.eshop_id=config.eshop_id and
        config.platform_num_id=pepsm.platform_num_id and config.platform_properties=pepsm.platform_properties_name  and config.target_type = #{targetType}
        <if test="targetType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleTargetTypeEnum@WAREHOUSE">
            and config.warehouse_code=#{warehouseCode}
        </if>
        <choose>
            <when test="targetType!=@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleTargetTypeEnum@NORMAL">
                left join pl_eshop_stock_sync_rule pessr on pessr.profile_id = config.profile_id and pessr.id=config.rule_id  and pessr.deleted=0
            </when>
            <otherwise>
                left join pl_eshop_stock_sync_rule pessr on pepsm.platform_xcode = pessr.xcode and
                pessr.profile_id=pepsm.profile_id and pessr.target_type=0 and pessr.pcategory!=2  and pessr.deleted=0
            </otherwise>
        </choose>
        left join pl_eshop_stock_sync_abstract_log alog on alog.profile_id=pepsm.profile_id and alog.eshop_id=pepsm.eshop_id
        and alog.platform_num_id=pepsm.platform_num_id and alog.platform_sku_id=pepsm.platform_sku_id and alog.target_type = #{targetType}
        <if test="targetType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleTargetTypeEnum@WAREHOUSE">
            and alog.warehouse_code=#{warehouseCode}
        </if>
        left join pl_eshop_product_mark mark on mark.profile_id=pepsm.profile_id and mark.eshop_id=pepsm.eshop_id and mark.unique_id=pepsm.unique_id and mark.mark_code=1014
        left join pl_eshop_product_mark mark2 on mark2.profile_id=pepsm.profile_id and mark2.eshop_id=pepsm.eshop_id and mark2.unique_id=pepsm.unique_id and mark2.mark_code=1012
        left join base_ptype_fullbarcode pf on pf.profile_id=pepsm.profile_id and pf.sku_id=bps.id and pf.unit_id =
        bpu.id and pf.defaulted=1
        where pepsm.profile_id=#{profileId}
        and pepsm.eshop_id=#{eshopId}
        and pepsm.storage_type=0
        and pepsm.platform_xcode!=''
        and bpx.xcode!=''
        and bpx.info_type=0
        and bp.stoped=0
        and bp.deleted=0
        and bp.id>0
        and exp.mapping_type=1
        <if test="stopMarkShow == false">
            and mark2.id is null
        </if>
        <if test="stopMarkShow == true">
            and mark2.id is not null
        </if>
        and mark.id is null
        and bpx.xcode not in (select bpx.xcode from base_ptype_xcode bpx left join base_ptype bp on bp.profile_id=bpx.profile_id and bp.id=bpx.ptype_id
        where bpx.profile_id=#{profileId} and bpx.info_type=1 and bp.stoped=0 and bp.deleted=0 and bp.pcategory = 2)
        <if test="platformNumIds!=null and platformNumIds.size()>0">
            and pepsm.platform_num_id in
            <foreach collection="platformNumIds" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="platformSkuIds!=null and platformSkuIds.size()>0">
            and pepsm.platform_sku_id in
            <foreach collection="platformSkuIds" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="queryTypeInt==0 and productInfoList!=null and productInfoList.size()>0">
            and
            <foreach collection="productInfoList" index="index" item="p" open="(" separator="or" close=")">
                pepsm.platform_xcode like CONCAT('%',#{p},'%')
                or pepsm.platform_properties_name like CONCAT('%',#{p},'%')
                or pepm.platform_xcode like CONCAT('%',#{p},'%')
                or pepm.platform_fullname like CONCAT('%',#{p},'%')
            </foreach>
        </if>
        <if test="queryTypeInt==1 and productInfoList!=null and productInfoList.size()>0">
            and
            <foreach collection="productInfoList" index="index" item="p" open="(" separator="or" close=")">
                bp.fullname like CONCAT('%',#{p},'%')
                or bp.usercode like CONCAT('%',#{p},'%')
                or bpx.xcode like CONCAT('%',#{p},'%')
                or bps.propvalue_names like CONCAT('%',#{p},'%')
            </foreach>
        </if>
        <if test="comboXcodeList!=null and comboXcodeList.size()>0">
            and pepsm.platform_xcode not in
            <foreach collection="comboXcodeList" index="index" item="xcode" open="(" separator="," close=")">
                #{xcode}
            </foreach>
        </if>
        <if test="platformPropertiesName!=null and platformPropertiesName!=''">
            and pepsm.platform_properties_name like CONCAT('%',#{platformPropertiesName},'%')
        </if>
        <if test="platformXcode!=null and platformXcode!=''">
            and (pepsm.platform_xcode like CONCAT('%',#{platformXcode},'%') or pepm.platform_xcode like CONCAT('%',#{platformXcode},'%'))
        </if>
        <if test="ptypeName!=null and ptypeName!=''">
            and bp.fullname like CONCAT('%',#{ptypeName},'%')
        </if>
        <if test="xcode!=null and xcode!=''">
            and bpx.xcode like CONCAT('%',#{xcode},'%')
        </if>
        <if test="warehouseSyncEnabled!=null">
            and if(ifnull(config.state, 0) = 0, true, false)=#{warehouseSyncEnabled}
        </if>
        <if test="allowAutoSync!=null">
            and if(ifnull(config.state, 0) = 0, true, false)=#{allowAutoSync}
        </if>
        <if test="receSyncState!=null">
            and ifnull(alog.sync_status, 0)=#{receSyncState}
        </if>
    </sql>

    <select id="queryNotifySyncRelationMessage"
            resultType="com.wsgjp.ct.sale.common.entity.stock.StockSyncRelationMessage">
        select peps.platform_num_id,peps.platform_sku_id,peps.profile_id,peps.eshop_id as otypeId,
        peps.platform_xcode,peps.platform_properties_name as platformProperties,
        ex.mapping_type,pep.default_sku_id,
        if(ex.mapping_type=0, map.sku_id, if(cb.id>0, 0, bpx.sku_id)) as skuId,
        if(ex.mapping_type=0, map.ptype_id, if(cb.id>0, cb.id, bpx.ptype_id)) as ptypeId,
        if(ex.mapping_type=0, map.unit_id, if(cb.id>0, 0, bpx.unit_id)) as unitId,
        if(ex.mapping_type=0, mbp.fullname, if(cb.id>0, cb.fullname, bs.fullname)) as ptypeName,
        if(ex.mapping_type=0, mbp.pcategory, if(cb.id>0, 2, bs.pcategory)) as pcategory,
        if(ex.mapping_type=0, mbpx.xcode, if(cb.id>0, cb.usercode, bpx.xcode)) as xcode
        from pl_eshop_product_sku peps
        left join pl_eshop_product pep on peps.profile_id=pep.profile_id and peps.eshop_id=pep.eshop_id and peps.platform_num_id=pep.platform_num_id
        left join pl_eshop_product_sku_expand ex on peps.profile_id=ex.profile_id and peps.eshop_id =ex.eshop_id and peps.unique_id=ex.unique_id
        left join pl_eshop_product_sku_mapping map on peps.profile_id=map.profile_id and peps.eshop_id =map.eshop_id and peps.unique_id=map.unique_id
        left join base_ptype mbp on map.ptype_id=mbp.id and map.profile_id=mbp.profile_id
        left join base_ptype_xcode mbpx on mbpx.profile_id=map.profile_id and mbpx.ptype_id=map.ptype_id and map.sku_id=mbpx.sku_id and map.unit_id=mbpx.unit_id and mbpx.defaulted=1
        left join base_ptype cb on cb.profile_id=peps.profile_id and peps.platform_xcode=cb.usercode and cb.pcategory=2
        left join base_ptype_xcode bpx on bpx.profile_id=peps.profile_id and bpx.xcode=peps.platform_xcode
        left join base_ptype bs on bs.profile_id=bpx.profile_id and bs.id=bpx.ptype_id
        where peps.profile_id=#{profileId} and peps.eshop_id=#{eshopId}
        and peps.platform_num_id in
        <foreach collection="platformNumIdList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and peps.platform_sku_id in
        <foreach collection="platformSkuIdList" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="queryProductMappingForStockCount" resultType="java.lang.Integer">
        select count(1)
        from pl_eshop_product_sku peps
        where peps.profile_id=#{profileId} and peps.eshop_id=#{otypeId}
        and peps.platform_num_id in
        <foreach collection="platformNumIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="queryProductMappingForStock"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncManagePageData">
        select peps.platform_num_id,peps.platform_sku_id,peps.profile_id,peps.eshop_id as otypeId,
        peps.platform_xcode,peps.platform_properties_name as platformProperties,
        ex.mapping_type,pep.default_sku_id,pep.platform_fullname,
        if(ex.mapping_type=0, map.sku_id, if(cb.id>0, 0, bpx.sku_id)) as skuId,
        if(ex.mapping_type=0, map.ptype_id, if(cb.id>0, cb.id, bpx.ptype_id)) as ptypeId,
        if(ex.mapping_type=0, map.unit_id, if(cb.id>0, 0, bpx.unit_id)) as unitId,
        if(ex.mapping_type=0, mbp.fullname, if(cb.id>0, cb.fullname, bs.fullname)) as ptypeName,
        if(ex.mapping_type=0, mbp.pcategory, if(cb.id>0, 2, bs.pcategory)) as pcategory,
        if(ex.mapping_type=0, mbpx.xcode, if(cb.id>0, cb.usercode, bpx.xcode)) as xcode
        from pl_eshop_product_sku peps
        left join pl_eshop_product pep on peps.profile_id=pep.profile_id and peps.eshop_id=pep.eshop_id and peps.platform_num_id=pep.platform_num_id
        left join pl_eshop_product_sku_expand ex on peps.profile_id=ex.profile_id and peps.eshop_id =ex.eshop_id and peps.unique_id=ex.unique_id
        left join pl_eshop_product_sku_mapping map on peps.profile_id=map.profile_id and peps.eshop_id =map.eshop_id and peps.unique_id=map.unique_id
        left join base_ptype mbp on map.ptype_id=mbp.id and map.profile_id=mbp.profile_id
        left join base_ptype_xcode mbpx on mbpx.profile_id=map.profile_id and mbpx.ptype_id=map.ptype_id and map.sku_id=mbpx.sku_id and map.unit_id=mbpx.unit_id and mbpx.defaulted=1
        left join base_ptype cb on cb.profile_id=peps.profile_id and peps.platform_xcode=cb.usercode and cb.pcategory=2
        left join base_ptype_xcode bpx on bpx.profile_id=peps.profile_id and bpx.xcode=peps.platform_xcode
        left join base_ptype bs on bs.profile_id=bpx.profile_id and bs.id=bpx.ptype_id
        where peps.profile_id=#{profileId} and peps.eshop_id=#{otypeId}
        and peps.platform_num_id in
        <foreach collection="platformNumIds" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        limit #{pageIndex},#{pageSize}
    </select>

</mapper>
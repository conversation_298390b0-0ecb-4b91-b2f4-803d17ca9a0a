package com.wsgjp.ct.sale.biz.shopsale.service;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wsgjp.ct.baseinfo.core.dao.entity.Dtype;
import com.wsgjp.ct.bill.core.handle.entity.BillAccountUpdate;
import com.wsgjp.ct.bill.core.handle.entity.BillEntity;
import com.wsgjp.ct.bill.core.handle.entity.BillLog;
import com.wsgjp.ct.bill.core.handle.entity.enums.BillCreateType;
import com.wsgjp.ct.bill.core.handle.entity.enums.BillPostState;
import com.wsgjp.ct.bill.core.handle.entity.enums.PayStateEnum;
import com.wsgjp.ct.bill.core.handle.entity.enums.Vchtypes;
import com.wsgjp.ct.bill.core.handle.service.BillCoreService;
import com.wsgjp.ct.framework.enums.AssertsChangeType;
import com.wsgjp.ct.framework.enums.AssertsSourceOperation;
import com.wsgjp.ct.framework.enums.AssertsSourceType;
import com.wsgjp.ct.open.notify.sdk.executor.NgpOpenMsgPushExecutor;
import com.wsgjp.ct.sale.biz.analysiscloud.api.request.SaveInvoiceRequest;
import com.wsgjp.ct.sale.biz.analysiscloud.api.response.SaveInvoiceResponse;
import com.wsgjp.ct.sale.biz.api.BillAPI;
import com.wsgjp.ct.sale.biz.api.SysConfigApi;
import com.wsgjp.ct.sale.biz.api.baseinfo.BaseInfoApi;
import com.wsgjp.ct.sale.biz.api.invoice.InvoiceApi;
import com.wsgjp.ct.sale.biz.api.response.GeneralResponse;
import com.wsgjp.ct.sale.biz.bill.exception.PostBillException;
import com.wsgjp.ct.sale.biz.bill.mapper.*;
import com.wsgjp.ct.sale.biz.bill.model.dao.PrintCountDAO;
import com.wsgjp.ct.sale.biz.bill.model.dto.ZyPtypeResult;
import com.wsgjp.ct.sale.biz.bill.model.entity.BillLoadRequest;
import com.wsgjp.ct.sale.biz.bill.model.request.BillUpdateFieldRequest;
import com.wsgjp.ct.sale.biz.bill.model.request.BillZyAuditEnableRequest;
import com.wsgjp.ct.sale.biz.bill.model.zy.ChangeTotalDetailEntity;
import com.wsgjp.ct.sale.biz.bill.model.zy.SubmitBillMessage;
import com.wsgjp.ct.sale.biz.bill.service.AddBillLogs;
import com.wsgjp.ct.sale.biz.bill.service.BillInterface;
import com.wsgjp.ct.sale.biz.bill.service.impl.GoodsBillLoadServiceImpl;
import com.wsgjp.ct.sale.biz.bill.service.impl.GoodsBillServiceImpl;
import com.wsgjp.ct.sale.biz.bill.service.impl.ZyBillServiceImpl;
import com.wsgjp.ct.sale.biz.bill.utils.CurrentContext;
import com.wsgjp.ct.sale.biz.common.util.CommonUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.biz.member.config.PosRedisLockTimeConfig;
import com.wsgjp.ct.sale.biz.member.mapper.SsCardAssertBillMapper;
import com.wsgjp.ct.sale.biz.member.mapper.SsPreferentialBillMapper;
import com.wsgjp.ct.sale.biz.member.mapper.SsPreferentialGoodsDetailMapper;
import com.wsgjp.ct.sale.biz.member.mapper.SsVipMapper;
import com.wsgjp.ct.sale.biz.member.model.dto.vip.AddOrEditVipDTO;
import com.wsgjp.ct.sale.biz.member.model.dto.vip.VipAssertsBillDetailDto;
import com.wsgjp.ct.sale.biz.member.model.dto.vip.VipAssertsBillDto;
import com.wsgjp.ct.sale.biz.member.model.entity.preferential.SsPreferentialBill;
import com.wsgjp.ct.sale.biz.member.model.entity.preferential.SsPreferentialGoodsDetail;
import com.wsgjp.ct.sale.biz.member.model.entity.vip.CustomerPhone;
import com.wsgjp.ct.sale.biz.member.model.entity.vip.SsVip;
import com.wsgjp.ct.sale.biz.member.model.entity.vip.SsVipBill;
import com.wsgjp.ct.sale.biz.member.model.entity.vip.VipScoreInfo;
import com.wsgjp.ct.sale.biz.member.service.ISsCardService;
import com.wsgjp.ct.sale.biz.member.service.ISsVipService;
import com.wsgjp.ct.sale.biz.member.utils.PosRedisLockerUtils;
import com.wsgjp.ct.sale.biz.member.utils.StringUtils;
import com.wsgjp.ct.sale.biz.record.enums.VchtypeEnum;
import com.wsgjp.ct.sale.biz.shopsale.common.FeignResult;
import com.wsgjp.ct.sale.biz.shopsale.common.ResultHandler;
import com.wsgjp.ct.sale.biz.shopsale.constanst.BillSaveResultTypeEnum;
import com.wsgjp.ct.sale.biz.shopsale.constanst.PriceTypeEnum;
import com.wsgjp.ct.sale.biz.shopsale.enums.BillBusinessTypeEnum;
import com.wsgjp.ct.sale.biz.shopsale.mapper.BasePtypeMapper;
import com.wsgjp.ct.sale.biz.shopsale.mapper.BillMapper;
import com.wsgjp.ct.sale.biz.shopsale.mapper.ShopSaleBillMapper;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.base.QiniuConfigDTO;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.*;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.cashbox.CashBoxPaymentDTO;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.*;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.base.AtypePayDto;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.base.BaseInfoOtypeRequest;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.base.BaseInfoPtypeRequest;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.base.PosBuyer;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.bill.*;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.ptype.*;
import com.wsgjp.ct.sale.biz.shopsale.model.vo.bill.BillAuditResponse;
import com.wsgjp.ct.sale.biz.shopsale.model.vo.bill.PtypeResponse;
import com.wsgjp.ct.sale.biz.shopsale.model.vo.ptype.BatchNo;
import com.wsgjp.ct.sale.monitor.MonitorService;
import com.wsgjp.ct.sale.monitor.entity.MonitorTypeEnum;
import com.wsgjp.ct.sale.platform.utils.RestHttpUtil;
import com.wsgjp.ct.sale.sdk.payment.biz.AggregatePaymentService;
import com.wsgjp.ct.sale.sdk.payment.entity.request.AggregatePaymentRequest;
import com.wsgjp.ct.sale.sdk.payment.entity.request.PayOrderQueryRequest;
import com.wsgjp.ct.sale.sdk.payment.entity.response.PayCallBackInfo;
import com.wsgjp.ct.sale.sdk.payment.entity.response.PayStatusInfo;
import com.wsgjp.ct.sale.sdk.payment.entity.response.PaymentInfo;
import com.wsgjp.ct.sale.sdk.payment.entity.response.PaymentResponse;
import com.wsgjp.ct.sale.sdk.payment.enums.PayStatusEnum;
import com.wsgjp.ct.sale.sdk.pos.biz.MemberAssertsChangeService;
import com.wsgjp.ct.sale.sdk.pos.entity.asserts.MemberAssert;
import com.wsgjp.ct.sale.sdk.pos.entity.asserts.MemberAssertsChange;
import com.wsgjp.ct.sale.sdk.pos.entity.asserts.VipAsserts;
import com.wsgjp.ct.sale.sdk.stock.biz.StockChangeService;
import com.wsgjp.ct.sale.sdk.stock.enums.StockChangeTypeEnum;
import com.wsgjp.ct.sale.sdk.stock.parameter.StockChangeQueueDto;
import com.wsgjp.ct.support.business.pubsystemlog.PubSystemLogService;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.localcache.ThreadLocalCache;
import com.wsgjp.ct.support.log.service.LogService;
import com.wsgjp.ct.support.thread.ThreadPool;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import com.wsgjp.ct.support.utils.QiniuUtils;
import ngp.idgenerator.UId;
import ngp.utils.*;
import org.apache.http.util.TextUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.CastUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import utils.StringUtil;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.Arrays.asList;

@Service
public class BillService {
    @Autowired
    BasePtypeMapper basePtypeMapper;
    @Autowired
    EtypeBillMapper baseEtypeMapper;
    @Autowired
    BillAPI billAPI;
    @Autowired
    SysConfigApi systemConfigApi;
    @Autowired
    BaseInfoApi baseInfoAPI;
    @Autowired
    ISsCardService cardService;
    @Autowired
    TaotaoguLogService taotaoguLogService;
    @Autowired
    AggregatePaymentService aggregatePaymentService;
    @Autowired
    SsVipMapper vipMapper;
    @Autowired
    SsPreferentialBillMapper preferentialBillMapper;
    @Autowired
    SsPreferentialGoodsDetailMapper preferentialGoodsDetailMapper;
    @Autowired
    ISsVipService vipService;
    @Autowired
    private CashBoxService cashBoxService;

    @Autowired
    SsCardAssertBillMapper assertBillMapper;

    @Autowired
    BillInterface billInterface;

    @Autowired
    ShopSaleBillMapper shopSaleBillMapper;

    @Autowired
    TdBillCoreMapper tdBillCoreMapper;

    @Autowired
    GoodsBillLoadServiceImpl billLoadService;
    @Autowired
    GoodsBillServiceImpl goodsBillService;

    @Autowired
    BillMapper billMapper;

    @Autowired
    ZyBillMapper zyBillMapper;

    @Autowired
    ZyBillServiceImpl zyBillService;

    @Autowired
    private AddBillLogs addBillLogs;

    @Autowired
    private PosRedisLockTimeConfig posRedisLockTimeConfig;

    @Autowired
    private BillCoreService billCoreService;

    @Autowired
    private InvoiceApi invoiceApi;

    @Autowired
    private StockChangeService stockChangeService;

    @Autowired
    private PosSisClientService sisClientService;

    @Autowired
    private MemberAssertsChangeService memberAssertsChangeService;


    private static final Logger logger = LoggerFactory.getLogger(BillService.class);
    private static final String encKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDRQhkM0rM6hNBySk2bTxY" +
            "+om6xHqUpwpaqgebm4J/62/YKCwYaKCNyCj097xV4DSjykt2XiPlrJJaCpBe/bOetSe7fTjIZooik2KwfCI+4nTDIix5i+ifdW" +
            "+dwybU33op4gFimKRB5t2+jVszCzSnThewHT4dDpT5OJ2fIrqnBCwIDAQAB";
    private static final String signKey = "by+k0sms0own1c3_7qrmokh6p53bk6a!";

    private static final String payCallBackUrlDev = "http://d7.mygjp.com.cn:18084/sale/shopsale/bill/payResultCallback";
    private static final String payCallBackUrl = "https://ngp-openwms.wsgjp.com/sale/shopsale/bill/payResultCallback";
    private static final String payCallBackHuiFuUrl = "https://ngp-openwms.wsgjp" +
            ".com/sale/shopsale/bill/payResultCallback";

    private static final String appSecret = "zxsawqdfsaxcsdxcc";

    //参数固定，不可修改
    public String sendSMS(Map request) {
        String returnString = "";
        try {
            String message = "{\"code\":\"" + request.get("code") + "\"}";
            String signname = "网上管家婆";
            String phonelist = (String) request.get("phone");
            String companyname = "sms01";
            String template_code = "SMS_461730153";

            String timestamp = DateUtils.formatDate(DateUtils.getDate(), "yyyy-MM-dd HH:mm:ss");
            String p = String.format("signname=%s&message=%s&phonelist=%s&companyname=%s&template_code=%s",
                    HttpUtils.urlEncode(signname),
                    HttpUtils.urlEncode(message),
                    HttpUtils.urlEncode(phonelist),
                    HttpUtils.urlEncode(companyname),
                    HttpUtils.urlEncode(template_code));
            p = RsaUtils.encryptWithCSharp(p, encKey);
            String sign = Md5Utils.md5(timestamp + p + signKey);

            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Type", "text/xml;charset=utf-8");
            headers.add("SOAPAction", "https://tempuri.org/SendInternalMessage");
            headers.add("Accept", "*/*");
            String postBody = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                    "<soap:Envelope xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns:xsd=\"http://www.w3" +
                    ".org/2001/XMLSchema\" xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n" +
                    "  <soap:Body>\n" +
                    "    <SendInternalMessage xmlns=\"https://tempuri.org/\">\n" +
                    "      <timestamp>" + timestamp + "</timestamp>\n" +
                    "      <p>" + p + "</p>\n" +
                    "      <sign>" + sign + "</sign>\n" +
                    "    </SendInternalMessage>\n" +
                    "  </soap:Body>\n" +
                    "</soap:Envelope>";
            HttpEntity<String> entity = new HttpEntity<>(postBody, headers);
            ResponseEntity<String> response = RestHttpUtil.getTemplate().exchange("https://smsnew.mygjp.com" +
                    ".cn/webService/MessageWebService.asmx", HttpMethod.POST, entity, String.class);
            returnString = response.getBody();
        } catch (Exception exception) {
            logger.info("短信发送失败" + exception.getMessage());
            throw new RuntimeException("短信发送失败");
        }
        return returnString;
    }

    public List<HashMap> getBaseInfoClass() {
        Map request = new HashMap<>();
        request.put("basicname", "Ptype");
        return (List<HashMap>) baseInfoAPI.getBaseInfoClass(request).getData();
    }


    public BaseInfoPtypeRequest saveBaseInfoPtype(Map request) {
        Map baseInfoRequest = (Map) request.get("ptype");
        baseInfoRequest.put("profileId", CurrentUser.getProfileId());
        BaseInfoPtypeRequest result = ResultHandler.result(baseInfoAPI.saveBaseInfoPtype(baseInfoRequest));

        BaseInfoOtypeRequest baseInfoOtypeRequest = JsonUtils.toObject(JsonUtils.toJson(request.get("otype")),
                BaseInfoOtypeRequest.class);
        BaseInfoOtypeRequest.UndefinedListDTO undefinedListDTO = baseInfoOtypeRequest.undefinedList.get(0);
        basePtypeMapper.insetPriceToOtypePriceList(CurrentUser.getProfileId(), UId.newId(), new BigInteger(result.id)
                , BigInteger.ZERO,
                new BigInteger(result.units.get(0).id), new BigInteger(undefinedListDTO.xtypeId),
                new BigDecimal(undefinedListDTO.getSalePrice()),
                new BigDecimal(undefinedListDTO.getSaleOtypeVipPrice()));

        return result;
    }


    public List<PtypeExChangeDTO> getStockQty(Map<String, List<PtypeStockRequest>> stockRequest) {
        return billAPI.getStockQty(stockRequest).getData();
    }

    public List<HashMap> getPriceFromOtypePriceList(PtypeOtypePriceDto param) {
        List<GoodsDetailDTO> goodsDetailDTOList = param.getGoods();
        for (GoodsDetailDTO goodsDetailDTO : goodsDetailDTOList) {
            if (goodsDetailDTO.getSkuPrice() == 0) {
                goodsDetailDTO.setSkuId(BigInteger.valueOf(0));
            }
        }
        List<HashMap> hashMaps = basePtypeMapper.getPriceFromOtypePriceList(param.getGoods(), param.getOtypeId(),
                CurrentUser.getProfileId());
        return hashMaps;
    }

    /**
     * 获取商品价格,可选择查询零售价或者价格本价格，
     * 或者同时查询
     */
    @Transactional(rollbackFor = Exception.class)
    public List<PtypePriceDTO> getPtypePrice(PtypePriceRequestDTO request) {
        List<PtypePriceRequestDTO.Ptype> goodsList = request.getGoodsList();
        //啥也不查
        if (request.getOtypeId() == null && !request.getSearchRetailPrice()) {
            return new ArrayList<>();
        }
        if (goodsList == null || goodsList.isEmpty()) {
            return new ArrayList<>();
        }
        //首先查询商品是否开启sku定价管理
        List<BigInteger> ptypeIds = goodsList.stream()
                .map(PtypePriceRequestDTO.Ptype::getPtypeId)
                .filter(ptypeId -> ptypeId != null && !ptypeId.equals(BigInteger.ZERO))
                .distinct()
                .collect(Collectors.toList());
        if (ptypeIds.isEmpty()) {
            return new ArrayList<>();
        }
        BigInteger profileId = CurrentUser.getProfileId();
        List<PtypePriceRequestDTO.Ptype> list = basePtypeMapper.selectPtypeSkuPriceEnabled(ptypeIds, profileId);
        //当商品未开启sku定价管理，则价格本中的skuId为0
        for (PtypePriceRequestDTO.Ptype ptype : list) {
            goodsList.stream()
                    .filter(goods -> goods.getPtypeId().equals(ptype.getPtypeId()))
                    .forEach(goods -> goods.setSkuPrice(ptype.getSkuPrice()));
        }
        //查询价格
        return basePtypeMapper.getPtypePrice(request, profileId);
    }

    /**
     * 获取七牛viewConfig，用于客户端url组装
     *
     * @return
     */
    public QiniuConfigDTO getQiniuViewConfig() {
        return baseInfoAPI.getQiniuViewConfig().getData();
    }

    /**
     * 根据序列号获取商品信息
     * 注意，这里仅仅是查询了商品，并未处理价格、库存等
     * 在pos端中，用于校验序列号是否存在（当使用该序列号查不到商品，说明序列号就不存在）
     */
    public PtypeResponse getGoodsInfoBySnWithoutPrice(PtypeCodeParamDTO ptypeCodeParamDTO) {
        return ResultHandler.result(billAPI.getPtypeInfoBySno(ptypeCodeParamDTO));
    }

    /**
     * 获取商品价格(价格本预售价)
     *
     * @param request
     * @return
     */
    public List<PtypeExChangeDTO> getPtypePrice(PtypePriceRequest request) {
        List<PtypeExChangeDTO> dataList = ResultHandler.result(billAPI.getPtypePrice(request));
        //COST类价格 没有进行单位关联。 其他均带有单位信息
        if (request.getPriceTypeEnum().equalsIgnoreCase(PriceTypeEnum.COST.name())) {
            int sysDigitalPrice = (int) systemConfigApi.getSysData().getData().get("sysDigitalPrice");
            dataList.forEach((priceItem -> request.getPtypeList().forEach(ptypeItem -> {
                if (priceItem.getPtypeId().equals(ptypeItem.getPtypeId())) {
                    if (priceItem.getPrice() != null && priceItem.getPrice().compareTo(BigDecimal.ZERO) == 1) {
                        priceItem.setPrice(priceItem.getPrice().multiply(ptypeItem.getUnitRate() == null ?
                                BigDecimal.ONE : ptypeItem.getUnitRate()).setScale(sysDigitalPrice,
                                RoundingMode.HALF_UP));
                    }
                }
            })));
        }
        return dataList;
    }


    /**
     * 查询商品列表中各商品下所有sku的库存
     */
    private void getGoodsListStock(List<PtypeResponse> ptypeList, BigInteger ktypeId) {
        //构建请求参数
        ArrayList<PtypeStockRequest> stockRequestArray = new ArrayList<>();
        for (int i = 0; i < ptypeList.size(); i++) {
            PtypeResponse response = ptypeList.get(i);
            //库存
            PtypeStockRequest model = new PtypeStockRequest();
            model.setRowIndex(i);
            model.setPtypeId(response.getId());
            model.setSkuId(response.getSku().getId()); //todo 这里库存不涉及单位吗？
            //todo 已经确认过单据组的接口中对应的实体类为PtypeParamDTO，其中有unitId，所以之前的请求方式存在问题
            model.setBatchenabled(false); //todo 批次库存or全部库存？
            model.setKtypeId(ktypeId);
            stockRequestArray.add(model);
        }
        //商品库存
        Map<String, List<PtypeStockRequest>> stockRequest = new HashMap<>();
        stockRequest.put("ptypeList", stockRequestArray);
        FeignResult<List<PtypeExChangeDTO>> ptypeStockResult = billAPI.getStockQty(stockRequest);
        List<PtypeExChangeDTO> ptypeStockList = ptypeStockResult.getData();
        for (int i = 0; i < ptypeList.size(); i++) {
            PtypeResponse response = ptypeList.get(i);
            //赋值库存,根据ptypeId和skuId筛选
            if (ptypeStockList != null && !ptypeStockList.isEmpty()) {
                PtypeExChangeDTO ptypeExChangeDTO = ptypeStockList.get(i);
                response.setStockQty(ptypeExChangeDTO.getStockQty());
            } else {
                response.setStockQty(BigDecimal.ZERO);
            }
        }
    }

    /**
     * 根据仓库+ 商品+ 数量 推算出各批次和数量信息
     * 用于自动带出商品的批次
     *
     * @param ptypeBatchParamQtyDTO
     * @return
     */
    public List<PtypeBatchDTO> getPtypeBatchQty(PtypeBatchParamQtyDTO ptypeBatchParamQtyDTO) {
        return ResultHandler.result(billAPI.getPtypeBatchQty(ptypeBatchParamQtyDTO));
    }

    /**
     * 根据id获取套餐信息
     */
    public Map getComboById(BigInteger id) {
        FeignResult<Map<String, Object>> result = baseInfoAPI.comboGet(id);
        Map<String, Object> data = result.getData();
        FeignResult<Map> responseFeign = new FeignResult<>();
        if (data == null) {
            responseFeign.setData(null);
            responseFeign.setCode(result.getCode());
            responseFeign.setMessage(result.getMessage());
            responseFeign.setTraceId(result.getTraceId());
            return ResultHandler.result(responseFeign);
        }
        //todo 这里似乎自带套餐明细行？后续待优化
        getSuitDetailList(Collections.singletonList(data));
        responseFeign.setData(data);
        responseFeign.setCode(result.getCode());
        responseFeign.setMessage(result.getMessage());
        responseFeign.setTraceId(result.getTraceId());
        return ResultHandler.result(responseFeign);
    }


    /**
     * 获取套餐明细
     *
     * @param
     * @return
     */
    private List<Map> getSuitDetailList(List<Map> suitList) {
        ArrayList<String> suitIds = new ArrayList();
        for (int i = 0; i < suitList.size(); i++) {
            Map response = suitList.get(i);
            suitIds.add(String.valueOf(response.get("id")));
        }
        FeignResult<Map> suitDetailList = baseInfoAPI.getPtypeSuitDetailList(suitIds);
        for (int i = 0; i < suitList.size(); i++) {
            Map response = suitList.get(i);
            response.put("suitDetails", suitDetailList.getData().get(String.valueOf(response.get("id"))));
        }
        return suitList;
    }

    /**
     * 获取商品类的单据和会员电话
     *
     * @param billRequestDto
     * @return
     */
    public GoodsBillDTO getGoodsBill(Map billRequestDto) {
        billRequestDto.put("sourceTag", "POS");
        GoodsBillDTO goodsBill = getBill(billRequestDto);
        if (goodsBill.getVipCardId() != null && !goodsBill.getVipCardId().equals(BigInteger.ZERO)) {
            BigInteger cpaId = null;
            if (goodsBill.getCreateType() == BillCreateType.FROM_SELF_SYSTEM) {
                if (goodsBill.getVchtype() == Vchtypes.SaleBackBill) {
                    cpaId = goodsBill.getRefundAccountId();
                } else {
                    cpaId = goodsBill.getProductAccountId();
                }
                if (cpaId == null || cpaId.equals(BigInteger.ZERO)) {
                    throw new RuntimeException("产品账户信息id不正确");
                }
            }
            CustomerPhone vipBillInfo = vipMapper.selectCustomerPhoneFromSSVIPBill(goodsBill.getVipCardId(),
                    CurrentUser.getProfileId(), cpaId);
            // 手机号解密
            List<PosBuyer> buyers =
                    sisClientService.batchDecryptBuyers(Collections.singletonList(vipBillInfo.getBuyerId()));
            if (buyers != null && !buyers.isEmpty()) {
                vipBillInfo.setPhone(buyers.get(0).getCustomerReceiverPhone());
            }
            goodsBill.setVipBillInfo(vipBillInfo);
        }
        isInStockSn(goodsBill);
        // 查收银台id
        BigInteger cashierId = vipMapper.getSsVipBill(goodsBill.getVchcode(), CurrentUser.getProfileId());
        goodsBill.setCashierId(cashierId);
        goodsBill.setInvoiceSign(CommonUtil.getInvoiceSign(goodsBill.getVchcode()));
        return goodsBill;
    }

    private GoodsBillDTO getGoodsBill(BigInteger vchcode) {
        BillLoadRequest billLoadRequest = new BillLoadRequest();
        billLoadRequest.setVchcode(vchcode);
        GoodsBillDTO result = null;
        try {
            result = billInterface.getBillByVchcode(billLoadRequest);
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
        if (result == null) {
            throw new RuntimeException("未获取到单据信息");
        }
        return result;
    }

    private GoodsBillDTO getBill(Map map) {
        if ("GoodsTrans".equals(map.get("vchtype"))) {
            map.put("vchtype", "GoodsTransInternalBill");
        }
        if ("Sale".equals(map.get("vchtype"))) {
            map.put("vchtype", "SaleBill");
        }
        if ("SaleBack".equals(map.get("vchtype"))) {
            map.put("vchtype", "SaleBackBill");
        }
        if ("SaleChange".equals(map.get("vchtype"))) {
            map.put("vchtype", "SaleChangeBill");
        }
        BillLoadRequest billLoadRequest = JsonUtils.toObject(JsonUtils.toJson(map), BillLoadRequest.class);
        GoodsBillDTO result = null;
        try {
            result = billInterface.getBillByVchcode(billLoadRequest);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e.getMessage());
        }
        if (result == null) {
            throw new RuntimeException("未获取到单据信息");
        }
        //回填赠金
        getPreferentialDetail(result.getVchcode().toString(), result);

        List<SsPreferentialBill> preferentialTotal =
                preferentialBillMapper.selectPreferentialByVchcode(result.getVchcode(), result.getProfileId());

        BigDecimal mathSumBigDecimal =
                preferentialTotal.stream().map(SsPreferentialBill::getPreferentialTotal).reduce(BigDecimal.ZERO,
                        BigDecimal::add); //BigDecimal类型

        result.setPreferentialTotal(mathSumBigDecimal);

        return result;
    }

    public void isInStockSn(GoodsBillDTO goodsBill) {
        List<GoodsDetailDTO> outDetail = goodsBill.getOutDetail();
        ArrayList<String> sns = new ArrayList<>();
        for (GoodsDetailDTO goods : outDetail) {
            if (goods.getSnenabled() != 0) {
                List<SerialNoDTO> serialNoList = goods.getSerialNoList();
                for (SerialNoDTO snDto : serialNoList) {
                    sns.add(snDto.getSnno());
                }
            }
        }
        BigInteger ktypeId = goodsBill.getKtypeId();
        if (!sns.isEmpty() && ktypeId != null) {
            List<String> inStockSn = basePtypeMapper.getInStockSn(sns, ktypeId.toString(), CurrentUser.getProfileId());
            for (GoodsDetailDTO goods : outDetail) {
                if (goods.getSnenabled() != 0) {
                    List<SerialNoDTO> serialNoList = goods.getSerialNoList();
                    for (SerialNoDTO snDto : serialNoList) {
                        if (inStockSn.contains(snDto.getSnno())) {
                            snDto.setInTheStock(true);
                        }
                    }
                }
            }
        }
    }

    /**
     * 获取商品类单据、会员电话、优惠辅助表 资产变动单
     *
     * @return
     */
    public GoodsDetailsAndPreferential getGoodsDetailsAndPreferential(BigInteger vchcode) {
        GoodsDetailsAndPreferential response = new GoodsDetailsAndPreferential();
        List<BigInteger> vchList = shopSaleBillMapper.getVchcodeListBySourceVchcode(vchcode,
                CurrentUser.getProfileId());
        List<ResponseGoodsBill> backGoodsBillList = new ArrayList<>();

        // 这里是构建退货单的
        vchList.forEach(code -> {
            HashMap<String, BigInteger> request = new HashMap<>();
            ResponseGoodsBill responseGoodsBill = new ResponseGoodsBill();
            request.put("vchcode", code);
            GoodsBillDTO goodsBill = getBill(request);
            responseGoodsBill.setGoodsbill(goodsBill);
            backGoodsBillList.add(responseGoodsBill);
        });
        if (!vchList.isEmpty()) {
            List<SsPreferentialBill> preferentialBillList = preferentialBillMapper.selectByVchcodeList(vchList,
                    CurrentUser.getProfileId());
            List<SsPreferentialGoodsDetail> preferentialGoodsDetailList =
                    preferentialGoodsDetailMapper.selectByVchcodeList(vchList, CurrentUser.getProfileId());
            List<VipAssertsBillDto> vipAssertsBillDtos =
                    assertBillMapper.selectAssertBillList(CurrentUser.getProfileId(), vchList);
            for (ResponseGoodsBill responseGoodsBill : backGoodsBillList) {
                BigInteger vch = responseGoodsBill.getGoodsbill().getVchcode();
                Vchtypes vchtype = responseGoodsBill.getGoodsbill().getVchtype();
                if (!preferentialBillList.isEmpty() && Vchtypes.SaleChangeBill == vchtype) {
                    List<SsPreferentialBill> list =
                            preferentialBillList.stream().filter(x -> x.getVchcode().equals(vch)).collect(Collectors.toList());
                    responseGoodsBill.setPreferentialBills(list);
                } else {
                    responseGoodsBill.setPreferentialBills(new ArrayList<>());
                }
                if (!preferentialGoodsDetailList.isEmpty() && Vchtypes.SaleChangeBill == vchtype) {
                    List<SsPreferentialGoodsDetail> list =
                            preferentialGoodsDetailList.stream().filter(x -> x.getVchcode().equals(vch)).collect(Collectors.toList());
                    responseGoodsBill.setPreferentialGoodsDetails(list);
                } else {
                    responseGoodsBill.setPreferentialGoodsDetails(new ArrayList<>());
                }
                if (vipAssertsBillDtos != null && !vipAssertsBillDtos.isEmpty()) {
                    for (VipAssertsBillDto vipAssertsBillDto : vipAssertsBillDtos) {
                        if (vipAssertsBillDto.getVchcode().equals(vch)) {
                            responseGoodsBill.setVipAssertsBillDto(vipAssertsBillDto);
                        }
                    }
                }
            }
        }

        // 这里是出库单的
        GoodsBillDTO goodsBill = getGoodsBill(vchcode);
        BigInteger payOutNo = vipMapper.selectOutNoWithVchcode(vchcode, CurrentUser.getProfileId());

        List<SsPreferentialBill> preferentialBills = preferentialBillMapper.selectByVchcode(vchcode,
                CurrentUser.getProfileId());
        List<SsPreferentialGoodsDetail> preferentialGoodsDetails =
                preferentialGoodsDetailMapper.selectByVchcode(vchcode, CurrentUser.getProfileId());

        preferentialBills = preferentialBills != null ? preferentialBills : new ArrayList<>();
        preferentialGoodsDetails = preferentialGoodsDetails != null ? preferentialGoodsDetails : new ArrayList<>();

        VipAssertsBillDto vipAssertsBillDto = assertBillMapper.selectAssertBill(CurrentUser.getProfileId(), vchcode);
        // 出库单serialNoList处理，需要把inoutId、inoutDetailId清空，否则保存退货单会报td_bill_detail_serialno主键冲突
        if (goodsBill.getOutDetail() != null && !goodsBill.getOutDetail().isEmpty()) {
            goodsBill.getOutDetail().parallelStream().forEach(x -> {
                for (SerialNoDTO serialNoDTO : x.getSerialNoList()) {
                    serialNoDTO.setInoutId(null);
                    serialNoDTO.setInoutDetailId(null);
                }
            });
        }
        if (null != goodsBill.getVipCardId()) {
            CustomerPhone vipBillInfo = vipMapper.selectCustomerPhoneFromSSVIPBill(goodsBill.getVipCardId(),
                    CurrentUser.getProfileId(), null);
            goodsBill.setVipBillInfo(vipBillInfo);
            response.setVipBillInfo(vipBillInfo);
        }
        response.setBackGoodsBillList(backGoodsBillList);
        response.setBillDetail(goodsBill);
        response.setPayOutNo(payOutNo);
        response.setPreferentialBills(preferentialBills);
        response.setPreferentialGoodsDetails(preferentialGoodsDetails);
        response.setVipAssertsBillDto(vipAssertsBillDto);

        return response;
    }

    private void getPreferentialDetail(String vchcode, GoodsBillDTO result) {
        if (!result.getOutDetail().isEmpty()) {
            getPreferentialDetail(vchcode, result.getOutDetail());
        }
        if (!result.getInDetail().isEmpty()) {
            getPreferentialDetail(vchcode, result.getInDetail());
        }
    }

    private void getPreferentialDetail(String vchcode, List<GoodsDetailDTO> detailList) {
        List<BigInteger> detailIds = detailList.stream().map(GoodsDetailDTO::getDetailId).collect(Collectors.toList());
        List<SsPreferentialBill> preferentialBills = preferentialBillMapper.selectAllByOrderDetailBills(detailIds,
                vchcode, CurrentUser.getProfileId());
        Map<BigInteger, GoodsDetailDTO> comboMap =
                detailList.stream().filter(GoodsDetailDTO::getComboRow).collect(Collectors.toMap(GoodsDetailDTO::getComboRowId, detail -> detail));
        Map<BigInteger, BigDecimal> map =
                preferentialBills.stream().collect(Collectors.toMap(SsPreferentialBill::getDetailId,
                        SsPreferentialBill::getPreferentialTotal));
        detailList.forEach(vo -> {
            vo.setPosGiftPrice(map.get(vo.getDetailId()));
            if (vo.getComboRowParId() != null && !vo.getComboRowParId().equals(BigInteger.ZERO)) {
                GoodsDetailDTO comboRow = comboMap.get(vo.getComboRowParId());
                if (comboRow != null) {
                    BigDecimal comboPosGiftPrice = comboRow.getPosGiftPrice();
                    if (comboPosGiftPrice == null) {
                        comboPosGiftPrice = BigDecimal.ZERO;
                    }
                    BigDecimal detailPosGiftPrice = vo.getPosGiftPrice();
                    if (detailPosGiftPrice == null) {
                        detailPosGiftPrice = BigDecimal.ZERO;
                    }
                    comboRow.setPosGiftPrice(comboPosGiftPrice.add(detailPosGiftPrice));
                }
            }
        });
    }

    /*
     * 提交商品类单据（调用的是JXC的接口）
     * */
    public BillSaveResultDTO jxcSubmitGoodsBill(Map goodsBillDTO) {
        return ResultHandler.result(billAPI.submitGoodsBill(goodsBillDTO));
    }

    /**
     * 验证单据
     *
     * @param request
     * @return
     */
    public BillSaveResultDTO validationGoodsBill(LinkedHashMap<String, Object> request) {
        GoodsBillDTO bill = JsonUtils.toObject(JsonUtils.toJson(request.get("goodsBill")), GoodsBillDTO.class);
        return billInterface.validationGoodsBill(bill);
    }

    /**
     * 获取商品单据
     */
    public AbstractBillDTO getGoodsBillDTO(LinkedHashMap<String, Object> request) {
        request.put("vchtype", "SaleBill".equals(request.get("vchtype")) ? "Sale" : request.get("vchtype"));
        return getBill(request);
    }

    /**
     * 提交商品类的单据
     *
     * @param request
     * @return
     */
    public BillSaveResultDTO submitGoodsBill(PosBill request) {
        setDetailIdToDetails(request.getGoodsBill());
        // 保存单据和会员资产
        return billInterface.submitBill(request.getGoodsBill(), request);
    }

    /**
     * 提交商品类的单据
     *
     * @param requests
     * @return
     */
    public List<BigInteger> submitGoodsBillList(List<PosBill> requests) {
        if (requests == null || requests.isEmpty()) {
            return new ArrayList<>();
        }
        // 找到requests 中已存在的vchcode，直接装入successVchcodes并从requests中删除
        List<BigInteger> vchcodes = requests.stream().map(x -> x.getGoodsBill().getVchcode()).collect(Collectors.toList());
        List<BigInteger> containVchcodes = billMapper.selectAllByVchcodes(vchcodes, CurrentUser.getProfileId());
        requests.removeIf(x -> containVchcodes.contains(x.getGoodsBill().getVchcode()));
        List<BigInteger> successVchcodes = new ArrayList<>(containVchcodes);
        for (PosBill request : requests) {
            GoodsBillDTO bill = request.getGoodsBill();
            setDetailIdToDetails(bill);
            // 同步保存单据
            bill.setAsyncSave(false);
            BillSaveResultDTO billSaveResultDTO = billInterface.submitBill(bill, request);
            if (billSaveResultDTO.getResultType() == BillSaveResultTypeEnum.SUCCESS) {
                successVchcodes.add(billSaveResultDTO.getVchcode());
            }
        }
        return successVchcodes;
    }

    /**
     * 自用系统提交商品类的单据
     *
     * @param request
     * @return
     */
    public BillSaveResultDTO zYsubmitGoodsBill(PosBill request) {
        // 保存单据和会员资产
        logger.info("[自用系统构建单据信息]--保存参数:{}", JsonUtils.toJson(request));
        GoodsBillDTO bill = request.getGoodsBill();
        setDetailIdToDetails(bill);
        if ((bill.getOutDetail() == null || bill.getOutDetail().isEmpty()) && (bill.getInDetail() == null || bill.getInDetail().isEmpty())) {
            return CurrentContext.createZyErrorResult(bill, BillSaveResultTypeEnum.ERROR, "请把单据明细补充完整再提交");
        }
        // 查询库里是否有该单据编号的单子，有的话不更新，直接返回
        BigInteger vchcode = billMapper.selectVchcodeByBillNumber(bill.getNumber(), CurrentUser.getProfileId());
        if (vchcode != null && !vchcode.equals(BigInteger.ZERO)) {
            logger.info("[自用系统构建单据信息]--单据编号已存在，单据编号:{}", bill.getNumber());
            GoodsBillDTO goodsBill = getGoodsBill(vchcode);
            return goodsBillService.createResult(goodsBill);
        }
        // 提交单据前校验或回填
        String re = zYBeforeSubmit(bill);
        if (StringUtils.isNotBlank(re)) {
            return CurrentContext.createZyErrorResult(bill, BillSaveResultTypeEnum.ERROR, re);
        }
        BillSaveResultDTO billSaveResultDTO = zYCheckDetail(bill);
        if (billSaveResultDTO != null) {
            return billSaveResultDTO;
        }
        // 回填商品税率和成本单价
        buildTaxRate(bill.getOutDetail());
        buildTaxRate(bill.getInDetail());
        // 重新计算价格
        changeTotal(bill);
        // 将库存商品的服务开始时间和结束时间清空
        zyBillService.emptyServiceTime(bill.getOutDetail());
        zyBillService.emptyServiceTime(bill.getInDetail());
        // 默认不核算
        if (bill.getWhetherPost() == null) {
            bill.setWhetherPost(false);
        }
        // 结算是否反向
        if (bill.getVchtype() == Vchtypes.SaleBackBill) {
            bill.setBalanceReverse(true);
        }
        // 创建会员
        if (bill.getCustomerId() != null) {
            // 查询有没有这个会员，有的话就不创建，没有就创建
            SsVip vip = vipService.getVipByCustomerId(bill.getCustomerId());
            if (vip == null || vip.getId() == null) {
                AddOrEditVipDTO dto = new AddOrEditVipDTO();
                dto.setCustomerId(bill.getCustomerId());
                dto.setRequiredPhone(false);
                dto.setOperationSource(AssertsSourceType.PC);
                try {
                    BigInteger vipId = vipService.addOrEditVip(dto);
                    bill.setVipCardId(vipId);
                } catch (Exception e) {
                    throw new RuntimeException(e.getMessage());
                }
            } else {
                bill.setVipCardId(vip.getId());
            }
        }
        BillSaveResultDTO saveResultDTO = billInterface.submitBill(bill, request);
        // 单据保存成功后给筑梦推送消息(因目前只有筑梦一家需要推消息，先写死，后续多了改到apollo里配置)
        if (saveResultDTO.getResultType() == BillSaveResultTypeEnum.SUCCESS && saveResultDTO.getVchcode() != null
                && (Objects.equals(bill.getCommissionBtypeId(), new BigInteger("6964566508393403331"))
                || Objects.equals(bill.getCommissionBtypeId(), new BigInteger("941184667796108634")))) {
            try {
                ngpOpenMsgPush(bill, "ngp.bill.after_audit");
            } catch (Exception e) {
                logger.info("通知筑梦异常", e);
                bill.setLogMsg(String.format("推送消息给筑梦失败，【单据编号：%s，失败原因：%s】", bill.getNumber(), e.getMessage()));
                addBillLogs.addBillLogs(bill);
            }
        }
        try {
            if (saveResultDTO.getResultType() == BillSaveResultTypeEnum.SUCCESS && saveResultDTO.getVchcode() != null && bill.getFinanceProcState() == 0
                    && (bill.getVchtype() == Vchtypes.SaleBackBill || (bill.getVchtype() == Vchtypes.SaleChangeBill && bill.getCurrencyBillTotal().compareTo(BigDecimal.ZERO) < 0))) {
                //检查审核
                BillZyAuditEnableRequest auditEnableRequest = new BillZyAuditEnableRequest();
                auditEnableRequest.setVchcode(saveResultDTO.getVchcode());
                auditEnableRequest.setBusinessType(BillBusinessTypeEnum.SaleNormal);
                auditEnableRequest.setVchtype(VchtypeEnum.codeOf(bill.getVchtype().getVchtype()));
                AuditEnableResponse enableResponse = checkAuditEnable(auditEnableRequest);
                if (enableResponse.getAuditEnable()) {
                    //开启审核
                    BillAuditRequest auditRequest = new BillAuditRequest();
                    auditRequest.setPostState(BillPostState.CONFIRM_WAIT);
                    auditRequest.setVchcode(saveResultDTO.getVchcode());
                    auditRequest.setVchtype(bill.getVchtype().getVchtype());
                    auditRequest.setBusinessType(com.wsgjp.ct.sale.common.enums.BillBusinessTypeEnum.StoreRetail);
                    auditRequest.setProfileId(CurrentUser.getProfileId().toString());
                    logger.info("单据准备提交审核");
                    BillAuditResponse auditResponse = submitAuditNew(auditRequest);
                    logger.info("单据已提交审核");
                    if (auditResponse.getSuccessed()) {
                        logger.info("单据:" + saveResultDTO.getBillNumber() + "提交审核成功");
                    } else {
                        logger.info("单据:" + saveResultDTO.getBillNumber() + "提交审核失败：" + auditResponse.getMessage());
                        bill.setLogMsg(String.format("用户提交审核失败，【单据编号：%s，失败原因：%s】", saveResultDTO.getBillNumber(),
                                auditResponse.getMessage()));
                        addBillLogs.addBillLogs(bill);
                    }
                }
            }
        } catch (Exception e) {
            logger.info("单据提交审核异常", e);
            bill.setLogMsg(String.format("用户提交审核失败，【单据编号：%s，失败原因：%s】", bill.getNumber(), e.getMessage()));
            addBillLogs.addBillLogs(bill);
        }
        return saveResultDTO;
    }

    private void changeTotal(GoodsBillDTO bill) {
        BigDecimal currencyBillTotal = BigDecimal.ZERO;
        if (bill.getOutDetail() != null && !bill.getOutDetail().isEmpty()) {
            for (GoodsDetailDTO outDetail : bill.getOutDetail()) {
                if (BigDecimal.ZERO.compareTo(outDetail.getTaxRate()) == 0) {
                    continue;
                }
                ChangeTotalDetailEntity detailEntity = new ChangeTotalDetailEntity();
                detailEntity.setFinanceProcState(bill.getFinanceProcState());
                detailEntity.setDetailId(outDetail.getDetailId());
                if (bill.getFinanceProcState() == 1) {
                    detailEntity.setTotal(outDetail.getBuyerDisedTaxedTotal());
                } else {
                    detailEntity.setTotal(outDetail.getCurrencyDisedTaxedTotal());
                }
                detailEntity.setServiceStartTime(outDetail.getServiceStartTime());
                detailEntity.setServiceEndTime(outDetail.getServiceEndTime());
                detailEntity.setServiceExpireTime(outDetail.getServiceExpireTime());
                currencyBillTotal = zyBillService.normalPriceCalculation(outDetail, detailEntity, currencyBillTotal,
                        true, BigDecimal.ZERO);
            }
        }
        if (bill.getInDetail() != null && !bill.getInDetail().isEmpty()) {
            for (GoodsDetailDTO inDetail : bill.getInDetail()) {
                if (BigDecimal.ZERO.compareTo(inDetail.getTaxRate()) == 0) {
                    continue;
                }
                ChangeTotalDetailEntity detailEntity = new ChangeTotalDetailEntity();
                detailEntity.setFinanceProcState(bill.getFinanceProcState());
                if (bill.getFinanceProcState() == 1) {
                    detailEntity.setTotal(inDetail.getBuyerDisedTaxedTotal());
                } else {
                    detailEntity.setTotal(inDetail.getCurrencyDisedTaxedTotal());
                }
                detailEntity.setServiceStartTime(inDetail.getServiceStartTime());
                detailEntity.setServiceEndTime(inDetail.getServiceEndTime());
                detailEntity.setServiceExpireTime(inDetail.getServiceExpireTime());
                currencyBillTotal = zyBillService.normalPriceCalculation(inDetail, detailEntity, currencyBillTotal,
                        false, BigDecimal.ZERO);
            }
        }
    }

    private void buildTaxRate(List<GoodsDetailDTO> details) {
        if (details != null && !details.isEmpty()) {
            Set<BigInteger> ptypeIds = new HashSet<>();
            details.forEach(detail -> {
                ptypeIds.add(detail.getPtypeId());
            });
            List<GoodsDetailDTO> taxRates = zyBillMapper.getTaxRateByPtypeId(ptypeIds, CurrentUser.getProfileId());
            details.forEach(detailDTO -> {
                for (GoodsDetailDTO taxRate : taxRates) {
                    if (detailDTO.getPtypeId().equals(taxRate.getPtypeId())) {
                        detailDTO.setTaxRate(taxRate.getTaxRate());
                        detailDTO.setCostPrice(taxRate.getCostPrice());
                        break;
                    }
                }
            });
        }
    }

    private BillSaveResultDTO zYCheckDetail(GoodsBillDTO bill) {
        List<GoodsDetailDTO> outDetail = bill.getOutDetail();
        List<GoodsDetailDTO> inDetail = bill.getInDetail();
        // 错误信息
        List<GoodsDetailDTO> nullUserCodes = new ArrayList<>();
        List<GoodsDetailDTO> nullDetails = new ArrayList<>();
        List<GoodsDetailDTO> moreDetails = new ArrayList<>();
        buildPtype(outDetail, nullUserCodes, nullDetails, moreDetails);
        buildPtype(inDetail, nullUserCodes, nullDetails, moreDetails);
        bill.setOutDetail(outDetail);
        bill.setInDetail(inDetail);

        List<BillSaveExceptionDTO> exceptionList = new ArrayList<>();
        if (!nullUserCodes.isEmpty()) {
            BillSaveExceptionDTO exceptionInfo = new BillSaveExceptionDTO();
            exceptionInfo.setMessage("下面商品的id为空，请处理");
            CurrentContext.createBillSaveExceptionDetail(exceptionInfo, nullUserCodes);
            exceptionList.add(exceptionInfo);
        }
        if (!nullDetails.isEmpty()) {
            BillSaveExceptionDTO exceptionInfo = new BillSaveExceptionDTO();
            exceptionInfo.setMessage("下面商品id无法找到对应的商品信息，请处理");
            CurrentContext.createBillSaveExceptionDetail(exceptionInfo, nullDetails);
            exceptionList.add(exceptionInfo);
        }
        if (!moreDetails.isEmpty()) {
            BillSaveExceptionDTO exceptionInfo = new BillSaveExceptionDTO();
            exceptionInfo.setMessage("下面商品id的商品有多个ptypeId或skuId，请处理");
            CurrentContext.createBillSaveExceptionDetail(exceptionInfo, moreDetails);
            exceptionList.add(exceptionInfo);
        }
        if (!exceptionList.isEmpty()) {
            return CurrentContext.createDetailErrResult(bill, BillSaveResultTypeEnum.ERROR, exceptionList);
        }
        return null;
    }

    private void buildPtype(List<GoodsDetailDTO> details, List<GoodsDetailDTO> nullUserCodes,
                            List<GoodsDetailDTO> nullDetails, List<GoodsDetailDTO> moreDetails) {
        for (GoodsDetailDTO goodsDetailDTO : details) {
            if (goodsDetailDTO.getPtypeId() == null || goodsDetailDTO.getPtypeId().equals(BigInteger.ZERO)) {
                nullUserCodes.add(goodsDetailDTO);
                continue;
            }
            List<ZyPtypeResult> zyPtypeResults =
                    basePtypeMapper.getPtypeIdAndSkuIdByPtypeId(goodsDetailDTO.getPtypeId(),
                            CurrentUser.getProfileId());
            if (zyPtypeResults == null || zyPtypeResults.isEmpty()) {
                nullDetails.add(goodsDetailDTO);
            }
            if (zyPtypeResults != null && zyPtypeResults.size() > 1) {
                moreDetails.add(goodsDetailDTO);
            }
            if (zyPtypeResults != null && zyPtypeResults.size() == 1) {
                goodsDetailDTO.setPtypeId(zyPtypeResults.get(0).getPtypeId());
                goodsDetailDTO.setSkuId(zyPtypeResults.get(0).getSkuId());
                goodsDetailDTO.setSelfSystem(true);
                goodsDetailDTO.setPcategory(zyPtypeResults.get(0).getPcategory());
                goodsDetailDTO.setPtypeStandard(zyPtypeResults.get(0).getStandard());
                goodsDetailDTO.setPtypeType(zyPtypeResults.get(0).getPtypeType());
                if (goodsDetailDTO.getUnitId() == null || goodsDetailDTO.getUnitId().equals(BigInteger.ZERO)) {
                    goodsDetailDTO.setUnitId(zyPtypeResults.get(0).getUnitId());
                }
            }
        }
    }

    private String zYBeforeSubmit(GoodsBillDTO bill) {
        if (bill.getCommissionBtypeId() != null && !bill.getCommissionBtypeId().equals(BigInteger.ZERO)) {
            // 需要分摊佣金
            BigDecimal needSharePtypeCommissionTotalIn = needSharePtypeCommissionTotal(bill.getInDetail());
            BigDecimal needSharePtypeCommissionTotalOut = needSharePtypeCommissionTotal(bill.getOutDetail());
            // 计算佣金
            BigDecimal currencyPtypeCommissionTotal = BigDecimal.ZERO;
            if (bill.getOutDetail() != null && !bill.getOutDetail().isEmpty()) {
                for (int i = 0; i < bill.getOutDetail().size(); i++) {
                    GoodsDetailDTO goodsDetailDTO = bill.getOutDetail().get(i);
                    BigDecimal commissionTotal =
                            goodsDetailDTO.getCurrencyDisedTaxedTotal().multiply(goodsDetailDTO.getPtypeCommissionRate()).setScale(2, RoundingMode.HALF_UP);
                    commissionTotal = i == 0 ? commissionTotal.add(needSharePtypeCommissionTotalOut) : commissionTotal;
                    goodsDetailDTO.setPtypeCommissionTotal(commissionTotal);
                    currencyPtypeCommissionTotal = currencyPtypeCommissionTotal.add(commissionTotal);
                }
            }
            if (bill.getInDetail() != null && !bill.getInDetail().isEmpty()) {
                for (int i = 0; i < bill.getInDetail().size(); i++) {
                    GoodsDetailDTO goodsDetailDTO = bill.getInDetail().get(i);
                    BigDecimal commissionTotal =
                            goodsDetailDTO.getCurrencyDisedTaxedTotal().multiply(goodsDetailDTO.getPtypeCommissionRate()).setScale(2, RoundingMode.HALF_UP);
                    commissionTotal = i == 0 ? commissionTotal.add(needSharePtypeCommissionTotalIn) : commissionTotal;
                    goodsDetailDTO.setPtypeCommissionTotal(commissionTotal);
                    currencyPtypeCommissionTotal = currencyPtypeCommissionTotal.subtract(commissionTotal);
                }
            }
            bill.setCurrencyPtypeCommissionTotal(currencyPtypeCommissionTotal);
        }
        // 更新退货单往来单位、门店
        if ((bill.getVchtype() == Vchtypes.SaleBackBill || bill.getVchtype() == Vchtypes.SaleChangeBill) && bill.getInDetail() != null && !bill.getInDetail().isEmpty()) {
            BigInteger sourceVchcode = bill.getInDetail().get(0).getSourceVchcode();
            if (sourceVchcode != null && !sourceVchcode.equals(BigInteger.ZERO)) {
                GoodsBillDTO dto = zyBillMapper.getAccBtypeAndOtypeByVchcode(sourceVchcode, CurrentUser.getProfileId());
                if (dto == null) {
                    dto = zyBillMapper.getTdBtypeAndOtypeByVchcode(sourceVchcode, CurrentUser.getProfileId());
                }
                if (dto == null) {
                    return "原单下没有往来单位和门店信息：sourceVchcode：" + sourceVchcode;
                }
                bill.setBtypeId(dto.getBtypeId());
                bill.setOtypeId(dto.getOtypeId());
            }
        }
        if (bill.getPayment() != null) {
            for (PayMentDTO payMentDTO : bill.getPayment()) {
                if (payMentDTO.getOutNo() != null && !payMentDTO.getOutNo().isEmpty()) {
                    payMentDTO.setOutNo(payMentDTO.getOutNo().trim());
                }
            }
        }

        return null;
    }

    private BigDecimal needSharePtypeCommissionTotal(List<GoodsDetailDTO> details) {
        BigDecimal ptypeCommissionTotal = BigDecimal.ZERO;
        BigDecimal detailCommissionTotal = BigDecimal.ZERO;
        if (details != null && !details.isEmpty()) {
            for (GoodsDetailDTO entity : details) {
                BigDecimal total = entity.getCurrencyDisedTaxedTotal().multiply(entity.getPtypeCommissionRate());
                ptypeCommissionTotal = ptypeCommissionTotal.add(total);
                BigDecimal detailTotal =
                        entity.getCurrencyDisedTaxedTotal().multiply(entity.getPtypeCommissionRate()).setScale(2,
                                RoundingMode.HALF_UP);
                detailCommissionTotal = detailCommissionTotal.add(detailTotal);

            }
        }
        return ptypeCommissionTotal.subtract(detailCommissionTotal);
    }


    /**
     * 提交单据前，加detailId
     *
     * @param goodsBill 单据
     */
    private void setDetailIdToDetails(GoodsBillDTO goodsBill) {
        List<GoodsDetailDTO> outDetail = goodsBill.getOutDetail();
        if (!outDetail.isEmpty()) {
            for (GoodsDetailDTO goodsDetailDTO : outDetail) {
                if (goodsDetailDTO.getDetailId() == null || Objects.equals(goodsDetailDTO.getDetailId(),
                        BigInteger.ZERO)) {
                    goodsDetailDTO.setDetailId(UId.newId());
                }
            }
        }
        List<GoodsDetailDTO> inDetail = goodsBill.getInDetail();
        if (!inDetail.isEmpty()) {
            for (GoodsDetailDTO goodsDetailDTO : inDetail) {
                if (goodsDetailDTO.getDetailId() == null || Objects.equals(goodsDetailDTO.getDetailId(),
                        BigInteger.ZERO)) {
                    goodsDetailDTO.setDetailId(UId.newId());
                }
            }
        }
    }

    /**
     * 设置单据默认商品单位
     *
     * @param goods
     * @param billType
     */
    private void setPTypeDefaultUnit(PtypeResponse goods, int billType) {
        PtypeUnitPrice unit = null;
        BigInteger unitId;
        switch (billType) {
            case 1:
                unitId = goods.getSaleDefaultUnit();
                break;
            case 2:
                unitId = goods.getBuyDefaultUnit();
                break;
            case 3:
                unitId = goods.getStockDefaultUnit();
                break;
            default:
                unitId = goods.getRetailDefaultUnit();

        }
        for (PtypeUnitPrice unitPrice : goods.getUnitList()) {
            if (!unitPrice.getId().equals(unitId)) {
                continue;
            }
            unit = unitPrice;
        }
        if (unit == null) {
            return;
        }
        goods.setUnit(unit);
    }


    /**
     * 获取商品对应的批次号列表
     * <br>
     * 请求参数如下:
     * <pre>
     * {
     *     "ktypeId":"仓库id",
     *     "ptypeId":"商品id",
     *     "skuId":"商品skuId",
     *     "costMode":1,
     *     "showZone":false,
     *     "pageIndex":1,
     *     "pageSize":200
     * }
     * </pre>
     * 返回的列表实体类如下:
     * <pre>
     * {
     *     "batchId":"批次id",
     *     "comboRowId":null,
     *     "ktypeId":"614502464955146240",
     *     "ptypeId":"624848790163087512",
     *     "skuId":"624878502746841240",
     *     "batchNo":"批次号",
     *     "produceDate":"2021-09-29T16:00:00Z",
     *     "expireDate":"2021-10-19T16:00:00Z",
     *     "stockQty":10,
     *     "unitQty":null,
     *     "stockPrice":3.6,
     *     "stockTotal":36,
     *     "protectDays":20,
     *     "batchTime":"2021-09-30T06:57:25Z",
     *     "costId":"625016989672509092"
     * }
     * </pre>
     *
     * @param requestBody 参见请求参数
     * @return 参见返回参数
     */
    public PageResponse<BatchNo> getGoodsBatchList(Map<String, Object> requestBody) {
        return ResultHandler.result(billAPI.getGoodsBatchList(requestBody));
    }


    /**
     * 获取套餐列表
     *
     * @return
     */
    public Map<String, Object> getComboList(LinkedHashMap<String, Object> requestParams) {
        FeignResult<Map<String, Object>> result = baseInfoAPI.getComboList(requestParams);
        Map<String, Object> data = result.getData();
        ArrayList<Map> suitList = CastUtils.cast(data.get("list"));
        List<Map> suitDetailList = getSuitDetailList(suitList);
        data.put("list", suitDetailList);
        return ResultHandler.result(result);
    }

    /**
     * 按照编号/名称/条码模糊搜索，获取商品信息(Sku级别)列表
     * 请求参数
     * <pre>
     *     {
     *     "refresh":true,
     *     "pageSize":6,
     *     "pageIndex":1,
     *     "queryParams":{
     *         "filterkey":"recordquick",
     *         "filtervalue":"模糊搜索内容",
     *         "existedSku":true,
     *         "onlyBaseUnit":false,
     *         "ptypeStoped":false,
     *         "skuStoped":false,
     *         "ktypeId":"",
     *         "btypeId":"",
     *         "priceTypeEnum":"SALE",
     *         "billDate":"",
     *         "onlyDefaultXcode":true,
     *         "onlyDefaultFBC":true
     *     }
     * }
     * </pre>
     */
    public PageResponse<PtypeResponse> getSkuList(PageRequest<Map<String, Object>> requestParams) {
        FeignResult<PageResponse<PtypeUnitSkuPo>> result = baseInfoAPI.getSkuList(requestParams);
        PageResponse<PtypeUnitSkuPo> data = result.getData();
        if (data == null || data.getList() == null || data.getList().isEmpty()) {
            return null;
        }
        PageResponse<PtypeResponse> response = new PageResponse<>();
        response.setTotal(data.getTotal());
        response.setPageIndex(data.getPageIndex());
        response.setPageSize(data.getPageSize());
        List<PtypeResponse> list = data.getList().stream().map(skuInfo -> {
            //将数据类型转换一下，方便pos端直接使用
            PtypeResponse ptype = new PtypeResponse();
            //商品类型 0为实物，1位虚拟，4.3版本增加
            ptype.setPcategory(skuInfo.getPtype().getPcategory());
            //商品id
            ptype.setId(skuInfo.getPtype().getId());
            //商品编号
            ptype.setUsercode(skuInfo.getPtype().getUsercode());
            //商品名称
            ptype.setFullname(skuInfo.getPtype().getFullname());
            //商品条码
            ptype.setFullbarcode(skuInfo.getFullbarcode() != null ? skuInfo.getFullbarcode().getFullbarcode() : "");
            //商品类型
            ptype.setPtypeType(skuInfo.getPtype().getPtypeType());
            //规格
            ptype.setStandard(skuInfo.getPtype().getStandard());
            //税率
            ptype.setTaxRate(skuInfo.getPtype().getTaxRate());
            //商品是否支持序列号0为未开启，1严格序列号,2是宽松序列号
            ptype.setSnenabled(skuInfo.getPtype().getSnenabled());
            //是否启用属性管理
            ptype.setPropenabled(skuInfo.getPtype().getPropenabled());
            //是否启用批次
            ptype.setBatchenabled(skuInfo.getPtype().getBatchenabled());
            //保质期天数(天):0不启用保质期 大于1启用
            ptype.setProtectDays(skuInfo.getPtype().getProtectDays());
            //成本算法
            ptype.setCostMode(skuInfo.getPtype().getCostMode());
            //商品图片
            PtypePic pic = skuInfo.getPtypePic();
            if (pic != null) {
                ptype.setPicUrl(pic.getPicUrl());
            }
            //价格
            ptype.setCurrencyPrice(skuInfo.getPrice().getRetailPrice());
            //单位
            PtypeUnitPrice unit = new PtypeUnitPrice();
            unit.setId(new BigInteger(skuInfo.getUnit().getId()));
            unit.setUnitCode(skuInfo.getUnit().getUnitCode());
            unit.setUnitName(skuInfo.getUnit().getUnitName());
            unit.setUnitRate(skuInfo.getUnit().getUnitRate());
            ptype.setUnit(unit);
            //参考成本价（这里其实是pos端取的是零售价）
            ptype.setCostPrice(skuInfo.getPtype().getCostPrice());
            //折扣
            ptype.setDiscount(BigDecimal.ONE);
            //model.setUnitList()
            //单位商品重量
            ptype.setWeight(skuInfo.getPtype().getWeight());
            //重量单位
            ptype.setWeightUnit(skuInfo.getPtype().getWeightUnit());
            //sku
            ptype.setSku(skuInfo.getSku());
            //skuPrice
            ptype.setSkuPrice(skuInfo.getPtype().getSkuPrice());
            return ptype;
        }).collect(Collectors.toList());
        response.setList(list);
//        Map<String, Object> queryParams = requestParams.getQueryParams();
//        BigInteger ktypeId = new BigInteger(((String) queryParams.getOrDefault("ktypeId", "0")));
//        getGoodsListStock(list, ktypeId);
        return response;
    }

    /**
     * 查询商品和套餐列表
     * 4.7新增根据序列号精确搜索商品
     * 当页码为1时，则搜索序列号，后续分页不用搜索
     *
     * @param request
     * @return
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public PageResponse<PtypeResponse> selectPtypeAndCombo(PageRequest<SelectGoodsRequestDTO> request) {
        long sTime = System.currentTimeMillis();
        MonitorService monitorService = com.wsgjp.ct.sale.platform.utils.BeanUtils.getBean(MonitorService.class);
        BigInteger profileId = CurrentUser.getProfileId();
        if (request.getQueryParams().getFullBarCode() == null) {
            request.getQueryParams().setFullBarCode(false);
        }
        SelectGoodsRequestDTO queryParams = request.getQueryParams();
        Page<Object> page = PageHelper.startPage(request.getPageIndex(), request.getPageSize());

        List<PtypeResponse> list = new ArrayList<>();

        // 根据fullBarCode参数决定使用哪种查询方式
        if (Boolean.TRUE.equals(queryParams.getFullBarCode())) {
            // 使用优化的二次查询方式：先过滤获取ID，再获取详细信息
            // 先使用filterPtypeInfo过滤出基本商品信息
            List<PtypeResponse> filteredItems = basePtypeMapper.filterPtypeInfo(
                    profileId,
                    queryParams.getBtypeId(),
                    queryParams.getFilterValue(),
                    queryParams.getFullBarCode(),
                    queryParams.getSkuBarCode(),
                    queryParams.getPtypeLimit(),
                    queryParams.getTypeId());

            // 收集过滤后的商品ID
            List<BigInteger> ptypeIds = filteredItems.stream()
                    .map(PtypeResponse::getId)
                    .collect(Collectors.toList());
            ///skuid filteredItems的PtypeSku的id
            List<BigInteger> skuIds = filteredItems.stream()
                    .map(item -> Optional.ofNullable(item.getSku()).map(PtypeSku::getId).orElse(null))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            ///unitId
            List<BigInteger> unitIds = filteredItems.stream()
                    .map(item -> Optional.ofNullable(item.getUnit()).map(PtypeUnitPrice::getId).orElse(null))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 如果没有找到匹配的商品，直接返回空结果
            if (!ptypeIds.isEmpty()) {
                // 使用找到的商品ID获取完整信息
                list = basePtypeMapper.selectPtypeByIds(
                        profileId, ptypeIds, skuIds, unitIds,
                        queryParams.getOtypeId(),
                        queryParams.getKtypeId(),
                        queryParams.getPtypeLimit(),
                        true);
            }


//            list = basePtypeMapper.selectPtypeByBarcode(
//                    profileId,
//                    queryParams.getBtypeId(),
//                    queryParams.getOtypeId(),
//                    queryParams.getFilterValue(),
//                    queryParams.getFullBarCode(),
//                    queryParams.getKtypeId(),
//                    queryParams.getPtypeLimit(),
//                    queryParams.getTypeId(),
//                    false,
//                    queryParams.getSkuBarCode());
        } else {
            // 使用原有的直接查询方式
            list = basePtypeMapper.selectPtypeList(
                    profileId,
                    queryParams.getBtypeId(),
                    queryParams.getOtypeId(),
                    queryParams.getFilterValue(),
                    queryParams.getFullBarCode(),
                    queryParams.getKtypeId(),
                    queryParams.getPtypeLimit(),
                    queryParams.getTypeId(),
                    false,
                    queryParams.getSkuBarCode());
        }

        PageResponse<PtypeResponse> pageInfo = new PageResponse<>();
        pageInfo.setTotal(page.getTotal());
        pageInfo.setList(list);
        fillPic(list);
        //填充商品标签ID
        fillPtypeLabelIds(list, profileId);
        //获取套餐详情
        List<PtypeResponse> comboList = list.stream().filter(PtypeResponse::getComboRow).collect(Collectors.toList());
        getComboDetails(comboList);
        if (Boolean.TRUE.equals(queryParams.getSearchBySn()) && request.getPageIndex() <= 1 && !TextUtils.isEmpty(queryParams.getFilterValue())) {
            //根据序列号搜索商品
            PtypeResponse ptypeBySno = basePtypeMapper.selectPtypeBySerialNo(profileId, queryParams.getKtypeId(),
                    queryParams.getOtypeId(), queryParams.getFilterValue());
            if (ptypeBySno != null) {
                fillPic(Collections.singletonList(ptypeBySno));
                list.add(0, ptypeBySno);
            }
        }
        if (pageInfo.getList() != null && !pageInfo.getList().isEmpty()) {
            monitorService.recordTP(MonitorTypeEnum.PL_API_SALE_POS_SELECT_PTYPE_TP_TIME.getTopic(), "shopType", "pos"
                    , (System.currentTimeMillis() - sTime) / pageInfo.getList().size());
        }
        return pageInfo;
    }

    /**
     * 先查询商品，再查询商品下的所有unitSku
     * 用于pos按商品维度查询商品
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public PageResponse<PtypeResponse> selectGoodsByPtype(PageRequest<SelectGoodsRequestDTO> request) {
        BigInteger profileId = CurrentUser.getProfileId();
        SelectGoodsRequestDTO queryParams = request.getQueryParams();
        Page<Object> page = PageHelper.startPage(request.getPageIndex(), request.getPageSize());
        //先查询商品
        List<PtypeResponse> list = basePtypeMapper.selectOnlyPtypeList(profileId, queryParams.getFilterValue(),
                queryParams.getTypeId(), queryParams.getPtypeLimit());
        PageResponse<PtypeResponse> pageInfo = new PageResponse<>();
        pageInfo.setTotal(page.getTotal());
        pageInfo.setList(list);

        if (list.isEmpty()) return pageInfo;

        fillPic(list);
        //查询unitSku和套餐
        List<BigInteger> ptypeIds = list.stream().map(PtypeResponse::getId).collect(Collectors.toList());
        List<PtypeResponse> unitSkuList = basePtypeMapper.selectPtypeByIds(profileId, ptypeIds, null, null,
                queryParams.getOtypeId(), queryParams.getKtypeId(), false, false);
        fillPic(unitSkuList);
        List<PtypeResponse> comboList = new ArrayList<>();
        for (PtypeResponse ptype : list) {
            ptype.setOnlyPtype(true);
            if (ptype.getUnitSkuPtypeList() == null) {
                ptype.setUnitSkuPtypeList(new ArrayList<>());
            }
            List<PtypeResponse> unitSkuPtypeList = ptype.getUnitSkuPtypeList();
            Iterator<PtypeResponse> iterator = unitSkuList.iterator();
            while (iterator.hasNext()) {
                PtypeResponse unitSku = iterator.next();
                //找到属于该商品的unitSku商品
                if (ptype.getId().equals(unitSku.getId())) {
                    //拷贝ptype信息
                    copyPtype(ptype, unitSku);
                    //找到该商品最低价
                    if (ptype.getCurrencyPrice() == null || unitSku.getCurrencyPrice().compareTo(ptype.getCurrencyPrice()) < 0) {
                        ptype.setCurrencyPrice(unitSku.getCurrencyPrice());
                    }
                    //这里只累加基本单位的库存，避免多单位时，重复累加库存
                    //因为每个sku存的库存都是基本单位库存
                    if (unitSku.getUnit() != null && Integer.valueOf(1).equals(unitSku.getUnit().getUnitCode())) {
                        //总库存累加
                        BigDecimal stockQty = ptype.getStockQty();
                        if (stockQty == null) {
                            stockQty = BigDecimal.ZERO;
                        }
                        BigDecimal unitSkuStockQty = unitSku.getStockQty();
                        if (unitSkuStockQty == null) {
                            stockQty = BigDecimal.ZERO;
                        }
                        stockQty = stockQty.add(unitSkuStockQty);
                        ptype.setStockQty(stockQty);
                    }
                    unitSkuPtypeList.add(unitSku);
                    iterator.remove();
                    //套餐
                    if (unitSku.getPcategory() == 2) {
                        comboList.add(unitSku);
                        break;
                    }
                }
            }
        }
        //查询套餐明细
        getComboDetails(comboList);
        return pageInfo;
    }

    private void copyPtype(PtypeResponse src, PtypeResponse dest) {
        dest.setProfileId(src.getProfileId());
        dest.setTypeid(src.getTypeid());
        dest.setPartypeid(src.getPartypeid());
        dest.setUsercode(src.getUsercode());
        dest.setFullname(src.getFullname());
        dest.setShortname(src.getShortname());
        dest.setNamepy(src.getNamepy());
        dest.setClassed(src.getClassed());
        dest.setStoped(src.getStoped());
        dest.setDeleted(src.getDeleted());
        dest.setRowindex(src.getRowindex());
        dest.setBarcode(src.getBarcode());
        dest.setStandard(src.getStandard());
        dest.setPtypeType(src.getPtypeType());
        dest.setPtypeArea(src.getPtypeArea());
        dest.setMemo(src.getMemo());
        dest.setCreateType(src.getCreateType());
        dest.setCostMode(src.getCostMode());
        dest.setPcategory(src.getPcategory());
        dest.setTaxNumber(src.getTaxNumber());
        dest.setTaxRate(src.getTaxRate());
        dest.setCostPrice(src.getCostPrice());
        dest.setBrandId(src.getBrandId());
        dest.setKtypeLimit(src.getKtypeLimit());
        dest.setSnenabled(src.getSnenabled());
        dest.setPropenabled(src.getPropenabled());
        dest.setBatchenabled(src.getBatchenabled());
        dest.setProtectDays(src.getProtectDays());
        dest.setProtectDaysUnit(src.getProtectDaysUnit());
        dest.setProtectWarndays(src.getProtectWarndays());
        dest.setProtectWarndaysUnit(src.getProtectWarndaysUnit());
        dest.setWeight(src.getWeight());
        dest.setWeightUnit(src.getWeightUnit());
        dest.setRetailDefaultUnit(src.getRetailDefaultUnit());
        dest.setSaleDefaultUnit(src.getSaleDefaultUnit());
        dest.setBuyDefaultUnit(src.getBuyDefaultUnit());
        dest.setStockDefaultUnit(src.getStockDefaultUnit());
        dest.setPtypeLength(src.getPtypeLength());
        dest.setPtypeWidth(src.getPtypeWidth());
        dest.setPtypeHeight(src.getPtypeHeight());
        dest.setLengthUnit(src.getLengthUnit());
        dest.setCreateTime(src.getCreateTime());
        dest.setUpdateTime(src.getUpdateTime());
        dest.setSkuPrice(src.getSkuPrice());
        dest.setPropvaluesDescartCount(src.getPropvaluesDescartCount());
        dest.setFullbarcodeRuleId(src.getFullbarcodeRuleId());
        dest.setPicUrl(src.getPicUrl());
        dest.setComboId(src.getComboId());
        dest.setComboRow(src.getComboRow());
    }


    /**
     * 获取商品所有一级分类
     */
    public List<PtypeType> getPtypeAllRootType() {
        final List<PtypeType> typeList = basePtypeMapper.getPtypeAllRootType(CurrentUser.getProfileId());
        List<PtypeType> firstTypeList = new ArrayList<>();
        Map<String, List<PtypeType>> secondTypeMap = new HashMap<>();
        for (PtypeType type : typeList) {
            String typeid = type.getTypeid();
            if (typeid.length() <= 5) {
                firstTypeList.add(type);
            } else {
                List<PtypeType> list = secondTypeMap.computeIfAbsent(type.getPartypeid(), k -> new ArrayList<>());
                list.add(type);
            }
        }
        for (PtypeType type : firstTypeList) {
            type.setChildren(secondTypeMap.get(type.getTypeid()));
        }
        return firstTypeList;
    }

    public void getComboDetails(List<PtypeResponse> comboList) {
        if (!comboList.isEmpty()) {
            Map<BigInteger, List<PtypeResponse>> comboDetails =
                    getComboDetailsByIds(comboList.stream().map(PtypeResponse::getComboId).collect(Collectors.toList()));
            for (PtypeResponse combo : comboList) {
                combo.setComboDetails(comboDetails.get(combo.getComboId()));
            }
        }
    }

    /**
     * 获取套餐明细
     *
     * @param comboIds
     * @return
     */
    public Map<BigInteger, List<PtypeResponse>> getComboDetailsByIds(List<BigInteger> comboIds) {
        List<PtypeResponse> list = basePtypeMapper.selectComboDetails(CurrentUser.getProfileId(), comboIds);
        if (list != null && !list.isEmpty()) {
            fillPic(list);
            return list.stream().collect(Collectors.groupingBy(PtypeResponse::getComboId));
        }
        return new HashMap<>(16);
    }

    /**
     * 根据ptypeId,unitId,skuId批量查询商品价
     *
     * @param ptypeList 商品列表，注意排除套餐
     * @return
     */
    //todo 商品和套餐都应该先查询往来单位价格本，再查询商品价格本，暂留此代码后续供pos端更改商品单位后进行价格查询
//    private void getPtypePrice1(List<PtypeResponse> ptypeList) {
//        ptypeList = ptypeList.stream().filter(ptype -> ptype.getComboId() == null || ptype.getComboId().equals
//        (BigInteger.ZERO)).collect(Collectors.toList());
//        List<HashMap<String, Object>> params = ptypeList.stream().filter(ptype -> ptype.getId() != null &&
//                ptype.getUnit() != null &&
//                ptype.getUnit().getId() != null &&
//                ptype.getSku() != null &&
//                ptype.getSku().getId() != null).map(ptype -> {
//            HashMap<String, Object> map = new HashMap<>(3);
//            map.put("ptypeId", ptype.getId());
//            map.put("unitId", ptype.getUnit().getId());
//            //如果是sku定价管理，则需要传入skuId，否则skuId传0
//            if (ptype.getSkuPrice() == 1) {
//                map.put("skuId", ptype.getId());
//            } else {
//                map.put("skuId", BigInteger.ZERO);
//            }
//            return map;
//        }).collect(Collectors.toList())
//        List<PtypePriceDTO> priceList = getPtypePrice(params).stream().distinct().collect(Collectors.toList());
//        todo distinct这里之前是通过id来去重，后面查两张表，应该用ptypeId,skuId，unitId来去重
//        for (PtypeResponse ptype : ptypeList) {
//            for (PtypePriceDTO priceDTO : priceList) {
//                //当开启sku定价管理，则必须三个id都相等，若未开启，则价格表中，skuId为0
//                if (priceDTO.getPtypeId().equals(ptype.getId()) && priceDTO.getUnitId().equals(ptype.getUnit()
//                .getId())) {
//                    if (priceDTO.getSkuId().equals(ptype.getSku().getId()) || (ptype.getSkuPrice() == 0 && priceDTO
//                    .getSkuId().equals(BigInteger.ZERO))) {
//                        ptype.setCurrencyPrice(priceDTO.getRetailPrice());
//                        break;
//                    }
//                }
//            }
//        }
//    }

    /**
     * 处理商品图片
     *
     * @param list
     */
    private void fillPic(List<PtypeResponse> list) {
        for (PtypeResponse item : list) {
            String picUrl = item.getPicUrl();
            if (!StringUtil.isNullOrEmpty(picUrl)) {
                if (!picUrl.contains("http:") && !picUrl.contains("https:")) {
                    item.setPicUrl(QiniuUtils.getThumbnail(picUrl));
                }
            }
            if (item.getSku() == null || item.getSku().getPicUrl() == null) {
                continue;
            }
            String skuPicUrl = item.getSku().getPicUrl();
            if (!StringUtil.isNullOrEmpty(skuPicUrl)) {
                if (!skuPicUrl.contains("http:") && !skuPicUrl.contains("https:")) {
                    item.getSku().setPicUrl(QiniuUtils.getThumbnail(skuPicUrl));
                }
            }
        }
    }


    /**
     * 根据传入的商品id(列表),查询这些商品下所有的sku价格信息。
     * <p>
     * //     * @see BaseInfoAPI#getSkuPriceListByPtypeIds(List)
     */
    private Hashtable<BigInteger, List<PtypePrice>> getSkuPriceListByPtypeIds(List<BigInteger> ptypeIds) {
        return ResultHandler.result(baseInfoAPI.getSkuPriceListByPtypeIds(ptypeIds));
    }

    /**
     * 查询某个商品(sku)下对应的价格信息
     *
     * @param ptypeId   商品id，用来查询商品下的价格列表(若开启了sku定价管理则会返回多个价格)
     * @param skuId     需要查询的skuId，用来筛选
     * @param unitId    单位id，用来筛选，处理辅助单位的问题
     * @param hashtable 查询到的各商品下的sku价格列表
     */
    @NotNull
    private BigDecimal getSkuPriceByPtypeIdAndSkuId(@NotNull BigInteger ptypeId, @NotNull BigInteger skuId,
                                                    @NotNull BigInteger unitId, Hashtable<BigInteger,
            List<PtypePrice>> hashtable) {
        //通过ptypeId拿到价格sku价格列表
        List<PtypePrice> priceList = hashtable.get(ptypeId);
        //通过skuId进行筛选，拿到价格
        return priceList.stream()
                //这里若商品没有开启sku定价管理，则返回的价格列表长度固定为1，且skuId为0
                .filter(price -> (skuId.equals(price.getSkuId()) || price.getSkuId().equals(BigInteger.ZERO)) && unitId.equals(price.getUnitId()))
                .findFirst()
                .map(PtypePrice::getRetailPrice)
                .orElse(BigDecimal.ZERO);
    }

    /**
     * 记录打印次数
     *
     * @param printCountDAO
     */
    public void writePrintCount(PrintCountDAO printCountDAO) {
        if ("SaleBill".equals(printCountDAO.getClientParams().getVchtype())) {
            printCountDAO.getClientParams().setVchtype("Sale");
        } else if ("SaleBackBill".equals(printCountDAO.getClientParams().getVchtype())) {
            printCountDAO.getClientParams().setVchtype("SaleBack");
        } else if ("SaleChangeBill".equals(printCountDAO.getClientParams().getVchtype())) {
            printCountDAO.getClientParams().setVchtype("SaleChange");
        }
//        map.put("clientParams", clientParams);
//        PrintCountDAO printCountDAO = new PrintCountDAO();
//        printCountDAO.setPrintTimes(Integer.parseInt(map.get("printTimes").toString()));
//        ClientParams clientParams = new ClientParams();
//        clientParams.setVchcode(vchcode);
//        clientParams.setVchtype(vchype);
        billAPI.writePrintCount(printCountDAO);
    }

    /**
     * 批量核算
     *
     * @param vchcodeList
     * @return
     */
    @Transactional
    public PubBillBatchSaveDTO batchPostBill(List<BigInteger> vchcodeList) {
        List<GoodsBillDTO> goodBillByVchcodeList = shopSaleBillMapper.getGoodBillByVchcodeList(vchcodeList,
                CurrentUser.getProfileId());
        if (goodBillByVchcodeList.size() == 0) {
            throw new RuntimeException("所选单据不符合条件,请重新选择!");
        }
        //将所有未确认的单据筛选出来
        List<GoodsBillDTO> collect =
                goodBillByVchcodeList.stream().filter(item -> !item.getPostState().equals(BillPostState.ACCOUNTING_COMPLETED)).collect(Collectors.toList());
        PubBillBatchSaveDTO pubBillBatchSaveDAO = new PubBillBatchSaveDTO();
        pubBillBatchSaveDAO.setId(UId.newId());
        pubBillBatchSaveDAO.setProfileId(CurrentUser.getProfileId());
        pubBillBatchSaveDAO.setEmployeeId(CurrentUser.getEmployeeId());
        pubBillBatchSaveDAO.setCreateTime(new Date());
        List<PubBillBatchSaveDetailDAO> list = new ArrayList<>();
        collect.stream().forEach(item -> {
            PubBillBatchSaveDetailDAO pubBillBatchSaveDetailDAO = new PubBillBatchSaveDetailDAO();
            pubBillBatchSaveDetailDAO.setId(UId.newId());
            pubBillBatchSaveDetailDAO.setBatchId(pubBillBatchSaveDAO.getId());
            pubBillBatchSaveDetailDAO.setCreateTime(pubBillBatchSaveDAO.getCreateTime());
            pubBillBatchSaveDetailDAO.setProfileId(pubBillBatchSaveDAO.getProfileId());
            pubBillBatchSaveDetailDAO.setVchcode(item.getVchcode());
            pubBillBatchSaveDetailDAO.setVchtype(item.getVchtype().getCode());
            pubBillBatchSaveDetailDAO.setUpdateTime(pubBillBatchSaveDAO.getCreateTime());
            pubBillBatchSaveDetailDAO.setBillNumber(item.getNumber());
            pubBillBatchSaveDetailDAO.setBillDate(item.getDate());
            pubBillBatchSaveDetailDAO.setState(0);
            pubBillBatchSaveDetailDAO.setErrType(0);
            pubBillBatchSaveDetailDAO.setErrDetail("");
            list.add(pubBillBatchSaveDetailDAO);
        });
        pubBillBatchSaveDAO.setSelCount(vchcodeList.size());
        pubBillBatchSaveDAO.setFilterCount(vchcodeList.size() - collect.size());
        pubBillBatchSaveDAO.setErrCount(0);
        pubBillBatchSaveDAO.setSucCount(0);
        pubBillBatchSaveDAO.setCreateType(0);
        if (list.size() > 0) {
            shopSaleBillMapper.doSave(pubBillBatchSaveDAO);
            shopSaleBillMapper.batchSave(list);
        }
        PubBillBatchSaveDTO pubBillBatchSaveDTO = new PubBillBatchSaveDTO();
        BeanUtils.copyProperties(pubBillBatchSaveDAO, pubBillBatchSaveDTO);
        ThreadPool mission = ThreadPoolFactory.build("batch-delete");
        mission.executeAsync(a -> mqBatchPostBill(pubBillBatchSaveDTO), null);
        PubSystemLogService.saveInfo("批量核算销售类单据");
        return pubBillBatchSaveDTO;
    }

    public Boolean mqBatchPostBill(PubBillBatchSaveDTO pubBillBatchSaveDTO) {
        try {
            // 先睡半秒，因为是异步执行的防止批量保存表查不出记录
            Thread.sleep(500);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        //查询出相关的单据
        List<PubBillBatchSaveDetailDAO> list =
                shopSaleBillMapper.getListByBatchSaveId(pubBillBatchSaveDTO.getProfileId(),
                        pubBillBatchSaveDTO.getId());
        PubBillBatchSaveDTO byId = shopSaleBillMapper.getById(pubBillBatchSaveDTO.getId(),
                pubBillBatchSaveDTO.getProfileId());
        //对不同的单据种类进行单独处理
        list.forEach(item -> {
            // 循环处理
            updatePost(pubBillBatchSaveDTO, byId, item);
        });
        return true;
    }

    public void updatePost(PubBillBatchSaveDTO pubBillBatchSaveDTO, PubBillBatchSaveDTO byId,
                           PubBillBatchSaveDetailDAO item) {
        if (item.getVchtype() >= 4000) { // 只处理交易单
            return;
        }
        try {
            GoodsBillDTO goodsBillDTO = billLoadService.loadBill(item.getVchcode(), item.getProfileId(),
                    pubBillBatchSaveDTO.getEmployeeId(), true);
            if (null == goodsBillDTO) {
                LoadErrItem(item, "单据不存在!");
            } else {
                goodsBillDTO.setPostState(BillPostState.STOCK_INOUT_COMPLETED);
                BillSaveResultDTO billSaveResultDTO = new BillSaveResultDTO();
                try {
                    ThreadLocalCache.set("batchPostBill", true);
                    goodsBillService.postBill(goodsBillDTO.getVchcode());
                    billSaveResultDTO = CurrentContext.createSuccessResult(goodsBillDTO.getVchcode(),
                            goodsBillDTO.getNumber());
                } catch (PostBillException err) {
                    billSaveResultDTO = CurrentContext.createErrorResult(goodsBillDTO.getVchcode(),
                            goodsBillDTO.getNumber(), BillSaveResultTypeEnum.ERROR, err);
                } finally {
                    ThreadLocalCache.remove("batchPostBill");
                }
                saveGoodsBillLog(byId.getEmployeeId(), byId.getEfullname(), billSaveResultDTO, goodsBillDTO);
                LoadErrDetail(billSaveResultDTO, item);
            }
        } catch (Exception e) {
            LoadErrItem(item, e.getMessage());
        }
        shopSaleBillMapper.updateDetailEntity(item);
        if (item.getState() == -1) {
            byId.setErrCount(byId.getErrCount() + 1);
        }
        if (item.getState() == 3) {
            byId.setSucCount(byId.getSucCount() + 1);
        }
        shopSaleBillMapper.updateEntity(byId);
    }

    private void saveGoodsBillLog(BigInteger employeeId, String efullname, BillSaveResultDTO billSaveResultDTO,
                                  GoodsBillDTO goodsBillDTO) {
        addLog(employeeId, efullname, billSaveResultDTO, goodsBillDTO.getVchtype());
    }

    private void addLog(BigInteger employeeId, String efullname, BillSaveResultDTO billSaveResultDTO,
                        Vchtypes vchtype) {
        if (billSaveResultDTO.getResultType().equals(BillSaveResultTypeEnum.SUCCESS)) {
            String optionType = "保存过账";
            BillLog billLog = new BillLog();
            String source = "批量操作";
            String content = String.format("单据核算成功，【单据编号：%s】 生成来源：%s", billSaveResultDTO.getBillNumber(),
                    "批量" + optionType);
            billLog.setId(UId.newId());
            billLog.setVchcode(billSaveResultDTO.getVchcode());
            billLog.setVchtype(vchtype.getVchtype());
            billLog.setProfileId(CurrentUser.getProfileId());
            billLog.setEtypeId(employeeId);
            billLog.setEtypeName(efullname);
            billLog.setUserIp(getServerIp());
            billLog.setServerIp(getServerIp());
            billLog.setContent(content);
            billLog.setRequestUrl("");
            billLog.setCreateTime(new Date());
            billLog.setSource(source);
            LogService.add(billLog);
        }
    }

    public String getServerIp() {
        InetAddress address = null;
        try {
            address = InetAddress.getLocalHost();
            return address.getHostAddress();
        } catch (UnknownHostException e) {
            e.printStackTrace();
            return null;
        }
    }

    public void LoadErrDetail(BillSaveResultDTO billSaveResultDTO, PubBillBatchSaveDetailDAO item) {
        if (billSaveResultDTO.getResultType().equals(BillSaveResultTypeEnum.SUCCESS)) {
            //保存成功 回写状态
            item.setErrDetail("");
            item.setErrType(loadErrType(billSaveResultDTO.getResultType()));
            item.setState(3);
            item.setUpdateTime(new Date());
        } else {
            item.setErrDetail(JsonUtils.toJson(billSaveResultDTO));
            item.setErrType(loadErrType(billSaveResultDTO.getResultType()));
            item.setState(-1);
            item.setUpdateTime(new Date());
            if (billSaveResultDTO.getMessage() != null && billSaveResultDTO.getMessage().indexOf("部分字段为空") > -1) {
                item.setErrType(3);
                item.setErrDetail("部分字段为空");
            }
        }

    }

    public int loadErrType(BillSaveResultTypeEnum billSaveResultTypeEnum) {
        int errType = 0;
        switch (billSaveResultTypeEnum) {
            case SUCCESS:
                errType = 0;
                break;
            case CONFIRM:
                errType = 1;
                break;
            case ERROR:
            case INIOVER:
            case NUMBER_RESPRET:
                errType = 2;
                break;
        }
        return errType;
    }

    public void LoadErrItem(PubBillBatchSaveDetailDAO item, String errMsg) {
        BillSaveResultDTO billSaveResultDTO = new BillSaveResultDTO();
        billSaveResultDTO.setResultType(BillSaveResultTypeEnum.ERROR);
        billSaveResultDTO.setMessage(errMsg);
        item.setErrDetail(JsonUtils.toJson(billSaveResultDTO));
        item.setErrType(2);
        item.setState(-1);
        item.setUpdateTime(new Date());
    }


    //审核单据
    public BillAuditResponse auditBill(BillAuditRequest request) {
        return ResultHandler.result(billAPI.auditBill(request));
    }

    /**
     * 判断单据编号是否存在
     */
    public boolean selectBillNumberIsExist(String billNumber) {
        return billMapper.selectBillNumberIsExist(CurrentUser.getProfileId(), billNumber);
    }

    /**
     * 调拨入库
     */
    public BillSaveResultDTO goodsTransInStock(GoodsTransInRequest request) {
        return ResultHandler.result(billAPI.goodsTransInStock(request));
    }

    /**
     * 提交审核
     */
    public BillAuditResponse submitAudit(BillAuditRequest request) {
        return ResultHandler.result(billAPI.submitAudit(request));
    }

    /**
     * 提交审核
     */
    public BillAuditResponse submitAuditNew(BillAuditRequest request) {
        return ResultHandler.result(billAPI.submitAuditNew(request));
    }

    /**
     * 单据审核检查
     */
    public AuditEnableResponse checkAuditEnable(BillZyAuditEnableRequest request) {
        return ResultHandler.result(billAPI.checkAuditEnable(request));
    }

    private void ngpOpenMsgPush(GoodsBillDTO bill, String messageType) {
        NgpOpenMsgPushExecutor.getInstance()
                .profileId(bill.getProfileId())
                .messageType(messageType)
                .execute(() -> {
                    SubmitBillMessage sendMessage = new SubmitBillMessage();
                    sendMessage.setNumber(bill.getNumber());
                    BigInteger cpaId;
                    if (bill.getVchtype() == Vchtypes.SaleBackBill) {
                        cpaId = bill.getRefundAccountId();
                    } else {
                        cpaId = bill.getProductAccountId();
                    }
                    sendMessage.setCpaId(cpaId);
                    return sendMessage;
                });
    }


    public SsVipBill getVipBillByVchcode(String vchcode) {
        return vipMapper.selectVipBillByVchcode(CurrentUser.getProfileId(), vchcode);
    }


//    /**
//     * 提交审核
//     */
//    public BillAuditResponse submitAudit(BillAuditRequest request) {
//        return ResultHandler.result(billAPI.submitAudit(request));
//    }
//
//    /**
//     * 单据审核检查
//     */
//    public AuditEnableResponse checkAuditEnable(BillZyAuditEnableRequest request) {
//        return ResultHandler.result(billAPI.checkAuditEnable(request));
//    }

    /**
     * 保存淘淘谷支付方式信息
     */
    public void saveTtgByOpenInfo(@RequestBody AtypePayDto request) {
        try {
            billAPI.saveTTGByOpenInfo(request);
        } catch (Exception e) {
            throw new RuntimeException(String.format("保存淘淘谷支付方式信息失败，错误信息：%s", e.getMessage()));
        }
    }

    public void updateBillPayState(GoodsBillDTO bill, boolean thrEx) {
        try {
            // 增加分布式锁，防止网络卡顿时重复修改支付状态
            boolean aTrue =
                    PosRedisLockerUtils.setLock("updateBillPayState:" + CurrentUser.getProfileId() + "-" + bill.getVchcode()
                            , "true", posRedisLockTimeConfig.getHalfMin());
            int count = 0;
            while (!aTrue && count < 30) {
                Thread.sleep(1000);
                aTrue =
                        PosRedisLockerUtils.setLock("updateBillPayState:" + CurrentUser.getProfileId() + "-" + bill.getVchcode(), "true", posRedisLockTimeConfig.getHalfMin());
                count++;
            }
            Assert.isTrue(aTrue, "请等待30秒,该单据正在修改支付状态中");
            bill.setPayState(PayStateEnum.Paied);
            billInterface.updateBillPayState(bill);

        } catch (Exception e) {
            logger.info("vchcode:" + bill.getVchcode() + "修改支付状态报错，错误信息：" + e.getMessage() + "，开始记录单据日志");
            logger.info("vchcode:" + bill.getVchcode() + "详细报错信息：" + Arrays.toString(e.getStackTrace()));
            String logMsg = String.format("用户更改支付状态失败，【单据编号：%s，异常信息：%s】", bill.getNumber(), e.getMessage());
            bill.setLogMsg(logMsg);
            addBillLogs.addBillLogs(bill);
            logger.info("vchcode:" + bill.getVchcode() + "异常单据日志记录完成");
            if (thrEx) {
                throw new RuntimeException(e.getMessage());
            }
        } finally {
            logger.info("vchcode:" + bill.getVchcode() + "修改支付状态执行结束，锁开始释放");
            PosRedisLockerUtils.unLock("updateBillPayState:" + CurrentUser.getProfileId() + "-" + bill.getVchcode());
            logger.info("vchcode:" + bill.getVchcode() + "修改支付状态执行结束，锁释放完成");
        }
    }

    public void logicalDeleteBill(BigInteger vchcode, String memo, AssertsSourceType source) throws Exception {
        String lockKey = "logicalDeleteBill:" + CurrentUser.getProfileId() + "-" + vchcode;
        boolean locked = PosRedisLockerUtils.setLock(lockKey, "true",
                posRedisLockTimeConfig.getHalfMin());
        if (!locked) {
            throw new RuntimeException(String.format("请等待30秒,单据%s正在删除中", vchcode));
        }
        try {
            Assert.isTrue(vchcode != null, "vchcode不能为空");
            // 已核算的单据要先反核算
            boolean billPosted = billMapper.isBillPosted(vchcode, CurrentUser.getProfileId());
            if (billPosted) {
                goodsBillService.cancelBill(vchcode);
            }
            try {
                GetBeanUtil.getBean(BillService.class).logicalDeleteBillExecute(vchcode, memo, source);
            } catch (Exception e) {
                // 如果是已核算的单据删除报错，要重新提交核算
                if (billPosted) {
                    goodsBillService.postBill(vchcode);
                }
                throw new RuntimeException(e);
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        } finally {
            PosRedisLockerUtils.unLock(lockKey);
        }

    }

    /**
     * @param vchcode 单据id
     * @param memo    备注
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void logicalDeleteBillExecute(BigInteger vchcode, String memo, AssertsSourceType source) throws Exception {
        Assert.isTrue(vchcode != null, "vchcode不能为空");
        // 有下游单据的不能删除
        int a = billMapper.getBillRelation(vchcode, CurrentUser.getProfileId());
        Assert.isTrue(a <= 0, "该单据已产生下游单据，不支持删除");

        BigInteger profileId = CurrentUser.getProfileId();
        BillEntity entity = new BillEntity();
        entity.setProfileId(profileId);
        entity.setVchcode(vchcode);
        entity.setDeleted(1);
        entity.setDeleteTime(new Date());
        entity.setMemo(memo);
        billCoreService.updateBill(entity);

        // 库存释放
        // 删除发货单
        billCoreService.deleteBillInout(profileId, vchcode);
        // 重新核算库存
        List<StockChangeQueueDto> dtos = new ArrayList<>();
        // 处理可销售库存
        StockChangeQueueDto dto = new StockChangeQueueDto();
        dto.setProfileId(profileId);
        dto.setSourceType(StockChangeTypeEnum.BILL_CORE);
        dto.setSourceOperation("门店单据删除");
        // 实际接口接收的是td_bill_inout_detail表的inout_id，但是因为我们的业务vchcode与inout_id必定相等，所以这里直接用vchcode
        dto.setSourceId(vchcode);
        dtos.add(dto);
        // 处理可发货库存
        StockChangeQueueDto deliverDto = new StockChangeQueueDto();
        deliverDto.setProfileId(profileId);
        deliverDto.setSourceType(StockChangeTypeEnum.IN_OUT_DETAIL);
        deliverDto.setSourceOperation("门店单据删除");
        // 实际接口接收的是td_bill_inout_detail表的inout_id，但是因为我们的业务vchcode与inout_id必定相等，所以这里直接用vchcode
        deliverDto.setSourceId(vchcode);
        dtos.add(deliverDto);
        stockChangeService.batchInsertChange(dtos);

        GoodsBillDTO bill = getGoodsBill(vchcode);

        // 资产退回
        backVipAsserts(bill, source);

        // 删除退货单或换货单要修改对应出库单的已退回数量
        // 这个跟资产退回的顺序不能换，资产退回中要判断退货单是否是全退，这里先处理了的话资产退回就无法判断了
        // 只有已支付的退货单、换货按删除时才用执行，因为未支付的不会更改退回数量
        if ((bill.getVchtype() == Vchtypes.SaleBackBill || bill.getVchtype() == Vchtypes.SaleChangeBill) &&
                bill.getPayState() == PayStateEnum.Paied) {
            // 把退货单的qty从出库单的ptypeCompletedQty减掉
            // 查出退货单的qty
            if (bill.getInDetail() != null && !bill.getInDetail().isEmpty()) {
                BigInteger sourceVchcode = bill.getInDetail().get(0).getSourceVchcode();
                // 看有没有原单，按商品退货时是没有原单的，不用处理出库单已退数量
                if (!BigInteger.ZERO.equals(sourceVchcode)) {
                    // refundQty处理
                    goodsBillService.subtractionRefundQty(bill);
                    BigDecimal inDetailQty =
                            bill.getInDetail().stream().map(GoodsDetailDTO::getQty).reduce(BigDecimal.ZERO,
                                    BigDecimal::add);
                    // 这里要减去，所以取反
                    goodsBillService.updateBillPtypeCompletedQty(sourceVchcode, inDetailQty.negate());
                }
            }
        }

        // 删除优惠辅助表
        preferentialBillMapper.deletePreferentialBills(vchcode, profileId);
        preferentialBillMapper.deletePreferentialGoodsList(vchcode, profileId);
    }

    public void backVipAsserts(GoodsBillDTO bill, AssertsSourceType source) {
        BigInteger vchcode = bill.getVchcode();
        VipAssertsBillDto vipAssertsBillDto = assertBillMapper.selectAssertBill(CurrentUser.getProfileId(), vchcode);
        // 没有会员资产变动的情况下也要构建一条只有money的MemberAssertsChange，要改累计消费金额和消费次数，前提是这个单据有会员id
        if (bill.getVipCardId() != null && !BigInteger.ZERO.equals(bill.getVipCardId())) {
            MemberAssertsChange vipAssertsChangeDto = new MemberAssertsChange();
            vipAssertsChangeDto.setVchcode(vchcode);
            vipAssertsChangeDto.setVchtype(com.wsgjp.ct.framework.enums.Vchtypes.codeOf(bill.getVchtype().getCode()));
            vipAssertsChangeDto.setBillNumber(bill.getNumber());
            vipAssertsChangeDto.setMemo(AssertsSourceOperation.DELETE_BILL.getName());
            vipAssertsChangeDto.setSourceOperation(AssertsSourceOperation.DELETE_BILL);
            // 只有已支付的单据才保存会员资产，未支付的不保存
            vipAssertsChangeDto.setSaveVipAsserts(bill.getPayState() == PayStateEnum.Paied);
            BigDecimal money = bill.getCurrencyBillTotal();
            List<VipAsserts> vipAsserts = new ArrayList<>();
            VipAsserts vipAssert = new VipAsserts();
            vipAssert.setVipId(bill.getVipCardId());
            List<MemberAssert> value = new ArrayList<>();
            if (vipAssertsBillDto != null && vipAssertsBillDto.getAssertsBillDetailDtoList() != null
                    && !vipAssertsBillDto.getAssertsBillDetailDtoList().isEmpty() && !vipAssertsBillDto.isStatused()) {
                List<VipAssertsBillDetailDto> assertsBillDetailDtoList = vipAssertsBillDto.getAssertsBillDetailDtoList();
                for (VipAssertsBillDetailDto detailDto : assertsBillDetailDtoList) {
                    if (detailDto.getTyped() == 2 && vipAssertsBillDto.getSourceOperation() != AssertsSourceOperation.SCORE_EXCHANGE) {
                        // 积分兑换没有money
                        // 出库单     单据金额为正  赠金为负  删除单据时应 单据金额 - 赠金 再取反 为 负数
                        // 退货单     单据金额为正  赠金为正  删除单据时应 单据金额 + 赠金 再取反 为 负数 （计算累计消费金额时有针对退货单取反）
                        // 正值换货单  单据金额为正  赠金为负  删除单据时应 单据金额 - 赠金 再取反 为 负数
                        // 负值换货单  单据金额为负  赠金为正  删除单据时应 单据金额 - 赠金 再取反 为 正数
                        if (vipAssertsChangeDto.getVchtype() == com.wsgjp.ct.framework.enums.Vchtypes.SaleBackBill) {
                            money = money.add(detailDto.getQty());
                        } else {
                            money = money.subtract(detailDto.getQty());
                        }
                    }
                    BigDecimal changeQty = detailDto.getQty().negate();
                    MemberAssert memberAssert = new MemberAssert();
                    memberAssert.setTyped(detailDto.getTyped());
                    memberAssert.setQty(changeQty);
                    // 未支付单据，assertId存的是卡券模板id，记录删除单据资产变动时也存卡券模板id就好
                    // 已支付单据，不管出库、退货、换货，记录的都是卡券id
                    // 如果要删出库单（正值换货单），出库单使用的卡券应该返回去就是反核销，应该传卡券id，出库单赠送的卡券应该解绑，应该传卡券id
                    // 如果要删退货单（负值换货单），退货单解绑的卡券应该返回去就是重新赠送，应该传卡券模板id，退货单增加的卡券应该扣除，应该传卡券id
                    // 退货单增加的卡券就是出库单使用的卡券，在表中的assertId相同，通过核销和反核销来实现使用和恢复
                    // 退货单扣减的卡券就是出库单赠送的卡券，在表中的assertId相同，通过赠送和解绑来实现赠送和扣减
                    // 有个问题是，解绑后的卡券，再次赠送，资产记录表里记录的就是新的cardId了，不过好像没什么影响
                    if (detailDto.getTyped() == 4 &&
                            changeQty.compareTo(BigDecimal.ZERO) > 0 &&
                            detailDto.getAssertId() != null &&
                            (bill.getVchtype() == Vchtypes.SaleBackBill ||
                                    (bill.getVchtype() == Vchtypes.SaleChangeBill && bill.getCurrencyBillTotal().compareTo(BigDecimal.ZERO) < 0))) {
                        // 只有删除退货单时有需要重新赠送的卡券，才需要传卡券模板id
                        memberAssert.setCardTemplateId(cardService.getCardTemplateIdByCardId(detailDto.getAssertId()));
                    } else {
                        memberAssert.setAssertId(detailDto.getAssertId());
                    }
                    memberAssert.setMemo(VipAssertsBillDetailDto.getTypedMemo(memberAssert.getTyped(), changeQty));
                    memberAssert.setChangeType(changeQty.compareTo(BigDecimal.ZERO) > 0 ?
                            AssertsChangeType.DELETE_ORDER_RETURN_CONSUME : AssertsChangeType.DELETE_ORDER_RETURN_GET);
                    value.add(memberAssert);
                }
            }
            vipAssert.setMoney(money.negate());
            vipAssert.setVipAssert(value);
            vipAsserts.add(vipAssert);
            vipAssertsChangeDto.setVipAsserts(vipAsserts);
            vipAssertsChangeDto.setOperationSource(source);
            vipAssertsChangeDto.setExecuteProtectionStrategy(false);
            memberAssertsChangeService.vipAssertsChange(vipAssertsChangeDto);
        }
    }

    public void saveCashPayment(BigInteger profile, BigInteger vchcode, Vchtypes vchtype) {
        CashBoxPaymentDTO paymentDTO = billMapper.getCashBoxPayment(profile, vchcode);
        if (paymentDTO != null) {
            switch (vchtype) {
                case SaleBill:
                    paymentDTO.setPaymentType(BigInteger.valueOf(2));
                    break;
                case SaleBackBill:
                    paymentDTO.setPaymentType(BigInteger.valueOf(3));
                    break;
                case SaleChangeBill:
                    if (paymentDTO.getAmount().compareTo(BigDecimal.ZERO) < 0) {
                        paymentDTO.setAmount(paymentDTO.getAmount().abs());
                        paymentDTO.setPaymentType(BigInteger.valueOf(3));
                    } else {
                        paymentDTO.setPaymentType(BigInteger.valueOf(2));
                    }
                    break;
            }
            paymentDTO.setId(UId.newId());
            try {
                logger.info("vchcode:" + vchcode + "新增钱箱记录");
                cashBoxService.insertPayment(paymentDTO);
            } catch (Exception e) {
                throw new RuntimeException(String.format("新增钱箱记录失败:" + e.getMessage()));
            }
        }
    }

    public HashMap getInvoiceInfo(BigInteger vchcode) {
        return billMapper.selectBillUnInvoiceInfo(CurrentUser.getProfileId(), vchcode);
    }


    /**
     * 提交发票源信息
     *
     * @param dto
     */

    public GeneralResponse submitInvoice(SaveInvoiceRequest dto) {
        HashMap map = new HashMap();
        map.put("vchcode", dto.getVchcodes().stream().findFirst());
        List<InvoiceBill> invoiceBillList = ResultHandler.result(invoiceApi.getInvoiceBillByVchcode(map));
        if (null == invoiceBillList || invoiceBillList.size() == 0) {
            dto.setSelectSamePayBtype(true);
            dto.setInvoiceType(0);
            SaveInvoiceResponse response = ResultHandler.result(invoiceApi.saveInvoiceBatch(dto));
            if (null != response.getErrorMsgList() && response.getErrorMsgList().size() > 0) {
                if (!response.getErrorMsgList().toString().contains("已提交至待开票")) {
                    throw new RuntimeException(response.getErrorMsgList().toString());
                }
            }
            invoiceBillList = ResultHandler.result(invoiceApi.getInvoiceBillByVchcode(map));
            if (null == invoiceBillList || invoiceBillList.size() == 0) {
                throw new RuntimeException("发票源生成失败");
            }

        }
        InvoiceBill invoiceBill = invoiceBillList.stream().findFirst().get();
        BillBatchInvoicedDto billBatchInvoicedDto = new BillBatchInvoicedDto();
        billBatchInvoicedDto.setInvoiceBillId(invoiceBill.getId());
        billBatchInvoicedDto.setVchtype(invoiceBill.getVchtype());
        billBatchInvoicedDto.setCreateType(1);
        billBatchInvoicedDto.setRowIndex(0);
        billBatchInvoicedDto.setInvoiceType(0);
        billBatchInvoicedDto.setBillNumber(invoiceBill.getBillNumber());
        billBatchInvoicedDto.setInvoiceTotal(invoiceBill.getDisedTaxedTotal());
        GeneralResponse result = invoiceApi.billBatchInvoiced(new ArrayList<>(asList(billBatchInvoicedDto)));

        return result;
    }

    /**
     * 获取会员积分余额和单据本次积分
     */
    public VipScoreInfo getBillVipScoreInfo(HashMap dto) {

        VipScoreInfo ssVipAsserts = vipMapper.getVipScoreInfo(new BigInteger(dto.get("vipId").toString()),
                CurrentUser.getProfileId());
        if (ssVipAsserts == null) {
            return new VipScoreInfo();
        }
        // 手机号解密
        List<PosBuyer> buyers =
                sisClientService.batchDecryptBuyers(Collections.singletonList(ssVipAsserts.getBuyerId()));
        // 这里只会有一条手机号
        if (!buyers.isEmpty()) {
            ssVipAsserts.setPhone(buyers.get(0).getCustomerReceiverPhone());
        }
        ///本次获取的积分
        int billScore = vipMapper.getBillScores(CurrentUser.getProfileId(),
                new BigInteger(dto.get("vchcode").toString()));
        ssVipAsserts.setSaleScore(billScore);
        //单据消费后储值余额获取
        SsVipBill ssVipBill = vipMapper.selectVipBillByVchcode(CurrentUser.getProfileId(),
                dto.get("vchcode").toString());

        ssVipAsserts.setChargeTotal(ssVipBill.getAssertTotal());
        return ssVipAsserts;
    }

    public void cancelBill(BigInteger vchcode) {
        goodsBillService.cancelBill(vchcode);
    }


    /******************************汇付和淘淘谷支付相关***********************************/


    /**
     * 聚合支付/退款并保存核算
     */
    public PayResultInfo payOrderAndSaveBillNew(@RequestBody LinkedHashMap<String, Object> requestParams) {
        long sTime = System.currentTimeMillis();
        MonitorService monitorService = com.wsgjp.ct.sale.platform.utils.BeanUtils.getBean(MonitorService.class);
        AggregatePaymentRequest payRequestDto = JsonUtils.toObject(JsonUtils.toJson(requestParams.get("payInfo")),
                AggregatePaymentRequest.class);
        ///开单需要关闭轮训
        payRequestDto.setEnablePollingQuery(false);
        PosBill posBill = JsonUtils.toObject(JsonUtils.toJson(requestParams),
                PosBill.class);
        GoodsBillDTO goodsBill = posBill.getGoodsBill();
        ///设置必穿字段goodsTitle和desc
        setGoodsTitleAndDesc(goodsBill, payRequestDto);
        ///不核算
        goodsBill.setWhetherPost(false);
        ///同步保存
        goodsBill.setAsyncSave(false);
        PayResultInfo resultInfo = new PayResultInfo();
        BillSaveResultDTO resultDTO = null;
        try {
            resultDTO = submitGoodsBill(posBill);
            // 保存单据时vchcode可能更新，payRequestDto中要重新赋值
            payRequestDto.setVchcode(resultDTO.getVchcode());
            payRequestDto.setBillTotal(goodsBill.getCurrencyBillTotal());
            ///防止网络中断的错误 catch 没有赋值
            resultInfo.setResultDTO(resultDTO);
            Assert.isTrue(resultDTO.getResultType() == BillSaveResultTypeEnum.SUCCESS, "保存单据失败，请重试！");
            if ((goodsBill.getVchtype() == Vchtypes.SaleBackBill) || (goodsBill.getVchtype() == Vchtypes.SaleChangeBill && goodsBill.getCurrencyAtypeTotal().compareTo(BigDecimal.ZERO) < 0)) {
                //退款
                resultInfo = payRefundNew(payRequestDto);
            } else {
                //收款
                resultInfo = payOrderNew(payRequestDto);
            }

            // 订单状态处理，把淘淘谷的等待输入密码换成等待
            if (resultInfo.getStatus() == PayStatusEnum.WAITING_PASSWORD) {
                resultInfo.setStatus(PayStatusEnum.PENDING);
            }
            ///保存单据结果
            resultInfo.setResultDTO(resultDTO);
            logger.info("用户profileId{},已请求下单接口{}", CurrentUser.getProfileId(), resultInfo);
            taotaoguLogService.saveLog(new BigInteger(goodsBill.getVchcode().toString()),
                    payRequestDto.getOutNo(), "payOrderAndSaveBill", JsonUtils.toJson(requestParams),
                    JsonUtils.toJson(resultInfo), false, "0", "请求下单接口结果");
            ///需要找到扫码支付id 保存orderNO
            Optional<PayMentDTO> payment =
                    goodsBill.getPayment().stream().filter(p -> p.getPaywayType() == 2).findFirst();
            if (payment.isPresent() && resultInfo.getOrderNo() != null) {
                updateBillAccount(payment.get().getPaywayId(), resultInfo.getOrderNo(), goodsBill.getVchcode());
            }
            if (resultInfo.getStatus() == PayStatusEnum.SUCCEEDED) {
                goodsBill.setVchcode(resultDTO.getVchcode());
                goodsBill.setNumber(resultDTO.getBillNumber());
                goodsBill.setWhetherPost(true);
                goodsBill.setOperationSource(AssertsSourceType.POS);
                updateBillPayState(goodsBill, false);
            }
            monitorService.recordTP(MonitorTypeEnum.PL_API_SALE_POS_PAY_TP_TIME.getTopic(), "shopType", "pos",
                    (System.currentTimeMillis() - sTime));
            return resultInfo;
        } catch (Exception e) {
            taotaoguLogService.saveLog(new BigInteger(goodsBill.getVchcode().toString()),
                    payRequestDto.getOutNo(), "payOrderAndSaveBill", JsonUtils.toJson(requestParams), "",
                    true,
                    "-3", e.getMessage());
            logger.error(isSale(goodsBill) ? "支付失败：" : "退款失败：" + e.getMessage());
            if (resultDTO != null) {
                resultDTO.setMessage(e.getMessage());
                resultInfo.setResultDTO(resultDTO);
            }
            resultInfo.setStatus(PayStatusEnum.CANCELLED);
            resultInfo.setMessage(e.getMessage());
            monitorService.recordTP(MonitorTypeEnum.PL_API_SALE_POS_PAY_TP_TIME.getTopic(), "shopType", "pos",
                    (System.currentTimeMillis() - sTime));
            return resultInfo;

        }
    }

    /**
     * 聚合支付（淘淘谷和汇付）
     */
    public PayResultInfo payOrderNew(AggregatePaymentRequest payRequestDto) {
        _buildPayRequest(payRequestDto);
        logger.info("*****payOrderNew********{}{}", CurrentUser.getProfileId(), payRequestDto);
        try {
            long sTime = System.currentTimeMillis();
            MonitorService monitorService = com.wsgjp.ct.sale.platform.utils.BeanUtils.getBean(MonitorService.class);
            PaymentResponse<PaymentInfo> response = aggregatePaymentService.payOrder(payRequestDto);
            monitorService.recordTP(MonitorTypeEnum.PL_API_SALE_POS_PAY_TP_TIME.getTopic(), "shopType", "pos",
                    (System.currentTimeMillis() - sTime));
            PayResultInfo resultInfo = new PayResultInfo();
            resultInfo.setMessage(response.getMessage());
            if (response.getData() != null) {
                BeanUtils.copyProperties(response.getData(), resultInfo);
            }
            return resultInfo;
        } catch (Exception e) {
            logger.error("用户profileId{},支付异常{}", CurrentUser.getProfileId(), e.getMessage());
            taotaoguLogService.saveLog(BigInteger.ZERO, payRequestDto.getOutNo(), "payOrderNew",
                    JsonUtils.toJson(payRequestDto), JsonUtils.toJson(payRequestDto), true, "-7",
                    "支付失败" + e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 聚合支付退款
     */
    public PayResultInfo payRefundNew(AggregatePaymentRequest payRequestDto) {
        _buildPayRequest(payRequestDto);
        logger.info("*****payRefundNew********{}{}", CurrentUser.getProfileId(), payRequestDto);
        try {

            MonitorService monitorService = com.wsgjp.ct.sale.platform.utils.BeanUtils.getBean(MonitorService.class);
            long sTime = System.currentTimeMillis();
            PaymentResponse<PaymentInfo> response = aggregatePaymentService.payBackOrder(payRequestDto);
            monitorService.recordTP(MonitorTypeEnum.PL_API_SALE_POS_PAY_TP_TIME.getTopic(), "shopType", "pos",
                    (System.currentTimeMillis() - sTime));
            PayResultInfo resultInfo = new PayResultInfo();
            resultInfo.setMessage(response.getMessage());
            if (response.getData() != null) {
                BeanUtils.copyProperties(response.getData(), resultInfo);
            }
            return resultInfo;
        } catch (Exception e) {
            logger.error("用户profileId{},退款异常{}", CurrentUser.getProfileId(), e.getMessage());
            taotaoguLogService.saveLog(BigInteger.ZERO, payRequestDto.getOutNo(), "payRefundNew",
                    JsonUtils.toJson(payRequestDto), JsonUtils.toJson(payRequestDto), true, "-7",
                    "退款失败" + e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 轮询
     */
    public PayResultInfo queryPayStatusNew(PayOrderQueryRequest request) {
        int retryTimes = 0;
        //轮询20次
        int maxRetryTimes = 40;
        while (retryTimes < maxRetryTimes) {
            PayResultInfo payResultInfo = requestInterfaceNew(request);
            if (payResultInfo.getStatus() == PayStatusEnum.FAILED || payResultInfo.getStatus() == PayStatusEnum.CANCELLED || payResultInfo.getStatus() == PayStatusEnum.SUCCEEDED || payResultInfo.getStatus() == null) {
                //支付成功或取消 取消轮询
                logger.error("用户profileId{},支付结果{}", CurrentUser.getProfileId(), payResultInfo);
                taotaoguLogService.saveLog(BigInteger.ZERO, request.getOutNo(), "queryPayStatus",
                        JsonUtils.toJson(request), JsonUtils.toJson(payResultInfo), false, "0", "支付轮询接口结果");
                return payResultInfo;
            } else {
                retryTimes++;
                try {
                    Thread.sleep(1000);
                } catch (Exception e) {
                    logger.error("用户profileId{},轮询查询异常{}", CurrentUser.getProfileId(), e.getMessage());
                    taotaoguLogService.saveLog(BigInteger.ZERO, request.getOutNo(), "queryPayStatus",
                            JsonUtils.toJson(request), JsonUtils.toJson(payResultInfo), true, "-4",
                            "支付轮询接口异常" + e.getMessage());
                    throw new RuntimeException(e.getMessage());
                }

            }
        }
        PayResultInfo payResultInfo = new PayResultInfo();
        payResultInfo.setStatus(PayStatusEnum.CANCELLED);
        return payResultInfo;
    }

    public PayResultInfo queryPayResult(PayOrderQueryRequest request) {
        try {
            PayResultInfo payResultInfo = requestInterfaceNew(request);
            //支付成功或取消 取消轮询
            logger.error("用户profileId{},支付结果{}", CurrentUser.getProfileId(), payResultInfo);
            taotaoguLogService.saveLog(BigInteger.ZERO, request.getOutNo(), "queryPayStatus",
                    JsonUtils.toJson(request), JsonUtils.toJson(payResultInfo), false, "0", "支付轮询接口结果");
            return payResultInfo;
        } catch (Exception e) {
            logger.error("用户profileId{},轮询查询异常{}", CurrentUser.getProfileId(), e.getMessage());
            taotaoguLogService.saveLog(BigInteger.ZERO, request.getOutNo(), "queryPayStatus",
                    JsonUtils.toJson(request), e.getMessage(), true, "-4",
                    "支付轮询接口异常" + e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    public PayResultInfo requestInterfaceNew(PayOrderQueryRequest payStatusRequest) {
        try {

            PaymentResponse<PayStatusInfo> response = aggregatePaymentService.payOrderQuery(payStatusRequest.getVchcode());
            PayResultInfo resultInfo = new PayResultInfo();
            resultInfo.setMessage(response.getMessage());
            if (response.getData() != null) {
                BeanUtils.copyProperties(response.getData(), resultInfo);
            }
            logger.info("用户profileId{},轮询结果{}", CurrentUser.getProfileId(), response);
            return resultInfo;
        } catch (Exception e) {
            logger.error("用户profileId{},轮询查询异常{}", CurrentUser.getProfileId(), e.getMessage());
            throw new RuntimeException(e.getMessage());
        }

    }

    ///是否是销售出库单
    public boolean isSale(GoodsBillDTO goodsBillDTO) {
        return goodsBillDTO.getVchtype() == Vchtypes.SaleBill;
    }

    ///给gooodsTitle和desc赋值
    void setGoodsTitleAndDesc(GoodsBillDTO goodsBillDTO, AggregatePaymentRequest payRequestDto) {
        payRequestDto.setVchcode(goodsBillDTO.getVchcode());
        if (goodsBillDTO.getVchtype() == Vchtypes.SaleBill) {
            Optional<GoodsDetailDTO> first = goodsBillDTO.getOutDetail().stream().findFirst();
            if (first.isPresent()) {
                payRequestDto.setGoodsTitle(first.get().getpFullName());
                payRequestDto.setGoodsDesc(first.get().getpFullName());
            } else {
                payRequestDto.setGoodsTitle("商品");
                payRequestDto.setGoodsDesc("商品描述");
            }
        }
    }

    ///设置请求
    private void _buildPayRequest(AggregatePaymentRequest payRequestDto) {
        payRequestDto.setServerName("sale");
        if (payRequestDto.getOutNo() == null) {
            payRequestDto.setOutNo(UId.newId().toString());
        }
        //ttg设置回调参数
        Map<String, String> map = new HashMap<>();
        map.put("outNo", payRequestDto.getOutNo());
        map.put("profileId", CurrentUser.getProfileId().toString());
        // 因再加上自定义参数，url会超长，淘淘谷会报错，暂且不加
//        String sign = SignUtils.getSign(map, appSecret);
//        Map<String, Object> mapNotify = new LinkedHashMap<>();
//        mapNotify.put("ngpSign", sign);
//        payRequestDto.setNotifyMap(mapNotify);

        //设置回调地址
        payRequestDto.setNotifyUrl("/sale/shopsale/bill/payResultCallback");
        if (payRequestDto.getGoodsDesc() == null || payRequestDto.getGoodsDesc().isEmpty()) {
            payRequestDto.setGoodsDesc("商品描述");
        }
        if (payRequestDto.getGoodsTitle() == null || payRequestDto.getGoodsTitle().isEmpty()) {
            payRequestDto.setGoodsTitle("商品");
        }
    }

    public String payResultCallbackNew(HttpServletRequest request, HttpServletResponse response, String routeCode) {
//        RouteContext context = new RouteContext();
//        context.setProfileId(new BigInteger("978285569865969665"));
//        context.setServerId("20231114");
//        context.setDeploy("master");
//        context.setEmployeeId(new BigInteger("978285569865969665"));
//        RouteThreadLocal.setRoute(context);
//        DataSourceManager.setName(RouteThreadLocal.getRoute().getServerId());

        logger.info("进入回调方法，路由参数：" + routeCode);
        PayCallBackInfo callBackInfo = new PayCallBackInfo();
        String callback = aggregatePaymentService.payCallback(request, response, routeCode, callBackInfo);
        // 执行完后，callBackInfo会被赋值，下面进行业务处理
        if (callBackInfo.getStatus() == PayStatusEnum.SUCCEEDED) {
            GoodsBillDTO goodsBillDTO =
                    tdBillCoreMapper.getBillInfoWithOutNo(callBackInfo.getProfileId(),
                            callBackInfo.getOutNo());
            taotaoguLogService.saveLog(goodsBillDTO.getVchcode(), callBackInfo.getOutNo(),
                    request.getRequestURL().toString(), request.getQueryString(),
                    JsonUtils.toJson(callBackInfo), false, "0", "进入回调");
            if (goodsBillDTO.getPayState() == PayStateEnum.UnPay) {
                try {
                    goodsBillDTO.setPayState(PayStateEnum.Paied);
                    goodsBillDTO.setPostState(BillPostState.PROCESS_COMPLETED);
                    goodsBillDTO.setProfileId(callBackInfo.getProfileId());
                    goodsBillDTO.setWhetherPost(true);
                    goodsBillDTO.setSource("POS");
                    goodsBillDTO.setOperationSource(AssertsSourceType.POS);
                    updateBillPayState(goodsBillDTO, false);
                    taotaoguLogService.saveLog(goodsBillDTO.getVchcode(), callBackInfo.getOutNo(),
                            request.getRequestURL().toString(), request.getQueryString(),
                            JsonUtils.toJson(callBackInfo), false, "0", "核算成功");
                } catch (Exception e) {
                    taotaoguLogService.saveLog(goodsBillDTO.getVchcode(), callBackInfo.getOutNo(),
                            request.getRequestURL().toString(), request.getQueryString(),
                            JsonUtils.toJson(callBackInfo), true, "-1", e.getMessage());

                }
            }
        }
        return callback;
    }

    public void payResultCallback(PayCallBackInfo callBackInfo) {
        logger.info("【支付回调】接收到的支付回调信息为：{}", JsonUtils.toJson(callBackInfo));
        if (callBackInfo.getStatus() == PayStatusEnum.SUCCEEDED) {
            GoodsBillDTO goodsBillDTO =
                    tdBillCoreMapper.getBillInfoWithOutNo(callBackInfo.getProfileId(),
                            callBackInfo.getOutNo());
            if (goodsBillDTO == null) {
                // 存在聚合支付已完成，但是单据还没生成的情况，下边的流程走不通，返回
                logger.info("【支付回调】聚合支付已完成，但是单据还没生成，中断流程"); // Billservice日志默认开启的是info
                return;
            }
            taotaoguLogService.saveLog(goodsBillDTO.getVchcode(), callBackInfo.getOutNo(),
                    "sale/shopsale/bill/payResultCallback", JsonUtils.toJson(callBackInfo),
                    "", false, "0", "进入回调");
            if (goodsBillDTO.getPayState() == PayStateEnum.UnPay && goodsBillDTO.getVchtype() != Vchtypes.ReceiptBill) {
                try {
                    goodsBillDTO.setPayState(PayStateEnum.Paied);
                    goodsBillDTO.setPostState(BillPostState.PROCESS_COMPLETED);
                    goodsBillDTO.setProfileId(callBackInfo.getProfileId());
                    goodsBillDTO.setWhetherPost(true);
                    goodsBillDTO.setSource("POS");
                    goodsBillDTO.setOperationSource(AssertsSourceType.POS);
                    updateBillPayState(goodsBillDTO, false);
                    taotaoguLogService.saveLog(goodsBillDTO.getVchcode(), callBackInfo.getOutNo(),
                            "sale/shopsale/bill/payResultCallback", JsonUtils.toJson(callBackInfo),
                            "", false, "0", "核算成功");
                } catch (Exception e) {
                    taotaoguLogService.saveLog(goodsBillDTO.getVchcode(), callBackInfo.getOutNo(),
                            "sale/shopsale/bill/payResultCallback", JsonUtils.toJson(callBackInfo),
                            "", true, "-1", e.getMessage());

                }
            }
            // 订单支付成功就调业务处理回调接口
            aggregatePaymentService.serviceCallback(goodsBillDTO.getVchcode(), null, goodsBillDTO.getNumber());
        }

    }

    ///更新payOrderNo
    void updateBillAccount(BigInteger paywayId, String payOrderNo, BigInteger vchcode) {
        Assert.isTrue(payOrderNo != null, "payOrderNo为null！");
        BillAccountUpdate entity = new BillAccountUpdate();
        entity.setProfileId(CurrentUser.getProfileId());
        entity.setPayOrderNo(payOrderNo);
        entity.setVchcode(vchcode);
        entity.setPaywayId(paywayId);
        try {
            billCoreService.updateBillAccount(entity);
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }

    }

    public void updateBill(BillUpdateFieldRequest request) {
        // 如果经手人不为空，则根据经手人查询部门
        if (request.getEtypeId() != null && !BigInteger.ZERO.equals(request.getEtypeId())) {
            Dtype dtype = baseEtypeMapper.getDtypeIdByEtypeId(CurrentUser.getProfileId(), request.getEtypeId());
            request.setDtypeId(dtype.getId());
            request.setDtypeName(dtype.getFullname());
        }
        FeignResult<String> mapFeignResult = baseInfoAPI.updateBill(request);
        if (!mapFeignResult.getCode().equals("200")) {
            throw new RuntimeException(mapFeignResult.getMessage());
        }
    }

    /**
     * 填充商品标签ID列表
     * @param ptypeList 商品列表
     * @param profileId 账套ID
     */
    private void fillPtypeLabelIds(List<PtypeResponse> ptypeList, BigInteger profileId) {
        if (ptypeList == null || ptypeList.isEmpty()) {
            return;
        }

        // 收集所有商品ID
        List<BigInteger> ptypeIds = ptypeList.stream()
                .map(PtypeResponse::getId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (ptypeIds.isEmpty()) {
            return;
        }

        // 批量查询商品标签ID
        List<Map<String, Object>> labelMappings = basePtypeMapper.getPtypeLabelIds(profileId, ptypeIds);

        // 构建商品ID到标签ID列表的映射
        Map<BigInteger, List<String>> ptypeLabelMap = new HashMap<>();
        for (Map<String, Object> mapping : labelMappings) {
            BigInteger ptypeId = (BigInteger) mapping.get("ptypeId");
            String labelIdsStr = (String) mapping.get("labelIds");

            if (ptypeId != null && labelIdsStr != null && !labelIdsStr.isEmpty()) {
                List<String> labelIds = Arrays.asList(labelIdsStr.split(","));
                ptypeLabelMap.put(ptypeId, labelIds);
            }
        }

        // 填充商品标签ID
        for (PtypeResponse ptype : ptypeList) {
            if (ptype.getId() != null) {
                List<String> labelIds = ptypeLabelMap.get(ptype.getId());
                ptype.setLabelIds(labelIds != null ? labelIds : new ArrayList<>());
            }
        }
    }
}

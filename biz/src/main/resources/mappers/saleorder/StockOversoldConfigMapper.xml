<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.StockOversoldConfigMapper">
    <select id="queryPageData" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.SafeSaleQtyConfigEntity">
        select cfg.id,
        cfg.profile_id,
        cfg.ktype_id,
        cfg.eshop_id,
        cfg.safe_qty_alarm,
        cfg.deleted,
        e.fullname as eshopName,
        bk.fullname as ktypeName
        from pl_eshop_safe_sale_qty_alarm_stock_config cfg
        left join pl_eshop e on cfg.profile_id = e.profile_id and e.otype_id = cfg.eshop_id
        left join base_ktype bk on bk.profile_id = cfg.profile_id and bk.id = cfg.ktype_id
        <if test="ktypeLimited">
            inner join base_limit_scope bls on bls.profile_id=cfg.profile_id and object_type=2 and
            cfg.ktype_id = bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where cfg.profile_id = #{profileId}
        <if test="showDeleted!=null and !showDeleted">
            and cfg.deleted= 0
        </if>
        <if test="eshopId!=null and eshopId>0">
            and cfg.eshop_id =#{eshopId}
        </if>
        <if test="ktypeId!=null and ktypeId>0">
            and cfg.ktype_id=#{ktypeId}
        </if>
        <if test="saleQtyAlarmMin != null and saleQtyAlarmMin != 0">
            and cfg.safe_qty_alarm &gt; #{saleQtyAlarmMin}
        </if>
        <if test="saleQtyAlarmMax != null and saleQtyAlarmMax != 0">
            and cfg.safe_qty_alarm &lt;=  #{saleQtyAlarmMax}
        </if>
    </select>

    <select id="queryProductSafeQtyPageData"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.SafeSaleQtyProductConfigEntity">
        select cfg.id,
        cfg.profile_id,
        cfg.ptype_id,
        cfg.sku_id,
        cfg.unit_id,
        cfg.deleted,
        cfg.safe_qty_alarm,
        bp.fullname as ptypeName,
        bpx.xcode,
        bpu.unit_rate,
        bpu.unit_name,
        cfg.ktype_id,
        bk.fullname as ktypeName,
        cfg.eshop_id,
        e.fullname as eshopName,
        CONCAT_WS(':',
        IF(IFNULL(sku.propvalue_name1,'')='', NULL, sku.propvalue_name1),
        IF(IFNULL(sku.propvalue_name2,'')='', NULL, sku.propvalue_name2),
        IF(IFNULL(sku.propvalue_name3,'')='', NULL, sku.propvalue_name3),
        IF(IFNULL(sku.propvalue_name4,'')='', NULL, sku.propvalue_name4),
        IF(IFNULL(sku.propvalue_name5,'')='', NULL, sku.propvalue_name5),
        IF(IFNULL(sku.propvalue_name6,'')='', NULL, sku.propvalue_name6)) as localProps
        from pl_eshop_safe_sale_qty_alarm_product_config cfg
        left join pl_eshop e on e.profile_id=cfg.profile_id and cfg.eshop_id=e.otype_id
        left join base_ktype bk on bk.profile_id=cfg.profile_id and bk.id=cfg.ktype_id
        left join base_ptype bp on bp.profile_id=cfg.profile_id and bp.id=cfg.ptype_id
        left join base_ptype_sku sku on sku.profile_id=cfg.profile_id and sku.id=cfg.sku_id
        left join base_ptype_xcode bpx on bpx.profile_id=cfg.profile_id and bpx.sku_id=cfg.sku_id and
        bpx.unit_id=cfg.unit_id and bpx.defaulted=1
        left join base_ptype_unit bpu on bpu.profile_id=cfg.profile_id and bpu.ptype_id=cfg.ptype_id and
        bpu.id=cfg.unit_id
        <if test="ktypeLimited">
            inner join base_limit_scope bls on bls.profile_id=cfg.profile_id and object_type=2 and
            cfg.ktype_id = bls.object_id and bls.etype_id=#{etypeId}
        </if>
        where cfg.profile_id = #{profileId}
        <if test="showDeleted!=null and !showDeleted">
            and cfg.deleted= 0
        </if>
        <if test="eshopId!=null and eshopId>0">
            and cfg.eshop_id =#{eshopId}
        </if>
        <if test="ktypeId!=null and ktypeId>0">
            and cfg.ktype_id=#{ktypeId}
        </if>
        <if test="ptypeName != null and ptypeName != ''">
            AND bp.fullname like CONCAT('%',#{ptypeName},'%')
        </if>
        <if test="xcode != null and xcode != ''">
            AND bpx.xcode like CONCAT('%',#{xcode},'%')
        </if>
        <if test="unitName != null and unitName != ''">
            AND bpu.unit_name like CONCAT('%',#{unitName},'%')
        </if>
        <if test="saleQtyAlarmMin != null and saleQtyAlarmMin != 0">
            and cfg.safe_qty_alarm &gt; #{saleQtyAlarmMin}
        </if>
        <if test="saleQtyAlarmMax != null and saleQtyAlarmMax != 0">
            and cfg.safe_qty_alarm &lt;=  #{saleQtyAlarmMax}
        </if>
    </select>

    <select id="querySafeQtyCfgList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.SafeSaleQtyConfigEntity">
        select cfg.id,
        cfg.profile_id,
        cfg.ktype_id,
        cfg.eshop_id,
        cfg.safe_qty_alarm,
        cfg.deleted,
        bk.fullname as ktypeName,
        true as defaultCfg
        from pl_eshop_safe_sale_qty_alarm_stock_config cfg
        left join base_ktype bk on bk.profile_id = cfg.profile_id and bk.id = cfg.ktype_id
        where cfg.profile_id = #{profileId}
        <if test="eshopId!=null and eshopId>0">
            and cfg.eshop_id=#{eshopId}
        </if>
        <if test="eshopIdList!=null and eshopIdList.size()>0">
            and cfg.eshop_id in
            <foreach collection="eshopIdList" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        union
        select cfg.id,
        cfg.profile_id,
        cfg.ktype_id,
        cfg.eshop_id,
        cfg.safe_qty_alarm,
        cfg.deleted,
        bk.fullname as ktypeName,
        false as defaultCfg
        from pl_eshop_safe_sale_qty_alarm_product_config cfg
        left join base_ktype bk on bk.profile_id = cfg.profile_id and bk.id = cfg.ktype_id
        where cfg.profile_id = #{profileId}
        <if test="eshopId!=null and eshopId>0">
            and cfg.eshop_id=#{eshopId}
        </if>
        <if test="eshopIdList!=null and eshopIdList.size()>0">
            and cfg.eshop_id in
            <foreach collection="eshopIdList" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="queryDefaultRuleByKtype" resultType="java.math.BigInteger">
        select eshop_id
        from pl_eshop_stock_sync_default_rule
        where profile_id = #{profileId}
          and FIND_IN_SET(#{ktypeId}, ktype_ids)
        union
        select eshop_id
        from pl_eshop_warehouse_stock_sync_rule
        where profile_id = #{profileId}
          and FIND_IN_SET(#{ktypeId}, ktype_ids)
        union
        select eshop_id
        from pl_eshop_ladder_default_sync_rule
        where profile_id = #{profileId}
          and FIND_IN_SET(#{ktypeId}, ktype_ids)
    </select>

    <select id="queryAllRuleSimpleInfo" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncRule">
        select r.id, r.profile_id, r.rule_name, r.xcode
        from pl_eshop_stock_sync_rule r
                 left join pl_eshop_stock_sync_rule_detail rd on rd.profile_id = r.profile_id and r.id = rd.rule_id
        where r.profile_id = #{profileId}
          and r.rule_type = 1
          and r.deleted=0
          and rd.ktype_id = #{ktypeId}
    </select>

    <select id="queryEshopIdByRule" resultType="java.math.BigInteger">
        select eshop_id
        from pl_eshop_product_sku_rule_config
        where profile_id=#{profileId}
        and rule_id in
        <foreach collection="ruleIdList" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
        union
        select eshop_id
        from pl_eshop_product_sku peps
        left join pl_eshop_stock_sync_rule r on peps.profile_id=r.profile_id and peps.platform_xcode=r.xcode
        where peps.profile_id=#{profileId}
        and r.target_type=0
        and r.deleted=0
        and r.id in
        <foreach collection="ruleIdList" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </select>

    <select id="queryEshopSelectorData" resultType="com.wsgjp.ct.sale.common.entity.eshop.EshopSelectorData">
        select fullname,otype_id,profile_id, eshop_type
        from pl_eshop
        where profile_id=#{profileId}
        and deleted=0
        and stoped=0
        and otype_id in
        <foreach collection="eshopIdList" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </select>

    <select id="queryAllEshopSelectorData" resultType="com.wsgjp.ct.sale.common.entity.eshop.EshopSelectorData">
        select fullname, otype_id, profile_id, eshop_type
        from pl_eshop
        where profile_id = #{profileId}
          and deleted = 0
          and stoped = 0
    </select>

    <insert id="insertDefaultSafeQtyRule">
        insert into pl_eshop_safe_sale_qty_alarm_stock_config
            (id, profile_id, ktype_id, eshop_id, safe_qty_alarm)
        values (#{id}, #{profileId}, #{ktypeId}, #{eshopId}, #{safeQtyAlarm})
    </insert>

    <update id="modifyDefaultSafeQtyRule">
        update pl_eshop_safe_sale_qty_alarm_stock_config
        set ktype_id=#{ktypeId},
            eshop_id=#{eshopId},
            safe_qty_alarm=#{safeQtyAlarm},
            deleted=0
        where profile_id = #{profileId}
          and id = #{id}
    </update>

    <update id="stopSafeQtyCfg">
        update pl_eshop_safe_sale_qty_alarm_stock_config
        set deleted = 1
        where profile_id=#{profileId}
        and id in
        <foreach collection="idList" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </update>

    <update id="deleteSafeQtyCfg">
        update pl_eshop_safe_sale_qty_alarm_stock_config
        set eshop_id=0,
        deleted = 1
        where profile_id=#{profileId}
        and id in
        <foreach collection="idList" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </update>


    <update id="stopProductSafeQtyCfg">
        update pl_eshop_safe_sale_qty_alarm_product_config
        set deleted = 1
        where profile_id=#{profileId}
        and id in
        <foreach collection="idList" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </update>

    <update id="deleteProductSafeQtyCfg">
        update pl_eshop_safe_sale_qty_alarm_product_config
        set eshop_id=0,
        deleted = 1
        where profile_id=#{profileId}
        and id in
        <foreach collection="idList" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </update>

    <select id="queryKtypeIdByEshopRule" resultType="java.math.BigInteger">
        select distinct ktype_id
        from pl_eshop_stock_sync_rule_detail
        where profile_id = #{profileId}
          and rule_id in
              (select r.id
               from pl_eshop_product_sku peps
                        left join pl_eshop_product_sku_expand pepse
                                  on peps.eshop_id = pepse.eshop_id and peps.profile_id = pepse.profile_id and
                                     pepse.unique_id = peps.unique_id
                        left join pl_eshop_stock_sync_rule r
                                  on r.profile_id = peps.profile_id and peps.platform_xcode = r.xcode
               where peps.profile_id = #{profileId}
                 and peps.eshop_id = #{eshopId}
                 and pepse.mapping_type = 1
                 and r.target_type = 0
                 and r.deleted=0
               union
               select cfg.rule_id
               from pl_eshop_product_sku peps
                        left join pl_eshop_product_sku_expand pepse
                                  on peps.eshop_id = pepse.eshop_id and peps.profile_id = pepse.profile_id and
                                     pepse.unique_id = peps.unique_id
                        left join pl_eshop_product_sku_rule_config cfg
                                  on cfg.profile_id = pepse.profile_id and peps.eshop_id = cfg.eshop_id and
                                     peps.platform_num_id = cfg.platform_num_id and
                                     peps.platform_properties_name = cfg.platform_properties
               where peps.profile_id = #{profileId}
                 and peps.eshop_id = #{eshopId}
                 and pepse.mapping_type = 0
                 and ifnull(cfg.rule_id, 0) > 0)
    </select>

    <select id="queryKtypeIdByEshopDefault" resultType="java.lang.String">
        select ktype_ids
        from pl_eshop_stock_sync_default_rule
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
        union
        select ktype_ids
        from pl_eshop_ladder_default_sync_rule
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
        union
        select ktype_ids
        from pl_eshop_warehouse_stock_sync_rule
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
    </select>

    <select id="queryProductSafeQtyCfg"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.SafeSaleQtyProductConfigEntity">
        select cfg.id,
        cfg.profile_id,
        cfg.ptype_id,
        cfg.sku_id,
        cfg.unit_id,
        cfg.deleted,
        cfg.safe_qty_alarm,
        bp.fullname as ptypeName,
        bpx.xcode,
        bpu.unit_rate,
        bpu.unit_name,
        cfg.ktype_id,
        bk.fullname as ktypeName,
        cfg.eshop_id,
        e.fullname as eshopName,
        CONCAT_WS(':',
        IF(IFNULL(sku.propvalue_name1,'')='', NULL, sku.propvalue_name1),
        IF(IFNULL(sku.propvalue_name2,'')='', NULL, sku.propvalue_name2),
        IF(IFNULL(sku.propvalue_name3,'')='', NULL, sku.propvalue_name3),
        IF(IFNULL(sku.propvalue_name4,'')='', NULL, sku.propvalue_name4),
        IF(IFNULL(sku.propvalue_name5,'')='', NULL, sku.propvalue_name5),
        IF(IFNULL(sku.propvalue_name6,'')='', NULL, sku.propvalue_name6)) as localProps
        from pl_eshop_safe_sale_qty_alarm_product_config cfg
        left join pl_eshop e on e.profile_id=cfg.profile_id and cfg.eshop_id=e.otype_id
        left join base_ktype bk on bk.profile_id=cfg.profile_id and bk.id=cfg.ktype_id
        left join base_ptype bp on bp.profile_id=cfg.profile_id and bp.id=cfg.ptype_id
        left join base_ptype_sku sku on sku.profile_id=cfg.profile_id and sku.id=cfg.sku_id
        left join base_ptype_xcode bpx on bpx.profile_id=cfg.profile_id and bpx.sku_id=cfg.sku_id and
        bpx.unit_id=cfg.unit_id and bpx.defaulted=1
        left join base_ptype_unit bpu on bpu.profile_id=cfg.profile_id and bpu.ptype_id=cfg.ptype_id and
        bpu.id=cfg.unit_id
        where cfg.profile_id = #{profileId}
        <if test="skuId!=null and skuId>0">
            and cfg.sku_id=#{skuId}
        </if>
        <if test="ktypeId!=null and ktypeId>0">
            and cfg.ktype_id=#{ktypeId}
        </if>
        <if test="eshopId!=null and eshopId>0">
            and cfg.eshop_id=#{eshopId}
        </if>
        <if test="unitId!=null and unitId>0">
            and cfg.unit_id=#{unitId}
        </if>
        limit 1
    </select>

    <select id="querySafeSaleQtyConfigEntity"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.SafeSaleQtyConfigEntity">
        select cfg.id,
        cfg.profile_id,
        cfg.ktype_id,
        cfg.eshop_id,
        cfg.safe_qty_alarm,
        cfg.deleted,
        e.fullname as eshopName,
        bk.fullname as ktypeName
        from pl_eshop_safe_sale_qty_alarm_stock_config cfg
        left join pl_eshop e on cfg.profile_id = e.profile_id and e.otype_id = cfg.eshop_id
        left join base_ktype bk on bk.profile_id = cfg.profile_id and bk.id = cfg.ktype_id
        where cfg.profile_id = #{profileId}
        <if test="ktypeId!=null and ktypeId>0">
            and cfg.ktype_id=#{ktypeId}
        </if>
        limit 1
    </select>

    <insert id="insertSafeSaleQtyProductConfigEntity">
        insert into pl_eshop_safe_sale_qty_alarm_product_config
        (id, profile_id, ktype_id, eshop_id, ptype_id, sku_id, unit_id, safe_qty_alarm)
        values (#{id}, #{profileId}, #{ktypeId}, #{eshopId}, #{ptypeId}, #{skuId}, #{unitId}, #{safeQtyAlarm})
    </insert>

    <update id="modifySafeSaleQtyProductConfigEntity">
        UPDATE pl_eshop_safe_sale_qty_alarm_product_config
        set ktype_id=#{ktypeId},
            eshop_id=#{eshopId},
            ptype_id=#{ptypeId},
            sku_id=#{skuId},
            unit_id=#{unitId},
            safe_qty_alarm=#{safeQtyAlarm},
            deleted=0
        where profile_id = #{profileId}
          and id = #{id}
    </update>

    <select id="queryProductSafeQtyCfgList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.SafeSaleQtyProductConfigEntity">
        select cfg.id, cfg.profile_id, cfg.ktype_id, cfg.eshop_id, cfg.ptype_id, cfg.sku_id, cfg.unit_id,
        cfg.safe_qty_alarm, cfg.deleted,bpu.unit_name,bpu.unit_rate
        from pl_eshop_safe_sale_qty_alarm_product_config cfg
        left join base_ptype_unit bpu on cfg.profile_id=bpu.profile_id and cfg.unit_id=bpu.id
        where cfg.profile_id=#{profileId}
        and cfg.deleted=0
        <if test="skuId!=null and skuId>0">
            and cfg.sku_id=#{skuId}
        </if>
        <if test="eshopId!=null and eshopId>0">
            and cfg.eshop_id=#{eshopId}
        </if>
        <if test="ktypeId!=null and ktypeId>0">
            and cfg.ktype_id=#{ktypeId}
        </if>
        <if test="unitId!=null and unitId>0">
            and cfg.unit_id=#{unitId}
        </if>
        <if test="skuIdList!=null and skuIdList.size()>0">
            and cfg.sku_id in
            <foreach collection="skuIdList" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="querySafeSaleQtyConfigList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.SafeSaleQtyConfigEntity">
        select id, profile_id, ktype_id, eshop_id, safe_qty_alarm, deleted
        from pl_eshop_safe_sale_qty_alarm_stock_config
        where profile_id=#{profileId}
        <if test="ktypeId!=null and ktypeId>0">
            and ktype_id=#{ktypeId}
        </if>
        <if test="eshopId!=null and eshopId>0">
            and eshop_id=#{eshopId}
        </if>
        and deleted=0
    </select>
</mapper>
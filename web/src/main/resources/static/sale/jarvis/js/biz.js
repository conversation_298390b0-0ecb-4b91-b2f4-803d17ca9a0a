// js\biz\atype.js
$jarvis.register.commonContrl('atype', {
    change: function (resMaps, newData) {
        this.determine(resMaps, newData);
    },

    atypeChange: function (newData) {
        this._doChangeSelf(this.control, newData);
    },

    _doChangeSelf: function (sender, newData) {
        sender.set_value(newData.id);
        sender.set_text(newData.fullname);
    },

    multiacctChange: function (data) {
        this.control.set_value(data.atypevalue);
        this.control.set_text(data.atypename);
        this.control.set_enabled(data.atypeenabled);
    },

    refresh: function () {
        this.control.set_text('');
        this.control.set_value('');
    },
    validation: function (bill) {
        var vchtype = bill.vchtype;
        // 不需要验证收款账户必填的单据
        if (vchtype.substr(0, 4) == "Sale"
            || vchtype.substr(0, 3) == "Buy") {
            return true;
        }
        //默认校验
        if (vchtype == "Expense") {
            //必须判断 金额是否相等
            if (bill.customType == 2) {
                this._checkNull(bill, "付款账户不能为空!");
                if (bill.currencyBillTotal != bill.total) {
                    $recordsheet._alertMsg("付款金额不等于费用金额,保存失败!")
                }
            } else {
                if (bill.total) {
                    this._checkNull(bill, "付款账户不能为空!");

                }
                return true;
            }
        }
        if (vchtype == "FixedAssetsBuy") {
            if (bill.total)
                this._checkNull(bill, "付款账户不能为空!");
            return true;
        }
        if (vchtype == "FixedAssetsSale") {
            if (bill.total)
                this._checkNull(bill, "收款账户不能为空!");
            return true;
        }
        if (vchtype == "OtherIncome") {
            //必须判断 金额是否相等
            if (bill.customType == 2) {
                this._checkNull(bill);
                if (bill.currencyBillTotal != bill.total) {
                    $recordsheet._alertMsg("收入金额不等于收款金额,保存失败!")
                }
            } else {
                return true;
            }
        }
        if (vchtype == "TurnAccount") {
            this._checkNull(bill, "付款账户不能为空!");
            if ((bill.expenseTotal && bill.expenseTotal != "0") && (!bill.expenseId || bill.expenseId == "0")) $recordsheet._alertMsg("手续费账户不能为空!")
            bill.expenseTotal = bill.expenseTotal ? bill.expenseTotal : 0
            bill.currencyBillTotal = bill.currencyBillTotal ? bill.currencyBillTotal : 0
            var countTotal = parseFloat(bill.currencyBillTotal + bill.expenseTotal);
            if (countTotal != bill.total) {
                $recordsheet._alertMsg("手续费与收款金额之和不等于付款金额,保存失败!")
            }
            return true;
        }
        return this._checkNull(bill);
    },


    _checkNull: function (bill, erroMsg) {
        if ((!bill.atypeId || bill.atypeId == "0") && !(bill.atypeId == "")) {
            erroMsg = erroMsg ? erroMsg : "收款账户不能为空!";
            $recordsheet._alertMsg(erroMsg);
        }
        //检测金额是否在允许范围之内
        return true;
    },

});
// js\biz\batchNoColumn.js
/**
 * 批次号--已没用，现在用jarvis-biz.js下的
 *
 *
 */
$jarvis.register.columnControl('batchNoColumnByAudit', {


    controlInfo: function () {
        var form = this.get_form();
        var parentForm = form.get_parentForm();
        var formAction = parentForm.get_action();
        return {
            property: {
                "SelectorPage": "jxc/recordsheet/selector/PtypeBatchSelector.gspx?unloadZero=true"
            },
            events: {
                "OnDelayChanged": this.bindControlEvent('_doDelayChanged'),
                "OnSelectorSelected": this.bindControlEvent('_doSelectorSelected'),
                "OnButtonClick": this.bindControlEvent("_doSelectorBtnClick")
            }
        }
    },

    _getOutType: function (formAction) {
        var inouttype = formAction.inouttype ? formAction.inouttype : '';
        var res = {isOut: false, kType: 'edKType'};
        if (!inouttype) {
            return res;
        }
        var gridId = this.getCurrentGridId();
        if (!gridId) {
            return res;
        }
        var inouttypeArr = inouttype.split(',');
        var thisGridType = '';
        for (var index in inouttypeArr) {
            var temp = inouttypeArr[index];
            if (temp.indexOf(gridId) != -1) {
                thisGridType = temp;
                break;
            }
        }
        if (!thisGridType) {
            return res;
        }
        var typeArr = thisGridType.split('_');
        var type = 'in';
        if (typeArr.length == 3) {
            res.kType = typeArr[2];
            type = typeArr[1];
        }
        if (type.toUpperCase() == 'OUT') {
            res.isOut = true;
            return res;
        }
        return res;
    },

    _doManyGridInit: function (formAction) {
        var inouttype = this._getOutType(formAction);
        if (inouttype.isOut) {
            return {
                property: {
                    "SelectorPage": "jxc/recordsheet/selector/PtypeBatchSelector.gspx"
                },
                events: {
                    "OnDelayChanged": this.bindControlEvent('_doDelayChanged'),
                    "OnSelectorSelected": this.bindControlEvent('_doSelectorSelected'),
                    "OnButtonClick": this.bindControlEvent("_doSelectorBtnClick")
                }
            }
        }
        return null;
    },

    _doDelayChanged: function (sender, e) {
        var form = sender.get_form();
        var text = sender.get_text();
        if (text == "") {
            var grid = sender.get_owner();
            var rowIndex = grid.get_selectedRowIndex();
            var rowData = grid.get_selectedRowData();
            if (rowData == null || !rowData.ptypeId) {
                return;
            }
            sender.set_value("");
            rowData.batchno = "";
            rowData.costId = 0;
            rowData.produceDate = "";
            rowData.expireDate = "";
            rowData.serialNo = [];
            rowData.snnoStr = "";
            if (rowData.snnoCount && rowData.snnoCount > 0) {
                rowData.snnoCount = 0;
            }
            rowData.modify = true;
            var clearHandler = this.get_form().get_action().doClearBatch;
            if (clearHandler) {
                clearHandler(rowData);
            }

            var gridId = this.getCurrentGridId();
            if (gridId) {
                grid.modifyRowData(rowIndex, rowData);
            }
            return;
        }
        //判断是否存在搜索面板
        if (!form["batchNoSearch"]) {
            //不存在 创建下拉面板
            var PopupBlock = $createControl(Craba.UI.PopupBlock, {
                ID: "batchNoSearch", CssClass: "SearchPopup", CssStyle: "padding:0"
            }, form);
            var ptypeSearchGrid = $createControl(Craba.UI.Grid, {
                ID: "batchNoSearchGrid",
                DataField: "batchNoSearchGrid",
                DefaultRowCount: "0",
                AllowCopy: "false",
                Width: "380",
                MinHeight: "50",
                MaxHeight: "200",
                ShowHeader: "true",
                ShowRowNo: "false",
                AllowFilter: "false",
                AutoHeight: "true",
                UseEvenRowColor: "false",
                OnRowClick: this.bindControlEvent("doColumnSelectorSelected"),
                OnRowEnterPress: this.bindControlEvent("doColumnSelectorSelected"),
                CssClass: "NoneBorder tleft"
            }, form, PopupBlock);
            var usercode = $createControl(Craba.UI.TextColumn, {
                Caption: "批次号",
                AllowHTML: 'true',
                OnGetDisplayText: this.bindControlEvent("doGetDisplayText"),
                DataField: "batchno"
            }, form, ptypeSearchGrid);
            PopupBlock.appendTo(form);
        }
        if (e.keyCode == Sys.UI.Key.up || e.keyCode == Sys.UI.Key.down) {
            if (form.batchNoSearchGrid.get_recordCount() < 1) return;
            form.batchNoSearch.popupAt(sender);
            form.batchNoSearchGrid.focus(); //切换焦点到数据载体上面
            return;
        }

        var searchText = sender.get_text().trimChar();
        this._doSearchBatchNo(sender, searchText, rowData);
    },

    doGetDisplayText: function (sender, args) {
        var text = args.get_text();
        if (!text) return;
        var searchText = this.control.get_text ? this.control.get_text() : (this.control._control ? this.control._control.get_text() : '');
        if (!searchText) {
            return;
        }
        text = $recordsheet.replaceSearchText(text, searchText);
        args.set_text(text);
    },

    doColumnSelectorSelected: function (sender, args) {
        var form = this.get_form();
        var grid = form.details;
        var selectdata = form.batchNoSearchGrid.get_selectedRowData();
        var rowData = grid.get_selectedRowData();
        var rowIndex = grid.get_selectedRowIndex();
        if (selectdata && selectdata.id) {
            rowData.batchNo = selectdata.batchno
            grid.modifyRowData(rowIndex, rowData);
        }

    },
    _doSearchBatchNo: function (sender, text, rowData) {
        var form = sender.get_form();
        if (!text) {
            text = "";
        }
        var service = form.get_action().service;
        var queryData = {};
        queryData.pageSize = 10;
        queryData.pageIndex = 1;
        if (!rowData || !rowData.ptypeId) {
            return;
        }

        queryData.ptypeId = rowData.ptypeId;
        queryData.ktypeId = rowData.ktypeId;
        queryData.unitName = rowData.unitName;
        queryData.unitRate = rowData.unitRate;
        queryData.batchno = text;
        service.post("jxc/recordsheet/baseSearch/searchBatch", queryData, function (rest) {
            if (rest.code == 200) {
                var datalist = rest.data.list;
                if (datalist.length == 0) {
                    form.batchNoSearchGrid.dataBind([{
                        "batchno": "没有匹配项"
                    }]);
                    form.batchNoSearch.popupAt(sender);
                } else {
                    form.batchNoSearchGrid.dataBind(datalist);
                    form.batchNoSearch.popupAt(sender);
                    form.batchNoSearchGrid.focus();
                }
            }
        })
    },

    _doSelectorBtnClick: function (sender, eventArgs) {
        var form = this.get_form();
        // var mainItem = this._getDeliverGridSelectedItem(sender);
        // if (mainItem == null) {
        //     return;
        // }
        var formAction = form.get_action();


        var grid = sender.get_owner();
        var rowData = grid.get_selectedRowData();
        if ((rowData.costMode == null || rowData.costMode == undefined) && rowData.ptype) {
            rowData.costMode = rowData.ptype.costMode;
        }

        var ktypeId = rowData.ktypeId;

        var action = this.businessModel.action;

        if (!rowData || !rowData.ptypeId) {
            return;
        }
        var ptypeId = rowData.ptypeId;

        var goodsInfo = action.goodsInfo;

        if (goodsInfo == undefined) {
            var rowIndex = grid.get_selectedRowIndex();
            // rowData.protectDays = rowData.ptype.protectDays;
            // rowData.costMode = rowData.ptype.costMode;
            if (!rowData) return;
            var queryParams = {
                rowIndex: rowIndex,
                rowData: rowData,
                ktypeId: ktypeId,
                unitName: rowData.unitName,
                unitRate: rowData.unitRate,
                pageTitle: "实物库存批次选择框"
            };
            sender.set_selectorPageParams({queryParams: queryParams});
            return;
        }
        var thisInfo = goodsInfo[ptypeId];
        var units = (!thisInfo || !thisInfo.units) ? null : thisInfo.units;
        if (!units || units.length == 0) {

            sender.get_form().get_action().service.post("/sale/jarvis/common/getPtypeUnits", ptypeId, function (res) {
                if (!res.data || res.data.length === 0) return;
                var data = res.data;
                units = data;
                var items = [];
                for (var i = 0; i < units.length; i++) {
                    var item = {};
                    var unit = units[i];
                    item.unitRate = unit.unitRate;
                    item.unitName = unit.unitName;
                    item.unitId = unit.unitId;
                    items.push(item);
                }
                if (!(items.length === 0)) {
                    var ptype = {};
                    ptype.units = units;
                    action.goodsInfo[ptypeId] = ptype;
                }
            }, false);
        }

        var rowIndex = grid.get_selectedRowIndex();
        if (rowData.ptype) {
            rowData.protectDays = rowData.ptype.protectDays;
            rowData.costMode = rowData.ptype.costMode;
        } else {
            rowData.protectDays = rowData.protectDays;
            rowData.costMode = rowData.costMode;
        }

        if (!rowData) return;
        var queryParams = {
            rowIndex: rowIndex,
            rowData: rowData,
            ktypeId: ktypeId,
            unitName: rowData.unitName,
            unitRate: rowData.unitRate
        };
        sender.set_selectorPageParams({queryParams: queryParams});
    },
    _getDeliverGridSelectedItem: function (sender) {
        var form = sender.get_form();
        var grid = form.deliverMainGrid;
        var deliverBill = grid.get_selectedRowData();
        if (!deliverBill)
            return null;
        deliverBill.rowIndex = grid.get_selectedRowIndex();
        return deliverBill;
    },
    _doSelectorSelected: function (sender, args) {
        var batchNoList = args.get_form().batchNoList;
        var grid = sender.get_owner();
        var isPickSecond = (grid._idPart == "orderPtypeListGrid");
        var rowIndex = grid.get_selectedRowIndex();
        var selectRowData = grid.get_selectedRowData();

        var action = this.businessModel.action;

        if (!selectRowData || !selectRowData.ptypeId) {
            return;
        }
        var ptypeId = selectRowData.ptypeId;

        var _this = this;
        var units = [];
        var goodsInfo = action.goodsInfo;
        if (goodsInfo != undefined) {
            var thisInfo = goodsInfo[ptypeId];
            var units = (!thisInfo || !thisInfo.units) ? null : thisInfo.units;
        }
        if (!units || units.length == 0) {
            units = $jarvisUtils.postSync("/sale/jarvis/common/getPtypeUnits", ptypeId).data;
        }
        // if (!units || units.length == 0) {
        //
        //     sender.get_form().get_action().service.post("/sale/jarvis/common/getPtypeUnits", ptypeId, function (res) {
        //         if (!res.data || res.data.length === 0) return;
        //         var data = res.data;
        //          units = data;
        //         var items = [];
        //         for (var i = 0; i < units.length; i++) {
        //             var item = {};
        //             var unit = units[i];
        //             item.unitRate = unit.unitRate;
        //             item.unitName = unit.unitName;
        //             item.unitId = unit.unitId;
        //             items.push(item);
        //         }
        //         if (!(items.length === 0)) {
        //             var ptype = {};
        //             ptype.units = units;
        //             action.goodsInfo[ptypeId] = ptype;
        //         }
        //     },false);
        // }


        var rowData = {};
        var allQty = 0;
        for (var j = 0; j < batchNoList.length; j++) {
            var batchQty = batchNoList[j].unitQty ? batchNoList[j].unitQty : 1;
            allQty += $jarvismoney._doCalculateQty(batchQty);
        }
        if (selectRowData.unitQty < allQty) {
            $common.alert($language.get("qtyMustMatch", "选择的数量超过了明细数量！"));
            return;
        }
        if (allQty == 0) {
            $common.alert($language.get("qtyMustMatch", "选择的数量为0！"));
            return;
        }
        if (batchNoList.length == 0) {
            $common.alert($language.get("noBatch", "没有选中批次！"));
            return;
        }
        var oldqty = selectRowData.unitQty;
        /** 折后含税金额**/
        var disedTaxedTotal = selectRowData.disedTaxedTotal;
        /** 含税金额**/
        var total = selectRowData.total;

        /** 服务费**/
        var serviceFee = !selectRowData.serviceFee ? 0 : selectRowData.serviceFee;
        /** 优惠金额**/
        // var preferentialTotal = !selectRowData.preferentialTotal ? 0 : selectRowData.preferentialTotal;
        /** 税额**/
        var taxTotal = !selectRowData.taxTotal ? 0 : selectRowData.taxTotal;
        /** 商城扣费**/
        var mallFee = !selectRowData.mallFee ? 0 : selectRowData.mallFee;
        /** 买家运费**/
        var buyerFreightFee = !selectRowData.buyerFreightFee ? 0 : selectRowData.buyerFreightFee;
        /** 商家单品优惠**/
        var sellerPtypePreferentialTotal = !selectRowData.sellerPtypePreferentialTotal ? 0 : selectRowData.sellerPtypePreferentialTotal;
        /** 平台单品优惠**/
        var platformPtypePreferentialTotal = !selectRowData.platformPtypePreferentialTotal ? 0 : selectRowData.platformPtypePreferentialTotal;
        /** 商家整单优惠分摊**/
        var sellerPreferentialTotal = !selectRowData.sellerPreferentialTotal ? 0 : selectRowData.sellerPreferentialTotal;
        /** 平台整单优惠分摊**/
        var platformPreferentialTotal = !selectRowData.platformPreferentialTotal ? 0 : selectRowData.platformPreferentialTotal;
        /** 分销结算含税金额**/
        var distributionBalanceTaxedTotal = !selectRowData.distributionBalanceTaxedTotal ? 0 : selectRowData.distributionBalanceTaxedTotal;
        /** 分销佣金**/
        var distributionCommissionTotal = !selectRowData.distributionCommissionTotal ? 0 : selectRowData.distributionCommissionTotal;
        /** 分销佣金**/
        var comboShareScale = !selectRowData.comboShareScale ? 0 : selectRowData.comboShareScale;

        var clearHandler = this.get_form().get_action().doClearBatch;

        for (var i = 0; i < batchNoList.length; i++) {
            // if (i == 0) {
            //     rowData = Object.clone(selectRowData);//selectRowData;
            // } else {
            //     rowData = Object.clone(selectRowData);
            // }
            rowData = Object.clone(selectRowData);
            rowData.batchno = batchNoList[i].batchNo;
            rowData.costId = batchNoList[i].costId;
            rowData.serialNo = [];
            rowData.snnoStr = "";
            if (rowData.snnoCount && rowData.snnoCount > 0) {
                rowData.snnoCount = 0;
            }
            var batchQty = batchNoList[i].unitQty ? batchNoList[i].unitQty : 1;
            rowData.unitQty = $jarvismoney._doCalculateQty(batchQty);
            if (rowData.noPackingQty || rowData.noPackingQty == 0) {
                rowData.noPackingQty = $jarvismoney._doCalculateQty(rowData.unitQty - rowData.packingQty);
            }
            rowData.qty = rowData.unitQty * rowData.unitRate;
            rowData.produceDate = batchNoList[i].produceDate;
            rowData.expireDate = batchNoList[i].expireDate;
            var AfterHandler = this.get_form().get_action().doSelectorSelectedAfter;
            if (AfterHandler) {
                AfterHandler(rowData);
            }

            if (!isPickSecond) {
                if (batchNoList.length == 1 && rowData.unitQty == selectRowData.unitQty) {
                    rowData.disedTaxedTotal = rowData.disedTaxedTotal;
                    rowData.total = rowData.total;

                } else {
                    rowData.disedTaxedTotal = $jarvismoney._doCalculateTotal(rowData.disedTaxedPrice, rowData.unitQty);
                    rowData.total = $jarvismoney._doCalculateTotal(rowData.price, rowData.unitQty);

                }
                rowData.serviceFee = $jarvismoney._doCalculateFee(rowData.serviceFee, rowData.unitQty, oldqty);
                //  rowData.preferentialTotal = $jarvismoney._doCalculateFee(rowData.preferentialTotal, rowData.unitQty, oldqty);
                rowData.taxTotal = $jarvismoney._doCalculateFee(rowData.taxTotal, rowData.unitQty, oldqty);
                rowData.mallFee = $jarvismoney._doCalculateFee(rowData.mallFee, rowData.unitQty, oldqty);
                rowData.buyerFreightFee = $jarvismoney._doCalculateFee(rowData.buyerFreightFee, rowData.unitQty, oldqty);
                rowData.comboShareScale = $jarvismoney._doCalculateComboShareScale(rowData.comboShareScale, rowData.unitQty, oldqty);
                rowData.sellerPtypePreferentialTotal = $jarvismoney._doCalculateFee(rowData.sellerPtypePreferentialTotal, rowData.unitQty, oldqty);
                rowData.platformPtypePreferentialTotal = $jarvismoney._doCalculateFee(rowData.platformPtypePreferentialTotal, rowData.unitQty, oldqty);
                rowData.sellerPreferentialTotal = $jarvismoney._doCalculateFee(rowData.sellerPreferentialTotal, rowData.unitQty, oldqty);
                rowData.platformPreferentialTotal = $jarvismoney._doCalculateFee(rowData.platformPreferentialTotal, rowData.unitQty, oldqty);
                rowData.distributionBalanceTaxedTotal = $jarvismoney._doCalculateFee(rowData.distributionBalanceTaxedTotal, rowData.unitQty, oldqty);
                rowData.distributionCommissionTotal = $jarvismoney._doCalculateFee(rowData.distributionCommissionTotal, rowData.unitQty, oldqty);


                disedTaxedTotal = $jarvismoney._doCalculate(disedTaxedTotal - rowData.disedTaxedTotal);
                total = $jarvismoney._doCalculate(total - rowData.total);
                serviceFee = $jarvismoney._doCalculate(serviceFee - rowData.serviceFee);
                // preferentialTotal = $jarvismoney._doCalculate(preferentialTotal - rowData.preferentialTotal);
                taxTotal = $jarvismoney._doCalculate(taxTotal - rowData.taxTotal);
                mallFee = $jarvismoney._doCalculate(mallFee - rowData.mallFee);
                buyerFreightFee = $jarvismoney._doCalculate(buyerFreightFee - rowData.buyerFreightFee);
                sellerPtypePreferentialTotal = $jarvismoney._doCalculate(sellerPtypePreferentialTotal - rowData.sellerPtypePreferentialTotal);
                platformPtypePreferentialTotal = $jarvismoney._doCalculate(platformPtypePreferentialTotal - rowData.platformPtypePreferentialTotal);
                sellerPreferentialTotal = $jarvismoney._doCalculate(sellerPreferentialTotal - rowData.sellerPreferentialTotal);
                platformPreferentialTotal = $jarvismoney._doCalculate(platformPreferentialTotal - rowData.platformPreferentialTotal);
                distributionBalanceTaxedTotal = $jarvismoney._doCalculate(distributionBalanceTaxedTotal - rowData.distributionBalanceTaxedTotal);
                distributionCommissionTotal = $jarvismoney._doCalculate(distributionCommissionTotal - rowData.distributionCommissionTotal);
                comboShareScale = math.round(parseFloat(comboShareScale - rowData.comboShareScale), 8);

            }
            rowData.modify = true;
            rowData.unitRateName = $jarviscommon._getUnitQty(units, $jarvismoney._doCalculateQty(rowData.unitQty * rowData.unitRate));

            if (i > 0) {
                if (i == batchNoList.length - 1 && selectRowData.unitQty == allQty) {
                    if (!isPickSecond) {
                        rowData.detailId = 0;
                        rowData.disedTaxedTotal = $jarvismoney._doCalculate(rowData.disedTaxedTotal + disedTaxedTotal);
                        rowData.total = $jarvismoney._doCalculate(rowData.total + total);
                        rowData.serviceFee = $jarvismoney._doCalculate(rowData.serviceFee + serviceFee);
                        // rowData.preferentialTotal = $jarvismoney._doCalculate(rowData.preferentialTotal + preferentialTotal);
                        rowData.taxTotal = $jarvismoney._doCalculate(rowData.taxTotal + taxTotal);
                        rowData.mallFee = $jarvismoney._doCalculate(rowData.mallFee + mallFee);
                        rowData.buyerFreightFee = $jarvismoney._doCalculate(rowData.buyerFreightFee + buyerFreightFee);

                        rowData.sellerPtypePreferentialTotal = $jarvismoney._doCalculate(rowData.sellerPtypePreferentialTotal + sellerPtypePreferentialTotal);
                        rowData.platformPtypePreferentialTotal = $jarvismoney._doCalculate(rowData.platformPtypePreferentialTotal + platformPtypePreferentialTotal);
                        rowData.sellerPreferentialTotal = $jarvismoney._doCalculate(rowData.sellerPreferentialTotal + sellerPreferentialTotal);
                        rowData.platformPreferentialTotal = $jarvismoney._doCalculate(rowData.platformPreferentialTotal + platformPreferentialTotal);
                        rowData.distributionBalanceTaxedTotal = $jarvismoney._doCalculate(rowData.distributionBalanceTaxedTotal + distributionBalanceTaxedTotal);
                        rowData.distributionCommissionTotal = $jarvismoney._doCalculate(rowData.distributionCommissionTotal + distributionCommissionTotal);
                        rowData.comboShareScale = math.round(parseFloat(rowData.comboShareScale + comboShareScale), 8);
                    }
                    if (grid.get_rowCount() == rowIndex + i) {
                        grid.appendRowData(rowData);
                    } else {
                        grid.insertRowData(rowIndex + i, rowData);
                    }
                } else {
                    rowData.detailId = 0;
                    if (grid.get_rowCount() == rowIndex + i) {
                        grid.appendRowData(rowData);
                    } else {
                        grid.insertRowData(rowIndex + i, rowData);
                    }
                }
            } else {
                var gridId = this.getCurrentGridId();
                if (gridId) {
                    grid.modifyRowData(rowIndex + i, rowData);
                }
            }
            if (clearHandler) {
                clearHandler(rowData);
            }
        }

        if (selectRowData.unitQty > allQty && allQty > 0) {
            rowData = Object.clone(selectRowData);

            rowData.batchno = "";
            rowData.unitQty = $jarvismoney._doCalculateQty(selectRowData.unitQty - allQty);
            rowData.checkQty = rowData.unitQty;
            if (rowData.noPackingQty || rowData.noPackingQty == 0) {
                rowData.noPackingQty = $jarvismoney._doCalculateQty(rowData.unitQty - rowData.packingQty);
                rowData.qty = rowData.unitQty;
            }
            rowData.produceDate = "";
            rowData.expireDate = "";
            rowData.serialNo = [];
            rowData.snnoStr = "";
            if (rowData.snnoCount && rowData.snnoCount > 0) {
                rowData.snnoCount = 0;
            }
            if (!isPickSecond) {
                rowData.detailId = 0;
                rowData.disedTaxedTotal = disedTaxedTotal;
                rowData.total = total;
                rowData.serviceFee = serviceFee;
                // rowData.preferentialTotal = preferentialTotal;
                rowData.taxTotal = taxTotal;
                rowData.mallFee = mallFee;
                rowData.buyerFreightFee = buyerFreightFee;
                rowData.sellerPtypePreferentialTotal = sellerPtypePreferentialTotal;
                rowData.platformPtypePreferentialTotal = platformPtypePreferentialTotal;
                rowData.sellerPreferentialTotal = sellerPreferentialTotal;
                rowData.platformPreferentialTotal = platformPreferentialTotal;
                rowData.distributionBalanceTaxedTotal = distributionBalanceTaxedTotal;
                rowData.distributionCommissionTotal = distributionCommissionTotal;
                rowData.disedTaxedPrice = $jarvismoney._doCalculatePrice(rowData.disedTaxedTotal, rowData.unitQty);
                rowData.distributionCommissionTotal = distributionCommissionTotal;
                rowData.comboShareScale = comboShareScale;
            }
            rowData.unitRateName = $jarviscommon._getUnitQty(units, $jarvismoney._doCalculateQty(rowData.unitQty * rowData.unitRate));
            rowData.modify = true;
            if (clearHandler) {
                clearHandler(rowData);
            }
            if (grid.get_rowCount() == rowIndex + i) {
                grid.appendRowData(rowData);
            } else {
                grid.insertRowData(rowIndex + i, rowData);
            }


        }

        var enable = selectRowData.ptype = null ? selectRowData.snenabled : selectRowData.ptype.snEnabled;
        if (!isPickSecond && enable) {
            $common.showOk($language.get("snTips", "批次选择成功！序列号已清空，若需要请重新选择！"));
        }
        enable = selectRowData.snEnabled = null ? selectRowData.snenabled : selectRowData.snEnabled;
        if (isPickSecond && enable) {
            $common.showOk($language.get("snTips", "批次选择成功！序列号已清空，若需要请重新选择！"));
        }
    },

    _doModifyRowData: function (newData) {
        var grid = this.control.get_grid();
        var rowIndex = newData.rowIndex;
        var rowData = newData.rowData;
        grid.modifyCellsValue(rowIndex, ['batchNo', 'produceDate', 'expireDate'], rowData);
    },

    _doRequestData: function (newData) {
        if (!newData) {
            return;
        }
        var form = this.get_form().get_parentForm();
        var edKtype = form.edKType ? form.edKType : form.edKtype;
        var formAction = form.get_action();
        var bussinessType = formAction.bussinesstype;
        if (bussinessType && bussinessType.toUpperCase() == 'IN') {
            return;
        }
        if (bussinessType && bussinessType.toUpperCase() == 'CHANGE') {
            var outType = this._getOutType(formAction);
            edKtype = form[outType.kType];
            if (!outType.isOut) {
                return;
            }
        }
        var ktypeId = edKtype.get_value() ? edKtype.get_value() : null;
        if (newData.length == 1) {
            newData[0].rowData.ktypeId ? ktypeId = newData[0].rowData.ktypeId : '';
        }
        // 没选仓库  不自动带出
        if (!ktypeId) {
            return;
        }
        var ptypeList = [];
        for (var i = 0; i < newData.length; i++) {
            if (!newData[i].rowData.batchenabled) {
                continue;
            }
            ptypeList.push({
                rowIndex: newData[i].rowIndex,
                ptypeId: newData[i].rowData.ptypeId ? newData[i].rowData.ptypeId : 0,
            });
        }
        if (ptypeList.length < 1) {
            return;
        }
        var request = {};
        request.ptypeList = ptypeList;
        request.ktypeId = ktypeId;
        var _this = this;
        form.get_action().service.post('jxc/recordsheet/ptype/getPtypeBatch', request, function (response) {
            if (response.code == '200' && response.data) {
                var data = response.data;
                if (data && data.length > 0) {
                    for (var i = 0; i < newData.length; i++) {
                        for (var j = 0; j < data.length; j++) {
                            if (newData[i].rowIndex == data[j].rowIndex && newData[j].rowData.ptypeId == data[j].ptypeId) {
                                newData[i].rowData.batchNo = !data[j].batchNo ? null : data[j].batchNo;
                                newData[i].rowData.produceDate = !data[j].produceDate ? null : data[j].produceDate;
                                newData[i].rowData.expireDate = !data[j].expireDate ? null : data[j].expireDate;
                                _this._doModifyRowData({
                                    rowData: newData[i].rowData,
                                    rowIndex: newData[i].rowIndex
                                });
                                break;
                            }
                        }
                    }
                }
            }
        });
    }

});
// js\biz\batchNoColumnByRefund.js
/**
 * 批次号 -- 已没用，现在用jarvis-biz.js下的
 *
 *
 */
$jarvis.register.columnControl('batchNoColumnByRefund', {


    controlInfo: function () {
        var form = this.get_form();
        var parentForm = form.get_parentForm();
        var formAction = parentForm.get_action();
        // 新增/编辑收货记录的批次选择框逻辑有点不一致，暂时直接用eshoporder自带的
        if (form.get_url().includes("EditEshopRefundReceiveCheckIn.gspx")) {
            return {
                property: {
                    "SelectorPage": "jxc/recordsheet/selector/PtypeBatchSelector.gspx"
                },
                events: {
                    /*                "OnDelayChanged": this.bindControlEvent('_doDelayChanged'),*/
                    /*                "OnSelectorSelected": this.bindControlEvent('_doSelectorSelected'),*/
                    "OnButtonClick": this.bindControlEvent("_doSelectorBtnClickForRefund"),
                    "OnSelectorLoaded": this.bindControlEvent("_doSelectorLoadedForRefund")
                }
            }
        } else {
            return {
                property: {
                    "SelectorPage": "jxc/recordsheet/selector/PtypeBatchSelector.gspx"
                },
                events: {
                    "OnDelayChanged": this.bindControlEvent('_doDelayChanged'),
                    "OnSelectorSelected": this.bindControlEvent('_doSelectorSelected'),
                    "OnButtonClick": this.bindControlEvent("_doSelectorBtnClick")
                }
            }
        }
    },

    // 售后-收货登记等页面批次框初始化表格方法
    _doSelectorLoadedForRefund: function (sender, eventArgs) {
        var grid = eventArgs.get_form().grid;
        grid.findColumn("enabled").set_visible(false);
        grid.findColumn("unitQty").set_visible(false);
        grid.findColumn("subQty").set_visible(false);
        grid.findColumn("unitQty").set_allowConfig(false);
        grid.findColumn("subQty").set_allowConfig(false);
    },

    // 售后-收货登记等页面批次框加载数据初始化方法
    _doSelectorBtnClickForRefund: function (sender) {
        var form = this.get_form();
        var grid = sender.get_owner();
        var rowIndex = grid.get_selectedRowIndex();
        // 如果对原来的rowData里的ktypeid进行修改，会导致直接入库的时候被过滤
        var rowData = Object.clone(grid.get_selectedRowData());
        // 新增售后用的ktypeId、收货登记用的edKtype，且收货登记页面不能保存明细的ktypeid，否则会出现问题
        var ktypeId = form.ktypeId ? form.ktypeId.get_value() : form.edKtype.get_value();
        if (!rowData || !rowData.ptypeId) {
            return;
        }
        //需要确认新增和编辑为什么仓库id不一样导致查询出来结果不一样
        if (form.ktypeId || form.edKtype) {
            rowData.ktypeId = ktypeId;
        }
        var queryParams = {
            rowIndex: rowIndex,
            rowData: rowData,
            ktypeId: ktypeId,
            unitId: rowData.unit,
            unitName: rowData.unitName,
            unitRate: rowData.unitRate,
            pageTitle: "选择成本批次"
        };
        sender.set_selectorPageParams({queryParams: queryParams});
    },

    _getOutType: function (formAction) {
        var inouttype = formAction.inouttype ? formAction.inouttype : '';
        var res = {isOut: false, kType: 'edKType'};
        if (!inouttype) {
            return res;
        }
        var gridId = this.getCurrentGridId();
        if (!gridId) {
            return res;
        }
        var inouttypeArr = inouttype.split(',');
        var thisGridType = '';
        for (var index in inouttypeArr) {
            var temp = inouttypeArr[index];
            if (temp.indexOf(gridId) != -1) {
                thisGridType = temp;
                break;
            }
        }
        if (!thisGridType) {
            return res;
        }
        var typeArr = thisGridType.split('_');
        var type = 'in';
        if (typeArr.length == 3) {
            res.kType = typeArr[2];
            type = typeArr[1];
        }
        if (type.toUpperCase() == 'OUT') {
            res.isOut = true;
            return res;
        }
        return res;
    },

    _doManyGridInit: function (formAction) {
        var inouttype = this._getOutType(formAction);
        if (inouttype.isOut) {
            return {
                property: {
                    "SelectorPage": "jxc/recordsheet/selector/PtypeBatchSelector.gspx"
                },
                events: {
                    "OnDelayChanged": this.bindControlEvent('_doDelayChanged'),
                    "OnSelectorSelected": this.bindControlEvent('_doSelectorSelected'),
                    "OnButtonClick": this.bindControlEvent("_doSelectorBtnClick")
                }
            }
        }
        return null;
    },

    _doDelayChanged: function (sender, e) {
        var form = sender.get_form();
        var text = sender.get_text();
        if (text == "") {
            var grid = sender.get_owner();
            var rowIndex = grid.get_selectedRowIndex();
            var rowData = grid.get_selectedRowData();
            if (rowData == null || !rowData.ptypeId) {
                return;
            }
            sender.set_value("");
            rowData.batchno = "";
            rowData.costId = 0;
            rowData.produceDate = "";
            rowData.expireDate = "";
            rowData.serialNo = [];
            rowData.snnoStr = "";
            if (rowData.snnoCount && rowData.snnoCount > 0) {
                rowData.snnoCount = 0;
            }
            rowData.modify = true;
            var clearHandler = this.get_form().get_action().doClearBatch;
            if (clearHandler) {
                clearHandler(rowData);
            }

            var gridId = this.getCurrentGridId();
            if (gridId) {
                grid.modifyRowData(rowIndex, rowData);
            }
            return;
        }
        //判断是否存在搜索面板
        if (!form["batchNoSearch"]) {
            //不存在 创建下拉面板
            var PopupBlock = $createControl(Craba.UI.PopupBlock, {
                ID: "batchNoSearch", CssClass: "SearchPopup", CssStyle: "padding:0"
            }, form);
            var ptypeSearchGrid = $createControl(Craba.UI.Grid, {
                ID: "batchNoSearchGrid",
                DataField: "batchNoSearchGrid",
                DefaultRowCount: "0",
                AllowCopy: "false",
                Width: "380",
                MinHeight: "50",
                MaxHeight: "200",
                ShowHeader: "true",
                ShowRowNo: "false",
                AllowFilter: "false",
                AutoHeight: "true",
                UseEvenRowColor: "false",
                OnRowClick: this.bindControlEvent("doColumnSelectorSelected"),
                OnRowEnterPress: this.bindControlEvent("doColumnSelectorSelected"),
                CssClass: "NoneBorder tleft"
            }, form, PopupBlock);
            var usercode = $createControl(Craba.UI.TextColumn, {
                Caption: "批次号",
                AllowHTML: 'true',
                OnGetDisplayText: this.bindControlEvent("doGetDisplayText"),
                DataField: "batchno"
            }, form, ptypeSearchGrid);
            PopupBlock.appendTo(form);
        }
        if (e.keyCode == Sys.UI.Key.up || e.keyCode == Sys.UI.Key.down) {
            if (form.batchNoSearchGrid.get_recordCount() < 1) return;
            form.batchNoSearch.popupAt(sender);
            form.batchNoSearchGrid.focus(); //切换焦点到数据载体上面
            return;
        }

        var searchText = sender.get_text().trimChar();
        this._doSearchBatchNo(sender, searchText, rowData);
    },

    doGetDisplayText: function (sender, args) {
        var text = args.get_text();
        if (!text) return;
        var searchText = this.control.get_text ? this.control.get_text() : (this.control._control ? this.control._control.get_text() : '');
        if (!searchText) {
            return;
        }
        text = $recordsheet.replaceSearchText(text, searchText);
        args.set_text(text);
    },

    doColumnSelectorSelected: function (sender, args) {
        var form = this.get_form();
        var grid = form.details;
        var selectdata = form.batchNoSearchGrid.get_selectedRowData();
        var rowData = grid.get_selectedRowData();
        var rowIndex = grid.get_selectedRowIndex();
        if (selectdata && selectdata.id) {
            rowData.batchNo = selectdata.batchno
            grid.modifyRowData(rowIndex, rowData);
        }

    },
    _doSearchBatchNo: function (sender, text, rowData) {
        var form = sender.get_form();
        if (!text) {
            text = "";
        }
        var service = form.get_action().service;
        var queryData = {};
        queryData.pageSize = 10;
        queryData.pageIndex = 1;
        if (!rowData || !rowData.ptypeId) {
            return;
        }

        queryData.ptypeId = rowData.ptypeId;
        queryData.ktypeId = rowData.ktypeId;
        queryData.unitName = rowData.unitName;
        queryData.unitRate = rowData.unitRate;
        queryData.batchno = text;
        service.post("jxc/recordsheet/baseSearch/searchBatch", queryData, function (rest) {
            if (rest.code == 200) {
                var datalist = rest.data.list;
                if (datalist.length == 0) {
                    form.batchNoSearchGrid.dataBind([{
                        "batchno": "没有匹配项"
                    }]);
                    form.batchNoSearch.popupAt(sender);
                } else {
                    form.batchNoSearchGrid.dataBind(datalist);
                    form.batchNoSearch.popupAt(sender);
                    form.batchNoSearchGrid.focus();
                }
            }
        })
    },

    _doSelectorBtnClick: function (sender, eventArgs) {
        var form = this.get_form();
        // var mainItem = this._getDeliverGridSelectedItem(sender);
        // if (mainItem == null) {
        //     return;
        // }
        var formAction = form.get_action();


        var grid = sender.get_owner();
        var rowData = grid.get_selectedRowData();

        var ktypeId = rowData.ktypeId;

        var action = this.businessModel.action;

        if (!rowData || !rowData.ptypeId) {
            return;
        }
        var ptypeId = rowData.ptypeId;

        var goodsInfo = action.goodsInfo;

        if (goodsInfo == undefined) {
            var rowIndex = grid.get_selectedRowIndex();
            if (!rowData) return;
            var queryParams = {
                rowIndex: rowIndex,
                rowData: rowData,
                ktypeId: ktypeId,
                unitName: rowData.unitName,
                unitRate: rowData.unitRate
            };
            sender.set_selectorPageParams({queryParams: queryParams});
            return;
        }
        var thisInfo = goodsInfo[ptypeId];
        var units = (!thisInfo || !thisInfo.units) ? null : thisInfo.units;
        if (!units || units.length == 0) {

            sender.get_form().get_action().service.post("/sale/jarvis/common/getPtypeUnits", ptypeId, function (res) {
                if (!res.data || res.data.length === 0) return;
                var data = res.data;
                units = data;
                var items = [];
                for (var i = 0; i < units.length; i++) {
                    var item = {};
                    var unit = units[i];
                    item.unitRate = unit.unitRate;
                    item.unitName = unit.unitName;
                    item.unitId = unit.unitId;
                    items.push(item);
                }
                if (!(items.length === 0)) {
                    var ptype = {};
                    ptype.units = units;
                    action.goodsInfo[ptypeId] = ptype;
                }
            }, false);
        }

        var rowIndex = grid.get_selectedRowIndex();
        if (rowData.ptype) {
            rowData.protectDays = rowData.ptype.protectDays;
            rowData.costMode = rowData.ptype.costMode;
        } else {
            rowData.protectDays = rowData.protectDays;
            rowData.costMode = rowData.costMode;
        }

        if (!rowData) return;
        var queryParams = {
            rowIndex: rowIndex,
            rowData: rowData,
            ktypeId: ktypeId,
            unitName: rowData.unitName,
            unitRate: rowData.unitRate
        };
        sender.set_selectorPageParams({queryParams: queryParams});
    },
    _getDeliverGridSelectedItem: function (sender) {
        var form = sender.get_form();
        var grid = form.deliverMainGrid;
        var deliverBill = grid.get_selectedRowData();
        if (!deliverBill)
            return null;
        deliverBill.rowIndex = grid.get_selectedRowIndex();
        return deliverBill;
    },
    _doSelectorSelected: function (sender, args) {
        var batchNoList = args.get_form().batchNoList;
        var grid = sender.get_owner();
        var isPickSecond = (grid._idPart == "orderPtypeListGrid");
        var rowIndex = grid.get_selectedRowIndex();
        var selectRowData = grid.get_selectedRowData();

        var action = this.businessModel.action;

        if (!selectRowData || !selectRowData.ptypeId) {
            return;
        }
        var ptypeId = selectRowData.ptypeId;

        var _this = this;
        var units = [];
        var goodsInfo = action.goodsInfo;
        if (goodsInfo != undefined) {
            var thisInfo = goodsInfo[ptypeId];
            var units = (!thisInfo || !thisInfo.units) ? null : thisInfo.units;
        }
        if (!units || units.length == 0) {
            units = $jarvisUtils.postSync("/sale/jarvis/common/getPtypeUnits", ptypeId).data;
        }
        // if (!units || units.length == 0) {
        //
        //     sender.get_form().get_action().service.post("/sale/jarvis/common/getPtypeUnits", ptypeId, function (res) {
        //         if (!res.data || res.data.length === 0) return;
        //         var data = res.data;
        //          units = data;
        //         var items = [];
        //         for (var i = 0; i < units.length; i++) {
        //             var item = {};
        //             var unit = units[i];
        //             item.unitRate = unit.unitRate;
        //             item.unitName = unit.unitName;
        //             item.unitId = unit.unitId;
        //             items.push(item);
        //         }
        //         if (!(items.length === 0)) {
        //             var ptype = {};
        //             ptype.units = units;
        //             action.goodsInfo[ptypeId] = ptype;
        //         }
        //     },false);
        // }


        var rowData = {};
        var allQty = 0;
        for (var j = 0; j < batchNoList.length; j++) {
            var batchQty = batchNoList[j].unitQty ? batchNoList[j].unitQty : 1;
            allQty += $jarvismoney._doCalculateQty(batchQty);
        }
        if (selectRowData.unitQty < allQty) {
            $common.alert($language.get("qtyMustMatch", "选择的数量超过了明细数量！"));
            return;
        }
        if (allQty == 0) {
            $common.alert($language.get("qtyMustMatch", "选择的数量为0！"));
            return;
        }
        if (batchNoList.length == 0) {
            $common.alert($language.get("noBatch", "没有选中批次！"));
            return;
        }
        var oldqty = selectRowData.unitQty;
        /** 折后含税金额**/
        var disedTaxedTotal = selectRowData.disedTaxedTotal;
        /** 含税金额**/
        var total = selectRowData.total;

        /** 服务费**/
        var serviceFee = !selectRowData.serviceFee ? 0 : selectRowData.serviceFee;
        /** 优惠金额**/
        // var preferentialTotal = !selectRowData.preferentialTotal ? 0 : selectRowData.preferentialTotal;
        /** 税额**/
        var taxTotal = !selectRowData.taxTotal ? 0 : selectRowData.taxTotal;
        /** 商城扣费**/
        var mallFee = !selectRowData.mallFee ? 0 : selectRowData.mallFee;
        /** 买家运费**/
        var buyerFreightFee = !selectRowData.buyerFreightFee ? 0 : selectRowData.buyerFreightFee;
        /** 商家单品优惠**/
        var sellerPtypePreferentialTotal = !selectRowData.sellerPtypePreferentialTotal ? 0 : selectRowData.sellerPtypePreferentialTotal;
        /** 平台单品优惠**/
        var platformPtypePreferentialTotal = !selectRowData.platformPtypePreferentialTotal ? 0 : selectRowData.platformPtypePreferentialTotal;
        /** 商家整单优惠分摊**/
        var sellerPreferentialTotal = !selectRowData.sellerPreferentialTotal ? 0 : selectRowData.sellerPreferentialTotal;
        /** 平台整单优惠分摊**/
        var platformPreferentialTotal = !selectRowData.platformPreferentialTotal ? 0 : selectRowData.platformPreferentialTotal;
        /** 分销结算含税金额**/
        var distributionBalanceTaxedTotal = !selectRowData.distributionBalanceTaxedTotal ? 0 : selectRowData.distributionBalanceTaxedTotal;
        /** 分销佣金**/
        var distributionCommissionTotal = !selectRowData.distributionCommissionTotal ? 0 : selectRowData.distributionCommissionTotal;
        /** 分销佣金**/
        var comboShareScale = !selectRowData.comboShareScale ? 0 : selectRowData.comboShareScale;

        var clearHandler = this.get_form().get_action().doClearBatch;

        for (var i = 0; i < batchNoList.length; i++) {
            // if (i == 0) {
            //     rowData = Object.clone(selectRowData);//selectRowData;
            // } else {
            //     rowData = Object.clone(selectRowData);
            // }
            rowData = Object.clone(selectRowData);
            rowData.batchno = batchNoList[i].batchNo;
            rowData.costId = batchNoList[i].costId;
            rowData.serialNo = [];
            rowData.snnoStr = "";
            if (rowData.snnoCount && rowData.snnoCount > 0) {
                rowData.snnoCount = 0;
            }
            var batchQty = batchNoList[i].unitQty ? batchNoList[i].unitQty : 1;
            rowData.unitQty = $jarvismoney._doCalculateQty(batchQty);
            if (rowData.noPackingQty || rowData.noPackingQty == 0) {
                rowData.noPackingQty = $jarvismoney._doCalculateQty(rowData.unitQty - rowData.packingQty);
            }
            rowData.qty = rowData.unitQty * rowData.unitRate;
            rowData.produceDate = batchNoList[i].produceDate;
            rowData.expireDate = batchNoList[i].expireDate;
            var AfterHandler = this.get_form().get_action().doSelectorSelectedAfter;
            if (AfterHandler) {
                AfterHandler(rowData);
            }

            if (!isPickSecond) {
                if (batchNoList.length == 1 && rowData.unitQty == selectRowData.unitQty) {
                    rowData.disedTaxedTotal = rowData.disedTaxedTotal;
                    rowData.total = rowData.total;

                } else {
                    rowData.disedTaxedTotal = $jarvismoney._doCalculateTotal(rowData.disedTaxedPrice, rowData.unitQty);
                    rowData.total = $jarvismoney._doCalculateTotal(rowData.price, rowData.unitQty);

                }
                rowData.serviceFee = $jarvismoney._doCalculateFee(rowData.serviceFee, rowData.unitQty, oldqty);
                //  rowData.preferentialTotal = $jarvismoney._doCalculateFee(rowData.preferentialTotal, rowData.unitQty, oldqty);
                rowData.taxTotal = $jarvismoney._doCalculateFee(rowData.taxTotal, rowData.unitQty, oldqty);
                rowData.mallFee = $jarvismoney._doCalculateFee(rowData.mallFee, rowData.unitQty, oldqty);
                rowData.buyerFreightFee = $jarvismoney._doCalculateFee(rowData.buyerFreightFee, rowData.unitQty, oldqty);
                rowData.comboShareScale = $jarvismoney._doCalculateComboShareScale(rowData.comboShareScale, rowData.unitQty, oldqty);
                rowData.sellerPtypePreferentialTotal = $jarvismoney._doCalculateFee(rowData.sellerPtypePreferentialTotal, rowData.unitQty, oldqty);
                rowData.platformPtypePreferentialTotal = $jarvismoney._doCalculateFee(rowData.platformPtypePreferentialTotal, rowData.unitQty, oldqty);
                rowData.sellerPreferentialTotal = $jarvismoney._doCalculateFee(rowData.sellerPreferentialTotal, rowData.unitQty, oldqty);
                rowData.platformPreferentialTotal = $jarvismoney._doCalculateFee(rowData.platformPreferentialTotal, rowData.unitQty, oldqty);
                rowData.distributionBalanceTaxedTotal = $jarvismoney._doCalculateFee(rowData.distributionBalanceTaxedTotal, rowData.unitQty, oldqty);
                rowData.distributionCommissionTotal = $jarvismoney._doCalculateFee(rowData.distributionCommissionTotal, rowData.unitQty, oldqty);


                disedTaxedTotal = $jarvismoney._doCalculate(disedTaxedTotal - rowData.disedTaxedTotal);
                total = $jarvismoney._doCalculate(total - rowData.total);
                serviceFee = $jarvismoney._doCalculate(serviceFee - rowData.serviceFee);
                // preferentialTotal = $jarvismoney._doCalculate(preferentialTotal - rowData.preferentialTotal);
                taxTotal = $jarvismoney._doCalculate(taxTotal - rowData.taxTotal);
                mallFee = $jarvismoney._doCalculate(mallFee - rowData.mallFee);
                buyerFreightFee = $jarvismoney._doCalculate(buyerFreightFee - rowData.buyerFreightFee);
                sellerPtypePreferentialTotal = $jarvismoney._doCalculate(sellerPtypePreferentialTotal - rowData.sellerPtypePreferentialTotal);
                platformPtypePreferentialTotal = $jarvismoney._doCalculate(platformPtypePreferentialTotal - rowData.platformPtypePreferentialTotal);
                sellerPreferentialTotal = $jarvismoney._doCalculate(sellerPreferentialTotal - rowData.sellerPreferentialTotal);
                platformPreferentialTotal = $jarvismoney._doCalculate(platformPreferentialTotal - rowData.platformPreferentialTotal);
                distributionBalanceTaxedTotal = $jarvismoney._doCalculate(distributionBalanceTaxedTotal - rowData.distributionBalanceTaxedTotal);
                distributionCommissionTotal = $jarvismoney._doCalculate(distributionCommissionTotal - rowData.distributionCommissionTotal);
                comboShareScale = math.round(parseFloat(comboShareScale - rowData.comboShareScale), 8);

            }
            rowData.modify = true;
            rowData.unitRateName = $jarviscommon._getUnitQty(units, $jarvismoney._doCalculateQty(rowData.unitQty * rowData.unitRate));

            if (i > 0) {
                if (i == batchNoList.length - 1 && selectRowData.unitQty == allQty) {
                    if (!isPickSecond) {
                        rowData.detailId = 0;
                        rowData.disedTaxedTotal = $jarvismoney._doCalculate(rowData.disedTaxedTotal + disedTaxedTotal);
                        rowData.total = $jarvismoney._doCalculate(rowData.total + total);
                        rowData.serviceFee = $jarvismoney._doCalculate(rowData.serviceFee + serviceFee);
                        // rowData.preferentialTotal = $jarvismoney._doCalculate(rowData.preferentialTotal + preferentialTotal);
                        rowData.taxTotal = $jarvismoney._doCalculate(rowData.taxTotal + taxTotal);
                        rowData.mallFee = $jarvismoney._doCalculate(rowData.mallFee + mallFee);
                        rowData.buyerFreightFee = $jarvismoney._doCalculate(rowData.buyerFreightFee + buyerFreightFee);

                        rowData.sellerPtypePreferentialTotal = $jarvismoney._doCalculate(rowData.sellerPtypePreferentialTotal + sellerPtypePreferentialTotal);
                        rowData.platformPtypePreferentialTotal = $jarvismoney._doCalculate(rowData.platformPtypePreferentialTotal + platformPtypePreferentialTotal);
                        rowData.sellerPreferentialTotal = $jarvismoney._doCalculate(rowData.sellerPreferentialTotal + sellerPreferentialTotal);
                        rowData.platformPreferentialTotal = $jarvismoney._doCalculate(rowData.platformPreferentialTotal + platformPreferentialTotal);
                        rowData.distributionBalanceTaxedTotal = $jarvismoney._doCalculate(rowData.distributionBalanceTaxedTotal + distributionBalanceTaxedTotal);
                        rowData.distributionCommissionTotal = $jarvismoney._doCalculate(rowData.distributionCommissionTotal + distributionCommissionTotal);
                        rowData.comboShareScale = math.round(parseFloat(rowData.comboShareScale + comboShareScale), 8);
                    }
                    if (grid.get_rowCount() == rowIndex + i) {
                        grid.appendRowData(rowData);
                    } else {
                        grid.insertRowData(rowIndex + i, rowData);
                    }
                } else {
                    rowData.detailId = 0;
                    if (grid.get_rowCount() == rowIndex + i) {
                        grid.appendRowData(rowData);
                    } else {
                        grid.insertRowData(rowIndex + i, rowData);
                    }
                }
            } else {
                var gridId = this.getCurrentGridId();
                if (gridId) {
                    grid.modifyRowData(rowIndex + i, rowData);
                }
            }
            if (clearHandler) {
                clearHandler(rowData);
            }
        }

        if (selectRowData.unitQty > allQty && allQty > 0) {
            rowData = Object.clone(selectRowData);

            rowData.batchno = "";
            rowData.unitQty = $jarvismoney._doCalculateQty(selectRowData.unitQty - allQty);
            rowData.checkQty = rowData.unitQty;
            if (rowData.noPackingQty || rowData.noPackingQty == 0) {
                rowData.noPackingQty = $jarvismoney._doCalculateQty(rowData.unitQty - rowData.packingQty);
                rowData.qty = rowData.unitQty;
            }
            rowData.produceDate = "";
            rowData.expireDate = "";
            rowData.serialNo = [];
            rowData.snnoStr = "";
            if (rowData.snnoCount && rowData.snnoCount > 0) {
                rowData.snnoCount = 0;
            }
            if (!isPickSecond) {
                rowData.detailId = 0;
                rowData.disedTaxedTotal = disedTaxedTotal;
                rowData.total = total;
                rowData.serviceFee = serviceFee;
                // rowData.preferentialTotal = preferentialTotal;
                rowData.taxTotal = taxTotal;
                rowData.mallFee = mallFee;
                rowData.buyerFreightFee = buyerFreightFee;
                rowData.sellerPtypePreferentialTotal = sellerPtypePreferentialTotal;
                rowData.platformPtypePreferentialTotal = platformPtypePreferentialTotal;
                rowData.sellerPreferentialTotal = sellerPreferentialTotal;
                rowData.platformPreferentialTotal = platformPreferentialTotal;
                rowData.distributionBalanceTaxedTotal = distributionBalanceTaxedTotal;
                rowData.distributionCommissionTotal = distributionCommissionTotal;
                rowData.disedTaxedPrice = $jarvismoney._doCalculatePrice(rowData.disedTaxedTotal, rowData.unitQty);
                rowData.distributionCommissionTotal = distributionCommissionTotal;
                rowData.comboShareScale = comboShareScale;
            }
            rowData.unitRateName = $jarviscommon._getUnitQty(units, $jarvismoney._doCalculateQty(rowData.unitQty * rowData.unitRate));
            rowData.modify = true;
            if (clearHandler) {
                clearHandler(rowData);
            }
            if (grid.get_rowCount() == rowIndex + i) {
                grid.appendRowData(rowData);
            } else {
                grid.insertRowData(rowIndex + i, rowData);
            }


        }

        if (!isPickSecond && selectRowData.ptype.snEnabled) {
            $common.showOk($language.get("snTips", "批次选择成功！序列号已清空，若需要请重新选择！"));
        }
        if (isPickSecond && selectRowData.snEnabled) {
            $common.showOk($language.get("snTips", "批次选择成功！序列号已清空，若需要请重新选择！"));
        }
    },

    _doModifyRowData: function (newData) {
        var grid = this.control.get_grid();
        var rowIndex = newData.rowIndex;
        var rowData = newData.rowData;
        grid.modifyCellsValue(rowIndex, ['batchNo', 'produceDate', 'expireDate'], rowData);
    },

    _doRequestData: function (newData) {
        if (!newData) {
            return;
        }
        var form = this.get_form().get_parentForm();
        var edKtype = form.edKType ? form.edKType : form.edKtype;
        var formAction = form.get_action();
        var bussinessType = formAction.bussinesstype;
        if (bussinessType && bussinessType.toUpperCase() == 'IN') {
            return;
        }
        if (bussinessType && bussinessType.toUpperCase() == 'CHANGE') {
            var outType = this._getOutType(formAction);
            edKtype = form[outType.kType];
            if (!outType.isOut) {
                return;
            }
        }
        var ktypeId = edKtype.get_value() ? edKtype.get_value() : null;
        if (newData.length == 1) {
            newData[0].rowData.ktypeId ? ktypeId = newData[0].rowData.ktypeId : '';
        }
        // 没选仓库  不自动带出
        if (!ktypeId) {
            return;
        }
        var ptypeList = [];
        for (var i = 0; i < newData.length; i++) {
            if (!newData[i].rowData.batchenabled) {
                continue;
            }
            ptypeList.push({
                rowIndex: newData[i].rowIndex,
                ptypeId: newData[i].rowData.ptypeId ? newData[i].rowData.ptypeId : 0,
            });
        }
        if (ptypeList.length < 1) {
            return;
        }
        var request = {};
        request.ptypeList = ptypeList;
        request.ktypeId = ktypeId;
        var _this = this;
        form.get_action().service.post('jxc/recordsheet/ptype/getPtypeBatch', request, function (response) {
            if (response.code == '200' && response.data) {
                var data = response.data;
                if (data && data.length > 0) {
                    for (var i = 0; i < newData.length; i++) {
                        for (var j = 0; j < data.length; j++) {
                            if (newData[i].rowIndex == data[j].rowIndex && newData[j].rowData.ptypeId == data[j].ptypeId) {
                                newData[i].rowData.batchNo = !data[j].batchNo ? null : data[j].batchNo;
                                newData[i].rowData.produceDate = !data[j].produceDate ? null : data[j].produceDate;
                                newData[i].rowData.expireDate = !data[j].expireDate ? null : data[j].expireDate;
                                _this._doModifyRowData({
                                    rowData: newData[i].rowData,
                                    rowIndex: newData[i].rowIndex
                                });
                                break;
                            }
                        }
                    }
                }
            }
        });
    }

});
// js\biz\billgrid.js
$jarvis.register.commonContrl('billgrid', {

    change: function (resMaps, newData) {
        this.determine(resMaps, newData);
    },

    validation: function (bill) {
        //根据不同的单据类型 执行不同的验证逻辑
        var vchtype = bill.vchtype;
        // 不在这判断
        return true;
    },

    _checkNull: function (bill, erroMsg) {
        if (bill.detail.length == 0) {
            erroMsg = "请录入商品明细！";
            $recordsheet._alertMsg(erroMsg);
        }
        return true;
    },

    refresh: function () {
        this.control.dataBind(null);
    }

});
// js\biz\comboCommon.js
$jarvis.register.commonContrl('combo', {
    change: function (resMaps, newData) {
        this.determine(resMaps, newData);
    },
    valueChange: function (newData) {
        var rowData = newData.rowData;

        if (rowData.comboRowParId != 0 || rowData.combo) {
            var sender = this.control;
            var grid = sender.get_owner();
            var details = grid.saveData();
            var combodetails = [];
            var parentPtype = null;

            var gift = 1;

            if (rowData.comboRowParId != 0) {
                var parentIndex = -1;
                for (var i = 0; i < details.length; i++) {
                    var combodetail = details[i];
                    if (combodetail.comboRowParId == rowData.comboRowParId) {
                        combodetail["rowindex"] = i;
                        combodetails.push(combodetail);
                        continue;
                    }
                    if (combodetail.comboRowId == rowData.comboRowParId) {
                        parentPtype = combodetail;
                        parentIndex = i;
                    }
                }
                var allTotal = 0;
                var allDisedTaxedTotal = 0;
                var allTaxTotal = 0;
                var allDistributionBalanceTaxedTotal = 0;
                var allDistributionCommissionTotal = 0;
                for (var i = 0; i < combodetails.length; i++) {
                    var combodetail = combodetails[i];

                    if (combodetail.gift == 0)
                        gift = 0;

                    allTotal = $jarvismoney._doCalculate(allTotal + $jarvismoney._doCalculate(combodetail.total));
                    allDisedTaxedTotal = $jarvismoney._doCalculate(allDisedTaxedTotal + $jarvismoney._doCalculate(combodetail.disedTaxedTotal));
                    allTaxTotal = $jarvismoney._doCalculate(allTaxTotal + $jarvismoney._doCalculate(combodetail.taxTotal));
                    allDistributionBalanceTaxedTotal = $jarvismoney._doCalculate(allDistributionBalanceTaxedTotal + $jarvismoney._doCalculate(combodetail.distributionBalanceTaxedTotal));
                    allDistributionCommissionTotal = $jarvismoney._doCalculate(allDistributionCommissionTotal + $jarvismoney._doCalculate(combodetail.distributionCommissionTotal));
                }
                parentPtype.total = $jarvismoney._doCalculate(allTotal);
                parentPtype.price = $jarvismoney._doCalculatePrice(parentPtype.total, parentPtype.unitQty);
                parentPtype.disedTaxedTotal = $jarvismoney._doCalculate(allDisedTaxedTotal);
                parentPtype.disedTaxedPrice = $jarvismoney._doCalculatePrice(parentPtype.disedTaxedTotal, parentPtype.unitQty);
                parentPtype.sellerPtypePreferentialTotal = $sale.$jarvisdetail._getCurrencyPreferentialDiscount(parentPtype, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
                parentPtype.discount = $jarvismoney._doCurrencyDiscount(parentPtype.disedTaxedTotal, parentPtype.total);
                parentPtype.taxTotal = $jarvismoney._doCalculate(allTaxTotal);
                parentPtype.taxRate = null;
                parentPtype.distributionBalanceTaxedTotal = $jarvismoney._doCalculate(allDistributionBalanceTaxedTotal);
                parentPtype.distributionCommissionTotal = $jarvismoney._doCalculate(allDistributionCommissionTotal);
                parentPtype.distributionBalanceTaxedPrice = $jarvismoney._doCalculatePrice(parentPtype.distributionBalanceTaxedTotal, parentPtype.unitQty);
                parentPtype.gift = gift;
                parentPtype.modify = true;

                grid.modifyRowData(parentIndex, parentPtype);
            } else if
            (rowData.combo && newData.modifyColumnName == 'unitQtyColumn') {//如果有值就是修改的数量
                for (var i = 0; i < details.length; i++) {
                    var combodetail = details[i];
                    if (combodetail.comboRowParId == rowData.detailId) {
                        combodetail["rowindex"] = i;
                        combodetails.push(combodetail);
                    }
                }
                var disedTaxedTotal = rowData.disedTaxedTotal;
                var total = rowData.total;

                var taxTotal = 0;
                var scale = (rowData.unitQty / newData.oldValue);
                for (var i = 0; i < combodetails.length; i++) {

                    var combodetail = combodetails[i];
                    combodetail.unitQty = $jarvismoney._doCalculateQty(combodetail.unitQty * scale);
                    combodetail.qty = $jarvismoney._doCalculateQty(combodetail.unitQty * (combodetail.unitrate ? combodetail.unitrate : 1));

                    var modifytype = $ms.ngpConfig.Sys.jarvisDeliverBillModifyQtyType;// 修改数量是重算单价还是金额   0:重算单价 1：重算金额
                    var sendType = $sale.$jarvisdetail._getSendType(this);// 这里是根据订单的业务类型来判断的    0:直营 1：分销/代销

                    if (modifytype == "0") {
                        combodetail.disedTaxedPrice = $jarvismoney._doCalculatePrice(combodetail.disedTaxedTotal, combodetail.unitQty);
                        combodetail.price = $jarvismoney._doCalculatePrice(combodetail.total, combodetail.unitQty);
                        if (sendType == 1) {
                            combodetail.distributionBalanceTaxedTotal =
                                $sale.$jarvisdetail._getDistributionBalanceTaxedTotal(combodetail, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
                            combodetail.distributionCommissionTotal = $sale.$jarvisdetail._getDistributionCommissionTotal(combodetail, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
                            combodetail.distributionBalanceTaxedPrice = $jarvismoney._doCalculatePrice(combodetail.distributionBalanceTaxedTotal, combodetail.unitQty);

                        }
                    } else {
                        combodetail.total = $jarvismoney._doCalculate(combodetail.price * combodetail.unitQty);
                        combodetail.disedTaxedTotal = $sale.$jarvisdetail._getCurrencyDisedTaxedTotal(combodetail, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
                        combodetail.disedTaxedPrice = $jarvismoney._doCalculatePrice(combodetail.disedTaxedTotal, combodetail.unitQty);
                        combodetail.taxTotal = $sale.$jarvisdetail._getCurrencyTaxTotal(combodetail, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
                        combodetail.discount = $jarvismoney._doCurrencyDiscount(combodetail.disedTaxedTotal, combodetail.total);
                        if (sendType == 1) {
                            combodetail.distributionBalanceTaxedTotal =
                                $sale.$jarvisdetail._getDistributionBalanceTaxedTotal(combodetail, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
                            combodetail.distributionCommissionTotal = $sale.$jarvisdetail._getDistributionCommissionTotal(combodetail, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
                            combodetail.distributionBalanceTaxedPrice = $jarvismoney._doCalculatePrice(combodetail.distributionBalanceTaxedTotal, combodetail.unitQty);

                        }
                    }

                    taxTotal += combodetail.taxTotal;
                    rowData.taxTotal = $jarvismoney._doCalculate(taxTotal);
                    combodetail.modify = true;
                    grid.modifyRowData(combodetail.rowindex, combodetail);
                }
            } else if (rowData.combo && (newData.modifyColumnName == 'disedTaxedPrice'
                || newData.modifyColumnName == 'disedTaxedTotal')) {
                // （2-3-2）修改套餐的单价，先算出金额；修改套餐的金额，先算出单据
                // （2-3-3）然后将套餐价格，根据套餐信息的明细分摊比例，完成分摊；分摊除不尽，赋值到金额最大行
                for (var i = 0; i < details.length; i++) {
                    var combodetail = details[i];
                    if (combodetail.comboRowParId == rowData.detailId) {
                        combodetail["rowindex"] = i;
                        combodetails.push(combodetail);
                    }
                }
                var disedTaxedTotal = rowData.disedTaxedTotal;
                var taxTotal = 0;
                var max;
                var sendType = $sale.$jarvisdetail._getSendType(this);// 这里是根据订单的业务类型来判断的    0:直营 1：分销/代销
                // 1、折后含税单价=折后含税金额/数量
                // 2、商家单品优惠=平台金额-折后含税金额-商家整单优惠分摊
                // 3、折后不含税金额=折后含税金额/(1+税率)
                // 4、折后不含税单价=折后不含税金额/数量
                // 5、税额=折后含税金额-折后不含税金额
                // 备注 这里的3、4、5 可以合并成一条 即 税额=折后含税金额-（折后含税金额/(1+税率)）
                for (var i = 0; i < combodetails.length; i++) {
                    var combodetail = combodetails[i];
                    var scale = combodetail.comboShareScale / 100;
                    combodetail.disedTaxedTotal = $jarvismoney._doCalculate(rowData.disedTaxedTotal * scale);
                    disedTaxedTotal = disedTaxedTotal - combodetail.disedTaxedTotal;
                    if (!max || combodetail.disedTaxedTotal > max.disedTaxedTotal) {
                        max = combodetail;
                    }
                }
                for (var i = 0; i < combodetails.length; i++) {
                    var combodetail = combodetails[i];
                    if (max && max.rowindex == combodetail.rowindex && !(disedTaxedTotal == 0)) {
                        combodetail.disedTaxedTotal = $jarvismoney._doCalculate(combodetail.disedTaxedTotal + disedTaxedTotal);
                    }
                    combodetail.disedTaxedPrice = $jarvismoney._doCalculatePrice(combodetail.disedTaxedTotal, combodetail.unitQty);
                    combodetail.sellerPtypePreferentialTotal = $sale.$jarvisdetail._getSellerPtypePreferentialTotal(combodetail, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
                    combodetail.taxTotal = $sale.$jarvisdetail._getCurrencyTaxTotal(combodetail, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
                    combodetail.discount = $jarvismoney._doCurrencyDiscount(combodetail.disedTaxedTotal, combodetail.total);
                    if (sendType == 1) {
                        combodetail.distributionBalanceTaxedTotal =
                            $sale.$jarvisdetail._getDistributionBalanceTaxedTotal(combodetail, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
                        combodetail.distributionCommissionTotal = $sale.$jarvisdetail._getDistributionCommissionTotal(combodetail, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
                        combodetail.distributionBalanceTaxedPrice = $jarvismoney._doCalculatePrice(combodetail.distributionBalanceTaxedTotal, combodetail.unitQty);

                    }
                    taxTotal += combodetail.taxTotal;
                    rowData.taxTotal = $jarvismoney._doCalculate(taxTotal);
                    if (combodetail.disedTaxedTotal > 0) {
                        combodetail.gift = rowData.gift;
                    }
                    combodetail.modify = true;
                    grid.modifyRowData(combodetail.rowindex, combodetail);
                }
            } else if (rowData.combo && (newData.modifyColumnName == 'price'
                || newData.modifyColumnName == 'total')) {
                // （2-3-2）修改套餐的单价，先算出金额；修改套餐的金额，先算出单据
                // （2-3-3）然后将套餐价格，根据套餐信息的明细分摊比例，完成分摊；分摊除不尽，赋值到金额最大行
                for (var i = 0; i < details.length; i++) {
                    var combodetail = details[i];
                    if (combodetail.comboRowParId == rowData.detailId) {
                        combodetail["rowindex"] = i;
                        combodetails.push(combodetail);
                    }
                }
                var total = rowData.total;
                var taxTotal = 0;
                var max;
                var sendType = $sale.$jarvisdetail._getSendType(this);// 这里是根据订单的业务类型来判断的    0:直营 1：分销/代销
                // 1、平台单价=平台金额/数量
                // 2、折后含税金额=平台金额-商家单品优惠-商家整单优惠分摊
                // 3、折后含税单价=折后含税金额/数量
                // 4、折后不含税金额=折后含税金额/(1+税率)
                // 5、折后不含税单价= 折后不含税金额/数量
                // 6、税额=折后含税金额-折后不含税金额
                // 备注 这里的4、5、6 可以合并成一条 即 税额=折后含税金额-（折后含税金额/(1+税率)）
                for (var i = 0; i < combodetails.length; i++) {
                    var combodetail = combodetails[i];
                    var scale = combodetail.comboShareScale / 100;
                    combodetail.total = $jarvismoney._doCalculate(rowData.total * scale);
                    total = total - combodetail.total;
                    if (!max || combodetail.total > max.total) {
                        max = combodetail;
                    }
                }
                for (var i = 0; i < combodetails.length; i++) {
                    var combodetail = combodetails[i];
                    if (max && max.rowindex == combodetail.rowindex && !(total == 0)) {
                        combodetail.total = $jarvismoney._doCalculate(combodetail.total + total);
                    }
                    combodetail.price = $jarvismoney._doCalculatePrice(combodetail.total, combodetail.unitQty);
                    combodetail.disedTaxedTotal = $jarvismoney._doCalculate(
                        combodetail.total - combodetail.sellerPtypePreferentialTotal - combodetail.sellerPreferentialTotal);
                    combodetail.disedTaxedPrice = $jarvismoney._doCalculatePrice(combodetail.disedTaxedTotal, combodetail.unitQty);
                    combodetail.sellerPtypePreferentialTotal = $sale.$jarvisdetail._getSellerPtypePreferentialTotal(combodetail, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
                    combodetail.taxTotal = $sale.$jarvisdetail._getCurrencyTaxTotal(combodetail, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
                    combodetail.discount = $jarvismoney._doCurrencyDiscount(combodetail.disedTaxedTotal, combodetail.total);
                    if (sendType == 1) {
                        combodetail.distributionBalanceTaxedTotal =
                            $sale.$jarvisdetail._getDistributionBalanceTaxedTotal(combodetail, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
                        combodetail.distributionCommissionTotal = $sale.$jarvisdetail._getDistributionCommissionTotal(combodetail, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
                        combodetail.distributionBalanceTaxedPrice = $jarvismoney._doCalculatePrice(combodetail.distributionBalanceTaxedTotal, combodetail.unitQty);

                    }
                    taxTotal += combodetail.taxTotal;
                    rowData.taxTotal = $jarvismoney._doCalculate(taxTotal);
                    if (combodetail.disedTaxedTotal > 0) {
                        combodetail.gift = rowData.gift;
                    }
                    combodetail.modify = true;
                    grid.modifyRowData(combodetail.rowindex, combodetail);
                }
            } else if (rowData.combo && newData.modifyColumnName == "distributionBalanceTaxedTotal") {
                // （2-3-2）修改套餐的单价，先算出金额；修改套餐的金额，先算出单据
                // （2-3-3）然后将套餐价格，根据套餐信息的明细分摊比例，完成分摊；分摊除不尽，赋值到金额最大行
                var oldComboAllDistributionBalanceTaxedTotal = 0;//老的套餐明细的分销金额的总和
                var distributionBalanceTaxedTotal = rowData.distributionBalanceTaxedTotal;

                for (var i = 0; i < details.length; i++) {
                    var combodetail = details[i];
                    if (combodetail.comboRowParId == rowData.detailId) {
                        combodetail["rowindex"] = i;
                        combodetails.push(combodetail);
                        oldComboAllDistributionBalanceTaxedTotal = oldComboAllDistributionBalanceTaxedTotal + combodetail.distributionBalanceTaxedTotal;
                    }
                }
                var max;
                var sendType = $sale.$jarvisdetail._getSendType(this);// 这里是根据订单的业务类型来判断的    0:直营 1：分销/代销
                for (var i = 0; i < combodetails.length; i++) {
                    var combodetail = combodetails[i];
                    var scale = combodetail.comboShareScale / 100;
                    if (sendType == 1) {
                        combodetail.distributionBalanceTaxedTotal = $jarvismoney._doCalculate(rowData.distributionBalanceTaxedTotal * scale);
                        combodetail.distributionCommissionTotal = $sale.$jarvisdetail._getDistributionCommissionTotal(combodetail, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
                        combodetail.distributionBalanceTaxedPrice = $jarvismoney._doCalculatePrice(combodetail.distributionBalanceTaxedTotal, combodetail.unitQty);
                    }
                    distributionBalanceTaxedTotal = distributionBalanceTaxedTotal - combodetail.distributionBalanceTaxedTotal;
                    combodetail.modify = true;
                    if (!max || combodetail.distributionBalanceTaxedTotal > max.distributionBalanceTaxedTotal) {
                        max = combodetail;
                    }
                    grid.modifyRowData(combodetail.rowindex, combodetail);
                }
                if (max && !(distributionBalanceTaxedTotal == 0)) {
                    if (sendType == 1) {
                        max.distributionBalanceTaxedTotal = $jarvismoney._doCalculate(max.distributionBalanceTaxedTotal + distributionBalanceTaxedTotal);
                        max.distributionCommissionTotal = $sale.$jarvisdetail._getDistributionCommissionTotal(max, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
                        max.distributionBalanceTaxedPrice = $jarvismoney._doCalculatePrice(max.distributionBalanceTaxedTotal, max.unitQty);
                    }
                    grid.modifyRowData(max.rowindex, max);
                }
            }
        }

    },


});
// js\biz\common.js
$jarviscommon =
    {
        _getUnitQty: function (unitList, qty) {
            unitList = $jarviscommon._bubbleSort(unitList);
            var unitQty = "";
            if (qty == "0")
                return unitQty;
            if (unitList != null && unitList.length > 0) {
                for (var j = 0; j < unitList.length; j++) {
                    var unitName = unitList[j];
                    if (j < unitList.length - 1) {
                        var tempQty = Math.floor(qty / unitName.unitRate);
                        if (tempQty > 0) {

                            unitQty += tempQty + unitName.unitName;
                            qty = $jarvismoney._doCalculateQty(qty - (tempQty * unitName.unitRate));
                        }
                    } else {
                        if (qty > 0)
                            unitQty += (qty / unitName.unitRate) + unitName.unitName;
                    }
                }
            }
            return unitQty;
        },
        _bubbleSort: function (arr) {
            var len = arr.length;
            if (len >= 1) {
                for (var i = 0; i < len - 1; i++) {
                    for (var j = 0; j < len - 1 - i; j++) {
                        if (arr[j].unitRate < arr[j + 1].unitRate) {
                            var temp = arr[j + 1];
                            arr[j + 1] = arr[j];
                            arr[j] = temp;
                        }
                    }
                }

            }
            return arr;
        },
        /**
         * 刷新合计
         * @param that
         * @param inData
         * @param failedCallback
         * @param succeededCallback
         * @private
         */
        _refreshSummary: function (url, that, inData, succeededCallback, failedCallback) {
            var postData = Object.clone(inData);
            // 获取查询条件
            $jarvisUtils.post(url, postData, function (data) {
                if (!data) {
                    failedCallback && failedCallback($language.get("summererror", "合计失败！"));
                    return;
                }
                if (data.data) {
                    var result = {};
                    that.event('beforeSummaryDataBind', [data.data, postData.queryParams]);
                    for (var i = 0; i < data.data.name.length; i++) {
                        result[data.data.name[i]] = data.data.pageSummary[i];
                    }
                    succeededCallback(result);
                } else {
                    failedCallback($language.get("summererror", "合计失败！"));
                }
            }, function () {
                failedCallback && failedCallback($language.get("summererror", "合计失败！"));
            });
        },
        // 用于ajax 异步处理表格刷新事件
        gridFailedCallback: function (message) {
            // throw Error(message);
            var that = this;
            return function () {
                // that.grid.findPager().endLoading();
            };
        },
        _refreshGrid: function (url, that, inData, succeededCallback, failedCallback) {
            var postData = Object.clone(inData);
            succeeded = function (res) {
                that.event('beforeDataBind', [res, postData.queryParams]);
                succeededCallback(res);
            };
            $jarviscommon.refresh(url, postData, succeeded, failedCallback);

        },
        refresh: function (uri, data, call, failed) {
            if (!uri || !data || !call || !failed) {
                throw new Error(this.Langage.NotNull);
            }
            var postData = Object.clone(data);
            $jarvisUtils.post(uri, postData, function (data) {
                if (!data || !data.data || !data.data.list || data.data.list.length == 0) {
                    call && call({itemList: [], itemCount: 0})
                } else {
                    call && call({itemList: data.data.list, itemCount: data.data.total});
                }
            }, failed);
        },

        _shareTotalToDetail: function (bill, succeededCallback) {
            // 获取查询条件
            $jarvisUtils.post("sale/jarvis/deliver/detail/sharePlatformPreferentialTotal", bill, function (data) {
                if (!data) {
                    return null;
                }
                if (data.data) {
                    succeededCallback(data.data);
                } else {
                    return null;
                }
            }, function () {
                return null;
            });
        },
        _getDisedTaxedTotalColumnName: function () {
            var filedNmae = $ms.ngpConfig.Sys.sysGlobalEnabledTax ? "折后含税金额" : "折后金额";
            return filedNmae;
        },
        _getDisedTaxedPriceColumnName: function () {
            var filedNmae = $ms.ngpConfig.Sys.sysGlobalEnabledTax ? "折后含税单价" : "折后单价";
            return filedNmae;
        },

        setCookie: function (name, value) {
            var Days = 30;
            var exp = new Date();
            exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
            document.cookie = name + "=" + escape(value) + ";expires=" + exp.toGMTString();
        },

        getCookie: function (name) {
            var arr, reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
            if (arr = document.cookie.match(reg))
                return unescape(arr[2]);
            else
                return null;
        },

        pddRiskControl: function () {
            if ($jarviscommon.getCookie("pati")) {
                return;
            }
            $jarvisUtils.post("sale/jarvis/pinduoduoController/getPageCode", function (result) {
                if (result != null && result.code == "200" && result.data) {
                    $jarviscommon.setCookie("pageCode", result.data.pageCode);
                    PDD_OPEN_init({
                        code: result.data.pageCode
                    }, function () {
                        window.PDD_OPEN_getPati().then(
                            function (pati) {
                                var Days = 30;
                                var exp = new Date();
                                exp.setTime(exp.getTime() + Days * 24 * 60 * 60 * 1000);
                                document.cookie = "pati=" + escape(pati) + ";expires=" + exp.toGMTString();
                            }).catch(function (err) {
                            console.log(err);
                        })
                    });
                }
            });
        },
        _getFullNameText: function (rowData, str) {
            var text = "<div  class=\"dflex pr10 overhidden\"><span  class=\"ellipsis\">" + str + "</span>";
            if (rowData && rowData.gift) {
                text = text + '&nbsp;<span class="ExpandTipText ExpandTipZeng">赠</span>';
            }
            var ptype = null;
            if (rowData.ptype) {
                ptype = rowData.ptype;
            } else {
                ptype = rowData;
            }
            if (rowData && rowData.combo) {
                text = text + '&nbsp;<span class="ExpandTipText ExpandTipTao">套</span>';
            }
            if (rowData && ptype.batchenabled) {
                text = text + '&nbsp;<span class="ExpandTipText ExpandTipPi">批</span>';
            }
            if (rowData && ptype.snEnabled) {
                text = text + '&nbsp;<span class="ExpandTipText ExpandTipXu">序</span>';
            }
            if (rowData && rowData.propFormat) {
                text = text + '&nbsp;<span class="ExpandTipText ExpandTipShu">属</span>';
            } else if (rowData && rowData.propEnabled) {
                text = text + '&nbsp;<span class="ExpandTipText ExpandTipShu">属</span>';
            }
            if (rowData && (ptype.pcategory == "VIRTUAL" || ptype.detailPcategory == "1" || ptype.pcategory == "1")) {
                text = text + '&nbsp;<span class="ExpandTipText ExpandTipFu">服</span>';
            }
            text += "</div>";
            return text;
        }
    }

// js\biz\currencyDisedTaxedPriceColumn.js
/**
 * 折后含税单价
 * 不管税：折后单价
 * 管税且商品含税：折后含税单价
 * 管税且商品不含税：折后含税单价
 *两种情况：1、直接修改  2、通过 折后含税金额/数量 计算
 */
$jarvis.register.columnControl('currencyDisedTaxedPriceColumn', {
    controlInfo: function () {
        return {
            property: {
                "NumberType": $ms.ngpConfig.Sys.sysDigitalPrice ? 1 : 3,
                "DecimalScale": $ms.ngpConfig.Sys.sysDigitalPrice
            }
        }
    },
    change: function (resMaps, newData) {
        this.determine(resMaps, newData);
    },
    _limitData: function (newData) {
        var rowData = newData.rowData;
        var disedTaxedPrice = rowData.disedTaxedPrice ? rowData.disedTaxedPrice : 0;
        rowData.disedTaxedPrice = disedTaxedPrice;
        if (disedTaxedPrice != 0) {
            rowData.gift = 0;
        }
        // if ( rowData.price < disedTaxedPrice) {
        //     $common.showInfo("折后含税单价不能大于含税单价");
        //     rowData.disedTaxedPrice = newData.oldValue;
        //     return;
        //
        // }
    },


    currencyDisedTaxedPriceColumnChange: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            var resMaps = {};
            var ptypeInTax = true;
            if (ptypeInTax) {
                this._limitData(newData);
                // 1、折后含税金额=折后含税单价*数量
                // 2、商家单品优惠 = 平台金额-折后含税金额-商家整单优惠分摊
                // 3、折后不含税单价 = 折后含税单价/(1+税率)
                // 4、折后不含税金额 = 折后不含税单价*数量
                // 5、税额=折后含税金额-折后不含税金额
                // 备注 这里的3、4、5 可以合并成一条 即 税额=折后含税金额-（折后含税单价/(1+税率)*数量）
                resMaps[gridId + '.disedTaxedTotal'] = 'currencyDisedTaxedTotalValueChangeByQty';
                resMaps[gridId + '.sellerPtypePreferentialTotal'] = 'sellerPtypePreferentialTotalValueChange';//商家单品优惠
                resMaps[gridId + '.taxTotal'] = 'currencyTaxTotalValueChangeByPrice';//(2\3\4)
                resMaps[gridId + '.onlinePtypeName'] = 'valueChange';//最终统一处理套餐
            }
            this.determine(resMaps, newData);
        }
    },
    currencyDisedTaxedPriceValueChange: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            var ptypeInTax = true;
            if (ptypeInTax) {
                newData.rowData.disedTaxedPrice = $sale.$jarvisdetail._getCurrencyDisedTaxedPrice(newData.rowData, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
                this._limitData(newData);
            }
        }
    },
    currencyDisedTaxedPriceValueChangeByDiscount: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            var ptypeInTax = true;
            if (ptypeInTax) {
                newData.rowData.disedTaxedPrice = $sale.$jarvisdetail._getCurrencyDisedTaxedPrice(newData.rowData, $jarvisdetailmodifytype.MODIFY_BY_DISCOUNT);
                this._limitData(newData);
            }
        }
    },

});
// js\biz\currencyDisedTaxedTotalColumn.js
/**
 * 折后含税金额
 * 不管税： 折后金额
 * 管税且商品含税：折后含税金额
 * 管税且商品不含税：折后含税金额
 *两种情况：1、直接修改  2、通过 含税金额-所有商家优惠-分销优惠 计算   3、通过  折后含税单价*数量 计算
 */
$jarvis.register.columnControl('currencyDisedTaxedTotalColumn', {

    controlInfo: function () {
        return {
            property: {
                "NumberType": $ms.ngpConfig.Sys.sysDigitalTotal ? 1 : 3,
                "DecimalScale": $ms.ngpConfig.Sys.sysDigitalTotal
            }
        }
    },
    change: function (resMaps, newData) {
        this.determine(resMaps, newData);
    },

    _limitData: function (newData) {
        var rowData = newData.rowData;
        var disedTaxedTotal = rowData.disedTaxedTotal ? rowData.disedTaxedTotal : 0;
        rowData.disedTaxedTotal = disedTaxedTotal;
        if (rowData.total < disedTaxedTotal) {
            //  $common.showInfo("折后含税金额不能大于含税金额");
            //rowData.currencyDisedTaxedTotal = newData.oldValue;
            return;

        }
    },
    currencyDisedTaxedTotalColumnChange: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            var resMaps = {};
            var ptypeInTax = true;
            if (ptypeInTax) {
                this._limitData(newData);
                // 1、折后含税单价=折后含税金额/数量
                // 2、商家单品优惠=平台金额-折后含税金额-商家整单优惠分摊
                // 3、折后不含税金额=折后含税金额/(1+税率)
                // 4、折后不含税单价=折后不含税金额/数量
                // 5、税额=折后含税金额-折后不含税金额
                // 备注 这里的3、4、5 可以合并成一条 即 税额=折后含税金额-（折后含税金额/(1+税率)）
                resMaps[gridId + '.disedTaxedPrice'] = 'currencyDisedTaxedPriceValueChange';
                resMaps[gridId + '.sellerPtypePreferentialTotal'] = 'sellerPtypePreferentialTotalValueChange';//商家单品优惠
                resMaps[gridId + '.taxTotal'] = 'currencyTaxTotalValueChangeByRate';
                resMaps[gridId + '.onlinePtypeName'] = 'valueChange';//最终统一处理套餐
            }
            this.determine(resMaps, newData);
        }
    },
    currencyDisedTaxedTotalValueChange: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            var ptypeInTax = true;
            if (ptypeInTax) {
                newData.rowData.disedTaxedTotal = $sale.$jarvisdetail._getCurrencyDisedTaxedTotal(newData.rowData, $jarvisdetailmodifytype.MODIFY_BY_QTY);
                this._limitData(newData);
            }
        }
    },
    currencyDisedTaxedTotalValueChangeByQty: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            var ptypeInTax = true;
            if (ptypeInTax) {
                newData.rowData.disedTaxedTotal = $jarvismoney._doCalculate(newData.rowData.unitQty * newData.rowData.disedTaxedPrice);
                this._limitData(newData);
            }
        }
    },
    currencyDisedTaxedTotalValueChangeByDiscount: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            var ptypeInTax = true;
            if (ptypeInTax) {
                //2、折后含税金额=平台金额-商家单品优惠-商家整单优惠分摊
                newData.rowData.disedTaxedTotal = $jarvismoney._doCalculate(
                    newData.rowData.total - newData.rowData.sellerPtypePreferentialTotal - newData.rowData.sellerPreferentialTotal);
                this._limitData(newData);
            }
        }
    },

});
// js\biz\currencyPreferentialDiscountColumn.js
/**
 *
 */
$jarvis.register.columnControl('currencyPreferentialDiscountColumn', {
    change: function (resMaps, newData) {
        this.determine(resMaps, newData);
    },

    currencyPreferentialDiscountValueChange: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            var ptypeInTax = true;
            if (ptypeInTax) {
                newData.rowData.currencyPreferentialDiscount = $sale.$jarvisdetail._getCurrencyPreferentialDiscount(newData.rowData, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
                if (newData.rowData.currencyPreferentialDiscount < 0) {
                    $common.showInfo("商家单品优惠不能小于0");
                    // newData.rowData.currencyPreferentialDiscount = newData.oldValue;
                    return;

                }
            }
        }
    },
})


// js\biz\currencyPreferentialTotalColumn.js
/**
 * 优惠金额
 *
 */

$jarvis.register.columnControl('currencyPreferentialTotalColumn', {
    controlInfo: function () {
        return {
            property: {
                "NumberType": $ms.ngpConfig.Sys.sysDigitalTotal ? 1 : 3,
                "DecimalScale": $ms.ngpConfig.Sys.sysDigitalTotal
            }
        }
    },
    change: function (resMaps, newData) {
        this.determine(resMaps, newData);
    },

    _limitData: function (newData) {
        var rowData = newData.rowData;
        var preferentialTotal = rowData.preferentialTotal ? rowData.preferentialTotal : 0;
        rowData.preferentialTotal = preferentialTotal;
        if (rowData.comboRowParId != 0) {
            var sender = this.control;
            var grid = sender.get_owner();
            var details = grid.saveData();
            var combodetails = [];
            var parentPtype = null;
            var parentIndex = -1;
            var gift = 1;
            for (var i = 0; i < details.length; i++) {
                var combodetail = details[i];
                if (combodetail.comboRowParId == rowData.comboRowParId) {
                    combodetail["rowindex"] = i;
                    combodetails.push(combodetail);
                    continue;
                }
                if (combodetail.comboRowId == rowData.comboRowParId) {
                    parentPtype = combodetail;
                    parentIndex = i;
                }
            }

            var allTotal = 0;
            for (var i = 0; i < combodetails.length; i++) {
                var combodetail = combodetails[i];
                allTotal = $jarvismoney._doCalculate(allTotal + $jarvismoney._doCalculate(combodetail.preferentialTotal));
            }
            parentPtype.preferentialTotal = $jarvismoney._doCalculate(allTotal);
            parentPtype.discount = $jarvismoney._doCurrencyDiscount(parentPtype.total, parentPtype.preferentialTotal);
            parentPtype.modify = true;
            grid.modifyRowData(parentIndex, parentPtype);
        }

        if (rowData.combo) {
            var sender = this.control;
            var detail_grid = sender.get_owner();
            var items = detail_grid.saveData();
            var combodetails = [];

            for (var i = 0; i < items.length; i++) {
                var combodetail = items[i];
                if (combodetail.comboRowParId == rowData.detailId) {
                    combodetail["rowindex"] = i;
                    combodetails.push(combodetail);
                }
            }

            for (var i = 0; i < combodetails.length; i++) {
                var combodetail = Object.clone(combodetails[i]);
                if (i == combodetails.length - 1) {
                    combodetail.preferentialTotal = preferentialTotal;
                } else {
                    combodetail.preferentialTotal = $jarvismoney._doCalculate(combodetail.total - (combodetail.total * combodetail.discount));
                    preferentialTotal = $jarvismoney._doCalculate(preferentialTotal - combodetail.preferentialTotal);

                }
                combodetail.modify = true;
                detail_grid.modifyRowData(combodetail.rowindex, combodetail);
            }
        }


    },

    currencyPreferentialTotalColumnChange: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            var resMaps = {};
            var ptypeInTax = true;
            if (ptypeInTax) {
                this._limitData(newData);
                //修改优惠金额：1、折扣  2、折后含税金额 = 含税金额-优惠  3、折后含税单价：折后含税金额/数量
                //4、折后不含税单价（折后含税单价/（1+税率））
                //5、折后不含税金额（折后不含税单价*数量） 6、税额（折后含税金额-折后不含税金额）

                resMaps[gridId + '.discount'] = 'discountValueChange';
                resMaps[gridId + '.disedTaxedTotal'] = 'currencyDisedTaxedTotalValueChange';
                resMaps[gridId + '.disedTaxedPrice'] = 'currencyDisedTaxedPriceMoreValueChange';//(4\5\6\7)

            }
            this.determine(resMaps, newData);
        }


    },
    currencyPreferentialTotalValueChangeByDiscount: function (newData) {
        //优惠=含税金额-（含税金额*折扣）
        newData.rowData.preferentialTotal = $jarvismoney._doCalculate(newData.rowData.total - (newData.rowData.total * newData.rowData.discount));
        this._limitData(newData);
    },
    currencyPreferentialTotalValueChange: function (newData) {
        //优惠=（含税金额-折后含税金额）
        newData.rowData.preferentialTotal = $jarvismoney._doCalculate(newData.rowData.total - newData.rowData.disedTaxedTotal);
        this._limitData(newData);
    },


    currencyPreferentialTotalMoreValueChange: function (newData) {
        //优惠=含税金额-（含税金额*折扣）
        this.currencyPreferentialTotalValueChange(newData);
        this._limitData(newData);
        $jarvismoney._currencyDisedTotalValueChange(this, newData);
        $jarvismoney._currencyDisedPriceValueChange(this, newData);

        $jarvismoney._currencyTaxTotalValueChange(this, newData);

    },

});
// js\biz\currencyPriceColumn.js
/**
 * 折前含税单价
 *两种情况：1、直接修改  2、通过 含税金额/数量 计算
 */

$jarvis.register.columnControl('currencyPriceColumn', {
    controlInfo: function () {
        return {
            property: {
                "NumberType": $ms.ngpConfig.Sys.sysDigitalPrice ? 1 : 3,
                "DecimalScale": $ms.ngpConfig.Sys.sysDigitalPrice
            }
        }
    },
    change: function (resMaps, newData) {
        this.determine(resMaps, newData);
    },

    _limitData: function (newData) {
        var rowData = newData.rowData;
        var price = rowData.price ? rowData.price : 0;
        rowData.price = price;
        if (price != 0) {
            rowData.gift = 0;
        }

    },
    currencyPriceColumnChange: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            var resMaps = {};
            var ptypeInTax = true;
            if (ptypeInTax) {
                this._limitData(newData);
                // 1、平台金额=平台单价*数量
                // 2、折后含税单价=平台单价-（商家单品优惠+商家整单优惠分摊）/数量
                // 3、折后含税金额=折后含税单价*数量
                // 4、折后不含税单价=折后含税单价/(1+税率)
                // 5、折后不含税金额= 折后不含税单价*数量
                // 6、税额=折后含税金额-折后不含税金额
                // 备注 这里的4、5、6 可以合并成一条 即 税额=折后含税金额-（折后含税单价/(1+税率)*数量）
                resMaps[gridId + '.total'] = 'currencyTotalValueChange';
                resMaps[gridId + '.disedTaxedPrice'] = 'currencyDisedTaxedPriceValueChangeByDiscount';
                resMaps[gridId + '.disedTaxedTotal'] = 'currencyDisedTaxedTotalValueChangeByQty';
                resMaps[gridId + '.taxTotal'] = 'currencyTaxTotalValueChangeByPrice';//(4\5\6\)
                resMaps[gridId + '.onlinePtypeName'] = 'valueChange';//最终统一处理套餐
            }
            this.determine(resMaps, newData);
        }
    },

    currencyPriceValueChange: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            var ptypeInTax = true;
            if (ptypeInTax) {
                newData.rowData.price = $sale.$jarvisdetail._getCurrencyPrice(newData.rowData, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
                this._limitData(newData);
            }
        }
    },
});
// js\biz\currencyTaxTotalColumn.js
/**
 * 税额
 * 不管税： 不显示
 * 管税且商品含税：税额
 * 管税且商品不含税：税额
 *  其实，就只有一种变动情况，就是 折后含税金额-折后不含税金额  即：折后含税金额-（折后含税金额/（1+税率））
 */
$jarvis.register.columnControl('currencyTaxTotalColumn', {
    change: function (resMaps, newData) {
        this.determine(resMaps, newData);
    },
    controlInfo: function () {
        return {
            property: {
                "NumberType": $ms.ngpConfig.Sys.sysDigitalTotal ? 1 : 3,
                "DecimalScale": $ms.ngpConfig.Sys.sysDigitalTotal
            }
        }
    },
    _doDataChange: function (newData) {
        var rowData = newData.rowData;
        var taxTotal = rowData.taxTotal ? rowData.taxTotal : 0;
        rowData.taxTotal = taxTotal;
        var val = rowData.taxRate;
        rowData.disedTaxedTotal = !rowData.disedTaxedTotal ? 0 : rowData.disedTaxedTotal;
        if (!val) {
            rowData.taxRate = 0;
        }
    },
    taxTotalColumnChange: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            // 1、折后不含税金额=折后含税金额-税额
            // 2、折后不含税单价=折后不含税金额/数量
            // 3、税率=折后含税单价/折后不含税单价-1
            // 备注 这里的1、2、3、 可以合并成一条 即 税率=折后含税单价/((折后含税金额-税额)/数量)
            var resMaps = {};
            var ptypeInTax = true;
            if (ptypeInTax) {
                this._doDataChange(newData);
                resMaps[gridId + '.taxRate'] = 'taxRateValueChange';//(4\5\6\)
                resMaps[gridId + '.onlinePtypeName'] = 'valueChange';//最终统一处理套餐
            }
            this.determine(resMaps, newData);
        }
    },

    currencyTaxTotalValueChangeByRate: function (newData) {
        newData.rowData.taxTotal = $sale.$jarvisdetail._getCurrencyTaxTotal(newData.rowData, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
    },
    currencyTaxTotalValueChangeByPrice: function (newData) {
        newData.rowData.taxTotal = $sale.$jarvisdetail._getCurrencyTaxTotal(newData.rowData, $jarvisdetailmodifytype.MODIFY_BY_DISEDTAXEDPRICE);
    },
});
// js\biz\currencyTotalColumn.js
/**
 * 折前含税金额
 *两种情况：1、直接修改 currencyTotalColumnChange  2、通过 含税单价*数量 计算 currencyTotalValueChange
 */


$jarvis.register.columnControl('currencyTotalColumn', {
    controlInfo: function () {
        return {
            property: {
                "NumberType": $ms.ngpConfig.Sys.sysDigitalTotal ? 1 : 3,
                "DecimalScale": $ms.ngpConfig.Sys.sysDigitalTotal
            }
        }
    },
    change: function (resMaps, newData) {
        this.determine(resMaps, newData);
    },

    _limitData: function (newData) {
        var rowData = newData.rowData;
        var total = rowData.total ? rowData.total : 0;
        rowData.total = total;
        // if(!(rowData.currencyTotal>0)){
        //     rowData.gift = 1;
        // }
        //todo;（禁止小于商家优惠之和）
        // if ( rowData.currencyTotal < (rowData.currencyPreferentialDiscount+rowData.currencyPreferentialShare)) {
        //     $common.showInfo("含税金额不能小于商家优惠之和");
        //     rowData.currencyTotal = newData.oldValue;
        //     return;
        //
        // }
    },

    currencyTotalColumnChange: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            var resMaps = {};
            var ptypeInTax = true;
            if (ptypeInTax) {
                this._limitData(newData);

                // 1、平台单价=平台金额/数量
                // 2、折后含税金额=平台金额-商家单品优惠-商家整单优惠分摊
                // 3、折后含税单价=折后含税金额/数量
                // 4、折后不含税金额=折后含税金额/(1+税率)
                // 5、折后不含税单价= 折后不含税金额/数量
                // 6、税额=折后含税金额-折后不含税金额
                // 备注 这里的4、5、6 可以合并成一条 即 税额=折后含税金额-（折后含税金额/(1+税率)）
                resMaps[gridId + '.price'] = 'currencyPriceValueChange';
                resMaps[gridId + '.disedTaxedTotal'] = 'currencyDisedTaxedTotalValueChangeByDiscount';
                resMaps[gridId + '.disedTaxedPrice'] = 'currencyDisedTaxedPriceValueChange';
                resMaps[gridId + '.taxTotal'] = 'currencyTaxTotalValueChangeByRate';//(4\5\6\)
                resMaps[gridId + '.onlinePtypeName'] = 'valueChange';//最终统一处理套餐

            }
            this.determine(resMaps, newData);
        }
    },
    currencyTotalValueChange: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            var ptypeInTax = true;
            if (ptypeInTax) {
                //  newData.rowData.currencyTotal = $jarvismoney._doCalculate(newData.rowData.unitQty * newData.rowData.currencyPrice);
                newData.rowData.total = $sale.$jarvisdetail._getCurrencyTotal(newData.rowData, $jarvisdetailmodifytype.MODIFY_BY_QTY);
                this._limitData(newData);
            }
        }
    },

});
// js\biz\detailColumn.js
/**
 *单据主表明细列
 */

$jarvis.register.columnControl('detailColumn', {
    controlInfo: function () {
        return {
            property: {
                'cssClass': "DetailColumn",
                'headerCssClass': 'IconAbsRight',
                'Icon': 'icon14 ArrowCombo aicon-xiaojiantou'
            },
            events: {
                "OnButtonClick": this.operateDetails,
                "OnGetDisplayText": this.displayText
            }
        }
    },

    operateDetails: function (sender, eventArgs) {
        var grid = sender.get_grid();
        var bill = grid.get_selectedRowData();
        var index = grid.get_selectedRowIndex();

        var buttonValue = eventArgs.get_buttonText();
        if (buttonValue.indexOf("收起") > -1) {
            bill.expand = false; // 折叠标记
        }
        if (buttonValue.indexOf("展开") > -1) {
            bill.expand = true; // 展开标记
        }
        grid.modifyCellValue(index, sender.get_dataField(), bill[sender.get_dataField()]);
    },

    displayText: function (sender, args) {
        var details = args.get_text();
        var rowIndex = args.get_rowIndex();
        var rowData = sender.get_grid().findRowData(rowIndex);
        if (!rowData) return; // 空行

        if (details) {
            var list = JSON.parse(details);
            if (list.length > 1) {
                if (rowData.expand) { // 展开状态，默认显示，不处理
                    list.push({2: "<font class='expand up bicon-zhankai2 reverseIcon FlexCenter'>收起</font>"});
                    args.set_text(JSON.stringify(list)); // 取第一行 + 新增展开按钮
                } else {// 折叠状态，只显示第一张图和展开按钮
                    var newDetails = [list[0]];
                    newDetails.push({2: "<font class='expand bicon-zhankai2 reverseIcon FlexCenter'>展开</font>"});
                    args.set_text(JSON.stringify(newDetails)); // 取第一行 + 新增展开按钮
                }
            }
        } else {
            args.set_text('<img src="/sale/jarvis/skins/img/nullImg.png" />'); // 使用默认图片
        }
    },
});
// js\biz\detailGrid.js
/**
 * 包含明细列grid
 */
$jarvis.register.columnControl('detailGrid', {
    controlInfo: function () {
        this.popupId = '__modifyPopup';
        return {
            events: {
                "OnRowMouseHover": this.displayDetails,
                "OnTableMouseOut": this.doGridOut,
                "OnBodyMouseOut": this.doGridOut,
                "OnHeaderClick": this.doHeaderClick,
            }
        }
    },

    doHeaderClick: function (sender, args) {
        var column = args.column;
        var dataField = column.get_dataField();
        if (dataField == 'detailInfo') {
            var event = args.event;
            this.createPopup(dataField);

            var popup = this.get_form()[this.popupId];
            popup.column = column;
            popup.popupAt(event.target, {align: 'right'});
        }
    },
    createPopup: function (dataField) {
        var form = this.get_form();
        if (form[this.popupId]) return; // 创建1次

        var popup = $createControl('PopupBlock', {ID: this.popupId, CssClass: 'FlexBlock FlexCenter'}, form);
        this.fieldName = dataField;
        $createControl('Label', {Text: '展示所有明细信息', CssStyle: 'margin-top:0;'}, form, popup);
        $createControl('SwitchButton', {
            ID: "expandDetailsSwitchButton",
            Value: false,
            CheckedValue: true,
            OnChange: $createDelegate(this, this.doExpandAll)
        }, form, popup);
        popup.appendTo(document.body);
    },
    doExpandAll: function (sender) {
        var grid = this.control;
        var bills = grid.getData();
        for (var i = 0; i < bills.length; i++) {
            var bill = bills[i];
            var newDetails = JSON.parse(bill[this.fieldName]);
            var length = newDetails.length;
            if (length > 1) {
                if (sender.get_value())
                    bill.expand = true;
                else
                    bill.expand = false;
                grid.modifyCellValue(i, this.fieldName, bill[this.fieldName]);
            }
        }
    },

    displayDetails: function (sender, args) {
        var column = args.get_column();
        if (!column || column.get_business() !== 'detailColumn') {
            this.hideToolTip();
            return;
        }
        var rowData = sender.findRowData(args.get_rowIndex());
        if (!rowData || !rowData[column.get_dataField()]) {
            this.hideToolTip();
            return;
        }

        var allDetails = JSON.parse(rowData[column.get_dataField()]);
        if (!allDetails || allDetails.length < 1) {
            this.hideToolTip();
            return;
        }

        this.isShow = true; // 标记显示提示了
        var sb = new Sys.StringBuilder();
        allDetails.forEach(function (item, i) {
            for (var key in item) {
                var txt = item[key];
                sb.append("<div class='item'>" + txt + "</div>");
            }
        });

        var e = args.get_event();
        $common.showToolTip({
            x: e.clientX,
            offset: 20
        }, e.clientY, sb.toString(), 'ImgDetailPopup');
    },

    hideToolTip: function () {
        if (this.isShow) {
            this.isShow = false; // 去掉显示标记
            $common.hideToolTip();
        }
    },

    doGridOut: function () {
        this.hideToolTip();
    }
});
// js\biz\discountColumn.js
/**
 * 折扣 现为商家折扣
 * 只有一种  折后含税金额/含税金额
 */

$jarvis.register.columnControl('discountColumn', {
    change: function (resMaps, newData) {
        this.determine(resMaps, newData);
    },

    _limitData: function (newData) {
        var rowData = newData.rowData;
        var discount = 1;
        if (rowData.discount == 0) {
            discount = 0
        } else {
            discount = rowData.discount ? rowData.discount : 1;
        }
        rowData.discount = discount;

    },

    discountColumnChange: function (newData) {


        var gridId = this.getCurrentGridId();
        if (gridId) {
            var resMaps = {};
            var ptypeInTax = true;
            if (ptypeInTax) {
                this._limitData(newData);
                //修改折扣：1、重算优惠 2、折后含税金额 = 含税金额-优惠  3、折后含税单价：折后含税金额/数量
                //4、折后不含税单价（折后含税单价/（1+税率））
                //5、折后不含税金额（折后不含税单价*数量） 6、税额（折后含税金额-折后不含税金额）

                resMaps[gridId + '.preferentialTotal'] = 'currencyPreferentialTotalValueChangeByDiscount';
                resMaps[gridId + '.disedTaxedTotal'] = 'currencyDisedTaxedTotalValueChange';
                resMaps[gridId + '.disedTaxedPrice'] = 'currencyDisedTaxedPriceMoreValueChange';//(3\4\5\6\)


            }
            this.determine(resMaps, newData);
        }

    },

    discountValueChangeOld: function (newData) {
        //折扣=（含税金额-优惠金额）/含税金额
        newData.rowData.discount = $jarvismoney._doCurrencyDiscount(newData.rowData.currencyTotal, newData.rowData.currencyPreferentialTotal);
    },
    discountValueChange: function (newData) {
        //折扣=折后含税金额/含税金额
        newData.rowData.discount = $jarvismoney._doCurrencyDiscount(newData.rowData.currencyDisedTaxedTotal, newData.rowData.currencyTotal);
    },

});
// js\biz\distributionBalanceTaxedPriceColumn.js
/**
 * 分销结算含税单价
 * 不管税：折后单价
 *两种情况：1、直接修改  2、通过 折后含税金额/数量 计算
 */
$jarvis.register.columnControl('distributionBalanceTaxedPriceColumn', {
    controlInfo: function () {
        return {
            property: {
                "NumberType": $ms.ngpConfig.Sys.sysDigitalPrice ? 1 : 3,
                "DecimalScale": $ms.ngpConfig.Sys.sysDigitalPrice
            }
        }
    },
    change: function (resMaps, newData) {
        this.determine(resMaps, newData);
    },
    _limitData: function (newData) {
        var rowData = newData.rowData;
        rowData.distributionBalanceTaxedPrice = rowData.distributionBalanceTaxedPrice ? rowData.distributionBalanceTaxedPrice : 0;
    },


    distributionBalanceTaxedPriceColumnChange: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            var resMaps = {};
            var ptypeInTax = true;
            if (ptypeInTax) {
                this._limitData(newData);
                // 1、分销结算含税金额=分销结算含税单价*数量
                // 2、分佣金额=折后含税金额-分销结算金额
                resMaps[gridId + '.distributionBalanceTaxedTotal'] = 'distributionBalanceTaxedTotalValueChange';
                resMaps[gridId + '.distributionCommissionTotal'] = 'distributionCommissionTotalValueChange';
                resMaps[gridId + '.onlinePtypeName'] = 'valueChange';//最终统一处理套餐
            }
            this.determine(resMaps, newData);
        }
    },
    currencyDisedTaxedPriceValueChange: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            var ptypeInTax = true;
            if (ptypeInTax) {
                newData.rowData.disedTaxedPrice = $sale.$jarvisdetail._getCurrencyDisedTaxedPrice(newData.rowData, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
                this._limitData(newData);
            }
        }
    },
});
// js\biz\distributionBalanceTaxedTotalColumn.js
/**
 * 分销结算含税金额
 */


$jarvis.register.columnControl('distributionBalanceTaxedTotalColumn', {
    controlInfo: function () {
        return {
            property: {
                "NumberType": $ms.ngpConfig.Sys.sysDigitalTotal ? 1 : 3,
                "DecimalScale": $ms.ngpConfig.Sys.sysDigitalTotal
            }
        }
    },
    change: function (resMaps, newData) {
        this.determine(resMaps, newData);
    },

    _limitData: function (newData) {
        var rowData = newData.rowData;
        var distributionBalanceTaxedTotal = rowData.distributionBalanceTaxedTotal ? rowData.distributionBalanceTaxedTotal : 0;
        rowData.distributionBalanceTaxedTotal = distributionBalanceTaxedTotal;
        rowData.distributionBalanceTaxedPrice = $sale.$jarvisdetail._getDistributionBalanceTaxedPrice(rowData, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
    },

    distributionBalanceTaxedTotalChange: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            var resMaps = {};
            var ptypeInTax = true;
            if (ptypeInTax) {
                this._limitData(newData);
                // 1、分销结算单价=分销结算含税金额/数量
                // 分佣金额=折后含税金额-分销结算金额

                resMaps[gridId + '.distributionCommissionTotal'] = 'distributionCommissionTotalValueChange';
                resMaps[gridId + '.onlinePtypeName'] = 'valueChange';//最终统一处理套餐
            }
            this.determine(resMaps, newData);
        }
    },
    distributionBalanceTaxedTotalValueChange: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            var ptypeInTax = true;
            if (ptypeInTax) {
                // 3、分销结算含税金额=分销结算单价*数量
                newData.rowData.distributionBalanceTaxedTotal =
                    $sale.$jarvisdetail._getDistributionBalanceTaxedTotal(newData.rowData, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
                this._limitData(newData);
            }
        }
    },

});
// js\biz\distributionCommissionTotalColumn.js
/**
 * 分销佣金
 */


$jarvis.register.columnControl('distributionCommissionTotalColumn', {
    controlInfo: function () {
        return {
            property: {
                "NumberType": $ms.ngpConfig.Sys.sysDigitalTotal ? 1 : 3,
                "DecimalScale": $ms.ngpConfig.Sys.sysDigitalTotal
            }
        }
    },
    change: function (resMaps, newData) {
        this.determine(resMaps, newData);
    },

    _limitData: function (newData) {
        var rowData = newData.rowData;
        var distributionCommissionTotal = rowData.distributionCommissionTotal ? rowData.distributionCommissionTotal : 0;
        rowData.distributionCommissionTotal = distributionCommissionTotal;
    },

    distributionCommissionTotalChange: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            var resMaps = {};
            var ptypeInTax = true;
            if (ptypeInTax) {
                this._limitData(newData);
                resMaps[gridId + '.onlinePtypeName'] = 'valueChange';//最终统一处理套餐
            }
            this.determine(resMaps, newData);
        }
    },
    distributionCommissionTotalValueChange: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            var ptypeInTax = true;
            if (ptypeInTax) {
                newData.rowData.distributionCommissionTotal = $sale.$jarvisdetail._getDistributionCommissionTotal(newData.rowData, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
                this._limitData(newData);
            }
        }
    },

});
// js\biz\expireDateColumn.js
/**
 过期时间
 */
$jarvis.register.columnControl('expireDateColumn', {

    change: function (resMaps, newData) {
        this.determine(resMaps, newData);
    },

    produceDateColumnChange: function (newData) {
        var rowData = newData.rowData;
        var protectDays = rowData.ptype.protectDays;
        var produceDate = rowData.produceDate;
        if (!produceDate || !protectDays) {
            rowData.expireDate = null;
            return;
        }
        ;
        rowData.expireDate = this._addDays(produceDate, protectDays);
    },


    _addDays: function (date, days) {
        var times = date.getTime();
        times += 24 * 60 * 60 * 1000 * days;//修改后的时间戳
        var newDate = new Date(times);//转换为时间
        return newDate;
    }
});
// js\biz\giftColumn.js
/**
 * 显示赠品
 *
 */
$jarvis.register.commonContrl('giftColumn', {
    controlInfo: function () {
        return {
            events: {
                "OnGetDisplayText": this.doGetFullNameText
            }
        }
    },
    doGetFullNameText: function (sender, args) {
        var grid = sender.get_grid();
        var rowIndex = args.get_rowIndex();
        var rowData = grid.findRowData(rowIndex);
        if (!rowData) return;
        var str = args.get_text();
        if (!str) str = "";
        var text = "<span><span>" + str + "</span>";
        if (rowData && rowData.gift) {
            text = '<span class="ExpandTipText ExpandTipZeng">赠</span>&nbsp;' + text;
        }
        text += "</span>";
        args.set_text(text);
        args._hint = str; // 提示文字保持不变
    },

});

// js\biz\grid.ext.js
//绑定基础配置中的支持商品自定义字段
Sys.UI.Controls.Grid.prototype.enableDynamicColumnForJarvis = function (options, afterFunc) {
    var columns = this.get_columns();
    var self = this;
    var form = this.get_rootForm() || this.get_form();
    var action = form.get_action();
    action.printNotifyName = Object.getTypeName(action);

    options = Object.assign({append: false, mapping: {}}, options);

    function modifyColumn(index, field) {
        var column = columns[index]

        //如果 allowConfig 是 false 不做任何修改
        if (column.get_allowConfig()) {
            if (field.title && field.title != field.oldTitle) {
                column.set_caption(field.title)
            }
            column.set_allowConfig(field.hasOwnProperty('allowConfig') ? field.allowConfig : field.visible)
            column.set_visibleWithConfig(field.visible)
            column.set_readOnly(field.readOnly)
        }
    }

    function buildColumn(field) {
        var existsIndex = columns.map(function (item) {
            //expandClounm的dataField会被替换为haschild
            if (item._dataField == '__haschild') {
                return item._displayField
            }
            //如果修改了，才配置发货单明细grid
            return item._dataField
        }).indexOf(field.field)
        if (existsIndex > -1) {
            return modifyColumn(existsIndex, field)
        }

        if (!options.append) {
            return
        }

        var column = {
            caption: field.title,
            dataField: field.field,
            properties: {
                visible: false,
                //新增的自定义字段，需要在商品信息、客户/供应商的列配置中，默认显示，允许用户手工配置
                allowConfig: field.visible,
                readOnly: field.readOnly,
            },
            events: {
                // dblClick: Function.createDelegate(this, this.doDblClick),
                // change: Function.createDelegate(this, this.doChange)
            },
            refresh: true
        };
        if (options.templateUse == true) {
            column.properties.reportVisible = field.visible;
        }
        // console.log(column.caption + '--------------' + column.properties.allowConfig)

        if (field.type == 1) {
            Object.assign(column, {type: Sys.UI.Controls.TextColumn});
        } else if (field.type == 2) {
            Object.assign(column, {type: Sys.UI.Controls.NumberColumn});
        } else if (field.type == 3) {
            Object.assign(column, {type: Sys.UI.Controls.DateColumn});
        }
        // else if(field.type == 4){
        //     Object.assign(column,{type: Sys.UI.Controls.DropDownColumn},
        //         {properties: {
        //             items: [{ value: 1, text: "下拉1" }, { value: 2, text: "下拉2" }, { value: 3, text: "下拉3" }],
        //             // dropDownStyle: Sys.UI.Controls.DropDownStyle.dropDownSearch,
        //             // filterType: Sys.UI.Controls.DataFilterType.range
        //         }});
        // }
        return column;
    }

    function task(data) {
        // type 1=文本，2=数值，3=日期
        var fields = []
        data.forEach(function (item) {
            var retVal = {
                type: item.dataType,
                field: item.dataField,
                title: item.displayName,
                oldTitle: item.fieldName,
                require: item.required,
                readOnly: true,
                visible: item.visible
            };
            var mapping = options.mapping[item.dataField];
            if (mapping instanceof Array) {
                for (var i in mapping) {
                    var _retVal = Object.assign({}, retVal);
                    if (mapping[i] instanceof Object) {
                        _retVal = Object.assign({}, retVal, mapping[i]);
                    } else {
                        _retVal.field = mapping[i];
                    }
                    fields.push(_retVal)
                }
            } else {
                if (mapping) {
                    if (mapping instanceof Object) {
                        retVal = Object.assign({}, retVal, mapping)
                    } else {
                        retVal.field = mapping;
                    }
                }
                fields.push(retVal)
            }
        })

        options = Object.assign({}, options, {fields: fields});

        if (typeof options.creatingFunc == 'function') {
            options.creatingFunc(options.fields);
        }

        var result = options.fields
            .map(function (item) {
                return buildColumn(item)
            })
            .filter(function (item) {
                return item
            })
            .map(function (item) {
                return self.appendColumn(item);
            })

        if (typeof options.createdFunc == 'function') {
            options.createdFunc(options.fields, result);
        }
        if (options.templateUse == true) {
            if (options.isLast == true) {
                //处理完毕之后通知打印组件 解决 bug#72834 的问题
                $notify.emitOnce(action.printNotifyName);
            }
            if (afterFunc && typeof afterFunc == 'function') {
                afterFunc();
            }
        } else {
            //处理完毕之后通知打印组件 解决 bug#72834 的问题
            $notify.emitOnce(action.printNotifyName);
        }
    }

    var reqData = {
        businessType: 1,
        subType: options.subType,
        usedTypes: (options.usedType instanceof Array ? options.usedType.join(',') : options.usedType)
    }

    $common.ajax({
        url: "jxc/baseinfo/customFields/list?timestamp=" + new Date().getTime(),
        data: reqData,
        router: 'ngp',
        success: function (res) {
            if (form.get_isDisposed()) {
                return;
            }
            if (res.code == 200) {
                var data = []
                if (res && res.data) {
                    for (var key in res.data) {
                        var value = res.data[key];
                        if (value instanceof Array) {
                            data = data.concat(res.data[key]);
                        }
                    }
                    if (data.length) {
                        data = data.filter(function (item) {
                            var usedTypes = [];
                            usedTypes = usedTypes.concat(options.usedType)
                            return usedTypes.indexOf(item.usedType) > -1;
                        })
                        try {
                            task(data)
                        } catch (e) {
                            console.error(e)
                        }
                    }
                }
            }
        }
    })
};


Sys.UI.Controls.FlowPanel.prototype.enableDynamicFieldForJarvis = function (options) {
    var self = this;
    var form = this.get_form();

    options = Object.assign({
        LabelCssClass: 'dynamicLabel',
        // Width: 120,
        labelSuffix: ':',
        append: false,
    }, options);

    var dataFields = form.get_controls()
        .filter(function (item) {
            try {
                return item.get_idPart() || item.get_dataField()
            } catch (e) {
            }
            return false;
        })
        .map(function (item) {
            var value;
            try {
                value = item.get_dataField();
            } catch (e) {
            }
            return {id: item.get_idPart(), field: value, node: item}
        });

    function modifyField(index, field) {
        var node = dataFields[index].node;
        try {
            //只有 label 显示，才会进行修改。
            if (field.title && node.get_showLabel()) {
                node.set_label(field.title + options.labelSuffix);
                node.set_requiredLabel(field.title + options.labelSuffix);
            }
        } catch (e) {
        }

        if (field.require) {
            node.set_labelCssClass((node.get_labelCssClass() || '') + ' MustCharLeft');
        } else {
            node.remove_labelCssClass('MustCharLeft');
        }
        node.set_required(field.require);
        node.set_visible(field.visible);
    }

    function buildField(field) {
        // var existsIndex = dataFields.map(function (item) {
        //     return item.field
        // }).indexOf(field.field)

        var existsIndex = -1;
        for (var i in dataFields) {
            var _id = dataFields[i].id;
            var _field = dataFields[i].field;

            if ((_field && _field == field.field) || (_id && _id == field.field)) {
                existsIndex = i;
                break;
            }
        }
        if (existsIndex > -1) {
            return modifyField(existsIndex, field);
        }

        if (!options.append) {
            return
        }

        var LabelCssClass = options.LabelCssClass || ''
        if (field.require) {
            if (LabelCssClass) {
                LabelCssClass += " MustCharLeft"
            } else {
                LabelCssClass = "MustCharLeft"
            }
        }

        var params = {
            Label: field.title + options.labelSuffix,
            Visible: field.visible,
            Width: options.Width,
            LabelCssClass: LabelCssClass,
            Required: field.require,
            DataField: field.field,
            RequiredLabel: field.title + options.labelSuffix,
            MaxLength: 200
            // OnChange: "doChange",
        };

        if (!params.Width) {
            delete params.Width
            params.CssClass = 'FlexAuto';
        }

        if (field.type === 1) {
            return $createControl('TextEdit', params, form)
        }

        if (field.type === 2) {
            return $createControl('NumberEdit', params, form)
        }

        if (field.type === 3) {
            return $createControl('DateEdit', params, form)
        }

        // if(field.type == 4){
        //     Object.assign(params,{
        //         DataValueField: "value",
        //         DataTextField: "text",
        //         DropDownStyle: "DropDownEdit",
        //         DataSource: [{ value: 1, text: "下拉1" }, { value: 2, text: "下拉2" }, { value: 3, text: "下拉3" }],
        //     });
        //     return $createControl('DropDownEdit',params, target.get_form())
        // }
    }

    function task(data) {
        var fields = data.filter(function (item) {
            if (options && options.mapping && options.mapping[item.dataField]) {
                return !(options.mapping[item.dataField] instanceof Array);
            }
            return true;
        }).map(function (item) {
            return {
                type: item.dataType,
                field: options && options.mapping && options.mapping[item.dataField] ? options.mapping[item.dataField] : item.dataField,
                title: item.displayName,
                require: item.required,
                visible: item.visible
            }
        })
        data.filter(function (item) {
            return options && options.mapping && options.mapping[item.dataField] instanceof Array;
        }).forEach(function (item) {
            options.mapping[item.dataField].filter(function (f) {
                return f
            }).forEach(function (f) {
                fields.push({
                    type: item.dataType,
                    field: f,
                    title: item.displayName,
                    require: item.required,
                    visible: item.visible
                })
            })
        })
        options = Object.assign({}, options, {fields: fields});

        if (typeof options.creatingFunc == 'function') {
            options.creatingFunc(options.fields);
        }

        var result = options.fields
            .map(function (item) {
                return buildField(item)
            })
            .filter(function (item) {
                return item
            })
            .map(function (item) {
                return self.appendUIControl(item)
            });

        form.dataBind(form.get_dataSource())
        if (typeof options.createdFunc == 'function') {
            options.createdFunc(options.fields, result);
        }
    }

    var reqData = {
        businessType: 1,
        subType: options.subType,
        usedTypes: (options.usedType instanceof Array ? options.usedType.join(',') : options.usedType)
    }
    $common.ajax({
        url: "jxc/baseinfo/customFields/list?timestamp=" + new Date().getTime(),
        data: reqData,
        router: 'ngp',
        success: function (res) {
            if (form.get_isDisposed()) {
                return;
            }
            if (res.code == 200) {
                var data = []
                if (res && res.data) {
                    for (var key in res.data) {
                        var value = res.data[key];
                        if (value instanceof Array) {
                            data = data.concat(res.data[key]);
                        }
                    }
                    if (data.length) {
                        data = data.filter(function (item) {
                            var usedTypes = [];
                            usedTypes = usedTypes.concat(options.usedType)
                            return usedTypes.indexOf(item.usedType) > -1;
                        })
                        try {
                            task(data)
                        } catch (e) {
                            console.error(e)
                        }
                    }
                }
            }
        }
    })
};

Sys.UI.Controls.FlowPanel.prototype.enableDynamicFieldForJarvis = function (options) {
    var self = this;
    var form = this.get_form();

    options = Object.assign({
        LabelCssClass: 'dynamicLabel',
        // Width: 120,
        labelSuffix: ':',
        append: false,
    }, options);

    var dataFields = form.get_controls()
        .filter(function (item) {
            try {
                return item.get_idPart() || item.get_dataField()
            } catch (e) {
            }
            return false;
        })
        .map(function (item) {
            var value;
            try {
                value = item.get_dataField();
            } catch (e) {
            }
            return {id: item.get_idPart(), field: value, node: item}
        });

    function modifyField(index, field) {
        var node = dataFields[index].node;
        try {
            //只有 label 显示，才会进行修改。
            if (field.title && node.get_showLabel()) {
                node.set_label(field.title + options.labelSuffix);
                node.set_requiredLabel(field.title + options.labelSuffix);
            }
        } catch (e) {
        }

        if (field.require) {
            node.set_labelCssClass((node.get_labelCssClass() || '') + ' MustCharLeft');
        } else {
            node.remove_labelCssClass('MustCharLeft');
        }
        node.set_required(field.require);
        node.set_visible(field.visible);
    }

    function buildField(field) {
        // var existsIndex = dataFields.map(function (item) {
        //     return item.field
        // }).indexOf(field.field)

        var existsIndex = -1;
        for (var i in dataFields) {
            var _id = dataFields[i].id;
            var _field = dataFields[i].field;

            if ((_field && _field == field.field) || (_id && _id == field.field)) {
                existsIndex = i;
                break;
            }
        }
        if (existsIndex > -1) {
            return modifyField(existsIndex, field);
        }

        if (!options.append) {
            return
        }

        var LabelCssClass = options.LabelCssClass || ''
        if (field.require) {
            if (LabelCssClass) {
                LabelCssClass += " MustCharLeft"
            } else {
                LabelCssClass = "MustCharLeft"
            }
        }

        var params = {
            Label: field.title + options.labelSuffix,
            Visible: field.visible,
            Width: options.Width,
            LabelCssClass: LabelCssClass,
            Required: field.require,
            DataField: field.field,
            RequiredLabel: field.title + options.labelSuffix,
            MaxLength: 200
            // OnChange: "doChange",
        };

        if (!params.Width) {
            delete params.Width
            params.CssClass = 'FlexAuto';
        }

        if (field.type == 1) {
            return $createControl('TextEdit', params, form)
        }

        if (field.type == 2) {
            return $createControl('NumberEdit', params, form)
        }

        if (field.type == 3) {
            return $createControl('DateEdit', params, form)
        }

        // if(field.type == 4){
        //     Object.assign(params,{
        //         DataValueField: "value",
        //         DataTextField: "text",
        //         DropDownStyle: "DropDownEdit",
        //         DataSource: [{ value: 1, text: "下拉1" }, { value: 2, text: "下拉2" }, { value: 3, text: "下拉3" }],
        //     });
        //     return $createControl('DropDownEdit',params, target.get_form())
        // }
    }

    function task(data) {
        var fields = data.filter(function (item) {
            if (options && options.mapping && options.mapping[item.dataField]) {
                return !(options.mapping[item.dataField] instanceof Array);
            }
            return true;
        }).map(function (item) {
            return {
                type: item.dataType,
                field: options && options.mapping && options.mapping[item.dataField] ? options.mapping[item.dataField] : item.dataField,
                title: item.displayName,
                require: item.required,
                visible: item.visible
            }
        })
        data.filter(function (item) {
            return options && options.mapping && options.mapping[item.dataField] instanceof Array;
        }).forEach(function (item) {
            options.mapping[item.dataField].filter(function (f) {
                return f
            }).forEach(function (f) {
                fields.push({
                    type: item.dataType,
                    field: f,
                    title: item.displayName,
                    require: item.required,
                    visible: item.visible
                })
            })
        })
        options = Object.assign({}, options, {fields: fields});

        if (typeof options.creatingFunc == 'function') {
            options.creatingFunc(options.fields);
        }

        var result = options.fields
            .map(function (item) {
                return buildField(item)
            })
            .filter(function (item) {
                return item
            })
            .map(function (item) {
                return self.appendUIControl(item)
            });

        form.dataBind(form.get_dataSource())
        if (typeof options.createdFunc == 'function') {
            options.createdFunc(options.fields, result);
        }
    }

    var reqData = {
        businessType: 1,
        subType: options.subType,
        usedTypes: (options.usedType instanceof Array ? options.usedType.join(',') : options.usedType)
    }

    $common.ajax({
        url: "jxc/baseinfo/customFields/list?timestamp=" + new Date().getTime(),
        data: reqData,
        router: 'ngp',
        success: function (res) {
            if (form.get_isDisposed()) {
                return;
            }
            if (res.code == 200) {
                var data = []
                if (res && res.data) {
                    for (var key in res.data) {
                        var value = res.data[key];
                        if (value instanceof Array) {
                            data = data.concat(res.data[key]);
                        }
                    }
                    if (data.length) {
                        data = data.filter(function (item) {
                            var usedTypes = [];
                            usedTypes = usedTypes.concat(options.usedType)
                            return usedTypes.indexOf(item.usedType) > -1;
                        })
                        try {
                            task(data)
                        } catch (e) {
                            console.error(e)
                        }
                    }
                }
            }
        },
        error: function () {
        },
        async: true
    });


}

// js\biz\modifyDetailCommon.js
$jarvisdetailmodifytype = {
    "MODIFY_NOT": 1,//不修改
    "MODIFY_BY_DISCOUNT": 2,//根据优惠重新计算
    "MODIFY_BY_TOTAL": 3,//根据金额重新计算
    "MODIFY_BY_QTY": 4,//根据数量重新计算
    "MODIFY_BY_DISEDTAXEDPRICE": 5//折后含税单价
}
$jarvisdetail = {
    /*
    商家单品优惠
     */
    _getCurrencyPreferentialDiscount: function (rowData, type) {
        if (type == $jarvisdetailmodifytype.MODIFY_BY_TOTAL) {
            /*
                商家单品优惠 =含税金额-折后含税金额-商家整单优惠分摊
            */
            return $jarvismoney._doCalculate(
                rowData.total - rowData.disedTaxedTotal - rowData.sellerPreferentialTotal);
        } else if (type == $jarvisdetailmodifytype.MODIFY_BY_DISEDTAXEDPRICE) {
            var disedTaxedTotal = $sale.$jarvisdetail._getCurrencyDisedTaxedTotal(rowData, $jarvisdetailmodifytype.MODIFY_BY_DISEDTAXEDPRICE);
            return $jarvismoney._doCalculate(
                rowData.total - disedTaxedTotal - rowData.sellerPreferentialTotal);
        } else {
            return $jarvismoney._doCalculate(rowData.sellerPtypePreferentialTotal);
        }
    },


    /*
   含税单价
    */
    _getCurrencyPrice: function (rowData, type) {
        if (type == $jarvisdetailmodifytype.MODIFY_BY_TOTAL) {
            /*
                 含税单价=含税金额/数量
            */
            return $jarvismoney._doCalculateSinglePrise(rowData.total / rowData.unitQty);
        } else {
            return $jarvismoney._doCalculateSinglePrise(rowData.price);
        }

    },

    /*
    含税金额
     */
    _getCurrencyTotal: function (rowData, type) {
        if (type == $jarvisdetailmodifytype.MODIFY_BY_QTY) {
            /*
                 含税金额=含税单价*数量
            */
            return $jarvismoney._doCalculate(
                rowData.price * rowData.unitQty);
        } else {
            return $jarvismoney._doCalculate(rowData.total);
        }

    },

    /*
   折后含税单价
    */
    _getCurrencyDisedTaxedPrice: function (rowData, type) {
        if (type == $jarvisdetailmodifytype.MODIFY_BY_TOTAL) {
            /*
                 折后含税单价=折后含税金额/数量
            */
            return $jarvismoney._doCalculateSinglePrise(rowData.disedTaxedTotal / rowData.unitQty);
        } else if (type == $jarvisdetailmodifytype.MODIFY_BY_DISCOUNT) {
            /*
                折后含税单价=平台单价-（商家单品优惠+商家整单优惠分摊）/数量
           */
            var preferential = (rowData.sellerPtypePreferentialTotal + rowData.sellerPreferentialTotal);
            var discountPrice = preferential > 0 ? preferential / rowData.unitQty : 0;
            return $jarvismoney._doCalculateSinglePrise(rowData.price - discountPrice);
        } else {
            return $jarvismoney._doCalculateSinglePrise(rowData.disedTaxedPrice);
        }

    },
    /*
    折后含税金额
     */
    _getCurrencyDisedTaxedTotal: function (rowData, type) {
        if (type == $jarvisdetailmodifytype.MODIFY_BY_TOTAL) {
            /*
                 2、折后含税金额=平台金额-商家单品优惠-商家整单优惠分摊
            */
            return $jarvismoney._doCalculate(
                rowData.total - rowData.sellerPtypePreferentialTotal - rowData.sellerPreferentialTotal);
        } else if (type == $jarvisdetailmodifytype.MODIFY_BY_DISEDTAXEDPRICE) {
            return $jarvismoney._doCalculate(rowData.disedTaxedPrice * rowData.unitQty);
        } else {
            return $jarvismoney._doCalculate(rowData.disedTaxedTotal);
        }

    },

    /*
    税额 其实，就只有一种变动情况，就是 折后含税金额-折后不含税金额  即：折后含税金额-（折后含税金额/（1+税率））
     */
    _getCurrencyTaxTotal: function (rowData, type) {
        var val = rowData.taxRate;
        if (!val) {
            return 0;
        }
        if (type == $jarvisdetailmodifytype.MODIFY_BY_TOTAL) {

            // 税额=折后含税金额-（折后含税金额/(1+税率)）
            return $jarvismoney._doCurrencyTaxTotal(val, !rowData.disedTaxedTotal ? 0 : rowData.disedTaxedTotal);

        } else if ($jarvisdetailmodifytype.MODIFY_BY_DISEDTAXEDPRICE) {

            val = val / 100 + 1;
            var disedTaxedTotal = !rowData.disedTaxedTotal ? 0 : rowData.disedTaxedTotal;
            var disedTaxedPrice = !rowData.disedTaxedPrice ? 0 : rowData.disedTaxedPrice;
            return $jarvismoney._doCalculate(disedTaxedTotal -
                (disedTaxedPrice / val * rowData.unitQty));

        } else {
            return $jarvismoney._doCalculate(rowData.taxTotal);
        }

    },

    _getValue: function (currencyValue, newCurrencyValue) {

        return newCurrencyValue;
    },
    _getSendType: function (_this) {
        try {
            //0:直营/代销 1：分销
            var form = _this.get_form();
            var grid = form.deliverMainGrid;
            var deliverBill = grid.get_selectedRowData();
            if (deliverBill.businessType == "SaleDistribution" || deliverBill.businessType == "SaleProxy") {
                return 1;
            } else {
                return 0;
            }
        } catch (e) {
            alert("获取业务类型出错");
        }

    },
    _getDistributionCommissionTotal: function (rowData, type) {
        if (type == $jarvisdetailmodifytype.MODIFY_BY_TOTAL) {
            /*
                 分销佣金=折后含税金额-分销结算含税金额
            */
            return $jarvismoney._doCalculate(
                rowData.disedTaxedTotal - rowData.distributionBalanceTaxedTotal);
        } else {
            return $jarvismoney._doCalculate(rowData.distributionCommissionTotal);
        }

    },
    _getDistributionBalanceTaxedTotal: function (rowData, type) {
        if (type == $jarvisdetailmodifytype.MODIFY_BY_TOTAL) {
            /*
                 分销结算含税金额=分销结算单价*数量
            */
            var distributionBalanceTaxedPrice = !rowData.distributionBalanceTaxedPrice ? 0 : rowData.distributionBalanceTaxedPrice;
            return $jarvismoney._doCalculate(
                distributionBalanceTaxedPrice * rowData.unitQty);
        } else {
            return $jarvismoney._doCalculate(rowData.distributionBalanceTaxedTotal);
        }

    },
    _getDistributionBalanceTaxedPrice: function (rowData, type) {
        if (type == $jarvisdetailmodifytype.MODIFY_BY_TOTAL) {
            /*
                1、分销结算单价=分销结算含税金额/数量
            */
            var distributionBalanceTaxedTotal = !rowData.distributionBalanceTaxedTotal ? 0 : rowData.distributionBalanceTaxedTotal;
            return $jarvismoney._doCalculate(
                distributionBalanceTaxedTotal / rowData.unitQty);
        } else {
            return $jarvismoney._doCalculate(rowData.distributionBalanceTaxedPrice);
        }

    },
    _getSellerPtypePreferentialTotal: function (rowData, type) {
        if (type == $jarvisdetailmodifytype.MODIFY_BY_TOTAL) {
            //2、商家单品优惠 = 平台金额-折后含税金额-商家整单优惠分摊
            var total = !rowData.total ? 0 : rowData.total;
            var disedTaxedTotal = !rowData.disedTaxedTotal ? 0 : rowData.disedTaxedTotal;
            var sellerPreferentialTotal = !rowData.sellerPreferentialTotal ? 0 : rowData.sellerPreferentialTotal;
            return $jarvismoney._doCalculate(total - disedTaxedTotal - sellerPreferentialTotal);
        }
        if (type == $jarvisdetailmodifytype.MODIFY_BY_DISEDTAXEDPRICE) {
            //2、商家单品优惠 = 平台金额-折后含税金额-商家整单优惠分摊
            var total = !rowData.total ? 0 : rowData.total;
            var disedTaxedTotal = !rowData.disedTaxedPrice ? 0 : $jarvismoney._doCalculate(rowData.disedTaxedPrice * rowData.unitQty);
            var sellerPreferentialTotal = !rowData.sellerPreferentialTotal ? 0 : rowData.sellerPreferentialTotal;
            return $jarvismoney._doCalculate(total - disedTaxedTotal - sellerPreferentialTotal);
        } else {
            return $jarvismoney._doCalculate(rowData.sellerPtypePreferentialTotal);
        }

    },
    /*
        税率
         */
    _geTaxRate: function (rowData, type) {
        if (type == $jarvisdetailmodifytype.MODIFY_BY_TOTAL) {
            var val = rowData.taxTotal;
            if (!val) {
                return 0;
            } else {
                // 3、税率=折后含税单价/折后不含税单价-1 税率=折后含税单价/((折后含税金额-税额)/数量)-1
                return $jarvismoney._doCurrencyTaxRateByPrice(val,
                    !rowData.disedTaxedTotal ? 0 : rowData.disedTaxedTotal
                    , rowData.disedTaxedPrice, rowData.unitQty
                );
            }
        } else {
            return $jarvismoney._doCalculate(rowData.taxTotal);
        }

    },

}

// js\biz\popupArea.js
$jarvis.register.commonContrl('popupArea', {
    controlInfo: function () {
        this.CurrentCountry = 'China';
        this.popupId = '__popupArea';
        this.treeId = '__areaTree';
        this.splitChar = '/';

        var that = this;
        if (!$ms.ngpConfig.Area) {
            $ms.ngpConfig.setConfig('Area.China', 'shell/config/getChinaArea', {}, {}, function (data) {
                that.areaData = $ms.ngpConfig.Area[that.CurrentCountry];
            });
        } else {
            that.areaData = $ms.ngpConfig.Area[that.CurrentCountry];
        }

        if (this.control.get_grid) {
            return {
                property: {
                    //readOnly:true, // popupArea当在grid下面不能只读，只读了就不能触发OnEditorShow事件了
                    allowDelIcon: true /*强制只读的编辑框也显示清空按钮，但不可输入*/
                },
                events: {
                    "OnEditorShow": this.doEditorShow
                }
            }
        } else {
            return {
                property: {
                    readOnly: true,
                    allowDelIcon: true
                },
                events: {
                    "OnFocus": this.doEditorShow
                }
            }
        }
    },
    doEditorShow: function (sender) {
        this._createPopup();
        this._bindValue(sender);
        this.get_form().popupSender = sender; // 同一个界面不同控件绑定的同一个doSelected
        this._getPopup().popupAt(this._getPopupAt());
    },
    doSelected: function (sender) {
        var node = sender.get_selectedNode();
        if (node.hasChildren()) {
            return this._getPopup().repos(); // 面板内部发生改变，重新计算位置
        }
        this.hide();

        var nodes = sender.get_selectedNodes();
        var textArr = [];
        Array.forEach(nodes, function (node) {
            textArr.push(node.get_text()); // 遍历选择节点，获取节点数据或文本。或其他操作；
        });
        var newText = textArr.join(this.splitChar);

        var control = this.get_form().popupSender; // 这里一定不能用this.control, 因为popup是公用的，它关联的业务控件和control可能已经不存在了
        this.set_text(newText, control);
        if (control.doChange) {
            control.doChange();
        }
    },
    _bindValue: function (sender) {
        var text = '';
        if (sender.get_grid) {
            var grid = sender.get_grid();
            var rowData = grid.get_selectedRowData();
            if (rowData && this.control.get_dataField()) {
                text = rowData[this.control.get_dataField()] || '';
            }
            sender.set_hasPopup(true);
        } else {
            text = sender.get_text();
        }

        if (text && text[text.length - 1] == this.splitChar) {
            text = text.substr(0, text.length - 1);
        }
        var arr = [''];
        if (text) {
            arr = text.split(this.splitChar);
        } else if (this.addressText) {
            arr = this.addressText.split(this.splitChar);
        }
        var lastText = arr[arr.length - 1];

        var form = this.get_form();
        var tree = form[this.treeId];
        if (!lastText) {
            tree.clearSelect(true);
        } else {
            //tree.locateNode('name', lastText); // 选中最后一个节点，自动级联选择上级(四川省/成都市/高新区，陕西省/长治市/高新区....太多高新区了)

            for (var i = arr.length - 1; i >= 0; i--) {
                lastText = arr[i];
                var node = tree.locateNode(function (node, v) {
                    var text = node.get_text();
                    if (text == lastText) {
                        var pNode = node.get_parent();
                        var level = pNode.get_level();
                        if (level > 0 && pNode) {
                            var pText = arr[level];
                            if (pText == pNode.get_text()) {
                                return true;
                            }
                        } else {
                            return true;
                        }
                    }
                    return false;
                }, lastText);
                if (node) break;
            }
        }
    },
    _getPopup: function () {
        var form = this.get_form();
        var popup = form[this.popupId];
        return popup;
    },
    _getPopupAt: function () {
        var target = this.control;
        if (target.get_grid) {
            target = target.get_grid().get_activeCell();
        }
        return target;
    },
    _createPopup: function () {
        var form = this.get_form();
        if (form[this.popupId]) return;

        var pb = $createControl('PopupBlock', {
            ID: this.popupId,
            CssClass: 'AreaPopup'
        }, form);

        $createControl('TreeView', {
            ID: this.treeId,
            OnTreeNodeClick: this.bindControlEvent('doSelected'),
            ShowColumns: true,
            DataSource: $common.toTreeData(this.areaData, 'name', 'id', 'parent_id', 1),
        }, form, pb);
        pb.appendTo(document.body);
    },
    _bindData: function (province, city, district, street, noBindText) {
        if (province == undefined) return;
        this.addressText = this._makeAddress(province, city, district, street);
        if (!noBindText) {
            this._bindText(this.addressText);
        }
    },
    _bindText: function (address) {
        this.hide();
        this.set_text(address);
    },
    set_text: function (address, control) {
        var c = control || this.control;
        if (c.get_grid) {
            var grid = c.get_grid();
            var rowData = grid.get_selectedRowData();
            if (c.get_dataField()) {
                rowData = rowData ? rowData : {};
                if (rowData[c.get_dataField()] != address) {
                    c._changed = true;
                    c._control.set_value(address);
                    c._hideEditor();
                }
            }
        } else {
            c.set_text(address);
        }
    },
    hide: function () {
        var form = this.get_form();
        if (form[this.popupId]) {
            form[this.popupId].hide();
        }
    },
    clear: function () {
        this.addressText = '';
    },
    addressChange: function (selectedData) {
        var text = this._makeAddress(selectedData.province, selectedData.city, selectedData.district);
        this.control.set_text(text);
    },
    _makeAddress: function (province, city, district, street) {
        var arr = [];
        if (province != undefined) arr.push(province);
        if (city != undefined) arr.push(city);
        if (district != undefined) arr.push(district);
        if (street != undefined) arr.push(street);
        var address = district == undefined ? "" : arr.join(this.splitChar);
        if (address.length > 0 && address.charAt(address.length - 1) == "/") {
            address = address.substring(0, address.length - 1);
        }
        return address;
    },
    get_value: function () {
        var text = this.control.get_value();
        var arr = text.split(this.splitChar);
        var rt = {
            Province: '',
            City: '',
            District: '',
            Street: ''
        }
        switch (arr.length) {
            case 1:
                rt.Province = arr[0];
                break;
            case 2:
                rt.Province = arr[0];
                rt.City = arr[1];
                break;
            case 3:
                rt.Province = arr[0];
                rt.City = arr[1];
                rt.District = arr[2];
                break;
            case 4:
                rt.Province = arr[0];
                rt.City = arr[1];
                rt.District = arr[2];
                rt.Street = arr[3];
                break;
        }
        return rt;
    }
});
// js\biz\produceDateColumn.js
/**
 * 生产日期控件
 *
 */
$jarvis.register.columnControl('produceDateColumn', {
    change: function (resMaps, newData) {
        this.determine(resMaps, newData);
    },

    expireDateColumnChange: function (newData) {
        var rowData = newData.rowData;
        var protectDays = rowData.ptype.protectDays * -1;
        var expireDate = rowData.expireDate;
        rowData.produceDate = this._addDays(expireDate, protectDays);
    },


    _addDays: function (date, days) {
        if (!date) {
            return;
        }
        var times = date.getTime();
        times += 24 * 60 * 60 * 1000 * days;//修改后的时间戳
        var newDate = new Date(times);//转换为时间
        return newDate;
    }
})
;


// js\biz\propColumn.js
/**
 * 属性
 */
$jarvis.register.columnControl("propColumn", {
    controlInfo: function () {
        return {
            events: {
                "OnEditorShow": this.bindControlEvent('_editorShow'),
                // "OnDblClick": this.bindControlEvent('_propsDbClick'),
            }
        }
    },

    _editorShow: function (sender) {
        var form = sender.get_form();
        // 属性个数，最大为6
        var propCount = 6;
        var grid = sender.get_owner();
        var rowIndex = grid.get_selectedRowIndex();
        var rowData = grid.get_selectedRowData();
        if (!rowData) {
            return;
        }
        var rowPropData = rowData.prop;
        if (!rowPropData) {
            return;
        }
        var ptypeId = rowData.ptypeId;
        var _this = this;
        form.get_action().service.post("/jxc/recordsheet/ptype/getPtypeProps", ptypeId, function (res) {
            if (!res.data) return;
            var props = res.data;
            if (!props || props.length < 1) return;
            var id = _this.control.get_dataField() + "_propColumn_popup";
            //判断是否存在搜索面板
            if (!form[id]) {
                //不存在 创建下拉面板
                var PopupBlock = $createControl(Craba.UI.PopupBlock, {
                    ID: id,
                    OnHide: _this.bindControlEvent("_doPropPopupBlockHide"),
                }, form);
                var VPanel = $createControl(Craba.UI.VPanel, {}, form, PopupBlock);
                for (var k = 1; k <= propCount; k++) {
                    $createControl(Craba.UI.DropDownEdit, {
                        Label: "属性" + k,
                        ID: _this._getPropId(k),
                        DataValueField: "propValueId",
                        DataTextField: "propValueName",
                        DropDownStyle: 1,
                        Visible: "false"
                    }, form, VPanel);
                }
                PopupBlock.appendTo(form);
            }
            // 属性下拉框数据源赋值
            for (var i = 1; i <= propCount; i++) {
                var propEdit = form[_this._getPropId(i)];
                var isExistProp = false;
                for (var j = 0; j < props.length; j++) {
                    var prop = props[j];
                    if (prop.propIndex === i) {
                        propEdit.set_label(prop.propName);
                        propEdit.set_items(prop.propValues);
                        propEdit.set_visible(true);
                        isExistProp = true;
                        break;
                    }
                }
                if (!isExistProp) {
                    propEdit.set_visible(false);
                }
            }
            // 若当前行有属性数据，进行属性赋值
            if (rowPropData && rowPropData.length > 0) {
                for (var i = 1; i <= propCount; i++) {
                    var propEdit = form[_this._getPropId(i)];
                    if (!propEdit.get_enabled()) continue;
                    for (var j = 0; j < rowPropData.length; j++) {
                        var rowProp = rowPropData[j];
                        if (rowProp.propIndex === i) {
                            propEdit.set_value(rowProp.propValueId);
                        }
                    }
                }
            }
            _this._propIndex = rowIndex;
            form[id].popupAt(sender._columnEdit);
            var edit = form[_this._getPropId(1)];
            if (edit) {
                edit.asyncFocus();
                sender._blur();
            }
        });
    },
    _getPropId: function (index) {
        return '_propDropDownEdit' + index;
    },
    // _propsDbClick: function(sender, args){
    //     args.set_cancel(true);
    // },
    _doPropPopupBlockHide: function (sender) {
        var form = sender.get_form();
        var grid = form.details;
        var rowIndex = this._propIndex;
        var rowData = grid.findRowData(rowIndex);
        if (!rowData || rowIndex == null) return;
        var propCount = 6;
        var propValues = "";
        var propFormat = "";
        var values = [];
        for (var i = 1; i <= propCount; i++) {
            var propEdit = form[this._getPropId(i)];
            if (propEdit.get_visible() && propEdit.get_value()) {
                var item = propEdit.get_selectedItem();
                item.propIndex = i;
                values.push(item);
                propValues += propEdit.get_text() + "：";
                propFormat += propEdit.get_label() + "：";
            }
        }
        var newPropertiesName = propFormat.substr(0, propFormat.length - 1);
        var newProperties = propValues.substr(0, propValues.length - 1);
        if (rowData.propFormat !== newPropertiesName || rowData.propValues !== newProperties) {
            rowData.propValues = newProperties;
            rowData.propFormat = newPropertiesName;
            rowData.prop = values;
            this._initSku(grid, rowData, rowIndex, form.get_action().service);
        }
    },

    _initSku: function (grid, rowData, rowIndex, service) {
        var url = "/jxc/baseinfo/ptype/sku/create";
        var skuList = [];
        var sku = {
            ptypeId: rowData.ptypeId
        };
        var prop = rowData.prop;
        if (!prop || prop.length == 0) {
            $common.alert("选择网店商品出现异常!");
            return;
        }
        for (var i = 0; i < prop.length; i++) {
            var item = prop[i];
            var index = item.propIndex;
            var key = "propvalueName" + index;
            sku[key] = item.propValueName;
        }
        skuList.push(sku);
        var that = this;
        service.post(url, skuList, function (response) {
            if (response.code != 200) {
                $common.alert(response.message);
            } else {
                var data = response.data;
                if (data && data.obj && data.obj.length > 0) {
                    rowData.skuId = data.obj[0].id;
                    that._initXcode(grid, rowData, rowIndex, service);
                }
            }
        });
    },

    _initXcode: function (grid, rowData, rowIndex, service) {
        var action = this.businessModel.action;
        var url = "/sale/jarvis/common/getBaseXcode";
        var that = this;
        var parameter = {
            skuId: rowData.skuId,
            unitId: rowData.unitId,
            ptypeId: rowData.ptypeId
        };
        service.post(url, parameter, function (response) {
            if (response.code != 200) {
                $common.alert(response.message);
            } else {
                var data = response.data;
                if (data) {
                    rowData.ptype.xcode = data.xcode;
                    rowData.ptype.fullBarcode = data.fullBarcode;
                    rowData.skuId = data.skuId;
                    rowData.batchno = "";
                    rowData.expireDate = "";
                    rowData.produceDate = "";
                    rowData.snnoStr = "";
                    rowData.serialNo = [];
                    rowData.modify = true;
                    that._propIndex = null;
                    grid.modifyRowData(rowIndex, rowData);
                    if (rowData.comboDetailId && rowData.comboDetailId > 0) {
                        var comboRowIndex = grid.findParentRowIndex(rowIndex);
                        grid.deleteRow(comboRowIndex);
                        var items = grid.saveData();
                        var comboDetailId = rowData.comboDetailId;
                        for (var i = 0; i < items.length; i++) {
                            if (items[i].comboDetailId == comboDetailId) {
                                var combodetail = Object.clone(items[i]);
                                combodetail.comboDetailId = 0;
                                combodetail.modify = true;
                                grid.modifyRowData(i, combodetail);
                            }
                        }
                        $common.showInfo("套餐明细商品已修改，现在不是一个套餐了！");
                    }
                }
            }
        });

    },

    change: function (resMaps, newData) {
        this.determine(resMaps, newData);
    }
});
// js\biz\readme.js
"此目录用于存放控件上的Business属性对应的业务控件js"
// js\biz\sellerPtypePreferentialTotalColumn.js
/**
 * 商家单品优惠
 */
$jarvis.register.columnControl('sellerPtypePreferentialTotalColumn', {
    controlInfo: function () {
        return {
            property: {
                "NumberType": $ms.ngpConfig.Sys.sysDigitalTotal ? 1 : 3,
                "DecimalScale": $ms.ngpConfig.Sys.sysDigitalTotal
            }
        }
    },
    change: function (resMaps, newData) {
        this.determine(resMaps, newData);
    },

    _limitData: function (newData) {
        var rowData = newData.rowData;
        var sellerPtypePreferentialTotal = rowData.sellerPtypePreferentialTotal ? rowData.sellerPtypePreferentialTotal : 0;
        rowData.sellerPtypePreferentialTotal = sellerPtypePreferentialTotal;
    },

    sellerPtypePreferentialTotalChange: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            var resMaps = {};
            var ptypeInTax = true;
            if (ptypeInTax) {
                this._limitData(newData);
                // 1、折后含税金额=平台金额-商家单品优惠-商家整单优惠分摊
                // 2、折后含税单价=折后含税金额/数量
                // 3、折后不含税金额=折后含税金额/(1+税率)
                // 4、折后不含税单价=折后不含税金额/数量
                // 5、税额=折后含税金额-折后不含税金额
                // 备注 这里的3、4、5 可以合并成一条 即 税额=折后含税金额-（折后含税金额/(1+税率)）
                resMaps[gridId + '.disedTaxedTotal'] = 'currencyDisedTaxedTotalValueChangeByDiscount';
                resMaps[gridId + '.disedTaxedPrice'] = 'currencyDisedTaxedPriceValueChange';
                resMaps[gridId + '.taxTotal'] = 'currencyTaxTotalValueChangeByRate';//(4\5\6\)
                resMaps[gridId + '.onlinePtypeName'] = 'valueChange';//最终统一处理套餐
            }
            this.determine(resMaps, newData);
        }
    },
    sellerPtypePreferentialTotalValueChange: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            var ptypeInTax = true;
            if (ptypeInTax) {
                // 商家单品优惠 = 平台金额-折后含税金额-商家整单优惠分摊
                newData.rowData.sellerPtypePreferentialTotal = $sale.$jarvisdetail._getSellerPtypePreferentialTotal(newData.rowData, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
                this._limitData(newData);
            }
        }
    },

});
// js\biz\serialNoColumn.js
/**
 * 序列号
 */

$sale.register.columnControl('serialNoColumn', {
    controlInfo: function () {
        var url = this._getModalUrl();
        return {
            property: {
                "SelectorPage": url
            },
            events: {
                // "OnDelayChanged": this.bindControlEvent('_doDelayChanged'),
                "OnSelectorSelected": this.bindControlEvent('_doSelectorSelected'),
                "OnButtonClick": this.bindControlEvent("_doSelectorBtnClick")
            }
        }
    },

    _doSelectorSelected: function (sender, args) {
        var ptypeList = args.get_form().ptypeList;
        if (ptypeList.length < 1) {
            return;
        }
        var grid = sender.get_owner();
        var index = grid.get_selectedRowIndex();

        var selectRowData = grid.get_selectedRowData();
        var rowData = ptypeList[0].rowData;

        var rowIndex = ptypeList[0].rowIndex;
        var sncount = rowData.serialNoList ? rowData.serialNoList.length : 0;
        if (sncount > selectRowData.qty) {
            $common.alert($language.get("snNoMoreThanQty", "序列号数量不能大于商品数量！"));
            return;
        }
        var datas = grid.saveData();
        var snnos = [];
        for (var i = 0; i < datas.length; i++) {
            if (datas[i].serialNo && datas[i].serialNo.length > 0) {
                for (var j = 0; j < datas[i].serialNo.length; j++) {
                    if (datas[i].detailId != selectRowData.detailId) {
                        snnos.push(datas[i].serialNo[j].snno);
                    }

                }
            }
        }
        var hasSnno = "";
        if (snnos.length > 0) {
            for (var i = 0; i < snnos.length; i++) {
                if (rowData.serialNoList && rowData.serialNoList.length > 0) {
                    for (var j = 0; j < rowData.serialNoList.length; j++) {
                        if (snnos[i] == rowData.serialNoList[j].snno) {
                            hasSnno += rowData.serialNoList[j].snno + ",";

                        }
                    }
                }
            }
        }
        if (hasSnno != "") {
            $common.alert($language.get("hasSnNo", hasSnno.trimEndChar(",") + "序列号已存在！"));
            return;
        }
        var snnoStr = "";
        if (rowData.serialNoList.length > 0) {
            for (var i = 0; i < rowData.serialNoList.length; i++) {
                snnoStr += rowData.serialNoList[i].snno == null ? "" : rowData.serialNoList[i].snno + ",";
                rowData.serialNoList[i].snRemark = rowData.serialNoList[i].snMemo;
            }

        }
        selectRowData.snnoStr = snnoStr.trimEndChar(",");
        selectRowData.serialNo = rowData.serialNoList;
        selectRowData.snnoCount = rowData.serialNoList.length;
        if (rowData.serialNoList.length > 0) {
            selectRowData.batchno = rowData.serialNoList[0].batchNo;
            selectRowData.expireDate = rowData.serialNoList[0].expireDate;
            selectRowData.produceDate = rowData.serialNoList[0].produceDate;
            selectRowData.costId = rowData.serialNoList[0].costId;
        }
        selectRowData.modify = true;
        grid.modifyRowData(index, selectRowData);
    },

    _doSelectorBtnClick: function (sender, eventArgs) {
        var grid = sender.get_owner();
        var selectRowData = grid.get_selectedRowData();
        var rowData = {};
        var ptype = selectRowData.ptype;
        if (ptype) {
            rowData.pFullName = ptype.fullName;
            rowData.ptypeId = ptype.id;
            rowData.snenabled = ptype.snEnabled;
            rowData.batchenabled = ptype.batchenabled;
            rowData.protectDays = ptype.protectDays;
            rowData.costMode = ptype.costMode;
        } else {
            rowData.pFullName = selectRowData.fullName;
            rowData.ptypeId = selectRowData.ptypeId;
            rowData.snenabled = selectRowData.snEnabled;
            rowData.batchenabled = selectRowData.batchenabled;
            rowData.protectDays = selectRowData.protectDays;
            rowData.costMode = selectRowData.costMode;
        }
        rowData.vchcode = selectRowData.vchcode;
        rowData.detailId = selectRowData.detailId;
        var serialNos = selectRowData.serialNo;
        if (serialNos != null && serialNos.length > 0) {
            for (var i = 0; i < serialNos.length; i++) {
                serialNos[i].snMemo = serialNos[i].snRemark;
            }
        }
        rowData.serialNoList = serialNos;
        rowData.ktypeId = selectRowData.ktypeId;
        rowData.skuId = selectRowData.skuId;
        rowData.batchNo = selectRowData.batchno;
        rowData.expireDate = selectRowData.expireDate;
        rowData.produceDate = selectRowData.produceDate;
        rowData.costId = selectRowData.costId;

        this._checkSnenabled(rowData);

        var rowIndex = grid.get_selectedRowIndex();
        if (!rowData) return;
        var ktypeId = selectRowData.ktypeId;
        var ptypeList = [{
            rowIndex: rowIndex,
            rowData: rowData
        }];
        var vchcode = rowData.vchcode;
        sender.set_selectorPageParams({
            ptypeList: ptypeList,
            ktypeId: ktypeId,
            vchcode: vchcode,
            saveModel: "Save_MODIFY"
        });
    },

    //是否需要严格序列号判断
    _checkSnenabled: function (data) {
        if (!data) {
            return;
        }
        var doSerialNoColumnSnenabled = this.get_form().get_action().doSerialNoColumnSnenabled;
        if (doSerialNoColumnSnenabled) {
            var snenabled = doSerialNoColumnSnenabled();
            if (snenabled) {
                data.snenabled = 1;
            }
        }
    },

    _getModalUrl: function () {
        var form = this.get_form();
        var parentForm = form.get_parentForm();
        var url = 'jxc/recordsheet/selector/PtypeSerialNoInput.gspx?outType=true';
        // var formAction = parentForm.get_action();
        // var businessType = formAction.bussinesstype;
        // if (businessType && businessType.toUpperCase() == 'OUT') {
        //     url += '?outType=true';
        // } else if (businessType && businessType.toUpperCase() == 'CHANGE') {
        //     var type = this._getOutType(formAction);
        //     if (type.isOut) {
        //         url += '?outType=true';
        //     } else {
        //         url += '?outType=false';
        //     }
        // } else {
        //     url += '?outType=false';
        // }
        return url;
    },

    // _getOutType: function (formAction) {
    //     var inouttype = formAction.inouttype ? formAction.inouttype : '';
    //     var res = {isOut: false, kType: 'edKType'};
    //     if (!inouttype) {
    //         return res;
    //     }
    //     var gridId = this.getCurrentGridId();
    //     if (!gridId) {
    //         return res;
    //     }
    //     var inouttypeArr = inouttype.split(',');
    //     var thisGridType = '';
    //     for (var index in inouttypeArr) {
    //         var temp = inouttypeArr[index];
    //         if (temp.indexOf(gridId) != -1) {
    //             thisGridType = temp;
    //             break;
    //         }
    //     }
    //     if (!thisGridType) {
    //         return res;
    //     }
    //     var typeArr = thisGridType.split('_');
    //     var type = 'in';
    //     if (typeArr.length == 3) {
    //         res.kType = typeArr[2];
    //         type = typeArr[1];
    //     }
    //     if (type.toUpperCase() == 'OUT') {
    //         res.isOut = true;
    //         return res;
    //     }
    //     return res;
    // },

    _getID: function () {
        var id = 'snnoSearch';
        if (this.control.get_grid && this.control.get_grid()) {
            id = this.control.get_grid()._idPart + '_' + id;
        }
        return id;
    },
    _getSearchGridID: function () {
        var id = 'snnoSearchGrid';
        if (this.control.get_grid && this.control.get_grid()) {
            id = this.control.get_grid()._idPart + '_' + id;
        }
        return id;
    },

    doGetDisplayText: function (sender, args) {
        var text = args.get_text();
        if (!text) return;
        var searchText = this.control.get_text ? this.control.get_text() : (this.control._control ? this.control._control.get_text() : '');
        if (!searchText) {
            return;
        }
        text = $recordsheet.replaceSearchText(text, searchText);
        args.set_text(text);
    },
    doColumnSelectorSelected: function (sender, args) {
        var form = this.get_form();
        var gridId = this.getCurrentGridId();
        var grid = form[gridId];
        var id = this._getID();
        var searchGridId = this._getSearchGridID();
        var selectdata = form[searchGridId].get_selectedRowData();
        if (selectdata && selectdata.id) {
            this._doFillSerialNoList(grid, selectdata);
            if (form[id]) form[id].hide();
        }

    },

    // _doShowSerialNoWindow: function (newData, ktypeId, vchcode) {
    //     var sender = this.control;
    //     var grid = sender.get_owner();
    //     var newForm = new Sys.UI.Form();
    //     var url = this._getModalUrl();
    //     var _this = this;
    //     var gridId = this.getCurrentGridId();
    //     newForm.add_closed(function (res) {
    //         if (!res) {
    //             return;
    //         }
    //         var ptypeList = res.ptypeList ? res.ptypeList : [];
    //         for (var i = 0; i < ptypeList.length; i++) {
    //             var rowIndex = ptypeList[i].rowIndex;
    //             var rowData = ptypeList[i].rowData;
    //             rowData.batchNo = rowData.serialNoList ? (rowData.serialNoList.length > 0 ? rowData.serialNoList[0].batchNo : null) : null;
    //             if (gridId) {
    //                 var resMaps = {};
    //                 resMaps[gridId + '.unitQty'] = 'serialNoColumnChange';
    //                 _this.determine(resMaps, {rowIndex: rowIndex, rowData: rowData});
    //             }
    //         }
    //     });
    //     newForm.showModal(url, {ptypeList: newData, ktypeId: ktypeId, vchcode: vchcode});
    // },

    /**
     * 将序列号填充到商品信息
     * @param rowData
     * @param serialNo
     * @private
     */
    _doFillSerialNoList: function (grid, serialNo) {
        var gridId = this.getCurrentGridId();
        var rowData = grid.get_selectedRowData();
        var rowIndex = grid.get_selectedRowIndex();
        if (!rowData.serialNoList) {
            rowData.serialNoList = [serialNo];
        } else {
            var serialNoList = rowData.serialNoList;
            var exist = false;
            for (var i = 0; i < serialNoList.length; i++) {
                if (serialNo.snNo == serialNoList[i].snNo) {
                    exist = true;
                    break;
                }
            }
            if (exist) {
                $common.alert('本单中已录入序列号（' + serialNo.snNo + '），请勿重复录入');
                return;
            }
            rowData.serialNoList.push(serialNo);
        }
        if (gridId) {
            var resMaps = {};
            resMaps[gridId + '.unitQty'] = 'serialNoColumnChange';
            this.determine(resMaps, {rowIndex: rowIndex, rowData: rowData});
        }
    }
});
// js\biz\serialNoColumnButton.js
/**
 * 序列号
 */

$jarvis.register.columnControl('serialNoColumnButton', {
    controlInfo: function () {
        return {
            events: {
                "OnButtonClick": this.bindControlEvent("_doSelectorBtnClick")
            }
        }
    },

    _doSelectorBtnClick: function (sender, eventArgs) {
        var grid = sender.get_owner();
        // if(grid.get_readOnly()){
        //     return;
        // }
        var rowData = grid.get_selectedRowData();
        if (rowData && !rowData.ptype.snEnabled) {
            $common.showTips('商品未启用序列号管理，不支持录入序列号');
            return;
        }
        if (sender.get_tag()) {
            var column = grid.findColumn(sender.get_tag());
            if (!column) {
                return;
            }
            var cell = grid._getSelectedRowCell(column._index);
            if (!cell) return;
            //临时处理
            if (!column.get_control()) {
                column.set_visible(true);
                column._showEditor(cell);
                column.set_visible(false);
            }
            column.get_control().doButtonClick();
            return;
        }
    }
});
// js\biz\serviceFeeColumn.js
/**
 * 税额
 * 不管税： 不显示
 * 管税且商品含税：税额
 * 管税且商品不含税：税额
 *
 */
$jarvis.register.columnControl('serviceFeeColumn', {
    change: function (resMaps, newData) {
        this.determine(resMaps, newData);
    },
    _doDataChange: function (newData) {
        var rowData = newData.rowData;
        if (!rowData.serviceFee) {
            rowData.serviceFee = 0;
        }
        if (rowData.comboDetailId != 0) {
            this._doDataChangeSetCombo(rowData);
        }

    },
    _doDataChangeSetCombo: function (rowData) {
        var sender = this.control;
        var grid = sender.get_owner();
        var details = grid.saveData();
        var combodetails = [];
        var parentPtype = null;
        var parentIndex = -1;
        var gift = 1;
        for (var i = 0; i < details.length; i++) {
            var combodetail = details[i];
            if (combodetail.comboRowParId == rowData.comboRowParId) {
                combodetail["rowindex"] = i;
                combodetails.push(combodetail);
                continue;
            }
            if (combodetail.comboRowId == rowData.comboRowParId) {
                parentPtype = combodetail;
                parentIndex = i;
            }
        }
        var serviceFee = 0;

        for (var i = 0; i < combodetails.length; i++) {
            var combodetail = combodetails[i];
            serviceFee = $jarvismoney._doCalculate(serviceFee + $jarvismoney._doCalculate(combodetail.serviceFee));
            if (combodetail.gift == 0)
                gift = 0;
        }
        parentPtype.serviceFee = serviceFee;
        parentPtype.modify = true;

        grid.modifyRowData(parentIndex, parentPtype);
    },
    serviceFeeColumnChange: function (newData) {
        this._doDataChange(newData);
    },
});
// js\biz\taxRateColumn.js
/**
 * 税率
 * 不管税： 不显示
 * 管税且商品含税：税率
 * 管税且商品不含税：税率
 *
 */
$jarvis.register.columnControl('taxRateColumn', {
    change: function (resMaps, newData) {
        this.determine(resMaps, newData);
    },
    _doDataChange: function (newData) {
        var rowData = newData.rowData;
        var val = rowData.taxRate;
        rowData.disedTaxedTotal = !rowData.disedTaxedTotal ? 0 : rowData.disedTaxedTotal;
        if (!val) {
            rowData.taxRate = 0;
        }
    },
    taxRateColumnChange: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            // 1、折后不含税单价=折后含税单价/(1+税率)
            // 2、折后不含税金额=折后不含税单价*数量
            // 3、税额 = 折后含税金额-折后不含税金额
            // 备注 这里的1、2、3、 可以合并成一条 即 税额=折后含税金额-（折后含税单价/(1+税率)*数量）
            var resMaps = {};
            var ptypeInTax = true;
            if (ptypeInTax) {
                this._doDataChange(newData);
                resMaps[gridId + '.taxTotal'] = 'currencyTaxTotalValueChangeByPrice';//(4\5\6\)
                resMaps[gridId + '.onlinePtypeName'] = 'valueChange';//最终统一处理套餐
            }
            this.determine(resMaps, newData);
        }
    },
    taxRateValueChange: function (newData) {
        newData.rowData.taxRate = $sale.$jarvisdetail._geTaxRate(newData.rowData, $jarvisdetailmodifytype.MODIFY_BY_TOTAL);
    },
});
// js\biz\unitNameColumn.js
/**
 *单位
 */
$jarvis.register.columnControl('unitNameColumn', {
    controlInfo: function () {
        return {
            events: {
                OnEditorShow: this._editorShow
            }
        }
    },

    _editorShow: function (sender) {
        var form = sender.get_form();
        var action = this.businessModel.action;
        var grid = sender.get_owner();
        var rowIndex = grid.get_selectedRowIndex();
        var rowData = grid.get_selectedRowData();
        if (!rowData || !rowData.ptypeId) {
            return;
        }
        var ptypeId = rowData.ptypeId;

        var _this = this;
        var goodsInfo = action.goodsInfo;


        var thisInfo = goodsInfo[ptypeId];
        var units = (!thisInfo || !thisInfo.units) ? null : thisInfo.units;
        if (!units || units.length == 0) {

            form.get_action().service.post("/sale/jarvis/common/getPtypeUnits", ptypeId, function (res) {
                if (!res.data || res.data.length === 0) return;
                var data = res.data;
                var units = data;
                var items = [];
                for (var i = 0; i < units.length; i++) {
                    var item = {};
                    var unit = units[i];
                    item.unitRate = unit.unitRate;
                    item.unitName = unit.unitName;
                    item.unitId = unit.unitId;
                    items.push(item);
                }
                if (items.length === 0) {
                    return;
                }

                _this.control.set_items(units);
                var ptype = {};
                ptype.units = units;
                action.goodsInfo[ptypeId] = ptype;
            });
        } else {
            var units = units;
            this.control.set_items(units);
        }

    },

    change: function (resMaps, newData) {
        this.determine(resMaps, newData);
    },

    // 单位数据源
    unitNameColumnChange: function (newData) {
        if (!newData || newData.length < 1) return;
        var grid = this.control.get_owner();
        var gridId = this.getCurrentGridId();
        var service = this.get_service();
        var action = this.businessModel.action;
        var goodsInfo = action.goodsInfo;
        if (!goodsInfo) goodsInfo = {};
        var ptypeIdList = [];
        for (var i = 0; i < newData.length; i++) {
            var row = newData[i];
            var rowData = row.rowData;
            if (!rowData || !rowData.ptypeId) {
                continue;
            }
            var ptypeId = rowData.ptypeId;
            if (!goodsInfo[ptypeId]) goodsInfo[ptypeId] = {};
            var thisInfo = goodsInfo[ptypeId];
            if (thisInfo.units && thisInfo.units.length > 0) {
                continue;
            }
            ptypeIdList.push(rowData.ptypeId);
        }
        var that = this;
        if (ptypeIdList.length == 0) {
            // 单位关系、辅助数量
            var resMaps = {};
            resMaps[gridId + '.unitRelation'] = 'unitRelationColumnListChange';
            resMaps[gridId + '.uRateQty'] = 'uRateQtyColumnListChange';
            this.determine(resMaps, newData);
            setTimeout(function () {
                that._doModifyRowData(grid, newData);
            }, 100);
            return;
        }
        service.post("/jxc/baseinfo/ptype/unit/ptypeiddic", ptypeIdList, function (res) {
            if (!res.data) return;
            var data = res.data;
            for (var key in data) {
                goodsInfo[key].units = data[key];
            }
            // 单位关系、辅助数量
            var resMaps = {};
            resMaps[gridId + '.unitRelation'] = 'unitRelationColumnListChange';
            resMaps[gridId + '.uRateQty'] = 'uRateQtyColumnListChange';
            that.determine(resMaps, newData);
            setTimeout(function () {
                that._doModifyRowData(grid, newData);
            }, 100);
        });
    },

    _doModifyRowData: function (grid, newData) {
        for (var i = 0; i < newData.length; i++) {
            var rowIndex = newData[i].rowIndex;
            var rowData = newData[i].rowData;
            grid.modifyCellsValue(rowIndex, ['unitRelation', 'uRateQty'], rowData);
        }
    },

    // 单位数据值改变
    unitNameColumnValueChange: function (newData) {
        var grid = newData.grid;
        var rowIndex = newData.rowIndex;
        var rowData = newData.rowData;
        if (!rowData) return;
        var unitName = rowData.unitName;
        var oldUnitRate = rowData.unitRate;
        var oldStockQty = rowData.stockQty;
        var baseStockQty = oldStockQty && oldUnitRate ? parseFloat(oldStockQty * oldUnitRate) : 0;
        var unit = {};
        var items = this.control.get_items();
        for (var i = 0; i < items.length; i++) {
            var item = items[i];
            if (unitName == item.unitName) {
                unit = item;
                break;
            }
        }
        rowData.unitId = unit.unitId;
        rowData.unitRate = unit.unitRate;
        rowData.unit = unit;
        if (rowData.unitQty) {
            rowData.qty = $jarvismoney._doCalculateQty(rowData.unitQty * unit.unitRate);
        }
        // 切换单位联动刷新sku信息
        var gridId = this.getCurrentGridId();
        var resMaps = {};
        resMaps[gridId + '.propValues'] = 'skuColumnChange';    //sku
        resMaps[gridId + '.uRateQty'] = 'uRateQtyColumnChange';    //辅助数量
        this.determine(resMaps, {rowIndex: rowIndex, rowData: rowData});
    }
});
// js\biz\unitQtyColumn.js
/**
 * 数量
 *只有一种方式：直接修改
 */
$jarvis.register.columnControl('unitQtyColumn', {
    change: function (resMaps, newData) {
        this.determine(resMaps, newData);
    },

    _doDataChange: function (newData) {
        var temp = newData.rowData;
        var sender = this.control;
        var detail_grid = sender.get_owner();

        if (!temp.unitQty || !$jarvismoney._doCalculateQty(temp.unitQty)) {
            $common.showInfo("数量不能小于或等于0");
            temp.unitQty = newData.oldValue;
            return;
        }
        if (temp.unitQty > 99999999999999) {
            $common.showInfo("数量不能大于99999999999999");
            temp.unitQty = newData.oldValue;
            return;
        }
        temp.qty = $jarvismoney._doCalculateQty(temp.unitQty * (temp.unitRate ? temp.unitRate : 1));
        temp.modify = true;
        detail_grid.modifyRowData(newData.rowIndex, temp);

    },

    //新使用的
    unitQtyColumnChange: function (newData) {
        var gridId = this.getCurrentGridId();
        if (gridId) {
            var resMaps = {};
            var ptypeInTax = true;
            if (ptypeInTax) {
                this._doDataChange(newData);
                var modifytype = $ms.ngpConfig.Sys.jarvisDeliverBillModifyQtyType;// 修改数量是重算单价还是金额   0:重算单价 1：重算金额
                var sendType = $sale.$jarvisdetail._getSendType(this);// 这里是根据订单的业务类型来判断的    0:直营 1：分销/代销
                if (modifytype == "0") {
                    // 1、平台单价=平台金额/数量
                    // 2、折后含税单价=折后含税金额/数量
                    // 3、分销结算含税金额=分销结算单价*数量
                    // 4、分销佣金=折后含税金额-分销结算含税金额
                    resMaps[gridId + '.currencyPrice'] = 'currencyPriceValueChange';
                    resMaps[gridId + '.disedTaxedPrice'] = 'currencyDisedTaxedPriceValueChange';
                    if (sendType == 1) {
                        resMaps[gridId + '.distributionBalanceTaxedTotal'] = 'distributionBalanceTaxedTotalValueChange';
                        resMaps[gridId + '.distributionCommissionTotal'] = 'distributionCommissionTotalValueChange';
                    }


                } else {
                    // 1、平台金额=平台单价*数量
                    // 2、折后含税单价=平台单价-（商家单品优惠+商家整单优惠分摊）/数量
                    // 3、折后含税金额=折后含税单价*数量
                    // 4、折后不含税单价=折后含税单价/(1+税率)
                    // 5、折后不含税金额= 折后不含税单价*数量
                    // 6、税额=折后含税金额-折后不含税金额
                    // 7、分销结算含税金额 = 分销结算单价*数量
                    // 8、分佣金额=折后含税金额-分销结算含税金额
                    // 备注 这里的4、5、6 可以合并成一条 即 税额=折后含税金额-（折后含税单价/(1+税率)*数量）
                    resMaps[gridId + '.total'] = 'currencyTotalValueChange';
                    resMaps[gridId + '.disedTaxedPrice'] = 'currencyDisedTaxedPriceValueChangeByDiscount';
                    resMaps[gridId + '.disedTaxedTotal'] = 'currencyDisedTaxedTotalValueChangeByQty';
                    if (sendType == 1) {
                        resMaps[gridId + '.distributionBalanceTaxedTotal'] = 'distributionBalanceTaxedTotalValueChange';
                        resMaps[gridId + '.distributionCommissionTotal'] = 'distributionCommissionTotalValueChange';
                    }
                    resMaps[gridId + '.taxTotal'] = 'currencyTaxTotalValueChangeByPrice';//(4\5\6\)

                }
                resMaps[gridId + '.onlinePtypeName'] = 'valueChange';//最终统一处理套餐
            }
            this.determine(resMaps, newData);
        }

    },

})


package com.wsgjp.ct.sale.tool.submit;


import com.wsgjp.ct.common.enums.core.enums.BaseOrderMarkEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.SysData;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MarkCreateType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MarkShowType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.OrderMarkType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.OrderSourceType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopOrderMarkEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.order.EshopOrderMarkParameter;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopOrderBaseInfoMapper;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopOrderEshopRefundMapper;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopSaleOrderMapper;
import com.wsgjp.ct.support.context.CurrentUser;
import ngp.idgenerator.UId;
import ngp.redis.RedisPoolFactory;
import ngp.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
public class AfterMarkCalculate {

    private static final Logger logger = LoggerFactory.getLogger(AfterMarkCalculate.class);

    @Autowired
    private EshopOrderEshopRefundMapper refundMapper;

    @Autowired
    private EshopSaleOrderMapper orderMapper;

    @Autowired
    private EshopOrderBaseInfoMapper baseInfoMapper;

    @Autowired
    private RedisPoolFactory factory;

    /**
     * 每小时执行一次的定时任务，获取最近7天的售后单
     * 采用游标分页解决深度分页问题
     */
    public void reportRefundRecords(BigInteger profileId) throws Exception {
        try {
            StringRedisTemplate biz = factory.getTemplate("biz");
            ValueOperations<String, String> redis = biz.opsForValue();
            String key = profileId + "AfterMarkCalculate";
            String value = redis.get(key);
            if (value == null) {
                handleBussiness(profileId);
                redis.set(key, "1", 30, TimeUnit.MINUTES);
            }
        } catch (Exception e) {
            logger.error("定时为售后单打标异常!", e);
        }
    }

    private void handleBussiness(BigInteger profileId) {
        Date now = new Date();
        Date sevenDaysBefore = DateUtils.addDays(now, -7);
        // 存储所有查询到的售后单
        List<EshopRefundEntity> allRefunds = new ArrayList<>();
        BigInteger lastId = BigInteger.ZERO;
        boolean hasMoreData = true;
        // 循环获取所有数据，使用游标分页
        while (hasMoreData) {
            // 每次查询都以上次查询的最后一个ID作为起点
            List<EshopRefundEntity> batchRefunds = refundMapper.getRefundMarkCalculateRefundList(profileId, now, sevenDaysBefore, lastId);
            // 添加到总列表
            allRefunds.addAll(batchRefunds);
            if (batchRefunds.isEmpty() || batchRefunds.size() < 1000) {
                hasMoreData = false;
                continue;
            }
            // 更新游标为当前批次的最后一个ID
            lastId = batchRefunds.get(batchRefunds.size() - 1).getId();
        }
        // 处理所有获取到的售后单
        processRefundData(allRefunds);
    }

    /**
     * 处理售后单的业务逻辑
     */
    private void processRefundData(List<EshopRefundEntity> refunds) {
        if (refunds.isEmpty()) {
            return;
        }
        BaseOrderMarkEnum afterSaleHandleTimeBeyond = BaseOrderMarkEnum.AFTER_SALE_HANDLE_TIME_BEYOND;
        BigInteger profileId = CurrentUser.getProfileId();
        ArrayList<EshopOrderMarkEntity> marks = new ArrayList<>();
        for (EshopRefundEntity refund : refunds) {
            if (refund.getRefundCreateTime() != null && refund.getRefundPromisedAgreeDuration() != -1) {
                long refundPromisedAgreeDurationTime = getRefundPromisedAgreeDurationTime(refund.getRefundPromisedAgreeDuration());
                // 平台创建时间 + 时效 > 当前时间 = 已超时
                if (refund.getRefundCreateTime().getTime() + refundPromisedAgreeDurationTime > System.currentTimeMillis()) {
                    // 构建标记
                    EshopOrderMarkEntity item = new EshopOrderMarkEntity();
                    item.setId(UId.newId());
                    item.setOrderId(refund.getId());
                    item.setProfileId(profileId);
                    item.setOrderType(OrderSourceType.Refund);
                    item.setMarkTarget(OrderMarkType.Main);
                    item.setMarkCode(BigInteger.valueOf(afterSaleHandleTimeBeyond.getCode()));
                    item.setBubble(afterSaleHandleTimeBeyond.getBubble());
                    item.setName(afterSaleHandleTimeBeyond.getName());
                    item.setShowType(MarkShowType.All);
                    item.setCreateType(MarkCreateType.DownloadSysCalc);
                    marks.add(item);
                }
            }
        }

        EshopOrderMarkParameter parameter = new EshopOrderMarkParameter();
        parameter.setEshopOrderIds(refunds.stream().map(EshopRefundEntity::getId).collect(Collectors.toList()));
        parameter.setMarkCodes(Collections.singletonList(BigInteger.valueOf(afterSaleHandleTimeBeyond.getCode())));
        parameter.setProfileId(profileId);
        parameter.setMarkTarget(-1);
        orderMapper.deleteEshopOrderMark(parameter);
        orderMapper.insertEshopOrderMark(marks);
    }

    /*
    -1=无时效,12=12小时,24=24小时,48=48小时,72=72小时
     */
    private long getRefundPromisedAgreeDurationTime(Integer refundPromisedAgreeDuration) {
        switch (refundPromisedAgreeDuration) {
            case 12:
                return 43200000L;
            case 24:
                return 86400000L;
            case 48:
                return 172800000L;
            case 72:
                return 259200000L;
            default:
                return 0L;
        }
    }

}
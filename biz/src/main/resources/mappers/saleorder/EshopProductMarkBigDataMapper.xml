<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopProductMarkBigDataMapper">
    <insert id="save" >
      INSERT INTO  pl_eshop_product_mark_data (profile_id, mark_id, big_data,id)
      VALUES(#{profileId}, #{markId},#{bigData},#{id});
    </insert>


    <delete id="delete">
        delete from pl_eshop_product_mark_data where profile_id=#{profileId} and mark_id=#{markId}
    </delete>

    <update id="update">
        UPDATE pl_eshop_product_mark_data SET big_data=#{bigData} where profile_id=#{profileId} and mark_id=#{markId}
    </update>
    <select id="query" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMarkData">
        select * from pl_eshop_product_mark_data where profile_id=#{profileId} and mark_id=#{markId}
    </select>


    <insert id="bulkInsertProductMarkData">
        INSERT INTO  pl_eshop_product_mark_data (profile_id, mark_id, big_data,id)
        VALUES
        <foreach item="item" index="index" collection="productMarkDataList" separator=",">
            (#{item.profileId}, #{item.markId},#{item.bigData},#{item.id})
        </foreach>
        ON DUPLICATE KEY
        UPDATE big_data  = values(big_data),
        mark_id = values(mark_id)
    </insert>

    <select id="queryByMarkIds"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMarkData">
        select id, big_data, mark_id, profile_id
        from pl_eshop_product_mark_data
        where profile_id = #{profileId}
          and mark_id in
        <foreach collection="markIds" close=")" open="(" separator="," item="markId">
            #{markId}
        </foreach>
    </select>
</mapper>
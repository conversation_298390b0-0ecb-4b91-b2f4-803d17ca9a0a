<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.TmcEshopNotifyChangeMapper">
    <insert id="insertMessageChange">
        REPLACE INTO `pl_eshop_notify_change` (`id`,
                                              `profile_id`,
                                              `trade_order_id`,
                                              `eshop_id`,
                                              `content`,
                                              `type`,`update_time`,`subtype`,`retry_times`,`change_biz_id`,`unique_id`)
        VALUES (#{id},
                #{profileId},
                #{tradeOrderId},
                #{eshopId},
                #{content},
                #{type},
                #{updateTime},#{subType},#{retryTimes},#{changeBizId},#{uniqueId});
    </insert>

    <select id="queryMessageChange" resultType="com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange">
        select `id`, `profile_id`,`trade_order_id`, `eshop_id`, `content`,`type`,`create_time`,`update_time`,`subtype`,`retry_times`
        from pl_eshop_notify_change
        where profile_id = #{profileId} and eshop_id = #{otypeId}
        <if test="tradeOrderIds!=null and tradeOrderIds.size()>0">
            AND trade_order_id IN
            <foreach collection="tradeOrderIds" close=")" open="(" separator="," item="tradeOrderId">
                #{tradeOrderId}
            </foreach>
        </if>
    </select>

    <select id="queryMessageChangeSorted" resultType="com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange">
        select `id`, `profile_id`,`trade_order_id`, `eshop_id`, `content`,`type`,`create_time`,`update_time`,`subtype`,`retry_times`
        from pl_eshop_notify_change
        where profile_id = #{profileId} and eshop_id = #{otypeId}
        and type=#{type}
        <if test="tradeIds!=null and tradeIds.size()>0">
            AND trade_order_id IN
            <foreach collection="tradeIds" close=")" open="(" separator="," item="tradeOrderId">
                #{tradeOrderId}
            </foreach>
        </if>
        order by `create_time` desc
    </select>

    <select id="queryMessageChangeByStatus" resultType="com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange">
        select `id`, `profile_id`,`trade_order_id`, `eshop_id`, `content`,`type`,`create_time`,`update_time`,`subtype`,`retry_times`
        from pl_eshop_notify_change
        where profile_id = #{profileId} and status = #{status} and type=#{type} and retry_times &lt; #{retryTimes} and subtype in(1,2)
        order by `create_time` desc
    </select>

    <select id="queryMessageChangeByTradeId"
            resultType="com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange">
        select `id`, `profile_id`,`trade_order_id`, `eshop_id`, `content`,`type`,`create_time`,`update_time`,`subtype`,`retry_times`
        from pl_eshop_notify_change
        where profile_id = #{profileId} and eshop_id = #{otypeId}
          AND trade_order_id = #{tradeOrderId}
    </select>

    <update id="updateEshopNotifyChangeStatus">
        update pl_eshop_notify_change
        set status=#{status},
            message=#{message},
            retry_times=#{retryTimes}
        where profile_id = #{profileId}
          and id = #{id}
    </update>

    <update id="updateEshopNotifyChange">
        update `pl_eshop_notify_change` set
        <if test="message!=null">
            `message`=#{message},
        </if>
        <if test="status!=null">
            `status`=#{status},
        </if>
        <if test="content!=null">
            `content` = #{content},
        </if>
        `update_time` = #{updateTime}
        where profile_id = #{profileId} and eshop_id = #{eshopId} and trade_order_id = #{tradeOrderId}
    </update>

    <update id="updateEshopNotifyChangeById">
        update `pl_eshop_notify_change` set
        <if test="type!=null">
            `type`=#{type},
        </if>
        <if test="content!=null">
            `content` = #{content},
        </if>
        `update_time` = #{updateTime}
        where profile_id = #{profileId} and id = #{id}
    </update>
</mapper>

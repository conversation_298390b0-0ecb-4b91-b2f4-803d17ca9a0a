Type.registerNamespace('sale.product');

sale.product.AttrRelationFormAction = function () {
    sale.product.AttrRelationFormAction.initializeBase(this);
};

sale.product.AttrRelationFormAction.prototype = {
    _addNewProp: null,
    context: function (cb) {
        var params = this.get_pageParams();
        var unRelationProps = params.unRelationProps;
        var res = $common.ajaxSync({
            url: "sale/eshoporder/product/queryLocalProps",
            router: 'ngp'
        });
        var localProps = [];
        if (res.data) {
            localProps = res.data;
        }
        cb({
            unRelationProps: unRelationProps,
            localProps: localProps
        });
    },
    initialize: function AttrRelationFormAction$initialize() {
        sale.product.AttrRelationFormAction.callBaseMethod(this, 'initialize');
    },

    doOperateAddProp: function () {
        var _this = this;
        var form = this.get_form();
        $eshoppower.checkPowerFalse("baseinfo.prop.edit");
        var rowData = form.grid.get_selectedRowData();
        var index = form.grid.get_selectedRowIndex();
        var params = new Object();
        params.propName = rowData.platformProp;
        var startObj = $saleUtils._getStartTiming('网店->网店商品管理->新增系统商品属性');
        var propSaveResult = $common.ajaxSync({
            url: "jxc/baseinfo/prop/save",
            data: params,
            type: 'post',
            router: 'ngp'
        });
        $saleUtils._logEndTiming(startObj);
        if (propSaveResult.code == "200") {
            $common.showOk("新增成功");
            rowData.propId = propSaveResult.data.id;
        } else {
            $common.showError(propSaveResult.message);
            var cus = form.cus.showModal("新增系统商品属性", {});
            cus.add_ok(function () {
                if (_this._addNewProp.data) {
                    rowData.propId = _this._addNewProp.data.id;
                }
                var response = $common.ajaxSync({
                    url: "sale/eshoporder/product/queryLocalProps",
                    data: null,
                    router: 'ngp'
                });
                var attr = response.data;
                form.grid.findColumn("propId").set_items(attr);
                form.grid.modifyRowData(index, rowData);
            });
        }
        var response = $common.ajaxSync({
            url: "sale/eshoporder/product/queryLocalProps",
            data: null,
            router: 'ngp'
        });
        var attr = response.data;
        form.grid.findColumn("propId").set_items(attr);
        form.grid.modifyRowData(index, rowData);
    },

    batchAddProp: function () {
        var form = this.get_form();
        $eshoppower.checkPowerFalse("baseinfo.prop.edit");
        var selectedItems = form.grid.get_selectedItems();
        if (selectedItems.length === 0) {
            $common.showError("请选择要新增的属性");
            form.Expand.set_enabled(true);
            return;
        }
        var bar = form.toolbar;
        bar.set_visible(true);
        var process = 100 / selectedItems.length;
        for (var i = 0; i < selectedItems.length; i++) {
            var params = {};
            var rowData = selectedItems[i];
            params.propName = rowData.platformProp;
            var startObj = $saleUtils._getStartTiming('网店->网店商品管理->新增系统商品属性');
            var propSaveResult = $common.ajaxSync({
                url: "jxc/baseinfo/prop/save", data: params, type: 'post', router: 'ngp'
            });
            $saleUtils._logEndTiming(startObj);
            rowData.__selected = false;
            if (propSaveResult.data) {
                rowData.propId = propSaveResult.data.id;
            }
            if (bar.get_value() < 100) {
                bar.set_value(bar.get_value() + process);
            }
        }
        var response = $common.ajaxSync({
            url: "sale/eshoporder/product/queryLocalProps", data: null, router: 'ngp'
        });
        var attr = response.data;
        form.grid.findColumn("propId").set_items(attr);
        form.grid.batchModifyRowData(null, selectedItems);
        bar.set_visible(false);
        $common.showOk('新增成功');
    },

    doSave: function () {
        var form = this.get_form();
        var list = form.grid.get_dataSource();
        if (!this._isAllCompleteRelation(list)){
            $common.alertWarn('还有商品属性未完成对应，请完成对应后在保存！');
            return;
        }
        var startObj = $saleUtils._getStartTiming('网店->网店商品管理->网店商品属性映射');
        startObj.batchCount = list.length;
        var res = $common.ajaxSync({url: "sale/eshoporder/product/saveEshopAttrRelation", data: list, router: 'ngp'});
        $saleUtils._logEndTiming(startObj);
        if (res.code != "200") {
            $common.showError(res.message);
        } else {
            form.data = list;
            form.doOk();
        }
    },
    _isAllCompleteRelation: function (attrList) {
        for (var i = 0; i < attrList.length; i++) {
            var attr = attrList[i];
            if (!attr.propId || attr.propId == "0") {
                return false;
            }
        }
        return true;
    },

    dispose: function () {
        sale.product.AttrRelationFormAction.callBaseMethod(this, 'dispose');
    }
};
sale.product.AttrRelationFormAction.registerClass('sale.product.AttrRelationFormAction', Sys.UI.PageAction);

Type.registerNamespace('sale.eshoporder.eshopplatformcheck');

sale.eshoporder.eshopplatformcheck.GatherOrderImportAction = function () {
    sale.eshoporder.eshopplatformcheck.GatherOrderImportAction.initializeBase(this);
};

sale.eshoporder.eshopplatformcheck.GatherOrderImportAction.prototype = {

    isImported: false,
    _stop : true,
    hasErrorData: false,
    _commonAction: null,
    initialize: function () {
        sale.eshoporder.eshopplatformcheck.GatherOrderImportAction.callBaseMethod(this, 'initialize');
        if (this._commonAction == null) {
            this._commonAction = new sale.jarvis.DeliverBill.common.CommonAction();
        }
        var AtypeId= this._commonAction.getLocalStorageDataByType(this,$user.profileid+'gather_edAtypeId', 0);
        var AtypeName= this._commonAction.getLocalStorageDataByType(this,$user.profileid+'gather_edAtypeName', "");
        var form=this.get_form();
        form.edAType.set_value(AtypeId);
        form.edAType.set_text(AtypeName);
    },
    context: function (cb) {
        cb({});
    },

    doEshopSelectorEnterPress:function(sender, eventArgs) {
        if (sender.get_value() && !sender.get_textChanged()) {
            eventArgs.set_cancel(true);
        }
    },

    doEshopSelectorSelected: function(sender,eventArgs) {
        var selector = eventArgs.get_form();
        var result = selector.selectedData;
        sender.set_value(result.id);
        sender.set_text(result.fullname);
        this._selectEshop=result;
        var req={};
        req.id=4;
        req.eshopId=result.id;
        var classData = $common.ajaxSync({
            url: "sale/eshoporder/relation/getRelationTreeNodes",
            data: req,
            type: 'post',
            router: 'ngp'
        });
        $common.toTreeData(classData.data,'name','id','pid',0);
    },

    doEshopInit:function(sender){
        var url = "jxc/baseinfo/selector/OtypeSelector.gspx";
        sender.set_selectorPageParams({
            showstopfilter: true,
            showadd: false,
            ocategorys: [0, 1, 2]
        });
        sender.set_selectorPage(url,{multiSelect: false})
    },
    doAtypeSelectorInit: function (sender) {
        var mode = "00001";
        var url = "/jxc/baseinfo/selector/AtypeSelector.gspx";
        // if ("0" == selectItem.processtype) {
        //     mode = "0000400003";
        // }
        var parameter = {
            showadd: false,
            parTypeId: mode
        };
        var filterStr = sender.get_text();
        if (filterStr && sender.get_textChanged()) {
            parameter.text = filterStr;
        }
        sender.set_selectorPageParams(parameter);
        sender.set_selectorPage(url);
    },
    doEnterPress: function (sender, eventArgs) {
        if (sender.get_value() && !sender.get_textChanged()) {
            eventArgs.set_cancel(true);
        }
    },
    doAtypeInfoSelect: function (sender, eventArgs) {
        var selector = eventArgs.get_form();
        var result = selector.selectedData;
        if (result == null) {
            Sys.UI.MessageBox.alert("没有选择任何科目信息");
            return;
        }
        var form = sender.get_form();
        form.edAType.set_value(result.id);
        form.edAType.set_text(result.fullname);


    },
    doAtypeChange:function()
    {
        var form=this.get_form();
        var atypeId=form.edAType.get_value();
        var atypeName=form.edAType.get_text();
        this._commonAction.setLocalStorageData(this, $user.profileid+'gather_edAtypeId', atypeId);
        this._commonAction.setLocalStorageData(this, $user.profileid+'gather_edAtypeName', atypeName);


    },


    doDownloadTemplate: function (sender) {
        $common.download("/sale/eshoporder/template/" + encodeURI("导入收款信息模板.xls"));
    },
    doImport: function (sender) {
        var form = sender.get_form();
        var eshopid = form.seEShopName.get_value();
        if (eshopid == null || eshopid == "") {
            var message = "请选择网店";
            $common.alert(message);
            return;
        }
        var atypeid = form.edAType.get_value();
        if (atypeid == null || atypeid == "") {
            var message = "请选择收款账户";
            $common.alert(message);
            return;
        }

        var fullName = form.loadfile.get_text();
        if (fullName == "") {
            $common.alert("请选择要导入的文件！");
            return;
        }
        form.fileName.set_text(fullName);
        var panelID = sender.get_tag(); // 这样做的目的是平台内部会根据panel.saveData()局部读取数据，而不是form.saveData()会触发全局验证
        $uploader(form[panelID], // 只会当前控件对应容器的FileUpload触发panel的saveData
            Function.createDelegate(this, this.doImportSucceeded),
            Function.createDelegate(this, this.doImportFailed),
            function (data) { // 支持动态构造服务端需要的参数，因此不是必须再gspx写隐藏域<HiddenField>也可以传参
                data.eshopId = eshopid;
                data.atypeId = atypeid;
            }, {}); // headers可以传空对象{}
    },

    doImportSucceeded: function (sender, result) {
        var _this = this;
        var form = sender.get_form();
        if (result.code != '200' ||  result.data == null || result.data == '') {
            Sys.UI.MessageBox.alert("导入收款失败："  +  result.message);
             return;
        }
         var taskid = result.data;
        _this._stop = false;
        _this._activeTimer = new Sys.Timer();
        _this._activeTimer.set_interval(1000);
        _this._activeTimer.set_enabled(true);
        _this._activeTimer.add_tick(Function.createDelegate(this, function () {
            _this._getMessage(form, taskid);
        }));
        form.btnStart.set_visible(false);
        form.mainBody.set_visible(false);
        form.process.set_visible(true);
        form.success.set_visible(false);
    },

    doImportFailed: function (sender, result) {
        if (result) {
            if(!result.message){
                result.message = "导入收款失败，可能是文件被从外部修改，请重新打开界面进行文件导入";
            }
            $common.alert(result.message);
        }
    },

    doSubmitAjax: function(sender) {
        var tag = sender.get_tag();
        var form = sender.get_form();
        var files = sender.get_element().files; // <FileEdit>
        $common.checkTips(files.length < 1, '请先选择文件', form.jsFile);
        if (tag == 'showName') { // 业务不需要
            this.get_form().lfileName.set_text(sender.get_text());
            this.get_form().btnIcon.addCssClass('SkinColor');
        }
    },

    exportErrorDate:function (){
        $common.showLoading();
        $common.ajax({
            type: 'post',
            data:this._GatherImportData,
            router:'ngp',
            responseType: 'blob',
            url: 'sale/eshoporder/platformcheck/saleOrderImportDate',
            fileName: "导入收款错误数据.xlsx", // 保存文件名，如果取不到头信息，就按这个值保存文件；
            complete: function (response) {
                // 不管失败还是成功都会触发
                $common.hideLoading();
                if (response.get_statusCode() != 200) {
                    $common.alert("下载失败")
                }
            }
        });

        this.hasErrorData =false;
    },

    _getMessage: function (form, taskId) {
        var _this = this;
        if(taskId==null){
            return;
        }
        var url = "/sale/eshoporder/common/getProcessMsg";
        var parameter = {requestStr: taskId};
        this.get_service().post(url, parameter, function (response) {
            var data = response.data;
            var completed = data.completed;
            var message = data.message;
            _this._GatherImportData = message;
            var success = 0;
            if (data.percent && data.percent < 100 &&  data.percent.toString() > form.progress.get_text())
            {
                form.processBar.set_value(data.percent);
                form.progress.set_text(data.percent + "%");
            }
            if (message) {
                var total = JSON.parse(data.message)
                for (var i=0;i<total.length;i++){
                    if(total[i].status){
                        success+=1;
                    }
                }
                form.fail.set_text(total.length-success);
                form.suc.set_text(success);
                if(total.length-success>0){
                    form.exportError.set_visible(true)
                    _this.hasErrorData =true;
                }else {
                    form.exportError.set_visible(false)
                }
            }
            if (completed) {
                var startObj = _this._startObj;
                if(startObj!=null){
                    startObj.endTiming();// 触发上报
                }
                _this._activeTimer.dispose();
                _this._activeTimer.set_enabled(false);
                _this._stop = true;
                if(!message){//导入完成但是没有错误信息，则导入的是空模版
                    $common.alert("模板错误或数据为空，导入失败",function (res) {
                        form.btnStart.set_visible(true);
                        form.mainBody.set_visible(true);
                        form.process.set_visible(false);
                    })
                    return;
                }
                form.processBar.set_value(100);
                form.progress.set_text("100%");
                form.success.set_visible(true);
                form.process.set_visible(false);
            }

        }, this);
    },

    doClosing: function (sender, args) {
        args.set_canClose(false);
        var form = this.get_form();
        var _this = this;
        if (!this._stop) {
            $common.alertError("正在导入数据，请导入完成后再退出！");
            return;
        }
        if (this.hasErrorData) {
            $msg.confirm("存在导入收款的失败数据没有下载，请确认是否继续退出？", function (result) {
                if (result) {
                    args.set_canClose(true);
                    form.close(true);
                }
            });
        } else {
            args.set_canClose(true);
        }
    },

    dispose: function () {
        sale.eshoporder.eshopplatformcheck.GatherOrderImportAction.callBaseMethod(this, 'dispose');
    }
}
sale.eshoporder.eshopplatformcheck.GatherOrderImportAction.registerClass('sale.eshoporder.eshopplatformcheck.GatherOrderImportAction', Sys.UI.PageAction);
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.PlSalePeriodGroupMapper">
    <!--时段组映射-->
    <resultMap id="PlSalePeriodGroupResultMap"
               type="com.wsgjp.ct.sale.biz.eshoporder.entity.salesperiod.PlSalePeriodGroup">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="profileId" column="profile_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="creatorId" column="creator_id" jdbcType="BIGINT"/>
        <result property="periodGroupName" column="period_group_name" jdbcType="VARCHAR"/>
        <result property="periodGroupCode" column="period_group_code" jdbcType="VARCHAR"/>
        <result property="periodGroupYear" column="period_group_year" jdbcType="VARCHAR"/>
        <result property="state" column="state" jdbcType="TINYINT"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="deleted" column="deleted" jdbcType="TINYINT"/>
        <result property="clazz" column="clazz" jdbcType="TINYINT"/>
        <!--        <collection property="periodList" column="id" javaType="List"-->
        <!--                    ofType="com.wsgjp.ct.sale.biz.eshoporder.entity.salesperiod.PlSalePeriod"-->
        <!--                    select="listPlSalePeriod"/>-->
    </resultMap>
    <!--时段组字段-->
    <sql id="PlSalePeriodGroup_Column_List">
        pspg
        .
        id
        ,pspg.profile_id,pspg.create_time,
        pspg.update_time,pspg.creator_id,pspg.period_group_name,
        pspg.period_group_code,pspg.period_group_year,pspg.state,
        pspg.remark,pspg.deleted,pspg.clazz,pspg.period_num
    </sql>
    <!--    修改一个时段组-->
    <update id="updateById" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.salesperiod.PlSalePeriodGroup">
        update pl_sale_period_group
        <set>
            <if test="createTime!=null">
                create_time=#{createTime},
            </if>
            <if test="updateTime!=null">
                update_time=#{updateTime},
            </if>
            <if test="creatorId!=null">
                creator_id=#{creatorId},
            </if>
            <if test="periodGroupName!=null and periodGroupName!=''">
                period_group_name= #{periodGroupName},
            </if>
            <if test="periodGroupCode!=null and periodGroupCode!=''">
                period_group_code= #{periodGroupCode},
            </if>
            <if test="periodGroupYear!=null and periodGroupYear!=''">
                period_group_year= #{periodGroupYear},
            </if>
            <if test="state !=null">
                state=#{state},
            </if>
            <if test="remark!=null and remark !=''">
                remark = #{remark},
            </if>
            <if test="deleted!=null">
                deleted = #{deleted},
            </if>
            <if test="clazz!=null">
                clazz = #{clazz},
            </if>
            <if test="periodNum!=null">
                period_num = #{periodNum}
            </if>
        </set>
        where id = #{id} and profile_id=#{profileId}
    </update>
    <!--    启用或者停用时段组-->
    <update id="onOrClose" >
        update pl_sale_period_group
        set state = #{arg2}
        where id in
        <foreach item="id" index="index" collection="arg0"
                 open="(" separator="," close=")">
            #{id}
        </foreach>
        and profile_id = #{arg1}
    </update>
    <!--    删除一个时段组-->
    <delete id="deleteById">
        delete
        from pl_sale_period_group
        where id = #{arg0}
          and profile_id = #{arg1}
    </delete>
    <!--    查询时段组列表-->
    <select id="listPlSalePeriodGroup"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.salesperiod.PlSalePeriodGroup"
            resultMap="PlSalePeriodGroupResultMap">
        SELECT
        <include refid="PlSalePeriodGroup_Column_List"/>
        FROM pl_sale_period_group pspg
        <where>
            <if test="id!=null and id!=''">
                pspg.id = #{id}
            </if>
            <if test="periodGroupName!=null and periodGroupName!=''">
                AND pspg.period_group_name LIKE "%"#{periodGroupName}"%"
            </if>
            <if test="periodGroupCode!=null and periodGroupCode!=''">
                AND pspg.period_group_code LIKE "%"#{periodGroupCode}"%"
            </if>
            <if test="periodGroupYear!=null and periodGroupYear!=''">
                AND pspg.period_group_year = #{periodGroupYear}
            </if>
            <if test=" state==0 or state==1 ">
                AND pspg.state = #{state}
            </if>
            <if test="clazz!=null">
                AND clazz=#{clazz}
            </if>
            <if test="true">
                AND profile_id=#{profileId}
            </if>
        </where>
    </select>
    <!--    查看时段组是否被引用-->
    <select id="isUsed" resultType="java.lang.Integer">
        select count(*)
        from pl_sale_task
        where period_group_id = #{arg0}
          and profile_id = #{arg1}
          and deleted = 0
    </select>
    <!--检测时段组名称和编号是否有重复-->
    <select id="isNameRepeat" resultType="java.lang.Integer">
        select count(0)
        from pl_sale_period_group
        <where>
            <if test="true">
                period_group_name = #{periodGroupName}
            </if>
            <if test="true">
                and profile_id = #{profileId}
            </if>
            <if test="id!=null and id!=''">
                and id!=#{id}
            </if>
        </where>
    </select>
    <!--检测编号是否重复-->
    <select id="isCodeRepeat" resultType="java.lang.Integer">
        select count(0)
        from pl_sale_period_group
        <where>
            <if test="true">
                period_group_code = #{periodGroupCode}
            </if>
            <if test="true">
                and profile_id = #{profileId}
            </if>
            <if test="id!=null and id!=''">
                and id!=#{id}
            </if>
        </where>
    </select>
    <!--    新增时段组-->
    <insert id="addPlSalePeriodGroup"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.salesperiod.PlSalePeriodGroup">
        insert into pl_sale_period_group
        (id, profile_id, create_time,
         update_time, creator_id, period_group_name,
         period_group_code, period_group_year, state,
         remark, deleted, clazz, period_num)
        values (#{id}, #{profileId}, #{createTime},
                #{updateTime}, #{creatorId}, #{periodGroupName},
                #{periodGroupCode}, #{periodGroupYear},
                #{state}, #{remark}, #{deleted}, #{clazz}, #{periodNum})
    </insert>
</mapper>
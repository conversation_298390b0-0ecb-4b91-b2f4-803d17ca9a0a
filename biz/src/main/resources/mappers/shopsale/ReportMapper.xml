<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wsgjp.ct.sale.biz.shopsale.mapper.ReportMapper">
    <select id="getBusinessPercentageList"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.report.BusinessPercentageDTO">
        Select coalesce(sum(value),0) as value,name
        <choose>
            <when test="groupType == 0">
                ,coalesce(sum(-qty),0) as saleNum
            </when>
            <when test="groupType == 1">
                ,coalesce(sum(-qty),0) as saleNum
            </when>
            <otherwise>
                ,count(distinct if(vchtype = 2000, vchcode, null )) as
                billCount
                ,count(distinct if(vchtype = 2100, vchcode, null )) as
                returnCount
                ,count(distinct if(vchtype = 2200, vchcode, null )) as
                changeCount
            </otherwise>
        </choose>
        from
        (select distinct
        <choose>
            <when test="groupType == 0">
                <include refid="isPTypeClass"/>
                ,b.typeid as groupid,abdcs.detail_id
            </when>
            <when test="groupType == 1">
                <include refid="isPTypeBrand"/>
                ,b.id as groupid,abdcs.detail_id
            </when>
            <when test="groupType == 2">
                <include refid="isPTypeClass"/>
                , abc.etype_id as groupid,abdcs.detail_id
            </when>
            <when test="groupType == 3">
                <include refid="isPTypeClass"/>
                , abc.create_etype_id as groupid,abdcs.detail_id
            </when>
            <when test="groupType == 4">
                <include refid="isPTypePayWay"/>
                ,IFNULL(b.id ,tbm.mark_code) as groupid
            </when>
            <when test="groupType == 5">
                <include refid="isVip"/>
                ,abdcs.detail_id
            </when>
            <when test="groupType == 6">
                <include refid="isPTypeClass"/>
                ,abc.otype_id as groupid,abdcs.detail_id
            </when>
            <otherwise>
                <include refid="isPTypeClass"/>
            </otherwise>
        </choose>
        ,abc.vchcode,abc.vchtype
        from acc_bill_core abc
        Left JOIN acc_bill_assinfo abaa on abaa.vchcode = abc.vchcode and abaa.profile_id = #{profileId}
        Left JOIN acc_bill_detail_core_sale abdcs on abdcs.vchcode = abc.vchcode and abdcs.profile_id = #{profileId}
        <choose>
            <when test="groupType == 0">
                Left JOIN base_ptype bp on bp.id = abdcs.ptype_id and bp.profile_id = #{profileId}
                Left JOIN base_ptype b on b.typeid = left(bp.typeid,5) and b.profile_id = #{profileId}
            </when>
            <when test="groupType == 1">
                Left JOIN base_ptype bp on bp.id = abdcs.ptype_id and bp.profile_id = #{profileId}
                Left join base_brandtype b on b.id = bp.brand_id and b.profile_id = #{profileId}
            </when>
            <when test="groupType == 2">
                Left JOIN base_etype b on b.id = abc.etype_id and b.profile_id = #{profileId}
            </when>
            <when test="groupType == 3">
                Left JOIN base_etype b on b.id = abc.create_etype_id and b.profile_id = #{profileId}
            </when>
            <when test="groupType == 4">
                Left JOIN acc_bill_account aba on aba.profile_id = #{profileId} and aba.vchcode = abc.vchcode
                Left JOIN base_payways b on b.id = aba.payway_id and b.profile_id = #{profileId}
                left join acc_deliver_mark tbm on tbm.profile_id = #{profileId} and tbm.vchcode = abc.vchcode
            </when>
            <when test="groupType == 6">
                LEFT JOIN base_otype b on b.id = abc.otype_id and b.profile_id = #{profileId}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        where abc.profile_id = #{profileId} and abc.order_sale_mode = 6 and abc.vchtype in (2000,2100,2200) and
        abc.post_state<![CDATA[ >= ]]>500 and abc.deleted = 0 and abaa.pay_state = 1
        <if test="startTime != null and endTime != null">
            AND abc.bill_date between #{startTime} AND #{endTime}
        </if>
        <include refid="query-filter">
            <property name="_list" value="gridFilter"/>
        </include>

        UNION

        select distinct
        <choose>
            <when test="groupType == 0">
                <include refid="isPTypeClass"/>
                ,b.typeid as groupid,abdcs.detail_id
            </when>
            <when test="groupType == 1">
                <include refid="isPTypeBrand"/>
                ,b.id as groupid,abdcs.detail_id
            </when>
            <when test="groupType == 2">
                <include refid="isPTypeClass"/>
                , abc.etype_id as groupid,abdcs.detail_id
            </when>
            <when test="groupType == 3">
                <include refid="isPTypeClass"/>
                , abc.create_etype_id as groupid,abdcs.detail_id
            </when>
            <when test="groupType == 4">
                <include refid="isPTypePayWay"/>
                ,IFNULL(b.id ,tbm.mark_code) as groupid
            </when>
            <when test="groupType == 5">
                <include refid="isVip"/>
                ,abdcs.detail_id
            </when>
            <when test="groupType == 6">
                <include refid="isPTypeClass"/>
                ,abc.otype_id as groupid,abdcs.detail_id
            </when>
            <otherwise>
                <include refid="isPTypeClass"/>
            </otherwise>
        </choose>
        ,abc.vchcode,abc.vchtype
        from td_bill_core abc
        Left JOIN td_bill_assinfo abaa on abaa.vchcode = abc.vchcode and abaa.profile_id = #{profileId}
        Left JOIN td_bill_detail_core abdcs on abdcs.vchcode = abc.vchcode and abdcs.profile_id = #{profileId}
        <choose>
            <when test="groupType == 0">
                Left JOIN base_ptype bp on bp.id = abdcs.ptype_id and bp.profile_id = #{profileId}
                Left JOIN base_ptype b on b.typeid = left(bp.typeid,5) and b.profile_id = #{profileId}
            </when>
            <when test="groupType == 1">
                Left JOIN base_ptype bp on bp.id = abdcs.ptype_id and bp.profile_id = #{profileId}
                Left join base_brandtype b on b.id = bp.brand_id and b.profile_id = #{profileId}
            </when>
            <when test="groupType == 2">
                Left JOIN base_etype b on b.id = abc.etype_id and b.profile_id = #{profileId}
            </when>
            <when test="groupType == 3">
                Left JOIN base_etype b on b.id = abc.create_etype_id and b.profile_id = #{profileId}
            </when>
            <when test="groupType == 4">
                Left JOIN td_bill_account aba on aba.profile_id = #{profileId} and aba.vchcode = abc.vchcode
                Left JOIN base_payways b on b.id = aba.payway_id and b.profile_id = #{profileId}
                left join td_deliver_mark tbm on tbm.profile_id = #{profileId} and tbm.vchcode = abc.vchcode
            </when>
            <when test="groupType == 6">
                LEFT JOIN base_otype b on b.id = abc.otype_id and b.profile_id = #{profileId}
            </when>
            <otherwise>
            </otherwise>
        </choose>
        where abc.profile_id = #{profileId} and abc.order_sale_mode = 6 and abc.vchtype in (2000,2100,2200) and
        abc.post_state<![CDATA[ >= ]]>500 and abc.deleted = 0 and abaa.pay_state = 1
        <if test="startTime != null and endTime != null">
            AND abc.bill_date between #{startTime} AND #{endTime}
        </if>
        <include refid="query-filter">
            <property name="_list" value="gridFilter"/>
        </include>

        )aabb
        <choose>
            <when test="groupType == 5">
                group by isVip
            </when>
            <otherwise>
                group by groupid
            </otherwise>
        </choose>
        <choose>
            <when test="sorts != null and sorts.size > 0">
                <foreach collection="sorts" item="item">
                    order by ${item.dataField}
                    <if test="item.ascending != true">
                        desc
                    </if>
                </foreach>
            </when>
            <otherwise>
                order by value desc
            </otherwise>
        </choose>
    </select>

    <sql id="isPTypeClass">
        IFNULL
            (b.fullname, '其他')
            as name,
        abdcs.qty,
                (IFNULL(-abdcs.dised_taxed_total, 0) + IFNULL(-abdcs.give_preferential_total, 0)) as value
    </sql>

    <sql id="isPTypeBrand">
        IFNULL
            (b.brand_name, '其他')
            as name,
        abdcs.qty,
               (IFNULL(-abdcs.dised_taxed_total, 0) + IFNULL(-abdcs.give_preferential_total, 0)) as value
    </sql>

    <sql id="isPTypePayWay">
        IFNULL
            (b.fullname, IF (tbm.mark_code=60000001, '积分兑换', '充值赠送'))
            as name,
        Case b.payway_type
            WHEN 3
                THEN IFNULL(IF(abc.vchtype = 2100, IFNULL(-abaa.currency_give_preferential_total, 0) - aba.currency_total,
                               (aba.currency_total + IFNULL(abaa.currency_give_preferential_total, 0))), 0)
            ELSE IFNULL(IF(abc.vchtype = 2100, -aba.currency_total,
                           aba.currency_total), 0) END as value
    </sql>

    <sql id="isVip">
        case abc.vip_card_id > 0 when 1 then '是' ELSE '否' END             as name,
        case abc.vip_card_id > 0 when 1 then true ELSE false END            as isVip,
        abdcs.qty,
         (IFNULL(-abdcs.dised_taxed_total, 0) + IFNULL(-abdcs.give_preferential_total, 0)) as value
    </sql>

    <sql id="query-filter">
        <if test="${_list} != null">
            <trim prefix=" and (" suffix=")">
                <foreach collection="${_list}" item="item" separator=" and ">
                    <choose>
                        <when test="item.magicQuery != null and item.magicQuery.size() != 0">
                            <trim prefix="(" suffix=")">
                                <foreach collection="item.magicQuery" item="magic_item" separator="or">
                                    ${magic_item.dataField} like concat('%',#{magic_item.value},'%')
                                </foreach>
                            </trim>
                        </when>
                        <when test="item.type == 0">
                            ${item.dataField} like concat('%',#{item.value},'%')
                        </when>
                        <when test="item.type == 1">
                            <trim prefixOverrides="and" prefix="(" suffix=")">
                                <if test="item.value1 != null">
                                    ${item.dataField} >= #{item.value1}
                                </if>
                                <if test="item.value2 != null">
                                    and ${item.dataField} <![CDATA[<=]]> #{item.value2}
                                </if>
                            </trim>
                        </when>
                        <when test="item.type == 2">
                            ${item.dataField} = #{item.value}
                        </when>
                        <when test="item.type == 3">
                            ${item.dataField}
                        </when>
                        <when test="item.type == 3">
                            ${item.dataField}>0
                        </when>
                        <when test="item.type == 4">
                            ${item.dataField}=0
                        </when>
                    </choose>
                </foreach>
            </trim>
        </if>
    </sql>

    <select id="getStoreStatisticsList"
            resultType="com.wsgjp.ct.sale.biz.member.model.entity.statistics.StoreStatisticsDto">
        select t.fullname as storeName,t.otype_id as otypeId,
        IFNULL(SUM(saleVaule),0) as sale,
        IFNULL(SUM(retrunVaule),0) as returnSale,
        IFNULL(SUM(replacementVaule),0) as exchangeSale,
        IFNULL(SUM(t.incomeValue),0) as income,
        IFNULL(SUM(t.vchtype = 2000),0) as saleCount,
        IFNULL(SUM(t.vchtype = 2100),0) as returnSaleCount,
        IFNULL(SUM(t.vchtype = 2200),0) as exchangeSaleCount,
        SUM(IF(isVip, saleVaule, 0)) as vipSale,
        SUM(IF(t.vchtype = 2000 and isVip, 1, 0)) as vipSaleCount, SUM(t.ptype_unit_qty) as connectNum,SUM(qty) as qty
        from (select distinct a.bill_number,
        a.vchtype,
        a.vchcode,
        IFNULL(a.otype_id,0) as otype_id,
        b.fullname,
        a.bill_total,
        IF(abs.ptype_unit_qty > 1 and a.vchtype = 2000, 1, 0) as
        ptype_unit_qty,
        IF(a.vchtype = 2000 or (a.vchtype = 2200 and a.bill_total > 0) ,ptype_unit_qty,0) as qty,
        abs.ptype_unit_qty as unit_qty,
        a.vip_card_id > 0 as isVip,
        IFNULL(IF(a.vchtype = 2100, IFNULL(-a.bill_total-ABS(abs.currency_give_preferential_total), 0),
        a.bill_total+abs.currency_give_preferential_total), 0) as incomeValue,
        IFNULL(IF(a.vchtype = 2000, IFNULL(a.bill_total+abs.currency_give_preferential_total, 0), 0), 0) as saleVaule,
        IFNULL(IF(a.vchtype = 2100, IFNULL(a.bill_total-ABS(abs.currency_give_preferential_total), 0), 0), 0) as
        retrunVaule,
        IFNULL(IF(a.vchtype = 2200, IFNULL(a.bill_total+abs.currency_give_preferential_total, 0), 0), 0) as
        replacementVaule
        from acc_bill_core a
        left join acc_bill_assinfo abs
        on a.vchcode = abs.vchcode and a.profile_id = abs.profile_id
        left join base_otype b on a.otype_id = b.id and b.profile_id = a.profile_id
        where a.profile_id = #{profileId}
        <if test="startTime != null and endTime != null">
            AND a.bill_date between #{startTime} AND #{endTime}
        </if>
        and a.vchtype in (2000, 2100, 2200)
        and a.process_type = 3
        <if test="otypeId != null">
            and a.otype_id = #{otypeId}
        </if>
        and a.order_sale_mode = 6 and a.deleted = 0 and abs.pay_state = 1
        UNION
        select distinct a.bill_number,
        a.vchtype,
        a.vchcode,
        IFNULL(a.otype_id,0) as otype_id,
        b.fullname,
        a.bill_total,
        IF(abs.ptype_unit_qty > 1 and a.vchtype = 2000, 1, 0) as
        ptype_unit_qty,
        IF(a.vchtype = 2000 or (a.vchtype = 2200 and a.bill_total > 0) ,ptype_unit_qty,0) as qty,
        abs.ptype_unit_qty as unit_qty,
        a.vip_card_id > 0 as isVip,
        IFNULL(IF(a.vchtype = 2100, IFNULL(-a.bill_total-ABS(abs.currency_give_preferential_total), 0),
        a.bill_total+abs.currency_give_preferential_total), 0) as incomeValue,
        IFNULL(IF(a.vchtype = 2000, IFNULL(a.bill_total+abs.currency_give_preferential_total, 0), 0), 0) as saleVaule,
        IFNULL(IF(a.vchtype = 2100, IFNULL(a.bill_total-ABS(abs.currency_give_preferential_total), 0), 0), 0) as
        retrunVaule,
        IFNULL(IF(a.vchtype = 2200, IFNULL(a.bill_total+abs.currency_give_preferential_total, 0), 0), 0) as
        replacementVaule

        from td_bill_core a
        left join td_bill_assinfo abs
        on a.vchcode = abs.vchcode and a.profile_id = abs.profile_id
        left join base_otype b on a.otype_id = b.id and b.profile_id = a.profile_id

        where a.profile_id = #{profileId}
        <if test="startTime != null and endTime != null">
            AND a.bill_date between #{startTime} AND #{endTime}
        </if>
        and a.vchtype in (2000, 2100, 2200)
        and a.process_type = 3
        <if test="otypeId != null">
            and a.otype_id = #{otypeId}
        </if>
        and a.order_sale_mode = 6 and a.post_state<![CDATA[ >= ]]>500 and a.deleted = 0 and abs.pay_state = 1) t
        group by t.otype_id
    </select>

    <select id="getStoredValueList"
            resultType="com.wsgjp.ct.sale.biz.member.model.entity.statistics.StoreStatisticsDto">
        select SUM(if(svrrl.charge_type = 1, 0, svrrl.charge_total)) as recharge , SUM(if(svrrl.charge_type = 0, 1, 0))
        as
        rechargeCount,
        svrrl.otype_id,bo.fullname as
        storeName
        from ss_vip_recharge_record_list svrrl
        left join base_otype bo on svrrl.otype_id = bo.id and bo.profile_id = svrrl.profile_id
        where svrrl.profile_id = #{profileId}
        and svrrl.create_time between #{startTime} AND #{endTime}
        <if test="otypeIds != null and otypeIds.size > 0">
            and svrrl.otype_id in
            <foreach collection="otypeIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="otypeId!= null">
            and svrrl.otype_id = #{otypeId}
        </if>
        group by svrrl.otype_id
    </select>

    <select id="getPayWayTypeIncomeList"
            resultType="com.wsgjp.ct.sale.biz.member.model.entity.statistics.StoreIncomePayWayDto">
        select t.fullname as payWayName,t.id as payWayId,sum(incomeValue) as income
        from (select a.bill_number,
        a.vchtype, a.vchcode,
        bp.fullname,
        bp.id,
        IF(a.vchtype = 2100,
        IF(bp.payway_type = 3, -aba.currency_total - ABS(abs.currency_give_preferential_total),
        -aba.currency_total), IF(bp.payway_type = 3,
        aba.currency_total +
        abs.currency_give_preferential_total,
        aba.currency_total)) as incomeValue
        from acc_bill_core a
        left join acc_bill_assinfo abs on a.vchcode = abs.vchcode and a.profile_id = abs.profile_id
        left join acc_bill_account aba on a.vchcode = aba.vchcode and a.profile_id = aba.profile_id
        left join base_payways bp on aba.payway_id = bp.id and bp.profile_id = a.profile_id


        where a.profile_id = #{profileId}
        <if test="startTime != null and endTime != null">
            AND a.create_time between #{startTime} AND #{endTime}
        </if>

        and a.deleted = 0 and a.otype_id = #{otypeId}
        and bp.fullname is not null
        and((a.vchtype in (2000, 2100, 2200,4001)
        and a.process_type = 3
        and a.order_sale_mode = 6
        and abs.pay_state = 1) or (a.business_type = 425 ))
        union

        select a.bill_number,
        a.vchtype, a.vchcode,
        bp.fullname,
        bp.id,
        IF(a.vchtype = 2100,
        IF(bp.payway_type = 3, -aba.currency_total - ABS(abs.currency_give_preferential_total),
        -aba.currency_total), IF(bp.payway_type = 3,
        aba.currency_total +
        abs.currency_give_preferential_total,
        aba.currency_total)) as incomeValue
        from td_bill_core a
        left join td_bill_assinfo abs on a.vchcode = abs.vchcode and a.profile_id = abs.profile_id
        left join td_bill_account aba on a.vchcode = aba.vchcode and a.profile_id = aba.profile_id
        left join base_payways bp on aba.payway_id = bp.id and bp.profile_id = a.profile_id


        where a.profile_id = #{profileId}
        <if test="startTime != null and endTime != null">
            AND a.create_time between #{startTime} AND #{endTime}
        </if>
        and a.deleted = 0 and a.otype_id = #{otypeId}
        and bp.fullname is not null and ((a.vchtype in (2000, 2100, 2200,4001)
        and a.process_type = 3 and a.post_state >= 500
        and a.order_sale_mode = 6
        and abs.pay_state = 1) or (a.business_type = 425 )) )t group by t.id
    </select>
</mapper>

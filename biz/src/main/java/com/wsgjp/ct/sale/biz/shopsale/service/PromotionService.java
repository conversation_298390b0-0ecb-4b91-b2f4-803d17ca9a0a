package com.wsgjp.ct.sale.biz.shopsale.service;

import bf.datasource.annotation.PageDataSource;
import bf.datasource.page.PageDevice;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSONObject;
import com.wsgjp.ct.bill.core.handle.entity.enums.Vchtypes;
import com.wsgjp.ct.framework.enums.AssertsChangeType;
import com.wsgjp.ct.framework.enums.AssertsSourceOperation;
import com.wsgjp.ct.framework.enums.AssertsSourceType;
import com.wsgjp.ct.sale.biz.bill.aspect.AddLogs;
import com.wsgjp.ct.sale.biz.bill.service.AddBillLogs;
import com.wsgjp.ct.sale.biz.bill.service.BillInterface;
import com.wsgjp.ct.sale.biz.bill.service.impl.GoodsBillServiceImpl;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.SysData;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopOrderBaseInfoMapper;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.biz.member.common.CustomResult;
import com.wsgjp.ct.sale.biz.member.exceptions.ShopException;
import com.wsgjp.ct.sale.biz.member.model.dto.vip.VipAssertsBillDetailDto;
import com.wsgjp.ct.sale.biz.member.model.entity.card.SsCardTemplateOtype;
import com.wsgjp.ct.sale.biz.member.service.ISsCardTemplateOtypeService;
import com.wsgjp.ct.sale.biz.member.utils.ProcessUtil;
import com.wsgjp.ct.sale.biz.member.utils.PromotionPtypeImportListener;
import com.wsgjp.ct.sale.biz.member.utils.PromotionPtypeImportWriteHandler;
import com.wsgjp.ct.sale.biz.shopsale.common.PromotionLogUtils;
import com.wsgjp.ct.sale.biz.shopsale.common.PromotionUtils;
import com.wsgjp.ct.sale.biz.shopsale.constanst.BillSaveResultTypeEnum;
import com.wsgjp.ct.sale.biz.shopsale.constanst.PromotionPtypeType;
import com.wsgjp.ct.sale.biz.shopsale.constanst.PromotionType;
import com.wsgjp.ct.sale.biz.shopsale.constanst.PtypeGroup;
import com.wsgjp.ct.sale.biz.shopsale.enums.BillSourceTypeEnum;
import com.wsgjp.ct.sale.biz.shopsale.mapper.PromotionAddressMapper;
import com.wsgjp.ct.sale.biz.shopsale.mapper.PromotionMapper;
import com.wsgjp.ct.sale.biz.shopsale.mapper.PromotionOtypeMapper;
import com.wsgjp.ct.sale.biz.shopsale.mapper.PromotionPtypeMapper;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.base.BaseInfoLog;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.*;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.promotion.PromotionPtypeRequest;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.promotion.QueryPromotionRequest;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.bill.PosBill;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.promotion.*;
import com.wsgjp.ct.sale.biz.shopsale.model.vo.baseinfo.Etype;
import com.wsgjp.ct.sale.biz.shopsale.model.vo.bill.PtypeResponse;
import com.wsgjp.ct.sale.biz.shopsale.model.vo.promotion.PromotionCredits;
import com.wsgjp.ct.sale.biz.shopsale.model.vo.promotion.PromotionPageResponse;
import com.wsgjp.ct.sale.sdk.pos.biz.MemberAssertsChangeService;
import com.wsgjp.ct.sale.sdk.pos.entity.asserts.MemberAssert;
import com.wsgjp.ct.sale.sdk.pos.entity.asserts.MemberAssertsChange;
import com.wsgjp.ct.sale.sdk.pos.entity.asserts.VipAsserts;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.log.service.LogService;
import com.wsgjp.ct.support.thread.ThreadPool;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import com.wsgjp.ct.support.utils.RedisBizUtils;
import ngp.idgenerator.UId;
import ngp.loadbalancer.context.RouteContext;
import ngp.loadbalancer.context.RouteThreadLocal;
import ngp.starter.web.base.ResultCode;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.wsgjp.ct.sale.biz.eshoporder.service.product.EshopProductHandleService.checkFilePrefix;

/**
 * <AUTHOR>
 */
@Service
public class PromotionService {
    @Autowired
    PromotionMapper promotionMapper;

    @Autowired
    EshopOrderBaseInfoMapper eshopOrderBaseInfoMapper;

    @Autowired
    ISsCardTemplateOtypeService otypeService;

    @Autowired
    PromotionOtypeMapper promotionOtypeMapper;
    @Autowired
    private PromotionPtypeMapper promotionPtypeMapper;

    @Autowired
    GoodsBillServiceImpl goodsBillService;

    @Autowired
    private AddBillLogs addBillLogs;

    @Autowired
    BillService billService;

    @Autowired
    ShopSaleBaseInfoService shopSaleBaseInfoService;

    @Autowired
    private BillInterface billInterface;

    @Autowired
    private MemberAssertsChangeService memberAssertsChangeService;

    private static final Logger LOGGER = LoggerFactory.getLogger(PromotionService.class);

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private PromotionAddressMapper promotionAddressMapper;

    public static final int ADD_MODE = 1;// 1=新增促销
    public static final int UPDATE_MODE = 2;// 2=修改促销

    //region 促销

    /// 获取促销 （新增时处理默认数据）
    public PromotionPageResponse getPromotionInfoInit(QueryPromotionRequest parameter) {

        PromotionInfo promotionInfo = new PromotionInfo();
        if (parameter != null && parameter.getPromotionId() != null && !parameter.getPromotionId().equals(BigInteger.ZERO)) {
            promotionInfo = getFullPromotionInfo(parameter.getPromotionId());
            promotionInfo.setMode(UPDATE_MODE);

        } else {
            promotionInfo.setId(UId.newId());
            promotionInfo.setUseRange(parameter != null ? parameter.getUseRange() : 0);
            promotionInfo.setSetStyle(0);
            promotionInfo.setBtypeRangType(7);
            promotionInfo.setRangTypeWholeSale(1);
            promotionInfo.setMode(ADD_MODE);
            promotionInfo.setPromotionType(null == parameter ? PromotionType.SPTJ.getCode() :
                    parameter.getPromotionType());
            promotionInfo.setStartTime("00:00:00");
            promotionInfo.setEndTime("23:59:59");
//            if (parameter != null) {
//                promotionInfo.setCalIntegral(parameter.getPromotionType() != PromotionType.SPTJ.getCode());
//            }
            promotionInfo.setEtypeId(CurrentUser.getEmployeeId());
            promotionInfo.setStartDate(DateUtils.parse(DateUtils.formatDate(DateUtils.getDate(), "yyyy-MM-dd")));
            promotionInfo.setEndDate(DateUtils.parse(DateUtils.formatDate(DateUtils.addMonths(DateUtils.getDate(), 1)
                    , "yyyy-MM-dd")));
            promotionInfo.setProfileId(CurrentUser.getProfileId());
            promotionInfo.setVipRights(true);
            if (null != parameter) {
                promotionInfo.setStrategy(buildNewStrategy(parameter.getPromotionType(), promotionInfo));
            }

            if (promotionInfo.getUseRange() == 0) {
                if (promotionInfo.getPromotionType() == 4) {
                    promotionInfo.setRangType(2);
                } else {
                    promotionInfo.setRangType(1);
                }
                promotionInfo.setCalIntegral(false);

            } else if (promotionInfo.getUseRange() == 1) {
                promotionInfo.setRangType(1);
                promotionInfo.setCalIntegral(false);
                promotionInfo.setStartDate(DateUtils.parse(DateUtils.formatDate(DateUtils.getDate(), "yyyy-MM-dd") + " 00:00:00"));
                promotionInfo.setEndDate(DateUtils.parse(DateUtils.formatDate(DateUtils.addMonths(DateUtils.getDate(), 1), "yyyy-MM-dd") + " 23:59:59"));


            } else if (promotionInfo.getUseRange() == 3) {
                promotionInfo.setRangType(1);
                promotionInfo.setCalIntegral(true);
            } else {
                promotionInfo.setRangType(1);

            }

        }
        PromotionPageResponse response = new PromotionPageResponse();
        response.setPromotionInfo(promotionInfo);
        return response;
    }

    //检查促销
    public boolean checkPromotionIn(PromotionInfo promotionInfo) {
        return promotionMapper.checkPromotionIn(promotionInfo);
    }

    //插入促销
    @Transactional(rollbackFor = {Exception.class})
    public boolean insertPromotion(PromotionInfo promotionInfo) {
        List<PromotionInfo> info = promotionMapper.getPromotionByName(CurrentUser.getProfileId(),
                promotionInfo.getFullname());
        if (!CollectionUtils.isEmpty(info)) {
            throw new ShopException("促销名称已存在");
        }

        if (promotionInfo.getJoinOrder() == null) {
            promotionInfo.setJoinOrder(false);
        }

        if (promotionInfo.getCalIntegral() == null) {
            promotionInfo.setCalIntegral(false);
        }

        BigInteger profileId = CurrentUser.getProfileId();
        promotionInfo.setProfileId(CurrentUser.getProfileId());
        promotionInfo.setEtypeId(CurrentUser.getEmployeeId());
        boolean isSuccess = false;


        BigInteger priority = promotionMapper.getMaxPromotionPriority(CurrentUser.getProfileId(),
                promotionInfo.getPromotionType());
        if (priority == null) {
            priority = BigInteger.ZERO;
        }
        promotionInfo.setPriority(priority.intValue() + 1);
        //添加基本信息

        if (promotionInfo.getUseRange() == 1 && promotionInfo.getRangTypeWholeSale() == 2) {
            promotionInfo.setRangType(promotionInfo.getBtypeRangType());
        } else if (promotionInfo.getUseRange() == 1 && promotionInfo.getRangTypeWholeSale() == 1) {
            promotionInfo.setRangType(1);
        }
        isSuccess = promotionMapper.insertPromotion(promotionInfo);


        //添加otype
        if (isSuccess) {
            int filterType = 0;
            if (!CollectionUtils.isEmpty(promotionInfo.getFilterIds())) {
                isSuccess = insertPromotionOtypeByIds(promotionInfo.getFilterIds(), promotionInfo.getId(), filterType);
            }
        }
        if (isSuccess) {
            isSuccess = insertStrategies(promotionInfo.getStrategy());
        }
        if (isSuccess) {
            if (Objects.equals(promotionInfo.getPtypeGroup(), 3)) {
                promotionInfo.getPtypeList().stream().forEach(s -> {
                    s.setId(UId.newId());
                });
                //添加
                promotionPtypeMapper.saveScorePromotionOtype(promotionInfo.getPtypeList(), promotionInfo.getId(),
                        promotionInfo.getProfileId(), promotionInfo.getPtypeGroup());
            } else if (Objects.equals(promotionInfo.getPtypeGroup(), 4)) {
                // TODO: 2022/5/25 留给服务商品的
            } else if (Objects.equals(promotionInfo.getPtypeGroup(), 5)) {
                promotionInfo.getPtypeList().stream().forEach(s -> {
                    //短暂存储一下id
                    s.setId(UId.newId());
                });
                //权益卡
                promotionPtypeMapper.saveScorePromotionCard(promotionInfo.getPtypeList(), promotionInfo.getId(),
                        promotionInfo.getProfileId(), promotionInfo.getPtypeGroup());
            } else if (Objects.equals(promotionInfo.getPtypeGroup(), 6)) {
                promotionInfo.getPtypeList().stream().forEach(s -> {
                    //短暂存储一下id
                    s.setId(UId.newId());

                });

                promotionPtypeMapper.saveScorePromotionCard(promotionInfo.getPtypeList(), promotionInfo.getId(),
                        promotionInfo.getProfileId(), promotionInfo.getPtypeGroup());
            } else if (Objects.equals(promotionInfo.getPtypeGroup(), 7)) {
                promotionInfo.getPtypeList().stream().forEach(s -> {
                    s.setId(UId.newId());
                });
                promotionPtypeMapper.saveScorePromotionAmount(promotionInfo.getPtypeList(), promotionInfo.getId(),
                        promotionInfo.getProfileId(), promotionInfo.getPtypeGroup());
            } else {
                //ptypeType 0:活动商品 1:搭配商品
                isSuccess = GetBeanUtil.getBean(PromotionService.class).insertPromotionPtypeList(promotionInfo.getPtypeList(), promotionInfo.getStrategy().get(0)
                        , PromotionPtypeType.MAIN.getValue(), promotionMapper);
            }

        }
        if (null != promotionInfo.getGiftPtypeList() && !promotionInfo.getGiftPtypeList().isEmpty()) {
            if (isSuccess) {
                //ptypeType 0:活动商品 1:搭配商品
                isSuccess = GetBeanUtil.getBean(PromotionService.class).insertPromotionPtypeList(promotionInfo.getGiftPtypeList(),
                        promotionInfo.getStrategy().get(1), PromotionPtypeType.GIFT.getValue(), promotionMapper);
            }
        }

        if (null != promotionInfo.getSpecifiedGiftPtypeList() && !promotionInfo.getSpecifiedGiftPtypeList().isEmpty()) {
            if (isSuccess) {
                //指定赠品插入
                isSuccess = GetBeanUtil.getBean(PromotionService.class).insertPromotionPtypeList(promotionInfo.getSpecifiedGiftPtypeList(),
                        promotionInfo.getStrategy().get(1), PromotionPtypeType.SPECIFIED_GIFT.getValue(), promotionMapper);
            }
        }
        ///处理促销对象
        if (isSuccess) {
            if (promotionInfo.getRangValueList() != null && !promotionInfo.getRangValueList().isEmpty()) {
                ///批量插入
                promotionInfo.getRangValueList().forEach(s -> {
                    s.setId(UId.newId());
                    s.setProfileId(CurrentUser.getProfileId());
                    if (s.getRangName() == null || s.getRangName().isEmpty()) {
                        s.setRangName("无");
                    }
                    if (s.getRangId() == null) {
                        s.setRangId(BigInteger.ZERO);
                    }
                });
                isSuccess = promotionMapper.insertPromotionRangValue(promotionInfo.getRangValueList());
            }
        }

        // 处理包邮促销地址数据
        if (isSuccess && promotionInfo.getPromotionType() == 8) { // 包邮促销类型
            isSuccess = handleFreeShippingAddresses(promotionInfo);
        }

        //保存日志
        savePromotionLog(promotionInfo, null);
        return isSuccess;
    }


    /// 保存增加促销策略日志
    private void savePromotionLog(PromotionInfo promotionInfo, PromotionInfo oldPromotion) {
        BaseInfoLog baseInfoLog = new BaseInfoLog();
        baseInfoLog.setBody(getPromotionLogBody(promotionInfo, oldPromotion));
        baseInfoLog.setObjectId(promotionInfo.getId());
        if (baseInfoLog.getBody().equals("【编辑促销】")) {
            baseInfoLog.setBody("【编辑促销】: 保存促销成功");
        }
        saveLog(baseInfoLog);
    }

    private String getPromotionLogBody(PromotionInfo promotionInfo, PromotionInfo oldPromotionInfo) {
        String body;
        body = buildLogMsg(promotionInfo, oldPromotionInfo);
        return body;
    }

    private String buildLogMsg(PromotionInfo promotionInfo, PromotionInfo oldPromotionInfo) {
        buildFilterNames(promotionInfo);
        String body = "";
        switch (promotionInfo.getPromotionType()) {
            case 0:
                //商品特价
            {
                body = PromotionLogUtils.getPromotionBaseInfoBody(promotionInfo, oldPromotionInfo) +
                        PromotionLogUtils.getSpecialPriceBody(promotionInfo, oldPromotionInfo) +
                        PromotionLogUtils.getPtypeListBody(promotionInfo, oldPromotionInfo);
            }
            break;
            case 1:
                //订单满减
                body = PromotionLogUtils.getPromotionBaseInfoBody(promotionInfo, oldPromotionInfo) +
                        PromotionLogUtils.getOrderReductionBody(promotionInfo, oldPromotionInfo) +
                        PromotionLogUtils.getPtypeListBody(promotionInfo, oldPromotionInfo);
                break;
            case 2:
                //第二件半价
                body = PromotionLogUtils.getPromotionBaseInfoBody(promotionInfo, oldPromotionInfo) +
                        PromotionLogUtils.getSpecialPriceBody(promotionInfo, oldPromotionInfo) +
                        PromotionLogUtils.getSecondHalfPrice(promotionInfo, oldPromotionInfo);
                break;
            case 3:
            case 5:
                //满件赠
                body = PromotionLogUtils.getPromotionBaseInfoBody(promotionInfo, oldPromotionInfo) +
                        PromotionLogUtils.getSpecialPriceBody(promotionInfo, oldPromotionInfo) +
                        PromotionLogUtils.getFullGift(promotionInfo, oldPromotionInfo);
                break;
            case 4:
                //积分兑换
                body = PromotionLogUtils.getPromotionBaseInfoBody(promotionInfo, oldPromotionInfo) +
                        PromotionLogUtils.getPointsFor(promotionInfo, oldPromotionInfo);
                break;
            case 6:
                //N元组合购
                body = PromotionLogUtils.getPromotionBaseInfoBody(promotionInfo, oldPromotionInfo) +
                        PromotionLogUtils.getCombinationBody(promotionInfo, oldPromotionInfo) +
                        PromotionLogUtils.getPtypeListBody(promotionInfo, oldPromotionInfo);
                break;
            case 7:
                //价格等级折扣
                body = PromotionLogUtils.getPromotionBaseInfoBody(promotionInfo, oldPromotionInfo) +
                        PromotionLogUtils.getPtypeListBody(promotionInfo, oldPromotionInfo);
                break;
            case 8:
                //包邮
                body = PromotionLogUtils.getPromotionBaseInfoBody(promotionInfo, oldPromotionInfo) +
                        PromotionLogUtils.getPtypeListBody(promotionInfo, oldPromotionInfo);
                break;
        }
        return body;
    }

    private void buildFilterNames(PromotionInfo info) {
        List<PromotionFilterType> filterTypesList = this.getPromotionOtype(Collections.singletonList(info.getId()));
        if (filterTypesList == null || filterTypesList.isEmpty()) return;
        Map<BigInteger, List<PromotionFilterType>> otypeGroupByPromotion =
                filterTypesList.stream().collect(Collectors.groupingBy(item -> item.getPromotionId()));
        List<String> filterNames =
                otypeGroupByPromotion.get(info.getId()).stream().map(PromotionFilterType::getFilterName).collect(Collectors.toList());
        info.setFilterNames(StringUtils.join(filterNames, ","));

    }

    public void saveLog(BaseInfoLog baseInfoLog) {
        baseInfoLog.setProfileId(CurrentUser.getProfileId());
        baseInfoLog.setEtypeId(CurrentUser.getEmployeeId());
        baseInfoLog.setId(UId.newId());
        Etype etype = shopSaleBaseInfoService.getEtypeById();
        baseInfoLog.setEfullname(etype.getFullname());
        baseInfoLog.setLogTime(DateUtils.getDate());
        baseInfoLog.setIp(CurrentUser.getClientIp());
        baseInfoLog.setObjectType("promotion");
        LogService.add(baseInfoLog);
    }

    private boolean insertPromotionOtypeByIds(List<BigInteger> filterIds, BigInteger promotionId, int filterType) {
        List<PromotionFilterType> promotionOtypeList = new ArrayList<>();
        for (BigInteger filterId : filterIds) {
            PromotionFilterType otype = new PromotionFilterType();
            otype.setId(UId.newId());
            otype.setPromotionId(promotionId);
            otype.setProfileId(CurrentUser.getProfileId());
            otype.setFilterId(filterId);
            otype.setFilterType(filterType);
            promotionOtypeList.add(otype);
        }
        return promotionMapper.insertPromotionOtypes(promotionOtypeList);
    }

    private boolean insertPromotionPtypeList(List<PromotionPtype> ptypeList, PromotionStrategy strategy,
                                             int ptypeType, PromotionMapper promotionMapper) {
        boolean isSuccess;
        if (null == ptypeList || ptypeList.isEmpty()) {
            return true;
        }
        ///过滤可能的重复数据
        ptypeList = PromotionUtils.filterUniquePtypes(ptypeList);

        for (int i = 0; i < ptypeList.size(); i++) {
            PromotionPtype ptype = ptypeList.get(i);
            ptype.setId(UId.newId());
            ptype.setStrategyId(strategy.getId());
            ptype.setPromotionId(strategy.getPromotionId());
            ptype.setProfileId(CurrentUser.getProfileId());
            ptype.setPromotionPtypeType(ptypeType);
            if (ptype.getBatchNo() == null || ptype.getBatchNo().isEmpty()) {
                ptype.setBatchNo("");
            }
            if (ptype.getBatchNos() == null || ptype.getBatchNos().isEmpty()) {
                ptype.setBatchNos(new ArrayList<>());
            }
            if (ptype.getGiftQty() == null) {
                ptype.setGiftQty(BigDecimal.ONE);
            }
            ptype.setRowindex(i);
        }
        isSuccess = promotionMapper.insertPromotionPtypes(ptypeList);
        if (isSuccess && ptypeType == 0) {
            ///插入批次表
            for (int i = 0; i < ptypeList.size(); i++) {
                PromotionPtype ptype = ptypeList.get(i);
                List<String> batchNos = ptype.getBatchNos();

                if (batchNos.isEmpty()) {
                    continue;
                }
                List<PromotionPtypeBatch> promotionPtypeBatches = new ArrayList<>();
                if (ptype.getBatchEnabled()) {
                    for (String batchNo : batchNos) {
                        PromotionPtypeBatch batch = new PromotionPtypeBatch();
                        batch.setId(UId.newId());
                        batch.setPromotionId(ptype.getPromotionId());
                        batch.setSsPromotionPtypeId(ptype.getId());
                        batch.setBatchNo(batchNo);
                        batch.setProfileId(CurrentUser.getProfileId());
                        promotionPtypeBatches.add(batch);
                    }
                    if (!CollectionUtils.isEmpty(promotionPtypeBatches)) {
                        isSuccess = promotionMapper.insertPromotionPtypeBatch(promotionPtypeBatches);
                    }
                }

            }
/*            ptypeList = ptypeList.stream().filter(item -> item.getBatchEnabled() && !item.getBatchNo().isEmpty()).collect(Collectors.toList());
            if (!ptypeList.isEmpty()) {
                isSuccess = promotionMapper.insertPromotionPtypeBatch(ptypeList);
            }*/
        }
        return isSuccess;
    }


    //修改促销
    @Transactional(rollbackFor = {Exception.class})
    public boolean updatePromotion(PromotionInfo promotionInfo) {
        List<PromotionInfo> info = promotionMapper.getPromotionByName(CurrentUser.getProfileId(),
                promotionInfo.getFullname());
        if (!CollectionUtils.isEmpty(info) && !info.get(0).getId().equals(promotionInfo.getId())) {
            throw new ShopException("促销名称已存在");
        }

        QueryPromotionRequest request = new QueryPromotionRequest();
        request.setPromotionId(promotionInfo.getId());
        request.setPromotionType(promotionInfo.getPromotionType());
        PromotionPageResponse response = getPromotionInfoInit(request);
        PromotionInfo oldPromotion = response.getPromotionInfo();

        if (promotionInfo.getUseRange() == 1 && promotionInfo.getRangTypeWholeSale() == 2) {
            promotionInfo.setRangType(promotionInfo.getBtypeRangType());
        } else if (promotionInfo.getUseRange() == 1 && promotionInfo.getRangTypeWholeSale() == 1) {
            promotionInfo.setRangType(1);
        }

        boolean isSuccess = promotionMapper.updatePromotion(promotionInfo);

        if (isSuccess) {
            promotionMapper.updateStrategys(promotionInfo.getStrategy());
        }
        if (isSuccess) {
            deletePromotionOtype(promotionInfo.getId());
            int filterType = 0;
            if (!CollectionUtils.isEmpty(promotionInfo.getFilterIds())) {
                insertPromotionOtypeByIds(promotionInfo.getFilterIds(), promotionInfo.getId(), filterType);
            }
        }
        promotionMapper.deletePromotionPtypes(CurrentUser.getProfileId(), promotionInfo.getId());
        if (isSuccess) {
            if (Objects.equals(promotionInfo.getPtypeGroup(), 3)) {
                promotionInfo.getPtypeList().stream().forEach(s -> {
                    s.setId(UId.newId());
                });
                //添加
                promotionPtypeMapper.saveScorePromotionOtype(promotionInfo.getPtypeList(), promotionInfo.getId(),
                        promotionInfo.getProfileId(), promotionInfo.getPtypeGroup());
            } else if (Objects.equals(promotionInfo.getPtypeGroup(), 4)) {
                // TODO: 2022/5/25 留给服务商品的
            } else if (Objects.equals(promotionInfo.getPtypeGroup(), 5)) {
                promotionInfo.getPtypeList().stream().forEach(s -> {
                    if (Objects.isNull(s.getPid()) || Objects.equals(s.getPid(), BigInteger.ZERO)) {
                        //短暂存储一下id
                        s.setPid(UId.newId());
                    }
                    s.setId(UId.newId());
                });
                //权益卡
                promotionPtypeMapper.saveScorePromotionCard(promotionInfo.getPtypeList(), promotionInfo.getId(),
                        promotionInfo.getProfileId(), promotionInfo.getPtypeGroup());
            } else if (Objects.equals(promotionInfo.getPtypeGroup(), 6)) {
                promotionInfo.getPtypeList().stream().forEach(s -> {
                    if (Objects.isNull(s.getPid()) || Objects.equals(s.getPid(), BigInteger.ZERO)) {
                        //短暂存储一下id
                        s.setPid(UId.newId());
                    }
                    s.setId(UId.newId());
                });

                promotionPtypeMapper.saveScorePromotionCard(promotionInfo.getPtypeList(), promotionInfo.getId(),
                        promotionInfo.getProfileId(), promotionInfo.getPtypeGroup());
            } else if (Objects.equals(promotionInfo.getPtypeGroup(), 7)) {
                promotionInfo.getPtypeList().stream().forEach(s -> {
                    s.setId(UId.newId());
                });
                promotionPtypeMapper.saveScorePromotionAmount(promotionInfo.getPtypeList(), promotionInfo.getId(),
                        promotionInfo.getProfileId(), promotionInfo.getPtypeGroup());
            } else {
                //先删除商品批次表数据
                promotionMapper.deletePromotionPtypesBatch(CurrentUser.getProfileId(), promotionInfo.getId());
                //ptypeType 0:活动商品 1:搭配商品
                isSuccess = insertPromotionPtypeList(promotionInfo.getPtypeList(), promotionInfo.getStrategy().get(0)
                        , PromotionPtypeType.MAIN.getValue(), promotionMapper);
            }
        }
        if (null != promotionInfo.getGiftPtypeList() && !promotionInfo.getGiftPtypeList().isEmpty()) {
            if (isSuccess) {
                isSuccess = insertPromotionPtypeList(promotionInfo.getGiftPtypeList(),
                        promotionInfo.getStrategy().get(1), PromotionPtypeType.GIFT.getValue(), promotionMapper);
            }
        }
        if (null != promotionInfo.getSpecifiedGiftPtypeList() && !promotionInfo.getSpecifiedGiftPtypeList().isEmpty()) {
            if (isSuccess) {
                //指定赠品插入
                isSuccess = GetBeanUtil.getBean(PromotionService.class).insertPromotionPtypeList(promotionInfo.getSpecifiedGiftPtypeList(),
                        promotionInfo.getStrategy().get(1), PromotionPtypeType.SPECIFIED_GIFT.getValue(), promotionMapper);
            }
        }

        ///处理促销对象
        if (isSuccess) {
            if (promotionInfo.getRangValueList() != null && !promotionInfo.getRangValueList().isEmpty()) {
                promotionMapper.deletePromotionRangValuePromotionId(CurrentUser.getProfileId(), promotionInfo.getId());
                ///批量插入
                promotionInfo.getRangValueList().forEach(s -> {
                    s.setId(UId.newId());
                    s.setProfileId(CurrentUser.getProfileId());
                    if (s.getRangName() == null || s.getRangName().isEmpty()) {
                        s.setRangName("无");
                    }
                    if (s.getRangId() == null) {
                        s.setRangId(BigInteger.ZERO);
                    }
                });
                isSuccess = promotionMapper.insertPromotionRangValue(promotionInfo.getRangValueList());
            }
        }

        // 处理包邮促销地址数据
        if (isSuccess && promotionInfo.getPromotionType() == 8) { // 包邮促销类型
            // 先删除原有的地址数据
            promotionAddressMapper.deletePromotionAddressesByPromotionId(CurrentUser.getProfileId(), promotionInfo.getId());
            // 再插入新的地址数据
            isSuccess = handleFreeShippingAddresses(promotionInfo);
        }

        if (promotionInfo.getState() != 2) {
            promotionInfo.setStoped(false);
            promotionMapper.stopPromotion(promotionInfo);
        }

        savePromotionLog(promotionInfo, oldPromotion);
        return isSuccess;
    }


    //优先级变更
    public boolean exChangePromotionPriority(QueryPromotionRequest request) {
        request.setProfileId(CurrentUser.getProfileId());
        //查出需要交换的2条数据
        PromotionInfoExpand promotionInfoExpand = promotionMapper.getExchangePromotionInfo(request);
        List<BigInteger> temp = new ArrayList<>();
        temp.add(promotionInfoExpand.getExchangeId());
        temp.add(promotionInfoExpand.getId());
        //查完整促销信息
        List<PromotionInfo> promotionInfos = promotionMapper.getPromotionInfo(CurrentUser.getProfileId(), temp);
        //交换优先级
        int priority = promotionInfos.get(0).getPriority();
        promotionInfos.get(0).setPriority(promotionInfos.get(1).getPriority());
        promotionInfos.get(1).setPriority(priority);

        AtomicBoolean isSuccess = new AtomicBoolean(true);
        try {
            promotionInfos.forEach(data -> {
                //更新促销信息
                isSuccess.set(promotionMapper.updatePromotion(data));
            });
        } catch (Exception e) {
            return false;
        }
        return isSuccess.get();
    }

    //自动促销配置变更
    public boolean exChangePromotionAutomation(SysData params) {
        SysData request = new SysData();
        request.setDescription("促销赠品自动配置");
        request.setProfileId(CurrentUser.getProfileId());
        request.setSubName("ss_promotion_gift_pick_type");
        if (params.getSubValue().equals("1")) {
            request.setSubValue("1");
        } else {
            request.setSubValue("0");
        }
        eshopOrderBaseInfoMapper.addSysData(request);
        return true;
    }

    //自动促销配置获取
    public String getPromotionAutomation() {
        String response = eshopOrderBaseInfoMapper.getSysData(CurrentUser.getProfileId(),
                "ss_promotion_gift_pick_type");
        if (response == null) {
            return "0";
        } else {
            return response;
        }
    }

    //删除促销
    public boolean deletePromotion(BigInteger id) {
        return promotionMapper.deletePromotion(CurrentUser.getProfileId(), id);
    }

    //促销启用状态改变
    public boolean stopPromotion(PromotionInfo promotionInfo) {
        BaseInfoLog baseInfoLog = new BaseInfoLog();
        baseInfoLog.setObjectId(promotionInfo.getId());
        baseInfoLog.setBody("【编辑促销】 " + (promotionInfo.getStoped() ? "停用促销" : "启用促销"));
        saveLog(baseInfoLog);
        return promotionMapper.stopPromotion(promotionInfo);
    }


    List<PromotionStrategy> buildNewStrategy(int promotionType, PromotionInfo promotion) {
        List<PromotionStrategy> list = new ArrayList<>();
        PromotionStrategy ptypeStrategy = new PromotionStrategy();
        ptypeStrategy.setId(UId.newId());
        ptypeStrategy.setProfileId(CurrentUser.getProfileId());
        ptypeStrategy.setRowIndex(0);


        //活动商品
        if (promotionType == PromotionType.DDMJ.getCode()) {
            Map<String, Object> ptypeTyperule = new HashMap();
            ptypeTyperule.put("type", "0");
            ptypeTyperule.put("name", "促销商品");
            ptypeStrategy.setRule(JsonUtils.toJson(ptypeTyperule));
            ptypeStrategy.setStrategyType(0);
            ptypeStrategy.setPromotionId(promotion.getId());
            list.add(ptypeStrategy);
        } else {
            Map<String, Object> ptypeTyperule = new HashMap();
            ptypeTyperule.put("type", "1");
            ptypeTyperule.put("name", "促销商品");
            ptypeStrategy.setRule(JsonUtils.toJson(ptypeTyperule));
            ptypeStrategy.setStrategyType(0);
            ptypeStrategy.setPromotionId(promotion.getId());
            list.add(ptypeStrategy);
        }


        if (promotionType == PromotionType.SPTJ.getCode()) {
            PromotionStrategy promotonStyleStrategy = new PromotionStrategy();
            promotonStyleStrategy.setId(UId.newId());
            promotonStyleStrategy.setProfileId(CurrentUser.getProfileId());
            promotonStyleStrategy.setRowIndex(0);
            Map<String, Object> promotionStyleRule = new HashMap<>();
            promotionStyleRule.put("type", "0");
            promotionStyleRule.put("name ", "促销方式");
            promotonStyleStrategy.setRule(JsonUtils.toJson(promotionStyleRule));
            promotonStyleStrategy.setStrategyType(1);
            promotonStyleStrategy.setPromotionId(promotion.getId());
            list.add(promotonStyleStrategy);
        }

        if (promotionType == PromotionType.DDMJ.getCode() || promotionType == PromotionType.MJZ.getCode() || promotionType == PromotionType.MEZ.getCode()) {
            PromotionStrategy strategy = new PromotionStrategy();
            strategy.setId(UId.newId());
            strategy.setProfileId(CurrentUser.getProfileId());
            strategy.setRowIndex(0);
            Map<String, Object> rule = new HashMap<>();
            rule.put("type", "1");
            strategy.setRule(JsonUtils.toJson(rule));
            strategy.setStrategyType(1);
            strategy.setPromotionId(promotion.getId());
            list.add(strategy);
        }

        if (promotionType == PromotionType.DDMJ.getCode() || promotionType == PromotionType.NYG.getCode()) {
            PromotionStrategy strategy = new PromotionStrategy();
            strategy.setId(UId.newId());
            strategy.setProfileId(CurrentUser.getProfileId());
            strategy.setRowIndex(0);
            Map<String, Object> rule = new HashMap<>();
            rule.put("type", 0);
            rule.put("conditionList", new ArrayList<>());
            if (promotionType == PromotionType.MJZ.getCode() || promotionType == PromotionType.MEZ.getCode()) {
                rule.put("typeWay", "1");
            }
            strategy.setRule(JsonUtils.toJson(rule));
            strategy.setStrategyType(2);
            strategy.setPromotionId(promotion.getId());
            list.add(strategy);
        }

        if (promotionType == PromotionType.MJZ.getCode() || promotionType == PromotionType.MEZ.getCode()) {
            PromotionStrategy strategy = new PromotionStrategy();
            strategy.setId(UId.newId());
            strategy.setProfileId(CurrentUser.getProfileId());
            strategy.setRowIndex(0);
            Map<String, Object> rule = new HashMap<>();
            Map<String, Object> ruleDetail = new HashMap<>();
            List<Map<String, Object>> listTemp = new ArrayList<>();
            listTemp.add(new HashMap<>());
            ruleDetail.put("conditionList", listTemp);
            ruleDetail.put("typeWay", "1");
            ruleDetail.put("maxTotal", "1");
            ruleDetail.put("doubleSend", false);
            ruleDetail.put("type", 0);
            ruleDetail.put("remarks", "买赠同品");
            rule.put("0", ruleDetail);
            strategy.setRule(JsonUtils.toJson(rule));
            strategy.setStrategyType(2);
            strategy.setPromotionId(promotion.getId());
            list.add(strategy);

            if (promotion.getUseRange() == 3) {
                ///云订货
                PromotionStrategy strategyGiftRule = new PromotionStrategy();
                strategyGiftRule.setId(UId.newId());
                strategyGiftRule.setProfileId(CurrentUser.getProfileId());
                strategyGiftRule.setRowIndex(0);
                Map<String, Object> giftRule = new HashMap<>();
                giftRule.put("removeGoods", false);
                giftRule.put("outOfStock", false);
                strategyGiftRule.setRule(JsonUtils.toJson(giftRule));
                strategyGiftRule.setStrategyType(3);
                strategyGiftRule.setPromotionId(promotion.getId());
                list.add(strategyGiftRule);
            }
        }

        if (promotionType == PromotionType.JGZK.getCode()) {
            PromotionStrategy strategy = new PromotionStrategy();
            strategy.setId(UId.newId());
            strategy.setProfileId(CurrentUser.getProfileId());
            strategy.setRowIndex(0);
            strategy.setStrategyType(1);
            strategy.setPromotionId(promotion.getId());
            list.add(strategy);
        }

        // 添加包邮促销类型处理
        if (promotionType == PromotionType.BY.getCode()) {
            PromotionStrategy strategy = new PromotionStrategy();
            strategy.setId(UId.newId());
            strategy.setProfileId(CurrentUser.getProfileId());
            strategy.setRowIndex(0);

            // 创建包含订单范围和配送方式的规则
            Map<String, Object> rule = new HashMap<>();
            // 订单范围: 0=整单, 1=部分商品，默认选中整单
            rule.put("orderScopeAll", true);
            rule.put("orderScopePart", false);

            // 配送方式: express=快递物流, delivery=配送，默认都选中
            rule.put("express", true);
            rule.put("delivery", true);

            strategy.setRule(JsonUtils.toJson(rule));
            strategy.setStrategyType(2); // 设置strategyType为1
            strategy.setPromotionId(promotion.getId());
            list.add(strategy);
        }

        return list;
    }

    //获取促销 （新增时处理默认数据）
    public PromotionInfo getPromotionInfo(QueryPromotionRequest parameter) {

        PromotionInfo promotionInfo = new PromotionInfo();
        if (parameter != null && parameter.getPromotionId() != null && !parameter.getPromotionId().equals(BigInteger.ZERO)) {
            promotionInfo = getFullPromotionInfo(parameter.getPromotionId());

            promotionInfo.setMode(UPDATE_MODE);

        } else {
            promotionInfo.setId(UId.newId());
            promotionInfo.setMode(ADD_MODE);
            promotionInfo.setProfileId(CurrentUser.getProfileId());
        }
        return promotionInfo;
    }

    //获取单个完成的促销信息
    public PromotionInfo getFullPromotionInfo(BigInteger id) {
        List<PromotionInfo> promotionInfoList = this.getFullPromotionInfo(Arrays.asList(id));
        if (null == promotionInfoList || promotionInfoList.size() == 0) {
            return null;
        }
        return promotionInfoList.get(0);
    }


    //查询促销列表
    @PageDataSource
    public List<PromotionInfo> getPromotionList(PageRequest<QueryPromotionRequest> request) {
        return promotionMapper.getPromotionList(request.getQueryParams());
    }


    //endregion


    //region 促销策略策略

    //插入促销策略
    public boolean insertStrategy(PromotionStrategy strategy) {
        strategy.setProfileId(CurrentUser.getProfileId());
        return promotionMapper.insertStrategy(strategy);
    }

    //批量插入促销粗略
    public boolean insertStrategies(List<PromotionStrategy> list) {
        return promotionMapper.insertStrategies(list);
    }


    //修改促销策略
    public boolean updateStrategy(PromotionStrategy strategy) {
        return promotionMapper.updateStrategy(strategy);
    }

    //获取促销策略列表
    public List<PromotionStrategy> getStrategy(List<BigInteger> promotionIds) {
        return promotionMapper.selectStrategy(CurrentUser.getProfileId(), promotionIds);
    }

    //endregion

    //region 促销机构
    //插入促销机构
    public boolean insertPromotionOtype(PromotionFilterType promotionOtype) {
        promotionOtype.setProfileId(CurrentUser.getProfileId());
        return promotionMapper.insertPromotionOtype(promotionOtype);
    }

    //插入促销机构列表
    public boolean insertPromotionOtypes(List<PromotionFilterType> promotionOtype) {
        return promotionMapper.insertPromotionOtypes(promotionOtype);
    }

    //删除策略销售机构
    public boolean deletePromotionOtype(BigInteger promotionId) {
        return promotionMapper.deletePromotionOtype(CurrentUser.getProfileId(), promotionId);
    }

    //获取销策略售机构列表
    public List<PromotionFilterType> getPromotionOtype(List<BigInteger> promotionIds) {
        return promotionMapper.selectPromotionOtype(CurrentUser.getProfileId(), promotionIds);
    }
    //endregion

    //region 促销商品

    //插入促销商品
    public boolean insertPromotionPtypes(List<PromotionPtype> ptypeList) {
        return promotionMapper.insertPromotionPtypes(ptypeList);
    }

    //修改促销商品
    public boolean updatePromotionPtype(PromotionPtype ptype) {
        return promotionMapper.updatePromotionPtype(ptype);
    }

    //删除促销商品
    public boolean deletePromotionPtypes(BigInteger promotionId) {
        return promotionMapper.deletePromotionPtypes(CurrentUser.getProfileId(), promotionId);
    }

    //删除单个促销商品
    public boolean deletePromotionPtypeById(BigInteger id) {
        return promotionMapper.deletePromotionPtypeById(CurrentUser.getProfileId(), id);
    }

    public List<PromotionPtype> getPromotionPtypes(List<BigInteger> promotionIds) {

        List<PromotionPtype> ptypeList = promotionMapper.selectPromotionPtypes(CurrentUser.getProfileId(),
                promotionIds);
        // 获取套餐明细
        List<BigInteger> comboIds = ptypeList.stream().filter(PromotionPtype::getCombo).map(PromotionPtype::getPtypeId).distinct().collect(Collectors.toList());
        Map<BigInteger, List<PtypeResponse>> comboDetailsByIds;
        if (!comboIds.isEmpty()) {
            comboDetailsByIds = billService.getComboDetailsByIds(comboIds);
        } else {
            comboDetailsByIds = new HashMap<>();
        }
        ptypeList.forEach(data -> {

            if (!data.getBatchNos().isEmpty()) {
                ///以逗号分割为字符串赋值到batchNo
                data.setBatchNo(StringUtils.join(data.getBatchNos(), ","));
            }

            if (!comboDetailsByIds.isEmpty()) {
                List<PtypeResponse> comboDetails = comboDetailsByIds.get(data.getPtypeId());
                if (comboDetails != null && !comboDetails.isEmpty()) {
                    data.setComboDetail(comboDetails);
                }
            }
            if (data.getPtypeGroup() == 6) {
                List<SsCardTemplateOtype> otypeList = otypeService.getCardTemplateOtypes(data.getPid());
                List<String> otypeNames =
                        otypeList.stream().map(SsCardTemplateOtype::getFullName).collect(Collectors.toList());
                data.setOfullNames(StringUtils.join(otypeNames, ","));

                if (data.getValidType() == 0) {
                    data.setValidFullName("固定到期日期:" + data.getValidValue());
                }
                if (data.getValidType() == 1) {
                    data.setValidFullName("领卡日起" + data.getValidValue() + "天内有效");
                }
                if (data.getValidType() == 2) {
                    data.setValidFullName("永久有效");
                }
            }
        });

        return ptypeList;
    }

    /**
     * 清除旧规则中的重复商品
     *
     * @param clearPtypeList 促销商品表中已经存在的商品集合
     * @return
     */
    public Boolean clearRepeadPromotionPtypes(List<PromotionStrategyPtype> clearPtypeList) {
        List<BigInteger> existPtypeId =
                clearPtypeList.stream().map(item -> (item.getId())).collect(Collectors.toList());
        if (null == existPtypeId || existPtypeId.isEmpty()) {
            return true;
        }
        return promotionMapper.deletePromotionPtypesByIds(CurrentUser.getProfileId(), existPtypeId);
    }


    private String buildSkuUnitId(PromotionPtype ptype) {
        return ptype.getPid() + "_" + ptype.getSkuId() + "_" + ptype.getUnitId();
    }

    //检查商品中是否存在重复商品
    public List<PromotionStrategyPtype> existRepeatPromotion(PromotionPtypeRequest request) {
        Assert.isTrue(request != null && request.getPromotionInfo() != null, "促销信息不能为空");
        // 目前只有订单满减、积分兑换、N元组合购需要校验重复商品 （订单满减之前一直没校验过重复，所以这次也不用处理）
        int promotionType = request.getPromotionInfo().getPromotionType();
        if (promotionType != 4 && promotionType != 6) {
            return null;
        }

        request.setProfileId(CurrentUser.getProfileId());

        if (null !=
                request.getPromotionInfo().getPtypeGroup() && (request.getPromotionInfo().getPtypeGroup() == PtypeGroup.RIGHT_PTYPE.getValue() || request.getPromotionInfo().getPtypeGroup() == PtypeGroup.CARD_PTYPE.getValue())) {
            List<BigInteger> requestUnitIds =
                    request.getPromotionInfo().getPtypeList().stream().map(item -> item.getPid()).collect(Collectors.toList());
            return promotionMapper.getExistPromotionCard(request, requestUnitIds);
        }

        if (null !=
                request.getPromotionInfo().getPtypeGroup() && request.getPromotionInfo().getPtypeGroup() == PtypeGroup.RECHARGE_PTYPE.getValue()) {
            List<BigDecimal> requestUnitIds =
                    request.getPromotionInfo().getPtypeList().stream().map(item -> item.getPrice()).collect(Collectors.toList());
            List<PromotionStrategyPtype> list = promotionMapper.getExistPromotionRecharge(request, requestUnitIds);
            return list;
        }

        List<String> requestUnitIds =
                request.getPromotionInfo().getPtypeList().stream().map(item -> buildSkuUnitId(item)).collect(Collectors.toList());
        //获取部分商品的促销商品
        return promotionMapper.getExistPromotionPtype(request, requestUnitIds);
    }

    /**
     * 根据促销id(列表)查询促销信息
     *
     * @param promotionIds
     * @return
     */
    public List<PromotionInfo> getFullPromotionInfo(List<BigInteger> promotionIds) {
        if (promotionIds == null || promotionIds.isEmpty()) return new ArrayList<>();
        List<PromotionInfo> promotionList = promotionMapper.getPromotionInfo(CurrentUser.getProfileId(), promotionIds);
        List<PromotionFilterType> filterTypesList = this.getPromotionOtype(promotionIds);
        List<PromotionStrategy> strategyList = this.getStrategy(promotionIds);
        List<PromotionPtype> ptypeList = this.getPromotionPtypes(promotionIds);
        List<PromotionRangValue> rangValueList = promotionMapper.selectPromotionRangValue(CurrentUser.getProfileId(), promotionIds);
        // 如果是包邮促销，查询地址列表
        for (PromotionInfo promotionInfo : promotionList) {
            if (promotionInfo.getPromotionType() == 8) {
                List<PromotionAddress> addressList = promotionAddressMapper.selectPromotionAddressesByPromotionIds(
                        CurrentUser.getProfileId(), promotionIds);
                promotionInfo.setAddressList(addressList);
            }
        }

        List<PromotionPtype> promotionPtypeList =
                ptypeList.stream().filter(item -> item.getPromotionPtypeType() == PromotionPtypeType.MAIN.getValue()).collect(Collectors.toList());
        List<PromotionPtype> giftPtypeList =
                ptypeList.stream().filter(item -> item.getPromotionPtypeType() == PromotionPtypeType.GIFT.getValue()).collect(Collectors.toList());
        List<PromotionPtype> specifiedGiftPtypeList =
                ptypeList.stream().filter(item -> item.getPromotionPtypeType() == PromotionPtypeType.SPECIFIED_GIFT.getValue()).collect(Collectors.toList());
        Map<BigInteger, List<PromotionFilterType>> otypeGroupByPromotion =
                filterTypesList.stream().collect(Collectors.groupingBy(item -> item.getPromotionId()));
        Map<BigInteger, List<PromotionStrategy>> strategyGroupByPromotion =
                strategyList.stream().collect(Collectors.groupingBy(item -> item.getPromotionId()));
        Map<BigInteger, List<PromotionPtype>> ptypeGroupByPromotion =
                promotionPtypeList.stream().collect(Collectors.groupingBy(item -> item.getPromotionId()));
        Map<BigInteger, List<PromotionPtype>> giftGroupByPromotion =
                giftPtypeList.stream().collect(Collectors.groupingBy(item -> item.getPromotionId()));
        Map<BigInteger, List<PromotionPtype>> specifiedGiftGroupByPromotion =
                specifiedGiftPtypeList.stream().collect(Collectors.groupingBy(item -> item.getPromotionId()));

        for (int i = 0; i < promotionList.size(); i++) {
            PromotionInfo promotionInfoItem = promotionList.get(i);
            ///判断otypeGroupByPromotion是否存在
            if (otypeGroupByPromotion.containsKey(promotionInfoItem.getId())) {
                promotionInfoItem.setFilterTypesList(otypeGroupByPromotion.get(promotionInfoItem.getId()));
                List<BigInteger> filterIds =
                        otypeGroupByPromotion.get(promotionInfoItem.getId()).stream().map(PromotionFilterType::getFilterId).collect(Collectors.toList());
                List<String> filterNames =
                        otypeGroupByPromotion.get(promotionInfoItem.getId()).stream().map(PromotionFilterType::getFilterName).collect(Collectors.toList());
                promotionInfoItem.setFilterIds(filterIds);

            }


            ///找到rangValueList相同的promotionInfoItem.getId()数组
            promotionInfoItem.setRangValueList(rangValueList.stream().filter(item -> item.getPromotionId().equals(promotionInfoItem.getId())).collect(Collectors.toList()));
            List<String> rangValuesNames =
                    promotionInfoItem.getRangValueList().stream().map(PromotionRangValue::getRangName).collect(Collectors.toList());
            List<BigInteger> rangValues =
                    promotionInfoItem.getRangValueList().stream().map(PromotionRangValue::getRangId).collect(Collectors.toList());
            promotionInfoItem.setRangValues(rangValues);
            promotionInfoItem.setFilterNames(StringUtils.join(rangValuesNames, ","));
            promotionInfoItem.setPtypeList(ptypeGroupByPromotion.get(promotionInfoItem.getId()));
            promotionInfoItem.setGiftPtypeList(giftGroupByPromotion.get(promotionInfoItem.getId()));
            promotionInfoItem.setSpecifiedGiftPtypeList(specifiedGiftGroupByPromotion.get(promotionInfoItem.getId()));
            promotionInfoItem.setStrategy(strategyGroupByPromotion.get(promotionInfoItem.getId()));
            promotionInfoItem.setBtypeRangType(7);

            ///处理促销对象
            if (promotionInfoItem.getUseRange() == 1 && promotionInfoItem.getRangType() != 1) {
                promotionInfoItem.setBtypeRangType(promotionInfoItem.getRangType());
                promotionInfoItem.setRangTypeWholeSale(2);
            } else {
                promotionInfoItem.setRangTypeWholeSale(1);

            }
        }

        return promotionList;
    }

    /**
     * 获取POS端 积分兑换列表
     */
    public PageResponse<PromotionCredits> getPromotionCreditsList(PageRequest<PromotionCredits> request) {
        PromotionCredits promotionCredits = request.getQueryParams();
        PageDevice.initPage(request);
        promotionCredits.setProfileId(CurrentUser.getProfileId());
        Date date = new Date();
        promotionCredits.setCurrentTime(date);
        SimpleDateFormat dateFormat = new SimpleDateFormat("HH:mm:ss");
        promotionCredits.setCurrentHHSS(dateFormat.format(date));
        //获取促销积分商品
        List<PromotionCredits> promotionCreditsList = promotionMapper.selectPromotionCredits(promotionCredits);
        return PageDevice.readPage(promotionCreditsList);
    }


    /**
     * pos积分兑换接口
     */
    public CustomResult exchangeCreditsGoods(PromotionCreditsRequest request) throws ParseException {
        BigInteger vchcode = BigInteger.ZERO;
        Vchtypes vchtype = null;
        String billNumber = "";
        if (request.getPtypeGroup() == 2 || request.getPtypeGroup() == 3) {
            if (request.getGoodsBill() == null) {
                return new CustomResult(ResultCode.FAILED.getCode(), "未获取到单据");
            }
            try {
                Map<String, Object> goodsBill = request.getGoodsBill();
                goodsBill.put("payState", "Paied");
                goodsBill.put("whetherPost", true);
                goodsBill.put("sourceType", BillSourceTypeEnum.INTEGRAL_EXCHANGE.toString());
                if (goodsBill != null) {
                    GoodsBillDTO goodsBillDTO = JSONObject.parseObject(JSONObject.toJSONString(goodsBill),
                            GoodsBillDTO.class);
                    PosBill posBill = new PosBill();
                    // 这里先不保存会员资产，保存会员资产在下面的代码里，后续整合
                    posBill.setSaveVipAsserts(false);
                    posBill.setGoodsBill(goodsBillDTO);
                    BillSaveResultDTO orderbill = billInterface.submitBill(goodsBillDTO, posBill);
                    if (orderbill.getResultType() != BillSaveResultTypeEnum.SUCCESS) {
                        BillSaveExceptionDTO resultDTO = orderbill.getExceptionInfo().get(0);
                        return new CustomResult(ResultCode.FAILED.getCode(), resultDTO.getMessage());
                    }
                    vchcode = orderbill.getVchcode();
                    vchtype = orderbill.getVchtype();
                    billNumber = orderbill.getBillNumber();
//                    vipAssertsService.insertVipBill(null, request.getVipId(), request.getCashierId(),
//                            (String) goodsBill.get("vchtype"), (String) goodsBill.get("vchcode"));
                }
            } catch (Exception e) {
                return new CustomResult(ResultCode.FAILED.getCode(), e.getMessage());
            }
        }
        MemberAssertsChange dto = new MemberAssertsChange();
        List<VipAsserts> vipAssertsList = new ArrayList<>();
        VipAsserts vipAssert = new VipAsserts();
        vipAssert.setVipId(new BigInteger(request.getVipId()));
        List<MemberAssert> vipAsserts = new ArrayList<>();
        vipAsserts.add(MemberAssert.createData(0, request.getScore().negate(), AssertsChangeType.SCORE_EXCHANGE));
        // 普通权益卡
        if (request.getPtypeGroup() == 5) {
            vipAsserts.add(MemberAssert.createData(4,
                    BigDecimal.ONE,
                    VipAssertsBillDetailDto.getTypedMemo(4, BigDecimal.ONE),
                    null,
                    new BigInteger(request.getPid()),
                    AssertsChangeType.SCORE_EXCHANGE));
        }
        // 优惠券
        else if (request.getPtypeGroup() == 6) {
            vipAsserts.add(MemberAssert.createData(4,
                    BigDecimal.ONE,
                    VipAssertsBillDetailDto.getTypedMemo(4, BigDecimal.ONE),
                    null,
                    new BigInteger(request.getPid()),
                    AssertsChangeType.SCORE_EXCHANGE));
        }
        // 赠送储值
        else if (request.getPtypeGroup() == 7) {
            vipAsserts.add(MemberAssert.createData(2, request.getAmount(), AssertsChangeType.SCORE_EXCHANGE));
        }
        vipAssert.setVipAssert(vipAsserts);
        vipAssertsList.add(vipAssert);
        dto.setVipAsserts(vipAssertsList);
        dto.setVchcode(vchcode);
        dto.setVchtype(com.wsgjp.ct.framework.enums.Vchtypes.codeOf(vchtype.getCode()));
        dto.setBillNumber(billNumber);
        dto.setMemo("积分兑换");
        dto.setSourceOperation(AssertsSourceOperation.SCORE_EXCHANGE);
        // 积分兑换只有云零售有
        dto.setOperationSource(AssertsSourceType.POS);
        memberAssertsChangeService.vipAssertsChange(dto);

        // 这里是扣积分
//        ssVipScoreService.vipScoreGiveAmount(new BigInteger(request.getVipId()), BigDecimal.valueOf(0),
//                request.getScore().negate());

        //更新兑换次数
        PromotionPtype ptype = new PromotionPtype();
        ptype.setId(new BigInteger(request.getPromotionPtypeId()));
        ptype.setProfileId(CurrentUser.getProfileId());
        ptype.setChangeCount(request.getChangeCount() + 1);
        try {
            promotionMapper.updatePromotionChangeCount(ptype);
        } catch (RuntimeException e) {
            return new CustomResult(ResultCode.FAILED.getCode(), "商品兑换次数更新失败");
        }
//        promotionMapper.updatePromotionChangeCount(ptype);

        return new CustomResult(ResultCode.SUCCESS.getCode(), "兑换成功");
    }


    public void deletedScorePromotion(PromotionRequest requests) {
        List<BigInteger> promotionId = promotionOtypeMapper.getPromotionId(CurrentUser.getProfileId(),
                requests.getOtypes());
        if (CollectionUtils.isEmpty(promotionId)) {
            return;
        }
        promotionId = promotionMapper.getPromotionListId(promotionId, requests.getEndDate(), requests.getStartDate());
        if (CollectionUtils.isEmpty(promotionId)) {
            return;
        }
        if (Objects.equals(requests.getType(), 3)) {
            promotionPtypeMapper.deletedByPtypeId(requests.getPids(), CurrentUser.getProfileId(), promotionId);
            return;
        }
        promotionPtypeMapper.deletedByCardId(requests.getPids(), CurrentUser.getProfileId(), promotionId);
    }

    public void getImportPromotionPtypeTemplate(HttpServletResponse response, Integer promotionType) throws IOException {
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("促销商品导入模板", "UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
        Set<String> excludeColumnFiledNames = new HashSet<>();
        if (promotionType != 0) {
            excludeColumnFiledNames.add("batchNo");
            excludeColumnFiledNames.add("multiPreferential");
        }
        EasyExcel.write(response.getOutputStream(), PromotionImportPtype.class)
                .excludeColumnFiledNames(excludeColumnFiledNames) // 除商品特价外，其他促销类型都不展示批次号和促销价列
                .inMemory(Boolean.TRUE)  // 指定内存模式，支持富文本和批注
                .sheet()
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(30))
                .registerWriteHandler(new PromotionPtypeImportWriteHandler(promotionType))
                .doWrite(Collections.emptyList());
    }

    public String importPromotionPtypeByExcel(MultipartFile file, double maxValue, Integer promotionType) throws IOException {
        checkFilePrefix(file, "xls", "xlsx");
        RouteContext routeContext = RouteThreadLocal.getRoute();
        if (routeContext == null) {
            throw new RuntimeException("请设置routeContext");
        }
        // 促销类型默认0
//        if (promotionType == null) {
//            promotionType = 0;
//        }
        String localProcessKey = RedisBizUtils.getRequestId();
        ThreadPool threadPool = ThreadPoolFactory.build("shopsale");
        threadPool.executeAsync(processKey -> {
            if (org.apache.commons.lang.StringUtils.isEmpty(processKey)) {
                LOGGER.error("currentProcessKey不能为空");
            }
            //设置进度ProcessId值
            ProcessUtil.setLocalProcessKey(processKey);
            PromotionPtypeImportListener listener =
                    new PromotionPtypeImportListener(applicationContext.getBean(PromotionService.class), maxValue, promotionType);
            try {
                EasyExcel.read(file.getInputStream(), PromotionImportPtype.class, listener
                ).sheet().doRead();
            } catch (FileNotFoundException e) {
                ProcessUtil.appendError("文件上传失败,请重新上传");
            } catch (Exception e) {
                LOGGER.error("未处理异常", e);
                ProcessUtil.appendError(e.getMessage());
            } finally {
                if (ProcessUtil.canComplete()) {
                    ProcessUtil.complete(null);
                }
                RouteThreadLocal.remove();
            }
        }, localProcessKey);
        return localProcessKey;
    }


    public void addPtypeExcelToList(PromotionImportPtype importPtype, List<PromotionImportPtype> list,
                                    PromotionPtypeImportListener listener,
                                    double maxValue, AnalysisContext context, Integer promotionType) throws MethodArgumentNotValidException {
        try {
            if (promotionType == 0 && importPtype.getMultiPreferential() == null) {
                listener.invokeError(importPtype, "促销价/促销折扣未填写", context);
            }
            //处理属性冒号的问题，数据库存的是中文符号
            if (importPtype.getPropValues() != null) {
                importPtype.setPropValues(importPtype.getPropValues().replace(":", "："));
            }
            if (importPtype.getBatchNo() != null) {
                importPtype.setBatchNo(importPtype.getBatchNo().replace("；", ";"));
            }
            //强匹配
            List<PromotionImportPtype> promotionImportPtypes =
                    promotionPtypeMapper.selectPtypeFromImport(CurrentUser.getProfileId(), importPtype);
            promotionImportPtypes.forEach(e -> {
                if (importPtype.getMultiPreferential() != null) {
                    e.setMultiPreferential(importPtype.getMultiPreferential());
                }
                if (importPtype.getBatchNo() != null && e.isBatchenabled()) {
                    e.setBatchNo(importPtype.getBatchNo());
                }
            });
            if (promotionImportPtypes.isEmpty()) {
                listener.invokeError(importPtype, "未获取到商品信息/不存在此商品SKU", context);
            }


            Map<String, PromotionImportPtype> listMap = list.stream()
                    .collect(Collectors.toMap(
                            e -> e.getPtypeId() + "_" + e.getUnitId() + "_" + e.getSkuId(),
                            Function.identity(),
                            (existing, replacement) -> existing
                    ));

            List<PromotionImportPtype> promotionImportPtypesTemp = new ArrayList<>();
            for (int i = 0; i < promotionImportPtypes.size(); i++) {
                PromotionImportPtype ptype = promotionImportPtypes.get(i);
                LOGGER.error("开始导入促销商品：用户profileId{},商品{}", CurrentUser.getProfileId(), ptype);
//                Optional<PromotionImportPtype> findOptional = list.stream().filter(e -> Objects.equals(e.getPtypeId()
//                        , ptype.getPtypeId()) && Objects.equals(e.getUnitId(), ptype.getUnitId()) && Objects.equals(ptype.getSkuId(), e.getSkuId())).findAny();


                Optional<PromotionImportPtype> findOptional = Optional.empty();
                for (PromotionImportPtype e : list) {
                    if ((e.getPtypeId() == null ? ptype.getPtypeId() == null : e.getPtypeId().equals(ptype.getPtypeId()))
                            && (e.getUnitId() == null ? ptype.getUnitId() == null : e.getUnitId().equals(ptype.getUnitId()))
                            && (ptype.getSkuId() == null ? e.getSkuId() == null : ptype.getSkuId().equals(e.getSkuId()))) {
                        findOptional = Optional.of(e);
                        break;
                    }
                }

                String key = ptype.getPtypeId() + "_" + ptype.getUnitId() + "_" + ptype.getSkuId();

                if (findOptional.isPresent() || listMap.containsKey(key)) {
                    listener.invokeError(importPtype, "重复商品", context);
                    LOGGER.error("导入促销商品重复：用户profileId{},商品{}", CurrentUser.getProfileId(), ptype);
                } else if (promotionType == 0 && StringUtils.isNotBlank(importPtype.getMultiPreferential()) &&
                        Double.valueOf(importPtype.getMultiPreferential()) > maxValue) {
                    listener.invokeError(importPtype, "促销价/促销折扣超过最大值", context);
                } else if (importPtype.getBatchNo() != null && !importPtype.getBatchNo().isEmpty()) {
                    if (!ptype.isBatchenabled()) {
                        listener.invokeAbnormal(importPtype, "没有开启批次管理,批次信息已清空", context);
                        ptype.setBatchNo("");
                    } else {
                        List<String> batchs = promotionMapper.getExistPtypeBatch(CurrentUser.getProfileId(), ptype.getPtypeId(), ptype.getSkuId());
                        //以逗号分割字符串转成数组 ptype.getBatchNo()
                        List<String> batchsPtype = Arrays.asList(importPtype.getBatchNo().trim().split(";"));
                        // 创建 HashSet 来存储相同的数据
                        Set<String> commonElements = new HashSet<>(batchs);
                        // 保留两个 List 中都存在的元素
                        commonElements.retainAll(batchsPtype);
                        if (commonElements.isEmpty()) {
                            listener.invokeAbnormal(importPtype, "此商品不存在所填写的批次,批次信息已清空", context);
                            ptype.setBatchNo("");
                        } else if (commonElements.size() != batchsPtype.size()) {
                            listener.invokeAbnormal(importPtype, "此商品已过滤掉不存在的批次", context);
                            //把commonElements转成字符串
                            ptype.setBatchNo(String.join(",", commonElements));
                        } else {
                            ptype.setBatchNo(String.join(",", commonElements));
                        }

                    }
                    promotionImportPtypesTemp.add(ptype);
                } else {
                    promotionImportPtypesTemp.add(ptype);
                    LOGGER.error("最终导入促销商品：用户profileId{},商品{}", CurrentUser.getProfileId(), ptype);
                }

            }


            list.addAll(promotionImportPtypesTemp);
        } catch (Exception e) {
            ProcessUtil.appendError("文件上传失败,商品明细获取失败");
        }
    }

    /**
     * 自定义表头
     *
     * @return
     */
    private List<List<String>> myriadPeopleHead() {
        List<List<String>> list = new ArrayList<>();
        List<String> head0 = new ArrayList<>();
        head0.add("商品匹配条件: 条码、SKU编号、商品名称、商品编号；支持只录入任一条件信息进行匹配；如果录入多条信息，则都匹配上才会导入；");
        head0.add("按条码或SKU编号匹配时，无需填写属性组合和单位信息；");
        head0.add("按商品名称或商品编号匹配时，属性商品未填属性组合，则默认导入所有属性组合；多单位商品未填写单位则默认导入所有单位");

        list.add(head0);
        return list;
    }

    /**
     * 处理包邮促销地址数据
     *
     * @param promotionInfo 促销信息
     * @return 是否成功
     */
    private boolean handleFreeShippingAddresses(PromotionInfo promotionInfo) {
        // 检查是否有地址数据
        if (promotionInfo.getAddressList() == null || promotionInfo.getAddressList().isEmpty()) {
            return true; // 没有地址数据，直接返回成功
        }

        try {
            // 准备地址数据
            List<PromotionAddress> addressList = new ArrayList<>();

            for (PromotionAddress address : promotionInfo.getAddressList()) {
                // 设置必要的字段
                address.setId(UId.newId());
                address.setProfileId(CurrentUser.getProfileId());
                address.setPromotionId(promotionInfo.getId());
                address.setDeleted(false);

                addressList.add(address);
            }

            // 批量插入地址数据
            if (!addressList.isEmpty()) {
                return promotionAddressMapper.insertPromotionAddresses(addressList);
            }

            return true;
        } catch (Exception e) {
            LOGGER.error("处理包邮促销地址数据失败", e);
            return false;
        }
    }

    /**
     * 根据促销ID查询包邮地址列表
     *
     * @param promotionId 促销ID
     * @return 包邮地址列表
     */
    public List<PromotionAddress> getPromotionAddressesByPromotionId(BigInteger promotionId) {
        if (promotionId == null) {
            return new ArrayList<>();
        }
        return promotionAddressMapper.selectPromotionAddressesByPromotionId(CurrentUser.getProfileId(), promotionId);
    }

    /**
     * 云订货查询促销信息
     * 支持多种查询方式：
     * 1. 根据促销id列表查询完整促销信息
     * 2. 根据otypeId查询相关促销信息（ptypeIds可选）
     * 3. 根据otypeId + ptypeIds查询相关促销信息
     * 4. 根据btypeId + otypeId查询相关促销信息（ptypeIds可选）
     * 5. 根据btypeId + otypeId + ptypeIds查询相关促销信息
     * <p>
     * 查询逻辑：
     * - SQL中同时查询指定条件的促销和全部促销（rangType=1）
     * - 一次查询完成，避免多次数据库交互，提高性能
     *
     * @param request 查询参数
     * @return 促销信息列表
     */
    public List<PromotionInfo> getCloudOrderPromotionInfo(QueryPromotionRequest request) {
        // 设置云订货查询的默认参数
        request.setState(1);        // 默认为1（进行中）
        request.setStoped(0);       // 默认为0（未停用）
        request.setUseRange(1);     // 默认为1（云订货）
        request.setPromotionType(-1); // 默认为-1（不限制类型）
        request.setProfileId(CurrentUser.getProfileId());

        List<BigInteger> promotionIds = request.getPromotionIds();

        // 如果传入了促销id列表，直接根据id查询完整促销信息
        if (promotionIds != null && !promotionIds.isEmpty()) {
            return getFullPromotionInfo(promotionIds);
        }

        // 如果传入了otypeId，或者btypeId + otypeId，先查询促销id列表，再获取完整信息
        // ptypeIds 可以为空，表示不限制商品
        if (request.getOtypeId() != null) {
            // 如果传入了btypeId，需要先获取往来单位的分类信息
            if (request.getBtypeId() != null) {
                List<String> btypeTypeIds = getBtypeTypeIdById(request.getBtypeId());
                request.setBtypeTypeIds(btypeTypeIds);
            }

            // 1. 通过getPromotionList查询到促销id列表（SQL中已包含全部促销的查询逻辑）
            List<PromotionInfo> promotionList = promotionMapper.getPromotionList(request);
            if (promotionList == null || promotionList.isEmpty()) {
                return new ArrayList<>();
            }

            // 2. 提取促销id列表
            List<BigInteger> foundPromotionIds = promotionList.stream()
                    .map(PromotionInfo::getId)
                    .collect(Collectors.toList());

            // 3. 根据促销id列表获取完整的促销信息
            return getFullPromotionInfo(foundPromotionIds);
        }

        // 参数不足，返回空列表
        return new ArrayList<>();
    }

    /**
     * 根据促销类型和ID获取促销商品列表
     * 如果类型不传，就按照id查询
     * 如果id不传，按照促销类型查询
     * 如果都传，按照所有条件都满足查询
     *
     * @param request 查询参数
     * @return 促销商品列表
     */
    public List<PromotionPtype> getPromotionPtypesByTypeAndId(QueryPromotionRequest request) {
        ///目前只支持云订货 设置前置条件
        request.setUseRange(3);
        request.setState(-1);
        request.setStoped(-1);
        request.setProfileId(CurrentUser.getProfileId());

        int promotionType = request.getPromotionType();
        BigInteger promotionId = request.getPromotionId();
        List<BigInteger> promotionIds = request.getPromotionIds();

        List<BigInteger> resultPromotionIds = new ArrayList<>();

        // 如果 promotionType 小于 0，表示不限制类型
        boolean hasPromotionType = promotionType >= 0;

        if (!hasPromotionType) {
            // 如果不限制类型，直接使用promotionId或promotionIds
            if (promotionId != null && !promotionId.equals(BigInteger.ZERO)) {
                resultPromotionIds.add(promotionId);
            }
            if (promotionIds != null && !promotionIds.isEmpty()) {
                resultPromotionIds.addAll(promotionIds);
            }
        } else {
            // 如果限制类型，查询符合该类型的促销ID
            List<PromotionInfo> promotionList = promotionMapper.getPromotionList(request);

            // 如果promotionId或promotionIds不为空，则需要过滤出同时满足promotionType和promotionId/promotionIds的促销
            if ((promotionId != null && !promotionId.equals(BigInteger.ZERO)) ||
                    (promotionIds != null && !promotionIds.isEmpty())) {

                List<BigInteger> filterIds = new ArrayList<>();
                if (promotionId != null && !promotionId.equals(BigInteger.ZERO)) {
                    filterIds.add(promotionId);
                }
                if (promotionIds != null && !promotionIds.isEmpty()) {
                    filterIds.addAll(promotionIds);
                }

                resultPromotionIds = promotionList.stream()
                        .filter(p -> filterIds.contains(p.getId()))
                        .map(PromotionInfo::getId)
                        .collect(Collectors.toList());
            } else {
                // 否则，直接使用所有符合promotionType的促销ID
                resultPromotionIds = promotionList.stream()
                        .map(PromotionInfo::getId)
                        .collect(Collectors.toList());
            }
        }

        // 如果没有符合条件的促销ID，返回空列表
        if (resultPromotionIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 调用现有的getPromotionPtypes方法
        List<PromotionPtype> ptypeList = getPromotionPtypes(resultPromotionIds);
        List<PromotionPtype> promotionPtypeList =
                ptypeList.stream().filter(item -> item.getPromotionPtypeType() == PromotionPtypeType.MAIN.getValue()).collect(Collectors.toList());
        return promotionPtypeList;
    }

    /**
     * pos批量积分兑换接口
     */
    @Transactional(rollbackFor = Exception.class)
    public BigInteger batchExchangeCreditsGoods(List<PromotionCreditsRequest> requests) throws ParseException {
        // 参数校验
        if (requests == null || requests.isEmpty()) {
            throw new RuntimeException("兑换商品列表不能为空");
        }

        BigInteger vchcode = BigInteger.ZERO;
        Vchtypes vchtype = null;
        String billNumber = "";

        // 检查是否包含商品类型的兑换
        boolean hasGoodsExchange = requests.stream()
                .anyMatch(item -> item.getPtypeGroup() == 2 || item.getPtypeGroup() == 3);

        // 处理商品出库单
        if (hasGoodsExchange) {
            try {
                // 获取所有商品类请求
                List<PromotionCreditsRequest> goodsRequests = requests.stream()
                        .filter(item -> item.getPtypeGroup() == 2 || item.getPtypeGroup() == 3)
                        .collect(Collectors.toList());

                if (goodsRequests.isEmpty() || goodsRequests.get(0).getGoodsBill() == null) {
                    throw new RuntimeException("未获取到单据");
                }

                // 使用第一个请求的 goodsBill 作为模板
                Map<String, Object> goodsBill = goodsRequests.get(0).getGoodsBill();
                goodsBill.put("payState", "Paied");
                goodsBill.put("sourceType", BillSourceTypeEnum.INTEGRAL_EXCHANGE.toString());

                // 转换为 GoodsBillDTO
                GoodsBillDTO goodsBillDTO = JSONObject.parseObject(JSONObject.toJSONString(goodsBill),
                        GoodsBillDTO.class);

                // 清空出库明细，准备重新添加
                goodsBillDTO.setOutDetail(new ArrayList<>());

                // 处理所有商品请求的明细
                for (PromotionCreditsRequest request : goodsRequests) {
                    if (request.getGoodsBill() == null) {
                        continue;
                    }

                    // 获取请求中的明细
                    GoodsBillDTO requestDTO = JSONObject.parseObject(
                            JSONObject.toJSONString(request.getGoodsBill()),
                            GoodsBillDTO.class);

                    if (requestDTO.getOutDetail() != null && !requestDTO.getOutDetail().isEmpty()) {
                        // 处理数量
                        if (request.getQuantity() > 1) {
                            for (GoodsDetailDTO detail : requestDTO.getOutDetail()) {
                                // 更新单位数量
                                if (detail.getUnitQty() != null) {
                                    detail.setUnitQty(detail.getUnitQty().multiply(BigDecimal.valueOf(request.getQuantity())));
                                }

                                // 更新库存数量
                                if (detail.getStockQty() != null) {
                                    detail.setStockQty(detail.getStockQty().multiply(BigDecimal.valueOf(request.getQuantity())));
                                }
                            }
                        }

                        // 添加到主单据
                        goodsBillDTO.getOutDetail().addAll(requestDTO.getOutDetail());
                    }
                }

                // 提交单据
                PosBill posBill = new PosBill();
                posBill.setSaveVipAsserts(false);
                posBill.setGoodsBill(goodsBillDTO);
                ///不核算
                goodsBillDTO.setWhetherPost(false);
                ///同步保存
                goodsBillDTO.setAsyncSave(false);
                BillSaveResultDTO orderbill = billService.submitGoodsBill(posBill);
                if (orderbill.getResultType() != BillSaveResultTypeEnum.SUCCESS) {
                    BillSaveExceptionDTO resultDTO = orderbill.getExceptionInfo().get(0);
                    throw new RuntimeException(resultDTO.getMessage());
                }
                vchcode = orderbill.getVchcode();
                vchtype = orderbill.getVchtype();
                billNumber = orderbill.getBillNumber();
            } catch (Exception e) {
                throw new RuntimeException("处理商品出库单失败", e);
            }
        }

        // 计算总积分
        BigDecimal totalScore = requests.stream()
                .map(item -> item.getScore().multiply(BigDecimal.valueOf(item.getQuantity())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 构建会员资产变动对象
        MemberAssertsChange dto = new MemberAssertsChange();
        List<VipAsserts> vipAssertsList = new ArrayList<>();
        VipAsserts vipAssert = new VipAsserts();
        vipAssert.setVipId(new BigInteger(requests.get(0).getVipId()));
        List<MemberAssert> vipAsserts = new ArrayList<>();

        // 添加积分扣减记录（合并为一条）
        vipAsserts.add(MemberAssert.createData(0, totalScore.negate(), AssertsChangeType.SCORE_EXCHANGE));

        // 处理每个兑换项的资产变动
        for (PromotionCreditsRequest item : requests) {
            // 普通权益卡
            if (item.getPtypeGroup() == 5) {
                // 根据数量重复添加
                for (int i = 0; i < item.getQuantity(); i++) {
                    vipAsserts.add(MemberAssert.createData(4,
                            BigDecimal.ONE,
                            VipAssertsBillDetailDto.getTypedMemo(4, BigDecimal.ONE),
                            null,
                            new BigInteger(item.getPid()),
                            AssertsChangeType.SCORE_EXCHANGE));
                }
            }
            // 优惠券
            else if (item.getPtypeGroup() == 6) {
                // 根据数量重复添加
                for (int i = 0; i < item.getQuantity(); i++) {
                    vipAsserts.add(MemberAssert.createData(4,
                            BigDecimal.ONE,
                            VipAssertsBillDetailDto.getTypedMemo(4, BigDecimal.ONE),
                            null,
                            new BigInteger(item.getPid()),
                            AssertsChangeType.SCORE_EXCHANGE));
                }
            }
            // 赠送储值
            else if (item.getPtypeGroup() == 7) {
                // 储值金额乘以数量
                vipAsserts.add(MemberAssert.createData(2,
                        item.getAmount().multiply(BigDecimal.valueOf(item.getQuantity())),
                        AssertsChangeType.SCORE_EXCHANGE));
            }
        }
        vipAssert.setVipAssert(vipAsserts);
        vipAssertsList.add(vipAssert);
        dto.setVipAsserts(vipAssertsList);
        dto.setVchcode(vchcode);
        if (vchtype != null) {
            dto.setVchtype(com.wsgjp.ct.framework.enums.Vchtypes.codeOf(vchtype.getCode()));
        }
        dto.setBillNumber(billNumber);
        dto.setMemo("批量积分兑换");
        dto.setSourceOperation(AssertsSourceOperation.SCORE_EXCHANGE);
        dto.setOperationSource(AssertsSourceType.POS);

        // 处理会员资产变动
        memberAssertsChangeService.vipAssertsChange(dto);

        // 更新兑换次数
        for (PromotionCreditsRequest item : requests) {
            PromotionPtype ptype = new PromotionPtype();
            ptype.setId(new BigInteger(item.getPromotionPtypeId()));
            ptype.setProfileId(CurrentUser.getProfileId());
            ptype.setChangeCount(item.getChangeCount() + item.getQuantity());
            promotionMapper.updatePromotionChangeCount(ptype);
        }

        // 直接返回vchcode
        return vchcode;
    }

    /**
     * 批量积分兑换接口(带核算)，在非事务环境下调用此方法
     * 先执行批量兑换，然后进行单据核算
     */
    @AddLogs
    public CustomResult batchExchangeCreditsGoodsWithPost(List<PromotionCreditsRequest> requests) throws ParseException {
        // 获取当前PromotionService的代理对象，以确保事务正常工作
        PromotionService promotionService = GetBeanUtil.getBean(PromotionService.class);
        // 获取vchcode
        BigInteger vchcode = promotionService.batchExchangeCreditsGoods(requests);
        try {
            // 如果有有效的单据ID，则进行核算
            if (vchcode != null && !vchcode.equals(BigInteger.ZERO)) {
                goodsBillService.postBill(vchcode);
            }
            addBillLogs.addPostBillLog(vchcode, true, null, "积分兑换", null,
                    false);
        } catch (Exception e) {
            addBillLogs.addPostBillLog(vchcode, false, e.getMessage(), "积分兑换", null,
                    false);
        }
        return new CustomResult(ResultCode.SUCCESS.getCode(), "兑换成功");

    }

    /**
     * 获取往来单位的所有分类
     */
    private List<String> getBtypeTypeIdById(BigInteger btypeId) {
        String typeId = promotionMapper.getBtypeTypeId(CurrentUser.getProfileId(), btypeId);
        List<String> typeIdList = new ArrayList<>();
        if (typeId != null && typeId.length() > 5) {
            while (typeId.length() > 5) {
                typeId = typeId.substring(0, typeId.length() - 5);
                typeIdList.add(typeId);
            }
        }
        return typeIdList;
    }

}


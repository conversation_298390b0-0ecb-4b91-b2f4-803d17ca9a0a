package com.wsgjp.ct.sale.biz.eshoporder.entity.base;


import com.wsgjp.ct.bill.core.handle.entity.enums.BillBusinessType;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.OrganizationType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.TaskType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StoreType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopConfig;
import com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopOrderSyncCondition;
import com.wsgjp.ct.sale.common.entity.EshopInfo;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description 机构表信息
 */
public class Otype {

	private BigInteger profileId;
	private BigInteger id;
	private BigInteger pid;
	private String usercode;
	private String fullname;
	private String people;
	private OrganizationType ocategory;
	private int ocategoryUI;
	private BigInteger ktypeId;
	private String ktypeName;
	private BigInteger btypeId;
	private String btypeName;
	private BigDecimal btypePrTotal;
	private BigInteger classId;
	private boolean stoped;
	private boolean deleted;
	private boolean tmallSpecialSale;
	private Date createTime;
	private Date updateTime;
	private String memo;
	private int accountType;
	private int expireNotice;
	private int checkAccountType;
	private boolean independentCheck;
	private boolean autoShelfOn;
	private String typeid;
	private String partypeid;


	/**
	 * 扩展表内容
	 */
	private BigInteger atypeId;
	private String payAccount;
	private BigInteger currencyId;
	private int deliverDuration;
	/**
	 * 承诺签收时间
	 */
	private int promisedSignDuration;
	/**
	 * 承诺揽收时间
	 */
	private int promisedCollectDuration;
	/**
	 *承诺最晚同步物流单号时间
	 */
	private int promisedSyncFreightDuration;
	private boolean ptypeAutoUploadEnabled;
	private boolean btypeAutoUploadEnabled;
	private boolean ktypeAutoUploadEnabled;
	private boolean invoiceUploadEnabled;
	private Integer refundSysPromisedConfirmDuration;
	private Integer refundPromisedConfirmDuration;
	private Integer refundPromisedAgreeDuration;
	private Integer refundPromisedDeliverDuration;
	private Integer refundPromisedReceiveDuration;
	private int mentionDeliverDuration;
	private String deliverDurationTxt;
	private String senderName;
	private String cellphone;
	private String telephone;
	private String address;
	private String province;
	private String city;
	private String district;
	private String street;
	private BigInteger rowindex;
	/**
	 *
	 */
	private Date tokenR1ExpireIn;
	private Date tokenExpireIn;

	private Date refreshTokenExpireIn;
	private String token;
	private String refreshToken;


	/**
	 * UI显示要用的字段
	 */
	private String orderLink;
	private String refundLink;
	private String authLink;
	private int authType;
	private String operation;
	private boolean selected;
	private StoreType storeType;
	private BillBusinessType businessType;
	private Integer btypeGenerateType;
	/**
	 * 1=上移
	 * 2=下移
	 * 3=置顶
	 * 4=置底
	 */
	private int changeOrderIndexType;

	private int refundShopType;
	private int maxIndex;

	private int eshopType;

	private ShopType shopType;
	private int deliverProcessType;
	private int processType;
	private int eshopSalePlatform;
	private String eshopAccount;

	//授权标记
	private String eshopAuthMark;

	private EshopInfo eshopInfo;
	private EshopConfig eshopConfig;
	private List<EshopOrderSyncCondition> condition;


	//自动下单配置的字段
	private boolean isAuth;
	private boolean btnChange;
	private Date firstDownloadTime;
	private Date lastDownloadTime;
	private Date lastDownloadSuccessEndTime;
	private String authState;
	private String authTag;
	private Integer autoSync;

	//开启AG
	private Boolean agEnabled;
	private Boolean tmcEnabled;
	private Boolean realStockQtyEnabled;

	private Integer mutiSelectAppkey;
	private String onlineEshopId;

	private String appKey;

	//网店设置微盟判断1.0 2.0
	private String platformEshopSnType;

	private boolean autoSyncState;
	private String tmcEnabledState;
	private String groupId;
	private boolean mainEshop;
	/**
	 *预约签收/送达的周期购订单预计提前多少小时
	 */
	private Integer planSendTimeDuration;

	/**
	 * 网店仓库对应在用
	 *
	 */
	private boolean  platformStoreSupport;

	public boolean isPlatformStoreSupport() {
		return platformStoreSupport;
	}

	public void setPlatformStoreSupport(boolean platformStoreSupport) {
		this.platformStoreSupport = platformStoreSupport;
	}

	public Integer getPlanSendTimeDuration() {
		return planSendTimeDuration;
	}

	public void setPlanSendTimeDuration(Integer planSendTimeDuration) {
		this.planSendTimeDuration = planSendTimeDuration;
	}

	public String getPeople() {
		return people;
	}

	public void setPeople(String people) {
		this.people = people;
	}

	public String getStreet() {
		return street;
	}

	public void setStreet(String street) {
		this.street = street;
	}

	public String getCellphone() {
		return cellphone;
	}

	public void setCellphone(String cellphone) {
		this.cellphone = cellphone;
	}

	public String getTelephone() {
		return telephone;
	}

	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}

	public boolean isMainEshop() {
		return mainEshop;
	}

	public void setMainEshop(boolean mainEshop) {
		this.mainEshop = mainEshop;
	}

	public String getTmcEnabledState() {
		return tmcEnabledState;
	}

	public void setTmcEnabledState(String tmcEnabledState) {
		this.tmcEnabledState = tmcEnabledState;
	}

	public boolean getAutoSyncState() {
		return autoSyncState;
	}

	public void setAutoSyncState(boolean autoSyncState) {
		this.autoSyncState = autoSyncState;
	}

	public boolean isTmallSpecialSale() {
		return tmallSpecialSale;
	}

	public void setTmallSpecialSale(boolean tmallSpecialSale) {
		this.tmallSpecialSale = tmallSpecialSale;
	}

	public String getRefreshToken() {
		return refreshToken;
	}

	public void setRefreshToken(String refreshToken) {
		this.refreshToken = refreshToken;
	}

	public Boolean getRealStockQtyEnabled() {
		if(null == realStockQtyEnabled){
			realStockQtyEnabled = false;
		}
		return realStockQtyEnabled;
	}

	public void setRealStockQtyEnabled(Boolean realStockQtyEnabled) {
		this.realStockQtyEnabled = realStockQtyEnabled;
	}

	public int getPromisedSyncFreightDuration() {
		return promisedSyncFreightDuration;
	}

	public void setPromisedSyncFreightDuration(int promisedSyncFreightDuration) {
		this.promisedSyncFreightDuration = promisedSyncFreightDuration;
	}

	public boolean isAutoShelfOn() {
		return autoShelfOn;
	}

	public void setAutoShelfOn(boolean autoShelfOn) {
		this.autoShelfOn = autoShelfOn;
	}

	public int getDeliverProcessType() {
		return deliverProcessType;
	}

	public void setDeliverProcessType(int deliverProcessType) {
		this.deliverProcessType = deliverProcessType;
	}

	public int getProcessType() {
		return processType;
	}

	public void setProcessType(int processType) {
		this.processType = processType;
	}

	public Date getLastDownloadSuccessEndTime() {
		return lastDownloadSuccessEndTime;
	}

	public void setLastDownloadSuccessEndTime(Date lastDownloadSuccessEndTime) {
		this.lastDownloadSuccessEndTime = lastDownloadSuccessEndTime;
	}

	public Integer getAutoSync() {
		return autoSync;
	}

	public void setAutoSync(Integer autoSync) {
		this.autoSync = autoSync;
	}

	public boolean isBtnChange() {
		return btnChange;
	}

	public void setBtnChange(boolean btnChange) {
		this.btnChange = btnChange;
	}

	public boolean isAuth() {
		return isAuth;
	}

	public void setAuth(boolean auth) {
		isAuth = auth;
	}

	public Date getFirstDownloadTime() {
		return firstDownloadTime;
	}

	public void setFirstDownloadTime(Date firstDownloadTime) {
		this.firstDownloadTime = firstDownloadTime;
	}

	public Date getLastDownloadTime() {
		return lastDownloadTime;
	}

	public void setLastDownloadTime(Date lastDownloadTime) {
		this.lastDownloadTime = lastDownloadTime;
	}

	public String getAuthTag() {
		return authTag;
	}

	public void setAuthTag(String authTag) {
		this.authTag = authTag;
	}

	public String getAuthState() {
		return authState;
	}

	public void setAuthState(String authState) {
		this.authState = authState;
	}

	/**
	 * 其他界面选择要使用的字段
	 *
	 * @return
	 */
	private MappingType mappingType;

	public BigInteger getProfileId() {
		return profileId;
	}

	public void setProfileId(BigInteger profileId) {
		this.profileId = profileId;
	}

	public BigInteger getId() {
		return id;
	}

	public void setId(BigInteger id) {
		this.id = id;
	}

	public String getUsercode() {
		return usercode;
	}

	public void setUsercode(String usercode) {
		this.usercode = usercode;
	}

	public String getFullname() {
		return fullname;
	}

	public void setFullname(String fullname) {
		this.fullname = fullname;
	}

	public OrganizationType getOcategory() {
		if (ocategory == null) {
			return OrganizationType.ZY_SHOP;
		}
		return ocategory;
	}

	public void setOcategory(OrganizationType ocategory) {
		this.ocategory = ocategory;
	}

	public BigInteger getKtypeId() {
		return ktypeId;
	}

	public void setKtypeId(BigInteger ktypeId) {
		this.ktypeId = ktypeId;
	}

	public BigInteger getBtypeId() {
		return btypeId;
	}

	public void setBtypeId(BigInteger btypeId) {
		this.btypeId = btypeId;
	}

	public BigInteger getClassId() {
		return classId;
	}

	public void setClassId(BigInteger classId) {
		this.classId = classId;
	}

	public boolean isStoped() {
		return stoped;
	}

	public void setStoped(boolean stoped) {
		this.stoped = stoped;
	}

	public boolean isDeleted() {
		return deleted;
	}

	public void setDeleted(boolean deleted) {
		this.deleted = deleted;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public int getAccountType() {
		return accountType;
	}

	public void setAccountType(int accountType) {
		this.accountType = accountType;
	}

	public int getCheckAccountType() {
		return checkAccountType;
	}

	public void setCheckAccountType(int checkAccountType) {
		this.checkAccountType = checkAccountType;
	}

	public BigInteger getAtypeId() {
		return atypeId;
	}

	public void setAtypeId(BigInteger atypeId) {
		this.atypeId = atypeId;
	}

	public String getPayAccount() {
		return payAccount;
	}

	public void setPayAccount(String payAccount) {
		this.payAccount = payAccount;
	}

	public BigInteger getCurrencyId() {
		return currencyId;
	}

	public void setCurrencyId(BigInteger currencyId) {
		this.currencyId = currencyId;
	}

	public int getDeliverDuration() {
		return deliverDuration;
	}

	public void setDeliverDuration(int deliverDuration) {
		this.deliverDuration = deliverDuration;
	}

	public String getDeliverDurationTxt() {
		if (this.deliverDuration == 0) {
			return "";
		} else {
			return deliverDuration + "小时";
		}
//    return deliverDurationrationTxt;
	}

	public void setDeliverDurationTxt(String deliverDurationTxt) {
		this.deliverDurationTxt = deliverDurationTxt;
	}

	public String getSenderName() {
		return senderName;
	}

	public void setSenderName(String senderName) {
		this.senderName = senderName;
	}



	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getDistrict() {
		return district;
	}

	public void setDistrict(String district) {
		this.district = district;
	}


	public Date getTokenR1ExpireIn() {
		return tokenR1ExpireIn;
	}

	public void setTokenR1ExpireIn(Date tokenR1ExpireIn) {
		this.tokenR1ExpireIn = tokenR1ExpireIn;
	}

	public String getOrderLink() {
		return orderLink;
	}

	public void setOrderLink(String orderLink) {
		this.orderLink = orderLink;
	}

	public String getRefundLink() {
		return refundLink;
	}

	public void setRefundLink(String refundLink) {
		this.refundLink = refundLink;
	}

	public String getAuthLink() {
		return authLink;
	}

	public void setAuthLink(String authLink) {
		this.authLink = authLink;
	}

	public int getAuthType() {
		return authType;
	}

	public void setAuthType(int authType) {
		this.authType = authType;
	}

	public String getOperation() {
		return operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public boolean isSelected() {
		return selected;
	}

	public void setSelected(boolean selected) {
		this.selected = selected;
	}

	public int getOcategoryUI() {
		return getOcategory().getCode();
	}

	public void setOcategoryUI(int ocategoryUI) {
		this.ocategoryUI = ocategoryUI;
	}

	public int getChangeOrderIndexType() {
		return changeOrderIndexType;
	}

	public void setChangeOrderIndexType(int changeOrderIndexType) {
		this.changeOrderIndexType = changeOrderIndexType;
	}

	public int getEshopType() {
		return eshopType;
	}

	public void setEshopType(int eshopType) {
		this.eshopType = eshopType;
	}

	public String getEshopAccount() {
		return eshopAccount;
	}

	public void setEshopAccount(String eshopAccount) {
		this.eshopAccount = eshopAccount;
	}

	public String getKtypeName() {
		return ktypeName;
	}

	public void setKtypeName(String ktypeName) {
		this.ktypeName = ktypeName;
	}

	public EshopInfo getEshopInfo() {
		return eshopInfo;
	}

	public void setEshopInfo(EshopInfo eshopInfo) {
		this.eshopInfo = eshopInfo;
	}

	public EshopConfig getEshopConfig() {
		return eshopConfig;
	}

	public void setEshopConfig(EshopConfig eshopConfig) {
		this.eshopConfig = eshopConfig;
	}

	public List<EshopOrderSyncCondition> getCondition() {
		return condition;
	}

	public void setCondition(List<EshopOrderSyncCondition> condition) {
		this.condition = condition;
	}

	public int getMaxIndex() {
		return maxIndex;
	}

	public void setMaxIndex(int maxIndex) {
		this.maxIndex = maxIndex;
	}

	public int getEshopSalePlatform() {
		return eshopSalePlatform;
	}

	public void setEshopSalePlatform(int eshopSalePlatform) {
		this.eshopSalePlatform = eshopSalePlatform;
	}

	public StoreType getStoreType() {
		return storeType;
	}

	public void setStoreType(StoreType storeType) {
		this.storeType = storeType;
	}

	public BillBusinessType getBusinessType() {
		return businessType;
	}

	public void setBusinessType(BillBusinessType businessType) {
		this.businessType = businessType;
	}

	public MappingType getMappingType() {
		return mappingType;
	}

	public void setMappingType(MappingType mappingType) {
		this.mappingType = mappingType;
	}

	public String getBtypeName() {
		if (btypeName == null) {
			return "";
		}
		return btypeName;
	}

	public void setBtypeName(String btypeName) {
		this.btypeName = btypeName;
	}

	public BigDecimal getBtypePrTotal() {
		if (btypePrTotal == null) {
			return BigDecimal.ZERO;
		}
		return btypePrTotal;
	}

	public void setBtypePrTotal(BigDecimal btypePrTotal) {
		this.btypePrTotal = btypePrTotal;
	}

	public EshopOrderSyncCondition getSyncCondition(TaskType taskType) {
		if (condition == null || condition.isEmpty()) {
			return null;
		}
		Optional<EshopOrderSyncCondition> optional = condition.stream().filter(c -> c.getTaskType() != null && c.getTaskType().equals(taskType)).findFirst();
		return optional.orElse(null);
	}

	public Integer getBtypeGenerateType() {
		if (btypeGenerateType == null) {
			return 0;
		}
		return btypeGenerateType;
	}

	public void setBtypeGenerateType(Integer btypeGenerateType) {
		this.btypeGenerateType = btypeGenerateType;
	}

	public Date getTokenExpireIn() {
		return tokenExpireIn;
	}

	public void setTokenExpireIn(Date tokenExpireIn) {
		this.tokenExpireIn = tokenExpireIn;
	}

	public int getRefundShopType() {
		return refundShopType;
	}

	public void setRefundShopType(int refundShopType) {
		this.refundShopType = refundShopType;
	}

	public BigInteger getRowindex() {
		return rowindex;
	}

	public void setRowindex(BigInteger rowindex) {
		this.rowindex = rowindex;
	}

	public boolean isIndependentCheck() {
		return independentCheck;
	}

	public void setIndependentCheck(boolean independentCheck) {
		this.independentCheck = independentCheck;
	}

	public Integer getMutiSelectAppkey() {
		return mutiSelectAppkey;
	}

	public void setMutiSelectAppkey(Integer mutiSelectAppkey) {
		this.mutiSelectAppkey = mutiSelectAppkey;
	}

	public Boolean isAgEnabled() {
		return agEnabled;
	}

	public void setAgEnabled(Boolean agEnabled) {
		this.agEnabled = agEnabled;
	}

	public String getTypeid() {
		return typeid;
	}

	public void setTypeid(String typeid) {
		this.typeid = typeid;
	}

	public String getPartypeid() {
		return partypeid;
	}

	public void setPartypeid(String partypeid) {
		this.partypeid = partypeid;
	}

	public String getEshopAuthMark() {
		return eshopAuthMark;
	}

	public void setEshopAuthMark(String eshopAuthMark) {
		this.eshopAuthMark = eshopAuthMark;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public int getExpireNotice() {
		return expireNotice;
	}

	public void setExpireNotice(int expireNotice) {
		this.expireNotice = expireNotice;
	}

	public Boolean getTmcEnabled() {
		return tmcEnabled;
	}

	public void setTmcEnabled(Boolean tmcEnabled) {
		this.tmcEnabled = tmcEnabled;
	}
	public int getPromisedSignDuration() {
		return promisedSignDuration;
	}

	public void setPromisedSignDuration(int promisedSignDuration) {
		this.promisedSignDuration = promisedSignDuration;
	}

	public int getPromisedCollectDuration() {
		return promisedCollectDuration;
	}

	public void setPromisedCollectDuration(int promisedCollectDuration) {
		this.promisedCollectDuration = promisedCollectDuration;
	}

	public boolean isPtypeAutoUploadEnabled() {
		return ptypeAutoUploadEnabled;
	}
	public Date getRefreshTokenExpireIn() {
		return refreshTokenExpireIn;
	}

	public void setRefreshTokenExpireIn(Date refreshTokenExpireIn) {
		this.refreshTokenExpireIn = refreshTokenExpireIn;
	}
		public void setPtypeAutoUploadEnabled(boolean ptypeAutoUploadEnabled) {
		this.ptypeAutoUploadEnabled = ptypeAutoUploadEnabled;
	}
	

	public boolean isBtypeAutoUploadEnabled() {
		return btypeAutoUploadEnabled;
	}

	public void setBtypeAutoUploadEnabled(boolean btypeAutoUploadEnabled) {
		this.btypeAutoUploadEnabled = btypeAutoUploadEnabled;
	}

	public boolean isKtypeAutoUploadEnabled() {
		return ktypeAutoUploadEnabled;
	}

	public void setKtypeAutoUploadEnabled(boolean ktypeAutoUploadEnabled) {
		this.ktypeAutoUploadEnabled = ktypeAutoUploadEnabled;
	}

	public boolean isInvoiceUploadEnabled() {
		return invoiceUploadEnabled;
	}

	public void setInvoiceUploadEnabled(boolean invoiceUploadEnabled) {
		this.invoiceUploadEnabled = invoiceUploadEnabled;
	}

	public Integer getRefundSysPromisedConfirmDuration() {
		return refundSysPromisedConfirmDuration;
	}

	public void setRefundSysPromisedConfirmDuration(Integer refundSysPromisedConfirmDuration) {
		this.refundSysPromisedConfirmDuration = refundSysPromisedConfirmDuration;
	}

	public Integer getRefundPromisedConfirmDuration() {
		return refundPromisedConfirmDuration;
	}

	public void setRefundPromisedConfirmDuration(Integer refundPromisedConfirmDuration) {
		this.refundPromisedConfirmDuration = refundPromisedConfirmDuration;
	}

	public Integer getRefundPromisedAgreeDuration() {
		return refundPromisedAgreeDuration;
	}

	public void setRefundPromisedAgreeDuration(Integer refundPromisedAgreeDuration) {
		this.refundPromisedAgreeDuration = refundPromisedAgreeDuration;
	}

	public Integer getRefundPromisedDeliverDuration() {
		return refundPromisedDeliverDuration;
	}

	public void setRefundPromisedDeliverDuration(Integer refundPromisedDeliverDuration) {
		this.refundPromisedDeliverDuration = refundPromisedDeliverDuration;
	}

	public Integer getRefundPromisedReceiveDuration() {
		return refundPromisedReceiveDuration;
	}

	public void setRefundPromisedReceiveDuration(Integer refundPromisedReceiveDuration) {
		this.refundPromisedReceiveDuration = refundPromisedReceiveDuration;
	}

	public ShopType getShopType() {
		return shopType;
	}

	public void setShopType(ShopType shopType) {
		this.shopType = shopType;
	}

	public String getOnlineEshopId() {
		return onlineEshopId;
	}

	public void setOnlineEshopId(String onlineEshopId) {
		this.onlineEshopId = onlineEshopId;
	}

	public String getAppKey() {
		return appKey;
	}

	public void setAppKey(String appKey) {
		this.appKey = appKey;
	}

	public int getMentionDeliverDuration() {
		return mentionDeliverDuration;
	}

	public void setMentionDeliverDuration(int mentionDeliverDuration) {
		this.mentionDeliverDuration = mentionDeliverDuration;
	}

	public String getPlatformEshopSnType() {
		return platformEshopSnType;
	}

	public void setPlatformEshopSnType(String platformEshopSnType) {
		this.platformEshopSnType = platformEshopSnType;
	}

	public BigInteger getPid() {
		return pid;
	}

	public void setPid(BigInteger pid) {
		this.pid = pid;
	}
}

<?xml version="1.0" encoding="UTF-8" ?>
<Page xmlns="Craba.UI" Title="订单收款"
      OnClose="doClose"
      ActionType="sale.eshoporder.eshopplatformcheck.OrderGatherInfoAction,sale/eshoporder/eshopplatformcheck/OrderGatherInfo.js,sale/jarvis/DeliverBill/common/CommonJs.js" AllowResize="false">
      <HiddenField ID="hfOrderInfo" DataField="orderInfo" />
      <HiddenField ID="hfOtypeId" DataField="otypeId"/>
      <HiddenField ID="hfBtypeId" DataField="btypeId"/>
    <VSpacer Height="10"/>
    <Block CssClass='dflex' >
        <ListView ID="lvGatherBoard"  CssClass="ListBlockItem NoBg NoBorder" OnItemRendering="doItemRender" ItemCssField="cname">
          <ListTemplate>
            <DataText CssClass="text" DataField="title" />
            <DataNumber DataField='value' CssClass='value' DisplayThousandSeperator='true'/>
          </ListTemplate>
        </ListView>
    </Block>
    <VSpacer Height="10"/>
    <FlowPanel LayoutDirection="Vert"  ItemLabelWidth="110" Width="500" >
        <DropDownEdit DropDownStyle="DropDownList"  ID="dropAmount" DataField="dropAmount"  Label="收款金额计算方式："
            ListItems="0=根据订单应收余额合计进行收款,1=根据国补金额合计进行收款,2=根据【订单应收余额-国补金额】的差额合计进行收款"
            SelectedIndex="0"  OnChange="doCheckChange" Width="380"  />
        <VSpacer Height="10"/>
        <SelectorEdit ID="edAType" Label="收款账户：" SelectOnly="true" OnButtonClick="doAtypeSelectorInit" OnEnterPress="doEnterPress"
            OnSelectorSelected="doAtypeInfoSelect" DataField="atypeId" DisplayField="atypeName" Tag="btype" OnChange="doAtypeChange"
            Required="true" Width="380"/>
        <VSpacer Height="10"/>
        <DateTimeEdit ID="billDate" Label="收款时间：" DataField="billDate" Required="true" Width="380"
               CssClass="MustCharLeft"/>
    </FlowPanel>

    <HBlock CssClass='BottomBlock'>
        <Block CssClass='Flex1'/>
        <Button ID="btnSave" OnClick="doSave" CssClass="SpecialButton" Text="保存"/>
        <CancelButton OnClick="doClose" Text="取消"  />
    </HBlock>

  <Style>
    .icon{color:#2288fc;width:60px;height:60px;align-self:center;background: linear-gradient( 114deg, #F4F7FF 0%, #E5EEFF 100%);border-radius: 8px;border: 1px solid #E5EEFF;padding:10px;margin-right:10px;}
    .icon:before{font-size:26px;align-self:center;margin:auto;}
    .title{font-size:22px;font-weight:500;}
    .ListBlockItem .text{font-size:15px;}
    .ListBlockItem .value{font-size:20px;line-height:30px;display:flex;}

    .ListBlockItem .value:before{align-self:center;color:#fff;padding:0 5px;border-radius:3px;font-size:12px;width:24px;height:24px;line-height:24px;display:block;flex:0;}
    .ListBlockItem .value.cha{color:#FA4050;}
    .ListBlockItem .value.cha:before{content:'差';background-color:#FA4050;}
    .btns .Button{cursor:default;padding:0 5px;height:20px;line-height:18px;color:#fff;border-radius:3px;}

    .ListBlockItem .bg1{background-color:#bae5f6}
    .ListBlockItem .bg2{background-color:#e7f6ba}
  </Style>
</Page>
package com.wsgjp.ct.sale.biz.bill.service.impl;

import com.wsgjp.ct.bill.core.handle.entity.enums.BillPostState;
import com.wsgjp.ct.bill.core.handle.entity.enums.PayStateEnum;
import com.wsgjp.ct.bill.core.handle.entity.enums.Vchtypes;
import com.wsgjp.ct.framework.enums.AssertsChangeType;
import com.wsgjp.ct.framework.enums.AssertsSourceOperation;
import com.wsgjp.ct.sale.biz.api.BillAPI;
import com.wsgjp.ct.sale.biz.bill.aspect.AddLogs;
import com.wsgjp.ct.sale.biz.bill.exception.BillValidationException;
import com.wsgjp.ct.sale.biz.bill.exception.PostBillException;
import com.wsgjp.ct.sale.biz.bill.exception.ValidationException;
import com.wsgjp.ct.sale.biz.bill.mapper.EtypeBillMapper;
import com.wsgjp.ct.sale.biz.bill.mapper.TdBillCoreMapper;
import com.wsgjp.ct.sale.biz.bill.model.dao.ClientParams;
import com.wsgjp.ct.sale.biz.bill.model.dao.LoadUserDAO;
import com.wsgjp.ct.sale.biz.bill.model.dao.PrintCountDAO;
import com.wsgjp.ct.sale.biz.bill.model.entity.BillLoadRequest;
import com.wsgjp.ct.sale.biz.bill.service.AddBillLogs;
import com.wsgjp.ct.sale.biz.bill.service.AfterSubmitBillService;
import com.wsgjp.ct.sale.biz.bill.service.BillImproveService;
import com.wsgjp.ct.sale.biz.bill.service.FinanceInvoiceService;
import com.wsgjp.ct.sale.biz.bill.utils.CurrentContext;
import com.wsgjp.ct.sale.biz.bill.utils.UserInfoUtils;
import com.wsgjp.ct.sale.biz.common.util.CommonUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.biz.member.mapper.SsCardAssertBillMapper;
import com.wsgjp.ct.sale.biz.member.model.dto.vip.VipAssertsBillDetailDto;
import com.wsgjp.ct.sale.biz.member.model.dto.vip.VipAssertsBillDto;
import com.wsgjp.ct.sale.biz.member.model.dto.vip.VipAssertsChange;
import com.wsgjp.ct.sale.biz.member.service.ISsVipAssertsChangeService;
import com.wsgjp.ct.sale.biz.member.service.ISsVipService;
import com.wsgjp.ct.sale.biz.shopsale.constanst.BillSaveResultTypeEnum;
import com.wsgjp.ct.sale.biz.shopsale.mapper.BillMapper;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.*;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.cashbox.CashBoxPaymentDTO;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.bill.PosBill;
import com.wsgjp.ct.sale.biz.shopsale.service.CashBoxService;
import com.wsgjp.ct.sale.biz.shopsale.service.PromotionCheckService;
import com.wsgjp.ct.sale.common.enums.BillBusinessTypeEnum;
import com.wsgjp.ct.sale.monitor.MonitorService;
import com.wsgjp.ct.sale.monitor.entity.MonitorTypeEnum;
import com.wsgjp.ct.sale.platform.utils.BeanUtils;
import com.wsgjp.ct.sale.sdk.pos.biz.MemberAssertsChangeService;
import com.wsgjp.ct.sale.sdk.pos.entity.asserts.MemberAssert;
import com.wsgjp.ct.sale.sdk.pos.entity.asserts.MemberAssertsChange;
import com.wsgjp.ct.sale.sdk.pos.entity.asserts.VipAsserts;
import com.wsgjp.ct.support.context.CurrentUser;
import ngp.idgenerator.UId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * 单据顶层服务
 *
 * <AUTHOR>
 */
@Service
public class BillCoordinatorServiceImpl<TBill extends AbstractBillDTO> {

    private GoodsBillServiceImpl goodsBillService;
    private BillAPI billAPI;
    private FinanceInvoiceService financeInvoiceService;
    private EtypeBillMapper etypeBillMapper;
    private CashBoxService cashBoxService;
    private AddBillLogs addBillLogs;
    private static final Logger logger = LoggerFactory.getLogger(BillCoordinatorServiceImpl.class);
    @Autowired
    private ISsVipAssertsChangeService ssVipAssertsChangeService;
    @Autowired
    BillMapper billMapper;
    private PromotionCheckService promotionCheckService;

    private SsCardAssertBillMapper assertBillMapper;

    private ISsVipService ssVipService;

    private TdBillCoreMapper tdBillCoreMapper;

    private BillImproveService billImproveService;

    private AfterSubmitBillService afterSubmitBillService;

    private MemberAssertsChangeService vipAssertsChangeService;

    public BillCoordinatorServiceImpl(GoodsBillServiceImpl goodsBillService, BillAPI billAPI,
                                      FinanceInvoiceService financeInvoiceService,
                                      EtypeBillMapper etypeBillMapper,
                                      CashBoxService cashBoxService,
                                      AddBillLogs addBillLogs,
                                      PromotionCheckService promotionCheckService,
                                      SsCardAssertBillMapper assertBillMapper,
                                      ISsVipService ssVipService,
                                      TdBillCoreMapper tdBillCoreMapper,
                                      BillImproveService billImproveService,
                                      AfterSubmitBillService afterSubmitBillService,
                                      MemberAssertsChangeService vipAssertsChangeService) {
        this.goodsBillService = goodsBillService;
        this.billAPI = billAPI;
        this.financeInvoiceService = financeInvoiceService;
        this.etypeBillMapper = etypeBillMapper;
        this.cashBoxService = cashBoxService;
        this.addBillLogs = addBillLogs;
        this.promotionCheckService = promotionCheckService;
        this.assertBillMapper = assertBillMapper;
        this.ssVipService = ssVipService;
        this.tdBillCoreMapper = tdBillCoreMapper;
        this.billImproveService = billImproveService;
        this.afterSubmitBillService = afterSubmitBillService;
        this.vipAssertsChangeService = vipAssertsChangeService;
    }


    /**
     * 单据保存
     *
     * @param bill
     * @return
     */
    @AddLogs
    public BillSaveResultDTO doSaveBill(TBill bill, BigInteger employeeId, PosBill request) {
        BillSaveResultDTO result = null;
        try {
            result = saveAction(bill, request);
            logger.info("单据保存过账结束");
        } catch (BillValidationException err) {
            logger.info(String.format("单据保存失败：%s", err.getMessage()), err);
            result = CurrentContext.createBillValidationErrorResult(err);
        } catch (ValidationException err) {
            logger.info(String.format("单据保存失败：%s", err.getMessage()), err);
            result = CurrentContext.createValidationErrorResult(err);
        } catch (PostBillException err) {
            logger.info(String.format("单据保存失败：%s", err.getMessage()), err);
            result = CurrentContext.createPostBillErrorResult(err, (GoodsBillDTO) bill);
        } catch (Exception err) {
            logger.info(String.format("单据保存失败：%s", err.getMessage()), err);
            err.printStackTrace();
            result = CurrentContext.createErrorResult(bill.getVchcode(), bill.getNumber(), BillSaveResultTypeEnum.ERROR
                    , err);
        } finally {
            ///处理返回前端的回填信息
            result.setBillNumber(bill.getNumber());
            result.setVchcode(bill.getVchcode());
            result.setVchtype(bill.getVchtype());
            result.setSummary(bill.getSummary());
            result.setBillDate(bill.getDate());
            result.setInvoiceSign(CommonUtil.getInvoiceSign(bill.getVchcode()));
            // 打印时重新获取，不再单独计算
//            Object giveScoreMoney = null == request ? "0" : request.getGiveScoreMoney();
//            BigDecimal giveScoreMoneyDecimal = giveScoreMoney == null ? BigDecimal.ZERO :
//                    new BigDecimal(giveScoreMoney.toString());
//            int saleScore = (null == bill.getVipCardId() || BigInteger.ZERO.equals(bill.getVipCardId())) ? 0 :
//                    ssVipAssertsChangeService.scoreCompute(bill.getVipCardId(),
//                            null == giveScoreMoney ?
//                                    BigDecimal.ZERO :
//                                    giveScoreMoneyDecimal,
//                            ScoreStrategyType.consumption);
//            result.setSaleScore(saleScore);
        }

        return result;
    }

    private String getValidationKey(TBill bill) {
        if (bill.getBusinessType() == null || BillBusinessTypeEnum.None.getCode() == bill.getBusinessType().getCode()) {
            return bill.getVchtype().toString();
        } else {
            return bill.getVchtype().toString() + "-" + bill.getBusinessType().toString();
        }
    }

    /**
     * 业务单据保存
     *
     * @param bill
     * @return
     * @throws Exception
     */
    private BillSaveResultDTO saveAction(TBill bill, PosBill request) throws Exception {
        String validationKey = getValidationKey(bill);
        GetBeanUtil.getBean(BillCoordinatorServiceImpl.class).saveBillAndSaveVip(validationKey,
                (GoodsBillDTO) bill, request);
        postBill((GoodsBillDTO) bill);
        return goodsBillService.createResult((GoodsBillDTO) bill);

    }

//    private void writePrintCount(TBill bill, PosBill request) {
//        String printTimes = "printTimes";
//        try {
//            if (bill.getPrintTimes() != null && bill.getPrintTimes() > 0) {
//                doWritePrintCount(bill.getPostState().getState(), bill.getVchcode(), bill.getVchtype(),
//                        bill.getPrintTimes());
//            }
//        } catch (Exception e) {
//            String logMsg = String.format("单据打印次数写入，【单据编号：%s，原因：%s】", bill.getNumber(), e.getMessage());
//            bill.setLogMsg(logMsg);
//            addBillLogs.addBillLogs((GoodsBillDTO) bill);
//        }
//    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void saveBillAndSaveVip(String validationKey, GoodsBillDTO bill, PosBill request) throws Exception {
        // 保存前单据信息完善
        billImproveService.improve(request);
        // 开始构建billEntity并保存
        goodsBillService.saveBill(validationKey, bill, false);
        logger.info("单据已保存，准备生成开票源");
        // 保存发票源
        if (bill.getVchtype() == Vchtypes.SaleBill) {
            logger.info("单据开始生成开票源");
            financeInvoiceService.saveInvoice(bill);

        }
        // 开始零售业务处理
        afterSubmitBillService.handlingRetail(request);
    }

    public void postBill(GoodsBillDTO bill) {
        // 核算
        if (bill.getPostState().getState() >= BillPostState.PROCESS_COMPLETED.getState() && bill.getWhetherPost()) {
            long sTime = System.currentTimeMillis();
            if (!TransactionSynchronizationManager.isSynchronizationActive()) {
                // 非事务环境
                goodsBillService.postBill(bill.getVchcode());
            } else {
                // 事务环境
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        goodsBillService.postBill(bill.getVchcode());
                    }
                });
            }
            MonitorService monitorService = BeanUtils.getBean(MonitorService.class);
            monitorService.recordTP(MonitorTypeEnum.PL_API_SALE_POS_SUBMIT_POST_BILL_TIME.getTopic(), "shopType", "pos",
                    (System.currentTimeMillis() - sTime));
            logger.info("单据{}核算完成", bill.getVchcode());
        }
    }

    public void doWritePrintCount(int state, BigInteger vchcode, Vchtypes vchype, int times) {
        if (state < BillPostState.PROCESS_COMPLETED.getState() || times == 0) {
            return;
        }
        PrintCountDAO printCountDAO = new PrintCountDAO();
        printCountDAO.setPrintTimes(times);
        ClientParams clientParams = new ClientParams();
        clientParams.setVchcode(vchcode);
        if ("SaleBill".equals(vchype.toString())) {
            clientParams.setVchtype("Sale");
        } else if ("SaleBackBill".equals(vchype.toString())) {
            clientParams.setVchtype("SaleBack");
        }
        printCountDAO.setClientParams(clientParams);
        billAPI.writePrintCount(printCountDAO);
    }


    public BillSaveResultDTO validationGoodsBill(TBill bill) {

        String validationKey = getValidationKey(bill);
        try {
            GoodsBillDTO goodsBillDTO = (GoodsBillDTO) bill;
            ssVipService.checkValidPaymentList(goodsBillDTO.getPayment());
            if (!promotionCheckService.checkPromotion(goodsBillDTO)) {
                BillSaveResultDTO result = new BillSaveResultDTO();
                result.setBillNumber(bill.getNumber());
                result.setVchcode(bill.getVchcode());
                result.setSummary(bill.getSummary());
                result.setMessage("促销核验不通过 " + (goodsBillDTO.getPromotionErrorMsg() == null ? "" :
                        goodsBillDTO.getPromotionErrorMsg()));
                result.setResultType(BillSaveResultTypeEnum.CONFIRM);
                return result;
            }
            // 明细表的ktypeId处理（目前发现使用了礼品券的话，礼品的商品明细没有ktypeId，后端统一处理一下，把主表的ktypeId给明细）
            billImproveService.buildKtype((GoodsBillDTO) bill);
            return goodsBillService.validationGoodsBill(validationKey, goodsBillDTO);
        } catch (ValidationException err) {
            return CurrentContext.createValidationErrorResult(err);
        } catch (Exception err) {
            err.printStackTrace();
            return CurrentContext.createErrorResult(bill.getVchcode(), bill.getNumber(), BillSaveResultTypeEnum.ERROR
                    , err);
        }
    }

    private void buildKtype(GoodsBillDTO bill) {
        BigInteger ktypeId = bill.getKtypeId();
        for (GoodsDetailDTO goodsDetailDTO : bill.getOutDetail()) {
            goodsDetailDTO.setKtypeId(ktypeId);
        }
        for (GoodsDetailDTO goodsDetailDTO : bill.getInDetail()) {
            goodsDetailDTO.setKtypeId(ktypeId);
        }
    }

    public GoodsBillDTO getBillByVchcode(BillLoadRequest request) throws Exception {
        // 这里用户信息装配虽然自用系统也在调，但是getBillByVchcode跟页面没关系，所以可以用baseUserInfo
        UserInfoUtils.baseUserInfo(request);
        GoodsBillDTO newBill = goodsBillService.getBill(request);
        if (request.getCopyEnable()) {
            newBill.setCreateEtypeId(CurrentUser.getEmployeeId());
            LoadUserDAO curLoadUserDAO = etypeBillMapper.loadUser(CurrentUser.getProfileId(),
                    CurrentUser.getEmployeeId());
            newBill.setCreateEfullname(curLoadUserDAO.getEmployeName());
        }
        if (newBill.getCreateType() != null) {
            newBill.setCreateTypeEnumName(newBill.getCreateType().toString());
        }
        return newBill;
    }

    public void updateBillPayState(GoodsBillDTO bill) {
        boolean updated = GetBeanUtil.getBean(BillCoordinatorServiceImpl.class).updateBillPayStateAndSaveAsserts(bill);
        // 只有在全部更新完后才记录日志
        if (updated) {
            // 记录日志
            if (bill.getPayState() != null) {
                String payStateName = "";
                if (bill.getPayState().getCode() == 0) {
                    payStateName = "未支付";
                }
                if (bill.getPayState().getCode() == 1) {
                    payStateName = "已支付";
                }
                String logMsg = String.format("更新支付状态，【单据编号：%s，变更为：%s，来源：%s】", bill.getNumber(), payStateName,
                        bill.getSource());
                bill.setLogMsg(logMsg);
                addBillLogs.addBillLogs(bill);
            }
            logger.info("vchcode:" + bill.getVchcode() + "记录修改成功日志");
            try {
                saveCashPayment(bill.getProfileId(), bill.getVchcode(), bill.getVchtype());
                postBill(bill);
                logger.info("vchcode:" + bill.getVchcode() + "提交核算成功");
            } catch (PostBillException e) {
                logger.info("vchcode:" + bill.getVchcode() + "核算失败，异常：" + e.getMessage());
                addBillLogs.addPostBillLog(bill.getVchcode(), false, e.getMessage(), bill.getSource(), null,
                        bill.isCallBack());
            }
        }
    }

    public void saveCashPayment(BigInteger profile, BigInteger vchcode, Vchtypes vchtype) {
        CashBoxPaymentDTO paymentDTO = billMapper.getCashBoxPayment(profile, vchcode);
        if (paymentDTO != null) {
            switch (vchtype) {
                case SaleBill:
                    paymentDTO.setPaymentType(BigInteger.valueOf(2));
                    break;
                case SaleBackBill:
                    paymentDTO.setPaymentType(BigInteger.valueOf(3));
                    break;
                case SaleChangeBill:
                    if (paymentDTO.getAmount().compareTo(BigDecimal.ZERO) < 0) {
                        paymentDTO.setAmount(paymentDTO.getAmount().abs());
                        paymentDTO.setPaymentType(BigInteger.valueOf(3));
                    } else {
                        paymentDTO.setPaymentType(BigInteger.valueOf(2));
                    }
                    break;
            }
            paymentDTO.setId(UId.newId());
            try {
                logger.info("vchcode:" + vchcode + "新增钱箱记录");
                cashBoxService.insertPayment(paymentDTO);
            } catch (Exception e) {
                throw new RuntimeException(String.format("新增钱箱记录失败:" + e.getMessage()));
            }
        }
    }


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public boolean updateBillPayStateAndSaveAsserts(GoodsBillDTO bill) {
        logger.info("vchcode:" + bill.getVchcode() + "开始修改支付状态");
        BillLoadRequest billLoadRequest = new BillLoadRequest();
        billLoadRequest.setVchcode(bill.getVchcode());
        // 异步回调不需要验证权限
        billLoadRequest.setCheckPermission(!bill.isCallBack());
        GoodsBillDTO bill1 = null;
        int a = 0;
        String exception = "";
        while (a < 10) {
            try {
                bill1 = getBillByVchcode(billLoadRequest);
                a = 10;
            } catch (Exception e) {
                a++;
                exception = e.getMessage();
            }
        }
        Assert.isTrue(bill1 != null, exception);
        logger.info("vchcode:" + bill.getVchcode() + "获取单据信息成功");
        bill.setVchtype(bill1.getVchtype());
        bill.setPostState(bill1.getPostState());
        bill.setProfileId(CurrentUser.getProfileId());
        bill.setNumber(bill1.getNumber());
        bill.setOutDetail(bill1.getOutDetail());
        bill.setInDetail(bill1.getInDetail());
        if (bill1.getPayState() == PayStateEnum.Paied) {
            // 如果单据已是已支付状态，不进行处理
            logger.info("vchcode:" + bill.getVchcode() + "单据是已支付状态，直接返回");
            return false;
        }
        boolean isDeleted = tdBillCoreMapper.getBillStatu(CurrentUser.getProfileId(), bill.getVchcode());
        if (isDeleted) {
            throw new RuntimeException("已删除的单据不能更改支付状态");
        }
        bill.setVchtype(bill1.getVchtype());
        bill.setPostState(bill1.getPostState());
        bill.setProfileId(CurrentUser.getProfileId());
        goodsBillService.updateBillPayState(bill);
        // 处理会员资产
        if (bill1.getVipCardId() != null && !Objects.equals(bill1.getVipCardId(), BigInteger.ZERO)) {
            logger.info("vchcode:" + bill.getVchcode() + "开始处理会员资产");
            bill1.setOperationSource(bill.getOperationSource());
            changeVipAsserts(bill1);
            // 会员等级变动
            ssVipService.vipLevelUpgrade(bill1.getVipCardId());
        }
        logger.info("vchcode:" + bill.getVchcode() + "更新支付状态执行结束");
        return true;
    }

    public void cancelBill(BigInteger vchcode) {
        goodsBillService.cancelBill(vchcode);
    }

    private void changeVipAsserts(GoodsBillDTO bill) {
        VipAssertsBillDto vipAssertsBillDto = assertBillMapper.selectAssertBill(CurrentUser.getProfileId(),
                bill.getVchcode());
        if (vipAssertsBillDto == null || vipAssertsBillDto.getId() == null || !vipAssertsBillDto.isStatused()) {
            vipAssertsBillDto = new VipAssertsBillDto();
            vipAssertsBillDto.setAssertsBillDetailDtoList(new ArrayList<>());
        }
        BigDecimal money = bill.getCurrencyBillTotal();
        MemberAssertsChange dto = new MemberAssertsChange();
        dto.setVchtype(com.wsgjp.ct.framework.enums.Vchtypes.codeOf(bill.getVchtype().getCode()));
        dto.setVchcode(bill.getVchcode());
        dto.setBillNumber(vipAssertsBillDto.getBillNumber());
        if (bill.getVchtype() == Vchtypes.SaleBill) {
            dto.setSourceOperation(AssertsSourceOperation.CONSUMPTION);
            dto.setMemo(AssertsSourceOperation.CONSUMPTION.getName());
        }
        if (bill.getVchtype() == Vchtypes.SaleBackBill) {
            dto.setSourceOperation(AssertsSourceOperation.REFUND);
            dto.setMemo(AssertsSourceOperation.REFUND.getName());
        }
        if (bill.getVchtype() == Vchtypes.SaleChangeBill) {
            dto.setMemo("门店换货");
            if (BigDecimal.ZERO.compareTo(bill.getCurrencyBillTotal()) > 0) {
                dto.setSourceOperation(AssertsSourceOperation.REFUND);
            } else {
                dto.setSourceOperation(AssertsSourceOperation.CONSUMPTION);
            }
        }
        List<VipAsserts> vipAssertsList = new ArrayList<>();
        VipAsserts vipAssert = new VipAsserts();
        vipAssert.setVipId(bill.getVipCardId());
        vipAssert.setMoney(money);
        List<MemberAssert> vipAsserts = new ArrayList<>();
        for (VipAssertsBillDetailDto detailDto : vipAssertsBillDto.getAssertsBillDetailDtoList()) {
            int typed = detailDto.getTyped();
            BigDecimal qty = detailDto.getQty();
            AssertsChangeType changeType = detailDto.getChangeType();
            switch (typed) {
                //积分、（抵扣积分、赠送积分）
                case 0:
                    vipAsserts.add(MemberAssert.createData(0, qty, changeType));
                    break;
                case 1://储值
                    vipAsserts.add(MemberAssert.createData(1, qty, changeType));
                    break;
                case 2://储值 赠金
                    vipAsserts.add(MemberAssert.createData(2, qty, changeType));
                    if (money.compareTo(BigDecimal.ZERO) < 0) {
                        money = money.multiply(qty.abs());
                    } else {
                        money = money.add(qty.abs());
                    }
                    break;
                case 3://成长值
                    vipAsserts.add(MemberAssert.createData(3, qty, changeType));
                    break;
                case 4://卡券
                    if (detailDto.getCardType() == 1) {
                        vipAsserts.add(MemberAssert.createData(4,
                                qty,
                                VipAssertsBillDetailDto.getTypedMemo(4, qty),
                                null,
                                detailDto.getAssertId(),
                                changeType));
                    } else if (detailDto.getCardType() == 2 || detailDto.getCardType() == 3 || detailDto.getCardType() == 4) {

                        if (qty.compareTo(BigDecimal.ZERO) > 0) {//赠送优惠券
                            vipAsserts.add(MemberAssert.createData(4,
                                    qty,
                                    VipAssertsBillDetailDto.getTypedMemo(4, qty),
                                    null,
                                    detailDto.getCardTemplateId(),
                                    changeType));
                        } else {//使用优惠券
                            vipAsserts.add(MemberAssert.createData(4,
                                    qty,
                                    VipAssertsBillDetailDto.getTypedMemo(4, qty),
                                    detailDto.getAssertId(),
                                    null,
                                    changeType));
                        }
                    }
                    break;
            }
        }
        vipAssert.setVipAssert(vipAsserts);
        vipAssertsList.add(vipAssert);
        dto.setVipAsserts(vipAssertsList);
        dto.setLastPayTime(bill.getDate());
        dto.setOperationSource(bill.getOperationSource());
        vipAssertsChangeService.vipAssertsChange(dto);
    }
//
//    public void checkRefundQty(GoodsBillDTO bill) {
//        // 自用系统单据 或 出库单 不校验
//        if (bill.getCreateType() == BillCreateType.FROM_SELF_SYSTEM || bill.getVchtype() == Vchtypes.SaleBill) {
//            return;
//        }
//        // PC编辑操作不校验
//        if (bill.getBillSource() == BillSourceEnum.PC) {
//            return;
//        }
//        List<GoodsDetailDTO> inDetail = bill.getInDetail();
//        if (inDetail.isEmpty()) {
//            return;
//        }
//        // 获取sourceDetailId
//        List<BigInteger> sourceDetailIds = new ArrayList<>();
//        for (GoodsDetailDTO goodsDetailDTO : inDetail) {
//            if (goodsDetailDTO.getSourceDetailId() != null && !Objects.equals(goodsDetailDTO.getSourceDetailId(),
//                    BigInteger.ZERO)) {
//                sourceDetailIds.add(goodsDetailDTO.getSourceDetailId());
//            }
//        }
//        if (sourceDetailIds.isEmpty()) {
//            return;
//        }
//        List<String> ptypeNames = tdBillCoreMapper.checkRefundQty(CurrentUser.getProfileId(), sourceDetailIds);
//        if (!ptypeNames.isEmpty()) {
//            String names = String.join("，", ptypeNames);
//            throw new RuntimeException("退货数量已超过出库单可退数量：" + names);
//        }
//    }
}

package com.wsgjp.ct.sale.biz.eshoporder.entity.refund.freightintercept;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.refund.AfterSaleFreightInterceptStatus;

import java.io.Serializable;

public class AfterSaleInterceptExcelEntity extends BaseRowModel implements Serializable {
    @ExcelProperty(value = "物流公司名称", index = 0)
    private String freightName;
    @ExcelProperty(value = "物流公司单号", index = 1)
    private String freightBillNo;
    @ExcelProperty(value = "拦截状态", index = 2,converter = InterceptStatusConverter.class)

    private AfterSaleFreightInterceptStatus interceptStatus;

    public String getFreightName() {
        return freightName;
    }

    public void setFreightName(String freightName) {
        this.freightName = freightName;
    }

    public String getFreightBillNo() {
        return freightBillNo;
    }

    public void setFreightBillNo(String freightBillNo) {
        this.freightBillNo = freightBillNo;
    }

    public AfterSaleFreightInterceptStatus getInterceptStatus() {
        return interceptStatus;
    }

    public void setInterceptStatus(AfterSaleFreightInterceptStatus interceptStatus) {
        this.interceptStatus = interceptStatus;
    }
}

Type.registerNamespace('sale.eshoporder.eshop');
sale.eshoporder.eshop.EshopAddAction = function () {
    sale.eshoporder.eshop.EshopAddAction.initializeBase(this);
};

sale.eshoporder.eshop.EshopAddAction.prototype = {
    _selectShoptypeInfo: null,
    _originData: null,
    _otypeId: 0,
    authStatus: {"authorizedAuth": false, "appSecret": ""},
    _stop: true,
    _childForm: null,
    importState: false,
    _ocategory: null,
    _defaultKtype: null,
    _oldVersion: false,
    context: function (cb) {
        if (typeof $skinUtils != 'undefined') {
            var cssList = ['.shouquan:before{background-color:{mainColor} !important;}',
                '.dinggou:before{background-color:{mainColor} !important;}',
                '.shouquanEnd:before{background-color:{mainColor} !important;}',];
            var skin = {
                css: cssList.join(' ')
            };
            $skinUtils.registerSkin(skin);
        }
        var form = this.get_form();
        var powers = this._getSomePower();
        var pageParam = form.get_pageParams();
        var auth = pageParam.auth;
        var authText = "重新授权"
        if (!auth) {
            authText = "立即授权"
            //如果是新增肯定为undifine所以auth也是false
            auth = false;
        }
        var param = {
            otypeId: 0,
            ocategory: pageParam.ocategory ? pageParam.ocategory : 0,
            typeId: pageParam.typeId ? pageParam.typeId : "00000"
        };
        this._ocategory = pageParam.ocategory;
        var title = "";
        if (pageParam.ocategory == 0) {
            title = "新增普通网店"
        }
        if (pageParam.ocategory == 1) {
            title = "新增分销网店"
        }
        if (pageParam.ocategory == 2) {
            title = "新增代运营网店"
        }
        if (pageParam.ocategory == 3) {
            title = "新增虚拟网店"
        }
        if (pageParam.ocategory == 6) {
            title = "新增批发部"
        }
        var res =
            $common.ajaxSync({
                url: "sale/eshoporder/eshop/getEshopPageResponse",
                data: param,
                router: 'ngp'
            });
        if (res.code !== "200") {
            Sys.UI.MessageBox.alert("服务请求出错,请稍后重试[" + res.message + "]");
            return;
        }
        var data = res.data;
        this._originData = data;
        var buyerparam = data.pageInfo;
        this.mutiSelectAppkey = buyerparam.mutiSelectAppkey;
        this._oldVersion = data && data.oldVersion;
        cb({
            "powers": powers,
            "dropdownDataSource": buyerparam.fieldInfos ? buyerparam.fieldInfos[0] ? buyerparam.fieldInfos[0].dropdownDataSource : null : null,
            "fieldInfos": buyerparam.fieldInfos,
            "needAuth": buyerparam.needAuth,
            "partypes": buyerparam.parTypeList,
            "title": title,
            "datasource": buyerparam,
            "auth": auth,
            "authText": authText,
            "typeId": pageParam.typeId ? pageParam.typeId : "00000",
            "allowShowEshopClass": buyerparam.allowShowEshopClass,
        });
    },
    initialize: function EshopAddAction$initialize() {
        sale.eshoporder.eshop.EshopAddAction.callBaseMethod(this, 'initialize');
        var form = this.get_form();
        this.bindData();
        var pageParam = form.get_pageParams();
        var category = pageParam.ocategory;
        this.selectParType(form);
        //当信息虚拟网店类型
        if (category == 3) {
            form.xnshopAccount.set_visible(true);
            form.getShopType.set_visible(false);
            form.auth.set_visible(false);
            form.eshopConfig.set_visible(true);
            this._selectShoptypeInfo = {"shoptype": 9999};
            form.XNShopStep.set_visible(true);
        }
        form.shouQuanStep.set_visible(false);
    },
    selectParType: function (form) {
        var partId = this.get_context('typeId');
        var partypes = this.get_context('partypes');
        if (partypes != null && form.dpParTypeId) {
            for (var i = 0; i < partypes.length; i++) {
                if (partypes[i].typeid == partId) {
                    form.dpParTypeId.set_selectedIndex(i);
                    break;
                }
            }
        }
    },
    bindData: function () {
        var form = this.get_form();
        var view = form.eshopView;
        // ajax获取数据，不能用同步，要改为异步回调绑定
        var list = this.getFiles("");
        view.refresh(list);
    },
    doOrder: function (sender) {
        var form = this.get_form();
        var orderLink = form.orderLink.get_value();
        if (!orderLink) return;
        if (form.shopAccount.get_value() == '') {
            $common.alert("请输入网店账号");
            return;
        }
        window.open(orderLink, "_blank");
    },
    doAuth: function (sender) {
        var form = sender.get_form();
        var saveData = form.saveData();
        if (!saveData.eshopAccount) {
            if (form.MTgrid) {
                var rowData = form.MTgrid.get_selectedRowData();
                saveData.eshopAccount = rowData.eshopAccount;
            }
        }
        if (form.shopAccount.get_value() == '' && saveData.eshopAccount == '') {
            $common.alert("请输入网店账号");
            return;
        }
        this.saveShopAccounttoRedis(saveData.eshopAccount);
        var msg = this.doAuthCheck(sender);
        if (msg) {
            $common.alert(msg);
            return;
        }
        this.authorizeAction(sender);
    },
    saveShopAccounttoRedis: function (shopAccount) {
        var param = {eshopAccount: shopAccount, shopType: this._selectShoptypeInfo.shoptype};
        var authresult =
            $common.ajaxSync({
                url: "sale/eshoporder/eshop/saveShopAccounttoRedis",
                data: param,
                router: 'ngp'
            });
    },
    authorizeAction: function (sender) {
        var form = sender.get_form();
        var authType = this._selectShoptypeInfo.authType;
        var mode = this.get_context('mode');
        var shopType = this._selectShoptypeInfo.shoptype;
        var fullName = form.orgName.get_value();
        var param = {otypeId: 0, shopType: shopType, mode: mode, fullName: fullName};
        if (this.mutiSelectAppkey != null) {
            param.mutiSelectAppkey = this.mutiSelectAppkey
        }
        if (authType == 1 || authType == 5)//需要弹出授权页面
        {
            var authresult =
                $common.ajaxSync({
                    url: "sale/eshoporder/eshop/getAuthUrl",
                    data: param,
                    router: 'ngp'
                });
            if (!authresult.code || authresult.code != '200') {
                if (authresult.message != null || authresult.message != '') {
                    if (authresult.message.includes(':')) {
                        $common.alert(authresult.message.split(':')[1]);
                    }
                } else {
                    $common.alert("获取授权链接失败");
                }
                return;
            }
            var authUrl = authresult.data;
            this.openEShopAuthorizeURL(authUrl);
        } else if (authType == 2) {
            param.appSecret = form.appSecret.get_value();
            param.appKey = form.appKey.get_value();
            var result =
                $common.ajaxSync({
                    url: "sale/eshoporder/eshop/doAuth",
                    data: param,
                    router: 'ngp'
                });
            if (result.code != "200") {
                $common.alert(result.message);
                return;
            }
            var data = result ? result.data : null;
            if (data && data.success) {
                this.authStatus.authorizedAuth = true;
                this.authStatus.appSecret = param.appSecret;
                this.sendMessage("授权");
                form.iconAuth.set_visible(true);
                $common.showOk("授权成功");
                return;
            }
            if (data && !data.success && data.message) {
                $common.alert(data.message);
            }
        }


    },
    openEShopAuthorizeURL: function (authUrl) {
        if (!authUrl) {
            return;
        }
        $common.openwin(authUrl);
    },
    doAuthCheck: function (sender) {
        var form = sender.get_form();
        var shopType = this._shopType;
        var otypeId = 0;
        var eshopAccount = form.shopAccount.get_value();
        var param = {shopType: shopType, eshopAccount: eshopAccount};
        var result =
            $common.ajaxSync({
                url: "sale/eshoporder/eshop/authorizeCheckNew",
                data: param,
                router: 'ngp'
            });
        var data = result ? result.data : null;
        if (data != null && !data.success) {
            return data.message;
        }


        return null;
    },
    supplierSelectorInit: function (sender) {
        var form = sender.get_form();
        var pageParams = new Object();
        pageParams.bcategory = 3;
        pageParams.cooperationType = 4;
        pageParams.cooperationTypeVisible = false;
        var url = "jxc/baseinfo/selector/BtypeSelector.gspx";
        sender.set_selectorPage(url);
        sender.set_selectorPageParams(pageParams);
    },
    onAtypeInit: function (sender) {
        var url = "/jxc/baseinfo/selector/AtypeSelector.gspx";
        var filterStr = sender.get_text();
        var parameter = {
            filterKey: 'quick',
            parTypeId: "00001",
            showadd: true,
            showstopfilter: true,
            pageTitle: '收支账户'
        };
        if (filterStr && sender.get_textChanged()) {
            parameter.filtervalue = filterStr;
        }
        sender.set_selectorPageParams(parameter);
        sender.set_selectorPage(url);

    },
    doEnterPress: function (sender, eventArgs) {
        if (sender.get_value() && !sender.get_textChanged()) {
            eventArgs.set_cancel(true);
        }
    }
    ,
    selectSupplier: function (sender, eventArgs) {
        var data = eventArgs.get_form().selectedData;
        if (data == undefined) {
            return;
        }
        sender.set_value(data.id);
        sender.set_text(data.fullname);
    },
    doBaseInfoSelect: function (sender, eventArgs) {
        var selector = eventArgs.get_form();
        var result = selector.selectedData;
        if (sender.get_value() === result.id) {
            return;
        }
        sender.set_value(result.id);
        sender.set_text(result.fullname);
    }
    ,
    onKtypeInit: function (sender) {
        var url = "/jxc/baseinfo/selector/KtypeSelector.gspx";
        var filterStr = sender.get_text();
        var parameter = {
            filtertype: 'quick',
            stockStates: '0',
            showadd: true
        };
        if (filterStr && sender.get_textChanged()) {
            parameter.filtervalue = filterStr;
        }
        sender.set_showMDI(true);
        sender.set_selectorPageParams(parameter);
        sender.set_selectorPage(url);
    }
    ,

    getFiles: function (key) {
        var form = this.get_form();
        var pageParam = form.get_pageParams();
        var parameter = {ocategory: pageParam.ocategory};
        parameter.key = key;
        var list = [];
        var res = $common.ajaxSync({
            url: 'sale/eshoporder/eshop/getShopTypeFiles',
            data: parameter,
            router: 'ngp',
            type: 'post',
        })
        return res.data;
    },
    getTrees: function (count, first) {
        var list = [];
        for (var i = 0; i < count; i++) {
            var index = i + first;
            var rowData = {
                "id": index,
                "name": "目录" + index,
                "pid": index % 2 == 0 ? 1 : 0
            };
            list.push(rowData);
        }
        return list;
    },
    _getSomePower: function () {
        var powers = {};
        //是否为批发产品
        powers.isWholesale = false;//$eshoppower.getSysdata("productType") == "WHOLESALE";
        return powers;
    },
    showAddress: function () {
        var form = this.get_form();
        var addressEdit = form.addressEdit.get_value();
        if (!addressEdit) {
            $common.showTips("请填写内容!")
        }
        var _this = this;
        this.get_service().post('shell/config/resolveAddress', {address: addressEdit}, function (rest) {
            if (!rest || rest.code != '200') {
                $common.showTips((data && data.message) ? data.message : '解析地址出错，请重试');
                return;
            }
            var data = rest.data
            if (data.address) {
                form.senderArea.popupArea._bindData(data.address.province, data.address.city, data.address.district, data.address.town);
                form.address.set_value(data.address.street);
            }
            if (data.receiverName) {
                form.senderName.set_value(data.receiverName)
            }
            if (data.phone && data.phone) {
                form.phone.set_value(data.phone)
            }
            if (data.telephone) {
                form.mobile.set_value(data.telephone);
            }
        })
    },
    chooseShopType: function (sender, arg) {
        var form = this.get_form();
        form.getShopType.set_visible(false);
        form.auth.set_visible(true);
        form.shouQuanStep.set_visible(true);
        form.BranchStorePanel.set_visible(false);
        this.onEShopTypeChange(sender, arg.get_data());
        this.checkShopAccountHasOrdered(form, null);
    },
    handleRendering: function (sender, args) {
        var data = args.get_data();
        var item = args.get_item();
        if (data.sign == true) {
            item.addCssClass('new');
        }
    },
    showToken: function () {
        var form = this.get_form();
        var chencked = form.radio2.get_checked();
        form.token.set_visible(false);
        form.usualAuth.set_visible(true);
        if (chencked) {
            form.token.set_visible(true);
            form.token.set_label("Token:");
            form.usualAuth.set_visible(false);
        }
    },
    onEShopTypeChange: function (sender, item) {
        var form = this.get_form();
        this._selectShoptypeInfo = item;
        var shopType = item.shoptype;
        this._shopType = shopType;
        var pageParam = form.get_pageParams();
        var params = {
            otypeId: 0,
            ocategory: pageParam.ocategory,
            shopType: shopType,
            notQueryEshop: true
        };
        var response = $common.ajaxSync({
            url: "sale/eshoporder/eshop/getEshopInfoByEshopType",
            data: params,
            router: 'ngp'
        });
        var data = response ? response.data : null;
        var eshopInfo = data ? data.pageInfo : null;
        this._eshopInfo = eshopInfo;
        form.fieldInfos.set_value(eshopInfo.fieldInfos);

        this.showBaseShopInfo(sender);
        form.eshopConfig.set_visible(true);

        if (eshopInfo.needOrder) {
            form.btnOrder.set_enabled(true);
            form.orderLink.set_value(eshopInfo.orderLink);
            form.cbxOrderhp.set_visible(true);
        } else {
            form.btnOrder.set_enabled(false);
            form.orderLink.set_value("");
            form.cbxOrderhp.set_visible(false);
        }
        if (eshopInfo.needAuth) {
            form.btnAuth.set_enabled(true);
            form.cbxAuthhp.set_visible(true);

        } else {
            form.btnAuth.set_enabled(false);
            form.cbxAuthhp.set_visible(false);
        }
        if (eshopInfo.subscribeApplications != null && eshopInfo.subscribeApplications.length > 0) {
            this._ShowSelectAppkeyView(form, eshopInfo.subscribeApplications);
        } else if (eshopInfo.authType == 5) {
            form.usualAuth.set_visible(false);
            form.threeStepAuth.set_visible(true);
            form.selectAppkey.set_visible(false);
        } else {
            form.usualAuth.set_visible(true);
            form.threeStepAuth.set_visible(false);
            form.selectAppkey.set_visible(false);
        }
        this.createControls(form);

        this.doShowCbx(form, shopType, false);
        this.doSetDingGouHintText(form, null);
    },
    doShowCbx: function (form, eshopType, isPlatEshopChange) {
        if (eshopType == 50) {
            form.shopAccount.set_required(false);
        }
        if (eshopType == 50) {
            form.shopAccount.set_required(false);
        }
        form.tmallSpecialSale.set_visible(false);
        if (eshopType == 90) {
            form.tmallSpecialSale.set_visible(true);
        }
        form.platformauthType.set_visible(false);
        if (eshopType == 114 || eshopType == 116 || eshopType == 134 || eshopType == 164) {
            form.platformauthType.set_visible(true);
            form.platformauthType.set_value(0);
        }
        var item = this._selectShoptypeInfo;
        var showAppKey = item.showAppKey;
        if (eshopType == item.type) {
            form.authType.set_value(item.authType);
            if (isPlatEshopChange) {
                form.shopType.set_selectedIndex(i);
            }
            showAppKey = item.showAppKey;
        }
        /**
         0, "无需授权"
         1, "需要弹出授权页面"
         2, "不需要弹出授权页面"
         3, "需要登录信息的授权"
         4, "需要弹出授权页面并且需要选择销售机构"
         */
        if (showAppKey == 2) {
            form.cbxOrderhp.set_visible(false);
            form.cbxAuthhp.set_visible(false);
        }
        if (showAppKey == 0 || showAppKey == 1) {
            form.cbxOrderhp.set_visible(true);
            form.cbxAuthhp.set_visible(true);
        }
        if (showAppKey == 3) {
            form.cbxOrderhp.set_visible(true);
            form.cbxAuthhp.set_visible(true);
        }
        if (showAppKey == 4) {
            // form.appAuth.set_visible(false);
            // form.appAuthLabel.set_visible(false);
            form.cbxOrderhp.set_visible(false);
            form.cbxAuthhp.set_visible(false);
        }
        if (showAppKey == 5) {
            form.cbxOrderhp.set_visible(true);
            form.cbxAuthhp.set_visible(true);
        }
    },
    getAccountTypeOptionItems: function (otype, allowBuyerAccount, assignAccountType) {

        if (otype == this.otypeCategory.OrdinarySale) {
            return this.getOrdinarySaleAccountTypeOptionItems(allowBuyerAccount);
        }

        if (otype == this.otypeCategory.Distribution) {
            return this.getDistributionAccountTypeOptionItems(allowBuyerAccount, assignAccountType);
        }

        if (otype == this.otypeCategory.AgentOperation) {
            return this.getAgentOperationAccountTypeOptionItems(allowBuyerAccount);
        }

        if (otype == this.otypeCategory.Virtual) {
            return this.getVirtualAccountTypeOptionItems(allowBuyerAccount);
        }
        if (otype == this.otypeCategory.PifaBu) {
            return this.getPifaAccountTypeOptionItems();
        }

    },
    getOrdinarySaleAccountTypeOptionItems: function (allowBuyerAccount) {

        var enumAccountType = this.getAccountEnumsDiscription();
        var result = [];
        result.push({text: enumAccountType[this.accountType.Otype], value: this.accountType.Otype});
        if (allowBuyerAccount) {
            // result.push({text: enumAccountType[this.accountType.Otype], value: this.accountType.Otype});
            result.push({text: enumAccountType[this.accountType.BuyerAccount], value: this.accountType.BuyerAccount});
            // return result;
        }
        result.push({text: enumAccountType[this.accountType.Distributor], value: this.accountType.Distributor});
        result.push({text: enumAccountType[this.accountType.Store], value: this.accountType.Store});
        return result;
    },

    getAgentOperationAccountTypeOptionItems: function () {
        var enumAccountType = this.getAccountEnumsDiscription();
        var result = [];
        result.push({text: enumAccountType[this.accountType.NoNeed], value: this.accountType.NoNeed});
        return result;
    },

    getDistributionAccountTypeOptionItems: function (allowBuyerAccount, assignAccountType) {
        var enumAccountType = this.getAccountEnumsDiscription();
        var result = [];
        if (assignAccountType > 0) {
            result.push({text: enumAccountType[assignAccountType], value: assignAccountType});
            return result;
        }
        if (allowBuyerAccount) {
            result.push({text: enumAccountType[this.accountType.Otype], value: this.accountType.Otype});
            result.push({text: enumAccountType[this.accountType.BuyerAccount], value: this.accountType.BuyerAccount});
            result.push({text: enumAccountType[this.accountType.Distributor], value: this.accountType.Distributor});
            return result;
        }
        result.push({text: enumAccountType[this.accountType.Otype], value: this.accountType.Otype});
        result.push({text: enumAccountType[this.accountType.Distributor], value: this.accountType.Distributor});
        return result;
    },

    getVirtualAccountTypeOptionItems: function (allowBuyerAccount) {
        var enumAccountType = this.getAccountEnumsDiscription();
        //if(allowBuyerAccount){
        var result = [];
        result.push({text: enumAccountType[this.accountType.Otype], value: this.accountType.Otype});
        result.push({text: enumAccountType[this.accountType.BuyerAccount], value: this.accountType.BuyerAccount});
        // }else {
        //     var result = [];
        //      result.push({text: enumAccountType[this.accountType.Otype], value: this.accountType.Otype});
        // }


        return result;

    },
    getPifaAccountTypeOptionItems: function () {
        var enumAccountType = this.getAccountEnumsDiscription();
        var result = [];
        result.push({text: enumAccountType[this.accountType.Otype], value: this.accountType.Otype});
        return result;

    },
    createControls: function (form) {
        var fields = form.fieldInfos.get_value();
        var shoptype = this._shopType;
        form.platformEshopSnType.set_visible(false);
        form.vendorId.set_visible(false);
        form.token.set_visible(false);
        form.platformEshopId.set_visible(false);
        form.onlineEshopId.set_visible(false);
        form.appKey.set_visible(false);
        form.appSecret.set_visible(false);
        form.shopAccount.set_label("网店账号");
        form.shopAccount.set_nullDisplayText('请填写网店账号');
        if (!fields) {
            return;
        }
        for (var i = 0; i < fields.length; i++) {
            var data = fields[i];
            var isRequired = data.required ? true : false;
            var title = data.title ? data.title : data.description;
            var hintText = "";
            var labelText = title;
            if (title.length > 8) {
                hintText = title;
                labelText = title.substring(0, 8) + "...";
            }
            if (data.controllerType == "TEXT" && data.field == "eshopAccount") {
                form.shopAccount.set_label(title);
                form.shopAccount.set_nullDisplayText('请填写' + title);
                form.shopAccount.set_required(isRequired);
                if (isRequired) {
                    form.shopAccount.set_labelCssClass("MustCharLeft");
                } else {
                    form.shopAccount.remove_labelCssClass("MustCharLeft");
                }
                if (data)
                    continue;
            }
            if (data.controllerType == "TEXT" && data.field == "appSecret") {
                form.appSecret.set_label(title);
                form.appSecret.set_nullDisplayText('请填写' + title);
                form.appSecret.set_required(isRequired);
                form.appSecret.set_visible(true);
                if (isRequired) {
                    form.appSecret.set_labelCssClass("MustCharLeft");
                }
                if (data)
                    continue;
            }

            if (data.controllerType == "SELECTOR" && data.field == "platformEshopSnType") {
                form.platformEshopSnType.set_label(title);
                // if (form.get_context("platformEshopSnType") == null) {
                form.platformEshopSnType.set_items(data.dropdownDataSource);
                // }
                var platformEshopSnType = this.get_context('platformEshopSnType');
                var containValue = false;
                if (data.dropdownDataSource != null) {
                    for (var index = 0; index < data.dropdownDataSource.length; index++) {
                        if (platformEshopSnType === data.dropdownDataSource[index].value) {
                            containValue = true;
                            break;
                        }
                    }
                }
                if (!containValue) {
                    if (data.defaultValue !== null && data.defaultValue !== '') {
                        form.platformEshopSnType.set_value(data.defaultValue);
                    }
                } else {
                    form.platformEshopSnType.set_value(platformEshopSnType);
                }
                form.platformEshopSnType.set_visible(true);
                form.platformEshopSnType.set_nullDisplayText("请填写" + title);
                form.platformEshopSnType.set_required(isRequired);
                if (isRequired) {
                    form.platformEshopSnType.set_labelCssClass("MustCharLeft");
                } else {
                    form.platformEshopSnType.remove_labelCssClass("MustCharLeft");
                }
                continue;
            }
            if (data.controllerType == "TEXT" && data.field == "vendorId") {
                form.vendorId.set_label(title);
                form.vendorId.set_visible(true);
                form.vendorId.set_nullDisplayText("请填写" + title);
                if (shoptype == 50) {
                    form.vendorId.set_nullDisplayText(data.hintText);
                }
                form.vendorId.set_required(isRequired);
                if (isRequired) {
                    form.vendorId.set_labelCssClass("MustCharLeft");
                } else {
                    form.vendorId.remove_labelCssClass("MustCharLeft");
                }
                continue;
            }
            if (data.controllerType == "TEXT" && data.field == "token") {
                form.token.set_label(title);
                form.token.set_visible(true);
                form.token.set_nullDisplayText("请填写" + title);
                form.token.set_required(isRequired);
                if (isRequired) {
                    form.token.set_labelCssClass("MustCharLeft");
                } else {
                    form.token.remove_labelCssClass("MustCharLeft");
                }
                continue;
            }
            if (data.controllerType == "TEXT" && data.field == "platformEshopId") {
                form.platformEshopId.set_label(title);
                form.platformEshopId.set_visible(true);
                form.platformEshopId.set_nullDisplayText("请填写" + title);
                form.platformEshopId.set_required(isRequired);
                if (isRequired) {
                    form.platformEshopId.set_labelCssClass("MustCharLeft");
                } else {
                    form.platformEshopId.remove_labelCssClass("MustCharLeft");
                }
                continue;
            }
            if (data.controllerType == "TEXT" && data.field == "onlineEshopId") {
                form.onlineEshopId.set_label(title);
                form.onlineEshopId.set_visible(true);
                form.onlineEshopId.set_nullDisplayText("请填写" + title);
                form.onlineEshopId.set_required(isRequired);
                if (isRequired) {
                    form.onlineEshopId.set_labelCssClass("MustCharLeft");
                } else {
                    form.onlineEshopId.remove_labelCssClass("MustCharLeft");
                }
                continue;
            }
            var elt = form[data.field];
            var controllerType = data.controllerType;
            if (elt && controllerType == "TEXT") {
                elt.set_visible(true);
                if (Sys.UI.Controls.TextEdit.isInstanceOfType(elt) && data.hintText) {
                    elt.set_nullDisplayText(data.hintText);
                }
                if (title) {
                    elt.set_nullDisplayText('请填写' + title);
                }
                if(labelText){
                    elt.set_label(labelText);
                }
                if(hintText){
                    elt.set_labelHint(hintText);
                }
                if (data.field == "shopAccount") {
                    form.shopAccountName.set_value(title);
                }
                form.shopAccountName.set_required(isRequired);
                if (isRequired) {
                    form.shopAccountName.set_labelCssClass("MustCharLeft");
                } else {
                    form.shopAccountName.remove_labelCssClass("MustCharLeft");
                }
            }
        }
    },
    doFeedbackUrl: function () {
        $common.openwin("https://fuwu.mygjp.com/demand/create?type=1");
    },
    btnQueryOnClick: function () {
        var form = this.get_form();
        var keyWord = form.keyWord.get_text();
        var list = this.getFiles(keyWord);
        var form = this.get_form();
        var view = form.eshopView;
        view.refresh(list);
    },
    doBaseEditNext: function () {
        var form = this.get_form();
        var saveData = form.saveData();
        this.doBtnSave(saveData);
    },
    doBtnSave: function (param) {
        var startObj = null;
        if (window.$startTiming) {
            startObj = $startTiming('网店->网店->新增');
        }
        var powers = this._getSomePower();
        var pageInfo = this._originData.pageInfo;
        var form = this.get_form();
        var pageParam = form.get_pageParams();
        param.isAuth = 0;
        if (pageParam && pageParam.ocategory == 2) {
            param.sendProcessWay = 4;
        } else {
            param.sendProcessWay = 0;
            var hasWmsFunc = $msModule.checkPoint("WmsFunc");
            if (hasWmsFunc) {
                param.sendProcessWay = 1;
            }
            var hasOfflineBillToDeliverFunc = $msModule.checkPoint("OfflineBillToDeliverFunc");
            if (hasOfflineBillToDeliverFunc) {
                param.sendProcessWay = 2;
            }
        }
        param.eshopType = this._selectShoptypeInfo.shoptype;
        var isYdh = (param.eshopType == param || param.eshopType == 889 || param.eshopType == 890 || param.eshopType == 891 || param.eshopType == 892);
        param.btypeGenerateType = isYdh ? 1 : 0;
        param.independentCheck = true;
        if (!pageParam.auth) {
            param.isAuth = 1;
        }
        var shoptype = this._selectShoptypeInfo.shoptype;
        param.eshopSalePlatform = shoptype;
        if (this.mutiSelectAppkey != null) {
            param.mutiSelectAppkey = this.mutiSelectAppkey;
        }
        //独立核算 #bug23519
        if (!powers.isWholesale) {
            var btypeId = this.getBtype();
            if (!btypeId || btypeId == undefined) {
                //$common.alert("获取往来单位信息错误，请稍后再试。");
                return;
            }
        }

        var par = {};
        par.ktypeId = param.ktypeId;
        var ktypeResult =
            $common.ajaxSync({
                url: "sale/eshoporder/eshop/checkBtypeIn",
                data: par,
                router: 'ngp'
            });
        if (ktypeResult.code != 200) {
            $common.alert("校验发货仓库失败");
            return;
        } else {
            if (ktypeResult.data == false) {
                $common.alert("发货仓库已经被删除");
                return;
            }
        }
        if (!param.ktypeName) {
            param.ktypeName = form.edKType.get_text();
        }
        param.mode = pageParam.mode;
        param.btypeId = btypeId;
        this.buildEshopInfo(form, param);
        if (param.eshopAccount == null) {
            param.eshopAccount = '';
        }
        param.checkAccountType = this.checkAccountType.ByOrder;
        //这些值默认为-1
        param.refundPromisedAgreeDuration = 24
        param.refundPromisedConfirmDuration = 24
        param.refundSysPromisedConfirmDuration = -1
        param.refundPromisedReceiveDuration = -1
        param.refundPromisedDeliverDuration = -1
        param.mentionDeliverDuration = -1
        param.promisedCollectDuration = -1
        param.promisedSyncFreightDuration = -1
        param.deliverDuration = 48
        param.promisedSignDuration = -1
        param.subscribeLogistics = 0;
        param.deliverProcessUsetype = 1;
        param.matchLocalSameBtypeEnable = true;
        if ($msModule.checkPoint("DistributeFunc") || $msModule.checkPoint("SaleCarFunc") || $msModule.checkPoint("VisitFunc")) {
            //【自配订单交货方式】看是否开启外勤模块，开启外勤模块才可见。默认值为简单配送。
            param.selfDeliveryMode = 0;
        }

        if (form.MTgrid && form.MTgrid.get_visible()) {
            if (form.MTgrid.get_dataSource() && form.MTgrid.get_dataSource().length > 0 && form.MTgrid.get_dataSource()[0].eshopAccount) {
                param.eshopBranch = form.MTgrid.get_dataSource();
            } else if (form.MTgrid.get_allItems() && form.MTgrid.get_allItems().length > 0 && form.MTgrid.get_allItems()[0].eshopAccount) {
                param.eshopBranch = form.MTgrid.get_allItems();
            }
        }
        if (form.grid && form.grid.get_visible()) {
            if (form.grid.get_dataSource() && form.grid.get_dataSource().length > 0 && form.grid.get_dataSource()[0].eshopAccount) {
                param.eshopBranch = form.grid.get_dataSource();
            } else if (form.grid.get_allItems() && form.grid.get_allItems().length > 0 && form.grid.get_allItems()[0].eshopAccount) {
                param.eshopBranch = form.grid.get_allItems();
            }
        }
        var otypeInfo = this.buildOtype(param);
        //todo检查重复
        var otypeSaveResult = this.saveOtype(otypeInfo);
        if (otypeSaveResult.data && otypeSaveResult.data.success) {
            param.otypeId = otypeSaveResult.data.message;
            this._otypeId = param.otypeId;
            otypeInfo.id = param.otypeId;
            this._buildPifaParam(param, form);
            //是否需要取消授权
            this.checkCancelAuth(param, form);
            var eshopSaveResult =
                $common.ajaxSync({
                    url: "sale/eshoporder/eshop/saveEshop",
                    data: param,
                    router: 'ngp'
                });
            if (!eshopSaveResult || !eshopSaveResult.data || !eshopSaveResult.data.success) {
                $common.alert("保存网店信息出错" + eshopSaveResult.data.message ? eshopSaveResult.data.message : "");
                var ids = [];
                ids.push(otypeInfo.id);
                var res = $common.ajaxSync({
                    url: "jxc/baseinfo/otype/eshopPhysicalDelete",
                    data: ids,
                    router: 'ngp'
                });
                form.set_enabled(true);
                return;
            }
            this.sendMessage(pageParam.mode == 1 ? "新增网店" : "编辑");
            //saveEshop的时候，data里面还是有授权信息的
            //这里当平台网店类型更改之后，需要把这些授权信息清除
            //所以后续又调用取消授权方法
            if (powers.isWholesale && pageInfo.independentCheck != param.independentCheck) {
                //批发 网店 通知独立核算修改
                var independentRequest = new Object();
                independentRequest.independentCheck = otypeInfo.independentCheck;
                independentRequest.otypeId = otypeInfo.id;
                var independentResult = $common.ajaxSync({
                    // todo===
                    url: "/accounting/otypeaccount/reCalcProfitAtypeTotalByOtype",
                    data: independentRequest,
                    type: 'post',
                    router: 'ngp-router'
                });
            }
            // form.createSuccess.set_visible(true);
            // form.eshopConfig.set_visible(false);
            // var datasource = form.bar4.get_dataSource();
            // if (datasource.length > 2) {
            //     form.bar4.set_value(3);
            // } else {
            //     form.bar4.set_value(1);
            // }
        } else {
            $common.alert(otypeSaveResult.data.message);
            form.set_enabled(true);
            return;
        }
        if (startObj != null) {
            startObj.endTiming();// 触发上报
        }
    },
    doFormClose: function (sender, eventArgs) {
        $notify.emit('messageA');
    },
    sendMessage: function (info) {
        var res = $common.ajax({
            url: 'sale/eshoporder/eshop/baseInfoChangeNotify',
            data: info,
            type: 'post',
            router: 'ngp'
        })
    },
    _buildPifaParam: function (parpam, form) {
        var powers = this._getSomePower();
        //默认
        parpam.checkAccountType = this.checkAccountType.ByOrder;
        if (parpam.btypeGenerateType == 0) {
            parpam.accountType = 1;
        } else if (parpam.btypeGenerateType == 1) {
            parpam.accountType = 3;
        }
        if (powers.isWholesale) {
            parpam.accountType = 0;
            parpam.btypeGenerateType = 0;
            parpam.checkAccountType = 0;
        }
        if (form.radio2.get_checked()) {
            parpam.tmallSpecialSale = true;
            parpam.tokenExpireIn = Date.parse('2099-12-30 :00:00:00');
        }
        var token = form.token.get_value();
        parpam.token = token;
    },
    saveOtype: function (otypeInfo) {
        var res = $common.ajaxSync({
            url: "sale/eshoporder/eshop/saveOtype",
            data: otypeInfo,
            router: 'ngp'
        });
        return res;
    },
    checkCancelAuth: function (param, form) {
        var authType = this._selectShoptypeInfo.authType;
        if (authType == 2) {
            //判断是否在已授权的状态下修改了授权码
            if (this.authStatus.authorizedAuth && this.authStatus.appSecret != param.appSecret) {
                if (this.get_context('mode') == 2) {
                    //取消授权
                    this.cancelAuth(param);
                } else {
                    this.deleteAuthInfoRedis(param);
                }
            }
        }
    },
    deleteAuthInfoRedis: function (param) {
        $common.ajaxSync({
            url: "sale/eshoporder/eshop/deleteAuthInfoRedis",
            data: param,
            router: 'ngp'
        });
    },
    cancelAuth: function (param) {
        var paramlist = [];
        paramlist.push(param.otypeId);

        var response = $common.ajaxSync({
            url: "sale/eshoporder/eshoplist/cancelEShopAuth",
            data: paramlist,
            type: 'post',
            router: 'ngp'
        });
    },
    buildEshopInfo: function (form, param) {
        param.mallType = param.mallType == null ? 0 : param.mallType;
        param.mappingType = param.mappingType == null ? 0 : param.mappingType;
        param.partypeId = form.dpParTypeId.get_value().typeid;
        if (!param.partypeId) {
            param.partypeId = "00000";
        }
    },
    buildOtype: function (param) {
        var form = this.get_form();
        var otypeInfo = {};
        var isnew = this.get_context('mode') == 1;
        otypeInfo.profileId = param.profileId;
        otypeInfo.id = param.otypeId;
        otypeInfo.usercode = '';
        otypeInfo.fullname = param.otypeFullname;
        otypeInfo.ocategory = param.ocategory;
        otypeInfo.ktypeId = param.ktypeId;
        otypeInfo.btypeId = param.btypeId;
        otypeInfo.atypeId = param.atypeId ? param.atypeId : 0;
        otypeInfo.currencyId = param.currencyId;
        // otypeInfo.classId = param.classId;
        otypeInfo.partypeId = param.typeid;
        if (param.btypeGenerateType == 0) {
            otypeInfo.accountType = 1;
        } else if (param.btypeGenerateType == 1) {
            otypeInfo.accountType = 3;
        }

        otypeInfo.checkAccountType = param.checkAccountType;
        otypeInfo.payAccount = "";//param.payAccount;
        otypeInfo.memo = param.memo;
        otypeInfo.deliverDuration = param.deliverDuration ? param.deliverDuration : -1;
        otypeInfo.people = param.senderName;
        otypeInfo.cellphone = param.mobile;
        otypeInfo.telephone = param.phone;
        otypeInfo.rowindex = param.rowindex;
        otypeInfo.independentCheck = param.independentCheck ? 1 : 0;

        var saveData = this.get_form().saveData()
        // var buyerArea = saveData.buyerArea.split('/');
        otypeInfo.province = "";
        otypeInfo.city = "";
        otypeInfo.district = "";
        otypeInfo.street = "";
        otypeInfo.address = "";
        otypeInfo.eshopBranch = param.eshopBranch;
        param.province = otypeInfo.province;
        param.city = otypeInfo.city;
        param.district = otypeInfo.district;
        param.street = otypeInfo.street;
        param.address = otypeInfo.address;
        if (isnew) otypeInfo.createTime = new Date();
        else otypeInfo.updateTime = new Date();
        return otypeInfo;
    },
    getBtype: function () {
        var form = this.get_form();
        var param = form.saveData();
        var btypeName = param.otypeFullname;
        if (param.btypeId) {
            return param.btypeId;
        }
        $common.checkTips(!btypeName, "网店名称不能为空");
        var url = "sale/eshoporder/eshop/getBtypeByName";
        var btypeId =
            $common.ajaxSync({
                url: url,
                data: btypeName,
                router: 'ngp'
            });
        if (!btypeId || !btypeId.data) {
            var btypeUserCode =
                $common.ajaxSync({
                    url: "sale/eshoporder/eshop/getBtypeUserCode",
                    type: 'get',
                    data: null,
                    router: 'ngp'
                });
            var btype = {};
            btype.fullname = btypeName;
            btype.usercode = btypeUserCode.data;
            btype.bcategory = 0;
            btype.cooperationType = 4;
            btype.cooperationTypeVisible = false;
            var response =
                $common.ajaxSync({
                    url: "jxc/baseinfo/btype/save",
                    data: btype,
                    router: 'ngp'
                });
            if (response && response.data) {
                return response.data;
            } else {
                $common.alert("尝试根据网店名称创建往来单位失败，" + response.message);
                return;
            }

        } else if (btypeId && btypeId.data) {
            return btypeId.data;
        }
        $common.alert("查询已有往来单位失败，请稍后重试");

    },

    doNext: function (sender) {
        this.doSaveFormData(sender);
        var form = this.get_form();
        form.eshopConfig.set_visible(false);
        form.auth.set_visible(false);
        form.createSuccess.set_visible(true);
        form.shouQuanStep.set_visible(false);
        // var saveData = form.saveData();
        // this.showBaseShopInfo(sender);
    },
    showBaseShopInfo: function (sender) {
        var form = sender.get_form();
        var pageParam = form.get_pageParams();
        var category = pageParam.ocategory;
        this.getDefaultKtype();
        if (this._defaultKtype && this._defaultKtype.id) {
            form.edKType.set_value(this._defaultKtype.id);
            form.edKType.set_text(this._defaultKtype.fullname);
        }
        form.remind.set_visible(false);
        if (this._selectShoptypeInfo.shoptype == 116 || this._selectShoptypeInfo.shoptype == 134 || this._selectShoptypeInfo.shoptype == 164) {
            form.remind.set_visible(true);
        }

    },
    checkBranchEshopIsRepeat: function (form) {
        var datasource = [];
        if (form.MTgrid) {
            if (form.MTgrid.get_dataSource()) {
                datasource = form.MTgrid.get_dataSource();
            } else if (form.MTgrid.get_allItems() && form.MTgrid.get_allItems().length > 0) {
                datasource = form.MTgrid.get_allItems();
            }
        }
        if (form.grid) {
            if (form.grid.get_dataSource()) {
                datasource = form.grid.get_dataSource();
            } else if (form.grid.get_allItems() && form.grid.get_allItems().length > 0) {
                datasource = form.grid.get_allItems();
            }
        }
        if (datasource.length > 0) {
            var res = $common.ajaxSync({
                url: "sale/eshoporder/eshop/checkBranchEshopIsRepeat",
                data: datasource,
                router: 'ngp'
            });
            if (res.code == 200) {
                if (res.data.length > 0) {
                    $common.alert(res.data);
                    return true;
                }
            }
            return false;
        }
    },
    doAuthCheckByShoptype: function (sender) {
        var form = sender.get_form();
        if (form.radio2 && form.radio2.get_checked() == false) {
            return "";
        }
        var shopType = this._shopType;
        var eshopAccount = form.shopAccount.get_value();
        var param = {shopType: shopType, queryVirtual: true};
        var result =
            $common.ajaxSync({
                url: "sale/eshoporder/eshop/authorizeCheckByShoptype",
                data: param,
                router: 'ngp'
            });
        var data = result ? result.data : null;
        if (data != null && !data.success) {
            return data.message;
        }

        return '';
    },
    doback: function () {
        var form = this.get_form();
        form.auth.dataBind(null);
        if (form.grid) {
            form.grid.dataBind([]);
        }
        if (form.MTgrid) {
            form.MTgrid.dataBind([]);
        }
        form.BranchStorePanel.set_visible(false);
        form.BranchStorePanelMT.set_visible(false);
        form.auth.set_visible(false);
        form.shouQuanStep.set_visible(false);
        form.getShopType.set_visible(true);
        form.eshopConfig.set_visible(false);
    },

    getAuthCheckResult: function () {
        var form = this.get_form();
        var pageParam = form.get_pageParams();
        var category = pageParam.ocategory;
        var _this = this;
        var shoptype = this._selectShoptypeInfo.shoptype;
        var url = "sale/eshoporder/eshop/getAuthCheckResult/" + shoptype;
        var resp = $common.ajaxSync({
            url: url,
            data: null,
            type: 'get',
            router: 'ngp'
        });
        if (!resp || !resp.data) {
            _this.selectParType(form)
            return true;
        }
        //2, "同账套其他网店存在相同授权",
        //3, "其他账套存在相同授权",
        var authCheckResult = resp.data.authCheckResult;
        if (authCheckResult == 2) {
            _this._doSameShopAutnConfirm('该网店的授权，在其它网店资料，已存在相同有效的授权，请确认是否继续？', true, _this, form);
        } else if (authCheckResult == 3) {
            var condition = category == 2;
            _this._doSameShopAutnConfirm('该网店的授权，在其它公司ERP，已存在相同有效的授权，请确认是否继续？', condition, _this, form);
        } else {
            _this.selectParType(form);
            return true;
        }
        return false;
    },

    _doSameShopAutnConfirm: function (tipMsg, condition, _this, form) {
        $common.alert(tipMsg, {
            iconName: 'bicon-tixing',
            title: '提示',
            buttons: ['继续', '取消'],
            btnClass: ['SpecialButton', ''],
            activeButton: 1, // 激活按钮序号，索引从0开始
            handler: function (result) {
                if (result == 0) {
                    if (condition) {
                        _this.selectParType(form);
                    }
                    _this._doFormOk(form, _this);

                }
            },
            className: 'AlertSelf' // 自己实现样式，比如控制图标颜色，字体样式。。。样式如下
        });
    },
    doOk: function (sender) {
        var form = this.get_form();
        form.doOk();
    },

    doSaveFormData: function (sender) {
        var form = this.get_form();
        var saveData = form.saveData();
        var isrepeat = this.checkBranchEshopIsRepeat(form);
        if (!isrepeat) {
            var msg = this.doAuthCheckByShoptype(sender);
            if (msg == '') {
                var ret = this.getAuthCheckResult();
                if (ret) {
                    this.doBtnSave(saveData);
                }
            } else {
                $common.showError(msg);
                return;
            }
        } else {
            this.doBtnSave(saveData);
        }
    },
    shopConfig: function (sender) {
        var form = this.get_form();
        var _this = this;
        var data = new Object();
        var param = new Object();
        param.otypeId = this._otypeId;
        param.eshopId = this._otypeId;
        param.queryVirtual = true;
        data.queryParams = param;
        var rowData = null;
        var response = $common.ajaxSync({
            url: 'sale/eshoporder/eshoplist/getOrgList',
            data: data,
            router: 'ngp'
        });
        var data = response && response.data ? response.data : null;
        var orgList = data && data.orgList ? data.orgList : {};
        if (orgList.length > 0) {
            rowData = orgList[0];
        }


        var EShopConfigForm = new Sys.UI.Form();
        EShopConfigForm.showMDI('sale/eshoporder/eshop/EShopConfig.gspx', rowData);
        EShopConfigForm.add_close(function () {
            form.close();
        })


    },
    //对账方式
    checkAccountType: {
        NoNeed: 0,
        ByOrder: 2,
        ByOtype: 3
    },
    openUrl: function () {
        var shoptype = this._selectShoptypeInfo.shoptype;
        var url = "sale/eshoporder/eshop/getUrl/" + shoptype;
        $common.ajax({
            url: url,
            data: null,
            type: 'get',
            router: 'ngp',
            success: function (data) {
                if (data && data.data.length > 0) {
                    window.open(data.data);
                } else {
                    $common.alert("该网店还暂未维护授权操作指引文档");
                }
            }
        })
    },
    doInderectAuth: function (sender) {
        var form = sender.get_form();
        var authType = form.authType.get_value();
        var mode = this.get_context('mode');
        var shopType = this._selectShoptypeInfo.shoptype;
        var fullName = form.orgName.get_value();
        var param = {shopType: shopType, mode: mode, fullName: fullName};
        if (this.mutiSelectAppkey != null) {
            param.mutiSelectAppkey = this.mutiSelectAppkey
        }

        if (authType == 2 || authType == 5) {
            param.appSecret = form.appSecret.get_value();
            param.appKey = form.appKey.get_value();
            var result =
                $common.ajaxSync({
                    url: "sale/eshoporder/eshop/doAuthNew",
                    data: param,
                    router: 'ngp'
                });
            if (result.code != "200") {
                $common.alert(result.message);
                return;
            }
            var data = result ? result.data : null;
            if (data && data.success) {
                this.authStatus.authorizedAuth = true;
                this.authStatus.appSecret = param.appSecret;
                this.sendMessage("授权");
                $common.showOk("授权成功");
                return;
            }
            if (data && !data.success && data.message) {
                $common.alert(data.message);
            }
        }
    },
    _ShowSelectAppkeyView: function (form, data) {
        form.usualAuth.set_visible(false);
        form.threeStepAuth.set_visible(false);
        form.selectAppkey.set_visible(true);
        this.subscribeApplications = {};
        var checkedIndex = 0;

        if (form.appendBlock) {
            form.removeControlById("appendBlock");
        }
        var block = $createControl('Block', {ID: "appendBlock", CssClass: "dflex"}, form);


        for (var i = 0; i < data.length; i++) {
            var entity = new Object();
            entity.appName = data[i].appName;
            entity.description = data[i].description;
            entity.orderLinkUrl = data[i].orderLinkUrl;
            this.subscribeApplications[data[i].key] = entity;
            var panel = $createControl('RadioButton',
                {
                    ID: "selectBtn" + i,
                    GroupName: "selAppkey",
                    CssClass: "selBtn usuallyBtn SkinBorderHover",
                    Text: data[i].appName,
                    Tag: data[i].key,
                    OnChange: "doSelectAppkey",
                    Checked: i === 0
                },
                form, block);
            if (this.mutiSelectAppkey == data[i].key) {
                checkedIndex = i;
            }
        }
        block.appendTo(form.selectBtns);
        this.initSelectAppKey(checkedIndex, form, data);

    },
    initSelectAppKey: function (index, form, data) {
        form.selectdescription.set_text(data[index].description);
        form.orderLink.set_value(data[index].orderLinkUrl);
        this.mutiSelectAppkey = data[index].key;
        var btnControl = $craba.findControl("selectBtn" + index, form)
        btnControl.set_checked(true);
        var btnEle = form.appendBlock._element.children[index];
        btnEle.classList.add("SkinColor");
        btnEle.classList.add("SkinBorder");
        btnEle.classList.remove("usuallyBtn");

        var btn = $common.getBounds(btnEle); // 按钮的位置
        var box = $common.getBounds(form.Box.get_element()); // 面板的位置
        var x = btn.x - box.x + 20; // 箭头相对面板的的x坐标。
        form.arrow2.get_element().style.left = x + "px";
        form.arrow1.get_element().style.left = x + 5 + "px";
    },
    doSelectAppkey: function (sender) {
        var form = this.get_form();
        var tag = sender.get_tag();
        var btns = $common.getElementsByClassName(sender.get_parent().get_element(), 'selBtn', false);
        for (var i = 0; i < btns.length; i++) {
            btns[i].classList.remove("SkinColor");
            btns[i].classList.remove("SkinBorder");
            btns[i].classList.remove("checked");
            //  btns[i].classList.remove("usuallyBtn");
            btns[i].classList.add("usuallyBtn");
        }

        var entity = this.subscribeApplications[tag];
        form.selectdescription.set_text(entity.description);
        form.orderLink.set_value(entity.orderLinkUrl);
        this.mutiSelectAppkey = tag;
        var btnEle = sender.get_element().parentElement;
        btnEle.classList.add("SkinColor");
        btnEle.classList.add("SkinBorder");
        btnEle.classList.remove("usuallyBtn");

        var btn = $common.getBounds(btnEle); // 按钮的位置
        var box = $common.getBounds(form.Box.get_element()); // 面板的位置

        var x = btn.x - box.x + 20; // 箭头相对面板的的x坐标。
        form.arrow2.get_element().style.left = x + "px";
        form.arrow1.get_element().style.left = x + 5 + "px";
    },
    handlePlatformuthType: function (sender) {
        var form = this.get_form();
        if (this._selectShoptypeInfo.shoptype == 114) {
            form.onlineEshopId.set_visible(true);
            form.BranchStorePanel.set_visible(false);
            form.grid.set_visible(false);
            var platformauthType = form.platformauthType.get_value();
            if (platformauthType == 1) {
                form.onlineEshopId.set_visible(false);
                form.BranchStorePanel.set_visible(true);
                form.grid.set_visible(true);
            }
        }
        if (this._selectShoptypeInfo.shoptype == 116 || this._selectShoptypeInfo.shoptype == 134 || this._selectShoptypeInfo.shoptype == 164) {
            form.BranchStorePanelMT.set_visible(false);
            form.MTgrid.set_visible(false);
            form.shopAccount.set_visible(true);
            form.usualAuth.set_visible(true);
            var platformauthType = form.platformauthType.get_value();
            if (platformauthType == 1) {
                form.BranchStorePanelMT.set_visible(true);
                form.shopAccount.set_visible(false);
                form.usualAuth.set_visible(false);
                form.MTgrid.set_visible(true);
                if (form.MTgrid._newRowDatas && form.MTgrid._newRowDatas.length == 0) {
                    form.MTgrid.appendRowData({
                        auth: "<font color='white' style='color:#2c57a0;border-radius:3px;padding:2px;padding-left:4px;padding-right:4px'>授权</font>",
                        enabled: true
                    });
                }
            }
        }

    },
    getBranchEshop: function (sender) {
        var form = sender.get_form();
        var saveData = form.saveData();
        var param = new Object();
        param.eshopAccount = saveData.eshopAccount;
        param.shoptype = this._selectShoptypeInfo.shoptype;
        var res =
            $common.ajaxSync({
                url: "sale/eshoporder/eshop/getBranchEshop",
                data: param,
                router: 'ngp'
            });
        if (res.code == '200') {
            form.grid.dataBind(res.data);
        }
    },
    doStopEshop: function (sender) {
        var form = sender.get_form();
        var grid = form.grid;
        var rowDatas = grid.get_selectedItems();

        if (!rowDatas || rowDatas.length == 0) {
            $common.showInfo("请至少选择一条网店！");
            return;
        }
        for (var i = 0; i < rowDatas.length; i++) {
            rowDatas[i].enabled = false;
            this.get_form().grid.modifyRowData(rowDatas[i].__rowIndex, rowDatas[i]);

        }

    },
    doOpenEshop: function (sender) {
        var form = sender.get_form();
        var grid = form.grid;
        var rowDatas = grid.get_selectedItems();
        if (!rowDatas || rowDatas.length == 0) {
            $common.showInfo("请至少选择一条网店！");
            return;
        }
        for (var i = 0; i < rowDatas.length; i++) {
            rowDatas[i].enabled = true;
            this.get_form().grid.modifyRowData(rowDatas[i].__rowIndex, rowDatas[i]);

        }
    },
    doOperation: function (sender, eventArgs) {

    },
    doIconColumnClick: function (sender, args) {
        var form = this.get_form();
        var grid = this.get_form().MTgrid;
        if (args.get_buttonIndex() == 0) {
            grid.insertRowData(args.get_rowIndex(), {
                auth: "<font color='white' style='color:#2c57a0;border-radius:3px;padding:2px;padding-left:4px;padding-right:4px'>授权</font>",
                enabled: true
            });
            //grid.insertRow(args.rowIndex);
        } else if (args.get_buttonIndex() == 1) {
            var eshopBranch = [];
            if (form.MTgrid) {
                if (form.MTgrid.get_dataSource() && form.MTgrid.get_dataSource().length > 0) {
                    eshopBranch = form.MTgrid.get_dataSource();
                } else if (form.MTgrid.get_allItems() && form.MTgrid.get_allItems().length > 0) {
                    eshopBranch = form.MTgrid.get_allItems();
                }
            }
            if (eshopBranch.length > 1) {
                grid.deleteRow(args.get_rowIndex());
            }

        }
    },

    doEditEshopAccount: function (sender, args) {
        if (sender && !sender.get_dataField()) {
            return;
        }
        var row = args.get_rowData();
        if (row && row.eshopAccount && row.eshopAccount != '') {
            sender.set_enabled(false);
            return;
        }
        sender.set_enabled(true);
    },

    uploadFile: function (sender) {
        var form = sender.get_form();
        this._childForm.submitBtn.set_enabled(false);
        if (!form.importFile.get_text()) {
            this._childForm.submitBtn.set_enabled(true);
            $common.checkTips(!form.importFile.get_text(), "请选择要导入的文件！", form.importFile);
        }
        var headers = {}; // 自定义请求头，传了该参数，则使用平台新的提交上传文件..
        var panelID = sender.get_tag(); // 这样做的目的是平台内部会根据panel.saveData()局部读取数据，而不是form.saveData()会触发全局验证
        $uploader(form[panelID], // 只会当前控件对应容器的FileUpload触发panel的saveData
            $createDelegate(this, this.doImportSucceeded),
            $createDelegate(this, this.doImportFailed),
            function (data) { // 支持动态构造服务端需要的参数，因此不是必须再gspx写隐藏域<HiddenField>也可以传参
            }, headers); // 自定义请求头，传了该参数，则使用平台新的提交上传文件..
        this.exportState = true;
    }
    ,
    doImportSucceeded: function (sender, result) {
        if (result.code != '200') {
            $common.showError("导入失败，" + result.message);
            return;
        }
        var _this = this;
        _this._activeTimer = new Sys.Timer();
        this._stop = false;
        var intervalCount = 2000;
        _this._activeTimer.set_interval(intervalCount);
        _this._activeTimer.set_enabled(true);
        _this._activeTimer.add_tick(Function.createDelegate(this, function () {
            _this.getMessage(result.data);
        }));

    },
    doImportFailed: function (sender, result) {
        var form = this.get_form();
        if (result) {
            $common.alert(result);
        }
        _this._childForm.submitBtn.set_enabled(true);
    },
    doExport: function () {
        var form = this.get_form();
        var _this = this;
        this._childForm = form.import.showModal('导入子网店', {});
        this._childForm.add_closed(function (popup) {
            if (_this.exportState) {
                _this.getMeituanBrachEshop();
            }
        });
    },
    downloadRealtionModel: function (sender) {
        $common.download("/sale/eshoporder/template/" + encodeURI("子网店导入模板.xlsx"));
    },
    getMeituanBrachEshop: function () {
        var form = this.get_form();
        var res = $common.ajaxSync({
            url: "sale/eshoporder/eshop/getMeituanBrachEshop",
            data: null,
            router: 'ngp'
        });
        if (res.code == 200) {
            if (res.data && res.data.length > 0) {
                form.MTgrid.dataBind(res.data);
            }
        }
    },
    getMessage: function (processId) {
        var form = this.get_form();
        if (null == form) {
            return;
        }
        var requestParam = new Object();
        requestParam.requestStr = processId;
        var _this = this;
        if (_this._childForm.processBar) {
            _this._childForm.processBar.set_value(80);
        } else {
            _this._activeTimer.set_enabled(false);
            return;
        }
        this.get_service().post("sale/eshoporder/common/getProcessMsg", requestParam,
            function (res) {
                var obj = res.data;
                if (!obj) {
                    return;
                }
                if (obj.completed) { //已经结束
                    _this._childForm.processBar.set_value(100);
                    _this._activeTimer.set_enabled(false);
                    _this._childForm.submitBtn.set_enabled(true);
                    if (obj.message) {
                        _this._childForm.info.set_text(obj.message);
                        _this._stop = true;
                    }
                }
            }, function (error) {
                if (_this._activeTimer) {
                    _this._activeTimer.set_enabled(false);
                }
                _this._stop = true;
                $common.alert(error);
            }
            , false);
    },

    doShopAccountChange: function (sender) {
        var form = sender.get_form();
        if ((!form.cbxOrderhp || !form.cbxOrderhp.get_visible())
            && (!form.selectAppkey || !form.selectAppkey.get_visible())) {
            //不显示订购时不需要操作
            return;
        }
        var shopAccountTxt = sender.get_value();
        shopAccountTxt = shopAccountTxt ? ("【" + shopAccountTxt + "】") : "该网店";
        shopAccountTxt = "请使用" + shopAccountTxt + "主账号登录服务市场进行订购";
        this.doSetDingGouHintText(form, shopAccountTxt);
        this.checkShopAccountHasOrdered(form, sender.get_value());
    },

    doSetDingGouHintText: function (form, shopAccountTxt) {
        if (!shopAccountTxt) {
            shopAccountTxt = "请使用该网店主账号登录服务市场进行订购";
        }
        if (form.cbxOrderhp && form.cbxOrderhp.get_visible()) {
            var showTxt = shopAccountTxt.length > 23 ? shopAccountTxt.substring(0, 22) + "..." : shopAccountTxt;
            form.lblAfterDGRemark.set_text(showTxt);
            form.lblAfterDGRemark.set_hint(shopAccountTxt);
            return;
        }

        if (form.selectAppkey && form.selectAppkey.get_visible()) {
            var showTxt = shopAccountTxt.length > 45 ? shopAccountTxt.substring(0, 44) + "..." : shopAccountTxt;
            form.lblAfterDingGouRemark.set_text(showTxt);
            form.lblAfterDingGouRemark.set_hint(shopAccountTxt);
        }
    },
    checkShopAccountHasOrdered: function (form, shopAccountTxt) {
        if (!shopAccountTxt) {
            form.btnOrder.set_text("去订购");
            form.btnUsualOrder.set_text("去订购");
            return;
        }
        var param = {};
        param.eshopAccount = shopAccountTxt;
        param.shopType = this._selectShoptypeInfo.shoptype;
        var res = $common.ajaxSync({
            url: "sale/eshoporder/eshop/checkSubscribe",
            data: param,
            router: 'ngp'
        });
        if (res.code == "200" && res.data) {
            form.btnOrder.set_text("已订购");
            form.btnUsualOrder.set_text("已订购");
        } else {
            form.btnOrder.set_text("去订购");
            form.btnUsualOrder.set_text("去订购");
        }
    },

    getDefaultKtype: function () {
        var res = $common.ajaxSync({
            url: "sale/eshoporder/eshop/getDefaultStock",
            router: 'ngp',
            type: "post"
        });
        // var res = $common.ajaxSync({
        //     url: "sale/eshoporder/eshop/getMeituanBrachEshop",
        //     data: null,
        //     router: 'ngp'
        // });
        if (res.code == "200" && res.data) {
            this._defaultKtype = res.data;
        }
        return;
    },
    dispose: function () {
        sale.eshoporder.eshop.EshopAddAction.callBaseMethod(this, 'dispose');
    }
};
sale.eshoporder.eshop.EshopAddAction.registerClass('sale.eshoporder.eshop.EshopAddAction', Sys.UI.PageAction);
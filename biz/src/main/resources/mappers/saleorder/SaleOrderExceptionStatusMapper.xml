<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.SaleOrderExceptionStatusMapper">

    <insert id="insertBatch">
        INSERT INTO td_deliver_exception_status (id, profile_id, vchcode, status,bill_type)
        VALUE
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id}, #{entity.profileId}, #{entity.vchcode}, #{entity.status},#{entity.billType})
        </foreach>
    </insert>

    <select id="getSaleOrderExceptionInfoList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.dto.order.SaleOrderExceptionInfoDTO">
        select id as id, mapping_state as hasMapping from pl_eshop_sale_order
        where   profile_id=#{profileId} and id IN
        <foreach collection="ids" separator="," item="id" open="(" close=")" >
            #{id}
        </foreach>
        AND deleted = 0 and mapping_state=0
    </select>

    <select id="countExceptionStatus"
            resultType="com.wsgjp.ct.sale.biz.jarvis.dto.DeliverExceptionStatusCountDTO">
        SELECT `status`.status, COUNT(1) AS `count`
        FROM td_deliver_exception_status `status`
        where profile_id=#{profileId} and bill_type=1 and vchcode in
       <foreach collection="vchcodes" separator="," item="vchcode" open="(" close=")" >
            #{vchcode}
        </foreach>
        group by `status`.status
    </select>
</mapper>
package com.wsgjp.ct.sale.biz.eshoporder.entity.enums.refund;

import bf.datasource.typehandler.CodeEnum;

import java.util.Arrays;
import java.util.stream.Collectors;

public enum AfterSaleFreightInterceptStatus implements CodeEnum {

    ALL(-1,"全部"),
    WAIT_INTERCEPT(0,"待拦截"),
    BEGIN_INTERCEPT(1,"拦截中"),
    SUCCEED_ONLINE_INTERCEPT(2,"物流拦截成功"),
    FAIL_INTERCEPT(3,"拦截失败"),
    SUCCEED_STOCK_INTERCEPT(4,"仓内拦截成功");


    private final int flag;

    private final String name;

    AfterSaleFreightInterceptStatus(int flag, String name) {
        this.flag = flag;
        this.name = name;
    }

    @Override
    public int getCode() {
        return flag;
    }

    @Override
    public String getName() {
        return name;
    }

    public static AfterSaleFreightInterceptStatus getEnum(int code) {
        return Arrays.stream(AfterSaleFreightInterceptStatus.values()).filter(item -> {
            return item.getCode() == code;
        }).collect(Collectors.toList()).get(0);
    }
}

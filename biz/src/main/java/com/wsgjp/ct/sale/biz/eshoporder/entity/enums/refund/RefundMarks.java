package com.wsgjp.ct.sale.biz.eshoporder.entity.enums.refund;

import bf.datasource.typehandler.CodeEnum;

public enum RefundMarks implements CodeEnum {
    NONE(200065, "无标记", true),
    REFUND_DELETED(200068, "已删除", true),
    UN_RELATION(200046, "网店商品未对", true),
    REFUSE_PACKAGE(200070, "拒收件", true),
    DISTRIBUTION(200072, "分销", true),
    SYS_PROMISED_CONFIRM_TIME_BEYOND(200073, "审核超时", false),
    PROMISED_CONFIRM_TIME_BEYOND(200074, "退货收货超时", false),
    PROMISED_AGREE_TIME_BEYOND(200075, "线上同意退款超时", false),
    PROMISED_DELIVER_TIME_BEYOND(200076, "补发超时", false),
    PROMISED_RECEIVE_TIME(200077, "线上同意退货超时", false),
    SHANG(2002, "商", true),
    J<PERSON>(2001, "价", true),
    LEI(2000, "类", true),
    TB_SELLER_TAKE_RESPONSIBILITY(90000032, "商家责任", true),
    RETURN_GOODS_POSTAGE(99990001, "退运费", true),
    ENCLOSURE(200089, "附", true),
    PING_TAI_SHENG_DAN(90480003, "平台补发生单", true),
    QI_MEI_ORDER(99000001, "奇门", true),
    REFUND_ERROR(2010, "退款金额异常", true),
    BUNKER(905200012, "集运仓合包", true),
    AFTER_SALE_HANDLE_TIME_BEYOND(2011, "售后处理超时", true);


    private final int flag;

    private final String name;

    private boolean needFilter;

    RefundMarks(int flag, String name, boolean needFilter) {
        this.flag = flag;
        this.name = name;
        this.needFilter = needFilter;
    }

    @Override
    public int getCode() {
        return flag;
    }

    @Override
    public String getName() {
        return name;
    }

    public boolean isNeedFilter() {
        return needFilter;
    }

}

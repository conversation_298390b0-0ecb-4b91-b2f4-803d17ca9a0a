<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.GoodsStockApiMapper">
    <select id="getSkuStocks"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.GoodsSkuStock">
        <include refid="handMapping"/>
        union
        <include refid="xcodeNormal"/>


    </select>
    <sql id="handMapping">
        select e.eshop_account,mp.platform_num_id,mp.platform_properties,mapping.ptype_id,ifnull(p.fullname,'') as ptypeName,ifnull(gs.ktype_id,0)
        ktype_id,ifnull(k.fullname,'') ktype_name,
        mapping.sku_id,ifnull(bar.fullbarcode,'') barcode,ifnull(pu.unit_rate,1) as unit_rate,ifnull(pu.unit_name,'') as unit_name,ifnull(gs.qty,0)
        qty,round(ifnull(gs.qty,0)/ifnull(pu.unit_rate,1),4) unit_qty,0 as sale_unit_qty
        from pl_eshop_product_sku mp
        left join pl_eshop_product_sku_mapping mapping on mapping.unique_id = mp.unique_id and mapping.profile_id = mp.profile_id and mapping.eshop_id = mp.eshop_id
        left join acc_goodsstock_detail gs on mapping.sku_id = gs.sku_id and mp.profile_id = gs.profile_id
        left join base_ptype p on p.id = mapping.ptype_id and p.profile_id = mp.profile_id
        left join base_ptype_unit pu
        on pu.id = mapping.unit_id and pu.profile_id = mp.profile_id and pu.ptype_id = mapping.ptype_id
        left join base_ptype_fullbarcode bar
        on bar.ptype_id = mapping.ptype_id and bar.sku_id = mapping.sku_id and bar.unit_id = mapping.unit_id and
        bar.profile_id = mp.profile_id
        left join base_ktype k on k.id = gs.ktype_id and k.profile_id = gs.profile_id
        left join pl_eshop e on e.otype_id = mp.eshop_id and e.profile_id = mp.profile_id and e.deleted = 0
        where mp.profile_id = #{profileId} and mapping.ptype_id >0 <include refid="condition"/>
        <if test="request.ptypeIds != null and request.ptypeIds.size() > 0 ">
            and mapping.ptype_id in
            <foreach collection="request.ptypeIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>
    <sql id="xcodeNormal">
        select e.eshop_account,mp.platform_num_id,mp.platform_properties,p.id as ptypeId,ifnull(p.fullname,'') as ptypeName,ifnull(gs.ktype_id,0)
        ktype_id,ifnull(k.fullname,'') ktype_name,
        bpx.sku_id,ifnull(bar.fullbarcode,'') barcode,ifnull(pu.unit_rate,1) as unit_rate,ifnull(pu.unit_name,'') as unit_name,ifnull(gs.qty,0)
        qty,round(ifnull(gs.qty,0)/ifnull(pu.unit_rate,1),4) unit_qty,0 as sale_unit_qty
        from pl_eshop_product_sku mp
        left join pl_eshop_product_sku_expand ex on mp.profile_id=ex.profile_id and mp.eshop_id =ex.eshop_id and mp.unique_id=ex.unique_id
        left join base_ptype_xcode bpx on bpx.profile_id=mp.profile_id and bpx.info_type=0 and bpx.xcode=mp.platform_xcode
        left join acc_goodsstock_detail gs on bpx.sku_id = gs.sku_id and mp.profile_id = gs.profile_id
        left join base_ptype p on p.id = bpx.ptype_id and p.profile_id = mp.profile_id
        left join base_ptype_unit pu
        on pu.id = bpx.unit_id and pu.profile_id = mp.profile_id and pu.ptype_id = bpx.ptype_id
        left join base_ptype_fullbarcode bar
        on bar.ptype_id = bpx.ptype_id and bar.sku_id = bpx.sku_id and bar.unit_id = bpx.unit_id and
        bar.profile_id = mp.profile_id
        left join base_ktype k on k.id = gs.ktype_id and k.profile_id = gs.profile_id
        left join pl_eshop e on e.otype_id = mp.eshop_id and e.profile_id = mp.profile_id and e.deleted = 0
        where mp.profile_id = #{profileId}
        and mp.platform_xcode!=''
        and bpx.xcode!=''
        and p.stoped=0
        and p.deleted=0
        and p.id>0
        and ex.mapping_type=1
        and bpx.xcode not in (select xcode from base_ptype_xcode where profile_id=#{profileId} and info_type=1)
        <include refid="condition"/>

        <if test="request.ptypeIds != null and request.ptypeIds.size() > 0 ">
            and p.id in
            <foreach collection="request.ptypeIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>
    <select id="getBatchStocks"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.GoodsBatchStock">
        select e.eshop_account,mp.platform_num_id,mp.platform_properties,mp.ptype_id,ifnull(p.fullname,'') as ptypeName,ifnull(gs.ktype_id,0)
        ktype_id,ifnull(k.fullname,'') ktype_name,
        mp.sku_id,bar.fullbarcode barcode,ifnull(pu.unit_rate,1) as unit_rate,ifnull(pu.unit_name,'') as unit_name,ifnull(gs.qty,0),gs.batchno,gs.produce_date,gs.expire_date,
        ifnull(gs.qty,0) qty,round(ifnull(gs.qty,0)/ifnull(pu.unit_rate,1),4) unit_qty from pl_eshop_product_sku_mapping mp
        left join acc_goodsstock_batch gs on mp.sku_id = gs.sku_id and mp.profile_id=gs.profile_id
        left join base_ptype p on p.id= mp.ptype_id and p.profile_id=mp.profile_id
        left join base_ptype_unit pu on pu.id = mp.unit_id and pu.profile_id=mp.profile_id and pu.ptype_id = mp.ptype_id
        left join base_ptype_fullbarcode bar on bar.ptype_id=mp.ptype_id and bar.sku_id=mp.sku_id and bar.unit_id=mp.unit_id and bar.profile_id=mp.profile_id
        left join base_ktype k on k.id = gs.ktype_id and k.profile_id=gs.profile_id
        left join pl_eshop e on e.otype_id=mp.eshop_id and e.profile_id=mp.profile_id and e.deleted=0
        where mp.profile_id = #{profileId} and mp.ptype_id >0 <include refid="condition"/>
    </select>
    <select id="getPlatformBatchStocks"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.PlatFormPtypeBatchStock">
        select * from (
                          select e.eshop_account,
                                 ifnull(gs.ktype_id, 0) as ktype_id,
                                 ifnull(k.fullname, '') as ktype_name,
                                 ifnull(k.usercode, '') as ktype_usercode,
                                 spm.platform_num_id,
                                 spm.platform_properties,
                                 pm.platform_fullname as platformName,
                                 gs.batchno,
                                 gs.produce_date,
                                 gs.expire_date,
                                 pu.unit_name as max_unit_name,
                                 round(ifnull(gs.qty, 0) / ifnull(pu.unit_rate, 1), 4) as max_unit_qty,
                                 pu.id as max_unit_id,
                                 bar.fullbarcode as max_unit_fullbarcode,
                                 xcode.xcode as max_unit_xcode,
                                 m.ptype_id,
                                 m.sku_id,
                                 ifnull(p.fullname, '') as ptype_name,
                                 p.usercode as ptype_usercode,
                                 case
                                     when pu.unit_code = 1 then ''
                                     else concat('1', pu.unit_name, '=', pu.unit_rate, bpu.unit_name) end as unit_rate_relation,
                                 ifnull(gs.qty, 0) qty,
                                 pu.unit_rate,
                                 bpu.unit_name as base_unit_name,
                                 bpu.id   as base_unit_id,
                                 bbar.fullbarcode as base_unit_fullbarcode,
                                 bxcode.xcode as base_unit_xcode
                          from pl_eshop e
                                   left join pl_eshop_product_sku spm on e.otype_id = spm.eshop_id and e.profile_id = spm.profile_id
                                   left join pl_eshop_product_sku_mapping m on m.profile_id = spm.profile_id and m.eshop_id = spm.eshop_id  and m.unique_id = spm.unique_id
                                   left join pl_eshop_product pm on e.otype_id = pm.eshop_id and e.profile_id = pm.profile_id and pm.platform_num_id = spm.platform_num_id
                                   inner join (select pu.*
                                               from base_ptype_unit pu
                                                        inner join (select mp.ptype_id, max(unit_rate) unit_rate
                                                                    from pl_eshop_product_sku_mapping mp
                                                                             inner join base_ptype_unit pu on pu.id = mp.unit_id and mp.profile_id = pu.profile_id
                                                                    where mp.profile_id = #{profileId} group by mp.ptype_id
                                               ) maxu on maxu.ptype_id = pu.ptype_id and maxu.unit_rate = pu.unit_rate
                                               where pu.profile_id = #{profileId}) pu
                                              on pu.id = m.unit_id and pu.profile_id = e.profile_id and pu.ptype_id = m.ptype_id
                                   left join base_ptype_unit bpu
                                             on bpu.profile_id = e.profile_id and bpu.ptype_id = m.ptype_id and bpu.unit_code = 1
                                   left join base_ptype_fullbarcode bar
                                             on bar.ptype_id = m.ptype_id and bar.sku_id = m.sku_id and bar.unit_id = pu.id and
                                                bar.profile_id = e.profile_id
                                   left join base_ptype_fullbarcode bbar
                                             on bbar.ptype_id = m.ptype_id and bbar.sku_id = m.sku_id and bbar.unit_id = bpu.id and
                                                bbar.profile_id = e.profile_id
                                   left join base_ptype_xcode xcode
                                             on xcode.ptype_id = m.ptype_id and xcode.sku_id = m.sku_id and xcode.unit_id = pu.id and
                                                xcode.profile_id = e.profile_id
                                   left join base_ptype_xcode bxcode on bxcode.ptype_id = m.ptype_id and bxcode.sku_id = m.sku_id and
                                                                        bxcode.unit_id = bpu.id and bxcode.profile_id = e.profile_id
                                   left join acc_goodsstock_batch gs on m.sku_id = gs.sku_id and e.profile_id = gs.profile_id
                                   left join base_ptype p on p.id = m.ptype_id and p.profile_id = e.profile_id
                                   left join base_ktype k on k.id = gs.ktype_id and k.profile_id = gs.profile_id
                          where e.profile_id = #{profileId}
                            and e.deleted = 0
                            and m.ptype_id > 0
                            and e.eshop_account = #{request.eshopAccount}
                      ) a group by sku_id,ktype_id,batchno,produce_date,expire_date
    </select>
    <select id="goodsSaleStock"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.stock.GoodsStocksResponse">
        select
        ptype.id as ptype_id,
        sku.id as sku_id,
        ifnull(sum(inventory.qty),0) as qty,
        ifnull(sum(inventory.qty),0) - ifnull(sum(sale.qty),0) as saleQty,
        inventory.ktype_id
        from
        base_ptype ptype left join
        base_ptype_sku sku on ptype.id  = sku.ptype_id and ptype.profile_id = sku.profile_id
        left join acc_inventory_detail inventory on inventory.sku_id =  sku.id and inventory.ptype_id = sku.ptype_id and inventory.profile_id = sku.profile_id
        left join stock_record_qty_sale sale on sale.sku_id = sku.id and sale.profile_id = sku.profile_id and inventory.ktype_id = sale.ktype_id
        where ptype.profile_id = #{profileId}   and inventory.ktype_id is not null
        <if test="request.ptypeIds != null and request.ptypeIds.size() > 0 ">
            and  inventory.ptype_id in
            <foreach collection="request.ptypeIds" item="ptypeId" separator="," open="(" close=")">
                #{ptypeId}
            </foreach>
        </if>
        <if test="request.ktypeIds != null and request.ktypeIds.size() > 0 ">
            and  inventory.ktype_id in
            <foreach collection="request.ktypeIds" item="ktypeId" separator="," open="(" close=")">
                #{ktypeId}
            </foreach>
        </if>
        and inventory.ktype_point_type in
        <foreach collection="request.ktypePointTypeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by sku.id,inventory.ktype_id
        union
        select
        ptype.id as ptype_id,
        sku.id as sku_id,
        ifnull(sum(inventory.qty),0) as qty,
        ifnull(sum(inventory.qty),0) - ifnull(sum(sale.qty),0) as saleQty,
        sale.ktype_id
        from
        base_ptype ptype left join
        base_ptype_sku sku on ptype.id  = sku.ptype_id and ptype.profile_id = sku.profile_id
        left join stock_record_qty_sale sale on sale.sku_id = sku.id and sale.profile_id = sku.profile_id
        left join acc_inventory_detail inventory on inventory.sku_id =  sku.id and inventory.ptype_id = sku.ptype_id and inventory.profile_id = sku.profile_id  and inventory.ktype_id = sale.ktype_id
        where ptype.profile_id = #{profileId}   and sale.qty != 0
        <if test="request.ptypeIds != null and request.ptypeIds.size() > 0 ">
            and  ptype.id in
            <foreach collection="request.ptypeIds" item="ptypeId" separator="," open="(" close=")">
                #{ptypeId}
            </foreach>
        </if>
        <if test="request.ktypeIds != null and request.ktypeIds.size() > 0 ">
            and  sale.ktype_id in
            <foreach collection="request.ktypeIds" item="ktypeId" separator="," open="(" close=")">
                #{ktypeId}
            </foreach>
        </if>
        and inventory.ktype_point_type is null
        group by sku.id,sale.ktype_id

    </select>
    <sql id="condition">
        <if test="request.ktypeIds.size() > 0">
            and gs.ktype_id in
            <foreach collection="request.ktypeIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="request.platFormList != null and request.platFormList.size() > 0 ">
                and e.eshop_account = #{request.eshopAccount}
                and (mp.platform_num_id,mp.platform_properties_name) in
                <foreach collection="request.platFormList" item="item" separator="," open="(" close=")">
                    (#{item.platformNumId},#{item.platformProperties})
                </foreach>
            </when>
            <otherwise>
                <if test="request.eshopAccount !=null and request.eshopAccount.length() > 0">
                    and e.eshop_account = #{request.eshopAccount}
                </if>
            </otherwise>
        </choose>

    </sql>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<Page xmlns="Craba.UI" Title="导入收款信息"
      OnClose="doClosing"
      ActionType="sale.eshoporder.eshopplatformcheck.GatherOrderImportAction,sale/eshoporder/eshopplatformcheck/GatherOrderImport.js,sale/jarvis/DeliverBill/common/CommonJs.js" AllowResize="false">
    <FileUpload CssClass="plr20 ptb10" WebMethod="sale/eshoporder/platformcheck/DoGatherOrderImport">
        <FlowPanel LayoutDirection="Vert"   ItemLabelWidth="65"  Width="500" ID="mainBody">
            <SpeedButton ID="btnDownloadSingleSheetTemplate" TextColor="Blue" Text=" 下载模板" OnClick="doDownloadTemplate" Hint="下载系统默认支持的模板"/>
            <SelectorEdit ID="seEShopName" Label="网店名称：" DataField="otypeId" DisplayField="fullname"
                NullDisplayText="仅支持单选"  OnEnterPress="doEshopSelectorEnterPress" Width="500"
                OnButtonClick="doEshopInit" OnSelectorSelected="doEshopSelectorSelected" Required="true"/>
            <SelectorEdit ID="edAType" Label="收款账户：" OnButtonClick="doAtypeSelectorInit"
                OnEnterPress="doEnterPress" OnSelectorSelected="doAtypeInfoSelect"  Width="500" OnChange="doAtypeChange"
                DataField="atypeId" DisplayField="atypeName" Tag="btype" Required="true"/>
            <Block CssClass="fileIcon FlexVert FlexCenter Border dashed bgfa ptb20"  ID="block23" Width="500">
                <Button Icon="aicon-upload" Flat="true" ID="btnIcon"/>
                <Label Text="选择文件后导入或者下载模板后导入" ID="lfileName"/>
                <Label Text="请选择.xls或者.xlsx文件，每次只能导入一个文件(支持拖入文件)" CssClass="aaa" />
                <FileEdit ID="loadfile"  DropTarget='block23' CssClass="ButtonOpacity" OnChange="doSubmitAjax" Tag='showName' Accept=".xls,.xlsx" />
            </Block>
        </FlowPanel>
    </FileUpload>
    <FlexColumn>
        <FlexColumn ID="process" CssClass="FlexCenter" Visible="false">
            <Label ID="fileName" CssStyle="margin-top:70px;float:left" />
            <HPanel>
                <ProgressBar ID="processBar" Width="300" Height="7" CssStyle="margin-top:20px" ShowValue="false" Value="100" />
                <Label ID="progress" />
            </HPanel>
        </FlexColumn>
        <FlexColumn CssClass="FlexCenter" ID="success" CssStyle="margin-top:20px" Visible="false">
            <Image Url="sale/eshoporder/image/success.svg" Width="50" Height="50" />
            <Label ID="message" Text="导入收款完成" FontSize="16" />
            <HBlock>
                <Label ID="exsuc" Text="导入成功：" FontSize="15" />
                <Label ID="suc" Label="" Text="1" FontSize="15" />
                <Label Label="" Text="           " FontSize="15" />
                <Label ID="exfail" Text="导入失败：" FontSize="15" />
                <Label ID="fail" Text="0" TextColor="red" FontSize="15" />
            </HBlock>
        </FlexColumn>
    </FlexColumn>
    <HBlock CssClass='BottomBlock'>
        <Block CssClass='Flex1'/>
        <Button ID="btnStart" OnClick="doImport" CssClass="SpecialButton" Text="开始导入"  Tag='loadfile'/>
        <Button ID="exportError" Text="导出失败数据" CssClass='SpecialButton' OnClick="exportErrorDate" Visible="false" />
        <CancelButton Text="退出"/>
    </HBlock>

    <Style>
        .import_bill {
        padding: 0 !important;
        }

        .import_bill .TabHeader {
        background-color: #eff3f4;
        height: 100%;
        width: 80px;
        }

        .import_bill .TabHeader .TabCaption {
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #eff3f4;
        border-left-style:none;
        }

        .import_bill .TabCaption.active {
        background-color: #ffffff;
        color: #000;
        }

        .import_bill .FlowPanel .FlowItem {
        margin-bottom: 20px;
        }

        .import_bill .FlowPanel .FlowItem,
        .import_bill .FlowPanel .FlowItem .FileEditor {
        width: 100% !important;
        }

        .import_bill .import_desc {
        display: flex;
        flex-direction: column;
        padding: 10px 5px;
        margin-left: 20px;
        background-color: #ecf4ff;
        border: 1px solid #e2e3e4;
        border-radius: 4px;
        }

        .import_bill .TabPage .FlexBlock {
        padding-right: 20px;
        padding-top: 15px;
        }

        .import_bill .SpecialButton {
        margin-left: 84px;
        }

        .import_bill .BottomBlock {
            display: flex !important;
            justify-content: end;
        }

    </Style>
</Page>
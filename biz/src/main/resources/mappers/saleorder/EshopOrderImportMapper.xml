<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopOrderImportMapper">

    <select id="getEshopProductSkuInfoListByPtypeCode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.response.EshopProductSkuInfo">
        SELECT  p.id as ptypeId,ps.id as skuId,p.profile_id as profileId,p.userCode,p.fullName,p.stoped AS ptypeStoped,ps.stoped AS skuStoped,pu.id as unitId,
        barcode.fullBarCode,p.tax_rate,p.propenabled,
        IFNULL(pu.`unit_code`,1) AS unitCode,ifnull(ps.stoped,0) as  skuStoped,
        IFNULL(pu.`unit_rate`,1) AS unitRate, IFNULL(pu.`unit_name`,'') AS unitName,
        CONCAT_WS(':',CASE WHEN ps.prop_name1 <![CDATA[<>]]> '' THEN ps.prop_name1 END,
        CASE WHEN ps.prop_name2 <![CDATA[<>]]> '' THEN ps.prop_name2 END,
        CASE WHEN ps.prop_name3 <![CDATA[<>]]> '' THEN ps.prop_name3 END,
        CASE WHEN ps.prop_name4 <![CDATA[<>]]> '' THEN ps.prop_name4 END,
        CASE WHEN ps.prop_name5 <![CDATA[<>]]> '' THEN ps.prop_name5 END,
        CASE WHEN ps.prop_name6 <![CDATA[<>]]> '' THEN ps.prop_name6 END) as localPropertiesName,
        CONCAT_WS(':',CASE WHEN ps.propvalue_name1 <![CDATA[<>]]> '' THEN ps.propvalue_name1 END,
        CASE WHEN ps.propvalue_name2 <![CDATA[<>]]> '' THEN ps.propvalue_name2 END,
        CASE WHEN ps.propvalue_name3 <![CDATA[<>]]> '' THEN ps.propvalue_name3 END,
        CASE WHEN ps.propvalue_name4 <![CDATA[<>]]> '' THEN ps.propvalue_name4 END,
        CASE WHEN ps.propvalue_name5 <![CDATA[<>]]> '' THEN ps.propvalue_name5 END,
        CASE WHEN ps.propvalue_name6 <![CDATA[<>]]> '' THEN ps.propvalue_name6 END) as localProperties
        FROM base_ptype p
        LEFT JOIN base_ptype_fullbarcode barcode ON barcode.ptype_id=p.id AND barcode.profile_id=p.profile_id
        LEFT JOIN base_ptype_sku ps ON ps.ptype_id=p.id  AND p.profile_id=ps.profile_id
        LEFT JOIN base_ptype_unit pu on pu.profile_id=p.profile_id and pu.ptype_id =p.id
        WHERE p.profile_id=#{profileId} AND p.deleted=0 AND ps.stoped =0  AND p.stoped = 0 and p.id is not null and ps.id is not null
        <if test="ptypeCodes!=null">
            and  p.usercode in
            <foreach collection="ptypeCodes" item="ptypeCode" separator="," index="i" close=")" open="(">
                #{ptypeCode}
            </foreach>
        </if>
        <if test="localPropertiesName!=null and localPropertiesName !=''">
            AND  CONCAT_WS(':',CASE WHEN ps.prop_name1 <![CDATA[<>]]> '' THEN ps.prop_name1 END,
            CASE WHEN ps.prop_name2 <![CDATA[<>]]> '' THEN ps.prop_name2 END,
            CASE WHEN ps.prop_name3 <![CDATA[<>]]> '' THEN ps.prop_name3 END,
            CASE WHEN ps.prop_name4 <![CDATA[<>]]> '' THEN ps.prop_name4 END,
            CASE WHEN ps.prop_name5 <![CDATA[<>]]> '' THEN ps.prop_name5 END,
            CASE WHEN ps.prop_name6 <![CDATA[<>]]> '' THEN ps.prop_name6 END)  =#{localPropertiesName}
        </if>
        <if test="localProperties!=null and localProperties !=''">
            AND CONCAT_WS(':',CASE WHEN ps.propvalue_name1 <![CDATA[<>]]> '' THEN ps.propvalue_name1 END,
            CASE WHEN ps.propvalue_name2 <![CDATA[<>]]> '' THEN ps.propvalue_name2 END,
            CASE WHEN ps.propvalue_name3 <![CDATA[<>]]> '' THEN ps.propvalue_name3 END,
            CASE WHEN ps.propvalue_name4 <![CDATA[<>]]> '' THEN ps.propvalue_name4 END,
            CASE WHEN ps.propvalue_name5 <![CDATA[<>]]> '' THEN ps.propvalue_name5 END,
            CASE WHEN ps.propvalue_name6 <![CDATA[<>]]> '' THEN ps.propvalue_name6 END) =#{localProperties}
        </if>
    </select>

    <select id="getEshopProductSkuInfoListByComboPtypeCode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.response.EshopProductSkuInfo">
        SELECT p.userCode,p.fullName,p.stoped AS ptypeStoped,p.`barcode` as barCode,p.pcategory,p.profile_id  AS profileId,
        c.combo_id AS comboId,detail.`ptype_id` AS ptypeId,detail.`sku_id` AS skuId,detail.`unit_id` AS unitId,pt.tax_rate,
        IFNULL(detail.`Qty`,0) AS ComboSkuQty,IFNULL(detail.`price`,0) AS ComboSkuPrice,  IFNULL(detail.`total`,0) AS ComboSkuTotal,
        IFNULL(detail.`gifted`,0) AS ComboSkuGifted,IFNULL(combopu.`unit_code`,1) AS unitCode,ifnull(detail.stoped,0) as  skuStoped,
        IFNULL(combopu.`unit_rate`,1) AS unitRate, IFNULL(combopu.`unit_name`,'') AS unitName,IFNULL(detail.`scale`,0) AS scaleRate,
        p.propenabled,c.sale_type
        FROM base_ptype p
        LEFT JOIN `base_ptype_combo` c ON p.profile_id =  c.profile_id  AND p.id = c.combo_id
        LEFT JOIN `base_ptype_combo_detail` detail ON detail.combo_id = c.combo_id AND detail.profile_id  = c.profile_id
        LEFT JOIN `base_ptype` pt ON detail.ptype_id = pt.id AND detail.profile_id  = pt.profile_id
        left join base_ptype_pic pic on pic.profile_id = p.profile_id and p.id = pic.ptype_id and pic.rowindex = 1
        LEFT JOIN base_ptype_unit combopu on combopu.profile_id=detail.profile_id and combopu.id =detail.unit_id and combopu.ptype_id=detail.ptype_id
        WHERE p.pcategory =2  AND p.profile_id  = #{profileId}  AND p.deleted=0 AND p.stoped = 0   AND c.combo_id > 0
        <if test="ptypeCodes!=null">
            and  p.usercode in
            <foreach collection="ptypeCodes" item="ptypeCode" separator="," index="i" close=")" open="(">
                #{ptypeCode}
            </foreach>
        </if>
    </select>

    <select id="getEshopProductSkuInfoListByComboPtypeName"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.response.EshopProductSkuInfo">
        SELECT p.userCode,p.fullName,p.stoped AS ptypeStoped,p.`barcode` as barCode,p.pcategory,p.profile_id  AS profileId,
        c.combo_id AS comboId,detail.`ptype_id` AS ptypeId,detail.`sku_id` AS skuId,detail.`unit_id` AS unitId,pt.tax_rate,
        IFNULL(detail.`Qty`,0) AS ComboSkuQty,IFNULL(detail.`price`,0) AS ComboSkuPrice,  IFNULL(detail.`total`,0) AS ComboSkuTotal,
        IFNULL(detail.`gifted`,0) AS ComboSkuGifted,IFNULL(combopu.`unit_code`,1) AS unitCode,ifnull(detail.stoped,0) as  skuStoped,
        IFNULL(combopu.`unit_rate`,1) AS unitRate, IFNULL(combopu.`unit_name`,'') AS unitName,IFNULL(detail.`scale`,0) AS scaleRate,
        p.propenabled,c.sale_type
        FROM base_ptype p
        LEFT JOIN `base_ptype_combo` c ON p.profile_id =  c.profile_id  AND p.id = c.combo_id
        LEFT JOIN `base_ptype_combo_detail` detail ON detail.combo_id = c.combo_id AND detail.profile_id  = c.profile_id
        LEFT JOIN `base_ptype` pt ON detail.ptype_id = pt.id AND detail.profile_id  = pt.profile_id
        left join base_ptype_pic pic on pic.profile_id = p.profile_id and p.id = pic.ptype_id and pic.rowindex = 1
        LEFT JOIN base_ptype_unit combopu on combopu.profile_id=detail.profile_id and combopu.id =detail.unit_id and combopu.ptype_id=detail.ptype_id
        WHERE p.pcategory =2  AND p.profile_id  = #{profileId}  AND p.deleted=0 AND p.stoped = 0   AND c.combo_id > 0
        <if test="ptypeNames!=null">
            and  p.fullname in
            <foreach collection="ptypeNames" item="ptypeName" separator="," index="i" close=")" open="(">
                #{ptypeName}
            </foreach>
        </if>
    </select>

    <select id="getEshopProductSkuInfoListByComboBarCode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.response.EshopProductSkuInfo">
        SELECT p.userCode,p.fullName,p.stoped AS ptypeStoped,p.`barcode` as barCode,p.pcategory,p.profile_id  AS profileId,
         c.combo_id AS comboId,detail.`ptype_id` AS ptypeId,detail.`sku_id` AS skuId,detail.`unit_id` AS unitId,pt.tax_rate,
        IFNULL(detail.`Qty`,0) AS ComboSkuQty,IFNULL(detail.`price`,0) AS ComboSkuPrice,  IFNULL(detail.`total`,0) AS ComboSkuTotal,
        IFNULL(detail.`gifted`,0) AS ComboSkuGifted,IFNULL(combopu.`unit_code`,1) AS unitCode,ifnull(detail.stoped,0) as  skuStoped,
        IFNULL(combopu.`unit_rate`,1) AS unitRate, IFNULL(combopu.`unit_name`,'') AS unitName,IFNULL(detail.`scale`,0) AS scaleRate,
        p.propenabled,c.sale_type
        FROM base_ptype p
        LEFT JOIN `base_ptype_combo` c ON p.profile_id =  c.profile_id  AND p.id = c.combo_id
        LEFT JOIN `base_ptype_combo_detail` detail ON detail.combo_id = c.combo_id AND detail.profile_id  = c.profile_id
        LEFT JOIN `base_ptype` pt ON detail.ptype_id = pt.id AND detail.profile_id  = pt.profile_id
        left join base_ptype_pic pic on pic.profile_id = p.profile_id and p.id = pic.ptype_id and pic.rowindex = 1
        LEFT JOIN base_ptype_unit combopu on combopu.profile_id=detail.profile_id and combopu.id =detail.unit_id and combopu.ptype_id=detail.ptype_id
        WHERE p.pcategory =2  AND p.profile_id  = #{profileId}  AND p.deleted=0 AND p.stoped = 0   AND c.combo_id > 0
        <if test="fullBarCodes!=null">
            and  p.barcode in
            <foreach collection="fullBarCodes" item="barcode" separator="," index="i" close=")" open="(">
                #{barcode}
            </foreach>
        </if>
    </select>

    <select id="getEshopProductSkuInfoListByBarCode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.response.EshopProductSkuInfo">
        SELECT  barcode.ptype_id as ptypeId,barcode.sku_id as skuId,barcode.profile_id as profileId,p.userCode,p.fullName,p.stoped AS ptypeStoped,ps.stoped AS skuStoped,barcode.unit_id as unitId,
        barcode.fullBarCode,p.tax_rate,p.propenabled,
        IFNULL(pu.`unit_code`,1) AS unitCode,ifnull(ps.stoped,0) as  skuStoped,
        IFNULL(pu.`unit_rate`,1) AS unitRate, IFNULL(pu.`unit_name`,'') AS unitName
        FROM base_ptype_fullbarcode barcode
        LEFT JOIN base_ptype p ON barcode.ptype_id=p.id AND barcode.profile_id=p.profile_id
        LEFT JOIN base_ptype_sku ps ON ps.id=barcode.sku_id  AND barcode.profile_id=ps.profile_id
        LEFT JOIN base_ptype_unit pu on pu.profile_id=barcode.profile_id and pu.id =barcode.unit_id and pu.ptype_id=barcode.ptype_id
        WHERE barcode.profile_id=#{profileId} AND p.deleted=0 AND ps.stoped =0  AND p.stoped = 0
        <if test="fullBarCodes!=null">
            and  barcode.fullbarcode in
            <foreach collection="fullBarCodes" item="barcode" separator="," index="i" close=")" open="(">
                #{barcode}
            </foreach>
        </if>
    </select>

    <select id="getEshopProductSkuInfoListByPtypeName"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.response.EshopProductSkuInfo">
        SELECT  p.id as ptypeId,ps.id as skuId,p.profile_id as profileId,p.userCode,p.fullName,p.stoped AS ptypeStoped,ps.stoped AS skuStoped,pu.id as unitId,
        barcode.fullBarCode,p.tax_rate,p.propenabled,
        IFNULL(pu.`unit_code`,1) AS unitCode,ifnull(ps.stoped,0) as  skuStoped,
        IFNULL(pu.`unit_rate`,1) AS unitRate, IFNULL(pu.`unit_name`,'') AS unitName,
        CONCAT_WS(':',CASE WHEN ps.prop_name1 <![CDATA[<>]]> '' THEN ps.prop_name1 END,
            CASE WHEN ps.prop_name2 <![CDATA[<>]]> '' THEN ps.prop_name2 END,
            CASE WHEN ps.prop_name3 <![CDATA[<>]]> '' THEN ps.prop_name3 END,
            CASE WHEN ps.prop_name4 <![CDATA[<>]]> '' THEN ps.prop_name4 END,
            CASE WHEN ps.prop_name5 <![CDATA[<>]]> '' THEN ps.prop_name5 END,
            CASE WHEN ps.prop_name6 <![CDATA[<>]]> '' THEN ps.prop_name6 END) as localPropertiesName,
        CONCAT_WS(':',CASE WHEN ps.propvalue_name1 <![CDATA[<>]]> '' THEN ps.propvalue_name1 END,
        CASE WHEN ps.propvalue_name2 <![CDATA[<>]]> '' THEN ps.propvalue_name2 END,
        CASE WHEN ps.propvalue_name3 <![CDATA[<>]]> '' THEN ps.propvalue_name3 END,
        CASE WHEN ps.propvalue_name4 <![CDATA[<>]]> '' THEN ps.propvalue_name4 END,
        CASE WHEN ps.propvalue_name5 <![CDATA[<>]]> '' THEN ps.propvalue_name5 END,
        CASE WHEN ps.propvalue_name6 <![CDATA[<>]]> '' THEN ps.propvalue_name6 END) as localProperties
        FROM base_ptype p
        LEFT JOIN base_ptype_sku ps on p.profile_id=ps.profile_id and p.id = ps.ptype_id
        LEFT JOIN base_ptype_fullbarcode barcode ON barcode.ptype_id=p.id AND barcode.profile_id=p.profile_id
        LEFT JOIN base_ptype_unit pu on pu.profile_id=p.profile_id  and pu.ptype_id=p.id AND pu.unit_code=1
        WHERE p.profile_id=#{profileId} AND p.deleted=0 AND p.stoped =0  AND p.stoped = 0 and p.id is not null and ps.id is not null
        <if test="ptypeNames!=null">
            and  p.fullname in
            <foreach collection="ptypeNames" item="ptypeName" separator="," index="i" close=")" open="(">
                #{ptypeName}
            </foreach>
        </if>
        <if test="localPropertiesName!=null and localPropertiesName !=''">
            AND  CONCAT_WS(':',CASE WHEN ps.prop_name1 <![CDATA[<>]]> '' THEN ps.prop_name1 END,
            CASE WHEN ps.prop_name2 <![CDATA[<>]]> '' THEN ps.prop_name2 END,
            CASE WHEN ps.prop_name3 <![CDATA[<>]]> '' THEN ps.prop_name3 END,
            CASE WHEN ps.prop_name4 <![CDATA[<>]]> '' THEN ps.prop_name4 END,
            CASE WHEN ps.prop_name5 <![CDATA[<>]]> '' THEN ps.prop_name5 END,
            CASE WHEN ps.prop_name6 <![CDATA[<>]]> '' THEN ps.prop_name6 END)  =#{localPropertiesName}
        </if>
        <if test="localProperties!=null and localProperties !=''">
            AND CONCAT_WS(':',CASE WHEN ps.propvalue_name1 <![CDATA[<>]]> '' THEN ps.propvalue_name1 END,
            CASE WHEN ps.propvalue_name2 <![CDATA[<>]]> '' THEN ps.propvalue_name2 END,
            CASE WHEN ps.propvalue_name3 <![CDATA[<>]]> '' THEN ps.propvalue_name3 END,
            CASE WHEN ps.propvalue_name4 <![CDATA[<>]]> '' THEN ps.propvalue_name4 END,
            CASE WHEN ps.propvalue_name5 <![CDATA[<>]]> '' THEN ps.propvalue_name5 END,
            CASE WHEN ps.propvalue_name6 <![CDATA[<>]]> '' THEN ps.propvalue_name6 END) =#{localProperties}
        </if>
    </select>

    <select id="getEshopProductSkuInfoListByComboXcode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.response.EshopProductSkuInfo">
        SELECT p.userCode,p.fullName,p.stoped AS ptypeStoped,p.`barcode` as barCode,p.pcategory,bpx.`xcode` AS xcode,pt.tax_rate,
        c.combo_id AS comboId,detail.`ptype_id` AS ptypeId,detail.`sku_id` AS skuId,detail.`unit_id` AS unitId,p.profile_id  AS profileId,
        IFNULL(detail.`Qty`,0) AS ComboSkuQty,IFNULL(detail.`price`,0) AS ComboSkuPrice,  IFNULL(detail.`total`,0) AS ComboSkuTotal,
        IFNULL(detail.`gifted`,0) AS ComboSkuGifted,IFNULL(combopu.`unit_code`,1) AS unitCode,ifnull(detail.stoped,0) as  skuStoped,
        IFNULL(combopu.`unit_rate`,1) AS unitRate, IFNULL(combopu.`unit_name`,'') AS unitName,IFNULL(detail.`scale`,0) AS scaleRate,
        p.propenabled,c.sale_type
        FROM base_ptype_xcode bpx
        LEFT JOIN `base_ptype` p ON p.profile_id =  bpx.profile_id  AND p.id = bpx.ptype_id
        LEFT JOIN `base_ptype_combo` c ON p.profile_id =  c.profile_id  AND p.id = c.combo_id
        LEFT JOIN `base_ptype_combo_detail` detail ON detail.combo_id = c.combo_id AND detail.profile_id  = c.profile_id
        LEFT JOIN `base_ptype` pt ON detail.ptype_id = pt.id AND detail.profile_id  = pt.profile_id
        LEFT JOIN base_ptype_pic pic on pic.profile_id = p.profile_id and detail.ptype_id = pic.ptype_id and pic.rowindex = 1
        LEFT JOIN base_ptype_unit combopu on combopu.profile_id=detail.profile_id and combopu.id =detail.unit_id and combopu.ptype_id=detail.ptype_id
        WHERE p.pcategory =2  AND p.profile_id  = #{profileId}  AND p.deleted=0 AND p.stoped = 0   AND c.combo_id > 0 AND bpx.info_type=1
        <if test="xcodes!=null">
            and  bpx.xcode in
            <foreach collection="xcodes" item="xcode" separator="," index="i" close=")" open="(">
                #{xcode}
            </foreach>
        </if>
    </select>

    <select id="getEshopProductSkuInfoListByXcode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.response.EshopProductSkuInfo">
        SELECT  xcode.ptype_id as ptypeId,xcode.sku_id as skuId,xcode.profile_id as profileId,p.userCode,p.fullName,p.stoped AS ptypeStoped,ps.stoped AS skuStoped,
        xcode.unit_id as unitId,xcode.xcode,p.tax_rate,p.propenabled,
        IFNULL(pu.`unit_code`,1) AS unitCode,ifnull(ps.stoped,0) as  skuStoped,
        IFNULL(pu.`unit_rate`,1) AS unitRate, IFNULL(pu.`unit_name`,'') AS unitName
        FROM base_ptype_xcode xcode
        LEFT JOIN base_ptype p ON xcode.ptype_id=p.id AND xcode.profile_id=p.profile_id
        LEFT JOIN base_ptype_sku ps ON ps.id=xcode.sku_id  AND xcode.profile_id=ps.profile_id
        LEFT JOIN base_ptype_unit pu on pu.profile_id=xcode.profile_id and pu.id =xcode.unit_id and pu.ptype_id=xcode.ptype_id
        WHERE xcode.profile_id=#{profileId} AND p.deleted=0 AND ps.stoped =0  AND p.stoped = 0  AND p.pcategory != 2
        <if test="xcodes!=null">
            and  xcode.xcode in
            <foreach collection="xcodes" item="xcode" separator="," index="i" close=")" open="(">
                #{xcode}
            </foreach>
        </if>
    </select>

    <select id="getEshopProductSkuInfoListByUserCode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.response.EshopProductSkuInfo">
        SELECT  p.id as ptypeId,ps.id as skuId,p.profile_id as profileId,p.userCode,p.fullName,p.stoped AS ptypeStoped,ps.stoped AS skuStoped,
        pu.id as unitId,xcode.xcode,p.tax_rate,p.propenabled,
        IFNULL(pu.`unit_code`,1) AS unitCode,ifnull(ps.stoped,0) as  skuStoped,
        IFNULL(pu.`unit_rate`,1) AS unitRate, IFNULL(pu.`unit_name`,'') AS unitName
        FROM base_ptype p
        LEFT JOIN base_ptype_sku ps ON ps.ptype_id=p.id  AND p.profile_id=ps.profile_id
        LEFT JOIN base_ptype_xcode xcode ON xcode.ptype_id=p.id AND xcode.profile_id=p.profile_id
        LEFT JOIN base_ptype_unit pu ON p.profile_id = pu.profile_id AND p.id = pu.ptype_id AND pu.unit_code = 1
        WHERE p.profile_id=#{profileId}  AND p.deleted=0 AND ps.stoped =0 AND p.stoped = 0 AND p.pcategory != 2
        <if test="xcodes!=null">
            and  p.usercode in
            <foreach collection="xcodes" item="xcode" separator="," index="i" close=")" open="(">
                #{xcode}
            </foreach>
        </if>
    </select>


    <select id="getNomarlEshopProductInfoListByPtypeName"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.response.EshopProductSkuInfo">
        SELECT
            IFNULL(pm.`platform_properties_name`,
        '') AS platformPropertiesName ,
        pep.`platform_fullname` AS platformFullName,
        IFNULL(psm.`platform_xcode`,
        pep.`platform_xcode`) AS platformXcode,
        pm.`platform_num_id` AS platformNumid,
        pm.`platform_sku_id` AS platformSkuid,
        pm.`unique_id` AS uniqueId,
        if(pm.`platform_pic_url` is null or pm.`platform_pic_url`='',pep.platform_pic_url,pm.`platform_pic_url`) AS platformPicUrl,
        p.userCode,
        p.fullName,
        p.stoped AS ptypeStoped,
        p.`barcode` AS barCode,
        p.pcategory,
        IFNULL(detail.`stoped`,
        ps.`stoped`) AS skuStoped,
        IFNULL(c.combo_id,
        0) AS comboId,
        IFNULL(detail.`ptype_id`,
        psm.`ptype_id`) AS ptypeId,
        IFNULL(detail.`sku_id`,
        psm.sku_id) AS skuId,
        IFNULL(detail.`unit_id`,
        psm.`unit_id`) AS unitId,
        pm.profile_id  AS profileId,
        p.tax_rate,
        p.propenabled,
        IFNULL(detail.`Qty`,
        0) AS ComboSkuQty,
        IFNULL(detail.`price`,
        0) AS ComboSkuPrice,
        IFNULL(detail.`total`,
        0) AS ComboSkuTotal,
        IFNULL(detail.`gifted`,
        0) AS ComboSkuGifted,
        IFNULL(combopu.`unit_code`,
        pu.`unit_code`) AS unitCode,
        IFNULL(detail.`scale`,
        0) AS scaleRate,
        IFNULL(combopu.`unit_rate`,
        pu.`unit_rate`) AS unitRate,
        IFNULL(combopu.`unit_name`,
        pu.`unit_name`) AS unitName,
        config.rule_id as stockSyncRuleId,
        c.sale_type
        FROM pl_eshop_product_sku pm
        LEFT JOIN pl_eshop_product pep ON pm.`platform_num_id`=pep.`platform_num_id` AND pm.profile_id=pep.profile_id AND pm.eshop_id = pep.eshop_id
        LEFT JOIN pl_eshop_product_sku_mapping psm ON pm.`unique_id`=psm.`unique_id` AND pm.profile_id=psm.profile_id AND pm.eshop_id = psm.eshop_id
        LEFT JOIN base_ptype_unit pu on pu.profile_id=psm.profile_id and pu.id =psm.unit_id and pu.ptype_id=psm.ptype_id
        LEFT JOIN base_ptype p ON p.id = psm.ptype_id AND  p.profile_id = psm.profile_id
        LEFT JOIN base_ptype_sku ps on ps.id=psm.sku_id and ps.profile_id=psm.profile_id
        LEFT JOIN `base_ptype_combo` c ON p.profile_id =  c.profile_id  AND p.id = c.combo_id
        LEFT JOIN `base_ptype_combo_detail` detail ON detail.combo_id = c.combo_id AND detail.profile_id  = c.profile_id
        LEFT JOIN base_ptype_unit combopu on combopu.profile_id=detail.profile_id and combopu.id =detail.unit_id and combopu.ptype_id=detail.ptype_id
        left join pl_eshop_product_sku_rule_config config on config.profile_id=pm.profile_id and
        config.platform_num_id=pm.platform_num_id and config.platform_properties = pm.platform_properties_name
        WHERE pm.profile_id=#{profileId} and pm.eshop_id = #{otypeId} and pm.storage_type=0
        and  pep.`platform_fullname` =#{ptypeName}
        and  pm.`platform_properties_name` = #{propertiesName}
    </select>


    <select id="getXcodeEshopProductInfoListByPtypeName"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.response.EshopProductSkuInfo">
        SELECT
                IFNULL(psm.`platform_properties_name`,
            '') AS platformPropertiesName ,
            pm.`platform_fullname` AS platformFullName,
            IFNULL(psm.`platform_xcode`,
            pm.`platform_xcode`) AS platformXcode,
            pm.`platform_num_id` AS platformNumid,
            psm.`platform_sku_id` AS platformSkuid,
            psm.`unique_id` AS uniqueId,
                p.propenabled,
            if(psm.`platform_pic_url` is null or psm.`platform_pic_url`='',pm.platform_pic_url,psm.`platform_pic_url`) AS platformPicUrl,
            pm.profile_id AS profileId,
            IFNULL(p.userCode,
            pp.userCode) AS userCode,
            IFNULL(p.fullName,
            pp.fullName) AS fullName,
            IFNULL(p.stoped,
            pp.stoped) AS ptypeStoped,
            IFNULL(p.`barcode`,
            pp.barcode) AS barCode,
            IFNULL(p.pcategory,
            pp.pcategory) AS pcategory,
            IFNULL(detail.`stoped`,
            ps.stoped) AS skuStoped,
            IFNULL(c.combo_id,
            0) AS comboId,
            IFNULL(detail.`ptype_id`,
            px.`ptype_id`) AS ptypeId,
            IFNULL(detail.`sku_id`,
            px.sku_id) AS skuId,
            IFNULL(detail.`unit_id`,
            px.`unit_id`) AS unitId,
            IFNULL(pt.tax_rate,
            pp.tax_rate)AS tax_rate,
            IFNULL(detail.`Qty`,
            0) AS ComboSkuQty,
            IFNULL(detail.`price`,
            0) AS ComboSkuPrice,
            IFNULL(detail.`total`,
            0) AS ComboSkuTotal,
            IFNULL(detail.`gifted`,
            0) AS ComboSkuGifted,
            IFNULL(detail.`scale`,
            0) AS scaleRate,
            IFNULL(combopu.`unit_code`,
            pu.`unit_code`) AS unitCode,
            IFNULL(combopu.`unit_rate`,
            pu.`unit_rate`) AS unitRate,
            IFNULL(combopu.`unit_name`,
            pu.unit_name) AS unitName,
            config.rule_id as stockSyncRuleId,
            c.sale_type
        FROM
            pl_eshop_product pm
        LEFT JOIN
            pl_eshop_product_sku psm
                ON pm.`platform_num_id`=psm.`platform_num_id`
                AND pm.profile_id=psm.profile_id
                AND pm.eshop_id = psm.eshop_id
        left join pl_eshop_product_sku_expand expand
                on expand.unique_id=psm.unique_id
                AND expand.eshop_id = psm.eshop_id
                AND expand.profile_id = psm.profile_id
        LEFT JOIN
            base_ptype p
                ON p.usercode = psm.platform_xcode
                AND p.profile_id = psm.profile_id
                AND p.pcategory = 2
        LEFT JOIN
            `base_ptype_combo` c
                ON p.profile_id = c.profile_id
                AND p.id = c.combo_id
        LEFT JOIN
            `base_ptype_combo_detail` detail
                ON detail.combo_id = c.combo_id
                AND detail.profile_id = c.profile_id
        LEFT JOIN
            `base_ptype` pt
                ON detail.ptype_id = pt.id
                AND detail.profile_id = pt.profile_id
        LEFT JOIN
            base_ptype_unit combopu
                ON combopu.profile_id=detail.profile_id
                AND combopu.id =detail.unit_id
                AND combopu.ptype_id=detail.ptype_id
        LEFT JOIN
            base_ptype_xcode px
                ON px.profile_id=psm.profile_id
                AND px.xcode=psm.platform_xcode
        LEFT JOIN
            base_ptype_unit pu
                ON pu.profile_id=px.profile_id
                AND pu.id =px.unit_id
                AND pu.ptype_id=px.ptype_id
        LEFT JOIN
            base_ptype pp
                ON pp.id = px.ptype_id
                AND pp.profile_id = px.profile_id
                AND p.pcategory != 2
        LEFT JOIN
            base_ptype_sku ps
                ON ps.id=px.sku_id
        AND ps.profile_id=px.profile_id
        left join pl_eshop_product_sku_rule_config config on config.profile_id=psm.profile_id and
        config.platform_num_id=psm.platform_num_id and config.platform_properties = psm.platform_properties_name
        WHERE pm.profile_id=#{profileId} and pm.eshop_id = #{otypeId}
        and  pm.`platform_fullname` =#{ptypeName}
        and psm.`platform_properties_name` = #{propertiesName}
    </select>

    <select id="queryNotImportOrderByTradeId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity">
        select od.* from pl_eshop_sale_order od
        where od.profile_id=#{profileId} and od.otype_id = #{otypeId} and od.deleted=0
        <if test="tradeOrderIds!=null">
            and  od.trade_order_id in
            <foreach collection="tradeOrderIds" item="tradeOrderId" separator="," index="i" close=")" open="(">
                #{tradeOrderId}
            </foreach>
        </if>
    </select>
    <select id="orderIsExist" resultType="java.math.BigInteger">
        select id from pl_eshop_sale_order od
        where od.profile_id=#{profileId} and od.otype_id = #{otypeId}
        <if test="tradeOrderId!=null">
            and od.trade_order_id=#{tradeOrderId}
        </if>
    </select>


    <select id="queryMappingTypeByPtypeName" resultType="java.lang.Integer">
                select expand.mapping_type
        from pl_eshop_product pm
         LEFT JOIN
     pl_eshop_product_sku psm
     ON pm.`platform_num_id` = psm.`platform_num_id`
         AND pm.profile_id = psm.profile_id
         AND pm.eshop_id = psm.eshop_id
         left join pl_eshop_product_sku_expand expand
                   on expand.unique_id = psm.unique_id
                       AND expand.eshop_id = psm.eshop_id
                       AND expand.profile_id = psm.profile_id
        where pm.profile_id =#{profileId} and
            pm.eshop_id = #{otypeId} and
            pm.`platform_fullname`=#{ptypeName}
        <if test="propertiesName!=null and propertiesName!=''">
            and psm.platform_properties_name=#{propertiesName}
        </if>
        limit 1;
    </select>



</mapper>
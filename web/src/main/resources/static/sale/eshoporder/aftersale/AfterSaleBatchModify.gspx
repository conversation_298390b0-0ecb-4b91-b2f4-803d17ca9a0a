<?xml version="1.0" encoding="UTF-8" ?>
<Page xmlns="Craba.UI" Title="批量修改"
      ActionType="sale.eshoporder.aftersale.AfterSaleBatchModifyAction, sale/eshoporder/aftersale/AfterSaleBatchModify.js">
    <FlexColumn>
        <VPanel CssStyle="padding:20px">
            <HPanel HAlign="Center">
                <CheckBox ID="refundReasonCheck" DataField="refundReasonCheck"/>
                <DropDownEdit ID="refundReason" SelectedIndex="0"
                              Label="售后原因" DataField="refundReason"
                              DataSource="${reasons}"
                              DataTextField="refundReason" Width="240"
                              DataValueField="id"
                />
            </HPanel>
            <HPanel HAlign="Center">
                <CheckBox ID="refundRemarkCheck" DataField="refundRemarkCheck"/>
                <TextEdit ID="refundRemark" Label="售后备注" DataField="refundStatement" MaxLength="200"
                          ColSpan="2"
                          NullDisplayText="最多支持200个汉字" Width="240"/>
            </HPanel>
            <HPanel HAlign="Center">
                <CheckBox ID="refundKtypeCheck" DataField="refundKtypeCheck"/>
                <SelectorEdit ID="edKtype" Label="退货仓库" DataField="ktypeId" DisplayField="ktypeName"
                              Tag="ktype" SelectOnly="true" AllowHandInput="false"
                              OnButtonClick="onKtypeInit" OnEnterPress="doEnterPress" OnSelectorSelected="doBaseInfoSelect" Width="240"
                              TabStop="false" />
            </HPanel>
            <HPanel HAlign="Center">
                <CheckBox ID="edCommentCheck" DataField="edCommentCheck"/>
                <SelectorEdit ID="edComment" OnSelectorSelected="doMemoSelectorSelected" Label="附加说明:" Width="240"
                              TabStop="true" Business="jxc.memo" DataField="memo" NullDisplayText="该字段信息会带到新生成原始订单的卖家备注字段" MaxLength="180" ReportSection="表头表尾字段"/>
            </HPanel>
            <HPanel HAlign="Center">
                <CheckBox ID="refundDutyIdsCheck" DataField="refundDutyIdsCheck"/>
                <DropDownCheckBoxList ID="refundDutyIds"
                                      Label="售后责任方" DataField="refundDutyIds"
                                      DataSource="${refundDuties}"
                                      DataTextField="refundDuty"
                                      DataValueField="id"
                                      Required="true" Width="240"
                />
            </HPanel>


        </VPanel>
        <HBlock CssClass="BottomBlock" HAlign="Right">
            <Block CssClass='Flex1'/>
            <Button Text="确定" OnClick="doSave" CssClass="SpecialButton"/>
            <CancelButton Text="取消"/>
        </HBlock>
    </FlexColumn>
    <HiddenField ID="vchcodes"/>
</Page>
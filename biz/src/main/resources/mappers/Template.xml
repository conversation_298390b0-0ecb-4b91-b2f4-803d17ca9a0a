<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wsgjp.ct.sale.template">
    <sql id="deleted-state-where">
        ${core_tb_name}.deleted in (0,2,3,4)
    </sql>
    <sql id="query-filter-or">
        <include refid="com.wsgjp.ct.sale.template.query-filter">
            <property name="_list" value="${_list}"/>
            <property name="prefix" value="and"/>
            <property name="relation" value="and"/>
        </include>
    </sql>

    <sql id="query-filter-or-refund">
        <include refid="com.wsgjp.ct.sale.template.query-filter-refund">
            <property name="_list" value="${_list}"/>
            <property name="prefix" value="and"/>
            <property name="relation" value="and"/>
        </include>
    </sql>
    <sql id="query-in">
        <foreach collection="${_list}" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </sql>
    <!--生产一组属性字段-->
    <sql id="merge-sku-props">
    <include refid="com.wsgjp.ct.sale.template.merge-props">
        <property name="_sku" value="sku"/>
    </include>
    </sql>
    <sql id="merge-sku">
     CONCAT_WS('',${_sku}.propvalue_name1,${_sku}.propvalue_name2,${_sku}.propvalue_name3,${_sku}.propvalue_name4,${_sku}.propvalue_name5,${_sku}.propvalue_name6) AS propvalue_all
    </sql>
    <sql id="merge-props">
        CONCAT_WS(':',${_sku}.prop_name1,${_sku}.prop_name2,${_sku}.prop_name3,${_sku}.prop_name4,${_sku}.prop_name5,${_sku}.prop_name6) AS prop_names
        ,CONCAT_WS(':',${_sku}.propvalue_name1,${_sku}.propvalue_name2,${_sku}.propvalue_name3,${_sku}.propvalue_name4,${_sku}.propvalue_name5,${_sku}.propvalue_name6) AS prop_values
    </sql>
    <sql id="query-filter">
        <if test="${_list} != null ">
            <trim prefix=" and (" suffix=")">
                <foreach collection="${_list}" item="item" separator="${relation}">
                    <choose>
                        <when test="item.magicQuery != null and item.magicQuery.size() != 0">
                            <trim prefix="(" suffix=")">
                                <foreach collection="item.magicQuery" item="magic_item" separator="or">
                                    ${magic_item.dataField} like concat('%',#{magic_item.value},'%')
                                </foreach>
                            </trim>
                        </when>
                        <when test="item.type == 0">
                            ${item.dataField} like concat('%',#{item.value},'%')
                        </when>
                        <when test="item.type == 1">
                            <trim prefixOverrides="and" prefix="(" suffix=")">
                                <if test="item.value1 != null ">
                                    ${item.dataField} >= #{item.value1}
                                </if>
                                <if test="item.value2 != null ">
                                   and ${item.dataField} <![CDATA[<=]]> #{item.value2}
                                </if>
                            </trim>
                        </when>
                        <when test="item.type == 2 and item.value!=null"> ${item.dataField} = #{item.value}</when>
                        <when test="item.type == 3 and item.value!=null and item.value.size()!=null and item.value.size()>0">
                            ${item.dataField} in
                            <foreach collection="item.value" item="value_item" separator="," open="(" close=")">
                                #{value_item}
                            </foreach>
                        </when>
                        <when test="item.type == 4 ">
                            <if test="item.value!=null and item.value==1">
                                ${item.dataField} >0
                            </if>
                            <if test="item.value!=null and item.value==0">
                                ${item.dataField} =0
                            </if>
                         </when>
                    </choose>
                </foreach>
            </trim>
        </if>
    </sql>


    <sql id="query-filter-refund">
        <if test="${_list} != null ">
            <trim prefix=" and (" suffix=")">
                <foreach collection="${_list}" item="item" separator="${relation}">
                    <choose>
                        <when test="item.magicQuery != null and item.magicQuery.size() != 0">
                            <trim prefix="(" suffix=")">
                                <foreach collection="item.magicQuery" item="magic_item" separator="or">
                                    ${magic_item.dataField} like concat('%',#{magic_item.value},'%')
                                </foreach>
                            </trim>
                        </when>
                        <when test="item.type == 0">
                            ${item.dataField} like concat('%',#{item.value},'%')
                        </when>
                        <when test="item.type == 1">
                            <trim prefixOverrides="and" prefix="(" suffix=")">
                                <if test="item.value1 != null ">
                                    ${item.dataField} >= #{item.value1}
                                </if>
                                <if test="item.value2 != null ">
                                    and ${item.dataField} <![CDATA[<=]]> #{item.value2}
                                </if>
                            </trim>
                        </when>
                        <when test="item.type == 2"> ${item.dataField} = #{item.value}</when>
                    </choose>
                </foreach>
            </trim>
        </if>
    </sql>


    <sql id="query-order">
        <if test="${_order} != null ">
            <trim prefix=" order by ">
                <foreach collection="${_order}" item="item" separator=",">
                    ${@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@getIllegality(item.dataField)}
                    <choose>
                        <when test="item.ascending">
                            asc
                        </when>
                        <otherwise>
                            desc
                        </otherwise>
                    </choose>
                </foreach>
            </trim>
        </if>
    </sql>
<!-- 00000 表示获取未分类下的数据,没有值表示不需要查询，其他值表示查询指定值下like的数据-->
    <sql id="query-typeid">
<if test="@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${typeid-alias}')">
        <choose>
            <when test="query.partypeid == '00000'">
                and ${typeid-alias}.partypeid = #{query.partypeid} and ${typeid-alias}.classed = 0
            </when>
            <when test="query.partypeid != null and query.partypeid != ''">
                and ${typeid-alias}.typeid like concat(#{query.partypeid},'%')
            </when>
        </choose>
</if>
    </sql>

    <sql id="limit-btype">
        <if test="@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-id}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-profileid}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isLimited('btype')">
            left join base_limit_scope _blsBtype on ${limit-alias-id} = _blsBtype.object_id and ${limit-alias-profileid} = _blsBtype.profile_id and
            _blsBtype.object_type = 4 and _blsBtype.etype_id = ${@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@getEtypeId()}
        </if>
    </sql>
    <sql id="limit-where-btype">
        <if test="@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-id}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isLimited('btype')">
            and (_blsBtype.object_id is not null or  ${limit-alias-id} =0)
        </if>
    </sql>

    <sql id="limit-otype">
        <if test="@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-id}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-profileid}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isLimited('otype')">
            left join base_limit_scope _blsOtype on ${limit-alias-id} = _blsOtype.object_id and ${limit-alias-profileid} = _blsOtype.profile_id and _blsOtype.object_type = 3 and _blsOtype.etype_id = ${@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@getEtypeId()}
        </if>
    </sql>
    <sql id="limit-where-otype">
        <if test="@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-id}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isLimited('otype')">
           and (_blsOtype.object_id is not null or  ${limit-alias-id} =0)
        </if>
    </sql>
    <sql id="limit-etype">
        <if test="@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-id}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-profileid}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isLimited('etype')">
            left join base_limit_scope _blsEtype on ${limit-alias-id} = _blsEtype.object_id and ${limit-alias-profileid} = _blsEtype.profile_id and _blsEtype.object_type = 1 and _blsEtype.etype_id = ${@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@getEtypeId()}
        </if>
    </sql>
    <sql id="limit-where-etype">
        <if test="@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-id}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isLimited('etype')">
           and (_blsEtype.object_id is not null or  ${limit-alias-id} =0)
        </if>
    </sql>
    <sql id="limit-ktype">
        <if test="@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-id}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-profileid}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isLimited('ktype')">
            left join base_limit_scope _blsKtype on ${limit-alias-id} = _blsKtype.object_id and ${limit-alias-profileid} = _blsKtype.profile_id and _blsKtype.object_type = 2 and _blsKtype.etype_id = ${@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@getEtypeId()}
        </if>
    </sql>
    <sql id="limit-ktype-join">
        <if test="@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-id}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-profileid}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isLimited('ktype')">
             join base_limit_scope _blsKtype on ${limit-alias-id} = _blsKtype.object_id and ${limit-alias-profileid} = _blsKtype.profile_id and _blsKtype.object_type = 2 and _blsKtype.etype_id = ${@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@getEtypeId()}
        </if>
    </sql>
    <sql id="limit-where-ktype">
        <if test="@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-id}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isLimited('ktype')">
           and (_blsKtype.object_id is not null or  ${limit-alias-id} =0)
        </if>
    </sql>
    <!-- 用于双仓控制仓库权限-->
    <sql id="limit-ktype2">
        <if test="@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-id}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-profileid}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isLimited('ktype')">
            left join base_limit_scope _blsKtype2 on ${limit-alias-id} = _blsKtype2.object_id and ${limit-alias-profileid} = _blsKtype2.profile_id and _blsKtype2.object_type = 2 and _blsKtype2.etype_id = ${@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@getEtypeId()}
        </if>
    </sql>
    <sql id="limit-ktype2-join">
        <if test="@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-id}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-profileid}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isLimited('ktype')">
            join base_limit_scope _blsKtype2 on ${limit-alias-id} = _blsKtype2.object_id and ${limit-alias-profileid} = _blsKtype2.profile_id and _blsKtype2.object_type = 2 and _blsKtype2.etype_id = ${@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@getEtypeId()}
        </if>
    </sql>
    <sql id="limit-where-ktype2">
        <if test="@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-id}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isLimited('ktype')">
          and  (_blsKtype2.object_id is not null or  ${limit-alias-id} =0)
        </if>
    </sql>
    <!-- 用于三个仓控制仓库权限-->
    <sql id="limit-ktype3">
        <if test="@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-id}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-profileid}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isLimited('ktype')">
            left join base_limit_scope _blsKtype3 on ${limit-alias-id} = _blsKtype3.object_id and ${limit-alias-profileid} = _blsKtype3.profile_id and _blsKtype3.object_type = 2 and _blsKtype3.etype_id = ${@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@getEtypeId()}
        </if>
    </sql>
    <sql id="limit-ktype3-join">
        <if test="@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-id}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-profileid}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isLimited('ktype')">
            join base_limit_scope _blsKtype3 on ${limit-alias-id} = _blsKtype3.object_id and ${limit-alias-profileid} = _blsKtype3.profile_id and _blsKtype3.object_type = 2 and _blsKtype3.etype_id = ${@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@getEtypeId()}
        </if>
    </sql>
    <sql id="limit-where-ktype3">
        <if test="@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-id}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isLimited('ktype')">
            and  (_blsKtype3.object_id is not null or  ${limit-alias-id} =0)
        </if>
    </sql>


    <sql id="limit-ptype">
        <if test="@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-id}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-profileid}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isLimited('ptype')">
            left join base_ptype_limit_scope _blsPtype on ${limit-alias-id} = _blsPtype.ptype_id and ${limit-alias-profileid} =
            _blsPtype.profile_id  and _blsPtype.etype_id = ${@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@getEtypeId()}
        </if>
    </sql>
    <sql id="limit-where-ptype">
        <if test="@com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isIllegality('${limit-alias-id}') and @com.wsgjp.ct.sale.biz.analysiscloud.util.BatisUtil@isLimited('ptype')">
            and (_blsPtype.ptype_id is not null or  ${limit-alias-id} =0)
        </if>
    </sql>

    <sql id="estimatedCostColumn">
        <choose>
            <when test="priceCode!=null and priceCode==-1">
                ,0  estimated_cost_price,0 estimated_cost_total
            </when>
            <when test="priceCode==0">
                ,ifnull(p.cost_price,0) as estimated_cost_price,ifnull(p.cost_price*gs.qty,0) as estimated_cost_total
            </when>
            <when test="priceCode==1">
                ,ifnull(ap.price,0) as estimated_cost_price,ifnull(ap.price*gs.qty,0) as estimated_cost_total
            </when>
            <when test="priceCode==2">
                ,case ifnull(p.cost_price,0) when 0 then ifnull(ap.price,0) else ifnull(p.cost_price,0) end as estimated_cost_price
                ,case ifnull(p.cost_price,0) when 0 then ifnull(ap.price*gs.qty,0) else ifnull(p.cost_price*gs.qty,0) end as estimated_cost_total
            </when>
            <when test="priceCode==3">
                ,case ifnull(ap.price,0) when 0 then  ifnull(p.cost_price,0) else ifnull(ap.price,0) end as estimated_cost_price
                ,case ifnull(ap.price,0)  when 0 then  ifnull(p.cost_price*gs.qty,0) else ifnull(ap.price*gs.qty,0) end as estimated_cost_total
            </when>
        </choose>
    </sql>


    <sql id="col-btype-initcol">
        <if test="${_btypeInitType} != null ">
            <choose>
                <when test="${_btypeInitType}==0">
                    init_ar_total
            </when>
                <when test="${_btypeInitType}==1">
                    init_ap_total
                </when>
                <when test="${_btypeInitType}==2">
                    init_pr_total
                </when>
                <when test="${_btypeInitType}==3">
                    init_pp_total
                </when>
                <otherwise>
                    init_ar_total
                </otherwise>
            </choose>
        </if>
    </sql>


    <!--    销售出库单-->
    <sql id="saleOutVchtypes">
        2000
    </sql>

    <!--    销售退货单-->
    <sql id="saleReturnVchtypes">
        2100
    </sql>

    <!--    销售换货单-->
    <sql id="saleExchangeVchtypes">
        2200
    </sql>

<!--    销售类单据-->
    <sql id="saleVchtypes">
        2000,2002,2003,2005,2100,2101,2102,2103,2200,2202,2203
    </sql>

    <!--    采购类单据-->
    <sql id="buyVchtypes">
        1000,1100,1200
    </sql>

<!--    销售报表中不需要统计的业务类型:刷单,仓库漏发，代销，分销 这个无用了-->
    <sql id="notSaleBusinessTypes">
        208,205,203,204
    </sql>
    <!--    销售报表中不需要统计的业务类型:刷单,仓库漏发，需要统计代销分销-->
    <sql id="notSaleGrossProfitBusinessTypes">
        208,205,209
    </sql>

    <!--    销售报表中不需要统计的业务类型:刷单,仓库漏发,代销业务(204,需求要求：销售统计，需要统计代销，库存类的，不用统计的)-->
    <sql id="notStockBusinessTypes">
        208,205,204
    </sql>

    <sql id="query-join-table">
        <foreach collection="${_listTable}" item="joinTable" index="index" open=" " close=" " separator=" ">
            ${joinTable}

        </foreach>
    </sql>

    <sql id="batchInfo">
      ${_batch}.batchno,${_batch}.produce_date,${_batch}.expire_date
    </sql>

    <sql id="common-query-state">
        <if test="${_queryState} != null">
            <if test="${_queryState} == @com.wsgjp.ct.sale.biz.jarvis.state.QueryConditionStateEnum@NONE">
                and ${_dataField} = ''
            </if>
            <if test="${_queryState} == @com.wsgjp.ct.sale.biz.jarvis.state.QueryConditionStateEnum@HAVE">
                <choose>
                    <when test="${_queryData} != ''">
                        and ${_dataField} like
                        <include refid="com.wsgjp.ct.sale.template.query-data-key-list">
                            <property name="_queryDataKey" value="${_queryDataKey}"/>
                        </include>
                    </when>
                    <otherwise>
                        and ${_dataField} != ''
                    </otherwise>
                </choose>
            </if>
            <if test="${_queryState} == @com.wsgjp.ct.sale.biz.jarvis.state.QueryConditionStateEnum@EXCLUDE">
                <choose>
                    <when test="${_queryData} != ''">
                        and ${_dataField} not like
                        <include refid="com.wsgjp.ct.sale.template.query-data-key-list">
                            <property name="_queryDataKey" value="${_queryDataKey}"/>
                        </include>
                    </when>
                    <otherwise>
                        and ${_dataField} != ''
                    </otherwise>
                </choose>
            </if>
        </if>
    </sql>
    <sql id="query-data-key-list">
        <if test="${_queryDataKey} == 'queryKeyForBuyerMessage'">
            concat('%',#{buyerMessage},'%')
        </if>
        <if test="${_queryDataKey} == 'queryKeyForSellerMemo'">
            concat('%',#{sellerMemo},'%')
        </if>
        <if test="${_queryDataKey} == 'queryKeyForMemo'">
            concat('%',#{memo},'%')
        </if>
    </sql>

    <sql id="redword">
       and ${tb_name}.redword = 0
    </sql>

    <sql id="redbillState">
        and ${tb_name}.redbill_state = 0
    </sql>
</mapper>

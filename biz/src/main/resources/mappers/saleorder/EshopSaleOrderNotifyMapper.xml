<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopSaleOrderNotifyMapper">

    <insert id="insertChangeInfo">
        insert into pl_eshop_sale_order_change_info
        (id, profile_id, trade_order_id, change_type, content, modify_time, eshop_order_id, otype_id,trade_order_detail_id,sub_change_type,producer,customer,unique_key)
        values (#{id}, #{profileId}, #{tradeOrderId}, #{changeType}, #{content}, #{modifyTime}, #{eshopOrderId}, #{otypeId}, #{oid}
        , #{subChangeType}, #{producer}, #{customer}, #{uniqueKey})
    </insert>
    <update id="modifyOrderState">
        update pl_eshop_sale_order
        set
            local_refund_state=#{localRefundState},
            re_send_state = #{reSendState}
        where profile_id = #{profileId}
          and id = #{id}
    </update>
    <update id="modifyDetailState">
        update pl_eshop_sale_order_detail
        set
            local_refund_process_state=#{localRefundProcessState},
            local_refund_state=#{localRefundState},
            deliver_required=#{deliverRequired},
            re_send_state = #{reSendState}
        where profile_id = #{profileId}
          and id = #{id}
    </update>

    <update id="modifyDetailStateByTradeOrderDetailId">
        update pl_eshop_sale_order_detail
        set
            local_refund_state=#{localRefundState},
            re_send_state = #{reSendState}
        where profile_id = #{profileId}
          and trade_order_detail_id = #{tradeOrderDetailId}
    </update>

    <update id="modifyAdvanceState">
        update td_orderbill_platform
        set
            local_refund_state=#{platform.localRefundState},
            re_send_state=#{platform.reSendState}
        where profile_id = #{profileId}
          and vchcode = #{vchcode}
    </update>

    <update id="modifyAdvanceDetailState">
        update td_orderbill_detail_platform
        set
            local_refund_process_state=#{platform.localRefundProcessState},
            local_refund_state=#{platform.localRefundState},
            re_send_state=#{platform.reSendState},
            deliver_required=#{platform.deliverRequired}
        where profile_id = #{profileId} and vchcode = #{vchcode}
          and detail_id = #{detailId}
    </update>

    <update id="doOrderFinish">
        update pl_eshop_sale_order
        set custom_trade_status=#{customTradeStatus},
            trade_modified_time=#{tradeModifiedTime},
            trade_finish_time=#{tradeFinishTime},
            modified_time=#{modifiedTime},
        where profile_id = #{profileId}
          and trade_order_id = #{tradeOrderId}
          and otype_id = #{otypeId}
          and create_type!=1
    </update>
    <update id="doOrderDetailFinish">
        update pl_eshop_sale_order_detail
        set platform_detail_trade_state=4
        where profile_id = #{profileId}
          and eshop_order_id = #{eshopOrderId}
    </update>
    <update id="modifyDeliverSendState">
        update pl_eshop_sale_order_extend
        set deliver_send_state = #{deliverSendState}
         where profile_id = #{profileId}
          and eshop_order_id = #{eshopOrderId}
    </update>

    <update id="doAdvanceOrderFinish">
        update td_orderbill_platform
        set local_trade_status=#{localTradeStatus},
            trade_modified_time=#{tradeModifiedTime},
            trade_finish_time=#{tradeFinishTime}
        where profile_id = #{profileId}
          and id = #{id}
          and create_type!=1
    </update>

    <update id="doAdvanceOrderDetailFinish">
        update pl_eshop_sale_order_advance_detail
        set platform_trade_state=4
        where profile_id = #{profileId}
          and eshop_order_id = #{eshopOrderId}
    </update>


    <update id="closeAdvanceOrder">
        update pl_eshop_sale_order_advance set custom_trade_status=5
        where profile_id=#{profileId}
        AND trade_id=#{tradeId}
    </update>

    <select id="getTradeOrderIdList" resultType="java.lang.String">
        select trade_order_id
        from pl_eshop_sale_order
        where profile_id=#{profileId}
        and otype_id=#{eshopId}
        and create_type=1
        AND trade_create_time <![CDATA[>=]]> #{start}
    </select>

    <select id="getTradeOrderIdListCount" resultType="java.lang.Integer">
        select count(1) from pl_eshop_sale_order
        where profile_id=#{profileId}
          and otype_id=#{eshopId}
          and create_type=1
          AND trade_create_time <![CDATA[>=]]> #{start}
    </select>
</mapper>
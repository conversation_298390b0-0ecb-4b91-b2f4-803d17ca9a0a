<?xml version="1.0" encoding="UTF-8" ?>
<Page xmlns="Craba.UI" Title="${title}"
      CssClass="pd0"
      MinWidth="780"
      MinHeight="600"
      ActionType="sale.product.StockDefaultRuleListAction, sale/eshoporder/product/StockDefaultRuleList.js">
    <FlexColumn CssClass="FlexAuto dflex">
        <FlowPanel CssClass="plr10">
            <Block CssClass='Flex1'/>
            <Button ID="btnFilter" Text="筛选" Flat="true" CssClass="aicon-filter" OnClick="doChangeFilter"/>
        </FlowPanel>
        <Grid ID="ruleGrid"
              DefaultRowCount="0"
              NeedRowIndex="true"
              OnRowDblClick="doDblClick"
              BindPagerDataSource="doBindPagerDataSource"
              OnCellRendering='doCellRender'
              OnSelectionChanged="doRuleGridSelectionChanged"
              Pager="Bottom"
              ReadOnly="false"
              CssClass="plr10">
            <IconColumn Caption="操作" AllowConfig="false" ListItems="编辑=aicon-bianji3" DisplayNull="true"
                        LayoutDirection="Horz" OnClick="doOperationClick" AllowFilter="false"/>
            <TextColumn Caption="网店" DataField="eshopName" ReadOnly="true"/>
            <DropDownCheckColumn ID="drKtype"
                                  ReadOnly="true"
                                  NullDisplayText="同步仓库"
                                  DataSource="${ktypeList}"
                                  DataField="ktypeIds"
                                  DataTextField="fullname"
                                  DataValueField="id"/>
<!--            <TextColumn Caption="同步仓库" DataField="ktypeNames" ReadOnly="true"/>-->
            <TextColumn Caption="同步公式" DataField="formula" Width="350" ReadOnly="true" AllowFilter="false"/>
            <SwitchButtonColumn Caption="库存自动同步" DataField="autoSyncEnabled" ReadOnly="false" AllowSort="false"
                                CheckedValue='true' OnChange="doAutoSyncEnableChanged" OnFilterRendering="doAutoSyncFilterRendering"/>
        </Grid>
        <HSplitter Mode="HideDown" ForDown="downFlex" ForUp="ruleGrid" CssClass="mlr10" MinHeight="100"
                   IsExpanded="false"/>
        <PageControl ID='downFlex' CssClass="NoBorder plr10" Height='200'>
            <TabPage Caption='操作日志' CssClass='pd0'>
                <CustomControl ID="rule_log_ctrl" Src="~/sale/eshoporder/customer/OperationLogViewFrom.gspx"
                               CssClass="FlexAuto dflex" ConfigID="dr_type"/>
            </TabPage>
        </PageControl>
    </FlexColumn>

</Page>
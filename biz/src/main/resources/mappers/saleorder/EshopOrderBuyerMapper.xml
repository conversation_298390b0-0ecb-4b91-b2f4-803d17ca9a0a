<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopOrderBuyerMapper">

    <insert id="insertBuyer">
        insert  into pl_buyer (buyer_id, otype_id, `customer_receiver`, `customer_id_card`, customer_id_card_name,
                              `customer_receiver_phone`, `customer_receiver_mobile`, `customer_receiver_zip_code`,
                              `customer_email`, `customer_receiver_country`, `customer_receiver_province`,
                              `customer_receiver_city`, `customer_receiver_district`, `customer_receiver_address`,
                              `customer_receiver_full_address`, `customer_pay_account`, `customer_shop_account`,
                              `profile_id`, `hash_key`, eshop_type, `di`, `ai`, `mi`, `pi`, `ri`, `pai`, `ici`, `ini`,
                              `addri`,`customer_receiver_town`,`platform_province_code`,`platform_city_code`,
                               `platform_district_code`,`platform_street_code`,`open_receiver_id`,`open_address_id`,
                               `open_address_id_2`)
        values (#{buyerId}, #{otypeId}, #{customerReceiver}, #{customerIdCard}, #{customerIdCardName},
                #{customerReceiverPhone}, #{customerReceiverMobile}, #{customerReceiverZipCode}, #{customerEmail},
                #{customerReceiverCountry}, #{customerReceiverProvince}, #{customerReceiverCity},
                #{customerReceiverDistrict}, #{customerReceiverAddress}, #{customerReceiverFullAddress},
                #{customerPayAccount}, #{customerShopAccount}, #{profileId}, #{hashKey}, #{eshopType}, #{di}, #{ai},
                #{mi}, #{pi}, #{ri}, #{pai}, #{ici}, #{ini}, #{addri},#{customerReceiverTown},#{platformProvinceCode},#{platformCityCode},
                #{platformDistrictCode},#{platformStreetCode},#{openReceiverId},
                #{openAddressId},#{openAddressId2})
    </insert>

    <insert id="batchInsertBuyer">
        INSERT INTO `pl_buyer` (buyer_id, otype_id, `customer_receiver`, `customer_id_card`, customer_id_card_name,
        `customer_receiver_phone`, `customer_receiver_mobile`, `customer_receiver_zip_code`,
        `customer_email`, `customer_receiver_country`, `customer_receiver_province`,
        `customer_receiver_city`, `customer_receiver_district`, `customer_receiver_address`,
        `customer_receiver_full_address`, `customer_pay_account`, `customer_shop_account`,
        `profile_id`, `hash_key`, eshop_type, `di`, `ai`, `mi`, `pi`, `ri`, `pai`, `ici`, `ini`,
        `addri`,`customer_receiver_town`,`platform_province_code`,`platform_city_code`,`platform_district_code`,
        `platform_street_code`,`open_receiver_id`,`open_address_id`,`open_address_id_2`)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.buyerId}, #{item.otypeId}, #{item.customerReceiver}, #{item.customerIdCard}, #{item.customerIdCardName},
            #{item.customerReceiverPhone}, #{item.customerReceiverMobile}, #{item.customerReceiverZipCode}, #{item.customerEmail},
            #{item.customerReceiverCountry}, #{item.customerReceiverProvince}, #{item.customerReceiverCity},
            #{item.customerReceiverDistrict}, #{item.customerReceiverAddress}, #{item.customerReceiverFullAddress},
            #{item.customerPayAccount}, #{item.customerShopAccount}, #{item.profileId}, #{item.hashKey}, #{item.eshopType}, #{item.di}, #{item.ai},
            #{item.mi}, #{item.pi}, #{item.ri}, #{item.pai}, #{item.ici}, #{item.ini}, #{item.addri},#{item.customerReceiverTown},#{item.platformProvinceCode},
             #{item.platformCityCode},#{item.platformDistrictCode},#{item.platformStreetCode},#{item.openReceiverId},
            #{item.openAddressId},#{item.openAddressId2})
        </foreach>
    </insert>


    <insert id="replaceBuyer" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.EshopBuyer">
        insert  into pl_buyer (buyer_id, otype_id, `customer_receiver`, `customer_id_card`, customer_id_card_name,
                               `customer_receiver_phone`, `customer_receiver_mobile`, `customer_receiver_zip_code`,
                               `customer_email`, `customer_receiver_country`, `customer_receiver_province`,
                               `customer_receiver_city`, `customer_receiver_district`, `customer_receiver_address`,
                               `customer_receiver_full_address`, `customer_pay_account`, `customer_shop_account`,
                               `profile_id`, `hash_key`, eshop_type, `di`, `ai`, `mi`, `pi`, `ri`, `pai`, `ici`, `ini`,
                               `addri`,`customer_receiver_town`,`platform_province_code`,`platform_city_code`,`platform_district_code`,`platform_street_code`,`open_receiver_id`,`open_address_id`)
        values (#{buyerId}, #{otypeId}, #{customerReceiver}, #{customerIdCard}, #{customerIdCardName},
                #{customerReceiverPhone}, #{customerReceiverMobile}, #{customerReceiverZipCode}, #{customerEmail},
                #{customerReceiverCountry}, #{customerReceiverProvince}, #{customerReceiverCity},
                #{customerReceiverDistrict}, #{customerReceiverAddress}, #{customerReceiverFullAddress},
                #{customerPayAccount}, #{customerShopAccount}, #{profileId}, #{hashKey}, #{eshopType}, #{di}, #{ai},
                #{mi}, #{pi}, #{ri}, #{pai}, #{ici}, #{ini}, #{addri},#{customerReceiverTown},#{platformProvinceCode},#{platformCityCode},#{platformDistrictCode},#{platformStreetCode},#{openReceiverId},
                #{openAddressId})
        ON DUPLICATE KEY UPDATE
                 customer_receiver=#{customerReceiver},
                 customer_id_card=#{customerIdCard},
                 customer_id_card_name=#{customerIdCardName},
                 customer_receiver_phone=#{customerReceiverPhone},
                 customer_receiver_mobile=#{customerReceiverMobile},
                 customer_receiver_zip_code=#{customerReceiverZipCode},
                 customer_email=#{customerEmail},
                 customer_receiver_country=#{customerReceiverCountry},
                 customer_receiver_province=#{customerReceiverProvince},
                 customer_receiver_city=#{customerReceiverCity},
                 customer_receiver_district=#{customerReceiverDistrict},
                 customer_receiver_address=#{customerReceiverAddress},
                 customer_receiver_full_address=#{customerReceiverFullAddress},
                 customer_pay_account= #{customerPayAccount},
                 customer_shop_account= #{customerShopAccount},
                 eshop_type=#{eshopType},
                 di=#{di},
                 ai=#{ai},
                 pi=#{pi},
                 mi=#{mi},
                 ri=#{ri},
                 pai=#{pai},
                 ici=#{ici},
                 ini=#{ini},
                 addri=#{addri},
                 customer_receiver_town= #{customerReceiverTown},
                 platform_province_code=#{platformProvinceCode},
                 platform_city_code=#{platformCityCode},
                 platform_district_code=#{platformDistrictCode},
                 platform_street_code=#{platformStreetCode}

    </insert>

    <update id="modifyBuyer">
        update pl_buyer
        set customer_receiver=#{customerReceiver},
            customer_id_card=#{customerIdCard},
            customer_id_card_name=#{customerIdCardName},
            customer_receiver_phone=#{customerReceiverPhone},
            customer_receiver_mobile=#{customerReceiverMobile},
            customer_receiver_zip_code=#{customerReceiverZipCode},
            customer_email=#{customerEmail},
            customer_receiver_country=#{customerReceiverCountry},
            customer_receiver_province=#{customerReceiverProvince},
            customer_receiver_city=#{customerReceiverCity},
            customer_receiver_district=#{customerReceiverDistrict},
            customer_receiver_address=#{customerReceiverAddress},
            customer_receiver_full_address=#{customerReceiverFullAddress},
            customer_pay_account= #{customerPayAccount},
            customer_shop_account= #{customerShopAccount},
            hash_key=#{hashKey},
            eshop_type=#{eshopType},
            di=#{di},
            ai=#{ai},
            pi=#{pi},
            mi=#{mi},
            ri=#{ri},
            pai=#{pai},
            ici=#{ici},
            ini=#{ini},
            addri=#{addri},
            `customer_receiver_town`= #{customerReceiverTown},
            `open_receiver_id`= #{openReceiverId},
            `open_address_id`= #{openAddressId}
        where profile_id = #{profileId}
          and buyer_id = #{buyerId}
    </update>

    <select id="queryBuyer"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.EshopBuyer">
        select b.*, e.fullname as otypeName from pl_buyer b
        left join pl_eshop e on e.otype_id=b.otype_id and b.profile_id=e.profile_id
        where b.profile_id=#{profileId}
        <if test="buyerId!=null">
            and b.buyer_id=#{buyerId}
        </if>
        <if test="otypeId!=null and otypeId!=''">
            and b.otype_id=#{otypeId}
        </if>
        <if test="hashKey!=null and hashKey!=''">
            and hash_key=#{hashKey}
        </if>
        limit 1
    </select>

    <select id="queryBuyerById" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.EshopBuyer">
        select b.*, e.fullname as otypeName
        from pl_buyer b
                 left join pl_eshop e on e.otype_id = b.otype_id and b.profile_id = e.profile_id
        where b.profile_id = #{profileId}
          and b.buyer_id = #{buyerId}
    </select>

    <select id="queryBuyerList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.EshopBuyer">
        select b.*, e.fullname as otypeName from pl_buyer b
        left join pl_eshop e on e.otype_id=b.otype_id and b.profile_id=e.profile_id
        where b.profile_id=#{profileId}
        <if test="buyerId!=null">
            and b.buyer_id=#{buyerId}
        </if>
        <if test="buyerIdList!=null and buyerIdList.size()>0">
            and b.buyer_id in
            <foreach item="id" collection="buyerIdList" separator="," open="(" close=")" index="">
                #{id}
            </foreach>
        </if>
        <if test="aiList!=null and aiList.size()>0">
            and b.ai in
            <foreach item="a" collection="aiList" separator="," open="(" close=")" index="">
                #{a}
            </foreach>
        </if>
        <if test="riList!=null and riList.size()>0">
            and b.ri in
            <foreach item="r" collection="riList" separator="," open="(" close=")" index="">
                #{r}
            </foreach>
        </if>
        <if test="otypeId!=null and otypeId!=''">
            and b.otype_id=#{otypeId}
        </if>
        <if test="eshopType!=null">
            and b.eshop_type=#{eshopType}
        </if>
    </select>

    <update id="modifyAdvanceOrderBuyerid">
        update td_orderbill_core c
        left join td_orderbill_platform p on c.profile_id=p.profile_id and c.vchcode=p.vchcode
        set c.buyer_id=#{buyerId}
        where c.profile_id = #{profileId}
          and c.otype_id = #{otypeId}
          and p.trade_id = #{tradeId}
    </update>

    <update id="modifyBuyerByTmc">
        update pl_buyer
        set customer_receiver=#{customerReceiver},
        <if test="di!=null and di!=''">
            di=#{di},
        </if>
        where  profile_id = #{profileId}
        buyer_id=#{buyerId}
    </update>

    <select id="queryBuyerOnlyId" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.EshopBuyer">
        select b.profile_id,b.buyer_id from pl_buyer b
        where b.profile_id=#{profileId}
        <if test="buyerId!=null">
            and b.buyer_id=#{buyerId}
        </if>
        <if test="hashKey!=null and hashKey!=''">
            and hash_key=#{hashKey}
        </if>
        limit 1
    </select>

    <select id="queryBuyerByDeliverInfo" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.EshopBuyer">
        select b.buyer_id from pl_buyer b
                     inner join base_btype_deliveryinfo  d on d.profile_id = b.profile_id and d.delivery_id=b.buyer_id
        where b.profile_id=#{profileId}  and d.btype_id=#{btypeId}
        <if test="customerReceiverProvince!=null and customerReceiverProvince!=''">
            and b.customer_receiver_province=#{customerReceiverProvince}
        </if>
        <if test="customerReceiverCity!=null and customerReceiverCity!=''">
            and b.customer_receiver_city=#{customerReceiverCity}
        </if>
        <if test="customerReceiverDistrict!=null and customerReceiverDistrict!=''">
            and b.customer_receiver_district=#{customerReceiverDistrict}
        </if>
        <if test="customerReceiverTown!=null and customerReceiverTown!=''">
            and b.customer_receiver_town=#{customerReceiverTown}
        </if>
        <if test="customerReceiverAddress!=null and customerReceiverAddress!=''">
            and b.customer_receiver_address=#{customerReceiverAddress}
        </if>
        <if test="customerReceiver!=null and customerReceiver!=''">
            and b.customer_receiver=#{customerReceiver}
        </if>
        <if test="customerReceiverMobile!=null and customerReceiverMobile!=''">
            and b.customer_receiver_mobile=#{customerReceiverMobile}
        </if>
        limit 1
    </select>

    <select id="queryBuyerBySaleOrder" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.EshopBuyer">
        SELECT *
        FROM pl_buyer
        WHERE profile_id = #{profileId}
          AND otype_id = #{otypeId}
          AND buyer_id =
              (SELECT buyer_id FROM pl_eshop_sale_order WHERE profile_id = #{profileId} AND otype_id = #{otypeId} AND trade_order_id = #{tradeOrderId});
    </select>
</mapper>
package com.wsgjp.ct.sale.biz.eshoporder.service.product;

import com.google.common.collect.Lists;
import com.wsgjp.ct.baseinfo.core.constant.BasicInfoNameEnum;
import com.wsgjp.ct.baseinfo.core.dal.po.BasicInfoClassPo;
import com.wsgjp.ct.baseinfo.core.dao.entity.*;
import com.wsgjp.ct.baseinfo.core.dao.entity.Brandtype;
import com.wsgjp.ct.baseinfo.core.dao.entity.PtypePic;
import com.wsgjp.ct.baseinfo.core.dto.*;
import com.wsgjp.ct.baseinfo.core.service.other.BasicInfoService;
import com.wsgjp.ct.baseinfo.core.service.ptype.PtypeService;
import com.wsgjp.ct.baseinfo.core.service.ptype.*;
import com.wsgjp.ct.sale.biz.eshoporder.api.PtypeApi;
import com.wsgjp.ct.sale.biz.eshoporder.api.enums.CostMode;
import com.wsgjp.ct.sale.biz.eshoporder.api.enums.SkuCostState;
import com.wsgjp.ct.sale.biz.eshoporder.api.request.ComboRepeatRequest;
import com.wsgjp.ct.sale.biz.eshoporder.api.request.stock.InitGoodsStockDetailDto;
import com.wsgjp.ct.sale.biz.eshoporder.api.request.stock.PtypeStockRequest;
import com.wsgjp.ct.sale.biz.eshoporder.api.response.PtypePriceDto;
import com.wsgjp.ct.sale.biz.eshoporder.api.response.ComboDetailPo;
import com.wsgjp.ct.sale.biz.eshoporder.api.response.ComboIsRepeatResponse;
import com.wsgjp.ct.sale.biz.eshoporder.api.response.PtypeCombo;
import com.wsgjp.ct.sale.biz.eshoporder.api.response.PtypeComboVo;
import com.wsgjp.ct.sale.biz.eshoporder.config.SaleBizConfig;
import com.wsgjp.ct.sale.biz.eshoporder.constant.StringConstant;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.PropValueName;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.Pcategory;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.ProductOperateLogType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.CreateComboParams;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.CreatePtypeParams;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.ComboSaveResponse;
import com.wsgjp.ct.sale.biz.eshoporder.impl.ProcessLoggerImpl;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopProductNewMapper;
import com.wsgjp.ct.sale.biz.eshoporder.util.ProductManageUtil;
import com.wsgjp.ct.support.business.Money;
import com.wsgjp.ct.support.business.MoneyUtils;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.global.GlobalConfig;
import com.wsgjp.ct.support.log.service.LogService;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 本地商品构建保存
 */
@Service
public class PtypeSaver {
    private static final Logger logger = LoggerFactory.getLogger(PtypeSaver.class);
    private final PtypeService ptypeService;
    private final BasicInfoService basicInfoService;
    private final BrandtypeService brandtypeService;
    private final EshopProductNewMapper productNewMapper;
    private final SaleBizConfig config;
    private final EshopProductNotifyService productNotifyService;

    public static final String PTYPE_UNIQUE_ID = "PtypeUniqueId";

    private final PtypeApi jxcApi;

    public PtypeSaver(BasicInfoService basicInfoService, PtypeService ptypeService, BrandtypeService brandtypeService, EshopProductNewMapper productNewMapper, SaleBizConfig config, EshopProductNotifyService productNotifyService, PtypeApi jxcApi) {
        this.basicInfoService = basicInfoService;
        this.ptypeService = ptypeService;
        this.brandtypeService = brandtypeService;
        this.productNewMapper = productNewMapper;
        this.config = config;
        this.productNotifyService = productNotifyService;
        this.jxcApi = jxcApi;
    }

    private BasicInfoClassPo getPtypeParTypeId(BasicInfoNameEnum type) {
        List<BasicInfoClassPo> classList = basicInfoService.getClassList(type);
        if (CollectionUtils.isNotEmpty(classList)) {
            for (BasicInfoClassPo basicInfoClassPo : classList) {
                if (basicInfoClassPo.getFullname().contains("未分类")) {
                    return basicInfoClassPo;
                }
            }
        }
        BasicInfoClassPo treeNodeResultDto = createNewClass("");
        return JsonUtils.toObject(JsonUtils.toJson(treeNodeResultDto), BasicInfoClassPo.class);
    }

    private BasicInfoClassPo createNewClass(String oldParTypId) {
        String serialNo = "1";
        LocalDate date = LocalDate.now();
        String formattedMonth = String.format("%02d", date.getMonthValue());
        String formattedDay = String.format("%02d", date.getDayOfMonth());
        String nowDate = date.getYear() + formattedMonth + formattedDay;
        if (StringUtils.isNotEmpty(oldParTypId) && oldParTypId.contains("-")) {
            String oldParTypeIdsubstring = oldParTypId.substring(oldParTypId.lastIndexOf("-"));
            if (oldParTypeIdsubstring.equals("未分类-" + nowDate)) {
                String newParTypeIdsubstring = oldParTypId.substring(oldParTypId.lastIndexOf("-"), oldParTypId.length() - 1);
                int no = Integer.parseInt(newParTypeIdsubstring) + 1;
                serialNo = String.valueOf(no);
            }
        }
        TreeNodeDto classNodeDto = new TreeNodeDto();
        classNodeDto.setFullname(String.format("未分类-%s-%s", nowDate, serialNo));
        classNodeDto.setBasicname("Ptype");
        classNodeDto.setPartypeid("00000");
        TreeNodeResultDto treeNodeResultDto = basicInfoService.addClass(classNodeDto);
        return JsonUtils.toObject(JsonUtils.toJson(treeNodeResultDto.getTreeNodeDto()), BasicInfoClassPo.class);
    }

    public List<PtypeInfoExt> buildPtypeInfoDto(List<EshopProductSkuMapping> skuMappings, CreatePtypeParams params, Map<String, EshopProductSkuAttrRelation> platPropAttrRelationMap, BasicInfoClassPo ptypeParTypeId, boolean isPropEnabled) {
        List<PtypeInfoExt> ptypeInfoExtList = new ArrayList<>();
        if (CollectionUtils.isEmpty(skuMappings)) {
            return ptypeInfoExtList;
        }
        if (isPropEnabled) {
            EshopProductSkuMapping skuMapping = skuMappings.get(0);
            PtypeInfoExt ptypeInfoExt = new PtypeInfoExt();
            PtypeInfoDto ptypeInfoDto = buildPtype(skuMapping, params, true, 0);
            buildPropAndPropValues(ptypeInfoDto, skuMappings, platPropAttrRelationMap);
            ptypeInfoExt.setPtypeInfoDto(ptypeInfoDto);
            ptypeInfoExt.setEshopId(skuMapping.getEshopId());
            buildPtypeSkus(ptypeInfoExt, skuMappings);
            if (StringUtils.isEmpty(skuMapping.getPartTypeId())) {
                ptypeInfoDto.setPartypeid(ptypeParTypeId.getTypeid());
            }
            ptypeInfoExtList.add(ptypeInfoExt);
        } else {
            int productSkuCount = 1;
            for (EshopProductSkuMapping skuMapping : skuMappings) {
                PtypeInfoExt ptypeInfoExt = new PtypeInfoExt();
                PtypeInfoDto ptypeInfoDto = buildPtype(skuMapping, params, false, productSkuCount);
                ptypeInfoExt.setPtypeInfoDto(ptypeInfoDto);
                ptypeInfoExt.setEshopId(skuMapping.getEshopId());
                //无属性商品也有一个默认SKU数据
                buildDefaultPtypeSkus(ptypeInfoExt, skuMapping);
                ptypeInfoExtList.add(ptypeInfoExt);
                productSkuCount++;
            }
        }
        return ptypeInfoExtList;
    }

    private void buildDefaultPtypeSkus(PtypeInfoExt ptypeInfoExt, EshopProductSkuMapping skuMapping) {
        List<PtypeSku> skus = new ArrayList<>();
        List<PtypeSkuExt> skuExts = new ArrayList<>();
        List<PtypeXcodeDto> ptypeXcodeList = new ArrayList<>();
        PtypeSku ptypeSku = new PtypeSku();
        PtypeInfoDto ptypeInfoDto = ptypeInfoExt.getPtypeInfoDto();
        ptypeSku.setProfileId(CurrentUser.getProfileId());
        ptypeSku.setPtypeId(ptypeInfoDto.getId());
        PtypeSkuExt ptypeSkuExt = new PtypeSkuExt();
        ptypeSkuExt.setPtypeSku(ptypeSku);
        ptypeSkuExt.setQty(skuMapping.getQty());
        skuMapping.setPtypeUniqueId(ptypeInfoDto.getCallCustomHashMap().get(PTYPE_UNIQUE_ID));
        skuExts.add(ptypeSkuExt);
        if (StringUtils.isNotBlank(skuMapping.getPmplatformXcode())) {
            PtypeXcodeDto ptypeXcodeDto = buildPtypeXcodeDto(skuMapping.getPmplatformXcode(), ptypeSku);
            ptypeXcodeList.add(ptypeXcodeDto);
        }
        ptypeInfoDto.setSkus(skus);
        ptypeInfoDto.setXcodes(ptypeXcodeList);
        ptypeInfoExt.setSkuExts(skuExts);
    }

    private PtypeXcodeDto buildPtypeXcodeDto(String xcode, PtypeSku ptypeSku) {
        PtypeXcodeDto xcodeEntity = new PtypeXcodeDto();
        xcodeEntity.setProfileId(CurrentUser.getProfileId());
        //编码是属于哪个SKU的
        xcodeEntity.setSku(ptypeSku);
        xcodeEntity.setId(BigInteger.ZERO);
        xcodeEntity.setXcode(xcode);
        xcodeEntity.setDefaulted(true);
        //单位编码
        xcodeEntity.setUnitCode(1);
        return xcodeEntity;
    }

    public List<PtypeInfoExt> buildPtypeInfoDtoList(boolean isPropEnabled, List<EshopProductSkuMapping> eshopProductSkuMappings, CreatePtypeParams params, ProcessLoggerImpl pLogger) {
        List<PtypeInfoExt> ptypeList = new ArrayList<>();
        BasicInfoClassPo ptypeParTypeId = getPtypeParTypeId(BasicInfoNameEnum.Ptype);
        Map<BigInteger, List<EshopProductSkuMapping>> eshopSkuMappingsGroup = eshopProductSkuMappings.stream().collect(Collectors.groupingBy(EshopProductSkuMapping::getEshopId));
        Map<BigInteger, List<EshopProductSkuAttrRelation>> eshopAttrRelationsGroup = params.getAttrRelations().stream().collect(Collectors.groupingBy(EshopProductSkuAttrRelation::getEshopId));
        eshopSkuMappingsGroup.forEach((eshopId, eshopSkuMappings) -> {
            List<EshopProductSkuAttrRelation> attrRelations = eshopAttrRelationsGroup.getOrDefault(eshopId, new ArrayList<>());
            Map<String, EshopProductSkuAttrRelation> platPropAttrRelationMap = attrRelations.stream().collect(Collectors.toMap(EshopProductSkuAttrRelation::getPlatformProp, relation -> relation));
            List<PtypeInfoExt> eshopPtypeList = buildPtypeInfoDtoListByShop(isPropEnabled, ptypeParTypeId, eshopSkuMappings, params, platPropAttrRelationMap);
            ptypeList.addAll(eshopPtypeList);
        });
        return ptypeList;
    }

    public List<PtypeInfoExt> buildPtypeInfoDtoListByShop(boolean isPropEnabled, BasicInfoClassPo ptypeParTypeId, List<EshopProductSkuMapping> eshopProductSkuMappings, CreatePtypeParams params,
                                                          Map<String, EshopProductSkuAttrRelation> platPropAttrRelationMap) {
        List<PtypeInfoExt> ptypeList = new ArrayList<>();
        Map<String, List<EshopProductSkuMapping>> skuMapGroup = eshopProductSkuMappings.stream().collect(Collectors.groupingBy(EshopProductSkuMapping::getPlatformNumId));
        skuMapGroup.forEach((numId, skuMappings) -> {
            List<PtypeInfoExt> ptypeInfoDtoList = buildPtypeInfoDto(skuMappings, params, platPropAttrRelationMap, ptypeParTypeId, isPropEnabled);
            if (CollectionUtils.isNotEmpty(ptypeInfoDtoList)) {
                ptypeList.addAll(ptypeInfoDtoList);
            }
        });
        return ptypeList;
    }

    public PtypeInfoDto buildPtype(EshopProductSkuMapping skuMapping, CreatePtypeParams params, boolean isPropEnabled, int productSkuCount) {
        try {
            String xcode;
            String fullName;
            xcode = skuMapping.getPmplatformXcode().length() > 100 ? skuMapping.getPmplatformXcode().substring(0, 100) : skuMapping.getPmplatformXcode();
            fullName = skuMapping.getPlatfullname().length() > 200 ? skuMapping.getPlatfullname().substring(0, 199) : skuMapping.getPlatfullname();
            if (!isPropEnabled && StringUtils.isNotBlank(skuMapping.getPlatformPropertiesName())) {
                fullName = String.format("%s(%s)", fullName, skuMapping.getPlatformPropertiesName());
                if (StringUtils.isNotBlank(skuMapping.getPlatformXcode())) {
                    xcode = StringUtils.isBlank(xcode) ? skuMapping.getPlatformXcode() : String.format("%s_%s", xcode, skuMapping.getPlatformXcode());
                } else if (StringUtils.isNotBlank(xcode) && productSkuCount > 1) {
                    xcode = String.format("%s_%s", xcode, productSkuCount);
                }
            }
            PtypeInfoDto ptype = new PtypeInfoDto();
            ptype.setShortname(fullName.length() > 4 ? fullName.substring(0, 4) : fullName);
            ptype.setProfileId(skuMapping.getProfileId());
            ptype.setUsercode(xcode);
            ptype.setFullname(fullName);
            ptype.setDeleted(skuMapping.getDeleteEnabled());
            ptype.setCreateType(1);
            if (StringUtils.isNotEmpty(skuMapping.getPartTypeId())) {
                ptype.setPartypeid(skuMapping.getPartTypeId());
            } else {
                ptype.setPartypeid("00000");
            }
            ptype.setPcategory(0);
            ptype.setCreateTime(new Date());
            ptype.setUpdateTime(new Date());
            String mainUrl = isPropEnabled ? skuMapping.getPlatformPicUrl() : skuMapping.getPicUrl();
            ptype.setPics(buildPics(mainUrl));
            ptype.setUnits(buildUnits(skuMapping));
            ptype.setPtypeType(skuMapping.getPtypeType());
            ptype.setStandard(skuMapping.getStandard());
            ptype.setBrandId(StringUtils.isEmpty(skuMapping.getBrandName()) ? BigInteger.ZERO : buildBrandId(skuMapping.getBrandName()));
            ptype.setPropenabled(isPropEnabled);
            ptype.setCostMode(params.getCostCalculate());
            //自定义字段供后续使用。构建ptypeUniqueId用于找生成本地商品对应关系
            String ptypeUniqueId = String.format("%s_%s", ptype.getUsercode(), ptype.getFullname());
            HashMap<String, String> customHashMap = new HashMap<>();
            customHashMap.put(PTYPE_UNIQUE_ID, ptypeUniqueId);
            ptype.setCallCustomHashMap(customHashMap);
            skuMapping.setPtypeUniqueId(ptypeUniqueId);
            return ptype;
        } catch (Exception ex) {
            logger.error("构建本地商品报错，构建信息：{}", JsonUtils.toJson(skuMapping));
            throw ex;
        }
    }

    private List<PtypePic> buildPics(String url) {
        List<PtypePic> ptypePicList = new ArrayList<>();
        PtypePic pic = new PtypePic();
        if (StringUtils.isNotBlank(url)) {
            pic.setPicUrl(url);
            ptypePicList.add(pic);
        }
        return ptypePicList;
    }

    private List<PtypeUnit> buildUnits(EshopProductSkuMapping skuMapping) {
        List<PtypeUnit> unitpriceList = new ArrayList<>();
        PtypeUnit unitPrice = new PtypeUnit();
        unitPrice.setUnitRate(BigDecimal.ONE);
        unitPrice.setUnitCode(1);
        unitPrice.setUnitName(skuMapping.getUnitName());
        unitpriceList.add(unitPrice);
        return unitpriceList;
    }

    private BigInteger buildBrandId(String brandName) {
        BigInteger brandId = BigInteger.ZERO;
        try {
            Brandtype brandTypeParam = new Brandtype();
            brandTypeParam.setBrandName(brandName);
            brandTypeParam.setProfileId(CurrentUser.getProfileId());
            Brandtype brandtype = brandtypeService.saveInfo(brandTypeParam);
            brandId = brandtype.getId();
        } catch (Exception e) {
            BrandtypeQueryDto queryDto = new BrandtypeQueryDto();
            queryDto.setBrandName(brandName);
            List<Brandtype> brandList = brandtypeService.getList(queryDto);
            if (CollectionUtils.isNotEmpty(brandList)) {
                Optional<Brandtype> brandOpt = brandList.stream().filter(x -> x.getBrandName().equals(brandName)).findFirst();
                if (brandOpt.isPresent()) {
                    brandId = brandOpt.get().getId();
                }
            }
        }
        return brandId;
    }

    private void buildPropAndPropValues(PtypeInfoDto ptype, List<EshopProductSkuMapping> skuMappings, Map<String, EshopProductSkuAttrRelation> platPropAttrRelationMap) {
        Map<String, Prop> propNamePropMap = new LinkedHashMap<>();
        Map<String, Propvalue> propValuePropValueMap = new LinkedHashMap<>();
        for (EshopProductSkuMapping skuMapping : skuMappings) {
            skuMapping.setPlatformFullProperties(null != skuMapping.getPlatformFullProperties() ? skuMapping.getPlatformFullProperties().replace("@|@", "").replaceAll("[\t\n\r]", "") : "");
            skuMapping.setPlatformPropertiesName(null != skuMapping.getPlatformPropertiesName() ? skuMapping.getPlatformPropertiesName().replace("@|@", "").replaceAll("[\t\n\r]", "") : "");
            skuMapping.setHasMemoFullPropertiesName(null != skuMapping.getHasMemoFullPropertiesName() ? skuMapping.getHasMemoFullPropertiesName().replace("@|@", "").replaceAll("[\t\n\r]", "") : "");
            skuMapping.setReadySkuName(null != skuMapping.getReadySkuName() ? skuMapping.getReadySkuName().replace("@|@", "") : "");
            if (StringUtils.isBlank(skuMapping.getPlatformFullProperties())) {
                continue;
            }
            String[] propNameAndValueList = skuMapping.getPlatformFullProperties().split(StringConstant.SEMICOLON);
            List<PropValueName> localPropValues = new ArrayList<>();
            for (String propNameAndValue : propNameAndValueList) {
                if (StringUtils.isBlank(propNameAndValue)) {
                    continue;
                }
                if (!propNameAndValue.contains(StringConstant.COLON)) {
                    continue;
                }
                int colonIndex = propNameAndValue.indexOf(StringConstant.COLON);
                if (colonIndex + 1 == propNameAndValue.length()) {
                    continue;
                }
                String attrName = propNameAndValue.substring(0, colonIndex);
                String attrValue = propNameAndValue.substring(colonIndex + 1);
                EshopProductSkuAttrRelation skuAttrRelation = platPropAttrRelationMap.get(attrName);
                //正常情况下不会找不到映射。前面逻辑已经做了属性映射。
                if (null == skuAttrRelation || skuAttrRelation.getPropId() == null || skuAttrRelation.getPropId().compareTo(BigInteger.ZERO) == 0) {
                    continue;
                }
                if (!propNamePropMap.containsKey(attrName)) {
                    Prop localProp = new Prop();
                    localProp.setProfileId(CurrentUser.getProfileId());
                    localProp.setCreateTime(DateUtils.getDate());
                    localProp.setUpdateTime(DateUtils.getDate());
                    localProp.setId(skuAttrRelation.getPropId());
                    localProp.setPropName(attrName);
                    localProp.setRowindex(skuAttrRelation.getRowIndex());
                    propNamePropMap.put(attrName, localProp);
                }
                if (!propValuePropValueMap.containsKey(attrValue)) {
                    Propvalue propValue = new Propvalue();
                    propValue.setProfileId(CurrentUser.getProfileId());
                    propValue.setPropId(skuAttrRelation.getPropId());
                    propValue.setPropvalueName(attrValue);
                    propValue.setRowindex(skuAttrRelation.getRowIndex());
                    propValuePropValueMap.put(attrValue, propValue);
                }
                PropValueName propValueName = new PropValueName();
                propValueName.setPropId(skuAttrRelation.getPropId());
                propValueName.setPropvalueName(attrValue);
                localPropValues.add(propValueName);
            }
            skuMapping.setLocalPropValues(localPropValues);
        }
        //商品所有的属性
        if (propNamePropMap.size() > 0) {
            ptype.setProps(new ArrayList<>(propNamePropMap.values()));
            ptype.setPropenabled(true);
        }
        //商品所有的属性值
        if (propValuePropValueMap.size() > 0) {
            ptype.setPropvalues(new ArrayList<>(propValuePropValueMap.values()));
        }
    }

    public String currentTime() {
        return DateUtils.formatDateTime(new Date());
    }

    public void savePtypeInfos(List<PtypeInfoExt> ptypeInfoExtList, ProcessLoggerImpl pLogger) {
        List<PtypeInfoDto> ptypeInfoDtoList = ptypeInfoExtList.stream().map(PtypeInfoExt::getPtypeInfoDto).collect(Collectors.toList());
        PtypeSaveParam ptypeSaveParam = new PtypeSaveParam();
        ptypeSaveParam.onAppendMsg(msg -> {
            //回调写消息
            ProductManageUtil.appendProcessMsg(pLogger, msg);
        });
        ptypeSaveParam.onError(ex -> logger.error("账套ID：{},保存商品信息报错，错误信息:{}", CurrentUser.getProfileId(), ex.getMessage(), ex));
        ptypeService.addPtypeInfos(ptypeSaveParam, ptypeInfoDtoList);
    }

    private PtypeSku buildPtypeSku(EshopProductSkuMapping skuMapping, PtypeInfoDto ptypeInfoDto) {
        PtypeSku ptypeSku = new PtypeSku();
        ptypeSku.setProfileId(CurrentUser.getProfileId());
        ptypeSku.setPtypeId(ptypeInfoDto.getId());
        // ptypeSku.setUsercode(ptype.getUsercode());
        buildSkuPic(ptypeSku, skuMapping);
        //当前SKU的所有属性值(有序的)
        List<PropValueName> propValueNames = skuMapping.getLocalPropValues();
        List<Prop> propNames = ptypeInfoDto.getProps();
        if (CollectionUtils.isEmpty(propNames)){
            return ptypeSku;
        }
        Map<BigInteger, Prop> propNamesMap = propNames.stream().collect(Collectors.toMap(Prop::getId, Function.identity(), (key1, key2) -> key2));

        PropValueName propValue1 = arraySizeIfOut(propValueNames, 1);
        ptypeSku.setPropId1(propValue1.getPropId());
        ptypeSku.setPropName1(null == propNamesMap.get(propValue1.getPropId()) ? "" : propNamesMap.get(propValue1.getPropId()).getPropName());
        ptypeSku.setPropvalueId1(propValue1.getId());
        ptypeSku.setPropvalueName1(trimEnd(propValue1.getPropvalueName()));

        PropValueName propValue2 = arraySizeIfOut(propValueNames, 2);
        ptypeSku.setPropId2(propValue2.getPropId());
        ptypeSku.setPropName2(null == propNamesMap.get(propValue2.getPropId()) ? "" : propNamesMap.get(propValue2.getPropId()).getPropName());
        ptypeSku.setPropvalueId2(propValue2.getId());
        ptypeSku.setPropvalueName2(trimEnd(propValue2.getPropvalueName()));

        PropValueName propValue3 = arraySizeIfOut(propValueNames, 3);
        ptypeSku.setPropId3(propValue3.getPropId());
        ptypeSku.setPropName3(null == propNamesMap.get(propValue3.getPropId()) ? "" : propNamesMap.get(propValue3.getPropId()).getPropName());
        ptypeSku.setPropvalueId3(propValue3.getId());
        ptypeSku.setPropvalueName3(trimEnd(propValue3.getPropvalueName()));

        PropValueName propValue4 = arraySizeIfOut(propValueNames, 4);
        ptypeSku.setPropId4(propValue4.getPropId());
        ptypeSku.setPropName4(null == propNamesMap.get(propValue4.getPropId()) ? "" : propNamesMap.get(propValue4.getPropId()).getPropName());
        ptypeSku.setPropvalueId4(propValue4.getId());
        ptypeSku.setPropvalueName4(trimEnd(propValue4.getPropvalueName()));

        PropValueName propValue5 = arraySizeIfOut(propValueNames, 5);
        ptypeSku.setPropId5(propValue5.getPropId());
        ptypeSku.setPropName5(null == propNamesMap.get(propValue5.getPropId()) ? "" : propNamesMap.get(propValue5.getPropId()).getPropName());
        ptypeSku.setPropvalueId5(propValue5.getId());
        ptypeSku.setPropvalueName5(trimEnd(propValue5.getPropvalueName()));

        PropValueName propValue6 = arraySizeIfOut(propValueNames, 6);
        ptypeSku.setPropId6(propValue6.getPropId());
        ptypeSku.setPropName6(null == propNamesMap.get(propValue6.getPropId()) ? "" : propNamesMap.get(propValue6.getPropId()).getPropName());
        ptypeSku.setPropvalueId6(propValue6.getId());
        ptypeSku.setPropvalueName6(trimEnd(propValue6.getPropvalueName()));
        return ptypeSku;
    }

    private void buildPtypeSkus(PtypeInfoExt ptypeInfoExt, List<EshopProductSkuMapping> skuMappings) {
        List<PtypeSkuExt> skuExts = new ArrayList<>();
        List<PtypeSku> skus = new ArrayList<>();
        List<PtypeXcodeDto> ptypeXcodeList = new ArrayList<>();
        PtypeInfoDto ptypeInfoDto = ptypeInfoExt.getPtypeInfoDto();
        for (EshopProductSkuMapping skuMapping : skuMappings) {
            PtypeSku ptypeSku = buildPtypeSku(skuMapping, ptypeInfoDto);
            skus.add(ptypeSku);
            PtypeSkuExt ptypeSkuExt = new PtypeSkuExt();
            ptypeSkuExt.setPtypeSku(ptypeSku);
            ptypeSkuExt.setQty(skuMapping.getQty());
            skuMapping.setPtypeUniqueId(ptypeInfoDto.getCallCustomHashMap().get(PTYPE_UNIQUE_ID));
            skuExts.add(ptypeSkuExt);
            if (StringUtils.isNotBlank(skuMapping.getPlatformXcode())) {
                PtypeXcodeDto ptypeXcodeDto = buildPtypeXcodeDto(skuMapping.getPlatformXcode(), ptypeSku);
                ptypeXcodeList.add(ptypeXcodeDto);
            }
        }
        ptypeInfoDto.setSkus(skus);
        ptypeInfoDto.setXcodes(ptypeXcodeList);
        ptypeInfoExt.setSkuExts(skuExts);
    }

    public void buildSkuPic(PtypeSku ptypeSku, EshopProductSkuMapping skuMapping) {
        ptypeSku.setPicUrl(StringUtils.isNotBlank(skuMapping.getPicUrl()) ? skuMapping.getPicUrl() : skuMapping.getPlatformPicUrl());
    }

    public PropValueName arraySizeIfOut(List<PropValueName> propValueName, int size) {
        if (propValueName.size() >= size) {
            return propValueName.get(size - 1);
        } else {
            return new PropValueName();
        }
    }

    public static String trimEnd(String source) {
        source = trimEnd(source, " ");
        source = trimEnd(source, "\t");
        source = trimEnd(source, "\n");
        return source;
    }

    public static String trimEnd(String source, String splitter) {
        if (StringUtils.isEmpty(source)) {
            return source;
        }
        if (!source.endsWith(splitter)) {
            return source;
        }
        int last = source.lastIndexOf(splitter);
        source = source.substring(0, last);
        return source;
    }

    public void afterProcess(List<EshopProductSkuMapping> allSkuList, List<PtypeInfoExt> successPtypeInfos, ProductOperateLogType operateLogType) {
        if (CollectionUtils.isEmpty(allSkuList) || CollectionUtils.isEmpty(successPtypeInfos)) {
            return;
        }
        HashMap<String, PtypeInfoDto> uniqueIdPtypeInfoMap = new HashMap<>();
        for (PtypeInfoExt successPtypeInfo : successPtypeInfos) {
            String ptypeUniqueId = successPtypeInfo.getPtypeInfoDto().getCallCustomHashMap().get(PtypeSaver.PTYPE_UNIQUE_ID);
            if (StringUtils.isNotEmpty(ptypeUniqueId)) {
                uniqueIdPtypeInfoMap.put(ptypeUniqueId, successPtypeInfo.getPtypeInfoDto());
            }
        }
        List<EshopProductSkuMapping> successSkuList = new ArrayList<>();
        List<EshopProductSkuMapping> manalMappingSkuList = new ArrayList<>();
        for (EshopProductSkuMapping skuMapping : allSkuList) {
            PtypeInfoDto ptypeInfoDto = uniqueIdPtypeInfoMap.get(skuMapping.getPtypeUniqueId());
            if (ptypeInfoDto != null) {
                successSkuList.add(skuMapping);
                skuMapping.setPtypeId(ptypeInfoDto.getId());
                List<PtypeSku> skus = ptypeInfoDto.getSkus();
                PtypeSku ptypeSku = matchPtypeSku(skuMapping, skus);
                if (ptypeSku != null) {
                    skuMapping.setSkuId(ptypeSku.getId());
                }
                if (CollectionUtils.isNotEmpty(ptypeInfoDto.getUnits())) {
                    skuMapping.setUnitId(ptypeInfoDto.getUnits().get(0).getId());
                }
                skuMapping.setPcategory(Pcategory.Ptype);
                skuMapping.setXcode(skuMapping.getPlatformXcode());
                buildNewSkuMappingType(skuMapping, ptypeInfoDto, ptypeSku);
                if (skuMapping.getMappingType() != null && skuMapping.getMappingType() == MappingType.XCODEMAPPING) {
                    LogService.add(EshopProductMappingLogService.toMappingLog(skuMapping, operateLogType, "按商家编码对应"));
                } else {
                    LogService.add(EshopProductMappingLogService.toMappingLog(skuMapping, operateLogType, "按手工对应"));
                    manalMappingSkuList.add(skuMapping);
                }
            }
        }
        if (CollectionUtils.isEmpty(successSkuList)) {
            return;
        }
        if (CollectionUtils.isNotEmpty(manalMappingSkuList)) {
            //改映射类型
            Map<BigInteger, List<EshopProductSkuMapping>> skusByShop = manalMappingSkuList.stream().collect(Collectors.groupingBy(EshopProductSkuMapping::getEshopId));
            skusByShop.forEach((eshopId, skus) -> {
                List<String> uniqueIds = skus.stream().map(EshopProductSkuMapping::getUniqueId).collect(Collectors.toList());
                productNewMapper.updateSkuExpandMappingType(CurrentUser.getProfileId(), uniqueIds, MappingType.NOMARL.getCode());
            });
        }
        //写映射关系
        List<List<EshopProductSkuMapping>> insertMappingsTasks = Lists.partition(successSkuList, config.getPageSize());
        for (List<EshopProductSkuMapping> insertMappingsTask : insertMappingsTasks) {
            productNewMapper.batchInsertOrUpdateProductSkuMapping(insertMappingsTask);
        }
        //通知订单更改对应关系
        productNotifyService.updateOrderDetailMapping(successSkuList, null);
        //todo 对应关系变更通知库存同步 暂时不做
    }

    private void buildNewSkuMappingType(EshopProductSkuMapping skuMapping, PtypeInfoDto ptypeInfoDto, PtypeSku ptypeSku) {
        //按商家编码对应的商品，未对应上就变成手工对应
        if (skuMapping.getMappingType() != null && skuMapping.getMappingType() == MappingType.XCODEMAPPING) {
            if (StringUtils.isEmpty(skuMapping.getPlatformXcode())) {
                skuMapping.setMappingType(MappingType.NOMARL);
            } else {
                //无属性商品
                String localXcode = "";
                if (!skuMapping.isHasProperties()) {
                    localXcode = ptypeInfoDto.getUsercode();
                } else if (CollectionUtils.isNotEmpty(ptypeInfoDto.getXcodes()) && ptypeSku != null) {
                    Optional<PtypeXcodeDto> xcodeDtoOpt = ptypeInfoDto.getXcodes().stream()
                            .filter(xcodeDto -> Objects.equals(xcodeDto.getSku().getId(), ptypeSku.getId())).findFirst();
                    if (xcodeDtoOpt.isPresent()) {
                        localXcode = xcodeDtoOpt.get().getXcode();
                    }
                }
                if (!StringUtils.equals(localXcode, skuMapping.getPlatformXcode())) {
                    skuMapping.setMappingType(MappingType.NOMARL);
                }
            }
        }
    }

    private PtypeSku matchPtypeSku(EshopProductSkuMapping skuMapping, List<PtypeSku> skus) {
        List<PropValueName> localPropValues = skuMapping.getLocalPropValues();
        //无属性商品
        if (CollectionUtils.isEmpty(localPropValues)) {
            if (CollectionUtils.isEmpty(skus)) {
                return null;
            } else {
                return skus.get(0);
            }
        }
        Set<String> skuPropValues = localPropValues.stream().map(PropValueName::getPropvalueName).collect(Collectors.toSet());
        for (PtypeSku ptypeSku : skus) {
            Set<String> ptypeSkuPropValues = new HashSet<>();
            if (StringUtils.isNotEmpty(ptypeSku.getPropvalueName1())) {
                ptypeSkuPropValues.add(ptypeSku.getPropvalueName1());
            }
            if (StringUtils.isNotEmpty(ptypeSku.getPropvalueName2())) {
                ptypeSkuPropValues.add(ptypeSku.getPropvalueName2());
            }
            if (StringUtils.isNotEmpty(ptypeSku.getPropvalueName3())) {
                ptypeSkuPropValues.add(ptypeSku.getPropvalueName3());
            }
            if (StringUtils.isNotEmpty(ptypeSku.getPropvalueName4())) {
                ptypeSkuPropValues.add(ptypeSku.getPropvalueName4());
            }
            if (StringUtils.isNotEmpty(ptypeSku.getPropvalueName5())) {
                ptypeSkuPropValues.add(ptypeSku.getPropvalueName5());
            }
            if (StringUtils.isNotEmpty(ptypeSku.getPropvalueName6())) {
                ptypeSkuPropValues.add(ptypeSku.getPropvalueName6());
            }
            if (ptypeSkuPropValues.containsAll(skuPropValues)) {
                return ptypeSku;
            }
        }
        return null;
    }

    public void createInitStock(CreatePtypeParams params, List<PtypeInfoExt> ptypeInfoExtList, ProcessLoggerImpl pLogger) {
        if (params.isCreateInitStock()) {
            String createInitStockShowLog = GlobalConfig.get("createInitStockShowLog");
            String pageSizeStr = GlobalConfig.get("createInitStockPageSize");
            if (StringUtils.isEmpty(pageSizeStr) || "0".equals(pageSizeStr)) {
                pageSizeStr = "50";
            }
            int pageSize = Integer.parseInt(pageSizeStr);
            ProductManageUtil.appendProcessMsg(pLogger, "开始创建期初库存，请等待...");
            List<PtypeStockRequest> requestList = new ArrayList<>();
            for (PtypeInfoExt ptypeInfoExt : ptypeInfoExtList) {
                if (ptypeInfoExt.getPtypeInfoDto() == null || CollectionUtils.isEmpty(ptypeInfoExt.getPtypeInfoDto().getSkus())) {
                    continue;
                }
                if (CollectionUtils.isEmpty(ptypeInfoExt.getSkuExts())) {
                    continue;
                }
                PtypeStockRequest request = buildInitStock(params, ptypeInfoExt);
                requestList.add(request);
            }
            List<List<PtypeStockRequest>> requestsPartition = Lists.partition(requestList, pageSize);
            for (List<PtypeStockRequest> stockRequests : requestsPartition) {
                try {
                    String postStock = jxcApi.batchPoststock(stockRequests);
                    if (StringUtils.isNotEmpty(createInitStockShowLog) && "1".equals(createInitStockShowLog)) {
                        logger.error(String.format("生成期初库存请求参数：%s,返回结果：%s", JsonUtils.toJson(stockRequests), postStock));
                    }
                } catch (Exception e) {
                    logger.error("创建期初库存报错，错误信息：{}", e.getMessage(), e);
                    ProductManageUtil.appendProcessMsg(pLogger, "创建期初库存报错，错误信息：" + e.getMessage());
                }
            }
            ProductManageUtil.appendProcessMsg(pLogger, "期初库存创建完成。");
        }
    }

    private PtypeStockRequest buildInitStock(CreatePtypeParams params, PtypeInfoExt ptypeInfoExt) {
        PtypeStockRequest request = new PtypeStockRequest();
        if (CollectionUtils.isNotEmpty(ptypeInfoExt.getSkuExts())) {
            BigInteger ptypeId = ptypeInfoExt.getPtypeInfoDto().getId();
            request.setPtypeId(ptypeId);
            request.setCostMode(CostMode.getcostMode(params.getCostCalculate()));
            request.setSkuCostState(SkuCostState.SKU_NO_COST_STATE);
            List<InitGoodsStockDetailDto> batchList = new ArrayList<>();
            for (PtypeSkuExt ptypeSkuExt : ptypeInfoExt.getSkuExts()) {
                PtypeSku ptypeSku = ptypeSkuExt.getPtypeSku();
                if (null == ptypeSku.getId()) {
                    continue;
                }
                InitGoodsStockDetailDto detailDto = new InitGoodsStockDetailDto();
                detailDto.setKtypeId(params.getKtypeId());
                detailDto.setKfullname(params.getKtypeName());
                detailDto.setSkuId(ptypeSku.getId());
                detailDto.setInitQty(ptypeSkuExt.getQty());
                detailDto.setCostMode(CostMode.getcostMode(params.getCostCalculate()));
                detailDto.setSkuCostState(SkuCostState.SKU_NO_COST_STATE);
                batchList.add(detailDto);
            }
            request.setBatchList(batchList);
        }
        return request;
    }

    public PtypeCombo getRepeatCombo(CreateComboParams params) {
        try {
            List<ComboRepeatRequest> requests = new ArrayList<>();
            for (CreateComboParams.CreateComboDetail detail : params.getDetails()) {
                ComboRepeatRequest req = new ComboRepeatRequest();
                req.setQty(detail.getQty());
                req.setPtypeId(detail.getPtypeId());
                req.setSkuId(detail.getSkuId());
                req.setUnitId(detail.getUnitId());
                requests.add(req);
            }
            ComboIsRepeatResponse response = jxcApi.ComboIsRepeat(requests);
            if (!Objects.equals(response.getCode(), StringConstant.SUCCESS_CODE)) {
                logger.error("账套{}快速生成套餐，检查明细是否重复时接口内部报错了，错误信息：{}", CurrentUser.getProfileId(), response.getMessage());
                return null;
            }
            List<String> data = response.getData();
            if (CollectionUtils.isEmpty(data)) {
                return null;
            }
            ComboSaveResponse query = jxcApi.getPtypeCombo(new BigInteger(data.get(0)));
            if (!Objects.equals(query.getCode(), StringConstant.SUCCESS_CODE)) {
                logger.error("账套{}快速生成套餐，检查明细是否重复查询套餐详情时接口内部报错了，错误信息：{}", CurrentUser.getProfileId(), query.getMessage());
                return null;
            }
            PtypeCombo ptypeCombo = query.getData();
            if (ptypeCombo.getDeleted() || ptypeCombo.getStoped()) {
                return null;
            }
            return ptypeCombo;
        } catch (Exception ex) {
            logger.error("账套{}快速生成套餐，检查明细是否重复时网络请求报错了，错误信息：{}", CurrentUser.getProfileId(), ex.getMessage(), ex);
            return null;
        }
    }

    public PtypeCombo saveCombo(CreateComboParams params) {
        try {
            if (CollectionUtils.isEmpty(params.getDetails())) {
                throw new RuntimeException("创建套餐失败：套餐明细不能为空！");
            }
            boolean allMatch = params.getDetails().stream().allMatch(CreateComboParams.CreateComboDetail::getGifted);
            if (allMatch) {
                throw new RuntimeException("创建套餐失败：套餐明细不能全部为赠品！");
            }

            params.getDetails().sort(Comparator.comparing(x -> x.getRetailPrice().doubleValue(), Comparator.reverseOrder()));
            PtypeCombo ptypeCombo = buildCombo(params);
            ComboSaveResponse save = jxcApi.createPtypeCombo(ptypeCombo);
            if(!save.getCode().equals(StringConstant.SUCCESS_CODE)){
                throw new RuntimeException(save.getMessage());
            }
            return save.getData();
        } catch (Exception ex) {
            throw new RuntimeException(ex.getMessage(), ex);
        }
    }

    private PtypeCombo buildCombo(CreateComboParams params) {
        BasicInfoClassPo ptypeParTypeId = getPtypeParTypeId(BasicInfoNameEnum.Combo);
        PtypeCombo combo = new PtypeCombo();
        combo.setPartypeid(ptypeParTypeId.getTypeid());
        combo.setUsercode(getComboDefaultCode(params));
        combo.setFullname(params.getComboName());
        combo.setPtypeCombo(new PtypeComboVo());
        buildComboPic(combo, params);
        buildComboXcode(combo, params);
        buildComboDetail(combo, params);
        buildComboPrice(combo);
        return combo;
    }

    private void buildComboPrice(PtypeCombo combo) {
        PtypePriceDto priceDto = new PtypePriceDto();
        priceDto.setUnitCode(1);
        priceDto.setRetailPrice(combo.getTotal());
        combo.setPriceList(Collections.singletonList(priceDto));
    }

    private void buildComboPic(PtypeCombo combo, CreateComboParams params) {
        if (StringUtils.isEmpty(params.getPicUrl())) {
            return;
        }
        com.wsgjp.ct.sale.biz.eshoporder.api.response.PtypePic ptypePic = new com.wsgjp.ct.sale.biz.eshoporder.api.response.PtypePic();
        ptypePic.setPicUrl(params.getPicUrl());
        ptypePic.setRowindex(1);
        combo.setPics(Collections.singletonList(ptypePic));
    }

    private void buildComboDetail(PtypeCombo combo, CreateComboParams params) {
        List<ComboDetailPo> comboDetailPoList = new ArrayList<>();
        double total = params.getDetails().stream().mapToDouble(x -> x.getQty().multiply(x.getRetailPrice()).doubleValue()).sum();
        boolean first = true;
        boolean zeroTotal = total == 0;
        BigDecimal fullScale = new BigDecimal("100");
        BigDecimal usedScale = BigDecimal.ZERO;
        for (CreateComboParams.CreateComboDetail detail : params.getDetails()) {
            ComboDetailPo detailPo = new ComboDetailPo();
            if (first) {
                detailPo.setMainPtype(true);
                first = false;
                if (zeroTotal) {
                    detailPo.setScale(fullScale);
                    usedScale = fullScale;
                }
            }
            detailPo.setPtypeId(detail.getPtypeId());
            detailPo.setSkuId(detail.getSkuId());
            detailPo.setUnitId(detail.getUnitId());
            detailPo.setQty(detail.getQty());
            detailPo.setPrice(detail.getRetailPrice());
            detailPo.setTotal(MoneyUtils.multiply(detail.getQty(), detail.getRetailPrice(), Money.Total));
            detailPo.setGifted(detail.getGifted());
            detailPo.setNecessarySku(true);
            if (!zeroTotal) {
                BigDecimal percent = MoneyUtils.divide(detailPo.getTotal(), BigDecimal.valueOf(total), Money.Price);
                usedScale = MoneyUtils.add(percent, usedScale, Money.Price);
                detailPo.setScale(percent);
            }
            comboDetailPoList.add(detailPo);
        }
        ComboDetailPo firstDetail = comboDetailPoList.get(0);
        firstDetail.setScale(MoneyUtils.add(firstDetail.getScale(), fullScale.subtract(usedScale), Money.Price));
        combo.setComboDetails(comboDetailPoList);
        combo.setTotal(new BigDecimal(total));
    }

    private String getComboDefaultCode(CreateComboParams params) {
        if (StringUtils.isNotEmpty(params.getComboCode())) {
            return params.getComboCode();
        }
        StringBuilder finalCode = new StringBuilder();
        StringBuilder finalPtypeCode = new StringBuilder();
        for (CreateComboParams.CreateComboDetail detail : params.getDetails()) {
            if (StringUtils.isNotEmpty(detail.getXcode())) {
                finalCode.append(detail.getXcode());
                if (detail.getQty().compareTo(BigDecimal.ONE) > 0) {
                    finalCode.append(String.format("*%s", detail.getQty().intValue()));
                }
                continue;
            }
            if (StringUtils.isNotEmpty(detail.getPtypeCode())) {
                finalPtypeCode.append(detail.getPtypeCode());
                if (detail.getQty().compareTo(BigDecimal.ONE) > 0) {
                    finalPtypeCode.append(String.format("*%s", detail.getQty().intValue()));
                }
            }
        }
        int maxSize = 200;
        if (StringUtils.isNotEmpty(finalCode.toString())) {
            return finalCode.toString().length()>maxSize ? finalCode.substring(0, maxSize) : finalCode.toString();
        }
        if (StringUtils.isNotEmpty(finalPtypeCode.toString())) {
            return finalPtypeCode.toString().length()>maxSize ? finalPtypeCode.substring(0, maxSize) : finalPtypeCode.toString();
        }
        return params.getComboName();
    }

    private void buildComboXcode(PtypeCombo combo, CreateComboParams params) {
        String xcode = combo.getUsercode();
        if (StringUtils.isNotEmpty(params.getXcode())) {
            xcode = params.getXcode();
        }
        PtypeXcodeDto dto = new PtypeXcodeDto();
        dto.setXcode(xcode);
        dto.setDefaulted(true);
        combo.setXcodes(Collections.singletonList(dto));
    }
}

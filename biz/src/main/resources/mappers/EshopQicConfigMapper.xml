<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopQicConfigMapper">
    <insert id="insertQualityOrgByOtypeId">
        insert into pl_eshop_qic_config(id,
                                        profile_id,
                                        eshop_id,
                                        platform_quality_org_id,
                                        platform_quality_org_name,
                                        platform_quality_warehouse_code,
                                        platform_quality_warehouse_name,
                                        platform_quality_refund_interception_code,
                                        platform_quality_receive_address,
                                        platform_quality_btype_code,
                                        platform_quality_btype_name,
                                        platform_quality_btype_insure,
                                        platform_quality_btype_insure_total,
                                        platform_quality_btype_insure_type,
                                        platform_quality_btype_backup_code,
                                        platform_quality_btype_backup_name,
                                        platform_quality_btype_backup_insure,
                                        platform_quality_btype_backup_insure_total,
                                        platform_quality_btype_backup_insure_type,
                                        platform_quality_comment,
                                        platform_quality_btype_product,
                                        platform_quality_btype_backup_product,
                                        qic_address_id)
        VALUES (#{id},
                #{profileId},
                #{eshopId},
                #{platformQualityOrgId},
                #{platformQualityOrgName},
                #{platformQualityWarehouseCode},
                #{platformQualityWarehouseName},
                #{platformQualityRefundInterceptionCode},
                #{platformQualityReceiveAddress},
                #{platformQualityBtypeCode},
                #{platformQualityBtypeName},
                #{platformQualityBtypeInsure},
                #{platformQualityBtypeInsureTotal},
                #{platformQualityBtypeInsureType},
                #{platformQualityBtypeCodeBackup},
                #{platformQualityBtypeNameBackup},
                #{platformQualityBtypeBackupInsure},
                #{platformQualityBtypeBackupInsureTotal},
                #{platformQualityBtypeBackupInsureType},
                #{platformQualityComment},
                #{platformQualityBtypeProduct},
                #{platformQualityBtypeBackupProduct},
                #{qicAddressId})
    </insert>
    <update id="updateQualityOrgByOtypeId">
        update pl_eshop_qic_config
        set platform_quality_org_id = #{platformQualityOrgId},
            platform_quality_org_name = #{platformQualityOrgName},
            platform_quality_warehouse_code = #{platformQualityWarehouseCode},
            platform_quality_warehouse_name = #{platformQualityWarehouseName},
            platform_quality_refund_interception_code = #{platformQualityRefundInterceptionCode},
            platform_quality_receive_address = #{platformQualityReceiveAddress},
            platform_quality_btype_code =#{platformQualityBtypeCode},
            platform_quality_btype_name = #{platformQualityBtypeName},
            platform_quality_btype_insure = #{platformQualityBtypeInsure},
            platform_quality_btype_insure_total = #{platformQualityBtypeInsureTotal},
            platform_quality_btype_insure_type = #{platformQualityBtypeInsureType},
            platform_quality_btype_backup_code = #{platformQualityBtypeCodeBackup},
            platform_quality_btype_backup_name = #{platformQualityBtypeNameBackup},
            platform_quality_btype_backup_insure = #{platformQualityBtypeBackupInsure},
            platform_quality_btype_backup_insure_total = #{platformQualityBtypeBackupInsureTotal},
            platform_quality_btype_backup_insure_type = #{platformQualityBtypeBackupInsureType},
            platform_quality_comment = #{platformQualityComment},
            platform_quality_btype_product = #{platformQualityBtypeProduct},
            platform_quality_btype_backup_product = #{platformQualityBtypeBackupProduct},
        <if test="qicAddressId != null">
            qic_address_id = #{qicAddressId}
        </if>
        where profile_id = #{profileId} and id = #{id}
    </update>

    <select id="getQualityOrgByOtypeIdByPlatformQualityType" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopQicConfig">
        select id,
               profile_id,
               eshop_id,
               update_time,
               create_time,
               platform_quality_org_id,
               platform_quality_org_name,
               platform_quality_warehouse_code,
               platform_quality_warehouse_name,
               platform_quality_status,
               platform_quality_refund_interception_code,
               platform_quality_receive_address,
               platform_quality_btype_code,
               platform_quality_btype_name,
               platform_quality_btype_insure,
               platform_quality_btype_insure_total,
               platform_quality_btype_insure_type as platformQualityBtypeInsureType,
               platform_quality_btype_backup_code as platformQualityBtypeCodeBackup,
               platform_quality_btype_backup_name as platformQualityBtypeNameBackup,
               platform_quality_btype_backup_insure,
               platform_quality_btype_backup_insure_total,
               platform_quality_btype_backup_insure_type as platformQualityBtypeBackupInsureType,
               platform_quality_comment,
               platform_quality_btype_product,
               platform_quality_btype_backup_product,
               qic_address_id
        from pl_eshop_qic_config where eshop_id = #{otypeId} and profile_id = #{profileId} and platform_quality_type=#{platformQualityType}
    </select>

    <select id="getQualityOrgByOtypeId" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopQicConfig">
        select id,
               profile_id,
               eshop_id,
               update_time,
               create_time,
               platform_quality_org_id,
               platform_quality_org_name,
               platform_quality_warehouse_code,
               platform_quality_warehouse_name,
               platform_quality_status,
               platform_quality_refund_interception_code,
               platform_quality_receive_address,
               platform_quality_btype_code,
               platform_quality_btype_name,
               platform_quality_btype_insure,
               platform_quality_btype_insure_total,
               platform_quality_btype_insure_type as platformQualityBtypeInsureType,
               platform_quality_btype_backup_code as platformQualityBtypeCodeBackup,
               platform_quality_btype_backup_name as platformQualityBtypeNameBackup,
               platform_quality_btype_backup_insure,
               platform_quality_btype_backup_insure_total,
               platform_quality_btype_backup_insure_type as platformQualityBtypeBackupInsureType,
               platform_quality_comment,
               platform_quality_btype_product,
               platform_quality_btype_backup_product,
               qic_address_id
        from pl_eshop_qic_config where eshop_id = #{otypeId} and profile_id = #{profileId}
    </select>
</mapper>
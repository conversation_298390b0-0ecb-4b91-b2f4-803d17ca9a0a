Type.registerNamespace('sale.eshoporder.eshop');

sale.eshoporder.eshop.EshopOnlineStoreWarehouseAction = function () {
    sale.eshoporder.eshop.EshopOnlineStoreWarehouseAction.initializeBase(this);
};

sale.eshoporder.eshop.EshopOnlineStoreWarehouseAction.prototype = {
    _activeTimer: null,
    _isStop: true,
    _returnobj: null,
    _catalogName: "",
    _selectMode: false,
    _taskid: "",
    _pageParams: null,
    _stockStates: null,
    _customFilterColumn: [],
    _notSupportShoptype: [],
    context: function (cb) {
        var changeFlag = $ms.power.eshoporder["eshoporder.onlineStoreWarehouse.set"];
        var ktypeFlag = $ms.power.baseinfo["baseinfo.ktype.view"];
        if (!ktypeFlag) {
            $common.checkError(true, "你没有仓库模块的权限，请前往菜单设置，添加仓库模块权限。");
        }
        var ktypeRes = $common.ajaxSync({
            url: "sale/eshoporder/eshopplatformstoremapping/getBaseKtypeListByProfileId",
            data: null,
            router: 'ngp'
        });
        var ktypeList = ktypeRes && ktypeRes.data ? ktypeRes.data : [];
        var otypeRes = $common.ajaxSync({
            url: "sale/eshoporder/eshoplist/getOtypeListBack",
            data: {"type": 0},
            router: 'ngp'
        });
        var shopTypeList = $common.ajaxSync({
            url: "sale/eshoporder/eshoplist/getnotSupportCreateShoptype",
            data: null,
            router: 'ngp'
        });
        this._notSupportShoptype= shopTypeList.data;
        var otypeList = otypeRes && otypeRes.data ? otypeRes.data : [];
        var filterOtypeList = otypeList.slice(0, otypeList.length);
        cb({
            "otypeList": otypeList ? otypeList : [],
            "filterOtypeList": filterOtypeList ? filterOtypeList : [],
            "ktypeList": ktypeList ? ktypeList : [],
            "changeFlag": !changeFlag.value,
            "ktypeFlag": ktypeFlag.value
        });
    },
    initialize: function EshopOnlineStoreWarehouseAction$initialize() {
        sale.eshoporder.eshop.EshopOnlineStoreWarehouseAction.callBaseMethod(this, 'initialize');
        var form = this.get_form();
        var pageParams = form.get_pageParams();
        if (pageParams == null) {
            pageParams = {};
            pageParams.eshopId = "0";
            form.baseOtype.set_value(0);
        } else {
            form.baseOtype.set_value(pageParams.eshopId);
        }
        this.addSysLog();
        this.doQuery(this);
    },
    addSysLog: function () {
        this.get_service().get("sale/eshoporder/eshopplatformstoremapping/addSysLog", function () {

        });
    }
    ,
    dispose: function () {
        sale.eshoporder.eshop.EshopOnlineStoreWarehouseAction.callBaseMethod(this, 'dispose');
    },
    doChangeBaseOtype: function (sender) {

    },
    getOtypeInfo: function (otypeId) {
        var otypeList = this.get_context("otypeList");
        // for (var otype of otypeList) {
        //     if (otypeId == otype.id) {
        //         return otype;
        //     }
        // }
        for (var i = 0; i < otypeList.length; i++) {
            if (otypeId == otypeList[i].id) {
                return otypeList[i];
            }
        }
    },
    doBatchPopUp: function (sender, e) {
        var r = sender.get_bounds();
        this.get_form().batchOperation.popup(e);
    },
    doCellBeginEdit: function (sender, args) {
        var dataField = args.get_column().get_dataField();
        var rowData =  args.get_rowData();
        var changeFlag = this.get_context("changeFlag");
        var ktypeFlag = this.get_context("ktypeFlag");
        args.set_cancel(true);
        if (dataField == "ktypeId" && !changeFlag && ktypeFlag) {
            args.set_cancel(false);
        }

        if (dataField == "selected"){
            args.set_cancel(false);
        }
        if (dataField == "platformStoreType") {
            args.set_cancel(false);
        }

        if (dataField == "platformStoreAddress" && rowData != null && rowData.source === 1) {
            args.set_cancel(false);
        }
        if (dataField == "platformStoreName" && rowData != null && rowData.source === 1) {
            args.set_cancel(false);
        }
        if (dataField == "platformStoreStockId" && rowData != null && rowData.source === 1) {
            args.set_cancel(false);
        }
    }
    ,
    importPlatformStore: function () {
        var form = this.get_form();
        var ktypeFlag = this.get_context("ktypeFlag");
        if (!ktypeFlag) {
            $common.alert("没有存货仓库查看权限，不能导入！");
            return;
        }
        /*var otypeId = form.baseOtype.get_value();
        var otypeName = form.baseOtype.get_text();
        $common.checkTips(!otypeId, "请选择网店再导入！", form.baseOtype);
        $common.checkTips("0" == otypeId, "请选择非全部的其他网店再导入!", form.baseOtype);
        var otype = this.getOtypeInfo(otypeId) != null ? this.getOtypeInfo(otypeId) : {};
        if (otype.stoped) {
            $common.alert("该网店已停用，不能导入！");
            return;
        }
        var selectOtype = form.baseOtype.get_selectedItem();
        if(Array.contains(this._notSupportShoptype, selectOtype.eshopType)){
            $common.alert("该网店不支持导入！");
            return;
        }
        this.set_context("eshopId", otypeId);
        this.set_context("eshopName", otypeName);*/
        form.import.showModal('导入网店仓库', {"ktypeList": this.get_form().get_context('ktypeList')});
    },
    importFile: function (sender) {
        var eshopId = this.get_context('eshopId');
        var otypeName = this.get_context('eshopName');
        var form = sender.get_form();
        $common.checkTips(!form.importFile.get_text(), "请选择要导入的文件！", form.importFile);
        /*var ktypeId = form.ktypeId.get_value();
        $common.checkTips(!ktypeId, "请选择网店仓库再导入！", form.ktypeId);*/
        var headers = {}; // 自定义请求头，传了该参数，则使用平台新的提交上传文件..
        var panelID = sender.get_tag();
       // 这样做的目的是平台内部会根据panel.saveData()局部读取数据，而不是form.saveData()会触发全局验证
        var _taskid = this.generateUUID();
        this._taskid = _taskid;
        this.doImportSucceedMessage(sender);
        var eshopTypes = this.get_form().get_context('otypeList')
        $uploader(form[panelID], // 只会当前控件对应容器的FileUpload触发panel的saveData
            $createDelegate(this, this.doImportSucceeded),
            $createDelegate(this, this.doImportFailed),
            function (data) { // 支持动态构造服务端需要的参数，因此不是必须再gspx写隐藏域<HiddenField>也可以传参
                data.eshopTypes =  JSON.stringify(eshopTypes);
                data.type = 99;
                data.taskId = _taskid;
            }, headers); // 自定义请求头，传了该参数，则使用平台新的提交上传文件..
    }
    ,
    generateUUID: function() {
        if (crypto && crypto.randomUUID) {
            return crypto.randomUUID();
        }
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            var r = Math.random() * 16 | 0;
            var v = c == 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    },
    doImportSucceeded: function (sender, result) {
        this.doQuery(this);
        /*if (result.code != '200') {
            $common.showError("导入失败，" + result.message);
            // Sys.UI.MessageBox.alert("导入失败：" + result.message);
            return;
        }
        var list = result.data;
        var form = this.get_form();
        sender.get_form().doOk();
        if (list.length > 0 && list != null) {
            form.importError.showModal('模板存在以下错误信息，请修改后重新导入', {"errorList": list}, {minWidth: 500, minHeight: 400});
        } else {
            $common.showOk("保存成功");
            this.doQuery(this);
        }*/
    },
    doImportSucceedMessage: function (sender) {
        var _this = this;
        _this._activeTimer = new Sys.Timer();
        var intervalCount = 2000;
        _this._activeTimer.set_interval(intervalCount);
        _this._activeTimer.set_enabled(true);
        _this._activeTimer.add_tick(Function.createDelegate(this, function () {
            _this.getMessage(sender);
        }));
    },

    getMessage: function (sender) {
        var form = sender.get_form();
        if (null == form) {
            return;
        }
        var reqParam = new Object();
        reqParam.requestStr = this._taskid;
        var _this = this;

        var service = this.get_service();
        service.post("sale/eshoporder/common/getProcessMsg", reqParam, function (res) {
            var obj = res.data;
            if (!obj) {
                return;
            }
            if (obj.completed) { //已经结束
                if (obj.message) {
                    form.messageInfo.set_text(obj.message);
                    if (_this._activeTimer) {
                        _this._activeTimer.set_enabled(false);
                    }
                    form.btnStart.set_enabled(true);
                    form.btnStop.set_enabled(false);
                    form.result = true;
                }
            }
            if (obj.message) {
                form.messageInfo.set_text(obj.message);
            }
        });
    },
    doStopProgress: function (sender) {
        var form = sender.get_form();
        form.btnStart.set_enabled(true);
        form.btnStop.set_enabled(false);
        $common.alert("已停止上传网店仓库信息");
        var param = form.get_params();
        var taskParama = new Object();
        taskParama.status = 3;
        taskParama.type = 0;
        taskParama.eshopid = param.eshopId;
        taskParama.taskid = this._taskid;
        taskParama.message = '手动停止网店仓库，任务执行失败。';
        var _this = this;
        if (_this._activeTimer) {
            _this._activeTimer.set_enabled(false);
        }
        _this._isStop = true;
        //刷新客户供应商暂时没有任务表
        // $common.ajaxSync({
        //     url: "sale/eshoporder/common/modifyPtypeDownloadTask",
        //     data: taskParama,
        //     type: 'post',
        //     router: 'ngp'
        // });
    },
    doImportFailed: function (sender, result) {
        var form = this.get_form();
        if (result) {
            $common.alert(result);
        }
    },
    doDownloadTemplate: function () {
        $common.download("/sale/eshoporder/template/" + encodeURI("网店仓库导入模板.xls"));
    },
    exportExcel: function (sender) {
        var form = sender.get_form();
        form.export.exportData();
    },

    doSave:function (sender){
        var form = sender.get_form();
        var _this = this;
        var saveData = form.saveData();
        saveData.platformStoreNew = true;
        var selectOtype = form.newBaseOtype.get_selectedItem();
        if(Array.contains(this._notSupportShoptype, selectOtype.eshopType)){
            $common.alert("该网店不支持新增！");
            return;
        }
        var response = $common.ajaxSync({
            url: "sale/eshoporder/eshopplatformstoremapping/addStore",
            data: saveData,
            type: 'post',
            router: 'ngp'
        });
        if (response && response.code == "200") {
            if (response.data.success) {
                sender.get_form().doOk();
                _this.doQuery(_this);
                $common.showOk("新增成功！");
            } else {
                $msg.alert(response.data.message);
            }
        } else {
            $msg.alert(response.message);
        }
    },

    updatePlatformStoreInfo:function (sender, eventArgs){
        var form = sender.get_form();
        var _this = this;
        var data = form.grid.get_selectedRowData();
        var index = form.grid.get_selectedRowIndex();
        var oldData = eventArgs.get_oldValue();
        if(!data.platformStoreName){
            $common.alert("网店仓库名称不能为空");
            data.platformStoreName = oldData;
            this.get_form().grid.modifyRowData(index, data);
            return;
        }
        if(!data.platformStoreStockId){
            $common.alert("网店仓库ID不能为空");
            data.platformStoreStockId = oldData;
            this.get_form().grid.modifyRowData(index, data);
            return;
        }
        if (eventArgs.get_column().get_dataField() == "platformStoreStockId"){
            data.updateUnique = true;
        }
        var response = $common.ajaxSync({
            url: "sale/eshoporder/eshopplatformstoremapping/platformStoreEdit",
            data: data,
            type: 'post',
            router: 'ngp'
        });
        if (response && response.code == "200") {
            if (response.data.success) {
                _this.doQuery(_this);
                $common.showOk("修改成功！");
            } else {
                $msg.alert(response.data.message);
                _this.doQuery(_this);            }
        } else {
            $msg.alert(response.message);
            _this.doQuery(_this);
        }
    },

    deleted:function (){
        var grid = this.get_form().grid;
        var dataList=grid.get_selectedItems();
        if (!dataList||dataList.length===0) {
            $common.showInfo("请选择需要操作的数据！");
            return;
        }
        var deleteFlag = this.get_context("changeFlag");
        if (deleteFlag) {
            $common.alert("没有删除权限！");
            return;
        }
        var _this = this;
        var postData = {
            inDTOs: dataList
        };
        $common.confirm("确认删除？", function (r) {
            if (r) {
                var updateResponse = $common.ajaxSync({
                    url: "sale/eshoporder/eshopplatformstoremapping/doBatchDelete",
                    data: dataList,
                    type: 'post',
                    router: 'ngp'
                });
                if (updateResponse && updateResponse.code == "200") {
                    if (updateResponse.data.success) {
                        _this.doQuery(_this);
                        $common.showOk("删除成功！");
                    } else {
                        $msg.alert(updateResponse.data.message);
                    }
                } else {
                    $msg.alert(updateResponse.message);
                }
            }
        });
    },

    addPlatformStore: function () {
        var _this = this;
        var form = this.get_form();
        var ktypeFlag = this.get_context("ktypeFlag");
        if (!ktypeFlag) {
            $common.alert("没有存货仓库查看权限，不能新增！");
            return;
        }
        var customForm = form.addPlatformArtesianRotation.showModal('新增网店仓库',
            {
                "ktypeList": _this.get_form().get_context('ktypeList'),
                "filterOtypeList": _this.get_form().get_context('filterOtypeList'),
            });
        var otypeId = form.baseOtype.get_value();
        customForm.newBaseOtype.set_value(otypeId)
    },

    doRefresh: function (sender) {
        var data = {};
        data.otypeList = this.get_context("otypeList");
        var _this = this;
        var popForm = new Sys.UI.Form(sender, 0, 0);
        popForm.set_params(data);
        popForm.showModal("sale/eshoporder/eshop/OnlineStoreDownloadProgressModal.gspx");

        popForm.add_closed(function (pop) {
            if (!pop.result) {
                return;
            }
            _this.doQuery(_this);
        });
    },

    refreshPlatformStore: function () {
        var form = this.get_form();
        var otypeId = form.baseOtype.get_value();
        $common.checkTips(!otypeId, "请选择网店再刷新!", form.baseOtype);
        $common.checkTips("0" == otypeId, "请选择非全部的其他网店再刷新!", form.baseOtype);
        var otype = this.getOtypeInfo(otypeId) != null ? this.getOtypeInfo(otypeId) : {};
        if (otype.stoped) {
            $common.alert("该网店已停用，不能刷新！");
            return;
        }
        if (otype.ocategory == 3) {
            $common.alert("暂不支持虚拟店铺刷新！");
            return;
        }
        var data = {};
        data.eshopId = otypeId;
        var response = $common.ajaxSync({
            url: "sale/eshoporder/eshopplatformstoremapping/refreshPlatformStore",
            data: data,
            router: 'ngp'
        });
        if (response && response.code == "200") {
            if (response.data.success) {
                this.doQuery(this);
                $common.alertOk("刷新成功！");
            } else {
                $msg.alert(response.data.message);
            }
        } else {
            $msg.alert(response.message);
        }

    },

    doShowFilter: function () {
        var grid = this.get_form().grid;
        grid.set_allowFilter(!grid.get_allowFilter());
    },

    buildFilterQueryParam: function (queryParam, filed) {
        if (!filed) return;
        if (filed.dataField == "eshopId") {
            queryParam.otypeFilter = filed.value;
        } else if (filed.dataField == "platformStoreStockId") {
            queryParam.storeIdFilter = filed.value;
        }  else if (filed.dataField == "platformStoreAddress") {
            queryParam.storeAddressFilter = filed.value;
        } else if (filed.dataField == "correspondFlag") {
            if (filed.value) {
                queryParam.correspondFlag = 1;
            } else {
                queryParam.correspondFlag = 0;
            }
        } else if (filed.dataField == "ktypeId") {
            queryParam.ktypeFilter = filed.value;
        } else if (filed.dataField == "platformStoreTypeTags"){
            queryParam.platformStoreTypeFilter = filed.value;
        } else if (filed.dataField == "platformStoreNew"){
            queryParam.platformStoreNew = filed.value;
        }
    },

    buildDefaultParam: function (params) {
        var requestParams = Object();
        var form = this.get_form();
        var otypeIds = form.baseOtype.get_value();
        var platformStoreType = form.platformStoreType.get_value();
        var platformStoreStockId = form.platformStoreStockId.get_value();
        /*var platformStoreName = form.platformStoreName.get_value();*/
        var correspondFlag = form.correspondFlag.get_value();
        requestParams.otypeIds = otypeIds ? otypeIds : "";
        requestParams.platformStoreStockId = platformStoreStockId ? platformStoreStockId : "";
       /* requestParams.platformStoreName = platformStoreName ? platformStoreName : "";*/
        requestParams.correspondFlag = correspondFlag;
        requestParams.platformStoreType = platformStoreType ? platformStoreType : 0;
        /*requestParams.type = 0;*/
        requestParams.deleted = 0;
        requestParams.otypeFilter = null;
        requestParams.storeIdFilter = "";
        requestParams.storeNameFilter = "";
        requestParams.storeAddressFilter = "";
        requestParams.storeFlagFilter = 2;
        requestParams.platformStoreNew = true;
        requestParams.ktypeFilter = null
        params.queryParams = requestParams;
    },

    gridPagerDataSource: function (path, params, binData) {
        var filter = !params.queryParams.gridFilter ? [] : params.queryParams.gridFilter;
        this.buildDefaultParam(params);
        if (filter && filter.length > 0) {
            for (var i = 0; i < filter.length; i++) {
                this.buildFilterQueryParam(params.queryParams, filter[i]);
            }
        }
        var startObj =null;
        if(window.$startTiming){
            startObj = $startTiming('店铺->网店->全渠道门店对应');
        }
        params.queryParams.select = false;
        var queryResponse = $common.ajaxSync({
            url: "sale/eshoporder/eshopplatformstoremapping/queryListByEshopId",
            data: params, router: 'ngp'
        });
        if (queryResponse.code != 200) {
            binData({
                itemList: [],
                itemCount: 0
            });
        } else {
            binData({
                itemList: queryResponse.data.list,
                itemCount: queryResponse.data.total
            });
        }
        if(startObj!=null){
            startObj.endTiming();// 触发上报
        }
    }
    ,
    doQuery: function (sender) {
        $common.showLoading();
        var form = sender.get_form();
        var queryParams = this.buildQueryParams();
        queryParams.select = true;
        setTimeout(function () {
            form.grid.get_pager().refresh(queryParams);
            $common.hideLoading();
        }, 1);
    }
    ,
    buildQueryParams: function () {
        var requestParams = Object();
        var form = this.get_form();
        if (form.baseOtype != undefined && form.baseOtype.get_value() != null) {
            requestParams.otypeIds = form.baseOtype.get_value();
        }

        requestParams.platformStoreStockId = form.platformStoreStockId.get_value();
        /*requestParams.platformStoreName = form.platformStoreName.get_value();*/
        requestParams.correspondFlag = form.correspondFlag.get_value();
        requestParams.platformStoreType = form.platformStoreType.get_value();
        requestParams.platformStoreNew = true;
        /*requestParams.type = 0;*/
        requestParams.deleted = 0;
        return requestParams;
    }
    ,
    selectList: function (sender) {
        $common.showLoading();
        var form = sender.get_form();
        var queryParams = this.buildQueryParams();
        queryParams.select = true;
        setTimeout(function () {
            form.grid.get_pager().refresh(queryParams);
            $common.hideLoading();
        }, 1);

    },
    doBindEmpty: function (sender, args) {
        var form = this.get_form();
        var block = $createControl('Block', {
            CssClass: 'EmptyBlock' // 这个样式平台已经实现
        }, form);
        if (args.isRefresh) { // 情况2：查询或刷新空数据
            var img = $createControl('Image', {UseServer: true, Src: 'shell/skins/images/empty.png'}, form, block);
            var btn = $createControl('Label', {
                Text: '未查询到数据'
            }, form, block);
        } else { // 情况1：初始就是空数据
            var img = $createControl('Image', {
                UseServer: true, Src: 'shell/skins/images/bindempty.png' // ngp环境下面，这个图片路径需要使用：shell/skins/images/bindempty.png
            }, form, block);
            var btn = $createControl('Label', {
                Text: '输入查询条件，点击【查询】吧'
            }, form, block);
        }
        args.emptyControl = block;
    }
    ,
    updateSelectedRow: function (param, _this, index, data, dataFiled, oldData) {
        var updateResponse = $common.ajaxSync({
            url: "sale/eshoporder/eshopplatformstoremapping/updateById",
            data: param, router: 'ngp'
        });
        if (updateResponse && updateResponse.code == "200") {
            if (updateResponse.data.success) {
                _this.doQuery(_this);
            } else {
                $msg.alert(updateResponse.data.message);
                if (dataFiled == "ktypeId") {
                    data.ktypeId = oldData;
                }
                if (dataFiled == "platformStoreType") {
                    data.platformStoreType = oldData;
                }
                _this.get_form().grid.modifyRowData(index, data);
            }
        } else {
            $msg.alert(updateResponse.message);
        }
    }
    ,
    doKtypeChange: function (sender, eventArgs) {
        var _this = this;
        var grid = this.get_form().grid;
        var index = grid.get_selectedRowIndex();
        var data = grid.get_selectedRowData();
        var oldData = eventArgs.get_oldValue();
        var param = {};
        param.id = data.id;
        param.eshopId = data.eshopId;
        param.platformStoreStockId = data.platformStoreStockId;
        param.ktypeId = data.ktypeId == null ? "0" : data.ktypeId;
        if (param.ktypeId == "0") {
            param.correspondFlag = 0;
        } else {
            param.correspondFlag = 1;
        }
        if (oldData) {
            $msg.confirm("已存在绑定的网店仓库，请确认是否更换绑定？", function (result) {
                if (result) {
                    _this.updateSelectedRow(param, _this, index, data, "ktypeId", oldData);
                } else {
                    data.ktypeId = oldData;
                    grid.modifyRowData(index, data);
                }
            });
        } else {
            _this.updateSelectedRow(param, _this, index, data, "ktypeId", oldData);
        }
    },

    doPlatformStoreTypeChange: function (sender, eventArgs) {
        var _this = this;
        var grid = this.get_form().grid;
        var index = grid.get_selectedRowIndex();
        var data = grid.get_selectedRowData();
        var oldData = eventArgs.get_oldValue();
        var param = {};
        param.id = data.id;
        param.eshopId = data.eshopId;
        param.platformStoreType = data.platformStoreType;
        _this.updateSelectedRow(param, _this, index, data, "platformStoreType", oldData);
    },

    doFilterRendering: function(sender, args) {
        var form = this.get_form();
        var column = sender;

        var dataField = column.get_dataField(); // 列名称

        var editID = dataField + "_edit";  // 获取编辑框控件的id，必须唯一
        var panel = $createControl(Craba.UI.Block, {CssClass:"FlexAuto"}, form);
        var bar = sender.get_grid()._filterBar;
        this._customFilterColumn.push(column);

        switch (dataField) {
            case 'platformStoreTypeTags':
                //导出状态
                var data = [
                    {name: '全部', code: 0},
                    {name: '门店', code: 1},
                    {name: '商家仓', code: 2},
                    {name: '平台仓', code: 3}
                ]
                $createControl('DropDownEdit',
                    {
                        ID: editID,
                        DataSource: data,
                        DataField: "code",
                        DataTextField: "name",
                        DataValueField: "code",
                        CssClass: "FlexAuto",
                        OnChange: $createDelegate(bar, bar.doFilter)
                    },
                    form,
                    panel);
                break;
            default:
                return;
        }
        args.set_control(panel); // 回传给平台的自定义编辑器
    },
    doGetOtherFilter: function (sender, args) {
        var filter = args.get_filter();

        var list = this._customFilterColumn;
        if (!list && list.length == 0) return;

        for (var i = 0; i < list.length; i++) {
            var dataField = list[i].get_dataField();
            var editID = dataField + "_edit";
            var form = this.get_form();
            var edit = form[editID]; // 取出筛选器控件
            if (!edit) continue;

            if (!filter) {
                //清空自定义筛选
                edit.set_value(null);
                continue;
            }

            var filterItem = {};
            filterItem.dataField = dataField;
            filterItem.value = edit.get_value();
            filterItem.type = list[i].get_filterType();

            filter.items.push(filterItem);
        }
    }
}
;
sale.eshoporder.eshop.EshopOnlineStoreWarehouseAction.registerClass('sale.eshoporder.eshop.EshopOnlineStoreWarehouseAction', Sys.UI.PageAction);
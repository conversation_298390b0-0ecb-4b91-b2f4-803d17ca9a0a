package com.wsgjp.ct.sale.biz.eshoporder.entity.eshop;


import com.wsgjp.ct.sale.biz.eshoporder.util.CommonUtil;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

/**
 * 全渠道门店/平台仓库对应表(PlEshopPlatformStoreMapping)实体类
 *
 * @since 2021-01-30 10:45:32
 */
public class EshopPlatformStoreMapping {

    /**
     * 账套id
     */
    private BigInteger profileId;
    /**
     * 网店id
     */
    private BigInteger eshopId;
    /**
     * id
     */
    private BigInteger id;
    /**
     * 线上仓库/门店id|平台仓库id
     */
    private String platformStoreStockId;
    /**
     * 本地仓库id
     */
    private BigInteger ktypeId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 门店/网点名称|平台仓库名称
     */
    private String platformStoreName;
    /**
     * 门店/网点地址|平台仓库地址
     */
    private String platformStoreAddress;
    /**
     * 门店/网点配送类型|平台仓库类型
     */
    private Integer platformStoreType;
    /**
     * 业务标签
     */
    private String businessLabel;
    /**
     * 对应关系（0为对应1已对应）
     */
    private Boolean correspondFlag;
    /**
     * 删除标识（1为已删除）
     */
    private Boolean deleted;

    /**
     * 区分门店/网点|平台类型
     * 0             1
     * 新的页面不在区分门店还是平台直接为99
     */
    private Integer type;

    /**
     * 数据来源（0线上1手工）
     * 0             1
     */
    private Integer source;

    /**
     * 网店名称
     */
    private String otypeName;
    /**
     * 本地仓库名称
     */
    private String btypeName;
    /**
     * 本地仓库编码
     */
    private String btypeUserCode;
    /**
     * 本地仓库地址
     */
    private String btypeAddress;

    private String platformStoreTypeTags;

    private BigInteger otypeFilter;

    private String storeIdFilter;

    private String storeNameFilter;

    private String storeAddressFilter;

    private int storeFlagFilter = 2;

    private BigInteger ktypeFilter;

    private boolean isUpdateUnique;

    private List<BigInteger> otypeIds;
    private String quickfilter;

    private boolean platformStoreNew;

    private String fullname ;

    private String userCode;

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getFullname() {
        return fullname;
    }

    public void setFullname(String fullname) {
        this.fullname = fullname;
    }

    public boolean isPlatformStoreNew() {
        return platformStoreNew;
    }

    public void setPlatformStoreNew(boolean platformStoreNew) {
        this.platformStoreNew = platformStoreNew;
    }

    public boolean isUpdateUnique() {
        return isUpdateUnique;
    }

    public void setUpdateUnique(boolean updateUnique) {
        isUpdateUnique = updateUnique;
    }

    private Integer platformStoreTypeFilter;

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getEshopId() {
        return eshopId;
    }

    public void setEshopId(BigInteger eshopId) {
        this.eshopId = eshopId;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getPlatformStoreStockId() {
        return null != platformStoreStockId ? platformStoreStockId.replaceAll("\\s+|\\r\\n|\\t", "") : platformStoreStockId;
    }

    public void setPlatformStoreStockId(String platformStoreStockId) {
        this.platformStoreStockId = platformStoreStockId;
    }

    public BigInteger getKtypeId() {
        return ktypeId;
    }

    public void setKtypeId(BigInteger ktypeId) {
        this.ktypeId = ktypeId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getPlatformStoreName() {
        return platformStoreName;
    }

    public void setPlatformStoreName(String platformStoreName) {
        this.platformStoreName = platformStoreName;
    }

    public String getPlatformStoreAddress() {
        return platformStoreAddress;
    }

    public void setPlatformStoreAddress(String platformStoreAddress) {
        this.platformStoreAddress = platformStoreAddress;
    }

    public Integer getPlatformStoreType() {
        return platformStoreType;
    }

    public void setPlatformStoreType(Integer platformStoreType) {
        this.platformStoreType = platformStoreType;
    }

    public String getBusinessLabel() {
        return businessLabel;
    }

    public void setBusinessLabel(String businessLabel) {
        this.businessLabel = businessLabel;
    }

    public Boolean getCorrespondFlag() {
        return correspondFlag;
    }

    public void setCorrespondFlag(Boolean correspondFlag) {
        this.correspondFlag = correspondFlag;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getOtypeName() {
        return otypeName;
    }

    public void setOtypeName(String otypeName) {
        this.otypeName = otypeName;
    }

    public String getBtypeName() {
        return btypeName;
    }

    public void setBtypeName(String btypeName) {
        this.btypeName = btypeName;
    }

    public String getBtypeUserCode() {
        return btypeUserCode;
    }

    public void setBtypeUserCode(String btypeUserCode) {
        this.btypeUserCode = btypeUserCode;
    }

    public String getBtypeAddress() {
        return btypeAddress;
    }

    public void setBtypeAddress(String btypeAddress) {
        this.btypeAddress = btypeAddress;
    }

    public String getPlatformStoreTypeTags() {
        return this.platformStoreType != null ? CommonUtil.buildPlatformStoreTypeTags(this.platformStoreType) : "";
    }

    public void setPlatformStoreTypeTags(String platformStoreTypeTags) {
        this.platformStoreTypeTags = platformStoreTypeTags;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Integer getPlatformStoreTypeFilter() {
        return platformStoreTypeFilter;
    }

    public void setPlatformStoreTypeFilter(Integer platformStoreTypeFilter) {
        this.platformStoreTypeFilter = platformStoreTypeFilter;
    }

    public BigInteger getOtypeFilter() {
        return otypeFilter;
    }

    public void setOtypeFilter(BigInteger otypeFilter) {
        this.otypeFilter = otypeFilter;
    }

    public String getStoreIdFilter() {
        return storeIdFilter;
    }

    public void setStoreIdFilter(String storeIdFilter) {
        this.storeIdFilter = storeIdFilter;
    }

    public String getStoreNameFilter() {
        return storeNameFilter;
    }

    public void setStoreNameFilter(String storeNameFilter) {
        this.storeNameFilter = storeNameFilter;
    }

    public String getStoreAddressFilter() {
        return storeAddressFilter;
    }

    public void setStoreAddressFilter(String storeAddressFilter) {
        this.storeAddressFilter = storeAddressFilter;
    }

    public int getStoreFlagFilter() {
        return storeFlagFilter;
    }

    public void setStoreFlagFilter(int storeFlagFilter) {
        this.storeFlagFilter = storeFlagFilter;
    }

    public BigInteger getKtypeFilter() {
        return ktypeFilter;
    }

    public void setKtypeFilter(BigInteger ktypeFilter) {
        this.ktypeFilter = ktypeFilter;
    }

    public List<BigInteger> getOtypeIds() {
        return otypeIds;
    }

    public void setOtypeIds(List<BigInteger> otypeIds) {
        this.otypeIds = otypeIds;
    }

    public String getQuickfilter() {
        return quickfilter;
    }

    public void setQuickfilter(String quickfilter) {
        this.quickfilter = quickfilter;
    }
}

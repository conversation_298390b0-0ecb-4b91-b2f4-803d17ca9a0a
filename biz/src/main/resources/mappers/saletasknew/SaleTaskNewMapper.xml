<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.SaleTaskNewMapper">
    <insert id="saveTaskMain">
        insert into pl_sale_task_new(id, profile_id, sort_index, rank_code, task_name, remark
                                    , task_type, task_clazz, period_group_id, task_total,creator_id,task_group_type,task_group_value,create_time)
        values (#{task.id},#{profileId},#{task.sortIndex},#{task.rankCode},#{task.taskName},#{task.remark},
                #{task.taskType},#{task.taskClazz},#{task.periodGroupId},#{task.taskTotal},#{userId}, #{task.taskGroupType}, #{task.taskGroupValue}, #{task.createTime});
    </insert>
    <insert id="saveTaskDetail">
        insert into pl_sale_task_new(id, profile_id, sort_index, rank_code,period_group_id, period_id, task_total, parent_id, task_group_type, task_group_value, ptype_id, sku_id, unit_id)
        values
        <foreach collection="taskDetails" item="detail" separator=",">
            (#{detail.id}, #{profileId}, #{detail.sortIndex}, #{detail.rankCode}, #{detail.periodGroupId}, #{detail.periodId}, #{detail.taskTotal}, #{detail.parentId}, #{detail.taskGroupType}, #{detail.taskGroupValue}, #{detail.ptypeId}, #{detail.skuId}, #{detail.unitId})
        </foreach>
    </insert>
    <insert id="savePeriodGroup">
        insert into pl_sale_period_group_new(id, profile_id, period_group_name, period_type, begin_time, end_time) values
           (#{periodGroup.id},#{profileId},#{periodGroup.periodGroupName},#{periodGroup.periodType},#{periodGroup.beginTime},#{periodGroup.endTime});
    </insert>
    <insert id="savePeriod">
        insert into pl_sale_period_new(id, profile_id, period_group_id, period_start_time, period_end_time, period_name) values
        <foreach collection="periods" item="period" separator=",">
            (#{period.id}, #{profileId}, #{period.periodGroupId}, #{period.periodStartTime}, #{period.periodEndTime}, #{period.periodName})
        </foreach>
    </insert>
    <insert id="saveCondition">
        insert into pl_sale_task_condition_new(id, profile_id, condition_type, condition_value, ptype_id, sku_id, unit_id, sale_task_id, sort_index, limit_type)
        values
        <foreach collection="conditions" item="condition" separator=",">
            (#{condition.id},#{profileId},#{condition.conditionType},#{condition.conditionValue},#{condition.ptypeId},#{condition.skuId},#{condition.unitId},#{condition.saleTaskId},#{condition.sortIndex},#{condition.limitType})
        </foreach>
    </insert>
    <update id="modifyTaskStatus">
        update pl_sale_task_new set task_status=#{taskStatus} where profile_id=#{profileId} and id in
        <foreach collection="taskIds" item="taskId" separator="," open="(" close=")">
            #{taskId}
        </foreach>;
        update pl_sale_task_new set task_status=#{taskStatus} where profile_id=#{profileId} and parent_id in
        <foreach collection="taskIds" item="taskId" separator="," open="(" close=")">
            #{taskId}
        </foreach>;
    </update>
    <update id="modifyTaskDeleteStatus">
        update pl_sale_task_new set deleted=1 where profile_id=#{profileId} and id in
        <foreach collection="taskIds" item="taskId" separator="," open="(" close=")">
            #{taskId}
        </foreach>;
        update pl_sale_task_new set deleted=1 where profile_id=#{profileId} and parent_id in
        <foreach collection="taskIds" item="taskId" separator="," open="(" close=")">
            #{taskId}
        </foreach>;
    </update>
    <update id="openTask">
        <if test="taskIds!=null and taskIds.size()>0">
            update pl_sale_task_new pstn
            join pl_sale_period_group_new pspgn on pstn.period_group_id=pspgn.id and pstn.profile_id=pspgn.profile_id
            set pstn.task_status = case
            when now()<![CDATA[ >= ]]>pspgn.begin_time and now()<![CDATA[ <= ]]>pspgn.begin_time then 0
            when now()<![CDATA[ <= ]]>pspgn.begin_time then 1
            when now()<![CDATA[ >= ]]>pspgn.end_time then 2 else 0 end
            where pstn.profile_id=#{profileId} and pstn.deleted=0
            and pstn.id in
            <foreach collection="taskIds" item="taskId" separator="," open="(" close=")">
                #{taskId}
            </foreach>;
            update pl_sale_task_new pstn
            join pl_sale_period_group_new pspgn on pstn.period_group_id=pspgn.id and pstn.profile_id=pspgn.profile_id
            set pstn.task_status = case
            when now()<![CDATA[ >= ]]>pspgn.begin_time and now()<![CDATA[ <= ]]>pspgn.begin_time then 0
            when now()<![CDATA[ <= ]]>pspgn.begin_time then 1
            when now()<![CDATA[ >= ]]>pspgn.end_time then 2 else 0 end
            where pstn.profile_id=#{profileId} and pstn.deleted=0
            and pstn.parent_id in
            <foreach collection="taskIds" item="taskId" separator="," open="(" close=")">
                #{taskId}
            </foreach>;
        </if>
        <if test="taskIds==null">
            update pl_sale_task_new pstn
            join pl_sale_period_group_new pspgn on pstn.period_group_id=pspgn.id and pstn.profile_id=pspgn.profile_id
            set pstn.task_status = case
            when now()<![CDATA[ >= ]]>pspgn.begin_time and now()<![CDATA[ <= ]]>pspgn.end_time then 0
            when now()<![CDATA[ <= ]]>pspgn.begin_time then 1
            when now()<![CDATA[ >= ]]>pspgn.end_time then 2 else 0 end
            where pstn.profile_id=#{profileId} and pstn.task_status!=3 and pstn.deleted=0
            <if test="timeType==0 and beginTime!=null and endTime!=null">
                and pstn.create_time<![CDATA[ >= ]]>#{beginTime} and pstn.create_time<![CDATA[ <= ]]>#{endTime}
            </if>
            <if test="timeType==1 and beginTime!=null and endTime!=null">
                and pspgn.begin_time <![CDATA[ >= ]]> #{beginTime}
                and pspgn.end_time <![CDATA[ <= ]]> #{endTime}
            </if>;
        </if>
    </update>
    <delete id="deletePeriodGroup">
        delete from pl_sale_period_group_new where profile_id=#{profileId} and id in
        <foreach collection="groupIds" item="groupId" open="(" close=")" separator=",">
            #{groupId}
        </foreach>
    </delete>
    <delete id="deletePeriod">
        delete from pl_sale_period_new where profile_id=#{profileId} and id in
        <foreach collection="periodIds" item="periodId" open="(" close=")" separator=",">
            #{periodId}
        </foreach>
    </delete>
    <delete id="deleteTask">
        delete from pl_sale_task_new where profile_id=#{profileId} and id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>
    <delete id="deletePeriodByGroupId">
        delete from pl_sale_period_new where profile_id=#{profileId} and period_group_id in
        <foreach collection="groupIds" item="groupId" open="(" close=")" separator=",">
            #{groupId}
        </foreach>
    </delete>
    <delete id="deleteCondition">
        delete from pl_sale_task_condition_new where profile_id=#{profileId} and sale_task_id in
        <foreach collection="taskIds" item="taskId" open="(" close=")" separator=",">
            #{taskId}
        </foreach>
    </delete>
    <select id="getSaleTaskByParamNew"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskNewDTO">
        select
        n.id,
        n.profile_id,
        n.sort_index,
        n.rank_code,
        n.task_name,
        n.remark,
        n.task_type,
        n.task_clazz,
        n.period_group_id,
        n.period_id,
        n.task_total,
        n.task_status,
        n.parent_id,
        n.task_group_type,
        n.task_group_value,
        n.ptype_id,
        n.sku_id,
        n.unit_id,
        n.task_total_basic,
        n.task_total_challenge,
        n.task_total_complete,
        n.deleted,
        n.create_time,
        n.update_time,
        n.creator_id,
        g.begin_time,
        g.end_time,
        be.fullname as etypeName
        from pl_sale_task_new n
        left join pl_sale_period_group_new g on n.period_group_id=g.id and n.profile_id=g.profile_id
        left join base_etype be on n.creator_id=be.id and n.profile_id=be.profile_id
        where n.profile_id = #{param.profileId}
        <if test="param.timeType==0">
            and n.create_time between #{param.beginTime} and #{param.endTime}
        </if>
        <if test="param.timeType==1">
            and g.begin_time <![CDATA[ >= ]]> #{param.beginTime}
            and g.end_time <![CDATA[ <= ]]> #{param.endTime}
        </if>
        <if test="param.taskName!=null and param.taskName!=''">
            and n.task_name like CONCAT('%', #{param.taskName},'%')
        </if>
        and n.task_clazz = #{param.TaskClazz}
        <choose>
            <when test="param.parentIds!=null and param.parentIds.size()>0">
                and n.parent_id in
                <foreach collection="param.parentIds" item="parentId" open="(" close=")" separator=",">
                    #{parentId}
                </foreach>
            </when>
            <otherwise>
                and n.parent_id = 0
            </otherwise>
        </choose>
        <if test="param.taskStatusList!=null and param.taskStatusList.size()>0">
            and n.task_status in
            <foreach collection="param.taskStatusList" item="status" open="(" close=")" separator=",">
                #{status}
            </foreach>
        </if>
        and n.deleted = #{param.deleted}
        order by n.create_time desc
    </select>
    <select id="getSaleTaskByTaskIds"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskNewDTO">
        select
        n.id,
        n.profile_id,
        n.sort_index,
        n.rank_code,
        n.task_name,
        n.remark,
        n.task_type,
        n.task_clazz,
        n.period_group_id,
        n.period_id,
        n.task_total,
        n.task_status,
        n.parent_id,
        n.task_group_type,
        n.task_group_value,
        n.ptype_id,
        n.sku_id,
        n.unit_id,
        n.task_total_basic,
        n.task_total_challenge,
        n.task_total_complete,
        n.deleted,
        n.create_time,
        n.update_time,
        n.creator_id,
        g.begin_time,
        g.end_time,
        ifnull(bd.fullname,'') dtypeName
        from pl_sale_task_new n
        left join pl_sale_period_group_new g on n.period_group_id=g.id and n.profile_id=g.profile_id
        left join base_dtype bd on n.profile_id=bd.profile_id and n.task_group_value=bd.id and n.task_group_type=1
        where n.profile_id = #{profileId}
        <if test="taskIds!=null and taskIds.size()>0">
            and n.id in
            <foreach collection="taskIds" item="taskId" open="(" close=")" separator=",">
                #{taskId}
            </foreach>
        </if>
        and n.deleted=0 order by n.sort_index
    </select>
    <select id="listPlSalePeriod"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.salesperiod.PlSalePeriodNew">
        select p.id,
               p.profile_id,
               p.period_group_id,
               p.period_start_time,
               p.period_end_time,
               p.period_name,
               p.create_time,
               p.update_time from pl_sale_period_group_new gp
               left join pl_sale_period_new p on gp.id=p.period_group_id and gp.profile_id=p.profile_id
        where gp.profile_id=#{profileId} and gp.period_group_name=#{name} order by p.period_start_time
    </select>
    <select id="getPeriodGroudById"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.salesperiod.PlSalePeriodGroupNew">
        select * from pl_sale_period_group_new where profile_id=#{profileId} and id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>
    <select id="getPeriodByGroupId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.salesperiod.PlSalePeriodNew">
        select * from pl_sale_period_new where profile_id=#{profileId} and period_group_id in
        <foreach collection="groupIds" item="groupId" open="(" close=")" separator=",">
            #{groupId}
        </foreach>
        order by period_start_time
    </select>
    <select id="getSaleTaskByParentIds"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskDetailNewDTO">
        select
        n.id,
        n.profile_id,
        n.sort_index,
        n.rank_code,
        n.task_name,
        n.remark,
        n.task_type,
        n.task_clazz,
        n.period_group_id,
        n.period_id,
        n.task_total,
        n.task_status,
        n.parent_id,
        n.task_group_type,
        n.task_group_value,
        n.ptype_id,
        n.sku_id,
        n.unit_id,
        n.task_total_basic,
        n.task_total_challenge,
        n.task_total_complete,
        n.deleted,
        n.create_time,
        n.update_time,
        n.creator_id,
        g.begin_time,
        g.end_time,
        '本公司' as companyName,
        ifnull(bd.fullname,'') dtypeName,
        case when n.task_group_type=3 then ifnull(bbd.brand_name,'') else ifnull(band.brand_name,'') end brandName,
        ifnull(be.fullname,'') etypeName,
        ifnull(bb.fullname,'') btypeName,
        ifnull(v.labelfield_value,'') btypeLabelName,
        ifnull(a.fullname,'') btypeAreaName,
        ifnull(bo.fullname,'') otypeName,
        ifnull(bp2.fullname,'') ptypeClassName,
        ifnull(v1.labelfield_value,'') ptypeLabelName,
        ifnull(bp.fullname,'') ptypeName,
        ifnull(bp.usercode,'') usercode,
        ifnull(bp.standard,'') standard,
        ifnull(bp.ptype_type,'') ptypeType,
        IFNULL(sku.propvalue_names, '') AS propValues,
        IFNULL(xcode.xcode, '') AS xcode
        , unit.unit_name

        , barcode.fullbarcode AS barcode
        , ifnull(sku.prop_names,'') propFormat
        , bp.snenabled as snEnabled
        , bp.batchenabled
        , IF (bp.pcategory=1
        , 1
        , 0) AS pcategory
        , IF (bp.pcategory=2
        , 1
        , 0) AS combo,
        case
        when n.task_group_type=0 then '本公司'
        when n.task_group_type=1 then bd.fullname
        when n.task_group_type=2 then be.fullname
        when n.task_group_type=3 then bbd.brand_name
        when n.task_group_type=4 then bp.fullname
        when n.task_group_type=5 then bp2.fullname
        when n.task_group_type=6 then v1.labelfield_value
        when n.task_group_type=7 then bb.fullname
        when n.task_group_type=8 then v.labelfield_value
        when n.task_group_type=9 then a.fullname
        when n.task_group_type=10 then bo.fullname
        else '' end taskGroupValueName
        from pl_sale_task_new n
        left join pl_sale_period_group_new g on n.period_group_id=g.id and n.profile_id=g.profile_id
        left join base_dtype bd on n.profile_id=bd.profile_id and n.task_group_value=bd.id and n.task_group_type=1
        left join base_etype be on n.profile_id=be.profile_id and n.task_group_value=be.id and n.task_group_type=2
        left join base_brandtype bbd on n.profile_id=bbd.profile_id and n.task_group_value=bbd.id and n.task_group_type=3
        left join base_ptype bp on n.ptype_id=bp.id and n.profile_id=bp.profile_id and n.task_group_type=4
        left join base_ptype bp2 on n.task_group_value=bp2.id and n.profile_id=bp2.profile_id and n.task_group_type=5
        left join cf_labelfield_value v1 on n.profile_id = v1.profile_id and n.task_group_value = v1.id and n.task_group_type=6
        left join base_btype bb on n.task_group_value=bb.id and n.profile_id=bb.profile_id and n.task_group_type=7
        left join cf_labelfield_value v on n.profile_id = v.profile_id and n.task_group_value = v.id and n.task_group_type=8
        left join base_areatype a on n.profile_id = a.profile_id and n.task_group_value = a.id and n.task_group_type=9
        left join base_otype bo on n.profile_id = bo.profile_id and n.task_group_value = bo.id and n.task_group_type=10


        LEFT JOIN base_brandtype band ON band.profile_id=bp.`profile_id`
        AND band.id=bp.`brand_id`
        LEFT JOIN `base_ptype_sku` sku ON sku.profile_id=n.`profile_id`
        AND sku.id=n.sku_id
        LEFT JOIN base_ptype_xcode xcode ON xcode.profile_id=n.`profile_id`
        AND xcode.ptype_id=n.ptype_id
        AND xcode.sku_id=n.sku_id
        AND xcode.unit_id=n.unit_id
        AND xcode.defaulted=1
        LEFT JOIN base_ptype_fullbarcode barcode ON barcode.profile_id=n.profile_id
        AND barcode.ptype_id=n.ptype_id
        AND barcode.sku_id=n.sku_id
        AND barcode.unit_id=n.unit_id
        AND barcode.defaulted=1
        LEFT JOIN base_ptype_pic pic ON pic.profile_id=n.`profile_id`
        AND pic.ptype_id=n.ptype_id
        AND pic.rowindex=1
        LEFT JOIN base_ptype_unit unit ON unit.profile_id=n.`profile_id`
        AND unit.ptype_id=n.ptype_id
        AND unit.id=n.unit_id


        where n.profile_id = #{profileId}
        <if test="parentIds!=null and parentIds.size()>0">
            and n.parent_id in
            <foreach collection="parentIds" item="parentId" open="(" close=")" separator=",">
                #{parentId}
            </foreach>
        </if>
        and n.deleted=0 order by n.sort_index
    </select>
    <select id="getConditionByTaskIds"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskConditionNew">
        select id,
        profile_id,
        create_time,
        update_time,
        condition_type,
        condition_value,
        ptype_id,
        sku_id,
        unit_id,
        sale_task_id,
        sort_index,
        limit_type
        from pl_sale_task_condition_new
        where profile_id=#{profileId} and sale_task_id in
        <foreach collection="taskIds" item="taskId" open="(" close=")" separator=",">
            #{taskId}
        </foreach>

    </select>

    <select id="getSaleTaskPtypeInfoForDetail"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskPtypeNew">
        SELECT b.id,b.profile_id,b.ptype_id,b.unit_id as unit_id,b.parent_id as sale_task_id,b.sku_id
        , c.fullname
        , c.usercode
        , c.standard
        , c.ptype_type
        , c.ptype_area
        , c.memo
        , IFNULL(sku.propvalue_names
        , '') AS propValues
        , IFNULL(xcode.xcode
        , '') AS xcode
        , IF (c.propenabled=0
        , pic.pic_url
        , (CASE WHEN sku.pic_url='' THEN pic.pic_url ELSE sku.pic_url END)) AS picUrl
        , unit.unit_name
        , band.brand_name
        , barcode.fullbarcode AS barcode
        ,
        <include refid="com.wsgjp.ct.sale.biz.jarvis.mapper.PtypeCommonMapper.sideShow">
            <property name="ptype" value="c"/>
        </include>
        (case c.weight_unit when 1 then c.weight else c.weight/1000 end) AS weightShow
        , ifnull(sku.prop_names,'') propFormat
        , c.snenabled as snEnabled
        , c.batchenabled
        , IF (c.pcategory=1
        , 1
        , 0) AS pcategory
        , IF (c.pcategory=2
        , 1
        , 0) AS combo
        , c.typeid,
        b.sort_index
        FROM pl_sale_task_new b
        LEFT JOIN base_ptype c ON c.`id`=b.`ptype_id`
        AND c.`profile_id`=b.`profile_id`
        LEFT JOIN base_brandtype band ON band.profile_id=c.`profile_id`
        AND band.id=c.`brand_id`
        LEFT JOIN `base_ptype_sku` sku ON sku.profile_id=b.`profile_id`
        AND sku.id=b.sku_id
        LEFT JOIN base_ptype_xcode xcode ON xcode.profile_id=b.`profile_id`
        AND xcode.ptype_id=b.ptype_id
        AND xcode.sku_id=b.sku_id
        AND xcode.unit_id=b.unit_id
        AND xcode.defaulted=1
        LEFT JOIN base_ptype_fullbarcode barcode ON barcode.profile_id=b.profile_id
        AND barcode.ptype_id=b.ptype_id
        AND barcode.sku_id=b.sku_id
        AND barcode.unit_id=b.unit_id
        AND barcode.defaulted=1
        LEFT JOIN base_ptype_pic pic ON pic.profile_id=b.`profile_id`
        AND pic.ptype_id=b.ptype_id
        AND pic.rowindex=1
        LEFT JOIN base_ptype_unit unit ON unit.profile_id=b.`profile_id`
        AND unit.ptype_id=b.ptype_id
        AND unit.id=b.unit_id
        WHERE b.profile_id=#{profileId} and b.task_group_type=4
        and b.parent_id in(
        <foreach collection="taskIds" item="taskId" separator=",">
            #{taskId}
        </foreach>
        )
        group by b.parent_id,b.sort_index
        order by b.create_time, b.id
    </select>

    <select id="getSaleTaskPtypeInfo"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskPtypeNew">
        SELECT b.id,b.profile_id,b.ptype_id,b.unit_id as unit_id,b.sale_task_id,b.sku_id
        , c.fullname
        , c.usercode
        , c.standard
        , c.ptype_type
        , c.ptype_area
        , c.memo
        , IFNULL(sku.propvalue_names
        , '') AS propValues
        , IFNULL(xcode.xcode
        , '') AS xcode
        , IF (c.propenabled=0
        , pic.pic_url
        , (CASE WHEN sku.pic_url='' THEN pic.pic_url ELSE sku.pic_url END)) AS picUrl
        , unit.unit_name
        , band.brand_name
        , barcode.fullbarcode AS barcode
        ,
        <include refid="com.wsgjp.ct.sale.biz.jarvis.mapper.PtypeCommonMapper.sideShow">
            <property name="ptype" value="c"/>
        </include>
        (case c.weight_unit when 1 then c.weight else c.weight/1000 end) AS weightShow
        , ifnull(sku.prop_names,'') propFormat
        , c.snenabled as snEnabled
        , c.batchenabled
        , IF (c.pcategory=1
        , 1
        , 0) AS pcategory
        , IF (c.pcategory=2
        , 1
        , 0) AS combo
        , c.typeid,
        b.sort_index
        FROM pl_sale_task_condition_new b
        LEFT JOIN base_ptype c ON c.`id`=b.`ptype_id`
        AND c.`profile_id`=b.`profile_id`
        LEFT JOIN base_brandtype band ON band.profile_id=c.`profile_id`
        AND band.id=c.`brand_id`
        LEFT JOIN `base_ptype_sku` sku ON sku.profile_id=b.`profile_id`
        AND sku.id=b.sku_id
        LEFT JOIN base_ptype_xcode xcode ON xcode.profile_id=b.`profile_id`
        AND xcode.ptype_id=b.ptype_id
        AND xcode.sku_id=b.sku_id
        AND xcode.unit_id=b.unit_id
        AND xcode.defaulted=1
        LEFT JOIN base_ptype_fullbarcode barcode ON barcode.profile_id=b.profile_id
        AND barcode.ptype_id=b.ptype_id
        AND barcode.sku_id=b.sku_id
        AND barcode.unit_id=b.unit_id
        AND barcode.defaulted=1
        LEFT JOIN base_ptype_pic pic ON pic.profile_id=b.`profile_id`
        AND pic.ptype_id=b.ptype_id
        AND pic.rowindex=1
        LEFT JOIN base_ptype_unit unit ON unit.profile_id=b.`profile_id`
        AND unit.ptype_id=b.ptype_id
        AND unit.id=b.unit_id
        WHERE b.profile_id=#{profileId} and b.condition_type=1
        and b.sale_task_id in(
        <foreach collection="taskIds" item="taskId" separator=",">
            #{taskId}
        </foreach>
        )
        order by b.create_time, b.id
    </select>

    <select id="getSaleTaskPtypeInfoForBrand"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskPtypeNew">
        select id,profile_id,sale_task_id,condition_value as brand_id,sort_index from pl_sale_task_condition_new where profile_id=#{profileId} and
        condition_type=4 and sale_task_id in(
        <foreach collection="taskIds" item="taskId" separator=",">
            #{taskId}
        </foreach>
        )
    </select>
    <select id="getSaleTaskPtypeInfoForClass"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskPtypeNew">
        select g.id,g.profile_id,sale_task_id,g.sort_index,g.condition_value ptype_class_id,g.condition_value as ptype_id,p.fullname from
        pl_sale_task_condition_new g
        left join base_ptype p on g.condition_value=p.id
        where g.profile_id=#{profileId} and g.condition_type=2 and g.sale_task_id in(
        <foreach collection="taskIds" item="taskId" separator=",">
            #{taskId}
        </foreach>
        )
    </select>
    <select id="getSaleTaskPtypeInfoForLabel"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskPtypeNew">
        select id,profile_id,sale_task_id,sort_index,condition_value as ptype_mark_id from pl_sale_task_condition_new where profile_id=#{profileId}
        and condition_type=3 and sale_task_id in(
        <foreach collection="taskIds" item="taskId" separator=",">
            #{taskId}
        </foreach>
        )
    </select>
    <select id="getExecStatusTopInfo"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskExecStatusTopInfo">

    </select>





    <select id="getSaleTaskPeriodIncomDetailsForBtype"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskPeriodIncomeNew">
        select inc.id,
        inc.profile_id,
        inc.period_id,
        inc.sort_index,
        inc.task_total as total,
        count(bb.id) finishTotal,
        CONCAT_WS('----', CONCAT(left(psp.period_start_time, 10), ' 00:00:00'), CONCAT(left(psp.period_end_time, 10),
        '23:59:59')) as timeRange
        from pl_sale_task_new inc
        left join pl_sale_period_new psp on inc.profile_id = psp.profile_id and inc.period_id = psp.id
        inner join base_btype bb on inc.profile_id=bb.profile_id
        inner join base_btype_bcategory bbb on bb.id=bbb.btype_id and bb.profile_id=bbb.profile_id
        inner join base_btype_extend bbe on bb.id=bbe.btype_id and inc.profile_id=bbe.profile_id
        <if test="param.dtypeList!=null and param.dtypeList.size()>0">
            left join base_etype be on inc.profile_id=be.profile_id and bbe.etype_id=be.id
        </if>
        where inc.profile_id=#{param.profileId} and inc.parent_id=#{param.taskId} and inc.sort_index=#{param.sortIndex} and bb.deleted=0 and bb.stoped=0 and
        bb.classed=0 and bbb.bcategory=0
        and bb.create_time between psp.period_start_time and psp.period_end_time
        <if test="param.etypeList!=null and param.etypeList.size()>0">
            and bbe.etype_id in(
            <foreach collection="param.etypeList" item="etypeId" separator=",">
                #{etypeId}
            </foreach>
            )
        </if>
        <if test="param.dtypeList!=null and param.dtypeList.size()>0">
            and be.dtype_id in(
            <foreach collection="param.dtypeList" item="dtypeId" separator=",">
                #{dtypeId}
            </foreach>
            )
        </if>
        group by inc.sort_index, period_id;
    </select>

    <select id="getSaleTaskPeriodIncomDetailsForReturnedMoney"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskPeriodIncomeNew">
        select inc.id,
        inc.profile_id,
        inc.period_id,
        inc.sort_index,
        inc.task_total total,
        sum(info.currency_bill_total-info.currency_order_preferential_allot_total) finishTotal,
        CONCAT_WS('----', CONCAT(left(psp.period_start_time, 10), ' 00:00:00'), CONCAT(left(psp.period_end_time, 10),
        '23:59:59')) as timeRange
        from pl_sale_task_new inc
        inner join pl_sale_period_new psp on inc.profile_id = psp.profile_id and inc.period_id = psp.id
        inner join acc_bill_core ac on inc.profile_id = ac.profile_id
        inner join acc_bill_assinfo info on inc.profile_id = info.profile_id and ac.vchcode=info.vchcode
        left join base_btype b on b.profile_id = inc.profile_id and b.id = ac.btype_id
        left join pl_eshop pe on ac.otype_id = pe.otype_id and inc.profile_id = pe.profile_id
        where
        <include refid="where"/>
        and ac.vchtype=4001
        <include refid="where2"/>
        group by inc.sort_index, period_id;
    </select>

    <select id="getSaleTaskPeriodIncomDetailsDefault"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskPeriodIncomeNew">
        select n.id,
               n.profile_id,
               n.period_id,
               n.sort_index,
               n.task_total total,
               0                                                                 as finishTotal,
               CONCAT_WS('----', CONCAT(left(psp.period_start_time,10),' 00:00:00'),
                         CONCAT(left(psp.period_end_time,10),'
        23:59:59')) as timeRange,
               psp.period_start_time                                                startTime,
               psp.period_end_time                                                  endTime,
               '本公司' as companyName,
               ifnull(bd.fullname,'') dtypeName,
               ifnull(be.fullname,'') etypeName,
               ifnull(bbd.brand_name,'') brandName,
               ifnull(bb.fullname,'') btypeName,
               ifnull(v.labelfield_value,'') btypeLabelName,
               ifnull(a.fullname,'') btypeAreaName,
               ifnull(bo.fullname,'') otypeName,
               ifnull(bp2.fullname,'') ptypeClassName,
               ifnull(v1.labelfield_value,'') ptypeLabelName,
               (case when n.task_group_type = 4 or n.task_group_type = 5 or n.task_group_type = 6 then concat(ptype_id,sku_id,unit_id) else n.task_group_value end) as task_group_value

        from pl_sale_task_new n
        inner join pl_sale_period_new psp on n.profile_id = psp.profile_id and n.period_id = psp.id
        left join base_dtype bd
                  on n.profile_id = bd.profile_id and n.task_group_value = bd.id and n.task_group_type = 1
        left join base_etype be
                  on n.profile_id = be.profile_id and n.task_group_value = be.id and n.task_group_type = 2
        left join base_brandtype bbd
                  on n.profile_id = bbd.profile_id and n.task_group_value = bbd.id and n.task_group_type = 3
        left join base_ptype bp on n.ptype_id = bp.id and n.profile_id = bp.profile_id and n.task_group_type = 4
        left join base_ptype bp2
                  on n.task_group_value = bp2.id and n.profile_id = bp2.profile_id and n.task_group_type = 5
        left join cf_labelfield_value v1
                  on n.profile_id = v1.profile_id and n.task_group_value = v1.id and n.task_group_type = 6
        left join base_btype bb
                  on n.task_group_value = bb.id and n.profile_id = bb.profile_id and n.task_group_type = 7
        left join cf_labelfield_value v
                  on n.profile_id = v.profile_id and n.task_group_value = v.id and n.task_group_type = 8
        left join base_areatype a
                  on n.profile_id = a.profile_id and n.task_group_value = a.id and n.task_group_type = 9
        left join base_otype bo
                  on n.profile_id = bo.profile_id and n.task_group_value = bo.id and n.task_group_type = 10
        where n.profile_id = #{profileId}
          and n.parent_id = #{taskId}
        order by n.sort_index,psp.period_start_time
    </select>

    <select id="getSaleTaskPeriodIncomDetailsForBtypeTask"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskPeriodIncomeNew">
        select a.id,
        a.profile_id,
        a.sort_index,
        a.total,
        a.period_id,
        a.task_group_value,
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_AMOUNT">
            -sum(a.finishTotal) as finishTotal,
        </if>
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_DISED_TAXED_TOTAL">
            -sum(a.finishTotal) as finishTotal,
        </if>
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_ML">
            -sum(a.finishTotal) as finishTotal,
        </if>
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_SALE_QTY">
            -sum(a.finishTotal) as finishTotal,
        </if>
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_SALE_KIND">
            count(distinct a.ptype_id,a.unit_id) as finishTotal,
        </if>
        CONCAT_WS('----', CONCAT(left(a.period_start_time, 10), ' 00:00:00'), CONCAT(left(a.period_end_time, 10),
        '23:59:59')) as timeRange
        from(
        <include refid="queryForNormalForBtypeTask"/>
        <if test="forBrand==false and !(param.ptypeClassList!=null and param.ptypeClassList.size()>0)">
            union all
            <include refid="querForComboByComboForBtypeTask"/>
            union all
            <include refid="queryForComboByDetailForBtypeTask"/>
        </if>
        ) a
        group by period_id, sort_index,task_group_value
    </select>

    <select id="getSaleTaskPeriodIncomDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskPeriodIncomeNew">
        <include refid="queryConcat"/>
    </select>
    <select id="listSaleTaskNewReport"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.QuerySaleTaskReportResult">
        select n.parent_id as task_id, d.task_name,d.task_clazz,d.task_type,d.task_status, n.sort_index,
        '本公司' as companyName,
        ifnull(bd.fullname,'') dtypeName,
        ifnull(be.fullname,'') etypeName,
        case when n.task_group_type=3 then ifnull(bbd.brand_name,'') else ifnull(band.brand_name,'') end brandName,
        ifnull(bb.fullname,'') btypeName,
        ifnull(v.labelfield_value,'') btypeLabelName,
        ifnull(a.fullname,'') btypeAreaName,
        ifnull(bo.fullname,'') otypeName,
        ifnull(bp2.fullname,'') ptypeClassName,
        ifnull(v1.labelfield_value,'') ptypeLabelName,
        ifnull(bp.fullname,'') ptypeName,
        ifnull(bp.usercode,'') usercode,
        ifnull(bp.standard,'') standard,
        ifnull(bp.ptype_type,'') ptypeType,
        IFNULL(sku.propvalue_names, '') AS propValues,
        IFNULL(xcode.xcode, '') AS xcode
        , unit.unit_name

        , barcode.fullbarcode AS barcode
        , case when n.task_group_type = 4 then ifnull(sku.prop_names,'') else '' end propFormat
        , bp.snenabled as snEnabled
        , bp.batchenabled
        , IF (bp.pcategory=1
        , 1
        , 0) AS pcategory
        , IF (bp.pcategory=2
        , 1
        , 0) AS combo,
        case
        when n.task_group_type = 0 then '本公司'
        when n.task_group_type = 1 then bd.fullname
        when n.task_group_type = 2 then be.fullname
        when n.task_group_type = 3 then bbd.brand_name
        when n.task_group_type = 4 then bp.fullname
        when n.task_group_type = 5 then bp2.fullname
        when n.task_group_type = 6 then v1.labelfield_value
        when n.task_group_type = 7 then bb.fullname
        when n.task_group_type = 8 then v.labelfield_value
        when n.task_group_type = 9 then a.fullname
        when n.task_group_type = 10 then bo.fullname
        else '' end taskGroupValueName
        from pl_sale_task_new d
        join pl_sale_task_new n on d.id=n.parent_id and d.profile_id=n.profile_id
        join pl_sale_period_new g on n.period_id=g.id and d.profile_id=g.profile_id
        left join base_dtype bd
        on n.profile_id = bd.profile_id and n.task_group_value = bd.id and n.task_group_type = 1
        left join base_etype be
        on n.profile_id = be.profile_id and n.task_group_value = be.id and n.task_group_type = 2
        left join base_brandtype bbd
        on n.profile_id = bbd.profile_id and n.task_group_value = bbd.id and n.task_group_type = 3
        left join base_ptype bp on n.ptype_id = bp.id and n.profile_id = bp.profile_id and n.task_group_type = 4
        left join base_ptype bp2
        on n.task_group_value = bp2.id and n.profile_id = bp2.profile_id and n.task_group_type = 5
        left join cf_labelfield_value v1
        on n.profile_id = v1.profile_id and n.task_group_value = v1.id and n.task_group_type = 6
        left join base_btype bb
        on n.task_group_value = bb.id and n.profile_id = bb.profile_id and n.task_group_type = 7
        left join cf_labelfield_value v
        on n.profile_id = v.profile_id and n.task_group_value = v.id and n.task_group_type = 8
        left join base_areatype a
        on n.profile_id = a.profile_id and n.task_group_value = a.id and n.task_group_type = 9
        left join base_otype bo
        on n.profile_id = bo.profile_id and n.task_group_value = bo.id and n.task_group_type = 10

        LEFT JOIN base_brandtype band ON band.profile_id=bp.`profile_id`
        AND band.id=bp.`brand_id`
        LEFT JOIN `base_ptype_sku` sku ON sku.profile_id=n.`profile_id`
        AND sku.id=n.sku_id
        LEFT JOIN base_ptype_xcode xcode ON xcode.profile_id=n.`profile_id`
        AND xcode.ptype_id=n.ptype_id
        AND xcode.sku_id=n.sku_id
        AND xcode.unit_id=n.unit_id
        AND xcode.defaulted=1
        LEFT JOIN base_ptype_fullbarcode barcode ON barcode.profile_id=n.profile_id
        AND barcode.ptype_id=n.ptype_id
        AND barcode.sku_id=n.sku_id
        AND barcode.unit_id=n.unit_id
        AND barcode.defaulted=1
        LEFT JOIN base_ptype_pic pic ON pic.profile_id=n.`profile_id`
        AND pic.ptype_id=n.ptype_id
        AND pic.rowindex=1
        LEFT JOIN base_ptype_unit unit ON unit.profile_id=n.`profile_id`
        AND unit.ptype_id=n.ptype_id
        AND unit.id=n.unit_id
        where d.profile_id = #{param.profileId} and g.period_start_time<![CDATA[ >= ]]>#{param.beginTime} and
        g.period_end_time<![CDATA[ <= ]]>#{param.endTime}
        <if test="param.taskName!=null and param.taskName!=''">
            and d.task_name like CONCAT('%', #{param.taskName},'%')
        </if>
        and d.task_clazz=#{param.taskClazz}
        and d.deleted=0
        <if test="param.powerDtypeIds!=null and param.powerDtypeIds.size()>0 and param.taskClazz==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskNewClazzEnum@COMPANY_SALE_TASK">
            and n.task_group_value in
            <foreach collection="param.powerDtypeIds" item="dtypeId" separator="," open="(" close=")">
                #{dtypeId}
            </foreach>
        </if>
        <if test="param.powerEtypeIds!=null and param.powerEtypeIds.size()>0 and param.taskClazz==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskNewClazzEnum@DEPARTMENT_SALE_TASK">
            and n.task_group_value in
            <foreach collection="param.powerEtypeIds" item="etypeId" separator="," open="(" close=")">
                #{etypeId}
            </foreach>
        </if>
        group by n.parent_id, n.sort_index
        order by n.parent_id,n.sort_index
    </select>
    <select id="getTaskForOpenTask"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskNewDTO">
        <if test="taskIds!=null and taskIds.size()>0">
            select pstn.* from pl_sale_task_new pstn
            join pl_sale_period_group_new pspgn on pstn.period_group_id=pspgn.id and pstn.profile_id=pspgn.profile_id
            where pstn.profile_id=#{profileId} and pstn.deleted=0
            and pstn.id in
            <foreach collection="taskIds" item="taskId" separator="," open="(" close=")">
                #{taskId}
            </foreach>;
        </if>
        <if test="taskIds==null">
            select pstn.* from pl_sale_task_new pstn
            join pl_sale_period_group_new pspgn on pstn.period_group_id=pspgn.id and pstn.profile_id=pspgn.profile_id
            where pstn.profile_id=#{profileId} and pstn.task_status!=3 and pstn.parent_id=0 and pstn.deleted=0
            <if test="timeType==0 and beginTime!=null and endTime!=null">
                and pstn.create_time<![CDATA[ >= ]]>#{beginTime} and pstn.create_time<![CDATA[ <= ]]>#{endTime}
            </if>
            <if test="timeType==1 and beginTime!=null and endTime!=null">
                and pspgn.begin_time <![CDATA[ >= ]]> #{beginTime}
                and pspgn.end_time <![CDATA[ <= ]]> #{endTime}
            </if>;
        </if>
    </select>
    <select id="getPtypeClassChildList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskPtypeNew">
        select p2.id as ptypeId from base_ptype p1
        left join base_ptype p2 on p2.typeid like concat(p1.typeid,'%') and p1.profile_id=p2.profile_id
        where p1.profile_id = #{profileId} and p2.classed=1 and p1.id in
        <foreach collection="ptypeIds" item="ptypeId" separator="," open="(" close=")">
            #{ptypeId}
        </foreach>;
    </select>

    <select id="getDtypeClassChildList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskPtypeNew">
        select p2.id as ptypeId from base_dtype p1
        left join base_dtype p2 on p2.typeid like concat(p1.typeid,'%') and p1.profile_id=p2.profile_id
        where p1.profile_id = #{profileId} and p1.id in
        <foreach collection="dtypeIds" item="dtypeId" separator="," open="(" close=")">
            #{dtypeId}
        </foreach>;
    </select>

    <sql id="queryConcat">
        select a.id,
        a.profile_id,
        a.sort_index,
        a.total,
        a.period_id,
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_CURRENCY_TOTAL">
            -sum(a.currency_total) as finishTotal,
        </if>
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_AMOUNT">
            -sum(a.dised_total) as finishTotal,
        </if>
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_DISED_TAXED_TOTAL">
            -sum(a.dised_taxed_total) as finishTotal,
        </if>
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_ML">
            -sum(a.dised_total-cost_total-order_fee_allot_total) as finishTotal,
        </if>
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_SALE_QTY">
            -sum(a.qty) as finishTotal,
        </if>
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_SALE_KIND">
            count(distinct a.ptype_id,a.unit_id) as finishTotal,
        </if>
        CONCAT_WS('----', CONCAT(left(a.period_start_time, 10), ' 00:00:00'), CONCAT(left(a.period_end_time, 10),
        '23:59:59')) as timeRange
        from(
        <include refid="queryForNormal"/>
        <if test="forBrand==false and !(param.ptypeClassList!=null and param.ptypeClassList.size()>0)">
            union all
            <include refid="querForComboByCombo"/>
            union all
            <include refid="queryForComboByDetail"/>
        </if>
        ) a
        group by period_id, sort_index
    </sql>

    <sql id="queryForNormalForBtypeTask">
        select inc.id,
        inc.profile_id,
        inc.period_id,
        inc.sort_index,
        psp.period_start_time,
        psp.period_end_time,
        inc.task_total total,
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_AMOUNT">
            adc.dised_total finishTotal,
        </if>
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_DISED_TAXED_TOTAL">
            adc.dised_taxed_total finishTotal,
        </if>
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_ML">
            (adc.dised_total-(<include refid="publicCostTotal"/>)-(-1*adc.order_fee_allot_total)) as finishTotal,
        </if>
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_SALE_QTY">
            das.unit_qty as finishTotal,
        </if>
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_SALE_KIND">
            concat(adc.ptype_id,das.unit_id) as finishTotal,
        </if>
        (case
        when inc.task_group_type = 7  then ac.btype_id
        else inc.task_group_value end ) as task_group_value
        from pl_sale_task_new inc
        inner join pl_sale_period_new psp on inc.profile_id = psp.profile_id and inc.period_id = psp.id and now()>=psp.period_end_time
        inner join acc_bill_core ac on inc.profile_id = ac.profile_id
        inner join acc_bill_detail_core_sale adc on inc.profile_id = adc.profile_id and ac.vchcode=adc.vchcode and adc.deleted=0
        inner join acc_bill_detail_assinfo_sale das on adc.vchcode = das.vchcode and adc.detail_id = das.detail_id and
        inc.profile_id=das.profile_id
        left join base_btype b on b.profile_id = inc.profile_id and b.id = adc.btype_id
        left join pl_eshop pe on adc.otype_id = pe.otype_id and inc.profile_id = pe.profile_id
        left join base_ptype p on adc.ptype_id = p.id and inc.profile_id = p.profile_id
        left join acc_periodcost ap ON adc.profile_id = ap.profile_id AND adc.cost_id = ap.id AND adc.ptype_id = ap.ptype_id
        where
        <include refid="publicWhereForBtypeTask"/>
        <include refid="publicPtypeWhere"/>
    </sql>
    <sql id="querForComboByComboForBtypeTask">
        select inc.id,
        inc.profile_id,
        inc.period_id,
        inc.sort_index,
        psp.period_start_time,
        psp.period_end_time,
        inc.task_total total,
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_AMOUNT">
            sum(adc.dised_total) finishTotal,
        </if>
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_DISED_TAXED_TOTAL">
            sum(adc.dised_taxed_total) finishTotal,
        </if>
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_ML">
            (sum(adc.dised_total)-(sum(<include refid="publicCostTotal"/>))-(-1*sum(adc.order_fee_allot_total))) as finishTotal,
        </if>
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_SALE_QTY">
            abdc.qty as finishTotal,
        </if>
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_SALE_KIND">
            concat(abdc.combo_id,0) as finishTotal,
        </if>
        (case

        when inc.task_group_type = 7  then ac.btype_id
        else inc.task_group_value end ) as task_group_value
        from pl_sale_task_new inc
        left join pl_sale_period_new psp on inc.profile_id = psp.profile_id and inc.period_id = psp.id and now()>=psp.period_end_time
        inner join acc_bill_core ac on inc.profile_id = ac.profile_id
        inner join acc_bill_detail_combo abdc on abdc.profile_id = inc.profile_id and ac.vchcode=abdc.vchcode and abdc.deleted=0
        inner join base_ptype_combo bpc on abdc.combo_id = bpc.combo_id and inc.profile_id = bpc.profile_id
        inner join acc_bill_detail_core_sale adc on inc.profile_id = adc.profile_id and abdc.vchcode = adc.vchcode and
        abdc.id = adc.combo_detail_id and adc.deleted=0
        left join base_btype b on b.profile_id = inc.profile_id and b.id = adc.btype_id
        left join pl_eshop pe on adc.otype_id = pe.otype_id and inc.profile_id = pe.profile_id
        left join base_ptype p on adc.ptype_id = p.id and inc.profile_id = p.profile_id
        left join acc_periodcost ap ON adc.profile_id = ap.profile_id AND adc.cost_id = ap.id AND adc.ptype_id = ap.ptype_id
        where
        <include refid="publicWhereForBtypeTask"/>
        <include refid="publicComboWhere"/>
        and bpc.single_ptype = 0
        group by inc.profile_id,
        inc.period_id,
        inc.sort_index, abdc.id
    </sql>
    <sql id="queryForComboByDetailForBtypeTask">
        select inc.id,
        inc.profile_id,
        inc.period_id,
        inc.sort_index,
        psp.period_start_time,
        psp.period_end_time,
        inc.task_total total,
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_AMOUNT">
            adc.dised_total finishTotal,
        </if>
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_DISED_TAXED_TOTAL">
            adc.dised_taxed_total finishTotal,
        </if>
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_ML">
            (adc.dised_total-(<include refid="publicCostTotal"/>)-(-1*adc.order_fee_allot_total)) as finishTotal,
        </if>
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_SALE_QTY">
            das.unit_qty as finishTotal,
        </if>
        <if test="param.taskType==@com.wsgjp.ct.sale.biz.eshoporder.entity.saleTask.saleTaskManagement.SaleTaskTypeNewEnum@BY_SALE_KIND">
            concat(abdc.combo_id,0) as finishTotal,
        </if>
        (case

        when inc.task_group_type = 7  then ac.btype_id
        else inc.task_group_value end ) as task_group_value
        from pl_sale_task_new inc
        left join pl_sale_period_new psp on inc.profile_id = psp.profile_id and inc.period_id = psp.id and now()>=psp.period_end_time
        inner join acc_bill_core ac on inc.profile_id = ac.profile_id
        inner join acc_bill_detail_combo abdc on abdc.profile_id = inc.profile_id and ac.vchcode=abdc.vchcode and abdc.deleted=0
        inner join base_ptype_combo bpc on abdc.combo_id = bpc.combo_id and inc.profile_id = bpc.profile_id
        inner join acc_bill_detail_core_sale adc on inc.profile_id = adc.profile_id and adc.combo_detail_id = abdc.id
        and ac.vchcode=adc.vchcode and adc.deleted=0
        inner join acc_bill_detail_assinfo_sale das on adc.vchcode = das.vchcode and adc.detail_id = das.detail_id and
        inc.profile_id=das.profile_id
        left join base_btype b on b.profile_id = inc.profile_id and b.id = adc.btype_id
        left join pl_eshop pe on adc.otype_id = pe.otype_id and inc.profile_id = pe.profile_id
        left join base_ptype p on adc.ptype_id = p.id and inc.profile_id = p.profile_id
        left join acc_periodcost ap ON adc.profile_id = ap.profile_id AND adc.cost_id = ap.id AND adc.ptype_id = ap.ptype_id
        where
        <include refid="publicWhereForBtypeTask"/>
        <include refid="publicComboWhere"/>
        and bpc.single_ptype = 1
    </sql>


    <sql id="queryForNormal">
        select inc.id,
        inc.profile_id,
        inc.period_id,
        inc.sort_index,
        psp.period_start_time,
        psp.period_end_time,
        inc.task_total total,
        adc.dised_total,
        adc.dised_taxed_total,
        -1*adc.order_fee_allot_total as order_fee_allot_total,
        das.unit_qty as qty,
        adc.ptype_id,
        das.unit_id,
        <include refid="publicCostTotal"></include>
        as cost_total,
        das.currency_total
        from pl_sale_task_new inc
        inner join pl_sale_period_new psp on inc.profile_id = psp.profile_id and inc.period_id = psp.id
        inner join acc_bill_core ac on inc.profile_id = ac.profile_id
        inner join acc_bill_detail_core_sale adc on inc.profile_id = adc.profile_id and ac.vchcode=adc.vchcode and adc.deleted=0
        inner join acc_bill_detail_assinfo_sale das on adc.vchcode = das.vchcode and adc.detail_id = das.detail_id and
        inc.profile_id=das.profile_id
        left join base_btype b on b.profile_id = inc.profile_id and b.id = adc.btype_id
        left join pl_eshop pe on adc.otype_id = pe.otype_id and inc.profile_id = pe.profile_id
        left join base_ptype p on adc.ptype_id = p.id and inc.profile_id = p.profile_id
        left join acc_periodcost ap ON adc.profile_id = ap.profile_id AND adc.cost_id = ap.id AND adc.ptype_id = ap.ptype_id
        where
        <include refid="publicWhere"/>
        <include refid="publicPtypeWhere"/>
    </sql>
    <sql id="querForComboByCombo">
        select inc.id,
        inc.profile_id,
        inc.period_id,
        inc.sort_index,
        psp.period_start_time,
        psp.period_end_time,
        inc.task_total total,
        sum(adc.dised_total) as dised_total,
        sum(adc.dised_taxed_total) as dised_taxed_total,
        -1*sum(adc.order_fee_allot_total) as order_fee_allot_total,
        abdc.qty,
        abdc.combo_id as ptype_id,
        0 as unit_id,
        sum(<include refid="publicCostTotal"></include>) cost_total,
        sum(das.currency_total) as currency_total
        from pl_sale_task_new inc
        left join pl_sale_period_new psp on inc.profile_id = psp.profile_id and inc.period_id = psp.id
        inner join acc_bill_core ac on inc.profile_id = ac.profile_id
        inner join acc_bill_detail_combo abdc on abdc.profile_id = inc.profile_id and ac.vchcode=abdc.vchcode and abdc.deleted=0
        inner join base_ptype_combo bpc on abdc.combo_id = bpc.combo_id and inc.profile_id = bpc.profile_id
        inner join acc_bill_detail_core_sale adc on inc.profile_id = adc.profile_id and abdc.vchcode = adc.vchcode and
        abdc.id = adc.combo_detail_id and adc.deleted=0
        inner join acc_bill_detail_assinfo_sale das on adc.vchcode = das.vchcode and adc.detail_id = das.detail_id and
        inc.profile_id=das.profile_id
        left join base_btype b on b.profile_id = inc.profile_id and b.id = adc.btype_id
        left join pl_eshop pe on adc.otype_id = pe.otype_id and inc.profile_id = pe.profile_id
        left join base_ptype p on adc.ptype_id = p.id and inc.profile_id = p.profile_id
        left join acc_periodcost ap ON adc.profile_id = ap.profile_id AND adc.cost_id = ap.id AND adc.ptype_id = ap.ptype_id
        where
        <include refid="publicWhere"/>
        <include refid="publicComboWhere"/>
        and bpc.single_ptype = 0
        group by inc.profile_id,
        inc.period_id,
        inc.sort_index, abdc.id
    </sql>
    <sql id="queryForComboByDetail">
        select inc.id,
        inc.profile_id,
        inc.period_id,
        inc.sort_index,
        psp.period_start_time,
        psp.period_end_time,
        inc.task_total total,
        adc.dised_total,
        adc.dised_taxed_total,
        -1*adc.order_fee_allot_total as order_fee_allot_total,
        das.unit_qty as qty,
        abdc.combo_id as ptype_id,
        0 as unit_id,
        <include refid="publicCostTotal"></include>
        as cost_total,
        das.currency_total
        from pl_sale_task_new inc
        left join pl_sale_period_new psp on inc.profile_id = psp.profile_id and inc.period_id = psp.id
        inner join acc_bill_core ac on inc.profile_id = ac.profile_id
        inner join acc_bill_detail_combo abdc on abdc.profile_id = inc.profile_id and ac.vchcode=abdc.vchcode and abdc.deleted=0
        inner join base_ptype_combo bpc on abdc.combo_id = bpc.combo_id and inc.profile_id = bpc.profile_id
        inner join acc_bill_detail_core_sale adc on inc.profile_id = adc.profile_id and adc.combo_detail_id = abdc.id
        and ac.vchcode=adc.vchcode and adc.deleted=0
        inner join acc_bill_detail_assinfo_sale das on adc.vchcode = das.vchcode and adc.detail_id = das.detail_id and
        inc.profile_id=das.profile_id
        left join base_btype b on b.profile_id = inc.profile_id and b.id = adc.btype_id
        left join pl_eshop pe on adc.otype_id = pe.otype_id and inc.profile_id = pe.profile_id
        left join base_ptype p on adc.ptype_id = p.id and inc.profile_id = p.profile_id
        left join acc_periodcost ap ON adc.profile_id = ap.profile_id AND adc.cost_id = ap.id AND adc.ptype_id = ap.ptype_id
        where
        <include refid="publicWhere"/>
        <include refid="publicComboWhere"/>
        and bpc.single_ptype = 1
    </sql>
    <sql id="publicCostTotal">
        (
        <include refid="com.wsgjp.ct.bill.core.handle.mapper.billdetailcosttotalcolumn">
            <property name="bill_name" value="adc"/>
            <property name="ptype_name" value="p"/>
            <property name="cost_name" value="ap"/>
        </include>
        )
    </sql>
    <sql id="publicWhereForBtypeTask">
        <include refid="whereForBtypeTask"/>
        and ac.vchtype in (2000,2001,2002,2100,2101,2102,2200)
        <include refid="where2"/>
    </sql>
    <sql id="publicWhere">
        <include refid="where"/>
        and ac.vchtype in (2000,2001,2002,2100,2101,2102,2200)
        <include refid="where2"/>
    </sql>

    <sql id="publicPtypeWhere">
        <if test="forBrand==false">
            and adc.combo_detail_id = 0
        </if>
        <if test="param.ptypeList!=null and param.ptypeList.size()>0">
            and (adc.ptype_id,adc.sku_id,das.unit_id) in(
            <foreach collection="param.ptypeList" item="item" separator=",">
                (#{item.ptypeId},#{item.skuId},#{item.unitId})
            </foreach>
            )
        </if>
        <if test="(param.brandList!=null and param.brandList.size()>0)">
            and p.brand_id in(
            <foreach collection="param.brandList" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="(param.ptypeClassList!=null and param.ptypeClassList.size()>0)">
            and exists(select 1 from base_ptype bp where bp.profile_id=inc.profile_id and p.partypeid = bp.typeid and bp.id in(
            <foreach collection="param.ptypeClassList" item="item" separator=",">
                #{item.ptypeId}
            </foreach>
            ))
        </if>
        <if test="param.ptypeLabelList!=null and param.ptypeLabelList.size()>0">
            and exists(select 1 from cf_data_label_ptype bl where bl.profile_id=inc.profile_id and
            bl.resource_id=adc.ptype_id and bl.labelfield_value_id in(
            <foreach collection="param.ptypeLabelList" item="item" separator=",">
                #{item}
            </foreach>
            ))
        </if>
    </sql>
    <sql id="publicComboWhere">
        and adc.combo_detail_id > 0
        <if test="param.ptypeList!=null and param.ptypeList.size()>0">
            and abdc.combo_id in(
            <foreach collection="param.ptypeList" item="item" separator=",">
                #{item.ptypeId}
            </foreach>
            )
        </if>
        <if test="param.ptypeLabelList!=null and param.ptypeLabelList.size()>0">
            and exists(select 1 from cf_data_label_ptype bl where bl.profile_id=inc.profile_id and
            bl.resource_id=abdc.combo_id and bl.labelfield_value_id in(
            <foreach collection="param.ptypeLabelList" item="item" separator=",">
                #{item}
            </foreach>
            ))
        </if>
        <if test="(param.ptypeClassList!=null and param.ptypeClassList.size()>0)">
            and exists(select 1 from base_ptype bp where bp.profile_id=inc.profile_id and p.partypeid = bp.typeid and bp.id in(
            <foreach collection="param.ptypeClassList" item="item" separator=",">
                #{item.ptypeId}
            </foreach>
            ))
        </if>
        <if test="(param.brandList!=null and param.brandList.size()>0)">
            and p.brand_id in(
            <foreach collection="param.brandList" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
    </sql>

    <sql id="whereForBtypeTask">
        inc.profile_id=#{param.profileId}
        and inc.parent_id=#{param.taskId}
        and inc.sort_index in(
        <foreach collection="param.sortIndexs" item="item" separator=",">
            #{item}
        </foreach>
        )
        and ac.bill_date between psp.period_start_time and psp.period_end_time
        and ac.redword= 0  and ac.redbill_state=0
        and ac.post_state= 800
    </sql>

    <sql id="where">
        inc.profile_id=#{param.profileId}
        and inc.parent_id=#{param.taskId}
        and inc.sort_index=#{param.sortIndex}
        and ac.bill_date between psp.period_start_time and psp.period_end_time
        and ac.redword= 0  and ac.redbill_state=0
        and ac.post_state= 800
    </sql>
    <sql id="where2">
        <if test="param.btypeLabelList!=null and param.btypeLabelList.size()>0">
            and exists(select 1 from cf_data_label_btype bl where bl.profile_id=inc.profile_id and
            bl.resource_id=ac.btype_id and bl.labelfield_value_id in(
            <foreach collection="param.btypeLabelList" item="item" separator=",">
                #{item}
            </foreach>
            )
        )
        </if>
        <if test="param.btypeList!=null and param.btypeList.size()>0">
            and ac.btype_id in (
            <foreach collection="param.btypeList" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="param.btypeAreaList!=null and param.btypeAreaList.size()>0">
            and b.areatype_id in (
            <foreach collection="param.btypeAreaList" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="param.dtypeList!=null and param.dtypeList.size()>0">
            and ac.dtype_id in (
            <foreach collection="param.dtypeList" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="param.etypeList!=null and param.etypeList.size()>0">
            and ac.etype_id in (
            <foreach collection="param.etypeList" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
<!--        <if test="param.ktypeId!=null and param.ktypeId!=0">-->
<!--            and ac.ktype_id=#{param.ktypeId}-->
<!--        </if>-->
        <if test="param.otypeList!=null and param.otypeList.size()>0">
            and ac.otype_id in (
            <foreach collection="param.otypeList" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        <if test="param.shopTypeList!=null and param.shopTypeList.size()>0">
            and pe.eshop_type in (
            <foreach collection="param.shopTypeList" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
    </sql>
</mapper>
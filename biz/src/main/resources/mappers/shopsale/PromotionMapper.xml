<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.shopsale.mapper.PromotionMapper">
    <!--促销开始-->
    <select id="getPromotionList" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.promotion.PromotionInfo">
        select sp.id,sp.etype_id ,sp.promotion_type ,sp.start_date ,sp.end_date ,sp.start_time
        ,sp.end_time,sp.priority
        ,sp.rang_type ,sp.profile_id ,sp.created_time ,sp.update_time ,sp.deleted ,sp.stoped
        ,sp.fullname,sp.cal_integral,sp.join_order,sp.vip_rights,sp.use_range
        ,be.fullname as etype_name ,group_concat(distinct(otype.fullname)) as
        filterNames,group_concat(distinct(spo.filter_id)) as filterIds
        from ss_promotion sp
        left join ss_promotion_filter as spo on sp.id=spo.promotion_id and spo.profile_id = #{param.profileId} and spo.deleted = 0
        <!--        left join ss_promotion_rang_value as sprv on sp.id=sprv.promotion_id and sprv.profile_id = #{param.profileId}-->
        left join ss_strategy ss on sp.id =ss.promotion_id and ss.strategy_type =0
        left join base_otype otype on spo.filter_id=otype.id and otype.deleted=0
        <!--        left join base_btype btype on sprv.rang_id=btype.id and btype.deleted=0-->
        <!--        left join cf_labelfield_value clv on clv.id = sprv.rang_id and clv.profile_id = spo.profile_id-->
        left join base_etype be on be.id=sp.etype_id
        left join ss_promotion_ptype spp on spp.promotion_id = sp.id and spp.deleted =0
        and spp.profile_id =#{param.profileId}

        <where>
            sp.deleted=0 and sp.profile_id=#{param.profileId}
            and
            ss.profile_id=#{param.profileId}
            <if test="param.fullname != null and param.fullname != ''">
                AND sp.fullname like CONCAT('%',#{param.fullname},'%')
            </if>
            <if test="param.rangType != null and param.rangType != 0">
                <!-- 同时查询指定rangType和全部促销(rangType=1) -->
                and (sp.rang_type = #{param.rangType} OR sp.rang_type = 1)
            </if>
            <if test="param.stoped > -1 and param.stoped != null">
                and sp.stoped = #{param.stoped}
            </if>
            <if test="param.startDate != null">
                and sp.start_date &gt;= #{param.startDate} and sp.start_date &lt;= #{param.endDate}
            </if>
            <if test="param.state == 0">
                and sp.start_date &gt; CURDATE()
            </if>
            <if test="param.state == 1">
                and sp.start_date &lt;= CURDATE() and sp.end_date &gt;= CURDATE()
                and sp.start_time &lt;= CURTIME() and sp.end_time &gt;= CURTIME()
            </if>
            <if test="param.state == 2">
                and sp.end_date &lt;= CURDATE()
            </if>
            <if test="param.state == 3">
                and sp.start_date &lt;= CURDATE() and sp.end_date &gt;= CURDATE()
                and (sp.start_time &gt;= CURTIME() or sp.end_time &lt;= CURTIME() )
            </if>

            <!--            给优先级列表使用-->
            <if test="param.state == 4">
                and (sp.start_date &gt; CURDATE() or( sp.start_date &lt;= CURDATE() and sp.end_date &gt;= CURDATE()
                and sp.start_time &lt;= CURTIME() and sp.end_time &gt;= CURTIME())or(sp.start_date &lt;= CURDATE() and
                sp.end_date &gt;= CURDATE()
                and (sp.start_time &gt;= CURTIME() or sp.end_time &lt;= CURTIME() )))
            </if>

            <if test="param.filterId != null">
                and (spo.filter_id = #{param.filterId} OR NOT EXISTS (
                SELECT 1 FROM ss_promotion_filter spo2
                WHERE spo2.promotion_id = sp.id
                AND spo2.profile_id = #{param.profileId}
                AND spo2.deleted = 0
                ))
            </if>

            <!-- 云订货根据otypeId筛选促销 -->
            <if test="param.otypeId != null">
                and (spo.filter_id = #{param.otypeId} and spo.filter_type = 0 OR NOT EXISTS (
                SELECT 1 FROM ss_promotion_filter spo3
                WHERE spo3.promotion_id = sp.id
                AND spo3.profile_id = #{param.profileId}
                AND spo3.deleted = 0
                AND spo3.filter_type = 0
                ))
            </if>


            <if test="param.useRange > -1 and param.useRange != null">
                and sp.use_range = #{param.useRange}
            </if>

            <if test="param.filterIds != null and param.filterIds.size() != 0">
                and (sp.id in (
                select promotion_id from ss_promotion_filter spo1
                <where>
                    spo1.deleted = 0 and spo1.profile_id = #{param.profileId} and spo1.filter_id in
                    <foreach collection="param.filterIds" separator="," item="filterId" open="(" close=")">
                        #{filterId}
                    </foreach>
                </where>
                group by promotion_id
                ) OR NOT EXISTS (
                SELECT 1 FROM ss_promotion_filter spo2
                WHERE spo2.promotion_id = sp.id
                AND spo2.profile_id = #{param.profileId}
                AND spo2.deleted = 0
                ))
            </if>

            <if test="null != param.promotionType and param.promotionType &gt; -1">
                and sp.promotion_type=#{param.promotionType}
            </if>

            <if test="null != param.promotionTypes and param.promotionTypes.size() != 0">
                and sp.promotion_type in
                <foreach collection="param.promotionTypes" separator="," item="promotionType" open="(" close=")">
                    #{promotionType}
                </foreach>
            </if>

            <if test="param.ptypeIds != null and param.ptypeIds.size() != 0">
                and spp.ptype_type = 0 and ((ss.rule like '%1%' and spp.deleted =0 and (spp.pid in
                <foreach collection="param.ptypeIds" separator="," item="ptypeId" open="(" close=")">
                    #{ptypeId}
                </foreach>
                or spp.sku_id in
                <foreach collection="param.ptypeIds" separator="," item="ptypeId" open="(" close=")">
                    #{ptypeId}
                </foreach>)) or
                ( ss.rule like '%3%' and sp.id not in (select spp1.promotion_id from ss_promotion_ptype spp1
                left join ss_promotion sp2 on sp2.id = spp1.promotion_id
                LEFT JOIN ss_strategy ss1
                ON spp1.promotion_id =ss1.promotion_id and ss1.strategy_type =0
                where spp1.deleted =0 AND (spp1.pid in
                <foreach collection="param.ptypeIds" separator="," item="ptypeId" open="(" close=")">
                    #{ptypeId}
                </foreach>
                or spp1.sku_id in
                <foreach collection="param.ptypeIds" separator="," item="ptypeId" open="(" close=")">
                    #{ptypeId}
                </foreach>
                or ((select count(0) from base_ptype_combo bpc where bpc.combo_id in
                <foreach collection="param.ptypeIds" separator="," item="ptypeId" open="(" close=")">
                    #{ptypeId}
                </foreach>
                and profile_id =#{param.profileId}) >0 and sp2.promotion_type=2 and sp2.profile_id=#{param.profileId}) )
                and ss1.profile_id=#{param.profileId} and spp1.profile_id=#{param.profileId} and ss.rule like '%3%'
                group by spp.promotion_id ))
                or ss.rule like '%0%')
            </if>

            <!-- 根据往来单位ID过滤促销 -->
            <if test="param.btypeId != null and param.btypeTypeIds != null">
                and (
                    sp.id in (
                        SELECT sprv.promotion_id
                        FROM (
                            SELECT #{param.btypeId} AS filter_id
                            UNION ALL (SELECT labelfield_value_id AS filter_id FROM cf_data_label_btype WHERE profile_id = #{param.profileId} AND resource_id = #{param.btypeId})
                            <if test="param.btypeTypeIds.size() > 0">
                                UNION ALL SELECT id AS filter_id FROM base_btype WHERE profile_id = #{param.profileId} AND classed = 1 AND typeid IN
                                <foreach collection="param.btypeTypeIds" separator="," open="(" close=")" item="item">
                                    #{item}
                                </foreach>
                            </if>
                        ) AS temp
                        INNER JOIN ss_promotion_rang_value sprv ON sprv.profile_id = #{param.profileId} AND sprv.rang_id = temp.filter_id
                        WHERE sprv.deleted = 0
                    )
                    OR NOT EXISTS (
                        SELECT 1 FROM ss_promotion_rang_value sprv2
                        WHERE sprv2.promotion_id = sp.id
                        AND sprv2.profile_id = #{param.profileId}
                        AND sprv2.deleted = 0
                    )
                )
            </if>
        </where>
        group by sp.id
        <if test="param.state == 4">
            order by priority desc
        </if>
        <if test="param.state != 4">
            order by created_time desc
        </if>
    </select>


    <select id="checkPromotionIn" resultType="boolean">
        SELECT count(1) > 0
        from ss_promotion sp
        <where>
            sp.deleted =0 and sp.fullname =#{fullname} and sp.id != #{id} and profile_id=${profileId}
        </where>
    </select>

    <select id="getExistPromotionPtype"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.promotion.PromotionStrategyPtype">
        select spp.price , spp.id, spp.promotion_id,ss.rule ,spp.ptype_id,spp.pid,spp.sku_id
        ,spp.unit_id,spp.preferential,sp.fullname as
        promotionName,STR_TO_DATE(sp.start_time , '%T') ,STR_TO_DATE(sp.end_time , '%T') ,DATE_FORMAT(sp.start_date
        ,'%Y-%m-%d'),DATE_FORMAT(sp.end_date ,'%Y-%m-%d'),sp.promotion_type,bp.fullname as ptypeName ,bpu.unit_name
        ,bps.propvalue_names as propertyName
        FROM ss_promotion_ptype spp
        LEFT JOIN ss_promotion sp
        ON sp.id = spp.promotion_id
        LEFT JOIN ss_strategy ss
        ON ss.promotion_id = spp.promotion_id
        LEFT JOIN ss_promotion_filter spo
        ON spp.promotion_id = spo.promotion_id
        left join base_ptype bp on bp.id = spp.pid
        LEFT JOIN base_ptype_sku bps
        ON bps.id = spp.sku_id and bps.profile_id =#{params.profileId}
        LEFT JOIN base_ptype_unit bpu
        ON bpu.id = spp.unit_id and bpu.profile_id =#{params.profileId}
        <where>
            sp.end_date &gt;= #{params.promotionInfo.startDate} and sp.start_date &lt;= #{params.promotionInfo.endDate}
            and spp.ptype_type=0 and sp.end_date &gt;= CURDATE() and bp.profile_id =#{params.profileId} and
            bp.deleted =0 and spp.deleted=0
            and sp.deleted =0 and ss.strategy_type =0 and ss.rule like '%1%' and spp.profile_id=#{params.profileId} and
            spp.promotion_id !=
            #{params.promotionInfo.id}
            <if test="null != params.promotionInfo.filterIds  and params.promotionInfo.filterIds.size() > 0">
                and spo.filter_id in
                <foreach item="filterId" collection="params.promotionInfo.filterIds" open="(" separator="," close=")">
                    ${filterId}
                </foreach>
            </if>

            <if test="null != params.promotionTypes  and params.promotionTypes.size() > 0">
                and sp.promotion_type in
                <foreach item="promotionType" collection="params.promotionTypes" open="(" separator="," close=")">
                    ${promotionType}
                </foreach>
            </if>
            <if test="null != requestUnitIds  and requestUnitIds.size() > 0">
                and concat(spp.pid,"_",spp.sku_id,"_",spp.unit_id) in
                <foreach item="unitSkuId" collection="requestUnitIds" open="(" separator="," close=")">
                    '${unitSkuId}'
                </foreach>
            </if>
            group by spp.id
        </where>
    </select>

    <!--促销中获取重复的优惠券信息-->
    <select id="getExistPromotionCard"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.promotion.PromotionStrategyPtype">
        select spp.price, spp.id, spp.promotion_id,spp.ptype_id,spp.pid,spp.sku_id
        ,spp.unit_id,spp.preferential,sp.fullname
        as
        promotionName,STR_TO_DATE(sp.start_time , '%T') ,STR_TO_DATE(sp.end_time , '%T') ,DATE_FORMAT(sp.start_date
        ,'%Y-%m-%d'),DATE_FORMAT(sp.end_date ,'%Y-%m-%d'),sp.promotion_type,sct.fullname as ptypeName
        FROM ss_promotion_ptype spp
        LEFT JOIN ss_promotion sp
        ON sp.id = spp.promotion_id
        LEFT JOIN ss_promotion_filter spo
        ON spp.promotion_id = spo.promotion_id
        left join ss_card_template sct on sct.id = spp.pid
        <where>
            sp.end_date &gt;= #{params.promotionInfo.startDate} and sp.start_date &lt;= #{params.promotionInfo.endDate}
            and spp.ptype_type=0 and sp.end_date &gt;= CURDATE() and spp.deleted=0 and sct.deleted =0 and
            sct.profile_id=#{params.profileId}
            and sp.deleted =0 and spp.profile_id=#{params.profileId} and
            spp.promotion_id != #{params.promotionInfo.id} and sp.promotion_type
            =#{params.promotionInfo.promotionType} and (spp.ptype_group=6 or spp.ptype_group=5)
            <if test="null != params.promotionInfo.filterIds  and params.promotionInfo.filterIds.size() > 0">
                and spo.filter_id in
                <foreach item="filterId" collection="params.promotionInfo.filterIds" open="(" separator="," close=")">
                    ${filterId}
                </foreach>
            </if>
            <if test="null != requestUnitIds  and requestUnitIds.size() > 0">
                and spp.pid in
                <foreach item="unitSkuId" collection="requestUnitIds" open="(" separator="," close=")">
                    '${unitSkuId}'
                </foreach>
            </if>
            group by spp.id
        </where>
    </select>

    <select id="getExistPromotionRecharge"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.promotion.PromotionStrategyPtype">
        select spp.price, spp.id, spp.promotion_id,spp.ptype_id,spp.pid,spp.sku_id
        ,spp.unit_id,spp.preferential,sp.fullname as
        promotionName,STR_TO_DATE(sp.start_time , '%T') ,STR_TO_DATE(sp.end_time , '%T') ,DATE_FORMAT(sp.start_date
        ,'%Y-%m-%d'),DATE_FORMAT(sp.end_date ,'%Y-%m-%d'),sp.promotion_type
        FROM ss_promotion_ptype spp
        LEFT JOIN ss_promotion sp
        ON sp.id = spp.promotion_id
        LEFT JOIN ss_promotion_filter spo
        ON spp.promotion_id = spo.promotion_id
        <where>
            sp.end_date &gt;= #{params.promotionInfo.startDate} and sp.start_date &lt;= #{params.promotionInfo.endDate}
            and spp.ptype_type=0 and sp.end_date &gt;= CURDATE() and spp.deleted=0
            and sp.deleted =0 and spp.profile_id=#{params.profileId} and
            spp.promotion_id != #{params.promotionInfo.id} and sp.promotion_type
            =#{params.promotionInfo.promotionType} and spp.ptype_group=7
            <if test="null != params.promotionInfo.filterIds  and params.promotionInfo.filterIds.size() > 0">
                and spo.filter_id in
                <foreach item="filterId" collection="params.promotionInfo.filterIds" open="(" separator="," close=")">
                    ${filterId}
                </foreach>
            </if>
            <if test="null != requestUnitIds  and requestUnitIds.size() > 0">
                and spp.price in
                <foreach item="unitSkuId" collection="requestUnitIds" open="(" separator="," close=")">
                    '${unitSkuId}'
                </foreach>
            </if>
            group by spp.id
        </where>
    </select>


    <select id="getValidPromotionId" resultType="java.math.BigInteger">
        select sp.id as promotion_id
        from ss_promotion sp
        <where>
            current_time between STR_TO_DATE(sp.start_time , '%T') and STR_TO_DATE(sp.end_time , '%T')
            and current_date between DATE_FORMAT(sp.start_date ,'%Y-%m-%d') and DATE_FORMAT(sp.end_date ,'%Y-%m-%d')
            and sp.deleted =0 and sp.stoped =0 and sp.profile_id=#{profileId} and sp.id in
            <foreach item="promotionId" collection="promotionIds" open="(" separator="," close=")">
                ${promotionId}
            </foreach>
        </where>
    </select>

    <select id="getPromotionInfo" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.promotion.PromotionInfo">
        select sp.id,sp.etype_id ,sp.promotion_type ,sp.start_date ,sp.end_date ,sp.start_time ,sp.end_time
        ,sp.rang_type ,sp.profile_id ,sp.created_time ,sp.update_time ,sp.deleted ,sp.stoped ,sp.fullname
        ,be.fullname as etypeName,sp.cal_integral,sp.join_order,sp.vip_rights,sp.priority,sp.rang_vaule as
        rangValue,sp.use_range,sp.set_style
        from ss_promotion sp
        left join base_etype be on be.id=sp.etype_id
        <where>
            sp.deleted=0 and sp.profile_id=#{profileId}
            <if test="null != ids and ids.size() > 0">
                and sp.id in
                <foreach collection="ids" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <insert id="insertPromotion">
        INSERT INTO ss_promotion
        (id, etype_id, promotion_type, start_date, end_date, start_time, end_time, rang_type, profile_id, fullname,
         cal_integral, join_order, vip_rights, priority, use_range, set_style)
        VALUES (#{id}, #{etypeId}, #{promotionType}, #{startDate}, #{endDate}, #{startTime}, #{endTime}, #{rangType},
                #{profileId},
                #{fullname}, #{calIntegral}, #{joinOrder}, #{vipRights}, #{priority}, #{useRange},
                #{setStyle})
    </insert>

    <update id="updatePromotion">
        UPDATE ss_promotion
        SET etype_id=#{etypeId}, start_date=#{startDate}, end_date=#{endDate},rang_type=#{rangType},
        start_time= #{startTime}, end_time=#{endTime}, fullname=#{fullname},cal_integral=#{calIntegral},
        join_order= #{joinOrder},
        vip_rights = #{vipRights},priority = #{priority},use_range = #{useRange},set_style =
        #{setStyle}
        <where>
            profile_id =#{profileId} and id =#{id}
        </where>
    </update>

    <delete id="deletePromotion">
        UPDATE ss_promotion
        SET deleted=1
        <where>
            id = #{id} and profile_id=#{profileId}
        </where>
    </delete>

    <update id="stopPromotion">
        UPDATE ss_promotion
        SET stoped = #{stoped}
        WHERE id = #{id}
          and profile_id = #{profileId}
    </update>
    <!--促销结束-->

    <!--促销策略开始-->

    <insert id="insertStrategy">
        insert into ss_strategy (id, promotion_id, rule, profile_id, strategy_type)
            value (#{id}, #{promotionId}, #{rule}, #{profileId}, #{strategyType})
    </insert>

    <insert id="insertStrategies">
        insert into ss_strategy (id,promotion_id,rule,profile_id,strategy_type)
        VALUES
        <foreach collection="strategyList" separator="," item="item">
            (#{item.id},#{item.promotionId},#{item.rule},#{item.profileId},#{item.strategyType})
        </foreach>
    </insert>

    <update id="updateStrategy">
        update ss_strategy set rule=#{rule}
        <where>
            id =#{item.id} and profile_id=#{profileId}
        </where>
    </update>

    <update id="updateStrategys">
        <foreach collection="strategyList" separator=";" item="item">
            update ss_strategy set rule=#{item.rule}
            where id =#{item.id} and profile_id=#{item.profileId}
        </foreach>
    </update>

    <select id="selectStrategy" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.promotion.PromotionStrategy">
        SELECT id, promotion_id, rule, strategy_type, create_time, update_time, profile_id, row_index, deleted
        from ss_strategy ss
        <where>
            profile_id=#{profileId}
            <if test="null != promotionIds and promotionIds.size() > 0">
                and ss.promotion_id in
                <foreach collection="promotionIds" item="promotionId" separator="," open="(" close=")">
                    #{promotionId}
                </foreach>
            </if>
        </where>
    </select>

    <!--促销策略结束-->

    <!--促销策略销售机构开始-->
    <insert id="insertPromotionOtype">
        insert into ss_promotion_filter (id, promotion_id, filter_id, profile_id)
            value (#{id}, #{promotionId}, #{otypeId}, #{profileId})
    </insert>
    <insert id="insertPromotionOtypes">
        INSERT INTO ss_promotion_filter (id,promotion_id,filter_id,profile_id,filter_type)
        VALUES
        <foreach collection="otypeList" separator="," item="item">
            (#{item.id},
            #{item.promotionId},
            #{item.filterId},
            #{item.profileId},
            #{item.filterType})
        </foreach>
    </insert>

    <delete id="deletePromotionOtype">
        delete from ss_promotion_filter
        <where>
            profile_id=#{profileId} and promotion_id=#{promotionId}
        </where>
    </delete>
    <select id="selectPromotionOtype"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.promotion.PromotionFilterType">
        select spo.id,spo.filter_id ,spo.promotion_id ,spo.create_time ,spo.update_time ,spo.profile_id, Case
        sp.use_range
        WHEN 0 THEN bo.fullname WHEN 3 THEN bo.fullname ELSE IFNULL(bb.fullname,clv.labelfield_value) END as filterName
        from ss_promotion_filter spo
        left join ss_promotion sp on sp.id = spo.promotion_id and sp.profile_id = spo.profile_id
        left join base_otype bo on bo.id = spo.filter_id and bo.profile_id = spo.profile_id and bo.deleted=0
        left join base_btype bb on bb.id = spo.filter_id and bb.profile_id = spo.profile_id and bb.deleted=0
        left join cf_labelfield_value clv on clv.id = spo.filter_id and clv.profile_id = spo.profile_id
        <where>
            spo.profile_id=#{profileId}
            <if test="null != promotionIds and promotionIds.size() > 0">
                and spo.promotion_id in
                <foreach collection="promotionIds" item="promotionId" separator="," open="(" close=")">
                    #{promotionId}
                </foreach>
            </if>
        </where>
    </select>
    <!--促销策略销售机构结束-->

    <!--促销策略商品列表开始-->
    <insert id="insertPromotionPtypes">
        INSERT INTO ss_promotion_ptype
        (id, ptype_group, promotion_id, ptype_type, pid, profile_id,strategy_id,qty, preferential,ptype_id,sku_id,
        unit_id,rowindex)
        values
        <foreach collection="ptypeList" separator="," item="item">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.id},
                #{item.ptypeGroup},
                #{item.promotionId},
                #{item.promotionPtypeType},
                #{item.pid},
                #{item.profileId},
                #{item.strategyId},
                #{item.giftQty},
                <choose>
                    <when test="item.preferential == null">
                        0,
                    </when>
                    <otherwise>
                        #{item.preferential},
                    </otherwise>
                </choose>
                <choose>
                    <when test="item.ptypeId == null">
                        0,
                    </when>
                    <otherwise>
                        #{item.ptypeId},
                    </otherwise>
                </choose>
                <choose>
                    <when test="item.skuId == null">
                        0,
                    </when>
                    <otherwise>
                        #{item.skuId},
                    </otherwise>
                </choose>
                <choose>
                    <when test="item.unitId == null">
                        0,
                    </when>
                    <otherwise>
                        #{item.unitId},
                    </otherwise>
                </choose>
                <choose>
                    <when test="item.rowindex == null">
                        0,
                    </when>
                    <otherwise>
                        #{item.rowindex},
                    </otherwise>
                </choose>
            </trim>
        </foreach>
    </insert>


    <update id="updatePromotionPtype">
        update ss_promotion_ptype set preferential =#{preferential}
        <where>
            id=#{id} and profile_id=#{profileId}
        </where>
    </update>

    <update id="updatePromotionChangeCount">
        update ss_promotion_ptype set change_count =#{changeCount}
        <where>
            id=#{id} and profile_id=#{profileId}
        </where>
    </update>

    <delete id="deletePromotionPtypes">
        delete from ss_promotion_ptype
        <where>
            promotion_id =#{promotionId} and profile_id=#{profileId}
        </where>
    </delete>
    <delete id="deletePromotionPtypeById">
        delete from ss_promotion_ptype
        <where>
            id=#{id} and profile_id=#{profileId}
        </where>
    </delete>
    <delete id="deletePromotionPtypesByIds">
        update ss_promotion_ptype sp set sp.deleted =1
        <where>
            profile_id=#{profileId}
            <if test="null != ids and ids.size() > 0">
                and id in
                <foreach collection="ids" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </if>
            <if test="ids == null or ids.size() == 0">
                and 1!=1
            </if>
        </where>
    </delete>

    <resultMap id="promotionPtypeResult" type="com.wsgjp.ct.sale.biz.shopsale.model.entity.promotion.PromotionPtype"
               autoMapping="true">
        <id property="id" column="id"/>
        <result property="ptypeGroup" column="ptype_group"/>
        <result property="promotionId" column="promotion_id"/>
        <result property="promotionPtypeType" column="ptype_type"/>
        <result property="ptypeId" column="ptype_id"/>
        <result property="skuId" column="sku_id"/>
        <result property="retailPrice" column="retail_price"/>
        <result property="combo" column="combo"/>
        <result property="batchEnabled" column="batchenabled"/>
        <result property="giftQty" column="giftQty"/>
        <result property="snEnabled" column="snenabled"/>
        <result property="preferential" column="preferential"/>
        <result property="profileId" column="profile_id"/>
        <result property="createTime" column="created_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="ptypeName" column="ptypeName"/>
        <result property="fullname" column="fullname"/>
        <result property="valueTypeName" column="valueTypeName"/>
        <result property="validType" column="valid_type"/>
        <result property="validValue" column="valid_value"/>
        <result property="unitName" column="unit_name"/>
        <result property="propertyName" column="propertyName"/>
        <result property="usercode" column="usercode"/>
        <result property="pid" column="pid"/>
        <result property="unitId" column="unit_id"/>
        <result property="xcode" column="xcode"/>
        <result property="pcategory" column="pcategory"/>
        <result property="fullbarcode" column="fullbarcode"/>
        <result property="skuBarcode" column="skuBarcode"/>
        <result property="protectDays" column="protect_days"/>
        <result property="propenabled" column="propenabled"/>
        <result column="cost_mode" property="costMode"/>
        <result column="picUrl" property="picUrl"/>
        <result column="price" property="price"/>
        <result column="prop_id1" property="propId1"/>
        <result column="prop_id2" property="propId2"/>
        <result column="prop_name1" property="propName1"/>
        <result column="prop_name2" property="propName2"/>
        <result column="propvalue_id1" property="propvalueId1"/>
        <result column="propvalue_id2" property="propvalueId2"/>
        <result column="propvalue_name1" property="propvalueName1"/>
        <result column="propvalue_name2" property="propvalueName2"/>
        <result column="sku_price" property="skuPrice"/>
        <result column="standard" property="standard"/>
        <result column="unit_rate" property="unitRate"/>
        <result column="weight" property="weight"/>
        <result column="weight_unit" property="weightUnit"/>
        <collection property="batchNos"
                    ofType="java.lang.String">
            <constructor>
                <arg column="batch_no"/>
            </constructor>
        </collection>
    </resultMap>

    <select id="selectPromotionPtypes"
            resultMap="promotionPtypeResult">
        select spp.id,spp.ptype_group ,spp.promotion_id,spp.ptype_type,spp.ptype_id,spp.sku_id ,bpp.retail_price
        ,if( bp.pcategory =2,true,false) as combo,bp.batchenabled ,bp.snenabled,bp.propenabled,
        spp.preferential ,spp.profile_id ,spp.created_time ,spp.update_time ,ifnull(bp.fullname,temp.fullname) as
        ptypeName, ifnull(bp.fullname,temp.fullname) as
        fullname,case ifnull(temp.card_type,0)
        when 2 then '代金券'
        when 3 then '折扣券'
        when 4 then '礼品券'
        else null end as valueTypeName,temp.valid_type,temp.valid_value
        ,bpu.unit_name ,bps.propvalue_names as
        propertyName,bp.usercode,spp.pid,spp.unit_id,bp.pcategory,ifnull(bpf.fullbarcode,bp.barcode) as
        fullbarcode, spp.qty as giftQty,
        ifnull(bpf.fullbarcode,bp.barcode) as
        skuBarcode,bp.protect_days,bp.protect_days_unit,bp.protect_days_view,bp.sku_price
        ,bp.tax_rate ,bpu.unit_rate,bp.weight,
        (SELECT bpx_inner.xcode
        FROM base_ptype_xcode bpx_inner
        WHERE bpx_inner.sku_id = spp.sku_id
        AND bpx_inner.profile_id = spp.profile_id
        AND bpx_inner.ptype_id = spp.pid
        AND bpx_inner.unit_id = spp.unit_id
        ORDER BY bpx_inner.defaulted DESC
        LIMIT 1
        ) AS xcode
        ,bp.weight_unit, if(bps.pic_url='' , bpp2.pic_url ,bps.pic_url) as picUrl,bps.prop_id1 ,bps.prop_id2
        ,bps.propvalue_name1,bps.propvalue_name2
        ,bps.prop_name1 ,bps.prop_name2 ,bps.propvalue_id1 ,bps.propvalue_id2,bp.ptype_type,spp.price as
        price,bp.cost_mode,sppbn.batch_no,bp.standard,bp.stoped,bp.deleted,bpp.preprice1,bpp.preprice2,bpp.preprice3,bpp.preprice4,bpp.preprice5,bpp.preprice6,bpp.preprice7,bpp.preprice8,bpp.preprice9,bpp.preprice10
        from ss_promotion_ptype spp
        LEFT JOIN base_ptype bp
        ON bp.id = spp.pid and bp.profile_id =#{profileId}
        LEFT JOIN base_ptype_fullbarcode bpf
        ON bpf.ptype_id =spp.pid and bpf.sku_id =spp.sku_id and bpf.unit_id = spp.unit_id and bpf.profile_id
        =#{profileId}
        LEFT JOIN base_ptype_pic bpp2
        ON bpp2.ptype_id =spp.pid and bpp2.rowindex =1 and bpp2.profile_id =#{profileId}
        LEFT JOIN base_ptype_sku bps
        ON bps.id = spp.sku_id and bps.profile_id =#{profileId}
        LEFT JOIN base_ptype_unit bpu
        ON bpu.id = spp.unit_id and bpu.profile_id =#{profileId}
        LEFT JOIN base_ptype_price bpp on bpp.profile_id =#{profileId} and bpp.ptype_id =spp.pid and (
        spp.ptype_group =2
        or ((spp.ptype_group =1 or spp.ptype_group =0) and bpp.unit_id = spp.unit_id and ( if(bp.sku_price =1,spp.sku_id
        =bpp.sku_id,spp.pid
        =bpp.ptype_id))))
        left join ss_card_template as temp
        on temp.id=spp.pid
        left join ss_promotion_ptype_batch_no sppbn
        on sppbn.promotion_id=spp.promotion_id and sppbn.ss_promotion_ptype_id =spp.id and sppbn.profile_id =
        #{profileId}
        <where>
            spp.profile_id=#{profileId}
            and spp.deleted=0
            and (
            (temp.valid_type IN (1, 2)
            OR (temp.valid_type = 0 AND DATE_FORMAT (temp.valid_value , '%Y-%m-%d') >= DATE_FORMAT (now() ,
            '%Y-%m-%d')))
            OR temp.id IS NULL
            )
            <if test="null != promotionIds and promotionIds.size() > 0">
                and spp.promotion_id in
                <foreach collection="promotionIds" item="promotionId" separator="," open="(" close=")">
                    #{promotionId}
                </foreach>
            </if>
            and (spp.ptype_id = 0 or (spp.ptype_id != 0 and bp.deleted = 0))
        </where>
        order by spp.rowindex
    </select>
    <!--促销策略商品列表结束-->

    <resultMap id="PromotionCredits"
               type="com.wsgjp.ct.sale.biz.shopsale.model.vo.promotion.PromotionCredits">
        <id property="id" column="id"/>
        <result property="ptypeId" column="ptype_id"/>
        <result property="ptypeGroup" column="ptype_group"/>
        <result property="skuId" column="sku_id"/>
        <result property="price" column="price"/>
        <result property="preferential" column="preferential"/>
        <result property="unitId" column="unit_id"/>
        <result property="pid" column="pid"/>
        <result property="changeCount" column="change_count"/>
        <result property="picUrl" column="picUrl"/>
        <result property="creditsName" column="creditsName"/>
    </resultMap>


    <select id="selectPromotionCredits"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.vo.promotion.PromotionCredits">
        SELECT spp.promotion_id as id,
        spp.id as promotionPtypeId,
        spp.ptype_id,
        spp.ptype_group as ptypeGroup,
        spp.sku_id,
        spp.price,
        spp.preferential,
        spp.unit_id,
        spp.pid,
        bpf.fullbarcode,
        spp.change_count,
        bp.cost_mode,bp.protect_days,
        bp.batchenabled,
        bp.snenabled,
        bps.prop_id1 ,bps.prop_id2,bps.prop_id3,bps.prop_id4,bps.prop_id5,bps.prop_id6
        ,bps.propvalue_name1,bps.propvalue_name2,bps.propvalue_name3,bps.propvalue_name4,bps.propvalue_name5,bps.propvalue_name6
        ,bps.prop_name1 ,bps.prop_name2 ,bps.prop_name3 ,bps.prop_name4 ,bps.prop_name5 ,bps.prop_name6 ,
        bps.propvalue_id1 ,bps.propvalue_id2,bps.propvalue_id3,bps.propvalue_id4,bps.propvalue_id5,bps.propvalue_id6,
        bpu.unit_rate as unitRate,bpu.unit_name as unitName,sct.card_type as valueType,
        (case spp.ptype_group when 2 then bp.fullname when 3 then bp.fullname when 5 then sct.fullname when 6 then
        sct.fullname else
        concat('储值金额',spp.price,'¥') end) as creditsName,
        if(bpp.pic_url='' , bps.pic_url ,bpp.pic_url) as picUrl
        FROM ss_promotion_ptype spp
        LEFT JOIN ss_promotion sp on spp.promotion_id = sp.id and sp.promotion_type = 4
        LEFT JOIN ss_promotion_filter spo on spo.promotion_id = spp.promotion_id
        LEFT JOIN base_ptype bp on spp.deleted = bp.deleted and bp.profile_id = #{profileId}
        and bp.id = spp.pid and (spp.ptype_group = 3 or spp.ptype_group = 2)
        LEFT JOIN base_ptype_pic bpp on bpp.profile_id = #{profileId} and (spp.ptype_group = 3 or spp.ptype_group = 2)
        and bpp.ptype_id =
        spp.pid
        LEFT JOIN base_ptype_sku bps on bps.profile_id = #{profileId} and (spp.ptype_group = 3 or spp.ptype_group = 2)
        and bps.id = spp.sku_id
        LEFT JOIN base_ptype_fullbarcode bpf on bpf.profile_id = #{profileId} and bpf.sku_id =spp.sku_id
        LEFT JOIN base_ptype_unit bpu on bpu.profile_id = #{profileId} and bpu.id = spp.unit_id
        LEFT JOIN ss_card_template sct on sct.id = spp.pid and sct.profile_id = spp.profile_id and spp.ptype_group in
        (5,6)
        LEFT JOIN ss_card_template_filter sctf on sctf.filter_type = 0 and
        spp.ptype_group = 6 and sctf.card_template_id = spp.pid

        where spp.ptype_group between 2 and 7 and spp.deleted = 0 and spp.profile_id = #{profileId} and
        ((spp.ptype_group != 6)or(spp.ptype_group=6 and sctf.filter_id
        = #{otypeId})) and sp.stoped = 0 and sp.deleted = 0 and #{currentTime} between sp.start_date and sp.end_date
        and #{currentHHSS} between sp.start_time and sp.end_time and spo.filter_id = #{otypeId}
        and ((spp.ptype_id != '' and bp.deleted = 0) or spp.ptype_id = '')
        and (
        (sct.valid_type IN (1, 2)
        OR (sct.valid_type = 0 AND DATE_FORMAT (sct.valid_value , '%Y-%m-%d') >= DATE_FORMAT (now() , '%Y-%m-%d')))
        OR sct.id IS NULL
        )
        <if test="betweenMin != null">
            and cast(spp.preferential as DECIMAL ) >= #{betweenMin}
        </if>
        <if test="betweenMax != 0">
            and #{betweenMax} >= cast(spp.preferential as DECIMAL )
        </if>
        GROUP BY creditsName,spp.id
        <if test="fullName != null">
            having creditsName
            like
            concat('%',#{fullName},'%')
        </if>
        <if test="sortType == 1">
            ORDER BY cast(spp.preferential as DECIMAL ) Asc
        </if>
        <if test="sortType == 2">
            ORDER BY cast(spp.preferential as DECIMAL ) desc
        </if>
    </select>

    <select id="getPromotionByName" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.promotion.PromotionInfo">
        SELECT id,
               etype_id,
               promotion_type,
               start_date,
               end_date,
               start_time,
               end_time,
               rang_type,
               profile_id,
               created_time,
               update_time,
               deleted,
               stoped,
               fullname,
               cal_integral,
               join_order,
               vip_rights,
               priority
        from ss_promotion
        where profile_id = #{profileId}
          and fullname = #{name}
          and deleted = 0
    </select>

    <select id="getPromotionListId" resultType="java.math.BigInteger">
        select id from ss_promotion
        where deleted = 0 and
        id
        in
        <foreach collection="promotionIds" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
        and
        ((start_date >= #{startDate} and start_date &lt;= #{endDate})
        OR (end_Date >= #{startDate} and end_Date &lt;= #{endDate})
        OR (start_date &lt;= #{startDate} and end_Date>= #{endDate}))
    </select>

    <select id="getMaxPromotionPriority" resultType="java.math.BigInteger">
        SELECT MAX(sp.priority),
               sp.id,
               sp.etype_id,
               sp.promotion_type,
               sp.start_date,
               sp.end_date,
               sp.start_time,
               sp.end_time,
               sp.rang_type,
               sp.profile_id,
               sp.created_time,
               sp.update_time,
               sp.deleted,
               sp.stoped,
               sp.fullname,
               sp.cal_integral,
               sp.join_order,
               sp.vip_rights,
               sp.priority
        FROM ss_promotion sp
        WHERE sp.profile_id = #{profileId}
          AND sp.deleted = 0
          AND sp.promotion_type = #{promotionType}
    </select>

    <select id="getExchangePromotionInfo"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.promotion.PromotionInfoExpand">
        SELECT sp.id, sp.priority, sp1.priority as exchangePriority, sp1.id as exchangeId
        FROM ss_promotion sp,
        ss_promotion sp1
        WHERE sp.deleted = 0
        and sp1.deleted = 0
        and sp.profile_id = #{param.profileId}
        and sp1.profile_id = #{param.profileId}
        and (sp.start_date > CURDATE() or
        (sp.start_date &lt;= CURDATE() and sp.end_date >= CURDATE() and sp.start_time &lt;= CURTIME() and
        sp.end_time >= CURTIME()) or (sp.start_date &lt;= CURDATE() and sp.end_date >= CURDATE() and
        (sp.start_time >= CURTIME() or sp.end_time &lt;= CURTIME())))
        and (sp1.start_date > CURDATE() or (sp1.start_date &lt;= CURDATE() and sp1.end_date >= CURDATE() and
        sp1.start_time &lt;= CURTIME() and sp1.end_time >= CURTIME()) or
        (sp1.start_date &lt;= CURDATE() and sp1.end_date >= CURDATE() and
        (sp1.start_time >= CURTIME() or sp1.end_time &lt;= CURTIME())))
        and sp.promotion_type = #{param.promotionType}
        and sp1.promotion_type = #{param.promotionType}
        and sp.id = #{param.promotionId}
        <if test="param.downOrUp == 0">
            and sp1.priority > sp.priority
        </if>
        <if test="param.downOrUp == 1">
            and sp1.priority &lt; sp.priority
        </if>
        <if test="param.downOrUp == 0">
            order by exchangePriority
        </if>
        <if test="param.downOrUp == 1">
            order by exchangePriority Desc
        </if>
        limit 1
    </select>

    <select id="getBtypeTypeId" resultType="java.lang.String">
        SELECT typeid
        FROM base_btype
        WHERE id = #{btypeId}
          AND profile_id = #{profileId}
    </select>

    <!--根据往来单位id获取促销id列表，目前促销和往来单位可以直接通过往来单位id关联，可以通过分类关联，可以通过往来单位的标签关联，所以要根据往来单位查询标签，查询分类-->
    <select id="getPromotionIdByBtypeIdAndTypeIds" resultType="java.math.BigInteger">
        SELECT sprv.promotion_id
        FROM (
        SELECT #{btypeId} AS filter_id
        UNION ALL (SELECT labelfield_value_id AS filter_id FROM cf_data_label_btype WHERE profile_id = #{profileId} AND
        resource_id = #{btypeId})
        <if test="typeIds!=null and typeIds.size() > 0">
            UNION ALL SELECT id AS filter_id FROM base_btype WHERE profile_id = #{profileId} AND classed = 1 AND typeid
            IN
            <foreach collection="typeIds" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
        ) AS temp
        INNER JOIN ss_promotion_rang_value sprv ON sprv.profile_id = #{profileId} AND sprv.rang_id = temp.filter_id
        <!--        <if test="otypeId != null and otypeId != ''">-->
        <!--            LEFT JOIN ss_promotion_filter spf ON spf.promotion_id = sprv.promotion_id-->
        <!--            AND spf.profile_id = #{profileId}-->
        <!--            AND spf.filter_id = #{otypeId}-->
        <!--            AND spf.deleted = 0-->
        <!--            WHERE spf.id IS NULL OR (spf.filter_id = #{otypeId} AND spf.deleted = 0)-->
        <!--        </if>-->
        <!--        当 otypeId 不传值时：-->
        <!--        如果 ss_promotion_filter 表中对某个 sprv.promotion_id 没有数据（即没有 deleted = 0 的记录），则匹配上。-->
        <!--        如果 ss_promotion_filter 表中对某个 sprv.promotion_id 有数据，则不匹配。-->
        <!--        当 otypeId 传值时：-->
        <!--        如果 ss_promotion_filter 表中对某个 sprv.promotion_id 没有数据（即没有 deleted = 0 的记录），则不验证，直接匹配上。-->
        <!--        如果 ss_promotion_filter 表中对某个 sprv.promotion_id 有数据，则需要验证是否匹配 otypeId 和 deleted = 0 条件，只有匹配时才能返回。-->
        LEFT JOIN ss_promotion_filter spf ON spf.promotion_id = sprv.promotion_id
        AND spf.profile_id = #{profileId}
        AND spf.deleted = 0
        <if test="otypeId != null and otypeId != ''">
            AND spf.filter_id = #{otypeId}
        </if>
        WHERE 1=1
        <if test="otypeId != null and otypeId != ''">
            AND (
            -- 情况1：ss_promotion_filter 表中有数据，且匹配 otypeId
            spf.id IS NOT NULL
            -- 情况2：ss_promotion_filter 表中没有数据（对该 promotion_id）
            OR NOT EXISTS (
            SELECT 1 FROM ss_promotion_filter spf2
            WHERE spf2.promotion_id = sprv.promotion_id
            AND spf2.profile_id = #{profileId}
            AND spf2.deleted = 0
            )
            )
        </if>
        <if test="otypeId == null or otypeId == ''">
            AND NOT EXISTS (
            -- 当 otypeId 不传时，验证 ss_promotion_filter 表中没有数据才能匹配
            SELECT 1 FROM ss_promotion_filter spf2
            WHERE spf2.promotion_id = sprv.promotion_id
            AND spf2.profile_id = #{profileId}
            AND spf2.deleted = 0
            )
        </if>
    </select>

    <insert id="insertPromotionPtypeBatch">
        INSERT INTO ss_promotion_ptype_batch_no
        (id, profile_id, promotion_id, ss_promotion_ptype_id, batch_no)
        values
        <foreach collection="batchNoList" separator="," item="item">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.id},
                #{item.profileId},
                #{item.promotionId},
                #{item.ssPromotionPtypeId},
                #{item.batchNo},
            </trim>
        </foreach>
    </insert>
    <delete id="deletePromotionPtypesBatch">
        delete from ss_promotion_ptype_batch_no
        <where>
            promotion_id =#{promotionId} and profile_id=#{profileId}
        </where>
    </delete>

    <select id="getExistPtypeBatch" resultType="String">
        select batchno from acc_goodsstock_batch where profile_id = #{profileId} and ptype_id = #{ptypeId}
        <if test="skuId != null and skuId != 0">
            and sku_id = #{skuId}
        </if>
    </select>

    <delete id="deletePromotionRangValuePromotionId">
        delete
        from ss_promotion_rang_value
        where promotion_id = #{promotionId}
          and profile_id = #{profileId}
    </delete>
    <insert id="insertPromotionRangValue">
        INSERT INTO ss_promotion_rang_value
        (id, profile_id, promotion_id, rang_id, range_value_type, vip_type,rang_name)
        values
        <foreach collection="rangValueList" separator="," item="item">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.id},
                #{item.profileId},
                #{item.promotionId},
                #{item.rangId},
                #{item.rangeValueType},
                #{item.vipType},
                #{item.rangName},
            </trim>
        </foreach>
    </insert>
    <select id="selectPromotionRangValue"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.promotion.PromotionRangValue">
        select yu.id as rangId, bb.usercode as usercode, bb.fullname as btypeName,sprv.promotion_id as promotionId,
        sprv.range_value_type as rangeValueType, sprv.vip_type as vipType,yu.user_name as
        userName,yu.phone,sprv.rang_id,Case
        sprv.range_value_type
        WHEN 4 THEN bb.fullname WHEN 6 THEN bb.fullname WHEN 7 THEN clv.labelfield_value ELSE
        IFNULL(sprv.rang_name,"") END as rangName

        from ss_promotion_rang_value sprv
        left join ydh_user yu on yu.id = sprv.rang_id
        left join ydh_user_relation yur on yu.id = yur.user_id
        left join base_btype bb on sprv.rang_id = bb.id and bb.deleted=0 and bb.profile_id = #{profileId}
        left join cf_labelfield_value clv on clv.id =sprv.rang_id and clv.profile_id = #{profileId}

        where sprv.profile_id = #{profileId}
        and sprv.promotion_id in
        <foreach item="promotionId" collection="promotionIds" open="(" separator="," close=")">
            ${promotionId}
        </foreach>
    </select>
</mapper>

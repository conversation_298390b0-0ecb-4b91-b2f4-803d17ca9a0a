<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopRefundOptLogMapper">
    <sql id="logInfo">
        log
        .
        id
        ,log.state_type, log.old_state, log.new_state, log.otype_id, log.create_time
    </sql>

    <select id="getRefundOptLogList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundOptLogEntity">
        SELECT
        <include refid="logInfo"/>
        FROM pl_eshop_refund_opt_log log
    </select>

    <insert id="insertRefundOptLog">
        INSERT INTO pl_eshop_refund_opt_log(id, state_type, old_state, new_state, create_time, profile_id, refund_order_id)
        VALUES (#{id}, #{stateType}, #{oldState}, #{newState}, #{createTime}, #{profileId}, #{refundOrderId})
    </insert>
</mapper>
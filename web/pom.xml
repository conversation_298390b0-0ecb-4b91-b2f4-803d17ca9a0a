<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>sale-web</artifactId>
    <version>1.0-SNAPSHOT</version>

    <parent>
        <groupId>com.wsgjp.ct</groupId>
        <artifactId>sale</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <dependencies>
        <!-- 排除冲突的annotation依赖 -->
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
            <version>1.3.2</version>
        </dependency>

        <dependency>
            <groupId>ngp</groupId>
            <artifactId>ngp-mq</artifactId>
            <version>${ngp.mq.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.tomcat</groupId>
                    <artifactId>annotations-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>ngp-service-component</groupId>
            <artifactId>ngp-service-component-job</artifactId>
            <version>2.0.5.5-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>io.opentracing.contrib</groupId>
            <artifactId>opentracing-concurrent</artifactId>
            <version>0.4.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wsgjp.ct</groupId>
            <artifactId>sale-biz</artifactId>
            <version>2.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>ngp</groupId>
                    <artifactId>ngp-mq</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wsgjp.ct</groupId>
                    <artifactId>sys-support</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.wsgjp.ct</groupId>
                    <artifactId>ngp-datasource</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.tomcat</groupId>
                    <artifactId>annotations-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>ngp</groupId>
            <artifactId>ngp-mq</artifactId>
            <version>${ngp.mq.version}</version>
        </dependency>
        <dependency>
            <groupId>ngp</groupId>
            <artifactId>ngp-dev</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wsgjp.ct</groupId>
            <artifactId>sys-support</artifactId>
            <version>${sys.support.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.tomcat</groupId>
                    <artifactId>annotations-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.2.0.RELEASE</version>
                <configuration>
                    <executable>true</executable>
                    <embeddedLaunchScript>../script/launch.sh</embeddedLaunchScript>
                    <!--启动类位置-->
                    <mainClass>com.wsgjp.ct.sale.web.WebApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
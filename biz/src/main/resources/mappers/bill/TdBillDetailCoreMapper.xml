<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.bill.mapper.TdBillDetailCoreMapper">
    <update id="batchUpdateRefundQty">
        <foreach collection="list" index="index" item="item" open="" close="" separator=";">
            update td_bill_detail_core
            set refund_qty = #{item.refundQty}
            where profile_id=#{item.profileId} and vchcode=#{item.vchcode} and detail_id=#{item.detailId}
        </foreach>
    </update>

    <resultMap id="BaseResultMap" type="com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.GoodsDetailDTO">
        <!--    主表的字段集合  c -->
        <id column="detail_id" property="detailId" jdbcType="BIGINT"/>
        <result column="stockQty" property="stockQty" jdbcType="DECIMAL"/>
        <result column="vchcode" property="vchcode" jdbcType="BIGINT"/>
        <result column="vchtype" property="vchtype" jdbcType="INTEGER"/>
        <result column="profile_id" property="profileId" jdbcType="BIGINT"/>
        <result column="ktype_id" property="ktypeId" jdbcType="BIGINT"/>
        <result column="ptype_id" property="ptypeId" jdbcType="BIGINT"/>
        <result column="ptypeClass" property="ptypeClass" jdbcType="VARCHAR"/>
        <result column="sku_id" property="skuId" jdbcType="BIGINT"/>
        <result column="sku_name" property="skuName" jdbcType="VARCHAR"/>
        <result column="shortname" property="shortname" jdbcType="VARCHAR"/>
        <result column="ptype_area" property="ptypeArea" jdbcType="VARCHAR"/>
        <result column="sku_pic_url" property="skuPicUrl" jdbcType="VARCHAR"/>
        <result column="cost_price" property="costPrice" jdbcType="DECIMAL"/>
        <result column="cost_total" property="costTotal" jdbcType="DECIMAL"/>
        <result column="tax_rate" property="taxRate" jdbcType="DECIMAL"/>
        <result column="memo" property="memo" jdbcType="VARCHAR"/>
        <result column="inout_type" property="inoutType" jdbcType="TINYINT"/>
        <result column="source_vchtype" property="sourceVchtype" jdbcType="BIGINT"/>
        <result column="source_vchcode" property="sourceVchcode" jdbcType="BIGINT"/>
        <result column="source_business_type" property="sourceBusinessType" jdbcType="BIGINT"/>
        <result column="source_detail_id" property="sourceDetailId" jdbcType="BIGINT"/>
        <result column="source_bill_number" property="sourceBillNumber" jdbcType="VARCHAR"/>
        <result column="combo_detail_id" property="comboDetailId" jdbcType="BIGINT"/>
        <result column="qty" property="qty" jdbcType="DECIMAL"/>
        <result column="request_qty" property="requestQty" jdbcType="DECIMAL"/>
        <result column="request_sub_qty" property="requestSubQty" jdbcType="DECIMAL"/>
        <result column="request_unit_qty" property="requestUnitQty" jdbcType="DECIMAL"/>
        <result column="completed_qty" property="completedQty" jdbcType="DECIMAL"/>
        <result column="row_index" property="rowIndex" jdbcType="INTEGER"/>
        <result column="row_index" property="__rowIndex" jdbcType="INTEGER"/>
        <result column="ptype_preferential_total" property="currencyPtypePreferentialTotal" jdbcType="DECIMAL"/>
        <!--   扩展表字段 tbde-->
        <result column="wmsRealityQty" property="wmsRealityQty" jdbcType="DECIMAL"/>
        <result column="custom_buy_type" property="customBuyType" jdbcType="BIGINT"/>
        <result column="give_vip_score" property="giveVipScore" jdbcType="TINYINT"/>
        <result column="discount_source_type" property="discountSourceType" jdbcType="TINYINT"
                typeHandler="com.wsgjp.ct.sale.biz.shopsale.handler.DiscountSourceEnumTypeHandler"/>
        <result column="typeid" property="typeId" jdbcType="VARCHAR"/>

        <!--   商品表字段  pt-->
        <result column="fullname" property="pFullName" jdbcType="VARCHAR"/>
        <result column="usercode" property="pUserCode" jdbcType="VARCHAR"/>
        <result column="snenabled" property="snenabled" jdbcType="TINYINT"/>
        <result column="pcategory" property="pcategory" jdbcType="TINYINT"/>
        <result column="propenabled" property="propenabled" jdbcType="BIT"/>
        <result column="batchenabled" property="batchenabled" jdbcType="BIT"/>
        <result column="gift" property="gift" jdbcType="TINYINT"/>

        <!--info  i表集合-->
        <result column="unit_qty" property="unitQty" jdbcType="DECIMAL"/>
        <result column="sub_qty" property="subQty" jdbcType="DECIMAL"/>
        <result column="completed_sub_qty" property="completedSubQty" jdbcType="DECIMAL"/>
        <result column="currency_price" property="currencyPrice" jdbcType="DECIMAL"/>
        <result column="currency_total" property="currencyTotal" jdbcType="DECIMAL"/>
        <result column="currency_dised_price" property="currencyDisedPrice" jdbcType="DECIMAL"/>
        <result column="currency_dised_total" property="currencyDisedTotal" jdbcType="DECIMAL"/>
        <result column="discount" property="discount" jdbcType="DECIMAL"/>
        <result column="currency_dised_taxed_price" property="currencyDisedTaxedPrice" jdbcType="DECIMAL"/>
        <result column="currency_dised_taxed_total" property="currencyDisedTaxedTotal" jdbcType="DECIMAL"/>
        <result column="currency_tax_total" property="currencyTaxTotal" jdbcType="DECIMAL"/>
        <result column="currency_completed_preferential_share" property="currencyCompletedPreferentialShare"
                jdbcType="DECIMAL"/>
        <result column="refund_qty" property="refundQty" jdbcType="DECIMAL"/>
        <result column="refund_sub_qty" property="refundSubQty" jdbcType="DECIMAL"/>
        <result column="refund_order_preferential_allot_total" property="refundOrderPreferentialAllotTotal"
                jdbcType="DECIMAL"/>
        <result column="ptype_commission_rate" property="ptypeCommissionRate" jdbcType="DECIMAL"/>

        <result column="batchno" property="batchNo" jdbcType="VARCHAR"/>
        <result column="sub_unit_name" property="subUnitName" jdbcType="VARCHAR"/>
        <result column="produce_date" property="produceDate" jdbcType="DATE"/>
        <result column="expire_date" property="expireDate" jdbcType="DATE"/>
        <result column="batch_price" property="batchPrice" jdbcType="DECIMAL"/>
        <result column="unit_rate" property="unitRate" jdbcType="DECIMAL"/>
        <result column="unit_id" property="unitId" jdbcType="BIGINT"/>
        <result column="unit_name" property="unitName" jdbcType="VARCHAR"/>
        <result column="kfullname" property="kfullname" jdbcType="VARCHAR"/>
        <result column="scategory" property="scategory" jdbcType="INTEGER"/>
        <result column="stock_type" property="stockType" jdbcType="INTEGER"/>
        <result column="stock_state" property="stockState" jdbcType="INTEGER"/>
        <result column="protect_days" property="protectDays" jdbcType="INTEGER"/>
        <result column="standard" property="standard" jdbcType="VARCHAR"/>
        <result column="ptype_type" property="ptypetype" jdbcType="VARCHAR"/>
        <result column="brand_name" property="brandName" jdbcType="VARCHAR"/>
        <result column="fullbarcode" property="fullbarcode" jdbcType="VARCHAR"/>
        <result column="xcode" property="xcode" jdbcType="VARCHAR"/>
        <result column="pic_url" property="picUrl" jdbcType="VARCHAR"/>
        <result column="objectKtypeId" property="objectKtypeId" jdbcType="BIGINT"/>
        <result column="ptype_length" property="ptypeLength" jdbcType="DECIMAL"/>
        <result column="ptype_width" property="ptypeWidth" jdbcType="DECIMAL"/>
        <result column="ptype_height" property="ptypeHeight" jdbcType="DECIMAL"/>
        <result column="weight" property="weight" jdbcType="DECIMAL"/>
        <result column="length_unit" property="lengthUnit" jdbcType="DECIMAL"/>
        <result column="weight_unit" property="weightUnit" jdbcType="DECIMAL"/>
        <result column="currencyOrderFeeAllotTotal" property="currencyOrderFeeAllotTotal" jdbcType="DECIMAL"/>
        <result column="custom_field01" property="customField01" jdbcType="VARCHAR"/>
        <result column="custom_field02" property="customField02" jdbcType="VARCHAR"/>
        <result column="custom_field03" property="customField03" jdbcType="VARCHAR"/>
        <result column="custom_field04" property="customField04" jdbcType="VARCHAR"/>
        <result column="custom_field05" property="customField05" jdbcType="VARCHAR"/>
        <result column="custom_field06" property="customField06" jdbcType="DECIMAL"/>
        <result column="custom_field07" property="customField07" jdbcType="DECIMAL"/>
        <result column="custom_field08" property="customField08" jdbcType="DECIMAL"/>
        <result column="custom_field09" property="customField09" jdbcType="DATE"/>
        <result column="custom_field10" property="customField10" jdbcType="DATE"/>
        <result column="custom_field11" property="customField11" jdbcType="DATE"/>
        <result column="custom_field12" property="customField12" jdbcType="DECIMAL"/>
        <result column="custom_field13" property="customField13" jdbcType="DECIMAL"/>
        <result column="orderUnitId" property="orderUnitId" jdbcType="BIGINT"/>
        <result column="orderUnitRate" property="orderUnitRate" jdbcType="DECIMAL"/>
        <result column="orderUnitQty" property="orderUnitQty" jdbcType="DECIMAL"/>
        <result column="orderCompletedQty" property="orderCompletedQty" jdbcType="DECIMAL"/>
        <result column="currencyBuyerTotal" property="currencyBuyerTotal" jdbcType="DECIMAL"/>
        <result column="ktype_point_id" property="ktypePointId" jdbcType="BIGINT"/>
        <result column="ktype_point_type" property="ktypePointType" jdbcType="TINYINT"/>
        <result column="car_id" property="carId" jdbcType="BIGINT"/>
        <result column="car_number" property="carNumber" jdbcType="VARCHAR"/>
        <result column="drfullname" property="drfullname" jdbcType="VARCHAR"/>
        <result column="buyer_dised_taxed_price" property="buyerDisedTaxedPrice" jdbcType="DECIMAL"/>
        <result column="buyer_dised_taxed_total" property="buyerDisedTaxedTotal" jdbcType="DECIMAL"/>

        <result column="productAccountName" property="productAccountName" jdbcType="VARCHAR"/>
        <result column="currency_give_preferential_total" property="currencyGivePreferentialTotal"
                jdbcType="DECIMAL"/>
        <result column="give_preferential_total" property="givePreferentialTotal"
                jdbcType="DECIMAL"/>
        <result column="ptype_standard" property="ptypeStandard" jdbcType="VARCHAR"/>
        <result column="zyPtypeType" property="ptypeType" jdbcType="VARCHAR"/>

        <!--   套餐明细表 tbdc-->
        <result column="combo_id" property="comboId" jdbcType="BIGINT"/>
    </resultMap>

    <select id="getBillDetailCoreList" resultMap="BaseResultMap" parameterType="Map">
        select distinct
        IFNULL(base_pf.fullbarcode, '') baseFullBarcode,
        tabc.bill_number as source_bill,
        tbde.wms_reality_qty wmsRealityQty,
        tbde.custom_buy_type,
        tbde.give_vip_score,
        c.detail_id, c.vchcode, c.profile_id, c.vchtype,
        c.ktype_id,c.ptype_id,c.sku_id,c.sku_name,c.inout_type,bpp.pic_url,c.combo_detail_id,c.gift,c.completed_qty,c.qty,c.row_index,i.request_qty,i.request_sub_qty,i.request_unit_qty,
        c.cost_price,c.cost_total,tbdb.batch_price,c.tax_rate,c.memo,c.source_vchtype,c.source_vchcode,c.source_business_type,c.source_detail_id,
        c.ptype_preferential_total,
        c.source_number as source_bill_number,pt.usercode,pt.fullname,pt.barcode,i.unit_qty,
        i.currency_price,i.currency_total,i.currency_dised_price,i.currency_dised_total,i.discount,i.currency_order_preferential_allot_total,
        i.currency_completed_preferential_share, i.currency_tax_total as currency_tax_total,
        c.refund_qty,c.refund_sub_qty,i.refund_order_preferential_allot_total,c.ptype_commission_total,
        i.currency_dised_taxed_price,i.currency_dised_taxed_total,tbdb.batchno,tbdb.produce_date,tbdb.expire_date,i.unit_rate,i.unit_id,bpu.unit_name,i.sub_unit_name,c.sub_qty,
        c.completed_sub_qty,
        bs.fullname as kfullname,bs.fullname as kfullname,bs.scategory,0 as stock_type,0 as stock_state,
        pt.protect_days,pt.propenabled, pt.snenabled,pt.batchenabled,pt.standard,pt.ptype_type,pt1.fullname as
        ptypeClass,bb.brand_name, c.sku_barcode fullbarcode,bpx.xcode,i.combo_share_scale AS comboShareScale,
        i.currency_order_fee_allot_total as currencyOrderFeeAllotTotal ,pt.pcategory,i.discount_source_type,
        pt.ptype_length,pt.ptype_width,pt.ptype_height,pt.weight,pt.length_unit,pt.weight_unit,sku.pic_url as
        sku_pic_url,pt.sku_price,pt.cost_mode,c.cost_id,
        cfb.custom_field01,cfb.custom_field02,cfb.custom_field03,cfb.custom_field04,cfb.custom_field05,cfb.custom_field06,
        cfb.custom_field07,cfb.custom_field08,cfb.custom_field09,cfb.custom_field10,cfb.custom_field11,cfb.custom_field12,cfb.custom_field13,
        custom.custom_head01,custom.custom_head02,custom.custom_head03,custom.custom_head04,custom.custom_head05,
        custom.custom_head06,custom.custom_head07,custom.custom_head08,custom.custom_head09,custom.custom_head10,
        custom.custom_head11,custom.custom_head12,custom.custom_head13,custom.custom_head14,
        IFNULL(SUM(abid.qty),0) as
        inOutQty,pt.shortname,pt.ptype_area,i.service_start_time,i.service_end_time,i.service_expire_time,i.product_account_id,i.ptype_commission_rate,
        abs(tbdp.purchase_price) purchasePrice,abs(tbdp.purchase_total) purchaseTotal,
        fin.invoice_total as invoiceTotal,(fin.dised_taxed_total-fin.invoice_total) as
        unInvoiceTotal,i.currency_give_preferential_total,c.give_preferential_total,
        CASE (IFNULL(tba.share_type,0) + IFNULL(tba.freight_share_type,0)) WHEN 0 THEN 0 ELSE 3 END AS
        isShare,c.ktype_point_id,c.ktype_point_type,
        car.id as car_id,car.car_number,driver.id as driver_id,driver.fullname as
        drfullname,tbddb.buyer_dised_taxed_price,tbddb.buyer_dised_taxed_total,oc.account_name as
        productAccountName,tbde.ptype_standard,tbde.ptype_type as zyPtypeType,
        tbdc.combo_id, pt.typeid
        <choose>
            <when test="vchtype!=null and (vchtype==1100 or vchtype==2100)">
                ,cfb.custom_field11
            </when>
            <otherwise>
                ,tode.unit_id as orderUnitId,tode.unit_rate as orderUnitRate,tode.unit_qty as
                orderUnitQty,tode.completed_qty as orderCompletedQty
            </otherwise>
        </choose>
        <if test="vchtype!=null and (vchtype==1100 or vchtype==2100)">
            ,toc.bill_date as source_bill_date,toc.period as source_detail_period
        </if>
        <if test="btypeId!=null">
            ,pu.usercode as bUserCode
        </if>
        <!--库存查询，分别查询，sku库存表和sku库存占用表，将两者库存相减就是sku当前库存-->
        <if test="ktypeId!=null and ktypeId!=''">
            ,(IFNULL(agd.qty,0) - IFNULL(srqs.qty,0)) as stockQty
        </if>
        from td_bill_detail_core c
        LEFT JOIN td_bill_detail_extend tbde on tbde.vchcode = c.vchcode and tbde.detail_id = c.detail_id and
        tbde.profile_id = #{profileId}
        left join td_bill_detail_batch tbdb on tbdb.detail_id = c.detail_id and tbdb.vchcode = c.vchcode
        and tbdb.profile_id=#{profileId}
        left join td_bill_detail_combo tbdc on tbdc.vchcode = c.vchcode and tbdc.id = c.combo_detail_id
        and tbdc.profile_id = #{profileId}
        left join td_bill_detail_distribution_buyer tbddb on tbddb.detail_id = c.detail_id and tbddb.vchcode = c.vchcode
        and tbddb.profile_id=#{profileId}
        LEFT JOIN td_bill_share_fee_to_detail tdd
        on tdd.target_detail_id=c.detail_id and tdd.profile_id=#{profileId}
        LEFT JOIN td_bill_assinfo tba
        on tba.vchcode = c.vchcode and tba.profile_id = #{profileId}
        left join td_bill_detail_assinfo i on c.detail_id = i.detail_id and c.vchcode = i.vchcode and i.profile_id =
        #{profileId}
        left join td_bill_detail_purchase as tbdp on tbdp.vchcode = c.vchcode and tbdp.detail_id = c.detail_id and
        tbdp.profile_id=#{profileId}
        left join base_ptype pt on c.ptype_id = pt.id and pt.profile_id = #{profileId}
        left join base_ptype pt1 on pt1.typeid = left(pt.typeid,5) and pt1.profile_id = #{profileId}
        LEFT JOIN `base_ptype_unit` base_bpu ON base_bpu.profile_id = #{profileId} AND base_bpu.ptype_id = c.ptype_id
        AND unit_code = 1
        LEFT JOIN `base_ptype_fullbarcode` base_pf ON base_pf.profile_id = #{profileId} AND base_pf.ptype_id =
        c.ptype_id AND base_pf.unit_id = base_bpu.id AND c.sku_id = base_pf.sku_id AND base_pf.defaulted=1
        left join base_ptype_pic bpp on pt.id = bpp.ptype_id and bpp.profile_id = #{profileId} AND bpp.rowindex = 1
        LEFT JOIN base_ptype_sku sku ON c.sku_id = sku.id AND c.ptype_id = sku.ptype_id AND sku.profile_id=#{profileId}
        LEFT JOIN base_ptype_fullbarcode bpf ON c.sku_id = bpf.sku_id AND c.ptype_id = bpf.ptype_id AND i.unit_id =
        bpf.unit_id AND
        bpf.profile_id=#{profileId} AND bpf.defaulted=1
        left join base_ptype_xcode bpx on c.sku_id = bpx.sku_id AND c.ptype_id = bpx.ptype_id AND i.unit_id =
        bpx.unit_id AND
        bpx.profile_id=#{profileId} AND bpx.defaulted=1
        left join base_brandtype bb on pt.brand_id = bb.id and bb.profile_id = #{profileId}
        LEFT JOIN base_ktype bs on c.ktype_id = bs.id and bs.profile_id = #{profileId}
        LEFT join base_ptype_unit bpu on i.unit_id = bpu.id and bpu.profile_id = #{profileId}
        LEFT JOIN acc_bill_core tabc ON tabc.profile_id=#{profileId} AND tabc.vchcode=c.source_vchcode
        <choose>
            <when test="vchtype!=null and (vchtype==1100 or vchtype==2100)">
                left join acc_bill_core toc on toc.profile_id=#{profileId} and toc.vchcode=c.source_vchcode and
                toc.vchtype=c.source_vchtype
            </when>
            <otherwise>
                left join td_orderbill_core toc on toc.profile_id=#{profileId} and toc.vchcode=c.source_vchcode and
                toc.vchtype=c.source_vchtype
                left join td_orderbill_detail_assinfo tode on tode.profile_id=#{profileId}
                and tode.vchcode = c.source_vchcode and tode.detail_id = c.source_detail_id
            </otherwise>
        </choose>
        <if test="btypeId!=null">
            LEFT JOIN base_ptype_usercodes pu ON pu.ptype_id = c.ptype_id AND pu.btype_id = #{btypeId} AND pu.profile_id
            = #{profileId}
        </if>
        <!--库存查询，分别查询，sku库存表和sku库存占用表，将两者库存相减就是sku当前库存-->
        <if test="ktypeId!=null and ktypeId!=''">
            left join acc_goodsstock_detail agd on agd.profile_id = #{profileId}
            and agd.sku_id =sku.id and agd.ktype_id = #{ktypeId}
            left join stock_record_qty_sale srqs on srqs.profile_id = #{profileId}
            and srqs.sku_id = sku.id and srqs.ktype_id = #{ktypeId}
        </if>
        left join pub_custom_field_billdetail cfb on c.detail_id = cfb.detail_id and c.vchcode = cfb.vchcode and
        cfb.profile_id = #{profileId}
        LEFT JOIN pub_custom_field_baseinfo custom ON custom.sub_type=5001 AND custom.xtype_id=pt.id AND
        custom.profile_id=#{profileId}
        LEFT JOIN fin_invoice_bill_detail fin ON c.vchcode = fin.vchcode AND c.detail_id = fin.detail_id AND
        fin.profile_id=#{profileId}
        LEFT JOIN td_bill_inout_detail abid ON c.vchcode = abid.vchcode AND c.detail_id = abid.detail_id AND
        abid.ktype_point_type = 0 AND abid.inout_detail_type != 9 AND abid.profile_id=#{profileId}
        LEFT JOIN base_car car on car.id = tba.car_id and car.profile_id=#{profileId}
        LEFT JOIN base_etype driver on driver.id = tba.driver_id and driver.profile_id=#{profileId}
        left join ops_cpa oc on oc.id = i.product_account_id and oc.profile_id = #{profileId}
        where c.vchcode = #{vchcode} and c.profile_id=#{profileId} group by c.detail_id
        order by c.row_index
    </select>

    <select id="getDetailByVchcode" resultType="com.wsgjp.ct.sale.biz.bill.validation.dao.BillGoodsStockDAO">
        select s.detail_id,s.ktype_id,k.`fullname` kfullname,s.ptype_id,p.`fullname` pfullname,p.usercode
        ptypeusercode,s.qty,s.ktype_point_id,s.ktype_point_type,
        a.unit_qty ,a.unit_rate,p.propenabled,p.batchenabled,s.batchno,s.sku_id,s.produce_date,s.expire_date,
        bp.protect_days, s.sku_barcode as fullbarcode,s.cost_id,p.cost_mode,s.batch_price
        from td_bill_detail_core s
        join `td_bill_detail_assinfo` a ON a.`profile_id` = #{profileId} and a.detail_id = s.detail_id
        left join `base_ptype` p ON p.`profile_id` = #{profileId} and s.ptype_id = p.id
        left join `base_ktype` k ON p.`profile_id` = #{profileId} and s.ktype_id = k.id
        left join base_ptype bp on bp.id=s.ptype_id and bp.profile_id=#{profileId}
        where s.profile_id = #{profileId} and s.vchcode = #{vchcode} and p.pcategory <![CDATA[ <> ]]> 1
        <if test="inoutType >=0">
            and s.inout_type = #{inoutType}
        </if>
    </select>
</mapper>
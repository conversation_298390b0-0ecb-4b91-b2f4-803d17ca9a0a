<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopProductMapper">
    <sql id="skuRelationOpenXcode">
        psm.id,
        psm.platform_num_id ,
        psm.profile_id,
		psm.platform_sku_id,
		psm.platform_properties_name,
		psm.platform_xcode,
		psm.platform_properties,
		psm.platform_pic_url,
		psm.platform_json,
		psm.platform_modified_time,
		expand.mark,
		expand.mapping_type,
		psm.qty,
		psm.platform_modified_time as modified_time,
		psm.update_time,
		psm.create_time,
		psm.unique_id,
        psm.eshop_id,
        pm.platform_fullname as platfullname,
        pm.default_sku_id,
        (case when (ifnull(bpx.id,0)=0 or p.deleted =1 or p.stoped =1) then false else true end) as isbind,
		bpx.ptype_id,
           bpx.sku_id,
           unit.id as unitId,
           ifnull(unit.unit_rate,1) as unitRate,
           bpx.xcode,
           baseunit.id as baseUnitId,
           p.pcategory,
           p.fullname as ptypeName
    </sql>
    <sql id="comboRelationOpenXcode">
        psm.id,
        psm.platform_num_id,
		psm.profile_id,
	       psm.platform_sku_id,
	       psm.platform_properties_name,
	       psm.platform_xcode,
	       psm.platform_properties,
	       psm.platform_pic_url,
	       psm.platform_json,
	       psm.platform_modified_time,
	       expand.mark,
	       expand.mapping_type,
	       psm.qty,
	       psm.update_time,
	       psm.create_time,
	       psm.unique_id,
	       psm.eshop_id,
	       pm.platform_fullname as platfullname,
	       p.pcategory,
	      (case when ( p.deleted =1 or p.stoped =1 or IFNULL(p.id,0)=0) then false else true end)
		as isbind,
	       p.id as ptype_id,
	       1 as unitRate,
	       p.usercode as xcode,
	       p.fullname as ptypeName
    </sql>
    <sql id="comboRelation">
        psm.id,
        psm.profile_id,
		expand.ready_sku_name,
		expand.ready_sku_xcode,
		expand.ready_pfullname,
		expand.ready_pusercode,
		mapping.ptype_id,
		mapping.unit_id,
		mapping.sku_id,
		psm.gift_id,
	       psm.platform_sku_id,
	       psm.platform_num_id,
	       psm.platform_properties_name,
	       psm.platform_xcode,
	       psm.platform_properties,
	       psm.platform_pic_url,
	       psm.platform_json,
	       psm.platform_modified_time,
	       expand.mark,
	       psm.qty,
	       psm.update_time,
	       psm.create_time,
	       psm.unique_id,
	       psm.eshop_id,pepsrc.rule_id as sync_rule_id,(case when ( p.deleted =1 or p.stoped =1 or IFNULL(p.id,0)=0) then false else true end)
		as isbind,pm.platform_fullname,pm.platform_fullname as platFullname,p.fullname as ptypeName,
		1 as unitRate, bpx.xcode as xcode,p.pcategory, p.barcode AS barcode,
		p.tax_rate as taxRate,
        p.cost_mode as costMode,
        (case when (temp.labelfieldValue = '代销') then true else false end)  as saleProxyLabel,
        bpc.sale_type as  saleType
    </sql>
    <sql id="skuRelation">
        psm.profile_id,
        psm.id,
		expand.ready_sku_name,
		expand.ready_sku_xcode,
		expand.ready_pfullname,
		expand.ready_pusercode,
		mapping.ptype_id,
		mapping.unit_id,
		mapping.sku_id,
		psm.gift_id,
	       psm.platform_sku_id,
	       psm.platform_num_id,
	       psm.platform_properties_name,
	       psm.platform_xcode,
	       psm.platform_properties,
	       psm.platform_pic_url,
	       psm.platform_json,
	       psm.platform_modified_time,
	       expand.mark,
	       psm.qty,
	       psm.platform_modified_time as modified_time,
	       psm.update_time,
	       psm.create_time,
	       psm.unique_id,
	       psm.eshop_id,pepsrc.rule_id as sync_rule_id,(case when ( p.deleted =1 or p.stoped =1 or IFNULL(p.id,0)=0) then false  else true end)
        as isbind,pm.default_sku_id,pm.platform_fullname,pm.platform_fullname as platFullName,bpx.xcode,p.fullname as ptypeName,unit.id as
        unitId,unit.unit_name,
		ifnull(unit.unit_rate,1) as unitRate,unit.id as baseUnitId,p.pcategory,
		mark.is_send_by_days,mark.pre_send_by_days,mark.is_send_on_date,mark.pre_send_on_date, bpf.fullbarcode AS fullbarcode,
		p.tax_rate as taxRate,
        p.cost_mode as costMode,
        (case when (temp.labelfieldValue = '代销') then true else false end)  as saleProxyLabel
    </sql>

    <sql id="skuRelationQueryFields">
        sku.id,sku.profile_id,sku.eshop_id,
        prd.platform_fullname as platName,
        sku.platform_num_id,
        sku.platform_sku_id,
        sku.platform_xcode as platXcode,
        sku.platform_properties_name,
        sku.platform_pic_url,
        IF(IFNULL(p.id,0)=0, false, true) as isBind,
        p.fullname as ptypeName,p.id as ptypeId,
        p.usercode,
        bpx.xcode,
        p.pcategory,p.pcategory as mappingDetailType,
        ps.propvalue_name1,ps.propvalue_name2,
        ps.propvalue_name3,ps.propvalue_name4,
        ps.propvalue_name5,ps.propvalue_name6,
        ps.prop_name1,ps.prop_name2,ps.prop_name3,
        ps.prop_name4,ps.prop_name5,ps.prop_name6,
        unit.unit_name,
        case p.pcategory when 2 then ifnull(p.barcode,'') else ifnull(bar.fullbarcode,'') end as skuBarcode
    </sql>
    <sql id="skuRelationQueryCondition">
        <if test="filterString !='' and queryType!=null and queryType==1">
            and (prd.platform_fullname like CONCAT('%',#{filterString},'%') or prd.platform_xcode like
            CONCAT('%',#{filterString},'%') or
            sku.platform_properties_name like CONCAT('%',#{filterString},'%') or sku.platform_xcode like
            CONCAT('%',#{filterString},'%'))
        </if>
        <if test="filterString !='' and queryType!=null and queryType==2">
            and (p.fullname like CONCAT('%',#{filterString},'%') or p.usercode like CONCAT('%',#{filterString},'%'))
        </if>
        <if test="filterString !='' and queryType!=null and queryType==3">
            and prd.platform_fullname like CONCAT('%',#{filterString},'%')
        </if>
        <if test="filterString !='' and queryType!=null and queryType==4">
            and sku.platform_properties_name like CONCAT('%',#{filterString},'%')
        </if>
        <if test="filterString !='' and queryType!=null and queryType==5">
            and prd.platform_xcode like CONCAT('%',#{filterString},'%')
        </if>
        <if test="filterString !='' and queryType!=null and queryType==6">
            and sku.platform_xcode like CONCAT('%',#{filterString},'%')
        </if>
        <if test="filterString !='' and queryType!=null and queryType==7">
            and p.fullname like CONCAT('%',#{filterString},'%')
        </if>
        <if test="filterString !='' and queryType!=null and queryType==8">
            and p.usercode like CONCAT('%',#{filterString},'%')
        </if>
        <if test="filterString !='' and queryType!=null and queryType==9">
            and bpx.xcode like CONCAT('%',#{filterString},'%')
        </if>

        <if test="eshopIds !=null and eshopIds.size>0">
            and sku.eshop_id in
            <foreach collection="eshopIds" item="id" index="i" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="otypeId!=null and otypeId>0">
            and sku.eshop_id = #{otypeId}
        </if>
    </sql>

    <sql id="skuRelationQueryAdditionalCondition">
        <if test="platformPropertiesNames !=null and platformPropertiesNames.size>0">
            and sku.platform_properties_name in
            <foreach collection="platformPropertiesNames" item="propname" index="i" separator="," open="(" close=")">
                #{propname}
            </foreach>
        </if>
        <if test="platformNumIds !=null and platformNumIds.size>0">
            and sku.platform_num_id in
            <foreach collection="platformNumIds" item="numId" index="i" separator="," open="(" close=")">
                #{numId}
            </foreach>
        </if>
    </sql>



    <sql id="skuAndComboRelationOpenXcode">
        psm.profile_id,
        psm.id,
        bpx.ptype_id,
        ifnull(unit.id,0) as unit_id,
        bpx.sku_id,
        psm.platform_sku_id,
        psm.platform_num_id,
        psm.platform_properties_name,
        psm.platform_xcode,
        psm.platform_properties,
        psm.eshop_id,
        (case when IFNULL(p.id,0)=0 then false else true end) as isbind,
        pm.default_sku_id,
        pm.platform_fullname as platFullName,
        bpx.xcode,
        p.fullname as ptypeName
    </sql>

    <sql id="skuAndComboRelation">
        psm.profile_id,
        psm.id,
		mapping.ptype_id,
		ifnull(mapping.unit_id,0) as unit_id,
		mapping.sku_id,
        psm.platform_sku_id,
        psm.platform_num_id,
        psm.platform_properties_name,
        psm.platform_xcode,
        psm.platform_properties,
        psm.eshop_id,
        (case when IFNULL(p.id,0)=0 then false else true end) as isbind,
        pm.default_sku_id,
        pm.platform_fullname as platFullName,
        bpx.xcode,
        p.fullname as ptypeName
    </sql>


    <sql id="skuMappingJoinSqlByOpenXcode">
        join pl_eshop_product pm on pm.profile_id=psm.profile_id and pm.eshop_id=psm.eshop_id and pm.platform_num_id=psm.platform_num_id
        join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=expand.profile_id and expand.mapping_type=1
        left join base_ptype_xcode bpx on px.xcode=psm.platform_xcode and px.profile_id=psm.profile_id
        left join base_ptype_unit unit on unit.profile_id=bpx.profile_id and unit.id=bpx.unit_id and unit.unit_type=0
        left join base_ptype p on px.profile_id = p.profile_id and px.ptype_id=p.id and p.deleted = 0 and p.stoped = 0
    </sql>
    <sql id="productSkuMappingJoinSql">
        join pl_eshop_product pm on pm.profile_id=psm.profile_id and pm.eshop_id=psm.eshop_id and
        pm.platform_num_id=psm.platform_num_id
        join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and
        psm.profile_id=expand.profile_id and expand.mapping_type=0
        left join pl_eshop_product_sku_mapping mapping on mapping.profile_id= psm.profile_id and mapping.unique_id =
        psm.unique_id and mapping.eshop_id = psm.eshop_id
        left join base_ptype_unit unit on unit.profile_id=psm.profile_id and unit.id=mapping.unit_id
    </sql>
    <sql id="subLeftJoinCondition">
        select cdlp.profile_id      as profileId,
               cdlp.resource_id     as ptypeId,
               clv.labelfield_value as labelfieldValue
        from cf_data_label_ptype cdlp
                 left join Cf_Labelfield_Value clv
                           on clv.id = cdlp.labelfield_value_id
                               and clv.profile_id = cdlp.profile_id
        where clv.labelfield_value = '代销'
          and cdlp.profile_id = #{profileId}
    </sql>
    <sql id="productSkuMappingWhereCondition">
        <choose>
            <when test="uniqueIdList!=null and uniqueIdList.size()>0">
                and psm.unique_id in
                <foreach collection="uniqueIdList" close=")" open="(" separator="," item="uniqueId">
                    #{uniqueId}
                </foreach>
            </when>
            <otherwise>
                <if test="platformNumIdList!=null and platformNumIdList.size()>0">
                    and psm.platform_num_id in
                    <foreach collection="platformNumIdList" item="numId" index="i" separator="," open="(" close=")">
                        #{numId}
                    </foreach>
                </if>
                <if test="platformSkuIdList!=null and platformSkuIdList.size()>0">
                    and psm.platform_sku_id in
                    <foreach collection="platformSkuIdList" item="skuId" index="i" separator="," open="(" close=")">
                        #{skuId}
                    </foreach>
                </if>
                <if test="xcode!=null and xcode!=''">
                    and psm.platform_xcode=#{xcode}
                </if>
                <if test="otypeId!=null and otypeId!=''">
                    and psm.eshop_id=#{otypeId}
                </if>
            </otherwise>
        </choose>
    </sql>


    <sql id="ptypeRelationPageQuery_ByMappingValue_selectClause">
        SELECT DISTINCT psm.platform_num_id FROM pl_eshop_product_sku psm
        left join pl_eshop_product_sku_expand expand on expand.unique_id = psm.unique_id and expand.profile_id=
        psm.profile_id and expand.eshop_id = psm.eshop_id
        left join pl_eshop_product_sku_mapping mapping on mapping.unique_id = psm.unique_id and mapping.profile_id=
        psm.profile_id and mapping.eshop_id = psm.eshop_id
        left join base_ptype p on p.id=mapping.ptype_id and p.profile_id=psm.profile_id and p.deleted=0 and
        p.stoped=0 and p.classed=0
        left join base_ptype_xcode bpx on bpx.profile_id=mapping.profile_id and bpx.ptype_id=mapping.ptype_id and
        bpx.sku_id=mapping.sku_id and bpx.unit_id=mapping.unit_id
        <if test="filterString!=null and filterString!=''">
            left join pl_eshop_product pm on pm.profile_id=psm.profile_id and
            pm.platform_num_id=psm.platform_num_id
            and pm.eshop_id = psm.eshop_id
        </if>
    </sql>

    <sql id="ptypeRelationPageQuery_ByMappingValue_whereClause">
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId} and expand.mapping_type=0 and
        psm.storage_type=0
        <if test="platformNumIds!=null">
            and psm.platform_num_id in
            <foreach collection="platformNumIds" item="item" index="i" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="markList !=null and markList.size()>0 ">
            and exists(select 1 from pl_eshop_product_mark m where m.profile_id=#{profileId} and
            m.eshop_id=#{otypeId}
            and psm.unique_id =m.unique_id and m.mark_code in
            <foreach collection="markList" item="mark" index="i" separator="," open="(" close=")">
                #{mark}
            </foreach>)
        </if>
        <if test="mark != null">
            and expand.mark=#{mark}
        </if>
        <if test="filterString !='' and queryType!=null and queryType==1">
            and (pm.platform_fullname like CONCAT('%',#{filterString},'%') or pm.platform_xcode like
            CONCAT('%',#{filterString},'%') or
            psm.platform_properties_name like CONCAT('%',#{filterString},'%') or psm.platform_xcode like
            CONCAT('%',#{filterString},'%'))
        </if>
        <if test="filterString !='' and queryType!=null and queryType==2">
            and (p.fullname like CONCAT('%',#{filterString},'%') or p.usercode like CONCAT('%',#{filterString},'%'))
        </if>
        <if test="filterString !='' and queryType!=null and queryType==3">
            and pm.platform_fullname like CONCAT('%',#{filterString},'%')
        </if>
        <if test="filterString !='' and queryType!=null and queryType==4">
            and psm.platform_properties_name like CONCAT('%',#{filterString},'%')
        </if>
        <if test="filterString !='' and queryType!=null and queryType==5">
            and pm.platform_xcode like CONCAT('%',#{filterString},'%')
        </if>
        <if test="filterString !='' and queryType!=null and queryType==6">
            and psm.platform_xcode like CONCAT('%',#{filterString},'%')
        </if>
        <if test="filterString !='' and queryType!=null and queryType==7">
            and p.fullname like CONCAT('%',#{filterString},'%')
        </if>
        <if test="filterString !='' and queryType!=null and queryType==8">
            and p.usercode like CONCAT('%',#{filterString},'%')
        </if>
        <if test="filterString !='' and queryType!=null and queryType==9">
            and bpx.xcode like CONCAT('%',#{filterString},'%')
        </if>

        <if test="xcodeState!=2 and xcodeState!=null">
            and expand.update_xcode_status=#{xcodeState}
        </if>
        <if test="platformXcodeIsNull!=0 and platformXcodeIsNull==1">
            and psm.platform_xcode=''
        </if>
        <if test="platformXcodeIsNull!=0 and platformXcodeIsNull==2">
            and psm.platform_xcode !=''
        </if>
        <if test="platformXcodeIsNull!=0 and platformXcodeIsNull==4">
            and (ifnull(bpx.xcode,'')!=psm.platform_xcode and p.id is not null)
        </if>

        <if test="mappingTypeState !=null and mappingTypeState!=2">
            and expand.mapping_type=#{mappingTypeState}
        </if>
        <if test="qtyState==1">
            and psm.qty>0
        </if>
        <if test="qtyState==2">
            and psm.qty &lt; 1
        </if>
        <if test="maxPrice != null">
            and psm.platform_price &lt;= #{maxPrice}
        </if>
        <if test="minPrice != null">
            and psm.platform_price &gt;= #{minPrice}
        </if>
        <if test="platformNumId != null and platformNumId !=''">
            and psm.platform_num_id= #{platformNumId}
        </if>
        <if test="platformSkuId != null and platformNumId !=''">
            and psm.platform_sku_id= #{platformSkuId}
        </if>
        <if test="relationState==1">
            and ((case when (ifnull(p.id,0)=0 or p.deleted =1 or p.stoped =1) then false else true end)=false )
        </if>
        <if test="relationState==2">
            and ((case when (ifnull(p.id,0)=0 or p.deleted =1 or p.stoped =1) then false else true end)=true )
        </if>

        <if test="startDownloadOrderIntervalDay != null and endDownloadOrderIntervalDay!=null">
            and ifnull(datediff(now(), expand.last_new_order_time), -1) between #{startDownloadOrderIntervalDay} and #{endDownloadOrderIntervalDay}
        </if>
        <if test="startRefreshProductIntervalDay != null and endRefreshProductIntervalDay!=null">
            and ifnull(datediff(now(), psm.update_time), -1) between #{startRefreshProductIntervalDay} and #{endRefreshProductIntervalDay}
        </if>
        <if test="platformXcodeIsNull!=0 and platformXcodeIsNull==3">
            and psm.platform_xcode in (select platform_xcode from pl_eshop_product_sku where profile_id=#{profileId}
            and eshop_id=#{otypeId} and storage_type=0 and platform_xcode is not null and platform_xcode!=''
            group by platform_xcode having count(1)>1)
        </if>
    </sql>

    <sql id="ptypeRelationPageQuery_ByXcode_selectClause">
        SELECT distinct psm.platform_num_id FROM pl_eshop_product_sku psm
        left join (select bpx.id,bpx.ptype_id,bpx.xcode,bpx.profile_id from base_ptype_xcode bpx
        left join base_ptype bp on bp.id = bpx.ptype_id and bp.profile_id = bpx.profile_id
        where bpx.profile_id = #{profileId} and bp.deleted = 0 and bp.stoped = 0 and bpx.unit_id not in(select id
        from base_ptype_unit where profile_id=#{profileId} and unit_type=1)) px on px.xcode=psm.platform_xcode and
        px.profile_id=psm.profile_id
        LEFT JOIN base_ptype p3 ON p3.id = px.ptype_id AND p3.profile_id = psm.profile_id and p3.deleted =0 and
        p3.stoped =0 and p3.classed=0
        left join pl_eshop_product_sku_expand expand on expand.unique_id = psm.unique_id and expand.profile_id=
        psm.profile_id and expand.eshop_id = psm.eshop_id
        <if test="filterString!=null and filterString!=''">
            left join pl_eshop_product pm on pm.profile_id=psm.profile_id and
            pm.platform_num_id=psm.platform_num_id
            and pm.eshop_id = psm.eshop_id
        </if>
    </sql>

    <sql id="ptypeRelationPageQuery_ByXcode_whereClause">
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId} and expand.mapping_type=1 and
        psm.storage_type=0
        <if test="platformNumIds!=null">
            and psm.platform_num_id in
            <foreach collection="platformNumIds" item="item" index="i" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="markList !=null and markList.size()>0 ">
            and exists(select 1 from pl_eshop_product_mark m where m.profile_id=#{profileId} and
            m.eshop_id=#{otypeId}
            and psm.unique_id =m.unique_id and m.mark_code in
            <foreach collection="markList" item="mark" index="i" separator="," open="(" close=")">
                #{mark}
            </foreach>)
        </if>
        <if test="mark != null">
            and expand.mark=#{mark}
        </if>
        <if test="filterString !='' and queryType!=null and queryType==1">
            and (pm.platform_fullname like CONCAT('%',#{filterString},'%') or pm.platform_xcode like
            CONCAT('%',#{filterString},'%') or
            psm.platform_properties_name like CONCAT('%',#{filterString},'%') or psm.platform_xcode like
            CONCAT('%',#{filterString},'%'))
        </if>
        <if test="filterString !='' and queryType!=null and queryType==2">
            and (p3.fullname like CONCAT('%',#{filterString},'%') or p3.usercode like
            CONCAT('%',#{filterString},'%'))
        </if>
        <if test="filterString !='' and queryType!=null and queryType==3">
            and pm.platform_fullname like CONCAT('%',#{filterString},'%')
        </if>
        <if test="filterString !='' and queryType!=null and queryType==4">
            and psm.platform_properties_name like CONCAT('%',#{filterString},'%')
        </if>
        <if test="filterString !='' and queryType!=null and queryType==5">
            and pm.platform_xcode like CONCAT('%',#{filterString},'%')
        </if>
        <if test="filterString !='' and queryType!=null and queryType==6">
            and psm.platform_xcode like CONCAT('%',#{filterString},'%')
        </if>
        <if test="filterString !='' and queryType!=null and queryType==7">
            and p3.fullname like CONCAT('%',#{filterString},'%')
        </if>
        <if test="filterString !='' and queryType!=null and queryType==8">
            and p3.usercode like CONCAT('%',#{filterString},'%')
        </if>
        <if test="filterString !='' and queryType!=null and queryType==9">
            and px.xcode like CONCAT('%',#{filterString},'%')
        </if>

        <if test="xcodeState!=2 and xcodeState!=null">
            and expand.update_xcode_status=#{xcodeState}
        </if>
        <if test="platformXcodeIsNull!=0 and platformXcodeIsNull==1">
            and psm.platform_xcode=''
        </if>
        <if test="platformXcodeIsNull!=0 and platformXcodeIsNull==2">
            and psm.platform_xcode !=''
        </if>

        <if test="mappingTypeState!=null and mappingTypeState!=2">
            and expand.mapping_type=#{mappingTypeState}
        </if>
        <if test="qtyState==1">
            and psm.qty>0
        </if>
        <if test="qtyState==2">
            and psm.qty &lt; 1
        </if>
        <if test="maxPrice != null">
            and psm.platform_price &lt;= #{maxPrice}
        </if>
        <if test="minPrice != null">
            and psm.platform_price &gt;= #{minPrice}
        </if>
        <if test="platformNumId != null and platformNumId !=''">
            and psm.platform_num_id= #{platformNumId}
        </if>
        <if test="platformSkuId != null and platformNumId !=''">
            and psm.platform_sku_id= #{platformSkuId}
        </if>
        <if test="relationState==1">
            and ifnull(p3.id,0)=0
        </if>
        <if test="relationState==2">
            and psm.platform_xcode !='' and ((case when (ifnull(px.id,0)=0) then false else true end)=true)

        </if>
        <if test="platformXcodeIsNull!=0 and platformXcodeIsNull==4">
            and (psm.platform_xcode !=px.xcode and px.id is not null)
        </if>
        <if test="startDownloadOrderIntervalDay != null and endDownloadOrderIntervalDay!=null">
            and ifnull(datediff(now(), expand.last_new_order_time), -1) between #{startDownloadOrderIntervalDay} and
            #{endDownloadOrderIntervalDay}
        </if>
        <if test="startRefreshProductIntervalDay != null and endRefreshProductIntervalDay!=null">
            and ifnull(datediff(now(), psm.update_time), -1) between #{startRefreshProductIntervalDay} and
            #{endRefreshProductIntervalDay}
        </if>
        <if test="platformXcodeIsNull!=0 and platformXcodeIsNull==3">
            and psm.platform_xcode in (select platform_xcode from pl_eshop_product_sku where profile_id=#{profileId}
            and eshop_id=#{otypeId} and storage_type=0 and platform_xcode is not null and platform_xcode!=''
            group by platform_xcode having count(1)>1)
        </if>
    </sql>
    <sql id="ptypeRelationSkuQuerySql_byXcodeCombo">
        select (case when (ifnull(p.id,0)=0 or p.deleted =1 or p.stoped =1) then false else true end) as isbind,
        psm.id,psm.platform_unit_name,pm.profile_id,pm.eshop_id,p.id as ptype_id,pm.category_id,psm.platform_pic_url as
        picUrl,psm.platform_num_id,pm.platform_alias,psm.platform_price as price,psm.unique_id,
        psm.platform_properties_name,psm.platform_full_properties,psm.memo_platform_full_properties as
        hasMemoFullPropertiesName,pepsrc.rule_id as sync_rule_id,rule.rule_name as
        ruleName, pm.platform_fullname as
        platname,psm.platform_sku_id,psm.platform_xcode as
        platxcode,pm.platform_xcode as pmXcode,concat('',p.fullname) as ptypeName,p.barcode as
        ptypeBarcode,p.barcode as skuBarcode,psm.platform_xcode, p.standard,p.ptype_type,p.ptype_area,b.brand_name,
        '' as pid,false as isptype,bpx.xcode,p.usercode as usercode,psm.platform_sku_id as
        relationid,psm.platform_properties,psm.platform_properties as properties,
        expand.ready_sku_xcode,expand.ready_pfullname,expand.ready_pusercode,expand.mapping_type,
        p.pcategory as mappingDetailType,p.propenabled,p.pcategory,pic.pic_url as localPicUrl,
        ifnull(datediff(now(),psm.update_time),-1) as refreshProductIntervalDay,
        ifnull(datediff(now(),expand.last_new_order_time),-1) as downloadOrderIntervalDay,
        ifnull(p.update_time,now())as modifiedtime,pm.platform_stock_state as stock_state,'' as unit_name,bpx.unit_id as
        unitId,bpx.sku_id as skuId,'' as
        propvalue_name1,'' as propvalue_name2,psm.qty,expand.mark,expand.update_xcode_status,1 as
        unitRate,if(p.sku_price=0,p.cost_price,bps.cost_price) as costPrice
        from pl_eshop_product_sku psm
        LEFT JOIN pl_eshop_product pm on pm.platform_num_id=psm.platform_num_id and pm.profile_id=psm.profile_id
        and pm.eshop_id =psm.eshop_id
        LEFT JOIN pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and expand.profile_id=
        psm.profile_id and expand.eshop_id =psm.eshop_id
        LEFT JOIN pl_eshop_product_sku_rule_config pepsrc
        on pepsrc.profile_id=psm.profile_id
        and pepsrc.platform_num_id=psm.platform_num_id and pepsrc.eshop_id=psm.eshop_id
        and pepsrc.platform_properties=psm.platform_properties_name and pepsrc.warehouse_code Is NULL
        LEFT JOIN base_ptype_xcode bpx on bpx.xcode=psm.platform_xcode and bpx.profile_id=psm.profile_id and
        bpx.info_type=1
        LEFT JOIN base_ptype p on p.id=bpx.ptype_id and p.profile_id=bpx.profile_id and p.classed=0
        LEFT JOIN base_ptype_sku bps on bps.id=bpx.sku_id and bps.profile_id=bpx.profile_id
        LEFT JOIN base_brandtype b ON p.profile_id = b.profile_id AND p.brand_id = b.id
        LEFT JOIN base_ptype_pic pic on pic.ptype_id=p.id and pic.profile_id=p.profile_id and pic.rowindex=1
        LEFT JOIN pl_eshop_stock_sync_rule rule on rule.id=pepsrc.rule_id and rule.profile_id=psm.profile_id and
        rule.deleted=0
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId} and p.pcategory=2 and p.usercode is not null and
        p.usercode !='' and expand.mapping_type=1 and psm.storage_type=0
        and p.stoped=0 and p.deleted =0
        <if test="platformNumIds!=null and platformNumIds.size()>0">
            and psm.platform_num_id in
            <foreach collection="platformNumIds" item="item" index="i" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>
    <sql id="ptypeRelationSkuQuerySql_byXcodeCombo_Where">
        <where>
            <if test="mark>0">
                and r.mark=#{mark}
            </if>
            <if test="filterString!=''  and queryType==1">
                and (r.platName like "%"#{filterString}"%" or r.pmXcode like "%"#{filterString}"%" or
                r.platform_properties_name like "%"#{filterString}"%" or r.platxcode like "%"#{filterString}"%")
            </if>
            <if test="filterString!='' and queryType==2">
                and (r.ptypeName like "%"#{filterString}"%" or r.xcode like "%"#{filterString}"%" or r.usercode like
                "%"#{filterString}"%")
            </if>
            <if test="filterString !='' and queryType!=null and queryType==3">
                and r.platName like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==4">
                and r.platform_properties_name like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==5">
                and r.pmXcode like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==6">
                and r.platxcode like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==7">
                and r.ptypeName like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==8">
                and r.usercode like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==9">
                and r.xcode like CONCAT('%',#{filterString},'%')
            </if>
            <if test="stockState>0">
                and r.stock_state=#{stockState}
            </if>
            <if test="maxPrice != null">
                and r.price &lt;= #{maxPrice}
            </if>
            <if test="minPrice != null">
                and r.price &gt;= #{minPrice}
            </if>
            <if test="platformNumId != null and platformNumId !=''">
                and r.platform_num_id= #{platformNumId}
            </if>
            <if test="platformSkuId != null and platformNumId !=''">
                and r.platform_sku_id= #{platformSkuId}
            </if>
            <if test="xcodeState!=2">
                and r.update_xcode_status=#{xcodeState}
            </if>
            <if test="platformXcodeIsNull!=0 and platformXcodeIsNull==1">
                and r.platform_xcode=''
            </if>
            <if test="platformXcodeIsNull!=0 and platformXcodeIsNull==2">
                and r.platform_xcode !=''
            </if>
            <if test="platformXcodeIsNull!=0 and platformXcodeIsNull==4">
                and 1=2
            </if>
            <if test="qtyState==1">
                and r.qty>0
            </if>
            <if test="qtyState==2">
                and r.qty &lt;1
            </if>
            <if test="relationState==1">
                and r.isbind=false
            </if>
            <if test="relationState==2">
                and r.isbind=true
            </if>
            <if test="startDownloadOrderIntervalDay != null and endDownloadOrderIntervalDay!=null">
                and r.downloadOrderIntervalDay &gt;= #{startDownloadOrderIntervalDay} and r.downloadOrderIntervalDay
                &lt;= #{endDownloadOrderIntervalDay}
            </if>
            <if test="startRefreshProductIntervalDay != null and endRefreshProductIntervalDay!=null">
                and r.refreshProductIntervalDay &gt;=#{startRefreshProductIntervalDay} and r.refreshProductIntervalDay
                &lt;= #{endRefreshProductIntervalDay}
            </if>
            <if test="downloadOrderIntervalDay != null ">
                and r.downloadOrderIntervalDay = #{downloadOrderIntervalDay}
            </if>
            <if test="refreshProductIntervalDay != null ">
                and r.refreshProductIntervalDay = #{refreshProductIntervalDay}
            </if>
            <if test="platformXcodeIsNull!=0 and platformXcodeIsNull==3">
                and r.platform_xcode in (select platform_xcode from pl_eshop_product_sku where profile_id=#{profileId}
                and eshop_id=#{otypeId} and storage_type=0 and platform_xcode is not null and platform_xcode!=''
                group by platform_xcode having count(1)>1)
            </if>
        </where>
    </sql>

    <sql id="ptypeRelationSkuQuerySql_XcodeComboCount_select">
        select (case when (ifnull(p.id,0)=0 or p.deleted =1 or p.stoped =1) then false else true end) as isbind,
        psm.id,pm.profile_id,pm.eshop_id,p.id as ptype_id,pm.category_id,psm.platform_pic_url as
        picUrl,psm.platform_num_id,psm.platform_price as price,p.ptype_type,p.ptype_area,b.brand_name,
        psm.platform_properties_name,pepsrc.rule_id as sync_rule_id,rule.rule_name as ruleName,pm.platform_fullname as
        platname,psm.platform_xcode,psm.platform_xcode as
        platxcode,pm.platform_xcode as pmXcode,concat('',p.fullname) as ptypeName,p.standard,pf.fullbarcode as
        ptypeBarcode,pf.fullbarcode as skuBarcode,psm.unique_id,
        '' as pid,false as isptype,if(p.id is null,'',px.xcode) as xcode,p.usercode as usercode,psm.platform_sku_id as
        relationid,psm.platform_sku_id,psm.platform_properties,psm.platform_full_properties,psm.platform_properties
        as properties,
        expand.ready_sku_xcode,expand.ready_pfullname,expand.ready_pusercode,expand.update_xcode_status,expand.mapping_type,p.pcategory
        as mappingDetailType,p.propenabled,p.pcategory,if(ps.pic_url='',pic.pic_url,ps.pic_url) as localPicUrl,
        ifnull(datediff(now(),psm.update_time),-1) as refreshProductIntervalDay,
        ifnull(datediff(now(),expand.last_new_order_time),-1) as downloadOrderIntervalDay,
        ifnull(p.update_time,now())as modifiedtime,pm.platform_stock_state as stock_state,pu.unit_name,px.unit_id as
        unitId,px.sku_Id as
        skuId,ps.propvalue_name1,ps.propvalue_name2,ps.propvalue_name3,ps.propvalue_name4,ps.propvalue_name5,ps.propvalue_name6,ps.prop_name1,ps.prop_name2,ps.prop_name3,ps.prop_name4,ps.prop_name5,ps.prop_name6,psm.qty,expand.mark,baseunit.id
        as
        baseUnitId, ifnull(pu.unit_rate,1) as unitRate,p.cost_price AS costPrice
        from pl_eshop_product_sku psm
        left join pl_eshop_product pm on pm.platform_num_id=psm.platform_num_id and pm.profile_id=psm.profile_id
        and pm.eshop_id =psm.eshop_id
        left join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=
        expand.profile_id and expand.eshop_id=psm.eshop_id
        left join pl_eshop_product_sku_rule_config pepsrc
        on pepsrc.profile_id=psm.profile_id and pepsrc.eshop_id=psm.eshop_id
        and pepsrc.platform_num_id=psm.platform_num_id
        and pepsrc.platform_properties=psm.platform_properties_name and pepsrc.warehouse_code Is NULL
        left join base_ptype_xcode px on px.profile_id=psm.profile_id and px.xcode=psm.platform_xcode and px.unit_id not
        in(select id from base_ptype_unit where profile_id=psm.profile_id and unit_type=1)
        left join base_ptype_unit pu on pu.profile_id=psm.profile_id and pu.id =px.unit_id and pu.ptype_id=px.ptype_id
        left join base_ptype_unit baseUnit on baseUnit.profile_id=psm.profile_id and baseunit.unit_code=1 and
        baseunit.ptype_id=px.ptype_id
        left join base_ptype_sku ps on ps.id=px.sku_id and ps.profile_id=px.profile_id
        left join base_ptype_fullbarcode pf on pf.profile_id=ps.profile_id and pf.sku_id=px.sku_id and pf.unit_id =
        px.unit_id and pf.defaulted=1
        left join base_ptype p on p.id=px.ptype_id and p.profile_id=psm.profile_id and p.deleted=0 and p.stoped=0 and
        p.classed=0
        LEFT JOIN base_brandtype b ON p.profile_id = b.profile_id AND p.brand_id = b.id
        left join base_ptype_pic pic on pic.ptype_id=p.id and pic.profile_id=p.profile_id and pic.rowindex=1
        left join pl_eshop_stock_sync_rule rule on rule.id=pepsrc.rule_id and rule.profile_id=psm.profile_id and
        rule.deleted=0
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId} and (p.pcategory !=2 OR p.pcategory IS NULL) and
        expand.mapping_type=1 and psm.storage_type=0

        <if test="platformNumIds!=null and platformNumIds.size()>0">
            and psm.platform_num_id in
            <foreach collection="platformNumIds" item="item" index="i" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="notInIds!=null">
            and psm.id not in
            <foreach collection="notInIds" item="item2" index="i" separator="," open="(" close=")">
                #{item2}
            </foreach>
        </if>
    </sql>

    <sql id="ptypeRelationSkuQuerySql_byXcode_select">
        select (case when (ifnull(p.id,0)=0 or p.deleted =1 or p.stoped =1) then false else true end) as isbind,
        psm.id,psm.platform_unit_name,pm.profile_id,pm.eshop_id,p.id as ptype_id,pm.category_id,psm.platform_pic_url as
        picUrl,psm.platform_num_id,psm.platform_price as price,p.ptype_type,p.ptype_area,b.brand_name,
        psm.platform_properties_name,pepsrc.rule_id as sync_rule_id,rule.rule_name as ruleName,pm.platform_fullname as
        platname,psm.platform_xcode,psm.platform_xcode as
        platxcode,pm.platform_xcode as pmXcode,concat('',p.fullname) as ptypeName,p.standard,pf.fullbarcode as
        ptypeBarcode,pf.fullbarcode as skuBarcode,psm.unique_id,
        '' as pid,false as isptype,if(p.id is null,'',px.xcode) as xcode,p.usercode as usercode,psm.platform_sku_id as
        relationid,psm.platform_sku_id,psm.platform_properties,psm.platform_full_properties,psm.memo_platform_full_properties
        as hasMemoFullPropertiesName,psm.platform_properties
        as properties,
        expand.ready_sku_xcode,expand.ready_pfullname,expand.ready_pusercode,expand.update_xcode_status,expand.mapping_type,p.pcategory
        as mappingDetailType,p.propenabled,p.pcategory,if(ps.pic_url='',pic.pic_url,ps.pic_url) as localPicUrl,
        ifnull(datediff(now(),psm.update_time),-1) as refreshProductIntervalDay,
        ifnull(datediff(now(),expand.last_new_order_time),-1) as downloadOrderIntervalDay,
        ifnull(p.update_time,now())as modifiedtime,pm.platform_stock_state as stock_state,pu.unit_name,px.unit_id as
        unitId,px.sku_Id as
        skuId,ps.propvalue_name1,ps.propvalue_name2,ps.propvalue_name3,ps.propvalue_name4,ps.propvalue_name5,ps.propvalue_name6,
        ps.prop_name1,ps.prop_name2,ps.prop_name3,ps.prop_name4,ps.prop_name5,ps.prop_name6,psm.qty,expand.mark,baseunit.id
        as baseUnitId,
        ifnull(pu.unit_rate,1) as unitRate,if(p.sku_price=0,p.cost_price,ps.cost_price) AS costPrice
        from pl_eshop_product_sku psm
        left join pl_eshop_product pm on pm.platform_num_id=psm.platform_num_id and pm.profile_id=psm.profile_id
        and pm.eshop_id =psm.eshop_id
        left join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=
        expand.profile_id and expand.eshop_id=psm.eshop_id
        left join pl_eshop_product_sku_rule_config pepsrc
        on pepsrc.profile_id=psm.profile_id and pepsrc.eshop_id=psm.eshop_id
        and pepsrc.platform_num_id=psm.platform_num_id
        and pepsrc.platform_properties=psm.platform_properties_name and pepsrc.warehouse_code Is NULL
        left join base_ptype_xcode px on px.profile_id=psm.profile_id and px.xcode=psm.platform_xcode and px.info_type=0
        and px.unit_id not in(select id from base_ptype_unit where profile_id=psm.profile_id and unit_type=1)
        left join base_ptype_unit pu on pu.profile_id=psm.profile_id and pu.id =px.unit_id and pu.ptype_id=px.ptype_id
        left join base_ptype_unit baseUnit on baseUnit.profile_id=psm.profile_id and baseunit.unit_code=1 and
        baseunit.ptype_id=px.ptype_id
        left join base_ptype_sku ps on ps.id=px.sku_id and ps.profile_id=px.profile_id and ps.ptype_id =px.ptype_id
        left join base_ptype_fullbarcode pf on pf.profile_id=ps.profile_id and pf.sku_id=px.sku_id and pf.unit_id =
        px.unit_id and pf.defaulted=1
        left join base_ptype p on p.id=px.ptype_id and p.profile_id=psm.profile_id and p.deleted=0 and p.stoped=0 and
        p.classed=0
        LEFT JOIN base_brandtype b ON p.profile_id = b.profile_id AND p.brand_id = b.id
        left join base_ptype_pic pic on pic.ptype_id=p.id and pic.profile_id=p.profile_id and pic.rowindex=1
        left join pl_eshop_stock_sync_rule rule on rule.id=pepsrc.rule_id and rule.profile_id=psm.profile_id and
        rule.deleted=0
        where psm.profile_id=#{request.profileId} and psm.eshop_id=#{request.otypeId} and (p.pcategory !=2 OR
        p.pcategory IS NULL) and
        expand.mapping_type=1 and psm.storage_type=0
        <if test="request.platformNumIds!=null and request.platformNumIds.size()>0">
            and psm.platform_num_id in
            <foreach collection="request.platformNumIds" item="item" index="i" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.notInIds!=null">
            and psm.id not in
            <foreach collection="request.notInIds" item="item2" index="i" separator="," open="(" close=")">
                #{item2}
            </foreach>
        </if>
    </sql>

    <sql id="querySkuRelationsManualComboSql">
        select(case when (ifnull(p.id,0)=0 or p.deleted =1 or p.stoped =1) then false else true
        end) as isbind,
        psm.id,psm.platform_unit_name,psm.unique_id,mapping.ptype_id,pepsrc.rule_id as
        sync_rule_id,pepsrc.warehouse_code,rule.rule_name as
        ruleName,pm.profile_id,pm.eshop_id,pm.default_sku_id,pm.category_id,psm.platform_pic_url as
        picUrl,psm.platform_num_id,pm.platform_alias,psm.platform_price as price,p.ptype_type,p.ptype_area,b.brand_name,
        psm.platform_properties_name,pm.platform_fullname as platName ,psm.platform_sku_id,psm.platform_xcode as
        platxcode,pm.platform_xcode as pmXcode,p.fullname as ptypeName,p.standard,p.barcode as
        ptypeBarcode,p.barcode as skuBarcode,
        '' as pid,false as isptype,px.xcode,p.usercode as usercode,psm.platform_sku_id as
        relationid,psm.platform_properties,psm.platform_full_properties,psm.memo_platform_full_properties as
        hasMemoFullPropertiesName,psm.platform_properties
        as properties,psm.platform_xcode,
        expand.ready_sku_xcode,expand.ready_pfullname,expand.ready_pusercode,expand.mapping_type,p.pcategory
        as mappingDetailType,p.pcategory,p.propenabled,pic.pic_url as localPicUrl,
        ifnull(datediff(now(),psm.update_time),-1) as refreshProductIntervalDay,
        ifnull(datediff(now(),expand.last_new_order_time),-1) as downloadOrderIntervalDay,
        ifnull(p.update_time,now())as modifiedtime,pm.platform_stock_state as stock_state,pu.unit_name,pu.id as
        unitId,ps.id as skuid,ps.propvalue_name1,ps.propvalue_name2,ps.prop_name1,ps.prop_name2,psm.qty,expand.mark,
        expand.update_xcode_status,if(p.sku_price=0,p.cost_price,ps.cost_price) as costPrice
        from pl_eshop_product_sku psm
        left join pl_eshop_product_sku_mapping mapping on mapping.profile_id= psm.profile_id and
        mapping.eshop_id=psm.eshop_id and mapping.unique_id = psm.unique_id
        left join pl_eshop_product pm on pm.platform_num_id=psm.platform_num_id and pm.profile_id=psm.profile_id and
        pm.eshop_id =psm.eshop_id
        left join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=
        expand.profile_id and expand.eshop_id =psm.eshop_id
        left join pl_eshop_product_sku_rule_config pepsrc
        on pepsrc.profile_id=psm.profile_id and pepsrc.eshop_id=psm.eshop_id
        and pepsrc.platform_num_id=psm.platform_num_id
        and pepsrc.platform_properties=psm.platform_properties_name and pepsrc.warehouse_code Is NULL
        left join base_ptype p on p.id=mapping.ptype_id and p.profile_id=psm.profile_id and p.deleted=0 and p.stoped=0
        and p.classed=0
        left join base_ptype_xcode px on px.profile_id=psm.profile_id and px.unit_id=mapping.unit_id and
        px.sku_id=mapping.sku_id AND px.defaulted=1
        left join base_ptype_sku ps on ps.id=mapping.sku_id and ps.profile_id=psm.profile_id
        LEFT JOIN base_brandtype b ON p.profile_id = b.profile_id AND p.brand_id = b.id
        left join base_ptype_unit pu on pu.profile_id=psm.profile_id and pu.id =mapping.unit_id and pu.ptype_id=p.id
        left join base_ptype_pic pic on pic.ptype_id=p.id and pic.profile_id=p.profile_id and pic.rowindex=1
        left join pl_eshop_stock_sync_rule rule on rule.id=pepsrc.rule_id and rule.profile_id = psm.profile_id
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId} and p.pcategory=2 and expand.mapping_type=0 and
        psm.storage_type=0
        <if test="platformNumIds!=null and platformNumIds.size()>0">
            and psm.platform_num_id in
            <foreach collection="platformNumIds" item="item" index="i" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="querySkuRelationsManualPtypeCountSql">
        select (case when (ifnull(ps.id,0)=0 or ifnull(pu.id,0)=0 or p.deleted =1 or p.stoped =1) then false else true
        end)
        as isbind,
        psm.id,pepsrc.rule_id as sync_rule_id,pepsrc.warehouse_code,rule.rule_name as
        ruleName,pm.profile_id,pm.eshop_id,pm.category_id,pm.default_sku_id,psm.platform_pic_url as
        picUrl,psm.platform_num_id,
        psm.platform_properties_name ,pm.platform_fullname as platName,psm.platform_sku_id,psm.platform_xcode as
        platxcode,pm.platform_xcode as pmXcode,concat('',p.fullname) as ptypeName,'' as standard,pf.fullbarcode as
        ptypeBarcode,pf.fullbarcode as skuBarcode,
        '' as pid,false as isptype,px.xcode as xcode,p.usercode as usercode,psm.platform_sku_id as
        relationid,psm.platform_properties,psm.platform_price as price,psm.platform_properties
        as properties,p.pcategory
        as mappingDetailType,p.pcategory,p.propenabled,pic.pic_url as localPicUrl,
        ifnull(p.update_time,now())as modifiedtime,pm.platform_stock_state as stock_state,pu.unit_name,pu.id as
        unitId,ps.id as
        skuid,ps.propvalue_name1,ps.propvalue_name2,ps.prop_name1,ps.prop_name2,psm.qty,expand.mark
        from pl_eshop_product_sku psm
        left join pl_eshop_product_sku_mapping mapping on mapping.profile_id= psm.profile_id and mapping.unique_id =
        psm.unique_id and mapping.eshop_id=psm.eshop_id
        left join pl_eshop_product pm on pm.platform_num_id=psm.platform_num_id and pm.profile_id=psm.profile_id
        and pm.eshop_id =psm.eshop_id
        left join pl_eshop_product_sku_expand expand on expand.unique_id = psm.unique_id and expand.profile_id=
        psm.profile_id and expand.eshop_id =psm.eshop_id
        left join pl_eshop_product_sku_rule_config pepsrc
        on pepsrc.profile_id=psm.profile_id and pepsrc.eshop_id=psm.eshop_id
        and pepsrc.platform_num_id=psm.platform_num_id
        and pepsrc.platform_properties=psm.platform_properties_name and pepsrc.warehouse_code Is NULL
        left join base_ptype_sku ps on ps.id=mapping.sku_id and ps.profile_id=psm.profile_id
        left join base_ptype_fullbarcode pf on pf.profile_id=ps.profile_id and pf.sku_id=mapping.sku_id and pf.unit_id =
        mapping.unit_id and pf.defaulted=1
        left join base_ptype_xcode px on px.profile_id=psm.profile_id and px.unit_id=mapping.unit_id and
        px.sku_id=mapping.sku_id AND px.defaulted=1
        left join base_ptype_unit pu on pu.profile_id=psm.profile_id and pu.id =mapping.unit_id and
        pu.ptype_id=mapping.ptype_id
        left join base_ptype p on p.id=mapping.ptype_id and p.profile_id=psm.profile_id and p.deleted=0 and p.stoped=0
        and p.classed=0
        left join base_ptype_pic pic on pic.ptype_id=p.id and pic.profile_id=p.profile_id and pic.rowindex=1
        left join pl_eshop_stock_sync_rule rule on rule.id=pepsrc.rule_id
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId} and (p.pcategory !=2 OR p.pcategory IS NULL) and
        expand.mapping_type=0 and psm.storage_type=0
        <if test="platformNumIds!=null and platformNumIds.size()>0">
            and psm.platform_num_id in
            <foreach collection="platformNumIds" item="item" index="i" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="notInIds!=null">
            and psm.id not in
            <foreach collection="notInIds" item="item2" index="i" separator="," open="(" close=")">
                #{item2}
            </foreach>
        </if>
    </sql>

    <sql id="querySkuRelationsManualPtypeByLimitSql">
        select (case when (ifnull(ps.id,0)=0 or ifnull(pu.id,0)=0 or p.deleted =1 or p.stoped =1) then false else true
        end)
        as isbind,
        psm.id,psm.platform_unit_name,mapping.ptype_id,pepsrc.rule_id as
        sync_rule_id,pepsrc.warehouse_code,rule.rule_name as
        ruleName,pm.profile_id,pm.eshop_id,psm.unique_id,pm.category_id,pm.default_sku_id,psm.platform_pic_url as
        picUrl,psm.platform_num_id,pm.platform_alias,p.ptype_type,p.ptype_area,b.brand_name,
        psm.platform_properties_name ,pm.platform_fullname as platName,psm.platform_sku_id,psm.platform_xcode as
        platxcode,pm.platform_xcode as pmXcode,concat('',p.fullname) as ptypeName,p.standard,pf.fullbarcode as
        ptypeBarcode,pf.fullbarcode as skuBarcode,
        '' as pid,false as isptype,px.xcode as xcode,p.usercode as usercode,psm.platform_sku_id as
        relationid,psm.platform_properties,psm.platform_full_properties,psm.memo_platform_full_properties as
        hasMemoFullPropertiesName,psm.platform_properties
        as properties,psm.platform_price as price,psm.platform_xcode,
        expand.ready_sku_xcode,expand.ready_pfullname,expand.ready_pusercode,expand.mapping_type,p.pcategory
        as mappingDetailType,p.pcategory,p.propenabled,if(ps.pic_url='',pic.pic_url,ps.pic_url) as localPicUrl,
        ifnull(datediff(now(),psm.update_time),-1) as refreshProductIntervalDay,
        ifnull(datediff(now(),expand.last_new_order_time),-1) as downloadOrderIntervalDay,
        ifnull(p.update_time,now())as modifiedtime,pm.platform_stock_state as stock_state,pu.unit_name,pu.id as
        unitId,ps.id as
        skuid,ps.propvalue_name1,ps.propvalue_name2,ps.prop_name1,ps.prop_name2,psm.qty,expand.mark,expand.update_xcode_status,if(p.sku_price=0,p.cost_price,ps.cost_price)
        as costPrice
        from pl_eshop_product_sku psm
        left join pl_eshop_product_sku_mapping mapping on mapping.profile_id= psm.profile_id and mapping.unique_id =
        psm.unique_id and mapping.eshop_id =psm.eshop_id
        left join pl_eshop_product pm on pm.platform_num_id=psm.platform_num_id and pm.profile_id=psm.profile_id
        and pm.eshop_id =psm.eshop_id
        left join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=
        expand.profile_id and expand.eshop_id=psm.eshop_id
        left join pl_eshop_product_sku_rule_config pepsrc
        on pepsrc.profile_id=psm.profile_id and pepsrc.eshop_id=psm.eshop_id
        and pepsrc.platform_num_id=psm.platform_num_id
        and pepsrc.platform_properties=psm.platform_properties_name and pepsrc.warehouse_code Is NULL
        left join base_ptype_sku ps on ps.id=mapping.sku_id and ps.profile_id=psm.profile_id
        left join base_ptype_fullbarcode pf on pf.profile_id=ps.profile_id and pf.sku_id=mapping.sku_id and pf.unit_id =
        mapping.unit_id and pf.defaulted=1
        left join base_ptype_xcode px on px.profile_id=psm.profile_id and px.unit_id=mapping.unit_id and
        px.sku_id=mapping.sku_id AND px.defaulted=1
        left join base_ptype_unit pu on pu.profile_id=psm.profile_id and pu.id =mapping.unit_id and
        pu.ptype_id=mapping.ptype_id
        left join base_ptype p on p.id=mapping.ptype_id and p.profile_id=psm.profile_id and p.deleted=0 and p.stoped=0
        and p.classed=0
        LEFT JOIN base_brandtype b ON p.profile_id = b.profile_id AND p.brand_id = b.id
        left join base_ptype_pic pic on pic.ptype_id=p.id and pic.profile_id=p.profile_id and pic.rowindex=1
        left join pl_eshop_stock_sync_rule rule on rule.id=pepsrc.rule_id and rule.deleted=0
        where psm.profile_id=#{request.profileId} and psm.eshop_id=#{request.otypeId} and (p.pcategory !=2 OR
        p.pcategory IS NULL) and expand.mapping_type=0 and psm.storage_type=0
        <if test="request.platformNumIds!=null and request.platformNumIds.size()>0">
            and psm.platform_num_id in
            <foreach collection="request.platformNumIds" item="item" index="i" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.notInIds!=null">
            and psm.id not in
            <foreach collection="request.notInIds" item="item2" index="i" separator="," open="(" close=")">
                #{item2}
            </foreach>
        </if>
    </sql>
    <sql id="ptypeRelationSkuQueryManualSelectColumns">
        (case when (ifnull(p.id,0)=0  or ifnull(pu.id,0)=0) then false else true end) as isbind,
        psm.id,psm.platform_unit_name,psm.unique_id,mapping.ptype_id,pepsrc.rule_id as sync_rule_id,pepsrc.warehouse_code,
        rule.rule_name as ruleName,pm.profile_id,pm.eshop_id,pm.default_sku_id,pm.category_id,
        psm.platform_pic_url as picUrl,psm.platform_num_id,pm.platform_alias,psm.platform_price as price,
        p.ptype_type,p.ptype_area,b.brand_name, psm.platform_properties_name,pm.platform_fullname as platName ,
        psm.platform_sku_id,psm.platform_xcode as platxcode,pm.platform_xcode as pmXcode,p.fullname as ptypeName,p.standard,
        case ifnull(p.pcategory,0) when 2 then  p.barcode else pf.fullbarcode end as ptypeBarcode,
        case ifnull(p.pcategory,0) when 2 then  p.barcode else pf.fullbarcode end as skuBarcode,
        '' as pid,false as isptype,px.xcode,p.usercode as usercode,psm.platform_sku_id as relationid,
        psm.platform_properties,psm.platform_full_properties,psm.memo_platform_full_properties as hasMemoFullPropertiesName,
        psm.platform_properties as properties,psm.platform_xcode, expand.ready_sku_xcode,expand.ready_pfullname,
        expand.ready_pusercode,expand.mapping_type,p.pcategory as mappingDetailType,p.pcategory,p.propenabled,
        if(ifnull(ps.pic_url,'')='',pic.pic_url,ps.pic_url) as localPicUrl,
        ifnull(datediff(now(),psm.update_time),-1) as refreshProductIntervalDay,
        ifnull(datediff(now(),expand.last_new_order_time),-1) as downloadOrderIntervalDay,
        ifnull(p.update_time,now())as modifiedtime,pm.platform_stock_state as stock_state,pu.unit_name,pu.id as
        unitId,ps.id as skuid,ps.propvalue_name1,ps.propvalue_name2,ps.prop_name1,ps.prop_name2,psm.qty,expand.mark,
        expand.update_xcode_status,if(p.sku_price=0,p.cost_price,ps.cost_price) as costPrice
    </sql>


    <sql id="ptypeRelationSkuQueryManualFromClause">
        pl_eshop_product_sku psm
        left join pl_eshop_product_sku_mapping mapping on mapping.profile_id= psm.profile_id
             and mapping.unique_id=psm.unique_id and mapping.eshop_id =psm.eshop_id
         join pl_eshop_product pm on pm.platform_num_id=psm.platform_num_id
             and pm.profile_id=psm.profile_id and pm.eshop_id =psm.eshop_id
         join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id
            and psm.profile_id=expand.profile_id and expand.eshop_id=psm.eshop_id
        left join pl_eshop_product_sku_rule_config pepsrc on pepsrc.profile_id=psm.profile_id
            and pepsrc.eshop_id=psm.eshop_id
            and pepsrc.platform_num_id=psm.platform_num_id
            and pepsrc.platform_properties=psm.platform_properties_name
            and pepsrc.warehouse_code Is NULL
        left join base_ptype_sku ps on ps.id=mapping.sku_id and ps.profile_id=psm.profile_id
        left join base_ptype_fullbarcode pf on pf.profile_id=ps.profile_id and pf.sku_id=mapping.sku_id
                and pf.unit_id = mapping.unit_id and pf.defaulted=1
        left join base_ptype_xcode px on px.profile_id=psm.profile_id and px.unit_id=mapping.unit_id
            and px.sku_id=mapping.sku_id AND px.defaulted=1
        left join base_ptype_unit pu on pu.profile_id=psm.profile_id and pu.id =mapping.unit_id
            and pu.ptype_id=mapping.ptype_id
        left join base_ptype p on p.id=mapping.ptype_id and p.profile_id=psm.profile_id
            and p.deleted=0 and p.stoped=0 and p.classed=0
        LEFT JOIN base_brandtype b ON p.profile_id = b.profile_id AND p.brand_id = b.id
        left join base_ptype_pic pic on pic.ptype_id=p.id and pic.profile_id=p.profile_id and pic.rowindex=1
        left join pl_eshop_stock_sync_rule rule on rule.id=pepsrc.rule_id and rule.deleted=0
    </sql>

    <sql id="ptypeRelationSkuQueryByManualAndXcodeCountWhereClause">
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId}  and psm.storage_type=0
        <if test="platformNumIds!=null and platformNumIds.size()>0">
            and psm.platform_num_id in
            <foreach collection="platformNumIds" item="item" index="i" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>




    <sql id="ptypeRelationSkuQueryXcodeSelectColumns">
            (case when (ifnull(p.id,0)=0  or ifnull(pu.id,0)=0) then false else true end) as isbind,
            psm.id,psm.platform_unit_name,psm.unique_id,px.ptype_id,pepsrc.rule_id as
            sync_rule_id,pepsrc.warehouse_code,rule.rule_name as
            ruleName,pm.profile_id,pm.eshop_id,pm.default_sku_id,pm.category_id,psm.platform_pic_url as
            picUrl,psm.platform_num_id,pm.platform_alias,psm.platform_price as price,p.ptype_type,p.ptype_area,b.brand_name,
            psm.platform_properties_name,pm.platform_fullname as platName ,psm.platform_sku_id,psm.platform_xcode as
            platxcode,pm.platform_xcode as pmXcode,p.fullname as ptypeName,p.standard,
            case ifnull(p.pcategory,0) when 2 then  p.barcode else pf.fullbarcode end as ptypeBarcode,
            case ifnull(p.pcategory,0) when 2 then  p.barcode else pf.fullbarcode end as skuBarcode,
            '' as pid,false as isptype,px.xcode,p.usercode as usercode,psm.platform_sku_id as
            relationid,psm.platform_properties,psm.platform_full_properties,psm.memo_platform_full_properties as
            hasMemoFullPropertiesName,psm.platform_properties as properties,psm.platform_xcode,
            expand.ready_sku_xcode,expand.ready_pfullname,expand.ready_pusercode,expand.mapping_type,p.pcategory as mappingDetailType,p.pcategory,p.propenabled,
            if(ifnull(ps.pic_url,'')='',pic.pic_url,ps.pic_url) as localPicUrl,
            ifnull(datediff(now(),psm.update_time),-1) as refreshProductIntervalDay,
            ifnull(datediff(now(),expand.last_new_order_time),-1) as downloadOrderIntervalDay,
            ifnull(p.update_time,now())as modifiedtime,pm.platform_stock_state as stock_state,pu.unit_name,pu.id as
            unitId,ps.id as skuid,ps.propvalue_name1,ps.propvalue_name2,ps.prop_name1,ps.prop_name2,psm.qty,expand.mark,
            expand.update_xcode_status,if(p.sku_price=0,p.cost_price,ps.cost_price) as costPrice
    </sql>

    <sql id="ptypeRelationSkuQueryXcodeFromClause">
        pl_eshop_product_sku psm
        join pl_eshop_product pm on pm.platform_num_id=psm.platform_num_id and pm.profile_id=psm.profile_id
            and pm.eshop_id =psm.eshop_id
        join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id
            and psm.profile_id= expand.profile_id and expand.eshop_id=psm.eshop_id
        left join pl_eshop_product_sku_rule_config pepsrc on pepsrc.profile_id=psm.profile_id and pepsrc.eshop_id=psm.eshop_id
            and pepsrc.platform_num_id=psm.platform_num_id
            and pepsrc.platform_properties=psm.platform_properties_name and pepsrc.warehouse_code Is NULL
        left join base_ptype_xcode px on px.profile_id=psm.profile_id and px.xcode=psm.platform_xcode
        left join base_ptype_unit pu on pu.profile_id=psm.profile_id and pu.id =px.unit_id and pu.ptype_id=px.ptype_id and pu.unit_type!=1
        left join base_ptype_unit baseUnit on baseUnit.profile_id=psm.profile_id and baseunit.unit_code=1
            and baseunit.ptype_id=px.ptype_id
        left join base_ptype_sku ps on ps.id=px.sku_id and ps.profile_id=px.profile_id and ps.ptype_id =px.ptype_id
        left join base_ptype_fullbarcode pf on pf.profile_id=ps.profile_id and pf.sku_id=px.sku_id
            and pf.unit_id = px.unit_id and pf.defaulted=1
        left join base_ptype p on p.id=px.ptype_id and p.profile_id=psm.profile_id and p.deleted=0 and p.stoped=0
            and p.classed=0
        LEFT JOIN base_brandtype b ON p.profile_id = b.profile_id AND p.brand_id = b.id
        left join base_ptype_pic pic on pic.ptype_id=p.id and pic.profile_id=p.profile_id and pic.rowindex=1
        left join pl_eshop_stock_sync_rule rule on rule.id=pepsrc.rule_id and rule.profile_id=psm.profile_id
            and rule.deleted=0
    </sql>


    <sql id="ptypeRelationSkuQueryByManualAndXcodeWhereClause">
        where psm.profile_id=#{request.profileId} and psm.eshop_id=#{request.otypeId}  and psm.storage_type=0
        <if test="request.platformNumIds!=null and request.platformNumIds.size()>0">
            and psm.platform_num_id in
            <foreach collection="request.platformNumIds" item="item" index="i" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <insert id="insertProductSkuMapping">
        insert into pl_eshop_product_sku
        (id, profile_Id, eshop_Id, platform_pic_url, platform_num_id,
         platform_sku_id,
         platform_properties,
         platform_properties_name,
         platform_xcode,
         platform_modified_time,
         platform_json,
         qty,
         unique_Id)
        values (#{id}, #{profileId}, #{eshopId}, #{platformPicUrl}, #{platformNumId},
                #{platformSkuId},
                #{platformProperties},
                #{platformPropertiesName},
                #{platformXcode},
                #{platformModifiedTime},
                #{platformJson},
                #{qty},
                #{uniqueId})
        ON DUPLICATE KEY
            UPDATE platform_sku_id        = #{platformSkuId},
                   platform_xcode         = #{platformXcode},
                   platform_properties    = #{platformProperties},
                   platform_pic_url       = #{platformPicUrl},
                   platform_json          = #{platformJson},
                   platform_modified_time = #{platformModifiedTime},
                   storage_type           = #{storageType},
                   qty                    = #{qty}
    </insert>
    <insert id="insertOrUpdateProductSkuMappingRelation">
        insert into pl_eshop_product_sku_mapping
        (id,
         eshop_id,
         profile_id,
         pcategory,
         ptype_id,
         sku_id,
         unit_id,
         xcode,
         unique_id,
         platform_full_properties_name)
            VALUE (#{id}, #{eshopId}, #{profileId}, #{pcategory}, #{ptypeId}, #{skuId}, #{unitId}, #{xcode},
                   #{uniqueId}, #{platformPropertiesName})
        ON DUPLICATE KEY
            UPDATE pcategory                     = #{pcategory},
                   ptype_id                      = #{ptypeId},
                   sku_id                        = #{skuId},
                   unit_id                       = #{unitId},
                   xcode                         = #{xcode},
                   platform_full_properties_name = #{platformPropertiesName}
    </insert>


    <insert id="batchInsertOrUpdateProductSkuMappingRelation">
        insert into pl_eshop_product_sku_mapping
        (id,
        eshop_id,
        profile_id,
        pcategory,
        ptype_id,
        sku_id,
        unit_id,
        xcode,
        unique_id,
        platform_full_properties_name)
        VALUES
        <foreach item="item" index="index" collection="skuMappings" separator=",">
            (#{item.id}, #{item.eshopId}, #{item.profileId}, #{item.pcategory}, #{item.ptypeId}, #{item.skuId},
            #{item.unitId}, #{item.xcode},
            #{item.uniqueId}, #{item.platformPropertiesName})
        </foreach>

        ON DUPLICATE KEY
        UPDATE pcategory= VALUES(pcategory),
        ptype_id= VALUES(ptype_id),
        sku_id= VALUES(sku_id),
        unit_id= VALUES(unit_id),
        xcode= VALUES(xcode),
        platform_full_properties_name = VALUES(platform_full_properties_name)
    </insert>



    <insert id="batchInsertOrUpdateProductSkuMapping">
        insert into pl_eshop_product_sku_mapping
        (id,
        eshop_id,
        profile_id,
        pcategory,
        ptype_id,
        sku_id,
        unit_id,
        xcode,
        unique_id,
        platform_full_properties_name,
        platform_num_id,
        platform_sku_id,
        platform_properties_name,
        platform_xcode,
        platform_properties,
        platform_modified_time)
        VALUES
        <foreach item="item" index="index" collection="skuMappings" separator=",">
            (#{item.id}, #{item.eshopId}, #{item.profileId}, #{item.pcategory}, #{item.ptypeId}, #{item.skuId},
            #{item.unitId}, #{item.xcode},
            #{item.uniqueId}, #{item.platformPropertiesName},
            #{item.platformNumId},#{item.platformSkuId},#{item.platformPropertiesName},
            #{item.platformXcode},#{item.platformProperties},#{item.platformModifiedTime})
        </foreach>

        ON DUPLICATE KEY
        UPDATE pcategory= VALUES(pcategory),
        ptype_id= VALUES(ptype_id),
        sku_id= VALUES(sku_id),
        unit_id= VALUES(unit_id),
        xcode= VALUES(xcode),
        platform_full_properties_name = VALUES(platform_full_properties_name),
        platform_num_id = VALUES(platform_num_id),
        platform_sku_id = VALUES(platform_sku_id),
        platform_properties_name = VALUES(platform_properties_name),
        platform_xcode = VALUES(platform_xcode),
        platform_properties = VALUES(platform_properties),
        platform_modified_time = VALUES(platform_modified_time)
    </insert>



    <insert id="insertProductMapping">
        insert into pl_eshop_product
        (id, profile_Id, eshop_Id, platform_pic_url, category_id, platform_num_id, platform_fullname, platform_xcode,
         has_properties,
         default_sku_id,
         platform_stock_state,
         platform_price,
         platform_alias,
         platform_modify_time)
        values (#{id}, #{profileId}, #{eshopId}, #{picUrl}, #{categoryId}, #{platformNumId}, #{fullName},
                #{xCode},
                #{hasProperties},
                #{defaultSkuId},
                #{stockState},
                #{price},
                #{propAlias},
                #{onlineModifyTime})
    </insert>

    <update id="updateProductSkuMapping"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        update pl_eshop_product_sku
        set platform_pic_url              = #{platformPicUrl},
            platform_num_id               = #{platformNumId},
            platform_sku_id               = #{platformSkuId},
            platform_properties           = #{platformProperties},
            platform_properties_name      = #{platformPropertiesName},
            platform_xcode                = #{platformXcode},
            platform_full_properties_name = #{platformFullPropertiesName},
            platform_full_properties_id   = #{platformFullPropertiesId},
            modified_time                 = #{modifiedTime},
            qty=#{qty},
            update_time                   = #{updateTime},
            platform_modified_time        =#{platformModifiedTime}
        where id = #{id}
          and profile_id = #{profileId}
          and eshop_id = #{eshopId}
    </update>

    <update id="updateSkuMappingToCombo"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        update pl_eshop_product_sku_mapping
        set ptype_id=#{ptypeId}
        where profile_id = #{profileId}
          and unique_id = #{uniqueId}
    </update>

    <update id="updateByids" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        update pl_eshop_product_sku
        set
        download_enable=#{downloadEnable}
        where profile_id = #{profileId} and id in
        <foreach collection="ids" item="id" index="i" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateProductSkuMappingByRelation"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductRelationEntity">
        update pl_eshop_product_sku
        set ready_pfullname = #{readyPfullname},
            ready_sku_name  = #{readySkuName},
            ready_pusercode = #{readyPusercode},
            ready_sku_xcode = #{readySkuXcode}
        where profile_id = #{profileid}
          and eshop_id = #{eshopId}
          and platform_sku_id = #{platformSkuId}
    </update>
    <update id="modifyProductSkuMappingValue">
        update pl_eshop_product_sku_mapping
        set sku_id  = #{skuId},
            unit_id=#{unitId},
            ptype_id=#{ptypeId}
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and unique_id = #{uniqueId}
    </update>
    <update id="updateProductMapping">
        update pl_eshop_product
        set pic_url=#{picUrl},
            category_Id=#{categoryId},
            platform_fullname=#{fullName},
            Xcode=#{xCode},
            has_properties=#{hasProperties},
            default_Sku_Id=#{defaultSkuId},
            prop_Alias=#{propAlias},
            platform_stock_state=#{stockState},
            price=#{price},
            qty = #{qty},
            online_modify_time =#{onlineModifyTime}
        where profile_Id = #{profileId}
          and platform_Num_Id = #{platformNumId}
    </update>
    <update id="modifyProductMappingValue">
        update pl_eshop_product
        set ptype_id=#{ptypeId}
        where platform_Num_id = #{platformNumId}
          and eshop_id = #{eshopId}
          and profile_id = #{profileId}
    </update>

    <update id="modifyProductskuMapping">
        update pl_eshop_product_sku
        set ptype_id=#{ptypeId},
            sku_id  = #{skuId},
            unit_id = #{unitId}
        where platform_Num_id = #{platformNumId}
          and eshop_id = #{eshopId}
          and profile_id = #{profileId}
    </update>

    <update id="modifyProductRelation">
        update pl_eshop_product_sku
        set ptype_id=#{ptypeId},sku_id = #{skuId},unit_id = #{unitId}
        where profile_id = #{profileId}
        and eshop_id = #{eshopId}
        <if test="platformNumId!=null and platformNumId !=''">
            and platform_num_id = #{platformNumId}
        </if>
        <if test="platformSkuId!=null and platformSkuId !=''">
            and platform_sku_id = #{platformSkuId}
        </if>
        <if test="platformPropertiesName!=null and platformPropertiesName!=''">
            and platform_properties_name=#{platformPropertiesName}
        </if>
    </update>

    <update id="modifyProductSkuRelation">
        update pl_eshop_product_sku_mapping
        set ptype_id=#{ptypeId},sku_id = #{skuId},unit_id = #{unitId}
        where profile_id = #{profileId}
        and eshop_id = #{eshopId}
        and platform_num_id = #{platformNumId}
        <if test="platformSkuId!=null and platformSkuId !=''">
            and platform_sku_id = #{platformSkuId}
        </if>
        <if test="platformPropertiesName!=null and platformPropertiesName!=''">
            and platform_properties_name=#{platformPropertiesName}
        </if>
    </update>
    <update id="modifyProductstockState">
        update pl_eshop_product
        set platform_stock_state=#{stockState}
        where platform_Num_id = #{platformNumId}
          and eshop_id = #{eshopId}
          and profile_id = #{profileId}
    </update>
    <delete id="clearProductSkuMappingsBySku">
        delete
        from pl_eshop_product_sku_mapping
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and unique_id = #{uniqueId}
    </delete>
    <select id="getProductMappingByUniqueId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select *
        from pl_eshop_product_sku_mapping
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and unique_id = #{uniqueId}
    </select>
    <delete id="clearProductSkuMappingsByuniqueIds">
        delete from pl_eshop_product_sku_mapping
        where profile_id = #{profileId}
        and eshop_id = #{eshopId}
        <if test="uniqueIds != null and uniqueIds.size()>0">
            and unique_id in
            <foreach collection="uniqueIds" item="uniqueId" index="i" separator="," open="(" close=")">
                #{uniqueId}
            </foreach>
        </if>
    </delete>
    <select id="queryProductSkuMappingsBySku" resultType="string">
        select unique_id
        from pl_eshop_product_sku
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and platform_Num_id = #{platformNumId}
    </select>

    <select id="queryProductSkuMappingsByNumIds" resultType="string">
        select platform_num_id
        from pl_eshop_product_sku
        where profile_id = #{profileId}
        and eshop_id = #{eshopId}
        <if test="platformNumIdList != null and platformNumIdList.size()>0">
            and platform_num_id in
            <foreach collection="platformNumIdList" item="platformNumId" separator="," index="i" close=")" open="(">
                #{platformNumId}
            </foreach>
        </if>
    </select>

    <update id="clearProductSkuMapping">
        update pl_eshop_product_sku set ptype_id=0,unit_id=0,sku_id=0 ,update_time=#{updateTime}
        where profile_id=#{profileId} and eshop_id=#{eshopId}
        <if test="id!=null">
            and id=#{id}
        </if>
        <if test="platformNumId">
            and platform_num_id=#{platformNumId}
        </if>

    </update>
    <update id="updateProductMappingXcode">
        update pl_eshop_product
        set platform_xcode=#{xCode}
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and platform_Num_Id = #{platformNumId}
    </update>

    <update id="updateEshopProductSkuMapping">
        update pl_eshop_product_sku_expand
        set mark=#{mark}
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and unique_id = #{uniqueId}
    </update>

    <delete id="clearProductSkuExpandsBySku">
        delete
        from pl_eshop_product_sku_expand
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and unique_id = #{uniqueId}
    </delete>

    <delete id="clearProductSkuExpandsByuniqueIds">
        delete
        from pl_eshop_product_sku_expand
        where profile_id = #{profileId}
        and eshop_id = #{eshopId}
        <if test="uniqueIds != null and uniqueIds.size()>0">
            and unique_id in
            <foreach collection="uniqueIds" item="uniqueId" index="i" separator="," open="(" close=")">
                #{uniqueId}
            </foreach>
        </if>
    </delete>
    <delete id="clearProductSkuByuniqueIds">
        delete from pl_eshop_product_sku
        where profile_id = #{profileId}
        and eshop_id = #{eshopId}
        <if test="uniqueIds != null and uniqueIds.size()>0">
            and unique_id in
            <foreach collection="uniqueIds" item="uniqueId" index="i" separator="," open="(" close=")">
                #{uniqueId}
            </foreach>
        </if>
    </delete>

    <delete id="clearProductRuleConfigBySkuIds">
        delete from pl_eshop_product_sku_rule_config
        where profile_id = #{profileId}
        and eshop_id = #{eshopId}
        and platform_num_id = #{numId}
        <if test="platfromSkuIds != null and platfromSkuIds.size()>0">
            and platform_sku_id in
            <foreach collection="platfromSkuIds" item="skuId" index="i" separator="," open="(" close=")">
                #{skuId}
            </foreach>
        </if>
    </delete>

    <update id="updateEshopProductSkuMappingdByNumId">
        update pl_eshop_product_sku_expand
        set mark=#{mark}
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and unique_id = #{uniqueId}
    </update>


    <update id="updateProductSkuMappingXcode">
        update pl_eshop_product_sku
        set platform_xcode=#{xCode}
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and platform_Num_Id = #{platformNumId}
          and platform_sku_id = #{platformSkuId}
    </update>

    <update id="updateProductSkuMappingXcodeStatus">
        update pl_eshop_product_sku_expand
        set update_xcode_status=#{xCodeStatus}
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and unique_id = #{uniqueId}
    </update>

    <select id="queryProductSkuMapping"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        SELECT
        psm.profile_id,psm.eshop_id,psm.platform_properties_name as platformProperties,psm.platform_num_id,psm.platform_sku_id,psm.platform_xcode,psm.platform_barcode,psm.unique_id,
        mapping.unit_id,expand.mapping_type,mapping.sku_id,mapping.ptype_id,bp.fullname,bp.fullname as
        ptypeName,mapping.pcategory,
        if(mapping.pcategory=2, bp.barcode,bpf.fullbarcode) as barcode,bpu.unit_name,
        if(mapping.pcategory=2, bp.usercode,bpx.xcode) as xcode
        FROM pl_eshop_product_sku psm
        left join pl_eshop_product_sku_expand expand on expand.profile_id= psm.profile_id and expand.unique_id =
        psm.unique_id
        left join pl_eshop_product_sku_mapping mapping on mapping.profile_id= psm.profile_id and mapping.unique_id =
        psm.unique_id
        left join base_ptype bp on bp.profile_id = psm.profile_id and bp.id = mapping.ptype_id and bp.deleted=0 and
        bp.stoped=0 and bp.classed=0
        left join base_ptype_unit bpu on bpu.profile_id = psm.profile_id and bpu.id = mapping.unit_id
        left join base_ptype_xcode bpx on bpx.profile_id = psm.profile_id and bpx.ptype_id = mapping.ptype_id and
        bpx.unit_id = mapping.unit_id and bpx.sku_id = mapping.sku_id and bpx.defaulted=1
        left join base_ptype_fullbarcode bpf on bpf.profile_id = psm.profile_id and bpf.ptype_id = mapping.ptype_id and
        bpf.unit_id = mapping.unit_id and bpf.sku_id = mapping.sku_id
        WHERE psm.profile_id = #{profileId} and psm.eshop_id = #{eshopId} and psm.storage_type=0
        and bp.stoped=0 and bp.deleted=0 and mapping.ptype_id >0 and expand.mapping_type=0
        <if test="params != null and params.size()>0">
            and
            <foreach collection="params" item="item" separator="or" open="(" close=")">
                (psm.platform_num_id=#{item.platformNumId} and psm.platform_properties_name=#{item.platformProperties})
            </foreach>
        </if>
        union all
        SELECT
        psm.profile_id,psm.eshop_id,psm.platform_properties_name as platformProperties,psm.platform_num_id,psm.platform_sku_id,psm.platform_xcode,psm.platform_barcode,psm.unique_id,
        bpx.unit_id,expand.mapping_type,bpx.sku_id,ifnull(cb.id, 0) as ptype_id,cb.fullname as
        fullname,cb.fullname as ptypeName,cb.pcategory as pcategory,
        ifnull(cb.barcode,bpf.fullbarcode) as barcode,bpu.unit_name,
        ifnull(cb.usercode,bpx.xcode) as xcode
        FROM pl_eshop_product_sku psm
        left join pl_eshop_product_sku_expand expand on expand.profile_id= psm.profile_id and expand.unique_id =
        psm.unique_id
        left join base_ptype_xcode bpx on bpx.profile_id=psm.profile_id and bpx.xcode=psm.platform_xcode and
        bpx.defaulted=1
        left join base_ptype cb on cb.profile_id = psm.profile_id and cb.id=bpx.ptype_id and cb.deleted=0 and
        cb.stoped=0 and cb.classed=0
        left join base_ptype_fullbarcode bpf on bpf.profile_id = psm.profile_id and bpf.ptype_id = bpx.ptype_id and
        bpf.unit_id = bpx.unit_id and bpf.sku_id = bpx.sku_id
        left join base_ptype_unit bpu on bpu.profile_id = bpx.profile_id and bpu.id = bpx.unit_id
        WHERE psm.profile_id = #{profileId} and psm.eshop_id = #{eshopId} and psm.storage_type=0
        and ifnull(cb.id, 0) >0 and psm.platform_xcode!='' and
        expand.mapping_type=1
        <if test="params != null and params.size()>0">
            and
            <foreach collection="params" item="item" separator="or" open="(" close=")">
                (psm.platform_num_id=#{item.platformNumId} and psm.platform_properties_name=#{item.platformProperties})
            </foreach>
        </if>
        limit #{pageNum}, #{pageSize}
    </select>

    <select id="queryProductSkuMappingCount" resultType="int">
        select count(1)
        from (SELECT psm.id
        FROM pl_eshop_product_sku psm
        left join pl_eshop_product_sku_expand expand on expand.profile_id= psm.profile_id and expand.unique_id =
        psm.unique_id
        left join pl_eshop_product_sku_mapping mapping on mapping.profile_id= psm.profile_id and mapping.unique_id =
        psm.unique_id
        left join base_ptype bp on bp.profile_id = psm.profile_id and bp.id = mapping.ptype_id and bp.deleted=0 and
        bp.stoped=0 and bp.classed=0
        left join base_ptype_unit bpu on bpu.profile_id = psm.profile_id and bpu.id = mapping.unit_id
        left join base_ptype_fullbarcode bpf on bpf.profile_id = psm.profile_id and bpf.ptype_id = mapping.ptype_id and
        bpf.unit_id = mapping.unit_id and bpf.sku_id = mapping.sku_id
        WHERE psm.profile_id = #{profileId} and psm.eshop_id = #{eshopId} and psm.storage_type=0
        and bp.stoped=0 and bp.deleted=0 and mapping.ptype_id >0 and expand.mapping_type=0
        <if test="params != null and params.size()>0">
            and
            <foreach collection="params" item="item" separator="or" open="(" close=")">
                (psm.platform_num_id=#{item.platformNumId} and psm.platform_properties_name=#{item.platformProperties})
            </foreach>
        </if>
        union all
        SELECT psm.id
        FROM pl_eshop_product_sku psm
        left join pl_eshop_product_sku_expand expand on expand.profile_id= psm.profile_id and expand.unique_id =
        psm.unique_id
        left join base_ptype_xcode bpx on bpx.profile_id=psm.profile_id and bpx.xcode=psm.platform_xcode
        left join base_ptype cb on cb.profile_id = psm.profile_id and cb.id=bpx.ptype_id and cb.deleted=0 and
        cb.stoped=0 and cb.classed=0
        left join base_ptype_fullbarcode bpf on bpf.profile_id = psm.profile_id and bpf.ptype_id = bpx.ptype_id and
        bpf.unit_id = bpx.unit_id and bpf.sku_id = bpx.sku_id
        left join base_ptype_unit bpu on bpu.profile_id = bpx.profile_id and bpu.id = bpx.unit_id
        WHERE psm.profile_id = #{profileId} and psm.eshop_id = #{eshopId} and psm.storage_type=0
        and ifnull(cb.id, 0) >0 and psm.platform_xcode!='' and
        expand.mapping_type=1
        <if test="params != null and params.size()>0">
            and
            <foreach collection="params" item="item" separator="or" open="(" close=")">
                (psm.platform_num_id=#{item.platformNumId} and psm.platform_properties_name=#{item.platformProperties})
            </foreach>
        </if>
        ) tmp
    </select>

    <delete id="clearEshopProductRelation">
        delete
        from pl_eshop_product_sku_mapping
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
    </delete>
    <delete id="clearEshopProductSkuRelationByNumId">
        delete from pl_eshop_product_sku_mapping
        where profile_id = #{profileId}
        and eshop_id = #{eshopId}
        <if test="platformNumId != null">
            and unique_id in (select unique_id from pl_eshop_product_sku sku where sku.profile_id = #{profileId}
            and sku.eshop_id = #{eshopId} and sku.platform_num_id=#{platformNumId})
        </if>
    </delete>

    <update id="deleteProductSkuByStandardApi">
        delete from pl_eshop_product where profile_id=#{profileId} and eshop_id =#{param.otypeId} and
        platform_num_id=#{param.platformNumId};
        delete from pl_eshop_product_sku
        where profile_id=#{profileId}
        and eshop_id=#{param.otypeId}
        and platform_num_id=#{param.platformNumId}
        <if test="param.platformSkuId != null and param.platformSkuId != ''">
            and platform_sku_id=#{param.platformSkuId};
        </if>
    </update>

    <delete id="deleteProductSkuByuniqueId">
        delete
        from pl_eshop_product_sku
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and unique_id = #{uniqueId}
    </delete>

    <select id="checkrelationByXcodeAndNoXcode" resultType="java.lang.Integer">
        select count(*)
        from pl_eshop_product_sku s
                 left join pl_eshop_product_sku_expand e
                           on e.profile_id = s.profile_id and e.eshop_id = s.eshop_id and e.unique_id = s.unique_id
        where s.profile_id = #{profileId}
          and s.eshop_id = #{eshopId}
          and e.mapping_type = 1
          and s.platform_xcode = ""
    </select>


    <update id="modifyProductSkuExpandMarkValue">
        update pl_eshop_product_sku_expand
        set mark = #{mark}
        where profile_id = #{profileId}
        and eshop_id = #{eshopId}
        and unique_id in
        <foreach collection="uniqueIdList" open="(" item="uniqueId" close=")" separator="," index="i">
            #{uniqueId}
        </foreach>
    </update>

    <delete id="deleteProductMappings">
        delete from pl_eshop_product where profile_id=#{profileId} and eshop_id = #{eshopId} and id in
        <foreach collection="ids" item="id" separator="," index="i" close=")" open="(">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteProductByNumId">
        delete pm
        from pl_eshop_product pm
                 join(select platform_num_id, profile_id, eshop_id
                      from pl_eshop_product_sku
                      where profile_id = #{profileId}
                        and eshop_id = #{eshopId}
                        and platform_num_id = #{platformNumId}
                        and storage_type = 0) tmp
                     on pm.profile_id = tmp.profile_id and pm.eshop_id = tmp.eshop_id and
                        pm.platform_num_id = tmp.platform_num_id
        where pm.profile_id = #{profileId}
          and pm.eshop_id = #{eshopId}
    </delete>
    <delete id="deleteProductByNumIdList">
        delete
        from pl_eshop_product
        where profile_id = #{profileId}
        and eshop_id = #{eshopId}
        <if test="platformNumIdList != null and platformNumIdList.size()>0">
            and platform_num_id in
            <foreach collection="platformNumIdList" item="platformNumId" separator="," index="i" close=")" open="(">
                #{platformNumId}
            </foreach>
        </if>
    </delete>

    <delete id="deleteProductByNumIdListWhenSkuIsNull">
        delete mk,mkd,expand,mapping
        from pl_eshop_product product
        left join pl_eshop_product_sku_mapping mapping on product.profile_id = mapping.profile_id and mapping.eshop_id = product.eshop_id and product.platform_num_id = mapping.platform_num_id
        left join pl_eshop_product_mark mk on mapping.profile_id = mk.profile_id and mapping.eshop_id=mk.eshop_id and mk.unique_id=mapping.unique_id
        left join pl_eshop_product_mark_data mkd on mk.profile_id = mkd.profile_id and mk.id = mkd.mark_id
        left join pl_eshop_product_sku_expand expand on mapping.profile_id = expand.profile_id and mapping.unique_id = expand.unique_id
        left join pl_eshop_product_sku sku on mapping.profile_id = sku.profile_id and mapping.unique_id=sku.unique_id
        where product.profile_id = #{profileId}
        and product.eshop_id = #{otypeId}
        <if test="platformNumIdList != null and platformNumIdList.size()>0">
            and product.platform_num_id in
            <foreach collection="platformNumIdList" item="platformNumId" separator="," index="i" close=")" open="(">
                #{platformNumId}
            </foreach>
        </if>
        and ifnull(sku.id,0)=0
    </delete>

    <delete id="deleteProductskuByNumId">
        delete
        from pl_eshop_product_sku
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and platform_num_id = #{platformNumId}
          and storage_type = 0
    </delete>

    <delete id="deleteProductskuByNumIds">
        delete
        from pl_eshop_product_sku
        where profile_id = #{profileId}
        and eshop_id = #{eshopId}
        <if test="platformNumIdList != null and platformNumIdList.size()>0">
            and platform_num_id in
            <foreach collection="platformNumIdList" item="platformNumId" separator="," index="i" close=")" open="(">
                #{platformNumId}
            </foreach>
        </if>
        and storage_type=0
    </delete>

    <delete id="deleteProductskuRuleConfig">
        delete
        from pl_eshop_product_sku_rule_config
        where profile_id = #{profileId}
        and eshop_id = #{eshopId}
        <if test="platformNumIdList != null and platformNumIdList.size()>0">
            and platform_num_id in
            <foreach collection="platformNumIdList" item="platformNumId" separator="," index="i" close=")" open="(">
                #{platformNumId}
            </foreach>
        </if>
    </delete>


    <delete id="deleteProductSkuExpandByNumIds">
        delete
        from pl_eshop_product_sku_expand
        where profile_id = #{profileId}
        and eshop_id = #{eshopId}
        and unique_id in (select unique_id
        from pl_eshop_product_sku
        where profile_id = #{profileId}
        and eshop_id = #{eshopId}
        <if test="platformNumIdList != null and platformNumIdList.size()>0">
            and platform_num_id in
            <foreach collection="platformNumIdList" item="platformNumId" separator="," index="i" close=")" open="(">
                #{platformNumId}
            </foreach>
        </if>
        and storage_type=0)
    </delete>

    <delete id="deleteProductSkuExpandByNumId">
        delete psme
        from pl_eshop_product_sku_expand psme
                 join(select unique_id, profile_id, eshop_id
                      from pl_eshop_product_sku psm
                      where psm.profile_id = #{profileId}
                        and psm.eshop_id = #{eshopId}
                        and psm.platform_num_id = #{platformNumId}
                        and psm.storage_type = 0) tmp
                     on psme.profile_id = tmp.profile_id and psme.eshop_id = tmp.eshop_id and
                        psme.unique_id = tmp.unique_id
        where psme.profile_id = #{profileId}
          and psme.eshop_id = #{eshopId}
    </delete>

    <delete id="deleteProductSkuMappingByNumIds">
        delete
        from pl_eshop_product_sku_mapping
        where profile_id = #{profileId}
        and eshop_id = #{eshopId}
        and unique_id in (select unique_id
        from pl_eshop_product_sku
        where profile_id = #{profileId}
        and eshop_id = #{eshopId}
        <if test="platformNumIdList != null and platformNumIdList.size()>0">
            and platform_num_id in
            <foreach collection="platformNumIdList" item="platformNumId" separator="," index="i" close=")" open="(">
                #{platformNumId}
            </foreach>
        </if>
        and storage_type=0)
    </delete>

    <delete id="deleteProductSkuMappingByNumId">
        delete psmm
        from pl_eshop_product_sku_mapping psmm
                 join(select unique_id, profile_id, eshop_id
                      from pl_eshop_product_sku psm
                      where psm.profile_id = #{profileId}
                        and psm.eshop_id = #{eshopId}
                        and psm.platform_num_id = #{platformNumId}
                        and psm.storage_type = 0) tmp
                     on psmm.profile_id = tmp.profile_id and psmm.eshop_id = tmp.eshop_id and
                        psmm.unique_id = tmp.unique_id
        where psmm.profile_id = #{profileId}
          and psmm.eshop_id = #{eshopId}
    </delete>

    <delete id="deleteProductSkuMappings">
        delete from pl_eshop_product_sku where profile_id=#{profileId} and eshop_id = #{eshopId} and id in
        <foreach collection="ids" item="id" separator="," index="i" close=")" open="(">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteSkusByNumId">
        delete
        from pl_eshop_product_sku
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and platform_num_id = #{platformNumId}
    </delete>

    <delete id="deleteSkusByNumIdList">
        delete
        from pl_eshop_product_sku
        where profile_id = #{profileId}
        and eshop_id = #{eshopId}
        and storage_type = 0
        <if test="platformNumIdList != null and platformNumIdList.size()>0">
            and platform_num_id in
            <foreach collection="platformNumIdList" item="platformNumId" separator="," index="i" close=")" open="(">
                #{platformNumId}
            </foreach>
        </if>
    </delete>

    <select id="querySkuCountByNumId" resultType="java.lang.Integer">
        select count(*)
        from pl_eshop_product_sku
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and platform_num_id = #{platformNumId}
          and (platform_sku_id &lt;&gt; #{platformSkuId} or platform_sku_id is null)
    </select>

    <select id="getEshopProductMappingType" resultType="java.lang.Integer">
        select expand.mapping_type
        from pl_eshop_product_sku ps
                 left join pl_eshop_product_sku_expand expand
                           on expand.profile_id = ps.profile_id and expand.unique_id = ps.unique_id
        where ps.profile_id = #{profileId}
          and ps.eshop_id = #{eshopId}
          and ps.platform_num_id = #{platformNumId}
          and ps.platform_properties_name = #{platformPropertiesName}
        limit 1
    </select>

    <select id="getEshopProductMappingTypeBySkuId" resultType="java.lang.Integer">
        select expand.mapping_type
        from pl_eshop_product_sku ps
                 left join pl_eshop_product_sku_expand expand
                           on expand.profile_id = ps.profile_id and expand.unique_id = ps.unique_id
        where ps.profile_id = #{profileId}
          and ps.eshop_id = #{eshopId}
          and ps.platform_num_id = #{platformNumId}
          and ps.platform_sku_id = #{platformSkuId}
        limit 1
    </select>

    <delete id="deleteProductSku">
        delete
        from pl_eshop_product_sku
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and platform_sku_id = #{platformSkuId}
    </delete>

    <delete id="deleteProductSkuByproperties">
        delete
        from pl_eshop_product_sku
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and platform_num_id = #{platformNumId}
          and platform_properties_name = #{platformPropertiesName}
    </delete>


    <select id="queryProductSkuMappingList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuNeedDelete">
        select sku.*,expand.last_new_order_time
        from pl_eshop_product_sku sku
        left join pl_eshop_product_sku_expand expand on expand.profile_id=sku.profile_id and expand.eshop_id =
        sku.eshop_id and expand.unique_id = sku.unique_id
        where sku.profile_id = #{profileid}
        and sku.eshop_id = #{eshopId}
        <if test="platformNumIdList != null and platformNumIdList.size()>0">
            and sku.platform_num_id in
            <foreach collection="platformNumIdList" item="numId" open="(" close=")" separator=",">
                #{numId}
            </foreach>
        </if>
    </select>

    <select id="queryProductSkuMappingByNumid"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select *
        from pl_eshop_product_sku
        where profile_id = #{profileid}
          and eshop_id = #{eshopId}
          and platform_num_id = #{platformNumId}
          and storage_type = 0;
    </select>

    <select id="queryProductSkuMappingByNumids"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select (case when (ifnull(p.id,0)=0 or p.deleted =1 or p.stoped =1) then false else true
        end)as isbind, sku.id, expand.ready_pfullname,expand.download_enable,pm.platform_xcode as
        pmplatformXcode,pm.platform_fullname,pm.platform_fullname
        as platfullName,
        expand.ready_sku_name,sku.profile_id,
        expand.mark,expand.mapping_type,sku.eshop_id,sku.platform_sku_id,skum.sku_id, sku.platform_Num_Id,
        sku.platform_properties_Name,sku.platform_properties,0 as
        isredundant,sku.platform_xcode,skum.ptype_id,skum.unit_id,skum.xcode,
        sku.platform_xcode as platxcode,sku.platform_modified_time,expand.ready_sku_name,p.pcategory,
        expand.ready_sku_xcode,expand.ready_pfullname,expand.ready_pusercode,sku.platform_pic_url as
        picUrl,config.rule_id as
        syncRuleId,p.fullname as ptypeName,pu.unit_rate
        from pl_eshop_product_sku sku
        left join pl_eshop_product pm on pm.profile_id = sku.profile_id and
        pm.platform_num_id=sku.platform_num_id
        left join pl_eshop_product_sku_mapping skum on skum.profile_id=sku.profile_id and skum.unique_id=sku.unique_id
        left join pl_eshop_product_sku_expand expand on sku.unique_id = expand.unique_id and sku.profile_id=
        expand.profile_id
        left join base_ptype p on p.id=skum.ptype_id and p.profile_id=skum.profile_id
        left join base_ptype_sku ps on ps.id=skum.sku_id and ps.profile_id=skum.profile_id
        left join base_ptype_unit pu on pu.profile_id=skum.profile_id and pu.id =skum.unit_id and
        pu.ptype_id=skum.ptype_id
        left join pl_eshop_product_sku_rule_config config on config.profile_id=sku.profile_id and
        config.platform_num_id=sku.platform_num_id and config.platform_properties = sku.platform_properties_name
        where sku.profile_id=#{profileid} and sku.eshop_id=#{eshopId} and expand.mapping_type=#{mappingType}
        <if test="platformNumIds!=null and platformNumIds.size()>0">
            and sku.platform_num_id in
            <foreach collection="platformNumIds" item="numId" separator="," index="i" close=")" open="(">
                #{numId}
            </foreach>
        </if>
    </select>

    <select id="queryProductSkuMappingByNumidsAndUsXcode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select sku.id, ifnull(px.ptype_id,p2.id) as ptype_id,
        expand.ready_pfullname,expand.download_enable,pm.platform_xcode as pmplatformXcode,pm.platform_fullname as
        platfullName,pm.platform_fullname,(case
        when (ifnull(px.id,p2.id)=0 or p.deleted =1 or p.stoped =1) then false else true end) as isbind,
        expand.ready_sku_name,sku.profile_id, expand.mark,sku.eshop_id,sku.platform_sku_id,px.sku_id,
        sku.platform_Num_Id,
        sku.platform_properties_Name,sku.platform_properties,0 as isredundant,sku.platform_xcode,
        sku.platform_xcode as platxcode,sku.platform_modified_time,expand.ready_sku_name,
        expand.ready_sku_xcode,expand.ready_pfullname,expand.ready_pusercode,sku.platform_pic_url as picUrl,pepsrc.id as
        syncRuleId,p.fullname as ptypeName,ifnull(p.pcategory,p2.pcategory) as pcategory,pu.unit_rate
        from pl_eshop_product_sku sku
        left join pl_eshop_product_sku_expand expand on sku.unique_id = expand.unique_id and sku.profile_id=
        expand.profile_id
        left join pl_eshop_product pm on pm.profile_id = sku.profile_id and
        pm.platform_num_id=sku.platform_num_id
        left join pl_eshop_stock_sync_rule pepsrc on pepsrc.profile_id=sku.profile_id and pepsrc.xcode
        =sku.platform_xcode and pepsrc.deleted=0
        left join base_ptype_xcode px ON px.profile_id = sku.profile_id and px.xcode=sku.platform_xcode
        left join base_ptype_unit pu on pu.profile_id = sku.profile_id and px.unit_id=pu.id
        LEFT JOIN base_ptype p ON p.id=px.ptype_id AND p.profile_id=px.profile_id
        left join base_ptype p2 ON p2.profile_id=sku.profile_id and p2.usercode = sku.platform_xcode
        where sku.profile_id=#{profileid} and sku.eshop_id=#{eshopId} and expand.mapping_type=#{mappingType}
        <if test="platformNumIds!=null and platformNumIds.size()>0">
            and sku.platform_num_id in
            <foreach collection="platformNumIds" item="numId" separator="," index="i" close=")" open="(">
                #{numId}
            </foreach>
        </if>
    </select>
    <sql id="skuMappingForSyncManual">
        select sku.id,pm.platform_fullname as platfullName,sku.profile_id,
               sku.eshop_id,sku.platform_sku_id,pm.platform_xcode as pmplatformXcode,pm.default_sku_id,
               mapping.sku_id,mapping.ptype_id,mapping.unit_id, sku.platform_Num_Id,
               sku.platform_properties_Name,sku.platform_properties,sku.platform_xcode,expand.mapping_type,
               sku.platform_xcode as platxcode,sku.platform_modified_time,sku.platform_pic_url as picUrl,
               p.fullname as ptypeName,p.pcategory,bpx.xcode,bpu.unit_rate, p.batchenabled as batchEnabled,
               pm.auto_shelf_on,pm.on_count,pm.off_count,pm.platform_stock_state as
               stockState,pm.has_properties,sku.platform_json,IF(ISNULL(mark.mark_code),0,1) as platformSkuStopped,
               case p.pcategory when 2 then p.usercode else pf.fullbarcode end as fullbarcode,mapping.sync_rule_id
               from pl_eshop_product_sku_mapping mapping
               left join pl_eshop_product_sku sku on sku.unique_id = mapping.unique_id and sku.profile_id= mapping.profile_id
               left join pl_eshop_product_sku_expand expand on expand.unique_id = mapping.unique_id and expand.profile_id=mapping.profile_id
               left join pl_eshop_product_mark mark on mark.unique_id = mapping.unique_id and mark.profile_id=mapping.profile_id
                         and mark.mark_code in ('1012','1014') and mark.eshop_id=mapping.eshop_id
               left join pl_eshop_product pm on pm.profile_id = mapping.profile_id and pm.platform_num_id=sku.platform_num_id and pm.eshop_id=sku.eshop_id
               left join base_ptype_xcode bpx on bpx.profile_id=mapping.profile_id and bpx.sku_id=mapping.sku_id and bpx.unit_id=mapping.unit_id
                 LEFT JOIN base_ptype p ON p.id = mapping.ptype_id AND p.profile_id = mapping.profile_id
                 LEFT JOIN base_ptype_unit bpu on bpu.profile_id = mapping.profile_id and bpu.id = mapping.unit_id
               left join base_ptype_fullbarcode pf on pf.profile_id=bpx.profile_id and pf.sku_id=bpx.sku_id and pf.unit_id = bpx.unit_id and pf.defaulted=1
        where mapping.profile_id=#{profileid} and mapping.eshop_id=#{eshopId}
          and expand.mapping_type=0 and p.deleted=0 and p.stoped=0 and p.pcategory != 2
    </sql>
    <sql id="skuMappingForSyncXcode">
        select sku.id,pm.platform_fullname as platfullName,sku.profile_id,
               sku.eshop_id,sku.platform_sku_id,pm.platform_xcode as pmplatformXcode,pm.default_sku_id,
               bpx.sku_id,bpx.ptype_id,bpx.unit_id, sku.platform_Num_Id,
               sku.platform_properties_Name,sku.platform_properties,sku.platform_xcode,expand.mapping_type,
               sku.platform_xcode as platxcode,sku.platform_modified_time,sku.platform_pic_url as picUrl,
               p.fullname as ptypeName,p.pcategory,bpx.xcode,bpu.unit_rate, p.batchenabled as batchEnabled,
               pm.auto_shelf_on,pm.on_count,pm.off_count,pm.platform_stock_state as
               stockState,pm.has_properties,sku.platform_json,IF(ISNULL(mark.mark_code),0,1) as platformSkuStopped,
               case p.pcategory when 2 then p.usercode else pf.fullbarcode end as fullbarcode,mapping.sync_rule_id
        from base_ptype_xcode bpx
                 LEFT JOIN base_ptype_unit bpu on bpu.profile_id = bpx.profile_id and bpu.id = bpx.unit_id
            left join pl_eshop_product_sku sku on sku.profile_id= bpx.profile_id and sku.platform_xcode = bpx.xcode
            left join pl_eshop_product_sku_expand expand on expand.unique_id = sku.unique_id and expand.profile_id=sku.profile_id
            left join pl_eshop_product_mark mark on mark.unique_id = sku.unique_id and mark.profile_id= sku.profile_id and
                              mark.mark_code in ('1012', '1014') and mark.eshop_id = sku.eshop_id
            left join pl_eshop_product pm on pm.profile_id = bpx.profile_id and pm.platform_num_id=sku.platform_num_id and pm.eshop_id=sku.eshop_id
                 LEFT JOIN base_ptype p ON p.id = bpx.ptype_id AND p.profile_id = bpx.profile_id
            left join base_ptype_fullbarcode pf on pf.profile_id=bpx.profile_id and pf.sku_id=bpx.sku_id and pf.unit_id = bpx.unit_id and pf.defaulted=1
            left join pl_eshop_product_sku_mapping mapping on sku.unique_id = mapping.unique_id and sku.profile_id= mapping.profile_id
        where bpx.profile_id=#{profileid} and sku.eshop_id=#{eshopId} and expand.mapping_type=1 and p.deleted=0 and p.stoped=0 and bpx.ptype_id>0 and p.pcategory != 2
          and bpx.xcode not in (select bpx.xcode from base_ptype_xcode bpx left join base_ptype bp on bp.profile_id=bpx.profile_id and bp.id=bpx.ptype_id
                                where bpx.profile_id=#{profileid} and bpx.info_type=1 and bp.stoped=0 and bp.deleted=0 and bp.pcategory = 2)
    </sql>


    <select id="queryRelationByRowId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select * from (
        <include refid="skuMappingForSyncManual"></include>
        <if test="platformSkuRowIds!=null and platformSkuRowIds.size()>0">
            and sku.id in
            <foreach collection="platformSkuRowIds" item="rowId" separator="," index="i" close=")" open="(">
                #{rowId}
            </foreach>
        </if>
        union
        <include refid="skuMappingForSyncXcode"></include>
        <if test="platformSkuRowIds!=null and platformSkuRowIds.size()>0">
            and sku.id in
            <foreach collection="platformSkuRowIds" item="rowId" separator="," index="i" close=")" open="(">
                #{rowId}
            </foreach>
        </if>
        union
        <include refid="ComboRelationForSyncManual"></include>
        <if test="platformSkuRowIds!=null and platformSkuRowIds.size()>0">
            and sku.id in
            <foreach collection="platformSkuRowIds" item="rowId" separator="," index="i" close=")" open="(">
                #{rowId}
            </foreach>
        </if>
        union
        <include refid="ComboRelationForSyncXCode"></include>
        <if test="platformSkuRowIds!=null and platformSkuRowIds.size()>0">
            and sku.id in
            <foreach collection="platformSkuRowIds" item="rowId" separator="," index="i" close=")" open="(">
                #{rowId}
            </foreach>
        </if>
        ) a
    </select>
    <select id="queryRelationBySkuId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select * from (
        <include refid="skuMappingForSyncManual"></include>
        <if test="skuIds!=null and skuIds.size()>0">
            and mapping.sku_id in
            <foreach collection="skuIds" item="skuId" separator="," index="i" close=")" open="(">
                #{skuId}
            </foreach>
        </if>
        union
        <include refid="skuMappingForSyncXcode"></include>
        <if test="skuIds!=null and skuIds.size()>0">
            and bpx.sku_id in
            <foreach collection="skuIds" item="skuId" separator="," index="i" close=")" open="(">
                #{skuId}
            </foreach>
        </if>
        ) a
        <if test="pageSize>0">
            limit #{currentIndex},#{pageSize}
        </if>
    </select>

    <select id="queryEshopRelation"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select * from (
        <include refid="skuMappingForSyncManual"></include>
        <if test="skuIndexId != null ">
            and sku.id > #{skuIndexId}
        </if>
        union
        <include refid="skuMappingForSyncXcode"></include>
        <if test="skuIndexId != null ">
            and sku.id > #{skuIndexId}
        </if>
        ) a order by id asc
        <if test="pageSize>0">
            limit #{pageSize}
        </if>
    </select>


    <select id="queryRelationBySkuIdJoinAbstractLog"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select distinct sku.id,sku.unique_id from
        left join pl_eshop_product_sku sku
        left join pl_eshop_stock_sync_abstract_log log on log.profile_id = sku.profile_id and log.platform_num_id
        =sku.platform_num_id and log.platform_sku_id = sku.platform_sku_id
        where sku.profile_id=#{profileid} and sku.eshop_id=#{eshopId}  and (log.update_time <![CDATA[<=]]> NOW() - INTERVAL #{hours} HOUR or log.update_time is null)
        and id>#{nowId}
        order by sku.id+0 asc
        <if test="pageSize>0">
            limit #{pageSize}
        </if>
    </select>

    <select id="queryRelationBySupplierProductDto"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        SELECT
        sku.id,
        pm.platform_fullname AS platfullName,
        sku.profile_id,
        sku.eshop_id,
        sku.platform_sku_id,
        pm.platform_xcode AS pmplatformXcode,
        pm.default_sku_id,
        mapping.sku_id,
        mapping.ptype_id,
        mapping.unit_id,
        sku.platform_Num_Id,
        sku.platform_properties_Name,
        sku.platform_properties,
        sku.platform_xcode,
        expand.mapping_type,
        sku.platform_xcode AS platxcode,
        sku.platform_modified_time,
        sku.platform_pic_url AS picUrl,
        p.fullname AS ptypeName,
        p.pcategory,
        bpx.xcode,
        bpu.unit_rate,
        p.batchenabled AS batchEnabled,
        pm.auto_shelf_on,
        pm.on_count,
        pm.off_count,
        pm.platform_stock_state AS stockState,
        pm.has_properties,
        sku.platform_json,
        sku.platform_unit_id,
        sku.platform_unit_name
        FROM pl_eshop_product_sku sku
        LEFT JOIN pl_eshop_product_sku_mapping mapping ON sku.unique_id = mapping.unique_id AND sku.profile_id =
        mapping.profile_id
        LEFT JOIN pl_eshop_product_sku_expand expand ON expand.unique_id = sku.unique_id AND expand.profile_id
        =sku.profile_id
        LEFT JOIN pl_eshop_product pm ON pm.profile_id = sku.profile_id AND pm.platform_num_id = sku.platform_num_id AND
        pm.eshop_id = sku.eshop_id
        LEFT JOIN base_ptype_xcode bpx ON bpx.profile_id = mapping.profile_id AND bpx.sku_id = mapping.sku_id AND
        bpx.unit_id = mapping.unit_id
        LEFT JOIN base_ptype p ON p.id = mapping.ptype_id AND p.profile_id = mapping.profile_id
        LEFT JOIN base_ptype_unit bpu ON bpu.profile_id = mapping.profile_id AND bpu.id = mapping.unit_id
        WHERE sku.profile_id = #{profileId}
        AND sku.eshop_id=#{eshopId}
        AND expand.mapping_type=0
        <if test="dtoList!=null and dtoList.size()>0">
            AND mapping.ptype_id IN
            <foreach collection="dtoList" item="dto" separator="," index="i" close=")" open="(">
                #{dto.ptypeId}
            </foreach>
        </if>
        <if test="dtoList!=null and dtoList.size()>0">
            AND mapping.sku_id IN
            <foreach collection="dtoList" item="dto" separator="," index="i" close=")" open="(">
                #{dto.skuId}
            </foreach>
        </if>
        <if test="dtoList!=null and dtoList.size()>0">
            AND mapping.unit_id IN
            <foreach collection="dtoList" item="dto" separator="," index="i" close=")" open="(">
                #{dto.unitId}
            </foreach>
        </if>
        <if test="filterString!=null and filterString!=''">
            AND (pm.platform_fullname LIKE CONCAT('%',#{filterString},'%')
            OR pm.platform_xcode LIKE CONCAT('%',#{filterString},'%')
            OR sku.platform_properties_name LIKE CONCAT('%',#{filterString},'%')
            OR sku.platform_xcode LIKE CONCAT('%',#{filterString},'%'))
        </if>
        UNION
        SELECT
        sku.id,
        pm.platform_fullname AS platfullName,
        sku.profile_id,
        sku.eshop_id,
        sku.platform_sku_id,
        pm.platform_xcode AS pmplatformXcode,
        pm.default_sku_id,
        bpx.sku_id,
        bpx.ptype_id,
        bpx.unit_id,
        sku.platform_num_id,
        sku.platform_properties_Name,
        sku.platform_properties,
        sku.platform_xcode,
        expand.mapping_type,
        sku.platform_xcode AS platxcode,
        sku.platform_modified_time,
        sku.platform_pic_url AS picUrl,
        p.fullname AS ptypeName,
        p.pcategory,
        bpx.xcode,
        bpu.unit_rate,
        p.batchenabled AS
        batchEnabled,
        pm.auto_shelf_on,
        pm.on_count,
        pm.off_count,
        pm.platform_stock_state AS stockState,
        pm.has_properties,
        sku.platform_json,
        sku.platform_unit_id,
        sku.platform_unit_name
        FROM pl_eshop_product_sku sku
        LEFT JOIN pl_eshop_product_sku_expand expand ON expand.unique_id = sku.unique_id AND
        expand.profile_id=sku.profile_id
        LEFT JOIN pl_eshop_product pm ON pm.profile_id = sku.profile_id AND pm.platform_num_id=sku.platform_num_id AND
        pm.eshop_id=sku.eshop_id
        LEFT JOIN base_ptype_xcode bpx ON sku.profile_id= bpx.profile_id AND sku.platform_xcode = bpx.xcode
        LEFT JOIN base_ptype_unit bpu ON bpu.profile_id=bpx.profile_id AND bpu.id=bpx.unit_id
        LEFT JOIN base_ptype p ON p.id=bpx.ptype_id AND p.profile_id=bpx.profile_id
        WHERE sku.profile_id=#{profileId}
        AND sku.eshop_id=#{eshopId}
        AND expand.mapping_type=1
        <if test="dtoList!=null and dtoList.size()>0">
            AND bpx.ptype_id IN
            <foreach collection="dtoList" item="dto" separator="," index="i" close=")" open="(">
                #{dto.ptypeId}
            </foreach>
        </if>
        <if test="dtoList!=null and dtoList.size()>0">
            AND bpx.sku_id IN
            <foreach collection="dtoList" item="dto" separator="," index="i" close=")" open="(">
                #{dto.skuId}
            </foreach>
        </if>
        <if test="dtoList!=null and dtoList.size()>0">
            AND bpx.unit_id IN
            <foreach collection="dtoList" item="dto" separator="," index="i" close=")" open="(">
                #{dto.unitId}
            </foreach>
        </if>
        <if test="dtoList!=null and dtoList.size()>0">
            AND bpx.xcode IN
            <foreach collection="dtoList" item="dto" separator="," index="i" close=")" open="(">
                #{dto.xcode}
            </foreach>
        </if>
        <if test="filterString!=null and filterString!=''">
            AND (pm.platform_fullname LIKE CONCAT('%',#{filterString},'%')
            OR pm.platform_xcode LIKE CONCAT('%',#{filterString},'%')
            OR sku.platform_properties_name LIKE CONCAT('%',#{filterString},'%')
            OR sku.platform_xcode LIKE CONCAT('%',#{filterString},'%'))
        </if>
    </select>

    <select id="queryComboRelationBySupplierProductDto"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        SELECT
        sku.id,
        pm.platform_fullname AS platfullName,
        sku.profile_id,
        sku.eshop_id,
        sku.platform_sku_id,
        pm.platform_xcode AS pmplatformXcode,
        pm.default_sku_id,
        mapping.sku_id,
        mapping.ptype_id,
        mapping.unit_id,
        sku.platform_Num_Id,
        sku.platform_properties_Name,
        sku.platform_properties,
        sku.platform_xcode,
        expand.mapping_type,
        sku.platform_xcode AS platxcode,
        sku.platform_modified_time,
        sku.platform_pic_url AS picUrl,
        p.fullname AS ptypeName,
        p.pcategory,
        bpx.xcode,
        bpu.unit_rate,
        p.batchenabled AS batchEnabled,
        pm.auto_shelf_on,
        pm.on_count,
        pm.off_count,
        pm.platform_stock_state AS stockState,
        pm.has_properties,
        sku.platform_json,
        sku.platform_unit_id,
        sku.platform_unit_name
        FROM pl_eshop_product_sku sku
        LEFT JOIN pl_eshop_product_sku_mapping mapping ON sku.unique_id = mapping.unique_id AND sku.profile_id =
        mapping.profile_id
        LEFT JOIN pl_eshop_product_sku_expand expand ON expand.unique_id = sku.unique_id AND expand.profile_id
        =sku.profile_id
        LEFT JOIN pl_eshop_product pm ON pm.profile_id = sku.profile_id AND pm.platform_num_id = sku.platform_num_id AND
        pm.eshop_id = sku.eshop_id
        LEFT JOIN base_ptype_xcode bpx ON bpx.profile_id = mapping.profile_id AND bpx.sku_id = mapping.sku_id AND
        bpx.unit_id = mapping.unit_id
        LEFT JOIN base_ptype p ON p.id = mapping.ptype_id AND p.profile_id = mapping.profile_id
        LEFT JOIN base_ptype_unit bpu ON bpu.profile_id = mapping.profile_id AND bpu.id = mapping.unit_id
        WHERE sku.profile_id = #{profileId}
        AND sku.eshop_id=#{eshopId}
        AND expand.mapping_type=0
        <if test="dtoList!=null and dtoList.size()>0">
            AND mapping.ptype_id IN
            <foreach collection="dtoList" item="dto" separator="," index="i" close=")" open="(">
                #{dto.ptypeId}
            </foreach>
        </if>
        <if test="filterString!=null and filterString!=''">
            AND (pm.platform_fullname LIKE CONCAT('%',#{filterString},'%')
            OR pm.platform_xcode LIKE CONCAT('%',#{filterString},'%')
            OR sku.platform_properties_name LIKE CONCAT('%',#{filterString},'%')
            OR sku.platform_xcode LIKE CONCAT('%',#{filterString},'%'))
        </if>
        UNION
        SELECT
        sku.id,
        pm.platform_fullname AS platfullName,
        sku.profile_id,
        sku.eshop_id,
        sku.platform_sku_id,
        pm.platform_xcode AS pmplatformXcode,
        pm.default_sku_id,
        bpx.sku_id,
        bpx.ptype_id,
        bpx.unit_id,
        sku.platform_num_id,
        sku.platform_properties_Name,
        sku.platform_properties,
        sku.platform_xcode,
        expand.mapping_type,
        sku.platform_xcode AS platxcode,
        sku.platform_modified_time,
        sku.platform_pic_url AS picUrl,
        p.fullname AS ptypeName,
        p.pcategory,
        bpx.xcode,
        bpu.unit_rate,
        p.batchenabled AS
        batchEnabled,
        pm.auto_shelf_on,
        pm.on_count,
        pm.off_count,
        pm.platform_stock_state AS stockState,
        pm.has_properties,
        sku.platform_json,
        sku.platform_unit_id,
        sku.platform_unit_name
        FROM pl_eshop_product_sku sku
        LEFT JOIN pl_eshop_product_sku_expand expand ON expand.unique_id = sku.unique_id AND
        expand.profile_id=sku.profile_id
        LEFT JOIN pl_eshop_product pm ON pm.profile_id = sku.profile_id AND pm.platform_num_id=sku.platform_num_id AND
        pm.eshop_id=sku.eshop_id
        LEFT JOIN base_ptype_xcode bpx ON sku.profile_id= bpx.profile_id AND sku.platform_xcode = bpx.xcode
        LEFT JOIN base_ptype_unit bpu ON bpu.profile_id=bpx.profile_id AND bpu.id=bpx.unit_id
        LEFT JOIN base_ptype p ON p.id=bpx.ptype_id AND p.profile_id=bpx.profile_id
        WHERE sku.profile_id=#{profileId}
        AND sku.eshop_id=#{eshopId}
        AND expand.mapping_type=1
        <if test="dtoList!=null and dtoList.size()>0">
            AND bpx.ptype_id IN
            <foreach collection="dtoList" item="dto" separator="," index="i" close=")" open="(">
                #{dto.ptypeId}
            </foreach>
        </if>
        <if test="dtoList!=null and dtoList.size()>0">
            AND bpx.xcode IN
            <foreach collection="dtoList" item="dto" separator="," index="i" close=")" open="(">
                #{dto.xcode}
            </foreach>
        </if>
        <if test="filterString!=null and filterString!=''">
            AND (pm.platform_fullname LIKE CONCAT('%',#{filterString},'%')
            OR pm.platform_xcode LIKE CONCAT('%',#{filterString},'%')
            OR sku.platform_properties_name LIKE CONCAT('%',#{filterString},'%')
            OR sku.platform_xcode LIKE CONCAT('%',#{filterString},'%'))
        </if>
    </select>

    <select id="queryRelationByComboId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        <include refid="ComboRelationForSyncManual"></include>
        <if test="comboIds!=null and comboIds.size()>0">
            and mapping.ptype_id in
            <foreach collection="comboIds" item="comboId" separator="," index="i" close=")" open="(">
                #{comboId}
            </foreach>
        </if>
        union
        <include refid="ComboRelationForSyncXCode"></include>
        <if test="comboIds!=null and comboIds.size()>0">
            and p.id in
            <foreach collection="comboIds" item="comboId" separator="," index="i" close=")" open="(">
                #{comboId}
            </foreach>
        </if>
    </select>

    <sql id="ComboRelationForSyncManual">
        select sku.id,pm.platform_fullname as platfullName,sku.profile_id,
               sku.eshop_id,sku.platform_sku_id,pm.platform_xcode as pmplatformXcode,pm.default_sku_id,
               mapping.sku_id,mapping.ptype_id,mapping.unit_id,sku.platform_Num_Id,
               sku.platform_properties_Name,sku.platform_properties,sku.platform_xcode,expand.mapping_type,
               sku.platform_xcode as platxcode,sku.platform_modified_time,sku.platform_pic_url as picUrl,
               p.fullname as ptypeName,p.pcategory,p.usercode as xcode,1 as unit_rate, p.batchenabled as batchEnabled,
               pm.auto_shelf_on,pm.on_count,pm.off_count,pm.platform_stock_state as
               stockState,pm.has_properties,sku.platform_json,IF(ISNULL(mark.mark_code),0,1) as platformSkuStopped,p.usercode as fullbarcode,
               mapping.sync_rule_id
        from pl_eshop_product_sku_mapping mapping
                 left join pl_eshop_product_sku sku on sku.unique_id = mapping.unique_id and sku.profile_id= mapping.profile_id
                 left join pl_eshop_product_sku_expand expand on expand.unique_id = sku.unique_id and expand.profile_id=sku.profile_id
                 left join pl_eshop_product_mark mark on mark.unique_id = mapping.unique_id and mark.profile_id=mapping.profile_id
            and mark.mark_code in ('1012','1014') and mark.eshop_id=mapping.eshop_id
                 left join pl_eshop_product pm on pm.profile_id = mapping.profile_id and pm.platform_num_id=sku.platform_num_id AND pm.eshop_id = sku.eshop_id
                 LEFT JOIN base_ptype p ON p.id=mapping.ptype_id AND p.profile_id=mapping.profile_id
        where sku.profile_id=#{profileid} and sku.eshop_id=#{eshopId} and expand.mapping_type=0 and p.deleted=0 and
            p.stoped=0 and  p.pcategory=2
    </sql>

    <sql id="ComboRelationForSyncXCode">
        select sku.id,pm.platform_fullname as platfullName,sku.profile_id,
               sku.eshop_id,sku.platform_sku_id,pm.platform_xcode as pmplatformXcode,pm.default_sku_id,
               0 as sku_id,p.id as ptype_id,0 as unit_id, sku.platform_Num_Id,
               sku.platform_properties_Name,sku.platform_properties,sku.platform_xcode,expand.mapping_type,
               sku.platform_xcode as platxcode,sku.platform_modified_time,sku.platform_pic_url as picUrl,
               p.fullname as ptypeName,p.pcategory,bpx.xcode as xcode,1 as unit_rate, p.batchenabled as batchEnabled,
               pm.auto_shelf_on,pm.on_count,pm.off_count,pm.platform_stock_state as
                   stockState,pm.has_properties,sku.platform_json,IF(ISNULL(mark.mark_code),0,1) as platformSkuStopped,
               p.usercode as fullbarcode,mapping.sync_rule_id
        from base_ptype_xcode bpx
                 left join base_ptype p on p.profile_id=bpx.profile_id and p.id=bpx.ptype_id
                 left join pl_eshop_product_sku sku on sku.profile_id= p.profile_id and sku.platform_xcode = bpx.xcode
                 left join pl_eshop_product_sku_expand expand on expand.unique_id = sku.unique_id and expand.profile_id=sku.profile_id
                 left join pl_eshop_product_mark mark on mark.unique_id = sku.unique_id and mark.profile_id= sku.profile_id
            and mark.mark_code in ('1012','1014') and mark.eshop_id=sku.eshop_id
                 left join pl_eshop_product pm on pm.profile_id = p.profile_id and pm.platform_num_id=sku.platform_num_id AND pm.eshop_id = sku.eshop_id
                 left join pl_eshop_product_sku_mapping mapping on sku.unique_id = mapping.unique_id and sku.profile_id= mapping.profile_id
        where p.profile_id=#{profileid} and sku.eshop_id=#{eshopId} and expand.mapping_type=1 and p.deleted=0 and
            p.stoped=0 and bpx.id>0
          and sku.platform_xcode !='' and p.pcategory=2
    </sql>

    <select id="queryRelationJoinAbstractLog"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select sku.id,
               pm.platform_fullname             as platfullName,
               pm.default_sku_id,
               sku.profile_id,
               log.create_time,
               sku.eshop_id,
               sku.platform_sku_id,
               0                                as sku_id,
               p.id                             as ptype_id,
               0                                as unit_id,
               sku.platform_num_id,
               sku.platform_properties_Name,
               sku.platform_properties,
               sku.platform_xcode,
               expand.mapping_type,
               sku.platform_xcode               as platxcode,
               sku.platform_modified_time,
               sku.platform_pic_url             as picUrl,
               p.fullname                       as
                                                   ptypeName,
               p.pcategory,
               bpx.xcode                        as xcode,
               pm.platform_xcode                as
                                                   pmplatformXcode,
               pm.auto_shelf_on,
               pm.on_count,
               pm.off_count,
               pm.platform_stock_state          as
                                                   stockState,
               pm.has_properties,
               sku.platform_json,
               IF(ISNULL(mark.mark_code), 0, 1) as platformSkuStopped
        from base_ptype_xcode bpx
                 left join base_ptype p on p.profile_id = bpx.profile_id and p.id = bpx.ptype_id
                 left join pl_eshop_product_sku sku on sku.profile_id = p.profile_id and sku.platform_xcode = bpx.xcode
                 left join pl_eshop_product_sku_expand expand
                           on expand.unique_id = sku.unique_id and expand.profile_id =
                                                                   sku.profile_id
                 left join pl_eshop_stock_sync_abstract_log log
                           on log.profile_id = sku.profile_id and log.platform_num_id = sku.platform_num_id and
                              log.platform_sku_id = sku.platform_sku_id
                 left join pl_eshop_product_mark mark
                           on mark.unique_id = sku.unique_id and mark.profile_id = sku.profile_id and
                              mark.mark_code = 1012 and mark.eshop_id = sku.eshop_id
                 left join pl_eshop_product pm
                           on pm.profile_id = p.profile_id and pm.platform_num_id = sku.platform_num_id
        where p.profile_id = #{profileid}
          and sku.eshop_id = #{eshopId}
          and expand.mapping_type = 1
          and p.deleted = 0
          and p.stoped = 0
          and bpx.id > 0
          and bpx.info_type = 1
          and sku.platform_xcode != ''
          and log.create_time <![CDATA[<=]]> NOW() - INTERVAL #{hours} HOUR
        group by sku.platform_num_id, sku.platform_sku_id
    </select>

    <select id="querySkuMappingByListNumids"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select * from pl_eshop_product_sku sku
        where sku.profile_id = #{profileId}
        and sku.eshop_id = #{eshopId}
        <if test="numIds!=null and numIds.size>0">
            and sku.platform_num_id in
            <foreach collection="numIds" item="numId" separator="," index="i" close=")" open="(">
                #{numId}
            </foreach>
        </if>
        <if test="skuIds!=null and skuIds.size>0 and numIds!=null and numIds.size>0">
            or sku.platform_sku_id in
            <foreach collection="skuIds" item="skuId" separator="," index="i" close=")" open="(">
                #{skuId}
            </foreach>
        </if>
        <if test="skuIds!=null and skuIds.size>0 and numIds.size==0">
            and sku.platform_sku_id in
            <foreach collection="skuIds" item="skuId" separator="," index="i" close=")" open="(">
                #{skuId}
            </foreach>
        </if>
    </select>


    <select id="queryEshopSkuMappingByList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select m.platform_fullname as platfullname,m.platform_pic_url as platformPicUrl,m.platform_xcode as
        xcode,m.platform_price as price,sku.id,
        expand.ready_pfullname,expand.download_enable,
        expand.ready_sku_name,sku.profile_Id, expand.mark,sku.eshop_id,sku.platform_sku_id,
        sku.platform_Num_Id,
        m.default_sku_id,platform_properties_Name,platform_properties,0 as
        isredundant,sku.platform_xcode,
        sku.platform_xcode as platxcode,sku.platform_modified_time,m.platform_stock_state as
        stock_state,expand.ready_sku_name,
        expand.ready_sku_xcode,expand.ready_pfullname,expand.ready_pusercode,sku.platform_pic_url as picUrl
        from pl_eshop_product_sku sku
        left join pl_eshop_product_sku_expand expand on sku.unique_id = expand.unique_id and sku.profile_id=
        expand.profile_id
        join pl_eshop_product m on m.platform_num_id = sku.platform_num_id and m.profile_id = sku.profile_id and
        m.eshop_id = sku.eshop_id
        where sku.profile_id = #{profileId}
        and sku.eshop_id = #{eshopId}
        <if test="numIds!=null">
            and sku.platform_num_id in
            <foreach collection="numIds" item="numId" separator="," index="i" close=")" open="(">
                #{numId}
            </foreach>
        </if>
        <if test="skuIds!=null">
            and sku.platform_sku_id in
            <foreach collection="skuIds" item="skuId" separator="," index="i" close=")" open="(">
                #{skuId}
            </foreach>
        </if>
    </select>

    <select id="queryEshopSkuMappingByProperties"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select m.platform_fullname as platfullname,m.platform_pic_url as platformPicUrl,m.platform_xcode as
        pmplatformXcode,m.platform_price as price,sku.id,
        expand.ready_pfullname,expand.download_enable,
        expand.ready_sku_name,sku.profile_Id, expand.mark,sku.eshop_id,sku.platform_sku_id,
        sku.platform_Num_Id,sku.platform_full_properties,sku.memo_platform_full_properties as hasMemoFullPropertiesName,
        m.default_sku_id,sku.platform_properties_name,platform_properties,0 as
        isredundant,sku.platform_xcode,
        sku.platform_xcode as platxcode,sku.platform_modified_time,m.platform_stock_state as
        stock_state,expand.ready_sku_name,
        expand.ready_sku_xcode,expand.ready_pfullname,expand.ready_pusercode,sku.platform_pic_url as picUrl
        from pl_eshop_product_sku sku
        left join pl_eshop_product_sku_expand expand on sku.unique_id = expand.unique_id and sku.profile_id=
        expand.profile_id
        join pl_eshop_product m on m.platform_num_id = sku.platform_num_id and m.profile_id = sku.profile_id and
        m.eshop_id = sku.eshop_id
        where sku.profile_id = #{profileId}
        and sku.eshop_id = #{eshopId}
        <if test="numIds!=null">
            and sku.platform_num_id in
            <foreach collection="numIds" item="numId" separator="," index="i" close=")" open="(">
                #{numId}
            </foreach>
        </if>
        <if test="platformPropertiesName!=null">
            and sku.platform_properties_name in
            <foreach collection="platformPropertiesName" item="propties" separator="," index="i" close=")" open="(">
                #{propties}
            </foreach>
        </if>
    </select>

    <select id="queryEshopappingByFullName"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select m.platform_fullname as platfullname,m.platform_pic_url as platformPicUrl,m.platform_xcode as
        xcode,m.platform_price as price,sku.id,
        expand.ready_pfullname,expand.download_enable,
        expand.ready_sku_name,sku.profile_Id, expand.mark,sku.eshop_id,sku.platform_sku_id,
        sku.platform_Num_Id,
        m.default_sku_id,platform_properties_Name,platform_properties,0 as
        isredundant,sku.platform_xcode,
        sku.platform_xcode as platxcode,sku.platform_modified_time,m.platform_stock_state as
        stock_state,expand.ready_sku_name,
        expand.ready_sku_xcode,expand.ready_pfullname,expand.ready_pusercode,sku.platform_pic_url as picUrl
        from pl_eshop_product_sku sku
        left join pl_eshop_product_sku_expand expand on sku.unique_id = expand.unique_id and sku.profile_id=
        expand.profile_id
        join pl_eshop_product m on m.platform_num_id = sku.platform_num_id and m.profile_id = sku.profile_id and
        m.eshop_id = sku.eshop_id
        where sku.profile_id = #{profileId}
        and sku.eshop_id = #{eshopId}
        <if test="fullNames !=null">
            and m.platform_fullname in
            <foreach collection="fullNames" item="fullName" separator="," index="i" close=")" open="(">
                #{fullName}
            </foreach>
        </if>
    </select>

    <select id="queryEshopSkuMapping"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select sku.*,expand.mapping_type from pl_eshop_product_sku sku
        left join pl_eshop_product_sku_expand expand on expand.profile_id=sku.profile_id and
        expand.eshop_id=sku.eshop_id and expand.unique_id=sku.unique_id
        where sku.profile_id = #{profileId}
        and sku.eshop_id = #{eshopId}
        and sku.platform_sku_id in
        <foreach collection="platformSkuId" item="skuid" separator="," index="i" close=")" open="(">
            #{skuid}
        </foreach>
    </select>

    <select id="queryEshopSkuMappingByFastRelation"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select sku.*, expand.mapping_type
        from pl_eshop_product_sku sku
                 left join pl_eshop_product p on p.profile_id = sku.profile_id and p.eshop_id = sku.eshop_id and
                                                 p.platform_num_id = sku.platform_num_id
                 left join pl_eshop_product_sku_expand expand on expand.profile_id = sku.profile_id and
                                                                 expand.eshop_id = sku.eshop_id and
                                                                 expand.unique_id = sku.unique_id
        where sku.profile_id = #{profileId}
          and sku.eshop_id = #{eshopId}
          and sku.platform_sku_id = #{platformSkuId}
          and p.platform_fullname = #{platName}
    </select>

    <select id="queryEshopSkuMappingByNumIdAndSkuId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select sku.*, expand.mapping_type
        from pl_eshop_product_sku sku
                 left join pl_eshop_product_sku_expand expand
                           on expand.profile_id = sku.profile_id and expand.eshop_id = sku.eshop_id and
                              expand.unique_id = sku.unique_id
        where sku.profile_id = #{profileId}
          and sku.eshop_id = #{eshopId}
          and sku.platform_num_id = #{platformNumId}
          and sku.platform_sku_id = #{platformSkuId}
    </select>


    <select id="queryEshopSkuMappingByWhere"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select * from pl_eshop_product_sku sku
        left join pl_eshop_product mp on mp.platform_num_id = sku.platform_num_id and mp.eshop_id = sku.eshop_id and
        mp.profile_id=sku.profile_id
        where sku.profile_id = #{profileId}
        and sku.eshop_id = #{eshopId}
        and mp.platform_fullname=#{platName}
        <if test="platformNumId !=null and platformNumId!=''">
            and platform_num_id=#{platformNumId}
        </if>
        <if test="platformPropertiesName != null and platformPropertiesName != ''">
            and platform_properties_name=#{platformPropertiesName}
        </if>
        <if test="platformSkuId !='' and platformSkuId !=null">
            and sku.platform_sku_id =#{platformSkuId}
        </if>
    </select>

    <select id="queryEshopSkuMappingByfullname"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select sku.*
        from pl_eshop_product mapping
                 left join pl_eshop_product_sku sku
                           on sku.platform_num_id = mapping.platform_num_id and sku.eshop_id = mapping.eshop_id and
                              sku.profile_id = mapping.profile_id
        where sku.profile_id = #{profileId}
          and sku.eshop_id = #{eshopId}
          and mapping.platform_fullname = #{fullName}
    </select>

    <select id="queryEshopSkuMappingByNumId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select * from pl_eshop_product_sku sku
        where sku.profile_id = #{profileId}
        and sku.eshop_id = #{eshopId}
        and sku.platform_num_id in
        <foreach collection="platformNumId" item="numid" separator="," index="i" close=")" open="(">
            #{numid}
        </foreach>
    </select>

    <select id="queryEshopSkuMappingCount"
            resultType="java.lang.Integer">
        select count(*)
        from pl_eshop_product_sku sku
                 left join pl_eshop_product_sku_mapping mapping
                           on mapping.profile_id = sku.profile_id and mapping.eshop_id = sku.eshop_id and
                              mapping.unique_id = sku.unique_id
        where sku.profile_id = #{profileId}
          and sku.eshop_id = #{eshopId}
          and mapping.ptype_id > 0
    </select>
    <select id="queryEshopSkuMappingByLimit"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select sku.*,
               mapping.ptype_id,
               mapping.sku_id,
               mapping.unit_id,
               mapping.pcategory,
               mapping.xcode,
               mapping.platform_full_properties_name
        from pl_eshop_product_sku sku
                 left join pl_eshop_product_sku_mapping mapping
                           on mapping.profile_id = sku.profile_id and mapping.eshop_id = sku.eshop_id and
                              mapping.unique_id = sku.unique_id
        where sku.profile_id = #{profileId}
          and sku.eshop_id = #{eshopId}
          and mapping.ptype_id > 0
        limit #{currentIndex}, #{pageSize}
    </select>

    <select id="queryEshopSkuMappingByskuIdAndNumid"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select sku.*, m.platform_fullname,m.platform_xcode as platxcode,m.platform_fullname as platname
        from pl_eshop_product_sku sku
        join pl_eshop_product m
        on m.platform_num_id = sku.platform_num_id and m.profile_id = sku.profile_id and
        m.eshop_id = sku.eshop_id
        where sku.profile_id = #{profileId}
        and sku.eshop_id = #{eshopId}
        and sku.platform_num_id = #{numId}
        <if test="platformSkuId != null and platformSkuId != ''">
            and sku.platform_sku_id = #{platformSkuId}
        </if>
        <if test="platformPropertiesName != null and platformPropertiesName != ''">
            and sku.platform_properties_name=#{platformPropertiesName}
        </if>
        limit 1
    </select>

    <select id="queryEshopProductmarkByMarkCode" resultType="java.lang.Integer">
        select count(*) from pl_eshop_product_mark where profile_id=#{profileId} AND mark_code=#{markCode} and eshop_id
        = #{eshopId}
        <if test="uniqueId != null and uniqueId != ''">
            AND unique_id=#{uniqueId}
        </if>
        <if test="platformNumId != null and platformNumId != ''">
            AND platform_num_id=#{platformNumId}
        </if>
    </select>

    <select id="queryEshopProductmarkByMarkCodeAndNumid"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMark">
        select profile_id,eshop_id,platform_num_id,mark_code,unique_id from pl_eshop_product_mark where profile_id=#{profileId}
        and eshop_id
        = #{eshopId}
        <if test="markCodes != null and markCodes.size() >0 ">
            AND mark_code in
            <foreach collection="markCodes" item="code" close=")" open="(" separator=",">
                #{code}
            </foreach>
        </if>
        <if test="platformNumIds != null and platformNumIds.size() >0 ">
            AND platform_num_id in
            <foreach collection="platformNumIds" item="numid" close=")" open="(" separator=",">
                #{numid}
            </foreach>
        </if>
    </select>


    <select id="loadEshopProductMappingList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMapping">
        select * from pl_eshop_product where profile_id=#{profileId} and eshop_id=#{eshopId}
        <if test="numIds!=null">
            and platform_num_id in
            <foreach collection="numIds" item="id" separator="," index="i" close=")" open="(">
                #{id}
            </foreach>
        </if>
        limit #{currentIndex}, #{pageSize}
    </select>


    <select id="getProductByNumId" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMapping">
        select * from pl_eshop_product where profile_id=#{profileId} and eshop_id=#{eshopId}
        <if test="numIds!=null">
            and platform_num_id =#{numIds}
        </if>
    </select>

    <select id="getProductSkuByUniqueId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select sku.*,pm.platform_fullname as platfullname from pl_eshop_product_sku sku
        left join pl_eshop_product pm on pm.profile_id=sku.profile_id and pm.eshop_id=sku.eshop_id and
        pm.platform_num_id=sku.platform_num_id
        where sku.profile_id=#{profileId} and sku.eshop_id=#{eshopId}
        <if test="uniqueId!=null">
            and sku.unique_id =#{uniqueId}
        </if>
    </select>

    <select id="loadEshopSkuMappingItem"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select
        <include refid="skuRelation"></include>
        from pl_eshop_product_sku psm
        left join pl_eshop_product_sku_mapping mapping on mapping.profile_id= psm.profile_id and mapping.unique_id =
        psm.unique_id and mapping.eshop_id = psm.eshop_id
        LEFT JOIN `base_ptype_fullbarcode` bpf ON bpf.profile_id = psm.profile_id
        AND bpf.sku_id = mapping.sku_id AND bpf.ptype_id = mapping.ptype_id AND bpf.unit_id = mapping.unit_id
        left join pl_eshop_product pm on pm.profile_id=psm.profile_id and pm.eshop_id=psm.eshop_id and
        pm.platform_num_id=psm.platform_num_id
        left join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=
        expand.profile_id
        left join pl_eshop_product_sku_rule_config pepsrc on pepsrc.profile_id=psm.profile_id and
        pepsrc.eshop_id=psm.eshop_id and pepsrc.platform_num_id=psm.platform_num_id and
        pepsrc.platform_properties=psm.platform_properties_name and pepsrc.warehouse_code=pm.warehouse_code
        left join base_ptype_unit unit on unit.id=mapping.unit_id and unit.profile_id=psm.profile_id
        left join base_ptype_xcode bpx on psm.profile_id = bpx.profile_id and mapping.unit_id=bpx.unit_id and
        mapping.sku_id=bpx.sku_id and bpx.defaulted=1
        left join base_ptype p on p.profile_id=psm.profile_id and p.id=mapping.ptype_id and p.pcategory!=2
        left join pl_eshop_product_mark mark on psm.profile_id= mark.profile_id and psm.eshop_id = mark.eshop_id
        AND psm.platform_num_id=mark.platform_num_id and psm.platform_sku_id = mark.platform_sku_id
        left join (select cdlp.profile_id as profileId,
        cdlp.resource_id as ptypeId,
        clv.labelfield_value as labelfieldValue
        from cf_data_label_ptype cdlp
        left join Cf_Labelfield_Value clv
        on clv.id = cdlp.labelfield_value_id
        and clv.profile_id = cdlp.profile_id
        where clv.labelfield_value = '代销'
        and cdlp.profile_id = #{profileId}) temp on temp.profileId = psm.profile_id and temp.ptypeId = p.id
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId}  and psm.storage_type=0
        <choose>
            <when test="uniqueIdList!=null and uniqueIdList.size()>0">
                and psm.unique_id in
                <foreach collection="uniqueIdList" close=")" open="(" separator="," item="uniqueId">
                    #{uniqueId}
                </foreach>
            </when>
            <otherwise>
                <if test="id!=null">
                    and psm.id=#{id}
                </if>
                <if test="xcode!=null">
                    and psm.platform_xcode=#{xcode}
                </if>
                <if test="numId!=null and numId!=''">
                    and psm.platform_num_id=#{numId}
                </if>
                <if test="properties!=null and properties!=''">
                    and psm.platform_properties_name=#{properties}
                </if>
                <if test="platformSkuId!=null and platformSkuId!=''">
                    and psm.platform_sku_id=#{platformSkuId}
                </if>
                <if test="refreshVersion!=null">
                    and CAST(psm.refresh_version AS UNSIGNED) &lt; #{refreshVersion}
                </if>
                <if test="platformNumIdList!=null and platformNumIdList.size()>0">
                    and psm.platform_num_id in
                    <foreach collection="platformNumIdList" close=")" open="(" separator="," item="numId">
                        #{numId}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        order by mapping.ptype_id desc
        limit 1
    </select>


    <select id="loadEshopSkuMappingItemList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select
        <include refid="skuRelation"></include>
        from pl_eshop_product_sku psm
        left join pl_eshop_product_sku_mapping mapping on mapping.profile_id= psm.profile_id and mapping.unique_id =
        psm.unique_id and mapping.eshop_id = psm.eshop_id
        LEFT JOIN `base_ptype_fullbarcode` bpf ON bpf.profile_id = psm.profile_id
        AND bpf.sku_id = mapping.sku_id AND bpf.ptype_id = mapping.ptype_id AND bpf.unit_id = mapping.unit_id
        left join pl_eshop_product pm on pm.profile_id=psm.profile_id and pm.eshop_id=psm.eshop_id and
        pm.platform_num_id=psm.platform_num_id
        left join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=
        expand.profile_id
        left join pl_eshop_product_sku_rule_config pepsrc on pepsrc.profile_id=psm.profile_id and
        pepsrc.eshop_id=psm.eshop_id and pepsrc.platform_num_id=psm.platform_num_id and
        pepsrc.platform_properties=psm.platform_properties_name and pepsrc.warehouse_code=pm.warehouse_code
        left join base_ptype_unit unit on unit.id=mapping.unit_id and unit.profile_id=psm.profile_id
        left join base_ptype_xcode bpx on psm.profile_id = bpx.profile_id and mapping.unit_id=bpx.unit_id and
        mapping.sku_id=bpx.sku_id and bpx.defaulted=1
        left join base_ptype p on p.profile_id=psm.profile_id and p.id=mapping.ptype_id and p.pcategory!=2
        left join pl_eshop_product_mark mark on psm.profile_id= mark.profile_id and psm.eshop_id = mark.eshop_id
        AND psm.platform_num_id=mark.platform_num_id and psm.platform_sku_id = mark.platform_sku_id
        left join (select cdlp.profile_id as profileId,
        cdlp.resource_id as ptypeId,
        clv.labelfield_value as labelfieldValue
        from cf_data_label_ptype cdlp
        left join Cf_Labelfield_Value clv
        on clv.id = cdlp.labelfield_value_id
        and clv.profile_id = cdlp.profile_id
        where clv.labelfield_value = '代销'
        and cdlp.profile_id = #{profileId}) temp on temp.profileId = psm.profile_id and temp.ptypeId = p.id
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId}
        <choose>
            <when test="uniqueIdList!=null and uniqueIdList.size()>0">
                and psm.unique_id in
                <foreach collection="uniqueIdList" close=")" open="(" separator="," item="uniqueId">
                    #{uniqueId}
                </foreach>
            </when>
            <otherwise>
                <if test="platformNumIdList!=null and platformNumIdList.size()>0">
                    and psm.platform_num_id in
                    <foreach collection="platformNumIdList" close=")" open="(" separator="," item="numId">
                        #{numId}
                    </foreach>
                </if>
                <if test="platformSkuIdList!=null and platformSkuIdList.size()>0">
                    and psm.platform_sku_id in
                    <foreach collection="platformSkuIdList" close=")" open="(" separator="," item="skuId">
                        #{skuId}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        order by mapping.ptype_id desc
    </select>

    <select id="getEshopSkuByNumIdAndRefreshVersion"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select
        psm.profile_id,
        psm.id,
        mapping.ptype_id,
        mapping.unit_id,
        mapping.sku_id,
        psm.platform_sku_id,
        psm.platform_num_id,
        psm.platform_properties_name,
        psm.platform_xcode,
        psm.platform_properties,
        psm.platform_pic_url,
        psm.platform_json,
        psm.platform_modified_time,
        psm.platform_modified_time as modified_time,
        psm.update_time,
        psm.create_time,
        psm.unique_id,
        psm.eshop_id,
        (case when IFNULL(p.id,0)=0 then false  else true end) as isbind,
        pm.default_sku_id,
        pm.platform_fullname as platFullName,
        bpx.xcode,
        p.fullname as ptypeName
        from pl_eshop_product_sku psm
        join pl_eshop_product pm on pm.profile_id=psm.profile_id and pm.eshop_id=psm.eshop_id and pm.platform_num_id=psm.platform_num_id
        left join pl_eshop_product_sku_mapping mapping on mapping.profile_id= psm.profile_id and mapping.unique_id = psm.unique_id and mapping.eshop_id = psm.eshop_id
        left join base_ptype_xcode bpx on psm.profile_id = bpx.profile_id and mapping.unit_id=bpx.unit_id and mapping.sku_id=bpx.sku_id and bpx.defaulted=1
        left join base_ptype p on p.profile_id=psm.profile_id and p.id=mapping.ptype_id and p.pcategory!=2 and p.deleted =1 and p.stoped =1
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId} and psm.storage_type = 0
        <if test="refreshVersion!=null">
            and CAST(psm.refresh_version AS UNSIGNED) &lt; #{refreshVersion}
        </if>
        <if test="platformNumIdList!=null and platformNumIdList.size()>0">
            and psm.platform_num_id in
            <foreach collection="platformNumIdList" close=")" open="(" separator="," item="numId">
                #{numId}
            </foreach>
        </if>
    </select>


    <select id="getEshopSkuCountByNumId"
            resultType="java.lang.Integer">
        select count(*)
        from pl_eshop_product_sku psm
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId}
        <if test="refreshVersion!=null">
            and CAST(psm.refresh_version AS UNSIGNED) &lt; #{refreshVersion}
        </if>
        <if test="platformNumIdList!=null and platformNumIdList.size()>0">
            and psm.platform_num_id in
            <foreach collection="platformNumIdList" close=")" open="(" separator="," item="numId">
                #{numId}
            </foreach>
        </if>
    </select>

    <select id="queryEshopProductRelationsOpenXcode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductRelationEntity">

        select pm.id,
               pm.default_sku_id,
               pm.profile_id,
               pm.eshop_id,
               0                       as ptype_id,
               pm.category_id,
               ''                      as user_code,
               ''                      as ptypename,
               ''                      as unitname,
               ''                      as xcode,
               ''                      as localPicUrl,
               pm.platform_pic_url,
               ''                      as moditytime,
               pm.platform_xcode       as platxcode,
               ''                      as standard,
               ''                      as pid,
               pm.platform_num_id      as relationid,
               ''                      as properties,
               ''                      as operation,
               pm.platform_stock_state as stock_state
        from pl_eshop_product pm
        where pm.profile_id = #{profileid}
          and pm.eshop_id = #{otypeId}

    </select>

    <select id="queryEshopSkuAttrRelation"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuAttrRelation">
        select *
        from pl_eshop_product_attr_relation
        where profile_id = #{profileId}
        and eshop_id = #{eshopId}
        <if test="propGroupCount != 0">
            and prop_group_count &gt; 0
        </if>
        <if test="propGroupCount == 0">
            and prop_group_count = 0
        </if>
        <if test="LocalPropList !=null and LocalPropList.size>0">
            and platform_prop in
            <foreach collection="LocalPropList" item="prop" separator="," index="i" close=")" open="(">
                #{prop}
            </foreach>
        </if>

    </select>

    <delete id="deleteEshopAttrRelation">
        delete
        from pl_eshop_product_attr_relation
        where profile_id = #{profileId}
          and id = #{id}
    </delete>
    <select id="querySingleEshopSkuAttrRelation"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuAttrRelation">
        select *
        from pl_eshop_product_attr_relation
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and platform_prop = #{LocalProp}
    </select>

    <select id="getEshopSkuAttrSeniorRelation"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuAttrRelation">
        select *
        from pl_eshop_product_attr_relation
        where profile_id = #{profileId}
        and eshop_id = #{eshopId}
        and prop_group_count &gt;1
        <if test="null != localProp">
            and platform_prop like CONCAT('%',#{localProp},'%')
        </if>
        order by row_index asc
    </select>
    <select id="queryEshopProductRelationsManual"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductRelationEntity">
        select
        pm.id,pm.profile_id,pm.eshop_id,pm.category_id,pm.platform_pic_url,pm.default_sku_id,
        pm.platform_num_id,pm.platform_alias,pm.has_properties,
        pm.platform_fullname as platname,'' as platformskuid,pm.platform_xcode,pm.platform_xcode as platxcode,'' as
        standard,
        '' as pid,true as isptype,pm.platform_num_id as relationid,'' as properties,
        pm.platform_stock_state as stockState,'' as cost_price,true as mainProduct
        from pl_eshop_product pm
        left join pl_eshop_product_class epc on epc.profile_id= pm.profile_id and epc.eshop_id=pm.eshop_id and
        epc.platform_class_id=pm.category_id
        where pm.profile_id=#{profileId} and pm.eshop_id=#{otypeId} and pm.platform_num_id in (
        SELECT * FROM (
        <if test="noConditionQuery == true">
            SELECT DISTINCT psm.platform_num_id FROM pl_eshop_product_sku psm
            where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId} and psm.storage_type=0
        </if>
        <if test="noConditionQuery == false">
            <if test="queryMarkCodeList!=null and queryMarkCodeList.size>0">
                (
                    <include refid="ptypeRelationPageQuery_ByMappingValue_selectClause"/>
                    left join pl_eshop_product_mark pmk on pmk.profile_id=psm.profile_id and
                    pmk.platform_num_id=psm.platform_num_id
                    and pmk.eshop_id = psm.eshop_id
                    <include refid="ptypeRelationPageQuery_ByMappingValue_whereClause"/>
                    and pmk.mark_code in
                    <foreach collection="queryMarkCodeList" item="mark" index="i" separator="," open="(" close=")">
                        #{mark}
                    </foreach>
                )
                union
                (
                    <include refid="ptypeRelationPageQuery_ByXcode_selectClause"/>
                    left join pl_eshop_product_mark pmk on pmk.profile_id=psm.profile_id and
                    pmk.platform_num_id=psm.platform_num_id
                    and pmk.eshop_id = psm.eshop_id

                    <include refid="ptypeRelationPageQuery_ByXcode_whereClause"/>
                    and pmk.mark_code in
                    <foreach collection="queryMarkCodeList" item="mark" index="i" separator="," open="(" close=")">
                        #{mark}
                    </foreach>
                )
            </if>
            <if test="(queryMarkCodeList==null or queryMarkCodeList.size==0) and (noMark==null or noMark==0)">
                (
                <include refid="ptypeRelationPageQuery_ByMappingValue_selectClause"/>
                <include refid="ptypeRelationPageQuery_ByMappingValue_whereClause"/>
                )
                union
                (
                <include refid="ptypeRelationPageQuery_ByXcode_selectClause"/>
                <include refid="ptypeRelationPageQuery_ByXcode_whereClause"/>
                )
            </if>

            <if test="noMark!=null and noMark==1 and needShowMarkCodes!=null and needShowMarkCodes.size()>0">
                <if test="queryMarkCodeList!=null and queryMarkCodeList.size>0">
                    union
                </if>
                (
                    <include refid="ptypeRelationPageQuery_ByMappingValue_selectClause"/>
                    <include refid="ptypeRelationPageQuery_ByMappingValue_whereClause"/>
                    and not exists (
                    select 1 from pl_eshop_product_mark pmk
                    where pmk.profile_id=psm.profile_id and pmk.unique_id=psm.unique_id and pmk.show_type=1
                    and pmk.mark_code in
                    <foreach collection="needShowMarkCodes" item="mark" index="i" separator="," open="(" close=")">
                        #{mark}
                    </foreach>
                    )
                )
                union
                (
                    <include refid="ptypeRelationPageQuery_ByXcode_selectClause"/>
                    <include refid="ptypeRelationPageQuery_ByXcode_whereClause"/>

                    and not exists (
                    select 1 from pl_eshop_product_mark pmk
                    where pmk.profile_id=psm.profile_id and pmk.unique_id=psm.unique_id and pmk.show_type=1
                    and pmk.mark_code in
                    <foreach collection="needShowMarkCodes" item="mark" index="i" separator="," open="(" close=")">
                        #{mark}
                    </foreach>
                    )
                )
            </if>
        </if>
        ) tmp)
        <if test="classId!=null and classId!=''">
            and pm.category_id in
            <foreach collection="classIdsForSelect" open="(" close=")" index="i" separator="," item="ca">
                #{ca}
            </foreach>
        </if>
        <if test="ids!=null and ids.size()>0">
            and pm.id in
            <foreach collection="ids" item="id" separator="," index="i" close=")" open="(">
                #{id}
            </foreach>
        </if>
        <if test="stockState==1">
            and pm.platform_stock_state=1
        </if>
        <if test="stockState==2">
            and pm.platform_stock_state=2
        </if>
        order by pm.platform_fullname
    </select>

    <select id="queryEshopProductmark"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductRelationEntity">
        select
        pm.id,pm.profile_id,pm.eshop_id,pm.category_id,pm.platform_pic_url as
        picUrl,pm.platform_pic_url,pm.default_sku_id,
        pm.platform_num_id,pm.platform_alias,pm.has_properties,
        pm.platform_fullname as platname,'' as platformskuid,pm.platform_xcode,pm.platform_xcode as platxcode,'' as
        standard,
        '' as pid,true as isptype,pm.platform_num_id as relationid,'' as properties,
        pm.platform_stock_state as stockState,'' as cost_price
        from pl_eshop_product pm
        join (
        SELECT DISTINCT psm.platform_num_id FROM pl_eshop_product_sku psm
        left join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=
        expand.profile_id and psm.eshop_id = expand.eshop_id
        left join pl_eshop_product_sku_mapping mapping on psm.unique_id = mapping.unique_id and psm.profile_id=
        mapping.profile_id and psm.eshop_id = mapping.eshop_id
        left join pl_eshop_product_mark m on m.profile_id=psm.profile_id and m.eshop_id=psm.eshop_id and
        m.unique_id=psm.unique_id
        left join base_ptype p on p.id=mapping.ptype_id and p.profile_id=psm.profile_id and p.deleted=0 and p.stoped=0

        <if test="filterString!=null and filterString!=''">
            left join pl_eshop_product pm on pm.profile_id=psm.profile_id and
            pm.platform_num_id=psm.platform_num_id
            and pm.eshop_id = psm.eshop_id
        </if>
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId} and expand.mapping_type=0 and psm.storage_type=0
        <if test="platformNumIds!=null">
            and psm.platform_num_id in
            <foreach collection="platformNumIds" item="item" index="i" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="markList != null and markList.size()>0 and noMark==0">
            and m.mark_code in
            <foreach collection="markList" item="mark" index="i" separator="," open="(" close=")">
                #{mark}
            </foreach>
        </if>
        <if test="markList != null and markList.size()>0 and noMark==1">
            and not exists(select alias.unique_id from pl_eshop_product_mark alias where alias.profile_id=#{profileId}
            and alias.show_type=1 and alias.eshop_id=#{otypeId} and psm.unique_id = alias.unique_id)
        </if>
        <if test="relationState==1">
            and ((case when (ifnull(p.id,0)=0 or p.deleted =1 or p.stoped =1) then false else true end)=false )
        </if>
        <if test="relationState==2">
            and ((case when (ifnull(p.id,0)=0 or p.deleted =1 or p.stoped =1) then false else true end)=true )
        </if>
        <if test="filterString!=null and filterString!=''">
            and (pm.platform_fullname like CONCAT('%',#{filterString},'%') or pm.platform_xcode like
            CONCAT('%',#{filterString},'%') or
            psm.platform_properties_name like CONCAT('%',#{filterString},'%') or psm.platform_xcode like
            CONCAT('%',#{filterString},'%'))
        </if>
        <if test="ptypeFilter!=null and ptypeFilter!=''">
            and (p.fullname like CONCAT('%',#{ptypeFilter},'%') or p.usercode like CONCAT('%',#{ptypeFilter},'%'))
        </if>
        union
        SELECT DISTINCT psm.platform_num_id FROM pl_eshop_product_sku psm
        left join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=
        expand.profile_id
        <if test="markList != null and markList.size()>0">
            left join pl_eshop_product_mark m on m.profile_id=psm.profile_id and m.eshop_id=psm.eshop_id and
            m.unique_id=psm.unique_id
        </if>
        left join base_ptype_xcode px on px.xcode=psm.platform_xcode and px.profile_id=psm.profile_id and psm.platform_xcode!=''
        LEFT JOIN base_ptype p3 ON p3.id = px.ptype_id AND p3.profile_id = psm.profile_id and p3.deleted =0 and
        p3.stoped =0
        LEFT JOIN base_ptype p2 ON p2.profile_id = psm.profile_id and p2.usercode = psm.platform_xcode and
        p2.pcategory=2 and p2.deleted =0 and p2.stoped =0 and psm.platform_xcode!=''
        <if test="filterString!=null and filterString!=''">
            left join pl_eshop_product pm on pm.profile_id=psm.profile_id and
            pm.platform_num_id=psm.platform_num_id
            and pm.eshop_id = psm.eshop_id
        </if>
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId} and expand.mapping_type=1 and psm.storage_type=0
        <if test="platformNumIds!=null">
            and psm.platform_num_id in
            <foreach collection="platformNumIds" item="item" index="i" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="markList != null and markList.size()>0 and noMark==0">
            and m.mark_code in
            <foreach collection="markList" item="mark" index="i" separator="," open="(" close=")">
                #{mark}
            </foreach>
        </if>
        <if test="markList != null and markList.size()>0 and noMark==1">
            and not exists(select alias.unique_id from pl_eshop_product_mark alias where alias.profile_id=#{profileId}
            and alias.show_type=1 and alias.eshop_id=#{otypeId} and psm.unique_id = alias.unique_id)
        </if>
        <if test="relationState==1">
            and ifnull(p2.id,ifnull(p3.id,0))=0
        </if>
        <if test="relationState==2">
            and ((case when (ifnull(px.id,0)=0) then false else true end)=true or (case when (ifnull(p2.id,0)=0) then
            false else true end)=true)
        </if>
        <if test="filterString!=null and filterString!=''">
            and (pm.platform_fullname like CONCAT('%',#{filterString},'%') or pm.platform_xcode like
            CONCAT('%',#{filterString},'%') or
            psm.platform_properties_name like CONCAT('%',#{filterString},'%') or psm.platform_xcode like
            CONCAT('%',#{filterString},'%'))
        </if>
        <if test="ptypeFilter!=null and ptypeFilter!=''">
            and (p.fullname like CONCAT('%',#{ptypeFilter},'%') or p.usercode like CONCAT('%',#{ptypeFilter},'%'))
        </if>
        )tmp on tmp.platform_num_id=pm.platform_num_id
        where pm.profile_id=#{profileId} and pm.eshop_id=#{otypeId}
        <if test="classId!=null and classId!=''">
            and pm.category_id in
            <foreach collection="classIdsForSelect" open="(" close=")" index="i" separator="," item="ca">
                #{ca}
            </foreach>
        </if>
        <if test="stockState==1">
            and pm.platform_stock_state=1
        </if>
        <if test="stockState==2">
            and pm.platform_stock_state=2
        </if>
        order by pm.platform_fullname
    </select>
    <select id="querySkuRelations"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductRelationEntity">
        SELECT * FROM
        (SELECT distinct psm.id,(case when (ifnull(ps.id,0)=0 or ifnull(pu.id,0)=0 or p.deleted =1 or p.stoped =1) then
        false else true
        end)as isbind,
        expand.ready_pfullname as readyPfullname,expand.ready_sku_name as readySkuName,mapping.ptype_id as
        ptypeId,mapping.unit_id,mapping.sku_id,
        expand.ready_pusercode,expand.ready_sku_xcode,p.barcode,pm.platform_price as
        price,pm.profile_id,pm.eshop_id,pm.platform_xcode as xcode,pm.category_id,
        pm.platform_pic_url as pic_url,psm.platform_pic_url,pm.platform_num_id,pm.has_properties,
        pm.platform_fullname AS platName,psm.platform_xcode AS
        platxcode,p.standard,psm.qty,expand.download_enable,psm.platform_sku_id,'' AS pid,TRUE AS
        isptype,pm.platform_num_id AS
        relationid,'' AS properties,pm.platform_stock_state as stock_state,psm.platform_properties_name
        FROM pl_eshop_product_sku psm
        left join pl_eshop_product_sku_mapping mapping on mapping.profile_id= psm.profile_id and mapping.unique_id =
        psm.unique_id
        left join pl_eshop_product_sku_expand expand on expand.profile_id= psm.profile_id and expand.unique_id =
        psm.unique_id
        LEFT JOIN pl_eshop_product pm ON pm.platform_num_id=psm.platform_num_id AND pm.profile_id=psm.profile_id
        AND pm.eshop_id=psm.eshop_id
        LEFT JOIN base_ptype_xcode px ON px.profile_id=psm.profile_id AND px.xcode=psm.platform_xcode AND px.defaulted=1
        LEFT JOIN base_ptype p ON p.id=mapping.ptype_id AND p.profile_id=pm.profile_id
        left join base_ptype_sku ps on ps.id=mapping.sku_id and ps.profile_id=psm.profile_id
        left join base_ptype_unit pu on pu.profile_id=psm.profile_id and pu.id =mapping.unit_id and
        pu.ptype_id=mapping.ptype_id
        WHERE pm.profile_id=#{profileId} and pm.eshop_id=#{otypeId}
        <if test="classId!=null and classId!=''">
            and pm.category_id in
            <foreach collection="classIdsForSelect" open="(" close=")" index="i" separator="," item="ca">
                #{ca}
            </foreach>
        </if>
        )r
        <where>
            <if test="filterString!=null and filterString!=''">
                and (r.platName like "%"#{filterString}"%" or r.platform_properties_name like "%"#{filterString}"%" or
                r.platxcode like "%"#{filterString}"%" or r.xcode like "%"#{filterString}"%")
            </if>
            <if test="qtyState==1">
                and r.qty>0
            </if>
            <if test="qtyState==2">
                and r.qty &lt; 1
            </if>
            <if test="stockState==1">
                and r.stock_state=1
            </if>
            <if test="stockState==2">
                and r.stock_state=2
            </if>
            <if test="relationState==1">
                and r.isbind =false
            </if>
            <if test="relationState==2">
                and r.isbind =true
            </if>
        </where>
        group by r.id
        ORDER BY r.platName,r.platform_properties_name
    </select>
    <select id="querySkuRelationsByXcode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductRelationEntity">
        SELECT * FROM
        (SELECT(case when (ifnull(px.id,0)=0 or p.deleted =1 or p.stoped =1) then false else true end) as isbind,
        expand.ready_pfullname as readyPfullname,expand.ready_sku_name as readySkuName,mapping.ptype_id as
        ptypeId,mapping.unit_id,mapping.sku_id,
        expand.ready_pusercode,expand.ready_sku_xcode,psm.id,p.barcode,pm.price,pm.profile_id,pm.eshop_id,pm.platform_xcode
        as xcode,mapping.ptype_id,pm.category_id,pm.platform_pic_url as
        picUrl,psm.platform_pic_url,pm.platform_num_id,pm.prop_alias,pm.has_properties,
        pm.platform_fullname AS platName,psm.platform_xcode AS platxcode,'' AS
        p.standard,psm.qty,expand.download_enable,psm.platform_sku_id,'' AS pid,TRUE AS isptype,pm.platform_num_id AS
        relationid,'' AS properties,pm.platform_stock_state as stock_state,psm.platform_properties_name
        FROM pl_eshop_product_sku psm
        left join pl_eshop_product_sku_mapping mapping on mapping.profile_id= psm.profile_id and mapping.unique_id =
        psm.unique_id
        left join pl_eshop_product_sku_expand expand on expand.profile_id= psm.profile_id and expand.unique_id =
        psm.unique_id
        LEFT JOIN pl_eshop_product pm ON pm.platform_num_id=psm.platform_num_id AND pm.profile_id=psm.profile_id
        AND pm.eshop_id=psm.eshop_id
        left join pl_eshop_product_sku_expand expand on expand.profile_id= psm.profile_id and expand.unique_id =
        psm.unique_id
        LEFT JOIN base_ptype_xcode px ON px.profile_id=psm.profile_id AND px.xcode=psm.platform_xcode AND px.defaulted=1
        LEFT JOIN base_ptype p ON p.id=mapping.ptype_id AND p.profile_id=pm.profile_id
        left join base_ptype_sku ps on ps.id=mapping.sku_id and ps.profile_id=psm.profile_id
        left join base_ptype_unit pu on pu.profile_id=psm.profile_id and pu.id =mapping.unit_id and
        pu.ptype_id=mapping.ptype_id
        WHERE pm.profile_id=#{profileId} and pm.eshop_id=#{otypeId}
        <if test="classId!=null and classId!=''">
            and pm.category_id in
            <foreach collection="classIdsForSelect" open="(" close=")" index="i" separator="," item="ca">
                #{ca}
            </foreach>
        </if>
        ORDER BY platName ,platform_properties_name
        )r
        <where>
            <if test="filterString!=null and filterString!=''">
                and (r.platName like "%"#{filterString}"%" or r.platform_properties_name like "%"#{filterString}"%" or
                r.platxcode like "%"#{filterString}"%" or r.xcode like "%"#{filterString}"%")
            </if>
            <if test="qtyState==1">
                and r.qty>0
            </if>
            <if test="qtyState==2">
                and r.qty &lt; 1
            </if>
            <if test="stockState==1">
                and r.stock_state=1
            </if>
            <if test="stockState==2">
                and r.stock_state=2
            </if>
            <if test="relationState==1">
                and r.isbind =false
            </if>
            <if test="relationState==2">
                and r.isbind =true
            </if>
        </where>
    </select>


    <select id="queryPtypeLoadingAndUnloading"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMapping">
        select * from (SELECT DISTINCT pm.eshop_id,platform_pic_url as
        pic_url,category_id,pm.platform_num_id,platform_fullname as fullName,platform_xcode
        as xCode,has_properties,pm.default_sku_id,platform_stock_state as stock_state,
        platform_price as price,platform_alias as prop_alias,platform_modify_time as
        online_modify_time,auto_shelf_on,on_count,off_count
        FROM pl_eshop_product pm
        left join pl_eshop_product_class epc on epc.profile_id= pm.profile_id and epc.eshop_id=pm.eshop_id and
        epc.platform_class_id=pm.category_id
        where pm.profile_id=#{profileId} and pm.eshop_id=#{otypeId})r
        <where>
            <if test="filterString!=null and filterString!=''">
                (r.fullName like "%"#{filterString}"%" or
                r.xcode like "%"#{filterString}"%")
            </if>

            <if test="stockState==1">
                and r.stock_state=1
            </if>
            <if test="stockState==2">
                and r.stock_state=2
            </if>
            <if test="classId!=null and classId!=''">
                and r.category_id in
                <foreach collection="classIdsForSelect" open="(" close=")" index="i" separator="," item="ca">
                    #{ca}
                </foreach>
            </if>
            <if test="platformNumIds!=null and platformNumIds.size()>0">
                and r.platform_num_id in
                <foreach collection="platformNumIds" open="(" close=")" index="i" separator="," item="numid">
                    #{numid}
                </foreach>
            </if>
        </where>
        order by r.fullName
    </select>


    <select id="querySkuRelationsManualPtypeCount"
            resultType="java.lang.Integer">
        select count(*) from(
        <if test="queryMarkCodeList!=null and queryMarkCodeList.size>0">
            (
            <include refid="querySkuRelationsManualPtypeCountSql"/>
            and exists (
            select 1 from pl_eshop_product_mark mk
            where mk.profile_id=psm.profile_id
            and mk .eshop_id=psm.eshop_id and mk.unique_id=psm.unique_id
            and mk.mark_code in
            <foreach collection="queryMarkCodeList" item="item" index="i" separator="," open=" (" close=")">
                #{item}
            </foreach>
            )
            )
        </if>
        <if test="(queryMarkCodeList==null or queryMarkCodeList.size==0) and (noMark==null or noMark==0)">
            <include refid="querySkuRelationsManualPtypeCountSql"/>
        </if>
        <if test="noMark!=null and noMark==1 and needShowMarkCodes!=null and needShowMarkCodes.size()>0">
            <if test="queryMarkCodeList!=null and queryMarkCodeList.size>0">
                union
            </if>
            (
            <include refid="querySkuRelationsManualPtypeCountSql"/>
            and not exists (
            select 1 from pl_eshop_product_mark mk
            where mk.profile_id=psm.profile_id
            and mk .eshop_id=psm.eshop_id and mk.unique_id=psm.unique_id
            and mk.mark_code in
            <foreach collection="needShowMarkCodes" item="item" index="i" separator="," open=" (" close=")">
                #{item}
            </foreach>
            )
            )
        </if>
        ) r
        <where>
            <if test="mark>0">
                and r.mark=#{mark}
            </if>
            <if test="filterString!=''  and queryType==1">
                and (r.platName like "%"#{filterString}"%" or r.pmXcode like "%"#{filterString}"%" or
                r.platform_properties_name like "%"#{filterString}"%" or r.platxcode like "%"#{filterString}"%")
            </if>
            <if test="filterString!='' and queryType==2">
                and (r.ptypeName like "%"#{filterString}"%" or r.xcode like "%"#{filterString}"%" or r.usercode like
                "%"#{filterString}"%")
            </if>
            <if test="filterString !='' and queryType!=null and queryType==3">
                and r.platName like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==4">
                and r.platform_properties_name like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==5">
                and r.pmXcode like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==6">
                and r.platxcode like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==7">
                and r.ptypeName like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==8">
                and r.usercode like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==9">
                and r.xcode like CONCAT('%',#{filterString},'%')
            </if>
            <if test="stockState>0">
                and r.stock_state=#{stockState}
            </if>
            <if test="maxPrice != null">
                and r.price &lt;= #{maxPrice}
            </if>
            <if test="minPrice != null">
                and r.price &gt;= #{minPrice}
            </if>
            <if test="platformNumId != null and platformNumId !=''">
                and r.platform_num_id= #{platformNumId}
            </if>
            <if test="platformSkuId != null and platformNumId !=''">
                and r.platform_sku_id= #{platformSkuId}
            </if>
            <if test="qtyState==1">
                and r.qty>0
            </if>
            <if test="qtyState==2">
                and r.qty &lt; 1
            </if>
            <if test="relationState==1">
                and r.isbind=false
            </if>
            <if test="relationState==2">
                and r.isbind=true
            </if>
        </where>
        order by r.platform_properties_name
    </select>
    <select id="querySkuRelationsManualPtype"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductRelationEntity">
        select * from(
        select (case when (ifnull(p.id,0)=0 or p.deleted =1 or p.stoped =1) then false else true
        end)
        as isbind,
        psm.id,psm.platform_unit_name,mapping.ptype_id,pepsrc.rule_id as
        sync_rule_id,pepsrc.warehouse_code,rule.rule_name as
        ruleName,pm.profile_id,pm.eshop_id,pm.category_id,pm.default_sku_id,psm.platform_pic_url as
        picUrl,psm.platform_num_id,pm.platform_alias,psm.unique_id,
        psm.platform_properties_name ,psm.platform_full_properties ,psm.memo_platform_full_properties as
        hasMemoFullPropertiesName,pm.platform_fullname as
        platName,psm.platform_sku_id,psm.platform_xcode as
        platxcode,pm.platform_xcode as pmXcode,concat('',p.fullname) as ptypeName,pf.fullbarcode as
        ptypeBarcode,pf.fullbarcode as skuBarcode,psm.platform_price as price,p.ptype_type,p.ptype_area,b.brand_name,
        '' as pid,false as isptype,px.xcode as xcode,p.usercode as usercode,psm.platform_sku_id as
        relationid,psm.platform_properties,psm.platform_properties
        as properties,psm.platform_xcode,p.standard, ifnull(datediff(now(),psm.update_time),-1) as
        refreshProductIntervalDay, ifnull(datediff(now(),expand.last_new_order_time),-1) as downloadOrderIntervalDay,
        expand.ready_sku_xcode,expand.ready_pfullname,expand.ready_pusercode,expand.update_xcode_status,expand.mapping_type,p.pcategory
        as mappingDetailType,p.pcategory,p.propenabled,if(ps.pic_url='',pic.pic_url,ps.pic_url) as localPicUrl,
        ifnull(p.update_time,now())as modifiedtime,pm.platform_stock_state as stock_state,pu.unit_name,pu.id as
        unitId,ps.id as
        skuid,ps.propvalue_name1,ps.propvalue_name2,ps.propvalue_name3,ps.propvalue_name4,ps.propvalue_name5,
        ps.propvalue_name6,ps.prop_name1,ps.prop_name2,ps.prop_name3,ps.prop_name4,ps.prop_name5,ps.prop_name6,psm.qty,expand.mark,if(p.sku_price=0,p.cost_price,ps.cost_price)AS
        costPrice
        from pl_eshop_product_sku psm
        left join pl_eshop_product_sku_mapping mapping on mapping.profile_id= psm.profile_id and mapping.unique_id =
        psm.unique_id and mapping.eshop_id=psm.eshop_id
        left join pl_eshop_product pm on pm.platform_num_id=psm.platform_num_id and pm.profile_id=psm.profile_id
        and pm.eshop_id =psm.eshop_id
        left join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=
        expand.profile_id and expand.eshop_id=psm.eshop_id
        left join pl_eshop_product_sku_rule_config pepsrc
        on pepsrc.profile_id=psm.profile_id and pepsrc.eshop_id=psm.eshop_id
        and pepsrc.platform_num_id=psm.platform_num_id
        and pepsrc.platform_properties=psm.platform_properties_name and pepsrc.warehouse_code Is NULL
        left join base_ptype_sku ps on ps.id=mapping.sku_id and ps.profile_id=psm.profile_id
        left join base_ptype_fullbarcode pf on pf.profile_id=ps.profile_id and pf.sku_id=mapping.sku_id and pf.unit_id =
        mapping.unit_id and pf.defaulted=1
        left join base_ptype_xcode px on px.profile_id=psm.profile_id and px.unit_id=mapping.unit_id and
        px.sku_id=mapping.sku_id AND px.defaulted=1
        left join base_ptype_unit pu on pu.profile_id=psm.profile_id and pu.id =mapping.unit_id and
        pu.ptype_id=mapping.ptype_id
        left join base_ptype p on p.id=mapping.ptype_id and p.profile_id=psm.profile_id and p.deleted=0 and p.stoped=0
        and p.classed=0
        LEFT JOIN base_brandtype b ON p.profile_id = b.profile_id AND p.brand_id = b.id
        left join base_ptype_pic pic on pic.ptype_id=p.id and pic.profile_id=p.profile_id and pic.rowindex=1
        left join pl_eshop_stock_sync_rule rule on rule.id=pepsrc.rule_id and rule.deleted=0
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId} and (p.pcategory !=2 OR p.pcategory IS NULL) and
        expand.mapping_type=0 and psm.storage_type=0
        <if test="platformNumIds!=null and platformNumIds.size()>0">
            and psm.platform_num_id in
            <foreach collection="platformNumIds" item="item" index="i" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="notInIds!=null">
            and psm.id not in
            <foreach collection="notInIds" item="item2" index="i" separator="," open="(" close=")">
                #{item2}
            </foreach>
        </if>


        <if test="queryMarkCodeList!=null and queryMarkCodeList.size>0">
            and exists (
            select 1 from pl_eshop_product_mark mk
            where mk.profile_id=psm.profile_id
            and mk .eshop_id=psm.eshop_id and mk.unique_id=psm.unique_id
            and mk.mark_code in
            <foreach collection="queryMarkCodeList" item="item" index="i" separator="," open=" (" close=")">
                #{item}
            </foreach>
            )
        </if>
        ) r
        <where>
            <if test="mark>0">
                and r.mark=#{mark}
            </if>
            <if test="filterString!=''  and queryType==1">
                and (r.platName like "%"#{filterString}"%" or r.pmXcode like "%"#{filterString}"%" or
                r.platform_properties_name like "%"#{filterString}"%" or r.platxcode like "%"#{filterString}"%")
            </if>
            <if test="filterString!='' and queryType==2">
                and (r.ptypeName like "%"#{filterString}"%" or r.xcode like "%"#{filterString}"%" or r.usercode like
                "%"#{filterString}"%")
            </if>
            <if test="filterString !='' and queryType!=null and queryType==3">
                and r.platName like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==4">
                and r.platform_properties_name like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==5">
                and r.pmXcode like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==6">
                and r.platxcode like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==7">
                and r.ptypeName like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==8">
                and r.usercode like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==9">
                and r.xcode like CONCAT('%',#{filterString},'%')
            </if>
            <if test="stockState>0">
                and r.stock_state=#{stockState}
            </if>
            <if test="xcodeState!=2">
                and r.update_xcode_status=#{xcodeState}
            </if>
            <if test="platformXcodeIsNull!=0 and platformXcodeIsNull==1">
                and r.platform_xcode=''
            </if>
            <if test="platformXcodeIsNull!=0 and platformXcodeIsNull==2">
                and r.platform_xcode !=''
            </if>
            <if test="platformXcodeIsNull!=0 and platformXcodeIsNull==4">
                and (ifnull(r.xcode,'')!=r.platxcode and r.isbind=true)
            </if>
            <if test="maxPrice != null">
                and r.price &lt;= #{maxPrice}
            </if>
            <if test="minPrice != null">
                and r.price &gt;= #{minPrice}
            </if>
            <if test="platformNumId != null and platformNumId !=''">
                and r.platform_num_id= #{platformNumId}
            </if>
            <if test="platformSkuId != null and platformNumId !=''">
                and r.platform_sku_id= #{platformSkuId}
            </if>
            <if test="qtyState==1">
                and r.qty>0
            </if>
            <if test="qtyState==2">
                and r.qty &lt; 1
            </if>
            <if test="relationState==1">
                and r.isbind=false
            </if>
            <if test="relationState==2">
                and r.isbind=true
            </if>
            <if test="startDownloadOrderIntervalDay != null and endDownloadOrderIntervalDay!=null">
                and r.downloadOrderIntervalDay &gt;= #{startDownloadOrderIntervalDay} and r.downloadOrderIntervalDay
                &lt;= #{endDownloadOrderIntervalDay}
            </if>
            <if test="startRefreshProductIntervalDay != null and endRefreshProductIntervalDay!=null">
                and r.refreshProductIntervalDay &gt;=#{startRefreshProductIntervalDay} and r.refreshProductIntervalDay
                &lt;= #{endRefreshProductIntervalDay}
            </if>
            <if test="downloadOrderIntervalDay != null ">
                and r.downloadOrderIntervalDay = #{downloadOrderIntervalDay}
            </if>
            <if test="refreshProductIntervalDay != null ">
                and r.refreshProductIntervalDay = #{refreshProductIntervalDay}
            </if>
            <if test="platformXcodeIsNull!=0 and platformXcodeIsNull==3">
                and r.platform_xcode in (select platform_xcode from pl_eshop_product_sku where profile_id=#{profileId}
                and eshop_id=#{otypeId} and storage_type=0 and platform_xcode is not null and platform_xcode!=''
                group by platform_xcode having count(1)>1)
            </if>
        </where>
        order by r.platform_properties_name
    </select>
    <select id="querySkuRelationsManualPtypeByLimit"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductRelationEntity">
        select * from(
        <if test="request.queryMarkCodeList!=null and request.queryMarkCodeList.size>0">
            (
                <include refid="querySkuRelationsManualPtypeByLimitSql"/>
                and exists (
                select 1 from pl_eshop_product_mark mk
                where mk.profile_id=psm.profile_id
                and mk .eshop_id=psm.eshop_id and mk.unique_id=psm.unique_id
                and mk.mark_code in
                <foreach collection="request.queryMarkCodeList" item="item" index="i" separator="," open=" (" close=")">
                    #{item}
                </foreach>
                )
            )
        </if>
        <if test="(request.queryMarkCodeList==null or request.queryMarkCodeList.size==0) and (request.noMark==null or request.noMark==0)">
            <include refid="querySkuRelationsManualPtypeByLimitSql"/>
        </if>
        <if test="request.noMark!=null and request.noMark==1 and request.needShowMarkCodes!=null and request.needShowMarkCodes.size()>0">
            <if test="request.queryMarkCodeList!=null and request.queryMarkCodeList.size>0">
                union
            </if>
            (
                <include refid="querySkuRelationsManualPtypeByLimitSql"/>
                and not exists (
                select 1 from pl_eshop_product_mark mk
                where mk.profile_id=psm.profile_id
                and mk .eshop_id=psm.eshop_id and mk.unique_id=psm.unique_id
                and mk.mark_code in
                <foreach collection="request.needShowMarkCodes" item="item" index="i" separator="," open=" (" close=")">
                    #{item}
                </foreach>
                )
            )
        </if>
        ) r
        <where>
            <if test="request.mark>0">
                and r.mark=#{request.mark}
            </if>
            <if test="request.filterString!='' and request.queryType==1">
                and (r.platName like "%"#{request.filterString}"%" or r.pmXcode like "%"#{request.filterString}"%" or
                r.platform_properties_name like "%"#{request.filterString}"%" or r.platxcode like
                "%"#{request.filterString}"%")
            </if>
            <if test="request.filterString!='' and request.queryType==2">
                and (r.ptypeName like "%"#{request.filterString}"%" or r.xcode like "%"#{request.filterString}"%" or
                r.usercode like
                "%"#{request.filterString}"%")
            </if>
            <if test="request.filterString !='' and request.queryType!=null and request.queryType==3">
                and r.platName like CONCAT('%',#{request.filterString},'%')
            </if>
            <if test="request.filterString !='' and request.queryType!=null and request.queryType==4">
                and r.platform_properties_name like CONCAT('%',#{request.filterString},'%')
            </if>
            <if test="request.filterString !='' and request.queryType!=null and request.queryType==5">
                and r.pmXcode like CONCAT('%',#{request.filterString},'%')
            </if>
            <if test="request.filterString !='' and request.queryType!=null and request.queryType==6">
                and r.platxcode like CONCAT('%',#{request.filterString},'%')
            </if>
            <if test="request.filterString !='' and request.queryType!=null and request.queryType==7">
                and r.ptypeName like CONCAT('%',#{request.filterString},'%')
            </if>
            <if test="request.filterString !='' and request.queryType!=null and request.queryType==8">
                and r.usercode like CONCAT('%',#{request.filterString},'%')
            </if>
            <if test="request.filterString !='' and request.queryType!=null and request.queryType==9">
                and r.xcode like CONCAT('%',#{request.filterString},'%')
            </if>

            <if test="request.stockState>0">
                and r.stock_state=#{request.stockState}
            </if>
            <if test="request.xcodeState!=2">
                and r.update_xcode_status=#{request.xcodeState}
            </if>
            <if test="request.platformXcodeIsNull!=0 and request.platformXcodeIsNull==1">
                and r.platform_xcode=''
            </if>
            <if test="request.platformXcodeIsNull!=0 and request.platformXcodeIsNull==2">
                and r.platform_xcode !=''
            </if>
            <if test="request.platformXcodeIsNull!=0 and request.platformXcodeIsNull==4">
                and (ifnull(r.xcode,'')!=r.platxcode and r.isbind=true)
            </if>
            <if test="request.qtyState==1">
                and r.qty>0
            </if>
            <if test="request.qtyState==2">
                and r.qty &lt; 1
            </if>
            <if test="request.maxPrice != null">
                and r.price &lt;= #{request.maxPrice}
            </if>
            <if test="request.minPrice != null">
                and r.price &gt;= #{request.minPrice}
            </if>
            <if test="request.platformNumId != null and request.platformNumId !=''">
                and r.platform_num_id= #{request.platformNumId}
            </if>
            <if test="request.platformSkuId != null and request.platformNumId !=''">
                and r.platform_sku_id= #{request.platformSkuId}
            </if>
            <if test="request.relationState==1">
                and r.isbind=false
            </if>
            <if test="request.relationState==2">
                and r.isbind=true
            </if>
            <if test="request.startDownloadOrderIntervalDay != null and request.endDownloadOrderIntervalDay!=null">
                and r.downloadOrderIntervalDay &gt;= #{startDownloadOrderIntervalDay} and r.downloadOrderIntervalDay
                &lt;= #{endDownloadOrderIntervalDay}
            </if>
            <if test="request.startRefreshProductIntervalDay != null and request.endRefreshProductIntervalDay!=null">
                and r.refreshProductIntervalDay &gt;=#{startRefreshProductIntervalDay} and
                r.refreshProductIntervalDay &lt;= #{endRefreshProductIntervalDay}
            </if>
            <if test="request.downloadOrderIntervalDay != null ">
                and r.downloadOrderIntervalDay = #{request.downloadOrderIntervalDay}
            </if>
            <if test="request.refreshProductIntervalDay != null ">
                and r.refreshProductIntervalDay = #{request.refreshProductIntervalDay}
            </if>
        </where>
        order by r.platform_properties_name desc
        limit #{pageNum}, #{pageSize}
    </select>

    <select id="querySkuRelationsManualCombo"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductRelationEntity">
        select * from(
        <if test="queryMarkCodeList!=null and queryMarkCodeList.size>0">
            (
            <include refid="querySkuRelationsManualComboSql"></include>
                and exists (
                select 1 from pl_eshop_product_mark mk
                where mk.profile_id=psm.profile_id
                and mk .eshop_id=psm.eshop_id and mk.unique_id=psm.unique_id
                and mk.mark_code in
                <foreach collection="queryMarkCodeList" item="item" index="i" separator="," open=" (" close=")">
                    #{item}
                </foreach>
                )
            )
        </if>
        <if test="(queryMarkCodeList==null or queryMarkCodeList.size==0) and (noMark==null or noMark==0)">
            <include refid="querySkuRelationsManualComboSql"></include>
        </if>
        <if test="noMark!=null and noMark==1 and needShowMarkCodes!=null and needShowMarkCodes.size()>0">
            <if test="queryMarkCodeList!=null and queryMarkCodeList.size>0">
                union
            </if>
            (
            <include refid="querySkuRelationsManualComboSql"></include>
                and not exists (
                select 1 from pl_eshop_product_mark mk
                where mk.profile_id=psm.profile_id
                and mk .eshop_id=psm.eshop_id and mk.unique_id=psm.unique_id
                and mk.mark_code in
                <foreach collection="needShowMarkCodes" item="item" index="i" separator="," open=" (" close=")">
                    #{item}
                </foreach>
                )
            )
        </if>
        ) r
        <where>
            <if test="mark>0">
                and r.mark=#{mark}
            </if>
            <if test="filterString!=''  and queryType==1">
                and (r.platName like "%"#{filterString}"%" or r.pmXcode like "%"#{filterString}"%" or
                r.platform_properties_name like "%"#{filterString}"%" or r.platxcode like "%"#{filterString}"%")
            </if>
            <if test="filterString!='' and queryType==2">
                and (r.ptypeName like "%"#{filterString}"%" or r.xcode like "%"#{filterString}"%" or r.usercode like
                "%"#{filterString}"%")
            </if>
            <if test="filterString !='' and queryType!=null and queryType==3">
                and r.platName like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==4">
                and r.platform_properties_name like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==5">
                and r.pmXcode like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==6">
                and r.platxcode like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==7">
                and r.ptypeName like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==8">
                and r.usercode like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==9">
                and r.xcode like CONCAT('%',#{filterString},'%')
            </if>
            <if test="stockState>0">
                and r.stock_state=#{stockState}
            </if>
            <if test="xcodeState!=2">
                and r.update_xcode_status=#{xcodeState}
            </if>
            <if test="platformXcodeIsNull!=0 and platformXcodeIsNull==1">
                and r.platform_xcode=''
            </if>
            <if test="platformXcodeIsNull!=0 and platformXcodeIsNull==2">
                and r.platform_xcode !=''
            </if>
            <if test="platformXcodeIsNull!=0 and platformXcodeIsNull==4">
                and (ifnull(r.xcode,'')!=r.platxcode and r.isbind=true)
            </if>
            <if test="maxPrice != null">
                and r.price &lt;= #{maxPrice}
            </if>
            <if test="minPrice != null">
                and r.price &gt;= #{minPrice}
            </if>
            <if test="platformNumId != null and platformNumId !=''">
                and r.platform_num_id= #{platformNumId}
            </if>
            <if test="platformSkuId != null and platformNumId !=''">
                and r.platform_sku_id= #{platformSkuId}
            </if>
            <if test="qtyState==1">
                and r.qty>0
            </if>
            <if test="qtyState==2">
                and r.qty &lt; 1
            </if>
            <if test="relationState==1">
                and r.isbind=false
            </if>
            <if test="relationState==2">
                and r.isbind=true
            </if>
            <if test="startDownloadOrderIntervalDay != null and endDownloadOrderIntervalDay!=null">
                and r.downloadOrderIntervalDay &gt;= #{startDownloadOrderIntervalDay} and r.downloadOrderIntervalDay
                &lt;= #{endDownloadOrderIntervalDay}
            </if>
            <if test="startRefreshProductIntervalDay != null and endRefreshProductIntervalDay!=null">
                and r.refreshProductIntervalDay &gt;=#{startRefreshProductIntervalDay} and r.refreshProductIntervalDay
                &lt;= #{endRefreshProductIntervalDay}
            </if>
            <if test="downloadOrderIntervalDay != null ">
                and r.downloadOrderIntervalDay = #{downloadOrderIntervalDay}
            </if>
            <if test="refreshProductIntervalDay != null ">
                and r.refreshProductIntervalDay = #{refreshProductIntervalDay}
            </if>
            <if test="platformXcodeIsNull!=0 and platformXcodeIsNull==3">
                and r.platform_xcode in (select platform_xcode from pl_eshop_product_sku where profile_id=#{profileId}
                and eshop_id=#{otypeId} and storage_type=0 and platform_xcode is not null and platform_xcode!=''
                group by platform_xcode having count(1)>1)
            </if>
        </where>
        order by r.platform_properties_name
    </select>
    <select id="getEshopProductMapping"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMapping">
        select id,
               profile_id,
               eshop_id,
               platform_pic_url     as picUrl,
               category_id          as categoryId,
               platform_num_id,
               platform_fullname    as fullName,
               platform_xcode       as xCode,
               has_properties,
               default_sku_id,
               platform_stock_state as stockState,
               platform_price,
               platform_alias,
               platform_modify_time as onlineModifyTime,
               create_time,
               update_time,
               warehouse_code,
               platform_brand
        from pl_eshop_product
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and platform_num_id = #{platformNumId}
    </select>
    <select id="getEshopProductSkuMappingByProduct"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select psm.platform_num_id,
        psm.profile_id,
        psm.platform_sku_id,
        psm.platform_properties_name,
        psm.platform_xcode,
        psm.platform_properties,
        psm.platform_pic_url,
        psm.platform_full_properties,
        psm.platform_json,
        psm.platform_modified_time,
        expand.mark,
        expand.mapping_type,
        psm.qty,
        psm.update_time,
        psm.create_time,
        psm.unique_id,
        psm.eshop_id,
        psm.memo_platform_full_properties as hasMemoFullPropertiesName,
        mapping.ptype_id,
        mapping.xcode,
        mapping.sku_id,
        mapping.unit_id,
        ptype.pcategory,
        psm.id
        from pl_eshop_product_sku psm
        left join pl_eshop_product_sku_mapping mapping on mapping.profile_id= psm.profile_id and mapping.unique_id =
        psm.unique_id
        left join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=
        expand.profile_id
        left join base_ptype_sku sku on psm.profile_id = sku.profile_id and mapping.sku_id = sku.id
        left join base_ptype_unit pu on pu.profile_id = psm.profile_id and pu.id = mapping.unit_id
        left join base_ptype_xcode px
        on psm.profile_id = px.profile_id and px.unit_id = mapping.unit_id and mapping.sku_id = px.sku_id AND
        px.defaulted = 1
        LEFT JOIN base_ptype ptype on ptype.profile_id = mapping.profile_id and ptype.id = mapping.ptype_id
        where psm.profile_id = #{profileId} and psm.storage_type=0
        and psm.eshop_id = #{eshopId}
        and psm.platform_num_id = #{platformNumId}
        <if test="platformPropertiesName != null">
            and psm.platform_properties_name = #{platformPropertiesName}
        </if>
    </select>

    <select id="getEshopProductSkuMappingByProperties"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select psm.platform_num_id,
        psm.profile_id,
        psm.platform_sku_id,
        psm.platform_properties_name,
        psm.platform_xcode,
        psm.platform_properties,
        psm.platform_pic_url,
        psm.platform_full_properties,
        psm.memo_platform_full_properties as hasMemoFullPropertiesName,
        psm.platform_json,
        psm.platform_modified_time,
        expand.mark,
        expand.mapping_type,
        psm.qty,
        psm.update_time,
        psm.create_time,
        psm.unique_id,
        psm.eshop_id,
        sku.ptype_id,
        px.xcode,
        mapping.sku_id,
        mapping.unit_id
        from pl_eshop_product_sku psm
        left join pl_eshop_product_sku_mapping mapping on mapping.profile_id= psm.profile_id and mapping.unique_id =
        psm.unique_id
        left join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=
        expand.profile_id
        left join base_ptype_sku sku on psm.profile_id = sku.profile_id and mapping.sku_id = sku.id
        left join base_ptype_unit pu on pu.profile_id = psm.profile_id and pu.id = mapping.unit_id
        left join base_ptype_xcode px
        on psm.profile_id = px.profile_id and px.unit_id = mapping.unit_id and mapping.sku_id = px.sku_id AND
        px.defaulted = 1
        where psm.profile_id = #{profileId}
        and psm.eshop_id = #{eshopId} and psm.storage_type=0
        and psm.platform_num_id = #{platformNumId}
        <if test="platformPropertiesNames != null and platformPropertiesNames.size()>0">
            and psm.platform_properties_name in
            <foreach collection="platformPropertiesNames" item="platformPropertiesName" index="i" separator="," open="("
                     close=")">
                #{platformPropertiesName}
            </foreach>
        </if>
    </select>
    <select id="getProductSkuMappingPtypeId" resultType="java.math.BigInteger">
        select px.ptype_id
        from pl_eshop_product_sku psm
                 left join pl_eshop_product_sku_mapping mapping
                           on mapping.profile_id = psm.profile_id and mapping.unique_id = psm.unique_id
                 join base_ptype_xcode px
                      on px.profile_id = psm.profile_id and mapping.sku_id = px.sku_id and
                         mapping.unit_id = px.unit_id AND
                         px.defaulted = 1
        where psm.platform_num_id = #{platformNumId}
          and psm.profile_Id = #{profileId}
          and psm.eshop_Id = #{eshopId}
    </select>
    <select id="querySkuRelationsOpenXcodeCombo"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductRelationEntity">
        select * from (
        <if test="queryMarkCodeList!=null and queryMarkCodeList.size>0">
            <include refid="ptypeRelationSkuQuerySql_byXcodeCombo"/>
            and exists (
            select 1 from pl_eshop_product_mark mk
            where mk.profile_id=psm.profile_id
            and mk .eshop_id=psm.eshop_id and mk.unique_id=psm.unique_id
            and mk.mark_code in
            <foreach collection="queryMarkCodeList" item="item" index="i" separator="," open=" (" close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="(queryMarkCodeList==null or queryMarkCodeList.size==0) and (noMark==null or noMark==0)">
            <include refid="ptypeRelationSkuQuerySql_byXcodeCombo"/>
        </if>
        <if test="noMark!=null and noMark==1 and needShowMarkCodes!=null and needShowMarkCodes.size()>0">
            <if test="queryMarkCodeList!=null and queryMarkCodeList.size>0">
             union
            </if>
            <include refid="ptypeRelationSkuQuerySql_byXcodeCombo"/>
            and not exists (
            select 1 from pl_eshop_product_mark mk
            where mk.profile_id=psm.profile_id
            and mk .eshop_id=psm.eshop_id and mk.unique_id=psm.unique_id
            and mk.mark_code in
            <foreach collection="needShowMarkCodes" item="item" index="i" separator="," open=" (" close=")">
                #{item}
            </foreach>
            )
        </if>
        )r
        <include refid="ptypeRelationSkuQuerySql_byXcodeCombo_Where"/>
        order by r.platform_properties_name
    </select>

    <select id="querySkuRelationsOpenXcodePtypeByLimit"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductRelationEntity">
        select * from (
        <if test="request.queryMarkCodeList!=null and request.queryMarkCodeList.size>0">
            <include refid="ptypeRelationSkuQuerySql_byXcode_select"/>
            and exists (
            select 1 from pl_eshop_product_mark mk
            where mk.profile_id=psm.profile_id
            and mk .eshop_id=psm.eshop_id and mk.unique_id=psm.unique_id
            and mk.mark_code in
            <foreach collection="request.queryMarkCodeList" item="item" index="i" separator="," open=" (" close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="(request.queryMarkCodeList==null or request.queryMarkCodeList.size==0) and (request.noMark==null or request.noMark==0)">
            <include refid="ptypeRelationSkuQuerySql_byXcode_select"/>
        </if>
        <if test="request.noMark!=null and request.noMark==1 and request.needShowMarkCodes!=null and request.needShowMarkCodes.size()>0">
            <if test="request.queryMarkCodeList!=null and request.queryMarkCodeList.size>0">
                union
            </if>
            <include refid="ptypeRelationSkuQuerySql_byXcode_select"/>
            and not exists (
            select 1 from pl_eshop_product_mark mk
            where mk.profile_id=psm.profile_id
            and mk .eshop_id=psm.eshop_id and mk.unique_id=psm.unique_id
            and mk.mark_code in
            <foreach collection="request.needShowMarkCodes" item="item" index="i" separator="," open=" (" close=")">
                #{item}
            </foreach>
            )
        </if>
        )r
        <where>
            <if test="request.mark>0">
                and r.mark=#{mark}
            </if>
            <if test="request.filterString!='' and request.queryType==1">
                and (r.platName like "%"#{request.filterString}"%" or r.pmXcode like "%"#{request.filterString}"%" or
                r.platform_properties_name like "%"#{request.filterString}"%" or r.platxcode like
                "%"#{request.filterString}"%")
            </if>
            <if test="request.filterString!='' and request.queryType==2">
                and (r.ptypeName like "%"#{request.filterString}"%" or r.xcode like "%"#{request.filterString}"%" or
                r.usercode like
                "%"#{request.filterString}"%")
            </if>
            <if test="request.filterString !='' and request.queryType!=null and request.queryType==3">
                and r.platName like CONCAT('%',#{request.filterString},'%')
            </if>
            <if test="request.filterString !='' and request.queryType!=null and request.queryType==4">
                and r.platform_properties_name like CONCAT('%',#{request.filterString},'%')
            </if>
            <if test="request.filterString !='' and request.queryType!=null and request.queryType==5">
                and r.pmXcode like CONCAT('%',#{request.filterString},'%')
            </if>
            <if test="request.filterString !='' and request.queryType!=null and request.queryType==6">
                and r.platxcode like CONCAT('%',#{request.filterString},'%')
            </if>
            <if test="request.filterString !='' and request.queryType!=null and request.queryType==7">
                and r.ptypeName like CONCAT('%',#{request.filterString},'%')
            </if>
            <if test="request.filterString !='' and request.queryType!=null and request.queryType==8">
                and r.usercode like CONCAT('%',#{request.filterString},'%')
            </if>
            <if test="request.filterString !='' and request.queryType!=null and request.queryType==9">
                and r.xcode like CONCAT('%',#{request.filterString},'%')
            </if>
            <if test="request.stockState>0">
                and r.stock_state=#{request.stockState}
            </if>
            <if test="request.relationState==1">
                and r.isbind=false
            </if>
            <if test="request.xcodeState!=2">
                and r.update_xcode_status=#{request.xcodeState}
            </if>
            <if test="request.platformXcodeIsNull!=0 and request.platformXcodeIsNull==1">
                and r.platform_xcode=''
            </if>
            <if test="request.platformXcodeIsNull!=0 and request.platformXcodeIsNull==2">
                and r.platform_xcode !=''
            </if>
            <if test="request.maxPrice != null">
                and r.price &lt;= #{request.maxPrice}
            </if>
            <if test="request.minPrice != null">
                and r.price &gt;= #{request.minPrice}
            </if>
            <if test="request.platformNumId != null and request.platformNumId !=''">
                and r.platform_num_id= #{request.platformNumId}
            </if>
            <if test="request.platformSkuId != null and request.platformNumId !=''">
                and r.platform_sku_id= #{request.platformSkuId}
            </if>
            <if test="request.qtyState==1">
                and r.qty>0
            </if>
            <if test="request.qtyState==2">
                and r.qty &lt; 1
            </if>
            <if test="request.relationState==2">
                and r.isbind=true
            </if>
            <if test="request.relationState==1">
                and r.isbind=false
            </if>
            <if test="request.startDownloadOrderIntervalDay != null and request.endDownloadOrderIntervalDay!=null">
                and r.downloadOrderIntervalDay &gt;= #{request.startDownloadOrderIntervalDay} and
                r.downloadOrderIntervalDay
                &lt;= #{request.endDownloadOrderIntervalDay}
            </if>
            <if test="request.startRefreshProductIntervalDay != null and request.endRefreshProductIntervalDay!=null">
                and r.refreshProductIntervalDay &gt;=#{request.startRefreshProductIntervalDay} and
                r.refreshProductIntervalDay
                &lt;= #{request.endRefreshProductIntervalDay}
            </if>
            <if test="request.downloadOrderIntervalDay != null ">
                and r.downloadOrderIntervalDay = #{request.downloadOrderIntervalDay}
            </if>
            <if test="request.refreshProductIntervalDay != null ">
                and r.refreshProductIntervalDay = #{request.refreshProductIntervalDay}
            </if>
        </where>
        order by r.platform_properties_name desc limit #{pageNum}, #{pageSize}
    </select>

    <select id="querySkuRelationsOpenXcodePtypeCount"
            resultType="java.lang.Integer">
        select count(*) from (
        <if test="queryMarkCodeList!=null and queryMarkCodeList.size>0">
            <include refid="ptypeRelationSkuQuerySql_XcodeComboCount_select"/>
            and exists (
            select 1 from pl_eshop_product_mark mk
            where mk.profile_id=psm.profile_id
            and mk .eshop_id=psm.eshop_id and mk.unique_id=psm.unique_id
            and mk.mark_code in
            <foreach collection="queryMarkCodeList" item="item" index="i" separator="," open=" (" close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="(queryMarkCodeList==null or queryMarkCodeList.size==0) and (noMark==null or noMark==0)">
            <include refid="ptypeRelationSkuQuerySql_XcodeComboCount_select"/>
        </if>
        <if test="noMark!=null and noMark==1 and needShowMarkCodes!=null and needShowMarkCodes.size()>0">
            <if test="queryMarkCodeList!=null and queryMarkCodeList.size>0">
                union
            </if>
            <include refid="ptypeRelationSkuQuerySql_XcodeComboCount_select"/>
            and not exists (
            select 1 from pl_eshop_product_mark mk
            where mk.profile_id=psm.profile_id
            and mk .eshop_id=psm.eshop_id and mk.unique_id=psm.unique_id
            and mk.mark_code in
            <foreach collection="needShowMarkCodes" item="item" index="i" separator="," open=" (" close=")">
                #{item}
            </foreach>
            )
        </if>
        )r
        <include refid="ptypeRelationSkuQuerySql_byXcodeCombo_Where"/>
        order by r.platform_properties_name
    </select>


    <select id="queryEshopProductMarks"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductRelationEntity">
        select * from (
        select (case when if(expand.mapping_type = 0, ifnull(p.id, 0) = 0, ifnull(p2.id,ifnull(p3.id,0)) = 0) then false
        else true end) as isbind,
        psm.id,pm.profile_id,pm.eshop_id,pm.category_id,psm.platform_pic_url as
        picUrl,psm.platform_num_id,
        psm.platform_sku_id as platformSkuId,expand.mark,psm.platform_xcode as
        platxcode,'' as standard,'' as pid,true as isptype,psm.platform_sku_id as relationid,psm.platform_properties,
        expand.ready_sku_xcode,expand.ready_pfullname,psm.qty,psm.platform_properties_name as
        platName,psm.platform_properties_name ,expand.ready_pusercode,
        pm.platform_stock_state as stock_state,'' as unitId,'' as
        skuId
        from pl_eshop_product_sku psm
        left join pl_eshop_product pm on pm.platform_num_id=psm.platform_num_id and pm.profile_id=psm.profile_id
        and pm.eshop_id =psm.eshop_id
        left join pl_eshop_product_sku_expand expand on expand.profile_id= psm.profile_id and expand.eshop_id=
        psm.eshop_id and expand.unique_id = psm.unique_id
        left join pl_eshop_product_sku_mapping mapping on mapping.profile_id= psm.profile_id and mapping.eshop_id=
        psm.eshop_id and mapping.unique_id = psm.unique_id
        left join base_ptype p on p.id=mapping.ptype_id and p.profile_id=psm.profile_id and p.deleted=0 and p.stoped=0
        left join base_ptype_xcode px on px.xcode=psm.platform_xcode and px.profile_id=psm.profile_id
        left join base_ptype p2
        on p2.id = px.ptype_id and p2.profile_id = psm.profile_id and p2.deleted = 0 and p2.stoped = 0
        left join base_ptype p3 on p3.profile_id = psm.profile_id and p3.usercode =psm.platform_xcode
        and p3.deleted = 0 and p3.stoped = 0 and p3.usercode is not null and p3.usercode != ''
        join(SELECT DISTINCT expand.unique_id FROM pl_eshop_product_sku sku
        left join pl_eshop_product_sku_expand expand on expand.profile_id= sku.profile_id and expand.eshop_id=
        sku.eshop_id and expand.unique_id = sku.unique_id
        left join pl_eshop_product_sku_mapping mapping on mapping.profile_id= sku.profile_id and mapping.eshop_id=
        sku.eshop_id and mapping.unique_id = sku.unique_id
        left join base_ptype p on p.id=mapping.ptype_id and p.profile_id=sku.profile_id and p.deleted=0 and p.stoped=0
        left join pl_eshop_product_mark mark on sku.profile_id =mark.profile_id and sku.eshop_id=mark.eshop_id and
        sku.unique_id=mark.unique_id
        where sku.profile_id= #{profileId} and sku.eshop_id=#{otypeId} and sku.storage_type=0
        and expand.mapping_type=0
        <if test="relationState==1">
            and ((case when (ifnull(p.id,0)=0 or p.deleted =1 or p.stoped =1) then false else true end)=false )
        </if>
        <if test="relationState==2">
            and ((case when (ifnull(p.id,0)=0 or p.deleted =1 or p.stoped =1) then false else true end)=true )
        </if>
        <if test="markList != null and markList.size()>0 and noMark==0">
            and mark.mark_code in
            <foreach collection="markList" item="mark" index="i" separator="," open="(" close=")">
                #{mark}
            </foreach>
        </if>
        <if test="markList != null and markList.size()>0 and noMark==1">
            and not exists(select alias.unique_id from pl_eshop_product_mark alias where alias.profile_id=#{profileId}
            and alias.show_type=1 and alias.eshop_id=#{otypeId} and sku.unique_id = alias.unique_id)
        </if>
        union
        SELECT DISTINCT expand.unique_id FROM pl_eshop_product_sku sku
        left join pl_eshop_product_sku_expand expand on expand.profile_id= sku.profile_id and expand.eshop_id=
        sku.eshop_id and expand.unique_id = sku.unique_id
        left join base_ptype_xcode px on px.xcode=sku.platform_xcode and px.profile_id=sku.profile_id and sku.platform_xcode!=''
        LEFT JOIN base_ptype p3 ON p3.id = px.ptype_id AND p3.profile_id = sku.profile_id and p3.deleted =0 and
        p3.stoped =0
        LEFT JOIN base_ptype p2 ON p2.profile_id = sku.profile_id and p2.usercode = sku.platform_xcode and
        p2.pcategory=2 and p2.deleted =0 and p2.stoped =0 and sku.platform_xcode!=''
        left join pl_eshop_product_mark mark on sku.profile_id =mark.profile_id and sku.eshop_id=mark.eshop_id and
        sku.unique_id=mark.unique_id
        where sku.profile_id= #{profileId} and sku.eshop_id=#{otypeId} and sku.storage_type=0
        and expand.mapping_type=1
        <if test="relationState==1">
            and ifnull(p2.id,ifnull(p3.id,0))=0
        </if>
        <if test="relationState==2">
            and ((case when (ifnull(px.id,0)=0) then false else true end)=true or (case when (ifnull(p2.id,0)=0) then
            false else true end)=true)
        </if>
        <if test="markList != null and markList.size()>0 and noMark==0">
            and mark.mark_code in
            <foreach collection="markList" item="mark" index="i" separator="," open="(" close=")">
                #{mark}
            </foreach>
        </if>
        <if test="markList != null and markList.size()>0 and noMark==1">
            and not exists(select alias.unique_id from pl_eshop_product_mark alias where alias.profile_id=#{profileId}
            and alias.show_type=1 and alias.eshop_id=#{otypeId} and sku.unique_id = alias.unique_id)
        </if>
        ) tmp on tmp.unique_id = psm.unique_id
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId} and psm.storage_type=0
        and psm.platform_num_id in
        <foreach collection="platformNumIds" item="item" index="i" separator="," open="(" close=")">
            #{item}
        </foreach>
        <if test="filterString!=null and filterString!=''">
            and (pm.platform_fullname like "%"#{filterString}"%" or psm.platform_properties_name like
            "%"#{filterString}"%" or
            psm.platform_xcode like "%"#{filterString}"%" or pm.platform_xcode like "%"#{filterString}"%")
        </if>) r
        <where>
            <if test="qtyState==1">
                and r.qty>0
            </if>
            <if test="qtyState==2">
                and r.qty &lt; 1
            </if>
        </where>
        GROUP BY r.platform_num_id,r.platName
    </select>
    <select id="isPlatformXcodeExists" resultType="java.lang.Boolean">
        select count(*) from pl_eshop_product_sku psm where psm.platform_xcode=#{platformXcode} and
        psm.eshop_id=#{eshopId} and psm.profile_id=#{profileId}
        and psm.platform_sku_id not in
        <foreach collection="platformSkuIds" open="(" close=")" index="i" separator="," item="skuId">
            #{skuId}
        </foreach>
    </select>
    <select id="loadPtypeSkuRelationMapping"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select psm.*, pm.default_sku_id, pm.platform_fullname as fullname
        from pl_eshop_product_sku psm
                 left join pl_eshop_product_sku_mapping mapping
                           on mapping.profile_id = psm.profile_id and mapping.unique_id = psm.unique_id
                 left join pl_eshop_product pm
                           on pm.profile_id = psm.profile_id and pm.platform_num_id = psm.platform_num_id
                 left join base_ptype_sku sku on sku.profile_id = psm.profile_id and sku.id = mapping.sku_id
        where psm.profile_id = #{profileId}
          and mapping.sku_id = #{skuId}
          and psm.eshop_id = #{otypeId}
    </select>
    <select id="loadEshopSkuMappingItemOpenXcode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select
        <include refid="skuRelationOpenXcode"></include>
        from pl_eshop_product_sku psm
        left join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=
        expand.profile_id
        left join (select px.id,px.ptype_id,px.sku_id,px.unit_id,px.xcode,px.profile_id from base_ptype_xcode px
        left join base_ptype bp on bp.id = px.ptype_id and bp.profile_id = px.profile_id
        where px.profile_id = #{profileId} and bp.deleted = 0 and bp.stoped = 0 and px.unit_id not in(select id from
        base_ptype_unit where profile_id=#{profileId} and unit_type=1)) bpx on bpx.xcode=psm.platform_xcode and
        bpx.profile_id=psm.profile_id
        left join base_ptype p on p.id=bpx.ptype_id and p.profile_id=psm.profile_id
        left join pl_eshop_product pm on pm.profile_id=psm.profile_id and pm.eshop_id=psm.eshop_id and
        pm.platform_num_id=psm.platform_num_id
        left join base_ptype_unit unit on unit.id=bpx.unit_id and unit.profile_id=bpx.profile_id
        left join base_ptype_unit baseunit on baseunit.profile_id=psm.profile_id and bpx.ptype_id=baseunit.ptype_id and
        baseunit.unit_code=1
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId}
        <if test="id!=null">
            and psm.id=#{id}
        </if>
        <if test="xcode!=null">
            and psm.platform_xcode=#{xcode}
        </if>
        <if test="numId!=null and numId!=''">
            and psm.platform_num_id=#{numId}
        </if>
        <if test="platformSkuId!=null and platformSkuId">
            and psm.platform_sku_id=#{platformSkuId}
        </if>
    </select>

    <select id="loadEshopSkuMappingByNumidAndProperties"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select mapping.unit_id,mapping.sku_id,mapping.ptype_id,
        psm.platform_num_id,psm.profile_id,psm.platform_sku_id,psm.platform_properties_name,psm.platform_xcode,
        psm.platform_properties,psm.platform_pic_url,
        psm.platform_json,psm.platform_modified_time,expand.mark,psm.qty,psm.platform_modified_time as
        modifiedTime,psm.update_time,
        psm.create_time,psm.unique_id,psm.eshop_id,
        pm.platform_fullname as platfullname,
        p.pcategory,
        (case when ( p.deleted =1 or p.stoped =1 or IFNULL(p.id,0)=0) then false else true end)
        as isbind,
        p.id as ptype_id,
        1 as unitRate,
        p.usercode as xcode,
        p.fullname as ptypeName,p.fullname,bpf.fullbarcode as barcode,bpu.unit_name,bpx.xcode
        from pl_eshop_product_sku psm
        left join pl_eshop_product_sku_mapping mapping on mapping.profile_id= psm.profile_id and mapping.unique_id =
        psm.unique_id
        left join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=
        expand.profile_id
        left join base_ptype_xcode bpx on psm.profile_id = bpx.profile_id and psm.platform_xcode=bpx.xcode
        left join base_ptype p on p.id=bpx.ptype_id and p.profile_id=psm.profile_id
        left join pl_eshop_product pm on pm.profile_id=psm.profile_id and pm.eshop_id=psm.eshop_id and
        pm.platform_num_id=psm.platform_num_id
        left join base_ptype_unit bpu on bpu.profile_id=psm.profile_id and bpu.ptype_id=mapping.ptype_id and
        bpu.unit_code=1
        left join base_ptype_fullbarcode bpf on bpf.profile_id = psm.profile_id and bpf.ptype_id = mapping.ptype_id and
        bpf.unit_id = mapping.unit_id and bpf.sku_id = mapping.sku_id
        where psm.profile_id=#{profileId} and psm.eshop_id=#{eshopId} and p.stoped=0 and p.deleted=0
        <if test="platformNumId !=null and platformNumId!=''">
            and psm.platform_num_id=#{platformNumId}
        </if>
        <if test="properties != null and properties != ''">
            and psm.platform_properties_name=#{properties}
        </if>
        <if test="mappingType != null ">
            and expand.mapping_type=#{mappingType}
        </if>
        order by psm.platform_num_id desc
    </select>

    <select id="loadEshopSkuMappingListOpenXcode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select
        <include refid="skuRelationOpenXcode"></include>

        from pl_eshop_product_sku psm
        left join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=
        expand.profile_id
        left join pl_eshop_product pm on pm.profile_id=psm.profile_id and pm.eshop_id=psm.eshop_id and
        pm.platform_num_id=psm.platform_num_id
        left join base_ptype_xcode bpx on psm.profile_id = bpx.profile_id and psm.platform_xcode=bpx.xcode
        left join base_ptype p on psm.profile_id = p.profile_id and bpx.ptype_id=p.id and p.classed=0
        left join base_ptype_unit unit on unit.id=bpx.unit_id and unit.profile_id=bpx.profile_id
        left join base_ptype_unit baseunit on baseunit.profile_id=psm.profile_id and p.id=baseunit.ptype_id and
        baseunit.unit_code=1
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId} and bpx.id>0
        <if test="xcodeList!=null and xcodeList.size()>0">
            and psm.platform_xcode in
            <foreach collection="xcodeList" open="(" close=")" index="i" separator="," item="xcode">
                #{xcode}
            </foreach>
        </if>
    </select>
    <select id="loadEshopSkuMappingListOpenXcodeCount"
            resultType="java.lang.Integer">
        select
        count(*)
        from pl_eshop_product_sku psm
        left join pl_eshop_product pm on pm.profile_id=psm.profile_id and pm.eshop_id=psm.eshop_id and
        pm.platform_num_id=psm.platform_num_id
        left join base_ptype_xcode bpx on psm.profile_id = bpx.profile_id and psm.platform_xcode=bpx.xcode
        left join base_ptype p on psm.profile_id = p.profile_id and bpx.ptype_id=p.id
        left join base_ptype_unit unit on unit.id=bpx.unit_id and unit.profile_id=bpx.profile_id
        left join base_ptype_unit baseunit on baseunit.profile_id=psm.profile_id and p.id=baseunit.ptype_id and
        baseunit.unit_code=1
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId}
        <if test="xcodeList!=null and xcodeList.size()>0">
            and psm.platform_xcode in
            <foreach collection="xcodeList" open="(" close=")" index="i" separator="," item="xcode">
                #{xcode}
            </foreach>
        </if>
    </select>

    <select id="loadSkuMappingToComboOpenXcodeCount"
            resultType="java.lang.Integer">
        select
        count(*)
        from pl_eshop_product_sku psm
        left join pl_eshop_product pm on pm.profile_id=psm.profile_id and pm.eshop_id=psm.eshop_id and
        pm.platform_num_id=psm.platform_num_id
        left join base_ptype p on psm.profile_id = p.profile_id and psm.platform_xcode=p.usercode and p.pcategory=2
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId}
        <if test="xcodeList!=null and xcodeList.size()>0">
            and psm.platform_xcode in
            <foreach collection="xcodeList" open="(" close=")" index="i" separator="," item="xcode">
                #{xcode}
            </foreach>
        </if>
    </select>
    <select id="loadSkuMappingToComboOpenXcode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select
        <include refid="comboRelationOpenXcode"></include>
        from pl_eshop_product_sku psm
        left join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=
        expand.profile_id
        left join pl_eshop_product pm on pm.profile_id=psm.profile_id and pm.eshop_id=psm.eshop_id and
        pm.platform_num_id=psm.platform_num_id
        left join base_ptype p on psm.profile_id = p.profile_id and psm.platform_xcode=p.usercode and p.pcategory=2 and
        p.classed=0
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId} and p.usercode is not null and p.usercode !=''
        <if test="xcodeList!=null and xcodeList.size()>0">
            and psm.platform_xcode in
            <foreach collection="xcodeList" open="(" close=")" index="i" separator="," item="xcode">
                #{xcode}
            </foreach>
        </if>
    </select>

    <select id="loadEshopSkuMappingList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select psm.*,mapping.sku_id,mapping.ptype_id,mapping.unit_id,(case when ( p.deleted =1 or p.stoped =1 or
        IFNULL(p.id,0)=0) then false else true end)
        as isbind,pm.platform_fullname,pm.platform_fullname as platFullName,bpx.xcode,p.fullname as ptypeName,unit.id as
        unitId,unit.unit_name,
        ifnull(unit.unit_rate,1) as unitRate,baseunit.id as baseUnitId,p.pcategory
        from pl_eshop_product_sku psm
        left join pl_eshop_product_sku_mapping mapping on mapping.profile_id= psm.profile_id and mapping.unique_id =
        psm.unique_id
        left join pl_eshop_product pm on pm.profile_id=psm.profile_id and pm.eshop_id=psm.eshop_id and
        pm.platform_num_id=psm.platform_num_id
        left join base_ptype_unit unit on unit.id=mapping.unit_id and unit.profile_id=psm.profile_id
        left join base_ptype_unit baseunit on baseunit.profile_id=psm.profile_id and mapping.ptype_id=baseunit.ptype_id
        and
        baseunit.unit_code=1
        left join base_ptype_xcode bpx on psm.profile_id = bpx.profile_id and mapping.unit_id=bpx.unit_id and
        mapping.sku_id=bpx.sku_id and bpx.defaulted=1
        left join base_ptype p on p.profile_id=psm.profile_id and p.id=mapping.ptype_id
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId}
        <if test="skuIds!=null">
            and mapping.sku_id in
            <foreach collection="skuIds" open="(" close=")" index="i" separator="," item="sku">
                #{sku}
            </foreach>
        </if>
        <if test="platformSkuIdList!=null and platformSkuIdList.size()>0">
            and psm.platform_sku_id in
            <foreach collection="platformSkuIdList" open="(" close=")" index="j" separator="," item="platformSkuId">
                #{platformSkuId}
            </foreach>
        </if>
    </select>

    <select id="loadEshopSkuMappingToCombo"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select psm.*,(case when ( p.deleted =1 or p.stoped =1 or IFNULL(p.id,0)=0) then false else true end)
        as isbind,pm.platform_fullname,pm.platform_fullname as platFullname,p.fullname as ptypeName,
        mapping.ptype_id,1 as unitRate, p.usercode as xcode,p.pcategory
        from pl_eshop_product_sku psm
        left join pl_eshop_product_sku_mapping mapping on mapping.profile_id= psm.profile_id and mapping.eshop_id =
        psm.eshop_id and mapping.unique_id = psm.unique_id
        left join pl_eshop_product pm on pm.profile_id=psm.profile_id and pm.eshop_id=psm.eshop_id and
        pm.platform_num_id=psm.platform_num_id
        left join base_ptype p on p.profile_id=psm.profile_id and p.id=mapping.ptype_id and p.pcategory=2
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId}
        <if test="comboIdList!=null and comboIdList.size()>0">
            and mapping.ptype_id in
            <foreach collection="comboIdList" open="(" close=")" index="i" separator="," item="pid">
                #{pid}
            </foreach>
        </if>
        <if test="platformSkuIdList!=null and platformSkuIdList.size()>0">
            and psm.platform_sku_id in
            <foreach collection="platformSkuIdList" open="(" close=")" index="j" separator="," item="platformSkuId">
                #{platformSkuId}
            </foreach>
        </if>
    </select>


    <select id="queryByProfileIdAndeshopIdCount"
            resultType="java.lang.Integer">
        select count(*)
        from pl_eshop_product_sku sku
        join pl_eshop_product m on m.platform_num_id = sku.platform_num_id and m.profile_id = sku.profile_id and
        m.eshop_id = sku.eshop_id
        left join pl_eshop_product_sku_rule_config pepsrc
        on pepsrc.profile_id=sku.profile_id and pepsrc.eshop_id=sku.eshop_id
        and pepsrc.platform_num_id=sku.platform_num_id
        and pepsrc.platform_properties=sku.platform_properties_name
        where sku.profile_id = #{profileId}
        and sku.eshop_id = #{eshopId}
        <if test="classId!=null and classId.size()>0">
            and m.category_id in
            <foreach collection="classId" open="(" close=")" index="i" separator="," item="ca">
                #{ca}
            </foreach>
        </if>
    </select>
    <select id="queryByProfileIdAndeshopId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select m.platform_xcode as pmplatformXcode,m.platform_fullname as platfullname,sku.id,m.platform_price as
        price,m.platform_pic_url as platformPicUrl,sku.profile_Id,
        expand.mark,sku.eshop_id,expand.mapping_type,sku.unique_id,
        expand.download_enable,sku.platform_sku_id, sku.platform_Num_Id, m.default_sku_id,
        sku.platform_properties_Name,sku.platform_properties,0 as isredundant,sku.platform_xcode,sku.platform_xcode as
        platxcode,sku.platform_full_properties,sku.qty,
        sku.platform_modified_time,m.platform_stock_state as
        stock_state,expand.ready_sku_name,expand.ready_sku_xcode,expand.ready_pfullname,
        expand.ready_pusercode,sku.platform_pic_url as picUrl,pepsrc.rule_id as sync_rule_id,m.category_id
        from pl_eshop_product_sku sku
        join pl_eshop_product m on m.platform_num_id = sku.platform_num_id and m.profile_id = sku.profile_id and
        m.eshop_id = sku.eshop_id
        left join pl_eshop_product_sku_expand expand on sku.unique_id = expand.unique_id and sku.profile_id=
        expand.profile_id
        left join pl_eshop_product_sku_rule_config pepsrc
        on pepsrc.profile_id=sku.profile_id and pepsrc.eshop_id=sku.eshop_id
        and pepsrc.platform_num_id=sku.platform_num_id
        and pepsrc.platform_properties=sku.platform_properties_name
        where sku.profile_id = #{profileId}
        and sku.eshop_id = #{eshopId} and sku.storage_type=0
        <if test="classId!=null and classId.size()>0">
            and m.category_id in
            <foreach collection="classId" open="(" close=")" index="i" separator="," item="ca">
                #{ca}
            </foreach>
        </if>
    </select>

    <select id="queryByProfileIdAndeshopIdByLimit"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select m.platform_fullname    as platfullname,
               sku.id,
               m.price,
               m.platform_pic_url     as platformPicUrl,
               sku.profile_Id,
               expand.mark,
               sku.eshop_id,
               expand.download_enable,
               sku.platform_sku_id,
               sku.platform_Num_Id,
               m.default_sku_id,
               sku.platform_properties_Name,
               sku.platform_properties,
               0                      as isredundant,
               sku.platform_xcode,
               sku.platform_xcode     as
                                         platxcode,
               sku.platform_modified_time,
               m.platform_stock_state as stock_state,
               expand.ready_sku_name,
               expand.ready_sku_xcode,
               expand.ready_pfullname,
               expand.ready_pusercode,
               sku.platform_pic_url   as picUrl,
               pepsrc.rule_id         as sync_rule_id
        from pl_eshop_product_sku sku
                 join pl_eshop_product m
                      on m.platform_num_id = sku.platform_num_id and m.profile_id = sku.profile_id and
                         m.eshop_id = sku.eshop_id
                 left join pl_eshop_product_sku_expand expand
                           on psm.unique_id = expand.unique_id and psm.profile_id = expand.profile_id
                 left join pl_eshop_product_sku_rule_config pepsrc
                           on pepsrc.profile_id = sku.profile_id and pepsrc.eshop_id = sku.eshop_id
                               and pepsrc.platform_num_id = sku.platform_num_id
                               and pepsrc.platform_properties = sku.platform_properties_name
        where sku.profile_id = #{profileId}
          and sku.eshop_id = #{eshopId}
        limit #{pageNum} , #{pageSize}
    </select>

    <select id="findProductByModifyTime"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select m.platform_fullname    as platfullname,
               sku.id,
               m.price,
               m.platform_pic_url     as platformPicUrl,
               sku.profile_Id,
               expand.mark,
               sku.eshop_id,
               sku.download_enable,
               sku.platform_sku_id,
               sku.platform_Num_Id,
               m.default_sku_id,
               sku.platform_properties_Name,
               sku.platform_properties,
               0                      as isredundant,
               sku.platform_xcode,
               sku.platform_xcode     as platxcode,
               sku.platform_modified_time,
               m.platform_stock_state as stock_state,
               expand.ready_sku_name,
               expand.ready_sku_xcode,
               expand.ready_pfullname,
               expand.ready_pusercode,
               sku.platform_pic_url   as picUrl,
               pepsrc.rule_id         as sync_rule_id
        from pl_eshop_product_sku sku
                 left join pl_eshop_product_sku_expand expand
                           on psm.unique_id = expand.unique_id and psm.profile_id = expand.profile_id
                 left join pl_eshop_product_sku_rule_config pepsrc
                           on pepsrc.profile_id = sku.profile_id and pepsrc.eshop_id = sku.eshop_id
                               and pepsrc.platform_num_id = sku.platform_num_id
                               and pepsrc.platform_properties = sku.platform_properties_name
                 join pl_eshop_product m
                      on m.platform_num_id = sku.platform_num_id and m.profile_id = sku.profile_id and
                         m.eshop_id = sku.eshop_id
        where sku.profile_id = #{profileId}
          and sku.eshop_id = #{eshopId}
          and modified_time BETWEEN #{productStartTime} and #{productEndTime}
    </select>


    <select id="loadEshopComboSkuMappingItemOpenXcode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select
        <include refid="comboRelationOpenXcode"></include>
        from pl_eshop_product_sku psm
        left join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=
        expand.profile_id
        left join (select bpx.id,bpx.ptype_id,bpx.xcode,bpx.profile_id from base_ptype_xcode bpx
        left join base_ptype bp on bp.id = bpx.ptype_id and bp.profile_id = bpx.profile_id
        where bpx.profile_id = #{profileId} and bpx.info_type=1 and bp.deleted = 0 and bp.stoped = 0 and bpx.unit_id not
        in(select id from base_ptype_unit where profile_id=#{profileId} and unit_type=1)) px on
        px.xcode=psm.platform_xcode and px.profile_id=psm.profile_id
        left join base_ptype p on px.profile_id = p.profile_id and px.ptype_id=p.id
        left join pl_eshop_product pm on pm.profile_id=psm.profile_id and pm.eshop_id=psm.eshop_id and
        pm.platform_num_id=psm.platform_num_id
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId} and p.pcategory=2 and psm.platform_xcode=#{xcode}
        <if test="numId!=null and numId!=''">
            and psm.platform_num_id=#{numId}
        </if>
        limit 1
    </select>

    <select id="loadEshopComboSkuMappingByNumidAndProperties"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select mapping.unit_id,mapping.sku_id,mapping.ptype_id,
        psm.platform_num_id,psm.profile_id,psm.platform_sku_id,psm.platform_properties_name,psm.platform_xcode,
        psm.platform_properties,psm.platform_pic_url,
        psm.platform_json,psm.platform_modified_time,expand.mark,psm.qty,psm.platform_modified_time as
        modifiedTime,psm.update_time,
        psm.create_time,psm.unique_id,psm.eshop_id,
        pm.platform_fullname as platfullname,
        p.pcategory,
        (IF(( p.deleted =1 or p.stoped =1 or IFNULL(p.id,0)=0), false, true))
        as isbind,
        p.id as ptype_id,
        1 as unitRate,
        p.usercode as xcode,
        p.fullname as ptypeName,p.fullname,bpf.fullbarcode as barcode,bpu.unit_name,bpx.xcode
        from pl_eshop_product_sku psm
        left join pl_eshop_product_sku_mapping mapping on mapping.profile_id= psm.profile_id and mapping.unique_id =
        psm.unique_id
        join base_ptype p on psm.profile_id = p.profile_id and psm.platform_xcode=p.usercode
        left join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=
        expand.profile_id
        left join base_ptype_unit bpu on bpu.profile_id = psm.profile_id and bpu.id = mapping.unit_id
        left join base_ptype_xcode bpx on bpx.profile_id = psm.profile_id and bpx.ptype_id = mapping.ptype_id and
        bpx.unit_id = mapping.unit_id and bpx.sku_id = mapping.sku_id
        left join base_ptype_fullbarcode bpf on bpf.profile_id = psm.profile_id and bpf.ptype_id = mapping.ptype_id and
        bpf.unit_id = mapping.unit_id and bpf.sku_id = mapping.sku_id
        left join pl_eshop_product pm on pm.profile_id=psm.profile_id and pm.eshop_id=psm.eshop_id and
        pm.platform_num_id=psm.platform_num_id
        where psm.profile_id=#{profileId} and psm.eshop_id=#{eshopId} and p.pcategory=2 and p.stoped=0 and p.deleted=0
        <if test="platformNumId != null and platformNumId != ''">
            and psm.platform_num_id=#{platformNumId}
        </if>
        <if test="properties != null and properties != ''">
            and psm.platform_properties_name=#{properties}
        </if>
        <if test="mappingType != null">
            and expand.mapping_type=#{mappingType}
        </if>
        order by psm.platform_num_id desc
    </select>
    <select id="loadEshopComboSkuMappingItem"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select
        <include refid="comboRelation"></include>
        from pl_eshop_product_sku psm
        left join pl_eshop_product_sku_mapping mapping on mapping.profile_id= psm.profile_id and mapping.unique_id =
        psm.unique_id and mapping.eshop_id = psm.eshop_id
        left join pl_eshop_product pm on pm.profile_id=psm.profile_id and pm.eshop_id=psm.eshop_id and
        pm.platform_num_id=psm.platform_num_id
        left join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=
        expand.profile_id
        left join pl_eshop_product_sku_rule_config pepsrc on pepsrc.profile_id=psm.profile_id and
        pepsrc.eshop_id=psm.eshop_id and pepsrc.platform_num_id=psm.platform_num_id
        and pepsrc.platform_properties=psm.platform_properties_name and pepsrc.warehouse_code=pm.warehouse_code
        join base_ptype p on p.profile_id=psm.profile_id and p.id=mapping.ptype_id
        left join base_ptype_xcode bpx on bpx.profile_id=psm.profile_id and bpx.ptype_id=mapping.ptype_id and
        bpx.sku_id=mapping.sku_id and bpx.unit_id=mapping.unit_id
        left join base_ptype_combo bpc on bpc.profile_id = psm.profile_id and bpc.combo_id = mapping.ptype_id
        left join pl_eshop_product_mark mark on psm.profile_id= mark.profile_id and psm.eshop_id = mark.eshop_id
        AND psm.platform_num_id=mark.platform_num_id and psm.platform_sku_id = mark.platform_sku_id
        left join (select cdlp.profile_id as profileId,
        cdlp.resource_id as ptypeId,
        clv.labelfield_value as labelfieldValue
        from cf_data_label_ptype cdlp
        left join Cf_Labelfield_Value clv
        on clv.id = cdlp.labelfield_value_id
        and clv.profile_id = cdlp.profile_id
        where clv.labelfield_value = '代销'
        and cdlp.profile_id = #{profileId}) temp on temp.profileId = psm.profile_id and temp.ptypeId = p.id
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId} and p.pcategory=2 and psm.storage_type=0
        <choose>
            <when test="uniqueIdList!=null and uniqueIdList.size()>0">
                and psm.unique_id in
                <foreach collection="uniqueIdList" close=")" open="(" separator="," item="uniqueId">
                    #{uniqueId}
                </foreach>
            </when>
            <otherwise>
                <if test="id!=null">
                    and psm.id=#{id}
                </if>
                <if test="xcode!=null">
                    and psm.platform_xcode=#{xcode}
                </if>
                <if test="numId!=null and numId!=''">
                    and psm.platform_num_id=#{numId}
                </if>
                <if test="properties!=null and properties!=''">
                    and psm.platform_properties_name=#{properties}
                </if>
                <if test="platformSkuId!=null and platformSkuId!=''">
                    and psm.platform_sku_id=#{platformSkuId}
                </if>
            </otherwise>
        </choose>
        order by mapping.ptype_id desc
        limit 1
    </select>

    <select id="loadEshopComboSkuMappingItemList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select
        <include refid="comboRelation"></include>
        from pl_eshop_product_sku psm
        left join pl_eshop_product_sku_mapping mapping on mapping.profile_id= psm.profile_id and mapping.unique_id =
        psm.unique_id and mapping.eshop_id = psm.eshop_id
        left join pl_eshop_product pm on pm.profile_id=psm.profile_id and pm.eshop_id=psm.eshop_id and
        pm.platform_num_id=psm.platform_num_id
        left join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=
        expand.profile_id
        left join pl_eshop_product_sku_rule_config pepsrc on pepsrc.profile_id=psm.profile_id and
        pepsrc.eshop_id=psm.eshop_id and pepsrc.platform_num_id=psm.platform_num_id
        and pepsrc.platform_properties=psm.platform_properties_name and pepsrc.warehouse_code=pm.warehouse_code
        join base_ptype p on p.profile_id=psm.profile_id and p.id=mapping.ptype_id
        left join base_ptype_xcode bpx on bpx.profile_id=psm.profile_id and bpx.ptype_id=mapping.ptype_id and
        bpx.sku_id=mapping.sku_id and bpx.unit_id=mapping.unit_id and bpx.defaulted=1
        left join base_ptype_combo bpc on bpc.profile_id = psm.profile_id and bpc.combo_id = mapping.ptype_id
        left join pl_eshop_product_mark mark on psm.profile_id= mark.profile_id and psm.eshop_id = mark.eshop_id
        AND psm.platform_num_id=mark.platform_num_id and psm.platform_sku_id = mark.platform_sku_id
        left join (select cdlp.profile_id as profileId,
        cdlp.resource_id as ptypeId,
        clv.labelfield_value as labelfieldValue
        from cf_data_label_ptype cdlp
        left join Cf_Labelfield_Value clv
        on clv.id = cdlp.labelfield_value_id
        and clv.profile_id = cdlp.profile_id
        where clv.labelfield_value = '代销'
        and cdlp.profile_id = #{profileId}) temp on temp.profileId = psm.profile_id and temp.ptypeId = p.id
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId} and p.pcategory=2
        <choose>
            <when test="uniqueIdList!=null and uniqueIdList.size()>0">
                and psm.unique_id in
                <foreach collection="uniqueIdList" close=")" open="(" separator="," item="uniqueId">
                    #{uniqueId}
                </foreach>
            </when>
            <otherwise>
                <if test="platformNumIdList!=null and platformNumIdList.size()>0">
                    and psm.platform_num_id in
                    <foreach collection="platformNumIdList" close=")" open="(" separator="," item="numId">
                        #{numId}
                    </foreach>
                </if>
                <if test="platformSkuIdList!=null and platformSkuIdList.size()>0">
                    and psm.platform_sku_id in
                    <foreach collection="platformSkuIdList" close=")" open="(" separator="," item="skuId">
                        #{skuId}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        order by mapping.ptype_id desc
    </select>

    <select id="queryEshopProductRelationsManualTotal" resultType="java.lang.Integer">

        select count(0)
        from pl_eshop_product pm
        left join pl_eshop_product_class epc on epc.profile_id= pm.profile_id and epc.eshop_id=pm.eshop_id and
        epc.platform_class_id=pm.category_id
        where pm.profile_id=#{profileId} and pm.eshop_id=#{otypeId}
        <if test="classId!=null and classId!=''">
            and pm.category_id in
            <foreach collection="classIdsForSelect" open="(" close=")" index="i" separator="," item="ca">
                #{ca}
            </foreach>
        </if>
        <if test="ids!=null and ids.size>0">
            and pm.id in
            <foreach collection="ids" item="id" separator="," index="i" close=")" open="(">
                #{id}
            </foreach>
        </if>
        <if test="stockState==1">
            and pm.platform_stock_state=1
        </if>
        <if test="stockState==2">
            and pm.platform_stock_state=2
        </if>
        <choose>
            <when test="openXcode and openXcode==true">
                and (exists (
                select 1 from (
                select (case when (ifnull(p.id,0)=0 or p.deleted =1 or p.stoped =1) then false else true end) as isbind,
                psm.id,
                psm.platform_num_id,
                psm.platform_properties_name,
                psm.platform_xcode as platxcode,
                concat('',p.fullname) as ptypeName,
                p.usercode as xcode,
                p.usercode as usercode,
                psm.qty,
                expand.mark
                from pl_eshop_product_sku psm
                left join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=
                expand.profile_id
                join base_ptype p on p.usercode=psm.platform_xcode and p.profile_id=psm.profile_id
                left join pl_eshop_product_mark m on m.profile_id=psm.profile_id and m.eshop_id=psm.eshop_id and psm
                .platform_sku_id=m.platform_sku_id
                where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId}
                and p.pcategory=2 and p.usercode is not null and p.usercode !=''
                <if test="platformNumIds!=null">
                    and psm.platform_num_id in
                    <foreach collection="platformNumIds" item="item" index="i" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                )r
                <where>
                    r.platform_num_id=pm.platform_num_id
                    <if test="mark>0">
                        and r.mark=#{mark}
                    </if>
                    <if test="filterString!=null and filterString!=''">
                        and (pm.platform_fullname like "%"#{filterString}"%" or pm.platform_xcode like
                        "%"#{filterString}"%" or
                        r.platform_properties_name like "%"#{filterString}"%" or r.platxcode like "%"#{filterString}"%")
                    </if>
                    <if test="ptypeFilter!=null and ptypeFilter!=''">
                        and (r.ptypeName like "%"#{ptypeFilter}"%" or r.xcode like "%"#{ptypeFilter}"%" or r.usercode
                        like "%"#{ptypeFilter}"%")
                    </if>

                    <if test="qtyState==1">
                        and r.qty>0
                    </if>
                    <if test="qtyState==2">
                        and r.qty &lt; 1
                    </if>
                    <if test="relationState==1">
                        and r.isbind=false
                    </if>
                    <if test="relationState==2">
                        and r.isbind=true
                    </if>
                </where>
                )
                or exists(
                select 1 from (
                select (case when (ifnull(px.id,0)=0 or p.deleted =1 or p.stoped =1) then false else true end) as
                isbind,
                psm.id,
                psm.platform_num_id,
                psm.platform_properties_name,
                psm.platform_xcode as platxcode,
                concat('',p.fullname) as ptypeName,
                p.usercode as xcode,
                p.usercode as usercode,
                psm.qty,
                expand.mark
                from pl_eshop_product_sku psm
                left join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=
                expand.profile_id
                left join base_ptype_xcode px on px.profile_id=psm.profile_id and px.xcode=psm.platform_xcode
                left join base_ptype p on p.id=px.ptype_id and p.profile_id=psm.profile_id
                left join pl_eshop_product_mark m on m.profile_id=psm.profile_id and m.eshop_id=psm.eshop_id and
                psm.platform_sku_id=m.platform_sku_id
                where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId}
                and (p.pcategory !=2 OR p.pcategory IS NULL)
                <if test="platformNumIds!=null">
                    and psm.platform_num_id in
                    <foreach collection="platformNumIds" item="item" index="i" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                )r2
                <where>
                    r2.platform_num_id=pm.platform_num_id
                    <if test="mark>0">
                        and r2.mark=#{mark}
                    </if>
                    <if test="filterString!=null and filterString!=''">
                        and (pm.platform_fullname like "%"#{filterString}"%" or pm.platform_xcode like
                        "%"#{filterString}"%" or
                        r2.platform_properties_name like "%"#{filterString}"%" or r2.platxcode like
                        "%"#{filterString}"%")
                    </if>
                    <if test="ptypeFilter!=null and ptypeFilter!=''">
                        and (r2.ptypeName like "%"#{ptypeFilter}"%" or r2.xcode like "%"#{ptypeFilter}"%" or r2.usercode
                        like "%"#{ptypeFilter}"%")
                    </if>
                    <if test="relationState==1">
                        and r2.isbind=false
                    </if>
                    <if test="qtyState==1">
                        and r2.qty>0
                    </if>
                    <if test="qtyState==2">
                        and r2.qty &lt; 1
                    </if>
                    <if test="relationState==2">
                        and r2.isbind=true
                    </if>
                </where>
                )
                )
            </when>
            <otherwise>
                and exists(
                select * from(
                select (case when (ifnull(p.id,0)=0 or p.deleted =1 or p.stoped =1) then false else true end)
                as isbind,
                psm.id,
                psm.platform_num_id,
                psm.platform_properties_name,
                psm.platform_xcode as platxcode,
                concat('',p.fullname) as ptypeName,
                p.usercode as xcode,
                p.usercode as usercode,
                psm.qty,
                expand.mark
                from pl_eshop_product_sku psm
                left join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=
                expand.profile_id
                left join pl_eshop_product_sku_mapping mapping on mapping.profile_id= psm.profile_id and
                mapping.unique_id = psm.unique_id
                left join base_ptype_xcode px on px.profile_id=psm.profile_id and px.unit_id=mapping.unit_id and
                px.sku_id=mapping.sku_id AND px.defaulted=1
                left join base_ptype p on p.id=mapping.ptype_id and p.profile_id=psm.profile_id
                left join pl_eshop_product_mark m on m.profile_id=psm.profile_id and m.eshop_id=psm.eshop_id and
                m.platform_sku_id=psm.platform_sku_id
                where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId}
                <if test="platformNumIds!=null">
                    and psm.platform_num_id in
                    <foreach collection="platformNumIds" item="item" index="i" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                ) r
                <where>
                    r.platform_num_id=pm.platform_num_id
                    <if test="mark>0">
                        and r.mark=#{mark}
                    </if>
                    <if test="filterString!=null and filterString!=''">
                        and (pm.platform_fullname like "%"#{filterString}"%" or pm.platform_xcode like
                        "%"#{filterString}"%" or
                        r.platform_properties_name like "%"#{filterString}"%" or r.platxcode like "%"#{filterString}"%")
                    </if>
                    <if test="ptypeFilter!=null and ptypeFilter!=''">
                        and (r.ptypeName like "%"#{ptypeFilter}"%" or r.xcode like "%"#{ptypeFilter}"%" or r.usercode
                        like "%"#{ptypeFilter}"%")
                    </if>
                    <if test="qtyState==1">
                        and r.qty>0
                    </if>
                    <if test="qtyState==2">
                        and r.qty &lt; 1
                    </if>
                    <if test="relationState==1">
                        and r.isbind=false
                    </if>
                    <if test="relationState==2">
                        and r.isbind=true
                    </if>
                </where>
                )
            </otherwise>
        </choose>
        order by pm.platform_fullname
    </select>
    <select id="loadEshopComboSkuMappingItemOpenXcodeForOrder"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select pepsrc.id                                                            as sync_rule_id,
               p.pcategory,
               (case when (p.deleted = 1 or p.stoped = 1 or IFNULL(p.id, 0) = 0) then false else true end)
                                                                                    as isbind,
               p.id                                                                 as ptype_id,
               1                                                                    as unitRate,
               bpx.xcode                                                            as xcode,
               p.fullname                                                           as ptypeName,
               p.barcode                                                            as barcode,
               p.tax_rate                                                           as taxRate,
               p.cost_mode                                                          as costMode,
               (case when (temp.labelfieldValue = '代销') then true else false end) as saleProxyLabel,
               r.id                                                                 as syncRuleId,
               bpc.sale_type as  saleType
        FROM base_ptype_xcode bpx
                 LEFT JOIN pl_eshop_stock_sync_rule r
                           ON r.profile_id = bpx.profile_id AND r.xcode = bpx.xcode AND r.pcategory = 2
                 LEFT JOIN pl_eshop_stock_sync_rule pepsrc
                           ON pepsrc.profile_id = bpx.profile_id AND pepsrc.xcode = bpx.xcode AND
                              pepsrc.pcategory = 2 and pepsrc.deleted = 0
                 LEFT JOIN base_ptype p ON bpx.profile_id = p.profile_id AND bpx.ptype_id = p.id AND p.pcategory = 2
                 LEFT JOIN base_ptype_combo bpc on bpc.profile_id = bpx.profile_id and bpc.combo_id = p.id
                 left join (select cdlp.profile_id      as profileId,
                                   cdlp.resource_id     as ptypeId,
                                   clv.labelfield_value as labelfieldValue
                            from cf_data_label_ptype cdlp
                                     left join Cf_Labelfield_Value clv
                                               on clv.id = cdlp.labelfield_value_id
                                                   and clv.profile_id = cdlp.profile_id
                            where clv.labelfield_value = '代销'
                              and cdlp.profile_id = #{profileId}) temp
                           on temp.profileId = bpx.profile_id and temp.ptypeId = bpx.ptype_id
        WHERE bpx.profile_id = #{profileId}
          AND bpx.xcode = #{xcode}
          AND bpx.info_type = 1
        ORDER BY isbind DESC
        LIMIT 1
    </select>
    <select id="loadEshopSkuMappingItemOpenXcodeForOrder"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select pepsrc.id                                                                                     as sync_rule_id,
               (case when (ifnull(bpx.id, 0) = 0 or p.deleted = 1 or p.stoped = 1) then false else true end) as isbind,
               bpx.ptype_id,
               bpx.sku_id,
               unit.id                                                                                       as unitId,
               ifnull(unit.unit_rate, 1)                                                                     as unitRate,
               bpx.xcode,
               baseunit.id                                                                                   as baseUnitId,
               p.pcategory,
               p.fullname                                                                                    as ptypeName,
               p.cost_mode                                                                                   as costMode,
               bpf.fullbarcode                                                                               AS fullbarcode,
               p.tax_rate                                                                                    as taxRate,
               (case when (temp.labelfieldValue = '代销') then true else false end)                          as saleProxyLabel
        FROM base_ptype_xcode bpx
                 LEFT JOIN pl_eshop_stock_sync_rule pepsrc
                           ON pepsrc.profile_id = bpx.profile_id AND pepsrc.xcode = bpx.xcode AND
                              pepsrc.pcategory != 2 and pepsrc.deleted = 0
                 LEFT JOIN base_ptype p ON p.id = bpx.ptype_id AND p.profile_id = bpx.profile_id
                 LEFT JOIN `base_ptype_fullbarcode` bpf
                           ON bpf.profile_id = bpx.profile_id AND bpf.sku_id = bpx.sku_id AND
                              bpf.ptype_id = bpx.ptype_id AND bpf.unit_id = bpx.unit_id
                 LEFT JOIN base_ptype_unit unit ON unit.id = bpx.unit_id AND unit.profile_id = bpx.profile_id
                 LEFT JOIN base_ptype_unit baseunit
                           ON baseunit.profile_id = bpx.profile_id AND bpx.ptype_id = baseunit.ptype_id AND
                              baseunit.unit_code = 1
                 left join (select cdlp.profile_id      as profileId,
                                   cdlp.resource_id     as ptypeId,
                                   clv.labelfield_value as labelfieldValue
                            from cf_data_label_ptype cdlp
                                     left join Cf_Labelfield_Value clv
                                               on clv.id = cdlp.labelfield_value_id
                                                   and clv.profile_id = cdlp.profile_id
                            where clv.labelfield_value = '代销'
                              and cdlp.profile_id = #{profileId}) temp
                           on temp.profileId = bpx.profile_id and temp.ptypeId = p.id
        WHERE bpx.profile_id = #{profileId}
          AND bpx.xcode = #{xcode}
        ORDER BY isbind DESC
        LIMIT 1
    </select>

    <select id="notLoadEshopSkuMappingItemOpenXcodeForOrder"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select pepsrc.id                                                                                     as sync_rule_id,
               (case when (ifnull(bpx.id, 0) = 0 or p.deleted = 1 or p.stoped = 1) then false else true end) as isbind,
               bpx.ptype_id,
               bpx.sku_id,
               unit.id                                                                                       as unitId,
               ifnull(unit.unit_rate, 1)                                                                     as unitRate,
               bpx.xcode,
               baseunit.id                                                                                   as baseUnitId,
               p.pcategory,
               p.fullname                                                                                    as ptypeName,
               bpf.fullbarcode                                                                               AS fullbarcode,
               p.tax_rate                                                                                    as taxRate,
               p.cost_mode                                                                                   as costMode,
               (case when (temp.labelfieldValue = '代销') then true else false end)                          as saleProxyLabel

        FROM base_ptype_xcode bpx
                 LEFT JOIN pl_eshop_stock_sync_rule pepsrc
                           ON pepsrc.profile_id = bpx.profile_id AND pepsrc.xcode = bpx.xcode and pepsrc.deleted = 0
                 LEFT JOIN base_ptype p ON p.id = bpx.ptype_id AND p.profile_id = bpx.profile_id
                 LEFT JOIN `base_ptype_fullbarcode` bpf
                           ON bpf.profile_id = bpx.profile_id AND bpf.sku_id = bpx.sku_id AND
                              bpf.ptype_id = bpx.ptype_id AND bpf.unit_id = bpx.unit_id
                 LEFT JOIN base_ptype_unit unit ON unit.id = bpx.unit_id AND unit.profile_id = bpx.profile_id
                 LEFT JOIN base_ptype_unit baseunit
                           ON baseunit.profile_id = bpx.profile_id AND bpx.ptype_id = baseunit.ptype_id AND
                              baseunit.unit_code = 1
                 left join (select cdlp.profile_id      as profileId,
                                   cdlp.resource_id     as ptypeId,
                                   clv.labelfield_value as labelfieldValue
                            from cf_data_label_ptype cdlp
                                     left join Cf_Labelfield_Value clv
                                               on clv.id = cdlp.labelfield_value_id
                                                   and clv.profile_id = cdlp.profile_id
                            where clv.labelfield_value = '代销'
                              and cdlp.profile_id = #{profileId}) temp
                           on temp.profileId = bpx.profile_id and temp.ptypeId = p.id
        WHERE bpx.profile_id = #{profileId}
          AND bpx.xcode = #{xcode}
        ORDER BY isbind DESC
        LIMIT 1
    </select>
    <select id="loadProductMappingListByEshopIdCount"
            resultType="java.lang.Integer">
        select count(*)
        from pl_eshop_product
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
    </select>
    <select id="loadProductMappingListByEshopId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMapping">
        select *
        from pl_eshop_product
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
    </select>
    <select id="loadProductMappingListByEshopIdByLimit"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMapping">
        select *
        from pl_eshop_product
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
        limit #{pageNum} , #{pageSize}
    </select>

    <select id="loadEshopSkuMappingByEshopIdCount"
            resultType="java.lang.Integer">
        select count(*)
        from pl_eshop_product_sku sku
                 join pl_eshop_product m
                      on m.platform_num_id = sku.platform_num_id and m.profile_id = sku.profile_id and
                         m.eshop_id = sku.eshop_id
        where sku.profile_id = #{profileId}
          and sku.eshop_id = #{eshopId}
    </select>
    <select id="loadEshopSkuMappingByEshopId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select m.platform_fullname as
        platfullname,m.has_properties,sku.id,sku.profile_Id,sku.eshop_id,sku.platform_sku_id,sku.platform_Num_Id,
        m.default_sku_id,sku.platform_properties_Name,sku.platform_properties,0 as isredundant,sku.platform_xcode,
        sku.platform_xcode as platxcode,sku.platform_modified_time,m.default_sku_id
        from pl_eshop_product_sku sku
        left join pl_eshop_product_sku_expand expand
        on expand.profile_id = sku.profile_id and expand.eshop_id = sku.eshop_id and
        expand.unique_id = sku.unique_id
        left join pl_eshop_product_sku_mapping mapping
        on mapping.profile_id = sku.profile_id and mapping.eshop_id = sku.eshop_id and
        mapping.unique_id = sku.unique_id
        join pl_eshop_product m
        on m.platform_num_id = sku.platform_num_id and m.profile_id = sku.profile_id and
        m.eshop_id = sku.eshop_id
        where sku.profile_id = #{profileId} and expand.mapping_type = 0
        and sku.storage_type = 0
        and sku.eshop_id = #{eshopId}
        <if test="relationState == 1">
            and mapping.ptype_id is null
        </if>
        union
        select m.platform_fullname as
        platfullname,m.has_properties,sku.id,sku.profile_Id,sku.eshop_id,sku.platform_sku_id,sku.platform_Num_Id,
        m.default_sku_id,sku.platform_properties_Name,sku.platform_properties,0 as isredundant,sku.platform_xcode,
        sku.platform_xcode as platxcode,sku.platform_modified_time,m.default_sku_id
        from pl_eshop_product_sku sku
        left join pl_eshop_product_sku_expand expand
        on expand.profile_id = sku.profile_id and expand.eshop_id = sku.eshop_id and
        expand.unique_id = sku.unique_id
        join pl_eshop_product m
        on m.platform_num_id = sku.platform_num_id and m.profile_id = sku.profile_id and
        m.eshop_id = sku.eshop_id
        left join base_ptype_xcode xcode on xcode.profile_id = sku.profile_id and xcode.xcode = sku.platform_xcode
        left join base_ptype p on p.id = xcode.ptype_id and p.profile_id = sku.profile_id and p.deleted = 0 and p.stoped
        = 0
        where sku.profile_id = #{profileId} and expand.mapping_type = 1
        and sku.storage_type = 0
        and sku.eshop_id = #{eshopId}
        <if test="relationState == 1">
            and p.id is null
        </if>
    </select>
    <select id="loadEshopSkuMappingByEshopIdByLimit"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select m.platform_fullname as platfullname,
               sku.id,
               sku.profile_Id,
               sku.eshop_id,
               sku.platform_sku_id,
               sku.platform_Num_Id,
               m.default_sku_id,

               sku.platform_properties_Name,
               platform_properties,
               0                   as isredundant,
               sku.platform_xcode,
               sku.platform_xcode  as platxcode,
               sku.platform_modified_time,
               m.default_sku_id
        from pl_eshop_product_sku sku
                 join pl_eshop_product m
                      on m.platform_num_id = sku.platform_num_id and m.profile_id = sku.profile_id and
                         m.eshop_id = sku.eshop_id
        where sku.profile_id = #{profileId}
          and sku.eshop_id = #{eshopId}
        limit #{pageNum} , #{pageSize}
    </select>

    <select id="queryOnLineBabyListByXcode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMappingApi">
        select *
        from pl_eshop_product_sku
        where profile_id = #{profileId}
          and eshop_id = #{otypeId}
          and platform_xcode = #{xcode}
          and deleted = 0
    </select>
    <select id="queryOnLineBabyListByPtype"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMappingApi">
        select *
        from pl_eshop_product_sku
        where profile_id = #{profileId}
          and eshop_id = #{otypeId}
          and ptype_id = #{ptypeId}
          and unit_id = #{unitId}
          and sku_id = #{skuId}
          and deleted = 0
    </select>
    <select id="queryRelationCountBySkuId" resultType="java.lang.Integer">
        select count(1) from (
        select sku.profile_id,sku.id
        from pl_eshop_product_sku_mapping mapping
        left join pl_eshop_product_sku sku on sku.unique_id = mapping.unique_id and sku.profile_id= mapping.profile_id
        left join pl_eshop_product_sku_expand expand on expand.unique_id = mapping.unique_id and expand.profile_id=
        mapping.profile_id
        left join base_ptype_xcode bpx on bpx.profile_id=mapping.profile_id and bpx.sku_id=mapping.sku_id and
        bpx.unit_id=mapping.unit_id
        LEFT JOIN base_ptype p ON p.id=mapping.ptype_id AND p.profile_id=mapping.profile_id
        where mapping.profile_id=#{profileid} and mapping.eshop_id=#{eshopId}
        and expand.mapping_type=0 and p.deleted=0 and p.stoped=0 and  p.pcategory!=2
        <if test="skuIds!=null and skuIds.size()>0">
            and mapping.sku_id in
            <foreach collection="skuIds" item="skuId" separator="," index="i" close=")" open="(">
                #{skuId}
            </foreach>
        </if>
        union
        select sku.profile_id,sku.id
        from base_ptype_xcode bpx
        left join pl_eshop_product_sku sku on sku.profile_id= bpx.profile_id and sku.platform_xcode = bpx.xcode
        left join pl_eshop_product_sku_expand expand on expand.unique_id = sku.unique_id and expand.profile_id=sku.profile_id
        LEFT JOIN base_ptype p ON p.id=bpx.ptype_id AND p.profile_id=bpx.profile_id
        where bpx.profile_id=#{profileid} and sku.eshop_id=#{eshopId} and expand.mapping_type=1 and p.deleted=0 and
        p.stoped=0 and bpx.ptype_id>0 and p.pcategory!=2
        and bpx.xcode not in (select bpx.xcode from base_ptype_xcode bpx left join base_ptype bp on
        bp.profile_id=bpx.profile_id and bp.id=bpx.ptype_id
        where bpx.profile_id=#{profileid} and bpx.info_type=1 and bp.stoped=0 and bp.deleted=0 and bp.pcategory = 2)
        <if test="skuIds!=null and skuIds.size()>0">
            and bpx.sku_id in
            <foreach collection="skuIds" item="skuId" separator="," index="i" close=")" open="(">
                #{skuId}
            </foreach>
        </if>
        ) a where a.profile_id=#{profileid}
    </select>
    <insert id="insertProductMappingList">
        insert into pl_eshop_product
        (id,
        profile_id,
        eshop_id,
        platform_pic_url,
        category_id,
        platform_num_id,
        platform_fullname,
        platform_xcode,
        has_properties,
        default_sku_id,
        platform_stock_state,
        platform_price,
        platform_alias,
        platform_modify_time,
        warehouse_code,
        platform_brand
        )
        values
        <foreach collection="newProductList" item="item" index="index" separator=",">
            (#{item.id}, #{item.profileId}, #{item.eshopId},
            #{item.picUrl}, #{item.categoryId}, #{item.platformNumId}, #{item.fullName}, #{item.xCode},
            #{item.hasProperties},
            #{item.defaultSkuId},
            #{item.stockState},
            #{item.price},
            #{item.propAlias},
            #{item.onlineModifyTime},
            #{item.warehouseCode},
            #{item.platformBrand}
            )
        </foreach>
        ON DUPLICATE KEY
        UPDATE
        platform_pic_url=values(platform_pic_url),
        category_id=values(category_id),
        platform_num_id=values(platform_num_id),
        platform_fullname=values(platform_fullname),
        platform_xcode=values(platform_xcode),
        has_properties=values(has_properties),
        default_sku_id=values(default_sku_id),
        platform_stock_state=values(platform_stock_state),
        platform_price=values(platform_price),
        platform_alias=values(platform_alias),
        platform_modify_time=values(platform_modify_time),
        warehouse_code=values(warehouse_code),
        platform_brand=values(platform_brand)
    </insert>
    <insert id="insertProductSkuList">
        insert into pl_eshop_product_sku
        (id,
        profile_id, eshop_id, platform_num_id, platform_sku_id, platform_properties_name, platform_xcode,
        platform_properties, platform_full_properties, platform_pic_url, platform_json, platform_modified_time, qty,
        unique_id, platform_barcode, platform_price, product_id,
        warehouse_code,storage_type,memo_platform_full_properties,platform_unit_id,platform_unit_name,refresh_version)
        values
        <foreach collection="newSkuList" item="item" index="index" separator=",">
            (#{item.id}, #{item.profileId}, #{item.eshopId},#{item.platformNumId},
            #{item.platformSkuId},
            #{item.platformPropertiesName},
            #{item.platformXcode},
            #{item.platformProperties},
            #{item.platformFullPropertiesName},
            #{item.platformPicUrl},
            #{item.platformJson},
            #{item.platformModifiedTime},
            #{item.qty},
            #{item.uniqueId},
            #{item.barcode},#{item.price},#{item.productId},#{item.warehouseCode},#{item.storageType},
            #{item.hasMemoFullPropertiesName},#{item.platformUnitId},#{item.platformUnitName},#{item.refreshVersion})
        </foreach>
        ON DUPLICATE KEY
        UPDATE
        platform_num_id = values(platform_num_id),
        platform_sku_id = values(platform_sku_id),
        platform_properties_name = values(platform_properties_name),
        platform_xcode = values(platform_xcode),
        platform_properties = values(platform_properties),
        platform_full_properties = values(platform_full_properties),
        platform_pic_url = values(platform_pic_url),
        platform_json = values(platform_json),
        platform_modified_time = values(platform_modified_time),
        qty = values(qty),
        gift_id = values(gift_id),
        unique_id = values(unique_id),
        platform_barcode = values(platform_barcode),
        platform_price = values(platform_price),
        product_id = values(product_id),
        warehouse_code = values(warehouse_code),
        storage_type = values(storage_type),
        memo_platform_full_properties = values(memo_platform_full_properties),
        platform_unit_id = values(platform_unit_id),
        platform_unit_name = values(platform_unit_name),
        refresh_version = values(refresh_version)
    </insert>

    <insert id="insertProductSkuExpand">
        insert into pl_eshop_product_sku_expand
        (id,
        profile_id,
        eshop_id,
        unique_id,
        ready_pfullname,
        ready_pusercode,
        ready_sku_xcode,
        ready_sku_name,
        mark,
        download_enable,mapping_type,auto_sync_enabled)
        values
        <foreach collection="newSkuExpandList" item="item" index="index" separator=",">
            (#{item.id}, #{item.profileId},
            #{item.eshopId},#{item.uniqueId},#{item.eshopId},#{item.readyPfullname},#{item.readySkuXcode},#{item.readySkuName},#{item.mark},#{item.downloadEnable},#{item.mappingType},#{item.autoSyncEnabled})
        </foreach>
        ON DUPLICATE KEY
        UPDATE
        profile_id= values(profile_id),
        eshop_id=values(eshop_id)
    </insert>

    <update id="updateProductSkuExpandMappingType">
        update pl_eshop_product_sku_expand set mapping_type=#{mappingTypeState}
        where profile_id=#{profileId} and eshop_id=#{eshopId}
        <if test="uniqueIds!=null and uniqueIds.size()>0">
            and unique_id in
            <foreach collection="uniqueIds" item="item" index="i" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </update>


    <update id="modifyProductExpandLastNewOrderTime">
        update pl_eshop_product_sku_expand set last_new_order_time=#{lastNewOrderTime}
        where profile_id=#{profileId} and eshop_id=#{eshopId}
        <if test="uniqueIds!=null and uniqueIds.size()>0">
            and unique_id in
            <foreach collection="uniqueIds" item="item" index="i" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="updateProductSkuExpandMappingTypeByNumId">
        update pl_eshop_product_sku_expand
        set mapping_type=#{mappingTypeState}
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and unique_id in (select unique_id
                            from pl_eshop_product_sku sku
                            where sku.profile_id = #{profileId}
                              and sku.eshop_id = #{eshopId}
                              and sku.platform_num_id = #{platformNumId})
    </update>


    <insert id="insertProductSkuAttrRelation">
        insert into pl_eshop_product_attr_relation
        (id,
        profile_id,
        eshop_id,
        platform_prop,
        row_index,
        prop_group_count,
        local_prop,
        prop_id)
        values
        <foreach collection="newSkuAttrRelationList" item="item" index="index" separator=",">
            (#{item.id}, #{item.profileId},
            #{item.eshopId},#{item.platformProp},#{item.rowIndex},#{item.propGroupCount},#{item.localProp},#{item.propId})
        </foreach>
        ON DUPLICATE KEY
        UPDATE
        platform_prop= values(platform_prop)
    </insert>

    <update id="updateProductSkuAttrRelation">
        update pl_eshop_product_attr_relation
        set local_prop= #{skuAttrRelationList.localProp},
            prop_id=#{skuAttrRelationList.propId}
        where profile_id = #{skuAttrRelationList.profileId}
          and id = #{skuAttrRelationList.id}

    </update>

    <update id="updatePlatformAttrRelation">
        update pl_eshop_product_attr_relation
        set local_prop= #{skuAttrRelationList.localProp},
            prop_id=#{skuAttrRelationList.propId},
            platform_prop=#{skuAttrRelationList.platformProp}
        where profile_id = #{skuAttrRelationList.profileId}
          and id = #{skuAttrRelationList.id}

    </update>

    <update id="updateShelfOnRules">
        update pl_eshop_product
        set auto_shelf_on= #{queryParams.autoShelfOn},on_count=#{queryParams.onCount},off_count=#{queryParams.offCount}
        where profile_id=#{queryParams.profileId} and eshop_id=#{queryParams.otypeId}
        <if test="queryParams.platformNumIds != null and queryParams.platformNumIds.size()>0">
            and platform_num_id in
            <foreach collection="queryParams.platformNumIds" item="numid" index="i" separator="," open="(" close=")">
                #{numid}
            </foreach>
        </if>
    </update>

    <insert id="saveMarkNew">
        INSERT INTO pl_eshop_product_mark (id, platform_sku_id, eshop_id, profile_id,
                                           mark_code, bubble, show_type, platform_properties_name, unique_id,
                                           platform_num_id)
        VALUES (#{id}, #{platformSkuId}, #{eshopId}, #{profileId}, #{markCode}, #{bubble}, #{showType},
                #{platformPropertiesName}, #{uniqueId}, #{platformNumId});
    </insert>


    <delete id="deleteProductMarkByMarkCodes">
        delete
        from pl_eshop_product_mark
        where profile_id = #{mark.profileId}
        and eshop_id = #{mark.eshopId}
        and unique_id = #{mark.uniqueId}
        <if test="markCodeList!= null and markCodeList.size()>0">
            and mark_code in
            <foreach collection="markCodeList" item="markCode" index="i" separator="," open="(" close=")">
                #{markCode}
            </foreach>
        </if>
    </delete>

    <delete id="deleteProductMarkByMarkCode">
        delete
        from pl_eshop_product_mark
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and unique_id = #{uniqueId}
          and mark_code = #{markCode};
    </delete>
    <select id="getProductMarkByMarkCode" resultType="java.math.BigInteger">
        select id
        from pl_eshop_product_mark
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and unique_id = #{uniqueId}
          and mark_code = #{markCode};
    </select>

    <delete id="deleteProductMarkByMarkCodeAndNumiId">
        delete
        from pl_eshop_product_mark
        where profile_id = #{mark.profileId}
        and eshop_id = #{mark.eshopId}
        <if test="numIds!=null and numIds.size()>0">
            and platform_num_id in
            <foreach collection="numIds" item="numId" index="i" separator="," open="(" close=")">
                #{numId}
            </foreach>
        </if>
        <if test="(numIds==null or numIds.size()==0) and mark.platformNumId!=null and mark.platformNumId!=''">
            and platform_num_id = #{mark.platformNumId}
        </if>
        and mark_code = #{mark.markCode};
    </delete>

    <delete id="batchDeleteProductMarkByMarkCode">
        delete
        from pl_eshop_product_mark
        where profile_id = #{profileId}
        and eshop_id = #{eshopId}
        and mark_code = #{markCode}
        and unique_id in
        <foreach collection="uniqueIdList" item="uniqueId" index="i" separator="," open="(" close=")">
            #{uniqueId}
        </foreach>
    </delete>

    <delete id="deleteProductMarkByuniqueId">
        delete
        from pl_eshop_product_mark
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and unique_id = #{uniqueId}
          and mark_code not in (1007, 1011, 1013, 1014)
    </delete>

    <delete id="deleteProductMarkBigDataByuniqueId">
        delete
        from pl_eshop_product_mark_data
        where profile_id = #{profileId}
          and mark_id in (select id
                          from pl_eshop_product_mark
                          where profile_id = #{profileId}
                            and eshop_id = #{eshopId}
                            and unique_id = #{uniqueId})
    </delete>

    <select id="getAllSkuMappingByPlatformModifiedTime"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select psm.profile_id,
               mapping.ptype_id,
               mapping.sku_id,
               mapping.unit_id,
               psm.platform_sku_id,
               psm.platform_num_id,
               psm.platform_properties_name,
               psm.platform_xcode,
               psm.platform_properties,
               psm.platform_pic_url,
               psm.platform_modified_time,
               psm.qty,
               psm.eshop_id,
               (case when (p.deleted = 1 or p.stoped = 1 or IFNULL(p.id, 0) = 0) then false else true end) as isbind,
               p.pcategory
        from pl_eshop_product_sku psm
                 left join pl_eshop_product_sku_mapping mapping
                           on mapping.profile_id = psm.profile_id and mapping.unique_id = psm.unique_id and
                              mapping.eshop_id = psm.eshop_id
                 left join base_ptype p
                           on p.profile_id = psm.profile_id and psm.platform_xcode = p.usercode and p.pcategory = 2
        where psm.profile_id = #{profileId}
          and psm.eshop_id = #{otypeId}
          and (psm.platform_modified_time > #{platformModifiedTime} or
               mapping.platform_modified_time > #{platformModifiedTime})
        union
        select psm.profile_id,
               bpx.ptype_id,
               bpx.sku_id,
               unit.id                                                                                     as unitId,
               psm.platform_sku_id,
               psm.platform_num_id,
               psm.platform_properties_name,
               psm.platform_xcode,
               psm.platform_properties,
               psm.platform_pic_url,
               psm.platform_modified_time,
               psm.qty,
               psm.eshop_id,
               (case when (p.deleted = 1 or p.stoped = 1 or IFNULL(p.id, 0) = 0) then false else true end) as isbind,
               p.pcategory
        from pl_eshop_product_sku psm
                 left join base_ptype_xcode bpx on bpx.profile_id = psm.profile_id and psm.platform_xcode = bpx.xcode
                 left join pl_eshop_product_sku_mapping mapping
                           on mapping.profile_id = psm.profile_id and mapping.unique_id = psm.unique_id and
                              mapping.eshop_id = psm.eshop_id
                 left join base_ptype p on p.id = bpx.ptype_id and p.profile_id = bpx.profile_id
                 left join base_ptype_unit unit on unit.id = bpx.unit_id and unit.profile_id = bpx.profile_id
        where psm.profile_id = #{profileId}
          and psm.eshop_id = #{otypeId}
          and (psm.platform_modified_time > #{platformModifiedTime} or
               mapping.platform_modified_time > #{platformModifiedTime})
        union
        select psm.profile_id,
               bpx.ptype_id,
               bpx.sku_id,
               unit.id                                                                                     as unitId,
               psm.platform_sku_id,
               psm.platform_num_id,
               psm.platform_properties_name,
               psm.platform_xcode,
               psm.platform_properties,
               psm.platform_pic_url,
               psm.platform_modified_time,
               psm.qty,
               psm.eshop_id,
               (case when (p.deleted = 1 or p.stoped = 1 or IFNULL(p.id, 0) = 0) then false else true end) as isbind,
               p.pcategory
        from base_ptype_xcode bpx
                 left join pl_eshop_product_sku psm
                           on psm.profile_id = bpx.profile_id and psm.platform_xcode = bpx.xcode
                 left join pl_eshop_product_sku_mapping mapping
                           on mapping.profile_id = psm.profile_id and mapping.unique_id = psm.unique_id and
                              mapping.eshop_id = psm.eshop_id
                 left join base_ptype p on p.id = bpx.ptype_id and p.profile_id = bpx.profile_id
                 left join base_ptype_unit unit on unit.id = bpx.unit_id and unit.profile_id = bpx.profile_id
        where psm.profile_id = #{profileId}
          and psm.eshop_id = #{otypeId}
          and (psm.platform_modified_time > #{platformModifiedTime} or
               mapping.platform_modified_time > #{platformModifiedTime})
        union
        select psm.profile_id,
               mapping.ptype_id,
               mapping.sku_id,
               mapping.unit_id,
               psm.platform_sku_id,
               psm.platform_num_id,
               psm.platform_properties_name,
               psm.platform_xcode,
               psm.platform_properties,
               psm.platform_pic_url,
               psm.platform_modified_time,
               psm.qty,
               psm.eshop_id,
               (case when (p.deleted = 1 or p.stoped = 1 or IFNULL(p.id, 0) = 0) then false else true end) as isbind,
               p.pcategory
        from pl_eshop_product_sku psm
                 left join pl_eshop_product_sku_mapping mapping
                           on mapping.profile_id = psm.profile_id and mapping.unique_id = psm.unique_id and
                              mapping.eshop_id = psm.eshop_id
                 left join pl_eshop_product pm on pm.profile_id = psm.profile_id and pm.eshop_id = psm.eshop_id and
                                                  pm.platform_num_id = psm.platform_num_id
                 left join pl_eshop_product_sku_expand expand
                           on psm.unique_id = expand.unique_id and psm.profile_id = expand.profile_id
                 left join pl_eshop_product_sku_rule_config pepsrc
                           on pepsrc.profile_id = psm.profile_id and pepsrc.eshop_id = psm.eshop_id and
                              pepsrc.platform_num_id = psm.platform_num_id and
                              pepsrc.platform_properties = psm.platform_properties_name and
                              pepsrc.warehouse_code = pm.warehouse_code
                 join base_ptype p on p.profile_id = psm.profile_id and p.id = mapping.ptype_id and p.pcategory = 2
        where psm.profile_id = #{profileId}
          and psm.eshop_id = #{otypeId}
          and (psm.platform_modified_time > #{platformModifiedTime} or
               mapping.platform_modified_time > #{platformModifiedTime})
        union
        select psm.profile_id,
               mapping.ptype_id,
               mapping.sku_id,
               mapping.unit_id,
               psm.platform_sku_id,
               psm.platform_num_id,
               psm.platform_properties_name,
               psm.platform_xcode,
               psm.platform_properties,
               psm.platform_pic_url,
               psm.platform_modified_time,
               psm.qty,
               psm.eshop_id,
               (case when (p.deleted = 1 or p.stoped = 1 or IFNULL(p.id, 0) = 0) then false else true end) as isbind,
               p.pcategory
        from pl_eshop_product_sku psm
                 left join pl_eshop_product_sku_mapping mapping
                           on mapping.profile_id = psm.profile_id and mapping.unique_id = psm.unique_id and
                              mapping.eshop_id = psm.eshop_id
                 left join pl_eshop_product pm on pm.profile_id = psm.profile_id and pm.eshop_id = psm.eshop_id and
                                                  pm.platform_num_id = psm.platform_num_id
                 left join base_ptype p
                           on p.profile_id = psm.profile_id and p.id = mapping.ptype_id and p.pcategory != 2
        where psm.profile_id = #{profileId}
          and psm.eshop_id = #{otypeId}
          and (psm.platform_modified_time
                   > #{platformModifiedTime}
            or mapping.platform_modified_time
                   > '2022-01-01')
    </select>

    <delete id="clearUnRelationTempEshopProductSku">
        delete
        from pl_eshop_product_sku
        where profile_id = #{profileId}
          and unique_id = #{uniqueId}
          and storage_type = 1
    </delete>
    <delete id="cleanProductSkuByNumIdAndRefreshVersion">
        delete sku
        from pl_eshop_product_sku sku
        where sku.profile_id = #{profileId}
        and sku.eshop_id = #{eshopId}
        and sku.storage_type = 0
        and  CAST(sku.refresh_version AS UNSIGNED) &lt; #{refreshVersion}
        <if test="distinctNumIdList !=null and distinctNumIdList.size()>0">
            and sku.platform_num_id in
            <foreach collection="distinctNumIdList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <insert id="insertEshopStockSyncAbstractLog">
        insert into pl_eshop_stock_sync_abstract_log
        (profile_id, eshop_id, unique_id,
         last_stock_sync_time_success,
         last_stock_sync_time, sync_qty, sync_status, etype_id, log_id, platform_num_id, platform_sku_id, xcode,
         target_type, warehouse_code)
        values (#{profileId}, #{eshopId}, #{uniqueId},
                #{lastStockSyncTimeSuccess}, #{lastStockSyncTime},
                #{syncQty}, #{syncStatus}, #{etypeId}, #{logId}, #{platformNumId}, #{platformSkuId}, #{xcode},
                #{targetType}, #{warehouseCode})
        ON DUPLICATE KEY UPDATE last_stock_sync_time         = #{lastStockSyncTime},
                                sync_qty                     = #{syncQty},
                                sync_status                  = #{syncStatus},
                                etype_id                     = #{etypeId},
                                log_id                       = #{logId},
                                last_stock_sync_time_success = #{lastStockSyncTimeSuccess},
                                xcode                        = #{xcode},
                                target_type                  = #{targetType},
                                warehouse_code               = #{warehouseCode}
    </insert>

    <update id="updateProductSkuUniqueId">
        <foreach collection="onlineProductSkuList" item="item" separator=";">
            update ignore pl_eshop_product_sku set unique_id = #{item.uniqueId}
            where profile_id= #{item.profileId} and eshop_id = #{item.eshopId} and unique_id = #{item.oldUniqueId}
        </foreach>
    </update>

    <update id="updateProductSkuExpandUniqueId">
        <foreach collection="onlineProductSkuList" item="item" separator=";">
            update ignore pl_eshop_product_sku_expand set unique_id = #{item.uniqueId}
            where profile_id= #{item.profileId} and eshop_id = #{item.eshopId} and unique_id = #{item.oldUniqueId}
        </foreach>
    </update>

    <update id="updateProductSkuMappingUniqueId">
        <foreach collection="onlineProductSkuList" item="item" separator=";">
            update ignore pl_eshop_product_sku_mapping set unique_id = #{item.uniqueId}
            where profile_id= #{item.profileId} and eshop_id = #{item.eshopId} and unique_id = #{item.oldUniqueId}
        </foreach>
    </update>

    <update id="updateProductSkuStorageType">
        update ignore pl_eshop_product_sku
        set storage_type = 0
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and unique_id = #{uniqueId}
    </update>

    <select id="getPtypeExist" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        SELECT pc.sale_type                                                         AS saleType,
        P.tax_rate                                                           AS taxRate,
        p.fullname                                                           AS ptypeName,
        pu.id                                                                AS unitId,
        p.id                                                                 AS ptypeId,
        ps.id                                                                AS skuId,
        (case when (temp.labelfieldValue  =  '代销') then true else false end)  as saleProxyLabel,
        ifnull(pu.unit_rate,1) as unitRate
        FROM base_ptype p
        LEFT JOIN base_ptype_sku ps ON p.profile_id = ps.profile_id AND p.id = ps.ptype_id
        LEFT JOIN base_ptype_unit pu ON pu.profile_id = p.profile_id AND pu.ptype_id = p.id
        LEFT JOIN base_ptype_combo pc ON pc.profile_id = p.profile_id AND pc.combo_id = p.id
        LEFT JOIN (SELECT cdlp.profile_id      AS profileId,
        cdlp.resource_id     AS ptypeId,
        clv.labelfield_value AS labelfieldValue
        FROM cf_data_label_ptype cdlp
        LEFT JOIN Cf_Labelfield_Value clv
        ON clv.id = cdlp.labelfield_value_id
        AND clv.profile_id = cdlp.profile_id
        WHERE clv.labelfield_value =  '代销'
        AND cdlp.profile_id = #{profileId}) temp
        ON temp.profileId = p.profile_id AND temp.ptypeId = p.id
        WHERE p.profile_id = #{profileId}
        AND p.id = #{ptypeId}
        <if test="pcategory !=null and pcategory!=2">
            AND ps.id = #{skuId}
            AND pu.id = #{unitId}
        </if>
        AND p.stoped = 0
        AND p.stoped = 0
        AND p.pcategory = #{pcategory}
        AND ps.deleted = 0
        AND ps.stoped = 0
        LIMIT 1
    </select>

    <select id="queryProductIdListByNumId" resultType="java.math.BigInteger">
        select id from pl_eshop_product
        where profile_id=#{profileId}
        and eshop_id=#{eshopId}
        and platform_num_id in
        <foreach collection="numIdList" item="platformNumId" separator="," close=")" open="(">
            #{platformNumId}
        </foreach>
    </select>

    <select id="querySkuIdListByNumId" resultType="java.math.BigInteger">
        select id from pl_eshop_product_sku
        where profile_id=#{profileId}
        and eshop_id=#{eshopId}
        and platform_num_id in
        <foreach collection="numIdList" item="platformNumId" separator="," close=")" open="(">
            #{platformNumId}
        </foreach>
    </select>

    <select id="queryUniqueIdListByNumId" resultType="java.lang.String">
        select unique_id from pl_eshop_product_sku
        where profile_id=#{profileId}
        and eshop_id=#{eshopId}
        and platform_num_id in
        <foreach collection="numIdList" item="platformNumId" separator="," close=")" open="(">
            #{platformNumId}
        </foreach>
    </select>

    <delete id="deleteProductById">
        delete from pl_eshop_product
        where profile_id=#{profileId}
        and id in
        <foreach collection="idList" item="id" separator="," close=")" open="(">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteProductSkuById">
        delete from pl_eshop_product_sku
        where profile_id=#{profileId}
        and id in
        <foreach collection="idList" item="id" separator="," close=")" open="(">
            #{id}
        </foreach>
    </delete>


    <select id="queryEshopProductSelectMain"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSelectorInfo">
        select
        pm.id,pm.profile_id,pm.eshop_id,pm.category_id,pm.platform_pic_url,
        pm.platform_num_id as platform_id,pm.has_properties,
        pm.platform_fullname as platname,pm.platform_xcode,
        "0" as pid,pm.platform_stock_state as stockState,true as mainProduct,'' as unique_id,
        pm.platform_num_id , '' as platform_sku_id ,pm.platform_fullname, '' as platform_properties_name
        from pl_eshop_product pm
        left join pl_eshop_product_class epc on epc.profile_id= pm.profile_id and epc.eshop_id=pm.eshop_id and
        epc.platform_class_id=pm.category_id
        where pm.profile_id=#{profileId} and pm.eshop_id=#{otypeId}
        <if test="quickfilter!=null and quickfilter!=''">
            AND (pm.platform_fullname LIKE CONCAT('%',#{quickfilter},'%')
            OR pm.platform_xcode LIKE CONCAT('%',#{quickfilter},'%')
            OR pm.platform_num_id LIKE CONCAT('%',#{quickfilter},'%')
            OR EXISTS(SELECT 1 FROM `pl_eshop_product_sku` psm
            WHERE psm.profile_id= pm.profile_id and psm.eshop_id=pm.eshop_id and psm.platform_num_id=pm.platform_num_id
            AND (psm.platform_properties_name LIKE CONCAT('%',#{quickfilter},'%')
            OR psm.platform_xcode LIKE CONCAT('%',#{quickfilter},'%')
            OR psm.platform_sku_id LIKE CONCAT('%',#{quickfilter},'%'))
            )
            )
        </if>
        order by pm.platform_fullname
    </select>


    <select id="queryEshopProductSelectSku"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSelectorInfo">
        select
        psm.id,pm.profile_id,pm.eshop_id,pm.category_id,psm. platform_pic_url,
        '' as platform_id, 0 has_properties,
        psm.platform_properties_name as platname,psm.platform_xcode,
        psm.platform_num_id as pid,pm.platform_stock_state as stockState,false as mainProduct,psm.unique_id,
        pm.platform_num_id , psm.platform_sku_id ,pm.platform_fullname, psm.platform_properties_name
        from pl_eshop_product pm
        left join pl_eshop_product_sku psm on psm.profile_id= pm.profile_id and psm.eshop_id=pm.eshop_id and
        psm.platform_num_id=pm.platform_num_id
        where pm.profile_id=#{profileId} and pm.eshop_id=#{otypeId}
        <if test="platformNumIds !=null and platformNumIds.size()>0">
            and pm.platform_num_id in
            <foreach collection="platformNumIds" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="quickfilter!=null and quickfilter!=''">
            AND (psm.platform_properties_name LIKE CONCAT('%',#{quickfilter},'%')
            OR psm.platform_xcode LIKE CONCAT('%',#{quickfilter},'%')
            OR psm.platform_sku_id LIKE CONCAT('%',#{quickfilter},'%'))
        </if>
        order by psm.platform_properties_name
    </select>
    <select id="queryOtypeIdByRowId" resultType="java.math.BigInteger">
        select eshop_id
        from pl_eshop_product_sku sku
        where profile_id = #{profileid}
          and id = #{platformSkuRowId}
        limit 1
    </select>

    <select id="checkUnRelationProduct" resultType="java.lang.Boolean">
        SELECT 1 FROM pl_eshop_product_sku peps
            JOIN(SELECT profile_id,unique_id FROM pl_eshop_product_mark WHERE profile_id=#{profileId}  AND mark_code = 1015 AND mark_code != 1001 GROUP BY profile_id, unique_id) temp
                   ON temp.profile_id = peps.profile_id AND temp.unique_id = peps.unique_id
                 WHERE peps.profile_id=#{profileId} limit 1
    </select>



    <insert id="markDataSave">
        INSERT INTO pl_eshop_product_mark_data (profile_id, mark_id, big_data, id)
        VALUES (#{profileId}, #{markId}, #{bigData}, #{id});
    </insert>


    <delete id="markDataDelete">
        delete
        from pl_eshop_product_mark_data
        where profile_id = #{profileId}
          and mark_id = #{markId}
    </delete>

    <select id="getProductMarkListByUniqueIdAndMarkCode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMark">
        select epm.id,
        epm.platform_sku_id,
        epm.eshop_id,
        epm.profile_id,
        epm.mark_code,
        epm.bubble,
        epm.show_type,
        epm.platform_properties_name,
        epm.unique_id,
        epm.platform_num_id,
        mrkdt.id as markDataId
        from pl_eshop_product_mark epm
        left join pl_eshop_product_mark_data mrkdt
        on mrkdt.profile_id = epm.profile_id and mrkdt.mark_id = epm.id
        where epm.profile_id = #{profileId}
        and epm.eshop_id = #{eshopId}
        <if test="uniqueIdList!=null and uniqueIdList.size()>0">
            and epm.unique_id in
            <foreach collection="uniqueIdList" item="uniqueId" index="i" separator="," open="(" close=")">
                #{uniqueId}
            </foreach>
        </if>

        <if test="markCodeList!=null and markCodeList.size()>0">
            and epm.mark_code in
            <foreach collection="markCodeList" item="markCode" index="i" separator="," open="(" close=")">
                #{markCode}
            </foreach>
        </if>
        ;
    </select>

    <select id="getProductMarkListByNumIds" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMark">
        select epm.id,
        epm.platform_sku_id,
        epm.eshop_id,
        epm.profile_id,
        epm.mark_code,
        epm.bubble,
        epm.show_type,
        epm.platform_properties_name,
        epm.unique_id,
        epm.platform_num_id,
        mrkdt.id as markDataId
        from pl_eshop_product_mark epm
        left join pl_eshop_product_mark_data mrkdt
        on mrkdt.profile_id = epm.profile_id and mrkdt.mark_id = epm.id
        where epm.profile_id = #{profileId}
        and epm.eshop_id = #{eshopId}
        <if test="numIds!=null and numIds.size()>0">
            and epm.platform_num_id in
            <foreach collection="numIds" item="numid" index="i" separator="," open="(" close=")">
                #{numid}
            </foreach>
        </if>
        <if test="markCodeList!=null and markCodeList.size()>0">
            and epm.mark_code in
            <foreach collection="markCodeList" item="markCode" index="i" separator="," open="(" close=")">
                #{markCode}
            </foreach>
        </if>
        and epm.show_type = 1
    </select>

    <select id="queryRelationBySkuIdJoinAbstractLogCount" resultType="java.lang.Integer">
    select count(1) from (
        select distinct sku.id,sku.unique_id from
            left join pl_eshop_product_sku sku
                 left join pl_eshop_stock_sync_abstract_log log on log.profile_id = sku.profile_id and log.platform_num_id
                =sku.platform_num_id and log.platform_sku_id = sku.platform_sku_id
        where sku.profile_id=#{profileid} and sku.eshop_id=#{eshopId}  and (log.update_time <![CDATA[<=]]> NOW() - INTERVAL #{hours} HOUR or log.update_time is null)
          and id>#{nowId}
            )a
    </select>


    <delete id="deleteProductMarkByUniqueIds">
        delete from pl_eshop_product_mark
        where profile_id = #{profileId}
        and eshop_id = #{eshopId}
        <if test="uniqueIdList!= null and uniqueIdList.size()>0">
            and unique_id in
            <foreach collection="uniqueIdList" item="item" index="i" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        and mark_code in (1007,1011,1012,1013,1014,1016)
    </delete>

    <delete id="deleteProductMarkDataByUniqueIds">
        delete mkd,pm from pl_eshop_product_mark_data mkd
        join pl_eshop_product_mark pm on mkd.profile_id = pm.profile_id and mkd.mark_id = pm.id
        where pm.profile_id = #{profileId}
        and pm.eshop_id = #{eshopId}
        <if test="uniqueIdList!= null and uniqueIdList.size()>0">
            and pm.unique_id in
            <foreach collection="uniqueIdList" item="item" index="i" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        and pm.mark_code in (1007,1011,1012,1013,1014,1016)
    </delete>


    <insert id="batchSaveMark">
        <foreach collection="markList" item="mark" index="index" separator=";">
            REPLACE INTO pl_eshop_product_mark (id, platform_sku_id, eshop_id, profile_id,
            mark_code, bubble, show_type, platform_properties_name, unique_id, platform_num_id)
            VALUES
            (#{mark.id}, #{mark.platformSkuId}, #{mark.eshopId}, #{mark.profileId}, #{mark.markCode},
            #{mark.bubble},#{mark.showType},#{mark.platformPropertiesName}, #{mark.uniqueId}, #{mark.platformNumId})
        </foreach>
    </insert>


    <insert id="markDataBatchSave">
        <foreach collection="bigDatas" item="record" index="index" separator=";">
            REPLACE INTO pl_eshop_product_mark_data (profile_id, mark_id, big_data, id)
            VALUES (#{record.profileId}, #{record.markId}, #{record.bigData}, #{record.id})
        </foreach>
    </insert>

    <delete id="markDataBatchDelete">
        delete
        from pl_eshop_product_mark_data
        where profile_id = #{profileId}
        and mark_id in
        <foreach collection="markIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>


    <select id="queryEshopProductSkuRelationsList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductRelationEntity">
        select * from (
        select
        <include refid="skuRelationQueryFields"/>
        from pl_eshop_product_sku sku
        join pl_eshop_product prd on sku.profile_id = prd.profile_id and sku.eshop_id = prd.eshop_id
            and sku.platform_num_id = prd.platform_num_id
        join pl_eshop_product_sku_expand expand on sku.unique_id = expand.unique_id
            and sku.profile_id=expand.profile_id and expand.mapping_type=0
        left join pl_eshop_product_sku_mapping skumap on sku.profile_id = skumap.profile_id
            and sku.eshop_id = skumap.eshop_id and sku.unique_id = skumap.unique_id
        left join base_ptype_unit unit on unit.profile_id=sku.profile_id and unit.id=skumap.unit_id
        left join base_ptype_xcode bpx on sku.profile_id = bpx.profile_id and skumap.unit_id=bpx.unit_id
            and skumap.sku_id=bpx.sku_id and bpx.ptype_id=skumap.ptype_id and bpx.defaulted=1
        left join base_ptype p on p.profile_id=sku.profile_id and p.id=skumap.ptype_id and p.deleted = 0 and p.stoped=0
        left join base_ptype_sku ps on ps.id=skumap.sku_id and ps.profile_id=sku.profile_id
        left join base_ptype_fullbarcode bar on bar.profile_id = sku.profile_id and bar.unit_id=skumap.unit_id and bar.defaulted = 1
        and bar.ptype_id=skumap.ptype_id and bar.sku_id = skumap.sku_id
        where sku.profile_id = #{profileId}
        <include refid="skuRelationQueryCondition"/>

        union

        select
        <include refid="skuRelationQueryFields"/>
        from pl_eshop_product_sku sku
        join pl_eshop_product prd on sku.profile_id = prd.profile_id
            and sku.eshop_id = prd.eshop_id and sku.platform_num_id = prd.platform_num_id
        join pl_eshop_product_sku_expand expand on sku.unique_id = expand.unique_id and sku.profile_id=expand.profile_id and expand.mapping_type=1
        left join base_ptype_xcode bpx on sku.profile_id = bpx.profile_id and bpx.xcode=sku.platform_xcode and sku.platform_xcode!=''
        left join base_ptype_unit unit on unit.profile_id=sku.profile_id and unit.id=bpx.unit_id
        left join base_ptype p on p.profile_id=sku.profile_id and p.id=bpx.ptype_id and p.deleted = 0 and p.stoped=0
        left join base_ptype_sku ps on ps.id=bpx.sku_id and ps.profile_id=sku.profile_id
        left join base_ptype_fullbarcode bar on bar.profile_id = sku.profile_id and bar.unit_id=bpx.unit_id and bar.defaulted = 1
         and bar.ptype_id=bpx.ptype_id and bar.sku_id=bpx.sku_id
        where sku.profile_id = #{profileId}
        <include refid="skuRelationQueryCondition"/>
        ) relations
        order by relations.platName
    </select>


    <select id="queryPtypeSelectorSelectedPtypeRelationList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductRelationEntity">
        select * from (
        select
        <include refid="skuRelationQueryFields"/>
        from pl_eshop_product_sku sku
        join pl_eshop_product prd on sku.profile_id = prd.profile_id and sku.eshop_id = prd.eshop_id
        and sku.platform_num_id = prd.platform_num_id
        join pl_eshop_product_sku_expand expand on sku.unique_id = expand.unique_id
        and sku.profile_id=expand.profile_id and expand.mapping_type=0
        left join pl_eshop_product_sku_mapping skumap on sku.profile_id = skumap.profile_id
        and sku.eshop_id = skumap.eshop_id and sku.unique_id = skumap.unique_id
        left join base_ptype_unit unit on unit.profile_id=sku.profile_id and unit.id=skumap.unit_id
        left join base_ptype_xcode bpx on sku.profile_id = bpx.profile_id and skumap.unit_id=bpx.unit_id
        and skumap.sku_id=bpx.sku_id and bpx.ptype_id=skumap.ptype_id and bpx.defaulted=1
        left join base_ptype p on p.profile_id=sku.profile_id and p.id=skumap.ptype_id and p.deleted = 0 and p.stoped=0
        left join base_ptype_sku ps on ps.id=skumap.sku_id and ps.profile_id=sku.profile_id
        left join base_ptype_fullbarcode bar on bar.profile_id = sku.profile_id and bar.unit_id=skumap.unit_id and bar.defaulted = 1
        and bar.ptype_id=skumap.ptype_id and bar.sku_id = skumap.sku_id
        where sku.profile_id = #{profileId}
        <include refid="skuRelationQueryCondition"/>
        <include refid="skuRelationQueryAdditionalCondition"/>
        union

        select
        <include refid="skuRelationQueryFields"/>
        from pl_eshop_product_sku sku
        join pl_eshop_product prd on sku.profile_id = prd.profile_id
        and sku.eshop_id = prd.eshop_id and sku.platform_num_id = prd.platform_num_id
        join pl_eshop_product_sku_expand expand on sku.unique_id = expand.unique_id and sku.profile_id=expand.profile_id and expand.mapping_type=1
        left join base_ptype_xcode bpx on sku.profile_id = bpx.profile_id and bpx.xcode=sku.platform_xcode and sku.platform_xcode!=''
        left join base_ptype_unit unit on unit.profile_id=sku.profile_id and unit.id=bpx.unit_id
        left join base_ptype p on p.profile_id=sku.profile_id and p.id=bpx.ptype_id and p.deleted = 0 and p.stoped=0
        left join base_ptype_sku ps on ps.id=bpx.sku_id and ps.profile_id=sku.profile_id
        left join base_ptype_fullbarcode bar on bar.profile_id = sku.profile_id and bar.unit_id=bpx.unit_id and bar.defaulted = 1
        and bar.ptype_id=bpx.ptype_id and bar.sku_id=bpx.sku_id
        where sku.profile_id = #{profileId}
        <include refid="skuRelationQueryCondition"/>
        <include refid="skuRelationQueryAdditionalCondition"/>
        ) relations
        order by relations.platName
    </select>

    <select id="loadEshopSkuAndComboMappingList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select
        <include refid="skuAndComboRelation"/>
        from pl_eshop_product_sku psm
        join pl_eshop_product pm on pm.profile_id=psm.profile_id and pm.eshop_id=psm.eshop_id and pm.platform_num_id=psm.platform_num_id
        join pl_eshop_product_sku_expand expand
        on psm.unique_id = expand.unique_id and psm.profile_id=expand.profile_id and expand.mapping_type=0
        left join pl_eshop_product_sku_mapping mapping
        on mapping.profile_id= psm.profile_id and mapping.unique_id = psm.unique_id and mapping.eshop_id = psm.eshop_id
        left join base_ptype_unit unit on unit.profile_id=psm.profile_id and unit.id=mapping.unit_id
        left join base_ptype_xcode bpx on psm.profile_id = bpx.profile_id and mapping.unit_id=bpx.unit_id
        and mapping.sku_id=bpx.sku_id and bpx.ptype_id=mapping.ptype_id and bpx.defaulted=1
        left join base_ptype p on p.profile_id=psm.profile_id and p.id=mapping.ptype_id and p.deleted = 0 and p.stoped =0
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId}
        <include refid="productSkuMappingWhereCondition"/>

        UNION

        select
        <include refid="skuAndComboRelationOpenXcode"/>
        from pl_eshop_product_sku psm
        join pl_eshop_product pm on pm.profile_id=psm.profile_id and pm.eshop_id=psm.eshop_id and pm.platform_num_id=psm.platform_num_id
        join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=expand.profile_id and expand.mapping_type=1
        left join base_ptype_xcode bpx on bpx.xcode=psm.platform_xcode and bpx.profile_id=psm.profile_id and psm.platform_xcode!=''
        left join base_ptype_unit unit on unit.profile_id=bpx.profile_id and unit.id=bpx.unit_id and unit.unit_type=0
        left join base_ptype p on psm.profile_id = p.profile_id and bpx.ptype_id=p.id and p.deleted = 0 and p.stoped = 0
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId}
        <include refid="productSkuMappingWhereCondition"/>
    </select>

    <select id="getSkuMappingByUniqueList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select psm.unique_id,psm.platform_xcode
        from pl_eshop_product_sku psm
        join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=expand.profile_id
        and expand.mapping_type=1
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId}
        and psm.unique_id in
        <foreach collection="uniqueIdList" close=")" open="(" separator="," item="uniqueId">
            #{uniqueId}
        </foreach>
        order by psm.unique_id desc
    </select>

    <select id="getSkuMappingByUniquesAndMappingType"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select psm.unique_id,psm.platform_xcode
        from pl_eshop_product_sku psm
        join pl_eshop_product_sku_expand expand on psm.unique_id = expand.unique_id and psm.profile_id=expand.profile_id
        where psm.profile_id=#{profileId} and psm.eshop_id=#{otypeId}
        and expand.mapping_type=#{mappingType}
        and psm.unique_id in
        <foreach collection="uniqueIdList" close=")" open="(" separator="," item="uniqueId">
            #{uniqueId}
        </foreach>
        order by psm.unique_id desc
    </select>


    <select id="querySkuRelationsManualAndXcode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductRelationEntity">
        select * from(
        (
        select <include refid="ptypeRelationSkuQueryManualSelectColumns"/>
        from <include refid="ptypeRelationSkuQueryManualFromClause"/>
        <include refid="ptypeRelationSkuQueryByManualAndXcodeWhereClause"/>
        and expand.mapping_type=0
        )
        union
        (
        select <include refid="ptypeRelationSkuQueryXcodeSelectColumns"/>
        from <include refid="ptypeRelationSkuQueryXcodeFromClause"/>
        <include refid="ptypeRelationSkuQueryByManualAndXcodeWhereClause"/>
        and expand.mapping_type=1
        )
        ) r
        <where>
            <if test="request.mark>0">
                and r.mark=#{request.mark}
            </if>
            <if test="request.filterString!='' and request.queryType==1">
                and (r.platName like "%"#{request.filterString}"%" or r.pmXcode like "%"#{request.filterString}"%" or
                r.platform_properties_name like "%"#{request.filterString}"%" or r.platxcode like
                "%"#{request.filterString}"%")
            </if>
            <if test="request.filterString!='' and request.queryType==2">
                and (r.ptypeName like "%"#{request.filterString}"%" or r.xcode like "%"#{request.filterString}"%" or
                r.usercode like
                "%"#{request.filterString}"%")
            </if>
            <if test="request.filterString !='' and request.queryType!=null and request.queryType==3">
                and r.platName like CONCAT('%',#{request.filterString},'%')
            </if>
            <if test="request.filterString !='' and request.queryType!=null and request.queryType==4">
                and r.platform_properties_name like CONCAT('%',#{request.filterString},'%')
            </if>
            <if test="request.filterString !='' and request.queryType!=null and request.queryType==5">
                and r.pmXcode like CONCAT('%',#{request.filterString},'%')
            </if>
            <if test="request.filterString !='' and request.queryType!=null and request.queryType==6">
                and r.platxcode like CONCAT('%',#{request.filterString},'%')
            </if>
            <if test="request.filterString !='' and request.queryType!=null and request.queryType==7">
                and r.ptypeName like CONCAT('%',#{request.filterString},'%')
            </if>
            <if test="request.filterString !='' and request.queryType!=null and request.queryType==8">
                and r.usercode like CONCAT('%',#{request.filterString},'%')
            </if>
            <if test="request.filterString !='' and request.queryType!=null and request.queryType==9">
                and r.xcode like CONCAT('%',#{request.filterString},'%')
            </if>

            <if test="request.stockState>0">
                and r.stock_state=#{request.stockState}
            </if>
            <if test="request.xcodeState!=2">
                and r.update_xcode_status=#{request.xcodeState}
            </if>
            <if test="request.platformXcodeIsNull==1">
                and r.platform_xcode=''
            </if>
            <if test="request.platformXcodeIsNull==2">
                and r.platform_xcode !=''
            </if>
            <if test="request.platformXcodeIsNull==4">
                and (ifnull(r.xcode,'')!=r.platxcode and r.isbind=true)
            </if>
            <if test="request.qtyState==1">
                and r.qty>0
            </if>
            <if test="request.qtyState==2">
                and r.qty &lt; 1
            </if>
            <if test="request.maxPrice != null">
                and r.price &lt;= #{request.maxPrice}
            </if>
            <if test="request.minPrice != null">
                and r.price &gt;= #{request.minPrice}
            </if>
            <if test="request.platformNumId != null and request.platformNumId !=''">
                and r.platform_num_id= #{request.platformNumId}
            </if>
            <if test="request.platformSkuId != null and request.platformNumId !=''">
                and r.platform_sku_id= #{request.platformSkuId}
            </if>
            <if test="request.relationState==1">
                and r.isbind=false
            </if>
            <if test="request.relationState==2">
                and r.isbind=true
            </if>
            <if test="request.startDownloadOrderIntervalDay != null and request.endDownloadOrderIntervalDay!=null">
                and r.downloadOrderIntervalDay &gt;= #{request.startDownloadOrderIntervalDay} and r.downloadOrderIntervalDay
                &lt;= #{request.endDownloadOrderIntervalDay}
            </if>
            <if test="request.startRefreshProductIntervalDay != null and request.endRefreshProductIntervalDay!=null">
                and r.refreshProductIntervalDay &gt;=#{request.startRefreshProductIntervalDay} and
                r.refreshProductIntervalDay &lt;= #{request.endRefreshProductIntervalDay}
            </if>
            <if test="request.downloadOrderIntervalDay != null ">
                and r.downloadOrderIntervalDay = #{request.downloadOrderIntervalDay}
            </if>
            <if test="request.refreshProductIntervalDay != null ">
                and r.refreshProductIntervalDay = #{request.refreshProductIntervalDay}
            </if>
            <if test="request.platformXcodeIsNull==3">
                and r.platform_xcode in (select platform_xcode from pl_eshop_product_sku where profile_id=#{request.profileId}
                and eshop_id=#{request.otypeId} and storage_type=0 and platform_xcode is not null and platform_xcode!=''
                group by platform_xcode having count(1)>1)
            </if>
        </where>
        order by r.platform_properties_name desc
        limit #{pageNum}, #{pageSize}
    </select>
    <select id="querySkuRelationsManualAndXcodeCount"
            resultType="java.lang.Integer">
        select count(0) from(
        (
        select <include refid="ptypeRelationSkuQueryManualSelectColumns"/>
        from <include refid="ptypeRelationSkuQueryManualFromClause"/>
        <include refid="ptypeRelationSkuQueryByManualAndXcodeCountWhereClause"/>
        and expand.mapping_type=0
        )
        union
        (
        select <include refid="ptypeRelationSkuQueryXcodeSelectColumns"/>
        from <include refid="ptypeRelationSkuQueryXcodeFromClause"/>
        <include refid="ptypeRelationSkuQueryByManualAndXcodeCountWhereClause"/>
        and expand.mapping_type=1
        )
        ) r
        <where>
            <if test="mark>0">
                and r.mark=#{mark}
            </if>
            <if test="filterString!=''  and queryType==1">
                and (r.platName like "%"#{filterString}"%" or r.pmXcode like "%"#{filterString}"%" or
                r.platform_properties_name like "%"#{filterString}"%" or r.platxcode like "%"#{filterString}"%")
            </if>
            <if test="filterString!='' and queryType==2">
                and (r.ptypeName like "%"#{filterString}"%" or r.xcode like "%"#{filterString}"%" or r.usercode like
                "%"#{filterString}"%")
            </if>
            <if test="filterString !='' and queryType!=null and queryType==3">
                and r.platName like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==4">
                and r.platform_properties_name like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==5">
                and r.pmXcode like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==6">
                and r.platxcode like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==7">
                and r.ptypeName like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==8">
                and r.usercode like CONCAT('%',#{filterString},'%')
            </if>
            <if test="filterString !='' and queryType!=null and queryType==9">
                and r.xcode like CONCAT('%',#{filterString},'%')
            </if>
            <if test="stockState>0">
                and r.stock_state=#{stockState}
            </if>
            <if test="xcodeState!=2">
                and r.update_xcode_status=#{xcodeState}
            </if>
            <if test="platformXcodeIsNull!=0 and platformXcodeIsNull==1">
                and r.platform_xcode=''
            </if>
            <if test="platformXcodeIsNull!=0 and platformXcodeIsNull==2">
                and r.platform_xcode !=''
            </if>
            <if test="platformXcodeIsNull!=0 and platformXcodeIsNull==4">
                and (ifnull(r.xcode,'')!=r.platxcode and r.isbind=true)
            </if>
            <if test="maxPrice != null">
                and r.price &lt;= #{maxPrice}
            </if>
            <if test="minPrice != null">
                and r.price &gt;= #{minPrice}
            </if>
            <if test="platformNumId != null and platformNumId !=''">
                and r.platform_num_id= #{platformNumId}
            </if>
            <if test="platformSkuId != null and platformNumId !=''">
                and r.platform_sku_id= #{platformSkuId}
            </if>
            <if test="qtyState==1">
                and r.qty>0
            </if>
            <if test="qtyState==2">
                and r.qty &lt; 1
            </if>
            <if test="relationState==1">
                and r.isbind=false
            </if>
            <if test="relationState==2">
                and r.isbind=true
            </if>
            <if test="startDownloadOrderIntervalDay != null and endDownloadOrderIntervalDay!=null">
                and r.downloadOrderIntervalDay &gt;= #{startDownloadOrderIntervalDay} and r.downloadOrderIntervalDay
                &lt;= #{endDownloadOrderIntervalDay}
            </if>
            <if test="startRefreshProductIntervalDay != null and endRefreshProductIntervalDay!=null">
                and r.refreshProductIntervalDay &gt;=#{startRefreshProductIntervalDay} and r.refreshProductIntervalDay
                &lt;= #{endRefreshProductIntervalDay}
            </if>
            <if test="downloadOrderIntervalDay != null ">
                and r.downloadOrderIntervalDay = #{downloadOrderIntervalDay}
            </if>
            <if test="refreshProductIntervalDay != null ">
                and r.refreshProductIntervalDay = #{refreshProductIntervalDay}
            </if>
            <if test="platformXcodeIsNull!=0 and platformXcodeIsNull==3">
                and r.platform_xcode in (select platform_xcode from pl_eshop_product_sku where profile_id=#{profileId}
                and eshop_id=#{otypeId} and storage_type=0 and platform_xcode is not null and platform_xcode!=''
                group by platform_xcode having count(1)>1)
            </if>
        </where>
        order by r.platform_properties_name
    </select>

</mapper>
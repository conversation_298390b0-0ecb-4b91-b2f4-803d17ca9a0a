<?xml version="1.0" encoding="UTF-8" ?>
<Page xmlns="Craba.UI" Title="${title}"
      ActionType="sale.product.listAction, sale/eshoporder/product/list.js" CssClass="pd0">
    <FlexColumn CssClass="FlexAuto dflex" CssStyle="padding-bottom:10px">
        <FlowPanel CssClass="plr10" CssStyle="overflow:visible;border-bottom: 1px solid #eee;">
            <DropDownCheckBoxList ID="eshop" Label="网店" ReportVisible="true"
                                  SelectOnly="true" Required="true"
                                  NullDisplayText="全部"
                                  DropDownStyle="DropDownMultiSearch" CssClass='FlexAuto'
                                  OnChange="doEshopSelectorSelected"
                                  DataSource="${eshopList}"
                                  DataField="otypeId"
                                  DataTextField="fullname"
                                  DataValueField="otypeId"
                                  Width="280"/>

            <HBlock CssClass='CombEditor'>
                <DropDownEdit ID="edQueryType" Value="0" DropDownStyle="DropDownList" Width="88"
                              ListItems="0=网店商品,1=系统商品" OnChange="doQueryTypeChange" TabStop="false"/>
                <TextEdit ID="edQueryText" Width="240" NullDisplayText="名称/编号/商家编码/属性/SKU ID"
                          OnEnterPress="doSkuQuery"/>
            </HBlock>
            <DropDownEdit ID="dropRelationState" DropDownStyle="DropDownList" Width="80" Label="对应状态"
                          SelectedIndex="0"
                          ListItems="0=全部,1=已对应,2=未对应"/>
            <DropDownEdit ID="dropStockState" DropDownStyle="DropDownList" Width="80" Label="上架状态" SelectedIndex="1"
                          ListItems="0=全部,1=出售中,2=仓库中"/>
            <DropDownEdit ID="dropXcode" DropDownStyle="DropDownList" Width="120" Label="商家编码" SelectedIndex="0"
                          ListItems="0=全部,1=为空,2=不为空,3=重复,4=与系统商家编码不一致"/>
            <Button Text="查询" OnClick="doSkuQuery" CssClass='BorderButton'/>
        </FlowPanel>
        <FlowPanel CssClass="FlowTable plr10" AllowConfig="true"
                   ConfigID="${configId}"
                   ConfigMode='DragPanel'
                   NotConfigIds='flex1,lbUnRelationCount,right'
                   StaticIds="flex1,lbUnRelationCount,right"
                   PopupIds="btnAutoStockSync,btnCreatePtype,btnCreatePropPtype,btnSkuMark,btnSkuStockRule,btnSyncRuleConfig,btnCreateAndUploadXcode,btnDeleteSku,btnExcelRelation,btnCreateCombo,btnSyncToSonEshop,btnDownloadMainPic">
            <Button ID="btnSkuRefresh" Text="刷新网店商品" CssClass="SpecialButton" OnClick="doSkuRefresh"
                    Visible="${refreshEnabled}"/>
            <Button ID="btnBatchBind" Text="绑定/解绑" PopupMenu="batchPopupBind"
                    Visible="${relationEnabled}"/>
            <Button ID="btnSkuStockSync" Text="同步库存"
                    OnClick='doSkuStockSync'
                    PopupMenu="skuStockSyncPopup"
                    Visible="${stockSyncEnabled}"/>
            <Button ID="btnCreatePtype" Text="生成系统商品" OnClick='doCreatePtype'
                    Visible="${!propsEnabled && createLocalEnabled}"/>
            <Button ID="btnCreatePropPtype" Text="生成系统商品" PopupMenu="createPtypePopup"
                    Visible="${createLocalEnabled && propsEnabled}"/>
            <Button ID="btnSkuMark" Text="标记" OnClick="doBatchMark"
                    Visible="${markEditEnable}"/>
            <Button ID="btnCreateAndUploadXcode" Text="生成网店商家编码"
                    OnClick="doCreateAndUploadXcode"
                    Visible="${xcodeEnable}"/>
            <Button ID="btnDeleteSku" Text="删除SKU"
                    Visible="${deleteEnabled}"
                    OnClick="doDeleteSkus"/>
            <!-- <Button ID="btnExcelRelation" Text="EXCEL快速对应"
                     Visible="${excelRelationEnabled}"  />-->
            <Button ID="btnCreateCombo" Text="智能创建套餐"
                    Visible="${showSmartCreateCombo}"
                    OnClick="autoCreateCombo"
                    RightIcon="aicon-question-circle"/>
            <Button ID="btnSyncToSonEshop" Text="同步商品数据到子店" Visible="false" OnClick="syncRelationToSubEshop"/>
            <Button ID="btnDownloadMainPic" Text="下载网店商品主图做商品主图"
                    Visible="${mainPicEnabled}"
                    OnClick="downloadMainImage"/>
            <Block CssClass='Flex1' ID="flex1" NoItem="true"/>
            <Label ID="lbUnRelationCount" LabelCssClass='aicon-tishi1 iconColor_pro' CssClass='link LinkItem'
                   Label="未对应的SKU数:" Text="0" OnClick="doShowUnRelation"/>
            <HBlock ID="right">
                <Button ID="skuFilter" Text="筛选" Flat="true" CssClass="aicon-filter" OnClick="doShowFilter"/>
                <Button ID="btnExport" Text="导出" Flat="true" Icon="aicon-daochu" CssStyle="border:none;padding:0px;"
                        OnClick="doBtnExportClick"/>
            </HBlock>
        </FlowPanel>
        <Grid ID="grid"
              PopupMenu="skuPopMenu"
              DefaultRowCount="0"
              NeedRowIndex="true"
              OnCellRendering="doCellRendering"
              OnCellBeginEdit="doBeginChange"
              OnSelectionChanged="doSelectionChanged"
              BindPagerDataSource="doBindPagerDataSource"
              OnRowMouseHover="doMouseHover"
              AutoPagerPageSize="false"
              LazyPageItem='true'
              Pager="Bottom"
              ReadOnly="false"
              ModifyOnly="true"
              ConfigID="${configId}"
              PrimaryKey="uniqueId"
              CssClass="ImgDetailGrid plr10">
            <MultiSelectColumn ShowHeaderCheckBox="true" ReadOnly="false" Width="30" DataField="selected"
                               AllowConfig="false" AllowFilter="false" AllowFrozen="false" ReportVisible="false"/>
            <Column Caption="网店商品" HeaderAlign="Center">
                <TextColumn DataField="eshopName" Caption="网店" Width="120" ReadOnly="true"/>
                <TextColumn DataField="eshopTypeName" Caption="网店类型" Width="100" ReadOnly="true" Visible="false"
                            AllowFilter="false"/>
                <!--                <DropDownColumn Caption="网店类型" DropDownStyle="DropDownSearch" DataField="eshopType"
                                                DataValueField="id" ListItems="${shopTypeSourceItems}"
                                                ReadOnly="true"
                                                Width="100" AllowSort="false"/>-->
                <ImageColumn Preview='all' Caption="图" DataField="platformPicUrl" UseResource="false" Width="30"
                             ImageHeight="24" ImageWidth="24" AllowSort="false" ReadOnly="true" AllowFilter="false"
                             ReportVisible="false"/>
                <DynamicButtonColumn Caption="标记" DataField="mark" Visible="false" ReadOnly="true" LayoutDirection='Vert'/>
                <TextColumn Caption="商品编号" DataField="platformNumId" Visible="false" ReadOnly="true"/>
                <TextColumn DataField="platformFullname" Caption="商品名称" Width="200" ReadOnly="true"
                            Hint="双击可以直接查询网店商品信息" OnDblClick="doOpenUrl"/>
                <TextColumn DataField="platformPropertiesName" Caption="属性" Width="160" ReadOnly="true"
                            Hint="双击可以直接查询网店商品信息" OnDblClick="doOpenUrl"/>
                <TextColumn Icon='${editIcon}' DataField="platformXcode" Caption="商家编码"
                            Width="160"
                            OnChange="doPlatformXcodeChange" ReadOnly="${!xcodeEnable}"/>
                <TextColumn Caption="SKU ID" DataField="platformSkuId" Visible="false" ReadOnly="true"/>
                <TextColumn Caption="单位名称" DataField="platformUnitName" Visible="false" ReadOnly="true"/>
                <DropDownColumn DataField="platformStockState" Caption="上架状态" Visible="false"
                                ListItems="1=出售中,2=仓库中" ReadOnly="true"/>
                <NumberColumn DataField="platformPrice" Caption="售价" Visible="false"
                              ReadOnly="true" DecimalScale="2"/>
                <NumberColumn Caption="上次刷新到商品" DataField="refreshProductIntervalDay" Visible="false"
                              OnGetDisplayText='changeDisplayTextProduct' AllowResize="true"
                              AllowFilter="false"
                              ReadOnly="true" AllowConfig="true"/>
                <NumberColumn Caption="最近一次有订单" DataField="downloadOrderIntervalDay" Visible="false"
                              OnGetDisplayText='changeDisplayTextOrder' AllowResize="true"
                              AllowFilter="false"
                              ReadOnly="true" AllowConfig="true"/>
                <NumberColumn Icon='aicon-bianji4' DataField="qty" Width="110" Caption="库存同步数量"
                              AllowConfig="true" Visible="true"
                              AllowFilter="false"
                              OnChange="doSyncQtyChange"
                              NullDisplayText=""/>
                <SelectorColumn ID="ruleName"
                                DataField="ruleName"
                                Caption="库存同步规则"
                                AllowFilter="false"
                                SelectOnly="true"
                                Icon='aicon-bianji4'
                                Width="160"
                                OnButtonClick="doOpenSkuRuleSelector"
                                DisplayField="ruleName"
                                OnChange="doRuleNameChange"
                                ReportVisible="true"
                                HeaderHint="默认使用网店默认规则，可设置单独的网店商品库存同步规则。如设置，则优先执行网店商品库存同步规则。"/>
                <DropDownColumn Caption="库存自动同步"
                                ReportVisible="true"
                                Width="110"
                                DataField="autoSyncEnabled" ReadOnly="true"
                                ListItems='true=● 开启,false=● 关闭'/>
            </Column>
            <DynamicButtonColumn Caption="对应" Icon="bicon-bangzhu" HeaderHint='默认按商家编码自动绑定系统商品。'
                                 HeaderCssClass='IconRight' ReportVisible="false" DataField="operation" Width="120"
                                 OnClick="doOperation" AllowConfig="true"/>
            <Column Caption="系统商品" HeaderAlign="Center">
                <ImageColumn Preview='all' Caption="图" DataField="localPicUrl" UseResource="false" Width="30"
                             ImageHeight="24" ImageWidth="24" AllowSort="false" ReadOnly="true"
                             Visible="false"
                             ReportVisible="false" AllowFilter="false"/>
                <SelectorColumn DataField="ptypeName" Caption="商品名称" Width="200" Icon='${editIcon}'
                                OnChange="doPTypeNameChange"
                                OnButtonClick="doPtypeColumnButtonClick"
                                OnSelectorSelected="doPtypeNameSelected"
                                ButtonVisible="true"
                                AllowResize="true"
                                AllowStretch="true"
                                ReadOnly="false"
                                Business="{'Name':'jxc.ptypeColumn', 'selectType':'Sku', 'vchtype': 'Sale', 'existedSku':'null'}"/>
                <TextColumn DataField="propValueNames" Caption="属性" Visible="${propsEnabled}" Width="140"
                            ReadOnly="true" AllowConfig="${propsEnabled}"/>
                <TextColumn DataField="xcode" Caption="商家编码" Width="140" ReadOnly="true"/>
                <TextColumn DataField="unitName" Caption="单位" Width="80" ReadOnly="true" AllowFilter="false"/>
                <TextColumn Caption="参考成本价" DataField="costPrice" Visible="false" ReadOnly="true"
                            AllowFilter="false"/>
                <TextColumn Caption="规格" DataField="standard" Visible="false" ReadOnly="true"/>
                <TextColumn Caption="型号" DataField="ptypeType" Visible="false" ReadOnly="true"/>
                <TextColumn Caption="品牌" DataField="brandName" Visible="false" ReadOnly="true"/>
            </Column>
        </Grid>
        <HSplitter Mode="HideDown" ForDown="downFlex" ForUp="grid" CssClass="mlr10" MinHeight="200"/>
        <PageControl ID='downFlex' CssClass="NoBorder plr10" Height='200' OnSelected="doLogTabChange">
            <TabPage Caption='操作日志' CssClass='pd0' Visible="true">
                <Grid ID="logGrid"
                      ReadOnly="false"
                      SaveConfig="false"
                      AllowConfig="false"
                      CssClass="mr0 pd0"
                      NeedRowIndex="true"
                      BindPagerDataSource="bindLogGridData"
                      AllowFilter="true"
                      PagerPageSize="50"
                      Pager="Bottom">
                    <DateTimeColumn Caption="操作时间" ReadOnly="true" DataField="opreateTime" Width="200"/>
                    <TextColumn Caption="操作员" ReadOnly="true" DataField="etypeName" Width="200"/>
                    <DropDownColumn Caption="操作类型" DropDownStyle="DropDownSearch" DataField="opreateType"
                                    DataValueField="id" DataSource="${productOperateLogTypes}"
                                    ReadOnly="true"
                                    DataTextField="typeName"
                                    Width="120" AllowSort="false"/>
                    <TextColumn Caption="日志内容" ReadOnly="true" DataField="description" AllowFilter="false"
                                AllowStretch="true" Width="400"/>
                </Grid>
            </TabPage>
            <TabPage ID="tabSync" Caption='库存同步日志' CssClass='pd0' Visible="true">
                <Grid ID="stockLogGrid" CssClass="GridPType" ReadOnly="true"
                      PrimaryKey="id"
                      BindPagerDataSource="doBindStockLogDataSource"
                      Pager="Bottom"
                      SaveConfig="false"
                      AllowConfig="false"
                      AllowFilter="true"
                      PagerPageSize="50"
                      AllowSort="false">
                    <DateTimeColumn Caption="同步时间" Formater="yyyy-MM-dd HH:mm:ss.fff" DataField="syncTime"
                                    Width="200"/>
                    <TextColumn Caption="操作员" DataField="etypeName" Width="200"/>
                    <TextColumn Caption="同步数量" DataField="syncQty" Width="100" AllowFilter="false"/>
                    <TextColumn Caption="线上仓库" DataField="warehouseCode" Width="120"/>
                    <TextColumn Caption="系统仓库" DataField="ktypeIdList" Width="200" AllowFilter="false"/>
                    <TextColumn Caption="同步规则" DataField="syncConfig" Width="220" Visible="false"
                                AllowFilter="false"/>
                    <DropDownColumn Caption="同步操作" DataField="syncType" Width="120"
                                    ListItems="0=自动同步,1=手工同步,3=网店商品上架"/>
                    <DropDownColumn Caption="同步状态" DataField="syncState" Width="120"
                                    ListItems="0=失败,1=成功"/>
                    <TextColumn Caption="错误信息" DataField="errorMsg" AllowStretch="true" AllowFilter="false"/>
                </Grid>
            </TabPage>
        </PageControl>
    </FlexColumn>
    <!--右键菜单-->
    <PopupMenu ID="skuPopMenu" OnPopup="doInitPopup">>
        <MenuItem ID="popRefreshSku" Text="刷新网店商品" OnClick="doSingleSkuRefresh" Visible="${refreshEnabled}"/>
        <MenuItem ID="popBind" Text="手工绑定" OnClick="doBindLocal" Visible="${relationEnabled}"/>
        <MenuItem ID="popClear" Text="解除手工绑定" OnClick="doClearRelation" Visible="${relationEnabled}"/>
        <MenuItem ID="popMark" Text="标记" OnClick="doSingleMark" Visible="${markEditEnable}"/>
        <MenuItem ID="popOpenSync" Text="开启自动库存同步" OnClick="doOpenSyncStock" Visible="${stockSyncEnabled}"/>
        <MenuItem ID="popCloseSync" Text="关闭自动库存同步" OnClick="doCloseSyncStock" Visible="${stockSyncEnabled}"/>
        <MenuItem CssClass="solidBorderBottom"/>
        <MenuItem ID="popSkuUp" Text="向上全选" OnClick="doSelectUp"/>
        <MenuItem ID="popSkuDown" Text="向下全选" OnClick="doSelectDown"/>
    </PopupMenu>


    <PopupMenu ID="batchPopupBind">
        <MenuItem ID="batchAddPopup" Text="手工绑定系统商品" OnClick="doBatchBindLocal"/>
        <MenuItem ID="btnBatchClearRelation" Text="解除手工绑定" OnClick="doBatchClearRelation"/>
    </PopupMenu>

    <PopupMenu ID="skuStockSyncPopup">
        <MenuItem Text="开启自动同步" OnClick="batchOpenSyncState"/>
        <MenuItem Text="关闭自动同步" OnClick="batchCloseSyncState"/>
        <MenuItem CssClass="solidBorderBottom"/>
        <MenuItem Text="使用自定义规则" OnClick="batchBindCustomRule"/>
        <MenuItem Text="使用默认规则" OnClick="batchBindDefaultStockSyncRule"/>
        <MenuItem CssClass="solidBorderBottom"/>
        <MenuItem Text="自定义规则配置" OnClick="doSyncRuleConfig"/>
        <MenuItem Text="默认规则配置" OnClick="modifyDefaultRule"/>
    </PopupMenu>

    <PopupMenu ID="createPtypePopup">
        <MenuItem Text="生成系统商品" OnClick="doCreatePtype"/>
        <MenuItem Text="生成系统属性商品" OnClick="doCreatePropPtype"/>
    </PopupMenu>

    <PopupBlock ID="ruleFlexPop" CssClass='pd0'>
        <CustomControl ID="ruleFlex"
                       Src="~/sale/eshoporder/product/StockRuleList.gspx"/>
    </PopupBlock>
    <PopupMenu ID='skuExportPopup'>
        <MenuItem Text='导出数据' OnClick='doExport'/>
        <MenuItem Text='导出记录' OnClick="doExportList"/>
    </PopupMenu>

    <PopupBlock ID='downloadConfigView' Width='550' Height="230" CssClass="htmlTip"
                CssStyle="margin:0px;padding:0px;color:#333333;">
        <FlexColumn CssClass="htmlTip" Height="220" CssStyle="padding:0px;margin-left:20px">
            <Label Text="智能创建套餐规则：" Height="20" CssStyle="margin-top:16px;height:19px;"/>
            <Label Text="   1.sku商家编码格式必须为：编码*数量+编码*数量；" Height="20" CssStyle="margin-top:10px;"/>
            <Label Text="   2.编码必须等于本地商品sku编号" CssStyle="height:19px;"/>
            <Label Text="比如：线上网店商品信息：网店商品名称：手机；sku商家编码：sp001*2+sp002*1；"
                   CssStyle="height:19px;margin-top:24px;"/>
            <Label Text="   本地商品：商品a的sku编号=sp001；商品b的sku编号=sp002；"/>
            <Label Text="   系统解析出来后自动创建套餐信息：套餐名称：手机。 套餐明细：商品a   数量2 ；商品b    数量1"
                   CssStyle="height:19px;"/>
        </FlexColumn>
    </PopupBlock>

    <PopupBlock ID="ruleView">
        <FlowPanel LayoutDirection='Vert' ItemLabelWidth="85" ItemWidth="385">
            <Label ID="lblStock" Text="同步仓库"/>
            <Label ID="lblFormula" Text="同步公式" IsMemo="true"/>
            <Label ID="lblSyncStock" Text="库存自动同步" IsMemo="true"/>
            <Label ID="lblZeroSync" Text="0库存同步" IsMemo="true"/>
            <Label ID="lblWarhouseStockSync" Text="分仓库存同步" IsMemo="true"/>
            <Label ID="lblMultiStockSync" Text="时效库存同步" IsMemo="true"/>
            <Grid AllowConfig="false"
                  ID="multiStockGrid"
                  CssClass='FlexAuto'
                  ReadOnly="true"
                  Visible="false"
                  DefaultRowCount="1">
                <TextColumn Caption="时效类型"
                            DataField="platformMultiName"/>
                <DropDownColumn Caption="同步类型" DataField="syncType" ListItems="0=按公式,1=固定数量"/>
                <SelectorColumn Caption="同步库存公式" SelectOnly="true" Width="200" AllowDelIcon='false'
                                DataField="formula" Icon='aicon-bianji4'/>
                <NumberColumn Caption="同步数量" NumberType="PositiveFloat" DefaultValue="0"
                              DataField="fixQty" AllowSort="false" DecimalPrecision="14" MinValue="0"/>
            </Grid>
        </FlowPanel>
    </PopupBlock>
    <PopupBlock ID="warehouseStockPopup" CssClass='pd0'>
        <FlexColumn CssClass="plr10" Enabled="false">
            <Grid ID="warehouseGrid"
                  MinHeight="300"
                  AllowConfig="false"
                  AutoPagerPageSize="true"
                  ReadOnly="false"
                  Pager="Bottom"
                  Visible="false">
                <TextColumn Caption="网店仓库" DataField="warehouseName"/>
                <NumberColumn Caption="库存同步数量" DataField="syncQty" Width="120"/>
            </Grid>

            <Grid ID="multiGrid"
                  MinHeight="300"
                  AllowConfig="false"
                  AutoPagerPageSize="true"
                  ReadOnly="false"
                  Pager="Bottom"
                  Visible="false">
                <TextColumn Caption="时效" DataField="timeDesc"/>
                <NumberColumn Caption="库存同步数量" DataField="stockNum"/>
            </Grid>
        </FlexColumn>
    </PopupBlock>

    <Style>
        .iconColor_pro:before{color:#efb10a;margin-right:5px;font-weight:bold;}
    </Style>
</Page>
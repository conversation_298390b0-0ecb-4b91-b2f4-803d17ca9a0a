<?xml version="1.0" encoding="UTF-8" ?>
<Page xmlns="Craba.UI" Title="批量生成商家编码"
      MinWidth="645"
      MinHeight="400"
      CssClass='pd0'
      AllowResize="false"
      ActionType="sale.product.GenerateXcodeRuleAction, sale/eshoporder/product/GenerateXcodeRule.js">
    <FlexColumn CssClass="plr10">
        <!--        <Block CssClass="px_tips warn">-->
        <!--            <Button Icon='bicon-zhuyi' Flat='true' />-->
        <!--            <Label Text="修改商家编码可能会影响商品在平台的权重，请谨慎修改" />-->
        <!--        </Block>-->
        <FlowPanel ID="idAssign" CssStyle="padding:0px;margin-top:10px">
            <RadioButton GroupName="rdType" ID="rdSettle" Checked="true" Text="网店商品，以商品维度商家编码"
                         OnChange="doRdChange"/>
            <!--            <CheckBox ID="ckPlatXcode" Checked="true" Text="网店商品商家编码"/>-->
            <!--            <CheckBox ID="ckPlatProperties" Checked="true" Text="网店商品属性名称"/>-->
            <DropDownEdit AllowTags="true" DropDownStyle="DropDownList"
                          Width="100"
                          ListItems="0=用&quot;_&quot;拼接,1=用&quot;-&quot;拼接" SelectedIndex="0" ID="dpSplit"/>
            <Label Text="商品属性" />
        </FlowPanel>
        <RadioButton GroupName="rdType" ID="rdEqualXcode" Text="等于系统商家编码" OnChange="doRdChange" CssClass="mt20"/>
        <RadioButton GroupName="rdType" ID="rdEqualSkuId" Text="等于网店商品SKUID" OnChange="doRdChange" CssClass="mt20"/>
<!--        <RadioButton GroupName="rdType" ID="rdEqualBarcode" Text="等于系统商品条码" OnChange="doRdChange"-->
<!--                     Visible="false"/>-->
        <RadioButton GroupName="rdType" ID="rdAuto" Text="系统自动生成" OnChange="doRdChange" CssClass="mt20"/>
        <FlowPanel ID="idAuto" ItemLabelWidth="80" ItemWidth="200">
            <TextEdit ID="edPre" NullDisplayText="支持最长10位字符" MaxLength="10" Label="前缀："/>
            <NumberEdit NumberType="PositiveInt" MinValue="1" MaxValue="999999999" ID="edStartNum"
                        NullDisplayText="支持最长10位数字" Label="起始号："/>
            <DropDownEdit AllowTags="true" DropDownStyle="DropDownList"
                          ListItems="0=用&quot;_&quot;拼接,1=用&quot;-&quot;拼接" ID="dpAutoSplit" SelectedIndex="0"
                          Label="生成方式："/>
        </FlowPanel>
    </FlexColumn>
    <HBlock CssClass='BottomBlock'>
        <Block CssClass='Flex1'/>
        <Button ID="btnSave" CssClass="BorderButton" Text="保存" OnClick="doSave"/>
        <CancelButton ID="btnCancel" Text="关闭"/>
    </HBlock>
</Page>
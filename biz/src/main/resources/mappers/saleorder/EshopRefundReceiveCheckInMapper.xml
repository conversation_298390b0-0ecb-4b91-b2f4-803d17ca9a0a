<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopRefundReceiveCheckInMapper">

    <sql id="alias_detail_columns">
        d.id,
               d.vchcode,
               d.profile_id,
               inout_detail.ptype_id,
               inout_detail.sku_id,
               inout_detail.unit_id               as unit,
               inout_detail.qty,
               inout_detail.sub_qty,
               inout_detail.unit_qty,
               inout_detail.combo_inout_detail_id as combo_row_id,
               d.create_time,
               d.update_time,
               inout_detail.quality_state         as good_state,
               d.ktype_id,
               d.loss_vchcode,
               d.other_stock_in_vchcode,
               d.batchno                          as 'batchNo',
               d.produce_date,
               d.expire_date,
               d.apply_refund_total,
               d.apply_refund_taxed_total,
               d.apply_refund_tax_total,
               d.apply_refund_freight_fee,
               d.apply_refund_mall_fee,
               d.apply_refund_service_fee,
               d.dised_taxed_price,
               d.tax_rate,
               d.batch_price
    </sql>

    <select id="queryRefundReceiveCheckInList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInEntity">
        SELECT distinct
        checkin.ktype_id,
        0 selected,
        checkin.inout_id,
        checkin.vchcode,
        checkin.checkin_number,
        rell.refund_order_id,
        refund.trade_refund_order_number,
        checkin.checkin_time,
        checkin.profile_id,
        checkin.freight_bill_no,
        checkin.freight_name,
        checkin.package_name,
        checkin.package_mobile,
        checkin.package_country,
        checkin.package_province,
        checkin.package_city,
        checkin.package_district,
        checkin.package_town,
        checkin.package_address,
        checkin.package_full_address,
        etype.fullname AS etypeName,
        ktype.fullname AS ktypeName,
        checkin.create_type,
        checkin.freight_btype_id,
        refund.receive_state,
        tbir.inout_state as inoutPostState,
        ifnull(tbir.etype_id,checkin.checkin_etype_id) as checkInEtypeId,
        refund.confirm_state,
        tbir.summary,
        checkin.receive_trigger,
        refund.trade_refund_order_number as receive_trigger_bill_id,
        tbir.failure_info,
        ktype.fullname as ktype_name,
        etype.fullname as etype_name,
        checkin.remark,
        checkin.checkin_state,
        checkin.has_print,
        checkin.otype_id,
        checkin.secret_id
        From pl_eshop_refund_receive_checkin as checkin
        LEFT JOIN pl_eshop_refund_checkin_relation rell on rell.checkin_id = checkin.vchcode and rell.profile_id = checkin.profile_id
        LEFT JOIN pl_eshop_refund refund on refund.profile_id = rell.profile_id and refund.id = rell.refund_order_id
        LEFT JOIN td_bill_inout_record tbir ON tbir.profile_id=checkin.profile_id and tbir.inout_id = checkin.inout_id
        LEFT JOIN td_bill_inout_detail inout_detail ON checkin.profile_id = inout_detail.profile_id and inout_detail.inout_id = tbir.inout_id
        LEFT JOIN base_ktype ktype ON ktype.id=checkin.ktype_id AND ktype.profile_id=checkin.profile_id
        LEFT JOIN base_etype etype ON etype.id=tbir.etype_id AND etype.profile_id=checkin.profile_id
        LEFT JOIN base_btype btype ON btype.id=checkin.freight_btype_id AND btype.profile_id=checkin.profile_id
        LEFT JOIN pl_eshop_receive_checkin_relation rel on rel.source_vchcode = checkin.vchcode and rel.profile_id = checkin.profile_id
        WHERE checkin.profile_id=#{profileId} AND checkin.deleted=0
        <if test="getNoBillData == 0">
            AND rel.id is null
        </if>
        <if test="vchcode!='' and vchcode!=null">
            AND checkin.vchcode=#{vchcode}
        </if>
        <if test="ktypeLimited">
            and (checkin.ktype_id in (select object_id from base_limit_scope where profile_id=#{profileId} and
            object_type=2 and etype_id=#{etypeId})
            or checkin.ktype_id=#{ktypeId} or checkin.ktype_id=0)
        </if>
        <if test="vchcodes!=null  and vchcodes.size()>0">
            AND checkin.vchcode in
            <foreach collection="vchcodes" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="keyWord!=null and keyWord!=''">
            <choose>
                <when test="filterKeyType.getCode()==1">
                    and checkin_number in
                    <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                        #{key}
                    </foreach>
                </when>
                <when test="filterKeyType.getCode()==0">
                    and freight_bill_no in
                    <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                        #{key}
                    </foreach>
                </when>
                <when test="filterKeyType.getCode()==2">
                    and refund.trade_refund_order_number in
                    <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                        #{key}
                    </foreach>
                </when>
                <when test="filterKeyType.getCode()==3">
                    AND checkin.vchcode in (SELECT source_vchcode FROM `pl_eshop_receive_checkin_relation` AS relation
                    WHERE relation.source_vchcode = checkin.vchcode and relation.profile_id=#{profileId} AND
                    vchtype=3301 and deleted=0
                    AND target_bill_number IN
                    <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                        #{key}
                    </foreach>
                    )
                </when>
                <when test="filterKeyType.getCode()==4">
                    AND checkin.vchcode in (SELECT source_vchcode FROM `pl_eshop_receive_checkin_relation` AS relation
                    WHERE relation.source_vchcode = checkin.vchcode and relation.profile_id=#{profileId} AND
                    vchtype=3302 and deleted=0
                    AND target_bill_number IN
                    <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                        #{key}
                    </foreach>
                    )
                </when>
                <when test="filterKeyType.getCode()==5">
                    AND checkin.vchcode in (SELECT source_vchcode FROM `pl_eshop_refund_checkin_relation` AS relation
                    WHERE relation.checkin_id = checkin.vchcode and relation.profile_id=#{profileId}
                    AND relation.refund_order_id IN
                    <foreach collection="keyArrays" close=")" open="(" separator="," item="key">
                        #{key}
                    </foreach>
                    )
                </when>
            </choose>
        </if>
        <if test="beginTime!=null and endTime!=null">
            AND checkin.checkin_time between #{beginTime} and #{endTime}
        </if>
        <if test="goodsState!=null">
            AND EXISTS(SELECT 1 FROM `pl_eshop_refund_receive_checkin_detail` AS detail
            WHERE detail.vchcode = checkin.vchcode and detail.profile_id=#{profileId} AND
            detail.goods_state=#{goodsState})
        </if>
        <if test="checkInInoutState!=null and checkInInoutState.getCode()==0">
            AND
            if(refund.receive_state is null , 1=1,refund.receive_state!=3)
            AND
            !EXISTS(SELECT 1 FROM `pl_eshop_receive_checkin_relation` AS relation
            WHERE relation.source_vchcode = checkin.vchcode and relation.profile_id=#{profileId} and deleted=0)
            AND
            if(tbir.inout_state is null , 1=1,tbir.inout_state!=100)
        </if>
        <if test="checkInInoutState!=null and checkInInoutState.getCode()==1">
            AND
            (
            if(refund.receive_state is null , 1=0,refund.receive_state=3)
            or
            EXISTS (SELECT 1 FROM `pl_eshop_receive_checkin_relation` AS relation
            WHERE relation.source_vchcode = checkin.vchcode and relation.profile_id=#{profileId} and deleted=0)
            or
            if(tbir.inout_state is null , 1=0,tbir.inout_state=100)
            )
        </if>

        <if test="hasPrint!=null and hasPrint.getCode()==0">
            AND checkin.has_print = #{hasPrint}
        </if>
        <if test="hasPrint!=null and hasPrint.getCode()==1">
            AND checkin.has_print = #{hasPrint}
        </if>

        <if test="hasRelateRefund!=null and   hasRelateRefund==0">
            AND rell.refund_order_id is null
        </if>
        <if test="hasRelateRefund!=null and   hasRelateRefund==1">
            AND rell.refund_order_id is not null
        </if>
        <if test="lossState!=null">
            AND EXISTS(SELECT 1 FROM `pl_eshop_refund_receive_checkin_detail` AS detail
            WHERE detail.vchcode = checkin.vchcode and detail.profile_id=#{profileId} AND
            detail.loss_state=#{lossState})
        </if>
        <!--        <if test="createType!=null">-->
        <!--            AND checkin.create_type=#{createType}-->
        <!--        </if>-->
        <if test="etypeLimited">
            and (checkin.checkin_etype_id in (select object_id from base_limit_scope where profile_id=#{profileId} and
            object_type=1 and etype_id=#{etypeId})
            or checkin.checkin_etype_id=#{etypeId} or checkin.checkin_etype_id=0)
        </if>
        <if test="tradeRefundOrderNumber!=null and tradeRefundOrderNumber!=''">
            and checkin.trade_refund_order_number = #{tradeRefundOrderNumber}
        </if>

        <if test="filter !=null">
            <if test="filter.checkinNumber!=null and filter.checkinNumber!=''">
                and checkin.checkin_number = #{filter.checkinNumber}
            </if>
            <if test="filter.freightBillNo!=null and filter.freightBillNo!=''">
                and checkin.freight_bill_no = #{filter.freightBillNo}
            </if>
            <if test="filter.receiveTriggerBillId!=null and filter.receiveTriggerBillId!=''">
                and checkin.receive_trigger_bill_id = #{filter.receiveTriggerBillId}
            </if>
            <if test="filter.summary!=null and filter.summary!=''">
                and tbir.summary like CONCAT('%', #{filter.summary}, '%')
            </if>
            <if test="filter.remark!=null and filter.remark!=''">
                and checkin.remark like CONCAT('%', #{filter.remark}, '%')
            </if>

            <if test="filter.receiveTriggerBillId!=null and filter.receiveTriggerBillId!=''">
                and checkin.receive_trigger_bill_id = #{filter.receiveTriggerBillId}
            </if>

            <if test="filter.checkInEtypeId!=null and filter.checkInEtypeId!=0">
                and checkin.checkin_etype_id = #{filter.checkInEtypeId}
            </if>
            <if test="filter.ktypeId!=null and filter.ktypeId!=0">
                and checkin.ktype_id = #{filter.ktypeId}
            </if>
            <if test="filter.freightBtypeId!=null and filter.freightBtypeId!=0">
                and checkin.freight_btype_id = #{filter.freightBtypeId}
            </if>
            <if test="filter.checkInInoutState!=null">
                <if test="filter.checkInInoutState.getCode()==0">
                    AND
                    if(refund.receive_state is null , 1=1,refund.receive_state!=3)
                    AND
                    !EXISTS(SELECT 1 FROM `pl_eshop_receive_checkin_relation` AS relation
                    WHERE relation.source_vchcode = checkin.vchcode and relation.profile_id=#{profileId} and deleted=0)
                    AND
                    if(tbir.inout_state is null , 1=1,tbir.inout_state!=100)
                </if>
                <if test="filter.checkInInoutState.getCode()==1">
                    AND
                    (
                    if(refund.receive_state is null , 1=0,refund.receive_state=3)
                    or
                    EXISTS (SELECT 1 FROM `pl_eshop_receive_checkin_relation` AS relation
                    WHERE relation.source_vchcode = checkin.vchcode and relation.profile_id=#{profileId} and deleted=0)
                    or
                    if(tbir.inout_state is null , 1=0,tbir.inout_state=100)
                    )
                </if>
            </if>
            <if test="filter.checkinState!=null and filter.checkinState.code != -1">
                and checkin.checkin_state = #{filter.checkinState}
            </if>
            <if test="filter.hasPrint!=null and filter.hasPrint.code != -1">
                and checkin.has_print = #{filter.hasPrint}
            </if>
            <if test="filter.otypeIds!=null and filter.otypeIds.size() > 0">
                AND checkin.otype_id in
                <foreach collection="filter.otypeIds" close=")" open="(" separator="," item="key">
                    #{key}
                </foreach>
            </if>
        </if>
        <if test="otypeIds!=null and otypeIds.size() > 0">
            AND checkin.otype_id in
            <foreach collection="otypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="checkinState!=null and checkinState.code!=-1">
            and checkin.checkin_state = #{checkinState}
        </if>
        ORDER BY checkin.checkin_time DESC
    </select>

    <select id="queryRefundReceiveCheckInDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInDetail">
        select
        d.id,
        d.vchcode,
        d.profile_id,
        d.ptype_id,
        d.sku_id,
        d.unit,
        d.qty,
        d.sub_qty,
        d.unit_qty,
        inout_detail.combo_inout_detail_id as combo_row_id,
        d.create_time,
        d.update_time,
        inout_detail.quality_state as goods_state,
        checkin.ktype_id,
        inout_detail.sub_unit_name as subUnit,
        inout_detail.sub_qty as subQty,
        bp.standard as ptypeStandard,
        bp.ptype_type,
        brand.brand_name,
        d.loss_vchcode,
        d.other_stock_in_vchcode,
        d.batchno as 'batchNo',
        d.produce_date,
        d.expire_date,
        d.apply_refund_total,
        d.apply_refund_taxed_total,
        d.apply_refund_tax_total,
        d.apply_refund_freight_fee,
        d.apply_refund_mall_fee,
        d.apply_refund_service_fee,
        d.dised_taxed_price,
        d.tax_rate,
        d.batch_price
        ,bp.pcategory,
        inout_detail.quality_state as oldGoodsState,
        bp.fullname as ptypeName,bp.ptype_type,bp.shortname
        as ptypeShortName,bp.usercode as ptypeCode,bp.standard as ptypeStandard,bp.memo as ptypeMemo,bp.protect_days,bp.protect_days_unit,bp.protect_days_view,
        bp.propenabled,bp.cost_mode,d.cost_period,
        bpx.xcode as xcode,bpf.fullbarcode as barcode,
        bps.prop_name1,bps.propvalue_name1,bps.prop_id1,bps.propvalue_id1,
        bps.prop_name2,bps.propvalue_name2,bps.prop_id2,bps.propvalue_id2,
        bps.prop_name3,bps.propvalue_name3,bps.prop_id3,bps.propvalue_id3,
        bps.prop_name4,bps.propvalue_name4,bps.prop_id4,bps.propvalue_id4,
        bps.prop_name5,bps.propvalue_name5,bps.prop_id5,bps.propvalue_id5,
        bps.prop_name6,bps.propvalue_name6,bps.prop_id6,bps.propvalue_id6,
        unit.unit_name as unitName,unit.unit_rate as unitRate,
        price.preprice1, price.preprice2, price.preprice3, price.preprice4, price.preprice5,
        price.preprice6, price.preprice7, price.preprice8,price.preprice9,price.retail_price,price.preprice10,
        IF(IFNULL(bps.pic_url,'')='', pic.pic_url, bps.pic_url) as picUrl,
        `ktype`.fullname AS 'ktypeName',bp.snenabled,bp.batchenabled,unit.unit_name ASunitName,unit.unit_rate AS unitRate,
        bsunit.unit_name AS baseUnitName,
        bpcd.gifted as gift,
        d.inout_detail_id,
        d.inout_order_id,
        d.cost_period,
        d.cost_price,
        d.source_vchcode,
        d.source_detail_id,
        inout_detail.memo,
        d.purchase_total,d.purchase_price,checkin.checkin_number,
        d.apply_refund_unit_qty,
        d.combo_share_scale
        from pl_eshop_refund_receive_checkin_detail d
        LEFT JOIN pl_eshop_refund_receive_checkin checkin on checkin.profile_id = d.profile_id and checkin.vchcode = d.vchcode
        LEFT JOIN td_bill_inout_detail inout_detail ON inout_detail.profile_id = d.profile_id AND inout_detail.inout_detail_id = d.inout_detail_id
        LEFT JOIN pl_eshop_refund_receive_checkin_detail_combo AS perrcdc ON perrcdc.profile_id=d.profile_id AND perrcdc.combo_row_id=inout_detail.combo_inout_detail_id
        LEFT JOIN base_ptype_combo_detail AS bpcd ON bpcd.profile_id=perrcdc.profile_id AND bpcd.combo_id= perrcdc.combo_id AND bpcd.sku_id=inout_detail.sku_id
        left join base_ptype bp on bp.profile_id=d.profile_id and inout_detail.ptype_id=bp.id
        left join base_brandtype brand on bp.brand_id = brand.id and bp.profile_id = brand.profile_id
        left join base_ptype_xcode bpx on bpx.profile_id=d.profile_id and bpx.sku_id=inout_detail.sku_id and bpx.unit_id=inout_detail.unit_id and
        bpx.ptype_id=inout_detail.ptype_id and bpx.defaulted=1
        left join base_ptype_sku bps on bps.profile_id=d.profile_id and bps.id=inout_detail.sku_id
        left join base_ptype_fullbarcode bpf on bpf.profile_id=d.profile_id and bpf.ptype_id=inout_detail.ptype_id and
        bpf.sku_id=inout_detail.sku_id and bpf.unit_id=inout_detail.unit_id AND bpf.defaulted=1
        left join base_ptype_unit unit on unit.profile_id=d.profile_id and unit.id=inout_detail.unit_id
        left join base_ptype_price price on price.profile_id=d.profile_id and price.unit_id=unit.id and price.sku_id = inout_detail.sku_id
        left join base_ptype_unit bsunit on bsunit.profile_id=d.profile_id and bsunit.ptype_id=inout_detail.ptype_id and
        bsunit.unit_code=1
        left join base_ptype_pic pic on pic.profile_id=d.profile_id and inout_detail.ptype_id=pic.ptype_id and pic.rowindex=1
        LEFT JOIN base_ktype ktype ON ktype.id=checkin.ktype_id AND ktype.profile_id=d.profile_id
        where d.profile_id=#{profileId}
        <if test="eshopOrderId!=null  and eshopOrderId!=0">
            AND d.vchcode=#{eshopOrderId}
        </if>
        <if test="eshopOrderDetailId!=null and eshopOrderDetailId!=0">
            AND d.id=#{eshopOrderDetailId}
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size>0">
            and d.id in
            <foreach collection="eshopOrderDetailIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="eshopOrderIds!=null and eshopOrderIds.size>0">
            and d.vchcode in
            <foreach collection="eshopOrderIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
    </select>

    <select id="queryRefundReceiveCheckInComboDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInCombo">
        select
        pl.combo_row_id,pl.vchcode,pl.profile_id,pl.combo_id,
        pl.qty,pl.create_time,pl.update_time,pl.goods_state,pl.ktype_id,pl.goods_state as 'oldGoodsState',
        pl.apply_refund_total,
        pl.apply_refund_taxed_total,
        pl.apply_refund_tax_total,
        pl.apply_refund_freight_fee,
        pl.apply_refund_mall_fee,
        pl.apply_refund_service_fee,
        pl.dised_taxed_price,
        ptype.fullname as ptypeName,ptype.shortname as ptypeShortName,ptype.id as ptypeId,'' as xcode,ptype.barcode,
        ptype.propenabled,
        pic.pic_url,ptype.usercode as ptypeCode,
        combo.need_print_name,checkin.checkin_number,
        pl.apply_refund_unit_qty
        from pl_eshop_refund_receive_checkin_detail_combo pl
        LEFT JOIN pl_eshop_refund_receive_checkin checkin on checkin.profile_id = pl.profile_id and checkin.vchcode = pl.vchcode
        left join base_ptype_combo combo on combo.combo_id = pl.combo_id and combo.profile_id = pl.profile_id
        left join base_ptype ptype on ptype.profile_id=pl.profile_id and pl.combo_id=ptype.id
        left join base_ptype_pic pic on pic.profile_id=pl.profile_id and pl.combo_id=pic.ptype_id and pic.rowindex=1
        where pl.profile_id=#{profileId}
        <if test="eshopOrderIds!=null and eshopOrderIds.size>0">
            and pl.vchcode in
            <foreach collection="eshopOrderIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="eshopOrderId!=null  and eshopOrderId!=0">
            AND pl.vchcode=#{eshopOrderId}
        </if>
        <if test="comboRowIds!=null and comboRowIds.size>0">
            and pl.combo_row_id in
            <foreach collection="comboRowIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
    </select>
    <insert id="insertRefundReceiveCheckIn">
        INSERT INTO pl_eshop_refund_receive_checkin (vchcode,
                                                     checkin_number,
                                                     refund_order_id,
                                                     trade_refund_order_number,
                                                     checkin_time,
                                                     profile_id,
                                                     freight_name,
                                                     freight_bill_no,
                                                     package_name,
                                                     package_mobile,
                                                     package_country,
                                                     package_province,
                                                     package_city,
                                                     package_district,
                                                     package_town,
                                                     package_address,
                                                     package_full_address,
                                                     create_type,
                                                     freight_btype_id,
                                                     receive_trigger,
                                                     receive_trigger_bill_id,
                                                     inout_id,
                                                     remark,
                                                     checkin_state, ktype_id, checkin_etype_id, otype_id, secret_id)
        values (#{vchcode},
                #{checkinNumber},
                #{refundOrderId},
                #{tradeRefundOrderNumber},
                #{checkinTime},
                #{profileId},
                #{freightName},
                #{freightBillNo},
                #{packageName},
                #{packageMobile},
                #{packageCountry},
                #{packageProvince},
                #{packageCity},
                #{packageDistrict},
                #{packageTown},
                #{packageAddress},
                #{packageFullAddress},
                #{createType},
                #{freightBtypeId},
                #{receiveTrigger},
                #{receiveTriggerBillId},
                #{inoutId},
                #{remark},
                #{checkinState}, #{ktypeId}, #{checkInEtypeId}, #{otypeId}, #{secretId})
    </insert>

    <insert id="insertRefundReceiveCheckInBatch">
        INSERT INTO pl_eshop_refund_receive_checkin (vchcode,
        checkin_number,
        refund_order_id,
        trade_refund_order_number,
        checkin_time,
        profile_id,
        freight_name,
        freight_bill_no,
        package_name,
        package_mobile,
        package_country,
        package_province,
        package_city,
        package_district,
        package_town,
        package_address,
        package_full_address,
        create_type,
        freight_btype_id,
        receive_trigger,
        receive_trigger_bill_id,
        inout_id,
        remark,
        checkin_state, ktype_id, checkin_etype_id, otype_id, secret_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.vchcode},
            #{item.checkinNumber},
            #{item.refundOrderId},
            #{item.tradeRefundOrderNumber},
            #{item.checkinTime},
            #{item.profileId},
            #{item.freightName},
            #{item.freightBillNo},
            #{item.packageName},
            #{item.packageMobile},
            #{item.packageCountry},
            #{item.packageProvince},
            #{item.packageCity},
            #{item.packageDistrict},
            #{item.packageTown},
            #{item.packageAddress},
            #{item.packageFullAddress},
            #{item.createType},
            #{item.freightBtypeId},
            #{item.receiveTrigger},
            #{item.receiveTriggerBillId},
            #{item.inoutId},
            #{item.remark},
            #{item.checkinState},
            #{item.ktypeId},
            #{item.checkInEtypeId},
            #{item.otypeId},
            #{item.secretId})
        </foreach>
    </insert>

    <insert id="insertRefundReceiveCheckInDetail">
        INSERT INTO pl_eshop_refund_receive_checkin_detail (id,
                                                            vchcode,
                                                            profile_id,
                                                            ptype_id,
                                                            sku_id,
                                                            unit,
                                                            qty,
                                                            sub_qty,
                                                            unit_qty,
                                                            combo_row_id,
                                                            goods_state,
                                                            ktype_id,
                                                            loss_state,
                                                            loss_vchcode,
                                                            other_stock_in_vchcode,
                                                            batchno,
                                                            expire_date,
                                                            produce_date,
                                                            apply_refund_total,
                                                            apply_refund_taxed_total,
                                                            apply_refund_tax_total,
                                                            apply_refund_freight_fee,
                                                            apply_refund_mall_fee,
                                                            apply_refund_service_fee,
                                                            dised_taxed_price,
                                                            tax_rate,
                                                            cost_period,
                                                            batch_price,
                                                            cost_type,
                                                            inout_detail_id,
                                                            inout_order_id,
                                                            cost_price,
                                                            source_detail_id,
                                                            source_vchcode,
                                                            purchase_price,
                                                            purchase_total,
                                                            apply_refund_unit_qty,
                                                            combo_share_scale)
        values (#{id},
                #{vchcode},
                #{profileId},
                #{ptypeId},
                #{skuId},
                #{unit},
                #{qty},
                #{subQty},
                #{unitQty},
                #{comboRowId},
                #{goodsState},
                #{ktypeId},
                #{lossState},
                #{lossVchcode},
                #{otherStockInVchcode},
                #{batchno},
                #{expireDate},
                #{produceDate},
                #{applyRefundTotal},
                #{applyRefundTaxedTotal},
                #{applyRefundTaxTotal},
                #{applyRefundFreightFee},
                #{applyRefundMallFee},
                #{applyRefundServiceFee},
                #{disedTaxedPrice},
                #{taxRate},
                #{costPeriod},
                #{batchPrice},
                #{costType},
                #{inoutDetailId},
                #{inoutOrderId},
                #{costPrice},
                #{sourceDetailId},
                #{sourceVchcode},
                #{purchasePrice},
                #{purchaseTotal},
                #{applyRefundUnitQty},
                #{comboShareScale})
    </insert>

    <insert id="insertRefundReceiveCheckInDetailBatch">
        INSERT INTO pl_eshop_refund_receive_checkin_detail (id,
        vchcode,
        profile_id,
        ptype_id,
        sku_id,
        unit,
        qty,
        sub_qty,
        unit_qty,
        combo_row_id,
        goods_state,
        ktype_id,
        loss_state,
        loss_vchcode,
        other_stock_in_vchcode,
        batchno,
        expire_date,
        produce_date,
        apply_refund_total,
        apply_refund_taxed_total,
        apply_refund_tax_total,
        apply_refund_freight_fee,
        apply_refund_mall_fee,
        apply_refund_service_fee,
        dised_taxed_price,
        tax_rate,
        cost_period,
        batch_price,
        cost_type,
        inout_detail_id,
        inout_order_id,
        cost_price,
        source_detail_id,
        source_vchcode,
        purchase_price,
        purchase_total,
        apply_refund_unit_qty,
        combo_share_scale)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},
            #{item.vchcode},
            #{item.profileId},
            #{item.ptypeId},
            #{item.skuId},
            #{item.unit},
            #{item.qty},
            #{item.subQty},
            #{item.unitQty},
            #{item.comboRowId},
            #{item.goodsState},
            #{item.ktypeId},
            #{item.lossState},
            #{item.lossVchcode},
            #{item.otherStockInVchcode},
            #{item.batchno},
            #{item.expireDate},
            #{item.produceDate},
            #{item.applyRefundTotal},
            #{item.applyRefundTaxedTotal},
            #{item.applyRefundTaxTotal},
            #{item.applyRefundFreightFee},
            #{item.applyRefundMallFee},
            #{item.applyRefundServiceFee},
            #{item.disedTaxedPrice},
            #{item.taxRate},
            #{item.costPeriod},
            #{item.batchPrice},
            #{item.costType},
            #{item.inoutDetailId},
            #{item.inoutOrderId},
            #{item.costPrice},
            #{item.sourceDetailId},
            #{item.sourceVchcode},
            #{item.purchasePrice},
            #{item.purchaseTotal},
            #{item.applyRefundUnitQty},
            #{item.comboShareScale})
        </foreach>
    </insert>

    <insert id="insertOrderComboRow">
        INSERT INTO pl_eshop_refund_receive_checkin_detail_combo (combo_row_id,
                                                                  vchcode,
                                                                  profile_id,
                                                                  combo_id,
                                                                  qty,
                                                                  goods_state,
                                                                  ktype_id,
                                                                  apply_refund_total,
                                                                  apply_refund_taxed_total,
                                                                  apply_refund_tax_total,
                                                                  apply_refund_freight_fee,
                                                                  apply_refund_mall_fee,
                                                                  apply_refund_service_fee,
                                                                  dised_taxed_price,
                                                                  apply_refund_unit_qty)
        values (#{comboRowId},
                #{vchcode},
                #{profileId},
                #{comboId},
                #{qty},
                #{goodsState},
                #{ktypeId},
                #{applyRefundTotal},
                #{applyRefundTaxedTotal},
                #{applyRefundTaxTotal},
                #{applyRefundFreightFee},
                #{applyRefundMallFee},
                #{applyRefundServiceFee},
                #{disedTaxedPrice},
                #{applyRefundUnitQty})
    </insert>

    <insert id="insertOrderComboRowBatch">
        INSERT INTO pl_eshop_refund_receive_checkin_detail_combo (combo_row_id,
        vchcode,
        profile_id,
        combo_id,
        qty,
        goods_state,
        ktype_id,
        apply_refund_total,
        apply_refund_taxed_total,
        apply_refund_tax_total,
        apply_refund_freight_fee,
        apply_refund_mall_fee,
        apply_refund_service_fee,
        dised_taxed_price,
        apply_refund_unit_qty)
        values

        <foreach collection="list" item="item" separator=",">
            (#{item.comboRowId},
            #{item.vchcode},
            #{item.profileId},
            #{item.comboId},
            #{item.qty},
            #{item.goodsState},
            #{item.ktypeId},
            #{item.applyRefundTotal},
            #{item.applyRefundTaxedTotal},
            #{item.applyRefundTaxTotal},
            #{item.applyRefundFreightFee},
            #{item.applyRefundMallFee},
            #{item.applyRefundServiceFee},
            #{item.disedTaxedPrice},
            #{item.applyRefundUnitQty})
        </foreach>
    </insert>

    <update id="updateRefundReceiveCheckIn">
        update pl_eshop_refund_receive_checkin
        SET `refund_order_id`           = #{refundOrderId},
            `trade_refund_order_number` = #{tradeRefundOrderNumber},
            `freight_name`              = #{freightName},
            `freight_bill_no`           = #{freightBillNo},
            `package_name`              = #{packageName},
            `package_mobile`            = #{packageMobile},
            `package_country`           = #{packageCountry},
            `package_province`          = #{packageProvince},
            `package_city`              = #{packageCity},
            `package_district`          = #{packageDistrict},
            `package_town`              = #{packageTown},
            `package_address`           = #{packageAddress},
            `package_full_address`      = #{packageFullAddress},
            freight_btype_id            = #{freightBtypeId},
            remark                      = #{remark},
            checkin_state               = #{checkinState},
            ktype_id                    = #{ktypeId},
            checkin_etype_id            = #{checkInEtypeId},
            otype_id                    = #{otypeId},
            secret_id                   = #{secretId}
        where profile_id = #{profileId}
          and vchcode = #{vchcode}
    </update>

    <select id="queryCheckCount"
            resultType="integer">
        select count(*)
        from pl_eshop_refund_receive_checkin_detail
        where profile_id = #{profileId}
          and vchcode = #{vchcode}
    </select>

    <update id="updateReceiveCheckInDeleteState">
        update pl_eshop_refund_receive_checkin set deleted=#{deleted}
        where profile_id=#{profileId}
        <if test="vchcode!=null  and vchcode!=0">
            and vchcode=#{vchcode}
        </if>
        <if test="vchcodes!=null and vchcodes.size()>0">
            and vchcode in
            <foreach collection="vchcodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>


    <select id="getReceiveCheckInRelations"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInRelationEntity">
        select * from pl_eshop_receive_checkin_relation
        where profile_id=#{profileId} AND deleted=0
        <if test="vchcode!=null  and vchcode!=0">
            and source_vchcode=#{vchcode}
        </if>
        <if test="targetBillVchcode!=null and targetBillVchcode!=0">
            AND target_bill_vchcode=#{targetBillVchcode}
        </if>
        <if test="targetBillNumber!=null and targetBillNumber!=''">
            AND target_bill_number=#{targetBillNumber}
        </if>
        <if test="vchcodes!=null and vchcodes.size()>0">
            and source_vchcode in
            <foreach collection="vchcodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="targetBillVchcodes!=null and targetBillVchcodes.size()>0">
            and target_bill_vchcode in
            <foreach collection="targetBillVchcodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>

    <delete id="deleteReceiveCheckInRelations">
        delete from pl_eshop_receive_checkin_relation
        where profile_id=#{profileId}
        <if test="vchcode!=null and vchcode!=0">
            and source_vchcode=#{vchcode}
        </if>
        <if test="targetBillVchcode!=null and targetBillVchcode!=0">
            AND target_bill_vchcode=#{targetBillVchcode}
        </if>
        <if test="targetBillNumber!=null and targetBillNumber!=''">
            AND target_bill_number=#{targetBillNumber}
        </if>
        <if test="vchcodes!=null and  vchcodes.size()>0">
            and source_vchcode in
            <foreach collection="vchcodes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="vchtype!=null">
            and vchtype=#{vchtype}
        </if>
    </delete>

    <delete id="deleteComboRow">
        delete
        from pl_eshop_refund_receive_checkin_detail_combo
        where profile_id = #{profileId}
          and vchcode = #{eshopOrderId}
    </delete>
    <delete id="deleteCheckInDetails">
        delete from pl_eshop_refund_receive_checkin_detail where profile_id=#{profileId} and id in
        <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <delete id="deleteSerialInfo">
        delete from pl_eshop_refund_receive_checkin_detail_serialno
        where profile_id=#{profileId}
        and detail_id in
        <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
    <delete id="deleteAllCheckInInfoByCheckInVchcode">
        delete
        from pl_eshop_refund_receive_checkin
        where profile_id = #{profileId}
          and vchcode = #{vchcode};
        delete
        from pl_eshop_refund_receive_checkin_detail_combo
        where profile_id = #{profileId}
          and vchcode = #{vchcode};
        delete
        from pl_eshop_refund_receive_checkin_detail
        where profile_id = #{profileId}
          and vchcode = #{vchcode};
        delete
        from pl_eshop_refund_receive_checkin_detail_serialno
        where profile_id = #{profileId}
          and vchcode = #{vchcode};
        delete
        from pl_eshop_refund_checkin_relation
        where profile_id = #{profileId}
          and checkin_id = #{vchcode};
    </delete>
    <delete id="deleteInoutInfo"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryOrderDetailParameter">
        delete
        from td_bill_inout_record
        where profile_id = #{profileId}
          and inout_id = #{eshopOrderId};
        delete
        from td_bill_inout_detail
        where profile_id = #{profileId}
          and inout_id = #{eshopOrderId};
        delete
        from td_bill_detail_batch
        where profile_id = #{profileId}
          and inout_id = #{eshopOrderId};
        delete
        from td_bill_detail_serialno
        where profile_id = #{profileId}
          and inout_id = #{eshopOrderId};
    </delete>

    <update id="relateRefund">
        update pl_eshop_refund_receive_checkin
        set refund_order_id=#{refundOrderId},
            trade_refund_order_number=#{tradeRefundOrderNumber},
            receive_trigger_bill_id = #{tradeRefundOrderNumber},
            receive_trigger = 1
        where profile_id = #{profileId}
          and vchcode = #{vchcode}
    </update>

    <select id="getCheckInConfig"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInConfig">
        SELECT config.*, bktype.fullname AS 'badProductKtypeName', lktype.fullname AS 'lossProductKtypeName'
        FROM pl_eshop_receive_checkin_config AS config
                 LEFT JOIN base_ktype bktype
                           ON bktype.id = `config`.bad_product_ktype_id AND bktype.profile_id = config.profile_id
                 LEFT JOIN base_ktype lktype
                           ON lktype.id = `config`.loss_product_ktype_id AND lktype.profile_id = config.profile_id
        WHERE config.profile_id = #{profileId}
        limit 1
    </select>
    <select id="getBatchInfo" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.GoodsBatchEntity">
        select *
        from acc_goodsstock_batchlife
        where profile_id = #{profileId}
          and batchno = #{batchNo}
          and ptype_id = #{ptypeId}
    </select>
    <select id="getSerialInfo" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.GoodsSerialEntity">
        select * from
        pl_eshop_refund_receive_checkin_detail_serialno
        where profile_id=#{profileId}
        and vchcode=#{eshopOrderId}
        <if test="eshopOrderDetailId!=null and  eshopOrderDetailId!=0">
            and detail_id=#{eshopOrderDetailId}
        </if>
    </select>
    <insert id="insertCheckInConfig">
        INSERT INTO pl_eshop_receive_checkin_config
        (`id`,
         `profile_id`,
         `bad_product_ktype_id`,
         `loss_product_ktype_id`,
         `enable_relate_refund`)
        values (#{id},
                #{profileId},
                #{badProductKtypeId},
                #{lossProductKtypeId},
                #{enableRelateRefund})
    </insert>
    <insert id="insertReceiveCheckInRelation">
        INSERT INTO pl_eshop_receive_checkin_relation
        (`id`,
         `profile_id`,
         `vchtype`,
         `target_bill_number`,
         `target_bill_vchcode`,
         `source_vchcode`,
         `create_time`,
         `update_time`,
         `deleted`)
        values (#{id},
                #{profileId},
                #{vchtype},
                #{targetBillNumber},
                #{targetBillVchcode},
                #{sourceVchcode},
                #{createTime},
                #{updateTime},
                #{deleted})
    </insert>
    <insert id="insertSerialInfo">
        insert into pl_eshop_refund_receive_checkin_detail_serialno
        (`id`,
         `vchcode`,
         `detail_id`,
         `profile_id`,
         `snno`,
         `sn1`,
         `sn2`,
         `sn3`,
         `sn_memo`)
        values (#{id},
                #{vchcode},
                #{detailId},
                #{profileId},
                #{snno},
                #{sn1},
                #{sn2},
                #{sn3},
                #{snMemo})
    </insert>

    <insert id="insertSerialInfoBatch">
        insert into pl_eshop_refund_receive_checkin_detail_serialno
        (`id`,
        `vchcode`,
        `detail_id`,
        `profile_id`,
        `snno`,
        `sn1`,
        `sn2`,
        `sn3`,
        `sn_memo`)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id},
            #{item.vchcode},
            #{item.detailId},
            #{item.profileId},
            #{item.snno},
            #{item.sn1},
            #{item.sn2},
            #{item.sn3},
            #{item.snMemo})
        </foreach>
    </insert>
    <insert id="insertCheckInDetailToCostTable">
        insert into pl_eshop_refund_detail_original_cost(id, refund_order_detail_id, refund_order_id, sub_qty,batch_price, cost_period, profile_id)
        values
        <foreach collection="details" item="detail" separator=",">
            (
            #{detail.uId},#{detail.id},#{detail.vchcode},#{detail.qty},#{detail.batchPrice},#{detail.costPeriod},#{profileId}
            )
        </foreach>
    </insert>

    <update id="updateCheckInConfig">
        update pl_eshop_receive_checkin_config
        set bad_product_ktype_id=#{badProductKtypeId},
            loss_product_ktype_id=#{lossProductKtypeId},
            enable_relate_refund=#{enableRelateRefund}
        where profile_id = #{profileId}
    </update>
    <update id="updateLossState">
        update pl_eshop_refund_receive_checkin_detail
        set loss_state =#{lossState},loss_vchcode=#{lossVchcode}
        where profile_id=#{profileId} AND vchcode=#{vchcode} and goods_state=#{goodsState}
        <if test="id!=null and id!=0">
            and id=#{id}
        </if>
        <if test="oldTargetBillVchcode!=null and oldTargetBillVchcode!=0">
            and loss_vchcode=#{oldTargetBillVchcode}
        </if>
    </update>
    <update id="updateOtherStockInKtype">
        update pl_eshop_refund_receive_checkin_detail
        set ktype_id=#{ktypeId},other_stock_in_vchcode=#{targetBillVchcode}
        where profile_id=#{profileId} and vchcode=#{vchcode}
        <if test="id!=null and id!=0">
            and id=#{id}
        </if>
        <if test="oldTargetBillVchcode!=null and oldTargetBillVchcode!=0">
            and other_stock_in_vchcode=#{oldTargetBillVchcode}
        </if>
    </update>
    <select id="getLossVchcodesByStockInVchcode" resultType="java.math.BigInteger">
        select distinct loss_vchcode
        from pl_eshop_refund_receive_checkin_detail
        where profile_id = #{profileId}
          AND other_stock_in_vchcode = #{otherStockInVchcode}
          AND vchcode = #{vchcode}
    </select>
    <select id="getKtypeInfo" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Ktype">
        select * from base_ktype
        where profile_id=#{profileId}
        and id in
        <foreach collection="ktypeIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <update id="updateCheckInDetailMoneyFiled">
        UPDATE pl_eshop_refund_receive_checkin_detail
        SET apply_refund_total=#{applyRefundTotal},
        apply_refund_taxed_total=#{applyRefundTaxedTotal},
        apply_refund_tax_total=#{applyRefundTaxTotal},
        apply_refund_freight_fee=#{applyRefundFreightFee},
        apply_refund_mall_fee=#{applyRefundMallFee},
        apply_refund_service_fee=#{applyRefundServiceFee},
        dised_taxed_price=#{disedTaxedPrice},
        tax_rate=#{taxRate}
        <if test="ktypeId!=0">
            ,ktype_id=#{ktypeId}
        </if>
        WHERE profile_id = #{profileId}
        and id = #{id}
    </update>
    <update id="updateCheckInComboMoneyFiled">
        UPDATE pl_eshop_refund_receive_checkin_detail_combo
        SET apply_refund_total=#{applyRefundTotal},
            apply_refund_taxed_total=#{applyRefundTaxedTotal},
            apply_refund_tax_total=#{applyRefundTaxTotal},
            apply_refund_freight_fee=#{applyRefundFreightFee},
            apply_refund_mall_fee=#{applyRefundMallFee},
            apply_refund_service_fee=#{applyRefundServiceFee},
            dised_taxed_price=#{disedTaxedPrice},
            ktype_id=#{ktypeId}
        WHERE profile_id = #{profileId}
          and combo_row_id = #{id}
    </update>
    <update id="updateRefundFreight">
        UPDATE pl_eshop_refund_freight
        SET freight_template_id=#{btypeId},
            freight_no=#{freightNo}
        where profile_id = #{profileId}
          and refund_order_id = #{vchcode}
    </update>
    <update id="updateRefundReceiveCheckInRefundInfo">
        update pl_eshop_refund_receive_checkin t1
        SET `refund_order_id` = #{refundOrderId},
        `trade_refund_order_number` = #{tradeRefundOrderNumber},
        receive_trigger_bill_id = #{tradeRefundOrderNumber}
        where profile_id = #{profileId}
        and vchcode in
        <foreach collection="checkVchcodeList" close=")" open="(" separator="," item="key">
            #{key}
        </foreach>

    </update>
    <update id="updateReceiveCheckInDetailsCost">
        <foreach collection="details" item="detail" index="index" open="" close="" separator=";">
            update pl_eshop_refund_receive_checkin_detail
            SET update_time=NOW()
            <if test="detail.costPeriod!=null and detail.costPeriod!=0">
                , cost_period = #{detail.costPeriod}
            </if>
            <if test="detail.batchPrice!=null and detail.batchPrice!=0">
                , batch_price = #{detail.batchPrice}
            </if>
            <if test="detail.batchno != null and detail.batchno!=''">
                , batchno = #{detail.batchno}
            </if>
            <if test="detail.expireDate != null">
                , expire_date = #{detail.expireDate}
            </if>
            <if test="detail.produceDate != null">
                , produce_date = #{detail.produceDate}
            </if>
            <if test="detail.costType != null">
                , cost_type = #{detail.costType}
            </if>
            where profile_id = #{profileId} and id = #{detail.id}
        </foreach>
    </update>
    <!--    <update id="updateCheckInAndDetialsKTypeByVchcode">-->
    <!--        UPDATE pl_eshop_refund_receive_checkin-->
    <!--        SET ktype_id=#{ktypeId}-->
    <!--        where profile_id = #{profileId}-->
    <!--        and vchcode in-->
    <!--        <foreach collection="vchcodes" close=")" open="(" separator="," item="vchcode">-->
    <!--            #{vchcode}-->
    <!--        </foreach>;-->
    <!--        UPDATE pl_eshop_refund_receive_checkin_detail-->
    <!--        set ktype_id=#{ktypeId}-->
    <!--        where profile_id = #{profileId}-->
    <!--        and vchcode in-->
    <!--        <foreach collection="vchcodes" close=")" open="(" separator="," item="vchcode">-->
    <!--            #{vchcode}-->
    <!--        </foreach>;-->
    <!--    </update>-->
    <update id="updateCheckInInoutBillInfo">
        update td_bill_inout_record
        set vchcode = #{vchcode}
        where profile_id = #{profileId}
          and inout_id = #{inoutId};
        update acc_bill_inout_record
        set vchcode = #{vchcode}
        where profile_id = #{profileId}
          and inout_id = #{inoutId};
    </update>
    <update id="updateCheckInInoutDetailBillInfo">
        update td_bill_inout_detail
        set vchcode   = #{vchcode},
            detail_id = #{detailId}
        where profile_id = #{profileId}
          and inout_detail_id = #{inoutDetailId};
        update acc_bill_inout_detail
        set vchcode   = #{vchcode},
            detail_id = #{detailId}
        where profile_id = #{profileId}
          and inout_detail_id = #{inoutDetailId};
        update td_bill_detail_serialno
        set vchcode   = #{vchcode},
            detail_id = #{detailId}
        where profile_id = #{profileId}
          and inout_detail_id = #{inoutDetailId};
        update acc_bill_detail_serialno
        set vchcode   = #{vchcode},
            detail_id = #{detailId}
        where profile_id = #{profileId}
          and inout_detail_id = #{inoutDetailId};
        update td_bill_detail_batch
        set vchcode   = #{vchcode},
            detail_id = #{detailId}
        where profile_id = #{profileId}
          and inout_detail_id = #{inoutDetailId};
        update acc_bill_detail_batch
        set vchcode   = #{vchcode},
            detail_id = #{detailId}
        where profile_id = #{profileId}
          and inout_detail_id = #{inoutDetailId};
    </update>
    <update id="updateCheckInInoutDetailBillInfoByDetails">
        <foreach collection="details" item="detail" index="index" open="" close="" separator=";">
            update td_bill_inout_detail
            set vchcode = #{detail.vchcode},
            detail_id = #{detail.billEntityDetailId}
            where profile_id = #{profileId}
            and inout_detail_id = #{detail.id};
            update acc_bill_inout_detail
            set vchcode = #{detail.vchcode},
            detail_id = #{detail.billEntityDetailId}
            where profile_id = #{profileId}
            and inout_detail_id = #{detail.id};
            update td_bill_detail_serialno
            set vchcode = #{detail.vchcode},
            detail_id = #{detail.billEntityDetailId}
            where profile_id = #{profileId}
            and inout_detail_id = #{detail.id};
            update acc_bill_detail_serialno
            set vchcode = #{detail.vchcode},
            detail_id = #{detail.billEntityDetailId}
            where profile_id = #{profileId}
            and inout_detail_id = #{detail.id};
            update td_bill_detail_batch
            set vchcode = #{detail.vchcode},
            detail_id = #{detail.billEntityDetailId}
            where profile_id = #{profileId}
            and inout_detail_id = #{detail.id};
            update acc_bill_detail_batch
            set vchcode = #{detail.vchcode},
            detail_id = #{detail.billEntityDetailId}
            where profile_id = #{profileId}
            and inout_detail_id = #{detail.id}
        </foreach>
    </update>
    <update id="changePrintState">
        update pl_eshop_refund_receive_checkin
        SET has_print = 1
        where profile_id = #{profileId}
        and vchcode in
        <foreach collection="list" close=")" open="(" separator="," item="item">
            #{item.vchcode}
        </foreach>
    </update>
    <update id="updateCheckinOtype">
        update pl_eshop_refund_receive_checkin
        set otype_id = #{otypeId}
        where profile_id = #{profileId}
          and vchcode = #{vchcode}
    </update>
    <update id="updateCheckinState">
        update pl_eshop_refund_receive_checkin
        set checkin_state = #{checkinState}
        where profile_id = #{profileId}
        and vchcode in
        <foreach collection="list" close=")" open="(" separator="," item="item">
            #{item.vchcode}
        </foreach>
    </update>
    <update id="updateCheckinRemarkForNoMessage">
        <foreach collection="list"  separator=";" item="item">
            update pl_eshop_refund_receive_checkin set remark = '无信息件'
            where profile_id = #{item.profileId} and vchcode = #{item.vchcode}
        </foreach>
    </update>

    <select id="queryCheckInDetailListByLogisticsNo"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInDetail">
        SELECT d.id,
               d.vchcode,
               d.profile_id,
               inout_detail.ptype_id,
               inout_detail.sku_id,
               inout_detail.unit_id               as unit,
               inout_detail.qty,
               inout_detail.sub_qty,
               inout_detail.unit_qty,
               inout_detail.combo_inout_detail_id as combo_row_id,
               d.create_time,
               d.update_time,
               inout_detail.quality_state         as good_state,
               d.ktype_id,
               d.loss_vchcode,
               d.other_stock_in_vchcode,
               d.batchno                          as 'batchNo',
               d.produce_date,
               d.expire_date,
               d.apply_refund_total,
               d.apply_refund_taxed_total,
               d.apply_refund_tax_total,
               d.apply_refund_freight_fee,
               d.apply_refund_mall_fee,
               d.apply_refund_service_fee,
               d.dised_taxed_price,
               d.tax_rate,
               d.batch_price,
               d.combo_share_scale
        From pl_eshop_refund_receive_checkin_detail d
                 LEFT JOIN td_bill_inout_detail inout_detail ON inout_detail.profile_id = d.profile_id AND inout_detail.inout_detail_id = d.inout_detail_id
                 Left JOIN pl_eshop_refund_receive_checkin t2 on t2.vchcode = d.vchcode AND t2.profile_id = d.profile_id
        WHERE t2.trade_refund_order_number = ''
          and t2.profile_id = #{profileId}
          and t2.freight_name = #{freightName}
          and t2.freight_bill_no = #{freightNo}
          and t2.deleted = 0
    </select>

    <select id="queryCheckInDetailListByLogisticsNoForCheck"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInDetail">
        SELECT distinct d.vchcode
        From pl_eshop_refund_receive_checkin_detail d
                 LEFT JOIN td_bill_inout_detail inout_detail ON inout_detail.profile_id = d.profile_id AND inout_detail.inout_detail_id = d.inout_detail_id
                 Left JOIN pl_eshop_refund_receive_checkin t2 on t2.vchcode = d.vchcode AND t2.profile_id = d.profile_id
                 left join pl_eshop_refund_checkin_relation rel on rel.checkin_id = t2.vchcode and rel.profile_id = t2.profile_id
                 left join td_bill_relation trel on trel.profile_id = t2.profile_id and trel.source_vchcode = t2.vchcode
        WHERE rel.id is null and trel.id is null
          and t2.profile_id = #{profileId}
          and t2.freight_name = #{freightName}
          and t2.freight_bill_no = #{freightNo}
          and t2.deleted = 0
    </select>

    <select id="queryCheckInDetailsByIdList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInDetail">
        SELECT
        <include refid="alias_detail_columns"/>
        ,t2.checkin_number,d.combo_share_scale
        From pl_eshop_refund_receive_checkin_detail d
        LEFT JOIN td_bill_inout_detail inout_detail ON inout_detail.profile_id = d.profile_id AND inout_detail.inout_detail_id = d.inout_detail_id
        Left JOIN pl_eshop_refund_receive_checkin t2 on t2.vchcode = d.vchcode AND t2.profile_id = d.profile_id
        WHERE d.profile_id = #{profileId} and d.id in
        <foreach collection="checkDetailIdList" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </select>
    <select id="queryRefundReceiveCheckInByRefundNumber"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInEntity">
        SELECT checkin.`trade_refund_order_number`
        From pl_eshop_refund_receive_checkin as checkin
        WHERE checkin.profile_id=#{profileId}
        and checkin.trade_refund_order_number in
        <foreach collection="refundNumberList" close=")" open="(" separator="," item="refundNumber">
            #{refundNumber}
        </foreach>

    </select>
    <select id="queryRefundReceiveCheckInComboDetailsById"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInCombo">
        select pl.combo_row_id,
               pl.vchcode,
               pl.profile_id,
               pl.combo_id,
               pl.qty,
               pl.create_time,
               pl.update_time,
               pl.goods_state,
               pl.ktype_id,
               pl.goods_state  as 'oldGoodsState',
               pl.apply_refund_total,
               pl.apply_refund_taxed_total,
               pl.apply_refund_tax_total,
               pl.apply_refund_freight_fee,
               pl.apply_refund_mall_fee,
               pl.apply_refund_service_fee,
               pl.dised_taxed_price,
               ptype.fullname  as ptypeName,
               ptype.shortname as ptypeShortName,
               ptype.id        as ptypeId,
               ""              as xcode,
               ptype.barcode,
               pic.pic_url,
               ptype.usercode  as ptypeCode
        from pl_eshop_refund_receive_checkin_detail_combo pl
                 left join base_ptype ptype on ptype.profile_id = pl.profile_id and pl.combo_id = ptype.id
                 left join base_ptype_pic pic
                           on pic.profile_id = pl.profile_id and pl.combo_id = pic.ptype_id and pic.rowindex = 1
        where pl.profile_id = #{profileId}
          AND pl.combo_row_id = #{comboRowId}
    </select>

    <select id="queryRefundReceiveCheckInDetailByComboId" resultType="java.math.BigInteger">
        SELECT d.id
        From pl_eshop_refund_receive_checkin_detail d
        WHERE d.profile_id = #{profileId}
          and d.combo_row_id = #{comboRowId}
    </select>
    <select id="queryDoLossOtyprId" resultType="java.math.BigInteger">
        SELECT otype_id
        FROM td_bill_core AS tdc
        WHERE tdc.vchcode = #{otherStockInVchcode}
          AND tdc.profile_id = #{profileId}
        UNION ALL
        SELECT otype_id
        FROM acc_bill_core AS adc
        WHERE adc.vchcode = #{otherStockInVchcode}
          AND adc.profile_id = #{profileId}
    </select>
    <select id="queryCheckInDetailsByRefundVchcodes"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInDetail">
        SELECT
        d.id,
        d.vchcode,
        d.profile_id,
        inout_detail.ptype_id,
        inout_detail.sku_id,
        inout_detail.unit_id as unit,
        inout_detail.qty,
        inout_detail.sub_qty,
        inout_detail.unit_qty,
        inout_detail.combo_inout_detail_id as combo_row_id,
        d.create_time,
        d.update_time,
        inout_detail.quality_state as good_state,
        d.ktype_id,
        d.loss_vchcode,
        d.other_stock_in_vchcode,
        d.batchno as 'batchNo',
        d.produce_date,
        d.expire_date,
        d.apply_refund_total,
        d.apply_refund_taxed_total,
        d.apply_refund_tax_total,
        d.apply_refund_freight_fee,
        d.apply_refund_mall_fee,
        d.apply_refund_service_fee,
        d.dised_taxed_price,
        d.tax_rate,
        d.batch_price,
        d.cost_period,
        d.cost_price,
        rc.checkin_number,
        rel.refund_order_id,
        d.source_detail_id,
        d.source_vchcode
        From pl_eshop_refund_receive_checkin rc
        LEFT JOIN pl_eshop_refund_receive_checkin_detail d on rc.vchcode=d.vchcode and rc.profile_id=d.profile_id
        LEFT JOIN td_bill_inout_detail inout_detail ON inout_detail.profile_id = d.profile_id AND inout_detail.inout_detail_id = d.inout_detail_id
        LEFT JOIN pl_eshop_refund_checkin_relation rel on rel.checkin_id = rc.vchcode and rel.profile_id = rc.profile_id
        WHERE rc.profile_id = #{profileId} and rc.refund_order_id in
        <foreach collection="refundVchcodes" close=")" open="(" separator="," item="refundVchcode">
            #{refundVchcode}
        </foreach>
    </select>
    <select id="checkCheckInHaveInoutByRefundVchcode" resultType="java.lang.Boolean">
        select count(tbir.vchcode) > 0
        from pl_eshop_refund per
                 left join pl_eshop_refund_receive_checkin perrc
                           on per.profile_id = perrc.profile_id and per.id = perrc.refund_order_id
                 left join td_bill_inout_record tbir on per.profile_id = tbir.profile_id and perrc.inout_id = tbir.inout_id
        where per.profile_id = #{profileId}
          and per.id = #{refundId};
    </select>
    <select id="getCheckInByRefundVchcode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInEntity">
        select *
        from pl_eshop_refund_receive_checkin
        where profile_id = #{profileId}
          and refund_order_id = #{refundOrderId};
    </select>
    <select id="getCheckInBySalveBackVchcode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInEntity">
        select c.*
        from td_bill_relation r
                 left join pl_eshop_refund_receive_checkin c on r.source_vchcode = c.refund_order_id and r.profile_id = c.profile_id
        where r.profile_id = #{profileId}
          and r.target_vchcode = #{billVchcode}
          and r.target_vchtype = 2100
          and c.trade_refund_order_number is not null
    </select>
    <select id="OrderHasTradeOrderId" resultType="com.wsgjp.ct.sale.common.notify.entity.FreightInfo">
        select local_freight_bill_no as freightBillNo, local_freight_name as freightName, trade_order_id, otype_id, id as eshopOrderId
        from pl_eshop_sale_order
        where profile_id = #{profileId}
          and local_freight_bill_no = #{freightNo}
        limit 1
    </select>
    <select id="DeliverHasTradeOrderId" resultType="com.wsgjp.ct.sale.common.notify.entity.FreightInfo">
        select distinct tdfi.freight_billno as freightBillNo, btype.fullname as freightName, tbd.trade_order_id, tbc.otype_id
        from td_deliver_freight_info tdfi
                 join td_bill_warehouse_task tbc on tbc.profile_id = tdfi.profile_id and tbc.warehouse_task_id = tdfi.warehouse_task_id
                 join td_bill_warehouse_task_detail taskd on taskd.profile_id = tbc.profile_id and taskd.warehouse_task_id = tbc.warehouse_task_id
                 join td_bill_detail_deliver tbd on taskd.profile_id = tbd.profile_id and taskd.detail_id = tbd.detail_id
                 left join base_btype btype on btype.id = tdfi.freight_btype_id and btype.profile_id = tdfi.profile_id
        where tdfi.profile_id = #{profileId}
          and tdfi.freight_billno = #{freightNo}
          and tbc.deleted = 0
          and tbd.deleted = 0
        UNION
        SELECT DISTINCT tdfi.freight_billno AS freightBillNo, btype.fullname AS freightName, tbd.trade_order_id, tbc.otype_id
        FROM acc_deliver_freight_info tdfi
                 JOIN acc_bill_warehouse_task tbc
                      ON tbc.profile_id = tdfi.profile_id
                          AND tbc.warehouse_task_id = tdfi.warehouse_task_id
                 JOIN acc_bill_warehouse_task_detail taskd
                      ON taskd.profile_id = tbc.profile_id
                          AND taskd.warehouse_task_id = tbc.warehouse_task_id
                 JOIN acc_bill_detail_deliver tbd
                      ON taskd.profile_id = tbd.profile_id
                          AND taskd.detail_id = tbd.detail_id
                 LEFT JOIN base_btype btype
                           ON btype.id = tdfi.freight_btype_id
                               AND btype.profile_id = tdfi.profile_id
        where tdfi.profile_id = #{profileId}
          and tdfi.freight_billno = #{freightNo}
          and tbc.deleted = 0
          and tbd.deleted = 0
    </select>
    <select id="getCheckinVchcodesByMark" resultType="java.math.BigInteger" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.ReceiveCheckInParameter">
        select order_id
        from pl_eshop_order_mark
        where profile_id = #{profileId}
        <if test="filter !=null and filter.markType !=null and  filter.markType.code != 0">
            and mark_code = #{filter.markType}
        </if>
        <if test="markType !=null and markType.code != 0">
            and mark_code = #{markType}
        </if>
        and order_type = 3
    </select>
    <select id="getPtypeCategory" resultType="java.lang.Integer">
        select pcategory
        from base_ptype
        where id = #{ptypeId}
          and profile_id = #{profileId}
    </select>
    <select id="isUpdate" resultType="int"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInEntity">
        select count(0)
        from pl_eshop_refund_receive_checkin
        where profile_id = #{profileId}
          and vchcode = #{vchcode}
    </select>
    <select id="getFreightBtypeIdByBtypeName" resultType="java.math.BigInteger" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInEntity">
        select id
        from base_btype
        where fullname = #{freightName}
          and profile_id = #{profileId}
    </select>
    <select id="checkSnExist" resultType="java.lang.String">
        select snno from acc_inventory_serialno where profile_id=#{profileId} and snno in
        <foreach collection="snList" close=")" open="(" separator="," item="sn">
            #{sn}
        </foreach>
    </select>
    <select id="queryCheckInSimple"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundReceiveCheckInEntity">
        select vchcode, check_in.profile_id, rel.refund_order_id
        from pl_eshop_refund_receive_checkin check_in
                 left join pl_eshop_refund_checkin_relation rel on rel.checkin_id = check_in.vchcode and rel.profile_id = check_in.profile_id
        where check_in.vchcode = #{id}
          and check_in.profile_id = #{profileId}
    </select>

</mapper>

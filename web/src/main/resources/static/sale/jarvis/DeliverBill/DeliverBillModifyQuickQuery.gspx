<?xml version="1.0" encoding="UTF-8" ?>
<Page xmlns="Craba.UI" Title="编辑自定义查询"
      ActionType="sale.jarvis.DeliverBill.DeliverBillModifyQuickQueryAction,
      sale/jarvis/DeliverBill/DeliverBillModifyQuickQuery.js,
      sale/jarvis/DeliverBill/common/CommonJs.js,
      sale/jarvis/DeliverBill/template/TemplateManagement.js">

    <FlexColumn CssStyle="padding:0;display:flex;flex-direction:column;">
        <FlexColumn Business='{"Name":"shell.dynamicNavMenu","ids":"orgId111"}'>
            <FlowPanel LayoutDirection="Vert" CssClass='EShopPage oauto FlexShrink1 VertItem margin0 pd10'
                       ID="customerQueryPanel">
                <HBlock>
                    <Label Text='名称：'/>
                    <TextEdit ID="customerQueryName" MaxLength="60" Width="148"/>
                </HBlock>
                <HBlock>
                    <Label Text='分类：'/>
                    <DropDownEdit  ID="categoryCode" Width="148" CssClass='FlexAuto' DropDownStyle="DropDownList"
                                   DataField="categoryCode" ListItems="${categoryCode}" SelectedIndex="3"/>
                </HBlock>
                <HBlock>
                    <Label Text='显示：'/>
                    <DropDownEdit ID="isDisplay" Width="148" CssClass='FlexAuto' DropDownStyle="DropDownList"
                                  DataSource="${isDisplay}" DataTextField="description" DataValueField="key"
                                  DataField="isDisplay" SelectedIndex="0"/>
                </HBlock>
                <HBlock>
                    <Label Text='公用：'/>
                    <DropDownEdit ID="isPublic" Width="148" CssClass='FlexAuto' DropDownStyle="DropDownList"
                                  DataSource="${isPublic}" DataTextField="description" DataValueField="key"
                                  DataField="isPublic" SelectedIndex="0"/>
                </HBlock>

                <Label Text="时间类型:"/>
                <DropDownEdit ReportVisible="false" ID="timeType" SelectedIndex="0"
                              DataSource="${queryTimeType}" CssClass='FlexAuto' DropDownStyle="DropDownList"
                              DataTextField="description" DataValueField="key" DataField="timeType"
                              OuterCssClass='border-bottom0' ItemCssClass='mbf0'/>
                <!--            <DropDownEdit ID="filterType" Label="批量:" SelectedIndex="0" CssClass='FlexAuto'-->
                <!--                          DataSource="${filterType}" DataField="filterType" OnChange="filterTypeChange"-->
                <!--                          DataTextField="description" DataValueField="key" ReportField="过滤条件"-->
                <!--                          OuterCssClass='border-bottom0' ItemCssClass='mbf0'/>-->
                <DropDownCheckBoxList ReportVisible="false" Label="网店" ID="orgId"
                                      DropDownStyle="DropDownSearch" CssClass="FlexAuto" MaxWidth="0"
                                      DataSource="${eshops}" DataField="eshops" ValueType="String"
                                      DataTextField="fullname" DataValueField="id"
                                      LayoutDirection="Vert" SelectedAll = "true"/>
                <DropDownCheckBoxList ReportVisible="false" Label="发货仓库" DropDownStyle="DropDownSearch" CssClass='FlexAuto'
                                      ID="ktype" MaxWidth="0"
                                      DataSource="${ktypes}" DataField="ktypes" ValueType="String"
                                      DataTextField="fullname" DataValueField="id" LayoutDirection="Vert"/>

                <SelectorEdit ReportVisible="false" Label="收货地址" Width="134" Business="selected"
                              SelectorHeight="600" SelectorWidth="340" OnButtonClick="areasSelectorInit"
                              OnSelectorSelected="selectArea" OnChange="clearArea" CssClass='FlexAuto'
                              DisplayField="areasInfo" DataField="areas" ID="areas"/>

                <NavBar>
                    <NavGroup Text="物流信息" Expanded="false">
                        <FlowPanel LayoutDirection='Vert' CssClass='VertItem margin0'>
                            <Label Text="物流公司:"/>
                            <DropDownCheckBoxList ReportVisible="false" ReportField="物流公司" DropDownStyle="DropDownSearch"
                                                  CssClass='FlexAuto'
                                                  ID="logistic" DataField="freights" ValueType='String'
                                                  DataSource="${freights}" Width="148"
                                                  DataTextField="fullName" DataValueField="id"
                                                  LayoutDirection="Vert"/>
                            <Label Text="物流服务模板:"/>
                            <DropDownCheckBoxList ReportVisible="false" ReportField="物流服务模板" ID="freightTemplate"
                                                  CssClass='FlexAuto'
                                                  DropDownStyle="DropDownSearch" DataField="freightTemplate"
                                                  ValueType="String" Width="148"
                                                  DataSource="${freightTemplates}" DataValueField="id"
                                                  DataTextField="templateName"
                                                  LayoutDirection="Vert"/>
                        </FlowPanel>
                    </NavGroup>
                </NavBar>

                <NavBar>
                    <NavGroup Text="商品信息" Expanded="false" GroupName="GroupB">
                        <FlowPanel LayoutDirection='Vert' CssClass='VertItem margin0'>
                            <DropDownEdit ID="ptypeType" Width="105" DataField="ptypeType"
                                          SelectedIndex="0" DropDownStyle="DropDownList"
                                          OnChange="doChangePtypeSelectType"
                            >
                                <ListItem Value="0" Text="按商品"/>
                                <ListItem Value="1" Text="按商品标签"/>
                                <ListItem Value="2" Text="按商品分类"/>
                            </DropDownEdit>
                            <DropDownEdit ReportVisible="false"  SelectedIndex="0" Width="105"
                                          ID="ptypeFilterType"
                                          DataField="ptypeFilterType" DropDownStyle="DropDownList"
                                          CssClass="FlexAuto">
                                <ListItem Value="0" Text="包含"/>
                                <ListItem Value="1" Text="不包含"/>
                                <ListItem Value="2" Text="仅包含"/>
                            </DropDownEdit>
                            <SelectorEdit ReportVisible="false" ReportField="商品信息" ID="ptypeFullname"
                                          DataField="skuIds"
                                          Business="{'Name':'jxc.ptypeColumn', 'selectType':'Sku','showOnlyBaseunit':'false'}" ShowMDI='true'
                                          DisplayField="ptypeNames" OnEnterPress="openPtypeSelect"
                                          OnButtonClick="openPtypeSelect" CssClass="FlexAuto"
                                          OnSelectorSelected="selectPtype" AutoFocusNext="false"
                            />
                            <DropDownCheckBoxList ReportField="商品标签" ID="ptypeLabel" DataField="ptypeLabelIds"
                                                  DropDownStyle="DropDownSearch"
                                                  Width="148" ValueType="String"
                                                  DataSource="${ptypeLabels}"
                                                  DataTextField="fullname" DataValueField="id"
                                                  LayoutDirection="Vert"/>
                            <SelectorEdit ReportField="商品分类" ID="ptypeClass" DataField="ptypeClassIds"
                                          SelectorPage="/jxc/baseinfo/selector/ClassSelector.gspx"
                                          ShowMDI='true'
                                          DisplayField="classNames" OnEnterPress="openPtypeClassSelect"
                                          OnButtonClick="openPtypeClassSelect" CssClass="FlexAuto"
                                          OnSelectorSelected="selectPtypeClass" AutoFocusNext="false"
                            />
                        </FlowPanel>
                        <HiddenField ID="ptypeids"/>
                        <VSpacer Height="5"/>
                        <FlowPanel LayoutDirection='Vert' CssClass='VertItem margin0' ShowBorder="true">
                            <CheckBox ReportVisible="false" Text="包含赠品" ID="containGift" DataField="containGift" Visible="false"/>
                            <Label Text="商品种类:"/>
                            <HPanel>
                                <NumberEdit ReportVisible="false" MaxValue="100000000" Width="60" ID="minType"
                                            ReportField="商品种类最小值"
                                            DecimalScale="2" DataField="minType" NumberType="PositiveFloat"/>
                                <Label Text="~"/>
                                <NumberEdit ReportVisible="false" MaxValue="100000000" Width="60" ID="maxType"
                                            ReportField="商品种类最大值"
                                            DecimalScale="2" DataField="maxType" NumberType="PositiveFloat"/>
                            </HPanel>
                            <Label Text="商品数量:"/>
                            <HPanel>
                                <NumberEdit ReportVisible="false" MaxValue="100000000" Width="60" ID="minCount"
                                            ReportField="商品数量最小值"
                                            DecimalScale="2" DataField="minCount" NumberType="PositiveFloat"/>
                                <Label Text="~"/>
                                <NumberEdit ReportVisible="false" MaxValue="100000000" Width="60" ID="maxCount"
                                            ReportField="商品数量最大值"
                                            DecimalScale="2" DataField="maxCount" NumberType="PositiveFloat"/>
                            </HPanel>
                        </FlowPanel>

                        <FlowPanel LayoutDirection='Vert' CssClass='VertItem margin0' ShowBorder="false">
                            <Label Text="预估重量(kg):"/>
                            <HPanel>
                                <NumberEdit ReportVisible="false" MaxValue="100000000" Width="63" ID="minWeight"
                                            ReportField="商品估重最小值"
                                            DecimalScale="8" DataField="minWeight" NumberType="PositiveFloat"/>
                                <Label Text="~"/>
                                <NumberEdit ReportVisible="false" MaxValue="100000000" Width="63" ID="maxWeight"
                                            ReportField="商品估重最大值"
                                            DecimalScale="8" DataField="maxWeight" NumberType="PositiveFloat"/>
                            </HPanel>
                            <Label Text="称重重量(kg):" Visible="${pageVisibleControl.queryInputWeight}"/>
                            <HPanel Visible="${pageVisibleControl.queryInputWeight}">
                                <NumberEdit ReportVisible="false" MaxValue="100000000" Width="63"
                                            ID="minInputWeight"
                                            ReportField="商品称重重最小值"
                                            DecimalScale="8" DataField="minInputWeight"
                                            NumberType="PositiveFloat"/>
                                <Label Text="~"/>
                                <NumberEdit ReportVisible="false" MaxValue="100000000" Width="63"
                                            ID="maxInputWeight"
                                            ReportField="商品称重最大值"
                                            DecimalScale="8" DataField="maxInputWeight"
                                            NumberType="PositiveFloat"/>
                            </HPanel>
                            <Label Text="预估体积(cm³):"/>
                            <HPanel>
                                <NumberEdit ReportVisible="false" MaxValue="100000000" Width="63" ID="minVolumn"
                                            ReportField="预估体积最小值"
                                            DecimalScale="3" DataField="minVolumn" NumberType="PositiveFloat"/>
                                <Label Text="~"/>
                                <NumberEdit ReportVisible="false" MaxValue="100000000" Width="63" ID="maxVolumn"
                                            ReportField="预估体积最大值"
                                            DecimalScale="3" DataField="maxVolumn" NumberType="PositiveFloat"/>
                            </HPanel>
                        </FlowPanel>
                    </NavGroup>
                </NavBar>

                <NavBar>
                    <NavGroup Text="单据信息" Align="Left" Expanded="false" GroupName="GroupE">
                        <FlowPanel LayoutDirection='Vert' CssClass='VertItem margin0 pd0'>
                            <VPanel>
                                <Label Text="创建方式:"/>
                                <DropDownCheckBoxList ReportVisible="false" ReportField="创建方式" ID="createType"
                                                      DropDownStyle="DropDownSearch"
                                                      Width="148" DataField="createType" ValueType="String"
                                                      DataSource="${createType}"
                                                      DataTextField="description" DataValueField="key"
                                                      LayoutDirection="Vert" CssClass='FlexAuto'/>
                                <Label Text="订单类型:" Visible="false"/>
                                <DropDownCheckBoxList ReportVisible="false" ReportField="订单类型" ID="orderSourceType"
                                                      Visible="false"
                                                      DropDownStyle="DropDownSearch"
                                                      Width="148" DataField="orderSourceType" ValueType="String"
                                                      DataSource="${orderSourceType}" DataTextField="description"
                                                      DataValueField="key" LayoutDirection="Vert" CssClass='FlexAuto'/>
                                <Label Text="驳回原因:" Visible="${pageVisibleControl.queryByReturnReason}"/>
                                <DropDownCheckBoxList ReportField="驳回原因" ID="returnReason" Visible="${pageVisibleControl.queryByReturnReason}"
                                                      DropDownStyle="DropDownSearch"
                                                      Width="148" DataField="returnReason" ValueType="String"
                                                      DataTextField="reason"
                                                      DataValueField="id" LayoutDirection="Vert" CssClass='FlexAuto'/>
                                <Label Text="截停说明:" Visible="${pageVisibleControl.queryByLockReason}"/>
                                <DropDownCheckBoxList ReportField="截停说明" ID="lockReason" Visible="${pageVisibleControl.queryByLockReason}"
                                                      DropDownStyle="DropDownSearch"
                                                      Width="148" DataField="lockReason" ValueType="String"
                                                      DataTextField="reason"
                                                      DataValueField="id" LayoutDirection="Vert" CssClass='FlexAuto'/>
                                <Label Text="单据类型:" Visible="false"/>
                                <DropDownCheckBoxList ReportVisible="false" ReportField="单据类型" ID="billType"
                                                      DropDownStyle="DropDownSearch"
                                                      Width="148" DataField="billType" ValueType="String"
                                                      DataSource="${billType}" DataTextField="description"
                                                      DataValueField="key" LayoutDirection="Vert"
                                                      Visible="false" CssClass='FlexAuto'/>
                                <Label Text="业务类型:" Visible="${pageVisibleControl.queryByBusinessType}"/>
                                <DropDownCheckBoxList ReportVisible="false" ReportField="业务类型" ID="businessType"
                                                      DropDownStyle="DropDownSearch"
                                                      Width="148" DataField="businessType" ValueType="String"
                                                      DataSource="${businessType}" DataTextField="description"
                                                      DataValueField="key" LayoutDirection="Vert"
                                                      Visible="${pageVisibleControl.queryByBusinessType}" CssClass='FlexAuto'/>
                                <Label Text="发货方:" Visible="${pageVisibleControl.queryByDeliverType}"/>
                                <DropDownCheckBoxList ReportVisible="false" ReportField="发货方" ID="deliverType"
                                                      DropDownStyle="DropDownSearch"
                                                      Width="148" DataField="deliverType" ValueType="String"
                                                      DataSource="${deliverType}" DataTextField="description"
                                                      DataValueField="key" LayoutDirection="Vert"
                                                      Visible="${pageVisibleControl.queryByDeliverType}" CssClass='FlexAuto'/>
                                <Label Text="交货方式:" Visible="${pageVisibleControl.isShowDeliveryMode}"/>
                                <DropDownCheckBoxList ReportField="交货方式" ID="selfDeliveryMode"
                                                      DropDownStyle="DropDownSearch"
                                                      Width="148" DataField="selfDeliveryMode" ValueType="String"
                                                      DataSource="${selfDeliveryModeFilter}" DataTextField="description"
                                                      DataValueField="key" LayoutDirection="Vert"
                                                      Visible="${pageVisibleControl.isShowDeliveryMode}" CssClass='FlexAuto'/>
                                <Label Text="支付方式:"/>
                                <DropDownCheckBoxList ReportVisible="false" ReportField="支付方式" ID="payMethod"
                                                      DropDownStyle="DropDownSearch"
                                                      Width="148" DataField="payMethod" ValueType="String"
                                                      DataSource="${payMethodEnum}" DataTextField="description"
                                                      DataValueField="key" LayoutDirection="Vert" CssClass='FlexAuto'/>
                                <Label Text="是否修改过明细:"/>
                                <DropDownEdit ReportVisible="false" ReportField="是否修改过明细" ID="billModifyState"
                                              DropDownStyle="DropDownList"
                                              Width="148" DataField="billModifyState"
                                              ListItems="ALL=全部,MODIFY=已修改,NONE=未修改" SelectedIndex="0"/>
                                <Label Text="是否保价:" Visible="false"/>
                                <DropDownEdit ReportVisible="false" ReportField="是否保价" ID="insured"
                                              DropDownStyle="DropDown"
                                              Width="148"
                                              ListItems="-1=全部,1=是,0=否" DataField="insured" SelectedIndex="0"
                                              Visible="false" CssClass='FlexAuto'/>
                                <Label Text="是否买家指定物流:"/>
                                <DropDownEdit ReportVisible="false" ReportField="是否买家指定物流" DropDownStyle="DropDownList"
                                              ID="isSureLogistic"
                                              Width="148" DataField="isSureLogistic"
                                              ListItems="-1=全部,1=是,0=否" SelectedIndex="0" CssClass='FlexAuto'/>
                                <Label Text="经手人:"/>
                                <DropDownCheckBoxList ReportVisible="false" ReportField="经手人" ID="employee"
                                                      DropDownStyle="DropDownSearch"
                                                      Width="148" DataField="employee" ValueType="String"
                                                      DataSource="${employees}" DataTextField="fullname"
                                                      DataValueField="id" LayoutDirection="Vert" CssClass='FlexAuto'/>
                                <Label Text="${disedTaxedTotalShow}:" Visible="${pageVisibleControl.visibleBtn}"/>
                            </VPanel>
                            <HPanel Visible="${pageVisibleControl.visibleBtn}">
                                <NumberEdit ReportVisible="false" MaxValue="100000" Width="63" ID="minTotal"
                                            ReportField="折后含税金额最小值"
                                            DecimalScale="2" DataField="minTotal"/>
                                <Label Text="~"/>
                                <NumberEdit ReportVisible="false" MaxValue="100000" Width="63" ID="maxTotal"
                                            ReportField="折后含税金额最大值"
                                            DecimalScale="2" DataField="maxTotal"/>
                            </HPanel>
                            <VPanel>
                                <Label Text="发货供应商:"/>
                                <SelectorEdit  ReportField="发货供应商" Business="selected" OnButtonClick="supplierSelectorInit"
                                               OnSelectorSelected="selectSupplier" OnChange="clearSupplier" CssClass='FlexAuto'
                                               DisplayField="fullName" DataField="supplierId" ID="supplierId"/>
                            </VPanel>
                            <VPanel>
                                <Label Text="锁定可发货库存状态:" Visible="${pageVisibleControl.queryLockSendQtyState}"/>
                                <DropDownCheckBoxList ReportField="锁定可发货库存状态" ID="lockSendQtyState"
                                                      DropDownStyle="DropDownSearch"
                                                      Width="148" DataField="lockSendQtyState" ValueType="String"
                                                      DataSource="${lockSendQtyState}" DataTextField="description"
                                                      DataValueField="key" LayoutDirection="Vert"
                                                      Visible="${pageVisibleControl.queryLockSendQtyState}"/>
                                <Label Text="团长名称:"/>
                                <TextEdit ID="platformSellCommanderName" ReportField="团长名称"
                                          NullDisplayText="" DataField="platformSellCommanderName" MaxLength="200"/>
                                <Label Text="质检状态:"/>
                                <DropDownCheckBoxList ReportVisible="false" ReportField="质检状态" ID="qcResultType"
                                                      DropDownStyle="DropDownSearch"
                                                      Width="148" DataField="qcResultType" ValueType="String"
                                                      DataSource="${qcResultType}" DataTextField="description"
                                                      DataValueField="key" LayoutDirection="Vert"/>
                                <Label Text="鉴定状态:"/>
                                <DropDownCheckBoxList ReportVisible="false" ReportField="鉴定状态"  ID="identifyResultType"
                                                      DropDownStyle="DropDownSearch"
                                                      Width="148" DataField="identifyResultType" ValueType="String"
                                                      DataSource="${identifyResultType}" DataTextField="description"
                                                      DataValueField="key" LayoutDirection="Vert"/>
                                <Label Text="质检方式:"/>
                                <DropDownCheckBoxList ReportVisible="false" ReportField="质检方式"
                                                      ID="platformQualityType"
                                                      DropDownStyle="DropDownSearch"
                                                      Width="148" DataField="platformQualityType"
                                                      ValueType="String"
                                                      ListItems="NORMAL=普通,RE_INSPECTION=复检" LayoutDirection="Vert"/>
                                <Label Text="质检机构:"/>
                                <TextEdit ReportVisible="false" ReportField="质检机构"
                                          ID="platformQualityOrgName"
                                          Width="148" DataField="platformQualityOrgName"/>
                            </VPanel>
                        </FlowPanel>
                    </NavGroup>
                </NavBar>

                <NavBar>
                    <NavGroup Text="留言/备注" Align="Left" Expanded="false" GroupName="Groupc">
                        <FlowPanel LayoutDirection='Vert' CssClass='VertItem margin0'>
                            <HPanel LayoutDirection='Horz'>
                                <Label Text="买家留言:" Width="60"/>
                                <DropDownEdit ID="buyerMessage"  DataField="buyerMessage" ListItems='0=不过滤,1=无留言,2=有留言,3=不包含'
                                              Value='0' OnChange="setEditEnabled" Width="80" Tag="buyerMessageMemo" DropDownStyle="DropDownList"/>
                            </HPanel>
                            <TextEdit ReportVisible="false" ID="buyerMessageMemo" Width="148"
                                      NullDisplayText="留言内容..." MaxLength="200"
                                      Enabled="false"
                                      DataField="buyerMessageMemo" ReportField="买家留言" CssClass="FlexAuto"/>
                            <VSpacer Height="5"/>
                            <HPanel LayoutDirection='Horz'>
                                <Label Text="卖家备注:" Width="60"/>
                                <DropDownEdit ID="sellerMemo"  DataField="sellerMemo" ListItems='0=不过滤,1=无备注,2=有备注,3=不包含'
                                              Value='0' OnChange="setEditEnabled" Width="80" Tag="sellerMemoMessage" DropDownStyle="DropDownList"/>
                            </HPanel>
                            <TextEdit ReportVisible="false" ID="sellerMemoMessage" Width="148"
                                      NullDisplayText="备注内容..." MaxLength="200"
                                      Enabled="false"
                                      DataField="sellerMemoMessage" ReportField="卖家备注" CssClass="FlexAuto"/>
                            <VSpacer Height="5"/>
                            <HPanel LayoutDirection='Horz'>
                                <Label Text="系统备注:" Width="60"/>
                                <DropDownEdit ID="memo"  DataField="memo" ListItems='0=不过滤,1=无备注,2=有备注,3=不包含'
                                              Value='0' OnChange="setEditEnabled" Width="80" Tag="memoMessage" DropDownStyle="DropDownList"/>
                            </HPanel>
                            <TextEdit ReportVisible="false" ID="memoMessage" Width="148" NullDisplayText="备注内容..."
                                      Enabled="false" MaxLength="200"
                                      DataField="memoMessage" ReportField="系统备注" CssClass="FlexAuto"/>
                        </FlowPanel>
                    </NavGroup>
                </NavBar>

                <NavBar>
                    <NavGroup Text="单据状态" Align="Left" Expanded="false">
                        <FlowPanel LayoutDirection='Vert' CssClass='VertItem margin0 pd0'>
                            <Label ID="fullLinkStatusLabel" Text="系统处理状态:"
                                   Visible="${pageVisibleControl.fullLinkStatusVisible}"/>
                            <DropDownCheckBoxList ReportField="系统处理状态" ID="fullLinkStatus"
                                                  DropDownStyle="DropDownSearch" DataField="fullLinkStatus"
                                                  ValueType="String"
                                                  Width="148"
                                                  DataSource="${fullLinkStatus}"
                                                  DataTextField="description" DataValueField="code"
                                                  LayoutDirection="Vert" CssClass="FlexAuto"
                                                  Visible="${pageVisibleControl.fullLinkStatusVisible}"/>

                            <Label Text="线上交易状态:"/>
                            <DropDownCheckBoxList ReportVisible="false" ReportField="线上交易状态" ID="tradeState"
                                                  DropDownStyle="DropDownSearch" DataField="tradeState"
                                                  ValueType="String"
                                                  Width="148"
                                                  DataSource="${tradeState}"
                                                  DataTextField="description" DataValueField="key"
                                                  LayoutDirection="Vert" CssClass="FlexAuto"/>
                            <Label Text="退款状态:"/>
                            <DropDownCheckBoxList ReportVisible="false" ReportField="退款状态" ID="refundState"
                                                  DropDownStyle="DropDownSearch" DataField="refundState"
                                                  ValueType="String"
                                                  Width="148"
                                                  LayoutDirection="Vert" CssClass="FlexAuto"
                                                  DataSource="${refundState}" DataTextField="description"
                                                  DataValueField="key"/>
                            <Label Text="换/补状态:"/>
                            <DropDownCheckBoxList ReportVisible="false" ReportField="换/补状态" ID="reSendState"
                                                  DropDownStyle="DropDownSearch" DataField="reSendState"
                                                  ValueType="String"
                                                  Width="148"
                                                  LayoutDirection="Vert" CssClass="FlexAuto"
                                                  DataSource="${reSendState}" DataTextField="description"
                                                  DataValueField="key"/>
                            <Label ID="lockStateLabel" Text="系统截停状态:"/>
                            <DropDownCheckBoxList ReportField="系统截停状态" ID="lockState"
                                                  DropDownStyle="DropDownSearch" DataField="lockState"
                                                  ValueType="String"
                                                  Width="148"
                                                  LayoutDirection="Vert" CssClass="FlexAuto"
                                                  DataSource="${lockStateQuerySource}" DataTextField="description"
                                                  DataValueField="key"/>
                            <Label Text="同步状态:"
                                   Visible="${pageVisibleControl.syncState}"/>
                            <DropDownCheckBoxList ReportField="同步状态" ID="syncState"
                                                  DropDownStyle="DropDownSearch" DataField="syncState"
                                                  ValueType="String"
                                                  Width="148"
                                                  LayoutDirection="Vert" CssClass="FlexAuto"
                                                  DataSource="${syncState}" DataTextField="description"
                                                  DataValueField="key"
                                                  Visible="${pageVisibleControl.syncState}"/>
                            <Label Text="导出状态:"/>
                            <DropDownEdit ReportVisible="false" ReportField="导出状态" ID="exportState"
                                          DropDownStyle="DropDownList"
                                          Width="148" DataField="exportState" SelectedIndex="0"
                                          CssClass="FlexAuto"
                                          ListItems="-1=全部,0=未导出,1=已导出"/>
                            <Label Text="配货状态:" Visible="${pageVisibleControl.showAssignState}"/>
                            <DropDownCheckBoxList ReportVisible="false" ReportField="配货状态" DropDownStyle="DropDownSearch"
                                                  CssClass='FlexAuto'
                                                  ID="assignStates" DataField="assignStates" ValueType='String'
                                                  DataSource="${queryAssignStates}"
                                                  DataTextField="description" DataValueField="key" LayoutDirection="Vert"
                                                  Visible="${pageVisibleControl.showAssignState}"/>
                            <Label Text="系统删除状态:" Visible="${pageVisibleControl.showDeletedState}"/>
                            <DropDownCheckBoxList ReportField="系统删除状态" ID="deletedBill"
                                                  DropDownStyle="DropDownSearch" DataField="deletedBill"
                                                  ValueType="String"
                                                  Width="148"
                                                  LayoutDirection="Vert" CssClass="FlexAuto"
                                                  DataSource="${deletedTypeQuerySource}" DataTextField="description"
                                                  DataValueField="key"
                                                  Visible="${pageVisibleControl.showDeletedState}"/>
                            <Label Text="策略执行状态:" Visible="${pageVisibleControl.showStrategyMark}"/>
                            <DropDownCheckBoxList Visible="${pageVisibleControl.showStrategyMark}" ReportVisible="false"
                                                  ReportField="策略执行状态" ID="strategyMark"
                                                  DropDownStyle="DropDownSearch" DataField="strategyMark"
                                                  ValueType="String"
                                                  Width="148"
                                                  LayoutDirection="Vert" CssClass="FlexAuto"
                                                  DataSource="${strategyMark}" DataTextField="description"
                                                  DataValueField="key"/>
                            <Label Text="分单状态:" Visible="${pageVisibleControl.taoBaoDistribution&amp;&amp;pageVisibleControl.taoBaoDistributionCancel}"/>
                            <DropDownCheckBoxList ReportField="分单状态" ID="scpProxyState"
                                                  DropDownStyle="DropDownSearch" DataField="scpProxyState"
                                                  ValueType="String"
                                                  Width="148"
                                                  LayoutDirection="Vert" CssClass="FlexAuto"
                                                  DataSource="${scpProxyState}" DataTextField="description"
                                                  DataValueField="key"
                                                  Visible="${pageVisibleControl.taoBaoDistribution&amp;&amp;pageVisibleControl.taoBaoDistributionCancel}"/>
                            <Label Text="接单状态:"/>
                            <DropDownCheckBoxList ReportField="接单状态" ID="platformConfirmStatus"
                                                  DropDownStyle="DropDownSearch" DataField="platformConfirmStatus"
                                                  ValueType="String"
                                                  Width="148"
                                                  DataSource="${platformConfirmStatus}"
                                                  DataTextField="description" DataValueField="key"
                                                  LayoutDirection="Vert" CssClass="FlexAuto"/>
                        </FlowPanel>
                    </NavGroup>
                </NavBar>

                <NavBar>
                    <NavGroup Text="标记/旗帜" Align="Left" Expanded="false" GroupName="GroupF">
                        <FlowPanel LayoutDirection='Vert' CssClass='VertItem margin0 pd0'>
                            <HPanel LayoutDirection='Horz'>
                                <Label Text="标记:" Width="60"/>
                                <DropDownEdit ID="markQueryState" DataField="markQueryState" ListItems='2=包含,3=不包含,4=仅包含'
                                              Value='2' Width="80" DropDownStyle="DropDownList"/>
                            </HPanel>
                            <DropDownCheckBoxList ReportVisible="false" ReportField="标记" ID="tradeMark"
                                                  DropDownStyle="DropDownSearch" CssClass="FlexAuto"
                                                  Width="148" DataField="tradeMark" ValueType="String"
                                                  DataTextField="markName" DataValueField="id"
                                                  LayoutDirection="Vert"/>
                            <FlexBlock CssStyle="margin-bottom:5px">
                                <DropDownEdit ID="flagCondition" SelectedIndex="0" Width="40" DataField="flagCondition"
                                              DataSource="${conditionDataSource}" DataTextField="description"
                                              DataValueField="key" DropDownStyle="DropDownList"/>
                                <Label Text="旗帜:" Width="30"/>
                                <DropDownEdit ID="flagQueryState" DataField="flagQueryState" ListItems='2=包含,3=不包含,4=仅包含'
                                              Value='2' Width="60" DropDownStyle="DropDownList"/>
                            </FlexBlock>
                            <DropDownCheckBoxList ReportField="旗帜" ID="flag" DropType='Image' CssClass="FlexAuto"
                                                  DataField="flag" DataSource="${flags}"
                                                  DataValueField="key" DataTextField="value"
                                                  Width="148" DropDownStyle="DropDownSearch" />
                        </FlowPanel>
                    </NavGroup>
                </NavBar>

                <NavBar>
                    <NavGroup Text="发票信息" Align="Left" Expanded="false">
                        <FlowPanel LayoutDirection='Vert' CssClass='VertItem margin0'>
                            <Label Text="是否需要开票:"/>
                            <DropDownEdit ReportVisible="false" ReportField="是否需要开票" ID="needInvoice"
                                          DropDownStyle="DropDownList"
                                          Width="148" DataField="needInvoice" CssClass="FlexAuto"
                                          ListItems="-1=全部,1=是,0=否" SelectedIndex="0"/>
                            <Label Text="开票状态:"/>
                            <DropDownEdit ReportVisible="false" ReportField="开票状态" ID="invoiceState"
                                          DropDownStyle="DropDownList"
                                          Width="148" DataField="invoiceState" SelectedIndex="0"
                                          ListItems="-1=全部,1=已开票,0=未开票" CssClass="FlexAuto"
                            />
                            <Label Text="上传发票状态:"/>
                            <DropDownCheckBoxList ReportField="上传发票状态" ID="invoiceUploadState"
                                                  DropDownStyle="DropDownSearch" DataField="invoiceUploadState"
                                                  ValueType="String"
                                                  Width="148"
                                                  DataSource="${invoiceUploadState}"
                                                  DataTextField="description" DataValueField="key"
                                                  LayoutDirection="Vert" CssClass="FlexAuto"/>
                        </FlowPanel>
                    </NavGroup>
                </NavBar>

                <NavBar >
                    <NavGroup Text="直播" Align="Left" Expanded="false" _quickIcon="bicon-weiwaidanjuguanli">
                        <FlowPanel LayoutDirection='Vert' CssClass='VertItem margin0 pd0'>
                            <Label Text="直播场次:" Visible="true"/>
                            <DropDownCheckBoxList ReportField="直播场次" ID="liveBrodcastSession"
                                                  DropDownStyle="DropDownSearch" DataField="liveBrodcastSession"
                                                  ValueType="String"
                                                  Width="148"
                                                  DataSource="${liveBrodcastSession}"
                                                  DataTextField="platformSessionName" DataValueField="id"
                                                  LayoutDirection="Vert" CssClass="FlexAuto"
                                                  Visible="true"/>
                            <Label Text="主播名称:" Visible="true"/>
                            <TextEdit ID="platformAnchorName" CssClass="FlexAuto " ReportField="主播名称"
                                      NullDisplayText="" DataField="platformAnchorName" MaxLength="200"/>
                        </FlowPanel>
                    </NavGroup>
                </NavBar>
                <NavBar ID="printStateNav">
                    <NavGroup Text="打印状态" Align="Left" Expanded="false"
                              _quickIcon="bicon-weiwaidanjuguanli">
                        <FlowPanel LayoutDirection='Vert' CssClass='VertItem margin0 pd0'>
                            <Label Text="物流单:" Visible="true"/>
                            <DropDownEdit ReportField="物流单" ID="freightPrintState"
                                          DropDownStyle="DropDownList"
                                          Width="148" DataField="freightPrintState" SelectedIndex="0"
                                          CssClass="FlexAuto"
                                          ListItems="-1=全部,1=已打印,0=未打印,2=打印中"/>
                            <Label Text="发货单:" Visible="true"/>
                            <DropDownEdit ReportField="发货单" ID="deliverPrintState"
                                          DropDownStyle="DropDownList"
                                          Width="148" DataField="deliverPrintState" SelectedIndex="0"
                                          CssClass="FlexAuto"
                                          ListItems="-1=全部,1=已打印,0=未打印"/>
                            <Label Text="汇总单:" Visible="true"/>
                            <DropDownEdit ReportField="汇总单" ID="assginPrintState"
                                          DropDownStyle="DropDownList"
                                          Width="148" DataField="assginPrintState" SelectedIndex="0"
                                          CssClass="FlexAuto"
                                          ListItems="-1=全部,1=已打印,0=未打印"/>
                        </FlowPanel>
                    </NavGroup>
                </NavBar>

            </FlowPanel>
        </FlexColumn>
        <VSpacer Height="1"/>
        <Block CssClass='BottomPanel margin0 pd10'>
            <HBlock CssClass='dflex mb5'>
                <Button Text="保存" ID="saveBtn" CssClass='SpecialButton FlexAuto' OnClick="saveQuickQuery"/>
                <Button Text="取消" CssClass='FlexAuto' OnClick="cancelPage"/>
            </HBlock>
        </Block>
    </FlexColumn>

</Page>

package com.wsgjp.ct.sale.biz.eshoporder.entity.enums;

import bf.datasource.typehandler.CodeEnum;

public enum GatherStatusEnum implements CodeEnum {
    NO_GATHER(0, "未收款"),
    HAS_GATHER(1, "已收款"),
    GATHERED_PART(2, "部分收款");
    private int code;
    private String desc;
    GatherStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    @Override
    public int getCode() {
        return code;
    }
    @Override
    public String getName() {
        return desc;
    }
    public String getDesc() {
        return desc;
    }

}
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopOrderStrategyMapper">
    <insert id="batchInsertEshopOrderStrategy">
        INSERT INTO td_deliver_strategy (`id`, `strategy_name`, `strategy_type`, 
         `strategy_sort`,`strategy_status`,`parent_id`, `profile_id`, `create_time`,
        `update_time`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.strategyName}, #{item.strategyType},
             #{item.strategySort}, #{item.strategyStatus}, #{item.parentId}, #{item.profileId},#{item.createTime},
             #{item.updateTime})
        </foreach>
    </insert>

    <insert id="batchInsertEshopOrderStrategyDetail">
        INSERT INTO td_deliver_strategy_detail (`strategy_id`,
         `profile_id`, `strategy_value`, `create_time`,`update_time`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.strategyId},
             #{item.profileId}, #{item.strategyValue}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>

    <insert id="batchInsertEshopOrderStrategyPtypeDetail">
        INSERT INTO td_deliver_strategy_ptype_detail (`id`,
         `strategy_id`, `combo_row`, `ptype_id`,
        `sku_id`,`unit_id`, `profile_id`, `contain`,
        `qty`, `create_time`, `update_time`,
        `data_type`, `qty_compare_type`, `max_qty`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id},
             #{item.strategyId}, #{item.comboRow}, #{item.ptypeId},
             #{item.skuId}, #{item.unitId}, #{item.profileId},#{item.contain},
             #{item.qty}, #{item.createTime}, #{item.updateTime},
            #{item.dataType}, #{item.qtyCompareType}, #{item.maxQty})
        </foreach>
    </insert>

    <select id="getEshopOrderStrategyList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.strategy.EshopOrderStrategy">
        SELECT * FROM td_deliver_strategy
                 WHERE profile_id=#{profileId} AND strategy_type=#{strategyType} AND strategy_status=#{strategyStatus}
    </select>

    <select id="getEshopOrderStrategyDetail"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.strategy.EshopOrderStrategyDetail">
        SELECT * FROM td_deliver_strategy_detail
        WHERE profile_id=#{profileId} AND strategy_id=#{strategyId}
    </select>

    <select id="getEshopOrderStrategyPtypeDetail"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.strategy.EshopOrderStrategyPtypeDetail">
        SELECT * FROM td_deliver_strategy_ptype_detail
        WHERE profile_id=#{profileId} AND strategy_id=#{strategyId}
    </select>

    <delete id="deleteEshopOrderStrategy">
        DELETE FROM td_deliver_strategy
               WHERE profile_id = #{profileId}
                    AND id = #{strategyId}
    </delete>

    <delete id="deleteEshopOrderStrategyDetail">
        DELETE FROM td_deliver_strategy_detail
            WHERE profile_id = #{profileId}
            AND strategy_id in
        <foreach collection="strategyIdList" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteEshopOrderStrategyPtypeDetail">
        DELETE FROM td_deliver_strategy_ptype_detail
            WHERE profile_id = #{profileId}
                AND strategy_id in
                <foreach collection="strategyIdList" item="item" close=")" open="(" separator=",">
                    #{item}
                </foreach>
    </delete>

</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.BillColumnNameTemplateMapper">
    <select id="querytemplateName" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.ColumNameModelEntity">
        select id,template_name
        from pl_eshop_bill_column_name_template
        where profile_id= #{profileId}
    </select>
    <select id="queryColumNameModel"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.ColumNameModelEntity">
        select id,
               profile_id,
               template_name,
               "网店" as TeshopName,
               eshop_name,
               "流水号"  as TnumberName,
               number_name,
               "订单编号" as TtradeNumberName,
               trade_number_name,
               "账单时间" as TtradeTimeName,
               trade_time_name,
               "收入金额" as TincomingAmountName,
               incoming_amount_name,
               "支出金额" as TspentAmountName,
               spent_amount_name,
               "交易项目" as TsaleItemName,
               sale_item_name,
               "账单备注" as TtradeMemoName,
               trade_memo_name
        from pl_eshop_bill_column_name_template
        where profile_id = #{profileId}
          and id = #{id}
    </select>

    <select id="queryModelByColumName"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.ColumNameModelEntity">
        select id,
               profile_id,
               template_name,
               "网店" as TeshopName,
               eshop_name,
               "流水号"  as TnumberName,
               number_name,
               "订单编号" as TtradeNumberName,
               trade_number_name,
               "账单时间" as TtradeTimeName,
               trade_time_name,
               "收入金额" as TincomingAmountName,
               incoming_amount_name,
               "支出金额" as TspentAmountName,
               spent_amount_name,
               "交易项目" as TsaleItemName,
               sale_item_name,
               "账单备注" as TtradeMemoName,
               trade_memo_name
        from pl_eshop_bill_column_name_template
        where profile_id = #{profileId}
          and template_name = #{templateName} limit 1
    </select>

    <select id="queryColumNameModelSingle"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.ColumNameModelEntity">
        select id,
               profile_id,
               template_name,
               "网店" as TeshopName,
               eshop_name,
               "流水号"  as TnumberName,
               number_name,
               "订单编号" as TtradeNumberName,
               trade_number_name,
               "账单时间" as TtradeTimeName,
               trade_time_name,
               "收入金额" as TincomingAmountName,
               incoming_amount_name,
               "支出金额" as TspentAmountName,
               spent_amount_name,
               "交易项目" as TsaleItemName,
               sale_item_name,
               "账单备注" as TtradeMemoName,
               trade_memo_name
        from pl_eshop_bill_column_name_template
        where profile_id = #{profileId}
          and id = #{id} limit 1
    </select>
    <insert id="createColumNameModel">
        INSERT INTO pl_eshop_bill_column_name_template (id, profile_id, template_name, eshop_name, number_name,
                                                        trade_number_name, trade_time_name, incoming_amount_name,
                                                        spent_amount_name, sale_item_name, trade_memo_name)
            value (#{id}, #{profileId}, #{templateName}, #{eshopName}, #{numberName}, #{tradeNumberName}, #{tradeTimeName}, #{incomingAmountName}, #{spentAmountName}, #{saleItemName}, #{tradeMemoName})
    </insert>
    <delete id="DeleteColumNameModel">
        delete
        from pl_eshop_bill_column_name_template
        where profile_id= #{profileid} and id=#{id}
    </delete>
</mapper>
package com.wsgjp.ct.sale.tool.submit;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.api.recordsheet.RecordSheetApi;
import com.wsgjp.ct.sale.biz.common.BaseInfoCacheUtil;
import com.wsgjp.ct.sale.biz.eshoporder.config.EshopOrderToolConfig;
import com.wsgjp.ct.sale.biz.eshoporder.config.sysdata.AutoSubmitConfig;
import com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceSubmitConfig;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.OrderSyncType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Otype;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.LogLevelEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.QueryOrderTimeType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.OrderRelationOperation;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryAdvanceOrderParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryEShopParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryOrderParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.order.QueryRelationParam;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.OrderRelationResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.SubmitResponse;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopAdvanceSaleOrderMapper;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopBizMapper;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopPtypeReationMapper;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopSaleOrderMapper;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopService;
import com.wsgjp.ct.sale.biz.eshoporder.service.middleground.EshopMiddleGroundService;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopAdvanceSaleOrderService;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopSaleOrderService;
import com.wsgjp.ct.sale.biz.eshoporder.submit.impl.EshopAdvanceOrderAutoSubmit;
import com.wsgjp.ct.sale.biz.eshoporder.submit.impl.EshopSaleOrderAutoSubmit;
import com.wsgjp.ct.sale.biz.eshoporder.util.CommonUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.SysDataUtil;
import com.wsgjp.ct.sale.biz.jarvis.utils.BillDeliverUtils;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.entity.dto.MqTopicConstant;
import com.wsgjp.ct.sale.common.entity.order.SubmitMessage;
import com.wsgjp.ct.sale.monitor.MonitorService;
import com.wsgjp.ct.sale.monitor.entity.MonitorTypeEnum;
import com.wsgjp.ct.sale.monitor.entity.VeidooConst;
import com.wsgjp.ct.sale.platform.enums.TradeTypeEnum;
import com.wsgjp.ct.sale.sdk.stock.biz.StockChangeService;
import com.wsgjp.ct.sale.sdk.stock.enums.StockChangeTypeEnum;
import com.wsgjp.ct.sale.sdk.stock.parameter.StockChangeQueueDto;
import com.wsgjp.ct.sale.tool.platform.AutoUploadService;
import com.wsgjp.ct.sale.tool.platform.TaoBaoSyncService;
import com.wsgjp.ct.sale.tool.syncorder.biz.DownloadOrderPreFeature;
import com.wsgjp.ct.sale.tool.syncorder.biz.ModificationOrderFeature;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.global.GlobalConfig;
import com.wsgjp.ct.support.mq.SysMqSend;
import ngp.mq.MqSendResult;
import ngp.service.component.job.BaseProfileJob;
import ngp.starter.web.base.GeneralResult;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2020-02-20
 */
@Service
@ConditionalOnProperty(value = "eshoporder-tool-submit.enabled", havingValue = "true")
public class SubmitOrder extends BaseProfileJob<SubmitMessage> {
    private static final Logger logger = LoggerFactory.getLogger(SubmitOrder.class);

    private static final int MAX_LEN = 1000;
    private static final int SUCCESS_CODE = 200;
    private final EshopPtypeReationMapper mapper;
    private final EshopBizMapper eshopBizMapper;
    private final EshopOrderToolConfig toolConfig;
    private final BaseInfoCacheUtil cacheUtil;
    private final EshopMiddleGroundService middleGroundService;
    private final TaoBaoSyncService taoBaoSyncService;
    private final AutoUploadService uploadService;
    private final ModificationOrderFeature modificationOrderFeature;
    private final DownloadOrderPreFeature preFeature;
    private final EshopService eshopService;
    private final SysDataUtil sysDatautil;
    private final EshopAdvanceSaleOrderMapper advanceSaleOrderMapper;
    private final StockChangeService stockChangeService;
    private final AfterMarkCalculate afterMarkCalculate;




    public SubmitOrder(EshopPtypeReationMapper mapper, EshopBizMapper eshopBizMapper, EshopOrderToolConfig toolConfig,
                       BaseInfoCacheUtil cacheUtil, EshopMiddleGroundService middleGroundService, TaoBaoSyncService taoBaoSyncService,
                       AutoUploadService uploadService, ModificationOrderFeature modificationOrderFeature, DownloadOrderPreFeature preFeature,
                       EshopService eshopService, SysDataUtil sysDatautil, EshopAdvanceSaleOrderMapper advanceSaleOrderMapper, StockChangeService stockChangeService, AfterMarkCalculate afterMarkCalculate) {
        this.mapper = mapper;
        this.eshopBizMapper = eshopBizMapper;
        this.toolConfig = toolConfig;
        this.cacheUtil = cacheUtil;
        this.middleGroundService = middleGroundService;
        this.uploadService = uploadService;
        this.taoBaoSyncService = taoBaoSyncService;
        this.modificationOrderFeature = modificationOrderFeature;
        this.preFeature = preFeature;
        this.eshopService = eshopService;
        this.sysDatautil = sysDatautil;
        this.advanceSaleOrderMapper = advanceSaleOrderMapper;
        this.stockChangeService = stockChangeService;
        this.afterMarkCalculate = afterMarkCalculate;
    }

    @Override
    public boolean execute(String messageId, long l, SubmitMessage message) {
        String profileId = message.getProfileId();
        BigInteger profileIdLong = new BigInteger(profileId);
        try {
            afterMarkCalculate.reportRefundRecords(profileIdLong);
        } catch (Exception e) {
            logger.error("售后处理超时标记异常", e);
        }
        try {
            uploadService.doUpLoadAlibabaOrderChanelInfo(profileIdLong);
            notifyPurchaseOrderRelation(profileId);
            AutoSubmitConfig config = GlobalConfig.getUserConfig(AutoSubmitConfig.class);
            MonitorService monitorService = GetBeanUtil.getBean(MonitorService.class);
            //平台订单提交
            QueryOrderParameter parameter = buildParameter(config,message);
            EshopSaleOrderAutoSubmit orderSubmit = new EshopSaleOrderAutoSubmit(null, parameter);
            //配置了不自动提交，则直接过滤
            if (!sysDatautil.isNotNeedAutoSubmit(profileIdLong)) {
                GeneralResult<SubmitResponse> orderResponse = orderSubmit.submit(null);
                if (orderResponse.getCode() != 200) {
                    logger.error(String.format("messageId:%s,profileId:%s,订单提交失败:%s", messageId, profileId, orderResponse.getMessage()));
                }
                //写监控数据
                writeSaleorderSubmitMonitor(monitorService, orderResponse);
                // 预售单提交
                QueryAdvanceOrderParameter advanceParameter = buildAdvanceParameter(config,message);
                EshopAdvanceOrderAutoSubmit advanceSubmit = new EshopAdvanceOrderAutoSubmit(null, advanceParameter);
                GeneralResult<SubmitResponse> advanceResponse = advanceSubmit.submit(null);
                if (advanceResponse.getCode() != 200) {
                    logger.error(String.format("messageId:%s,profileId:%s,预售订单提交失败:%s", messageId, profileId, advanceResponse.getMessage()));
                }
                logger.info(String.format("messageId:%s,profileId:%s message:提交任务运行完毕", messageId, profileId));
            }
            //处理其他任务
            doBaseInfoUpload(profileId);
            // 处理需要更改交易状态调用接口判断
            douUpdateOrderStatus(profileId);
            taoBaoSyncService.doUpload(profileIdLong);
            //已提交QIC订单获取QIC码
            doGetSubmitedQicOrderCode(profileId);
        } catch (Exception ex) {
            logger.error(String.format("messageId:%s,profileId:%s,提交任务执行失败:%s", messageId, profileId, ex.getMessage()), ex);
            logger.error(String.format("%s,autoSubmit consume error:%s", profileId, messageId), ex);
        }
        return true;
    }


    private void doGetSubmitedQicOrderCode(String profileId) {
        if (!toolConfig.isEnableSubmitedQicOrder()) {
            return;
        }
        Calendar rightNow = Calendar.getInstance();
        rightNow.setTime(new Date());
        rightNow.add(Calendar.DAY_OF_MONTH, -toolConfig.getGetSubmitedQicOrderDay());
        List<EshopSaleOrderEntity> submitedQicOrder = GetBeanUtil.getBean(EshopSaleOrderMapper.class).getSubmitedQicOrder(new BigInteger(profileId), rightNow.getTime());
        if (CollectionUtils.isEmpty(submitedQicOrder)) {
            return;
        }
        EshopSaleOrderService orderService = GetBeanUtil.getBean(EshopSaleOrderService.class);
        for (EshopSaleOrderEntity order : submitedQicOrder) {
            orderService.doSendTmc(order);
        }
    }

    private void doQtyOccupyCycleOrder(EshopAdvanceSubmitConfig advanceSubmitConfig) {
        CommonUtil.doLogByEnable(logger, LogLevelEnum.ERROR,String.format("profileId:%s,qtyOccupyCycleOrder:%s",CurrentUser.getProfileId(),JsonUtils.toJson(advanceSubmitConfig)));
        if (null == advanceSubmitConfig || !advanceSubmitConfig.isCyclePurchaseQtyChecked() || advanceSubmitConfig.getQtyDay() < 0){
            return;
        }
        QueryAdvanceOrderParameter param = new QueryAdvanceOrderParameter();
        param.setAdvanceTimeType(0 == advanceSubmitConfig.getQtyTimeType() ? 3 : 0);
        Calendar rightNow = Calendar.getInstance();
        rightNow.setTime(new Date());
        rightNow.add(Calendar.DAY_OF_MONTH, advanceSubmitConfig.getQtyDay());
        param.setEndTime(rightNow.getTime());
        param.setBeginTime(new Date());
        param.setQueryCyclePurchaseType(1);
        List<EshopAdvanceOrderEntity> cyclePurchaseOrders = advanceSaleOrderMapper.queryAllAdvanceOrderFields(param);
        if (CollectionUtils.isEmpty(cyclePurchaseOrders)) {
            return;
        }
        List<StockChangeQueueDto> list = new ArrayList<>();
        for (EshopAdvanceOrderEntity cyclePurchaseOrder : cyclePurchaseOrders) {
            StockChangeQueueDto stockChangeQueueDto = new StockChangeQueueDto();
            stockChangeQueueDto.setProfileId(CurrentUser.getProfileId());
            stockChangeQueueDto.setSourceId(cyclePurchaseOrder.getVchcode());
            stockChangeQueueDto.setSourceType(StockChangeTypeEnum.ADVANCE_ORDER);
            stockChangeQueueDto.setSourceOperation("周期购订单自动占用库存");
            list.add(stockChangeQueueDto);
        }
        stockChangeService.batchInsertChange(list);
    }

    private void douUpdateOrderStatus(String messageProfileId) {
        try {
            BigInteger profileId = new BigInteger(messageProfileId);
            List<Otype> otypeList = cacheUtil.getAllOtype(profileId);
            if (CollectionUtils.isEmpty(otypeList)) {
                return;
            }
            modificationOrderFeature.doExecute(otypeList);
        } catch (Exception e) {
            logger.error("账套{}执行修改订单状态报错{}", messageProfileId, e.getMessage(), e);
        }

    }


    private void writeSaleorderSubmitMonitor(MonitorService monitorService, GeneralResult<SubmitResponse> orderResponse) {
        if (orderResponse.getData() == null || orderResponse.getData().getMap() == null) {
            return;
        }
        HashMap<String, Object> map = orderResponse.getData().getMap();
        //  待提交订单数
        if (map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_MANUAL_TP_WAIT_COUNT.getTopic()) != null) {
            monitorService.recordSum(MonitorTypeEnum.PL_BS_ORDER_COMMIT_AUTO_TP_WAIT_COUNT.getTopic(), VeidooConst.ORDER_TYPE, "ALL", (Integer) map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_MANUAL_TP_WAIT_COUNT.getTopic()));
        }
        //  提交预售单平均耗时
        if (map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_PRESALE_MANUAL_TP_TIME.getTopic()) != null) {
            monitorService.recordTP(MonitorTypeEnum.PL_BS_ORDER_COMMIT_PRESALE_AUTO_TP_TIME.getTopic(), VeidooConst.ORDER_TYPE, TradeTypeEnum.ADVANCE_FORWARD_SALE.getName(), (Long) map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_PRESALE_MANUAL_TP_TIME.getTopic()));
        }
        //  提交预售单QPS
        if (map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_PRESALE_MANUAL_QPS.getTopic()) != null) {
            monitorService.recordOPSSSuccess(MonitorTypeEnum.PL_BS_ORDER_COMMIT_PRESALE_AUTO_QPS.getTopic(), VeidooConst.ORDER_TYPE, TradeTypeEnum.ADVANCE_FORWARD_SALE.getName(), (Integer) map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_PRESALE_MANUAL_QPS.getTopic()));
        }
        //  提交预售单异常QPS
        if (map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_PRESALE_MANUAL_ERROR_QPS.getTopic()) != null) {
            monitorService.recordOPSSFail(MonitorTypeEnum.PL_BS_ORDER_COMMIT_PRESALE_AUTO_QPS.getTopic(), VeidooConst.ORDER_TYPE, TradeTypeEnum.ADVANCE_FORWARD_SALE.getName(), (Integer) map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_PRESALE_MANUAL_ERROR_QPS.getTopic()));
        }
        //  提交交易单平均耗时
        if (map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_DELIVERBILL_MANUAL_TP_TIME.getTopic()) != null) {
            monitorService.recordTP(MonitorTypeEnum.PL_BS_ORDER_COMMIT_DELIVERBILL_AUTO_TP_TIME.getTopic(), VeidooConst.ORDER_TYPE, TradeTypeEnum.NORMAL.getName(), (Long) map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_DELIVERBILL_MANUAL_TP_TIME.getTopic()));
        }
        //  提交交易单QPS
        if (map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_DELIVERBILL_MANUAL_QPS.getTopic()) != null) {
            monitorService.recordOPSSSuccess(MonitorTypeEnum.PL_BS_ORDER_COMMIT_DELIVERBILL_AUTO_QPS.getTopic(), VeidooConst.ORDER_TYPE, TradeTypeEnum.NORMAL.getName(), (Integer) map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_DELIVERBILL_MANUAL_QPS.getTopic()));
        }
        // 提交交易单异常QPS
        if (map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_DELIVERBILL_MANUAL_ERROR_QPS.getTopic()) != null) {
            monitorService.recordOPSSFail(MonitorTypeEnum.PL_BS_ORDER_COMMIT_DELIVERBILL_AUTO_QPS.getTopic(), VeidooConst.ORDER_TYPE, TradeTypeEnum.NORMAL.getName(), (Integer) map.get(MonitorTypeEnum.PL_BS_ORDER_COMMIT_DELIVERBILL_MANUAL_ERROR_QPS.getTopic()));
        }
    }


    @Override
    public String name() {
        return MqTopicConstant.AUTO_SUBMIT_ORDER_TOPIC_NAME;
    }

    @Override
    public List<SubmitMessage> producer(String profileId) {
        logger.error(String.format("%s,autoSubmit produce", profileId));
        if(!BillDeliverUtils.isOpenEshopFunc()) {
            logger.info(String.format("账套【%s】【%s】TMC生产任务已关闭,无网店资产", profileId, OrderSyncType.AUTO_INCREASE.getName()));
            return new ArrayList<>();
        }
        List<SubmitMessage> profileIds = new ArrayList<>(1);
        SubmitMessage message = new SubmitMessage();
        message.setProfileId(profileId);
        profileIds.add(message);
        try {
            List<MqSendResult<SubmitMessage>> send = SysMqSend.send(profileIds, MqTopicConstant.AUTO_SUBMIT_ORDER_TOPIC_NAME);
            logger.error(String.format("%s,autoSubmit produce result:%s", profileId, JsonUtils.toJson(send)));
        } catch (Exception e) {
            logger.error(String.format("%s,autoSubmit produce error", profileId), e);
        }
        return new ArrayList<>();
    }

    private QueryOrderParameter buildParameter(AutoSubmitConfig config, SubmitMessage message) {
        QueryOrderParameter parameter = new QueryOrderParameter();
        Calendar rightNow = Calendar.getInstance();
        rightNow.setTime(new Date());
        if (0 == config.getOffsetDay()) {
            rightNow.add(Calendar.MONTH, -config.getOffsetMonth());
        } else {
            rightNow.add(Calendar.DAY_OF_MONTH, -config.getOffsetDay());
        }
        parameter.setEndTime(new Date());
        parameter.setBeginTime(rightNow.getTime());
        parameter.setTimeType(QueryOrderTimeType.DOWNLOAD_TIME);
        if (null != message && !CommonUtil.isInvalidBigInteger(message.getOtypeId())){
            parameter.setOtypeIds(Collections.singletonList(message.getOtypeId()));
        }
        return parameter;
    }

    private QueryAdvanceOrderParameter buildAdvanceParameter(AutoSubmitConfig config, SubmitMessage message) {
        QueryAdvanceOrderParameter parameter = new QueryAdvanceOrderParameter();
        Calendar rightNow = Calendar.getInstance();
        rightNow.setTime(new Date());
        if (0 == config.getAdvanceOffsetDay()) {
            rightNow.add(Calendar.MONTH, -config.getAdvanceOffsetMonth());
        } else {
            rightNow.add(Calendar.DAY_OF_MONTH, -config.getAdvanceOffsetDay());
        }
        parameter.setEndTime(new Date());
        parameter.setBeginTime(rightNow.getTime());
        parameter.setAdvanceTimeType(2);
        if (null != message && !CommonUtil.isInvalidBigInteger(message.getOtypeId())){
            parameter.setOtypeIds(Collections.singletonList(message.getOtypeId()));
        }
        return buildAutoSubmitAdvanceParam(parameter);
    }

    private QueryAdvanceOrderParameter buildAutoSubmitAdvanceParam(QueryAdvanceOrderParameter parameter) {
        EshopAdvanceSubmitConfig advanceSubmitConfig = GetBeanUtil.getBean(EshopAdvanceSaleOrderService.class).getAdvanceSubmitConfig();
        //周期购订单自动占用库存
        doQtyOccupyCycleOrder(advanceSubmitConfig);
        if (null == advanceSubmitConfig || null == parameter){
            return parameter;
        }
        boolean advanceSubmitChecked = advanceSubmitConfig.isAdvanceSubmitChecked();
        if (!advanceSubmitChecked) {
            return parameter;
        }
        int advanceSubmitDay = advanceSubmitConfig.getAdvanceSubmitDay();
        if (advanceSubmitDay < 0) {
            return parameter;
        }
        //预计发货时间
        parameter.setAdvanceTimeType(3);
        Calendar rightNow = Calendar.getInstance();
        rightNow.setTime(new Date());
        rightNow.add(Calendar.DAY_OF_MONTH, advanceSubmitDay);
        parameter.setEndTime(rightNow.getTime());
        return parameter;
    }

    /**
     * 临时功能：自动通知元气订单进行自动对应
     *
     * @param profileId 账套id
     */
    private void notifyPurchaseOrderRelation(String profileId) {
        if (!toolConfig.getNotifyPurchaseOrderRelationEnabled()) {
            return;
        }
        BigInteger pid = new BigInteger(profileId);
        List<EshopInfo> eshopInfos = queryYuanQiShopIdList();
        if (CollectionUtils.isEmpty(eshopInfos)) {
            return;
        }
        logger.info("账套{}开始执行通知元气订单对应操作,一共{}个网店需要通知", profileId, eshopInfos.size());
        for (EshopInfo eshop : eshopInfos) {
            String key = String.format("YuanQiNotifyOrderRelation_%s", eshop.getFullname());
            String val = GlobalConfig.get(key);
            if (StringUtils.isNotEmpty(val)) {
                continue;
            }
            GlobalConfig.put(key, DateUtils.formatDate(new Date()), "通知元气订单自动对应");
            doNotify(pid, eshop.getOtypeId());
            logger.info("账套{}网店{}通知元气订单对应完成", profileId, eshop.getFullname());
        }
    }

    private void doNotify(BigInteger profileId, BigInteger eshopId) {
        try {
            RecordSheetApi api = GetBeanUtil.getBean(RecordSheetApi.class);
            QueryRelationParam param = new QueryRelationParam();
            param.setEshopId(eshopId);
            param.setProfileId(profileId);
            param.setPageSize(MAX_LEN);
            int total = mapper.queryAllRelationCount(param);
            if (total <= MAX_LEN) {
                doSplitNotify(api, param, 0);
                return;
            }
            int pageTotal = (int) Math.ceil((double) total / (double) MAX_LEN);
            for (int i = 0; i < pageTotal; i++) {
                doSplitNotify(api, param, i);
            }
        } catch (Exception ex) {
            logger.error("账套{}网店【{}】通知元气订单对应失败{}", profileId, eshopId, ex.getMessage(), ex);
        }
    }

    private void doSplitNotify(RecordSheetApi api, QueryRelationParam param, int pageIndex) {
        param.setPageIndex(pageIndex);
        List<OrderRelationOperation> operations = mapper.queryAllRelation(param);
        GeneralResult<OrderRelationResponse> result = api.correspondingPlatformBill(operations);
        if (result.getCode() != SUCCESS_CODE) {
            logger.error(JsonUtils.toJson(result));
            return;
        }
        if (!result.getData().getSuccess()) {
            logger.error(result.getData().getErrorMsg());
        }
    }

    private List<EshopInfo> queryYuanQiShopIdList() {
        QueryEShopParameter parameter = new QueryEShopParameter();
        parameter.setShopType(ShopType.YuanQiSenLin);
        return eshopBizMapper.getEshopByShopTypes(parameter);
    }

    private void doBaseInfoUpload(String messageProfileId) {
        BigInteger profileId = new BigInteger(messageProfileId);
        List<Otype> otypeList = cacheUtil.getAllOtype(profileId);
        if (CollectionUtils.isEmpty(otypeList)) {
            return;
        }
        eshopService.checkAndRegisterRds(otypeList);
        preFeature.doExecute(otypeList);
        if (!toolConfig.isEnableAutoUploadBaseInfo()) {
            return;
        }
        //status处理状态（0：未处理，1：处理成功，2：处理失败）
        for (Otype otype : otypeList) {
            if (otype.isBtypeAutoUploadEnabled()) {
                middleGroundService.doUploadBtype(profileId, otype);
            }
        }
    }
}

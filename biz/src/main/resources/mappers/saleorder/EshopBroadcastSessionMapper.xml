<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopBroadcastSessionMapper">
    <insert id="saveBroadcastSession">
        insert into pl_live_broadcast_session (id,platform_anchor_id,platform_live_room_id,platform_type,start_time,end_time,profile_id,platform_anchor_name,platform_session_name,create_time,match_order_type)
        values (#{param.id},#{param.platformAnchorId},#{param.platformLiveRoomId},0,#{param.startTime},#{param.endTime},#{param.profileId},#{param.platformAnchorName},#{param.platformSessionName},#{param.createTime},#{param.matchOrderType})
    </insert>
    <insert id="saveBroadcastSessionTeam">
        insert into pl_live_broadcast_session_team (id,live_brodcast_session_id,role_type,etype_id,deleted,create_time,profile_id)
        values
        <foreach collection="teamList" item="item" index="index" separator=",">
        (#{item.id},#{item.liveBrodcastSessionId},#{item.roleType},#{item.etypeId},#{item.deleted},#{item.createTime},#{item.profileId})
        </foreach>
    </insert>
    <update id="modifyBroadcastSession">
        update pl_live_broadcast_session set platform_anchor_id=#{param.platformAnchorId},platform_live_room_id=#{param.platformLiveRoomId},platform_type=0,start_time=#{param.startTime},
        end_time=#{param.endTime},platform_anchor_name=#{param.platformAnchorName},platform_session_name=#{param.platformSessionName},match_order_type=#{param.matchOrderType}  where profile_id=#{param.profileId} and id=#{param.id}
    </update>
    <delete id="deleteBroadcastSession">
        delete from pl_live_broadcast_session where profile_id=#{profileId} and id=#{id};
    </delete>
    <delete id="deleteBroadcastSessionTeam">
        delete from pl_live_broadcast_session_team where profile_id=#{profileId} and live_brodcast_session_id=#{id};
    </delete>

    <select id="queryBroadcastSessions"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.dto.order.BroadcastSessionDto">
        select distinct  sess.id,sess.platform_anchor_id,sess.platform_anchor_name,sess.profile_id,sess.create_time,sess.update_time,sess.platform_type,sess.start_time,sess.end_time,sess.platform_session_name,sess.platform_live_room_id,sess.match_order_type from pl_live_broadcast_session
        sess
        left join pl_live_broadcast_session_team st on st.profile_id=sess.profile_id and sess.id=st.live_brodcast_session_id  and st.deleted=0
        where sess.profile_id=#{param.profileId}

        <if test="param.startTime">
            and start_time>=#{param.startTime}
        </if>
        <if test="param.endTime!=null">
         and end_time<![CDATA[<=]]>#{param.endTime}
        </if>
        <if test="param.sessionName!=null and param.sessionName!=''">
          and platform_session_name like CONCAT('%',#{param.sessionName},'%')
        </if>
        <if test="param.accurateSessionName!=null and param.accurateSessionName!=''">
            and platform_session_name =#{param.accurateSessionName}
        </if>

        <if test="param.etypeIds!=null and param.etypeIds.size()>0">
            and st.role_type=#{param.roleType}
            and `st`.etype_id in
            <foreach collection="param.etypeIds" close=")" open="(" separator="," item="key">
                #{key}
            </foreach>
        </if>
        <if test="param.sessionIdForEdit!=null">
            and sess.id!=#{param.sessionIdForEdit}
        </if>
    </select>

    <select id="listBroadcastSessions"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.dto.order.BroadcastSessionDto">
        select distinct  sess.id,sess.platform_anchor_id,sess.platform_anchor_name,sess.profile_id,sess.create_time,sess.update_time,sess.platform_type,sess.start_time,sess.end_time,sess.platform_session_name,sess.platform_live_room_id from pl_live_broadcast_session
        sess
        where sess.profile_id=#{param.profileId}
        <if test="param.endTime!=null">
            and end_time>=#{param.endTime}
        </if>
    </select>

    <select id="queryBroadcastSessionsById"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.dto.order.BroadcastSessionDto">
        select sess.id,sess.platform_anchor_id,sess.platform_anchor_name,sess.platform_type,sess.start_time,sess.end_time,sess.platform_session_name,sess.platform_live_room_id
        from pl_live_broadcast_session sess
        left join pl_live_broadcast_session_team st on st.profile_id=sess.profile_id and sess.id=st.live_brodcast_session_id and st.role_type=0 and st.deleted=0
        where sess.profile_id=#{profileId}
        and `sess`.id in
        <foreach collection="sessionIds" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </select>

    <select id="queryBroadcastSessionTeams"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.dto.order.BroadcastSessionTeamDto">
        select st.id,st.live_brodcast_session_id,st.role_type,st.etype_id,st.create_time,st.update_time,st.profile_id,be.fullname as etypeName from pl_live_broadcast_session_team
        st left join base_etype be on be.profile_id=st.profile_id and be.id=st.etype_id
        where st.profile_id=#{profileId}
        and `st`.live_brodcast_session_id in
        <foreach collection="sessionIds" close=")" open="(" separator="," item="key">
            #{key}
        </foreach>
    </select>

    <select id="queryBroadcastSessionsIdBySaleOrder" resultType="java.math.BigInteger">
        select id from pl_live_broadcast_session where profile_id=#{profileId} and platform_anchor_name=#{platformAnchorName} and platform_live_room_id=#{platformLiveRoomId} and #{payTime} between start_time and end_time limit 1
    </select>

    <select id="queryBroadcastSessionsBySaleOrder"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.dto.order.BroadcastSessionDto">
        select sess.id as id,
               st.etype_id etypeHandler
                from pl_live_broadcast_session sess
                 left join pl_live_broadcast_session_team st on st.profile_id=sess.profile_id and sess.id=st.live_brodcast_session_id and st.role_type=3 and st.deleted=0
                  where sess.profile_id=#{profileId}
                    <if test="platformAnchorId!=null and platformAnchorId !='' ">
                        and sess.platform_anchor_id=#{platformAnchorId}
                    </if>
                    <if test="platformLiveRoomId!=null and platformLiveRoomId !='' ">
                        and sess.platform_live_room_id=#{platformLiveRoomId}
                    </if>
                    and #{payTime} between sess.start_time and sess.end_time
                  limit 1
    </select>

    <select id="queryBroadcastOrderInfo" resultType="java.math.BigInteger">
        SELECT distinct live.live_broadcast_session_id FROM td_bill_core deliver
        LEFT JOIN td_bill_detail_deliver detail ON detail.profile_id=deliver.profile_id AND detail.vchcode=deliver.vchcode AND detail.deleted=0
        LEFT JOIN td_bill_detail_deliver_live_broadcast live ON detail.profile_id=live.profile_id AND detail.vchcode=live.vchcode AND live.detail_id=detail.detail_id
        WHERE deliver.profile_id=#{profileId} AND deliver.deleted=0 AND live.live_broadcast_session_id IS NOT NULL AND detail.deleted=0 and live.live_broadcast_session_id
        in
        <foreach collection="sessionIds" close=")" open="(" separator="," item="key">
            #{key}
        </foreach>
        UNION
        SELECT distinct live.live_broadcast_session_id FROM td_bill_core deliver
        LEFT JOIN td_bill_detail_combo_deliver detail ON detail.profile_id=deliver.profile_id AND detail.vchcode=deliver.vchcode
        LEFT JOIN td_bill_detail_combo_deliver_live_broadcast live ON detail.profile_id=live.profile_id AND detail.vchcode=live.vchcode AND live.combo_detail_id=detail.id
        WHERE deliver.profile_id=#{profileId} AND deliver.deleted=0 AND live.live_broadcast_session_id IS NOT NULL  AND detail.deleted=0 and live.live_broadcast_session_id
        in
        <foreach collection="sessionIds" close=")" open="(" separator="," item="key">
            #{key}
        </foreach>
        UNION
        SELECT distinct live.live_broadcast_session_id FROM acc_bill_core deliver
        LEFT JOIN acc_bill_detail_deliver detail ON detail.profile_id=deliver.profile_id AND detail.vchcode=deliver.vchcode AND detail.deleted=0
        LEFT JOIN acc_bill_detail_deliver_live_broadcast live ON detail.profile_id=live.profile_id AND detail.vchcode=live.vchcode AND live.detail_id=detail.detail_id
        WHERE deliver.profile_id=#{profileId} AND deliver.deleted=0 AND live.live_broadcast_session_id IS NOT NULL AND detail.deleted=0 and live.live_broadcast_session_id
        in
        <foreach collection="sessionIds" close=")" open="(" separator="," item="key">
            #{key}
        </foreach>
        UNION
        SELECT distinct live.live_broadcast_session_id FROM acc_bill_core deliver
        LEFT JOIN acc_bill_detail_combo_deliver detail ON detail.profile_id=deliver.profile_id AND detail.vchcode=deliver.vchcode
        LEFT JOIN acc_bill_detail_combo_deliver_live_broadcast live ON detail.profile_id=live.profile_id AND detail.vchcode=live.vchcode AND live.combo_detail_id=detail.id
        WHERE deliver.profile_id=#{profileId} AND deliver.deleted=0 AND live.live_broadcast_session_id IS NOT NULL  AND detail.deleted=0 and live.live_broadcast_session_id
        in
        <foreach collection="sessionIds" close=")" open="(" separator="," item="key">
            #{key}
        </foreach>

    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.PlatformBtypeMapper">

    <insert id="insertOrUpdatePlatformBtype">
        INSERT INTO pl_eshop_platform_btype_mapping (
        profile_id,eshop_id,id,platform_btype_name,
        platform_btype_code,platform_btype_unique_id,platform_btype_address,
        platform_btype_mobile,platform_btype_tel,platform_btype_contactor,
        create_time,update_time,deleted,
        create_type,btype_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.profileId}, #{item.eshopId}, #{item.id}, #{item.platformBtypeName},
            #{item.platformBtypeCode}, #{item.platformBtypeUniqueId}, #{item.platformBtypeAddress},
            #{item.platformBtypeMobile}, #{item.platformBtypeTel}, #{item.platformBtypeContactor},
            #{item.createTime}, #{item.updateTime}, #{item.deleted},
            #{item.createType}, #{item.btypeId})
        </foreach>
        ON DUPLICATE KEY update
        profile_id = VALUES(profile_id),
        eshop_id = VALUES(eshop_id),
        id = VALUES(id),
        platform_btype_name = VALUES(platform_btype_name),
        platform_btype_code = VALUES(platform_btype_code),
        platform_btype_unique_id = VALUES(platform_btype_unique_id),
        platform_btype_address = VALUES(platform_btype_address),
        platform_btype_mobile = VALUES(platform_btype_mobile),
        platform_btype_tel = VALUES(platform_btype_tel),
        platform_btype_contactor = VALUES(platform_btype_contactor),
        update_time = VALUES(update_time),
        deleted = VALUES(deleted),
        create_type = VALUES(create_type),
        btype_id = VALUES(btype_id)
    </insert>


    <insert id="savePlatformBtypeMapping">
        INSERT INTO pl_eshop_platform_btype_mapping (
        profile_id,eshop_id,id,platform_btype_name,
        platform_btype_code,platform_btype_unique_id,platform_btype_address,
        platform_btype_mobile,platform_btype_tel,platform_btype_contactor,
        deleted,create_type,btype_id,mark_data_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.profileId}, #{item.eshopId}, #{item.id}, #{item.platformBtypeName},
            #{item.platformBtypeCode}, #{item.platformBtypeUniqueId}, #{item.platformBtypeAddress},
            #{item.platformBtypeMobile}, #{item.platformBtypeTel}, #{item.platformBtypeContactor},
            #{item.deleted},#{item.createType}, #{item.btypeId},#{item.markDataId})
        </foreach>
        ON DUPLICATE KEY update
        platform_btype_name = VALUES(platform_btype_name),
        platform_btype_code = VALUES(platform_btype_code),
        platform_btype_address = VALUES(platform_btype_address),
        platform_btype_mobile = VALUES(platform_btype_mobile),
        platform_btype_tel = VALUES(platform_btype_tel),
        platform_btype_contactor = VALUES(platform_btype_contactor),
        deleted = 0
    </insert>

    <insert id="updatePlatformBtypeMapping">
        INSERT INTO pl_eshop_platform_btype_mapping (profile_id, eshop_id, id, platform_btype_name,
                                                     platform_btype_code, platform_btype_unique_id,
                                                     platform_btype_address,
                                                     platform_btype_mobile, platform_btype_tel,
                                                     platform_btype_contactor,
                                                     deleted, create_type, btype_id)
        values (#{profileId}, #{eshopId}, #{id}, #{platformBtypeName}, #{platformBtypeCode}, #{platformBtypeUniqueId},
                #{platformBtypeAddress},
                #{platformBtypeMobile}, #{platformBtypeTel}, #{platformBtypeContactor}, #{deleted}, #{createType},
                #{btypeId})
        ON DUPLICATE KEY UPDATE btype_id=#{btypeId},
                                deleted=0
    </insert>


    <insert id="updatePlatformBtypeMappingByUniqueIds">
        <foreach collection="list" open="" close="" separator=";" item="item">
            update pl_eshop_platform_btype_mapping set mark_data_id=#{item.markDataId}
            where profile_id = #{profileId}
            AND platform_btype_unique_id = #{item.platformBtypeUniqueId}
        </foreach>
    </insert>
    <update id="clearPlatformBtypeMapping"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.standardApi.platformBtype.PlatformBtypeMapping">
        update pl_eshop_platform_btype_mapping
        set btype_id=0
        where profile_id = #{profileId}
          and id = #{id}
    </update>

    <select id="queryPlatformBtypeExist" resultType="java.math.BigInteger">
        select id
        from pl_eshop_platform_btype_mapping
        where profile_id = #{profileId}
          and platform_btype_unique_id = #{uniqueId}
          and eshop_id = #{eshopId}
    </select>

    <select id="queryPlatformBtypeByOrder"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.standardApi.platformBtype.PlatformBtypeMapping">
        select platform_distributor_name as platformBtypeName,
               o.buyer_id,
               o.profile_id,
               o.otype_id                as eshopId,
               o.platform_distributor_id as platformBtypeUniqueId,
               o.platform_distributor_id as platformBtypeCode,
                mkd.big_data              as markBigData
        from pl_eshop_sale_order o
                 left join pl_buyer b on o.profile_id = b.profile_id and o.buyer_id = b.buyer_id
                 left join pl_eshop_order_mark mrk
                           on o.profile_id = mrk.profile_id and o.id = mrk.order_id and mrk.mark_code = '********'
                 left join mark_data mkd on mkd.profile_id = mrk.profile_id and mrk.mark_data_id = mkd.id
        where o.profile_id = #{profileId}
          and o.trade_order_id = #{tradeId}
          and o.otype_id = #{eshopId}
          and (platform_distributor_name != '' or b.customer_shop_account != '')
    </select>

    <select id="queryPlatformBtypeList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.standardApi.platformBtype.PlatformBtypeMapping">
        select map.profile_id, map.eshop_id, map.id, map.platform_btype_name as platformBtypeName,
        map.platform_btype_code, map.platform_btype_unique_id,
        map.platform_btype_address, map.platform_btype_mobile, map.platform_btype_tel,
        map.platform_btype_contactor, map.deleted, map.create_type,map.btype_id,
        b.fullname as btypeName,b.usercode as btypeCode,e.fullname as eshopName,map.mark_data_id,
        md.big_data as markBigData
        from pl_eshop_platform_btype_mapping map
        left join base_btype b on b.profile_id=map.profile_id and b.id=map.btype_id
        left join pl_eshop e on e.profile_id=map.profile_id and e.otype_id=map.eshop_id
        left join mark_data md on md.profile_id=map.profile_id and md.id=map.mark_data_id
        where map.profile_id=#{profileId}
        and map.platform_btype_name!=''
        <if test="platformBtypeUniqueIdList != null and platformBtypeUniqueIdList.size()>0">
            AND map.platform_btype_unique_id in
            <foreach collection="platformBtypeUniqueIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="eshopId!=null and eshopId!=0">
            and map.eshop_id=#{eshopId}
        </if>
        <if test="state==1">
            and map.btype_id>0
        </if>
        <if test="state==2">
            and map.btype_id=0
        </if>
        <if test="filter!=null and filter.size>0">
            <trim prefix=" and (" suffix=")">
                <foreach collection="filter" item="item" separator="and">
                    ${item.dataField} like concat('%',#{item.value},'%')
                </foreach>
            </trim>
        </if>
    </select>

    <select id="getEtypeById" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Etype">
        select id, profile_id, usercode, fullname, mobile
        from base_etype
        where profile_id = #{profileId}
          and id = #{etypeId}
    </select>

    <select id="getBtypeInfo" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Btype">
        select id, fullname, usercode, deleted, stoped
        from base_btype
        where profile_id = #{profileId}
          and fullname = #{name}
        limit 1
    </select>

    <select id="getBtypeInfoByCode" resultType="java.math.BigInteger">
        select id
        from base_btype
        where profile_id = #{profileId}
          and usercode = #{code}
        limit 1
    </select>
    <select id="queryPlatformBtypeListForUpload"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.standardApi.platformBtype.PlatformBtypeMapping">
        select distinct map.profile_id, map.eshop_id, map.id, map.platform_btype_name as platformBtypeName,
        map.platform_btype_code, map.platform_btype_unique_id,
        map.platform_btype_address, map.platform_btype_mobile, map.platform_btype_tel,
        map.platform_btype_contactor, map.deleted, map.create_type,map.btype_id,
        b.fullname as btypeName,b.usercode as btypeCode,e.fullname as eshopName
        from pl_eshop_platform_btype_mapping map
        left join base_btype b on b.profile_id=map.profile_id and b.id=map.btype_id
        left join base_btype_bcategory bbb on bbb.profile_id=map.profile_id and bbb.btype_id=map.btype_id
        left join pl_eshop e on e.profile_id=map.profile_id and e.otype_id=map.eshop_id
        left join pl_eshop_baseinfo_upload_record ebur on rbur.profile_id=map.profile_id and rbur.info_type=1 and
        ebur.info_id=b.id
        where map.profile_id=#{profileId}
        and map.platform_btype_name!=''
        and (ebur.info_id is null or ebur.upload_time <![CDATA[ < ]]> b.update_time or ebur.upload_status!=1)
        <if test="queryMapping">
            and map.btype_id>0
        </if>
        <if test="eshopId!=null and eshopId!=0">
            and map.eshop_id=#{eshopId}
        </if>
        <if test="state==1">
            and map.btype_id>0
        </if>
        <if test="state==2">
            and map.btype_id=0
        </if>
        <if test="bcateforys != null and bcateforys.size()>0">
            AND map.platform_btype_unique_id in
            <foreach collection="platformBtypeUniqueIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getBtypeInfoById" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Btype">
        select id, fullname, usercode, deleted, stoped
        from base_btype
        where profile_id = #{profileId}
          and id = #{btypeId}
        limit 1
    </select>

    <insert id="savePlatformBtypeMappingBigData">
        replace into mark_data
            (id, profile_id, big_data)
        values (#{id}, #{profileId}, #{bigData})
    </insert>
</mapper>
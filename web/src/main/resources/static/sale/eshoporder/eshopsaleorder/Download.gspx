<?xml version="1.0" encoding="UTF-8" ?>
<Page xmlns="Craba.UI" Title="下载订单" CssClass="NoDisabledPage download " OnClose="doFormClose" CssStyle="padding-top:0"
      ActionType="sale.eshoporder.eshopsaleorder.DownloadAction, sale/eshoporder/eshopsaleorder/Download.js">
    <Style>
        .logBox {
        max-width: 650px;
        }
        .CombEditor .Edit{
        text-indent: 6px;
        }
        .filterComb .filterLabel{
        color: #ababab;
        }
        .filterComb .EditBlock{
        border-right: 1px solid #c9c9c9;
        border-radius: 0px;
        }
        .tradeComb .tradeLabel2{
        color: #ababab;
        }
        .logLabel {
        line-height: 20px;
        }
        .tradeLabelBox{
        width: 89px;
        text-align: right;
        }
        .tradeLabelBox .tradeLabel2{
        line-height: 30px;
        margin-right: 10px;
        }
        .download .FlowPanel .FlowLabel {
        margin-right: 10px;
        }
        .tradeLabelBox .normal{
        color:#ccc;
        }
        .tradeLabelBox .number{
        color:#333;
        }
        .tradeLabelBox .error{
        color:#e43a1c;
        }
        .tradeIdColumn .TextArea {
        width: 70%!important;
        }
        .otypeSelect .FlowItem
        .FlowLabel:before{content:"*";width:6px;height:12px;border-radius:3px;color:#e43a1c;display:inline-block;margin-right:4px;vertical-align:middle;}
        .filterComb .EditBlock .SkinButton {
        border: 0;
        width: 23px;
        }

        .Grid.NoWordWrap .GridDynamicButtonBody a {
            background-color:transparent;
        }
        .tipMsg{
            background-color:#F0F7FF;border:2px solid #D2E4FF;padding:10px;border-radius:5px;
        }
        .SwitchButton.hasText {color: grey;}
        .SwitchButton.hasText .SwitchBlock {
            background-color: grey;
        }

    </Style>
    <FlexBlock>
        <FlexColumn ID="right" CssClass='FlexAuto oauto'>
            <PageControl ID="control" CssClass="MaxTab">
                <TabPage ID="autoDownload" Tag="autoDownload" Caption="自动下载" >
                    <VPanel CssClass="tipMsg">
                        <Label Text="自动下载业务说明:"/>
                        <Label Text="     1.已授权的网店才能启动自动下载，每隔十分钟，自动将线上订单下载到系统中。如果平台支持秒级同步，则开启自动下载后，自动开启秒级同步。"/>
                        <HPanel>
                            <Label Text="     2.请尽量先做网店商品对应，以便系统能够准确记录库存占用，同步数量正确，方便订单审核确认等"/>
                            <Label ID="button" Text="[前往设置]" CssClass='SpecialButton SkinColor' OnClick="doClick"   Visible="${ptypeRelationVisible}" Enabled="${ptypeRelationEnable}"/>
                        </HPanel>
                        <Label Text="     3.填写首次下载时间,下载首次下载时间以后的订单,自动下载开启后不能关闭"/>
                        <Label Text="     4.下载订单时，售后单将同步自动下载"/>
                    </VPanel>
                    <FlowPanel>
                        <Block CssClass='Flex1'/>
                        <Label Width="0" ID="btnExport1"/>
                    </FlowPanel>
                    <Grid ID="grid"  PagerAutoFocus="false"    WordWrap="false" OnFilter="doGetOtherFilter"
                          NeedRowIndex="true"  AllowCopy="true" ModifyOnly="true" AllowFilter="true"
                          ReadOnly="false"  BindPagerDataSource="bindGridData"
                          Pager="Bottom" DefaultRowCount="1" PrimaryKey="id" Enabled="${ptypeRelationEnable}"
                          PagerShowGotoButton="true" PagerShowRefreshButton="true"  OnCellBeginEdit="doCellBeginEdit" >
                        <TextColumn DataField="fullname" Caption="网店" HeaderAlign="Center" ReadOnly="true"  />
                        <DynamicButtonColumn CssClass='Normal' DataField="authState" Caption="授权状态" HeaderAlign="Center" ReadOnly="true"  AllowFilter="true" OnFilterRendering='doFilterRendering'/>
                        <SwitchButtonColumn CheckedValue='true' Text='未开启,已开启' CssClass='Norma' DataField="autoSyncState" Caption="自动下载" HeaderAlign="Center" ReadOnly="true"  AllowFilter="true" OnFilterRendering='doFilterRendering'/>
<!--                        <DynamicButtonColumn DataField="tmcEnabledState" Caption="秒级同步" HeaderAlign="Center" ReadOnly="true"  AllowFilter="true" OnFilterRendering='doFilterRendering'/>-->
                        <DateTimeColumn ID="firstDownloadTime"  AllowFilter="false"  DataField="firstDownloadTime" Caption="开始下载时间" AllowSort="false" AllowStretch="true" HeaderAlign="Center"  ReadOnly="true"/>
                        <DateTimeColumn ID="lastDownloadTime"  AllowFilter="false" DataField="lastDownloadTime" Caption="当前订单下载截止时间" AllowSort="false" AllowStretch="true" ReadOnly="true"/>
                        <DynamicButtonColumn CssClass='Normal' Width="180" Caption="操作列" DataField="operation" OnButtonClick="doOperation"
                                             LayoutDirection="Horz" AllowStretch="true" ReadOnly="true" AllowSort="false"  AllowFilter="false"
                                             AllowConfig="false"/>
                    </Grid>
                </TabPage>
                <TabPage ID="param" Tag="paramTag" Caption="手工下载">
                    <FlexColumn>
                        <FlowPanel CssClass='BorderBottom pltr10 paramBox' ID="timePanel">
                            <DateRangeEdit ID="dateRange" Label="拍下订单时间范围:" ReportField="拍下订单时间范围" DataEndField="endTime"
                                           DataStartField="beginTime"
                                           CssClass='FlexAuto' OuterCssClass='border' SelectOnly='true'
                                           NullDisplayText='选择开始时间和结束时间' Width="265" Enabled="eshoporder.eshopDownloadOrderPage.download"/>

<!--                            <DropDownCheckBoxList ID="drShopTypes" Label="平台店铺类型:" ReportField="平台店铺类型"-->
<!--                                                  DataField="shoptypes"-->
<!--                                                  OnChange="doShopTypesSelected"-->
<!--                                                  DropDownStyle="DropDownSearch"-->
<!--                                                  DataSource="${shopTypes}"-->
<!--                                                  DataTextField="name" DataValueField="code"-->
<!--                                                  LayoutDirection="Vert"-->
<!--                                                  Width="150" Enabled="eshoporder.eshopDownloadOrderPage.download"-->
<!--                            />-->

                            <DropDownCheckBoxList ID="drEshopList" DataSource="${eshopList}" Label="网店:"
                                                  ReportField="网店" DataField="otypeIds"
                                                  DropDownStyle="DropDownSearch"
                                                  DataTextField="fullname" DataValueField="otypeId"
                                                  LayoutDirection="Vert" Width="150" OnChange="doEshopSelected"
                                                  Enabled="eshoporder.eshopDownloadOrderPage.download"
                            />

                            <DropDownEdit ID="drStatus" Label="线上交易状态:" Width="150" SelectedIndex="0"
                                          DropDownStyle="DropDownList"
                                          ListItems="0=全部,1=未付款,2=已付款,3=已发货,4=交易成功,5=交易关闭,6=部分发货,7=部分付款"
                                          TabIndex="1"
                                          DataTextField="text" DataField="value"
                                          Enabled="eshoporder.eshopDownloadOrderPage.download"
                            />
                            <Button CssClass="SpecialButton" ID="btnDownload" Text="开始下载"
                                    Enabled="eshoporder.eshopDownloadOrderPage.download" OnClick="doDownload"/>
                            <Label Text="*订单的售后单将同步下载"/>
                            <Block CssClass='Flex1'/>
                            <Label Width="0" ID="btnExport"/>
                            <Block CssClass="clear"/>
                            <Block ColSpan='3' CssClass='CombEditor filterComb' Label="过滤条件:" Visible="${enableMulDownloadFilter}">
                                <DropDownEdit ID="drFilterType" SelectedIndex="0" DropDownStyle="DropDownList"
                                              ListItems="1=按商家编码排除,2=按买家账号排除" Width="113"
                                              Enabled="eshoporder.eshopDownloadOrderPage.download"/>
                                <TextEdit ID="edFilter" Width="890" NullDisplayText="支持多个关键字用逗号隔开，比如(A11,B22)" Enabled="eshoporder.eshopDownloadOrderPage.download"/>
                            </Block>
                        </FlowPanel>
                        <Grid ID="progressGrid" OnCellRendering='doCellRender'
                              PrimaryKey="taskId" NeedRowIndex="true" AllowMouseWheel="false">
                            <TextColumn Caption="下载网店" DataField="otypeName" Width="150"/>
                            <ProgressColumn Caption="下载进度" DataField="progress" CssClass="blueValue" Width="350"/>
                            <CheckBoxColumn DataField='success' ReadOnly='true' Width="50" AllowConfig="false"/>
                            <TextColumn Caption="概况" DataField="message" Width="500"/>
                            <DynamicButtonColumn Caption="操作" AllowConfig="false" Width="400" HeaderAlign="Center"
                                                 DataField="operation" AllowFilter="false" AllowSort="false"
                                                 LayoutDirection="Horz" TextAlign="Center"
                                                 OnButtonClick="doOperateShowLog"/>
                        </Grid>
                    </FlexColumn>
                </TabPage>
                <TabPage ID="trade" Tag="tradeTag" Caption="按订单号" >
                    <FlexColumn ID="tradeIdPanel" CssClass="tradeIdColumn">
                        <FlowPanel ItemWidth="300" ItemLabelWidth="90" CssClass="otypeSelect">
                            <DropDownEdit ID="drEshop" DataSource="${eshopList}" Label="网店:"
                                          DropDownStyle="DropDownSearch"
                                          DataTextField="fullname" DataValueField="otypeId"
                                          Enabled="eshoporder.eshopDownloadOrderPage.downloadById"/>
                            <Button ID="btnImport" Text="导入订单号" OnClick="doImportInit" Enabled="eshoporder.eshopDownloadOrderPage.downloadById"/>
                            <Label Text="*订单的售后单将同步下载"/>
                            <Block CssClass='Flex1'/>
                            <Label Width="0" ID="btnExport2"/>
                        </FlowPanel>
                        <FlexBlock CssClass="textAreaBlock" Height="300">
                            <Block CssClass="tradeLabelBox">
                                <Label Text="* " FontColor="#e43a1c" CssStyle="vertiacl-align:middle"/>
                                <Label CssClass="tradeLabel1" Text="线上订单编号:" Enabled="eshoporder.eshopDownloadOrderPage.downloadById"/>
                            </Block>
                            <MemoEdit Height="300" ID="meTradeIds"
                                      CssClass="ResizeHeight"
                                      DataField="${ids}" Enabled="eshoporder.eshopDownloadOrderPage.downloadById"
                                      NullDisplayText="多个订单以逗号隔开,比如(DD111,DD2222,DD3333)"/>
                        </FlexBlock>
                        <FlexBlock CssClass="Flex0">
                            <HBlock CssStyle="margin-left: 100px" CssClass="Flex0">
                                <Button CssClass="SpecialButton" ID="btnDownloadByTid" Text="开始下载"
                                        Enabled="eshoporder.eshopDownloadOrderPage.downloadById" OnClick="doDownload"/>
                            </HBlock>
                        </FlexBlock>
                        <FlexBlock CssClass="textAreaBlock" Height="300">
                            <Label Width="90" CssStyle="text-align: right;" Text="进度信息:"/>
                            <MemoEdit CssClass="ResizeHeight" Height="300" CssStyle="text-indent:0" ID="tradeInfo"
                                      ReadOnly="true" NullDisplayText="暂无进度" Enabled="eshoporder.eshopDownloadOrderPage.downloadById"/>
                        </FlexBlock>
                    </FlexColumn>
                </TabPage>
                <TabContainer ID="autoDownloadConfig">
                    <Button Text="自动下载订单" RightIcon="aicon-shezhi2 " OnClick="btnAutoDownloadConfig"
                            OnRightIconClick="btnAutoDownloadConfig"
                            Enabled="eshoporder.eshopDownloadOrderPage.autoDownload" TabIndex="-1"/>
                </TabContainer>
            </PageControl>
            <PopupBlock ID="logBlock" CssClass="logBox">
                <Block>
                </Block>
            </PopupBlock>
        </FlexColumn>
    </FlexBlock>
    <PopupBlock ID="popAutoDownloadTips">
        <FlowPanel LayoutDirection="Vert">
            <Label Text="1.已授权店铺的订单，自动下载！"/>
            <Label Text="2.建议先完成网店商品对应，系统可识别是否同一商品！"/>
        </FlowPanel>
    </PopupBlock>
</Page>

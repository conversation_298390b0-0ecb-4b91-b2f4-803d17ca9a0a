Type.registerNamespace('sale.product');

sale.product.EditRuleFormAction = function () {
    sale.product.EditRuleFormAction.initializeBase(this);
};

sale.product.EditRuleFormAction.prototype = {
    context: function (cb) {
        var params = this.get_pageParams();
        var rule = params.rule;
        var mode = this.get_mode();
        var isDefault = mode == 'default';
        if (!rule) {
            rule = {
                otypeId: 0,
                warehouseStockSyncEnabled: false,
                eshopMultiStockSyncEnabled: false,
            };
        }
        if (!rule.calculate) {
            rule.calculate = this.getDefaultCalculate();
        }
        var canEdit = $saleUtils.getPower("eshoporder.stock.sync");
        //自定义规则或者抖店默认规则,带出时效配置
        var isSupportMultiSync = !isDefault || (rule.eshopType && 52 == rule.eshopType);
        var multiList = [];
        var allowMultiTypes = [];
        var defulatCaculate = this.getDefaultCalculate();
        if (isSupportMultiSync) {
            multiList = this.queryMultiDetails(rule.id);
            if (!multiList || multiList.length == 0) {
                multiList = [{
                    calculate: defulatCaculate,
                    syncType: 0,
                    fixQty: 0,
                    syncTimeQty: 0,
                    formula: this.buildFormulaFromCalculate(defulatCaculate)
                }];
            } else {
                for (var i = 0; i < multiList.length; i++) {
                    var multiData = multiList[i];
                    if (1 == multiData.syncType) {
                        multiData.formula = "";
                    } else {
                        if (!multiData.calculate) {
                            multiData.calculate = defulatCaculate;
                        }
                        multiData.formula = this.buildFormulaFromCalculate(multiData.calculate);
                    }
                    multiData.syncTimeQty = multiData.fixQty;
                }
            }
            var multiTypes = this.queryMultiDetails(0);
            if (multiTypes && multiTypes.length > 0) {
                for (var i = 0; i < multiTypes.length; i++) {
                    var multiType = multiTypes[i];
                    var multiData = {
                        platformMultiId: multiType.platformMultiId,
                        platformMultiName: multiType.platformMultiName
                    };
                    allowMultiTypes.push(multiData);
                }
            }
        }
        var title = params.title;
        if (!title) {
            if (mode == 'custom_edit') {
                title = '编辑自定义库存同步规则';
            } else if (mode == 'custom_add') {
                title = '新增自定义库存同步规则';
            } else {
                title = '编辑网店默认库存同步规则';
            }
        }

        var context = {
            title: title,
            isDefault: isDefault,
            ktypeList: params.ktypeList,
            canEdit: canEdit,
            datasource: rule,
            allowMultiTypes: allowMultiTypes,
            isSupportMultiSync: isSupportMultiSync,
            eshopMultiStockSyncEnabled: rule.eshopMultiStockSyncEnabled,
            multiList: multiList,
            minHeight: !isDefault ? 630 : (isSupportMultiSync ? 570 : 435),
            showMore: rule.warehouseStockSyncEnabled || rule.eshopMultiStockSyncEnabled,
            warehouseTip: "<span>将按网店的<a href='#' style='color:#2288fc' link='sale/eshoporder/eshop/EshopOnlineStoreWarehouse.gspx'>【全渠道门店对应】</a>和<a href='#' style='color:#2288fc' link='sale/eshoporder/eshop/PlatformWarehouseCorrespond.gspx'>【平台仓库对应】</a>的仓库对应关系，将系统内仓库库存，按公式同步到网店仓库</span>"
        }
        cb(context);
    },
    get_mode: function () {
        var params = this.get_pageParams();
        if (!params || !params.mode) {
            return 'default';
        }
        return params.mode;
    },
    getDefaultCalculate: function () {
        return {
            percentage: 100,
            calculateMode: 0,
            ruleCalQty: 0
        }
    },
    queryMultiDetails: function (ruleId) {
        var params = {};
        params.ruleId = ruleId;
        var url = '/sale/eshoporder/product/queryMultiDetails';
        var response = $common.ajaxSync({
            url: url,
            data: params,
            type: 'post',
            router: 'ngp'
        });
        if (response.code != 200 || !response.data) {
            return;
        }
        return response.data;
    },
    showHideMore: function (sender) {
        //点击展开还是收起
        var isShowMore = sender.get_idPart() == 'btnShowMore';
        var form = this.get_form();
        form.btnShowMore.set_visible(!isShowMore)
        form.fpMore.set_visible(isShowMore);
        form.btnHideMore.set_visible(isShowMore);
    },

    initialize: function EditRuleFormAction$initialize() {
        sale.product.EditRuleFormAction.callBaseMethod(this, 'initialize');
    },

    doTypeChange: function (sender) {
        var form = sender.get_form();
        var num = form.txtVirtualQty.get_value();
        var type = form.drCalculateMode.get_value();
        if (type && type == "3" && num == 0) {
            form.txtVirtualQty.set_value(1);
        }
    },
    doWarehouseTipClick: function (sender, e) {
        if (e.target.tagName != 'A') return; // 不是点击a标签，不处理
        e.preventDefault();
        var form = this.get_form();
        var link = e.target.getAttribute('link');
        var pageParams = this.get_pageParams();
        var rule = pageParams.rule;
        var params = {
            mode: "simple",
            eshopId: rule ? rule.otypeId : 0
        };
        var modal = new Sys.UI.Form(form);
        modal.showModal(link, params);
        // $common.showPage(sender, link);
    },
    doWarehouseSyncClick: function (sender) {
        if (!sender.get_value()) {
            return;
        }
        var form = this.get_form();
        var mutiSyncValue = form.saveData().eshopMultiStockSyncEnabled;
        if (!mutiSyncValue) {
            return;
        }
        $common.alert("分仓库存同步和时效库存同步，同时支持选择一个使用");
        sender.set_value(false);
    },
    doMultiSyncClick: function (sender) {
        if (!sender.get_value()) {
            this.get_form().multiStockGrid.set_visible(sender.get_value());
            return;
        }
        var form = this.get_form();
        var mutiSyncValue = form.saveData().warehouseStockSyncEnabled;
        if (!mutiSyncValue) {
            this.get_form().multiStockGrid.set_visible(sender.get_value());
            return;
        }
        $common.alert("分仓库存同步和时效库存同步，同时支持选择一个使用");
        sender.set_value(false);
    },
    doIconColumnClick: function (sender, args) {
        var grid = this.get_form().multiStockGrid;
        var bIndex = args.get_buttonIndex();
        if (bIndex == 1) {
            grid.deleteSelectedRow();
        } else {
            var defulatCaculate = this.getDefaultCalculate();
            var defaultRowData = {
                calculate: defulatCaculate,
                syncType: 0,
                syncTimeQty: 0,
                formula: this.buildFormulaFromCalculate(defulatCaculate)
            };
            this.get_form().multiStockGrid.appendRowData(defaultRowData);
        }
    },
    doMultiStockCellBeginEdit: function (sender, args) {
        var form = this.get_form();
        var column = args.get_column()
        var grid = column.get_grid();
        if (!grid) return;
        var data = grid.get_selectedRowData();
        if (!data) {
            args.set_cancel(true);
            return;
        }
        var dataField = column.get_dataField();
        var popup = form.formulaPopup;
        popup.hide();
        var power = $ms.power.eshoporder["eshoporder.stock.rule.config"];
        if (dataField == 'formula' && (!power || !power.value)) {
            args.set_cancel(true);
            return;
        }
        if (dataField == 'formula') {
            data.fixQty = data.syncTimeQty;
            if (data.syncType == 0) {
                form.formulaForm.set_visible(true);
                form.formulaQtyForm.set_visible(false);
            } else {
                form.formulaForm.set_visible(false);
                form.formulaQtyForm.set_visible(true);
            }
            popup.dataBind(data);
            popup.popupAt(sender.get_activeCell());
        }
    },
    onSyncTypeChange: function (sender, arg) {
        var form = sender.get_form();
        var grid = form.multiStockGrid;
        if (!grid) {
            return;
        }
        this._changeCalcType(form, grid);
    },
    _changeCalcType: function (form, grid) {
        var data = grid.get_selectedRowData();
        var index = grid.get_selectedRowIndex();
        if (!data.calculate) {
            data.calculate = this.getDefaultCalculate();
        }
        if (!data.syncTimeQty) {
            data.syncTimeQty = 0;
        }
        if (!data.fixQty) {
            data.fixQty = 0;
        }
        var calculate = data.calculate;

        if (data.syncType == 0) {
            var formula = this.buildFormulaFromCalculate(calculate);
            data.formula = formula;
            data.syncTimeQty = 0;
            form.formulaForm.set_visible(false);
            form.formulaQtyForm.set_visible(true);
        } else {
            data.formula = "固定数量:" + math.round(data.syncTimeQty, 2);
            form.formulaForm.set_visible(true);
            form.formulaQtyForm.set_visible(false);
        }
        data.calculate = calculate;
        grid.modifyRowData(index, data);
    },
    _getMode: function (mode) {
        var result;
        switch (mode + "") {
            case "1":
                result = "-";
                break;
            case "2":
                result = "*";
                break;
            case "3":
                result = "/";
                break;
            default:
                result = "+";
                break;
        }
        return result;
    },
    _calcSyncTimeQty: function (form, item) {
        if (item.syncType == 1) {
            item.syncTimeQty = math.round(item.fixQty, 2);
        }
        // switch (item.timeCalculate.calculateMode + "") {
        //     case "1":
        //         item.syncTimeQty = math.floor(syncQty * item.timeCalculate.percentage / 100 - item.timeCalculate.ruleCalQty);
        //         break;
        //     case "2":
        //         item.syncTimeQty = math.floor(syncQty * item.timeCalculate.percentage / 100 * item.timeCalculate.ruleCalQty);
        //         break;
        //     case "3":
        //         item.syncTimeQty = math.floor(syncQty * item.timeCalculate.percentage / 100 / item.timeCalculate.ruleCalQty);
        //         break;
        //     default:
        //         item.syncTimeQty = math.floor(syncQty * item.timeCalculate.percentage / 100 + item.timeCalculate.ruleCalQty);
        //         break;
        // }
    },
    doSyncTimeQtyChanged: function (sender, args) {
        var form = sender.get_form();
        var grid = form.multiStockGrid;
        var data = grid.get_selectedRowData();
        if (1 == data.syncType) {
            data.fixQty = data.syncTimeQty;
            data.formula = "固定数量:" + math.round(data.fixQty, 2);
            grid.modifyRowData(args.get_rowIndex(), data);
        } else {
            args.set_cancel(true);
        }
    },
    doSaveFormula: function (sender, args) {
        var form = sender.get_form();
        var grid = form.multiStockGrid;
        var data = grid.get_selectedRowData();
        var popupData = form.formulaPopup.saveData();
        if (!popupData) {
            form.formulaPopup.hide(true);
            return;
        }
        if (!data.calculate) {
            data.calculate = {};
        }
        if (data.syncType == 0) {
            data.calculate.percentage = popupData.calculate.percentage;
            data.calculate.calculateMode = popupData.calculate.calculateMode;
            data.calculate.ruleCalQty = popupData.calculate.ruleCalQty;
        } else {
            data.syncTimeQty = popupData.fixQty;
        }
        data.syncCron = JSON.stringify(data.calculate);
        this._changeCalcType(form, grid);
        form.formulaPopup.hide(true);
    },
    doTimeQtyChange: function (sender, args) {

    },
    doBindPagerDataSource: function (pageParam) {
        var form = this.get_form();
        if (!pageParam) {
            return;
        }
        if (pageParam.setting && pageParam.setting.length > 0) {
            this._bindTimeRuleEditItems(form, pageParam.setting);
            form.multiStockGrid.dataBind(pageParam.setting);
        } else {
            form.multiStockGrid.dataBind(null);
        }
    },
    _bindTimeRuleEditItems: function (form, data) {
        if (data == null || data.length == 0) {
            return;
        }
        var list = [];
        for (var i = 0; i < data.length; i++) {
            var item = data[i];
            this._calcSyncTimeQty(form, item);
            list.push({platformMultiId: item.platformMultiId, platformMultiName: item.platformMultiName});
        }
        var column = form.multiStockGrid.findColumn('platformMultiId');
        column.set_items(list); // 业务js动态修改下拉列的数据源
    },
    doMultiTimeTypeChanged: function (sender, args) {
        //一个时效只能配置一次
        var form = sender.get_form();
        var grid = form.multiStockGrid;
        var index = args.get_rowIndex();
        var count = grid.get_rowCount();
        var saveData = grid.saveData();
        for (var i = 0; i < count; i++) {
            if (i != index) {
                var item = saveData[i];
                if (item.platformMultiId && item.platformMultiId == args.get_value()) {
                    $common.alert("同一时效只能配置一条规则");
                    args.set_cancel(true);
                    return;
                }
            }
        }
    },
    doSave: function (sender) {
        var form = this.get_form();
        var grid = form.multiStockGrid;
        var saveData = form.saveData();
        var calculate = saveData.calculate;
        if (!calculate) {
            calculate = {};
        }
        calculate.percentage = form.txtPercentage.get_value();
        calculate.calculateMode = form.drCalculateMode.get_value();
        calculate.ruleCalQty = form.txtVirtualQty.get_value();
        if (calculate.calculateMode == 3 && calculate.ruleCalQty == 0) {
            $common.alert("除数不能为0！");
            return;
        }
        saveData.ktypeDataSource = this.get_context('ktypeList');
        saveData.formula = this.buildFormulaFromCalculate(calculate);
        form.set_enabled(false);
        saveData.ruleCron = JSON.stringify(calculate);
        saveData.ktypeIds = saveData.ktypeIds.join(',');
        saveData.ktypeNames = form.drKtype.get_text();
        if (saveData.eshopMultiStockSyncEnabled) {
            //获取时效配置
            var gridData = grid.saveData();
            saveData.multiStockSyncDetailList = this.buildMultiStockSyncDetailList(saveData, gridData);
        }
        var startObj = $saleUtils._getStartTiming('网店->网店商品管理->' + this.get_context('title'));
        var url = '/sale/eshoporder/product/saveDefaultRule';
        var mode = this.get_mode();
        if (mode && (mode == 'custom_edit' || mode == 'custom_add')) {
            url = '/sale/eshoporder/product/saveCustomRule';
        }
        var response = $common.ajaxSync({
            url: url,
            data: saveData,
            type: 'post',
            router: 'ngp'
        });
        $saleUtils._logEndTiming(startObj);
        if (response.code != 200) {
            $common.showError(response.message);
            form.set_enabled(true);
            return;
        }

        if (response.data && response.data.length > 0) {
            $common.showError(response.data);
            form.set_enabled(true);
            return;
        }
        $common.showOk("库存规则编辑成功");
        form.doOk();
    },
    buildFormulaFromCalculate: function (calculate) {
        if (!calculate) {
            return;
        }
        return "可销售库存*" + math.round(calculate.percentage, 2) + "%"
            + this._getMode(calculate.calculateMode) + math.round(calculate.ruleCalQty, 2);
    },
    buildMultiStockSyncDetailList: function (saveData, gridData) {
        if (!gridData) {
            return;
        }
        var multiStockSyncDetailList = [];
        var multiTypeSource = this.get_context('allowMultiTypes');
        for (var i = 0; i < gridData.length; i++) {
            var item = gridData[i];
            //未选择时效的行,不保存
            if (!item || !item.platformMultiId) {
                continue;
            }
            var data = {}
            data.ruleId = saveData.id;
            data.platformMultiId = item.platformMultiId;
            data.syncType = item.syncType;
            //公式
            if (item.syncType == 0) {
                data.syncCron = item.syncCron;
                if (!data.calculate) {
                    data.calculate = this.getDefaultCalculate();
                }
                if (!data.syncCron) {
                    data.syncCron = JSON.stringify(data.calculate);
                }
                data.fixQty = 0;
            } else {
                //固定数量
                data.syncCron = "";
                data.fixQty = item.syncTimeQty;
            }
            data.platformMultiName = this.getMultiTypeName(multiTypeSource, item.platformMultiId);
            //时效
            data.platformMultiType = 0;
            multiStockSyncDetailList.push(data);
        }
        return multiStockSyncDetailList;
    },
    getMultiTypeName: function (multiTypeSource, multiTypeId) {
        if (!multiTypeId || !multiTypeSource) {
            return null;
        }
        for (var i = 0; i < multiTypeSource.length; i++) {
            var item = multiTypeSource[i];
            if (item.platformMultiId == multiTypeId) {
                return item.platformMultiName;
            }
        }
    },
    dispose: function () {
        sale.product.EditRuleFormAction.callBaseMethod(this, 'dispose');
    }
};
sale.product.EditRuleFormAction.registerClass('sale.product.EditRuleFormAction', Sys.UI.PageAction);

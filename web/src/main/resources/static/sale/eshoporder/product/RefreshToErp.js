Type.registerNamespace('sale.product');

sale.product.RefreshToErpAction = function () {
    sale.product.RefreshToErpAction.initializeBase(this);
};

sale.product.RefreshToErpAction.prototype = {

    _activeTimer: null,
    _isStop: true,
    _isDownloaded : false,
    _ktypeList: null,
    _initStockSaveData: null,
    _supportCreateInitStock: [],
    _allowPropsRsp: false,
    _productPageInfo: [],
    _productRefreshSupportTypes: [],
    _unRelationSkuNum: 0,

    context: function (cb) {
        var params = this.get_pageParams();
        var otypeId = 0;
        var selectedEshop;
        if (params.selectedEshopList && params.selectedEshopList.length > 0) {
            selectedEshop = params.selectedEshopList[0];
            otypeId = selectedEshop.otypeId;
        }
        if (otypeId === 0 && params.eshopList && params.eshopList.length > 0) {
            selectedEshop = params.eshopList[0];
            otypeId = selectedEshop.otypeId;
        }
        var stockStates = $common.ajaxSync({
            url: "sale/eshoporder/relation/getStockState/stockState/" + otypeId,
            type: 'get',
            router: 'ngp'
        });
        cb({
            eshopList: params.eshopList,
            otypeId: otypeId,
            selectedEshop: selectedEshop,
            stockStates: stockStates.data
        });
    },
    initialize: function RefreshToErpAction$initialize() {
        sale.product.RefreshToErpAction.callBaseMethod(this, 'initialize');
        var selectedEshop = this.get_context("selectedEshop");
        this.get_form().drEshop.set_value(selectedEshop.otypeId);
        this._initParams(selectedEshop.otypeId, selectedEshop.eshopType);
    },

    _initParams: function (otypeId, eshopType) {
        var _this = this;
        $common.ajax({
            url: "sale/eshoporder/relation/getShopTypeSupportRefreshProductPageInfo?shopType=" + eshopType,
            data: null,
            router: 'ngp',
            success: function (productPageInfoRsp) {
                _this._productPageInfo = productPageInfoRsp && productPageInfoRsp.data ? productPageInfoRsp.data : null;
                _this._supportCreateInitStock = _this._productPageInfo ? _this._productPageInfo.supportCreateInitStockShopType : [];
                _this._ktypeList = _this._productPageInfo ? _this._productPageInfo.ktypeList : [];
                _this._productRefreshSupportTypes = _this._productPageInfo ? _this._productPageInfo.productRefreshSupportTypes : [];
            }
        });
    },

    doEshopSelectedChange: function () {
        var form = this.get_form();
        var selectedEshop = form.drEshop.get_selectedItem();
        var otypeId = selectedEshop.otypeId;
        this.set_context("selectedEshop", selectedEshop);
        this.set_context("otypeId", selectedEshop.otypeId);
        var response = $common.ajaxSync({
            url: "sale/eshoporder/relation/getStockState/stockState/" + otypeId,
            type: 'get',
            router: 'ngp'
        });
        if (response.data){
            form.statusDropEdit.set_dataSource(response.data);
            form.statusDropEdit.set_selectedIndex(0);
        }
        this._initParams(selectedEshop.otypeId, selectedEshop.eshopType);
    },

    refreshTypeChange: function (sender) {
        var type = sender.get_value();
        if (type === 1) {
            this.get_form().numIdPanel.set_visible(true);
            this.get_form().timePanel.set_visible(false);
        } else {
            this.get_form().numIdPanel.set_visible(false);
            this.get_form().timePanel.set_visible(true);
        }
    },

    numIdTypeChange: function (sender) {
        var type = sender.get_value();
        var numIdTypeMap = {
            1: "商品ID",
            2: "商品货号",
        }
        var typeName = numIdTypeMap[type];
        this.get_form().importNumIdBtn.set_text("导入" + typeName);
        this.get_form().numIdEdit.set_nullDisplayText("多个" + typeName + "以英文逗号分隔，例如(AAA,BBB,CCC)");
    },

    doStart: function () {
        var startObj = $saleUtils._getStartTiming('网店->网店商品管理->刷新网店商品');
        var form = this.get_form();
        var _this = this;
        //先区分是按什么刷新
        var data = form.saveData();

        var reqParams = {};
        //0:按时间刷新 1:按商品编号刷新
        if (data.refreshType === 0) {
            reqParams.eshopId = data.otypeId;
            reqParams.stockState = data.status;
            //选全部没有开始、结束时间
            if (data.startTime === "" && data.endTime === "") {
                reqParams.isIncrement = 0;
            } else {
                reqParams.startTime = data.startTime;
                reqParams.endTime = data.endTime;
                reqParams.isIncrement = 1;
            }
        } else {
            reqParams.eshopId = data.otypeId;
            //1=按后台商品ID下载,2=按商品货号下载
            if (data.numIds != null && data.numIds.length > 0) {
                if (data.productNumType === 1) {
                    reqParams.numIds = data.numIds.split(",");
                } else {
                    reqParams.articleNumber = data.numIds;
                }
            } else {
                var msg = '';
                if (data.productNumType === 1) {
                    msg = "请输入商品ID";
                } else {
                    msg = "请输入商品货号";
                }
                $common.alert(msg);
                return;
            }
        }
        form.drEshop.set_enabled(false);
        form.radioType.set_enabled(false);
        if(form.timePanel){
            form.timePanel.set_enabled(false);
        }
        if(form.numIdPanel){
            form.numIdPanel.set_enabled(false);
        }
        form.startBtn.set_visible(false);
        form.stopBtn.set_visible(true);
        var _eshopId = data.otypeId;
        $common.ajax({
            url: '/sale/eshoporder/product/doRefresh',
            data: reqParams,
            router: 'ngp',
            waiting: '正在刷新商品,请稍等',
            success: function (response) {
                if (response.code !== '200') {
                    form.startBtn.set_visible(true);
                    form.stopBtn.set_visible(false);
                    $common.alert(response.message);
                    return;
                }
                _this._isDownloaded = true;
                _this.taskId = response.data;
                _this._isStop = false;
                _this._activeTimer = new Sys.Timer();
                var intervalCount = 500;
                _this._activeTimer.set_interval(intervalCount);
                _this._activeTimer.set_enabled(true);
                _this._activeTimer.add_tick(Function.createDelegate(this, function () {
                    _this.getMessage(response, _eshopId, startObj);
                }));
            }
        });
    },

    doStop: function () {
        var form = this.get_form();
        var taskId = this.taskId;
        var that = this;
        var url = "sale/eshoporder/common/killTask/taskId" + taskId;
        $common.ajax({
            url: url,
            type: 'get',
            router: 'ngp',
            success: function (res){
                if(res.code == 200){
                    that._isStop = true;
                    that._activeTimer.set_enabled(false);
                    that._activeTimer = null;
                    that.taskId = "";
                    form.startBtn.set_visible(true);
                    form.stopBtn.set_visible(false);
                    form.drEshop.set_enabled(true);
                    form.radioType.set_enabled(true);
                    if(form.timePanel){
                        form.timePanel.set_enabled(true);
                    }
                    if(form.numIdPanel){
                        form.numIdPanel.set_enabled(true);
                    }
                    window.setTimeout(function () {
                        form.processMemo.set_text("");
                    }, 500);
                }
            }
        });
    },

    _getSysDataValue: function (subName) {
        var status = $common.ajaxSync({
            url: "sale/eshoporder/relation/getSysData?subName=" + subName,
            router: 'ngp',
            type: 'get'
        });
        return status.data;
    },

    //生成本地商品
    doCreatePtype: function () {
        var selectedEshop = this.get_context("selectedEshop");
        if (!selectedEshop) {
            $common.alert("请选择网店");
            return;
        }
        var otypeId = selectedEshop.otypeId;
        var shopType = selectedEshop.eshopType;

        this._allowPropsRsp = $ms.ngpConfig.Sys.sysIndustryEnabledProps;
        var status = $ms.ngpConfig.Sys.sysGlobalIniover;
        var costMode1 = this._getSysDataValue("baseinfoPtypeCostmodeEnableA");//全月
        var costMode2 = this._getSysDataValue("baseinfoPtypeCostmodeEnableB");//个别计价
        var costMode3 = this._getSysDataValue("baseinfoPtypeCostmodeEnableC");//移动加权
        var hasCostModeAB = (costMode1 || costMode3) == false
        var onlyC = (hasCostModeAB && costMode2) == true;
        var costModeList = [];
        if (costMode1) {
            costModeList.push({"value": 0, "text": "全月一次加权平均"});
        }
        if (costMode3) {
            costModeList.push({"value": 2, "text": "移动加权平均"});
        }
        var supportEshop = this._supportCreateInitStock;
        var _this = this;
        if (supportEshop.includes(shopType.toString())) {
            if ((!status) && (!onlyC)) {
                var custo = this.get_form().initStock.showModal("生成期初库存", {
                    "ktypeList": this._ktypeList,
                    "costModeList": costModeList
                });
                custo.add_ok(function (frm, args) {
                    _this._initStockSaveData = custo.saveData();
                    var ktypeItem = _this.get_form().ktype.get_selectedItem();
                    _this._initStockSaveData.fullname = ktypeItem.fullname;
                    _this._createPtype(otypeId);
                });
            } else {
                _this._createPtype(otypeId);
            }
        } else {
            _this._createPtype(otypeId);
        }
    },

    _createPtype: function (otypeId) {
        var form = this.get_form();
        var attrRelations = [];
        if (this._allowPropsRsp) {
            attrRelations = this._doGetAttrRelation(form, otypeId);
            if (!attrRelations) {
                return;
            }
        }
        this._executeCreatePtype(attrRelations);
    },

    _executeCreatePtype: function (attrRelations) {
        var form = this.get_form();
        var selectedEshop = this.get_context("selectedEshop");
        var otypeId = selectedEshop.otypeId;
        var createPtypeParams = new Object();
        createPtypeParams.eshopId = otypeId;
        if (this._initStockSaveData) {
            createPtypeParams.ktypeId = this._initStockSaveData.ktypeId;
            createPtypeParams.ktypeName = this._initStockSaveData.fullname;
            createPtypeParams.costCalculate = this._initStockSaveData.costCalculate;
            createPtypeParams.createInitStock = this._initStockSaveData.isCreateInitStock;
        }
        createPtypeParams.attrRelations = attrRelations;
        createPtypeParams.createPropPtype = this._allowPropsRsp;
        var parameter = {
            process: "createPtype",
            tittle: "生成系统属性商品",
            tipsType: "createPtype",
            createPtypeParams: createPtypeParams,
            totalSku: this._unRelationSkuNum
        };
        var modal = new Sys.UI.Form(form);
        modal.showModal("sale/eshoporder/product/process.gspx", parameter);
        modal.add_closed(function (pop) {
            if (!pop.result) {
                return;
            }
            form.result = true;
            form.doOk();
        });
    },

    _doGetAttrRelation: function (form, otypeId) {
        var attrResponse = $common.ajaxSync({
            url: "sale/eshoporder/product/queryAttrRelationsByShop",
            data: otypeId,
            router: 'ngp'
        });
        if (attrResponse.code != 200) {
            alert(attrResponse.message);
            return [];
        }
        var attrList = attrResponse.data;
        if (!attrList || attrList.length == 0) {
            return attrList;
        }
        var relationList = [];
        var unRelationList = [];
        for (var i = 0; i < attrList.length; i++) {
            var attr = attrList[i];
            if (attr.propId && attr.propId > 0) {
                relationList.push(attr);
                continue;
            }
            unRelationList.push(attr);
        }
        if (unRelationList.length === 0) {
            return attrList;
        }
        var parameter = {
            unRelationProps: unRelationList
        };
        var modal = new Sys.UI.Form(form);
        var _this = this;
        modal.showModal("sale/eshoporder/product/AttrRelationForm.gspx", parameter);
        modal.add_ok(function (frm) {
            var attrs = frm.data;
            if (attrs && attrs.length > 0) {
                for (var i = 0; i < attrs.length; i++) {
                    relationList.push(attrs[i]);
                }
            }
            _this._executeCreatePtype(relationList);
        })
    },

    showUnRelationProductCount: function (eshopId) {
        var form = this.get_form();
        if (!form || !form.createLocalProductBlock) {
            return;
        }
        var createLocalEnabled = $saleUtils.getPower('eshoporder.product.createLocal');
        if(!createLocalEnabled){
            return;
        }
        var params = {
            otypeIdList: [eshopId],
            mappingState: 2
        };
        var res = $common.ajaxSync({
            url: "/sale/eshoporder/product/skuUnRelationCount",
            data: params,
            router: 'ngp'
        });
        var count = res.data;
        if (count > 0) {
            form.createLocalProductBlock.set_visible(true);
            form.unRelationNum.set_text(count);
            this._unRelationSkuNum = count;
        }
    },

    getMessage: function (processId, eshopId, startObj) {
        var form = this.get_form();
        if (null == form) {
            return;
        }
        var reqParam = new Object();
        reqParam.requestStr = processId.data;
        var _this = this;
        $common.ajax({
            url: "sale/eshoporder/common/getProcessMsg",
            data: reqParam,
            router: 'ngp',
            success: function (res) {
                var obj = res.data;
                if (!obj) {
                    return;
                }
                if (obj.completed) { //已经结束
                    if (_this._activeTimer) {
                        _this._activeTimer.set_enabled(false);
                    }
                    if (form) {
                        form.processMemo.set_text(obj.message);
                        form.startBtn.set_visible(true);
                        form.stopBtn.set_visible(false);
                    }
                    _this._isStop = true;
                    _this.showUnRelationProductCount(eshopId);
                    if (startObj) {
                        startObj.batchCount = obj.successCount;
                    }
                    $saleUtils._logEndTiming(startObj);
                }
                if (obj.message && form) {
                    form.processMemo.set_text(obj.message);
                }
            }
        });
    },

    doImportInit: function (sender) {
        var form = sender.get_form();
        var popForm = new Sys.UI.Form(sender, true);
        popForm.add_ok(function (pop) {
            form.numIdEdit.set_value(pop.listId);
        });
        var title = form.importNumIdBtn.get_text();
        popForm.showModal("sale/eshoporder/eshopproduct/EshopProductArticleNumberImport.gspx?title=" + title);
    },

    doClose: function (sender, eventArgs){
        var form = sender.get_form();
        eventArgs.set_canClose(false);
        form.result = this._isDownloaded;
        if(this._isStop){
            form.close(true);
        }else {
            $common.confirm("刷新网店商品还未完成，是否确认要退出？退出之后后台会继续完成刷新",function (r){
                if(r){
                    form.close(true);
                }
            })
        }
    },

    dispose: function () {
        sale.product.RefreshToErpAction.callBaseMethod(this, 'dispose');
    }
};
sale.product.RefreshToErpAction.registerClass('sale.product.RefreshToErpAction', Sys.UI.PageAction);

package com.wsgjp.ct.sale.biz.api.wms;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.ngp.wms.sdk.dto.StragegyDTO;
import com.wsgjp.ct.sale.biz.api.request.wms.*;
import com.wsgjp.ct.sale.biz.api.response.BaseResponse;
import com.wsgjp.ct.sale.biz.api.response.GeneralResponse;
import com.wsgjp.ct.sale.biz.api.response.openwms.OnlineFreightInterceptResponse;
import com.wsgjp.ct.sale.biz.api.response.openwms.WmsApiResult;
import com.wsgjp.ct.sale.biz.api.response.openwms.WmsStrategyResult;
import com.wsgjp.ct.sale.biz.eshoporder.api.response.PutStorageResponse;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.SubmitPutStorageDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.openwms.ProcessRequestForSale;
import com.wsgjp.ct.sale.biz.jarvis.dto.openwms.WmsOrderQueryDTO;
import com.wsgjp.ct.sale.biz.jarvis.dto.response.RebuildMessageVo;
import com.wsgjp.ct.sale.biz.jarvis.dto.response.WmsRebuildMessageVo;
import com.wsgjp.ct.sale.biz.shopsale.common.FeignResult;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@FeignClient(name = "wms", contextId = "wms-wms")
@Component
public interface WmsApi {
    /**
     * 截停任务单
     *
     * @param vchcodes 单据vchcodes
     * @param lockMemo 截停备注
     * @return
     */
    @PostMapping("/wms/deliver/lockDeliverBillsApi")
    GeneralResponse<BaseResponse> deliverLockDeliverBills(@RequestBody LockDeliverBillsRequest request);

    /**
     * 取消截停任务单
     *
     * @param vchcodes 单据vchcodes
     * @param lockMemo ""
     * @return
     */
    @PostMapping("/wms/deliver/cancelLockDeliverBillsApi")
    GeneralResponse<BaseResponse> deliverCancelLockDeliverBills(@RequestBody LockDeliverBillsRequest request);

    @PostMapping("/wms/WarehousePlatfromBusiness/syncWarehouseStatusInfoToPlatform")
    GeneralResponse<BaseResponse> syncWarehouseStatusInfoToPlatform(@RequestBody PlatformWarehouseStatusRequest request);

    /**
     * 更新物流单同步状态
     *
     * @param bills 待更新订单列表
     * @return 更新结果
     */
    @PostMapping("/wms/deliver/updateSyncState")
    GeneralResponse<BaseResponse> updateSyncState(List<UpdateSyncStateBill> bills);

    /**
     * 获取对应核销状态下的单据详情
     * BillDeliverDTO wms和sale使用的实体有区别 无法映射
     *
     * @param request 请求参数
     * @return 结果
     */
    @PostMapping("/wms/verificate/queryVerificateBillInfo")
    FeignResult<PageResponse<Map>> queryVerificateBillInfo(@RequestBody PageRequest<VerificateRequest> request);

    @PostMapping("/wms/verificate/queryVerificateHeaderInfo")
    FeignResult<PageResponse<Map>> queryVerificateHeaderInfo(@RequestBody PageRequest<VerificateRequest> request);

    /**
     * 获取每个核销状态下的条数
     *
     * @param request 请求参数
     * @return 结果
     */
    @PostMapping("/wms/verificate/queryVerificateStateCount")
    FeignResult<VerificateStateCountResponse> queryVerificateStateCount(@RequestBody VerificateRequest request);

    /**
     * 进行核销
     *
     * @param request 请求参数
     * @return 结果
     */
    @PostMapping("/wms/verificate/doVerificate")
    FeignResult<VerificateResponse> doVerificate(@RequestBody VerificateRequest request);


    /**
     * 系统发货
     *
     * @param request
     * @return
     */
    @PostMapping("/wms/verificate/doYunLiLocalSend")
    FeignResult<VerificateResponse> doYunLiLocalSend(@RequestBody VerificateRequest request);

    /**
     * 根据核销码查询商品详情
     * BillDeliverDTO wms和sale使用的实体有区别 无法映射
     *
     * @param request 请求参数
     * @return 结果
     */
    @PostMapping("/wms/verificate/queryBillInfoByNumber")
    FeignResult<List<Map>> queryBillInfoByNumber(@RequestBody VerificateRequest request);


    @PostMapping("/wms/freightInfo/doPlateformMark?orderMark={orderMarkCode}")
    GeneralResponse<BaseResponse> doPlateformMark(@PathVariable("orderMarkCode") String orderMarkCode,
                                                  @RequestBody List<DoPlateformMarkRequest> doPlateformMarkRequest);


    /**
     * "获取运力列表
     */
    @PostMapping("/wms/freightYunLi/getPlatformTemplateList")
    FeignResult<List<YunLiTemplate>> getPlatformTemplateList(@RequestBody Map param);


    /**
     * 保存运力
     */
    @PostMapping("/wms/freightYunLi/doSaveYunLiTemplate")
    FeignResult doSaveYunLiTemplate(@RequestBody YunLiTemplate param);

    /**
     * 确认运力
     */
    @PostMapping("/wms/freightYunLi/doSubmitYunLiTemplate")
    FeignResult doSubmitYunLiTemplate(@RequestBody PlatformInfoQueryParam param);

    /**
     * 取消运力
     */
    @PostMapping("/wms/freightYunLi/cancelYunLi")
    FeignResult cancelYunLi(@RequestBody List<PlatformInfoQueryParam> param);

    /**
     * 查询运力状态
     */
    @PostMapping("/wms/freightYunLi/queryYunLiStatus")
    FeignResult queryYunLiStatus(@RequestBody PlatformInfoQueryParam param);

    /**
     * 追加保存小费
     */
    @PostMapping("/wms/freightYunLi/addYunLiPrice")
    FeignResult addYunLiPrice(@RequestBody List<PlatformInfoQueryParam> param);

    /**
     * 查询取消原因"
     */
    @PostMapping("/wms/freightYunLi/queryYunLiCancelReason")
    FeignResult<Map<Integer, String>> queryYunLiCancelReason(@RequestBody List<PlatformInfoQueryParam> param);

    /**
     * 获取全渠道订单 筛选条件
     *
     * @param param
     * @return
     */
    @PostMapping("/wms/baseInfo/getStatus")
    FeignResult<HashMap> getStatus(@RequestBody Map param);

    /**
     * 全渠道订单更新序列号批次号
     *
     * @param param
     * @return
     */
    @PostMapping("/wms/verificate/updateTaskDetails")
    FeignResult<VerificateResponse> updateTaskDetails(@RequestBody VerificateModifyDetailsRequest param);

    /**
     * 全渠道订单选单核销
     *
     * @param param
     * @return
     */
    @PostMapping("/wms/verificate/verify")
    FeignResult<VerificateResponse> verifyWithOrder(@RequestBody VerificateRequest param);


    // todo:待整改
    @PostMapping("/wms/openwms/wmsOrderCenter/deliverRefundDoCancel")
    GeneralResponse<List<WmsApiResult>> deliverRefundDoCancel(@RequestBody WmsOrderQueryDTO syncInfo);


    @RequestMapping(method = RequestMethod.POST, value = "/wms/openwms/storageManagerCenter/submitPutStorage")
    PutStorageResponse submitPutStorage(@RequestBody SubmitPutStorageDTO request);

    /**
     * 判断是否需要生单（云仓不能生单直接不生单）
     */
    @RequestMapping(method = RequestMethod.POST, value = "/wms/openwms/storageManagerCenter/checkSubmitRefundBill")
    PutStorageResponse checkSubmitRefundBill(@RequestBody SubmitPutStorageDTO request);
    /**
     * 同步单号
     */
    @RequestMapping(method = RequestMethod.POST, value = "/wms/deliver/syncWayBillNew")
    GeneralResponse<BaseResponse> syncWayBillNew(@RequestBody SyncBillRequest request);



    @PostMapping({"/wms/audit/doStrategyByTaskIdsSync"})
    GeneralResponse<WmsStrategyResult> doStrategy(@RequestBody StragegyDTO request);
    @PostMapping({"/wms/audit/doStrategyByTaskIdsSync"})
    FeignResult<List<WmsRebuildMessageVo>> syncWayBillForSale(@RequestBody ProcessRequestForSale request);

    @PostMapping({"/wms/freighttrack/logisticServiceInterec"})
    FeignResult<List<OnlineFreightInterceptResponse>> wmsFreightIntercept(@RequestBody List<OnlineFreightInterceptRequest> requestList);
}

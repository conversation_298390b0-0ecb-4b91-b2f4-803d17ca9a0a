<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.publish.EshopProductPublishResultMapper">
    <insert id="addProductPublishResult">
        insert into pl_eshop_product_publish_result(id,
                                                    profile_id,
                                                    eshop_id,
                                                    publish_id,
                                                    platform_numid,
                                                    platform_status,
                                                    batch_update_status,
                                                        publish_status,image_audit_status,first_publish_success_time,last_update_publish_success_time,product_apply_id)
        values (#{id}, #{profileId}, #{eshopId}, #{publishId}, #{platformNumid}, #{platformStatus}, #{batchUpdateStatus},
                #{publishStatus},#{imageAuditStatus},#{firstPublishSuccessTime},#{lastUpdatePublishSuccessTime},#{applyId})
    </insert>
    <update id="updateProductPublishResult">
        update pl_eshop_product_publish_result
        <set>
            <if test="platformStatus!= null">
                platform_status=#{platformStatus},
            </if>
            <if test="publishStatus!= null">
                publish_status=#{publishStatus},
            </if>
            <if test="imageAuditStatus!= null">
                image_audit_status=#{imageAuditStatus},
            </if>
            <if test="platformNumid!= null">
                platform_numid=#{platformNumid},
            </if>
            <if test="applyId!= null and applyId!= ''">
                product_apply_id=#{applyId},
            </if>
            <if test="newPublishId!= null ">
                publish_id=#{newPublishId},
            </if>
            <if test="platformNumid!= null and platformNumid!='' ">
                platform_numid=#{platformNumid},
            </if>
            <if test="imageApplyId!= null and imageApplyId!='' ">
                image_apply_id=#{imageApplyId},
            </if>
            <if test="lastUpdatePublishSuccessTime!= null">
                last_update_publish_success_time=#{lastUpdatePublishSuccessTime},
            </if>
            <if test="batchUpdateStatus!= null ">
                batch_update_status=#{batchUpdateStatus},
            </if>
        </set>
        <where>
            profile_id = #{profileId} and publish_id=#{publishId} and eshop_id=#{eshopId}
        </where>
    </update>

    <delete id="cleanEshopRelationFromPublishResult">
        delete from pl_eshop_product_publish_result where profile_id= #{profileId}
        <if test="publishStatus != null">
            and publish_status &lt;&gt; #{publishStatus}
        </if>
        <if test="publishIdList!=null and publishIdList.size()!=0">
            and publish_id in
            <foreach collection="publishIdList" item="publishId" separator="," open="(" close=")">
                #{publishId}
            </foreach>
        </if>
    </delete>

    <delete id="deletePublishResultByEshopId">
        delete from pl_eshop_product_publish_result where profile_id= #{profileId}
        and publish_id=#{publishId}
        <if test="eshopIdList!=null and eshopIdList.size()!=0">
            and eshop_id not in
            <foreach collection="eshopIdList" item="eshopId" separator="," open="(" close=")">
                #{eshopId}
            </foreach>
        </if>
    </delete>

    <select id="queryPubllishResultforCleanEshopRelation"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.productpublish.EshopProductPublishResult">
        select
        id,profile_id,eshop_id,publish_id,platform_numid,platform_status,batch_update_status,publish_status,create_time,update_time,image_audit_status,product_apply_id
        as applyId,image_apply_id
        from pl_eshop_product_publish_result where profile_id=#{profileId}
        <if test="publishStatus != null">
            and publish_status=#{publishStatus}
        </if>
        <if test="publishIdList!=null and publishIdList.size()!=0">
            and publish_id in
            <foreach collection="publishIdList" item="publishId" separator="," open="(" close=")">
                #{publishId}
            </foreach>
        </if>
    </select>

    <select id="queryPubllishResultByPublishId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.productpublish.EshopProductPublishResult">
        select id,
               profile_id,
               eshop_id,
               publish_id,
               platform_numid,
               platform_status,
               batch_update_status,
               publish_status,
               create_time,
               update_time,
               image_audit_status,
               product_apply_id as applyId,
               image_apply_id
        from pl_eshop_product_publish_result
        where profile_id = #{profileId}
          and publish_id = #{publishId} limit 1
    </select>

    <select id="getOpenUrlInfo" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.productpublish.EshopProductPublishOpenUrlReponse">
        select
        res.platform_numid as platformNumId,sku.platform_sku_id as platformSkuId
        from pl_eshop_product_publish_result res
        left join pl_eshop_product_publish_sku_info sku on res.publish_id = sku.publish_id and res.profile_id = sku.profile_id
        where res.profile_id = #{profileId} and res.publish_id = #{publishId} and sku.platform_sku_id !='' limit 1
    </select>

    <select id="queryPublishResultByApplyId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.productpublish.EshopProductPublishResult">
        select id, profile_id, eshop_id, publish_id, platform_numid, platform_status, batch_update_status, publish_status, product_apply_id, first_publish_success_time, last_update_publish_success_time, image_audit_status, image_apply_id
        from pl_eshop_product_publish_result where profile_id=#{profileId} and product_apply_id=#{applyId}
    </select>

    <select id="queryPublishResultByImageApplyId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.productpublish.EshopProductPublishResult">
        select id, profile_id, eshop_id, publish_id, platform_numid, platform_status, batch_update_status, publish_status, product_apply_id, first_publish_success_time, last_update_publish_success_time, image_audit_status, image_apply_id
        from pl_eshop_product_publish_result where profile_id=#{profileId} and image_apply_id=#{imageApplyId}
    </select>
</mapper>
package com.wsgjp.ct.sale.web.eshoporder.entity.response.product;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.OperateType;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import ngp.utils.CollectionUtils;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2025/6/9 14:57
 */
public class ProductManagePageInitData {
    private List<BigInteger> selectedEshopIds;
    private List<EshopInfo> eshopInfoList;
    private List<OperateType> productOperateLogTypes;
    private List<Integer> supportSyncToSonShopTypes;
    private List<ShopType> supportModifyPlatformXcodeShopTypes;

    public List<BigInteger> getSelectedEshopIds() {
        return selectedEshopIds;
    }

    public void setSelectedEshopIds(List<BigInteger> selectedEshopIds) {
        this.selectedEshopIds = selectedEshopIds;
    }

    public List<EshopInfo> getEshopInfoList() {
        return eshopInfoList;
    }

    public void setEshopInfoList(List<EshopInfo> eshopInfoList) {
        this.eshopInfoList = eshopInfoList;
    }
    public List<Integer> getSupportSyncToSonShopTypes() {
        return supportSyncToSonShopTypes;
    }

    public void setSupportSyncToSonShopTypes(List<Integer> supportSyncToSonShopTypes) {
        this.supportSyncToSonShopTypes = supportSyncToSonShopTypes;
    }

    public void setProductOperateLogTypes(List<OperateType> productOperateLogTypes) {
        this.productOperateLogTypes = productOperateLogTypes;
    }

    public List<OperateType> getProductOperateLogTypes() {
        return productOperateLogTypes;
    }

    public String getShopTypeSourceItems(){
        if(CollectionUtils.isEmpty(getEshopInfoList())){
            return "0=淘宝";
        }
        List<String> list = new ArrayList<>();
        List<ShopType> typeList = getEshopInfoList().stream().map(EshopInfo::getEshopType).distinct().collect(Collectors.toList());
        for (ShopType shopType: typeList){
            list.add(String.format("%s=%s",shopType.getCode(),shopType.getName()));
        }
        return String.join(",", list);
    }

    public List<ShopType> getSupportModifyPlatformXcodeShopTypes() {
        return supportModifyPlatformXcodeShopTypes;
    }

    public void setSupportModifyPlatformXcodeShopTypes(List<ShopType> supportModifyPlatformXcodeShopTypes) {
        this.supportModifyPlatformXcodeShopTypes = supportModifyPlatformXcodeShopTypes;
    }
}

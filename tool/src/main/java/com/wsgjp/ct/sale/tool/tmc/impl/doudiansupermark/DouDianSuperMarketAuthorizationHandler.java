package com.wsgjp.ct.sale.tool.tmc.impl.doudiansupermark;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.eshoporder.impl.auth.AuthManager;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.entity.tmc.TmcAuthMessage;
import com.wsgjp.ct.sale.common.enums.TMCType;
import com.wsgjp.ct.sale.platform.config.EshopTmcConfig;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.sdk.entity.Etype;
import com.wsgjp.ct.sale.platform.sdk.mapper.PlatformSdkEshopBaseInfoMapper;
import com.wsgjp.ct.sale.platform.utils.BeanUtils;
import com.wsgjp.ct.sale.tool.tmc.entity.InvokeMessageEntity;
import com.wsgjp.ct.sale.tool.tmc.impl.doudiansupermark.entitys.spi.auth.AuthMessage;
import com.wsgjp.ct.sale.tool.tmc.producer.MessageHandler;
import com.wsgjp.ct.sale.tool.tmc.service.EshopTmcUtils;
import com.wsgjp.ct.sale.tool.tmc.service.SupportUtil;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.pubmessage.PubMessageCenterPip;
import com.wsgjp.ct.support.pubmessage.PubMessageCenterRequest;
import ngp.starter.web.base.GeneralResult;
import ngp.utils.CollectionUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import sun.misc.BASE64Decoder;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class DouDianSuperMarketAuthorizationHandler extends DDSNotifyBase implements MessageHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(DouDianSuperMarketAuthorizationHandler.class);
    private final EshopTmcConfig config;
    private final EshopTmcUtils eshopTmcUtils;

    public DouDianSuperMarketAuthorizationHandler(EshopTmcConfig config, EshopTmcUtils eshopTmcUtils) {
        this.config = config;
        this.eshopTmcUtils = eshopTmcUtils;
    }

    @Override
    public String invoker(InvokeMessageEntity invokeMessage) {
        String message = invokeMessage.getMessage();
        String appkey = config.getDoudianSupermarkAppSecret().replaceAll("-", "");
        String tmMessage = decrypt(message, appkey);
        if (StringUtils.isEmpty(tmMessage)) {
            return JsonUtils.toJson(new GeneralResult(10003L, "抖店超市授权解密失败", null));
        }
        AuthMessage apiRequest;
        try {
            apiRequest = JsonUtils.toObject(tmMessage, AuthMessage.class);
        } catch (Exception ex) {
            LOGGER.error("{}tmMessage数据转换成抖店超市授权信息实体出错，错误信息：{},tmMessage:{}", ShopType.DouDianSupermarket, ex.getMessage(), tmMessage, ex);
            return JsonUtils.toJson(new GeneralResult(10003L, "序列化失败", null));
        }

        if (apiRequest.getMsgType() == null && apiRequest.getMsgType() != 2) {
            return JsonUtils.toJson(new GeneralResult(10003L, "不是授权相关消息", null));
        }
        if (apiRequest.getMsg() == null) {
            return JsonUtils.toJson(new GeneralResult(10003L, "获取code失败", null));
        }
        EshopRegisterNotify eshopRegister = SupportUtil.buildNotify(apiRequest.getMsg().getAuthorityID(), invokeMessage.getShopType().getCode());
        if (null == eshopRegister) {
            LOGGER.error("抖店超市找不到对应的店铺信息,shopAccount:{},tmMessage:{},", apiRequest.getMsg().getAuthorityID(), tmMessage);
            return JsonUtils.toJson(new GeneralResult(10003L, "找不到对应的店铺信息", null));
        }
        if (apiRequest.getMsg().getActionType() == 1 || apiRequest.getMsg().getActionType() == 3) {
            EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
            eshopNotifyChange.setOnlineShopId(apiRequest.getMsg().getAuthorityID());
            eshopNotifyChange.setTradeOrderId(StringUtils.isEmpty(apiRequest.getMsg().getAuthorityID()) ? "" : apiRequest.getMsg().getAuthorityID());
            eshopNotifyChange.setType(TMCType.AUTHORIZATION_CANCEL);
            eshopNotifyChange.setContent(JsonUtils.toJson(apiRequest));
            eshopNotifyChange.setProfileId(eshopRegister.getProfileId());
            eshopNotifyChange.setEshopId(eshopRegister.getId());
            SupportUtil.saveNotifyChange(eshopNotifyChange);
            invokeMessage.setEshopId(eshopRegister.getId());
            invokeMessage.setProfileId(eshopRegister.getProfileId());
            SupportUtil.doWriteTmcMqLog(invokeMessage, eshopNotifyChange.getTradeOrderId(), TMCType.AUTHORIZATION_CANCEL.getName());
            EshopInfo eshopInfo = eshopTmcUtils.queryEshopInfo(invokeMessage.getProfileId(), eshopRegister.getId());
            if (Objects.isNull(eshopInfo)) {
                LOGGER.info("profileId:{},eshopId:{},店铺类型:{},抖店超市查询店铺信息为空!", invokeMessage.getProfileId(), invokeMessage.getEshopId(), ShopType.DouDianSupermarket.getName());
                return JsonUtils.toJson(new GeneralResult(10003L, "没有查到对应的店铺", null));
            }
            AuthManager authManager = GetBeanUtil.getBean(AuthManager.class);
            boolean refreshToken = authManager.doRefreshToken(eshopInfo.getProfileId(), eshopInfo.getOtypeId());
            if (!refreshToken) {
                PubMessageCenterRequest request = new PubMessageCenterRequest();
                List<BigInteger> etypeList = getetypeList(CurrentUser.getProfileId());
                request.setEventType("业务提醒");
                request.setSubjectType("pubMsgEshopAuthorization");
                String msg = "未知原因";
                if (apiRequest.getMsg().getActionType() == 1) {
                    msg = "授权关闭";
                }
                if (apiRequest.getMsg().getActionType() == 3) {
                    msg = "授权过期";
                }
                request.setContext("【" + eshopInfo.getFullname() + "】已经授权过期" + "，请尽快重新授权!导致过期原因:" + msg);
                request.setEtypeIds(etypeList);
                request.setTitle("网店授权");
                String url = "sale/eshoporder/eshop/EShopList.gspx";
                request.setUri(url);
                PubMessageCenterPip.Util.push(request);
            }
            return JsonUtils.toJson(new GeneralResult(0L, "success", null));
        }
        invokeMessage.setEshopId(eshopRegister.getId());
        EshopNotifyChange eshopNotifyChange = buildInsertTmcMsg(eshopRegister, apiRequest, invokeMessage);
        SupportUtil.saveNotifyChange(eshopNotifyChange);
        invokeMessage.setEshopId(eshopRegister.getId());
        invokeMessage.setProfileId(eshopRegister.getProfileId());
        SupportUtil.doWriteTmcMqLog(invokeMessage, eshopNotifyChange.getTradeOrderId(), TMCType.SAVE_TOKEN.getName());
        TmcAuthMessage tmcMessage = buildAuthMessage(eshopNotifyChange, apiRequest);
        AuthManager download = new AuthManager();
        String result = download.getTokenAndSave(tmcMessage);
        if ("ok".equals(result)) {
            return JsonUtils.toJson(new GeneralResult(0L, "success", null));
        } else {
            return JsonUtils.toJson(new GeneralResult(10003L, "保存授权信息失败", null));
        }
    }

    @Override
    public String serviceName() {
        return "douDianSupermarketAuthorization";
    }

    private EshopNotifyChange buildInsertTmcMsg(EshopRegisterNotify eshopRegister, AuthMessage apiRequest, InvokeMessageEntity invokeMessage) {
        EshopNotifyChange eshopNotifyChange = new EshopNotifyChange();
        eshopNotifyChange.setProfileId(eshopRegister.getProfileId());
        eshopNotifyChange.setOnlineShopId(apiRequest.getMsg().getAuthorityID());
        eshopNotifyChange.setTradeOrderId(StringUtils.isEmpty(apiRequest.getMsg().getCode()) ? "" : apiRequest.getMsg().getCode());
        eshopNotifyChange.setType(TMCType.SAVE_TOKEN);
        eshopNotifyChange.setEshopId(invokeMessage.getEshopId());
        eshopNotifyChange.setContent(invokeMessage.getMessage());
        return eshopNotifyChange;
    }

    private TmcAuthMessage buildAuthMessage(EshopNotifyChange change, AuthMessage apiRequest) {
        TmcAuthMessage message = new TmcAuthMessage();
        message.setAuthCode(apiRequest.getMsg().getCode());
        message.setEshopId(change.getEshopId());
        message.setShopType(ShopType.DouDianSupermarket);
        if (apiRequest.getMsg() != null) {
            message.setShopAccount(apiRequest.getMsg().getAuthorityID());
        }
        message.setProfileId(change.getProfileId());
        return message;
    }

    public static String decrypt(String sSrc, String sKey) {
        try {
            byte[] raw = sKey.getBytes("ASCII");
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            int blockSize = cipher.getBlockSize();
            IvParameterSpec iv = new IvParameterSpec(sKey.substring(0, blockSize).getBytes());
            cipher.init(Cipher.DECRYPT_MODE, skeySpec, iv);
            byte[] encrypted1 = new BASE64Decoder().decodeBuffer(sSrc);//先用base64解密
            byte[] original = cipher.doFinal(encrypted1);
            String originalString = new String(original, "utf-8");
            return originalString;
        } catch (Exception ex) {
            return null;
        }
    }

    private List<BigInteger> getetypeList(BigInteger profileId) {
        PlatformSdkEshopBaseInfoMapper baseInfoMapper = BeanUtils.getBean(PlatformSdkEshopBaseInfoMapper.class);
        List<Etype> etypeInfoList = baseInfoMapper.getEtypeList(profileId);

        List<BigInteger> etypeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(etypeInfoList)) {
            etypeList = etypeInfoList.stream().map(Etype::getId).collect(Collectors.toList());
        }
        return etypeList;
    }
}

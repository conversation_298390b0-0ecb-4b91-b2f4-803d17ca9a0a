<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopMiddleGroundMapper">
    <delete id="deletePublishStatus">
        delete
        from pl_eshop_product_publish
        where ptype_id in
        <foreach item="item" collection="params.productIds" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and publish_eshop_id = #{params.publishEshopId}
        and profile_id = #{params.profileId}
    </delete>
    <delete id="deletePublishDetails" parameterType="com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.PtypePo">
        delete
        from pl_eshop_product_publish_detail
        where ptype_id in
        <foreach item="item" collection="params.productIds" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and publish_eshop_id = #{params.publishEshopId}
        and profile_id = #{params.profileId}
    </delete>
    <select id="queryEshopProductDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.middleground.EshopPtypeDetailEntity">
        select p.id as eshopProductDetailId,
        p.whole_detail as wholeDetail,
        p.has_detail as hasDetail,
        bp.pic_url as platformPicUrl,
        b.id as id,
        b.profile_id as profileId,
        b.usercode as usercode,
        b.fullname as fullname
        From `base_ptype` b
        LEFT JOIN `pl_eshop_product_detail` p on b.profile_id = p.profile_id and b.id = p.ptype_id
        LEFT JOIN `base_ptype_pic` bp
        on bp.ptype_id = b.id and bp.profile_id = b.profile_id and bp.rowindex = 1
        <if test="param.showClassNames != null and param.showClassNames == true">
            LEFT JOIN `base_ptype` par4 ON par4.typeid = RPAD(SUBSTR(b.typeid, 1, 20), 20, '0') AND
            par4.profile_id = #{param.profileId,jdbcType=BIGINT} AND par4.classed = 1
            LEFT JOIN `base_ptype` par3 ON par3.typeid = RPAD(SUBSTR(b.typeid, 1, 15), 15, '0') AND
            par3.profile_id = #{param.profileId,jdbcType=BIGINT} AND par3.classed = 1
            LEFT JOIN `base_ptype` par2 ON par2.typeid = RPAD(SUBSTR(b.typeid, 1, 10), 10, '0') AND
            par2.profile_id = #{param.profileId,jdbcType=BIGINT} AND par2.classed = 1
            LEFT JOIN `base_ptype` par1 ON par1.typeid = RPAD(SUBSTR(b.typeid, 1, 5), 5, '0') AND
            par1.profile_id = #{param.profileId,jdbcType=BIGINT} AND par1.classed = 1
        </if>
        WHERE b.profile_id = #{param.profileId}
        and b.deleted = 0
        and b.classed = 0
        and b.pcategory in (0, 1)

        <if test="param.lefttypeid != null">
            AND b.partypeid = #{param.lefttypeid}
        </if>
        <if test="param.partypeid != null">
            AND b.partypeid = #{param.partypeid}
        </if>

        <if test="param.stoped != null">
            AND b.stoped = #{param.stoped}
        </if>
        <if test="param.filterkey != null and param.filterkey != ''">
            <!-- 模糊查询 -->
            <trim prefix="and (" prefixOverrides="or" suffix=")">
                <if test="param.filterkey.toString() == 'quick'.toString()  and param.filtervalue != null and param.filtervalue != ''">
                    b.fullname like concat('%', #{param.filtervalue,jdbcType=VARCHAR}, '%')
                    or b.usercode like concat('%', #{param.filtervalue,jdbcType=VARCHAR}, '%')
                    or b.shortname like concat('%', #{param.filtervalue,jdbcType=VARCHAR}, '%')
                    or b.namepy like concat('%', #{param.filtervalue,jdbcType=VARCHAR}, '%')
                    or b.standard like concat('%', #{param.filtervalue,jdbcType=VARCHAR}, '%')
                    or b.ptype_type like concat('%', #{param.filtervalue,jdbcType=VARCHAR}, '%')
                </if>
                <if test="param.filterkey.toString() == 'fullname'.toString() and param.filtervalue != null and param.filtervalue != ''">
                    b.fullname like concat('%',#{param.filtervalue,jdbcType=VARCHAR} ,'%')
                </if>
                <if test="param.filterkey.toString() == 'usercode'.toString() and param.filtervalue != null and param.filtervalue != ''">
                    b.usercode like concat('%', #{param.filtervalue,jdbcType=VARCHAR} ,'%')
                </if>
            </trim>
        </if>
        <if test="param.filterParameter != null">
            <include refid="product_detail_filter"/>
        </if>
    </select>

    <sql id="product_detail_filter">
        <if test="param.filterParameter.usercode != null">
            AND b.usercode like concat('%', #{param.filterParameter.usercode}, '%')
        </if>
        <if test="param.filterParameter.fullname != null">
            AND b.fullname like concat('%',
            #{param.filterParameter.fullname}, '%')
        </if>
        <if test="param.filterParameter.hasDetail == null">
        </if>
        <if test="param.filterParameter.hasDetail == true">
            AND p.has_detail = true
        </if>
        <if test="param.filterParameter.hasDetail == false">
            AND p.has_detail IS NULL
        </if>
    </sql>

    <select id="queryProductDetailCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM `pl_eshop_product_detail`
        WHERE ptype_id = #{id}
    </select>

    <insert id="batchInsertProductDetail" parameterType="java.util.List">
        insert into `pl_eshop_product_detail`(id,
        profile_id,
        ptype_id,
        whole_detail,
        shop_type,
        has_detail,
        create_time,
        update_time,
        deleted)
        values
        <foreach collection="productDetails" item="item" index="index" separator=",">
            (#{item.eshopProductDetailId},
            #{item.profileId},
            #{item.id},
            #{item.wholeDetail},
            #{item.shopType},
            #{item.hasDetail},
            DEFAULT,
            DEFAULT,
            DEFAULT)
        </foreach>
    </insert>
    <insert id="markPublish">
        insert into pl_eshop_product_publish(id, ptype_id, profile_id, publish_status, publish_eshop_id,
                                             publish_eshop_name, publish_time,
                                             deleted)
        values (#{uid}, #{id}, #{profileId}, 1, #{publishEhopId}, #{publishEshopName}, #{publishTime}, 0);
    </insert>

    <update id="updProductDetail"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.middleground.EshopPtypeDetailEntity">
        UPDATE `pl_eshop_product_detail`
        <set>
            <if test="hasDetail != null">
                has_detail = #{hasDetail},
            </if>
            <if test="wholeDetail != null">
                whole_detail = #{wholeDetail},
            </if>
            <if test="deleted != null">
                deleted = #{deleted},
            </if>
            update_time = DEFAULT
        </set>
        where id = #{eshopProductDetailId}
        and profile_id = #{profileId}
    </update>

    <select id="queryBtypeList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.middleground.BtypeEntity">
        SELECT b.*
        FROM base_btype b
        left join base_btype_bcategory bb on bb.profile_id = b.profile_id and bb.btype_id = b.id
        left join base_btype_extend be on be.profile_id = b.profile_id and be.btype_id = b.id
        WHERE b.profile_id = #{profileId}
        and be.cooperation_type = 1
        and bb.bcategory = 0
        <if test="queryDeleted">
            and b.deleted = true
        </if>
    </select>


    <select id="queryPtypeForPublishCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM `pl_eshop_product_publish` p
        WHERE p.profile_id = #{profileId}
        and p.ptype_id in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item.ptype.id}
        </foreach>
        AND p.deleted = 0
    </select>

    <select id="queryPtypeForPublish"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.middleground.EshopPublishPtypeEntity">
        SELECT p.id AS ptypePublishId,
        p.ptype_id AS ptypeId,
        p.publish_status AS publishStatus,
        p.publish_eshop_id AS publishEshop,
        p.publish_time AS publishTime
        FROM `pl_eshop_product_publish` p
        WHERE p.profile_id = #{profileId}
        and p.ptype_id in
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item.ptype.id}
        </foreach>
        AND p.deleted = 0
    </select>

    <select id="ptypeList" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.PtypePo">
        select ptype.id as id,
        ptype.fullname as fullName,
        ptype.usercode as userCode,
        if(publish.publish_status, 1, 2) as publishStatus,
        publish.publish_time as publishTime,
        publish.publish_eshop_id as publishEhopId,
        publish.publish_eshop_name as publishEshopName,
        detail.whole_detail as description,
        pic.pic_url as picUrl,
        ptype.sku_price as skuPrice
        from base_ptype ptype
        left join pl_eshop_product_publish publish
        on publish.profile_id = ptype.profile_id and publish.ptype_id =
        ptype.id and
        publish.publish_eshop_id = #{otypeId} and publish.deleted = 0
        left join pl_eshop_product_detail detail
        on detail.profile_id = ptype.profile_id and detail.ptype_id = ptype.id
        left join base_ptype_pic pic
        on pic.ptype_id = ptype.id and pic.profile_id = ptype.profile_id and pic.rowindex = 1
        where ptype.profile_id = #{profileId}
        and ptype.classed = 0
        and ptype.pcategory!=2
        and ptype.deleted = 0
        <if test="partypeid != null and partypeid != ''">
            and ptype.partypeid = #{partypeid}
        </if>
        <if test="fullName != null and fullName != ''">
            and (ptype.fullname like concat('%', #{fullName},'%'))
        </if>
        <if test="fullName == '' and filtervalue != ''">
            and (ptype.usercode like concat('%', #{filtervalue}, '%') OR ptype.fullname like concat('%', #{filtervalue},
            '%'))
        </if>
        <if test="publishStatus == 1">
            AND if(publish.publish_status, 1, 2) = 1
        </if>
        <if test="publishStatus == 2">
            AND if(publish.publish_status, 1, 2) = 2
        </if>
        <if test="userCode!=null and userCode!=''">
            AND ptype.usercode like concat('%', #{userCode}, '%')
        </if>
    </select>
    <select id="skuList" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.PtypePo">
        select sku.id                                                                                     as id,
               ptype.id                                                                                   as pid,
               sku.pic_url                                                                                as picUrl,
               ptype.fullname                                                                             as fullName,
               ptype.usercode                                                                             as userCode,
               sku.propvalue_names                                                                        as props,
               group_concat(xcode.xcode)                                                                  as xcodes,
               if(publish.publish_status, 1, 2)                                                           as publishStatus,
               publish.publish_time                                                                       as publishTime,
               publish.publish_eshop_id                                                                   as publishEhopId,
               publish.publish_eshop_name                                                                 as publishEshopName,
               IF(IF(price.retail_price is null, price_back.retail_price, price.retail_price) is null, ptype.sku_price,
                  IFNULL(IF(price.retail_price is null, price_back.retail_price, price.retail_price), 0)) AS costPrice,
               unit.id                                                                                    as unitId,
               unit.unit_name                                                                             as unitName,
               0                                                                                          as exId,
               CONCAT(ptype.id, unit.unit_name)                                                           as exPid
        from base_ptype_sku sku
                 left join base_ptype ptype on sku.profile_id = ptype.profile_id and sku.ptype_id = ptype.id
                 left JOIN base_ptype_unit unit ON ptype.id = unit.`ptype_id` AND ptype.`profile_id` = unit.`profile_id`
                 left join base_ptype_xcode xcode
                           on xcode.profile_id = sku.profile_id and xcode.ptype_id = sku.ptype_id and
                              unit.id = xcode.unit_id and sku.id = xcode.sku_id
                 left join pl_eshop_product_publish publish
                           on publish.profile_id = ptype.profile_id and publish.ptype_id = ptype.id and
                              publish.publish_eshop_id = 862576106169969796 and publish.deleted = 0
                 left join pl_eshop eshop
                           on eshop.profile_id = publish.profile_id and eshop.otype_id = publish.publish_eshop_id
                 LEFT JOIN base_ptype_price price
                           ON price.ptype_id = ptype.id AND price.unit_id = unit.id AND price.sku_id = 0 and
                              price.profile_id = sku.profile_id
                 LEFT JOIN base_ptype_price price_back
                           ON price_back.ptype_id = ptype.id AND price_back.unit_id = unit.id AND
                              price_back.sku_id = sku.id and
                              price_back.profile_id = sku.profile_id
        where ptype.profile_id = #{profileId}
          and ptype.id = #{id}
        group by sku.id, unit.unit_name;
    </select>
    <select id="getSkuList" resultType="com.wsgjp.ct.sale.platform.dto.product.SkuPublishEntity">
        select sku.id as skuId,
               sku.*
        from base_ptype_sku sku
        where profile_id = #{profileId}
          and ptype_id = #{id}
    </select>
    <select id="getUnitList" resultType="com.wsgjp.ct.sale.platform.dto.product.UnitEntity">
        select id as unitId,
               profile_id,
               ptype_id,
               unit_code,
               unit_name,
               unit_rate,
               barcode,
               create_time,
               update_time
        from base_ptype_unit
        where ptype_id = #{id}
          and profile_id = #{profileId}
    </select>
    <select id="getBtypeList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Btype">
        select btype.id,
               btype.profile_id,
               typeid,
               partypeid,
               fullname,
               usercode,
               bcategory,
               allused,
               stoped,
               deleted,
               btype.create_time,
               btype.update_time,
               shortname,
               postcode,
               tel,
               person,
               tax_number,
               bank,
               bank_account,
               memo,
               ar_warnup,
               warnup_enabled,
               fax,
               rowindex,
               namepy,
               email,
               phone,
               areatype_id,
               change_day,
               change_rate,
               sysrow,
               freighted,
               currency_id,
               tax,
               sale_discount,
               buy_discount,
               payment_days,
               freight_alias,
               balancebtype_id,
               classed,
               register_addr,
               payment_type,
               addr_lng,
               addr_lat,
               stop_time,
               create_etype_id,
               modified_etype_id,
               acc_type
        from base_btype btype
        where btype.profile_id = #{profileId}
    </select>

    <select id="getPriceList" resultType="com.wsgjp.ct.sale.platform.dto.distributor.ProductPriceInfo">
        select ptype.id                                                                    as ptypeId,
               sku.id                                                                      as skuid,
               sku.propvalue_names                                                         as properties,
               unit.id                                                                     as unitId,
               unit.unit_name                                                              as unitName,
               xcode.xcode                                                                 as skuCode,
               if(price.retail_price is null, p_price.retail_price, price.retail_price)    as retailPrice,
               if(pb_price.sale_price is null, p_pb_price.sale_price, pb_price.sale_price) as distributorPrice
        from base_ptype_sku sku
                 left join base_ptype ptype on sku.profile_id = ptype.profile_id and sku.ptype_id = ptype.id
                 left join base_ptype_unit unit on unit.profile_id = ptype.profile_id and unit.ptype_id = ptype.id
                 left join base_ptype_xcode xcode
                           on xcode.profile_id = ptype.profile_id and xcode.ptype_id = ptype.id and
                              sku.id = xcode.sku_id and
                              unit.id = xcode.unit_id and xcode.defaulted = 1

                 left join base_ptype_price price
                           on ptype.profile_id = price.profile_id and sku.id = price.sku_id and
                              ptype.id = price.ptype_id and
                              unit.id = price.unit_id
                 left join base_ptype_price p_price
                           on ptype.profile_id = p_price.profile_id and p_price.sku_id = 0 and
                              ptype.id = p_price.ptype_id and
                              unit.id = p_price.unit_id
                 left join pub_bill_price pb_price
                           on pb_price.profile_id = ptype.profile_id and pb_price.create_type = 1 and
                              pb_price.xtype_id = #{btype.id} and
                              pb_price.sku_id = 0 and pb_price.ptype_id = ptype.id and unit.id = pb_price.unit_id
                 left join pub_bill_price p_pb_price
                           on p_pb_price.profile_id = ptype.profile_id and p_pb_price.create_type = 1 and
                              p_pb_price.xtype_id = #{btype.id} and p_pb_price.sku_id = sku.id and
                              p_pb_price.ptype_id = ptype.id and
                              unit.id = p_pb_price.unit_id
        where ptype.profile_id = #{profileId}
          and ptype.id = #{id}
--           and if(pb_price.sale_price is null, p_pb_price.sale_price, pb_price.sale_price) is not null
        group by ptype.id, sku.id, unit.id;
    </select>

    <select id="getPriceListNoSkuPrice" resultType="com.wsgjp.ct.sale.platform.dto.distributor.ProductPriceInfo"
            parameterType="com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.PtypePo">
        select ptype.id                                                                    as ptypeId,
               sku.id                                                                      as skuid,
               unit.id                                                                     as unitId,
               sku.propvalue_names                                                         as properties,
               unit.unit_name                                                              as unitName,
               xcode.xcode                                                                 as skuCode,
               if(price.retail_price is null, p_price.retail_price, price.retail_price)    as retailPrice,
               if(pb_price.sale_price is null, p_pb_price.sale_price, pb_price.sale_price) as distributorPrice
        from base_ptype ptype
                 left join base_ptype_sku sku on sku.profile_id = ptype.profile_id and sku.ptype_id = ptype.id
                 left join base_ptype_unit unit on unit.profile_id = ptype.profile_id and unit.ptype_id = ptype.id
                 left join base_ptype_xcode xcode
                           on xcode.profile_id = ptype.profile_id and xcode.ptype_id = ptype.id and
                              sku.id = xcode.sku_id and
                              unit.id = xcode.unit_id and xcode.defaulted = 1

                 left join base_ptype_price price
                           on ptype.profile_id = price.profile_id and sku.id = price.sku_id and
                              ptype.id = price.ptype_id and unit.id = price.unit_id
                 left join base_ptype_price p_price
                           on ptype.profile_id = p_price.profile_id and p_price.sku_id = 0 and
                              ptype.id = p_price.ptype_id and unit.id = p_price.unit_id

                 left join pub_bill_price pb_price
                           on pb_price.profile_id = ptype.profile_id and pb_price.create_type = 1 and
                              pb_price.xtype_id = #{btype.id} and
                              pb_price.sku_id = 0 and pb_price.ptype_id = ptype.id and unit.id = pb_price.unit_id
                 left join pub_bill_price p_pb_price
                           on p_pb_price.profile_id = ptype.profile_id and p_pb_price.create_type = 1 and
                              p_pb_price.xtype_id = #{btype.id} and p_pb_price.sku_id = sku.id and
                              p_pb_price.ptype_id = ptype.id and unit.id = p_pb_price.unit_id
        where ptype.profile_id = #{profileId}
          and ptype.id = #{id}
--           and if(pb_price.sale_price is null, p_pb_price.sale_price, pb_price.sale_price) is not null
    </select>

    <select id="getPytype" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.PtypePo">
        select ptype.id                as id,
               ptype.fullname          as fullName,
               ptype.usercode          as userCode,
               detail.whole_detail     as description,
               ptype.typeid,
               ptype.partypeid,
               bb.brand_name           as brandName,
               ptype.shortname         as shortName,
               ptype.usercode          as code,
               ptype.ptype_area        as area,
               ptype.ptype_type        as ptypeType,
               ptype.protect_days_unit as protectType,
               ptype.protect_days      as protect,
               ptype.standard          as specs,
               ptype.classed           as classed,
               par.fullname            as className,
               ptype.sku_price         as skuPrice
        from base_ptype ptype
                 left join base_ptype par on ptype.partypeid = par.typeid and ptype.profile_id = par.profile_id
                 left join pl_eshop_product_detail detail
                           on detail.profile_id = ptype.profile_id and detail.ptype_id = ptype.id
                 left join base_brandtype bb on bb.profile_id = ptype.profile_id and bb.id = ptype.brand_id
        where ptype.profile_id = #{profileId}
          and ptype.id = ${id}
    </select>
    <select id="getProductPriceInfoList"
            resultType="com.wsgjp.ct.sale.platform.dto.distributor.ProductPriceInfo">
        select sku.id                                                                            as skuid,
               ptype.id                                                                          as ptypeId,
               bpu.id                                                                            as unitId,
               IF(ptype.sku_price = 0, IFNULL(upp.retail_price, 0), IFNULL(ups.retail_price, 0)) AS retailPrice
        from base_ptype_sku sku
                 left join base_ptype ptype on sku.profile_id = ptype.profile_id and sku.ptype_id = ptype.id
                 JOIN base_ptype_unit bpu ON ptype.id = bpu.`ptype_id` AND ptype.`profile_id` = bpu.`profile_id`
                 left join base_ptype_pic pic on pic.profile_id = ptype.profile_id and pic.ptype_id = ptype.id
                 left join base_ptype_xcode xcode on xcode.profile_id = ptype.profile_id and xcode.ptype_id = ptype.id
                 left join pl_eshop_product_publish publish
                           on publish.profile_id = ptype.profile_id and publish.ptype_id = ptype.id and
                              publish.publish_eshop_id = #{publishEhopId} and publish.deleted = 0
                 left join pl_eshop eshop
                           on eshop.profile_id = publish.profile_id and eshop.otype_id = publish.publish_eshop_id
                 LEFT JOIN base_ptype_price upp ON upp.ptype_id = ptype.id AND upp.unit_id = bpu.id AND upp.sku_id = 0
            AND upp.profile_id = #{profileId,jdbcType=BIGINT}
                 LEFT JOIN base_ptype_sku ps ON ps.ptype_id = ptype.id
            AND ps.profile_id = #{profileId,jdbcType=BIGINT}
                 LEFT JOIN base_ptype_price ups
                           ON ups.ptype_id = ptype.id AND ups.unit_id = bpu.id AND ups.sku_id = ps.id
                               AND ups.profile_id = #{profileId,jdbcType=BIGINT}
        where ptype.profile_id = #{profileId}
          and ptype.id = #{id}
        group by sku.id
    </select>
    <select id="getPtypePicList" resultType="com.wsgjp.ct.sale.platform.dto.product.ProductPic"
            parameterType="com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.PtypePo">
        select pic_url     picUrl,
               pic_name as picName
        from base_ptype_pic
        where ptype_id = #{id}
          and profile_id = #{profileId} order by rowindex asc
    </select>
    <select id="getXcodeList" resultType="com.wsgjp.ct.sale.platform.dto.product.SkuUnitInfo" flushCache="true"
            useCache="false">
        select sku.id as skuId,
        unit.id as unitId,
        unit.unit_name as unitName,
        IF(price.retail_price is null, price_back.retail_price, price.retail_price) as distributionPrice,
        IF(price.retail_price is null, price_back.retail_price, price.retail_price) as price,
        group_concat(xcode.xcode) as xcode,UUID() as uid
        from base_ptype_sku sku
        left join base_ptype_unit unit on sku.ptype_id = unit.ptype_id and unit.profile_id = sku.profile_id
        left join base_ptype_price price
        on price.sku_id = sku.id and price.unit_id = unit.id and price.profile_id = sku.profile_id
        left join base_ptype_price price_back on price_back.sku_id = 0 and price_back.unit_id = unit.id and
        price_back.profile_id = sku.profile_id
        left join base_ptype_xcode xcode
        on xcode.ptype_id = sku.ptype_id and unit.id = xcode.unit_id and sku.id = xcode.sku_id and
        xcode.profile_id = sku.profile_id
        where sku.ptype_id = #{id}
        and sku.profile_id = #{profileId,jdbcType=BIGINT}
        <if test="skuList!=null and skuList.size()>0">
            and sku.id in
            <foreach item="item" collection="skuList" separator="," open="(" close=")">
                #{item.id}
            </foreach>
        </if>
        group by sku.id, unit.id
    </select>
    <select id="hasPublish" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.ptype.PlEshopProductPublishDetail">
        select *
        from pl_eshop_product_publish
        where ptype_id = #{id}
          and profile_id = #{profileId}
          and publish_status = 1
          and publish_eshop_id = #{publishEhopId}
    </select>
    <select id="listPublishDetail"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.ptype.PlEshopProductPublishDetail">
        select *
        from pl_eshop_product_publish_detail
        <where>
            <if test="btypeName != null and btypeName != ''">
                and (btype_name like concat('%', #{btypeName,jdbcType=VARCHAR}, '%') or btype_phone = #{btypeName} or
                btype_code = #{btypeName})
            </if>

            <if test="skuId != null ">
                and sku_id = #{skuId}
            </if>
            <if test="unitId != null ">
                and unit_id = #{unitId}
            </if>
            <if test="ptypeId != null ">
                and ptype_id = #{ptypeId}
            </if>
        </where>
    </select>

    <select id="exists" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.ptype.PlEshopProductPublishDetail"
            parameterType="com.wsgjp.ct.sale.biz.shopsale.model.entity.ptype.PlEshopProductPublishDetail">
        select *
        from pl_eshop_product_publish_detail
        where btype_id = #{btypeId}
          and ptype_id = #{ptypeId}
          and sku_id = #{skuId}
          and unit_id = #{unitId}
    </select>
    <select id="getSkuAndUnit" resultType="com.wsgjp.ct.sale.platform.dto.product.SkuUnitInfo"
            parameterType="com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.UnitSkuQueryPageDto">
        select sku.id              as skuId,
               unit.id             as unitId,
               sku.propvalue_names as skuName,
               unit.unit_name      as unitName
        from base_ptype_sku sku
                 left join base_ptype_unit unit on sku.ptype_id = unit.ptype_id and unit.profile_id = sku.profile_id
        where sku.ptype_id = #{ptypeId}
          and sku.profile_id = #{profileId,jdbcType=BIGINT}
    </select>

    <select id="listBatch" resultType="com.wsgjp.ct.sale.common.entity.middle.vo.BatchInfoVo"
            parameterType="com.wsgjp.ct.sale.common.entity.middle.dto.ListBatchQueryDto">
        select
        distinct batch.batchno,
        batch.id,
        batch.profile_id,
        batch.create_time,
        batch.update_time,
        batch.ktype_id,
        batch.ptype_id,
        batch.sku_id,
        batch.quality_state,
        batch.produce_date,
        batch.expire_date,
        batch.qty,
        batch.sub_qty,
        batch.batch_hash,
        batch.ktype_point_id,
        batch.ktype_point_type,
        batch.batch_price,
        batch.batch_time,
        ktype.fullname as ktypeName,
        datediff(batch.expire_date, batch.produce_date) as protectDays,
        ptype.fullname as ptypeName,
        ptype.usercode as usercode,
        sku.prop_names,
        sku.propvalue_names,
        bp.pic_url,
        (batch.qty - ifnull(qty_send_batch.qty,0)) as stockSendQty
        from acc_inventory_batch batch
        left join stock_record_qty_send_batch qty_send_batch on batch.ptype_id = qty_send_batch.ptype_id and
        batch.sku_id = qty_send_batch.sku_id and batch.batchno = qty_send_batch.batchno and batch.expire_date =
        qty_send_batch.expire_date and batch.produce_date = qty_send_batch.produce_date and batch.batch_price =
        qty_send_batch.batch_price
        left join base_ktype ktype on ktype.profile_id = batch.profile_id and ktype.id = batch.ktype_id
        left join base_ptype ptype on ptype.profile_id = batch.profile_id and ptype.id = batch.ptype_id
        left join base_ptype_sku sku on sku.profile_id = batch.profile_id and sku.id = batch.sku_id
        LEFT JOIN `base_ptype_pic` bp
        on ptype.id = bp.ptype_id and ptype.profile_id = bp.profile_id and bp.rowindex = 1
        where batch.profile_id = #{profileId}
        <if test="batchnoStr!=null and batchnoStr!=''">
            and batch.batchno like CONCAT('%', #{batchnoStr}, '%')
        </if>
        <if test="ptypeStr!=null and ptypeStr!=''">
            and (ptype.fullname like CONCAT('%', #{ptypeStr}, '%') or ptype.usercode like CONCAT('%', #{ptypeStr}, '%'))
        </if>
        <!--        <if test="startTime!=null and endTime!=null">-->
        <!--            and ( batch.expire_date &gt; #{startTime} and batch.expire_date &lt; #{endTime})-->
        <!--        </if>-->
        <if test="startTime!=null">
            and batch.expire_date &gt; #{startTime}
        </if>
        <if test="endTime!=null">
            and batch.expire_date &lt; #{endTime}
        </if>
        <if test=" ktypeIdList!=null and ktypeIdList.size()>0">
            and batch.ktype_id in
            <foreach item="item" collection="ktypeIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="ktypeIdList!=null and ktypeIdList.size()>0">
            and batch.ktype_id in
            <foreach item="item" collection="ktypeIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="ptypeIdList!=null and ptypeIdList.size()>0">
            and batch.ptype_id in
            <foreach item="item" collection="ptypeIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="listKtype" resultType="com.wsgjp.ct.sale.common.entity.middle.pojo.KtypeInfo"
            parameterType="java.math.BigInteger">
        select *
        from base_ktype
        where profile_id = #{profileId}
    </select>
    <select id="existsBatch" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.ptype.PlEshopProductPublishDetail"
            parameterType="com.wsgjp.ct.sale.biz.shopsale.model.entity.ptype.PlEshopProductPublishDetail">
        select *
        from pl_eshop_product_publish_detail
        where batch_id = #{batch_id}
          and ptype_id = #{ptypeId}
          and sku_id = #{skuId}
    </select>

    <select id="listBatchLog" resultType="com.wsgjp.ct.sale.biz.shopsale.model.entity.ptype.MarkData"
            parameterType="com.wsgjp.ct.sale.biz.shopsale.model.entity.ptype.PlEshopProductPublishDetail">
        select md.*
        from pl_eshop_product_publish_detail detail
        left join mark_data md on md.id = detail.id and md.profile_id = detail.profile_id
        <where>
            detail.profile_id = #{profileId}
            <if test="ptypeId!=null">
                and detail.ptype_id = #{ptypeId}
            </if>
            <if test="skuId!=null">
                and detail.sku_id = #{skuId}
            </if>
            <if test="batchno!=null and batchno!=''">
                and detail.batchno like CONCAT('%', #{batchno}, '%')
            </if>
        </where>
        order by md.create_time desc
    </select>


    <resultMap id="ProductPublishDetailMap"
               type="com.wsgjp.ct.sale.biz.shopsale.model.entity.ptype.PlEshopProductPublishDetail">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="profileId" column="profile_id" jdbcType="BIGINT"/>
        <result property="btypeId" column="btype_id" jdbcType="BIGINT"/>
        <result property="btypeName" column="btype_name" jdbcType="VARCHAR"/>
        <result property="btypeCode" column="btype_code" jdbcType="VARCHAR"/>
        <result property="btypeStatus" column="btype_status" jdbcType="TINYINT"/>
        <result property="btypePhone" column="btype_phone" jdbcType="INTEGER"/>
        <result property="btypePerson" column="btype_person" jdbcType="VARCHAR"/>
        <result property="ptypeId" column="ptype_id" jdbcType="BIGINT"/>
        <result property="ptypeName" column="ptype_name" jdbcType="VARCHAR"/>
        <result property="ptypeCode" column="ptype_code" jdbcType="VARCHAR"/>
        <result property="skuId" column="sku_id" jdbcType="BIGINT"/>
        <result property="skuCode" column="sku_code" jdbcType="VARCHAR"/>
        <result property="properties" column="properties" jdbcType="VARCHAR"/>
        <result property="unitId" column="unit_id" jdbcType="BIGINT"/>
        <result property="unitName" column="unit_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="product_publish_detail_col">
        id,
        profile_id,
        btype_id,
        btype_name,
        btype_code,
        btype_status,
        btype_phone,
        btype_person,
        ptype_id,
        ptype_name,
        ptype_code,
        sku_id,
        sku_code,
        properties,
        unit_id,
        unit_name,
        create_time,
        update_time
    </sql>

    <insert id="insertProductPublishDetail"
            parameterType="com.wsgjp.ct.sale.biz.shopsale.model.entity.ptype.PlEshopProductPublishDetail">
        insert into pl_eshop_product_publish_detail (id, profile_id, btype_id,
        btype_name, btype_code, btype_status,
        btype_phone, btype_person, ptype_id,
        ptype_name, ptype_code, sku_id,
        sku_code, properties, unit_id,
        unit_name, create_time, update_time,
        publish_price,publish_eshop_id,batch_id,ktype_id,batchno,produce_date,expire_date,batch_price,detail_type)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=BIGINT}, #{item.profileId,jdbcType=BIGINT}, #{item.btypeId,jdbcType=BIGINT},
            #{item.btypeName,jdbcType=VARCHAR}, #{item.btypeCode,jdbcType=VARCHAR}, #{item.btypeStatus,jdbcType=BIT},
            #{item.btypePhone,jdbcType=INTEGER}, #{item.btypePerson,jdbcType=VARCHAR}, #{item.ptypeId,jdbcType=BIGINT},
            #{item.ptypeName,jdbcType=VARCHAR}, #{item.ptypeCode,jdbcType=VARCHAR}, #{item.skuId,jdbcType=BIGINT},
            #{item.skuCode,jdbcType=VARCHAR}, #{item.properties,jdbcType=VARCHAR}, #{item.unitId,jdbcType=BIGINT},
            #{item.unitName,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP},#{item.publishPrice},#{item.publishEshopId},
            #{item.batchId},#{item.ktypeId},#{item.batchno},#{item.produceDate},#{item.expireDate},#{item.batchPrice},#{item.detailType})
        </foreach>
    </insert>

    <insert id="insertBatchExtendInfo"
            parameterType="com.wsgjp.ct.sale.biz.shopsale.model.entity.ptype.MarkData">
        insert into mark_data (id, profile_id, big_data, create_time, update_time)
        values (#{id}, #{profileId}, #{bigData}, #{createTime}, #{updateTime})
    </insert>

    <update id="updateProductPublishDetail" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update pl_eshop_product_publish_detail
            <set>
                <if test="item.btypeId != null">
                    btype_id = #{item.btypeId,jdbcType=BIGINT},
                </if>
                <if test="item.btypeName != null">
                    btype_name = #{item.btypeName,jdbcType=VARCHAR},
                </if>
                <if test="item.btypeCode != null">
                    btype_code = #{item.btypeCode,jdbcType=VARCHAR},
                </if>
                <if test="item.btypeStatus != null">
                    btype_status = #{item.btypeStatus,jdbcType=BIT},
                </if>
                <if test="item.btypePhone != null">
                    btype_phone = #{item.btypePhone,jdbcType=VARCHAR},
                </if>
                <if test="item.btypePerson != null">
                    btype_person = #{item.btypePerson,jdbcType=VARCHAR},
                </if>
                <if test="item.ptypeId != null">
                    ptype_id = #{item.ptypeId,jdbcType=BIGINT},
                </if>
                <if test="item.ptypeName != null">
                    ptype_name = #{item.ptypeName,jdbcType=VARCHAR},
                </if>
                <if test="item.ptypeCode != null">
                    ptype_code = #{item.ptypeCode,jdbcType=VARCHAR},
                </if>
                <if test="item.skuId != null">
                    sku_id = #{item.skuId,jdbcType=BIGINT},
                </if>
                <if test="item.skuCode != null">
                    sku_code = #{item.skuCode,jdbcType=VARCHAR},
                </if>
                <if test="item.properties != null">
                    properties = #{item.properties,jdbcType=VARCHAR},
                </if>
                <if test="item.unitId != null">
                    unit_id = #{item.unitId,jdbcType=BIGINT},
                </if>
                <if test="item.unitName != null">
                    unit_name = #{item.unitName,jdbcType=VARCHAR},
                </if>
                <if test="item.publishPrice != null">
                    publish_price = #{item.publishPrice},
                </if>
                <if test="item.publishEshopId != null">
                    publish_eshop_id = #{item.publishEshopId},
                </if>
            </set>
            where id = #{item.id}
        </foreach>
    </update>
    <update id="updatePublish"
            parameterType="com.wsgjp.ct.sale.biz.shopsale.model.entity.ptype.PlEshopProductPublishDetail">
        update pl_eshop_product_publish
        set publish_time = #{publishTime}
        where id = #{id};
    </update>
</mapper>
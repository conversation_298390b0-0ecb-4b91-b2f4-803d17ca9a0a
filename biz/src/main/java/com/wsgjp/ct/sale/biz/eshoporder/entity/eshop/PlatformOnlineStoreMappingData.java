package com.wsgjp.ct.sale.biz.eshoporder.entity.eshop;

import com.alibaba.excel.annotation.ExcelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/2/3 16:24
 */
public class PlatformOnlineStoreMappingData implements Serializable {
    /**
     * 门店/网点ID
     */
    @ExcelProperty("网店")
    private String fullName;
    /**
     * 门店/网点ID
     */
    @ExcelProperty("网店仓库ID")
    private String platformStoreStockId;
    /**
     * 门店/网点名称
     */
    @ExcelProperty("网店仓库名称")
    private String platformStoreName;
    /**
     * 分店名称
     */
    @ExcelProperty("分店名称")
    private String platformBranchStoreName;
    /**
     * 门店/网点地址
     */
    @ExcelProperty("网店仓库地址")
    private String platformStoreAddress;
    /**
     * 门店/网点配送类型
     */
    @ExcelProperty("网店仓库类型")
    private String platformStoreType;

    @ExcelProperty("系统仓库编号")
    private String userCode;

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public String getPlatformStoreStockId() {
        return platformStoreStockId;
    }

    public void setPlatformStoreStockId(String platformStoreStockId) {
        this.platformStoreStockId = platformStoreStockId;
    }

    public String getPlatformStoreName() {
        return platformStoreName;
    }

    public void setPlatformStoreName(String platformStoreName) {
        this.platformStoreName = platformStoreName;
    }

    public String getPlatformBranchStoreName() {
        return platformBranchStoreName;
    }

    public void setPlatformBranchStoreName(String platformBranchStoreName) {
        this.platformBranchStoreName = platformBranchStoreName;
    }

    public String getPlatformStoreAddress() {
        return platformStoreAddress;
    }

    public void setPlatformStoreAddress(String platformStoreAddress) {
        this.platformStoreAddress = platformStoreAddress;
    }


    public String getPlatformStoreType() {
        return platformStoreType;
    }

    public void setPlatformStoreType(String platformStoreType) {
        this.platformStoreType = platformStoreType;
    }
}

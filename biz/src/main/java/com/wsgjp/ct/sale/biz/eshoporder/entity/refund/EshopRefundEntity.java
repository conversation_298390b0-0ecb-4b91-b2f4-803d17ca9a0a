package com.wsgjp.ct.sale.biz.eshoporder.entity.refund;

import cn.hutool.json.JSONUtil;
import com.wsgjp.ct.bill.core.handle.entity.enums.BillBusinessType;
import com.wsgjp.ct.common.enums.core.enums.BaseOrderMarkEnum;
import com.wsgjp.ct.common.enums.core.enums.MarkShowType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.EshopBuyer;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Ktype;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.refund.PlatformChangeState;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.refund.PlatformConfirmState;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.refund.PlatformRefundState;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.refund.PlatformReturnState;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopOrderMarkEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity;
import com.wsgjp.ct.sale.biz.eshoporder.util.CommonUtil;
import com.wsgjp.ct.sale.biz.jarvis.entity.BillEnclosure;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.enums.ReturnSyncStateEnum;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.platform.dto.order.entity.ShopRefundAddress;
import com.wsgjp.ct.sale.platform.entity.response.refund.RefundRejectReason;
import com.wsgjp.ct.sale.platform.enums.*;
import com.wsgjp.ct.support.context.CurrentUser;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class EshopRefundEntity extends RefundCheckBase implements Serializable {


    public EshopRefundEntity() {
        setTradeCreateTime(new Date());
        setTradeCreateTime(new Date());
        setCreateTime(new Date());
        setCreateTime(new Date());
        setReceiveBuyer(new EshopBuyer());
        setBuyer(new EshopBuyer());
        setRefundDetails(new ArrayList<>());
        setRefundApplyDetails(new ArrayList<>());
        setRefundApplyComboDetails(new ArrayList<>());
        setRefundFreights(new ArrayList<>());
        setSaleOrder(new EshopSaleOrderEntity());
        setPayDetails(new ArrayList<>());
        setCheckInDetails(new ArrayList<>());
        setAtypeId(BigInteger.ZERO);
        setTradeRefundOrderId("");
        setRefundTiming(new RefundTiming());
    }

    /**
     * 是否退预收款
     */
    private boolean advancePay;


    /**
     * -1=其他全部 0=微信、1=储值、2=预收款、3=线下打款
     */
    private RefundPayTypeEnum refundPayType;

    private String appKey;
    /**
     * 提醒标记列表
     */
    private List<BaseOrderMarkEnum> marks;

    private BigInteger id;
    //上传附件地址
    private String fileUploas;
    private String fileName;
    private String tradeRefundOrderId;
    private String tradeRefundOrderNumber;
    private BigInteger profileId;
    private BigInteger etypeId;
    private BigInteger otypeId;
    private EshopInfo eshopInfo;
    private BigInteger buyerId;
    private String etypeName;
    private Date createTime;
    private Date refundCreateTime;
    private Date refundModifyTime;
    private Date modifyTime;
    private Date tradeCreateTime;
    private Date tradeFinishTime;
    private Date tradePayTime;
    private String freightName;
    /**
     * 售后类型
     */
    private RefundTypeEnum refundTypeEnum;
    private String refundReason;
    private String reasonId;
    private EshopRefundConfigReason reason;
    private EshopRefundConfigReason platformReason;
    /**
     * 售后单创建类型
     */
    private RefundDownloadTypeEnum refundDownloadType;

    private RefundCreateTypeEnum createType;
    /**
     * 审核状态
     * 审核状态
     */
    private RefundAuditStatus confirmState;
    private Date confirmTime;
    private String confirmRemark;
    private BigInteger confirmEtypeId;
    private String confirmEtypeName;
    /**
     * 收货状态
     */
    private RefundReceiveStatus receiveState;
    private BigInteger receiveEtypeId;
    private String receiveRemark;
    private Date receiveTime;
    private BigInteger receiveBuyerId;
    /**
     * 漏换补发货人信息
     */
    private EshopBuyer receiveBuyer;
    /**
     * 补发生单信息
     */
    private RefundSaleProcessEnum refundProcessState;
    private Date refundProcessTime;
    private BigInteger processEtypeId;
    /**
     * 支付状态
     */
    private RefundPayStatus payState;
    private String payAccount;
    private Date payTime;
    private BigInteger payEtypeId;
    private String payNumber;
    private String receiveAccount;
    /**
     * 申请退款金额
     */
    private BigDecimal refundApplyTotal;
    private BigDecimal refundApplyTaxedTotal;
    private BigDecimal refundApplyTaxTotal;
    private BigDecimal refundApplyFreightFee;
    private BigDecimal refundApplyMallFee;
    private BigDecimal refundApplyServiceFee;
    private Date updateTime;
    private BigInteger ktypeId;
    private BigInteger oldKtypeId;
    private BigDecimal sellerPreferentialTotal;
    private boolean enabledPreferentialAffect;
    // 这个售后状态不受任何影响
    private RefundStatus refundState;
    // 这个售后状态会受退款支付和生单入库的影响
    private RefundStatus systemRefundState;
    private RefundPhase refundPhase;
    private String refundStatement;
    private TradeStatus tradeStatus;
    private RefundDeleteStateEnum deleted;
    private String tradeOrderId;
    // 父级交易单号
    private String platformParentOrderId;
    private BigInteger eshopOrderId;
    private BigDecimal orderFee = BigDecimal.ZERO;
    /**
     * 从订单明细构建而来的售后明细（待申请明细）
     */
    private List<EshopRefundApplyDetail> refundDetails;
    private List<EshopRefundApplyDetail> refundApplyDetails;
    private List<EshopRefundApplyDetailCombo> refundApplyComboDetails;
    private List<EshopRefundSendDetail> sendDetails;
    private List<EshopRefundSendDetailCombo> sendCombos;
    private EshopSaleOrderEntity saleOrder;
    private EshopBuyer buyer;
    private List<EshopRefundFreight> refundFreights;
    private String freightNos;
    private String freightNo;
    private List<EshopRefundPayDetail> payDetails;
    private List<EshopRefundReceiveCheckInDetail> checkInDetails;
    private BigInteger billVchcode;
    private SubmitState billPoseted;
    private boolean hasEdit;
    private boolean saveFromPage;
    private boolean mappingState;
    private String memo = "";
    private String saleorderSellerMemo;
    private String saleorderRemark;
    private String billSellerMemo;
    private String billRemark;
    private String originOrderMark;

    /**
     * 0=新增，1=修改
     */
    private RefundOperationType refundOperationType;
    private String otypeName;
    /**
     * 往来单位
     */
    private BigInteger btypeId;
    /**
     * 退款金额（退款支付时用：默认等于退款申请金额）
     */
    private BigDecimal refundTotal;

    /**
     * 售后单网店默认退款科目id
     **/
    private BigInteger atypeId;

    /**
     * 售后单网店默认退款科目名称
     **/
    private String atypeName;

    /**
     * 原始订单-订单经手人-ID
     */
    private BigInteger orderEtypeId;
    /**
     * 原始订单-订单经手人-名称
     */
    private String orderEtypeName;

    private BigInteger dtypeId;
    /**
     * 退款支付的科目
     */
    private BigInteger payATypeId;

    private RefundNoDetail noDetail;
    private boolean createBillByReceiveDetail;
    /**
     * 实际过账金额（不包含运费，服务费）
     */
    private BigDecimal billTotal;
    /**
     * 实际过账服务费
     */
    private BigDecimal billServiceFee;

    /**
     * 多仓情况下：收货明细有多个仓库时：用于记录售后单总实际过账金额
     * 实际过账金额（不包含运费，服务费）
     */
    private BigDecimal orgBillTotal;
    /**
     * 多仓情况下：收货明细有多个仓库时：用于记录售后单总实际过账服务费
     * 实际过账服务费
     */
    private BigDecimal orgBillServiceFee;

    private BigDecimal orgBillFreightFee;

    private BigDecimal restFreightFee;
    private BigDecimal restServiceFee;


    private List<EshopRefundBillReleationEntity> billReleationList;
    /**
     * 原单提交到交易单 生成的系统处理状态
     */
    private TradeProcessStateEnum tradeProcessState;
    /**
     * 是否需要将收货明细添加到售后明细中去
     */
    private boolean needRelated;
    /**
     * 当前售后单 是否关联收货明细
     */
    private boolean relatedCheckin;
    /**
     * 原单金额（从原始订单查询出来的）
     */
    private BigDecimal disedTaxedTotal;

    /**
     * 成本状态
     **/
    private RefundCostState costState;

    /**
     * 实际退货入库的仓库名称
     **/
    private String ktypeName;

    /**
     * 原退货仓库名称（发货单、或者原始订单带入）
     **/
    private String oldKtypeName;

    /**
     * 售后单业务类型
     */
    private BillBusinessType businessType = BillBusinessType.SaleNormal;

    /**
     * 关联收货记录的退货物流公司
     **/
    private String checkinFreightName;

    /**
     * 关联收货记录的退货物流单号
     **/
    private String checkinFreightBillNo;

    /**
     * 售后单主表所有物流公司拼接成的字符串
     */
    private String refundCompany;
    /**
     * 售后单主表所有物流公司编号拼接成的字符串
     */
    private String refundCompanyNumber;
    /**
     * 仓库是否为wms仓库
     */

    private Integer hasSubmitToWms;

    private LocalProcessStatus deliverBillProcessStatus;

    /**
     * 仅用于判断是否需要重新构建Buyer
     */

    private boolean hasExchangeAddress;

    private RefundSpeedType refundSpeedType;

    private BigInteger deliverVchcode;

    List<EshopOrderMarkEntity> refundMarks;
    List<EshopOrderMarkEntity> refundSpecialMarks;
    List<EshopOrderMarkEntity> refundTimeMarks;

    private String orderTags;
    private String orderTimeTags;

    // 0不支持 1支持
    private int agRefund = 0;

    private ReturnSyncStateEnum returnSyncState;

    private BigInteger deliverKtypeId;

    private Date deliverTime;

    private RefundOrderTypeEnum orderType;

    private PlatformSignStatusEnum platformSignStatus;

    List<BillEnclosure> addList;

    private Date customPayTime;

    private String btypeName;
    private String supplierName;

    private String refundOrderDetailSummary;
    private BigInteger refundOrderDetailSummaryId;


    private List<RefundOrderDetailSummary> refundOrderDetailSummaryStr;

    private String refundOrderDetailSummaryGet;

    private boolean cycle;
    // 买家申请退款金额
    private BigDecimal refundBuyerApplyTotal;
    // 退平台优惠金额
    private BigDecimal refundPlatformAmount;
    // 退国家补贴金额
    private BigDecimal refundNationalSubsidyTotal;

    private BigDecimal originalRefundApplyTaxedTotal;

    // 退平台优惠金额
    private BigDecimal originalRefundPlatformAmount;
    // 退国家补贴金额
    private BigDecimal originalRefundNationalSubsidyTotal;

    // 退平台优惠金额
    private BigDecimal beforeRefundPlatformAmount;
    // 退国家补贴金额
    private BigDecimal beforeRefundNationalSubsidyTotal;

    private boolean partDeliver;

    private boolean wmsKtype;

    // -1=无时效,12=12小时,24=24小时,48=48小时,72=72小时
    private Integer refundPromisedAgreeDuration;

    public Integer getRefundPromisedAgreeDuration() {
        if (refundPromisedAgreeDuration == null) {
            return -1;
        }
        return refundPromisedAgreeDuration;
    }

    public void setRefundPromisedAgreeDuration(Integer refundPromisedAgreeDuration) {
        this.refundPromisedAgreeDuration = refundPromisedAgreeDuration;
    }

    public boolean isWmsKtype() {
        return wmsKtype;
    }

    public void setWmsKtype(boolean wmsKtype) {
        this.wmsKtype = wmsKtype;
    }

    public RefundStatus getSystemRefundState() {
        if (systemRefundState == null) {
            return refundState;
        }
        return systemRefundState;
    }

    public void setSystemRefundState(RefundStatus systemRefundState) {
        this.systemRefundState = systemRefundState;
    }

    public boolean isPartDeliver() {
        return partDeliver;
    }

    public void setPartDeliver(boolean partDeliver) {
        this.partDeliver = partDeliver;
    }

    public BigDecimal getBeforeRefundPlatformAmount() {
        if (beforeRefundPlatformAmount == null) {
            return BigDecimal.ZERO;
        }
        return beforeRefundPlatformAmount;
    }

    public void setBeforeRefundPlatformAmount(BigDecimal beforeRefundPlatformAmount) {
        this.beforeRefundPlatformAmount = beforeRefundPlatformAmount;
    }

    public BigDecimal getBeforeRefundNationalSubsidyTotal() {
        if (beforeRefundNationalSubsidyTotal == null) {
            return BigDecimal.ZERO;
        }
        return beforeRefundNationalSubsidyTotal;
    }

    public void setBeforeRefundNationalSubsidyTotal(BigDecimal beforeRefundNationalSubsidyTotal) {
        this.beforeRefundNationalSubsidyTotal = beforeRefundNationalSubsidyTotal;
    }

    public BigDecimal getOriginalRefundPlatformAmount() {
        if (originalRefundPlatformAmount == null) {
            return BigDecimal.ZERO;
        }
        return originalRefundPlatformAmount;
    }

    public void setOriginalRefundPlatformAmount(BigDecimal originalRefundPlatformAmount) {
        this.originalRefundPlatformAmount = originalRefundPlatformAmount;
    }

    public BigDecimal getOriginalRefundNationalSubsidyTotal() {
        if (originalRefundNationalSubsidyTotal == null) {
            return BigDecimal.ZERO;
        }
        return originalRefundNationalSubsidyTotal;
    }

    public void setOriginalRefundNationalSubsidyTotal(BigDecimal originalRefundNationalSubsidyTotal) {
        this.originalRefundNationalSubsidyTotal = originalRefundNationalSubsidyTotal;
    }

    public BigDecimal getOriginalRefundApplyTaxedTotal() {
        if (originalRefundApplyTaxedTotal == null) {
            return BigDecimal.ZERO;
        }
        return originalRefundApplyTaxedTotal;
    }

    public void setOriginalRefundApplyTaxedTotal(BigDecimal originalRefundApplyTaxedTotal) {
        this.originalRefundApplyTaxedTotal = originalRefundApplyTaxedTotal;
    }

    public BigDecimal getRefundBuyerApplyTotal() {
        if (refundBuyerApplyTotal == null) {
            return BigDecimal.ZERO;
        }
        return refundBuyerApplyTotal;
    }

    public void setRefundBuyerApplyTotal(BigDecimal refundBuyerApplyTotal) {
        this.refundBuyerApplyTotal = refundBuyerApplyTotal;
    }

    public BigDecimal getRefundPlatformAmount() {
        if (refundPlatformAmount == null) {
            return BigDecimal.ZERO;
        }
        return refundPlatformAmount;
    }

    public void setRefundPlatformAmount(BigDecimal refundPlatformAmount) {
        this.refundPlatformAmount = refundPlatformAmount;
    }

    public BigDecimal getRefundNationalSubsidyTotal() {
        if (refundNationalSubsidyTotal == null) {
            return BigDecimal.ZERO;
        }
        return refundNationalSubsidyTotal;
    }

    public void setRefundNationalSubsidyTotal(BigDecimal refundNationalSubsidyTotal) {
        this.refundNationalSubsidyTotal = refundNationalSubsidyTotal;
    }

    private String freightCode;

    public String getFreightCode() {
        if (freightCode == null) {
            return "";
        }
        return freightCode;
    }

    public void setFreightCode(String freightCode) {
        this.freightCode = freightCode;
    }

    public boolean isCycle() {
        return cycle;
    }

    public void setCycle(boolean cycle) {
        this.cycle = cycle;
    }

    public BigInteger getRefundOrderDetailSummaryId() {
        if (refundOrderDetailSummaryId == null) {
            return BigInteger.ZERO;
        }
        return refundOrderDetailSummaryId;
    }

    public void setRefundOrderDetailSummaryId(BigInteger refundOrderDetailSummaryId) {
        this.refundOrderDetailSummaryId = refundOrderDetailSummaryId;
    }

    public String getRefundOrderDetailSummaryGet() {
        return refundOrderDetailSummaryGet;
    }

    public void setRefundOrderDetailSummaryGet(String refundOrderDetailSummaryGet) {
        this.refundOrderDetailSummaryGet = refundOrderDetailSummaryGet;
    }

    public List<RefundOrderDetailSummary> getRefundOrderDetailSummaryStr() {
        if (refundOrderDetailSummaryGet == null || refundOrderDetailSummaryGet.isEmpty()) {
            return new ArrayList<RefundOrderDetailSummary>();
        } else {
            return JsonUtils.toList(refundOrderDetailSummaryGet, RefundOrderDetailSummary.class);
        }
    }

    public void setRefundOrderDetailSummaryStr(List<RefundOrderDetailSummary> refundOrderDetailSummaryStr) {
        this.refundOrderDetailSummaryStr = refundOrderDetailSummaryStr;
    }

    public String getRefundOrderDetailSummary() {
        if (refundApplyDetails != null && !refundApplyDetails.isEmpty()) {
            ArrayList<RefundOrderDetailSummary> list = new ArrayList<>();
            for (EshopRefundApplyDetail refundApplyDetail : refundApplyDetails) {
                RefundOrderDetailSummary summary = new RefundOrderDetailSummary();
                summary.setPlatformPicUrl(refundApplyDetail.getPlatformPicUrl());
                summary.setPtypeName(refundApplyDetail.getPtypeName());
                summary.setApplyRefundUnitQty(refundApplyDetail.getApplyRefundUnitQty());
                list.add(summary);
            }
            return JSONUtil.toJsonStr(list);
        }
        return "";
    }

    public void setRefundOrderDetailSummary(String refundOrderDetailSummary) {
        this.refundOrderDetailSummary = refundOrderDetailSummary;
    }

    private Ktype ktype;

    public Ktype getKtype() {
        return ktype;
    }

    public void setKtype(Ktype ktype) {
        this.ktype = ktype;
    }

    public String getBtypeName() {
        if (btypeName == null) {
            return "";
        }
        return btypeName;
    }

    public void setBtypeName(String btypeName) {
        this.btypeName = btypeName;
    }

    public String getSupplierName() {
        if (supplierName == null) {
            return "";
        }
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    public Date getCustomPayTime() {
        if (customPayTime == null) {
            return new Date();
        }
        return customPayTime;
    }

    public void setCustomPayTime(Date customPayTime) {
        this.customPayTime = customPayTime;
    }

    public List<BillEnclosure> getAddList() {
        if (addList == null) {
            return new ArrayList<BillEnclosure>();
        }
        return addList;
    }

    public void setAddList(List<BillEnclosure> addList) {
        this.addList = addList;
    }

    public PlatformSignStatusEnum getPlatformSignStatus() {
        if (platformSignStatus == null) {
            return PlatformSignStatusEnum.UN_KNOW;
        }
        return platformSignStatus;
    }

    public void setPlatformSignStatus(PlatformSignStatusEnum platformSignStatus) {
        this.platformSignStatus = platformSignStatus;
    }

    public RefundOrderTypeEnum getOrderType() {
        if (orderType == null) {
            return RefundOrderTypeEnum.ESHOP_ORDER;
        }
        return orderType;
    }

    public void setOrderType(RefundOrderTypeEnum orderType) {
        this.orderType = orderType;
    }

    private boolean toWms = false;

    public boolean isToWms() {
        return toWms;
    }

    public void setToWms(boolean toWms) {
        this.toWms = toWms;
    }

    public List<EshopOrderMarkEntity> getRefundSpecialMarks() {
        if (refundSpecialMarks == null) {
            return new ArrayList<EshopOrderMarkEntity>();
        }
        return refundSpecialMarks;
    }

    public void setRefundSpecialMarks(List<EshopOrderMarkEntity> refundSpecialMarks) {
        this.refundSpecialMarks = refundSpecialMarks;
    }

    private String processTradeOrderId;

    public String getProcessTradeOrderId() {
        if (processTradeOrderId == null) {
            return "";
        }
        return processTradeOrderId;
    }

    public void setProcessTradeOrderId(String processTradeOrderId) {
        this.processTradeOrderId = processTradeOrderId;
    }

    public BigInteger getDeliverKtypeId() {
        return deliverKtypeId;
    }

    public void setDeliverKtypeId(BigInteger deliverKtypeId) {
        this.deliverKtypeId = deliverKtypeId;
    }

    public Date getDeliverTime() {
        return deliverTime;
    }

    public void setDeliverTime(Date deliverTime) {
        this.deliverTime = deliverTime;
    }

    public ReturnSyncStateEnum getReturnSyncState() {
        return returnSyncState;
    }

    public void setReturnSyncState(ReturnSyncStateEnum returnSyncState) {
        this.returnSyncState = returnSyncState;
    }

    public int getAgRefund() {
        return agRefund;
    }

    public void setAgRefund(int agRefund) {
        this.agRefund = agRefund;
    }

    public List<EshopOrderMarkEntity> getRefundTimeMarks() {
        return refundTimeMarks;
    }

    public void setRefundTimeMarks(List<EshopOrderMarkEntity> refundTimeMarks) {
        this.refundTimeMarks = refundTimeMarks;
    }

    public String getOrderTimeTags() {
        if (StringUtils.isEmpty(orderTimeTags)) {
            this.orderTimeTags = CommonUtil.buildOrderMarks(getRefundTimeMarks(), MarkShowType.ONLY_SALE_ORDER);
        }
        return orderTimeTags;
    }

    public void setOrderTimeTags(String orderTimeTags) {
        this.orderTimeTags = orderTimeTags;
    }

    /**
     * 原单发货物流公司
     */
    private String originalRefundCompany;
    /**
     * 原单发货物流单号
     */
    private String originalRefundCompanyNumber;

    private BigInteger supplierId;

    private boolean useDistributor;

    // 是否提交分销商处理 0否 1是
    private int hasCommitToDistributor;


    //    平台审核状态
    private PlatformConfirmState platformConfirmState;
    //    平台退款状态
    private PlatformRefundState platformRefundState;
    //    平台退货状态
    private PlatformReturnState platformReturnState;
    //    平台换货状态
    private PlatformChangeState platformChangeState;

    // 售后时效
    private RefundTiming refundTiming;

    private String refundDuties;

    private String refundDutyIds;

    private List<BigInteger> refundDutyIdsList;

    private Date refundFinishTime;

    private BigInteger creatorId;

    private RefundInterceptAgreeStatusEnum platformAutoInterceptAgree;
    private boolean needConfirm;

    private String buyMessage;

    private List<EshopRefundReceiveCheckInDetail> checkinDetailsNew;

    private Date refundSuccessTimerefundrefun;

    private PlEshopRefundDistributionBuyer plEshopRefundDistributionBuyer;

    private BigDecimal distributionDisedTaxedTotal;

    private BigInteger freightBtypeId;

    private String showDetails;

    private BigInteger platformRefundReason;

    private String platformRefundReasonStr;

    private PlatformRefundTypeEnum platformRefundType;
    // 退预存款
    private BigDecimal refundAdvanceTotal;
    // 退储值
    private BigDecimal refundSaveTotal;
    // 退储值本金
    private BigDecimal refundSavePrincipalTotal;
    // 退储值赠金
    private BigDecimal refundSavePresentTotal;

    private BigInteger vipCardId;

    private boolean hasChange;

    public boolean isHasChange() {
        return hasChange;
    }

    public void setHasChange(boolean hasChange) {
        this.hasChange = hasChange;
    }

    public BigInteger getVipCardId() {
        if (vipCardId == null) {
            return BigInteger.ZERO;
        }
        return vipCardId;
    }

    public void setVipCardId(BigInteger vipCardId) {
        this.vipCardId = vipCardId;
    }

    public BigDecimal getRefundAdvanceTotal() {
        if (refundAdvanceTotal == null) {
            return BigDecimal.ZERO;
        }
        return refundAdvanceTotal;
    }

    public void setRefundAdvanceTotal(BigDecimal refundAdvanceTotal) {
        this.refundAdvanceTotal = refundAdvanceTotal;
    }

    public BigDecimal getRefundSaveTotal() {
        if (refundSaveTotal == null) {
            return BigDecimal.ZERO;
        }
        return refundSaveTotal;
    }

    public void setRefundSaveTotal(BigDecimal refundSaveTotal) {
        this.refundSaveTotal = refundSaveTotal;
    }

    public BigDecimal getRefundSavePrincipalTotal() {
        if (refundSavePrincipalTotal == null) {
            return BigDecimal.ZERO;
        }
        return refundSavePrincipalTotal;
    }

    public void setRefundSavePrincipalTotal(BigDecimal refundSavePrincipalTotal) {
        this.refundSavePrincipalTotal = refundSavePrincipalTotal;
    }

    public BigDecimal getRefundSavePresentTotal() {
        if (refundSavePresentTotal == null) {
            return BigDecimal.ZERO;
        }
        return refundSavePresentTotal;
    }

    public void setRefundSavePresentTotal(BigDecimal refundSavePresentTotal) {
        this.refundSavePresentTotal = refundSavePresentTotal;
    }

    public String getPlatformRefundReasonStr() {
        if (platformRefundReasonStr == null) {
            return "";
        }
        return platformRefundReasonStr;
    }

    public void setPlatformRefundReasonStr(String platformRefundReasonStr) {
        this.platformRefundReasonStr = platformRefundReasonStr;
    }

    public PlatformRefundTypeEnum getPlatformRefundType() {
        if (platformRefundType == null) {
            return PlatformRefundTypeEnum.ALL;
        }
        return platformRefundType;
    }

    public void setPlatformRefundType(PlatformRefundTypeEnum platformRefundType) {
        this.platformRefundType = platformRefundType;
    }

    public BigInteger getPlatformRefundReason() {
        if (platformRefundReason == null) {
            return BigInteger.ZERO;
        }
        return platformRefundReason;
    }

    public void setPlatformRefundReason(BigInteger platformRefundReason) {
        this.platformRefundReason = platformRefundReason;
    }

    public String getShowDetails() {
        return showDetails;
    }

    public void setShowDetails(String showDetails) {
        this.showDetails = showDetails;
    }

    private BigDecimal orderDetailTotal;

    public BigDecimal getOrderDetailTotal() {
        if (orderDetailTotal == null) {
            return BigDecimal.ZERO;
        }
        return orderDetailTotal;
    }

    public void setOrderDetailTotal(BigDecimal orderDetailTotal) {
        this.orderDetailTotal = orderDetailTotal;
    }

    public BigInteger getFreightBtypeId() {
        if (freightBtypeId == null) {
            return BigInteger.ZERO;
        }
        return freightBtypeId;
    }

    public void setFreightBtypeId(BigInteger freightBtypeId) {
        this.freightBtypeId = freightBtypeId;
    }

    public BigDecimal getDistributionDisedTaxedTotal() {
        if (distributionDisedTaxedTotal == null) {
            return BigDecimal.ZERO;
        }
        return distributionDisedTaxedTotal;
    }

    public void setDistributionDisedTaxedTotal(BigDecimal distributionDisedTaxedTotal) {
        this.distributionDisedTaxedTotal = distributionDisedTaxedTotal;
    }

    public PlEshopRefundDistributionBuyer getPlEshopRefundDistributionBuyer() {
        return plEshopRefundDistributionBuyer;
    }

    public void setPlEshopRefundDistributionBuyer(PlEshopRefundDistributionBuyer plEshopRefundDistributionBuyer) {
        this.plEshopRefundDistributionBuyer = plEshopRefundDistributionBuyer;
    }

    public List<EshopRefundReceiveCheckInDetail> getCheckinDetailsNew() {
        if (checkinDetailsNew == null) {
            return new ArrayList<EshopRefundReceiveCheckInDetail>();
        }
        return checkinDetailsNew;
    }

    public void setCheckinDetailsNew(List<EshopRefundReceiveCheckInDetail> checkinDetailsNew) {
        this.checkinDetailsNew = checkinDetailsNew;
    }

    public String getBuyMessage() {
        return buyMessage;
    }

    public void setBuyMessage(String buyMessage) {
        this.buyMessage = buyMessage;
    }

    public boolean isNeedConfirm() {
        return needConfirm;
    }

    public void setNeedConfirm(boolean needConfirm) {
        this.needConfirm = needConfirm;
    }

    public BigInteger getCreatorId() {
        if (creatorId == null) {
            return BigInteger.ZERO;
        }
        return creatorId;
    }

    public void setCreatorId(BigInteger creatorId) {
        this.creatorId = creatorId;
    }

    private List<EshopRefundReceiveCheckInEntity> checkinList;

    public List<EshopRefundReceiveCheckInEntity> getCheckinList() {
        if (checkinList == null) {
            return new ArrayList<EshopRefundReceiveCheckInEntity>();
        }
        return checkinList;
    }

    public void setCheckinList(List<EshopRefundReceiveCheckInEntity> checkinList) {
        this.checkinList = checkinList;
    }

    private String platformRefundVersion;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileUploas() {
        return fileUploas;
    }

    public void setFileUploas(String fileUploas) {
        this.fileUploas = fileUploas;
    }

    public String getPlatformRefundVersion() {
        if (platformRefundVersion == null) {
            return "";
        }
        return platformRefundVersion;
    }

    public void setPlatformRefundVersion(String platformRefundVersion) {
        this.platformRefundVersion = platformRefundVersion;
    }

    public String getRefundDutyIds() {
        if (refundDutyIds == null) {
            return "";
        }
        return refundDutyIds;
    }

    public void setRefundDutyIds(String refundDutyIds) {
        this.refundDutyIds = refundDutyIds;
    }

    public List<BigInteger> getRefundDutyIdsList() {
        if (refundDutyIdsList == null && refundDutyIds != null && !refundDutyIds.isEmpty()) {
            return Arrays.stream(refundDutyIds.split(",")).map(BigInteger::new).collect(Collectors.toList());
        } else {
            refundDutyIdsList = new ArrayList<>();
        }
        return refundDutyIdsList;
    }

    public void setRefundDutyIdsList(List<BigInteger> refundDutyIdsList) {
        this.refundDutyIdsList = refundDutyIdsList;
    }

    public String getRefundDuties() {
        return refundDuties;
    }

    public void setRefundDuties(String refundDuties) {
        this.refundDuties = refundDuties;
    }

    private boolean delivered;
    private BaseOrderMarkEnum caseEnum;

    private String deliverFreightCompany;

    private String deliverFreightNumber;

    public String getDeliverFreightCompany() {
        if (deliverFreightCompany == null) {
            return "";
        }
        return deliverFreightCompany;
    }

    public void setDeliverFreightCompany(String deliverFreightCompany) {
        this.deliverFreightCompany = deliverFreightCompany;
    }

    public String getDeliverFreightNumber() {
        if (deliverFreightNumber == null) {
            return "";
        }
        return deliverFreightNumber;
    }

    public void setDeliverFreightNumber(String deliverFreightNumber) {
        this.deliverFreightNumber = deliverFreightNumber;
    }

    public BaseOrderMarkEnum getCaseEnum() {
        return caseEnum;
    }

    public void setCaseEnum(BaseOrderMarkEnum caseEnum) {
        this.caseEnum = caseEnum;
    }

    public RefundTiming getRefundTiming() {
        if (refundTiming == null) {
            return new RefundTiming();
        }
        return refundTiming;
    }

    public void setRefundTiming(RefundTiming refundTiming) {
        this.refundTiming = refundTiming;
    }

    public PlatformConfirmState getPlatformConfirmState() {
        return platformConfirmState;
    }

    public void setPlatformConfirmState(PlatformConfirmState platformConfirmState) {
        this.platformConfirmState = platformConfirmState;
    }

    public PlatformRefundState getPlatformRefundState() {
        return platformRefundState;
    }

    public void setPlatformRefundState(PlatformRefundState platformRefundState) {
        this.platformRefundState = platformRefundState;
    }

    public PlatformReturnState getPlatformReturnState() {
        return platformReturnState;
    }

    public void setPlatformReturnState(PlatformReturnState platformReturnState) {
        this.platformReturnState = platformReturnState;
    }

    public PlatformChangeState getPlatformChangeState() {
        return platformChangeState;
    }

    public void setPlatformChangeState(PlatformChangeState platformChangeState) {
        this.platformChangeState = platformChangeState;
    }


    public int getHasCommitToDistributor() {
        return hasCommitToDistributor;
    }

    public void setHasCommitToDistributor(int hasCommitToDistributor) {
        this.hasCommitToDistributor = hasCommitToDistributor;
    }

    public boolean isUseDistributor() {
        return useDistributor;
    }

    public void setUseDistributor(boolean useDistributor) {
        this.useDistributor = useDistributor;
    }

    public BigInteger getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(BigInteger supplierId) {
        this.supplierId = supplierId;
    }

    public String getOriginalRefundCompany() {
        return originalRefundCompany;
    }

    public void setOriginalRefundCompany(String originalRefundCompany) {
        this.originalRefundCompany = originalRefundCompany;
    }

    public String getOriginalRefundCompanyNumber() {
        return originalRefundCompanyNumber;
    }

    public void setOriginalRefundCompanyNumber(String originalRefundCompanyNumber) {
        this.originalRefundCompanyNumber = originalRefundCompanyNumber;
    }

    public String getOrderTags() {
        if (StringUtils.isEmpty(orderTags)) {
            this.orderTags = CommonUtil.buildOrderMarks(getRefundMarks(), MarkShowType.ONLY_SALE_ORDER);
        }
        return orderTags;
    }

    public void setOrderTags(String orderTags) {
        this.orderTags = orderTags;
    }

    public List<EshopOrderMarkEntity> getRefundMarks() {
        if (refundMarks == null) {
            return new ArrayList<EshopOrderMarkEntity>();
        }
        return refundMarks;
    }

    public void setRefundMarks(List<EshopOrderMarkEntity> refundMarks) {
        this.refundMarks = refundMarks;
    }

    public boolean isHasExchangeAddress() {
        return hasExchangeAddress;
    }

    public void setHasExchangeAddress(boolean hasExchangeAddress) {
        this.hasExchangeAddress = hasExchangeAddress;
    }

    public String getRefundCompany() {
        return refundCompany;
    }

    public void setRefundCompany(String refundCompany) {
        this.refundCompany = refundCompany;
    }

    public String getRefundCompanyNumber() {
        return refundCompanyNumber;
    }

    public void setRefundCompanyNumber(String refundCompanyNumber) {
        this.refundCompanyNumber = refundCompanyNumber;
    }

    public String getCheckinFreightName() {
        return checkinFreightName;
    }

    public void setCheckinFreightName(String checkinFreightName) {
        this.checkinFreightName = checkinFreightName;
    }

    public String getCheckinFreightBillNo() {
        return checkinFreightBillNo;
    }

    public void setCheckinFreightBillNo(String checkinFreightBillNo) {
        this.checkinFreightBillNo = checkinFreightBillNo;
    }

    public BillBusinessType getBusinessType() {
        if (null == businessType) {
            return BillBusinessType.None;
        }
        return businessType;
    }

    public void setBusinessType(BillBusinessType businessType) {
        this.businessType = businessType;
    }

    public boolean isMappingState() {
        return mappingState;
    }

    public void setMappingState(boolean mappingState) {
        this.mappingState = mappingState;
    }

    public TradeProcessStateEnum getTradeProcessState() {
        return tradeProcessState;
    }

    public void setTradeProcessState(TradeProcessStateEnum tradeProcessState) {
        this.tradeProcessState = tradeProcessState;
    }

    public String getPlatformParentOrderId() {
        return platformParentOrderId == null || "0".equals(platformParentOrderId) ? "" : platformParentOrderId;
    }

    public void setPlatformParentOrderId(String platformParentOrderId) {
        this.platformParentOrderId = platformParentOrderId;
    }

    public BigDecimal getSellerPreferentialTotal() {
        return sellerPreferentialTotal;
    }

    public void setSellerPreferentialTotal(BigDecimal sellerPreferentialTotal) {
        this.sellerPreferentialTotal = sellerPreferentialTotal;
    }

    public boolean isEnabledPreferentialAffect() {
        return refundDetails.stream().allMatch(p -> p.getOtherFee().compareTo(BigDecimal.ZERO) > 0);
    }

    public BigDecimal getOrderFee() {
        return orderFee;
    }

    public void setOrderFee(BigDecimal orderFee) {
        this.orderFee = orderFee;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getTradeRefundOrderId() {
        return tradeRefundOrderId;
    }

    public void setTradeRefundOrderId(String tradeRefundOrderId) {
        this.tradeRefundOrderId = tradeRefundOrderId;
    }

    public String getTradeRefundOrderNumber() {
        return tradeRefundOrderNumber;
    }

    public void setTradeRefundOrderNumber(String tradeRefundOrderNumber) {
        this.tradeRefundOrderNumber = tradeRefundOrderNumber;
    }

    public BigInteger getProfileId() {
        if (null == profileId) {
            profileId = CurrentUser.getProfileId();
        }
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getOtypeId() {
        return otypeId;
    }

    public void setOtypeId(BigInteger otypeId) {
        this.otypeId = otypeId;
    }

    public BigInteger getBuyerId() {
        if (buyerId == null) {
            buyerId = BigInteger.ZERO;
        }
        return buyerId;
    }

    public void setBuyerId(BigInteger buyerId) {
        this.buyerId = buyerId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getRefundCreateTime() {
        return refundCreateTime;
    }

    public void setRefundCreateTime(Date refundCreateTime) {
        this.refundCreateTime = refundCreateTime;
    }

    public Date getRefundModifyTime() {
        return refundModifyTime;
    }

    public void setRefundModifyTime(Date refundModifyTime) {
        this.refundModifyTime = refundModifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getTradeCreateTime() {
        return tradeCreateTime;
    }

    public void setTradeCreateTime(Date tradeCreateTime) {
        this.tradeCreateTime = tradeCreateTime;
    }

    public Date getTradeFinishTime() {
        return tradeFinishTime;
    }

    public void setTradeFinishTime(Date tradeFinishTime) {
        this.tradeFinishTime = tradeFinishTime;
    }

    public Date getTradePayTime() {
        return tradePayTime;
    }

    public void setTradePayTime(Date tradePayTime) {
        this.tradePayTime = tradePayTime;
    }


    public RefundTypeEnum getRefundTypeEnum() {
//        if (refundTypeEnum == null) {
//            refundTypeEnum = RefundTypeEnum.MONEY_GOODS;
//        }
        return refundTypeEnum;
    }

    public void setRefundTypeEnum(RefundTypeEnum refundTypeEnum) {
        this.refundTypeEnum = refundTypeEnum;
    }

    public String getRefundReason() {

        if (refundReason == null) {
            refundReason = "";
        }
        return refundReason;
    }

    public void setRefundReason(String refundReason) {
        this.refundReason = refundReason;
    }

    public String getReasonId() {
        return reasonId;
    }

    public void setReasonId(String reasonId) {
        this.reasonId = reasonId;
    }

    public RefundDownloadTypeEnum getRefundDownloadType() {
        if (refundDownloadType == null) {
            refundDownloadType = RefundDownloadTypeEnum.MANUAL_CREATE;
        }
        return refundDownloadType;
    }

    public void setRefundDownloadType(RefundDownloadTypeEnum refundDownloadType) {
        this.refundDownloadType = refundDownloadType;
    }

    public RefundAuditStatus getConfirmState() {
        if (confirmState == null) {
            confirmState = RefundAuditStatus.WAIT_AUDIT;
        }
        return confirmState;
    }

    public void setConfirmState(RefundAuditStatus confirmState) {
        this.confirmState = confirmState;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public String getConfirmRemark() {
        if (confirmRemark == null) {
            confirmRemark = "";
        }
        return confirmRemark;
    }

    public void setConfirmRemark(String confirmRemark) {
        this.confirmRemark = confirmRemark;
    }

    public String getConfirmEtypeName() {
        return confirmEtypeName;
    }

    public void setConfirmEtypeName(String confirmEtypeName) {
        this.confirmEtypeName = confirmEtypeName;
    }

    public BigInteger getConfirmEtypeId() {
        if (confirmEtypeId == null) {
            confirmEtypeId = BigInteger.ZERO;
        }
        return confirmEtypeId;
    }

    public void setConfirmEtypeId(BigInteger confirmEtypeId) {
        if (confirmEtypeId == null) {
            this.confirmEtypeId = BigInteger.ZERO;
        } else {
            this.confirmEtypeId = confirmEtypeId;
        }
    }

    public RefundReceiveStatus getReceiveState() {
        if (receiveState == null) {
            receiveState = RefundReceiveStatus.NONE;
        }
        return receiveState;
    }

    public void setReceiveState(RefundReceiveStatus receiveState) {
        this.receiveState = receiveState;
    }

    public BigInteger getProcessEtypeId() {
        if (processEtypeId == null) {
            return BigInteger.ZERO;
        }
        return processEtypeId;
    }

    public void setProcessEtypeId(BigInteger processEtypeId) {
        this.processEtypeId = processEtypeId;
    }

    public BigInteger getReceiveEtypeId() {
        if (receiveEtypeId == null) {
            receiveEtypeId = BigInteger.ZERO;
        }
        return receiveEtypeId;
    }

    public void setReceiveEtypeId(BigInteger receiveEtypeId) {
        this.receiveEtypeId = receiveEtypeId;
    }

    public String getReceiveRemark() {
        if (receiveRemark == null) {
            receiveRemark = "";
        }
        return receiveRemark;
    }

    public void setReceiveRemark(String receiveRemark) {
        this.receiveRemark = receiveRemark;
    }

    public Date getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(Date receiveTime) {
        this.receiveTime = receiveTime;
    }

    public RefundPayStatus getPayState() {
        if (payState == null) {
            payState = RefundPayStatus.NONE;
        }
        return payState;
    }

    public void setPayState(RefundPayStatus payState) {
        this.payState = payState;
    }

    public String getPayAccount() {
        if (payAccount == null) {
            payAccount = "";
        }
        return payAccount;
    }

    public void setPayAccount(String payAccount) {
        this.payAccount = payAccount;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public BigInteger getPayEtypeId() {
        if (payEtypeId == null) {
            payEtypeId = BigInteger.ZERO;
        }
        return payEtypeId;
    }

    public void setPayEtypeId(BigInteger payEtypeId) {
        this.payEtypeId = payEtypeId;
    }

    public String getPayNumber() {
        if (payNumber == null) {
            payNumber = "";
        }
        return payNumber;
    }

    public void setPayNumber(String payNumber) {
        this.payNumber = payNumber;
    }

    public String getReceiveAccount() {
        if (receiveAccount == null) {
            receiveAccount = "";
        }
        return receiveAccount;
    }

    public void setReceiveAccount(String receiveAccount) {
        this.receiveAccount = receiveAccount;
    }

    public BigDecimal getRefundApplyTotal() {
        if (refundApplyTotal == null) {
            refundApplyTotal = BigDecimal.ZERO;
        }
        return refundApplyTotal;
    }

    public void setRefundApplyTotal(BigDecimal refundApplyTotal) {
        this.refundApplyTotal = refundApplyTotal;
    }

    public BigDecimal getRefundApplyTaxedTotal() {
        if (refundApplyTaxedTotal == null || refundApplyTaxedTotal.compareTo(BigDecimal.ZERO) == 0) {
            refundApplyTaxedTotal = BigDecimal.ZERO;
        }
        return refundApplyTaxedTotal;
    }

    public void setRefundApplyTaxedTotal(BigDecimal refundApplyTaxedTotal) {
        if (refundApplyTaxedTotal == null) {
            refundApplyTaxedTotal = BigDecimal.ZERO;
        }
        this.refundApplyTaxedTotal = refundApplyTaxedTotal;
    }

    public BigDecimal getRefundApplyTaxTotal() {
        return refundApplyTaxTotal;
    }

    public void setRefundApplyTaxTotal(BigDecimal refundApplyTaxTotal) {
        this.refundApplyTaxTotal = refundApplyTaxTotal;
    }

    public BigDecimal getRefundApplyFreightFee() {
        if (refundApplyFreightFee == null) {
            refundApplyFreightFee = BigDecimal.ZERO;
        }
        return refundApplyFreightFee;
    }

    public void setRefundApplyFreightFee(BigDecimal refundApplyFreightFee) {
        this.refundApplyFreightFee = refundApplyFreightFee;
    }

    public BigDecimal getRefundApplyMallFee() {
        if (refundApplyMallFee == null) {
            refundApplyMallFee = BigDecimal.ZERO;
        }
        return refundApplyMallFee;
    }

    public void setRefundApplyMallFee(BigDecimal refundApplyMallFee) {
        this.refundApplyMallFee = refundApplyMallFee;
    }

    public BigDecimal getRefundApplyServiceFee() {
        if (refundApplyServiceFee == null) {
            refundApplyServiceFee = BigDecimal.ZERO;
        }
        return refundApplyServiceFee;
    }

    public void setRefundApplyServiceFee(BigDecimal refundApplyServiceFee) {
        this.refundApplyServiceFee = refundApplyServiceFee;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public BigInteger getKtypeId() {
//        if (ktypeId == null) {
//            ktypeId = BigInteger.ZERO;
//        }
        return ktypeId;
    }

    public void setKtypeId(BigInteger ktypeId) {
        this.ktypeId = ktypeId;
    }

    public RefundStatus getRefundState() {
        if (refundState == null) {
            refundState = RefundStatus.NONE;
        }
        return refundState;
    }

    public void setRefundState(RefundStatus refundState) {
        this.refundState = refundState;
    }

    public RefundPhase getRefundPhase() {
        if (refundPhase == null) {
            refundPhase = RefundPhase.AFTER_SALE;
        }
        return refundPhase;
    }

    public void setRefundPhase(RefundPhase refundPhase) {
        this.refundPhase = refundPhase;
    }

    public String getRefundStatement() {
        if (refundStatement == null) {
            return "";
        }
        if (refundStatement.length() > 400) {
            return StringUtils.substring(refundStatement, 0, 400);
        }
        return refundStatement;
    }

    public void setRefundStatement(String refundStatement) {
        this.refundStatement = StringUtils.isEmpty(refundStatement) ? "" : refundStatement;
    }

    public TradeStatus getTradeStatus() {
        return tradeStatus;
    }

    public void setTradeStatus(TradeStatus tradeStatus) {
        this.tradeStatus = tradeStatus;
    }

    public RefundDeleteStateEnum getDeleted() {
        if (deleted == null) {
            deleted = RefundDeleteStateEnum.NotDelete;
        }
        return deleted;
    }

    public void setDeleted(RefundDeleteStateEnum deleted) {
        this.deleted = deleted;
    }

    public String getTradeOrderId() {
        if (CommonUtil.isNullOrEmpty(tradeOrderId)) {
            tradeOrderId = "";
        }
        return tradeOrderId;
    }

    public void setTradeOrderId(String tradeOrderId) {
        this.tradeOrderId = tradeOrderId;
    }

    public BigInteger getEshopOrderId() {
        if (eshopOrderId == null) {
            eshopOrderId = BigInteger.ZERO;
        }
        return eshopOrderId;
    }

    public void setEshopOrderId(BigInteger eshopOrderId) {
        this.eshopOrderId = eshopOrderId;
    }

    public List<EshopRefundApplyDetail> getRefundDetails() {
        return refundDetails;
    }

    public void setRefundDetails(List<EshopRefundApplyDetail> refundDetails) {
        this.refundDetails = refundDetails;
    }

    public List<EshopRefundApplyDetail> getRefundApplyDetails() {
        if (refundApplyDetails == null) {
            refundApplyDetails = new ArrayList<>();
        }
        return refundApplyDetails;
    }

    public void setRefundApplyDetails(List<EshopRefundApplyDetail> refundApplyDetails) {
        this.refundApplyDetails = refundApplyDetails;
    }

    public List<EshopRefundApplyDetailCombo> getRefundApplyComboDetails() {
        if (refundApplyComboDetails == null) {
            refundApplyComboDetails = new ArrayList<>();
        }
        return refundApplyComboDetails;
    }

    public void setRefundApplyComboDetails(List<EshopRefundApplyDetailCombo> refundApplyComboDetails) {
        this.refundApplyComboDetails = refundApplyComboDetails;
    }

    public List<EshopRefundSendDetail> getSendDetails() {
        return sendDetails;
    }

    public void setSendDetails(List<EshopRefundSendDetail> sendDetails) {
        this.sendDetails = sendDetails;
    }

    public EshopInfo getEshopInfo() {
        return eshopInfo;
    }

    public void setEshopInfo(EshopInfo eshopInfo) {
        this.eshopInfo = eshopInfo;
    }

    public List<EshopRefundSendDetailCombo> getSendCombos() {
        return sendCombos;
    }

    public void setSendCombos(List<EshopRefundSendDetailCombo> sendCombos) {
        this.sendCombos = sendCombos;
    }

    public EshopSaleOrderEntity getSaleOrder() {
        return saleOrder;
    }

    public void setSaleOrder(EshopSaleOrderEntity saleOrder) {
        this.saleOrder = saleOrder;
    }

    public EshopBuyer getBuyer() {
        return buyer;
    }

    public void setBuyer(EshopBuyer buyer) {
        this.buyer = buyer;
    }

    public List<EshopRefundFreight> getRefundFreights() {
        return refundFreights;
    }

    public void setRefundFreights(List<EshopRefundFreight> refundFreights) {
        this.refundFreights = refundFreights;
    }

    public RefundOperationType getRefundOperationType() {
        return refundOperationType;
    }

    public void setRefundOperationType(RefundOperationType refundOperationType) {
        this.refundOperationType = refundOperationType;
    }

    public String getOtypeName() {
        return otypeName;
    }

    public void setOtypeName(String otypeName) {
        this.otypeName = otypeName;
    }

    public BigInteger getBtypeId() {
        if (btypeId == null) {
            return BigInteger.ZERO;
        }
        return btypeId;
    }

    public void setBtypeId(BigInteger btypeId) {
        this.btypeId = btypeId;
    }

    public BigDecimal getRefundTotal() {
        return refundTotal;
    }

    public void setRefundTotal(BigDecimal refundTotal) {
        this.refundTotal = refundTotal;
    }

    public BigInteger getAtypeId() {
        return atypeId;
    }

    public void setAtypeId(BigInteger atypeId) {
        this.atypeId = atypeId;
    }

    public List<EshopRefundPayDetail> getPayDetails() {
        return payDetails;
    }

    public void setPayDetails(List<EshopRefundPayDetail> payDetails) {
        this.payDetails = payDetails;
    }

    public List<EshopRefundReceiveCheckInDetail> getCheckInDetails() {
        return checkInDetails;
    }

    public void setCheckInDetails(List<EshopRefundReceiveCheckInDetail> checkInDetails) {
        this.checkInDetails = checkInDetails;
    }

    public String getFreightNos() {
        if (refundFreights != null && refundFreights.size() > 0) {
            List<String> nos = refundFreights.stream().filter(p -> StringUtils.isNotEmpty(p.getFreightNo())).map(EshopRefundFreight::getFreightNo).collect(Collectors.toList());
            return StringUtils.join(nos, ",");
        }
        return freightNos;
    }

    public void setFreightNos(String freightNos) {
        this.freightNos = freightNos;
    }

    public BigInteger getOrderEtypeId() {
        if (orderEtypeId == null) {
            orderEtypeId = BigInteger.ZERO;
        }
        return orderEtypeId;
    }

    public void setOrderEtypeId(BigInteger orderEtypeId) {
        this.orderEtypeId = orderEtypeId;
    }

    public String getOrderEtypeName() {
        if (orderEtypeName == null) {
            orderEtypeName = "";
        }
        return orderEtypeName;
    }

    public void setOrderEtypeName(String orderEtypeName) {
        this.orderEtypeName = orderEtypeName;
    }

    public BigInteger getReceiveBuyerId() {
        if (receiveBuyerId == null) {
            receiveBuyerId = BigInteger.ZERO;
        }
        return receiveBuyerId;
    }

    public void setReceiveBuyerId(BigInteger receiveBuyerId) {
        this.receiveBuyerId = receiveBuyerId;
    }

    public EshopBuyer getReceiveBuyer() {
        return receiveBuyer;
    }

    public void setReceiveBuyer(EshopBuyer receiveBuyer) {
        this.receiveBuyer = receiveBuyer;
    }

    public BigInteger getDtypeId() {
        if (dtypeId == null) {
            dtypeId = BigInteger.ZERO;
        }
        return dtypeId;
    }

    public void setDtypeId(BigInteger dtypeId) {
        this.dtypeId = dtypeId;
    }

    public BigInteger getPayATypeId() {
        if (payATypeId == null) {
            payATypeId = BigInteger.ZERO;
        }
        return payATypeId;
    }

    public void setPayATypeId(BigInteger payATypeId) {
        this.payATypeId = payATypeId;
    }

    public RefundNoDetail getNoDetail() {
        if (noDetail == null) {
            noDetail = RefundNoDetail.NO_DETAILS;
        }
        return noDetail;
    }

    public void setNoDetail(RefundNoDetail noDetail) {
        this.noDetail = noDetail;
    }

    public boolean isCreateBillByReceiveDetail() {
        if (RefundTypeEnum.EXCHANGE_GOODS.equals(refundTypeEnum)
                || RefundTypeEnum.MONEY_GOODS.equals(refundTypeEnum)) {
            createBillByReceiveDetail = true;
        }
        return createBillByReceiveDetail;
    }

    public BigInteger getBillVchcode() {
        if (billVchcode == null) {
            billVchcode = BigInteger.ZERO;
        }
        return billVchcode;
    }

    public void setBillVchcode(BigInteger billVchcode) {
        this.billVchcode = billVchcode;
    }

    public SubmitState getBillPoseted() {
        if (billPoseted == null) {
            billPoseted = SubmitState.NoSubmit;
        }
        return billPoseted;
    }

    public void setBillPoseted(SubmitState billPoseted) {
        this.billPoseted = billPoseted;
    }

    public EshopRefundConfigReason getPlatformReason() {
        return platformReason;
    }

    public void setPlatformReason(EshopRefundConfigReason platformReason) {
        this.platformReason = platformReason;
    }

    public EshopRefundConfigReason getReason() {
        return reason;
    }

    public void setReason(EshopRefundConfigReason reason) {
        this.reason = reason;
    }

    public void setCreateBillByReceiveDetail(boolean createBillByReceiveDetail) {
        this.createBillByReceiveDetail = createBillByReceiveDetail;
    }

    public BigDecimal getBillTotal() {
        if (billTotal == null) {
            billTotal = BigDecimal.ZERO;
        }
        return billTotal;
    }

    public void setBillTotal(BigDecimal billTotal) {
        this.billTotal = billTotal;
    }

    public BigDecimal getBillServiceFee() {
        if (billServiceFee == null) {
            billServiceFee = BigDecimal.ZERO;
        }
        return billServiceFee;
    }

    public void setBillServiceFee(BigDecimal billServiceFee) {
        this.billServiceFee = billServiceFee;
    }

    public BigDecimal getOrgBillTotal() {
        if (orgBillTotal == null) {
            orgBillTotal = BigDecimal.ZERO;
        }
        return orgBillTotal;
    }

    public void setOrgBillTotal(BigDecimal orgBillTotal) {
        this.orgBillTotal = orgBillTotal;
    }

    public BigDecimal getOrgBillServiceFee() {
        if (orgBillServiceFee == null) {
            orgBillServiceFee = BigDecimal.ZERO;
        }
        return orgBillServiceFee;
    }

    public void setOrgBillServiceFee(BigDecimal orgBillServiceFee) {
        this.orgBillServiceFee = orgBillServiceFee;
    }

    public BigDecimal getOrgBillFreightFee() {
        if (orgBillFreightFee == null) {
            orgBillFreightFee = BigDecimal.ZERO;
        }
        return orgBillFreightFee;
    }

    public void setOrgBillFreightFee(BigDecimal orgBillFreightFee) {
        this.orgBillFreightFee = orgBillFreightFee;
    }

    public BigDecimal getRestFreightFee() {
        if (restFreightFee == null) {
            restFreightFee = BigDecimal.ZERO;
        }
        return restFreightFee;
    }

    public void setRestFreightFee(BigDecimal restFreightFee) {
        this.restFreightFee = restFreightFee;
    }

    public BigDecimal getRestServiceFee() {
        return restServiceFee;
    }

    public void setRestServiceFee(BigDecimal restServiceFee) {
        this.restServiceFee = restServiceFee;
    }

    public String getFreightNo() {
        if (StringUtils.isEmpty(freightNo)) {
            return "";
        }
        return freightNo;
    }

    public void setFreightNo(String freightNo) {
        this.freightNo = freightNo;
    }

    public boolean isHasEdit() {
        return hasEdit;
    }

    public void setHasEdit(boolean hasEdit) {
        this.hasEdit = hasEdit;
    }

    public boolean isSaveFromPage() {
        return saveFromPage;
    }

    public void setSaveFromPage(boolean saveFromPage) {
        this.saveFromPage = saveFromPage;
    }

    public boolean needNotify() {
        return !(RefundTypeEnum.RESEND_GOODS.equals(getRefundTypeEnum()) || RefundTypeEnum.OMIT_SEND_GOODS.equals(getRefundTypeEnum()));
    }

    public String getFreightName() {
        return freightName;
    }

    public void setFreightName(String freightName) {
        this.freightName = freightName;
    }

    public RefundSaleProcessEnum getRefundProcessState() {
        if (refundProcessState == null) {
            refundProcessState = RefundSaleProcessEnum.WAIT_PROCESS;
        }
        return refundProcessState;
    }

    public void setRefundProcessState(RefundSaleProcessEnum refundProcessState) {
        this.refundProcessState = refundProcessState;
    }

    public void setEnabledPreferentialAffect(boolean enabledPreferentialAffect) {
        this.enabledPreferentialAffect = enabledPreferentialAffect;
    }

    public Date getRefundProcessTime() {
        return refundProcessTime;
    }

    public void setRefundProcessTime(Date refundProcessTime) {
        this.refundProcessTime = refundProcessTime;
    }


    public List<EshopRefundBillReleationEntity> getBillReleationList() {
        return billReleationList;
    }

    public void setBillReleationList(List<EshopRefundBillReleationEntity> billReleationList) {
        this.billReleationList = billReleationList;
    }

    public boolean isNeedRelated() {
        return needRelated;
    }

    public void setNeedRelated(boolean needRelated) {
        this.needRelated = needRelated;
    }

    public boolean isRelatedCheckin() {
        return relatedCheckin;
    }

    public void setRelatedCheckin(boolean relatedCheckin) {
        this.relatedCheckin = relatedCheckin;
    }

    public RefundCostState getCostState() {
        return costState;
    }

    public void setCostState(RefundCostState costState) {
        this.costState = costState;
    }

    public String getKtypeName() {
        return ktypeName;
    }

    public void setKtypeName(String ktypeName) {
        this.ktypeName = ktypeName;
    }

    public BigInteger getOldKtypeId() {
        return oldKtypeId;
    }

    public void setOldKtypeId(BigInteger oldKtypeId) {
        this.oldKtypeId = oldKtypeId;
    }

    public String getOldKtypeName() {
        return oldKtypeName;
    }

    public void setOldKtypeName(String oldKtypeName) {
        this.oldKtypeName = oldKtypeName;
    }

    public BigDecimal getDisedTaxedTotal() {
        return disedTaxedTotal;
    }

    public void setDisedTaxedTotal(BigDecimal disedTaxedTotal) {
        this.disedTaxedTotal = disedTaxedTotal;
    }

    public String getEtypeName() {
        return etypeName;
    }

    public void setEtypeName(String etypeName) {
        this.etypeName = etypeName;
    }

    public BigInteger getEtypeId() {
        if (etypeId == null) {
            return BigInteger.ZERO;
        }
        return etypeId;
    }

    public void setEtypeId(BigInteger etypeId) {
        this.etypeId = etypeId;
    }

    public String getAtypeName() {
        return atypeName;
    }

    public void setAtypeName(String atypeName) {
        this.atypeName = atypeName;
    }

    public String getMemo() {
        if (memo == null) {
            return "";
        }
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public List<BaseOrderMarkEnum> getMarks() {
        return marks;
    }

    public void setMarks(List<BaseOrderMarkEnum> marks) {
        this.marks = marks;
    }


    public Integer getHasSubmitToWms() {
        return hasSubmitToWms;
    }

    public void setHasSubmitToWms(Integer hasSubmitToWms) {
        this.hasSubmitToWms = hasSubmitToWms;
    }

    public LocalProcessStatus getDeliverBillProcessStatus() {
        return deliverBillProcessStatus;
    }

    public void setDeliverBillProcessStatus(LocalProcessStatus deliverBillProcessStatus) {
        this.deliverBillProcessStatus = deliverBillProcessStatus;
    }

    public RefundSpeedType getRefundSpeedType() {
        return refundSpeedType;
    }

    public void setRefundSpeedType(RefundSpeedType refundSpeedType) {
        this.refundSpeedType = refundSpeedType;
    }

    public BigInteger getDeliverVchcode() {
        return deliverVchcode;
    }

    public void setDeliverVchcode(BigInteger deliverVchcode) {
        this.deliverVchcode = deliverVchcode;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getSaleorderSellerMemo() {
        return saleorderSellerMemo;
    }

    public void setSaleorderSellerMemo(String saleorderSellerMemo) {
        this.saleorderSellerMemo = saleorderSellerMemo;
    }

    public String getSaleorderRemark() {
        return saleorderRemark;
    }

    public void setSaleorderRemark(String saleorderRemark) {
        this.saleorderRemark = saleorderRemark;
    }

    public String getBillSellerMemo() {
        return billSellerMemo;
    }

    public void setBillSellerMemo(String billSellerMemo) {
        this.billSellerMemo = billSellerMemo;
    }

    public String getBillRemark() {
        return billRemark;
    }

    public void setBillRemark(String billRemark) {
        this.billRemark = billRemark;
    }

    public void setOriginOrderMark() {
        this.originOrderMark = getOriginOrderMark();
    }

    public String getOriginOrderMark() {
        if (StringUtils.isEmpty(billSellerMemo)) {
            if (saleOrder != null && StringUtils.isNotEmpty(saleOrder.getSellerMemo())) {
                return "卖家备注：" + saleOrder.getSellerMemo()
                        + "\r\n"
                        + "留言：" + (StringUtils.isEmpty(buyMessage) ? "" : buyMessage);
            } else {
                return "";
            }
        } else {
            return "卖家备注：" + billSellerMemo
                    + "\r\n"
                    + "系统备注：" + (StringUtils.isEmpty(billRemark) ? "" : billRemark)
                    + "\r\n"
                    + "留言：" + (StringUtils.isEmpty(buyMessage) ? "" : buyMessage);
        }
    }

    public RefundCreateTypeEnum getCreateType() {
        if (createType == null) {
            createType = RefundCreateTypeEnum.MANUAL_CREATE;
        }
        return createType;
    }

    public void setCreateType(RefundCreateTypeEnum createType) {
        this.createType = createType;
    }

    public boolean isDelivered() {
        return delivered;
    }

    public void setDelivered(boolean delivered) {
        this.delivered = delivered;
    }

    public Date getRefundFinishTime() {
        return refundFinishTime;
    }

    public void setRefundFinishTime(Date refundFinishTime) {
        this.refundFinishTime = refundFinishTime;
    }

    public RefundInterceptAgreeStatusEnum getPlatformAutoInterceptAgree() {
        if (platformAutoInterceptAgree == null) {
            return RefundInterceptAgreeStatusEnum.No;
        }
        return platformAutoInterceptAgree;
    }

    public void setPlatformAutoInterceptAgree(RefundInterceptAgreeStatusEnum platformAutoInterceptAgree) {
        this.platformAutoInterceptAgree = platformAutoInterceptAgree;
    }

    public RefundPayTypeEnum getRefundPayType() {
        if (refundPayType == null) {
            return RefundPayTypeEnum.ALL;
        }
        return refundPayType;
    }

    public void setRefundPayType(RefundPayTypeEnum refundPayType) {
        this.refundPayType = refundPayType;
    }

    public boolean isAdvancePay() {
        return advancePay;
    }

    public void setAdvancePay(boolean advancePay) {
        this.advancePay = advancePay;
    }


    /*
    线上操作的参数
     */
    /*
    备注
     */
    private String remark;

    private List<String> picUrl;

    /*
    同意仅退款验证码
     */
    private String yzmCode;

    /*
    退款说明
     */
    private String refundExplain;

    /*
    授权码
     */
    private String eshopToken;

    /*
    拒绝原因
     */
    private RefundRejectReason rejectReason;

    /*
    邮费承担人
     */
    private PostFeeBearRoleType postFeeBearRole;

    /*
    是否虚拟退货
     */
    private boolean virtualReturnGoods;

    /*
    退货地址
     */
    private ShopRefundAddress refundAddress;

    private String rejectReasonText;

    private boolean onlyRefund;

    public boolean isOnlyRefund() {
        return onlyRefund;
    }

    public void setOnlyRefund(boolean onlyRefund) {
        this.onlyRefund = onlyRefund;
    }

    public String getRejectReasonText() {
        if (rejectReasonText == null) {
            return "";
        }
        return rejectReasonText;
    }

    public void setRejectReasonText(String rejectReasonText) {
        this.rejectReasonText = rejectReasonText;
    }

    public boolean getVirtualReturnGoods() {
        return virtualReturnGoods;
    }

    public void setVirtualReturnGoods(boolean virtualReturnGoods) {
        this.virtualReturnGoods = virtualReturnGoods;
    }

    public ShopRefundAddress getRefundAddress() {
        return refundAddress;
    }

    public void setRefundAddress(ShopRefundAddress refundAddress) {
        this.refundAddress = refundAddress;
    }

    public PostFeeBearRoleType getPostFeeBearRole() {
        return postFeeBearRole;
    }

    public void setPostFeeBearRole(PostFeeBearRoleType postFeeBearRole) {
        this.postFeeBearRole = postFeeBearRole;
    }

    public RefundRejectReason getRejectReason() {
        return rejectReason;
    }

    public void setRejectReason(RefundRejectReason rejectReason) {
        this.rejectReason = rejectReason;
    }

    public String getEshopToken() {
        return eshopToken;
    }

    public void setEshopToken(String eshopToken) {
        this.eshopToken = eshopToken;
    }

    public String getRefundExplain() {
        return refundExplain;
    }

    public void setRefundExplain(String refundExplain) {
        this.refundExplain = refundExplain;
    }

    public String getYzmCode() {
        return yzmCode;
    }

    public void setYzmCode(String yzmCode) {
        this.yzmCode = yzmCode;
    }

    public List<String> getPicUrl() {
        if (picUrl == null) {
            return new ArrayList<String>();
        }
        return picUrl;
    }

    public void setPicUrl(List<String> picUrl) {
        this.picUrl = picUrl;
    }

    public String getRemark() {
        if (remark == null) {
            return "";
        }
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}

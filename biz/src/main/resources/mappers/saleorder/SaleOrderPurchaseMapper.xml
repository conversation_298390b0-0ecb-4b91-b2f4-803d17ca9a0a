<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.SaleOrderPurchaseMapper">
    <insert id="insertSaleOrderDetailPurchase">
        INSERT INTO pl_eshop_sale_order_detail_purchase (`eshop_order_id`, `profile_id`, `eshop_order_detail_id`, `purchase_price`, `purchase_total`,
                                                `purchase_tax_rate`, `purchase_tax_total`, `purchase_dised_price`,
                                                `purchase_dised_total`, `purchase_discount`, `purchase_dised_taxed_price`,
                                                 `purchase_dised_taxed_total`, `create_time`,
                                                `update_time`)
            value (#{eshopOrderId}, #{profileId}, #{eshopOrderDetailId}, #{purchasePrice}, #{purchaseTotal}, #{purchaseTaxRate}, #{purchaseTaxTotal},
            #{purchaseDisedPrice}, #{purchaseDisedTotal}, #{purchaseDiscount}, #{purchaseDisedTaxedPrice},
            #{purchaseDisedTaxedTotal}, #{createTime},  #{updateTime})
        ON DUPLICATE KEY
        UPDATE `eshop_order_detail_id`=#{eshopOrderDetailId},
            `profile_id`=#{profileId}
    </insert>

    <insert id="batchInsertSaleOrderDetailPurchase">
        INSERT INTO pl_eshop_sale_order_detail_purchase (`eshop_order_id`, `profile_id`, `eshop_order_detail_id`, `purchase_price`, `purchase_total`,
                                                         `purchase_tax_rate`, `purchase_tax_total`, `purchase_dised_price`,
                                                         `purchase_dised_total`, `purchase_discount`, `purchase_dised_taxed_price`,
                                                         `purchase_dised_taxed_total`, `create_time`,
                                                         `update_time`)
            values
        <foreach collection="list" item="purchaseEntity" separator=",">
                (#{purchaseEntity.eshopOrderId}, #{purchaseEntity.profileId}, #{purchaseEntity.eshopOrderDetailId}, #{purchaseEntity.purchasePrice}, #{purchaseEntity.purchaseTotal}, #{purchaseEntity.purchaseTaxRate}, #{purchaseEntity.purchaseTaxTotal},
                   #{purchaseEntity.purchaseDisedPrice}, #{purchaseEntity.purchaseDisedTotal}, #{purchaseEntity.purchaseDiscount}, #{purchaseEntity.purchaseDisedTaxedPrice},
                   #{purchaseEntity.purchaseDisedTaxedTotal}, #{purchaseEntity.createTime},  #{purchaseEntity.updateTime})
        </foreach>
        ON DUPLICATE KEY
            UPDATE `eshop_order_detail_id`=VALUES(eshop_order_detail_id),
                   `profile_id`=VALUES(profile_id)
    </insert>

    <insert id="insertOrderComboRowPurchase">
        insert into pl_eshop_sale_order_detail_combo_purchase
        (profile_id, combo_id, trade_order_detail_id, eshop_order_id, purchase_price, purchase_total, purchase_tax_rate, purchase_tax_total, purchase_dised_price,
        purchase_dised_total, purchase_discount, purchase_dised_taxed_price, purchase_dised_taxed_total, create_time,
        update_time, eshop_order_combo_row_id)
        values
            (#{profileId}, #{comboId}, #{tradeOrderDetailId}, #{eshopOrderId},
            #{purchasePrice}, #{purchaseTotal},
            #{purchaseTaxRate}, #{purchaseTaxTotal}, #{purchaseDisedPrice}, #{purchaseDisedTotal}, #{purchaseDiscount},
            #{purchaseDisedTaxedPrice},
            #{purchaseDisedTaxedTotal}, #{createTime}, #{updateTime},
            #{eshopOrderComboRowId})
            ON DUPLICATE KEY
        UPDATE `eshop_order_combo_row_id`=#{eshopOrderComboRowId},
            `profile_id`=#{profileId}
    </insert>

    <insert id="batchSaleOrderDetailPurchase">
        insert into pl_eshop_sale_order_detail_purchase
        (profile_id, eshop_order_id, purchase_price, purchase_total, purchase_tax_rate, purchase_tax_total, purchase_dised_price,
         purchase_dised_total, purchase_discount, purchase_dised_taxed_price, purchase_dised_taxed_total, create_time,
         update_time, eshop_order_detail_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.profileId},   #{item.eshopOrderId}, #{item.purchasePrice},
            #{item.purchaseTotal}, #{item.purchaseTaxRate},
            #{item.purchaseTaxTotal}, #{item.purchaseDisedPrice}, #{item.purchaseDisedTotal}, #{item.purchaseDiscount}, #{item.purchaseDisedTaxedPrice},
            #{item.purchaseDisedTaxedTotal}, #{item.createTime}, #{item.updateTime}, #{item.eshopOrderDetailId})
        </foreach>
    </insert>

    <insert id="batchInsertOrderComboRowPurchase">
        insert into pl_eshop_sale_order_detail_combo_purchase
        (profile_id, combo_id, eshop_order_id, purchase_price, purchase_total, purchase_tax_rate, purchase_tax_total, purchase_dised_price,
        purchase_dised_total, purchase_discount, purchase_dised_taxed_price, purchase_dised_taxed_total, create_time,
        update_time, eshop_order_combo_row_id)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.profileId}, #{item.comboId},  #{item.eshopOrderId}, #{item.purchasePrice},
            #{item.purchaseTotal}, #{item.purchaseTaxRate},
            #{item.purchaseTaxTotal}, #{item.purchaseDisedPrice}, #{item.purchaseDisedTotal}, #{item.purchaseDiscount}, #{item.purchaseDisedTaxedPrice},
            #{item.purchaseDisedTaxedTotal}, #{item.createTime}, #{item.updateTime}, #{item.eshopOrderComboRowId})
        </foreach>
        ON DUPLICATE KEY
        UPDATE `eshop_order_combo_row_id`=VALUES(eshop_order_combo_row_id),
        `profile_id`=VALUES(profile_id)
    </insert>

    <insert id="insertPlatformBtype">
        INSERT INTO pl_eshop_platform_btype_mapping ( profile_id,eshop_id,id,platform_btype_name,
            platform_btype_code,platform_btype_unique_id,platform_btype_address,
            platform_btype_mobile,platform_btype_tel,platform_btype_contactor,
            deleted, create_type,btype_id)
        values (#{profileId},#{eshopId},#{id},#{platformBtypeName},#{platformBtypeCode},#{platformBtypeUniqueId},#{platformBtypeAddress},
                #{platformBtypeMobile},#{platformBtypeTel},#{platformBtypeContactor},#{deleted},#{createType},#{btypeId})
        ON DUPLICATE KEY UPDATE platform_btype_name=#{platformBtypeName},
                                platform_btype_code=#{platformBtypeCode},
                                platform_btype_mobile=#{platformBtypeMobile},
                                deleted=0,
                                platform_btype_contactor=#{platformBtypeContactor},
                                platform_btype_address=#{platformBtypeAddress}
    </insert>

    <insert id="batchInsertPlatformBtype">
        INSERT INTO pl_eshop_platform_btype_mapping ( profile_id,eshop_id,id,platform_btype_name,
                                                      platform_btype_code,platform_btype_unique_id,platform_btype_address,
                                                      platform_btype_mobile,platform_btype_tel,platform_btype_contactor,
                                                      deleted, create_type,btype_id)
        values
        <foreach collection="list" item="info" separator=",">
            (#{info.profileId},#{info.eshopId},#{info.id},#{info.platformBtypeName},#{info.platformBtypeCode},#{info.platformBtypeUniqueId},#{info.platformBtypeAddress},
                #{info.platformBtypeMobile},#{info.platformBtypeTel},#{info.platformBtypeContactor},#{info.deleted},#{info.createType},#{info.btypeId})
        </foreach>
        ON DUPLICATE KEY UPDATE platform_btype_name=VALUES(platform_btype_name),
                                platform_btype_code=VALUES(platform_btype_code),
                                platform_btype_mobile=VALUES(platform_btype_mobile),
                                deleted=0,
                                platform_btype_contactor=VALUES(platform_btype_contactor),
                                platform_btype_address=VALUES(platform_btype_address)
    </insert>

    <delete id="deleteSaleOrderDetailPurchase">
        delete from pl_eshop_sale_order_detail_purchase
        where profile_id = #{profileId}
        <if test="eshopOrderId!=null and eshopOrderId!=0">
            and eshop_order_id = #{eshopOrderId}
        </if>
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            AND eshop_order_id IN
            <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <delete id="deleteSaleOrderDetailSerialnos">
        delete from pl_eshop_sale_order_detail_serialno
        where profile_id = #{profileId}
        <if test="eshopOrderId!=null and eshopOrderId!=0">
            and eshop_order_id = #{eshopOrderId}
        </if>
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            AND eshop_order_id IN
            <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <delete id="deleteSaleOrderDetailGiftRelations">
        delete from pl_eshop_saleorder_detail_gift_relation
        where profile_id = #{profileId}
        <if test="eshopOrderId!=null and eshopOrderId!=0">
            and eshop_order_id = #{eshopOrderId}
        </if>
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            AND eshop_order_id IN
            <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <delete id="deleteSaleOrderDetailBatch">
        delete from pl_eshop_sale_order_detail_batch
        where profile_id = #{profileId}
        <if test="eshopOrderId!=null and eshopOrderId!=0">
            and eshop_order_id = #{eshopOrderId}
        </if>
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            AND eshop_order_id IN
            <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <delete id="deleteSaleOrderDetailTiming">
        delete from pl_eshop_sale_order_detail_timing
        where profile_id = #{profileId}
        <if test="eshopOrderId!=null and eshopOrderId!=0">
            and eshop_order_id = #{eshopOrderId}
        </if>
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            AND eshop_order_id IN
            <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </delete>

    <select id="queryExistSaleOrderDetailPurchase"   resultType="java.lang.Integer">
        select count(0) from pl_eshop_sale_order_detail_purchase
        where profile_id = #{profileId}
        <if test="eshopOrderId!=null and eshopOrderId!=0">
            and eshop_order_id = #{eshopOrderId}
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="batchQueryExistSaleOrderDetailPurchase"   resultType="java.math.BigInteger">
        select eshop_order_detail_id from pl_eshop_sale_order_detail_purchase
        where profile_id = #{profileId}
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            AND eshop_order_id IN
            <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryExistSaleOrderDetailBatch" resultType="java.lang.Integer">
        select count(0) from pl_eshop_sale_order_detail_batch
        where profile_id = #{profileId}
        <if test="eshopOrderId!=null and eshopOrderId!=0">
            and eshop_order_id = #{eshopOrderId}
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="batchQueryExistSaleOrderDetailBatch" resultType="java.math.BigInteger">
        select id from pl_eshop_sale_order_detail_batch
        where profile_id = #{profileId}
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            AND eshop_order_id IN
            <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryExistSaleOrderDetailTiming" resultType="java.lang.Integer">
        select count(0)  from pl_eshop_sale_order_detail_timing
        where profile_id = #{profileId}
        <if test="eshopOrderId!=null and eshopOrderId!=0">
            and eshop_order_id = #{eshopOrderId}
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="batchQueryExistSaleOrderDetailTiming" resultType="java.math.BigInteger">
        select id  from pl_eshop_sale_order_detail_timing
        where profile_id = #{profileId}
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            AND eshop_order_id IN
            <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryExistSaleOrderDetailSerialnos" resultType="java.lang.Integer">
        select count(0)  from pl_eshop_sale_order_detail_serialno
        where profile_id = #{profileId}
        <if test="eshopOrderId!=null and eshopOrderId!=0">
            and eshop_order_id = #{eshopOrderId}
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="batchQueryExistSaleOrderDetailSerialnos" resultType="java.math.BigInteger">
        select id from pl_eshop_sale_order_detail_serialno
        where profile_id = #{profileId}
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            AND eshop_order_id IN
            <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="queryExistSaleOrderDetailGiftRelations" resultType="java.lang.Integer">
        select count(0) from pl_eshop_saleorder_detail_gift_relation
        where profile_id = #{profileId}
        <if test="eshopOrderId!=null and eshopOrderId!=0">
            and eshop_order_id = #{eshopOrderId}
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="batchQueryExistSaleOrderDetailGiftRelations" resultType="java.math.BigInteger">
        select id from pl_eshop_saleorder_detail_gift_relation
        where profile_id = #{profileId}
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            AND eshop_order_id IN
            <foreach collection="eshopOrderIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="eshopOrderDetailIds!=null and eshopOrderDetailIds.size()>0">
            AND eshop_order_detail_id IN
            <foreach collection="eshopOrderDetailIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>
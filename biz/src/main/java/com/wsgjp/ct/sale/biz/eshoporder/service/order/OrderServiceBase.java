package com.wsgjp.ct.sale.biz.eshoporder.service.order;

import com.wsgjp.ct.sale.biz.eshoporder.dao.EshopSaleOrderDao;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.ComboDetail;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.EshopBuyer;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.GetSaleOrderInfoRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryComboParameter;
import com.wsgjp.ct.sale.biz.eshoporder.service.baseinfo.EshopOrderBaseInfoService;
import com.wsgjp.ct.sale.biz.eshoporder.util.CommonUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sis.client.entity.EncryptFullAdapter;
import com.wsgjp.ct.support.business.Money;
import com.wsgjp.ct.support.business.MoneyUtils;
import com.wsgjp.ct.support.utils.QiniuUtils;
import ngp.utils.CollectionUtils;
import ngp.utils.JsonUtils;
import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-03-20
 */
public abstract class OrderServiceBase {
    protected final EshopSaleOrderDao orderDao;

    protected OrderServiceBase(EshopSaleOrderDao orderDao) {
        this.orderDao = orderDao;
    }

    public List<EncryptFullAdapter> getEncryptInfos(List<EshopSaleOrderEntity> orderEntityList) {
        List<EncryptFullAdapter> encryptFullAdapterList = new ArrayList<>();
        if (null == orderEntityList || orderEntityList.size() == 0) {
            return encryptFullAdapterList;
        }
        for (EshopSaleOrderEntity order : orderEntityList) {
            EshopBuyer buyer = order.getEshopBuyer();
            buyer.setOtypeId(order.getOtypeId());
            encryptFullAdapterList.add(buyer);
        }
        return encryptFullAdapterList;
    }

    public List<EshopSaleOrderDetail> filterOrderDetails(List<EshopSaleOrderDetail> orderDetails,
                                                         List<EshopSaleDetailCombo> comboRows) {
        List<EshopSaleOrderDetail> result = new ArrayList<>();
        List<EshopSaleOrderDetail> comboDetails = doFilterComboRow(comboRows, orderDetails);
        if (!comboDetails.isEmpty()) {
            result.addAll(comboDetails);
        }
        List<EshopSaleOrderDetail> normalDetail =
                orderDetails.stream()
                        .filter(x -> x.getComboRowId().longValue() == 0)
                        .collect(Collectors.toList());
        if (!normalDetail.isEmpty()) {
            doFilterDetail(normalDetail, BigInteger.ZERO);
            result.addAll(normalDetail);
        }
        List<EshopSaleOrderDetail> orderResult = buildTrueOrderDetail(orderDetails, result);
        orderResult=orderResult.stream().filter(x-> x.getOrderDetailMarks().stream().noneMatch(p->p.getMarkCode().equals(new BigInteger("91390003")))).collect(Collectors.toList());
        //以防构建出重复明细了
        return orderResult.size() == result.size() ? orderResult : result;
    }



    /**
     * 构建正确明细顺序
     * orderDetailList 不包含套餐行的明细按整体顺序
     * detailList 包含套餐行的明细无序
     * @param orderDetailList
     * @param detailList
     * @return {@link List}<{@link EshopSaleOrderDetail}>
     */
    @NotNull
    private  List<EshopSaleOrderDetail> buildTrueOrderDetail(List<EshopSaleOrderDetail> orderDetailList, List<EshopSaleOrderDetail> detailList) {
        List<EshopSaleOrderDetail> sortDetails = sortComboDetail(orderDetailList);
        List<EshopSaleOrderDetail> result = new ArrayList<>();
        for (EshopSaleOrderDetail detail : sortDetails) {
            BigInteger trueOrderDetailId = detail.getId();
            BigInteger comboRowId = detail.getComboRowId();
            Optional<EshopSaleOrderDetail> first = detailList.stream().filter(d -> d.getId().compareTo(trueOrderDetailId) == 0).findFirst();
            if (first.isPresent()){
                //套餐明细
                if (BigInteger.ZERO.compareTo(comboRowId) != 0){
                    //寻找套餐行
                    Optional<EshopSaleOrderDetail> comboRow = detailList.stream().filter(d -> d.getId().compareTo(comboRowId) == 0 && d.isComboRow()).findFirst();
                    if (comboRow.isPresent() &&
                            result.stream().noneMatch(r->r.getId().compareTo(comboRow.get().getId()) == 0)){
                        //先插入套餐行
                        result.add(comboRow.get());
                    }
                }
                result.add(first.get());
            }
        }
        return result;
    }

    //     * orderDetailList 不包含套餐行的明细按整体顺序
    //     * detailList 包含套餐行的明细无序
    private  List<EshopSaleOrderDetail> sortComboDetail(List<EshopSaleOrderDetail> orderDetailList) {
        if (CollectionUtils.isEmpty(orderDetailList)){
            return orderDetailList;
        }
        List<BigInteger> comboIds = orderDetailList.stream().map(EshopSaleOrderDetail::getComboId).filter(d -> null != d && BigInteger.ZERO.compareTo(d) != 0).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(comboIds)){
            return orderDetailList;
        }
        EshopOrderBaseInfoService baseInfoService = GetBeanUtil.getBean(EshopOrderBaseInfoService.class);
        QueryComboParameter parameter = new QueryComboParameter();
        parameter.setPtypeIdList(comboIds);
        List<ComboDetail> comboDetails = baseInfoService.getComboDetails(parameter);
        if (CollectionUtils.isEmpty(comboDetails)){
            return orderDetailList;
        }
        List<EshopSaleOrderDetail> normalDetails = new ArrayList<>();
        List<SortComboDetail> sortComboDetails = new ArrayList<>();
        Map<BigInteger, List<ComboDetail>> comboMap = comboDetails.stream().collect(Collectors.groupingBy(ComboDetail::getComboId));
        for (EshopSaleOrderDetail detail : orderDetailList) {
            BigInteger comboId = detail.getComboId();
            if (BigInteger.ZERO.compareTo(comboId) == 0){
                //普通明细入队
                normalDetails.add(detail);
                continue;
            }
            List<ComboDetail> comboDetailList = comboMap.get(comboId);
            if (CollectionUtils.isEmpty(comboDetailList)){
                //分离套餐明细和普通明细
                appendToSortComboDetails(detail, sortComboDetails);
                continue;
            }
            BigInteger ptypeId = detail.getPtypeId();
            BigInteger skuId = detail.getSkuId();
            BigInteger unit = detail.getUnit();
            //针对相同商品寻找唯一匹配
            Optional<ComboDetail> first = comboDetailList.stream().filter(c -> c.getPtypeId().compareTo(ptypeId) == 0 &&
                    c.getSkuId().compareTo(skuId) == 0 &&
                    c.getUnitId().compareTo(unit) == 0).findFirst();
            if (!first.isPresent()){
                //分离套餐明细和普通明细
                appendToSortComboDetails(detail, sortComboDetails);
                continue;
            }
            BigInteger id = first.get().getId();
            detail.setComboDetailRowIndex(id);
            comboDetailList.removeIf(c->c.getId().compareTo(id)==0);
            appendToSortComboDetails(detail, sortComboDetails);
        }

        // 排序每个套餐内部的明细
        //默认升序
        for (SortComboDetail combo : sortComboDetails) {
            combo.getDetails().sort(Comparator.comparing(EshopSaleOrderDetail::getComboDetailRowIndex));
        }

        // 合并明细
        List<EshopSaleOrderDetail> sortedOrderDetails = new ArrayList<>(orderDetailList.size());
        int normalIndex = 0;
        for (EshopSaleOrderDetail originalDetail : orderDetailList) {
            if (originalDetail.getComboId().compareTo(BigInteger.ZERO) !=0) {
                for (SortComboDetail combo : sortComboDetails) {
                    if (combo.getComboId().compareTo(originalDetail.getComboId())==0 && !combo.isMatch()) {
                        sortedOrderDetails.addAll(combo.getDetails());
                        combo.setMatch(true);
                        break;
                    }
                }
            } else {
                sortedOrderDetails.add(normalDetails.get(normalIndex++));
            }
        }
        return sortedOrderDetails;
    }

    private  void appendToSortComboDetails(EshopSaleOrderDetail detail, List<SortComboDetail> sortComboDetails) {
        boolean addedToCombo = false;
        for (SortComboDetail combo : sortComboDetails) {
            if (combo.getComboId().compareTo(detail.getComboId())==0) {
                combo.addDetail(detail);
                addedToCombo = true;
                break;
            }
        }
        if (!addedToCombo) {
            SortComboDetail newCombo = new SortComboDetail(detail.getComboId());
            newCombo.addDetail(detail);
            sortComboDetails.add(newCombo);
        }
    }

    private List<EshopSaleOrderDetail> doFilterComboRow(List<EshopSaleDetailCombo> comboRows,
                                                        List<EshopSaleOrderDetail> orderDetails) {
        List<EshopSaleOrderDetail> comboDetails = new ArrayList<>();
        if (comboRows == null || comboRows.isEmpty()) {
            return comboDetails;
        }
        for (EshopSaleDetailCombo combo : comboRows) {
            EshopSaleOrderDetail comboRow = combo.toOrderDetail();
            BigInteger comboRowId = combo.getEshopOrderDetailComboRowId();
            List<EshopSaleOrderDetail> details =
                    orderDetails.stream()
                            .filter(x -> x.getComboRowId().equals(comboRowId))
                            .collect(Collectors.toList());
            if (details.isEmpty()) {
                continue;
            }
            double sum = details.stream().mapToDouble(x -> x.getTotalWeight().doubleValue()).sum();
            comboRow.setTotalWeight(MoneyUtils.multiply(BigDecimal.valueOf(sum), BigDecimal.ONE, Money.Total));
            EshopSaleOrderDetail first = details.get(0);
//            comboRow.setGift(details.stream().allMatch(EshopSaleOrderDetail::getGift));
            comboRow.setPlatformPtypeId(first.getPlatformPtypeId());
            comboRow.setPlatformSkuId(first.getPlatformSkuId());
            comboRow.setPlatformPtypeXcode(first.getPlatformPtypeXcode());
            comboRow.setPlatformPtypeName(first.getPlatformPtypeName());
            comboRow.setPlatformPropertiesName(first.getPlatformPropertiesName());
            List<EshopOrderMarkEntity> comboMarks = doFilterComboRowMarks(first);
            List<EshopOrderMarkEntity> allDetailMarks = new ArrayList<>();
            details.forEach(d->allDetailMarks.addAll(d.getOrderDetailMarks()));
            Iterator<EshopOrderMarkEntity> iterator = comboMarks.iterator();
            while (iterator.hasNext()) {
                BigInteger markCode = iterator.next().getMarkCode();
                boolean allHas = (allDetailMarks.stream().filter(a -> a.getMarkCode().compareTo(markCode) == 0).count() >= details.size());
                if (!allHas) {
                    iterator.remove();
                }
            }
            comboRow.setOrderDetailMarks(comboMarks);
            comboRow.setKtypeId(first.getKtypeId());
            comboRow.setKtypeName(first.getKtypeName());
            comboRow.setVerifyCode(first.getVerifyCode());
            comboRow.setDeleted(first.getDeleted());
            comboRow.setLocalRefundState(first.getLocalRefundState());
            comboRow.setReSendState(first.getReSendState());
            comboRow.setLocalRefundProcessState(first.getLocalRefundProcessState());
            comboRow.setPlatformDetailTradeState(first.getPlatformDetailTradeState());
            comboRow.setOrderSaleType(first.getOrderSaleType());
            comboRow.setProcessState(first.getProcessState());
            comboRow.setDisplayCustomInfo(first.getDisplayCustomInfo());
            comboRow.setPlatformQcResult(first.getPlatformQcResult());
            comboRow.setPlatformQcResultDesc(first.getPlatformQcResultDesc());
            comboRow.setPlatformIdentifyResult(first.getPlatformIdentifyResult());
            comboRow.setPlatformIdentifyResultDesc(first.getPlatformIdentifyResultDesc());
            comboRow.setFlowChannel(first.getFlowChannel());
            comboRow.setPlatformStockId(first.getPlatformStockId());
            comboRow.setPlatformStockName(first.getPlatformStockName());
            //构建套餐行商品种类
            List<Integer> pcategorys = details.stream().map(EshopSaleOrderDetail::getPcategory).distinct().collect(Collectors.toList());
            if (pcategorys.size() == 1){
                comboRow.setPcategory(pcategorys.get(0));
            }
            if (null != combo.getPurchase()) {
                if (null == comboRow.getPurchase() || null ==combo.getPurchase().getEshopOrderId() ||   combo.getPurchase().getEshopOrderId().compareTo(BigInteger.ZERO)==0){
                    comboRow.setPurchase(new EshopSaleOrderComboPurchaseEntity());
                }
                comboRow.getPurchase().setPurchasePrice(combo.getPurchase().getPurchasePrice());
                comboRow.getPurchase().setPurchaseTotal(combo.getPurchase().getPurchaseTotal());
            }
            if (null != combo.getLiveBroadcast()) {
                if (null == comboRow.getLiveBroadcast() || combo.getLiveBroadcast().getEshopOrderId().compareTo(BigInteger.ZERO)==0){
                    comboRow.setLiveBroadcast(new EshopSaleOrderDetailLiveBroadcast());
                }
                comboRow.getLiveBroadcast().setPlatformAnchorId(combo.getLiveBroadcast().getPlatformAnchorId());
                comboRow.getLiveBroadcast().setPlatformAnchorName(combo.getLiveBroadcast().getPlatformAnchorName());
                comboRow.getLiveBroadcast().setPlatformLiveRoomId(combo.getLiveBroadcast().getPlatformLiveRoomId());
                comboRow.getLiveBroadcast().setProfileId(combo.getLiveBroadcast().getProfileId());
                comboRow.getLiveBroadcast().setEshopId(combo.getLiveBroadcast().getEshopId());
                comboRow.getLiveBroadcast().setLiveBrodcastSessionId(combo.getLiveBroadcast().getLiveBrodcastSessionId());
            }
            comboDetails.add(comboRow);
            doFilterDetail(details, comboRow.getComboId());
            comboDetails.addAll(details);
        }
        return comboDetails;
    }

    /**
     * 过滤套餐行不展示的标记
     *
     */
    @NotNull
    private List<EshopOrderMarkEntity> doFilterComboRowMarks(EshopSaleOrderDetail first) {
        List<EshopOrderMarkEntity> comboMarks = JsonUtils.toList(JsonUtils.toJson(first.getOrderDetailMarks()),EshopOrderMarkEntity.class);
        return comboMarks;
    }

    protected void doFilterDetail(List<EshopSaleOrderDetail> orderDetails, BigInteger comboId) {
        for (EshopSaleOrderDetail detail : orderDetails) {
            detail.setComboId(comboId);
            if (detail.getPicUrl() != null && !detail.getPicUrl().startsWith("http")) {
                detail.setPicUrl(QiniuUtils.getThumbnail(detail.getPicUrl()));/*会导致重复获取缩略图*/
            }
        }
    }

    public EshopBuyer buildReceiverInfo(EncryptFullAdapter entity, EshopBuyer orderEntity) {
        return CommonUtil.getEshopBuyer(entity, orderEntity);
    }


    public Integer getOrderProcessState(GetSaleOrderInfoRequest request) {
        return orderDao.getOrderProcessState(request);
    }


    public List<Integer> getPostState(GetSaleOrderInfoRequest request) {
        return orderDao.getPostState(request);
    }

    class SortComboDetail {
        private BigInteger comboId;
        private List<EshopSaleOrderDetail> details;
        private boolean match;

        public SortComboDetail(BigInteger comboId) {
            this.comboId = comboId;
            this.details = new ArrayList<>();
        }

        public void addDetail(EshopSaleOrderDetail detail) {
            if (null != detail.getComboId() && BigInteger.ZERO.compareTo(detail.getComboId()) != 0
                    && detail.getComboId().compareTo(this.comboId)==0) {
                this.details.add(detail);
            }
        }

        public List<EshopSaleOrderDetail> getDetails() {
            return details;
        }

        public BigInteger getComboId() {
            return comboId;
        }

        public boolean isMatch() {
            return match;
        }

        public void setMatch(boolean match) {
            this.match = match;
        }
    }
}

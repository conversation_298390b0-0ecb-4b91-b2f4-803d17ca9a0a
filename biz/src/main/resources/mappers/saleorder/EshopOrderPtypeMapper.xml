<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopOrderPtypeMapper">
    <select id="getPtypeListForRelation" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.Ptype">
        select id,usercode from base_ptype where profile_id=#{profileId}  and deleted=0
    </select>
    <select id="getUserCodeExist" resultType="java.lang.Integer">
        select count(1) from base_ptype where profile_id=#{profileId} and userCode=#{usercode} and fullname=#{fullname} and deleted=0
    </select>
    <select id="getPtypeExistedUsercode" resultType="java.lang.String">
        select xcode from  base_ptype_xcode px
        join base_ptype p on p.profile_id=px.profile_id and p.id= px.id and p.deleted=0
        where px.profile_id=#{profileId} and  px.xcode in
        <foreach collection="userCodes" item="xcode" separator="," index="i" close=")" open="(">
            #{xcode}
        </foreach>
    </select>
    <select id="getPtypeByFullName" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.Ptype">
        select * from base_ptype where profile_id=#{profileId} and fullname=#{fullname} and deleted=0 and pcategory=0
    </select>
    <select id="getPtypeByUsercode" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.Ptype">
                select * from base_ptype where profile_id=#{profileId} and usercode=#{usercode} and deleted=0 and pcategory=0

    </select>

    <select id="getPtypeByUsercodeAndFullname" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.Ptype">
                select * from base_ptype where profile_id=#{profileId} and usercode=#{usercode} and usercode=#{fullname} and deleted=0 and pcategory=0

    </select>

    <select id="getComboByUsercode" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.Ptype">
                select * from base_ptype where profile_id=#{profileId} and usercode=#{usercode} and deleted=0 and pcategory=2 limit 1
    </select>

    <select id="getPtypeById" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.Ptype">
        select * from base_ptype where profile_id=#{profileId} and id=#{id}
    </select>
    <select id="getPtypeSkuById" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.PtypeSkuEntity">
        select * from base_ptype_sku where profile_id=#{profileId} and id=#{skuId}
    </select>

    <select id="getSkuByPtype" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.PtypeSkuEntity">
        select * from base_ptype_sku where profile_id=#{profileId} and ptype_id=#{ptypeId}
    </select>
    <select id="getPtypeXcode" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.PtypeXcode">
        select * from base_ptype_xcode where profile_id=#{profileId} and xcode=#{xcode};
    </select>

    <select id="getSalePorxyLabel" resultType="java.lang.Integer">
        SELECT count(1) FROM `cf_data_label_ptype` p INNER JOIN Cf_Labelfield_Value lvalue ON p.labelfield_value_id=lvalue.id AND p.profile_id=lvalue.profile_id
        WHERE resource_id=#{ptypeId} AND labelfield_value='代销' AND p.profile_id=#{profileId}
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.AccGoodsStockBatchlifeExtMapper">
    <resultMap id="BaseResultMap" type="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.AccGoodsStockBatchlife">
        <constructor>
            <idArg column="id" javaType="java.math.BigInteger" jdbcType="BIGINT"/>
            <arg column="profile_id" javaType="java.math.BigInteger" jdbcType="BIGINT"/>
            <arg column="ktype_id" javaType="java.math.BigInteger" jdbcType="BIGINT"/>
            <arg column="ptype_id" javaType="java.math.BigInteger" jdbcType="BIGINT"/>
            <arg column="batchno" javaType="java.lang.String" jdbcType="VARCHAR"/>
            <arg column="produce_date" javaType="java.util.Date" jdbcType="DATE"/>
            <arg column="expire_date" javaType="java.util.Date" jdbcType="DATE"/>
            <arg column="qty" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>
            <arg column="sub_qty" javaType="java.math.BigDecimal" jdbcType="DECIMAL"/>
            <arg column="create_time" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
            <arg column="update_time" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
        </constructor>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
    id, profile_id, ktype_id, ptype_id, batchno, produce_date, expire_date, qty, sub_qty,
    create_time, update_time
  </sql>
    <select id="getBatchInfo" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.GoodsBatchEntity">
        select
        <include refid="Base_Column_List"/>
        from acc_goodsstock_batchlife
        where profile_id=#{profileId}
        and batchno=#{batchNo}
        and ptype_id=#{ptypeId}
    </select>
</mapper>
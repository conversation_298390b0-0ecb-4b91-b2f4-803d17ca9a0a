<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.PtypeDownloadTaskMapper">
    <insert id="insertPtypeDownloadTask">
        insert into pl_eshop_ptype_download_task (taskid, `type`, status, content, etypeid, profile_id, message, endtime,
                                                  targetType, id, eshopid,sync_mode,clear_redundant,stock_state,seller_class)
        values (#{taskid}, #{type}, #{status}, #{content}, #{etypeId}, #{profileId}, #{message}, #{endtime},
                #{targetType}, #{id}, #{eshopid}, #{syncMode},#{clearRedundant},#{stockState},#{sellerClass})
    </insert>

    <insert id="initProductSyncConfig">
        insert into pl_eshop_product_sync_condition (id, eshop_id, profile_id, auto_refresh_product_enabled)
        values
        <foreach collection="syncConfigList" close=")" open="(" separator="," item="item">
            #{item.id},#{item.eshopId},#{item.profileId},#{item.autoRefreshProductEnabled}
        </foreach>

    </insert>

    <update id="modifyPtypeDownloadTask">
        update pl_eshop_ptype_download_task set
        <if test="content!=null">
            content=#{content},
        </if>
        <if test="message!=null">
            message=#{message},
        </if>
        <if test="endtime!=null">
            endtime=#{endtime},
        </if>
        status=#{status}
        where profile_id=#{profileId} and etypeid=#{etypeId} and eshopid=#{eshopid} and
        `type`=#{type}
    </update>

    <update id="modifyDownloadTaskStatus">
        update pl_eshop_ptype_download_task set
        status=#{excuteStatus}
        where profile_id=#{profileId} and taskid=#{taskId}
    </update>

    <update id="modifyProductSyncConfig">
        update pl_eshop_product_sync_condition
        set last_download_success_time=#{lastDownloadSuccessTime}
        where profile_id=#{profileId} and eshop_id=#{eshopId}
    </update>

    <select id="queryPtypeDownloadTask"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.PtypeDownloadTask">
        select taskid,
               `type`,
               status,
               content,
               etypeid,
               profile_id,
               message,
               endtime,
               targetType,
               id,
               createtime,
               updatetime,
               sync_mode,
               clear_redundant,
               stock_state,
               seller_class
        from pl_eshop_ptype_download_task
        where profile_id = #{profileId}
          and etypeid = #{etypeId}
          and status != 4
          and `type` = #{type}
          and targetType = #{targetType}
          and eshopid = #{eshopid}
        ORDER BY createtime DESC
        limit 1
    </select>

    <select id="queryAllSyncConfig" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.ProductSyncConfig">
        select profile_id, eshop_id, auto_refresh_product_enabled, last_download_success_time
        from pl_eshop_product_sync_condition
        where profile_id = #{profileId}
    </select>

    <select id="queryPtypeDownloadTaskForPublish" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.PtypeDownloadTask">
        select taskid,
               type,
               status,
               content,
               etypeid,
               profile_id,
               createtime,
               message,
               endtime,
               targettype,
               id,
               updatetime,
               eshopid,
               sync_mode,
               clear_redundant,
               stock_state,
               seller_class
        from pl_eshop_ptype_download_task
        where profile_id = #{profileId} and type in(2,3,4) and status &lt; 2 limit 1
    </select>

</mapper>

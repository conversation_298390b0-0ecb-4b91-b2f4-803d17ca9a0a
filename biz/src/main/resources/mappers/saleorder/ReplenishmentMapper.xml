<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.ReplenishmentMapper">

    <select id="getKtypeLimitedEnable" resultType="boolean">
        SELECT ktype_limited FROM base_etype WHERE profile_id = #{profileId} AND id = #{etypeId}
    </select>

    <select id="getEmployeeAllKtypeLimit" resultType="java.math.BigInteger">
        SELECT limits.object_id FROM base_limit_scope AS limits
                                         LEFT JOIN base_ktype AS ktype ON ktype.profile_id = limits.profile_id AND ktype.id = limits.object_id
                                         LEFT JOIN base_etype AS etype ON etype.profile_id = limits.profile_id AND etype.id = limits.etype_id
        WHERE limits.profile_id =#{profileId} AND limits.etype_id=#{etypeId} AND limits.object_type = 2
          AND ktype.classed = 0 AND ktype.deleted = 0 AND ktype.stoped = 0 AND etype.ktype_limited = 1
    </select>

</mapper>

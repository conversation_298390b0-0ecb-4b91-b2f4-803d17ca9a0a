<?xml version="1.0" encoding="UTF-8"?>
<root>
    <menus identifier="retail">
        <menu name="资料" path="" isnew="true" position="882" icon='bicon-ziliao'>
            <menu name="网店商品" path="" isnew="true" position="882-5">
                <menu name="网店商品对应" key="eshoporder.eshopPtypeRelationPage"
                      function="BuyPlatformFunc||EshopFunc||CloudOrderMiddlegroundFunc"
                      permission-key="eshoporder.eshopPtypeRelationPage.view"
                      path="sale/eshoporder/eshopproduct/PtypeRelation.gspx?closeType=closeAll"
                      position="882-5-2" icon="bicon-baobeiduiying">
                    <permission key="eshoporder.eshopPtypeRelationPage.view" roles="客服,仓管,运营" name="查看"/>

                    <permission key="eshoporder.eshopPtypeRelationPage.refresh" roles="客服,仓管,运营" name="刷新网店商品"/>
                    <permission key="eshoporder.eshopPtypeRelationPage.modifyXcode" name="修改商家编码"/>
                    <permission key="eshoporder.eshopPtypeRelationPage.RelationRule" name="网店配置"/>
                    <permission key="eshoporder.eshopPtypeRelationPage.batchBind" name="绑定/解绑"/>
                    <permission key="eshoporder.eshopPtypeRelationPage.batchAdd" name="增"/>
                    <permission key="eshoporder.eshopPtypeRelationPage.queryPrice" name="查看价格力"/>
                    <permission key="eshoporder.eshopPtypeRelationPage.batchRelationByXcode"
                                name="批量按照商家编码自动对应"/>
                    <permission key="eshoporder.eshopPtypeDownLoadPage.fastRelationExcel" name="Excel快速对应"/>
                    <permission key="eshoporder.eshopPtypeDownLoadPage.addLocalPtype" name="增加本地普通商品"/>
                    <permission key="eshoporder.eshopPtypeDownLoadPage.addSingleLocalPtype"
                                name="增加一个本地属性商品"/>
                    <permission key="eshoporder.eshopPtypeDownLoadPage.addBatchLocalPtype" name="批量增加本地属性商品"/>
                    <permission key="eshoporder.eshopPtypeRelationPage.attrMapping" name="网店商品属性映射"/>
                    <!--                    <permission key="eshoporder.eshopPtypeRelationPage.ExpandAndCose" name="展开/收起当页"/>-->
                    <permission key="eshoporder.eshopPtypeRelationPage.dowmnloadAndCreate"
                                name="全部下载网店商品并生成本地商品"/>
                    <permission key="eshoporder.eshopPtypeRelationPage.syncRelation"
                                name="同步对应关系到子店"/>
                    <permission key="eshoporder.eshopPtypeRelationPage.createCombo" name="智能创建套餐"/>
                    <permission key="eshoporder.eshopPtypeRelationPage.BatchRelationByBarcode"
                                name="批量按照条码自动对应"/>
                    <permission key="eshoporder.eshopPtypeRelationPage.changeRelationByHands" name="按手工对应"/>
                    <permission key="eshoporder.eshopPtypeRelationPage.downlaodMainImage"
                                name="下载网店商品主图做商品主图"/>
                    <permission key="eshoporder.eshopPtypeRelationPage.deleteProduct" name="删除网店商品"/>
                    <include-permission include-key="eshoporder.baseinfo.ptype.add"/>
                </menu>
                <menu name="网店商品打标" key="eshoporder.eshopPtypeMarkPage"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      permission-key="eshoporder.eshopPtypeMarkPage.view"
                      path="sale/eshoporder/eshopproduct/PtypeMarkList.gspx"
                      position="882-5-3" icon="bicon-baobeidabiao">
                    <permission key="eshoporder.eshopPtypeMarkPage.view" roles="客服,运营" name="查看"/>
                    <include-permission include-key="eshoporder.eshopPtypeRelationPage.refresh"/>
                    <permission key="eshoporder.eshopPtypeMarkPage.modify" name="设置网店商品标记"/>
                    <permission key="eshoporder.eshopPtypeMarkPage.export" name="导出数据"/>
                    <permission key="eshoporder.eshopPtypeMarkPage.syncProductMark" name="同步网店商品打标到子店"/>
                    <!--                    <permission key="eshoporder.eshopPtypeRelationPage.batchBind" name="绑定/解绑"/>-->
                    <!--                    <permission key="eshoporder.eshopPtypeRelationPage.batchRelationByXcode"-->
                    <!--                                name="批量按照商家编码自动对应"/>-->
                    <!--                    <permission key="eshoporder.eshopPtypeDownLoadPage.fastRelationExcel" name="EXCEL快速对应"/>-->
                    <include-permission include-key="eshoporder.baseinfo.ptype.add"/>
                </menu>
                <menu name="网店商品上下架" key="eshoporder.eshopPtypeLoadingAndUnloadingPage"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      permission-key="eshoporder.eshopPtypeLoadingAndUnloadingPage.view"
                      path="sale/eshoporder/eshopproduct/EshopPtypeLoadingAndUnloadingPage.gspx"
                      position="882-5-4" icon="bicon-baobeishangxiajia">
                    <permission key="eshoporder.eshopPtypeLoadingAndUnloadingPage.view" roles="客服,运营" name="查看"/>
                    <permission key="eshoporder.eshopPtypeLoadingAndUnloadingPage.excuteUp" name="网店商品上架"/>
                    <permission key="eshoporder.eshopPtypeLoadingAndUnloadingPage.excuteDown" name="网店商品下架"/>
                    <permission key="eshoporder.eshopPtypeLoadingAndUnloadingPage.setRule" name="网店商品上下架规则"/>
                </menu>
                <menu name="商品详情" key="eshoporder.eshopPtypeDetailPage"
                      function="SaleMiddlegroundFunc||EshopFunc||CloudOrderMiddlegroundFunc"
                      permission-key="eshoporder.eshopPtypeDetailPage.view"
                      path="sale/eshoporder/eshopmiddleground/EshopPtypeDetailPage.gspx"
                      position="882-1-5" icon="bicon-baobeiduiying">
                    <permission key="eshoporder.eshopPtypeDetailPage.view" roles="客服,运营" name="查看"/>
                </menu>
                <menu name="网店商品发布" key="eshoporder.eshopPtypePubnish"
                      function="SaleMiddlegroundFunc||EshopFunc||CloudOrderMiddlegroundFunc"
                      permission-key="eshoporder.eshopPtypePubnish.view"
                      path="sale/eshoporder/publish/EshopProductPublishList.gspx"
                      position="882-5-5" icon="bicon-baobeishangxiajia">
                    <permission key="eshoporder.eshopPtypePubnish.view" roles="客服,运营" name="查看"/>
                    <permission key="eshoporder.eshopPtypePubnish.add" roles="客服,运营" name="新增"/>
                    <permission key="eshoporder.eshopPtypePubnish.publish" roles="客服,运营" name="发布"/>
                    <permission key="eshoporder.eshopPtypePubnish.delete" roles="客服,运营" name="删除"/>
                    <permission key="eshoporder.eshopPtypePubnish.batchDelete" roles="客服,运营" name="批量删除"/>
                    <permission key="eshoporder.eshopPtypePubnish.edit" roles="客服,运营" name="编辑"/>
                    <permission key="eshoporder.eshopPtypePubnish.publishImage" roles="客服,运营" name="关联图片"/>
                    <permission key="eshoporder.eshopPtypePubnish.updateAuditStatus" roles="客服,运营"
                                name="更新审核状态"/>
                    <permission key="eshoporder.eshopPtypePubnish.publishEdit" roles="客服,运营" name="批量发布修改"/>
                    <permission key="eshoporder.eshopPtypePubnish.eshopBind" roles="客服,运营" name="绑定网店"/>
                    <permission key="eshoporder.eshopPtypePubnish.eshopBindClear" roles="客服,运营" name="解绑网店"/>
                    <permission key="eshoporder.eshopPtypePubnish.reset" roles="客服,运营" name="重置发布状态"/>
                </menu>
                <menu name="网店商品管理" key="eshoporder.product"
                      function="SaleMiddlegroundFunc||EshopFunc||CloudOrderMiddlegroundFunc"
                      permission-key="eshoporder.product.view"
                      path="sale/eshoporder/product/list.gspx?mode=ptype"
                      position="882-5-6" icon="bicon-baobeishangxiajia">
                    <permission key="eshoporder.product.view" roles="客服,仓管,运营" name="查看"/>
                    <permission key="eshoporder.product.modify" roles="客服,仓管,运营" name="编辑"/>
                    <permission key="eshoporder.product.stock" roles="客服,仓管,运营" name="修改"/>
                </menu>
            </menu>
            <menu name="客户/供应商" path="" position="882-6">
                <menu name="客户/供应商对应" key="eshoporder.platformBtypeMap"
                      path="sale/eshoporder/basic/BtypeMappingList.gspx"
                      isnew="true"
                      position="882-6-2" permission-key="eshoporder.platformBtypeMap.view"
                      icon="bicon-kehugongyingshang">
                    <permission key="eshoporder.platformBtypeMap.view" roles="运营" name="查看"/>
                    <permission key="eshoporder.platformBtypeMap.add" depend="eshoporder.platformBtypeMap.view"
                                roles="运营" name="增"/>
                    <permission key="eshoporder.platformBtypeMap.bind" depend="eshoporder.platformBtypeMap.view"
                                roles="运营" name="选"/>
                    <permission key="eshoporder.platformBtypeMap.clear" depend="eshoporder.platformBtypeMap.view"
                                roles="运营" name="解"/>
                    <permission key="eshoporder.platformBtypeMap.batch" depend="eshoporder.platformBtypeMap.view"
                                roles="运营" name="批量操作"/>
                    <permission key="eshoporder.platformBtypeMap.refresh" depend="eshoporder.platformBtypeMap.view"
                                roles="运营" name="刷新"/>
                </menu>
            </menu>
            <menu name="店铺" path="" isnew="true" position="882-7">
                <menu name="网店" key="eshoporder.eshopInfoPage" path="sale/eshoporder/eshop/EShopList.gspx"
                      function="BuyPlatformFunc||EshopFunc||CloudOrderMiddlegroundFunc"
                      isnew="true"
                      position="882-7-1" permission-key="eshoporder.eshop.view" icon="bicon-xiaoshoujigou">
                    <permission key="eshoporder.eshop.view" roles="运营" name="查看"/>
                    <permission key="eshoporder.eshop.add" roles="运营" name="新增" depend="eshop.view"/>
                    <permission key="eshoporder.eshop.edit" roles="运营" name="修改" depend="eshop.view"/>
                    <permission key="eshoporder.eshop.saleRegister" roles="运营" name="营销活动报名"
                                depend="eshop.view"/>
                    <permission key="eshoporder.eshop.delete" roles="运营" name="删除" depend="eshop.view,eshop.view"/>
                    <permission key="eshoporder.eshop.auth" roles="运营" name="授权" depend="eshop.view"/>
                    <permission key="eshoporder.eshop.cancelauth" roles="运营" name="取消授权" depend="eshop.view"/>
                    <permission key="eshoporder.eshop.defaultConfig" roles="运营" name="通用设置" depend="eshop.view"/>
                    <permission key="eshoporder.eshop.open" roles="运营" name="启用" depend="eshop.view"/>
                    <permission key="eshoporder.eshop.stop" roles="运营" name="停用" depend="eshop.view"/>
                    <permission key="eshoporder.eshop.editClassification" roles="运营" name="编辑分类"
                                depend="eshop.view"/>
                    <!--全渠道门店对应权限-->
                    <permission key="eshoporder.eshopCorrespond.view" name="全渠道门店查看"/>
                    <permission key="eshoporder.eshopCorrespond.set" name="全渠道门店设置"/>
                    <!--平台仓库对应权限-->
                    <permission key="eshoporder.platformWarehouseCorrespond.view" name="平台仓库查看"/>
                    <permission key="eshoporder.platformWarehouseCorrespond.set" name="平台仓库设置"/>
                    <!--商城扣费设置权限-->
                    <permission key="eshoporder.mallDeductionFeeSetting.view" name="商城扣费设置查看"/>
                    <permission key="eshoporder.mallDeductionFeeSetting.edit" name="商城扣费设置设置"/>
                    <!--物流对应设置权限-->
                    <permission key="eshoporder.freightMapping.view" name="物流对应查看"/>
                    <permission key="eshoporder.freightMapping.edit" name="物流对应设置"/>
                    <permission key="eshoporder.stateSubsidies.edit" name="国补业务设置"/>
                </menu>
                <menu name="门店" key="shopsale.store" path="sale/shopsale/pages/store/StoreList.gspx" isnew="true"
                      function="StoreFunc||WebRetailBillFunc||OtypeAccountFunc"
                      position="882-7-2" permission-key="shopsale.store.view" icon="bicon-quanqudaomendianduiying">
                    <permission key="shopsale.store.view" roles="运营,销售" name="查看"/>
                    <permission key="shopsale.store.add" roles="运营,销售" name="新增" depend="shopsale.store.view"/>
                    <permission key="shopsale.store.edit" roles="运营,销售" name="修改" depend="shopsale.store.view"/>
                    <permission key="shopsale.store.delete" roles="运营,销售" name="删除" depend="shopsale.store.view"/>
                    <permission key="shopsale.store.open" roles="运营,销售" name="启用" depend="shopsale.store.view"/>
                    <permission key="shopsale.store.stop" roles="运营,销售" name="停用" depend="shopsale.store.view"/>

                    <permission key="shopsale.store.setting" roles="运营,销售" name="门店设置"
                                depend="shopsale.store.edit"/>
                    <permission key="shopsale.store.allowPosLogin" roles="运营,销售" name="门店登录"
                                depend="shopsale.store.edit"/>
                    <permission key="shopsale.store.barcodeScaleConfig" roles="运营,销售" name="条码秤设置"
                                depend="shopsale.store.view"/>
                    <permission key="shopsale.store.barcodeScalePtype" roles="运营,销售" name="条码秤商品设置"
                                depend="shopsale.store.view"/>
                </menu>
            </menu>
            <!--<menu name="物流管理" key="logistic_manage" isnew="true" position="882-3">
                <menu name="物流模板管理" icon="bicon-wuliumobanguanli" key="logisticPrintTemplate" position="882-3-1"
                      permission-key="jarvis.logisticPrintTemplate.view"
                      path="/sale/jarvis/templatecenter/LogisticPrintTemplateList.gspx"
                      target="iframe" isnew="true">
                    <permission key="jarvis.logisticPrintTemplate.view" roles="仓管" name="查看"/>
                    <permission key="jarvis.logisticPrintTemplate.add" roles="仓管" name="新增"/>
                    <permission key="jarvis.logisticPrintTemplate.modify" roles="仓管" name="编辑"/>
                    <permission key="jarvis.logisticPrintTemplate.design" roles="仓管" name="样式设计"/>
                    <permission key="jarvis.logisticPrintTemplate.stop" roles="仓管" name="停用"/>
                    <permission key="jarvis.logisticPrintTemplate.start" roles="仓管" name="启用"/>
                    <permission key="jarvis.logisticPrintTemplate.delete" roles="仓管" name="删除"/>
                    <permission key="jarvis.logisticPrintTemplate.set" roles="仓管" name="运费设置"/>
                </menu>
                <menu name="物流不达区域" icon="bicon-diquguanli" key="freightAreaNotSend" position="882-3-2"
                      permission-key="jarvis.freightAreaNotSend.view"
                      path="/sale/jarvis/DeliverBill/Strategy/FreightNotSendArea.gspx"
                      target="iframe" isnew="true">
                    <permission key="jarvis.freightAreaNotSend.view" roles="仓管" name="查看"/>
                    <permission key="jarvis.freightAreaNotSend.add" roles="仓管" name="新增"/>
                    <permission key="jarvis.freightAreaNotSend.modify" roles="仓管" name="修改"/>
                    <permission key="jarvis.freightAreaNotSend.delete" roles="仓管" name="删除"/>
                </menu>
            </menu>-->
            <menu name="直播" path="" isnew="true" position="882-7.5">
                <menu name="直播场次管理" key="eshoporder.eshopBroadcastSession"
                      permission-key="eshoporder.eshopBroadcastSession.view"
                      path="sale/eshoporder/eshopsaleorder/BroadcastSessionList.gspx"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      position="882-7.5-1" icon="bicon-baobeidabiao">
                    <permission key="eshoporder.eshopBroadcastSession.view" roles="客服,运营" name="查看"/>
                    <permission key="eshoporder.eshopBroadcastSession.add" name="新增"/>
                    <permission key="eshoporder.eshopBroadcastSession.modify" name="修改"/>
                    <permission key="eshoporder.eshopBroadcastSession.delete" name="删除"/>
                    <permission key="eshoporder.eshopBroadcastSession.copy" name="复制新增"/>
                </menu>
                <!--            <menu name="主播管理" key="eshoporder.eshopBroadcastSessionEdit"-->
                <!--                  permission-key="eshoporder.eshopBroadcastSessionEdit.view"-->
                <!--                  path="sale/eshoporder/eshopproduct/BroadcastSessionEdit.gspx"-->
                <!--                  position="882-9-2" icon="bicon-baobeidabiao">-->
                <!--                <permission key="eshoporder.eshopBroadcastSessionEdit.view" roles="客服,运营" name="查看"/>-->
                <!--                <permission key="eshoporder.eshopBroadcastSessionEdit.modify" name="修改"/>-->
                <!--                <permission key="eshoporder.eshopBroadcastSessionEdit.delete" name="删除"/>-->
                <!--            </menu>-->
            </menu>

        </menu>
        <menu name="网店" path="" isnew="true" position="886" icon='bicon-wangdian'>
            <menu name="平台订单" path="" isnew="true" position="886-1">
                <menu name="平台原始订单池" key="eshoporder.eshopSaleOrderPage"
                      permission-key="eshoporder.eshopsaleorder.view"
                      path="sale/eshoporder/eshopsaleorder/EShopSaleOrderList.gspx?closeType=closeAll"
                      function="BuyPlatformFunc||EshopFunc||CloudOrderMiddlegroundFunc"
                      position="886-1-1" icon="bicon-pingtaidingdan">
                    <permission key="eshoporder.eshopsaleorder.view" roles="审单员,仓管" name="查看"/>
                    <permission key="eshoporder.eshopsaleorder.downloadOrder" roles="审单员" name="下载订单"
                                depend="eshopDownloadOrderPage.download"/>
                    <permission key="eshoporder.eshopsaleorder.updateOrderAllData" roles="审单员"
                                name="重新下载"/>
                    <permission key="eshoporder.eshopsaleorder.submit" roles="审单员" name="确认提交"/>
                    <permission key="eshoporder.eshopsaleorder.relation" roles="审单员" name="快速对应"/>
                    <permission key="eshoporder.eshopsaleorder.relation.add" roles="审单员" name="快速对应-增"
                                value="true"/>
                    <permission key="eshoporder.eshopsaleorder.submitConfig" roles="审单员" name="提交说明"/>
                    <!--                    <permission key="eshoporder.eshopsaleorder.updateBuyerMessage" roles="审单员" name="修改卖家备注"/>-->
                    <!--                    <permission key="eshoporder.eshopsaleorder.updateRemark" roles="审单员" name="修改系统备注"/>-->
                    <!--                    <permission key="eshoporder.eshopsaleorder.closeOrder" roles="审单员" name="关闭交易"/>-->
                    <permission key="eshoporder.eshopsaleorder.export" roles="审单员" name="导出"/>
                    <permission key="eshoporder.eshopCreateOrderPage.view" roles="审单员" name="新增订单"/>
                    <permission key="eshoporder.eshopEditOrderPage.view" roles="审单员" name="编辑订单"/>
                    <permission key="eshoporder.eshopImportOrderPage.view" roles="审单员" name="导入订单"/>
                    <permission key="eshoporder.eshopsaleorder.viewOnlineOrder" roles="审单员" name="查看网店订单"/>
                    <permission key="eshoporder.eshopsaleorder.copy" roles="审单员" name="复制该字段"/>
                    <!--                    <permission key="eshoporder.eshopsaleorder.gather" roles="审单员,财务" name="线下收款"/>-->
                    <!--                    <permission key="eshoporder.eshopsaleorder.allowautogather" roles="审单员,财务"-->
                    <!--                                name="流水自动生成收款单"/>-->
                    <permission key="eshoporder.eshopsaleorder.platformBtypeMapBind"
                                depend="eshoporder.platformBtypeMap.view" roles="审单员,财务,运营"
                                name="客户/供应商对应"/>
                    <permission key="eshoporder.eshopsaleorder.deleteOrRecoveryOrder" roles="审单员,财务,运营"
                                name="删除/恢复订单"/>
                    <include-permission include-key="sale.buyer.cryptograph.view"/>
                    <permission key="eshoporder.eshopsaleorder.copyBuyerInfo" roles="审单员,仓管,打单员" value="true"
                                name="复制买家信息"/>
                </menu>
                <menu name="下载订单" key="eshoporder.eshopDownloadOrderPage"
                      permission-key="eshoporder.eshopDownloadOrderPage.view"
                      path="sale/eshoporder/eshopsaleorder/Download.gspx"
                      function="BuyPlatformFunc||EshopFunc||CloudOrderMiddlegroundFunc"
                      position="886-1-2" icon="bicon-xiazaidingdan">
                    <permission key="eshoporder.eshopDownloadOrderPage.view" roles="审单员" name="查看"/>
                    <permission key="eshoporder.eshopDownloadOrderPage.downloadById" roles="审单员" name="按单号下载"
                                depend="eshopDownloadOrderPage.view"/>
                    <permission key="eshoporder.eshopDownloadOrderPage.download" name="手工下载" roles="审单员"
                                depend="eshopDownloadOrderPage.view"/>
                    <permission key="eshoporder.eshopDownloadOrderPage.autoDownload" name="自动下载" roles="审单员"
                                depend="eshopDownloadOrderPage.view"/>
                </menu>
                <menu name="新增订单" key="eshoporder.eshopCreateOrderPage"
                      path="/jxc/recordsheet/stock/SupplyMarketingBillNew.gspx?vchtype=OriginalSaleOrder"
                      permission-key="eshoporder.eshopCreateOrderPage.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      position="886-1-3" icon="bicon-xinzengxiaoshoudingdan">
                    <permission key="eshoporder.eshopCreateOrderPage.view" roles="审单员" name="查看"/>
                    <permission key="eshoporder.eshopCreateOrderPage.save" roles="审单员" name="保存"
                                depend="eshopCreateOrderPage.view"/>
                    <permission key="eshoporder.eshopCreateOrderPage.gift" name="标记/取消赠品"
                                depend="eshopCreateOrderPage.view"/>
                </menu>
                <menu name="导入订单" key="eshoporder.eshopImportOrderPage"
                      path="sale/eshoporder/eshopsaleorder/EShopSaleOrderImport.gspx?closeType=closeAll"
                      permission-key="eshoporder.eshopImportOrderPage.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      position="886-1-4" icon="bicon-daorudingdan">
                    <permission key="eshoporder.eshopImportOrderPage.view" roles="审单员" name="查看"/>
                </menu>
            </menu>
            <!--            //todo:#新增#数据库字段#多个界面的权限点处理-->
            <menu name="订单处理" isnew="true" position="886-2">
                <!--                <menu name="订单处理业务流程引导" icon="bicon-yifahuodingdanchaxun" key="eShopBusinessProcessGuideDiagram.view"-->
                <!--                      permission-key="jarvis.eShopBusinessProcessGuideDiagram.view"-->
                <!--                      function="EshopFunc"-->
                <!--                      path="/sale/jarvis/DeliverBill/EShopBusinessProcessGuideDiagram.gspx?closeType=url"-->
                <!--                      target="iframe" isnew="true" position="886-2-0.1">-->
                <!--                    <permission key="jarvis.eShopBusinessProcessGuideDiagram.view" roles="客服,仓管,审单员" name="查看"/>-->
                <!--                </menu>-->
                <menu name="预售订单处理" key="eshoporder.eshopCreateAdvanceOrderPage"
                      permission-key="eshoporder.eshopAdvanceSaleOrder.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      path="sale/eshoporder/advance/EshopAdvanceSaleOrderList.gspx?caption=预售订单处理&amp;tag=AUDIT&amp;closeType=url"
                      position="886-2-1" icon="bicon-yushoudingdanguanli">
                    <permission key="eshoporder.eshopAdvanceSaleOrder.view" roles="审单员,仓管" name="查看"/>
                    <permission key="eshoporder.eshopAdvanceSaleOrder.submit" roles="审单员" name="确认提交"/>
                    <!--                    <permission key="eshoporder.eshopAdvanceSaleOrder.submitConfig" roles="审单员" name="自动提交规则"/>-->
                    <permission key="eshoporder.eshopAdvanceSaleOrder.add" roles="审单员" name="新增预售订单"/>
                    <permission key="eshoporder.eshopAdvanceSaleOrder.export" roles="审单员" name="导出"/>
                    <permission key="eshoporder.eshopAdvanceSaleOrder.updatePlanSendTime" roles="审单员"
                                name="修改预计发货时间"/>
                    <permission key="eshoporder.eshopAdvanceSaleOrder.updateSellerMemo" roles="审单员"
                                name="改卖家备注(旗帜)"/>
                    <!--                    <permission key="eshoporder.eshopAdvanceSaleOrder.updateBusinessType" roles="审单员"-->
                    <!--                                name="修改预售交易类型"/>-->
                    <permission key="eshoporder.eshopAdvanceSaleOrder.copy" roles="审单员" name="复制该字段"/>
                    <!--<permission key="eshoporder.eshopAdvanceSaleOrder.updateOrder" roles="审单员" name="更新订单"/>-->
                    <permission key="eshoporder.eshopAdvanceSaleOrder.viewOnlineOrder" roles="审单员"
                                name="查看网店订单"/>
                    <permission key="eshoporder.eshopAdvanceSaleOrder.update" roles="审单员" name="更新"/>
                    <permission key="eshoporder.eshopAdvanceSaleOrder.mark" roles="审单员" name="自定义标记"
                                value="true"/>
                    <include-permission include-key="sale.buyer.cryptograph.view"/>
                    <permission key="eshoporder.eshopAdvanceSaleOrder.copyBuyerInfo" roles="审单员,仓管,打单员"
                                value="true"
                                name="复制买家信息"/>
                </menu>
                <menu name="代销订单管理" key="deliverSaleProxy" icon="bicon-dingdanchulicelve"
                      permission-key="jarvis.deliverSaleProxy.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      path="/sale/jarvis/DeliverBill/DeliverBillList.gspx?caption=代销订单管理&amp;tag=SALEPROXY&amp;closeType=url"
                      target="iframe" isnew="true" position="886-2-1.5">
                    <permission key="jarvis.deliverSaleProxy.view" roles="仓管,审单员" name="查看"/>
                    <permission key="jarvis.deliverSaleProxy.viewLog" roles="审单员" name="查看操作日志"/>
                    <permission key="jarvis.deliverCommon.rejectUnSend" roles="仓管,打单员,发货员"
                                name="驳回审核(未出库单据)"/>
                    <permission key="jarvis.deliverCommon.rejectShipped" roles="仓管,打单员,发货员"
                                name="驳回审核(已出库未核算)"/>
                    <permission key="jarvis.deliverCommon.rejectAudit" roles="仓管,打单员,发货员"
                                name="驳回审核(已财务核算)"/>
                    <permission key="jarvis.deliverCommon.rejectAuditConfig" roles="仓管,打单员,发货员"
                                name="驳回原因配置"/>
                    <permission key="jarvis.deliverSaleProxy.copyValue" roles="审单员" name="复制该字段"/>
                    <permission key="jarvis.deliverCommon.lockDeliverBill" roles="审单员" name="截停单据"/>
                    <permission key="jarvis.deliverCommon.lockDeliverBillConfig" roles="审单员" name="截停说明配置"/>
                    <permission key="jarvis.deliverCommon.cancelLock" roles="审单员" name="取消截停"/>
                    <permission key="jarvis.deliverSaleProxy.importFreight" roles="审单员" name="导入修改订单"/>
                    <permission key="jarvis.deliverCommon.modifyFreight" roles="仓管,审单员" name="修改物流信息"/>
                    <permission key="jarvis.deliverCommon.modifySupplier" roles="审单员" name="修改发货供应商"/>
                    <permission key="jarvis.deliverCommon.modifyPurchasePrice" roles="仓管,审单员" name="改采购单价"
                    />
                    <permission key="jarvis.deliverCommon.export" roles="仓管,打单员" name="导出"/>
                    <permission key="jarvis.deliverCommon.copyReceiveInfo" roles="审单员,仓管,打单员"
                                name="复制收货信息"/>
                    <permission key="jarvis.deliverCommon.send" name="系统发货" roles="仓管,审单员"/>
                    <include-permission include-key="jarvis.deliverCommon.syncWayBill"/>
                    <permission key="jarvis.deliverSaleProxy.modifyDetail" roles="审单员" name="修改明细信息"/>
                    <permission key="jarvis.deliverSaleProxy.taoBaoDistribution" roles="审单员" name="淘宝分单"/>
                    <permission key="jarvis.deliverSaleProxy.taoBaoDistributionCancel" roles="审单员"
                                name="取消淘宝分单"/>
                    <permission key="jarvis.deliverCommon.viewPriceAndTotal" roles="审单员,仓管"
                                name="查看单价和金额" value="true"/>
                    <include-permission include-key="jarvis.deliverCommon.contactBuyer"/>
                    <include-permission include-key="sale.buyer.cryptograph.view"/>
                    <!--                    <include-permission include-key="jarvis.sendDeliver.showInvoiceDecrypt"/>
                                        <include-permission include-key="jarvis.deliverAudit.showSenderDecrypt"/>-->
                    <!--                    <include-permission include-key="jarvis.deliverCommon.showBuyerInfo"/>-->
                </menu>
                <menu name="订单处理" icon="bicon-xiaoshoudingdanshenhe" key="deliverDeal"
                      permission-key="jarvis.deliverAudit.view,jarvis.simplePrintAndDeliver.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      path="/sale/jarvis/DeliverBill/DeliverBillDeal.gspx?caption=订单处理&amp;tag=DEAL&amp;closeType=closeAll"
                      target="iframe" isnew="true" position="886-2-2.99">
                </menu>
                <menu name="待审核" icon="bicon-xiaoshoudingdanshenhe" key="deliverAudit"
                      permission-key="jarvis.deliverAudit.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      path="/sale/jarvis/DeliverBill/DeliverBillList.gspx?caption=订单审核&amp;tag=AUDIT&amp;closeType=url"
                      hidden="true"
                      target="iframe" isnew="true" position="886-2-3">
                    <permission key="jarvis.deliverAudit.view" roles="仓管,审单员" name="查看"/>
                    <permission key="jarvis.deliverAudit.batchAddPtype" roles="审单员" name="增商品"/>
                    <permission key="jarvis.deliverAudit.batchUpdatePtype" roles="审单员" name="换商品"/>
                    <permission key="jarvis.deliverAudit.batchDelPtype" roles="审单员" name="删商品"/>
                    <!--            <permission key="jarvis.deliverAudit.autoFillBatch" roles="审单员" name="自动填写批次"/>-->
                    <permission key="jarvis.deliverAudit.modifyDetail" roles="审单员" name="修改明细信息"/>
                    <permission key="jarvis.deliverCommon.splitByHandel" roles="审单员" name="手工拆分"/>
                    <permission key="jarvis.deliverAudit.cancelSplit" name="取消拆分" roles="审单员"/>
                    <permission key="jarvis.deliverAudit.splitByAuto" roles="审单员" name="策略拆分"/>
                    <permission key="jarvis.deliverAudit.mergeByHandle" roles="审单员" name="手工合并"/>
                    <permission key="jarvis.deliverAudit.cancelMerge" name="取消合并" roles="审单员"/>
                    <permission key="jarvis.deliverAudit.mergeByAuto" roles="审单员" name="策略合并"/>
                    <permission key="jarvis.deliverAudit.modifySeller" roles="审单员" name="改备注信息(旗帜)"/>
                    <include-permission include-key="jarvis.sendDeliver.modifyInvoice"/>
                    <!--<include-permission include-key="jarvis.sendDeliver.showInvoiceDecrypt"/>-->
                    <permission key="jarvis.deliverAudit.modifySender" roles="审单员" name="改寄件信息"/>
                    <!--<permission key="jarvis.deliverAudit.showSenderDecrypt" roles="审单员" name="明文显示寄件信息"/>-->
                    <permission key="jarvis.deliverAudit.modifyReceiver" roles="审单员" name="改收件信息"/>
                    <permission key="jarvis.deliverAudit.viewLog" roles="审单员" name="查看操作日志"/>
                    <permission key="jarvis.deliverAudit.showAdvancePayment" roles="审单员" name="查看预收款"/>
                    <permission key="jarvis.deliverAudit.copyValue" roles="审单员" name="复制该字段"/>

                    <permission key="jarvis.deliverCommon.matchStrategy" roles="审单员" name="策略匹配"/>
                    <!--            <permission key="jarvis.deliverAudit.batchUpdateMark" roles="审单员" name="批量更新"/>-->

                    <permission key="jarvis.deliverAudit.autoAudit" roles="审单员" name="策略审核"/>
                    <permission key="jarvis.deliverAudit.handAudit" roles="审单员" name="手工审核"/>
                    <permission key="jarvis.deliverAudit.forceAudit" roles="审单员" name="强制审核" value="true"
                                depend="jarvis.deliverAudit.handAudit"/>

                    <permission key="jarvis.deliverAudit.mark" roles="审单员" name="自定义标记"/>
                    <permission key="jarvis.deliverAudit.markConfig" roles="审单员" name="标记管理"/>

                    <permission key="jarvis.deliverAudit.updateDeliverBill" roles="审单员" name="还原单据"/>
                    <permission key="jarvis.deliverAudit.resetAndupdateDeliverBill" roles="审单员" name="更新并还原"/>
                    <permission key="jarvis.deliverCommon.deleteDeliverBill" roles="审单员" name="删除单据"/>
                    <permission key="jarvis.deliverCommon.recoverDeletedBill" roles="审单员" name="取消删除单据"/>

                    <permission key="jarvis.deliverCommon.lockDeliverBill" roles="审单员" name="截停单据"/>
                    <permission key="jarvis.deliverCommon.lockDeliverBillConfig" roles="审单员" name="截停说明配置"/>
                    <permission key="jarvis.deliverCommon.cancelLock" roles="审单员" name="取消截停"/>
                    <permission key="jarvis.deliverCommon.lockSendQty" roles="审单员" name="可发货库存锁定"
                                value="true"/>
                    <permission key="jarvis.deliverCommon.cancelLockSendQty" roles="审单员" name="取消可发货库存锁定"
                                value="true"/>
                    <permission key="jarvis.deliverCommon.modifyAuthor" roles="审单员" name="改经手人"/>
                    <permission key="jarvis.deliverCommon.modifyBusinessSelfDeliver" roles="审单员" name="改发货信息"/>
                    <include-permission include-key="jarvis.deliverPrint.modifyDeliverTemplate"/>

                    <!--            <permission key="jarvis.deliverAudit.modifyDeliverBillMain" roles="审单员" name="修改主表信息"/>-->
                    <permission key="jarvis.deliverAudit.importFreight" roles="审单员" name="导入修改订单"/>
                    <!--            <permission key="jarvis.deliverCommon.addDeliverBill" roles="审单员" name="新增销售单据"/>-->
                    <permission key="jarvis.deliverCommon.batchModify" roles="仓管,打单员" name="改单据信息"/>
                    <permission key="jarvis.deliverCommon.modifyKType" roles="仓管,打单员" name="修改仓库"/>
                    <permission key="jarvis.deliverCommon.modifyDeliverProcessType" roles="仓管,打单员"
                                name="修改发货流程管理方式"/>
                    <permission key="jarvis.deliverCommon.modifyFreight" roles="仓管,审单员" name="修改物流信息"/>
                    <permission key="jarvis.deliverCommon.modifySupplier" roles="审单员" name="修改发货供应商"/>
                    <permission key="jarvis.deliverCommon.export" roles="仓管,打单员" name="导出"/>
                    <permission key="jarvis.deliverCommon.copyReceiveInfo" roles="审单员,仓管,打单员"
                                name="复制收货信息"/>
                    <include-permission include-key="jarvis.deliverCommon.doRelation"/>
                    <permission key="jarvis.deliverCommon.viewPriceAndTotal" roles="审单员,仓管"
                                name="查看单价和金额" value="true"/>
                    <include-permission include-key="jarvis.deliverCommon.contactBuyer"/>

                    <permission key="sale.buyer.cryptograph.view" roles="仓管,审单员" name="明文显示收货信息"
                                value="true"/>

                    <permission key="jarvis.deliverCommon.modifyDisedTaxPrice" roles="仓管,审单员" name="改折后含税单价"
                    />
                    <permission key="jarvis.deliverCommon.modifyPurchasePrice" roles="仓管,审单员" name="改采购单价"
                    />
                    <permission key="jarvis.deliverCommon.modifyBroadcast" roles="仓管,审单员" name="改直播场次信息"
                    />
                    <permission key="jarvis.deliverCommon.importModifyBill" roles="仓管,打单员"
                                name="导入修改单据信息"/>
                    <permission key="jarvis.deliverCommon.checkNotSendArea" roles="仓管,打单员,发货员"
                                name="物流不达检查"/>
                    <permission key="jarvis.deliverCommon.confirmOrder" roles="仓管,审单员" name="接单"/>
                    <permission key="jarvis.deliverCommon.syncPlatformEnclosure" roles="仓管,审单员"
                                name="上传发货要求"/>
                    <!--                    <permission key="jarvis.deliverCommon.showBuyerInfo" roles="审单员,仓管,打单员"-->
                    <!--                                name="查看买家信息"/>-->
                    <permission key="jarvis.deliverAudit.delayDelivery" roles="审单员" name="延迟发货" value="true"/>
                    <permission key="jarvis.deliverAudit.cancelDelayDelivery" roles="审单员" name="取消延迟发货"
                                value="true"/>
                    <permission key="jarvis.deliverCommon.uploadSnToOrders" roles="仓管,审单员"
                                name="平台验证SN"/>
                    <permission key="jarvis.deliverAudit.modifyMainBill" roles="仓管,审单员"
                                name="更换主单号"/>
                    <permission key="jarvis.deliverAudit.modifyQIC" roles="仓管,审单员"
                                name="修改QIC质检方式"/>
                    <permission key="jarvis.deliverAudit.modifyRelation" roles="仓管,审单员"
                                name="更新网店商品与商品对应关系"/>
                    <!--                    <permission key="jarvis.deliverAudit.editConsumables" roles="仓管,审单员"-->
                    <!--                                name="编辑耗材"/>-->
                    <permission key="jarvis.deliverAudit.eshopBillConfig" roles="审单员"
                                name="网店单据设置"/>
                    <permission key="jarvis.deliverCommon.getFreightBillNoAudit" roles="仓管,审单员"
                                name="获取物流单号"/>
                    <!--                    <permission key="jarvis.deliverCommon.syncFreightBillNoAudit" roles="仓管,审单员"-->
                    <!--                                name="同步物流单号"/>-->
                    <permission key="jarvis.deliverCommon.syncWayBill" roles="仓管,审单员" name="同步单号"/>
                    <permission key="jarvis.deliverCommon.cancelFreightBillNoAudit" roles="仓管,审单员"
                                name="取消物流单号"/>
                    <permission key="jarvis.deliverCommon.copyCreateNewSaleOrder" roles="仓管,打单员"
                                name="复制新增订单"/>
                </menu>
                <menu name="订单查询" icon="bicon-yifahuodingdanchaxun" key="sendDeliver.view"
                      permission-key="jarvis.sendDeliver.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      path="/sale/jarvis/DeliverBill/DeliverBillList.gspx?caption=订单查询&amp;tag=QUERY&amp;closeType=url"
                      target="iframe" isnew="true" position="886-2-5">
                    <permission key="jarvis.sendDeliver.view" roles="客服,仓管,审单员" name="查看"/>
                    <permission key="jarvis.sendDeliver.copyValue" roles="客服,仓管,审单员" name="复制该字段"/>
                    <permission key="jarvis.deliverCommon.rejectUnSend" roles="仓管,打单员,发货员"
                                name="驳回审核(未出库单据)"/>
                    <permission key="jarvis.deliverCommon.rejectShipped" roles="仓管,打单员,发货员"
                                name="驳回审核(已出库未核算)"/>
                    <permission key="jarvis.deliverCommon.rejectAudit" roles="仓管,打单员,发货员"
                                name="驳回审核(已财务核算)"/>
                    <permission key="jarvis.deliverCommon.rejectAuditConfig" roles="仓管,打单员,发货员"
                                name="驳回原因配置"/>
                    <permission key="jarvis.sendDeliver.viewLog" roles="仓管,审单员" name="查看操作日志"/>
                    <permission key="jarvis.sendDeliver.showAdvancePayment" roles="仓管,审单员" name="查看预收款"/>
                    <permission key="jarvis.deliverCommon.syncWayBill" roles="仓管,审单员" name="同步单号"/>
                    <permission key="jarvis.sendDeliver.importFreight" name="导入修改订单" roles="仓管,审单员"/>
                    <include-permission include-key="jarvis.deliverCommon.export"/>
                    <!--<include-permission include-key="jarvis.deliverAudit.showSenderDecrypt"/>-->
                    <permission key="jarvis.sendDeliver.modifyDetail" roles="仓管,审单员" name="修改明细信息"/>
                    <permission key="jarvis.deliverCommon.lockDeliverBill" roles="仓管,审单员" name="截停单据"/>
                    <permission key="jarvis.deliverCommon.lockDeliverBillConfig" roles="审单员" name="截停说明配置"/>
                    <permission key="jarvis.deliverCommon.cancelLock" roles="仓管,审单员" name="取消截停"/>
                    <permission key="jarvis.sendDeliver.rePost" name="重新核算" roles="仓管,审单员,打单员"/>
                    <permission key="jarvis.sendDeliver.rePostSetBtn" name="核算后单据时间设置"
                                roles="仓管,审单员,打单员"/>
                    <permission key="jarvis.deliverCommon.modifyFreight" roles="仓管,审单员" name="修改物流信息"/>
                    <permission key="jarvis.deliverCommon.copyReceiveInfo" roles="仓管,打单员" name="复制收货信息"/>
                    <permission key="jarvis.deliverQuery.mark" roles="审单员" name="自定义标记"/>
                    <permission key="jarvis.deliverQuery.markConfig" roles="审单员" name="标记管理"/>
                    <permission key="jarvis.deliverCommon.viewPriceAndTotal" roles="审单员,仓管" name="查看单价和金额"
                                value="true"/>
                    <include-permission include-key="eshoporder.eshoprefund.add"/>
                    <permission key="jarvis.deliverCommon.contactBuyer" roles="审单员" name="联系买家"/>
                    <include-permission include-key="sale.buyer.cryptograph.view"/>
                    <include-permission include-key="jarvis.deliverAudit.modifySeller"/>
                    <permission key="jarvis.sendDeliver.modifyInvoice" roles="仓管,审单员" name="改发票信息"/>
                    <!--<permission key="jarvis.sendDeliver.showInvoiceDecrypt" roles="仓管,审单员" name="明文显示发票信息"/>-->
                    <permission key="jarvis.sendDeliver.reSubmitInvoice" roles="仓管,审单员" name="提交发票"/>
                    <permission key="jarvis.sendDeliver.uploadInvoice" roles="审单员" name="上传发票"/>
                    <permission key="jarvis.deliverCommon.modifyDisedTaxPrice" roles="仓管,审单员"
                                name="改折后含税单价"/>
                    <include-permission include-key="jarvis.deliverCommon.syncPlatformEnclosure"/>
                    <!--                    <include-permission include-key="jarvis.deliverCommon.showBuyerInfo"/>-->
                    <include-permission include-key="jarvis.deliverCommon.uploadSnToOrders"/>
                    <permission key="jarvis.sendDeliver.reverseAccount" roles="审单员" name="驳回库存待核算"
                                value="false"/>
                    <permission key="jarvis.deliverCommon.batchUpdateMessageAndMemoMark" roles="仓管,审单员"
                                name="批量更新留言备注"/>
                    <permission key="jarvis.deliverCommon.batchUpdateAddressMark" roles="仓管,审单员"
                                name="批量更新地址"/>
                    <permission key="jarvis.deliverCommon.batchUpdateInvoiceMark" roles="仓管,审单员"
                                name="批量更新发票"/>
                    <permission key="jarvis.deliverCommon.copyCreateNewSaleOrder" roles="仓管,打单员"
                                name="复制新增订单"/>
                    <!--<permission key="jarvis.deliverCommon.doRelation" roles="审单员" name="临时对应"/>-->
                    <!--<permission key="jarvis.sendDeliver.send" name="系统发货" roles="仓管,审单员"/>-->
                    <!--<permission key="jarvis.sendDeliver.reversePost" roles="仓管,审单员" name="反核算"/>-->

                    <!--<permission key="jarvis.sendDeliver.post" roles="仓管,审单员" name="批量核算"/>-->
                    <!--                    <permission key="jarvis.sendDeliver.waitAccountBill" name="待核算订单" roles="仓管,审单员,打单员"/>-->
                </menu>
                <menu name="同步单号" icon="bicon-yifahuodingdanchaxun" key="syncWayBillList.view"
                      permission-key="jarvis.syncWayBillList.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      path="/sale/jarvis/DeliverBill/SyncWayBillList.gspx?caption=同步单号&amp;tag=SYNC&amp;closeType=url"
                      target="iframe" isnew="true" position="886-2-6">
                    <permission key="jarvis.syncWayBillList.view" roles="客服,仓管,审单员" name="查看"/>
                    <include-permission include-key="jarvis.deliverCommon.syncWayBill"/>
                    <permission key="jarvis.syncWayBillList.handleSyncBill" roles="仓管,打单员" name="手工处理"/>
                    <permission key="jarvis.syncWayBillList.export" roles="仓管,打单员" name="导出"/>
                    <permission key="jarvis.syncWayBillList.viewLog" roles="仓管,审单员" name="查看操作日志"/>
                    <permission key="jarvis.syncWayBillList.hint" roles="仓管,打单员" name="发货异常提示" value="true"/>
                </menu>
                <menu name="查找订单" icon="bicon-yifahuodingdanchaxun" key="findBillList.view"
                      permission-key="jarvis.findBillList.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      path="/sale/jarvis/DeliverBill/FindBillList.gspx?caption=查找订单&amp;tag=FIND&amp;closeType=url"
                      target="iframe" isnew="true" position="886-2-7">
                    <permission key="jarvis.findBillList.view" roles="客服,仓管,审单员" name="查看"/>
                    <permission key="jarvis.findBillList.viewLog" roles="仓管,审单员" name="查看操作日志"/>
                    <permission key="jarvis.findBillList.export" roles="仓管,打单员" name="导出"/>
                    <permission key="jarvis.findBillList.viewPriceAndTotal" roles="审单员,仓管" name="查看单价和金额"/>
                </menu>
            </menu>
            <menu name="库存同步" path="" isnew="true" position="886-4.5">
                <menu name="库存同步" key="eshoporder.eshopStockSync" path="sale/eshoporder/stock/StockSyncIndex.gspx"
                      function="BuyPlatformFunc||EshopFunc||CloudOrderMiddlegroundFunc"
                      permission-key="eshoporder.eshopStockSync.view" position="886-4.5-1" icon="bicon-kucuntongbu">
                    <permission key="eshoporder.eshopStockSync.view" roles="仓管,运营" name="查看"/>
                    <permission key="eshoporder.eshopStockSync.sync" roles="仓管,运营" name="手工同步"
                                depend="eshopStockSync.view"/>
                    <permission key="eshoporder.eshopStockSync.warehouseSync" roles="仓管,运营" name="分仓库存同步"
                                depend="eshopStockSync.view"/>
                    <permission key="eshoporder.eshopStockSync.ladderSync" roles="仓管,运营" name="阶梯库存同步"
                                depend="eshopStockSync.view"/>
                    <permission key="eshoporder.eshopStockSync.showLog" roles="仓管,运营" name="查看日志"
                                depend="eshopStockSync.view"/>
                    <permission key="eshoporder.stockRule.view" roles="仓管,运营" name="查看同步库存规则"
                                depend="eshopStockSync.view"/>
                    <permission key="eshoporder.stockRule.add" roles="仓管,运营" name="新增同步库存规则"
                                depend="eshopStockSync.view"/>
                    <permission key="eshoporder.stockRule.update" roles="仓管,运营" name="编辑同步库存规则"
                                depend="eshopStockSync.view"/>
                    <permission key="eshoporder.stockRule.delete" roles="仓管,运营" name="删除同步库存规则"
                                depend="eshopStockSync.view"/>
                </menu>
                <menu name="超卖设置" key="eshoporder.stock.oversold.config"
                      path="sale/eshoporder/stock/OversoldConfig.gspx"
                      function="BuyPlatformFunc||EshopFunc||CloudOrderMiddlegroundFunc"
                      permission-key="eshoporder.stock.oversold.config.view" position="886-4.5-2"
                      icon="bicon-kucuntongbu">
                    <permission key="eshoporder.stock.oversold.config.view" roles="仓管,运营" name="查看"/>
                    <permission key="eshoporder.stock.oversold.config.edit" roles="仓管,运营" name="设置"/>
                </menu>
            </menu>
            <menu name="售后业务处理" path="" isnew="true" position="886-4">
                <menu name="下载售后单" key="eshoporder.eshopRefundDownloadPage"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      path="sale/eshoporder/eshopsaleorder/Download.gspx?from=refund"
                      position="886-4-1" permission-key="eshoporder.eshoprefund.download"
                      icon="bicon-xiazaishouhoudan">
                    <permission key="eshoporder.eshoprefund.download" roles="仓管,审单员" name="下载售后单"
                                depend="eshoprefund.download"/>
                </menu>
                <menu name="售后管理" key="eshoporder.eshopRefundListPage"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      path="sale/jarvis/eshoprefund/EshopRefundList.gspx?closeType=closeAll"
                      position="886-4-2" cross-server-permission-key="eshoporder.eshoprefund.view"
                      icon="bicon-shouhouguanli">
                    <permission key="eshoporder.eshoprefund.view" roles="客服,仓管,审单员" name="查看"/>
                    <permission key="eshoporder.eshoprefund.add" roles="仓管,审单员" name="新增售后单"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.edit" roles="仓管,审单员" name="修改售后单"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.relation.add" roles="仓管,审单员" name="快速对应-增"
                                value="true"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.delete" roles="仓管,审单员" name="删除售后单"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.cancle" roles="仓管,审单员" name="撤销删除"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.audit" roles="仓管,审单员" name="售后审核"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.fileUpload" roles="仓管,审单员" name="附件上传"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <!--                    <permission key="eshoprefund.unAudit" roles="仓管,审单员" name="售后信息取消审核" depend="eshoprefund.view"/>-->
                    <!--fixbug:41252-->
                    <!--<permission key="eshoprefund.bind" roles="仓管,审单员" name="关联收货记录" depend="eshoprefund.view"/>
                    <permission key="eshoprefund.unbind" roles="仓管,审单员" name="解绑收货记录" depend="eshoprefund.view"/>-->
                    <permission key="eshoporder.eshoprefund.relateCheckin" roles="客服,仓管,审单员"
                                name="关联/取消收货记录"
                                cross-server-depend="eshoporder.eshoprefund.view" value="true"/>
                    <permission key="eshoporder.eshoprefund.updaterefund" roles="客服,仓管,审单员"
                                name="重新下载"
                                value="true"/>
                    <permission key="eshoporder.eshoprefund.refundagree" roles="客服,仓管,审单员" name="同意/拒绝仅退款"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <!--                    <permission key="eshoporder.eshoprefund.refundrefuse" roles="客服,仓管,审单员" name="拒绝仅退款"-->
                    <!--                                cross-server-depend="eshoporder.eshoprefund.view"/>-->

                    <permission key="eshoporder.eshoprefund.returnagree" roles="客服,仓管,审单员" name="同意/拒绝退货"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <!--                    <permission key="eshoporder.eshoprefund.returnrefuse" roles="客服,仓管,审单员" name="拒绝退货"-->
                    <!--                                cross-server-depend="eshoporder.eshoprefund.view"/>-->
                    <permission key="eshoporder.eshoprefund.returnrefundagree" roles="客服,仓管,审单员"
                                name="已退货并同意/拒绝退款"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <!--                    <permission key="eshoporder.eshoprefund.returnrefundrefuse" roles="客服,仓管,审单员" name="已退货并拒绝退款"-->
                    <!--                                cross-server-depend="eshoporder.eshoprefund.view"/>-->

                    <permission key="eshoporder.eshoprefund.exchangeagree" roles="客服,仓管,审单员" name="同意/拒绝换货"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <!--                    <permission key="eshoporder.eshoprefund.exchangerefuse" roles="客服,仓管,审单员" name="拒绝换货"-->
                    <!--                                cross-server-depend="eshoporder.eshoprefund.view"/>-->
                    <permission key="eshoporder.eshoprefund.returnexchangeagree" roles="客服,仓管,审单员"
                                name="已退货并完成/拒绝换货"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <!--                    <permission key="eshoporder.eshoprefund.returnexchangerefuse" roles="客服,仓管,审单员" name="已退货并拒绝换货"-->
                    <!--                                cross-server-depend="eshoporder.eshoprefund.view"/>-->

                    <permission key="eshoporder.eshoprefund.processagree" roles="客服,仓管,审单员"
                                name="同意/拒绝补发"/>
                    <!--                                cross-server-depend="eshoporder.eshoprefund.view"/>-->
                    <!--                    <permission key="eshoporder.eshoprefund.allowautopayment" roles="财务,审单员"-->
                    <!--                                name="流水自动生成付款单"/>-->
                    <!--<permission server-key="eshoporder.eshoprefund.config" roles="仓管,审单员" name="售后业务配置"-->
                    <!--            cross-server-depend="eshoporder.eshoprefund.view"/>-->
                    <!--                    <permission key="eshoporder.eshoprefund.processrefuse" roles="客服,仓管,审单员" name="拒绝补发"-->
                    <!--                                cross-server-depend="eshoporder.eshoprefund.view"/>-->


                    <permission key="eshoporder.eshoprefund.receive" roles="客服,仓管,审单员" name="生单并入库"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.factoryprocessing" roles="仓管,审单员"
                                name="厂家一并处理售后"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.distributorprocessing" roles="仓管,审单员"
                                name="提交分销商处理"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.yunreceive" roles="客服,仓管,审单员" name="生单并提交云仓"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.deal" roles="仓管,审单员" name="补发生单"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.cancelProcess" roles="仓管,审单员" name="取消补发"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.reProcess" roles="仓管,审单员" name="重新补发"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.pay" roles="仓管,审单员" name="退款支付"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <!--                    <permission key="eshoporder.eshoprefund.payment" roles="财务,审单员" name="线下付款"-->
                    <!--                                cross-server-depend="eshoporder.eshoprefund.view"/>-->
                    <!--                    <permission key="eshoporder.eshoprefund.allowautopayment" roles="财务,审单员"-->
                    <!--                                name="流水自动生成付款单"/>-->
                    <permission key="eshoporder.eshoprefund.config" roles="仓管,审单员" name="售后业务配置"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.export" roles="仓管,审单员" name="导出"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <!--<permission key="eshoprefund.viewexport" roles="仓管,审单员" name="查看导出记录" depend="eshoprefund.view"/>-->
                    <permission key="eshoporder.eshoprefund.editrefundtype" roles="仓管,审单员"
                                name="修改售后类型"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.modifyktype" roles="仓管,审单员" name="修改退货仓库"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.receiveback" roles="仓管,审单员" name="收货回传"
                                cross-server-depend="eshoporder.eshoprefund.view" value="true"/>
                    <!--                    <permission key="eshoporder.eshoprefund.updaterefund" roles="仓管,审单员" name="重新下载"-->
                    <!--                                cross-server-depend="eshoporder.eshoprefund.view"/>-->
                    <!--                    <permission key="eshoporder.eshoprefund.refundconfigduty" roles="仓管,审单员" name="设置责任方"-->
                    <!--                                cross-server-depend="eshoporder.eshoprefund.view"/>-->
                    <!--                    <permission key="eshoporder.eshoprefund.updaterefundduty" roles="仓管,审单员" name="修改责任方"-->
                    <!--                                cross-server-depend="eshoporder.eshoprefund.view"/>-->
                    <permission key="eshoporder.eshoprefund.batchupdate" roles="仓管,审单员" name="批量修改"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.copy" roles="审单员" name="复制该字段"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.relation" roles="仓管,审单员" name="快速对应"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <permission key="eshoporder.eshoprefund.contact" roles="仓管,审单员" name="联系买家"
                                cross-server-depend="eshoporder.eshoprefund.view"/>
                    <include-permission include-key="sale.buyer.cryptograph.view"/>
                    <permission key="eshoporder.eshoprefund.buyeredit" roles="仓管,审单员" name="编辑收件人"
                                value="true"/>
                </menu>
                <menu name="收货登记" key="eshoporder.eshopReceiveCheckInPage"
                      cross-server-permission-key="eshoporder.receivecheckin.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      path="sale/jarvis/eshoprefund/EshopRefundReceiveCheckInList.gspx" position="886-4-3"
                      icon="bicon-shouhuodengji">
                    <permission key="eshoporder.receivecheckin.view" roles="仓管,审单员" name="查看"/>
                    <permission key="eshoporder.receivecheckin.add" roles="仓管,审单员" name="新增"/>
                    <!--                    <permission key="eshoporder.receivecheckin.edit" roles="仓管,审单员" name="编辑"/>-->
                    <permission key="eshoporder.receivecheckin.delete" roles="仓管,审单员" name="删除"/>
                    <permission key="eshoporder.receivecheckin.relate" roles="仓管,审单员" name="关联售后单"/>
                    <permission key="eshoporder.receivecheckin.otherstockin" roles="仓管,审单员" name="无信息件处理"/>
                    <permission key="eshoporder.receivecheckin.batchCheckin" roles="仓管,审单员" name="批量验货" value="true"/>
                    <permission key="eshoporder.receivecheckin.print" roles="仓管,审单员" name="打印收货登记"
                                value="true"/>
                    <permission key="eshoporder.receivecheckin.saveandprint" roles="仓管,审单员"
                                name="保存并打印收货登记" value="true"/>
                    <permission key="eshoporder.receivecheckin.export" roles="仓管,审单员" name="导出收货登记"
                                value="true"/>
                    <!--                <permission key="receivecheckin.loss" roles="仓管,审单员" name="商品报损" />-->
                    <!--            <permission key="eshoporder.receivecheckin.config" roles="仓管,审单员" name="收货业务配置"/>-->
                </menu>

            </menu>
            <menu name="自动策略中心" isnew="true" position="886-4.4">
                <menu name="网店订单赠品策略" key="giftRuleStrategy" icon="bicon-zengpinguizecelve"
                      permission-key="jarvis.giftRule.view"
                      path="/sale/jarvis/DeliverBill/Strategy/GiftRuleList.gspx"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      target="iframe" isnew="true" position="886-4.4-1">
                    <permission key="jarvis.giftRule.view" roles="仓管,运营,审单员" name="查看"/>
                    <permission key="jarvis.giftRule.new" roles="运营,审单员" name="新增网店订单赠品策略"/>
                    <permission key="jarvis.giftRule.edit" roles="运营,审单员" name="编辑网店订单赠品策略"/>
                    <permission key="jarvis.giftRule.delete" roles="运营,审单员" name="删除网店订单赠品策略"/>
                    <permission key="jarvis.giftRule.priority" roles="运营,审单员" name="优先级调整"/>
                    <permission key="jarvis.giftRule.group" roles="运营,审单员" name="赠品分类管理"/>
                </menu>
                <menu name="原始订单策略" icon="bicon-dingdanchulicelve" key="eshopOrderStrategy"
                      permission-key="jarvis.strategy.goodsReplace,jarvis.strategy.goodsAnalysis"
                      path="/sale/jarvis/DeliverBill/Strategy/DeliverBillStrategyConfig.gspx?key=eshopOrderStrategy"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      target="iframe" isnew="true" position="886-4.4-2">
                    <permission key="jarvis.strategy.goodsAnalysis" roles="仓管,审单员" name="查看商品解析策略"/>
                    <permission key="jarvis.strategy.goodsAnalysisEdit" roles="仓管,审单员" name="编辑商品解析策略"/>
                </menu>
                <menu name="订单处理策略" icon="bicon-dingdanchulicelve" key="deliverBillStrategy"
                      permission-key="jarvis.strategy.supplier,jarvis.strategy.ktype,jarvis.strategy.split,jarvis.strategy.merge,jarvis.strategy.author,jarvis.strategy.lock,jarvis.strategy.author,jarvis.strategy.audit,jarvis.strategy.lack,jarvis.strategy.mark,jarvis.strategy.mergedCheck,jarvis.strategy.goodsReplace,jarvis.strategy.liveBroadcast"
                      path="/sale/jarvis/DeliverBill/Strategy/DeliverBillStrategyConfig.gspx?key=deliverBillStrategy"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      target="iframe" isnew="true" position="886-4.4-3">
                    <permission key="jarvis.strategy.ktype" roles="仓管,审单员" name="查看仓库策略"/>
                    <permission key="jarvis.strategy.ktypeEdit" roles="仓管,审单员" name="编辑仓库策略" value="true"/>
                    <permission key="jarvis.strategy.split" roles="仓管,审单员" name="查看拆分策略"/>
                    <permission key="jarvis.strategy.splitEdit" roles="仓管,审单员" name="编辑拆分策略" value="true"/>
                    <permission key="jarvis.strategy.merge" roles="仓管,审单员" name="查看合并策略"/>
                    <permission key="jarvis.strategy.mergeEdit" roles="仓管,审单员" name="编辑合并策略" value="true"/>
                    <permission key="jarvis.strategy.author" roles="仓管,审单员" name="查看经手人策略"/>
                    <permission key="jarvis.strategy.authorEdit" roles="仓管,审单员" name="编辑经手人策略"
                                value="true"/>
                    <permission key="jarvis.strategy.lock" roles="仓管,审单员" name="查看截停策略"/>
                    <permission key="jarvis.strategy.lockEdit" roles="仓管,审单员" name="编辑截停策略" value="true"/>
                    <permission key="jarvis.strategy.mark" roles="仓管,审单员" name="查看标记策略"/>
                    <permission key="jarvis.strategy.markEdit" roles="仓管,审单员" name="编辑标记策略" value="true"/>

                    <permission key="jarvis.strategy.supplier" roles="仓管,审单员" name="查看供应商策略"/>
                    <permission key="jarvis.strategy.supplierEdit" roles="仓管,审单员" name="编辑供应商策略"
                                value="true"/>

                    <permission key="jarvis.strategy.audit" roles="仓管,审单员" name="查看审核策略"/>
                    <permission key="jarvis.strategy.auditEdit" roles="仓管,审单员" name="编辑审核策略" value="true"/>
                    <permission key="jarvis.strategy.mergedCheck" roles="仓管,审单员" name="查看合并检查"/>
                    <permission key="jarvis.strategy.mergedCheckEdit" roles="仓管,审单员" name="编辑合并策略"
                                value="true"/>
                    <permission key="jarvis.strategy.goodsReplace" roles="仓管,审单员" name="查看商品替换策略"/>
                    <permission key="jarvis.strategy.goodsReplaceEdit" roles="仓管,审单员" name="编辑商品替换策略"
                                value="true"/>
                    <permission key="jarvis.strategy.liveBroadcast" roles="仓管,审单员" name="查看直播策略"/>
                    <permission key="jarvis.strategy.liveBroadcastEdit" roles="仓管,审单员" name="编辑直播策略"
                                value="true"/>
                    <permission key="jarvis.strategy.consumables" roles="仓管,审单员" name="耗材策略"/>
                </menu>
                <menu name="退货业务策略" icon="bicon-dingdanchulicelve" key="refundStrategy"
                      permission-key="jarvis.strategy.refundAddress"
                      path="/sale/jarvis/DeliverBill/Strategy/DeliverBillStrategyConfig.gspx?key=refundStrategy"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      target="iframe" isnew="true" position="886-4.4-5">
                    <permission key="jarvis.strategy.refundAddress" roles="仓管,审单员" name="查看退件地址策略"/>
                    <permission key="jarvis.strategy.refundAddressEdit" roles="仓管,审单员" name="编辑退件地址策略"
                                value="true"/>
                </menu>

            </menu>
            <menu name="统计报表" path="" isnew="true" position="886-5"
                  icon='bicon-wangdian'>
                <menu name="网店发货统计" icon="bicon-wangdianfahuomingxibiao" key="eshopSendBills"
                      permission-key="jarvis.eshopSendBills.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      path="/sale/jarvis/report/EShopSendBills.gspx"
                      target="iframe" isnew="true" position="886-5-0.1">
                    <permission key="jarvis.eshopSendBills.view" roles="发货员,审单员,打单员,仓管" name="查看"/>
                    <permission key="jarvis.eshopSendBills.export" roles="发货员,审单员,打单员,仓管" name="导出"/>
                </menu>
                <menu name="网店发货明细表" icon="bicon-wangdianfahuomingxibiao" key="eshopSendDetails"
                      permission-key="jarvis.eshopSendDetails.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      path="/sale/jarvis/report/EShopSendDetails.gspx?closeType=closeAll"
                      target="iframe" isnew="true" position="886-5-1">
                    <permission key="jarvis.eshopSendDetails.view" roles="发货员,审单员,打单员,仓管" name="查看"/>
                    <permission key="jarvis.eshopSendDetails.export" roles="发货员,审单员,打单员,仓管" name="导出"/>
                </menu>
                <menu name="网店运营统计" icon="bicon-wangdianyunyingtongji" key="eShopSaleStatistics"
                      permission-key="jarvis.eShopSaleStatistics.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      path="/sale/jarvis/report/EShopSaleStatistics.gspx"
                      target="iframe" isnew="true" position="886-5-2">
                    <permission key="jarvis.eShopSaleStatistics.view" roles="发货员,审单员,打单员,仓管" name="查看"/>
                    <permission key="jarvis.eShopSaleStatistics.export" roles="发货员,审单员,打单员,仓管" name="导出"/>
                </menu>
                <menu name="网店商品销售统计" icon="bicon-wangdianyunyingtongji" key="platformProductSale"
                      permission-key="jarvis.platformProductSale.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      path="/sale/jarvis/report/PlatformProductSaleDetailReport.gspx"
                      target="iframe" isnew="true" position="886-5-3">
                    <permission key="jarvis.platformProductSale.view" roles="发货员,审单员,打单员,仓管" name="查看"/>
                    <permission key="jarvis.platformProductSale.export" roles="发货员,审单员,打单员,仓管" name="导出"/>
                </menu>
                <menu name="平台订单销售统计" icon="bicon-yuandanxiaoshoutongji" key="EshopSaleOrderStatistics"
                      permission-key="eshoporder.EshopSaleOrderStatistics.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      path="sale/analysiscloud/EshopSaleOrderStatistics.gspx" position="886-5-4">
                    <permission key="eshoporder.EshopSaleOrderStatistics.view" roles="运营,财务" name="查看"/>
                    <permission key="eshoporder.EshopSaleOrderStatistics.export" roles="运营,财务" name="导出"
                                depend="EshopSaleOrderStatistics.view"/>
                    <permission key="eshoporder.EshopSaleOrderDetailStatistics.view" roles="运营,财务" name="明细查看"/>
                    <permission key="eshoporder.EshopSaleOrderDetailStatistics.export" roles="运营,财务" name="明细导出"
                                depend="EshopSaleOrderDetailStatistics.view"/>
                </menu>
                <menu name="售后明细统计" key="eshoporder.EShopRefundDetailStatisticsPage"
                      cross-server-permission-key="eshoporder.EShopRefundDetailStatisticsPage.view"
                      path="sale/jarvis/eshoprefund/EShopRefundDetailStatistics.gspx"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      position="886-5-5" icon="bicon-shouhoumingxitongji">
                    <permission key="eshoporder.EShopRefundDetailStatisticsPage.view" roles="运营,财务"
                                name="查看"/>
                    <permission key="eshoporder.EShopRefundDetailStatisticsPage.export" roles="运营,财务"
                                name="导出"/>
                </menu>
                <menu name="售后统计" key="eshoporder.EShopRefundReportPage"
                      cross-server-permission-key="eshoporder.EShopRefundReportPage.view"
                      path="sale/eshoporder/eshoprefund/refundreport/RefundReport.gspx"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      position="886-5-13" icon="bicon-shouhoumingxitongji">
                    <permission key="eshoporder.EShopRefundReportPage.view" roles="运营,财务"
                                name="查看"/>
                </menu>

                <menu name="商品销售渠道统计" icon="bicon-yuandanxiaoshoutongji"
                      key="eshoporder.EshopProductSaleChannelReportPage"
                      cross-server-permission-key="eshoporder.EshopProductSaleChannelReportPage.view"
                      path="/sale/jarvis/report/EshopProductSaleChannelReport.gspx"
                      position="886-5-9">
                    <permission key="eshoporder.EshopProductSaleChannelReportPage.view" roles="运营,财务"
                                name="查看"/>
                    <permission key="eshoporder.EshopProductSaleChannelReportPage.export" roles="运营,财务"
                                name="导出"/>
                    <permission key="eshoporder.EshopProductSaleChannelReportPage.queryPrice" name="查看金额"/>
                </menu>
                <!--首版先不上-->
                <!--                <menu name="商品销售渠道统计" icon="bicon-yuandanxiaoshoutongji"-->
                <!--                      key="eshoporder.EshopProductSaleChannelReportPage"-->
                <!--                      cross-server-permission-key="eshoporder.EshopProductSaleChannelReportPage.view"-->
                <!--                      path="/sale/jarvis/report/EshopProductSaleChannelReport.gspx"-->
                <!--                      position="886-5-9">-->
                <!--                    <permission key="eshoporder.EshopProductSaleChannelReportPage.view" roles="运营,财务"-->
                <!--                                name="查看"/>-->
                <!--                    <permission key="eshoporder.EshopProductSaleChannelReportPage.export" roles="运营,财务"-->
                <!--                                name="导出"/>-->
                <!--                    <permission key="eshoporder.EshopProductSaleChannelReportPage.queryPrice" name="查看金额"/>-->
                <!--                </menu>-->

                <menu name="区域销售统计" icon="bicon-yuandanxiaoshoutongji"
                      key="eshoporder.EshopAreaSaleReportPage"
                      cross-server-permission-key="eshoporder.EshopAreaSaleReportPage.view"
                      path="/sale/jarvis/report/EshopAreaSaleReport.gspx"
                      position="886-5-12">
                    <permission key="eshoporder.EshopAreaSaleReportPage.view" roles="运营,财务"
                                name="查看"/>
                    <permission key="eshoporder.EshopAreaSaleReportPage.queryPrice" name="查看金额"/>

                </menu>
                <menu name="直播订单统计" icon="bicon-wangdianfahuomingxibiao" key="liveBroadcast"
                      permission-key="jarvis.liveBroadcast.view"
                      path="/sale/jarvis/report/LiveBroadcast.gspx"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      target="iframe" isnew="true" position="886-5-10">
                    <permission key="jarvis.liveBroadcast.view" roles="发货员,审单员,打单员,仓管" name="查看"/>
                    <permission key="jarvis.liveBroadcast.export" roles="发货员,审单员,打单员,仓管" name="导出"/>
                    <permission key="jarvis.liveBroadcastdetail.view" roles="发货员,审单员,打单员,仓管"
                                name="明细查看" value="true"/>
                    <permission key="jarvis.liveBroadcastdetail.export" roles="发货员,审单员,打单员,仓管"
                                name="明细导出" value="true"/>
                </menu>
                <menu name="订单处理状态看板" icon="bicon-wangdianyunyingtongji" key="billDataStatistics.view"
                      permission-key="jarvis.billDataStatistics.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      path="/sale/jarvis/DeliverBill/statistics/BillDataStatistics.gspx?caption=订单处理状态看板&amp;closeType=url"
                      target="iframe" isnew="true" position="886-5-11">
                    <permission key="jarvis.billDataStatistics.view" roles="客服,仓管" name="查看"/>
                </menu>

                <menu name="分销毛利统计" icon="bicon-wangdianfahuomingxibiao" key="distributionGrossProfitStatistics"
                      permission-key="jarvis.distributionGrossProfitStatistics.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      path="/sale/jarvis/report/DistributionGrossProfitStatistics.gspx?closeType=closeAll"
                      target="iframe" isnew="true" position="886-5-20">
                    <permission key="jarvis.distributionGrossProfitStatistics.view" roles="审单员,财务" name="查看"/>
                    <permission key="jarvis.distributionGrossProfitStatistics.export" roles="审单员,财务" name="导出"/>
                    <permission key="jarvis.distributionGrossProfitStatisticsDetail.view" roles="审单员,财务"
                                name="查看明细"/>
                    <permission key="jarvis.distributionGrossProfitStatisticsDetail.export" roles="审单员,财务"
                                name="导出明细"/>
                </menu>

                <!--                <menu name="分销毛利统计明细" icon="bicon-wangdianfahuomingxibiao" key="distributionGrossProfitStatisticsDetail"-->
                <!--                      permission-key="jarvis.distributionGrossProfitStatisticsDetail.view"-->
                <!--                      function="EshopFunc||CloudOrderMiddlegroundFunc"-->
                <!--                      path="/sale/jarvis/report/DistributionGrossProfitStatisticsDetail.gspx?closeType=closeAll"-->
                <!--                      target="iframe" isnew="true" position="886-16-1">-->
                <!--                    <permission key="jarvis.distributionGrossProfitStatisticsDetail.view" roles="审单员,财务" name="查看"/>-->
                <!--                    <permission key="jarvis.distributionGrossProfitStatisticsDetail.export" roles="审单员,财务" name="导出"/>-->
                <!--                </menu>-->
            </menu>
            <menu name="直播" path="" isnew="true" position="886-6"
                  icon='bicon-zhibo'>
                <menu name="直播选品" icon="bicon-wangdianyunyingtongji" key="eshoporder.liveBroadcastStockUpRecord"
                      permission-key="eshoporder.liveBroadcastStockUpRecord.view"
                      path="/sale/eshoporder/livebroadcast/LiveBroadcastStockUpRecord.gspx"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      isnew="true" position="886-6-1">
                    <permission key="eshoporder.liveBroadcastStockUpRecord.view" roles="发货员,审单员,打单员,仓管"
                                name="查看"/>
                    <permission key="eshoporder.liveBroadcastStockUpRecord.add" roles="发货员,审单员,打单员,仓管"
                                name="新增"/>
                    <permission key="eshoporder.liveBroadcastStockUpRecord.update" roles="发货员,审单员,打单员,仓管"
                                name="修改"/>
                    <permission key="eshoporder.liveBroadcastStockUpRecord.delete" roles="发货员,审单员,打单员,仓管"
                                name="删除"/>
                </menu>
            </menu>
        </menu>
        <menu name="财务" path="" isnew="true" position="892" icon='bicon-caiwu'>
            <menu name="对账结算" path="" isnew="true" position="892-3"
                  icon="bicon-duizhangjiesuan">
                <menu name="平台原始账单" key="eshoporder.platformCheckAccounts"
                      path="sale/eshoporder/eshopplatformcheck/PlatformCheckAccounts.gspx"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      permission-key="eshoporder.platformCheckAccounts.view"
                      position="892-3-1" icon="bicon-yuanshizhangdan">
                    <permission roles="财务" key="eshoporder.platformCheckAccounts.view" name="查看"/>
                    <permission roles="财务" key="eshoporder.platformCheckAccounts.download" name="下载账单"
                                depend="platformCheckAccounts.view"/>
                    <permission roles="财务" key="eshoporder.platformCheckAccounts.import" name="导入账单"
                                depend="platformCheckAccounts.view"/>
                    <permission roles="财务" key="eshoporder.platformCheckAccounts.balance" name="生成财务单据"
                                depend="platformCheckAccounts.view"/>
                    <permission roles="财务" key="eshoporder.platformCheckAccounts.accountConfig"
                                name="交易项目与科目对应设置"
                                depend="platformCheckAccounts.view"/>
                    <permission roles="财务" key="eshoporder.platformCheckAccounts.delete" name="删除账单"
                                depend="platformCheckAccounts.view"/>
                    <permission roles="财务" key="eshoporder.platformCheckAccounts.audit" name="审核账单"
                                depend="platformCheckAccounts.view"/>
                    <permission roles="财务" key="eshoporder.platformCheckAccounts.export" name="导出账单"
                                depend="platformCheckAccounts.view"/>
                </menu>
                <menu name="订单对账" key="eshoporder.platformFinanceCheck"
                      path="sale/eshoporder/eshopplatformcheck/PlatformFinanceCheck.gspx"
                      permission-key="eshoporder.platformFinanceCheck.view"
                      function="EshopFunc||CloudOrderMiddlegroundFunc"
                      position="892-3-2" icon="bicon-andanduizhang">
                    <permission roles="财务" key="eshoporder.platformFinanceCheck.view" name="查看"/>
                    <permission roles="财务" key="eshoporder.platformCheckAccounts.view" name="原始账单"/>
                    <permission roles="财务" key="eshoporder.platformFinanceCheck.financeCheck" name="手工对账"
                                depend="platformFinanceCheck.view"/>
                    <permission roles="财务" key="eshoporder.platformFinanceCheck.doRefundPay" name="退款支付"
                                depend="platformFinanceCheck.view"/>
                    <permission roles="财务" key="eshoporder.platformFinanceCheck.export" name="导出"
                                depend="platformFinanceCheck.view"/>
                    <permission roles="财务" key="eshoporder.platformFinanceCheck.batchModifyMemo" name="批量备注"
                                depend="platformFinanceCheck.view"/>
                    <permission roles="财务" key="eshoporder.platformFinanceCheck.difffeebalance" name="订单收款"
                                depend="platformFinanceCheck.view"/>
                </menu>

                <!--                <menu name="门店对账" key="eshoporder.platformStoreFinanceCheck"-->
                <!--                      path="sale/eshoporder/eshopplatformcheck/StoreFinanceCheck.gspx"-->
                <!--                      permission-key="eshoporder.platformStoreFinanceCheck.view"-->
                <!--                      position="892-3-5" icon="bicon-andanduizhang">-->
                <!--                    <permission roles="财务" key="eshoporder.platformStoreFinanceCheck.view" name="查看"/>-->
                <!--                    <permission roles="财务" key="eshoporder.platformStoreFinanceCheck.recommendFinanceCheck"-->
                <!--                                name="未对账按金额推荐对账" depend="platformStoreFinanceCheck.view"/>-->
                <!--                    <permission roles="财务" key="eshoporder.platformStoreFinanceCheck.storefinanceCheck"-->
                <!--                                name="手工对账"-->
                <!--                                depend="platformStoreFinanceCheck.view"/>-->
                <!--                    &lt;!&ndash;                    <permission roles="财务" key="eshoporder.platformStoreFinanceCheck.batchModifyMemo" name="批量备注"&ndash;&gt;-->
                <!--                    &lt;!&ndash;                                depend="platformFinanceCheck.view"/>&ndash;&gt;-->
                <!--                </menu>-->

                <menu name="资金流水" key="eshoporder.platformCapitalpaymentFlow"
                      path="sale/eshoporder/eshopplatformcheck/CapitalpaymentFlow.gspx"
                      permission-key="eshoporder.platformCapitalpaymentFlow.view"
                      position="892-3-6" icon="bicon-andanduizhang" function="ServiceIndustryFunc">
                    <permission roles="财务" key="eshoporder.platformCapitalpaymentFlow.view" name="查看"/>
                    <permission roles="财务" key="eshoporder.platformCapitalpaymentFlow.export"
                                name="导入流水" depend="platformCapitalpaymentFlow.view"/>
                    <permission roles="财务" key="eshoporder.platformCapitalpaymentFlow.delete"
                                name="删除流水" depend="platformCapitalpaymentFlow.view"/>
                    <permission roles="财务" key="eshoporder.platformCapitalpaymentFlow.canceled"
                                name="作废流水" depend="platformCapitalpaymentFlow.view"/>
                </menu>
                <!--<menu name="运费结算" icon="bicon-wuliuyunfeijiesuan" key="freightFeeSettle"
                      permission-key="jarvis.freightFeeSettle.view"
                      path="/sale/jarvis/DeliverBill/settleaccounts/FreightFeeSettle.gspx"
                      target="iframe" isnew="true" position="886-3-4">
                    <permission key="jarvis.freightFeeSettle.view" roles="财务" name="查看"/>
                    <permission key="jarvis.freightFeeSettle.import" roles="财务" name="导入对账单"/>
                    <permission key="jarvis.freightFeeSettle.errorSetting" roles="财务" name="异常设置"/>
                    <permission key="jarvis.freightFeeSettle.createBill" roles="财务" name="结算"/>
                    <permission key="jarvis.freightFeeSettle.calculationFee" roles="财务" name="计算预估运费"/>ma
                    <permission key="jarvis.freightFeeSettle.modifyImportFreightFee" roles="财务" name="修改结算运费"/>
                    <permission key="jarvis.freightFeeSettle.export" roles="财务" name="导出"/>
                    <permission key="jarvis.freightFeeSettle.showSettleInfo" roles="财务" name="查看结算明细"/>
                </menu>-->
            </menu>
        </menu>

        <menu name="会员" position="888.8" icon='bicon-tongji'
              function="VipManageFunc||StoreFunc||CloudOrderMiddlegroundFunc">
            <menu name="会员卡" position="888.8-1" icon='bicon-huiyuan'
                  function="VipManageFunc||StoreFunc||CloudOrderMiddlegroundFunc">
                <menu name="会员等级管理" key="sale.member-level-manager"
                      function="VipManageFunc||StoreFunc||CloudOrderMiddlegroundFunc"
                      path="sale/member/pages/vip/level/VipLevelManage.gspx"
                      isnew="true"
                      position="888.8-1-1" icon="bicon-huiyuandengjiguanli" permission-key="member.level.view">
                    <permission key="member.level.view" roles="运营,销售" name="查看"/>
                    <permission key="member.level.add" roles="运营,销售" name="新增" depend="member.level.view"/>
                    <permission key="member.level.edit" roles="运营,销售" name="编辑" depend="member.level.view"/>
                    <permission key="member.level.open" roles="运营,销售" name="启用" depend="member.level.view"/>
                    <permission key="member.level.stop" roles="运营,销售" name="停用" depend="member.level.view"/>
                    <permission key="member.level.assess" roles="运营,销售" name="等级评估周期"
                                depend="member.level.view"/>
                    <permission key="member.level.rule" roles="运营,销售" name="成长值规则" depend="member.level.view"/>
                </menu>
                <menu name="权益卡管理" key="rightsmanager" path="sale/member/pages/rights/RightsCardManager.gspx"
                      isnew="true" function="VipManageFunc||StoreFunc||CloudOrderMiddlegroundFunc"
                      position="888.8-1-2" icon="bicon-quanyikaguanli" permission-key="member.rightsmanager.view">
                    <permission key="member.rightsmanager.view" roles="运营,销售" name="查看"/>
                    <permission key="member.rightsmanager.add" roles="运营,销售" name="新增"
                                depend="member.rightsmanager.view"/>
                    <permission key="member.rightsmanager.edit" roles="运营,销售" name="修改"
                                depend="member.rightsmanager.view"/>
                    <permission key="member.rightsmanager.delete" roles="运营,销售" name="删除"
                                depend="member.rightsmanager.view"/>
                    <permission key="member.rightsmanager.open" roles="运营,销售" name="启用"
                                depend="member.rightsmanager.view"/>
                    <permission key="member.rightsmanager.stop" roles="运营,销售" name="停用"
                                depend="member.rightsmanager.view"/>
                    <permission key="member.rightsmanager.showMember" roles="运营,销售" name="查看成员"
                                depend="member.rightsmanager.view"/>
                    <permission key="member.rightsmanager.member" roles="运营,销售" name="发卡给会员"
                                depend="member.rightsmanager.view"/>
                    <permission key="member.rights.view" roles="运营,销售" name="权益配置查看"/>
                    <permission key="member.rights.add" roles="运营,销售" name="权益配置新增"
                                depend="member.rights.view"/>
                    <permission key="member.rights.edit" roles="运营,销售" name="权益配置修改"
                                depend="member.rights.view"/>
                    <permission key="member.rights.delete" roles="运营,销售" name="权益配置删除"
                                depend="member.rights.view"/>
                    <permission key="member.rights.open" roles="运营,销售" name="权益配置启用"
                                depend="member.rights.view"/>
                    <permission key="member.rights.stop" roles="运营,销售" name="权益配置停用"
                                depend="member.rights.view"/>

                </menu>
                <menu name="优惠券管理" key="cardtemplate" path="sale/member/pages/card/CardTemplate.gspx" isnew="true"
                      position="888.8-1-3" icon="bicon-kaquanmoban" permission-key="member.cardtemplate.view"
                      function="VipManageFunc||StoreFunc||CloudOrderMiddlegroundFunc">
                    <permission key="member.cardtemplate.view" roles="运营,销售" name="查看"/>
                    <permission key="member.cardtemplate.add" roles="运营,销售" name="新增"
                                depend="member.cardtemplate.view"/>
                    <permission key="member.cardtemplate.edit" roles="运营,销售" name="编辑"
                                depend="member.cardtemplate.view"/>
                    <permission key="member.cardtemplate.delete" roles="运营,销售" name="删除"
                                depend="member.cardtemplate.view"/>
                    <permission key="member.cardtemplate.open" roles="运营,销售" name="启用"
                                depend="member.cardtemplate.view"/>
                    <permission key="member.cardtemplate.stop" roles="运营,销售" name="停用"
                                depend="member.cardtemplate.view"/>
                    <permission key="member.cardtemplate.showMember" roles="运营,销售" name="查看成员"
                                depend="member.cardtemplate.view"/>
                    <permission key="member.cardtemplate.creatcard" roles="运营,销售" name="发放优惠券"
                                depend="member.cardtemplate.view"/>
                    <permission key="member.cardtemplate.showRecord" roles="运营,销售" name="查看优惠券发放记录"
                                depend="member.cardtemplate.view" value="true"/>

                </menu>
                <menu name="会员管理" key="member-manager"
                      function="VipManageFunc||StoreFunc||CloudOrderMiddlegroundFunc"
                      path="sale/member/pages/vip/manage/VipManage.gspx?closeType=closeAll" isnew="true"
                      position="888.8-1-4" icon="bicon-huiyuanguanli" permission-key="member.vip.view">

                    <permission key="member.vip.detail" roles="运营,销售" name="会员详情"/>
                    <permission key="member.vip.view" roles="运营,销售" name="查看"/>
                    <permission key="member.vip.add" roles="运营,销售" name="新增" depend="member.vip.view"/>
                    <permission key="member.vip.delete" roles="运营,销售" name="删除" depend="member.vip.view"/>
                    <permission key="member.vip.edit" roles="运营,销售" name="编辑" depend="member.vip.view"/>
                    <permission key="member.vip.giveScore" roles="运营，销售" name="赠送积分" depend="member.vip.view"/>
                    <permission key="member.vip.bindRightsCard" roles="运营，销售" name="发放权益卡"
                                depend="member.vip.view"/>
                    <permission key="member.vip.card.delete" roles="运营，销售" name="解绑权益卡"
                                depend="member.vip.view"/>
                    <permission key="member.vip.card" roles="运营，销售" name="发放优惠券" depend="member.vip.view"/>
                    <permission key="member.vip.coupon.delete" roles="运营，销售" name="解绑优惠券"
                                depend="member.vip.view"/>
                    <!--                    <permission key="member.vip.recharge" roles="运营，销售" name="充值" depend="member.vip.view"/>-->
                    <permission key="member.vip.export" roles="运营，销售" name="会员导出" depend="member.vip.view"/>
                    <permission key="member.vip.import" roles="运营，销售" name="会员导入" depend="member.vip.add"/>
                    <permission key="member.vip.addTags" roles="运营，销售" name="加标签" depend="member.vip.view"/>
                    <permission key="member.vip.rechargeRefund" roles="运营，销售" name="储值退款"
                                depend="member.vip.view"/>
                    <permission key="member.vip.download" roles="运营，销售" name="下载会员" depend="member.vip.view"/>
                    <permission key="member.vip.registerQrCode" roles="运营，销售" name="扫码注册会员"
                                depend="member.vip.view"/>
                    <permission key="member.vip.sendsms" roles="运营，销售" name="短信推送" depend="member.vip.view"/>
                    <permission key="member.vip.payVipRefund" roles="运营，销售" name="付费会员退款"
                                depend="member.vip.view"/>
                    <permission key="member.vip.scoreQuickReason" roles="运营，销售" name="积分调整原因设置" value="true"
                                depend="member.vip.view"/>
                    <permission key="member.vip.viewHistoryConsume" roles="运营，销售" name="查看历史购买记录"
                                depend="member.vip.view"/>
                    <permission key="member.vip.batchRecharge" roles="运营，销售" name="批量会员充值"
                                depend="member.vip.view"/>
                    <permission key="member.rechargeStrategy.editGiveMoney" roles="运营,销售" name="编辑赠送金额"/>
                    <permission key="member.vip.modifyLevel" roles="运营，销售" name="修改会员等级"
                                depend="member.vip.view"/>
                </menu>

                <menu name="会员积分策略" key="pointsConfiguration"
                      function="VipManageFunc||StoreFunc||CloudOrderMiddlegroundFunc"
                      path="sale/member/pages/vip/points/PointsConfiguration.gspx" isnew="true"
                      position="888.8-1-5" icon="bicon-huiyuanguanli" permission-key="member.pointsConfiguration.view">
                    <permission key="member.pointsConfiguration.view" roles="运营,销售" name="查看"/>
                    <permission key="member.pointsConfiguration.edit" roles="运营,销售" name="编辑"
                                depend="member.pointsConfiguration.view"/>
                </menu>
                <menu name="会员储值策略" key="rechargeStrategy"
                      path="sale/member/pages/recharge/RechargeStrategy.gspx"
                      permission-key="member.rechargeStrategy.view"
                      isnew="true" function="VipManageFunc||StoreFunc||CloudOrderMiddlegroundFunc"
                      position="888.8-1-6" icon="bicon-chuzhizengsongguize">
                    <permission key="member.rechargeStrategy.view" roles="运营,销售" name="查看"/>
                    <permission key="member.rechargeStrategy.add" roles="运营,销售" name="新增"
                                depend="member.rightsmanager.view"/>
                    <permission key="member.rechargeStrategy.edit" roles="运营,销售" name="编辑"
                                depend="member.rightsmanager.view"/>
                    <permission key="member.rechargeStrategy.open" roles="运营,销售" name="启用/停用"
                                depend="member.rightsmanager.view"/>
                </menu>

                <menu name="微信会员" key="weixin-member"
                      function="VipManageFunc||StoreFunc||CloudOrderMiddlegroundFunc"
                      path="sale/member/pages/wx/WeixinMember.gspx" isnew="true"
                      position="888.8-1-7" icon="bicon-weixinhuiyuan" permission-key="weixin.member.view">
                    <permission key="weixin.member.view" roles="运营,销售" name="查看"/>
                    <permission key="weixin.auth.view" roles="运营,销售" name="公众号授权查看"
                                depend="weixin.member.view"/>
                    <permission key="weixin.auth.authorization" roles="运营,销售" name="公众号授权/解绑"
                                depend="weixin.auth.view"/>
                    <permission key="weixin.member.addTemplate" roles="运营,销售" name="新增会员卡"
                                depend="weixin.auth.view"/>
                    <permission key="weixin.member.editTemplate" roles="运营,销售" name="修改"
                                depend="weixin.auth.view"/>
                    <permission key="weixin.member.deleteTemplate" roles="运营,销售" name="删除"
                                depend="weixin.auth.view"/>
                    <permission key="weixin.member.sendCard" roles="运营,销售" name="发卡"
                                depend="weixin.auth.view"/>
                    <permission key="weixin.member.refreshAuditState" roles="运营,销售" name="刷新审核状态"
                                depend="weixin.auth.view"/>
                    <permission key="weixin.member.wxSync" roles="运营,销售" name="自动同步设置"
                                depend="weixin.auth.view"/>
                </menu>
            </menu>
            <menu name="会员统计分析" position="888.8-2" icon='bicon-tongji'
                  function="StoreFunc">
                <menu name="会员储值记录" key="sale.member-recharge-record"
                      function="StoreFunc"
                      path="sale/member/pages/recharge/RechargeRecord.gspx"
                      isnew="true"
                      position="888.8-2-1" icon="bicon-huiyuanchuzhijilu" permission-key="shopsale.rechargeRecord.view">
                    <permission key="shopsale.rechargeRecord.view" roles="运营,销售" name="查看"/>
                    <permission key="shopsale.rechargeRecord.export" roles="运营,销售" name="导出" depend="shopsale.rechargeRecord.view"/>
                </menu>
                <menu name="会员储值变动统计" key="StoredValueChangeRecord"
                      function="StoreFunc"
                      path="sale/member/pages/vip/statistics/StoredValueChangeRecord.gspx"
                      isnew="true"
                      position="888.8-2-2" icon="bicon-huiyuanchuzhibiandongtongji"
                      permission-key="member.storedValueChangeRecord.view">
                    <permission key="member.storedValueChangeRecord.view" roles="运营,销售" name="查看"/>
                    <permission key="member.storedValueChangeRecord.export" roles="运营,销售" name="导出" depend="member.storedValueChangeRecord.view"/>
                </menu>
                <menu name="会员积分变动统计" key="ScoreChangeRecord"
                      function="StoreFunc"
                      path="sale/member/pages/vip/statistics/ScoreChangeRecord.gspx"
                      isnew="true"
                      position="888.8-2-3" icon="bicon-huiyuanjifenbiandongtongji"
                      permission-key="member.scoreChangeRecord.view">
                    <permission key="member.scoreChangeRecord.view" roles="运营,销售" name="查看"/>
                    <permission key="member.scoreChangeRecord.export" roles="运营,销售" name="导出" depend="member.scoreChangeRecord.view"/>
                </menu>
                <menu name="会员积分分析" key="ScoreAnalysisRecord"
                      function="StoreFunc"
                      path="sale/member/pages/vip/statistics/scoreAnalysis/ScoreAnalysisRecord.gspx"
                      isnew="true"
                      position="888.8-2-4" icon="bicon-huiyuanjifenfenxi"
                      permission-key="member.scoreAnalysisRecord.view">
                    <permission key="member.scoreAnalysisRecord.view" roles="运营,销售" name="查看"/>
                </menu>
                <menu name="会员储值分析" key="StoredValueAnalysisRecord"
                      function="StoreFunc"
                      path="sale/member/pages/vip/statistics/storedValueAnalysis/StoredValueAnalysisRecord.gspx"
                      isnew="true"
                      position="888.8-2-5" icon="bicon-huiyuanchuzhijilu"
                      permission-key="member.storedValueAnalysisRecord.view">
                    <permission key="member.storedValueAnalysisRecord.view" roles="运营,销售" name="查看"/>
                </menu>
            </menu>
        </menu>

        <menu name="门店" position="887" icon='bicon-tongji' function="StoreFunc">
            <menu name="售价管理" position="887-0.6" icon="bicon-dianpujiageben" function="StoreFunc">
                <menu name="促销策略" key="shopsale.promotion"
                      path="sale/shopsale/pages/promotion/PromotionList.gspx?PromotionUseRange=retail"
                      isnew="true" function="StoreFunc"
                      position="887-0.6-1" permission-key="shopsale.promotion.view"
                      icon="bicon-quanqudaomendianduiying">
                    <permission key="shopsale.promotion.view" roles="运营,销售" name="查看"/>
                    <permission key="shopsale.promotion.add" roles="运营,销售" name="新增"
                                depend="shopsale.promotion.view"/>
                    <permission key="shopsale.promotion.edit" roles="运营,销售" name="修改"
                                depend="shopsale.promotion.view"/>
                    <permission key="shopsale.promotion.delete" roles="运营,销售" name="删除"
                                depend="shopsale.promotion.view"/>
                    <permission key="shopsale.promotion.open" roles="运营,销售" name="启用"
                                depend="shopsale.promotion.view"/>
                    <permission key="shopsale.promotion.stop" roles="运营,销售" name="停用"
                                depend="shopsale.promotion.view"/>
                    <permission key="shopsale.promotion.priority" roles="运营,销售" name="促销优先级"
                                depend="shopsale.promotion.view"/>
                    <permission key="shopsale.promotion.automation" roles="运营,销售" name="促销配置"
                                depend="shopsale.promotion.view"/>
                </menu>
                <!--                <menu name="云订货促销管理" key="shopsale.promotionCloud"-->
                <!--                      path="sale/shopsale/pages/promotion/PromotionList.gspx?PromotionUseRange=cloudOrdering"-->
                <!--                      isnew="true"-->
                <!--                      position="887-0.6-2" permission-key="shopsale.promotionCloud.view"-->
                <!--                      icon="bicon-quanqudaomendianduiying">-->
                <!--                    <permission key="shopsale.promotionCloud.view" roles="运营,销售" name="查看"/>-->
                <!--                    <permission key="shopsale.promotionCloud.add" roles="运营,销售" name="新增"-->
                <!--                                depend="shopsale.promotionCloud.view"/>-->
                <!--                    <permission key="shopsale.promotionCloud.edit" roles="运营,销售" name="修改"-->
                <!--                                depend="shopsale.promotionCloud.view"/>-->
                <!--                    <permission key="shopsale.promotionCloud.delete" roles="运营,销售" name="删除"-->
                <!--                                depend="shopsale.promotionCloud.view"/>-->
                <!--                    <permission key="shopsale.promotionCloud.open" roles="运营,销售" name="启用"-->
                <!--                                depend="shopsale.promotionCloud.view"/>-->
                <!--                    <permission key="shopsale.promotionCloud.stop" roles="运营,销售" name="停用"-->
                <!--                                depend="shopsale.promotionCloud.view"/>-->
                <!--                    <permission key="shopsale.promotionCloud.priority" roles="运营,销售" name="促销优先级"-->
                <!--                                depend="shopsale.promotionCloud.view"/>-->
                <!--                    <permission key="shopsale.promotionCloud.automation" roles="运营,销售" name="促销配置"-->
                <!--                                depend="shopsale.promotionCloud.view"/>-->
                <!--                </menu>-->

            </menu>
            <menu name="销售" position="888" icon="bicon-xiaoshou"/>
            <menu name="销售价格" position="888-1"/>
            <menu name="批发促销管理" key="shopsale.promotionWholesale"
                  path="sale/shopsale/pages/promotion/PromotionWholesaleList.gspx"
                  isnew="false" function="WholeSalePromotionFunc"
                  position="888-1-3" permission-key="shopsale.promotion.view"
                  icon="bicon-quanqudaomendianduiying">
                <permission key="shopsale.promotion.view" roles="运营,销售" name="查看"/>
                <permission key="shopsale.promotion.add" roles="运营,销售" name="新增"
                            depend="shopsale.promotion.view"/>
                <permission key="shopsale.promotion.edit" roles="运营,销售" name="修改"
                            depend="shopsale.promotion.view"/>
                <permission key="shopsale.promotion.delete" roles="运营,销售" name="删除"
                            depend="shopsale.promotion.view"/>
                <permission key="shopsale.promotion.open" roles="运营,销售" name="启用"
                            depend="shopsale.promotion.view"/>
                <permission key="shopsale.promotion.stop" roles="运营,销售" name="停用"
                            depend="shopsale.promotion.view"/>
                <permission key="shopsale.promotion.priority" roles="运营,销售" name="促销优先级"
                            depend="shopsale.promotion.view"/>
                <permission key="shopsale.promotion.automation" roles="运营,销售" name="促销配置"
                            depend="shopsale.promotion.view"/>
            </menu>

            <menu name="门店统计" position="887-2" icon='bicon-tongji' function="StoreFunc">
                <menu name="钱箱存取记录" key="shopsale.cashboxrecord"
                      path="sale/shopsale/pages/cashbox/CashBoxRecord.gspx"
                      isnew="true" function="StoreFunc"
                      position="887-2-1" permission-key="shopsale.cashboxrecord.view"
                      icon="bicon-qianxiangcunqujilu">
                    <permission key="shopsale.cashboxrecord.view" name="查看"/>
                    <permission key="shopsale.cashboxrecord.export" name="导出" depend="shopsale.cashboxrecord.view"/>
                </menu>
                <menu name="交接班记录" key="shopsale.shiftchangesrecord"
                      path="sale/shopsale/pages/shiftchange/ShiftChangesRecord.gspx"
                      isnew="true" function="StoreFunc"
                      position="887-2-2" permission-key="shopsale.shiftchangesrecord.view"
                      icon="bicon-xiaoshoukaipiaodanjuchaxun">
                    <permission key="shopsale.shiftchangesrecord.view" name="查看"/>
                    <permission key="shopsale.shiftchangesrecord.export" name="导出"
                                depend="shopsale.shiftchangesrecord.view"/>
                </menu>
                <menu name="门店单据查询" key="shopsale.saleorderquery"
                      path="sale/shopsale/pages/saleorder/SaleOrderQueryRetail.gspx"
                      isnew="true" function="StoreFunc &amp;&amp; !CumsterFunc"
                      position="887-2-3" permission-key="shopsale.saleorderquery.view"
                      icon="bicon-mendiandanjuchaxun">
                    <permission key="shopsale.saleorderquery.view" name="查看"/>
                    <permission key="shopsale.saleorderquery.edit" name="修改明细" value="true"/>
                    <permission key="shopsale.saleorderquery.balance" name="批量结存"/>
                    <permission key="shopsale.saleorderquery.export" name="导出"
                                depend="shopsale.saleorderquery.view"/>
                    <permission key="shopsale.saleorderquery.delete" name="删除单据"/>
                    <permission key="shopsale.saleorderquery.paystate" name="修改支付状态"/>
                    <permission key="shopsale.saleorderquery.modifyBill" name="修改单据信息"/>
                </menu>

                <menu name="直营门店单据查询" key="shopsale.saleorderqueryretail"
                      path="sale/shopsale/pages/saleorder/SaleOrderQuery.gspx?SaleOrderType=DirectSales"
                      isnew="true" function="CumsterFunc"
                      position="887-2-6" permission-key="shopsale.saleorderqueryretail.view"
                      icon="bicon-xiaoshoukaipiaodanjuchaxun">
                    <permission key="shopsale.saleorderqueryretail.view" name="查看"/>
                    <permission key="shopsale.saleorderqueryretail.superView" name="查看全部"/>
                    <permission key="shopsale.saleorderqueryretail.edit" name="修改明细"/>
                    <permission key="shopsale.saleorderqueryretail.editOrder" name="修改营业员"/>
                    <permission key="shopsale.saleorderqueryretail.balance" name="提交核算"/>
                    <permission key="shopsale.saleorderqueryretail.export" name="导出"
                                depend="shopsale.saleorderqueryretail.view"/>
                    <permission key="shopsale.saleorderqueryretail.confirmpay" name="确认收款"/>
                    <permission key="shopsale.saleorderqueryretail.delete" name="删除单据"/>
                    <permission key="shopsale.saleorderqueryretail.viewpayinfo" name="查看账单流水"/>
                    <permission key="shopsale.saleorderqueryretail.checking" name="无需对账"/>
                    <permission key="shopsale.saleorderqueryretail.cancelChecking" name="取消无需对账"/>
                    <permission key="shopsale.saleorderqueryretail.editpay" name="填写支付信息"/>
                    <permission key="shopsale.saleorderqueryretail.editpayway" name="编辑支付方式"/>
                    <permission key="shopsale.saleorderqueryretail.editoutno" name="编辑支付流水号"/>
                    <permission key="shopsale.saleorderqueryretail.editmemo" name="编辑附加说明"/>
                    <permission key="shopsale.saleorderqueryretail.editstarttime" name="编辑服务开始时间"/>
                    <permission key="shopsale.saleorderqueryretail.editendtime" name="编辑服务结束时间"/>
                    <permission key="shopsale.saleorderqueryretail.currencydisedtaxedtotal" name="编辑现价"/>
                    <permission key="shopsale.saleorderqueryretail.editcustomsalestage" name="编辑销售类型"/>
                    <permission key="shopsale.saleorderqueryretail.upLoadFile" name="上传附件"/>
                    <permission key="shopsale.saleorderqueryretail.deleteFile" name="删除附件"/>
                </menu>


                <menu name="门店营业占比" key="shopsale.businessPercentage"
                      path="sale/shopsale/pages/shopreport/BusinessPercentage.gspx"
                      isnew="true" function="StoreFunc"
                      position="887-2-4" permission-key="shopsale.businessPercentage.view"
                      icon="bicon-mendianyingyezhanbi">
                    <permission key="shopsale.businessPercentage.view" name="查看"/>
                    <permission key="shopsale.businessPercentage.export" name="导出"
                                depend="shopsale.businessPercentage.view"/>
                </menu>
                <menu name="门店收入统计" key="shopsale.incomeStatistics"
                      path="sale/member/pages/statistics/StoreIncomeStatistics.gspx"
                      isnew="true" function="StoreFunc"
                      position="887-2-7" permission-key="shopsale.incomeStatistics.view"
                      icon="bicon-mendianshourutongji">
                    <permission key="shopsale.incomeStatistics.view" name="查看"/>
                    <permission key="shopsale.incomeStatistics.export" name="导出"
                                depend="shopsale.incomeStatistics.view"/>
                </menu>

                <menu name="渠道门店单据查询" key="shopsale.saleorderquerydistributors"
                      path="sale/shopsale/pages/saleorder/SaleOrderQuery.gspx?SaleOrderType=Distributors"
                      isnew="true" function="ServiceIndustryFunc"
                      position="887-2-5" permission-key="shopsale.saleorderquerydistributors.view"
                      icon="bicon-xiaoshoukaipiaodanjuchaxun">
                    <permission key="shopsale.saleorderquerydistributors.view" name="查看"/>
                    <permission key="shopsale.saleorderquerydistributors.superView" name="查看全部"/>
                    <permission key="shopsale.saleorderquerydistributors.edit" name="修改明细"/>
                    <permission key="shopsale.saleorderquerydistributors.editOrder" name="修改营业员"/>
                    <permission key="shopsale.saleorderquerydistributors.balance" name="提交核算"/>
                    <permission key="shopsale.saleorderquerydistributors.export" name="导出"
                                depend="shopsale.saleorderquerydistributors.view"/>
                    <permission key="shopsale.saleorderquerydistributors.confirmpay" name="确认收款"/>
                    <permission key="shopsale.saleorderquerydistributors.delete" name="删除单据"/>
                    <permission key="shopsale.saleorderquerydistributors.viewpayinfo" name="查看账单流水"/>
                    <permission key="shopsale.saleorderquerydistributors.checking" name="无需对账"/>
                    <permission key="shopsale.saleorderquerydistributors.cancelChecking" name="取消无需对账"/>
                    <permission key="shopsale.saleorderquerydistributors.editpay" name="填写支付信息"/>
                    <permission key="shopsale.saleorderquerydistributors.editpayway" name="编辑支付方式"/>
                    <permission key="shopsale.saleorderquerydistributors.editoutno" name="编辑支付流水号"/>
                    <permission key="shopsale.saleorderquerydistributors.commission" name="修改分佣比例"/>
                    <permission key="shopsale.saleorderquerydistributors.editmemo" name="编辑附加说明"/>
                    <permission key="shopsale.saleorderquerydistributors.editcustomsalestage" name="编辑销售类型"/>
                    <permission key="shopsale.saleorderquerydistributors.invoice" name="批量提交开票"/>
                    <permission key="shopsale.saleorderquerydistributors.upLoadFile" name="上传附件"/>
                    <permission key="shopsale.saleorderquerydistributors.deleteFile" name="删除附件"/>
                </menu>
            </menu>


            <menu name="钱箱" position="887-900" icon='bicon-tongji' function="StoreFunc">
                <menu name="钱箱" key="shopsale.cashbox" function="StoreFunc"
                      isnew="true" path="sale/shopsale/pages/cashbox/CashBoxRecord.gspx" hidden="true"
                      position="887-900-1" permission-key="shopsale.cashbox.open"
                      icon="bicon-xiaoshoukaipiaodanjuchaxun">
                    <permission key="shopsale.cashbox.open" name="打开钱箱"/>
                </menu>
            </menu>
            <menu name="交接班" position="887-991" icon='bicon-tongji'>
                <menu name="交接班" key="shopsale.shiftchanges" function="StoreFunc"
                      isnew="true" path="sale/shopsale/pages/cashbox/CashBoxRecord.gspx" hidden="true"
                      position="887-991-1" permission-key="shopsale.shiftchanges.view"
                      icon="bicon-xiaoshoukaipiaodanjuchaxun">
                    <permission key="shopsale.shiftchanges.view" name="查看"/>
                    <permission key="shopsale.shiftchanges.saledetail" name="查看销售额明细"/>
                    <permission key="shopsale.shiftchanges.print" name="交接时打印小票"/>
                    <permission key="shopsale.shiftchanges.out" name="交接并登出"/>
                </menu>
            </menu>
            <menu name="POS配置" position="887-992" icon='bicon-tongji' function="StoreFunc">
                <menu name="要货申请" key="shopsale.transferOrder" function="StoreFunc"
                      isnew="true" path="sale/shopsale/pages/cashbox/CashBoxRecord.gspx" hidden="true"
                      position="887-992-1" permission-key="shopsale.transferOrder.view"
                      icon="bicon-xiaoshoukaipiaodanjuchaxun">
                    <permission key="shopsale.transferOrder.view" name="查看"/>
                    <permission key="shopsale.transferOrder.create" name="新增"/>
                    <permission key="shopsale.transferOrder.edit" name="编辑"/>
                    <permission key="shopsale.transferOrder.confirm" name="提交"/>
                    <permission key="shopsale.transferOrder.send" name="发货"/>
                    <permission key="shopsale.transferOrder.delete" name="删除"/>
                    <permission key="shopsale.transferOrder.print" name="打印"/>
                </menu>
                <menu name="调货管理" key="shopsale.tenderManage" function="StoreFunc"
                      isnew="true" path="sale/shopsale/pages/cashbox/CashBoxRecord.gspx" hidden="true"
                      position="887-992-2" permission-key="shopsale.tenderManage.view"
                      icon="bicon-xiaoshoukaipiaodanjuchaxun">
                    <permission key="shopsale.tenderManage.view" name="查看"/>
                    <permission key="shopsale.tenderManage.confirm" name="确认入库"/>
                    <permission key="shopsale.tenderManage.autoPrint" name="确认后自动打印单据"/>
                    <permission key="shopsale.tenderManage.columnConfig" name="列配置"/>
                    <permission key="shopsale.tenderManage.print" name="打印"/>
                </menu>
                <menu name="通用设置" key="shopsale.setting" function="StoreFunc"
                      isnew="true" path="sale/shopsale/pages/cashbox/CashBoxRecord.gspx" hidden="true"
                      position="887-992-3" permission-key="shopsale.setting.edit"
                      icon="bicon-xiaoshoukaipiaodanjuchaxun">
                    <permission key="shopsale.setting.edit" name="编辑"/>
                    <permission key="shopsale.hotkey.edit" name="快捷键设置" value="true"/>
                </menu>
                <menu name="打印设置" key="shopsale.print" function="StoreFunc"
                      isnew="true" path="sale/shopsale/pages/cashbox/CashBoxRecord.gspx" hidden="true"
                      position="887-992-4" permission-key="shopsale.print.edit" icon="bicon-xiaoshoukaipiaodanjuchaxun">
                    <permission key="shopsale.print.edit" name="编辑"/>
                    <permission key="shopsale.print.patchworknote" name="补打小票" roles="销售"/>
                </menu>
                <menu name="副屏设置" key="shopsale.screen" function="StoreFunc"
                      isnew="true" path="sale/shopsale/pages/cashbox/CashBoxRecord.gspx" hidden="true"
                      position="887-992-5" permission-key="shopsale.screen.edit"
                      icon="bicon-xiaoshoukaipiaodanjuchaxun">
                    <permission key="shopsale.screen.edit" name="编辑"/>
                </menu>
                <menu name="打印模版配置" key="shopsale.printmodel" function="StoreFunc"
                      isnew="true" path="sale/shopsale/pages/cashbox/CashBoxRecord.gspx" hidden="true"
                      position="887-992-6" permission-key="shopsale.printmodel.edit"
                      icon="bicon-xiaoshoukaipiaodanjuchaxun">
                    <permission key="shopsale.printmodel.edit" name="编辑"/>
                </menu>
                <menu name="开单设置" key="shopsale.salesetting" function="StoreFunc"
                      isnew="true" path="sale/shopsale/pages/cashbox/CashBoxRecord.gspx" hidden="true"
                      position="887-992-7" permission-key="shopsale.salesetting.editDetailDiscount"
                      icon="bicon-xiaoshoukaipiaodanjuchaxun">
                    <permission key="shopsale.salesetting.editDetailDiscount" name="编辑明细折扣" value="true"/>
                    <permission key="shopsale.salesetting.editDetailPrice" name="编辑明细售价" value="true"/>
                    <permission key="shopsale.salesetting.editOrderPreferential" name="总额优惠" value="true"/>
                    <permission key="shopsale.salesetting.editGift" name="设置赠品" value="true"/>
                    <permission key="shopsale.salesetting.saveGraft" name="挂单" value="true"/>
                    <permission key="shopsale.salesetting.getGraft" name="取单" value="true"/>
                    <permission key="shopsale.salesetting.billDiscount" name="整单折扣" value="true"/>
                    <permission key="shopsale.salesetting.editRetailPrice" name="修改零售价" value="true"/>
                    <permission key="shopsale.salesetting.backGoods" name="按商品退货" value="true"/>
                    <permission key="shopsale.salesetting.viewBatchCost" name="查看批次成本"/>
                </menu>
                <menu name="储值记录" key="shopsale.rechargeRecord" function="StoreFunc" isnew="true" hidden="true"
                      position="887-992-8" path="sale/shopsale/pages/cashbox/CashBoxRecord.gspx"
                      permission-key="shopsale.rechargeRecord.view" icon="bicon-xiaoshoukaipiaodanjuchaxun">
                    <permission key="shopsale.rechargeRecord.view" name="查看"/>
                    <permission key="shopsale.rechargeRecord.invalidate" name="作废" value="true"/>
                </menu>
                <menu name="会员储值" key="shopsale.recharge" function="StoreFunc"
                      isnew="true" path="sale/shopsale/pages/cashbox/CashBoxRecord.gspx" hidden="true"
                      position="887-992-9" permission-key="shopsale.recharge.view"
                      icon="bicon-xiaoshoukaipiaodanjuchaxun">
                    <permission key="shopsale.recharge.view" name="会员充值" value="true"/>
                    <permission key="shopsale.recharge.manual" name="手工充值" value="true"/>
                    <permission key="shopsale.recharge.gift" name="充值赠送" value="true"
                                depend="shopsale.recharge.manual"/>
                </menu>
            </menu>
        </menu>
        <menu name="销售" position="888">
            <menu name="销售任务(即将下线)" path="" position="888-1.5">
                <menu name="销售时段划分" key="eshoporder.SalesPeriodList"
                      function="SaleTaskFunc"
                      path="sale/eshoporder/sales/PeriodList.gspx" icon="bicon-xiaoshoushiduan"
                      permission-key="eshoporder.SalesPeriodList.view"
                      position="888-1.5-1">
                    <permission key="eshoporder.SalesPeriodList.view" name="查看"/>
                    <permission key="eshoporder.SalesPeriodList.edit" name="修改"/>
                    <permission key="eshoporder.SalesPeriodList.delete" name="删除"/>
                    <!--                    <permission key="eshoporder.SalesPeriodList.export" name="导出"/>-->
                    <permission key="eshoporder.SalesPeriodList.add" name="新增"/>
                    <permission key="eshoporder.SalesPeriodList.open" name="启用"/>
                    <permission key="eshoporder.SalesPeriodList.close" name="停用"/>
                </menu>
                <menu name="销售任务管理(即将下线)" key="eshoporder.SalesTaskList"
                      function="SaleTaskFunc"
                      path="sale/eshoporder/sales/TaskList.gspx" icon="bicon-xiaoshourenwuguanli"
                      permission-key="eshoporder.SalesTaskList.view"
                      position="888-1.5-2">
                    <permission key="eshoporder.SalesTaskList.view" name="查看"/>
                    <permission key="eshoporder.SalesTaskList.add" name="新增任务"/>
                    <permission key="eshoporder.SalesTaskList.queryCondition" name="任务状况查询"/>
                    <permission key="eshoporder.SalesTaskList.update" name="修改任务"/>
                    <permission key="eshoporder.SalesTaskList.delete" name="删除任务"/>
                    <!--                    <permission key="eshoporder.SalesTaskList.export" name="导出任务"/>-->
                </menu>
            </menu>
        </menu>

        <menu name="库存" position="891" icon="bicon-kucun">
            <menu name="库存查询" position="891-8">
                <menu name="可销售库存报表" icon="bicon-kucunzhuangkuangchaxun" key="analysiscloud.saleStockReport"
                      permission-key="analysiscloud.saleStockReport.view"
                      path="sale/eshoporder/stock/SaleStockReport.gspx"
                      position="891-8-1.1">
                    <permission key="analysiscloud.saleStockReport.view" name="查看"/>
                    <permission key="analysiscloud.saleStockReport.export" name="导出"
                                depend="analysiscloud.saleStockReport.view"/>
                    <permission key="analysiscloud.saleStockReport.print" name="打印"
                                depend="analysiscloud.saleStockReport.view"/>
                    <permission key="analysiscloud.saleStockReport.ruleConfig" name="可销售库存公式设置"
                                depend="analysiscloud.saleStockReport.view"/>
                </menu>

                <menu name="可发货库存报表" icon="bicon-kucunzhuangkuangchaxun" key="analysiscloud.sendStockReport"
                      permission-key="analysiscloud.sendStockReport.view"
                      path="sale/eshoporder/stock/SendStockReport.gspx"
                      position="891-8-1.2" function="SendQtyFunc || EshopFunc">
                    <permission key="analysiscloud.sendStockReport.view" name="查看"/>
                    <permission key="analysiscloud.sendStockReport.export" name="导出"
                                depend="analysiscloud.sendStockReport.view"/>
                    <permission key="analysiscloud.sendStockReport.print" name="打印"
                                depend="analysiscloud.sendStockReport.view"/>
                </menu>
            </menu>
        </menu>

        <menu name="目标" isnew="true" position="887.8"
              icon="bicon-mubiao2">
            <menu name="目标设置" isnew="true" position="887.8-1">
                <menu name="销售目标" key="eshoporder.TaskListNew"
                      function="SaleTaskFunc"
                      path="sale/eshoporder/tasknew/TaskListNew.gspx" icon="bicon-xiaoshoumubiaoxiankuang"
                      permission-key="eshoporder.TaskListNew.view"
                      position="887.8-1-1">
                    <permission key="eshoporder.TaskListNew.view" name="查看"/>
                    <permission key="eshoporder.TaskListNew.add" name="新增"/>
                    <permission key="eshoporder.TaskListNew.edit" name="修改"/>
                    <permission key="eshoporder.TaskListNew.delete" name="删除"/>
                    <permission key="eshoporder.TaskListNew.open" name="启用"/>
                    <permission key="eshoporder.TaskListNew.close" name="终止"/>
                    <permission key="eshoporder.TaskListNew.copy" name="复制"/>
                    <permission key="eshoporder.TaskListNew.exec" name="目标执行情况"/>
                </menu>
            </menu>
            <menu name="统计报表" path="" isnew="true" position="887.8-2">
                <menu name="公司销售目标" key="eshoporder.TaskListNew.report1"
                      function="SaleTaskFunc"
                      path="sale/eshoporder/tasknew/report/TaskAllReport.gspx?taskClazz=COMPANY_SALE_TASK"
                      icon="bicon-gongsixiaoshoumubiao"
                      permission-key="eshoporder.TaskListNew.reportCompanyView"
                      position="887.8-2-1">
                    <permission key="eshoporder.TaskListNew.reportCompanyView" name="公司销售目标查看"/>
                </menu>
                <menu name="部门销售目标" key="eshoporder.TaskListNew.report2"
                      function="SaleTaskFunc"
                      path="sale/eshoporder/tasknew/report/TaskAllReport.gspx?taskClazz=DEPARTMENT_SALE_TASK"
                      icon="bicon-bumenxiaoshoumubiao"
                      permission-key="eshoporder.TaskListNew.reportDepartmentView"
                      position="887.8-2-2">
                    <permission key="eshoporder.TaskListNew.reportDepartmentView" name="部门销售目标查看"/>

                </menu>
                <menu name="品牌销售目标" key="eshoporder.TaskListNew.report3"
                      function="SaleTaskFunc"
                      path="sale/eshoporder/tasknew/report/TaskAllReport.gspx?taskClazz=BRAND_SALE_TASK"
                      icon="bicon-pinpaixiaoshoumubiao"
                      permission-key="eshoporder.TaskListNew.reportBrandView"
                      position="887.8-2-3">
                    <permission key="eshoporder.TaskListNew.reportBrandView" name="品牌销售目标查看"/>

                </menu>
                <menu name="商品销售目标" key="eshoporder.TaskListNew.report4"
                      function="SaleTaskFunc"
                      path="sale/eshoporder/tasknew/report/TaskAllReport.gspx?taskClazz=PTYPE_SALE_TASK"
                      icon="bicon-shangpinxiaoshoumubiao"
                      permission-key="eshoporder.TaskListNew.reportPtypeView"
                      position="887.8-2-4">
                    <permission key="eshoporder.TaskListNew.reportPtypeView" name="商品销售目标查看"/>

                </menu>
                <menu name="客户销售目标" key="eshoporder.TaskListNew.report5"
                      function="SaleTaskFunc"
                      path="sale/eshoporder/tasknew/report/TaskAllReport.gspx?taskClazz=BTYPE_SALE_TASK"
                      icon="bicon-kehuxiaoshoumubiao"
                      permission-key="eshoporder.TaskListNew.reportBtypeView"
                      position="887.8-2-5">
                    <permission key="eshoporder.TaskListNew.reportBtypeView" name="客户销售目标查看"/>

                </menu>
                <menu name="店铺销售目标" key="eshoporder.TaskListNew.report6"
                      function="SaleTaskFunc"
                      path="sale/eshoporder/tasknew/report/TaskAllReport.gspx?taskClazz=OTYPE_SALE_TASK"
                      icon="bicon-dianpuxiaoshoumubiao"
                      permission-key="eshoporder.TaskListNew.reportOtypeView"
                      position="887.8-2-6">
                    <permission key="eshoporder.TaskListNew.reportOtypeView" name="店铺销售目标查看"/>

                </menu>

            </menu>
        </menu>
    </menus>
</root>

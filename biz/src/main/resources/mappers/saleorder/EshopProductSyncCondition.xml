<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopProductSyncConditionMapper">
    <insert id="insertAutoDownloadProductTask">
        insert into pl_eshop_product_sync_condition (id, eshop_id, profile_id, last_download_success_time, auto_refresh_product_enabled)
        values (#{id},  #{eshopId}, #{profileId},#{lastDownloadSuccessTime}, #{autoRefreshProductEnabled})
    </insert>

    <update id="updateAutoDownloadProductTask">
        update pl_eshop_product_sync_condition
        set last_download_success_time=#{lastDownloadSuccessTime},auto_refresh_product_enabled=#{autoRefreshProductEnabled}
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
    </update>

    <select id="queryAutoDownloadProductTask" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSyncCondition">
        select id, eshop_id, profile_id, last_download_success_time, auto_refresh_product_enabled
        from pl_eshop_product_sync_condition
        where profile_id = #{profileId}
          and eshop_id = #{eshopId} limit 1
    </select>
</mapper>
package com.wsgjp.ct.sale.biz.member.service;

import com.wsgjp.ct.sale.biz.member.common.ScoreStrategyType;
import com.wsgjp.ct.sale.biz.member.model.dto.vip.VipAssertsChange;
import com.wsgjp.ct.sale.biz.member.model.dto.vip.VipSendMessageDto;
import com.wsgjp.ct.sale.biz.member.model.dto.vip.VipStoreChangeDTO;
import com.wsgjp.ct.sale.biz.member.model.dto.vip.VipStoreConsumptionDTO;
import com.wsgjp.ct.sale.biz.member.model.vo.vip.VipStoreConsumptionResponse;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;

/**
 * 会员资产变动类 （具体算法）
 */
public interface ISsVipAssertsChangeService {


    /**
     * 积分算法（会员此次行为应该获得多少积分）
     *
     * @param vipId 会员id
     * @param money 消费/充值金额
     * @param type  使用消费规则还是充值规则
     * @return 获得的积分
     */
    Integer scoreCompute(BigInteger vipId, BigDecimal money, ScoreStrategyType type);


    /**
     * 成长值算法（不包含累计）
     *
     * @param vipId 会员id
     * @param money 消费/充值金额
     * @param type 使用消费规则还是充值规则
     * @return 获得的成长值
     */
    Integer growthCompute(BigInteger vipId, BigDecimal money, ScoreStrategyType type);

    /**
     * 成长值算法（累计）
     *
     * @param vipId 会员id
     * @param type 使用消费规则还是充值规则
     * @param allowSaleChangeBill 是否是换货单
     * @return 获得的成长值
     */
    Integer accumulatedGrowthCompute(BigInteger vipId, ScoreStrategyType type, boolean allowSaleChangeBill);

    /**
     * 会员资产变动
     *
     * @param vipAssertsChange 变动信息
     * @return 变动信息
     */
    void vipAssertsChange(VipAssertsChange vipAssertsChange);

    VipStoreConsumptionResponse vipStoreConsumption(VipStoreConsumptionDTO dto);

    void vipStoreChange(VipStoreChangeDTO dto);

    void wxVipSyncList(List<BigInteger> vipIds, BigInteger profileId) throws Exception;

    void wxVipAsync(BigInteger vipId, BigInteger profileId);

    void sendMessageList(List<VipSendMessageDto> dto);
}

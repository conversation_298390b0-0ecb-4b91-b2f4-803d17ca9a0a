<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.shopsale.mapper.ShiftChangesMapper">
    <insert id="insertShiftChangesRecord">
        INSERT INTO ss_shift_changes_record( ID, profile_id, etype_id, start_time, change_time, total_amount
                                           , cash_amount, pay_amount, cashbox_deposit, cashbox_takeout
                                           , sale_total, return_total, return_amount, cashier_id, otype_id
                                           , stored_amount, transfer_amount, un_pay_total, un_pay_amount
                                           , other_thrid_amount, change_amount, change_total, sale_amount
                                           , recharge_amount, cashbox_balance)
        VALUES (#{newId}, #{profileId}, #{etypeId}, #{startTime}, #{changeTime}, #{totalAmount}, #{cashAmount},
                #{payAmount}, #{cashboxDeposit},
                #{cashboxTakeout}, #{saleTotal}, #{returnTotal}, #{returnAmount}, #{cashierId}, #{otypeId},
                #{storedAmount}, #{transferAmount}, #{unPayTotal}, #{unPayAmount}, #{otherThridAmount}, #{changeAmount},
                #{changeTotal}, #{saleAmount}, #{rechargeAmount}, #{cashboxBalance});
    </insert>

    <select id="selectShiftChanges"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.shiftchanges.ShiftChangesRequset">
        SELECT scr.etype_id, scr.create_time, scr.update_time, scr.deleted, STR_TO_DATE(scr.change_time, '%Y-%m-%d
        %H:%i:%s') as changeTime, scr.id, scr.profile_id,
        STR_TO_DATE(scr.start_time, '%Y-%m-%d %H:%i:%s') as startTime, scr.total_amount, scr.cash_amount,
        scr.pay_amount, scr.cashbox_deposit, scr.cashbox_takeout,
        scr.sale_total, scr.return_total, scr.return_amount, scr.cashier_id, scr.otype_id, scr.stored_amount,
        scr.transfer_amount,scr.un_pay_total,scr.un_pay_amount,scr.other_thrid_amount,scr.change_amount,scr.change_total,
        scr.cashbox_balance,
        o.fullname as storeName,sc.fullname as cashierName,e.fullname as etypeName,scr.sale_amount,scr.recharge_amount
        FROM ss_shift_changes_record scr
        left join base_otype o on o.id = scr.otype_id and o.deleted=0
        left join ss_cashier sc on sc.id = scr.cashier_id
        left join base_etype e on e.id = scr.etype_id
        WHERE
        scr.profile_id = #{params.queryParams.profileId}
        <if test="params.queryParams.startDate != null and params.queryParams.endDate != null">
            AND scr.create_time between #{params.queryParams.startDate} AND #{params.queryParams.endDate}
        </if>
        <if test="params.queryParams.cashierId != null">
            AND cashier_id = #{params.queryParams.cashierId}
        </if>
        <if test="params.queryParams.etypeId != null">
            AND etype_id = #{params.queryParams.etypeId}
        </if>
        <if test="params.queryParams.storeName != null and params.queryParams.storeName != ''">
            and o.fullname like CONCAT('%', #{params.queryParams.storeName},'%')
        </if>

        <if test="params.queryParams.cashierName != null and params.queryParams.cashierName != ''">
            and sc.fullname like CONCAT('%', #{params.queryParams.cashierName},'%')
        </if>

        <if test="params.queryParams.etypeName != null and params.queryParams.etypeName != ''">
            and e.fullname like CONCAT('%', #{params.queryParams.etypeName},'%')
        </if>
        <if test="params.queryParams.partypeid != null and params.queryParams.partypeid != ''">
            and o.partypeid like CONCAT(#{params.queryParams.partypeid},'%')
        </if>

        <if test="params.queryParams.otypeIds != null and params.queryParams.otypeIds.size > 0">
            and scr.otype_id in
            <foreach collection="params.queryParams.otypeIds" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="params.queryParams.cashierIds != null and params.queryParams.cashierIds.size > 0">
            and scr.cashier_id in
            <foreach collection="params.queryParams.cashierIds" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>

        <if test="params.queryParams.etypeIds != null and params.queryParams.etypeIds.size > 0">
            and scr.etype_id in
            <foreach collection="params.queryParams.etypeIds" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>

        <if test="params.queryParams.orderField != null and params.queryParams.orderField != ''">
            ORDER BY ${params.queryParams.orderField}
        </if>
        <if test="params.queryParams.orderField == null or params.queryParams.orderField == ''">
            ORDER BY scr.create_time desc
        </if>

        <if test="params.pageSize>0">
            limit #{params.pageIndex},#{params.pageSize}
        </if>
    </select>

    <select id="getShiftChangesRecordCount" resultType="java.lang.Integer">
        SELECT count(scr.id)
        FROM ss_shift_changes_record scr
        left join base_otype o on o.id = scr.otype_id and o.deleted=0
        left join ss_cashier sc on sc.id = scr.cashier_id
        left join base_etype e on e.id = scr.etype_id
        WHERE
        scr.profile_id = #{params.queryParams.profileId}
        <if test="params.queryParams.startDate != null and params.queryParams.endDate != null">
            AND scr.create_time between #{params.queryParams.startDate} AND #{params.queryParams.endDate}
        </if>
        <if test="params.queryParams.cashierId != null">
            AND cashier_id = #{params.queryParams.cashierId}
        </if>
        <if test="params.queryParams.etypeId != null">
            AND etype_id = #{params.queryParams.etypeId}
        </if>
        <if test="params.queryParams.storeName != null and params.queryParams.storeName != ''">
            and o.fullname like CONCAT('%', #{params.queryParams.storeName},'%')
        </if>

        <if test="params.queryParams.cashierName != null and params.queryParams.cashierName != ''">
            and sc.fullname like CONCAT('%', #{params.queryParams.cashierName},'%')
        </if>

        <if test="params.queryParams.etypeName != null and params.queryParams.etypeName != ''">
            and e.fullname like CONCAT('%', #{params.queryParams.etypeName},'%')
        </if>

        <if test="params.queryParams.partypeid != null and params.queryParams.partypeid != ''">
            and o.partypeid like CONCAT(#{params.queryParams.partypeid},'%')
        </if>

        <if test="params.queryParams.otypeIds != null and params.queryParams.otypeIds.size > 0">
            and scr.otype_id in
            <foreach collection="params.queryParams.otypeIds" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>

        <if test="params.queryParams.cashierIds != null and params.queryParams.cashierIds.size > 0">
            and scr.cashier_id in
            <foreach collection="params.queryParams.cashierIds" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>

        <if test="params.queryParams.etypeIds != null and params.queryParams.etypeIds.size > 0">
            and scr.etype_id in
            <foreach collection="params.queryParams.etypeIds" index="index" item="item" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getShiftChangesRecordSum"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.shiftchanges.ShiftChangesRequset">
        select SUM(total_amount) as totalAmountSum,SUM(cash_amount) as cashAmountSum, SUM(pay_amount) as
        payAmountSum,SUM(cashbox_deposit) as cashboxDepositSum,
        SUM(cashbox_takeout) as cashboxTakeoutSum, SUM(sale_total) as saleTotalSum,SUM(return_total) as
        returnTotalSum,SUM(return_amount) as returnAmountSum,SUM(transfer_amount) as
        transferAmountSum,SUM(stored_amount) as storedAmountSum,sum(un_pay_amount) as unPayAmountSum,sum(un_pay_total)
        as unPayTotalSum,sum(other_thrid_amount) as otherThridAmountSum,SUM(change_total) as
        changeTotalSum,SUM(change_amount) as changeAmountSum
        FROM ss_shift_changes_record scr
        left join base_otype o on o.id = scr.otype_id and o.deleted=0
        left join ss_cashier sc on sc.id = scr.cashier_id
        left join base_etype e on e.id = scr.etype_id
        WHERE
        <if test="profileId != null">
            scr.profile_id = #{profileId}
            <if test="startDate != null and endDate != null">
                AND scr.create_time between #{startDate} AND #{endDate}
            </if>
            <if test="cashierId != null">
                AND cashier_id = #{cashierId}
            </if>
            <if test="etypeId != null">
                AND etype_id = #{etypeId}
            </if>
            <if test="storeName != null and storeName != ''">
                and o.fullname like CONCAT('%', #{storeName},'%')
            </if>

            <if test="cashierName != null and cashierName != ''">
                and sc.fullname like CONCAT('%', #{cashierName},'%')
            </if>

            <if test="etypeName != null and etypeName != ''">
                and e.fullname like CONCAT('%', #{etypeName},'%')
            </if>

            <if test="partypeid != null and partypeid != ''">
                and o.partypeid like CONCAT(#{partypeid},'%')
            </if>

            <if test="etypeIds != null and etypeIds.size > 0">
                and scr.etype_id in
                <foreach collection="etypeIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="otypeIds != null and otypeIds.size > 0">
                and scr.otype_id in
                <foreach collection="otypeIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="cashierIds != null and cashierIds.size > 0">
                and scr.cashier_id in
                <foreach collection="cashierIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </if>
    </select>

    <select id="listPostTimeAndVchtype"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.shiftchanges.BillFinanceInfoDTO">
        select distinct a.bill_number as billNumber, aba2.pay_state,
        a.vchtype as vchtype,a.vchcode,a.bill_total as billTotal,
        IFNULL(ap.fullname, IF(adm.mark_code = 60000001,'积分兑换',IF(adm.mark_code = 60000002,'充值兑换', '未知支付')))
        as payFullName,
        IFNULL(aba.payway_id, adm.mark_code) as atypeId,
        ap.payway_type,
        CASE WHEN aba.currency_total <![CDATA[ < ]]> 0 THEN 2 WHEN aba.currency_total <![CDATA[ > ]]> 0 THEN 1 END AS
        payMethod,
        IFNULL (IF(ap.payway_type=3,aba.currency_total+IFNULL(aba2.currency_give_preferential_total,
        0),aba.currency_total),aba.currency_total) as total
        from acc_bill_core as a
        left join ss_preferential_bill spb on spb.vchcode = a.vchcode and spb.profile_id = a.profile_id and
        spb.preferential_type = 6
        LEFT join acc_bill_account aba on aba.vchcode = a.vchcode and aba.profile_id = a.profile_id
        LEFT join acc_bill_assinfo aba2 on aba2.vchcode = a.vchcode and aba2.profile_id = a.profile_id
        LEFT join base_payways ap on ap.id = aba.payway_id and ap.profile_id = a.profile_id
        LEFT join acc_deliver_mark adm on adm.vchcode = a.vchcode and adm.profile_id = a.profile_id
        LEFT join ss_vip_recharge_record_list svrrl on svrrl.vchcode = a.vchcode
        and svrrl.profile_id = a.profile_id
        and svrrl.charge_type != 2

        <!--        Left Join base_atype bat on bat.deleted = 0 and bat.typeid = '***************' and bat.profile_id = a.profile_id-->
        <!--        Left Join acc_bill_account aba2 on aba2.atype_id = bat.id and aba2.vchcode = a.vchcode-->
        <!--        LEFT join base_payways ap2 on ap2.id = aba2.payway_id and ap.profile_id = a.profile_id-->

        <where>
            <if test="query.vchtypes != null and query.vchtypes.size > 0">
                a.vchtype in
                <foreach collection="query.vchtypes" item="vchtype" open="(" close=")" separator=",">
                    #{vchtype}
                </foreach>
            </if>
            <if test="query.createEtypeId != null">
                AND a.create_etype_id = #{query.createEtypeId}
            </if>
            <if test="query.otypeId != null">
                AND a.otype_id = #{query.otypeId}
            </if>
            <if test="query.postBeginTime != null">
                AND a.bill_date <![CDATA[ >= ]]> #{query.postBeginTime}
            </if>
            <if test="query.postEndTime != null">
                AND a.bill_date <![CDATA[ <= ]]> #{query.postEndTime}
            </if>
            AND a.profile_id = #{query.profileId} AND a.deleted = 0 AND (a.order_sale_mode = 6 or a.business_type = 425)
            -- 关键条件：当 vchtype = 4001 时，要求 svrrl 表必须有匹配记录且满足 charge_type != 2
            AND (a.vchtype != 4001 OR (a.vchtype = 4001 AND svrrl.vchcode IS NOT NULL))
        </where>

        UNION
        select distinct a.bill_number as billNumber,aba2.pay_state,
        a.vchtype as vchtype,a.vchcode,a.bill_total as billTotal,
        IFNULL(ap.fullname, IF(adm.mark_code = 60000001,'积分兑换',IF(adm.mark_code = 60000002,'充值兑换', '未知支付')))
        as payFullName,
        IFNULL(aba.payway_id, adm.mark_code) as atypeId,
        ap.payway_type,
        CASE WHEN aba.currency_total <![CDATA[ < ]]> 0 THEN 2 WHEN aba.currency_total <![CDATA[ > ]]> 0 THEN 1 END AS
        payMethod,
        IFNULL (IF(ap.payway_type=3,aba.currency_total+IFNULL(aba2.currency_give_preferential_total,
        0),aba.currency_total),aba.currency_total) as total
        from td_bill_core as a
        left join ss_preferential_bill spb on spb.vchcode = a.vchcode and spb.profile_id = a.profile_id and
        spb.preferential_type = 6
        LEFT join td_bill_account aba on aba.vchcode = a.vchcode and aba.profile_id = a.profile_id
        LEFT join td_bill_assinfo aba2 on aba2.vchcode = a.vchcode and aba2.profile_id = a.profile_id
        LEFT join base_payways ap on ap.id = aba.payway_id and ap.profile_id = a.profile_id
        LEFT join td_deliver_mark adm on adm.vchcode = a.vchcode and adm.profile_id = a.profile_id
        LEFT join ss_vip_recharge_record_list svrrl on svrrl.vchcode = a.vchcode and svrrl.profile_id = a.profile_id
        and svrrl.charge_type != 2

        <!--        Left Join base_atype bat on bat.deleted = 0 and bat.typeid = '***************' and bat.profile_id = a.profile_id-->
        <!--        Left Join acc_bill_account aba2 on aba2.atype_id = bat.id and aba2.vchcode = a.vchcode-->
        <!--        LEFT join base_payways ap2 on ap2.id = aba2.payway_id and ap.profile_id = a.profile_id-->

        <where>
            <if test="query.vchtypes != null and query.vchtypes.size > 0">
                a.vchtype in
                <foreach collection="query.vchtypes" item="vchtype" open="(" close=")" separator=",">
                    #{vchtype}
                </foreach>
            </if>
            <if test="query.createEtypeId != null">
                AND a.create_etype_id = #{query.createEtypeId}
            </if>
            <if test="query.otypeId != null">
                AND a.otype_id = #{query.otypeId}
            </if>
            <if test="query.postBeginTime != null">
                AND a.bill_date <![CDATA[ >= ]]> #{query.postBeginTime}
            </if>
            <if test="query.postEndTime != null">
                AND a.bill_date <![CDATA[ <= ]]> #{query.postEndTime}
            </if>
            AND a.profile_id = #{query.profileId} AND a.post_state<![CDATA[ >= ]]>500 AND
            a.deleted = 0 AND (a.order_sale_mode = 6 or a.business_type = 425)
            -- 关键条件：当 vchtype = 4001 时，要求 svrrl 表必须有匹配记录且满足 charge_type != 2
            AND (a.vchtype != 4001 OR (a.vchtype = 4001 AND svrrl.vchcode IS NOT NULL))
        </where>
    </select>

    <select id="getShiftChangesSaleRecord"
            resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.shiftchanges.ShiftChangesRecordDto">
        select b.*,
        if(b.saleNum!=0,format( returnNum/saleNum,4),0) as returnRate,
        saleNum-returnNum as actualSaleNum,
        saleTotal-returnTotal as actualSaleTotal
        from (
        select a.*,
        sum(a.dised_taxed_total) as billTotal,
        sum(IFNULL(a.give_preferential_total, 0)) as preTotal,
        sum(if(a.vchtype = 2000 or a.vchtype = 2200, a.sQty, 0))*-1 as saleNum,
        sum(if(a.vchtype = 2000 or a.vchtype = 2200,(a.dised_taxed_total + a.give_preferential_total), 0))*-1 as
        saleTotal,
        sum(if(a.vchtype = 2100, a.dised_taxed_total + a.give_preferential_total, 0)) as returnTotal,
        sum(if(a.vchtype = 2100, a.sQty, 0)) as returnNum
        from (select tbdc.ptype_id as ptypeId,tbdc.detail_id ,
        bp.usercode,
        bp.fullname,
        bpf.fullbarcode,
        bps.propvalue_names as propNames,
        bps.id as skuId,
        tbc.vchtype,
        sum(tbdc.dised_taxed_total) as dised_taxed_total,
        sum(IFNULL(tbdc.give_preferential_total, 0)) as give_preferential_total,
        sum(qty) as sQty
        from td_bill_core tbc
        left join td_bill_assinfo tba
        on tba.profile_id = #{profileId} and tba.vchcode = tbc.vchcode
        left join td_bill_detail_core tbdc
        on tbdc.profile_id = #{profileId} and tbdc.vchcode = tbc.vchcode
        left join base_ptype bp on bp.profile_id = #{profileId} and bp.id = tbdc.ptype_id
        left join base_ptype_sku bps on bps.profile_id = #{profileId} and bps.id = tbdc.sku_id
        left join base_ptype_unit bpu
        on bpu.profile_id = #{profileId} and bpu.ptype_id = tbdc.ptype_id and bpu.unit_code = 1
        left join base_ptype_fullbarcode bpf on bpf.profile_id = #{profileId} and bpf.ptype_id =tbdc.ptype_id and
        tbdc.sku_id =bpf.sku_id and bpf.unit_id = bpu.id and bpf.defaulted=1
        where tbc.profile_id = #{profileId}
        AND tbc.create_time between #{startTime} AND #{endTime}
        and tbc.otype_id = #{otypeId}
        <if test="createEtypeId !=null and createEtypeId !=''">
            and tbc.create_etype_id = #{createEtypeId}
        </if>

        and tbc.vchtype in (2000, 2100, 2200)
        and tbc.post_state >= 500
        and tbc.post_state <![CDATA[ < ]]> 800
        and tba.pay_state = 1
        and tbc.deleted = 0
        <if test="pFullName !=null and pFullName !=''">
            and bp.fullname like CONCAT('%', #{pFullName},'%')
        </if>
        group by tbdc.ptype_id, tbdc.vchtype, bps.id
        union
        select tbdc.ptype_id as ptypeId,tbdc.detail_id ,
        bp.usercode,
        bp.fullname,
        bpf.fullbarcode,
        bps.propvalue_names as propNames,
        bps.id as skuId,
        tbc.vchtype,
        sum(tbdc.dised_taxed_total) as dised_taxed_total,
        sum(IFNULL(tbdc.give_preferential_total, 0)) as give_preferential_total,
        sum(qty) as sQty
        from acc_bill_core tbc
        left join acc_bill_assinfo tba
        on tba.profile_id = #{profileId} and tba.vchcode = tbc.vchcode
        left join acc_bill_detail_core_sale tbdc
        on tbdc.profile_id = #{profileId} and tbdc.vchcode = tbc.vchcode
        left join base_ptype bp on bp.profile_id = #{profileId} and bp.id = tbdc.ptype_id
        left join base_ptype_sku bps on bps.profile_id = #{profileId} and bps.id = tbdc.sku_id
        left join base_ptype_unit bpu
        on bpu.profile_id = #{profileId} and bpu.ptype_id = tbdc.ptype_id and bpu.unit_code = 1
        left join base_ptype_fullbarcode bpf on bpf.profile_id = #{profileId} and bpf.ptype_id =tbdc.ptype_id and
        tbdc.sku_id =bpf.sku_id and bpf.unit_id = bpu.id and bpf.defaulted=1
        where tbc.profile_id = #{profileId}
        AND tbc.create_time between #{startTime} AND #{endTime}
        and tbc.otype_id = #{otypeId}
        <if test="createEtypeId !=null and createEtypeId !=''">
            and tbc.create_etype_id = #{createEtypeId}
        </if>
        and tbc.vchtype in (2000, 2100, 2200)
        and tbc.post_state >= 500
        and tba.pay_state = 1
        and tbc.deleted = 0
        <if test="pFullName !=null and pFullName !=''">
            and bp.fullname like CONCAT('%', #{pFullName},'%')
        </if>
        group by tbdc.ptype_id, tbdc.vchtype, bps.id) a
        group by
        a.ptypeId,
        a.skuId)b
        <if test="sort != null ">
            order by ${sort.dataField}
            <if test="sort.ascending != true">
                desc
            </if>
        </if>
    </select>

    <select id="getFullBarCode" resultType="java.lang.String">
        select fullbarcode
        from base_ptype_fullbarcode
        where profile_id = #{params.profileId}
        and ptype_id = #{params.ptypeId}
        <if test="params.skuId != null">
            and sku_id = #{params.skuId}
        </if>
        <if test="params.unitId != null">
            and unit_id = #{params.unitId}
        </if>
        limit 1
    </select>

    <insert id="insertLoginRecord">
        INSERT INTO ss_login_record( id, profile_id, otype_id, etype_id, cashier_id, login_time
                                   , shift_changed, client_type)
        VALUES (#{id}, #{profileId}, #{otypeId}, #{etypeId}, #{cashierId}, #{loginTime}, #{shiftChanged},
                #{clientType});
    </insert>

    <select id="getLoginRecordFirst" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.shiftchanges.LoginRecordDto">
        select *
        from ss_login_record
        where profile_id = #{profileId}
          and shift_changed = 0
          and otype_id = #{otypeId}
          and etype_id = #{etypeId}
          and cashier_id = #{cashierId}
        ORDER BY create_time
        limit 1
    </select>

    <update id="updateAllLoginRecordShiftChanged">
        update ss_login_record
        set shift_changed = 1
        where profile_id = #{profileId}
          and otype_id = #{otypeId}
          and etype_id = #{etypeId}
          and cashier_id = #{cashierId}
    </update>
</mapper>

package com.wsgjp.ct.sale.biz;

import com.wsgjp.ct.bill.core.handle.common.BillSwitch;
import com.wsgjp.ct.pm.entity.Menu;
import com.wsgjp.ct.pm.entity.MenuPermissionInterlayerEntity;
import com.wsgjp.ct.pm.entity.Permission;
import com.wsgjp.ct.pm.enums.SysProfileModelKeyEnum;
import com.wsgjp.ct.pm.inter.Filter;
import com.wsgjp.ct.pm.service.PermissionValiateService;
import com.wsgjp.ct.pm.service.SysProfileModelService;
import com.wsgjp.ct.sale.biz.common.util.VersionUtil;
import com.wsgjp.ct.sale.biz.eshoporder.constant.StringConstant;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopOrderBaseInfoMapper;
import com.wsgjp.ct.sale.biz.eshoporder.service.refund.impl.NewRefundServiceImpl;
import com.wsgjp.ct.sale.biz.jarvis.config.NavProcessStatus;
import com.wsgjp.ct.sale.biz.jarvis.service.nav.NavStockService;
import com.wsgjp.ct.sale.biz.jarvis.utils.BillDeliverUtils;
import com.wsgjp.ct.sis.client.common.CurrentUser;
import com.wsgjp.ct.support.global.GlobalConfig;
import com.wsgjp.ct.support.global.entity.SysGlobalConfig;
import ngp.utils.StringUtils;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/6 17:22
 */
@Configuration
public class PermissionFilter implements Filter {
    private final NavStockService navStockService;
    private final SysProfileModelService sysProfileService;
    private final EshopOrderBaseInfoMapper mapper;

    private final NewRefundServiceImpl newRefundService;

    private BillSwitch billSwitch;

    public PermissionFilter(NavStockService navStockService, SysProfileModelService sysProfileService, EshopOrderBaseInfoMapper mapper, NewRefundServiceImpl newRefundService, BillSwitch billSwitch) {
        this.navStockService = navStockService;
        this.sysProfileService = sysProfileService;
        this.mapper = mapper;
        this.newRefundService = newRefundService;
        this.billSwitch = billSwitch;
    }

    @Override
    public void filterPermission(List<Permission> list) {
        //jarvis的
        closeProcessStateControlPermission(list);
    }

    @Override
    public void filterMenu(List<Menu> list) {
        if (!isOpenPublishProduct()) {
            for (Menu menu : list) {
                if ("eshoporder.eshopPtypePubnish".equals(menu.getKey())) {
                    menu.setHidden(true);
                    break;
                }
            }
        }
//        if (!newRefundService.isShowCheckin()) {
//            for (Menu menu : list) {
//                if ("eshoporder.eshopReceiveCheckInPage".equals(menu.getKey())) {
//                    menu.setDisable(true);
//                    break;
//                }
//            }
//        }
        if (!getPlatformReconciliationFunc())
            for (Menu menu : list) {
                if ("eshoporder.platformFinanceCheck".equals(menu.getKey())) {
                    menu.setDisable(true);
                }
                if ("eshoporder.platformCheckAccounts".equals(menu.getKey())) {
                    menu.setDisable(true);
                }
            }
        if (isHiddenOverSold()) {
            for (Menu menu : list) {
                if ("eshoporder.stock.oversold.config".equals(menu.getKey())) {
                    menu.setHidden(true);
                    menu.setDisable(true);
                    break;
                }
            }
        }
        //是否老用户
        boolean oldVersion = isOldVersion();
        //是否开启商品管理开关
        boolean eshopProductManagerEnabled = isEshopProductManagerEnabled();
        // 是否开启复杂库存状况表和可销售库存开关  2个开启显示可销售
        Map<String, Boolean> configureColumns = getConfigureColumns();
        list.forEach(menu -> {
            if (StringUtils.isNotBlank(menu.getKey()) && (!configureColumns.get("InventoryInnerFunc")
                    ||
                    (configureColumns.get("InventoryInnerFunc") && !configureColumns.get(SysProfileModelKeyEnum.SaleQtyFunc.getKey())
                            && !SysProfileModelService.checkCurrentEnabledByKeyEnum(SysProfileModelKeyEnum.EshopFunc))
            )) {
                disableTrue(menu, "analysiscloud.saleStockReport");
            }
            if(oldVersion){
                 if(eshopProductManagerEnabled){
                     //宝贝对应
                     disableMenu(menu, "eshoporder.eshopPtypeRelationPage",true);
                     //商品打标
                     disableMenu(menu, "eshoporder.eshopPtypeMarkPage",true);
//                     disableTrue(menu, "eshoporder.eshopPtypeLoadingAndUnloadingPage");
                     //库存同步
                     disableMenu(menu, "eshoporder.eshopStockSync",true);
                     //商品管理
                     disableMenu(menu, "eshoporder.product",false);
                 }
                 else{
                     //宝贝对应
                     disableMenu(menu, "eshoporder.eshopPtypeRelationPage",false);
                     //商品打标
                     disableMenu(menu, "eshoporder.eshopPtypeMarkPage",false);
//                     disableTrue(menu, "eshoporder.eshopPtypeLoadingAndUnloadingPage");
                     //库存同步
                     disableMenu(menu, "eshoporder.eshopStockSync",false);
                     //商品管理
                     disableMenu(menu, "eshoporder.product",true);
                 }

            }
        });
    }



    private boolean getPlatformReconciliationFunc() {
        boolean platformReconciliationFuncSta = SysProfileModelService.checkCurrentEnabledByKeyEnum(SysProfileModelKeyEnum.PlatformReconciliationFunc);
        boolean eshopFuncSta = SysProfileModelService.checkCurrentEnabledByKeyEnum(SysProfileModelKeyEnum.EshopFunc);
        boolean cloudOrderMiddlegroundFuncSta = SysProfileModelService.checkCurrentEnabledByKeyEnum(SysProfileModelKeyEnum.CloudOrderMiddlegroundFunc);
        return (eshopFuncSta || cloudOrderMiddlegroundFuncSta) && platformReconciliationFuncSta;
    }

    private boolean isOpenPublishProduct() {
        String sysDataValue = mapper.getSysData(CurrentUser.getProfileId(), "openPublishProduct");
        return "1".equals(sysDataValue);
    }
    private boolean isOpenAuditGetFreightBillNo() {
        String sysDataValue = mapper.getSysData(CurrentUser.getProfileId(), "openAuditGetFreightBillNo");
        return "1".equals(sysDataValue);
    }
    private boolean isOpenAuditSyncWayBill() {
        String sysDataValue = mapper.getSysData(CurrentUser.getProfileId(), "openAuditSyncWayBill");
        return VersionUtil.isNewVersion()&&"1".equals(sysDataValue);
    }

    private boolean isOldVersion() {
        return !VersionUtil.isNewVersion();
    }

    private boolean isEshopProductManagerEnabled() {
        String sysDataValue = mapper.getSysData(CurrentUser.getProfileId(), "sys.func.user-eshop-product-manager.enabled");
        return StringUtils.isNotBlank(sysDataValue) && "true".equals(sysDataValue);
    }
    /**
     * 当流程被关闭需要控制的权限点
     */
    private void closeProcessStateControlPermission(List<Permission> list) {
        NavProcessStatus navProcessStatus = this.navStockService.getNavProcessStatus();
        boolean sysGlobalNoMappingSubmit = GlobalConfig.getBoolean("sysGlobalNoMappingSubmit");
        SysGlobalConfig config = GlobalConfig.get(SysGlobalConfig.class);
        boolean isTaxEnable = config.isEnabledTax();
        boolean isOpenSimpleProcess = BillDeliverUtils.isOpenSimpleProcess();
        boolean isOpenPublishProduct = isOpenPublishProduct();
        boolean useNewSendQtyCalc = billSwitch.useNewSendQtyCalc(CurrentUser.getProfileId());
        boolean isNewVersion=VersionUtil.isNewVersion();

        for (Permission permission : list) {
            String key = permission.getKey();
            if (!isOpenPublishProduct) {
                if (key.contains("eshopPtypePubnish")) {
                    permission.setDisable(true);

                }
            }

            // 如果启用了新的发货数量计算，则关闭锁定发货数量和取消锁定发货数量权限
            if (useNewSendQtyCalc) {
                if (key.contains("jarvis.deliverCommon.lockSendQty")
                        || key.contains("jarvis.deliverCommon.cancelLockSendQty")) {
                    permission.setDisable(true);
                }
            }

            if (key.equals("jarvis.deliverCommon.modifyDisedTaxPrice")) {
                permission.setName(isTaxEnable ? "改折后含税单价" : "改折后单价");
            }
            if (!navProcessStatus.getCheckEnabled()) {
                if (key.contains("deliverScanCheckGoodsBy")) {
                    permission.setDisable(true);
                }
            }
            if (!navProcessStatus.getWeightEnabled()) {
                if (key.contains("deliverSingleScanWeigh.") || key.contains("deliverBatchScanWeigh.")) {
                    permission.setDisable(true);
                }
            }
            if (!navProcessStatus.getSendEnabled()) {
                if (key.contains("deliverBillSendByBill.")
                        || key.contains("deliverBillSendByBatch.")
                        || key.equalsIgnoreCase("deliverCommon.send")) {
                    permission.setDisable(true);
                }
            }
            if (isOpenSimpleProcess) {
                if (key.contains("printBatchConfigStrategy")) {
                    permission.setDisable(true);
                }
            } else {
                if (key.contains("jarvis.warehousingStrategyConfig.freightStrategy") // 物流匹配策略
                        || key.contains("jarvis.warehousingStrategyConfig.deliverStrategy") // 发货单策略
                        || key.contains("jarvis.warehousingStrategyConfig.sortStrategy") // 排序策略
                ) {
                    permission.setDisable(true);
                }
            }


            if (!sysGlobalNoMappingSubmit) {
                if (key.equalsIgnoreCase("deliverCommon.doRelation")) {
                    permission.setDisable(true);
                }
            }

            if(!isOpenAuditGetFreightBillNo()){
                if(isNewVersion){
                    if (key.contains("jarvis.deliverCommon.getFreightBillNoAudit")
                            || key.contains("jarvis.deliverCommon.cancelFreightBillNoAudit")
                    ) {
                        permission.setDisable(true);
                    }
                }
            }
            if(!isOpenAuditSyncWayBill()){
                if(isNewVersion){
                    if (key.contains("jarvis.deliverCommon.syncWayBill")
                    ) {
                        permission.setDisable(true);
                    }
                }
            }

            if(key.contains("eshoporder.product.downloadMainPic")){
                if(!GlobalConfig.get("sysFuncEshopPtypeManagerMainPicEnabled","0").equals(StringConstant.SYS_DATA_SUCCESS)){
                    permission.setDisable(true);
                }
            }

            if(key.contains("eshoporder.eshopordergather.view") && !SysProfileModelService.checkCurrentEnabledByKeyEnum(SysProfileModelKeyEnum.PlatformReconciliationFunc)){
                    permission.setDisable(true);
            }
        }
    }

    /**
     * 设置权限点不可用
     *
     * @param menuPermission
     * @param key
     */
    private void disableTrue(MenuPermissionInterlayerEntity menuPermission, String key) {
        if (key.equals(menuPermission.getKey())) {
            menuPermission.setDisable(true);
        }
    }

    private void disableMenu(MenuPermissionInterlayerEntity menuPermission, String key,boolean visible) {
        if (key.equals(menuPermission.getKey())) {
            menuPermission.setDisable(visible);
        }
    }

    public Map<String, Boolean> getConfigureColumns() {
        Map<String, Boolean> enabledByAllKeys = SysProfileModelService.getEnabledByAllKeys();
        // 盘点先出入库后核算 默认关闭 开关不可见
        Boolean stockCheckPostAsyncFunc = enabledByAllKeys.get(SysProfileModelKeyEnum.StockCheckPostAsyncFunc.getKey());
        // 调拨在途
        Boolean enabledTransporting = enabledByAllKeys.get(SysProfileModelKeyEnum.TransportingFunc.getKey());
        // 实物出入库完成后自动过账 名称改为：允许先出入库完成后过账(反向)
        Boolean goodsInOutPostAsyncFunc = enabledByAllKeys.get(SysProfileModelKeyEnum.GoodsInOutPostAsyncFunc.getKey());
        // 收货登记 开关屏蔽 进销存产品默认关  网店产品默认开
        Boolean receiveCheckinFunc = enabledByAllKeys.get(SysProfileModelKeyEnum.ReceiveCheckinFunc.getKey());
        // 外勤业务管理（配送、巡店、车销）
        //配送
        Boolean distributeFunc = enabledByAllKeys.get(SysProfileModelKeyEnum.DistributeFunc.getKey());
        //巡店
        Boolean visitFunc = enabledByAllKeys.get(SysProfileModelKeyEnum.VisitFunc.getKey());
        //车销
        Boolean saleCarFunc = enabledByAllKeys.get(SysProfileModelKeyEnum.SaleCarFunc.getKey());
        // 仓储模块 开关屏蔽 进销存产品默认关  网店产品默认开
        Boolean wmsFunc = enabledByAllKeys.get(SysProfileModelKeyEnum.WmsFunc.getKey());
        // 网店模块开关
        Boolean eshopFunc = enabledByAllKeys.get(SysProfileModelKeyEnum.EshopFunc.getKey());

        //4.5增加
        //委外
        Boolean outsourcingFunc = enabledByAllKeys.get(SysProfileModelKeyEnum.OutsourcingFunc.getKey());
        //借出
        Boolean lendFunc = enabledByAllKeys.get(SysProfileModelKeyEnum.LendFunc.getKey());
        //委托销售
        Boolean entrustFunc = enabledByAllKeys.get(SysProfileModelKeyEnum.EntrustFunc.getKey());
        // 是否显示仓内 上面任意一个为true 则为true
        Boolean inventoryInnerFunc = eshopFunc || stockCheckPostAsyncFunc || enabledTransporting || goodsInOutPostAsyncFunc
                || receiveCheckinFunc || wmsFunc || distributeFunc || visitFunc || saleCarFunc || outsourcingFunc || lendFunc || entrustFunc;
        enabledByAllKeys.put("InventoryInnerFunc", inventoryInnerFunc);
        return enabledByAllKeys;
    }
    private boolean isHiddenOverSold() {
        String sysDataValue = mapper.getSysData(CurrentUser.getProfileId(), "eshop_func_oversell_setting_enabled");
        boolean openedOverSold =  "1".equals(sysDataValue) || "ture".equals(sysDataValue) ;
        return !openedOverSold && VersionUtil.isNewVersion();
    }
}

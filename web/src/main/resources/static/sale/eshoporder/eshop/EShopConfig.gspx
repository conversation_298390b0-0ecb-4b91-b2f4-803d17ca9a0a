<?xml version="1.0" encoding="UTF-8"?>
<Page xmlns="Craba.UI" Title="网店设置" CssClass="BasePage"
      ActionType="sale.eshoporder.eshop.EShopConfigAction,sale/eshoporder/eshop/EShopConfig.js"
      DataSource="${datasource}" AllowResize="false" OnClose="doFormClose" CssStyle="overflow: hidden;">
    <Style>
        .EshopAddPage .LabelTitle {
        margin: 0;
        font-size: 15px;
        font-weight:bold;
        }
        .EshopAddPage:first-child {
        margin-top: 10px;
        }
        .EshopAddPage .LabelRemark {
        font-size: 12px;
        -webkit-transform: scale(0.9);
        -o-transform: scale(1);
        display: inline-block;
        color: #787878;
        line-height: 14px;
        height: 14px;
        }
        .EshopAddPage .FlowItem .LabelRemark{
        margin: -20px 0px -20px 60px;
        }
        .EshopAddPage .Card{
        width:356.5px;
        height:125px;
        background-size:100% 100%;
        background-repeat:no-repeat;
        margin-bottom:10px;
        border: 0.5px solid;
        border-radius: 5px;
        border-color: #********;
        padding:10px 20px 10px 10px;
        position:relative;
        }
        .body {padding: 10px 10px 0 0;margin-right:0;overflow: auto !important;}
        .block {padding:0 10px;background-color: #fff;border-radius: 4px;margin-bottom: 10px !important;}

        .mainbody {
        flex-shrink: 1 !important;
        }
        .EshopAddPage .LabelTitle {
        margin: 0;
        font-size: 15px;
        font-weight:bold;
        }
        .EshopAddPage:first-child {
        margin-top: 10px;
        }
        .EshopAddPage .LabelRemark {
        font-size: 12px;
        -webkit-transform: scale(0.9);
        -o-transform: scale(1);
        display: inline-block;
        color: #787878;
        line-height: 14px;
        height: 14px;
        }
        .EshopAddPage .FlowItem .LabelRemark{
        margin: -20px 0px -20px 60px;
        }
        .EshopAddPage .Card{
        width:356.5px;
        height:125px;
        background-size:100% 100%;
        background-repeat:no-repeat;
        margin-bottom:10px;
        margin-left:110px;
        border: 0.5px solid;
        border-radius: 5px;
        border-color: #********;
        padding:10px 20px 10px 10px;
        position:relative;
        }
        .EshopAddPage .Card .Label {
        display:block;
        line-height:24px;
        height:24px;
        }
        .EshopAddPage .Card .LabelCardTitle {
        display: inline-block;
        font-size: 14px;
        font-weight:bold;
        }
        .EshopAddPage .Card .LabelBtn {
        display: inline-block;
        font-size: 12px;
        margin-top:30px;
        font-weight:bold;
        cursor: pointer;
        }
        .EshopAddPage .Card .LabelRemark {
        margin-left: -10px;
        -webkit-transform: scale(0.96);
        }
        .remarkTextArea{
        width: 815px;
        }
        .OtherTipBox { padding: 10px 10px 20px 20px; position: relative; margin-top: 20px; border: 1px dashed #7bcbff;
        min-width: 200px; min-height: 60px; box-shadow: 3px 4px 2px 4px #e2f1ff; border-radius: 4px; }
        .OtherTipBox .Arrow1 { content: ' '; border-top: 1px dashed #7bcbff; width: 16px; position: absolute; right:
        10%; top: -8px; height: 1px; transform: rotate(45deg); height: 12px; background: #fff; }
        .OtherTipBox .Arrow2{ content: ' '; border-top: 1px dashed #7bcbff; width: 16px; position: absolute; right: 10%;
        top: -8px; transform: rotate(-45deg); margin-right: 4px; background: #fff; height: 12px; }
        .OtherTipBox .Title { font-weight: bold; line-height: 40px; }
        .OtherTipBox .Label { color: #999; }
        .selBox .selTitle{
        max-width: 100%;
        margin-bottom: 5px;
        text-align: right;
        width: 130px;
        font-size: 14px;
        margin-left: 60px;
        }
        .selBox .selBtns{
        margin-left:120px;
        }
        .selBox .selBtn{
        margin-right:20px;
        border-radius:50px;
        padding: 0px 16px;
        height: 36px;
        font-size: 13px;
        border: 0.5px solid;
        }
        .selBox .usuallyBtn{
        border-color: transparent;
        background-color:#f5f6f8;
        color: #666
        }
        .selBox .checked{
        background-color: #ebf3ff;
        }
        .selBox .selBtn label{
        margin:0;
        }
        .selBox .OtherTipBox{
        margin-left:120px;
        border: 1px solid #e1e1e1;
        box-shadow: unset;
        position: relative;
        width: 820px;
        margin-bottom: 30px;
        }
        .selBox .OtherTipBox .Arrow1 {
        border-top: 1px solid #e1e1e1;
        }
        .selBox .OtherTipBox .Arrow2 {
        border-top: 1px solid #e1e1e1;
        }
        .OtherTipBox .Line{
        background-color: #a5a5a5;
        padding: 0;
        margin: 0;
        font-size: 0;
        overflow: hidden;
        height: 50px;
        width: 1px;
        position: absolute;
        top: 90px;
        left: 25px;
        z-index: 0;
        }
        .OtherTipBox .TipsBox{
        background-color: #fef6eb;
        margin-bottom: 20px;
        min-width: 600px;
        padding: 0px 5px;
        border-radius: 5px;
        }
        .OtherTipBox .TipsIcon{
        color: red;
        margin-right: 5px;
        }
        .OtherTipBox .Tipsdescription{
        color: #2b2a28;
        }
        .OtherTipBox .ProcessBox:not(:last-child) {
        margin-bottom: 25px;
        }
        .OtherTipBox .ProcessBox {
        height: 50px;
        position: relative;
        }
        .ProcessBox .ProcessIcon{
        position: absolute;
        }
        .ProcessBox .ProcessItem{
        position: absolute;
        left: 20px;
        color: #2b2a28;
        font-size: 13px;
        }
        .ProcessBox .ProcessBtn{
        position: absolute;
        top: 3px;
        left: 105px;
        width: 64px;
        height: 28px;
        line-height: 25px;
        padding: 0px 5px;
        font-size: 13px;
        }
        .ProcessBox .ProcessLabel{
        position: absolute;
        left: 105px;
        top: 30px;
        }
        .EshopAddPage .FlowPanel .FlowLabel {
        font-size: 13px
        }
        .selBtns .ChkBoxStyle input {
        top: 10px;
        left: 15px;
        }
        .Padding110
        {
        padding:0px 110px;
        }
        .htmlTipBlock .p{display:block;line-height:30px;height:auto;color:#333}
        .htmlTipBlock .p:before{content:"
        ";width:6px;height:6px;border-radius:3px;background-color:#ccc;display:inline-block;margin-right:10px;vertical-align:middle;}
    </Style>
    <HiddenField ID="otypeId" DataField="otypeId"/>
    <HiddenField ID="profileId" DataField="profileId"/>
    <HiddenField ID="hfAutoPickKtype" Value="${pickKtypeInfo}"/>
    <HiddenField ID="hdSendProcessWay" Value="${hdSendProcessWay}"/>
    <HiddenField ID="oldAgEnabled" Value="${oldAgEnabled}" DataField="oldAgEnabled"/>
<!--    <HiddenField ID="hdDefaultBuyerArea" Value="${buyerParams}"/>-->
    <HiddenField ID="fieldInfos" Value="${fieldInfos}"/>
    <HiddenField ID="shopAccountName"/>
    <HiddenField ID="oldShopAccount" DataField="oldShopAccount"/>
    <HiddenField ID="authType" DataField="authType"/>
    <HiddenField ID="orderLink" DataField="orderLink"/>
    <HiddenField ID="needAuth" DataField="needAuth"/>
    <HiddenField ID="authID" DataField="auth"/>
    <HiddenField ID="btypeId" DataField="btypeId"/>

    <FlexBlock CssClass="mainbody" ID="flbMainBody">
        <FlexColumn CssClass="body" ID="TopicMain">
            <!--            <Label ID="topicCWGL13" Text='基础信息' Tag="BaseInfoTopic" CssClass='LayoutGroupCaption'/>-->
            <FlowPanel ColSpan='2' ItemLabelWidth="125" Width="950">
                <TextEdit ID="orgName" DataField="otypeFullname" Label="网店名称:" AllowTags='true'
                          LabelCssClass="MustCharLeft just-content-right"
                          Required="true" NullDisplayText="网店名称不能重复"
                          MaxLength="100"/>

                <SelectorEdit ID="edKType" Label="发货仓库" DataField="ktypeId" MaxLength="100"
                              NullDisplayText="请选择发货仓库" Enabled="${pageEnabled}"
                              OnButtonClick="onKtypeInit" DisplayField="ktypeName" LabelCssClass="MustCharLeft just-content-right"
                              OnEnterPress="doEnterPress" OnSelectorSelected="doBaseInfoSelect" Required="true"
                              SelectOnly="true"/>

                <DropDownEdit ID="btypeGenerateType" DataField="btypeGenerateType" Label="收入结算方"
                              LabelCssClass="MustCharLeft just-content-right" Visible="false"
                              ListItems="0=网店所属平台,1=订单中的分销商"
                              DropDownStyle="DropDownList"
                              Required="true"/>

                <DropDownEdit ID="dpParTypeId" DataField="partypeid" Label="选择分类:" LabelCssClass="MustCharLeft just-content-right"
                              DataSource="${partypes}" DataTextField="fullname" DataValueField="typeid"
                              DropDownStyle="DropDownList" Required="true" Visible="${allowShowEshopClass}"/>

                <SelectorEdit Label="平台的往来单位" Business="selected" OnButtonClick="supplierSelectorInit"
                              AllowTags='true' SelectOnly="true" Visible="false"
                              OnSelectorSelected="selectSupplier" CssClass='FlexAuto just-content-right'
                              DisplayField="btypeName" DataField="btypeId" ID="BtypeId"/>
                <DropDownEdit ID="shopType" Enabled="false" DataField="eshopType" Label="平台网店类型"
                              DataSource="${eshopTypeSupport}"
                              DataTextField="name" DataValueField="type" DropDownStyle="DropDownSearch"
                              LabelCssClass="MustCharRight just-content-right" Visible="false"
                              OnChange="onEShopTypeChange" Required="true"/>
            </FlowPanel>


            <Label Text="授权信息" Tag="BaseInfoTopic" CssClass='LayoutGroupCaption LabelTitle' ID="appAuthLabel"
                   Visible="${!powers.isWholesale}"/>
            <FlowPanel ID="appAuthInfo" ColSpan="2" Visible="${!powers.isWholesale}" Width="950" ItemLabelWidth="125">
                <DropDownEdit ID="platformauthType" Label="平台授权类型" Enabled="false" LabelCssClass="MustCharLeft just-content-right"
                              DataField="platformauthType" ListItems="0=单店授权,1=连锁网店授权"
                              Onchange="handlePlatformuthType"
                              DataTextField="text" DataValueField="value" Required="true"
                              Visible="false"/>
                <TextEdit ID="shopAccount" DataField="eshopAccount" Label="网店账号" Required="true" AllowTags='true'
                          Visible="${!powers.isWholesale}" Enabled="${pageEnabled}"
                          LabelCssClass="MustCharLeft just-content-right" NullDisplayText="请填写网店账号"
                          MaxLength="100"/>
                <DropDownEdit ID="platformEshopSnType" Label="序列号类型" LabelCssClass="MustCharLeft just-content-right"
                              DataField="platformEshopSnType" DataSource="${dropdownDataSource}"
                              DataTextField="text" DataValueField="value" Required="true"
                              Visible="false"/>
                <TextEdit ID="vendorId" DataField="vendorId" Label="供应商id" Required="true"
                          LabelCssClass="MustCharLeft just-content-right" Visible="false"
                          MaxLength="200"/>
                <TextEdit ID="platformEshopId" DataField="platformEshopId" Label="常态合作编码"
                          Visible="false"
                          MaxLength="250"/>
                <TextEdit ID="onlineEshopId" DataField="onlineEshopId" Label=""
                          Visible="false" CssClass="just-content-right"
                          MaxLength="250"/>
                <TextEdit Label="AppKey" ID="appKey" DataField="appKey" Required="true" Visible="false"
                          CssStyle="width: 312px"
                          LabelCssClass="MustCharLeft just-content-right" MaxLength="200"/>
                <TextEdit Label="AppSecret" ID="appSecret" DataField="appSecret" Required="true" CssStyle="width: 312px"
                          Visible="false" MaxLength="1000"/>
                <HBlock ID="tmallSpecialSale" Label='天猫超市特卖业务:'>
                    <RadioButton ID="radio1" Width="100" Checked="true" GroupName="isTmallSpecialSale"
                                 OnChange="showToken" Text="否"/>
                    <RadioButton ID="radio2" Width="100" GroupName="isTmallSpecialSale" OnChange="showToken" Text="是"/>
                </HBlock>
                <TextEdit ID="token" DataField="token" Label="授权码" Required="true"
                          LabelCssClass="MustCharLeft just-content-right" Visible="false"
                          MaxLength="200"/>
            </FlowPanel>

            <FlowPanel ColSpan='1' ID="appAuth" Visible="${!powers.isWholesale}" Width="950">
                <HBlock ID="usualAuth" Visible="false" CssClass="FlexWrap pd10">
                    <Block CssClass='Card' ID="cbxOrderhp">
                        <Label CssClass="LabelCardTitle dinggou" Text='订购'/>
                        <Label CssClass="LabelRemark"
                               Text='将跳转销售平台的服务市场，在销售平台订购管家婆ERP后，支付成功后再授权'/>
                        <Label CssClass="LabelBtn SkinColor" Text='去订购' ID="btnOrder" OnClick="doOrder"/>
                    </Block>
                    <Block CssClass='Card' CssStyle="margin-left:2px" ID="cbxAuthhp">
                        <Label CssClass="LabelCardTitle shouquan" Text='授权'/>
                        <Label CssStyle="position:absolute;right:5px;top:5px;color:#54bb15" CssClass="aicon-duigou"
                               Text="已授权" Visible="false" ID="iconAuth"/>
                        <Label CssClass="LabelRemark" Text='将跳转销售平台授权页面，输入网店主账号完成授权'/>
                        <Label CssClass="LabelBtn SkinColor" Text='去授权' ID="btnAuth" OnClick="doAuth"
                               Enabled="${needAuth}"/>
                    </Block>
                </HBlock>
                <FlexBlock ID="threeStepAuth" Visible="false" CssClass="FlexWrap pd10">
                    <Block CssClass='Card' ID="cbxInfo">
                        <Label CssClass="LabelCardTitle dinggou" Text='发起授权申请'/>
                        <Label CssClass="LabelRemark" Text='复制京东秒送网店id给服务顾问，发起授权申请'/>
                    </Block>
                    <Block CssClass='Card' ID="cbxSendAuth">
                        <Label CssClass="LabelCardTitle shouquan" Text='建立授权'/>
                        <Label CssClass="LabelRemark"
                               Text='到京东秒送开放平台，同意我们发起的授权，并且向我们操作发送授权码。发送授权码后，请耐心等待成功'/>
                        <Label CssClass="LabelBtn SkinColor" Text='同意授权并发送授权码' ID="btnSendAuth"
                               OnClick="doAuth"/>
                    </Block>
                    <Block CssClass='Card' ID="cbxSaveAuth">
                        <Label CssClass="LabelCardTitle shouquanEnd" Text='保存授权'/>
                        <Label CssStyle="position:absolute;right:5px;top:5px;color:#54bb15" CssClass="aicon-duigou"
                               Text="已授权" Visible="${auth}" ID="iconAuth2"/>
                        <Label CssClass="LabelRemark" Text='建立授权后，京东秒送平台需要额外保存授权'/>
                        <Label CssClass="LabelBtn SkinColor" Text='保存授权' ID="btnSendAuth2" OnClick="doInderectAuth"
                               Enabled="${needAuth}"/>
                    </Block>
                </FlexBlock>
                <Block CssClass="selBox pd10" ID="selectAppkey" Visible="false">
                    <Label CssClass="selTitle" Text='请选择要订购的应用' CssStyle="margin-right:10px"/>
                    <Block CssClass="selBtns" ID="selectBtns"/>
                    <Block CssClass='OtherTipBox' ID='Box'>
                        <Block CssClass='Arrow1' ID='arrow1'/>
                        <Block CssClass='Arrow2' ID='arrow2'/>

                        <Block CssClass="TipsBox">
                            <Label CssClass="TipsIcon" Text='*'/>
                            <Label CssClass="Tipsdescription" ID="selectdescription" Text=''/>
                        </Block>
                        <Block CssClass="Line" Visible="false"/>
                        <Block CssClass='ProcessBox' Visible="false">
                            <Label CssClass="ProcessIcon" Text='①'/>
                            <Label CssClass="ProcessItem" Text='订购'/>
                            <Button CssClass="ProcessBtn SkinColor SkinBorder" Text='去订购' OnClick="doOrder"/>
                            <Label CssClass="ProcessLabel"
                                   Text='将跳转销售平台的服务市场，在销售平台订购管家婆ERP后，支付成功后再授权'/>
                        </Block>
                        <Block CssClass='ProcessBox' Visible="false">
                            <Label CssClass="ProcessIcon" Text='②'/>
                            <Label CssClass="ProcessItem" Text='授权'/>
                            <Button CssClass="ProcessBtn SkinColor SkinBorder" Text='${authText}' OnClick="doAuth"
                                    Enabled="${needAuth}"/>
                            <Label CssStyle="position:absolute;right:5px;top:5px;color:#54bb15" CssClass="aicon-duigou"
                                   Text="已授权" Visible="${auth}"/>
                            <Label CssClass="ProcessLabel" Text='将跳转销售平台授权页面，输入网店主账号完成授权'/>
                        </Block>
                    </Block>
                </Block>
                <HBlock/>
            </FlowPanel>
            <FlowPanel ID="BranchStorePanel" CssClass="pd10" LayoutDirection="Vert" Visible="false">
                <Label ItemCssClass="mt-o" CssStyle="font-weight: bold;height: 12px;" CssClass="mr0"
                       Text="第三步：选择要授权的子店" Visible="false"/>
                <Label ItemCssClass="mt-o" Text="完成授权后，才能获取并选择" CssStyle="color:#********;height: 12px;"
                       CssClass="mr0" Visible="false"/>
                <HBlock>
                    <Button CssStyle="text-align: center; font-size:11px;border-radius:4px;" CssClass="mr0 pd0"
                            Text="启用" OnClick="doOpenEshop" Width="35" Height="20" Enabled="eshoporder.eshop.open"/>
                    <Button CssStyle="text-align: center; font-size:11px;border-radius:4px;margin-left:10px"
                            CssClass="mr10 pd0" Text="停用" OnClick="doStopEshop" Width="35" Height="20"
                            Enabled="eshoporder.eshop.stop"/>
                    <Button CssStyle="text-align: center; font-size:11px;border-radius:4px;margin-left:10px"
                            CssClass="mr10 pd0" Text="刷新子店" OnClick="doStopEshop" Visible="false" Width="65"
                            Height="20" Enabled="eshoporder.eshop.stop"/>
                </HBlock>
                <Grid ID="grid" Height="150" Width="950" DefaultRowCount="1" ReadOnly="false"
                      DataSource="${eshopBranchList}" NeedRowIndex="true">
                    <MultiSelectColumn ReadOnly="false"/>
                    <SwitchButtonColumn DataField="enabled" AllowStretch="true" Caption="是否启用"
                                        CssClass="MySwitchColumn"
                                        CheckedValue='true'/>
                    <TextColumn Caption="平台商户ID" DataField="eshopAccount" ReadOnly="true" AllowStretch="true"/>
                    <TextColumn Caption="平台商户名称" DataField="fullname" ReadOnly="true" AllowStretch="true"/>
                </Grid>
            </FlowPanel>
            <FlowPanel ID="BranchStorePanelMT" CssClass="pd10" LayoutDirection="Vert" Visible="false">
                <Label ItemCssClass="mt-o" CssStyle="font-weight: bold;height: 12px;" CssClass="mr0"
                       Text="授权连锁的子店"/>
                <Label ItemCssClass="mt-o" Text="请添加后，完成订购并授权" CssStyle="color:#********;height: 12px;"
                       CssClass="mr0"/>
                <Button CssStyle="text-align: center; font-size:11px;border-radius:4px;" CssClass="mr0 pd0" Text="导入"
                        OnClick="doExport" Width="35" Height="20" Enabled="eshoporder.eshop.open"/>
                <Grid ID="MTgrid" Height="150" Width="950" DefaultRowCount="1" ReadOnly="false"
                      DataSource="${eshopBranchList}" NeedRowIndex="true">
                    <MultiSelectColumn Caption="" AllowConfig="false" AllowFilter="false" DataField="select"
                                       ReadOnly="false"
                                       ShowHeaderCheckBox="true" Width="40" AllowSort="false" DisplayNull="false"
                                       ReportVisible="false"/>
                    <IconColumn Caption='操作列' DataField='icon' ListItems='=aicon-zengjia,=aicon-jian'
                                OnClick="doIconColumnClick"/>
                    <TextColumn Caption="APP方门店ID" DataField="eshopAccount" AllowStretch="true" MaxLength="30"
                                OnClick="doEditEshopAccount" Enabled="false"/>
                    <TextColumn Caption="子店名称" DataField="fullname" AllowStretch="true" MaxLength="100"
                                OnChange="OnEshopBranchFullNameChange"/>
                    <DynamicButtonColumn Width="180" TextAlign="Left" Caption="订购" DataField="operation"
                                         OnButtonClick="doOrder"
                                         LayoutDirection="Horz" AllowStretch="true" ReadOnly="true" AllowSort="false"
                                         AllowConfig="false"
                    />
                    <DynamicButtonColumn Width="180" TextAlign="Left" Caption="授权" DataField="auth"
                                         OnButtonClick="doAuth"
                                         LayoutDirection="Horz" AllowStretch="true" ReadOnly="true" AllowSort="false"
                                         AllowConfig="false"
                    />
                </Grid>
            </FlowPanel>

            <Label ID="expandMore" CssClass="LabelBtn SkinColor" Text="展开更多设置" OnClick="onExpandMore"/>
            <Block ID="managementBlock" Visible="false">
                <Label ID="manageModeTitle" Text="管理模式" CssClass='LayoutGroupCaption' Tag="BaseInfoTopic"
                       Visible="false"/>
                <FlowPanel ID="manageModeBody1" CssClass='FlexSpace' ColSpan="2" Width="950" ItemLabelWidth="125"
                           Visible="${orderManageModeVisible}"
                           Enabled="${pageEnabled}">
                    <HPanel Visible="false">
                        <Label CssClass='tableLabel' Text="订单的发货流程"/>
                        <DropDownEdit ID="cbUseDeliverSendProcessWay"
                                      DataField="deliverProcessUsetype"
                                      SelectedIndex="0" DropDownStyle="DropDownList"
                                      ListItems="0=以网店配置为准,1=以仓储配置为准" Width="330" CssClass="mr0 pd0"/>
                        <Label CssClass='aicon-tishishuoming SkinColor' ID='orderDeliverProcessExplain'/>
                    </HPanel>
                    <DropDownEdit Label="发货流程方式" ID="sendProcessWay" LabelCssClass="MustCharLeft just-content-right"
                                  DataValueField="id" DataTextField="text" DataField="sendProcessWay"
                                  SelectedIndex="0" Visible="false"
                                  NullDisplayText="请选择发货流程方式" Required="true"/>
                    <DropDownEdit Label="订单管理方式" ID="processType" DataField="processType"
                                  NullDisplayText="请选择订单管理方式"
                                  Required="true"
                                  ListItems="0=线下订单管理,1=网店订单管理" Enabled="false"
                                  DropDownStyle="DropDownEdit" SelectedIndex="1" Visible="${orderManageModeVisible}"/>
                </FlowPanel>
            </Block>

            <Block ID="orderRefundEffective">
                <Label ID="orderEffective" Text='订单/售后处理时效' Tag="BaseInfoTopic" CssClass='LayoutGroupCaption'/>
                <FlowPanel ID="orderEffectivePanel" ColSpan='2' ItemLabelWidth="125" Width="950"
                           Visible="${!powers.isWholesale}">
                    <DropDownEdit Label="发货时效" ID="deliverDuration" DataField="deliverDuration"
                                  NullDisplayText="请选择发货时效"
                                  LabelCssClass="MustCharLeft just-content-right" Required="true"
                                  ListItems="-1=无时效,12=12小时,24=24小时,48=48小时,72=72小时"
                                  DropDownStyle="DropDownList" MaxLength="4"
                    />
                    <HPanel Width="475">
                        <Label Text="揽收时效" CssClass="MustCharLeft tableLabel just-content-right"/>
                        <DropDownEdit ID="promisedCollectDuration" DataField="promisedCollectDuration"
                                      NullDisplayText="请选择揽收时效" Width="150"
                                      LabelCssClass="MustCharLeft just-content-right" Required="true"
                                      ListItems="-1=无时效,12=12小时,24=24小时,48=48小时,72=72小时"
                                      DropDownStyle="DropDownList" MaxLength="4"
                                      Visible="${!powers.isWholesale}"/>
                        <DropDownEdit DataField="promisedCollectDurationConfig"
                                      ListItems="0=以付款时间计算,1=以发货时间计算" SelectedIndex="0"
                                      DropDownStyle="DropDownList"
                                      Visible="${!powers.isWholesale}" Width="150"/>
                    </HPanel>
                    <DropDownEdit Label="售后处理时效" ID="refundPromisedAgreeDuration"
                                  DataField="refundPromisedAgreeDuration"
                                  NullDisplayText="请选择签收时效"
                                  LabelCssClass="MustCharLeft just-content-right" Required="true"
                                  ListItems="-1=无时效,12=12小时,24=24小时,48=48小时,72=72小时"
                                  DropDownStyle="DropDownList" MaxLength="4"
                                  Visible="${!powers.isWholesale}"/>
                    <HPanel>
                        <Label Text="时效即将超时提前" CssClass="MustCharLeft tableLabel just-content-right"/>
                        <DropDownEdit ID="mentionDeliverDuration" DataField="mentionDeliverDuration"
                                      NullDisplayText="请选择时效提醒" Width="280"
                                      LabelCssClass="MustCharLeft just-content-right" Required="true"
                                      ListItems="-1=无时效,1=1小时,2=2小时,3=3小时,4=4小时,5=5小时,6=6小时,8=8小时,10=10小时"
                                      DropDownStyle="DropDownList" MaxLength="4"/>
                        <Label Text='提醒'/>
                    </HPanel>
                </FlowPanel>
            </Block>

            <HBlock ID="customerSupply" Width="950">
                <Label ID="topicCWGL" Text='客户供应' Tag="BaseInfoTopic" Visible="${kehugongyingVisible}"
                       CssClass='LayoutGroupCaption'  Width="96"/>
                <VPanel CssClass='switchPanel' Visible="${kehugongyingVisible}">
                    <HPanel CssClass='switchHPanel'>
                        <SwitchButton Width="40" ID="ptypeAutoUploadBtn" DataField="ptypeAutoUploadEnabled"
                                      Visible="${datasource.ptypeAutoUploadEnabled}" OnChange="doAutoOnChange"
                                      CheckedValue="true" CssClass="switchHPanel"/>
                        <HPanel CssClass="switchHPanel">
                            <Label Text="自动同步本地商品到平台" Visible="${datasource.ptypeAutoUploadEnabled}"/>
                            <Label Text='系统定时同步本地商品资料到平台' FontSize="12" CssClass='tipLabel'
                                   Visible="${datasource.ptypeAutoUploadEnabled}"/>
                        </HPanel>
                    </HPanel>
                    <HPanel  CssClass='switchHPanel' Visible="${btypeAutoUploadEnabled}">
                        <SwitchButton Width="40" ID="btypeAutoUploadBtn" DataField="btypeAutoUploadEnabled"
                                      Visible="${btypeAutoUploadEnabled}" OnChange="doAutoBtypeOnChange"
                                      CheckedValue="true"  CssClass="switchHPanel"/>
                        <HPanel  CssClass="switchHPanel">
                            <Label Text="自动同步本地往来单位到平台" Visible="${btypeAutoUploadEnabled}"/>
                            <Label Text='系统定时同步本地往来资料到平台' FontSize="12" CssClass='tipLabel'
                                   Visible="${btypeAutoUploadEnabled}"/>
                        </HPanel>
                    </HPanel>
                    <HPanel CssClass='switchHPanel' Visible="${datasource.ktypeAutoUploadEnabled}">
                        <SwitchButton Width="40" ID="ktypeAutoUploadBtn" DataField="ktypeAutoUploadEnabled"
                                      Visible="${datasource.ktypeAutoUploadEnabled}" OnChange="doAutoOnChange"
                                      CheckedValue="true" CssClass="switchHPanel"/>
                        <Label Text='系统定时同步本地仓库资料到平台' FontSize="12" CssClass='tipLabel switchHPanel'
                               Visible="${datasource.ktypeAutoUploadEnabled}" />
                    </HPanel>
                </VPanel>
            </HBlock>

            <Block ID="customerSupplier" CssClass="flexItem" Visible="${customerBtypeVisible}">
                <Label ID="topicCWGL8" Text='客户/供应商' Tag="BaseInfoTopic" CssClass='LayoutGroupCaption'
                       Visible="${customerBtypeVisible}" Width="96"/>
                <VPanel CssClass='FlexSpace switchPanel' Visible="${customerBtypeVisible}">
                    <HPanel CssClass='switchHPanel'>
                        <SwitchButton Width="40" ID="autoCreateBtypeEnabled" DataField="autoCreateBtypeEnabled"
                                      CheckedValue="true"/>
                        <Label Text='自动创建往来单位'/>
                    </HPanel>
                    <Label Text="订单分销商/买家账号未查询到对应关系时自动创建往来单位并对应" FontColor="#808080"
                           Visible="${customerBtypeVisible}"/>
                </VPanel>
                <Block CssClass="flexItem" Visible="false">
                    <Block CssClass="flexBg" CssStyle="flex-direction:column">
                        <Block>
                            <FlowPanel CssClass='FlexSpace'>
                                <VPanel>
                                    <Label Text='默认匹配本地买家账号同名往来单位'/>
                                    <Label Text="1:勾选了配置，订单处理订单的往来单位逻辑和以前保持一致"
                                           FontColor="#808080"/>
                                    <Label Text="2:不勾选配置，订单默认不匹配本地同名往来单位" FontColor="#808080"/>
                                </VPanel>
                                <Block CssClass="Flex1"/>
                                <SwitchButton Width="40" ID="matchLocalSameBtypeEnable"
                                              DataField="matchLocalSameBtypeEnable"
                                              CheckedValue="true"/>
                            </FlowPanel>
                        </Block>
                    </Block>
                </Block>
            </Block>

            <Block ID="shopProduct" CssClass="flexItem" Visible="${showSkuMemo||specUnitDownloadEnabledVisible}">
                <Label Text='网店商品' Tag="BaseInfoTopic" CssClass='LayoutGroupCaption' Width="96"
                       Visible="${showSkuMemo||specUnitDownloadEnabledVisible}"/>
                <FlowPanel CssClass='FlexSpace switchPanel' Visible="${showSkuMemo}">
                    <SwitchButton Width="40" ID="memo" DataField="skuMemoDesired" CheckedValue="true"
                                  Visible="${showSkuMemo}"/>
                    <Label Text='将网店属性备注作为商品属性处理订单发货'/>
                </FlowPanel>
                <FlowPanel CssClass='FlexSpace switchPanel' Visible="${specUnitDownloadEnabledVisible}">
                    <SwitchButton Width="40" ID="specUnitDownloadEnabled" DataField="specUnitDownloadEnabled"
                                  CheckedValue="true"/>
                    <Label Text="将网店规格单位作为商品属性处理订单发货"/>

                </FlowPanel>
            </Block>

            <Block ID="topicOrder" CssClass="flexItem">
                <Label Text='订单' Tag="BaseInfoTopic" CssClass='LayoutGroupCaption' Width="96"/>
                <VPanel>
                    <FlowPanel CssClass='switchPanel' Visible="${allowTaobao}">
                        <Label Text='下载订单类型:'/>
                        <CheckBoxList DataField="downloadOrderType" ID="downloadOrderType" DataValueField='value'
                                      EnabledField="eb"
                                      DataTextField='text' LayoutDirection="Horz" ShowBorder="false"
                                      DataSource="${downloadOrderTypelist}"/>

                    </FlowPanel>
                    <FlowPanel CssClass='FlexSpace switchPanel' Visible="${allowDistributionHandle}">
                        <HPanel>
                            <SwitchButton Width="40" ID="isDistributionHandle" DataField="usePriceStrategy"
                                          CheckedValue="true"/>
                            <Label Text="平台未提供金额的订单，按销售价格策略的获取订单金额"/>
                        </HPanel>

                    </FlowPanel>

                    <Block ID="preSaleBLock" CssClass="mr0 pd0" Visible="true">
                        <VPanel CssStyle="margin-top:12px; margin-left:10px">
                            <Hpanel>
                                <Label Text='预售/周期购订单，预计提前'/>
                                <NumberEdit ID="planSendTimeDuration" FormatNumber="true" MinValue="0"
                                            MaxValue="9999999"
                                            DataField="planSendTimeDuration"
                                            NumberType="PositiveInt" Width="60"
                                            OnChange="showPlanSendTimeDuration"/>
                                <Label Text='小时发货'/>
                                <Label ID="planSendTimeDurationShow" Text="" FontColor="#808080"/>
                            </Hpanel>
                            <Label FontColor="#808080"
                                   Text='  买家下单预售/周期购订单，需要以预约签收时间计算商家预计发货时间。如果是在获取订单时平台返回了预计发货时间，则以平台返回预计发货时间为准'/>
                        </VPanel>
                    </Block>
                    <!-- 平台订单寻仓 放到订单分类下-->
                    <FlowPanel ID="xunCang1" CssClass='FlexSpace switchPanelMarginLeft'
                               Visible="${autoPickKtypeVisible}">
                        <HBlock>
                            <SwitchButton Width="40" ID="sbAutoPickKtype" DataField="autoPickKtype"
                                          OnChange="doAutoPickKtypeChange" CheckedValue="true"
                                          CssStyle="margin-top:8px"/>
                            <HPanel>
                                <Label Text='开启自动寻仓' FontSize="14"/>
                                <Label Text='开启寻仓策略时，请先完成' FontSize="12" CssClass='tipLabel'/>
                                <Label Text='平台仓对应' FontSize="12" FontColor="blue" OnClick="doOpenStockMapping"/>
                            </HPanel>
                        </HBlock>
                    </FlowPanel>
                    <FlowPanel ID="xunCang2" ColSpan='1'
                               ItemLabelWidth="125"
                               Width="500" CssClass='FlowTable switchPanelMarginLeft'
                               Visible="${autoPickKtypeVisible}">
                        <DropDownEdit ID="dpPickKtypeStrategyType" DataField="pickKtypeStrategyType"
                                      Label="寻仓规则："
                                      LabelCssClass="MustCharLeft just-content-right"
                                      ListItems="0=按仓库可发货库存寻仓,2=全部寻仓到指定仓库并且检查可发货库存"
                                      SelectedIndex="0"
                                      DropDownStyle="DropDownList" OnChange="onPickKtypeStrategyTypeChange"
                                      Required="true"
                                      Width="300"/>

                        <DropDownCheckBoxList ID="dpKtypeRangeStr" Label="寻仓范围：" DataField="ktypeRangeStr"
                                              LabelCssClass="MustCharLeft just-content-right" Required="true"
                                              DropDownStyle="DropDownSearch"
                                              DataSource="${platformKtypeList}" MaxWidth="0"
                                              DataTextField="platformStoreName"
                                              DataValueField="platformStoreStockId"
                                              LayoutDirection="Vert" Width="300"/>
                        <DropDownEdit ID="deDefaultStrategyType" DataField="defaultStrategyType"
                                      Label="库存不足订单处理方式："
                                      LabelCssClass="MustCharLeft just-content-right" ListItems="0=寻仓失败,1=不寻仓,2=寻仓到指定仓库"
                                      SelectedIndex="0"
                                      DropDownStyle="DropDownList" OnChange="onDefaultStrategyTypeChange"
                                      Required="true"
                                      Width="300"/>

                        <DropDownEdit ID="deDefaultKtype" DataField="defaultKtype" Label="固定仓库："
                                      LabelCssClass="MustCharLeft just-content-right" Visible="false"
                                      DataSource="${platformKtypeList}" DataTextField="platformStoreName"
                                      DataValueField="platformStoreStockId"
                                      DropDownStyle="DropDownList" Required="true" Width="300"/>
                    </FlowPanel>
                    <!--发票-->
                    <VPanel CssClass='switchPanel' Visible="${invoiceUploadEnabled}">
                        <HPanel>
                            <SwitchButton Width="40" ID="invoiceUploadEnabled" DataField="invoiceUploadEnabled"
                                          Visible="${invoiceUploadEnabled}" OnChange="doInvoiceOnChange"
                                          CheckedValue="true"/>
                            <Label Text="开票后上传发票到平台" Visible="${invoiceUploadEnabled}"/>
                        </HPanel>
                        <Label Text='在给买家订单开发票后，自动回传平台' FontSize="12" CssClass='tipLabel'
                               Visible="${invoiceUploadEnabled}"/>
                    </VPanel>
                    <!-- QIC -->
                    <FlowPanel Visible="${allowQicConfig}" CssClass="switchPanelMarginLeft">
                        <SwitchButton Width="40" ID="platformQualityStatus" DataField="platformQualityStatus"
                                      OnChange="changeQualityStatus" CheckedValue="true"/>
                        <Label Text="QIC订单默认质检信息"/>
                        <Label Text="视频号的QIC订单，将默认这些信息作为发货质检信息。" FontColor="#808080"/>
                    </FlowPanel>
                </VPanel>
            </Block>

            <Block ID="eshopQicConfig" Visible="false">
                <!-- QIC -->
                <FlowPanel ColSpan='2' ItemLabelWidth="125" Width="950" CssStyle="margin-left:0px">
                    <SelectorEdit ID="qualityWarehouse" Label="质检仓库:" OnButtonClick="loadQualityWarehouse"
                                  DataField="eshopQicConfig.platformQualityWarehouseName"
                                  OnSelectorSelected="doChooseWarehouse"/>
                    <SelectorEdit ID="qualityOrg" Label="质检机构:" OnButtonClick="loadQualityOrg"
                                  DataField="eshopQicConfig.platformQualityOrgName"
                                  OnSelectorSelected="doChooseOrg"/>
                    <DropDownEdit ID="refundInterception" Label="售后拦截策略:" SelectedIndex="0"
                                  DropDownStyle="DropDownList"
                                  DataField="eshopQicConfig.platformQualityRefundInterceptionCode"
                                  ListItems="0=发生售后即拦截,1=部分退款不拦截"/>
                    <TextEdit Label="质检机构收货地址:" ID="address"
                              DataField="eshopQicConfig.platformQualityReceiveAddress" Enabled="false"/>

                    <DropDownEdit ID="qualityBtype" Label="首发发货快递物流:"
                                  DataField="eshopQicConfig.platformQualityBtypeCode"
                                  OnChange="setProductDataSource" DropDownStyle="DropDownList"
                                  DataTextField="name"
                                  DataValueField="id"/>

                    <DropDownEdit ID="qualityBtypeProduct" Label="发货快递物流产品:"
                                  DataField="eshopQicConfig.platformQualityBtypeProduct"
                                  OnChange="setInsureVisible"
                                  DropDownStyle="DropDownList" DataTextField="name" DataValueField="id"/>

                    <CheckBox ID="qualityBtypeInsure" Label="保价:"
                              DataField="eshopQicConfig.platformQualityBtypeInsure" Visible="false"
                              OnChange="doQualityBtypeInsureChange"/>
                    <NumberEdit ID="qualityBtypeInsureTotal" Label="保价金额:" Visible="false" MinValue="0"
                                DecimalScale="2"
                                DataField="eshopQicConfig.platformQualityBtypeInsureTotal"/>
                    <DropDownEdit ID="qualityBtypeInsureType" Label="保价类型:" Visible="false"
                                  SelectedIndex="0"
                                  DropDownStyle="DropDownList"
                                  DataField="eshopQicConfig.platformQualityBtypeInsureType"
                                  ListItems="" DataTextField="name" DataValueField="id"/>
                    <DropDownEdit ID="qualityBtypeBackup" Label="备用发货快递物流:"
                                  DataField="eshopQicConfig.platformQualityBtypeCodeBackup" ListItems=""
                                  OnChange="setBackupProductDataSource" DropDownStyle="DropDownList"
                                  DataTextField="name"
                                  DataValueField="id"/>

                    <DropDownEdit ID="qualityBtypeBackupProduct" Label="备用发货快递物流产品:"
                                  DataField="eshopQicConfig.platformQualityBtypeBackupProduct"
                                  OnChange="setBackupInsureVisible" DropDownStyle="DropDownList"
                                  DataTextField="name"
                                  DataValueField="id"/>

                    <CheckBox ID="qualityBtypeBackupInsure" Label="保价:" Visible="false"
                              DataField="eshopQicConfig.platformQualityBtypeBackupInsure"
                              OnChange="doQualityBtypeBackupInsureChange"/>
                    <NumberEdit ID="qualityBtypeBackupInsureTotal" Label="保价金额:" DecimalScale="2"
                                MinValue="0"
                                Visible="false"
                                DataField="eshopQicConfig.platformQualityBtypeBackupInsureTotal"/>
                    <DropDownEdit ID="qualityBtypeBackupInsureType" Label="保价类型:" SelectedIndex="0"
                                  Visible="false"
                                  DropDownStyle="DropDownList"
                                  DataField="eshopQicConfig.platformQualityBtypeBackupInsureType" ListItems=""
                                  DataTextField="name" DataValueField="id"/>
                </FlowPanel>
                <FlowPanel ColSpan='1' ItemLabelWidth="125" Width="950" CssStyle="margin-left:0px">
                    <MemoEdit Label="备注:" ID="qualityComment" CssClass='FlexAuto'
                              NullDisplayText="请输入备注" DataField="eshopQicConfig.platformQualityComment"
                              MaxLength="200" Width="795" Height="50"/>
                </FlowPanel>
            </Block>

            <Block ID="topicRefund" CssClass="flexItem" Visible="${refundVisible}">
                <Label Text='售后' Tag="BaseInfoTopic" CssClass='LayoutGroupCaption' Width="96"
                       Visible="${refundVisible}"/>
                <VPanel>
                    <VPanel ID="messageCheck" CssClass='FlexSpace switchPanel'
                            Visible="${allowProcessRefundOnline}">
                        <HPanel>
                            <SwitchButton Width="40" ID="needMessageCheck" DataField="needMessageCheck"
                                          CheckedValue="true"/>
                            <Label Text='同意退款无需短信验证'/>
                        </HPanel>
                        <Label CssClass="aicon-question-circle" CssStyle="color:#555555;"
                               ID='downloadConfig'
                               Hint='开启后可能产生商家资损，请谨慎开启'/>
                    </VPanel>
                    <VPanel CssClass='FlexSpace switchPanel' Visible="${agVisible}">
                        <HPanel>
                            <SwitchButton Width="40" ID="agEnabled" DataField="agEnabled" CheckedValue="true"/>
                            <Label Text='开启极速退款'/>
                        </HPanel>
                        <Label Text='支持平台极速退款业务:PG、AG、SA、售后小助手等' FontSize="12" CssClass='tipLabel'/>
                    </VPanel>
                    <VPanel CssClass='FlexSpace switchPanel' Visible="${synFreightVisible}">
                        <HPanel>
                            <SwitchButton Width="40" ID="reissiueSyncFreight" DataField="reissiueSyncFreight"
                                          CheckedValue="true"/>
                            <Label Text='手工创建的换货/补寄售后单支持同步物流信息'/>
                        </HPanel>
                        <Label Text='开启配置后，售后管理手工创建的换货/补发售后单，生单发货后，可以通过平台补寄接口同步物流信息到平台订单。'
                               FontSize="12"
                               CssClass='tipLabel'/>
                    </VPanel>
                </VPanel>
            </Block>

            <Block ID="topicShip" CssClass="flexItem">
                <Label Text='发货' Tag="BaseInfoTopic" CssClass='LayoutGroupCaption'
                       Visible="true" Width="96"/>
                <VPanel CssClass='FlexSpace switchPanel' Visible="true">
                    <Block >
                        <CheckBox ID="subscribeLogistics" DataField="subscribeLogistics"
                                  OnChange="doSubscribeLogisticsChange"/>
                        <Label Text='物流轨迹监控'/>
                    </Block>
                    <Block  Visible="${allowSplitSendOrder}">>
                        <Label Text='多子单/多包裹订单同步发货' CssStyle="margin-right:5px"/>
                        <DropDownEdit ID="rdSplitSendTag"
                                      Value="${splitSendTagValue}" Width="320"
                                      SelectedIndex="0" DropDownStyle="DropDownList"
                                      ListItems="0=按实际发货情况处理,1=发货订单内任意商品，视为全部发货,3=最后一个子单/包裹发货，同步所有订单网店商品明细发货"/>
                    </Block>
                    <Block Visible="${isShowSelfDeliveryMode}">
                        <Label Text='自配订单交货方式' CssStyle="margin-right:5px"/>
                        <DropDownEdit ID="selfDeliveryMode" LabelCssClass="MustCharLeft just-content-right"
                                      DataValueField="id" DataTextField="text" DataField="selfDeliveryMode"
                                      NullDisplayText="请选择" Required="true" Width="150"
                        />
                    </Block>
                    <Block  Visible="${storeProcessVisible}">
                        <FlowPanel>
                            <SwitchButton Width="40" ID="storeProcessUpload" DataField="storeProcessUpload"
                                          CheckedValue="true"/>
                            <Label Text='仓储作业节点回传'/>
                        </FlowPanel>
                        <Label Text='开启后将平台订单在仓储作业的节点实时回传平台' FontSize="12"
                               CssClass='tipLabel'/>
                    </Block>
                </VPanel>

            </Block>
            <Label ID="showLess" CssClass="LabelBtn SkinColor" Text="收起更多设置" OnClick="onShowLess"/>

        </FlexColumn>
    </FlexBlock>

    <FlowPanel CssClass="BottomBlock" ID="flbBottomBody">
        <Block CssClass='Flex1'/>
        <Button ID="btnSave" CssClass='SpecialButton' Text="保存" OnClick="doBtnSave"/>
        <CancelButton ID="closeForm" AccessKey="X" Text="关闭"/>
        <Block CssClass='Flex1'/>
    </FlowPanel>
    <PopupBlock ID='downloadConfigView' Width='560' CssClass="htmlTip">
        <FlexColumn CssClass="htmlTip">
            <Label Text="开启后可能产生的商家资损，请谨慎开启" Height="20" CssStyle="margin-top:16px;height:19px;"/>
        </FlexColumn>
    </PopupBlock>
    <PopupBlock ID='orderTimingExplainView' CssClass="htmlTipBlock" Width='460'>
        <FlexColumn CssClass="htmlTip">
            <Label Text='时效说明' CssStyle='font-weight:bold;font-size:14px;'/>
            <Label CssClass='p' Text='订单时效优先以下载订单时线上网店接口返回时效为准'/>
            <Label CssClass='p'
                   Text='当线上网店未返回时效，系统做了设置后，会根据订单付款时间以及系统设置时效进行计算 。'/>
            <Image Src='sale/eshoporder/image/shili.png' Width="37" CssStyle="z-index:10" Height="37"/>
            <VBlock CssStyle="margin-top:-42px;background: #F5F6F8;" Width="440">
                <Label CssStyle="margin-left:30px;line-height:20px;" Text='${html1}'/>
                <Label CssStyle="margin-left:30px;line-height:14px;" Text='${html2}'/>
            </VBlock>
            <Label CssClass='p' Text='时效提醒根据订单设置时效到达之前多久进行提醒。'/>
            <Image Src='sale/eshoporder/image/shili.png' CssStyle="z-index:10" Width="37" Height="37"/>
            <VBlock CssStyle="margin-top:-42px;background: #F5F6F8;">
                <Label CssStyle="margin-left:30px;line-height:20px;" Text='${html3}'/>
            </VBlock>

        </FlexColumn>
    </PopupBlock>
    <PopupBlock ID='refundTimingExplainView' CssClass="htmlTipBlock" Width='526'>
        <FlexColumn CssClass="htmlTip">
            <Label Text='时效说明' CssStyle='font-weight:bold;font-size:14px;'/>
            <Label CssClass='p' Text='启用售后时效，系统会根据售后单在网店创建时间与系统设置时效进行计算。'/>
            <Image Src='sale/eshoporder/image/shili.png' CssStyle="z-index:10" Width="37" Height="37"/>
            <VBlock CssStyle="margin-top:-42px;background: #F5F6F8;" Width="440">
                <Label CssStyle="margin-left:30px;line-height:20px;" Text='${html4}'/>
                <Label CssStyle="margin-left:30px;line-height:14px;" Text='${html5}'/>
            </VBlock>
        </FlexColumn>
    </PopupBlock>
    <PopupBlock ID="orderDeliverProcessExplainView" CssClass='FlexSpace' CssStyle="background:#F5F6F8;">
        <FlexColumn CssClass="htmlTip" CssStyle="padding-left:5px;">
            <VPanel CssStyle="padding-left:4px;">
                <Label Text='功能说明：订单处理，订单发货流程以订单上仓库配置为准。影响订单范围：' CssClass='tipLabel'
                       CssStyle="margin-left:4px;"/>
                <Label Text='  1.在原始订单，订单发货流程方式为简易仓储和仓储管理。提交进入订单处理界面会根据订单发货仓库配置单据发货流程更新订单发货流程'
                       CssClass='tipLabel'/>
                <Label Text='  2.在订单处理界面，修改订单发货仓库，会根据订单发货仓库配置单据发货流程更新订单发货流程'
                       CssClass='tipLabel'/>
                <Label Text='  3.手工修改订单发货流程方式不受仓库和网店发货流程方式控制'
                       CssClass='tipLabel'/>
            </VPanel>
        </FlexColumn>
    </PopupBlock>

    <CustomControl ID='import'>
        <Label Text="导入说明："/>
        <Label Text="1、导入文件需要录入 app 方门店 ID 和子网店名称。"/>
        <Label Text="2、app 方门店 ID 和子网店名称，任意一个已经存在了，都无法导入该条数据。"/>
        <SpeedButton CssStyle="text-align: left;" TextColor="Blue" Text="下载模板" OnClick="downloadRealtionModel"
                     Hint="下载系统默认支持的模板"/>
        <FileUpload WebMethod="sale/eshoporder/eshop/importBranchEshop">
            <HPanel ID="fileUpload">
                <FileEdit Label="选择文件：" ID="importFile" Accept=".xls,.xlsx" DataField="importFile" Width="200"/>
                <Button ID="submitBtn" Text="开始上传" OnClick="uploadFile" Tag="fileUpload"/>
            </HPanel>
        </FileUpload>
        <HBlock CssClass='BottomBlock'/>
        <HBlock>
            <ProgressBar ID="processBar" Value="0"/>
        </HBlock>
        <MemoEdit ID="info" Height="200" Width="100%" Rows="10000" ReadOnly="true" TabStop="true"
                  CssStyle="border: 0.5px solid;border-color: #********;text-indent:0;padding-left:5px"/>
    </CustomControl>

    <Style>
        .SubmitConfigBtn{
        cursor: -webkit-zoom-in;
        }
        .SubmitConfigBtn:hover{
        color: #2288fc;
        }
        .htmlTip{background-color:#fff;padding:10px;color:#333;border-radius:4px;}
        .htmlTip .p{line-height:20px;}
        .htmlTip .p:before{content:"
        ";width:6px;height:6px;border-radius:3px;background-color:#ccc;display:inline-block;margin-right:10px;vertical-align:middle;}
        .htmlTip .bg2{background-color:#FEFAF6;padding:10px;color:#EAA957;}

        .top .LayoutGroupCaption {
        margin-top: 0;
        }
        .top {
        overflow: unset;
        }
        .flexItem{display:flex;margin-bottom:24px;}
        .flexItem .top{padding-right:0;margin-right:4px;width:120px;}
        .flexBg{background-color:#F5F6F8;display:flex;flex:auto;padding:8px 13px;border-radius:4px;}

        .FormCaptionText{
        font-family: PingFang-SC-Bold;
        font-size: 16px;
        font-weight: 700;
        color: #333333;
        }

        .FlowCaption{
        font-family: PingFangSC-Semibold;
        font-size: 14px;
        color: #333333;
        font-weight: 600;
        }
        .OtherTipBox { padding: 10px 10px 20px 20px; position: relative; margin-top: 20px; border: 1px dashed #7bcbff;
        min-width: 200px; min-height: 60px; box-shadow: 3px 4px 2px 4px #e2f1ff; border-radius: 4px; }
        .OtherTipBox .Arrow1 { content: ' '; border-top: 1px dashed #7bcbff; width: 16px; position: absolute; right:
        10%; top: -8px; height: 1px; transform: rotate(45deg); height: 12px; background: #fff; }
        .OtherTipBox .Arrow2{ content: ' '; border-top: 1px dashed #7bcbff; width: 16px; position: absolute; right: 10%;
        top: -8px; transform: rotate(-45deg); margin-right: 4px; background: #fff; height: 12px; }
        .OtherTipBox .Title { font-weight: bold; line-height: 40px; }
        .OtherTipBox .Label { color: #999; }
        .selBox .selTitle{
        max-width: 100%;
        margin-bottom: 5px;
        text-align: right;
        width: 130px;
        font-size: 14px;
        }
        .selBox .selBtns{
        margin-top: 10px;
        }
        .selBox .selBtn{
        margin-right:20px;
        border-radius:50px;
        padding: 0px 16px;
        height: 36px;
        font-size: 13px;
        border: 0.5px solid;
        }
        .selBox .usuallyBtn{
        border-color: transparent;
        background-color:#f5f6f8;
        color: #666
        }
        .selBox .checked{
        background-color: #ebf3ff;
        }
        .selBox .selBtn label{
        margin:0;
        }
        .selBox .OtherTipBox{
        border: 1px solid #e1e1e1;
        box-shadow: unset;
        position: relative;
        width: 800;
        margin-bottom: 30px;
        }
        .selBox .OtherTipBox .Arrow1 {
        border-top: 1px solid #e1e1e1;
        }
        .selBox .OtherTipBox .Arrow2 {
        border-top: 1px solid #e1e1e1;
        }
        .OtherTipBox .Line{
        background-color: #a5a5a5;
        padding: 0;
        margin: 0;
        font-size: 0;
        overflow: hidden;
        height: 20px;
        width: 1px;
        position: absolute;
        top: 90px;
        left: 25px;
        z-index: 0;
        }
        .OtherTipBox .TipsBox{
        background-color: #fef6eb;
        margin-bottom: 20px;
        min-width: 600px;
        padding: 0px 5px;
        border-radius: 5px;
        }
        .OtherTipBox .TipsIcon{
        color: red;
        margin-right: 5px;
        }
        .OtherTipBox .Tipsdescription{
        color: #2b2a28;
        }
        .OtherTipBox .ProcessBox:not(:last-child) {
        margin-bottom: 25px;
        }
        .OtherTipBox .ProcessBox {
        height: 50px;
        position: relative;
        }
        .ProcessBox .ProcessIcon{
        position: absolute;
        }
        .ProcessBox .ProcessItem{
        position: absolute;
        left: 20px;
        color: #2b2a28;
        font-size: 13px;
        }
        .ProcessBox .ProcessBtn{
        position: absolute;
        top: 3px;
        left: 105px;
        width: 64px;
        height: 28px;
        line-height: 25px;
        padding: 0px 5px;
        font-size: 13px;
        }
        .ProcessBox .ProcessLabel{
        position: absolute;
        left: 105px;
        top: 30px;
        }
        .EshopAddPage .FlowPanel .FlowLabel {
        font-size: 13px
        }
        .selBtns .ChkBoxStyle input {
        top: 10px;
        left: 15px;
        }
        .Padding110
        {
        padding:0px 110px;
        }
        .htmlTipBlock .p{display:block;line-height:30px;height:auto;color:#333}
        .htmlTipBlock .p:before{content:"
        ";width:6px;height:6px;border-radius:3px;background-color:#ccc;display:inline-block;margin-right:10px;vertical-align:middle;}

        .block_wrapper {
        display: flex;
        width: 100%;
        justify-content: space-between;
        }
        .tableLabel{
        justify-content: right;
        Width: 150px;
        }
        .switchPanel{
        margin-left: 20px;
        align-items: center;
        margin-top: 12px;
        }
        .switchHPanel{
        margin-top: 6px;
        }
        .switchPanelMarginLeft{
        margin-left: 10px;
        }
        .just-content-right {
        justify-content: right !important;
        }
        .panelCss{
        padding-top:0px;
        padding-bottom:0px;
        }
        .panelItemCss{
        margin:10 0 0 0;
        padding:0;
        height:40px;
        }
        .FlowPanel.HasColSpan .FlowItem {
        margin-bottom:0px;
        }
    </Style>
</Page>
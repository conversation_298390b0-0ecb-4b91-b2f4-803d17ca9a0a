package com.wsgjp.ct.sale.tool.export;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.alibaba.fastjson.JSONArray;
import com.wsgjp.ct.ngp.export.sdk.BaseExport;
import com.wsgjp.ct.ngp.export.sdk.export.writer.ExcelWriter;
import com.wsgjp.ct.sale.biz.eshoporder.config.ExportConfig;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuPageData;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.product.QueryEshopSkuListRequest;
import com.wsgjp.ct.sale.biz.eshoporder.service.product.ProductManageService;
import ngp.utils.CollectionUtils;
import ngp.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

@Component
@ConditionalOnProperty(value = "eshoporder-tool-export.enabled", havingValue = "true")
public class EshopProductListExport extends BaseExport<QueryEshopSkuListRequest, EshopProductSkuPageData> {
    private final ExportConfig exportConfig;
    private final ProductManageService manageService;
    private static final Logger logger = LoggerFactory.getLogger(EshopProductListExport.class);

    public EshopProductListExport(ExportConfig exportConfig, ProductManageService manageService) {
        this.exportConfig = exportConfig;
        this.manageService = manageService;
    }

    @Override
    public Class<QueryEshopSkuListRequest> getQueryParamsType() {
        return QueryEshopSkuListRequest.class;
    }

    @Override
    public Class<EshopProductSkuPageData> getResultType() {
        return EshopProductSkuPageData.class;
    }

    @Override
    public void export(QueryEshopSkuListRequest queryEshopSkuListRequest,
                       ExcelWriter<EshopProductSkuPageData> excelWriter) throws Exception {
        logger.info("开始导出商品对应:{}", JsonUtils.toJson(getTask()));
        try {
            PageRequest<QueryEshopSkuListRequest> queryParams = new PageRequest<>();
            queryParams.setQueryParams(queryEshopSkuListRequest);
            int pageSize = exportConfig.getExportPageSize();
            queryParams.setPageSize(pageSize);
            int pageIndex = 1;
            PageResponse<EshopProductSkuPageData> page;
            while (true) {
                queryParams.setPageIndex(pageIndex);
                QueryEshopSkuListRequest params = queryParams.getQueryParams();
                queryParams.setQueryParams(params);
                page = manageService.querySkuList(queryParams, true);
                List<EshopProductSkuPageData> export = page.getList();
                if (CollectionUtils.isEmpty(export)) {
                    break;
                }
                List<EshopProductSkuPageData> eshopProductListForExportEntities
                        = JSONArray.parseArray(JSONArray.toJSONString(export), EshopProductSkuPageData.class);
                for (EshopProductSkuPageData entity : eshopProductListForExportEntities) {
                    //订单间隔时间
                    if (entity.getDownloadOrderIntervalDay().equals("-1")) {
                        entity.setDownloadOrderIntervalDay("");
                    }
                    //商品间隔时间
                    if (entity.getRefreshProductIntervalDay().equals("-1")) {
                        entity.setRefreshProductIntervalDay("");
                    }
                    if(entity.getPtypeId()==null || entity.getPtypeId().longValue()==0){
                        entity.setQty(null);
                    }
                }
                excelWriter.setCount((int) page.getTotal());
                excelWriter.write(eshopProductListForExportEntities);
                pageIndex++;
            }
            logger.info("导出商品对应成功,taskId:{},title:{}", getTask().getId(),getTask().getTitle());
        } catch (Exception e) {
            logger.error("导出商品对应出现异常:{}", e.getMessage(), e);
            e.printStackTrace();
            throw e;
        }
    }

    @Override
    public String getJobName() {
        return "product-list";
    }
}

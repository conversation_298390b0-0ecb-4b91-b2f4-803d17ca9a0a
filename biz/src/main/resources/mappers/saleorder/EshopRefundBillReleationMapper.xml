<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopRefundBillReleationMapper">
    <sql id="billReleationInfo">
        t1.id,t1.source_vchcode as refundOrderId,t1.target_vchcode as billVchcode,
        CASE
           WHEN t1.target_vchtype = 2101 OR t1.target_vchtype = 2100 THEN 1
           WHEN t1.target_vchtype = 4005 THEN 2
           WHEN t1.target_vchtype = 2001 OR t1.target_vchtype = 2000 THEN 3
           WHEN t1.target_vchtype = 3301 THEN 5
           WHEN t1.target_vchtype = 4001  THEN 6
           ELSE 4 END AS billType
        ,t1.create_time,t1.update_time
    </sql>

    <select id="getBillReleationList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundBillReleationEntity">
        SELECT
        t3.business_type,
        t3.create_type,
        t3.post_state,
        t3.process_type,
        ass.freight_btype_name as 'logisticsCompany',
        ass.freight_billno as 'logisticsNumber',
        <include refid="billReleationInfo"/>
        FROM td_bill_relation t1
        LEFT JOIN td_bill_core t3 on t1.target_vchcode=t3.vchcode and t1.profile_id=t3.profile_id and t3.deleted = 0
        left join acc_bill_assinfo ass on ass.vchcode = t3.vchcode and ass.profile_id = t3.profile_id
        where t1.source_vchcode=#{id} and t1.profile_id=#{profileId} and t3.vchcode is not null
        union all
        SELECT
        t2.business_type,
        t2.create_type,
        t2.post_state,
        t2.process_type,
        ass.freight_btype_name as 'logisticsCompany',
        ass.freight_billno as 'logisticsNumber',
        <include refid="billReleationInfo"/>
        FROM td_bill_relation t1
        LEFT JOIN acc_bill_core t2 on t1.target_vchcode=t2.vchcode and t1.profile_id=t2.profile_id and t2.deleted = 0
        left join acc_bill_assinfo ass on ass.vchcode = t2.vchcode and ass.profile_id = t2.profile_id
        where t1.source_vchcode=#{id} and t1.profile_id=#{profileId} and t2.vchcode is not null
    </select>
    <select id="getBillReleationListV2"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundBillReleationEntity">
        SELECT
        t1.vchcode billVchcode,t1.create_type,t1.business_type,t1.post_state
        FROM td_bill_core t1
        where t1.vchcode in
        <foreach collection="billVchcodeList" index="index" item="vchcode" open="(" separator="," close=")">
            #{vchcode}
        </foreach>
    </select>

    <select id="getBillReleationLimit1"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundBillReleationEntity">
        SELECT
        <include refid="billReleationInfo"/>
        FROM td_bill_relation t1
        where t1.target_vchcode=#{billVchcode} limit 1
    </select>
    <select id="getBillNumberByVchcodeList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.BillCoreEntity">
        SELECT vchcode, bill_number
        FROM acc_bill_core t1
        where t1.vchcode in
        <foreach collection="vchcodeList" index="index" item="vchcode" open="(" separator="," close=")">
            #{vchcode}
        </foreach>
    </select>
    <select id="getBuniessBillNumberByVchcodeList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.BillCoreEntity">
        SELECT vchcode, bill_number
        FROM td_bill_core t1
        where t1.vchcode in
        <foreach collection="vchcodeList" index="index" item="vchcode" open="(" separator="," close=")">
            #{vchcode}
        </foreach>
    </select>

    <select id="checkBillPost" resultType="java.lang.Integer">
        select count(*)
        from acc_bill_core
        where profile_id = #{profileId}
          and vchcode = #{vchcode}
          and post_state = 800
    </select>
    <select id="getReceiveCheckinVchcode" resultType="java.math.BigInteger">
        SELECT inout_id
        FROM pl_eshop_refund_receive_checkin
        where profile_id = #{profileId}
          and refund_order_id = #{refundOrderId}
          and create_type = #{createType}
    </select>

    <select id="getReceiveCheckinVchcodeNew" resultType="java.math.BigInteger">
        SELECT vchcode
        FROM pl_eshop_refund_receive_checkin
        where profile_id = #{profileId}
        and vchcode in
        <foreach collection="vchcodes" index="index" item="vchcode" open="(" separator="," close=")">
            #{vchcode}
        </foreach>
        and create_type=#{createType}
    </select>
    <select id="checkBillReleation" resultType="java.lang.Integer">
        select count(*) from td_bill_relation where profile_id=@profileid and td_bill_relation.source_vchcode in
        <foreach collection="refundVchcodes" index="index" item="vchcode" open="(" separator="," close=")">
            #{vchcode}
        </foreach>
    </select>
    <select id="checkTdBillRelation" resultType="java.lang.Integer">
        select count(*)
        from td_bill_relation
        where profile_id = #{profileId}
          and source_vchcode = #{sourceVchcode}
          and target_vchcode = #{targetVchcode}
    </select>
    <select id="getBillReleationListByVchcodes"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundBillReleationEntity">
        SELECT
        case when t2.business_type is null then t3.business_type else t2.business_type end business_type,
        case when t2.create_type is null then t3.create_type else t2.create_type end create_type,
        case when t2.post_state is null then t3.post_state else t2.post_state end post_state,
        case when t2.process_type is null then t3.process_type else t2.process_type end process_type,
        <include refid="billReleationInfo"/>
        FROM td_bill_relation t1
        LEFT JOIN acc_bill_core t2 on t1.target_vchcode=t2.vchcode and t1.profile_id=t2.profile_id
        left join td_bill_core t3 on t1.target_vchcode=t3.vchcode and t1.profile_id=t3.profile_id
        where t1.source_vchcode in
        <foreach collection="vchcodes" index="index" item="vchcode" open="(" separator="," close=")">
            #{vchcode}
        </foreach>
        and t1.profile_id=#{profileId}
    </select>
    <select id="getOrderReleationList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundBillReleationEntity" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity">
        select o.*, 3 as billType, trade_order_id as billNumber, o.business_type as businessType, o.id as billVchcode, relation.source_vchcode as refundOrderId
        from td_bill_relation relation
        left join pl_eshop_sale_order o on o.id = relation.target_vchcode and o.profile_id = relation.profile_id
        where relation.source_vchcode = #{id}
        and relation.profile_id = #{profileId}
        and relation.source_vchtype = 9801
        and relation.target_vchtype = 9802
        and o.local_trade_state != 5
    </select>
    <select id="getOrderReleationListBatch" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundBillReleationEntity">
        select *, 3 as billType, trade_order_id as billNumber, o.business_type as businessType, o.id as billVchcode, relation.source_vchcode as refundOrderId
        from td_bill_relation relation
        left join pl_eshop_sale_order o on o.id = relation.target_vchcode and o.profile_id = relation.profile_id
        where relation.source_vchcode in
        <foreach collection="ids" index="index" item="vchcode" open="(" separator="," close=")">
            #{vchcode}
        </foreach>
        and relation.profile_id = #{profileId}
        and relation.source_vchtype = 9801
        and relation.target_vchtype = 9802
        and o.local_trade_state != 5
    </select>
    <delete id="deleteReceiveCheckin">
        delete
        from pl_eshop_refund_receive_checkin
        where profile_id = #{profileId}
          and refund_order_id = #{refundOrderId}
          and create_type = #{createType}
    </delete>
    <delete id="deleteReceiveCheckinDetail">
        delete
        from pl_eshop_refund_receive_checkin_detail
        where profile_id = #{profileId}
        and vchcode IN
        <foreach collection="vchcodeList" index="index" item="vchcode" open="(" separator="," close=")">
            #{vchcode}
        </foreach>
    </delete>
    <delete id="deleteReceiveCheckinDetailCombo">
        delete
        from pl_eshop_refund_receive_checkin_detail_combo
        where profile_id = #{profileId}
        and vchcode IN
        <foreach collection="vchcodeList" index="index" item="vchcode" open="(" separator="," close=")">
            #{vchcode}
        </foreach>
    </delete>

</mapper>

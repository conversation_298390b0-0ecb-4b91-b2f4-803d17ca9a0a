<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.BaseOtypeMapper">
    <resultMap id="BaseResultMap" type="com.wsgjp.ct.sale.biz.eshoporder.entity.base.BaseOtype">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="profile_id" jdbcType="BIGINT" property="profileId"/>
        <result column="usercode" jdbcType="VARCHAR" property="usercode"/>
        <result column="fullname" jdbcType="VARCHAR" property="fullname"/>
        <result column="ocategory" jdbcType="TINYINT" property="ocategory"/>
        <result column="store_type" jdbcType="TINYINT" property="storeType"/>
        <result column="business_type" jdbcType="TINYINT" property="businessType"/>
        <result column="ktype_id" jdbcType="BIGINT" property="ktypeId"/>
        <result column="btype_id" jdbcType="BIGINT" property="btypeId"/>
        <result column="check_account_type" jdbcType="TINYINT" property="checkAccountType"/>
        <result column="currency_id" jdbcType="BIGINT" property="currencyId"/>
        <result column="atype_id" jdbcType="BIGINT" property="atypeId"/>
        <result column="memo" jdbcType="VARCHAR" property="memo"/>
        <result column="deleted" jdbcType="BOOLEAN" property="deleted"/>
        <result column="stoped" jdbcType="BOOLEAN" property="stoped"/>
        <result column="deliver_duration" jdbcType="INTEGER" property="deliverDuration"/>
        <result column="rowindex" jdbcType="BIGINT" property="rowindex"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="deliver_id" jdbcType="BIGINT" property="deliverId"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, profile_id, usercode, fullname, ocategory, store_type, business_type, ktype_id, 
    btype_id, check_account_type, currency_id, atype_id,
    memo, deleted, stoped, deliver_duration, rowindex, create_time, update_time, deliver_id
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from base_otype
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectOtypeByEhsopId" parameterType="java.math.BigInteger" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.BaseOtype">
        select
        <include refid="Base_Column_List"/>
        from base_otype
        where id = #{id}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from base_otype
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.BaseOtype"
            useGeneratedKeys="true">
    insert into base_otype (profile_id, usercode, fullname, 
      ocategory, store_type, business_type, 
      ktype_id, btype_id,  check_account_type, currency_id,
      atype_id,  memo,
      deleted, stoped, deliver_duration, 
      rowindex, create_time, update_time,
      deliver_id)
    values (#{profileId,jdbcType=BIGINT}, #{usercode,jdbcType=VARCHAR}, #{fullname,jdbcType=VARCHAR}, 
      #{ocategory,jdbcType=TINYINT}, #{storeType,jdbcType=TINYINT}, #{businessType,jdbcType=TINYINT}, 
      #{ktypeId,jdbcType=BIGINT}, #{btypeId,jdbcType=BIGINT}, #{classId,jdbcType=BIGINT}, 
      #{accountType,jdbcType=TINYINT}, #{checkAccountType,jdbcType=TINYINT}, #{currencyId,jdbcType=BIGINT}, 
      #{atypeId,jdbcType=BIGINT}, #{payAccount,jdbcType=VARCHAR}, #{memo,jdbcType=VARCHAR}, 
      #{deleted,jdbcType=BOOLEAN}, #{stoped,jdbcType=BOOLEAN}, #{deliverDuration,jdbcType=INTEGER}, 
      #{rowindex,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{deliverId,jdbcType=BIGINT})
  </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.BaseOtype" useGeneratedKeys="true">
        insert into base_otype
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="profileId != null">
                profile_id,
            </if>
            <if test="usercode != null">
                usercode,
            </if>
            <if test="fullname != null">
                fullname,
            </if>
            <if test="ocategory != null">
                ocategory,
            </if>
            <if test="storeType != null">
                store_type,
            </if>
            <if test="businessType != null">
                business_type,
            </if>
            <if test="ktypeId != null">
                ktype_id,
            </if>
            <if test="btypeId != null">
                btype_id,
            </if>
            <if test="checkAccountType != null">
                check_account_type,
            </if>
            <if test="currencyId != null">
                currency_id,
            </if>
            <if test="atypeId != null">
                atype_id,
            </if>
            <if test="memo != null">
                memo,
            </if>
            <if test="deleted != null">
                deleted,
            </if>
            <if test="stoped != null">
                stoped,
            </if>
            <if test="deliverDuration != null">
                deliver_duration,
            </if>
            <if test="rowindex != null">
                rowindex,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="deliverId != null">
                deliver_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="profileId != null">
                #{profileId,jdbcType=BIGINT},
            </if>
            <if test="usercode != null">
                #{usercode,jdbcType=VARCHAR},
            </if>
            <if test="fullname != null">
                #{fullname,jdbcType=VARCHAR},
            </if>
            <if test="ocategory != null">
                #{ocategory,jdbcType=TINYINT},
            </if>
            <if test="storeType != null">
                #{storeType,jdbcType=TINYINT},
            </if>
            <if test="businessType != null">
                #{businessType,jdbcType=TINYINT},
            </if>
            <if test="ktypeId != null">
                #{ktypeId,jdbcType=BIGINT},
            </if>
            <if test="btypeId != null">
                #{btypeId,jdbcType=BIGINT},
            </if>
            <if test="classId != null">
                #{classId,jdbcType=BIGINT},
            </if>
            <if test="accountType != null">
                #{accountType,jdbcType=TINYINT},
            </if>
            <if test="checkAccountType != null">
                #{checkAccountType,jdbcType=TINYINT},
            </if>
            <if test="currencyId != null">
                #{currencyId,jdbcType=BIGINT},
            </if>
            <if test="atypeId != null">
                #{atypeId,jdbcType=BIGINT},
            </if>
            <if test="payAccount != null">
                #{payAccount,jdbcType=VARCHAR},
            </if>
            <if test="memo != null">
                #{memo,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                #{deleted,jdbcType=BOOLEAN},
            </if>
            <if test="stoped != null">
                #{stoped,jdbcType=BOOLEAN},
            </if>
            <if test="deliverDuration != null">
                #{deliverDuration,jdbcType=INTEGER},
            </if>
            <if test="rowindex != null">
                #{rowindex,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deliverId != null">
                #{deliverId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.BaseOtype">
        update base_otype
        <set>
            <if test="profileId != null">
                profile_id = #{profileId,jdbcType=BIGINT},
            </if>
            <if test="usercode != null">
                usercode = #{usercode,jdbcType=VARCHAR},
            </if>
            <if test="fullname != null">
                fullname = #{fullname,jdbcType=VARCHAR},
            </if>
            <if test="ocategory != null">
                ocategory = #{ocategory,jdbcType=TINYINT},
            </if>
            <if test="storeType != null">
                store_type = #{storeType,jdbcType=TINYINT},
            </if>
            <if test="businessType != null">
                business_type = #{businessType,jdbcType=TINYINT},
            </if>
            <if test="ktypeId != null">
                ktype_id = #{ktypeId,jdbcType=BIGINT},
            </if>
            <if test="btypeId != null">
                btype_id = #{btypeId,jdbcType=BIGINT},
            </if>
            <if test="checkAccountType != null">
                check_account_type = #{checkAccountType,jdbcType=TINYINT},
            </if>
            <if test="currencyId != null">
                currency_id = #{currencyId,jdbcType=BIGINT},
            </if>
            <if test="atypeId != null">
                atype_id = #{atypeId,jdbcType=BIGINT},
            </if>
            <if test="memo != null">
                memo = #{memo,jdbcType=VARCHAR},
            </if>
            <if test="deleted != null">
                deleted = #{deleted,jdbcType=BOOLEAN},
            </if>
            <if test="stoped != null">
                stoped = #{stoped,jdbcType=BOOLEAN},
            </if>
            <if test="deliverDuration != null">
                deliver_duration = #{deliverDuration,jdbcType=INTEGER},
            </if>
            <if test="rowindex != null">
                rowindex = #{rowindex,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deliverId != null">
                deliver_id = #{deliverId,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.BaseOtype">
    update base_otype
    set profile_id = #{profileId,jdbcType=BIGINT},
      usercode = #{usercode,jdbcType=VARCHAR},
      fullname = #{fullname,jdbcType=VARCHAR},
      ocategory = #{ocategory,jdbcType=TINYINT},
      store_type = #{storeType,jdbcType=TINYINT},
      business_type = #{businessType,jdbcType=TINYINT},
      ktype_id = #{ktypeId,jdbcType=BIGINT},
      btype_id = #{btypeId,jdbcType=BIGINT},
      check_account_type = #{checkAccountType,jdbcType=TINYINT},
      currency_id = #{currencyId,jdbcType=BIGINT},
      atype_id = #{atypeId,jdbcType=BIGINT},
      memo = #{memo,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=BOOLEAN},
      stoped = #{stoped,jdbcType=BOOLEAN},
      deliver_duration = #{deliverDuration,jdbcType=INTEGER},
      rowindex = #{rowindex,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deliver_id = #{deliverId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
    <update id="updateOrderIndexByPrimaryKey" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.BaseOtype">
    update base_otype
    set
      rowindex = #{rowindex,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

</mapper>
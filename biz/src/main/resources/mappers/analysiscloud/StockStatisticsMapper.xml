<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.analysiscloud.mapper.StockStatisticsMapper">

    <sql id="saleOrder">
        select peso.id as vchcode,
        peso.trade_order_id as bill_number,
        '销售-网店平台原始订单' as vchtype_name,
        peso.trade_create_time as bill_date,
        case peso.process_state
        when 0 then '未提交'
        when 1 then '已提交审核'
        when 2 then '已部分提交'
        when 3 then '已提交分期购'
        else '' end as bill_status,
        (pesod.qty - ifnull(giftod.qty,0)) as bill_qty,
        pesod.sku_id,
        pesod.ptype_id,
        (-1 * (pesod.qty - ifnull(giftod.qty,0))) as sale_qty,
        p.fullname as p_fullname,
        p.usercode as p_usercode,
        bpu.unit_name,
        k.fullname as k_fullname,
        b.fullname as b_fullname,
        e.fullname as e_fullname,
        '' as memo,
        k.id as k_id,
        '' as summary,
        bps.propvalue_name1,
        bps.propvalue_name2,
        bps.propvalue_name3,
        bps.propvalue_name4,
        bps.propvalue_name5,
        bps.propvalue_name6,
        pesod.batchno,
        pesod.batch_price,
        pesod.produce_date,
        pesod.expire_date,
        datediff(pesod.expire_date, pesod.produce_date) as sell_by_dates,
        p.protect_days_unit,p.protect_days_view,
        r.rule_name,'9802' as vchtype
        from pl_eshop_sale_order peso
        left join pl_eshop_sale_order_detail pesod on peso.profile_id = pesod.profile_id and peso.id = pesod.eshop_order_id
        left join pl_eshop_sale_order gifto on peso.profile_id = gifto.profile_id and peso.otype_id = gifto.otype_id
                  and peso.trade_order_id=gifto.platform_parent_order_id and peso.order_sale_type =6 and gifto.order_sale_type = 7
        left join pl_eshop_sale_order_detail giftod on giftod.profile_id = gifto.profile_id and giftod.eshop_order_id = gifto.id and giftod.platform_ptype_id=pesod.platform_ptype_id
        left join base_ptype p on p.profile_id = peso.profile_id and p.id = pesod.ptype_id
        left join base_ktype k on k.id = peso.ktype_id and k.profile_id = peso.profile_id
        left join base_etype e on e.id = peso.etype_id and e.profile_id = peso.profile_id
        left join base_btype b on b.id = peso.btype_id and b.profile_id = peso.profile_id
        left join base_ptype_sku bps on bps.profile_id=pesod.profile_id and bps.id=pesod.sku_id
        left join base_ptype_unit bpu on bpu.profile_id = p.profile_id and bpu.ptype_id = p.id and bpu.unit_code=1
        left join pl_eshop_stock_sync_rule r
        on r.profile_id = peso.profile_id and r.id = pesod.stock_sync_rule_id and r.rule_type in(3,4)
        where peso.profile_id = #{profileId}
        and pesod.process_state = 0
        and pesod.deleted = 0
        and peso.deleted = 0
        and peso.business_type!=204
        and peso.business_type!=205
        and peso.business_type!=208
        and pesod.local_refund_state != 4
        and pesod.deliver_required = 1
        and peso.self_delivery_mode != 3
        and pesod.qty != ifnull(giftod.qty,0)
        AND (`peso`.order_sale_type != 3 OR (`peso`.order_sale_type=3 AND `peso`.platform_parent_order_id=''))
        <if test="batchno!=null and batchno!=''">
            and pesod.batchno = #{batchno}
        </if>
        <if test="produceDate!=null">
            and DATE_FORMAT(pesod.produce_date,'%Y-%m-%d') = DATE_FORMAT(#{produceDate},'%Y-%m-%d')
        </if>
        <if test="expireDate!=null">
            and DATE_FORMAT(pesod.expire_date,'%Y-%m-%d') = DATE_FORMAT(#{expireDate},'%Y-%m-%d')
        </if>
        <if test="!showLock">
            and r.id is null
        </if>
        <if test="ptypeId!=null and ptypeId!=0">
            and pesod.ptype_id = #{ptypeId}
        </if>
        <if test="skuId!=null and skuId!=0">
            and pesod.sku_id = #{skuId}
        </if>
        and pesod.platform_detail_trade_state in
        <foreach collection="saleOrderState" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and peso.ktype_id in
        <foreach collection="ktypeIdList" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </sql>

    <sql id="advance">
        select todp.eshop_order_id as vchcode,
        top.trade_id as bill_number,
        '销售-网店预售订单' as vchtype_name,
        top.trade_create_time as bill_date,
        case todp.process_state
        when 0 then '未提交'
        when 1 then '已提交审核'
        when 2 then '已提交预售'
        when 3 then '已提交分期购'
        else '' end as bill_status,
        todc.qty as bill_qty,
        todc.sku_id,
        todc.ptype_id,
        (-1 * todc.qty) as sale_qty,
        p.fullname as p_fullname,
        p.usercode as p_usercode,
        bpu.unit_name,
        k.fullname as k_fullname,
        b.fullname as b_fullname,
        e.fullname as e_fullname,
        '' as memo,
        k.id as k_id,
        '' as summary,
        bps.propvalue_name1,
        bps.propvalue_name2,
        bps.propvalue_name3,
        bps.propvalue_name4,
        bps.propvalue_name5,
        bps.propvalue_name6,
        tdds.batchno,
        tdds.batch_price,
        tdds.produce_date,
        tdds.expire_date,
        datediff(tdds.expire_date, tdds.produce_date) as sell_by_dates,
        p.protect_days_unit,p.protect_days_view,
        r.rule_name,'9802' as vchtype
        from td_orderbill_detail_platform todp
        left join td_orderbill_detail_core todc on todp.profile_id = todc.profile_id  and todp.detail_id=todc.detail_id
        left join td_orderbill_platform top on todp.profile_id=top.profile_id and todp.vchcode=top.vchcode
        left join td_orderbill_core toc on todp.profile_id=toc.profile_id  and todp.vchcode=toc.vchcode
        left join td_orderbill_detail_serialno tdds on tdds.profile_id=todp.profile_id and tdds.detail_id= todc.detail_id and tdds.vchcode=todc.vchcode
        left join base_ptype p on p.profile_id = todc.profile_id and p.id = todc.ptype_id
        left join base_ktype k on k.id = toc.ktype_id and k.profile_id = toc.profile_id
        left join base_etype e on e.id = toc.etype_id and e.profile_id = toc.profile_id
        left join base_btype b on b.id = toc.btype_id and b.profile_id = toc.profile_id
        left join base_ptype_sku bps on bps.profile_id=todc.profile_id and bps.id=todc.sku_id
        left join base_ptype_unit bpu on bpu.profile_id = p.profile_id and bpu.ptype_id = p.id and bpu.unit_code=1
        left join pl_eshop_stock_sync_rule r
        on r.profile_id = todp.profile_id and r.id = todp.stock_sync_rule_id and r.rule_type in (3,4)
        where todp.profile_id = #{profileId}
        and todp.process_state = 0
        and todp.deleted = 0
        and toc.business_type!=204
        and toc.business_type!=205
        and toc.business_type!=208
        and todp.local_refund_state != 4
        and todp.deliver_required = 1
        and toc.self_delivery_mode != 3
        and todp.is_sale_qty_need_to_occupy = 0
        AND (`toc`.order_sale_type != 3 OR (`toc`.order_sale_type=3 AND `toc`.platform_parent_order_id!=''))
        <if test="batchno!=null and batchno!=''">
            and tdds.batchno = #{batchno}
        </if>
        <if test="produceDate!=null">
            and DATE_FORMAT(tdds.produce_date,'%Y-%m-%d') = DATE_FORMAT(#{produceDate},'%Y-%m-%d')
        </if>
        <if test="expireDate!=null">
            and DATE_FORMAT(tdds.expire_date,'%Y-%m-%d') = DATE_FORMAT(#{expireDate},'%Y-%m-%d')
        </if>
        <if test="!showLock">
            and r.id is null
        </if>
        <if test="ptypeId!=null and ptypeId!=0">
            and todc.ptype_id = #{ptypeId}
        </if>
        <if test="skuId!=null and skuId!=0">
            and todc.sku_id = #{skuId}
        </if>
        and todp.platform_detail_trade_state in
        <foreach collection="saleOrderState" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and toc.ktype_id in
        <foreach collection="ktypeIdList" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </sql>

    <sql id="deliver">
        select tbc.vchcode,
        tbc.bill_number,
        vc.fullname as vchtype_name,
        tbc.bill_date,
        case
        when tbc.post_state = 0 then '草稿'
        when tbc.post_state = 100 and tba.audit_state = 1  then '待审核'
        when tbc.post_state = 100 and tba.audit_state = 2  then '审核中'
        when tbc.post_state = 100 and tba.audit_state = 3  then '审核不通过'
        when tbc.post_state = 100 and tba.audit_state = 4  then '审核通过'
        when tbc.post_state = 500 then '待出入库'
        when tbc.post_state = 550 then '部分出入库'
        else '' end as bill_status,
        abs(tbdc.qty) as bill_qty,
        tbdc.sku_id,
        tbdc.ptype_id,
        tbdc.qty as sale_qty,
        p.fullname as p_fullname,
        p.usercode as p_usercode,
        bpu.unit_name,
        k.fullname as k_fullname,
        b.fullname as b_fullname,
        e.fullname as e_fullname,
        tbc.memo,
        k.id as k_id,
        tbc.summary,
        bps.propvalue_name1,
        bps.propvalue_name2,
        bps.propvalue_name3,
        bps.propvalue_name4,
        bps.propvalue_name5,
        bps.propvalue_name6,
        tbdb.batchno,
        tbdb.batch_price,
        tbdb.produce_date,
        tbdb.expire_date,
        datediff(tbdb.expire_date, tbdb.produce_date) as sell_by_dates,
        p.protect_days_unit,p.protect_days_view,
        rule.rule_name,'2000' as vchtype
        from td_bill_deliver tbd
        left join td_bill_core tbc on tbd.profile_id = tbc.profile_id and tbd.vchcode = tbc.vchcode
        left join td_bill_detail_deliver tbdd on tbd.profile_id = tbdd.profile_id and tbd.vchcode = tbdd.vchcode
        left join td_bill_detail_core tbdc on tbd.profile_id = tbdc.profile_id and tbdc.vchcode = tbdd.vchcode and
        tbdc.detail_id = tbdd.detail_id
        left join td_bill_detail_batch tbdb on tbdb.profile_id=tbdc.profile_id and tbdc.detail_id = tbdb.detail_id
        left join pl_eshop_stock_sync_rule rule on rule.profile_id = tbd.profile_id and rule.id =
        tbdd.stock_sync_rule_id and rule.rule_type in(3,4)
        left join base_ptype p on p.profile_id = tbd.profile_id and p.id = tbdc.ptype_id
        left join base_ptype_sku bps on bps.profile_id=tbdc.profile_id and bps.id=tbdc.sku_id
        left join base_ktype k on k.id = tbc.ktype_id and k.profile_id = tbc.profile_id
        left join base_etype e on e.id = tbc.etype_id and e.profile_id = tbc.profile_id
        left join base_btype b on b.id = tbc.btype_id and b.profile_id = tbc.profile_id
        left join acc_vchtype vc on tbc.vchtype = vc.vchtype and vc.profile_id = tbc.profile_id
        left join base_ptype_unit bpu on bpu.profile_id = p.profile_id and bpu.unit_code = 1 and bpu.ptype_id = p.id
        left join td_bill_audit tba on tbc.vchcode = tba.vchcode and tbc.profile_id=tba.profile_id and tba.obsolete_enabled = 0
        where tbd.profile_id = #{profileId}
        and tbc.deleted = 0
        and tbdd.refund_state != 2
        and tbc.business_type != 204
        and tbc.business_type != 205
        and tbc.business_type != 208
        and vc.vchtype!=3100
        and tbdc.stock_occupy_type=0
        and tbc.ktype_id > 0
        and tbc.post_state <![CDATA[<]]> 600
        <if test="batchno!=null and batchno!=''">
            and tbdb.batchno = #{batchno}
        </if>
        <if test="produceDate!=null">
            and DATE_FORMAT(tbdb.produce_date,'%Y-%m-%d') = DATE_FORMAT(#{produceDate},'%Y-%m-%d')
        </if>
        <if test="expireDate!=null">
            and DATE_FORMAT(tbdb.expire_date,'%Y-%m-%d') = DATE_FORMAT(#{expireDate},'%Y-%m-%d')
        </if>
        <if test="ptypeId!=null and ptypeId!=0">
            and tbdc.ptype_id = #{ptypeId}
        </if>
        <if test="skuId!=null and skuId!=0">
            and tbdc.sku_id = #{skuId}
        </if>
        <if test="!showLock">
            and rule.id is null
        </if>
        and tbdd.trade_state in
        <foreach collection="deliverState" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and tbc.ktype_id in
        <foreach collection="ktypeIdList" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </sql>

    <sql id="sale_warehouse">
        select tbc.vchcode,
        tbc.bill_number,
        vc.fullname as vchtype_name,
        tbc.bill_date,
        case
        when tbc.post_state = 0 then '草稿'
        when tbc.post_state = 100 and tba.audit_state = 1  then '待审核'
        when tbc.post_state = 100 and tba.audit_state = 2  then '审核中'
        when tbc.post_state = 100 and tba.audit_state = 3  then '审核不通过'
        when tbc.post_state = 100 and tba.audit_state = 4  then '审核通过'
        when tbc.post_state = 500 then '待出入库'
        when tbc.post_state = 550 then '部分出入库'
        else '' end as bill_status,
        abs((tbdc.qty-tbdc.inout_qty)) as bill_qty,
        tbdc.sku_id,
        tbdc.ptype_id,
        tbdc.qty as sale_qty,
        p.fullname as p_fullname,
        p.usercode as p_usercode,
        bpu.unit_name,
        k.fullname as k_fullname,
        b.fullname as b_fullname,
        e.fullname as e_fullname,
        tbc.memo,
        k.id as k_id,
        tbc.summary,
        bps.propvalue_name1,
        bps.propvalue_name2,
        bps.propvalue_name3,
        bps.propvalue_name4,
        bps.propvalue_name5,
        bps.propvalue_name6,
        tbdb.batchno,
        tbdb.batch_price,
        tbdb.produce_date,
        tbdb.expire_date,
        datediff(tbdb.expire_date, tbdb.produce_date) as sell_by_dates,
        p.protect_days_unit,p.protect_days_view,
        rule.rule_name,'2000' as vchtype
        from td_bill_warehouse_task task
        left join td_bill_warehouse_task_detail task_detail on task_detail.profile_id = task.profile_id and task_detail.warehouse_task_id = task.warehouse_task_id
        left join td_bill_detail_deliver tbdd on task_detail.profile_id = tbdd.profile_id and task_detail.detail_id = tbdd.detail_id
        left join td_bill_detail_core tbdc on task_detail.profile_id = tbdc.profile_id and task_detail.detail_id = tbdc.detail_id
        left join td_bill_core tbc on tbdc.profile_id = tbc.profile_id and tbdc.vchcode = tbc.vchcode
        left join td_bill_detail_batch tbdb on task_detail.profile_id=tbdb.profile_id and task_detail.detail_id=tbdb.detail_id
        left join pl_eshop_stock_sync_rule rule on rule.profile_id = tbdd.profile_id and rule.id =
        tbdd.stock_sync_rule_id and rule.rule_type in(3,4)
        left join base_ptype p on p.profile_id = tbdc.profile_id and p.id = tbdc.ptype_id
        left join base_ptype_sku bps on bps.profile_id=tbdc.profile_id and bps.id=tbdc.sku_id
        left join base_ktype k on k.id = tbc.ktype_id and k.profile_id = tbc.profile_id
        left join base_etype e on e.id = tbc.etype_id and e.profile_id = tbc.profile_id
        left join base_btype b on b.id = tbc.btype_id and b.profile_id = tbc.profile_id
        left join acc_vchtype vc on tbc.vchtype = vc.vchtype and vc.profile_id = tbc.profile_id
        left join base_ptype_unit bpu on bpu.profile_id = p.profile_id and bpu.unit_code = 1 and bpu.ptype_id = p.id
        left join td_bill_audit tba on tbc.vchcode = tba.vchcode and tbc.profile_id=tba.profile_id and tba.obsolete_enabled = 0
        where task.profile_id = #{profileId}
        and tbc.deleted = 0
        and tbdc.deleted = 0
        and tbdd.refund_state != 2
        and task.business_type != 204
        and task.business_type != 205
        and task.business_type != 208
        and vc.vchtype!=3100
        and tbdc.stock_occupy_type=0
        and tbc.ktype_id > 0
        and tbc.post_state <![CDATA[<]]> 600
        and tbc.order_sale_mode = 5
        and (task.self_delivery_mode != 4 or task.full_link_status <![CDATA[<]]> 80)
        and task.wholesale_bill != 1
        <if test="batchno!=null and batchno!=''">
            and tbdb.batchno = #{batchno}
        </if>
        <if test="produceDate!=null">
            and DATE_FORMAT(tbdb.produce_date,'%Y-%m-%d') = DATE_FORMAT(#{produceDate},'%Y-%m-%d')
        </if>
        <if test="expireDate!=null">
            and DATE_FORMAT(tbdb.expire_date,'%Y-%m-%d') = DATE_FORMAT(#{expireDate},'%Y-%m-%d')
        </if>
        <if test="ptypeId!=null and ptypeId!=0">
            and tbdc.ptype_id = #{ptypeId}
        </if>
        <if test="skuId!=null and skuId!=0">
            and tbdc.sku_id = #{skuId}
        </if>
        <if test="!showLock">
            and rule.id is null
        </if>
        and tbdd.trade_state in
        <foreach collection="deliverState" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and tbdc.ktype_id in
        <foreach collection="ktypeIdList" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and (tbdc.qty-tbdc.inout_qty)!=0
    </sql>

    <sql id="bill">
        select tbc.vchcode,
        tbc.bill_number,
        vc.fullname as vchtype_name,
        tbc.bill_date,
        case
        when tbc.post_state = 0 then '草稿'
        when tbc.post_state = 100 and tba.audit_state = 1  then '待审核'
        when tbc.post_state = 100 and tba.audit_state = 2  then '审核中'
        when tbc.post_state = 100 and tba.audit_state = 3  then '审核不通过'
        when tbc.post_state = 100 and tba.audit_state = 4  then '审核通过'
        when tbc.post_state = 500 then '待出入库'
        when tbc.post_state = 550 then '部分出入库'
        else '' end as bill_status,
        abs(tbdc.qty) as bill_qty,
        tbdc.sku_id,
        tbdc.ptype_id,
        (tbdc.qty-tbdc.inout_qty) as sale_qty,
        p.fullname as p_fullname,
        p.usercode as p_usercode,
        bpu.unit_name,
        k.fullname as k_fullname,
        b.fullname as b_fullname,
        e.fullname as e_fullname,
        tbc.memo,
        k.id as k_id,
        tbc.summary,
        bps.propvalue_name1,
        bps.propvalue_name2,
        bps.propvalue_name3,
        bps.propvalue_name4,
        bps.propvalue_name5,
        bps.propvalue_name6,
        tbdb.batchno,
        tbdb.batch_price,
        tbdb.produce_date,
        tbdb.expire_date,
        datediff(tbdb.expire_date, tbdb.produce_date) as sell_by_dates,
        p.protect_days_unit,p.protect_days_view,
        '' as rule_name,tbc.vchtype
        from td_bill_core tbc
        left join td_bill_deliver tdb on tdb.profile_id=tbc.profile_id and tbc.vchcode=tdb.vchcode
        left join td_bill_detail_core tbdc on tbc.vchcode = tbdc.vchcode and tbdc.profile_id = tbc.profile_id
        left join base_ptype p on p.profile_id = tbc.profile_id and p.id = tbdc.ptype_id
        left join base_ptype_sku bps on bps.profile_id=tbdc.profile_id and bps.id=tbdc.sku_id
        left join base_ktype k on k.id = tbdc.ktype_id and k.profile_id = tbdc.profile_id
        left join base_etype e on e.id = tbc.etype_id and e.profile_id = tbdc.profile_id
        left join base_btype b on b.id = tbc.btype_id and b.profile_id = tbdc.profile_id
        left join acc_vchtype vc on tbc.vchtype = vc.vchtype and vc.profile_id = tbdc.profile_id
        left join base_ptype_unit bpu on bpu.profile_id = p.profile_id and bpu.unit_code = 1 and bpu.ptype_id = p.id
        left join td_bill_detail_batch tbdb on tbdb.profile_id=tbdc.profile_id and tbdc.detail_id = tbdb.detail_id
        left join td_bill_audit tba on tbc.vchcode = tba.vchcode and tbc.profile_id=tba.profile_id and tba.obsolete_enabled = 0
        left join td_bill_warehouse_task tdwt on tdwt.profile_id=tbc.profile_id and tdwt.vchcode = tbc.vchcode
        where tbc.profile_id=#{profileId}
        and tbc.post_state <![CDATA[<]]> 600 and tbc.post_state != -100
        and tbc.business_type!=204
        and tbc.business_type!=205
        and tbc.business_type != 208
        and tbdc.stock_occupy_type=0
        and tbc.deleted=0
        and tbc.process_type != 1
        and tbdc.ktype_id >0
        and tbdc.sku_id>0
        and (tbdc.qty-tbdc.inout_qty)!=0
        <if test="batchno!=null and batchno!=''">
            and tbdb.batchno = #{batchno}
        </if>
        <if test="produceDate!=null">
            and DATE_FORMAT(tbdb.produce_date,'%Y-%m-%d') = DATE_FORMAT(#{produceDate},'%Y-%m-%d')
        </if>
        <if test="expireDate!=null">
            and DATE_FORMAT(tbdb.expire_date,'%Y-%m-%d') = DATE_FORMAT(#{expireDate},'%Y-%m-%d')
        </if>
        <if test="ptypeId!=null and ptypeId!=0">
            and tbdc.ptype_id = #{ptypeId}
        </if>
        <if test="skuId!=null and skuId!=0">
            and tbdc.sku_id = #{skuId}
        </if>
        <if test="batchno!=null and batchno!=''">
            and tbdb.batchno = #{batchno}
        </if>
        and tbdc.ktype_id in
        <foreach collection="ktypeIdList" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and tbdc.inout_type in
        <foreach collection="allowInOutBills" index="index" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and tbdc.ktype_point_type = 0
        and (tdwt.vchcode is null or tdwt.self_delivery_mode != 4  or tdwt.full_link_status <![CDATA[<]]> 80 )

    </sql>

    <sql id="order">
        select * from (
        select tbc.vchcode,
        tbc.bill_number,
        vc.fullname as vchtype_name,
        tbc.bill_date,
        case tbc.post_state
        when 0 then '草稿'
        when 100 then '未审核'
        when 300 then '已审核'
        when 500 then '已确认'
        else ''
        end as bill_status,
        abs(tbdc.qty) as bill_qty,
        tbdc.sku_id,
        tbdc.ptype_id,
        <if test="!billOutSaleRecordEnabled and !billInSaleRecordEnabled">
            CASE WHEN tbdc.inout_type = 0 THEN (-1*(tbdc.qty + ifnull(todc.inout_qty,0))) ELSE (tbdc.qty -
            ifnull(todc.inout_qty,0)) END
        </if>
        <if test="!billOutSaleRecordEnabled and billInSaleRecordEnabled">
            CASE WHEN tbdc.inout_type = 0 THEN (-1*(tbdc.qty+ ifnull(todc.inout_qty,0))) ELSE (tbdc.qty -
            tbdc.reference_qty) END
        </if>
        <if test="billOutSaleRecordEnabled and !billInSaleRecordEnabled">
            CASE WHEN tbdc.inout_type = 0 THEN (-1*(tbdc.qty- tbdc.reference_qty)) ELSE (tbdc.qty -
            ifnull(todc.inout_qty,0)) END
        </if>
        <if test="billOutSaleRecordEnabled and billInSaleRecordEnabled">
            CASE WHEN tbdc.inout_type = 0 THEN (-1*(tbdc.qty-tbdc.reference_qty)) ELSE (tbdc.qty-tbdc.reference_qty) END
        </if>
        as sale_qty,
        p.fullname as p_fullname,
        p.usercode as p_usercode,
        bpu.unit_name,
        k.fullname as k_fullname,
        b.fullname as b_fullname,
        e.fullname as e_fullname,
        tbc.memo,
        k.id as k_id,
        tbc.summary,
        bps.propvalue_name1,
        bps.propvalue_name2,
        bps.propvalue_name3,
        bps.propvalue_name4,
        bps.propvalue_name5,
        bps.propvalue_name6,
        ass.batchno,
        tdds.batch_price,
        tdds.produce_date,
        tdds.expire_date,
        datediff(tdds.expire_date, tdds.produce_date) as sell_by_dates,
        p.protect_days_unit,p.protect_days_view,
        '' as rule_name,vc.vchtype
        from td_orderbill_core tbc
        left join td_orderbill_detail_core tbdc on tbc.vchcode = tbdc.vchcode and tbdc.profile_id = tbc.profile_id
        left join td_orderbill_detail_assinfo ass on ass.profile_id=tbc.profile_id and ass.detail_id=tbdc.detail_id
        left join td_orderbill_detail_batch tdds on tdds.profile_id=tbdc.profile_id and tdds.detail_id=tbdc.detail_id
        left join base_ptype p on p.profile_id = tbc.profile_id and p.id = tbdc.ptype_id
        left join base_ptype_sku bps on bps.profile_id=tbdc.profile_id and bps.id=tbdc.sku_id
        left join base_ktype k on k.id = tbdc.ktype_id and k.profile_id = tbdc.profile_id
        left join base_etype e on e.id = tbc.etype_id and e.profile_id = tbdc.profile_id
        left join base_btype b on b.id = tbc.btype_id and b.profile_id = tbdc.profile_id
        left join acc_vchtype vc on tbc.vchtype = vc.vchtype and vc.profile_id = tbdc.profile_id
        left join base_ptype_unit bpu on bpu.profile_id = p.profile_id and bpu.unit_code = 1 and bpu.ptype_id = p.id
        left join td_orderbill_detail_complete todc on tbdc.profile_id=todc.profile_id and todc.detail_id=tbdc.detail_id
        and
        todc.vchcode=tbdc.vchcode
        where tbc.profile_id = #{profileId}
        and tbc.orderover_state in (0,3)
        and tbc.post_state = 500
        and tbc.vchtype in
        <foreach collection="orderBillVchtypes" index="index" item="vchtype" open="(" separator="," close=")">
            #{vchtype}
        </foreach>
        and tbdc.ktype_id >0
        and tbdc.sku_id >0
        <if test="batchno!=null and batchno!=''">
            and tdds.batchno = #{batchno}
        </if>
        <if test="produceDate!=null">
            and DATE_FORMAT(tdds.produce_date,'%Y-%m-%d') = DATE_FORMAT(#{produceDate},'%Y-%m-%d')
        </if>
        <if test="expireDate!=null">
            and DATE_FORMAT(tdds.expire_date,'%Y-%m-%d') = DATE_FORMAT(#{expireDate},'%Y-%m-%d')
        </if>
        <if test="ptypeId!=null and ptypeId!=0">
            and tbdc.ptype_id = #{ptypeId}
        </if>
        <if test="skuId!=null and skuId!=0">
            and tbdc.sku_id = #{skuId}
        </if>
        and tbdc.ktype_id in
        <foreach collection="ktypeIdList" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        <if test="!billOutSaleRecordEnabled and !billInSaleRecordEnabled">
            and if(tbdc.inout_type = 0, tbdc.qty + ifnull(todc.inout_qty,0) > 0, tbdc.qty - ifnull(todc.inout_qty,0) > 0)
        </if>
        <if test="!billOutSaleRecordEnabled and billInSaleRecordEnabled">
            and if(tbdc.inout_type = 0, tbdc.qty + ifnull(todc.inout_qty,0) > 0, tbdc.qty - tbdc.reference_qty > 0)
        </if>
        <if test="billOutSaleRecordEnabled and !billInSaleRecordEnabled">
            and if(tbdc.inout_type = 0, tbdc.qty- tbdc.reference_qty > 0, tbdc.qty - ifnull(todc.inout_qty,0) > 0)
        </if>
        <if test="billOutSaleRecordEnabled and billInSaleRecordEnabled">
            and if(tbdc.inout_type = 0, tbdc.qty-tbdc.reference_qty > 0, tbdc.qty-tbdc.reference_qty > 0)
        </if>

        )orderTemp where ordertemp.sale_qty!=0
    </sql>

    <select id="queryInventorySaleQty"
            resultType="com.wsgjp.ct.sale.biz.analysiscloud.entity.inventory.InventorySaleQtyEntity">
        select * from(
        <include refid="saleOrder"/>
        union all
        <include refid="advance"/>
        union all
        <include refid="sale_warehouse"/>
        union all
        <include refid="bill"/>
        <if test="availableStockEnabled or buyOrderSaleQtyEnabled or transOrderSaleQtyEnabled">
            union all
            <include refid="order"/>
        </if>
        ) a
        <where>
            ptype_id is not null
            <if test="filter!=null and filter.size>0">
                <include refid="filter"/>
            </if>
        </where>
    </select>

    <select id="queryInventorySaleQtyCount"
            resultType="java.lang.Integer">
        select COUNT(0) from(
        <include refid="saleOrder"/>
        union all
        <include refid="advance"/>
        union all
        <include refid="sale_warehouse"/>
        union all
        <include refid="bill"/>
        <if test="availableStockEnabled or buyOrderSaleQtyEnabled or transOrderSaleQtyEnabled">
            union all
            <include refid="order"/>
        </if>
        ) a
        <where>
            ptype_id is not null
            <if test="filter!=null and filter.size>0">
                <include refid="filter"/>
            </if>
        </where>
    </select>

    <select id="queryInventorySaleQtyByLimit"
            resultType="com.wsgjp.ct.sale.biz.analysiscloud.entity.inventory.InventorySaleQtyEntity">
        select * from(
        <include refid="saleOrder"/>
        union all
        <include refid="advance"/>
        union all
        <include refid="sale_warehouse"/>
        union all
        <include refid="bill"/>
        <if test="availableStockEnabled or buyOrderSaleQtyEnabled or transOrderSaleQtyEnabled">
            union all
            <include refid="order"/>
        </if>
        ) a
        <where>
            ptype_id is not null
            <if test="filter!=null and filter.size>0">
                <include refid="filter"/>
            </if>
        </where>
        <if test="bizPageLimitEnabled">
            limit #{pageNum}, #{pageSize}
        </if>
    </select>

    <sql id="send_inout">
        select tbc.vchcode,
        tbc.bill_number,
        tbir.inout_number,
        vc.fullname as vchtype_name,
        vc.vchtype,
        tbc.bill_date,
        case tbc.post_state
        when 0 then '草稿'
        when 100 then '未审核'
        when 300 then '已审核'
        when 500 then '待出入库'
        when 550 then '部分出入库'
        else '' end as bill_status,
        case tdwt.warehouse_state
        when 1 then '待审核'
        when 5 then '待财审'
        when 10 then '海关处理中'
        when 15 then '待配货'
        when 20 then '验货'
        when 25 then '称重'
        when 30 then '待发货'
        when 35 then '已发货'
        when 40 then '已记账'
        when 45 then '结束'
        else '' end as warehouse_state,
        case tbir.inout_state
        when 0 then '仓库作业中'
        when 30 then '配送中'
        when 60 then '库存待核算'
        when 80 then '库存核算失败'
        when 81 then '库存核算失败已处理'
        when 100 then '库存核算成功'
        else '' end as inout_state,
        abs(tbid.qty) as bill_qty,
        tbid.sku_id,
        tbid.ptype_id,
        tbid.qty as qty,
        tbid.ktype_id,
        bps.propvalue_name1,
        bps.propvalue_name2,
        bps.propvalue_name3,
        bps.propvalue_name4,
        bps.propvalue_name5,
        bps.propvalue_name6,
        tbdb.batchno,
        tbdb.batch_price,
        tbdb.produce_date,
        tbdb.expire_date,
        datediff(tbdb.expire_date, tbdb.produce_date) as sell_by_dates,
        p.protect_days_unit,p.protect_days_view
        from td_bill_inout_detail tbid
        left join td_bill_inout_record tbir on tbid.profile_id=tbir.profile_id and tbir.inout_id=tbid.inout_id
        left join td_bill_warehouse_task tdwt on tdwt.profile_id=tbid.profile_id and tdwt.warehouse_task_id =
        tbir.inout_id
        left join base_ptype p on p.profile_id=tbid.profile_id and p.id=tbid.ptype_id
        left join base_ptype_sku bps on bps.profile_id=tbid.profile_id and bps.id=tbid.sku_id
        left join td_bill_detail_batch tbdb on tbdb.profile_id=tbid.profile_id and
        tbdb.inout_detail_id=tbid.inout_detail_id
        left join td_bill_detail_core tbdc on tbdc.profile_id=tbid.profile_id and tbdc.detail_id=tbid.detail_id
        left join td_bill_core tbc on tbc.profile_id=tbdc.profile_id and tbc.vchcode=tbdc.vchcode
        left join acc_vchtype vc on tbc.vchtype = vc.vchtype and vc.profile_id = tbc.profile_id
        left join td_bill_detail_deliver tbdd on tbid.vchcode = tbdd.vchcode and tbid.detail_id = tbdd.detail_id and tbdd.profile_id = tbid.profile_id
        where tbid.profile_id = #{profileId}
        and tbir.inout_state != 100
        and tbir.record_type not in (3,13)
        and tbc.business_type!=204
        and tbc.business_type!=205
        and tbc.business_type!=208
        and tbc.deleted=0
        and tbid.ktype_point_type=0
        and (( tbdd.trade_state !=5 and tbdd.refund_state !=2) or tbdd.detail_id is null)
        and (tdwt.vchcode is null or tdwt.self_delivery_mode != 4 or tdwt.full_link_status <![CDATA[<]]> 80 )
        <if test="!newSendQtyCalcEnabled">
            and (tbc.deliver_process_type != 3 OR tbir.inout_state != 60)
        </if>
        <if test="ptypeId!=null and ptypeId!=0">
            and tbid.ptype_id = #{ptypeId}
        </if>
        <if test="skuId!=null and skuId!=0">
            and tbid.sku_id = #{skuId}
        </if>
        <if test="batchno!=null and batchno!=''">
            and tbdb.batchno = #{batchno}
        </if>
        <if test="produceDate!=null">
            and DATE_FORMAT(tbdb.produce_date,'%Y-%m-%d') = DATE_FORMAT(#{produceDate},'%Y-%m-%d')
        </if>
        <if test="expireDate!=null">
            and DATE_FORMAT(tbdb.expire_date,'%Y-%m-%d') = DATE_FORMAT(#{expireDate},'%Y-%m-%d')
        </if>
        and tbid.ktype_id in
        <foreach collection="ktypeIdList" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </sql>

    <sql id="send_wms_out">
        SELECT tbc.vchcode,
        tbc.bill_number,
        b.wms_order_code as inout_number,
        vc.fullname as vchtype_name,
        vc.vchtype,
        tbc.bill_date,
        CASE tbc.post_state
        WHEN 0 THEN '草稿'
        WHEN 100 THEN '未审核'
        WHEN 300 THEN '已审核'
        WHEN 500 THEN '待出入库'
        WHEN 550 THEN '部分出入库'
        ELSE '' END AS bill_status,
        CASE b.wms_status
        WHEN 0 THEN '未确认'
        WHEN 1 THEN '部分确认'
        WHEN 2 THEN '已确认'
        WHEN 3 THEN '强制确认'
        ELSE '' END AS warehouse_state,
        '' as inout_state,
        abs(a.qty) as bill_qty,
        a.sku_id,
        a.ptype_id,
        (-1*(a.qty-a.reality_qty)) as qty,
        b.ktype_id,
        bps.propvalue_name1,
        bps.propvalue_name2,
        bps.propvalue_name3,
        bps.propvalue_name4,
        bps.propvalue_name5,
        bps.propvalue_name6,
        a.batchno,
        tbdc.batch_price,
        a.produce_date,
        a.expire_date,
        datediff(a.expire_date, a.produce_date) as sell_by_dates,
        p.protect_days_unit,p.protect_days_view
        FROM td_wms_bill_detail_out a
        JOIN `td_wms_bill_core` b ON a.profile_id=b.profile_id AND a.vchcode=b.vchcode
        LEFT JOIN td_bill_core tbc ON tbc.profile_id=a.profile_id AND tbc.vchcode=a.vchcode
        LEFT JOIN td_bill_detail_core tbdc ON tbdc.detail_id=a.detail_id AND tbdc.profile_id=a.profile_id
        LEFT JOIN acc_vchtype vc ON tbc.vchtype = vc.vchtype AND vc.profile_id = tbc.profile_id
        LEFT JOIN base_ptype p ON p.id = a.ptype_id AND p.profile_id = tbc.profile_id
        LEFT JOIN base_ptype_sku bps ON bps.profile_id=a.profile_id AND bps.id=a.sku_id
        WHERE a.profile_id = #{profileId}
        AND b.wms_status!=2
        AND b.wms_status!=3
        AND b.order_type!=5
        AND tbc.business_type!=204
        AND tbc.business_type!=205
        AND tbc.business_type!=208
        <if test="ptypeId!=null and ptypeId!=0">
            and a.ptype_id = #{ptypeId}
        </if>
        <if test="skuId!=null and skuId!=0">
            and a.sku_id = #{skuId}
        </if>
        <if test="batchno!=null and batchno!=''">
            and a.batchno = #{batchno}
        </if>
        <if test="produceDate!=null">
            and DATE_FORMAT(a.produce_date,'%Y-%m-%d') = DATE_FORMAT(#{produceDate},'%Y-%m-%d')
        </if>
        <if test="expireDate!=null">
            and DATE_FORMAT(a.expire_date,'%Y-%m-%d') = DATE_FORMAT(#{expireDate},'%Y-%m-%d')
        </if>
        and b.ktype_id in
        <foreach collection="ktypeIdList" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        union all
        SELECT tbc.vchcode,
        tbc.bill_number,
        b.wms_order_code as inout_number,
        vc.fullname as vchtype_name,
        vc.vchtype,
        tbc.bill_date,
        CASE tbc.post_state
        WHEN 0 THEN '草稿'
        WHEN 100 THEN '未审核'
        WHEN 300 THEN '已审核'
        WHEN 500 THEN '待出入库'
        WHEN 550 THEN '部分出入库'
        ELSE '' END AS bill_status,
        CASE b.wms_status
        WHEN 0 THEN '未确认'
        WHEN 1 THEN '部分确认'
        WHEN 2 THEN '已确认'
        WHEN 3 THEN '强制确认'
        ELSE '' END AS warehouse_state,
        '' as inout_state,
        abs(a.qty) as bill_qty,
        a.sku_id,
        a.ptype_id,
        (-1*(a.qty-a.reality_qty)) as qty,
        b.ktype_id,
        bps.propvalue_name1,
        bps.propvalue_name2,
        bps.propvalue_name3,
        bps.propvalue_name4,
        bps.propvalue_name5,
        bps.propvalue_name6,
        a.batchno,
        tbdc.batch_price,
        a.produce_date,
        a.expire_date,
        datediff(a.expire_date, a.produce_date) as sell_by_dates,
        p.protect_days_unit,p.protect_days_view
        FROM td_wms_bill_detail_out a
        JOIN `td_wms_bill_core` b ON a.profile_id=b.profile_id AND a.vchcode=b.vchcode
        LEFT JOIN td_bill_warehouse_task task ON task.profile_id=a.profile_id AND task.warehouse_task_id=a.vchcode
        LEFT JOIN td_bill_warehouse_task_detail task_detail ON task_detail.warehouse_task_detail_id=a.detail_id AND task_detail.profile_id=a.profile_id
        LEFT JOIN td_bill_detail_core tbdc ON tbdc.detail_id=task_detail.detail_id AND tbdc.profile_id=task_detail.profile_id
        left join td_bill_core tbc on tbdc.profile_id = tbc.profile_id and tbdc.vchcode = tbc.vchcode
        LEFT JOIN acc_vchtype vc ON tbc.vchtype = vc.vchtype AND vc.profile_id = tbc.profile_id
        LEFT JOIN base_ptype p ON p.id = a.ptype_id AND p.profile_id = tbc.profile_id
        LEFT JOIN base_ptype_sku bps ON bps.profile_id=a.profile_id AND bps.id=a.sku_id
        WHERE a.profile_id = #{profileId}
        AND b.wms_status!=2
        AND b.wms_status!=3
        AND b.order_type=5
        AND tbc.business_type!=204
        AND tbc.business_type!=205
        AND tbc.business_type!=208
        <if test="ptypeId!=null and ptypeId!=0">
            and a.ptype_id = #{ptypeId}
        </if>
        <if test="skuId!=null and skuId!=0">
            and a.sku_id = #{skuId}
        </if>
        <if test="batchno!=null and batchno!=''">
            and a.batchno = #{batchno}
        </if>
        <if test="produceDate!=null">
            and DATE_FORMAT(a.produce_date,'%Y-%m-%d') = DATE_FORMAT(#{produceDate},'%Y-%m-%d')
        </if>
        <if test="expireDate!=null">
            and DATE_FORMAT(a.expire_date,'%Y-%m-%d') = DATE_FORMAT(#{expireDate},'%Y-%m-%d')
        </if>
        and b.ktype_id in
        <foreach collection="ktypeIdList" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </sql>
    <sql id="send_billcore_out">
        SELECT tc.vchcode,
        tc.bill_number,
        tc.bill_number as inout_number,
        vc.fullname as vchtype_name,
        vc.vchtype,
        tc.bill_date,
        CASE tc.post_state
        WHEN 0 THEN '草稿'
        WHEN 100 THEN '未审核'
        WHEN 300 THEN '已审核'
        WHEN 500 THEN '待出入库'
        WHEN 550 THEN '部分出入库'
        ELSE '' END AS bill_status,
        '锁定'  AS warehouse_state,
        '' as inout_state,
        abs(tbdc.qty) as bill_qty,
        tbdc.sku_id,
        tbdc.ptype_id,
        tbdc.qty as qty,
        tc.ktype_id,
        bps.propvalue_name1,
        bps.propvalue_name2,
        bps.propvalue_name3,
        bps.propvalue_name4,
        bps.propvalue_name5,
        bps.propvalue_name6,
        tbdc.batchno,
        tbdc.batch_price,
        tbdc.produce_date,
        tbdc.expire_date,datediff(tbdc.expire_date, tbdc.produce_date) as sell_by_dates,
        p.protect_days_unit,p.protect_days_view
        from td_bill_deliver tdb
        left join td_bill_core tc on tdb.profile_id = tc.profile_id and tdb.vchcode = tc.vchcode
        left join td_bill_detail_deliver tbdd on tdb.profile_id = tbdd.profile_id and tdb.vchcode = tbdd.vchcode
        left join td_bill_detail_core tbdc on tdb.profile_id = tbdc.profile_id and tdb.vchcode = tbdc.vchcode and
        tbdd.detail_id=tbdc.detail_id
        left join td_bill_detail_batch tbdb on tbdb.profile_id=tbdc.profile_id and tbdb.detail_id=tbdc.detail_id and
        tbdb.vchcode=tbdc.vchcode
        left join acc_vchtype vc on tc.vchtype = vc.vchtype and vc.profile_id = tc.profile_id
        LEFT JOIN base_ptype p ON p.id = tbdc.ptype_id AND p.profile_id = tbdc.profile_id
        LEFT JOIN base_ptype_sku bps ON bps.profile_id=tbdc.profile_id AND bps.id=tbdc.sku_id
        where tdb.profile_id = #{profileId}
        and tc.deleted=0
        and tc.business_type!=204
        and tc.business_type!=205
        and tc.business_type!=208
        and tbdc.stock_occupy_type=0
        and tbdd.refund_state!=2
        and tbdc.sku_id>0
        and tbdd.send_qty_lock_status=1
        and tc.post_state <![CDATA[<]]> 500

        <if test="errBusinessOrderEnabled and nonPaidOrderEnabled">
            and tbdd.trade_state in (0,1,2,3,4,6,7)
        </if>
        <if test="!errBusinessOrderEnabled and nonPaidOrderEnabled">
            and tbdd.trade_state in (1,2,3,4,6,7)
        </if>
        <if test="errBusinessOrderEnabled and !nonPaidOrderEnabled">
            and tbdd.trade_state in (0,2,3,4,6,7)
        </if>
        <if test="!errBusinessOrderEnabled and !nonPaidOrderEnabled">
            and tbdd.trade_state in (2,3,4,6,7)
        </if>
        <if test="ptypeId!=null and ptypeId!=0">
            and tbdc.ptype_id = #{ptypeId}
        </if>
        <if test="skuId!=null and skuId!=0">
            and tbdc.sku_id = #{skuId}
        </if>
        <if test="batchno!=null and batchno!=''">
            and tbdc.batchno = #{batchno}
        </if>
        <if test="produceDate!=null">
            and DATE_FORMAT(tbdc.produce_date,'%Y-%m-%d') = DATE_FORMAT(#{produceDate},'%Y-%m-%d')
        </if>
        <if test="expireDate!=null">
            and DATE_FORMAT(tbdc.expire_date,'%Y-%m-%d') = DATE_FORMAT(#{expireDate},'%Y-%m-%d')
        </if>
        and tbdc.ktype_id in
        <foreach collection="ktypeIdList" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </sql>
    <sql id="send_warehouse_out">
        SELECT tc.vchcode,
        tc.bill_number,
        '' as inout_number,
        vc.fullname as vchtype_name,
        vc.vchtype,
        tc.bill_date,
        CASE  task.warehouse_state
        WHEN 0 THEN '未知'
        WHEN 1 THEN '待审核'
        WHEN 15 THEN '待配货'
        WHEN 20 THEN '验货'
        WHEN 25 THEN '称重'
        WHEN 30 THEN '待发货'
        WHEN 35 THEN '已发货'
        WHEN 40 THEN '已发货'
        WHEN 45 THEN '结束'
        ELSE '' END AS bill_status,
        '锁定'  AS warehouse_state,
        '' as inout_state,
        abs(tbdc.qty) as bill_qty,
        tbdc.sku_id,
        tbdc.ptype_id,
        tbdc.qty as qty,
        tc.ktype_id,
        bps.propvalue_name1,
        bps.propvalue_name2,
        bps.propvalue_name3,
        bps.propvalue_name4,
        bps.propvalue_name5,
        bps.propvalue_name6,
        tbdc.batchno,
        tbdc.batch_price,
        tbdc.produce_date,
        tbdc.expire_date,
        datediff(tbdc.expire_date, tbdc.produce_date) as sell_by_dates,
        p.protect_days_unit,p.protect_days_view
        from td_bill_warehouse_task task
        left join td_bill_warehouse_task_detail detail_task on detail_task.warehouse_task_id = task.warehouse_task_id and detail_task.profile_id = task.profile_id
        left join td_bill_detail_deliver tbdd on detail_task.profile_id = tbdd.profile_id and detail_task.detail_id = tbdd.detail_id
        left join td_bill_detail_core tbdc on detail_task.profile_id = tbdc.profile_id and detail_task.detail_id = tbdc.detail_id
        left join td_bill_core tc on tbdc.profile_id = tc.profile_id and tbdc.vchcode = tc.vchcode
        left join td_bill_detail_batch tbdb on tbdb.profile_id=tbdc.profile_id and tbdb.detail_id=tbdc.detail_id and tbdb.vchcode=tbdc.vchcode
        left join acc_vchtype vc on tc.vchtype = vc.vchtype and vc.profile_id = tc.profile_id
        LEFT JOIN base_ptype p ON p.id = tbdc.ptype_id AND p.profile_id = tbdc.profile_id
        LEFT JOIN base_ptype_sku bps ON bps.profile_id=tbdc.profile_id AND bps.id=tbdc.sku_id
        where task.profile_id = #{profileId}
        and tc.deleted=0
        and task.deleted=0
        and task.business_type!=204
        and task.business_type!=205
        and task.business_type!=208
        and tbdc.stock_occupy_type=0
        and tbdd.refund_state!=2
        and tbdc.sku_id>0
        and tbdd.send_qty_lock_status=1
        and task.warehouse_state=1
        and task.wholesale_bill != 1
        <if test="errBusinessOrderEnabled and nonPaidOrderEnabled">
            and tbdd.trade_state in (0,1,2,3,4,6,7)
        </if>
        <if test="!errBusinessOrderEnabled and nonPaidOrderEnabled">
            and tbdd.trade_state in (1,2,3,4,6,7)
        </if>
        <if test="errBusinessOrderEnabled and !nonPaidOrderEnabled">
            and tbdd.trade_state in (0,2,3,4,6,7)
        </if>
        <if test="!errBusinessOrderEnabled and !nonPaidOrderEnabled">
            and tbdd.trade_state in (2,3,4,6,7)
        </if>
        <if test="ptypeId!=null and ptypeId!=0">
            and tbdc.ptype_id = #{ptypeId}
        </if>
        <if test="skuId!=null and skuId!=0">
            and tbdc.sku_id = #{skuId}
        </if>
        <if test="batchno!=null and batchno!=''">
            and tbdc.batchno = #{batchno}
        </if>
        <if test="produceDate!=null">
            and DATE_FORMAT(tbdc.produce_date,'%Y-%m-%d') = DATE_FORMAT(#{produceDate},'%Y-%m-%d')
        </if>
        <if test="expireDate!=null">
            and DATE_FORMAT(tbdc.expire_date,'%Y-%m-%d') = DATE_FORMAT(#{expireDate},'%Y-%m-%d')
        </if>
        and tbdc.ktype_id in
        <foreach collection="ktypeIdList" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </sql>

    <sql id="send_order">
        select tbc.vchcode,
        tbc.bill_number,
        tbc.bill_number as inout_number,
        vc.fullname as vchtype_name,
        vc.vchtype,
        tbc.bill_date,
        case tbc.post_state
        when 0 then '草稿'
        when 100 then '未审核'
        when 300 then '已审核'
        when 500 then '已确认'
        else ''
        end as bill_status,
        '锁定'  AS warehouse_state,
        '' as inout_state,
        abs(tbdc.qty) as bill_qty,
        tbdc.sku_id,
        tbdc.ptype_id,
        -1 * if(tbdc.qty - tbdc.reference_qty  - sum(if(t.vchcode  is null,0,tbdc2.qty)) &lt; 0,0,tbdc.qty - tbdc.reference_qty - sum(if(t.vchcode  is null,0,tbdc2.qty))) AS qty,
        k.id as ktype_id,
        bps.propvalue_name1,
        bps.propvalue_name2,
        bps.propvalue_name3,
        bps.propvalue_name4,
        bps.propvalue_name5,
        bps.propvalue_name6,
        ass.batchno,
        tdds.batch_price,
        tdds.produce_date,
        tdds.expire_date,
        datediff(tdds.expire_date, tdds.produce_date) as sell_by_dates,
        p.protect_days_unit,p.protect_days_view
        from td_orderbill_core tbc
        left join td_orderbill_detail_core tbdc on tbc.vchcode = tbdc.vchcode and tbdc.profile_id = tbc.profile_id
        left join td_orderbill_detail_assinfo ass on ass.profile_id=tbc.profile_id and ass.detail_id=tbdc.detail_id
        left join td_orderbill_detail_batch tdds on tdds.profile_id=tbdc.profile_id and tdds.detail_id=tbdc.detail_id
        left join base_ptype p on p.profile_id = tbc.profile_id and p.id = tbdc.ptype_id
        left join base_ptype_sku bps on bps.profile_id=tbdc.profile_id and bps.id=tbdc.sku_id
        left join base_ktype k on k.id = tbdc.ktype_id and k.profile_id = tbdc.profile_id
        left join base_etype e on e.id = tbc.etype_id and e.profile_id = tbdc.profile_id
        left join base_btype b on b.id = tbc.btype_id and b.profile_id = tbdc.profile_id
        left join acc_vchtype vc on tbc.vchtype = vc.vchtype and vc.profile_id = tbdc.profile_id
        left join base_ptype_unit bpu on bpu.profile_id = p.profile_id and bpu.unit_code = 1 and bpu.ptype_id = p.id
        left JOIN td_bill_detail_core tbdc2
        on tbdc.detail_id = tbdc2.source_detail_id AND tbdc2.ptype_id = tbdc.ptype_id  and tbdc.profile_id = tbdc2.profile_id
        LEFT JOIN td_bill_core t ON t.vchcode = tbdc2.vchcode AND t.profile_id = tbdc2.profile_id and t.post_state &lt; 500
        where tbc.profile_id = #{profileId}
        and tbc.orderover_state in (0,3)
        and tbc.post_state = 500
        and tbc.vchtype = 9001
        and tbdc.ktype_id >0
        and tbdc.sku_id >0
        <if test="batchno!=null and batchno!=''">
            and tdds.batchno = #{batchno}
        </if>
        <if test="produceDate!=null">
            and DATE_FORMAT(tdds.produce_date,'%Y-%m-%d') = DATE_FORMAT(#{produceDate},'%Y-%m-%d')
        </if>
        <if test="expireDate!=null">
            and DATE_FORMAT(tdds.expire_date,'%Y-%m-%d') = DATE_FORMAT(#{expireDate},'%Y-%m-%d')
        </if>
        <if test="ptypeId!=null and ptypeId!=0">
            and tbdc.ptype_id = #{ptypeId}
        </if>
        <if test="skuId!=null and skuId!=0">
            and tbdc.sku_id = #{skuId}
        </if>
        and tbdc.ktype_id in
        <foreach collection="ktypeIdList" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        group  by tbdc.detail_id having qty &lt; 0
    </sql>

    <sql id="send_wms_in">
        SELECT tbc.vchcode,
        tbc.bill_number,
        b.wms_order_code as inout_number,
        vc.fullname as vchtype_name,
        vc.vchtype,
        tbc.bill_date,
        CASE tbc.post_state
        WHEN 0 THEN '草稿'
        WHEN 100 THEN '未审核'
        WHEN 300 THEN '已审核'
        WHEN 500 THEN '待出入库'
        WHEN 550 THEN '部分出入库'
        ELSE '' END AS bill_status,
        CASE b.wms_status
        WHEN 0 THEN '未确认'
        WHEN 1 THEN '部分确认'
        WHEN 2 THEN '已确认'
        WHEN 3 THEN '强制确认'
        ELSE '' END AS warehouse_state,
        '' as inout_state,
        abs(a.qty) as bill_qty,
        a.sku_id,
        a.ptype_id,
        (a.qty-a.reality_qty) as qty,
        b.ktype_id,
        bps.propvalue_name1,
        bps.propvalue_name2,
        bps.propvalue_name3,
        bps.propvalue_name4,
        bps.propvalue_name5,
        bps.propvalue_name6,
        a.batchno,
        a.produce_date,
        a.expire_date,
        p.protect_days_unit,p.protect_days_view,
        datediff(a.expire_date, a.produce_date) as sell_by_dates
        FROM td_wms_bill_detail_in a
        JOIN `td_wms_bill_core` b ON a.profile_id=b.profile_id AND a.vchcode=b.vchcode
        LEFT JOIN td_bill_core tbc ON tbc.profile_id=a.profile_id AND tbc.vchcode=a.vchcode
        LEFT JOIN td_bill_detail_core tbdc ON tbdc.detail_id=a.detail_id AND tbdc.profile_id=a.profile_id
        LEFT JOIN acc_vchtype vc ON tbc.vchtype = vc.vchtype AND vc.profile_id = tbc.profile_id
        LEFT JOIN base_ptype p ON p.id = a.ptype_id AND p.profile_id = a.profile_id
        LEFT JOIN base_ptype_sku bps ON bps.profile_id=a.profile_id AND bps.id=a.sku_id
        WHERE a.profile_id = #{profileId}
        AND b.wms_status!=2
        AND tbc.business_type!=204
        AND tbc.business_type!=205
        AND tbc.business_type!=208
        <if test="ptypeId!=null and ptypeId!=0">
            and a.ptype_id = #{ptypeId}
        </if>
        <if test="skuId!=null and skuId!=0">
            and a.sku_id = #{skuId}
        </if>
        <if test="batchno!=null and batchno!=''">
            and a.batchno = #{batchno}
        </if>
        <if test="produceDate!=null">
            and DATE_FORMAT(a.produce_date,'%Y-%m-%d') = DATE_FORMAT(#{produceDate},'%Y-%m-%d')
        </if>
        <if test="expireDate!=null">
            and DATE_FORMAT(a.expire_date,'%Y-%m-%d') = DATE_FORMAT(#{expireDate},'%Y-%m-%d')
        </if>
        and b.ktype_id in
        <foreach collection="ktypeIdList" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </sql>

    <select id="queryInventorySendQty"
            resultType="com.wsgjp.ct.sale.biz.analysiscloud.entity.inventory.InventorySendQtyEntity">

        select * from (
        <include refid="send_inout"/>
        <if test="!newSendQtyCalcEnabled">
            union all
            <include refid="send_wms_out"/>
        </if>
        union all
        <include refid="send_warehouse_out"/>
        <if test="saleBillSendQtyEnabled">
            union all
            <include refid="send_order"/>
        </if>
        ) temp
        <where>
            vchcode is not null  and qty!=0
            <if test="filter!=null and filter.size>0">
                <include refid="filter"/>
            </if>
        </where>
    </select>

    <sql id="lock_sale">
        select peso.id as vchcode,
        peso.trade_order_id as bill_number,
        '销售-平台原始订单' as vchtype_name,
        peso.trade_create_time as bill_date,
        case pesod.process_state
        when 0 then '未提交'
        when 1 then '已提交审核'
        when 2 then '已提交预售'
        when 3 then '已提交分期购'
        else '' end as bill_status,
        pesod.qty as bill_qty,
        pesod.sku_id,
        pesod.ptype_id,
        (-1 * pesod.qty) as qty,
        bpu.unit_name,
        k.fullname as k_fullname,
        b.fullname as b_fullname,
        e.fullname as e_fullname,
        peso.ktype_id,
        bps.propvalue_name1,
        bps.propvalue_name2,
        bps.propvalue_name3,
        bps.propvalue_name4,
        bps.propvalue_name5,
        bps.propvalue_name6,
        pesod.batchno,
        pesod.produce_date,
        pesod.expire_date,
        pesod.batch_price,
        p.protect_days_unit,p.protect_days_view,
        datediff(pesod.expire_date, pesod.produce_date) as sell_by_dates
        from pl_eshop_sale_order peso
        left join pl_eshop_sale_order_detail pesod on peso.profile_id = pesod.profile_id and peso.id =
        pesod.eshop_order_id
        left join pl_eshop_sale_order_detail_combo pesodc on pesodc.profile_id=peso.profile_id and
        pesodc.eshop_order_detail_combo_row_id=pesod.combo_row_id
        left join base_ptype_sku bps on bps.profile_id=peso.profile_id and bps.id=pesod.sku_id
        left join base_ptype p on p.profile_id = peso.profile_id and p.id = pesod.ptype_id
        left join base_ktype k on k.id = peso.ktype_id and k.profile_id = peso.profile_id
        left join base_etype e on e.id = peso.etype_id and e.profile_id = peso.profile_id
        left join base_btype b on b.id = peso.btype_id and b.profile_id = peso.profile_id
        left join base_ptype_unit bpu on bpu.profile_id = p.profile_id and bpu.ptype_id = p.id and bpu.unit_code=1
        left join pl_eshop_stock_sync_rule_detail rd on pesod.profile_id=rd.profile_id and
        pesod.stock_sync_rule_id=rd.rule_id
        and rd.ktype_id=peso.ktype_id
        left join pl_eshop_stock_sync_rule r on r.profile_id = rd.profile_id and r.id = rd.rule_id
        where peso.profile_id = #{profileId}
        and pesod.process_state = 0
        and pesod.deleted = 0
        and peso.business_type!=204
        and peso.business_type!=205
        and peso.business_type!=208
        and pesod.local_refund_state != 4
        and pesod.deliver_required = 1
        and peso.self_delivery_mode != 3
        <if test="ptypeId!=null and ptypeId!=0">
            and pesod.ptype_id = #{ptypeId}
        </if>
        <if test="skuId!=null and skuId!=0">
            and pesod.sku_id = #{skuId}
        </if>
        <if test="comboId!=null and comboId!=0">
            and pesodc.combo_id = #{comboId}
        </if>
        and r.id >0
        and r.deleted=0
        and r.rule_type = 3
        and pesod.platform_detail_trade_state in
        <foreach collection="saleOrderState" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and peso.ktype_id in
        <foreach collection="ktypeIdList" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </sql>

    <sql id="lock_advance">
        select toc.vchcode,
        top.trade_id as bill_number,
        '销售-网店预售订单' as vchtype_name,
        top.trade_create_time as bill_date,
        case todp.process_state
        when 0 then '未提交'
        when 1 then '已提交审核'
        when 2 then '已提交预售'
        when 3 then '已提交分期购'
        else '' end as bill_status,
        todc.qty as bill_qty,
        todc.sku_id,
        todc.ptype_id,
        (-1 * todc.qty) as qty,
        bpu.unit_name,
        k.fullname as k_fullname,
        b.fullname as b_fullname,
        e.fullname as e_fullname,
        k.id as ktypeId,
        bps.propvalue_name1,
        bps.propvalue_name2,
        bps.propvalue_name3,
        bps.propvalue_name4,
        bps.propvalue_name5,
        bps.propvalue_name6,
        tdds.batchno,
        tdds.produce_date,
        tdds.expire_date,
        tdds.batch_price,
        p.protect_days_unit,p.protect_days_view,
        datediff(tdds.expire_date, tdds.produce_date) as sell_by_dates
        from td_orderbill_detail_platform todp
        left join td_orderbill_detail_core todc on todp.profile_id = todc.profile_id  and todp.detail_id=todc.detail_id
        left join td_orderbill_platform top on todp.profile_id=top.profile_id and todp.vchcode=top.vchcode
        left join td_orderbill_core toc on todp.profile_id=toc.profile_id  and todp.vchcode=toc.vchcode
        left join td_orderbill_detail_combo co on co.profile_id=todc.profile_id and todc.combo_detail_id=co.id and todc.vchcode=co.vchcode
        left join td_orderbill_detail_serialno tdds on tdds.profile_id=todp.profile_id and tdds.detail_id= todc.detail_id and tdds.vchcode=todc.vchcode
        left join base_ptype p on p.profile_id = todc.profile_id and p.id = todc.ptype_id
        left join base_ktype k on k.id = toc.ktype_id and k.profile_id = toc.profile_id
        left join base_etype e on e.id = toc.etype_id and e.profile_id = toc.profile_id
        left join base_btype b on b.id = toc.btype_id and b.profile_id = toc.profile_id
        left join base_ptype_sku bps on bps.profile_id=todc.profile_id and bps.id=todc.sku_id
        left join base_ptype_unit bpu on bpu.profile_id = p.profile_id and bpu.ptype_id = p.id and bpu.unit_code=1
        left join pl_eshop_stock_sync_rule_detail rd on todp.profile_id=rd.profile_id and
        todp.stock_sync_rule_id=rd.rule_id
        and rd.ktype_id=toc.ktype_id
        left join pl_eshop_stock_sync_rule r on r.profile_id = toc.profile_id and r.id = rd.rule_id
        where todp.profile_id = #{profileId}
        and todp.process_state = 0
        and todp.deleted = 0
        and todp.is_sale_qty_need_to_occupy = 0
        and r.id >0
        and r.deleted=0
        and r.rule_type = 3
        and toc.business_type!=204
        and toc.business_type!=205
        and toc.business_type!=208
        and todp.local_refund_state != 4
        and todp.deliver_required = 1
        and toc.self_delivery_mode != 3
        <if test="ptypeId!=null and ptypeId!=0">
            and todc.ptype_id = #{ptypeId}
        </if>
        <if test="skuId!=null and skuId!=0">
            and todc.sku_id = #{skuId}
        </if>
        <if test="comboId!=null and comboId!=0">
            and co.combo_id = #{comboId}
        </if>
        and todp.platform_detail_trade_state in
        <foreach collection="saleOrderState" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and toc.ktype_id in
        <foreach collection="ktypeIdList" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </sql>

    <sql id="lock_deliver">
        select tbd.vchcode,
        tbc.bill_number,
        vc.fullname as vchtype_name,
        tbc.bill_date,
        case tbc.post_state
        when 0 then '草稿'
        when 100 then '未审核'
        when 300 then '已审核'
        when 500 then '待出入库'
        when 550 then '部分出入库'
        else '' end as bill_status,
        abs(tbdc.qty) as bill_qty,
        tbdc.sku_id,
        tbdc.ptype_id,
        tbdc.qty as qty,
        bpu.unit_name,
        k.fullname as k_fullname,
        b.fullname as b_fullname,
        e.fullname as e_fullname,
        tbc.ktype_id,
        bps.propvalue_name1,
        bps.propvalue_name2,
        bps.propvalue_name3,
        bps.propvalue_name4,
        bps.propvalue_name5,
        bps.propvalue_name6,
        tbdb.batchno,
        tbdb.produce_date,
        tbdb.expire_date,
        tbdb.batch_price,
        p.protect_days_unit,p.protect_days_view,
        datediff(tbdb.expire_date, tbdb.produce_date) as sell_by_dates
        from td_bill_deliver tbd
        left join td_bill_core tbc on tbd.profile_id = tbc.profile_id and tbd.vchcode = tbc.vchcode
        left join td_bill_detail_deliver tbdd on tbd.profile_id = tbdd.profile_id and tbd.vchcode = tbdd.vchcode
        left join td_bill_detail_core tbdc on tbd.profile_id = tbdc.profile_id and tbdc.vchcode = tbdd.vchcode and tbdc.detail_id = tbdd.detail_id
        left join td_bill_detail_batch tbdb on tbdb.profile_id=tbdc.profile_id and tbdc.detail_id = tbdb.detail_id
        left join td_bill_detail_combo co on co.profile_id=tbd.profile_id and co.id=tbdc.combo_detail_id
        left join pl_eshop_stock_sync_rule_detail rd on rd.profile_id=tbd.profile_id and rd.rule_id =
        tbdd.stock_sync_rule_id
        and tbc.ktype_id=rd.ktype_id
        left join pl_eshop_stock_sync_rule rule on rule.profile_id = tbd.profile_id and rule.id = rd.rule_id
        left join base_ptype p on p.profile_id = tbd.profile_id and p.id = tbdc.ptype_id
        left join base_ktype k on k.id = tbc.ktype_id and k.profile_id = tbc.profile_id
        left join base_etype e on e.id = tbc.etype_id and e.profile_id = tbc.profile_id
        left join base_btype b on b.id = tbc.btype_id and b.profile_id = tbc.profile_id
        left join acc_vchtype vc on tbc.vchtype = vc.vchtype and vc.profile_id = tbc.profile_id
        left join base_ptype_sku bps on bps.profile_id=tbdc.profile_id and bps.id=tbdc.sku_id
        left join base_ptype_unit bpu on bpu.profile_id = p.profile_id and bpu.unit_code = 1 and bpu.ptype_id = p.id
        where tbd.profile_id = #{profileId}
        and tbc.deleted = 0
        and tbdd.refund_state != 2
        and tbc.business_type != 204
        and tbc.business_type != 205
        and tbc.business_type != 208
        and tbc.post_state <![CDATA[<]]> 600
        <if test="ptypeId!=null and ptypeId!=0">
            and tbdc.ptype_id = #{ptypeId}
        </if>
        <if test="skuId!=null and skuId!=0">
            and tbdc.sku_id = #{skuId}
        </if>
        <if test="comboId!=null and comboId!=0">
            and co.combo_id = #{comboId}
        </if>
        and rule.id > 0
        and rule.deleted=0
        and rule.rule_type = 3
        and tbdd.trade_state in
        <foreach collection="deliverState" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and tbc.ktype_id in
        <foreach collection="ktypeIdList" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </sql>

    <select id="queryInventoryLockQty"
            resultType="com.wsgjp.ct.sale.biz.analysiscloud.entity.inventory.InventoryLockQtyEntity">
        select * from(
        <include refid="lock_sale"/>
        union all
        <include refid="lock_advance"/>
        union all
        <include refid="lock_deliver"/>
        ) tmp
        <where>
            vchcode is not null
            <if test="filter!=null and filter.size>0">
                <include refid="filter"/>
            </if>
        </where>
    </select>

    <sql id="filter">
        <trim prefix=" and (" suffix=")">
            <foreach collection="filter" item="item" separator="and">
                <choose>
                    <when test="item.type==0">
                        ${item.dataField} like concat('%',#{item.value},'%')
                    </when>
                    <when test="item.type == 1">
                        <trim prefixOverrides="and">
                            <if test="item.value1 != null ">
                                and  ${item.dataField} >= #{item.value1}
                            </if>
                            <if test="item.value2 != null ">
                                and ${item.dataField} <![CDATA[<=]]> #{item.value2}
                            </if>
                        </trim>
                    </when>
                    <when test="item.type == 2">
                        ${item.dataField} = #{item.value}
                    </when>
                </choose>
            </foreach>
        </trim>
    </sql>

    <select id="queryPtypeBatchEnabled" resultType="boolean">
        select batchenabled from base_ptype where profile_id=#{profileId} and id= #{ptypeId}
    </select>

    <select id="queryPtypeBatchEnabledBySkuId" resultType="boolean">
        select p.batchenabled from base_ptype_sku s
                                       left join base_ptype p on s.profile_id=p.profile_id and s.ptype_id=p.id
        where s.profile_id=#{profileId} and s.id=#{skuId}
        limit 1
    </select>
    <select id="querySendQtyListAnalysis" resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockSendQtyEntity">
        select profile_id,ktype_id,sku_id,ptype_id, sum(stockQty) as stockQty,sum(recordQty) as recordQty
        from(
        select re.profile_id,re.ktype_id,re.sku_id,re.ptype_id,re.recordQty,re.stockQty from (
        <include refid="send_inout_ana"/>
        union all
        <include refid="send_wms_out_ana"/>
        union all
        <include refid="send_warehouse_out_ana"/>
        <if test="saleBillSendQtyEnabled">
            union all
            <include refid="send_order_ana"/>
        </if>
        ) re
        union all
        select profile_id,ktype_id,sku_id,ptype_id,qty as stockQty,0 as recordQty from acc_inventory_detail
        <include refid="qty_sql"/>
        and ktype_point_type in
        <foreach collection="ktypePointTypeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ) temp
        group by sku_id,ktype_id
    </select>
    <select id="queryBatchSendQtyListAnalysis"
            resultType="com.wsgjp.ct.sale.sdk.stock.entity.StockBatchSendQtyEntity">
        select profile_id,ktype_id,sku_id,ptype_id, sum(stockQty) as stockQty,sum(recordQty) as recordQty,batchno,produce_date,expire_date,batch_price
        from(
        select re.profile_id,re.ktype_id,re.sku_id,re.ptype_id, re.recordQty,re.stockQty,re.produce_date,re.expire_date,binary re.batchno as batchno,re.batch_price,re.batch_time from (
        <include refid="send_inout_ana"/>
        union all
        <include refid="send_wms_out_ana"/>
        union all
        <include refid="send_billcore_out_ana"/>
        <if test="saleBillSendQtyEnabled">
            union all
            <include refid="send_order_ana"/>
        </if>
        ) re
        union all
        select profile_id,ktype_id,sku_id,ptype_id, 0 as recordQty,qty as stockQty,produce_date,expire_date,binary batchno as batchno,batch_price,batch_time
        from acc_inventory_batch
        <include refid="qty_sql"/>
        and ktype_point_type in
        <foreach collection="ktypePointTypeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        ) temp group by sku_id,ktype_id,batchno,produce_date,expire_date,batch_price

    </select>
    <sql id="qty_sql">
        where profile_id=#{profileId} and ktype_id>0
        <if test="ktypeIdList!=null and ktypeIdList.size()>0">
            and ktype_id in
            <foreach open="(" close=")" collection="ktypeIdList" item="item" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="skuId!=null">
            and sku_id =#{skuId}
        </if>
    </sql>
    <sql id="send_inout_ana">
        select
        tbid.profile_id,
        tbid.ktype_id,
        tbid.sku_id,
        tbid.ptype_id,
        (-1 * tbid.qty) as recordQty,
        0 as stockQty,
        tbdb.produce_date,
        tbdb.expire_date,
        tbdb.batchno,
        tbdc.batch_price,
        '' as batch_time
        from td_bill_inout_detail tbid
        left join td_bill_inout_record tbir on tbid.profile_id=tbir.profile_id and tbir.inout_id=tbid.inout_id
        left join td_bill_warehouse_task tdwt on tdwt.profile_id=tbid.profile_id and tdwt.warehouse_task_id =
        tbir.inout_id
        left join td_bill_core tbc on tbc.profile_id=tbid.profile_id and tbc.vchcode=tbid.vchcode
        left join acc_vchtype vc on tbc.vchtype = vc.vchtype and vc.profile_id = tbc.profile_id
        left join base_ptype p on p.profile_id=tbid.profile_id and p.id=tbid.ptype_id
        left join base_ptype_sku bps on bps.profile_id=tbid.profile_id and bps.id=tbid.sku_id
        left join td_bill_detail_batch tbdb on tbdb.profile_id=tbid.profile_id and
        tbdb.inout_detail_id=tbid.inout_detail_id
        left join td_bill_detail_core tbdc on tbdc.profile_id=tbid.profile_id and tbdc.detail_id=tbid.detail_id
        left join td_bill_deliver_state tbds  on tbds.vchcode = tbc.vchcode and tbds.profile_id = tbc.profile_id
        left join td_bill_detail_deliver tbdd on tbid.vchcode = tbdd.vchcode and tbid.detail_id = tbdd.detail_id and tbdd.profile_id = tbid.profile_id
        where tbid.profile_id = #{profileId}
        and tbir.inout_state != 100
        and tbir.record_type not in (3,13)
        and tbc.business_type!=204
        and tbc.business_type!=205
        and tbc.business_type!=208
        and tbc.deleted=0
        and tbid.ktype_point_type=0
        and (( tbdd.trade_state !=5 and tbdd.refund_state !=2) or tbdd.detail_id is null)

        <if test="skuId!=null and skuId!=0">
            and tbid.sku_id = #{skuId}
        </if>

        and tbid.ktype_id in
        <foreach collection="ktypeIdList" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </sql>

    <sql id="send_wms_out_ana">
        SELECT
        a.profile_id,
        b.ktype_id,
        a.sku_id,
        a.ptype_id,
        (a.qty-a.reality_qty) as recordQty,
        0 as stockQty,
        a.produce_date,a.expire_date,a.batchno,tbdc.batch_price,'' as batch_time
        FROM td_wms_bill_detail_out a
        JOIN `td_wms_bill_core` b ON a.profile_id=b.profile_id AND a.vchcode=b.vchcode
        LEFT JOIN td_bill_core tbc ON tbc.profile_id=a.profile_id AND tbc.vchcode=a.vchcode
        LEFT JOIN td_bill_detail_core tbdc ON tbdc.detail_id=a.detail_id AND tbdc.profile_id=a.profile_id
        LEFT JOIN acc_vchtype vc ON tbc.vchtype = vc.vchtype AND vc.profile_id = tbc.profile_id
        LEFT JOIN base_ptype p ON p.id = a.ptype_id AND p.profile_id = tbc.profile_id
        LEFT JOIN base_ptype_sku bps ON bps.profile_id=a.profile_id AND bps.id=a.sku_id
        WHERE a.profile_id = #{profileId}
        AND b.wms_status!=2
        AND tbc.business_type!=204
        AND tbc.business_type!=205
        AND tbc.business_type!=208

        <if test="skuId!=null and skuId!=0">
            and a.sku_id = #{skuId}
        </if>

        and b.ktype_id in
        <foreach collection="ktypeIdList" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </sql>
    <sql id="send_billcore_out_ana">
        SELECT
        tbdc.profile_id,
        tc.ktype_id,
        tbdc.sku_id,
        tbdc.ptype_id,
        (-1*(tbdc.qty-tbdc.inout_qty))  as recordQty,0 as stockQty,
        tbdb.produce_date,tbdb.expire_date,tbdb.batchno,tbdb.batch_price,'' as batch_time
        from td_bill_deliver tdb
        left join td_bill_core tc on tdb.profile_id = tc.profile_id and tdb.vchcode = tc.vchcode
        left join td_bill_assinfo tba on tba.profile_id=tdb.profile_id and tba.vchcode=tdb.vchcode
        left join td_bill_detail_deliver tbdd on tdb.profile_id = tbdd.profile_id and tdb.vchcode = tbdd.vchcode
        left join td_bill_detail_core tbdc on tdb.profile_id = tbdc.profile_id and tdb.vchcode = tbdc.vchcode and
        tbdd.detail_id=tbdc.detail_id
        left join td_bill_detail_batch tbdb on tbdb.profile_id=tbdc.profile_id and tbdb.detail_id=tbdc.detail_id and
        tbdb.vchcode=tbdc.vchcode
        left join acc_vchtype vc on tc.vchtype = vc.vchtype and vc.profile_id = tc.profile_id
        LEFT JOIN base_ptype p ON p.id = tbdc.ptype_id AND p.profile_id = tbdc.profile_id
        LEFT JOIN base_ptype_sku bps ON bps.profile_id=tbdc.profile_id AND bps.id=tbdc.sku_id
        where tdb.profile_id = #{profileId}
        and tc.deleted=0
        and tc.business_type!=204
        and tc.business_type!=205
        and tc.business_type!=208
        and tbdc.stock_occupy_type=0
        and tbdd.refund_state!=2
        and tbdc.sku_id>0
        and tbdd.send_qty_lock_status=1
        and (tc.post_state <![CDATA[<]]> 500 or (tc.post_state=500 and  tba.deliver_state=0))

        <if test="errBusinessOrderEnabled and nonPaidOrderEnabled">
            and tbdd.trade_state in (0,1,2,3,4,6,7)
        </if>
        <if test="!errBusinessOrderEnabled and nonPaidOrderEnabled">
            and tbdd.trade_state in (1,2,3,4,6,7)
        </if>
        <if test="errBusinessOrderEnabled and !nonPaidOrderEnabled">
            and tbdd.trade_state in (0,2,3,4,6,7)
        </if>
        <if test="!errBusinessOrderEnabled and !nonPaidOrderEnabled">
            and tbdd.trade_state in (2,3,4,6,7)
        </if>

        <if test="skuId!=null and skuId!=0">
            and tbdc.sku_id = #{skuId}
        </if>
        and tbdc.ktype_id in
        <foreach collection="ktypeIdList" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </sql>
    <sql id="send_warehouse_out_ana">
        SELECT
        tbdc.profile_id,
        tc.ktype_id,
        tbdc.sku_id,
        tbdc.ptype_id,
        (-1*(tbdc.qty-tbdc.inout_qty))  as recordQty,0 as stockQty,
        tbdb.produce_date,tbdb.expire_date,tbdb.batchno,tbdb.batch_price,'' as batch_time
        from td_bill_warehouse_task task
        left join td_bill_warehouse_task_detail detail_task on detail_task.warehouse_task_id = task.warehouse_task_id and detail_task.profile_id = task.profile_id
        left join td_bill_detail_deliver tbdd on detail_task.profile_id = tbdd.profile_id and detail_task.detail_id = tbdd.detail_id
        left join td_bill_detail_core tbdc on detail_task.profile_id = tbdc.profile_id and detail_task.detail_id = tbdc.detail_id
        left join td_bill_core tc on tbdc.profile_id = tc.profile_id and tbdc.vchcode = tc.vchcode
        left join td_bill_detail_batch tbdb on tbdb.profile_id=tbdc.profile_id and tbdb.detail_id=tbdc.detail_id and tbdb.vchcode=tbdc.vchcode
        left join acc_vchtype vc on tc.vchtype = vc.vchtype and vc.profile_id = tc.profile_id
        LEFT JOIN base_ptype p ON p.id = tbdc.ptype_id AND p.profile_id = tbdc.profile_id
        LEFT JOIN base_ptype_sku bps ON bps.profile_id=tbdc.profile_id AND bps.id=tbdc.sku_id
        where task.profile_id = #{profileId}
        and tc.deleted=0
        and task.deleted=0
        and task.business_type!=204
        and task.business_type!=205
        and task.business_type!=208
        and tbdc.stock_occupy_type=0
        and tbdd.refund_state!=2
        and tbdc.sku_id>0
        and tbdd.send_qty_lock_status=1
        and task.warehouse_state=1
        and task.wholesale_bill != 1

        <if test="errBusinessOrderEnabled and nonPaidOrderEnabled">
            and tbdd.trade_state in (0,1,2,3,4,6,7)
        </if>
        <if test="!errBusinessOrderEnabled and nonPaidOrderEnabled">
            and tbdd.trade_state in (1,2,3,4,6,7)
        </if>
        <if test="errBusinessOrderEnabled and !nonPaidOrderEnabled">
            and tbdd.trade_state in (0,2,3,4,6,7)
        </if>
        <if test="!errBusinessOrderEnabled and !nonPaidOrderEnabled">
            and tbdd.trade_state in (2,3,4,6,7)
        </if>

        <if test="skuId!=null and skuId!=0">
            and tbdc.sku_id = #{skuId}
        </if>
        and tbdc.ktype_id in
        <foreach collection="ktypeIdList" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
    </sql>

    <sql id="send_order_ana">
        select
        tbdc.profile_id,
        k.id as ktype_id,
        tbdc.sku_id,
        tbdc.ptype_id,
        if(tbdc.qty - tbdc.reference_qty  - sum(if(t.vchcode  is null,0,tbdc2.qty)) &lt; 0,0,tbdc.qty - tbdc.reference_qty - sum(if(t.vchcode  is null,0,tbdc2.qty))) AS recordQty,
        0 as stockQty,
        tdds.produce_date,
        tdds.expire_date,
        ass.batchno,
        tdds.batch_price,
        '' as batch_time
        from td_orderbill_core tbc
        left join td_orderbill_detail_core tbdc on tbc.vchcode = tbdc.vchcode and tbdc.profile_id = tbc.profile_id
        left join td_orderbill_detail_assinfo ass on ass.profile_id=tbc.profile_id and ass.detail_id=tbdc.detail_id
        left join td_orderbill_detail_batch tdds on tdds.profile_id=tbdc.profile_id and tdds.detail_id=tbdc.detail_id
        left join base_ptype p on p.profile_id = tbc.profile_id and p.id = tbdc.ptype_id
        left join base_ptype_sku bps on bps.profile_id=tbdc.profile_id and bps.id=tbdc.sku_id
        left join base_ktype k on k.id = tbdc.ktype_id and k.profile_id = tbdc.profile_id
        left join base_etype e on e.id = tbc.etype_id and e.profile_id = tbdc.profile_id
        left join base_btype b on b.id = tbc.btype_id and b.profile_id = tbdc.profile_id
        left join acc_vchtype vc on tbc.vchtype = vc.vchtype and vc.profile_id = tbdc.profile_id
        left join base_ptype_unit bpu on bpu.profile_id = p.profile_id and bpu.unit_code = 1 and bpu.ptype_id = p.id
        left JOIN td_bill_detail_core tbdc2
        on tbdc.detail_id = tbdc2.source_detail_id AND tbdc2.ptype_id = tbdc.ptype_id  and tbdc.profile_id = tbdc2.profile_id
        LEFT JOIN td_bill_core t ON t.vchcode = tbdc2.vchcode AND t.profile_id = tbdc2.profile_id and t.post_state &lt; 500
        where tbc.profile_id = #{profileId}
        and tbc.orderover_state in (0,3)
        and tbc.post_state = 500
        and tbc.vchtype = 9001
        and tbdc.ktype_id >0
        and tbdc.sku_id >0
        <if test="batchno!=null and batchno!=''">
            and tdds.batchno = #{batchno}
        </if>
        <if test="produceDate!=null">
            and DATE_FORMAT(tdds.produce_date,'%Y-%m-%d') = DATE_FORMAT(#{produceDate},'%Y-%m-%d')
        </if>
        <if test="expireDate!=null">
            and DATE_FORMAT(tdds.expire_date,'%Y-%m-%d') = DATE_FORMAT(#{expireDate},'%Y-%m-%d')
        </if>
        <if test="ptypeId!=null and ptypeId!=0">
            and tbdc.ptype_id = #{ptypeId}
        </if>
        <if test="skuId!=null and skuId!=0">
            and tbdc.sku_id = #{skuId}
        </if>
        and tbdc.ktype_id in
        <foreach collection="ktypeIdList" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        and vc.vchtype not in
        <foreach collection="ignoreVchtype" separator="," open="(" close=")" index="index" item="item">
            #{item}
        </foreach>
        group  by tbdc.detail_id having recordQty > 0
    </sql>
</mapper>
package com.wsgjp.ct.sale.biz.eshoporder.service.aftersale.manager;

import bf.datasource.page.PageDevice;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.api.request.wms.OnlineFreightInterceptRequest;
import com.wsgjp.ct.sale.biz.api.response.openwms.OnlineFreightInterceptResponse;
import com.wsgjp.ct.sale.biz.api.wms.WmsApi;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.refund.AfterSaleFreightInterceptStatus;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.freightintercept.AfterSaleFreightInterceptEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.aftersale.AssignInterceptStatusRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.aftersale.QueryFreightInterceptParameter;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.aftersale.AfterSaleFreightInterceptMapper;
import com.wsgjp.ct.sale.biz.shopsale.common.FeignResult;
import com.wsgjp.ct.support.context.CurrentUser;
import ngp.idgenerator.UId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service

public class AfterSaleFreightInterceptService {
    private static final Logger logger = LoggerFactory.getLogger(AfterSaleFreightInterceptService.class);
    private AfterSaleFreightInterceptMapper afterSaleFreightInterceptMapper;

    private WmsApi wmsApi;

    public AfterSaleFreightInterceptService(AfterSaleFreightInterceptMapper afterSaleFreightInterceptMapper, WmsApi wmsApi) {
        this.afterSaleFreightInterceptMapper = afterSaleFreightInterceptMapper;
        this.wmsApi = wmsApi;
    }

    public PageResponse<AfterSaleFreightInterceptEntity> queryAfterSaleFreightInterceptPageInfo(PageRequest<QueryFreightInterceptParameter> parameter)
    {
        PageDevice.initPage(parameter, false);

        List<AfterSaleFreightInterceptEntity> interceptEntityList= afterSaleFreightInterceptMapper.queryAfterSaleFreightInterceptList(parameter.getQueryParams());
        return PageDevice.readPage(interceptEntityList);
    }
    public List<AfterSaleFreightInterceptEntity> queryAfterSaleFreightIntercept(QueryFreightInterceptParameter param)
    {
        List<AfterSaleFreightInterceptEntity> interceptEntityList= afterSaleFreightInterceptMapper.queryAfterSaleFreightInterceptList(param);
        return interceptEntityList;
    }
    public String doOnlineFreightIntercept(AssignInterceptStatusRequest assignInterceptStatusRequest)
    {
        if (assignInterceptStatusRequest.getInterceptEntityList()==null||assignInterceptStatusRequest.getInterceptEntityList().isEmpty())
        {
            return "没有需要更新的数据，请重新选择后操作！";
        }
        List<OnlineFreightInterceptRequest> wmsInterceptRequest=new ArrayList<>();
        for (AfterSaleFreightInterceptEntity item: assignInterceptStatusRequest.getInterceptEntityList()) {
            OnlineFreightInterceptRequest req=new OnlineFreightInterceptRequest();
            req.setFreightInfoId(item.getFreightInterceptId());
            wmsInterceptRequest.add(req);
        }
        try {
            FeignResult<List<OnlineFreightInterceptResponse>> result = wmsApi.wmsFreightIntercept(wmsInterceptRequest);
            if (!Objects.equals(result.getCode(), "200")) {
                return "调用物流拦截失败" + result.getMessage();
            } else {
                if (result.getData() == null || result.getData().isEmpty()) {
                    return "调用物流拦截返回结果异常，数据为空" + result.getMessage();
                }
                List<BigInteger> successList = result.getData().stream().filter(OnlineFreightInterceptResponse::isSueecess).map(OnlineFreightInterceptResponse::getFreightInfoId).collect(Collectors.toList());
                List<AfterSaleFreightInterceptEntity> interceptEntityList = assignInterceptStatusRequest.getInterceptEntityList();
                for (AfterSaleFreightInterceptEntity item : interceptEntityList) {
                    item.setId(UId.newId());
                    item.setProfileId(CurrentUser.getProfileId());
                }
                interceptEntityList = interceptEntityList.stream().filter(x -> successList.contains(x.getFreightInterceptId())).collect(Collectors.toList());
                if (interceptEntityList.size()>0)
                {
                    int count = afterSaleFreightInterceptMapper.doAssignFreightInterceptStatus(interceptEntityList, CurrentUser.getProfileId(), AfterSaleFreightInterceptStatus.SUCCEED_ONLINE_INTERCEPT);

                }
            }
        }
        catch (Exception ex)
        {
            logger.error("执行wms物流拦截失败："+ex.getMessage(),ex);
        }
        return "";
    }
    public String assignFreightInterceptStatus(AssignInterceptStatusRequest assignInterceptStatusRequest)
    {
        if (assignInterceptStatusRequest.getInterceptEntityList()==null||assignInterceptStatusRequest.getInterceptEntityList().isEmpty())
        {
            return "没有需要更新的数据，请重新选择后操作！";
        }
        if (assignInterceptStatusRequest.getInterceptStatus()==null)
        {
            return "错误的拦截状态，请重新选择后操作！";
        }
        List<AfterSaleFreightInterceptEntity> interceptEntityList= assignInterceptStatusRequest.getInterceptEntityList();
        for (AfterSaleFreightInterceptEntity item:interceptEntityList) {
            item.setId(UId.newId());
            item.setProfileId(CurrentUser.getProfileId());
        }

        int count=afterSaleFreightInterceptMapper.doAssignFreightInterceptStatus(assignInterceptStatusRequest.getInterceptEntityList(), CurrentUser.getProfileId(),assignInterceptStatusRequest.getInterceptStatus());
        return "";
    }
}

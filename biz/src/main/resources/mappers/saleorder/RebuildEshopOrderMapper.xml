<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.RebuildEshopOrderMapper">
    <insert id="insertEshopRefundConfigReason">
        INSERT INTO pl_eshop_refund_config_reason(`id`, `profile_id`, `reason_type`, `refund_reason`, `deleted`)
        values(#{id},#{profileId},0,#{reason},0);
    </insert>
    <update id="clearEshopAuth">
        UPDATE `pl_eshop`
        SET `online_eshop_id`='',
            `token` ='',
            `app_key`='',
            `app_secret`='',
            `refresh_token` ='',
            `token_expire_in`=NULL,
            `token_r1expire_in`=NULL,
            `refresh_token_expire_in`=NULL,
            `refresh_time`=NULL,
            `has_token_expired`=0,
            `has_exception`=0,
            `update_time`=now()
        WHERE profile_id = #{profileId}
    </update>
    <update id="clearEshopConfig">
        UPDATE `pl_eshop_config`
        SET `rds_enabled`=0,
            `rds_apply_time`=NULL,
            `auto_sync_order_enabled`=0,
            `auto_sync_stock_enabled`=0,
            `msg_notify_enabled`=0,
            `tmc_enabled`=0
        WHERE `profile_id` = #{profileId}
    </update>

    <update id="disaleAutoSync">
        update pl_eshop_config
        set auto_sync_order_enabled=0,
            auto_sync_stock_enabled=0
        where profile_id = #{profileId}
    </update>


    <delete id="deletePlatformBtypeMap">
        delete from pl_eshop_platform_btype_mapping where profile_id = #{profileId};
    </delete>
</mapper>
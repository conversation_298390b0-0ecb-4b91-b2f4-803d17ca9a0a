<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopOrderRelationMapper">
    <insert id="replaceUnRelationItem">
        REPLACE
        INTO pl_eshop_sale_order_to_be_matched_item
        (id,profile_id,eshop_id,platform_ptype_name,platform_properties_name,platform_ptype_id,platform_sku_id,platform_ptype_xcode,hash_key,platform_ptype_pic_url,has_normal_order)
        value
        (
        #{id},
        #{profileId},
        #{eshopId},
        #{platformPtypeName},
        #{platformPropertiesName},
        #{platformPtypeId},
        #{platformSkuId},
        #{platformPtypeXcode},
        #{hashKey},
        #{platformPtypePicUrl},
        #{hasNormalOrder}
        )
    </insert>

    <insert id="batchInsertUnRelationItem">
        INSERT INTO pl_eshop_sale_order_to_be_matched_item
        (id,profile_id,eshop_id,platform_ptype_name,platform_properties_name,platform_ptype_id,platform_sku_id,
         platform_ptype_xcode,hash_key,platform_ptype_pic_url)
        values
        <foreach collection="relations" separator="," item="item">
            (#{item.id},
            #{item.profileId},
            #{item.eshopId},
            #{item.platformPtypeName},
            #{item.platformPropertiesName},
            #{item.platformPtypeId},
            #{item.platformSkuId},
            #{item.platformPtypeXcode},
            #{item.hashKey},
            #{item.platformPtypePicUrl})
        </foreach>
    </insert>



    <delete id="clearUnRelationItem">
        delete
        from pl_eshop_sale_order_to_be_matched_item
        where profile_id = #{profileId}
          and hash_key = #{hashKey}
    </delete>

    <delete id="clearUnRelationItemById">
        delete
        from pl_eshop_sale_order_to_be_matched_item
        where profile_id = #{profileId}
          and id = #{id}
    </delete>

    <select id="queryUnRelationItems" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.OrderRelationOperation">
        SELECT
        peps.*,
        peps.platform_pic_url AS platformPtypePicUrl,
        e.fullname AS eshopName,
        e.otype_id AS eshopId,
        pep.platform_fullname AS platformPtypeName,
        peps.platform_properties_name AS platformPropertiesName,
        peps.platform_xcode AS platformPtypeXcode,
        peps.platform_num_id AS platformPtypeId,
        peps.eshop_id,peps.platform_price as price
        FROM pl_eshop_product_sku peps
        LEFT JOIN pl_eshop e ON e.profile_id = peps.profile_id AND e.otype_id = peps.eshop_id
        LEFT JOIN pl_eshop_product pep ON pep.profile_id = peps.profile_id AND pep.platform_num_id = peps.platform_num_id AND pep.eshop_id=peps.eshop_id
        JOIN(SELECT profile_id,unique_id FROM pl_eshop_product_mark WHERE profile_id=#{profileId}  AND mark_code = 1015 AND mark_code != 1001 GROUP BY profile_id, unique_id) temp
            ON temp.profile_id = peps.profile_id AND temp.unique_id = peps.unique_id
        WHERE peps.profile_id =#{profileId}
        <if test="eshopId!=null">
            AND peps.eshop_id=#{eshopId}
        </if>
        <if test="insertUnRelationByNormalOrder != -1">
            AND peps.has_normal_order=#{insertUnRelationByNormalOrder}
        </if>
        <if test="platformSkuInfo!=null and platformSkuInfo!=''">
            AND (pep.platform_fullname LIKE CONCAT('%',#{platformSkuInfo},'%')
            OR peps.platform_xcode LIKE CONCAT('%',#{platformSkuInfo},'%')
            OR peps.platform_properties_name LIKE CONCAT('%',#{platformSkuInfo},'%'))
        </if>
        <if test="hashKeyList!=null and hashKeyList.size()>0">
            AND peps.unique_id IN
            <foreach collection="hashKeyList" close=")" open="(" separator="," item="item">
                #{item}
            </foreach>
        </if>

        <if test="markList != null and markList.size()>0 ">
            AND  EXISTS(SELECT 1 FROM pl_eshop_product_mark  WHERE profile_id=#{profileId}  AND unique_id = peps.unique_id AND mark_code IN
            <foreach collection="markList" item="mark" index="i" separator="," open="(" close=")">
                #{mark}
            </foreach> )
        </if>
    </select>

    <select id="queryRelationDetailsCount" resultType="java.lang.Integer">
        select count(*)
        from pl_eshop_sale_order_detail d
        LEFT JOIN base_ptype p ON p.id = d.ptype_id AND  p.profile_id = d.profile_id
        where d.profile_id = #{profileId}
        <if test="eshopOrderIdList!=null and eshopOrderIdList.size()>0">
            and d.eshop_order_id in
            <foreach collection="eshopOrderIdList" close=")" open="(" separator="," item="eshopOrderId">
                #{eshopOrderId}
            </foreach>
        </if>
    </select>

    <select id="queryRelationDetails" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderDetail">
        select d.*,p. pcategory
        from pl_eshop_sale_order_detail d
        LEFT JOIN base_ptype p ON p.id = d.ptype_id AND  p.profile_id = d.profile_id
        where d.profile_id = #{profileId}
        <if test="eshopOrderIdList!=null and eshopOrderIdList.size()>0">
            and d.eshop_order_id in
            <foreach collection="eshopOrderIdList" close=")" open="(" separator="," item="eshopOrderId">
                #{eshopOrderId}
            </foreach>
        </if>
    </select>

    <select id="querySimpleOrder" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity">
        select `order`.*, `timing`.plan_send_time AS 'timing.plan_send_time'
        from pl_eshop_sale_order `order`
                 LEFT JOIN pl_eshop_sale_order_timing timing ON timing.profile_id = `order`.profile_id AND
                                                                timing.eshop_order_id = `order`.id
        where `order`.profile_id = #{profileId}
          and `order`.id = #{id}
    </select>


    <update id="batchModifyOrderRelationInfo" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update pl_eshop_sale_order set
            mapping_state=#{item.mappingState},
            dised_taxed_total=#{item.disedTaxedTotal},
            order_deliver_required=#{item.orderDeliverRequired},
            deliver_type=#{item.deliverType},
            tax_total=#{item.taxTotal}
            where profile_id = #{item.profileId}
            and id = #{item.id}
        </foreach>
    </update>

    <update id="modifyOrderTotal">
        update pl_eshop_sale_order
        set ptype_preferential_total=#{ptypeTreferentialTotal},
            dised_taxed_total=#{disedTaxedTotal},
            total=#{taxedTotal}
        where profile_id = #{profileId}
          and id = #{id}
    </update>

    <update id="batchModifyOrderPlanSendTime">
        <foreach collection="list" item="item" separator=";">
            update pl_eshop_sale_order_timing
            set plan_send_time = #{item.planSendTime}
            where profile_id = #{item.profileId}
            and eshop_order_id = #{item.eshopOrderId}
        </foreach>
    </update>

    <select id="queryRelationRefundDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundApplyDetail">
        select *
        from pl_eshop_refund_apply_detail
        where profile_id = #{profileId}
          and vchcode = #{vchcode}
    </select>
    <select id="queryAdvanceRelationDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderDetail">
         select *
        from td_orderbill_detail_core
        where profile_id = #{profileId}
          and eshop_order_id = #{eshopOrderId}
    </select>
    <select id="querySimpleAdvanceOrder"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderEntity">
          select `order`.*, `timing`.plan_send_time AS 'timing.plan_send_time'
        from td_orderbill_core `order`
                 LEFT JOIN td_orderbill_core_timing timing ON timing.profile_id = `order`.profile_id AND
                                                                timing.vchcode = `order`.vchcode
        where `order`.profile_id = #{profileId}
          and `order`.vchcode = #{id}

    </select>

    <update id="modifyRefundMappingState">
        update pl_eshop_refund
        set mapping_state=#{hasMapping}
        where profile_id = #{profileId}
          and id = #{id}
    </update>
    <update id="modifyAdvanceOrderPlanSendTime">
          update pl_eshop_sale_order_advance_timing
        set plan_send_time = #{planSendTime}
        where profile_id = #{profileId}
          and eshop_order_id = #{eshopOrderId}
    </update>
    <update id="modifyAdvanceOrderMentionType">
         update td_orderbill_platform
        set
            mapping_state=#{advance.platform.mappingState},
            deliver_required=#{advance.platform.orderDeliverRequired}
        where profile_id = #{advance.profileId}
          and vchcode = #{advance.vchcode}
    </update>

    <update id="clearOrderUnRelationStatusByVchcode">
        update pl_eshop_sale_order
        set mapping_state=1
        where profile_id = #{profileId}
        and id in
        <foreach collection="idList" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </update>

    <select id="queryUnRelationDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.OrderRelationOperation">
        SELECT id,eshop_order_id,profile_id,otype_id as eshop_id,ptype_id,price,
        platform_ptype_id,platform_sku_id,platform_ptype_xcode,platform_ptype_pic_url,platform_ptype_name,platform_properties_name
        FROM pl_eshop_sale_order_detail
        WHERE mapping_state=0  AND profile_id =#{profileId} AND deliver_required != 0
        and eshop_order_id in
        <foreach collection="eshopOrderIdList" open="(" separator="," close=")" item="eshopOrderId">
            #{eshopOrderId}
        </foreach>
        UNION
        SELECT d.eshop_order_detail_id AS id,r.eshop_order_id AS vchcode,d.profile_id,r.otype_id as eshop_id,d.ptype_id,d.dised_taxed_price as price,
        d.platform_num_id,d.platform_sku_id,d.platform_xcode,d.platform_pic_url, d.platform_full_name AS platform_product_name,d.platform_properties_name
        FROM `pl_eshop_refund_apply_detail` d
        LEFT JOIN `pl_eshop_refund` r ON d.profile_id = r.profile_id  AND d.refund_order_id =  r.id
        LEFT JOIN pl_eshop_sale_order_detail od on d.profile_id=od.profile_id and d.eshop_order_detail_id=od.id
        WHERE d.ptype_id=0  AND d.profile_id =#{profileId} and d.eshop_order_detail_id != 0 and od.deliver_required !=0
        and r.eshop_order_id in
        <foreach collection="eshopOrderIdList" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="queryHashKeyHasExist" resultType="java.lang.String">
        SELECT hash_key FROM `pl_eshop_sale_order_to_be_matched_item`
        WHERE  profile_id =#{profileId}
        and hash_key in
        <foreach collection="hashKeyList" open="(" separator="," close=")" item="key">
            #{key}
        </foreach>
    </select>

    <select id="queryUniqueIdHasExist" resultType="java.lang.String">
        SELECT distinct (sku.unique_id) FROM `pl_eshop_product_sku` sku
        INNER JOIN pl_eshop_product_mark mark ON sku.profile_id=mark.profile_id AND sku.unique_id = mark.unique_id AND mark_code=1015
        WHERE  sku.profile_id =#{profileId}
        and sku.unique_id in
        <foreach collection="uniqueIdList" open="(" separator="," close=")" item="key">
            #{key}
        </foreach>
    </select>

    <select id="queryUnRelationRefundSendDetails"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.OrderRelationOperation">
        SELECT d.id,r.eshop_order_id AS vchcode,d.profile_id,r.otype_id as eshop_id,d.ptype_id,d.dised_taxed_price as price,
        d.platform_num_id as platform_ptype_id ,d.platform_sku_id,d.platform_xcode as platform_ptype_xcode,d.platform_full_name AS platform_ptype_name,d.platform_properties_name
        FROM `pl_eshop_refund_send_detail` d
        LEFT JOIN `pl_eshop_refund` r ON d.profile_id = r.profile_id  AND d.refund_order_id =  r.id
        WHERE d.mapping_state=0  AND d.profile_id =#{profileId}
        and r.eshop_order_id in
        <foreach collection="eshopOrderIdList" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>

    </select>

    <select id="queryMappingTypeBySaleOrderDetail" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuExpand">
        select expand.* from pl_eshop_product_sku_expand expand
                          left join pl_eshop_product_sku sku on sku.profile_id= expand.profile_id and sku.unique_id = expand.unique_id
                 where expand.profile_id=#{profileId} and expand.unique_id=#{uniqueId} AND sku.unique_id IS NOT null limit 1
    </select>

    <select id="queryProductExpandByUniqueIds" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuExpand">
        select expand.* from pl_eshop_product_sku_expand expand
            left join pl_eshop_product_sku sku on sku.profile_id= expand.profile_id and sku.unique_id = expand.unique_id
                 where expand.profile_id=#{profileId} AND sku.unique_id IS NOT null
        and expand.unique_id in
        <foreach collection="uniqueIds" open="(" separator="," close=")" item="key">
            #{key}
        </foreach>
    </select>

    <select id="checkProductHasNormalOrder" resultType="java.lang.Boolean">
        select 1
        from pl_eshop_sale_order_detail d
        where d.profile_id = #{profileId}
          and d.otype_id = #{otypeId}
          and d.platform_ptype_id = #{platformPtypeId}
          and d.platform_sku_id = #{platformSkuId}
          and d.local_refund_state != 4
          and d.platform_detail_trade_state != 5
          and not exists(select 1
                     from pl_eshop_order_mark m
                     where m.profile_id = d.profile_id
                       and m.mark_target = 1
                       and m.order_id = d.eshop_order_id
                       and m.detail_id = d.id
                       and m.mark_code = 90870001)
    </select>

    <select id="checkProductHasDeletedOrder" resultType="java.lang.Boolean">
        select 1
        from pl_eshop_sale_order_detail d
        where d.profile_id = #{profileId}
          and d.otype_id = #{otypeId}
          and d.platform_ptype_id = #{platformPtypeId}
          and d.platform_sku_id = #{platformSkuId}
          and d.platform_properties_name = #{platformPropertiesName}
          and d.deleted=0 limit 1
    </select>

    <select id="checkSaleOrderIsFirst" resultType="java.lang.Boolean">
        select count(0) = 0
        from pl_eshop_sale_order_detail d
        where d.profile_id = #{profileId}
          and d.otype_id = #{otypeId}
          and d.platform_ptype_id = #{platformPtypeId}
          and d.platform_sku_id = #{platformSkuId}
    </select>

    <select id="checkProductHasClosedOrder" resultType="java.lang.Boolean">
        select 1
        from pl_eshop_sale_order_detail d
        where d.profile_id = #{profileId}
          and d.otype_id = #{otypeId}
          and d.platform_ptype_id = #{platformPtypeId}
          and d.platform_sku_id = #{platformSkuId}
          and d.platform_properties_name = #{platformPropertiesName}
          and d.platform_detail_trade_state != 5 limit 1
    </select>
</mapper>
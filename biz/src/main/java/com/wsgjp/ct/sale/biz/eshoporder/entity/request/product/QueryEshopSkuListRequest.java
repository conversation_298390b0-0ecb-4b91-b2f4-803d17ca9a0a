package com.wsgjp.ct.sale.biz.eshoporder.entity.request.product;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.BaseQuery;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuPageData;

import java.math.BigInteger;
import java.util.List;


/**
 * <AUTHOR>
 */
public class QueryEshopSkuListRequest extends BaseQuery {

    private List<BigInteger> otypeIdList;

    private String localInfo;
    private String platformInfo;

    private int xcodeQueryType;

    private ShopType shopType;

    private int mappingState;
    /**
     * 排序字段
     */
    private String orderCase = "pro.id";
    /**
     * 排序类型
     */
    private String orderTag = "desc";

    /**
     * 列配置是否需要查询本地图片
     */
    private Boolean queryLocalPicEnabled = false;

    /**
     * 列配置是否需要查询本地商品属性
     */
    private Boolean queryLocalPropEnabled = true;

    /**
     * 列配置是否需要查询本地商品品牌
     */
    private Boolean queryLocalBrand = false;

    /**
     * 列配置是否需要查询本地商品单位
     */
    private Boolean queryUnitNameEnabled = true;

    /**
     * 页面模式：商品管理则不查询库存规则
     */
    private Boolean queryStockRuleEnabled = false;

    private List<String> uniqueIds;

    private String eshopName;
    private String platformNumId;
    private String platformFullName;
    private String platformXcode;
    private String platformSkuId;
    private String platformPropertiesName;
    private String platformUnitName;
    private Integer platformStockState;
    private String ptypeName;
    private String xcode;
    private String propValueNames;
    private String standard;
    private String ptypeType;
    private String brandName;
    private Boolean autoSyncEnabled;

    /**
     * 部分刷新的时候使用
     */
    private List<EshopProductSkuPageData> dataList;

    private List<BigInteger> eshopProductSkuIds;

    public String getLocalInfo() {
        return localInfo;
    }

    public void setLocalInfo(String localInfo) {
        this.localInfo = localInfo;
    }

    public String getPlatformInfo() {
        return platformInfo;
    }

    public void setPlatformInfo(String platformInfo) {
        this.platformInfo = platformInfo;
    }

    public List<BigInteger> getOtypeIdList() {
        return otypeIdList;
    }

    public void setOtypeIdList(List<BigInteger> otypeIdList) {
        this.otypeIdList = otypeIdList;
    }

    public int getMappingState() {
        return mappingState;
    }

    public void setMappingState(int mappingState) {
        this.mappingState = mappingState;
    }

    public int getXcodeQueryType() {
        return xcodeQueryType;
    }

    public void setXcodeQueryType(int xcodeQueryType) {
        this.xcodeQueryType = xcodeQueryType;
    }

    public String getOrderCase() {
        return orderCase;
    }

    public void setOrderCase(String orderCase) {
        this.orderCase = orderCase;
    }

    public String getOrderTag() {
        return orderTag;
    }

    public void setOrderTag(String orderTag) {
        this.orderTag = orderTag;
    }

    public Boolean getQueryLocalPicEnabled() {
        return queryLocalPicEnabled;
    }

    public void setQueryLocalPicEnabled(Boolean queryLocalPicEnabled) {
        this.queryLocalPicEnabled = queryLocalPicEnabled;
    }

    public Boolean getQueryLocalBrand() {
        return queryLocalBrand;
    }

    public void setQueryLocalBrand(Boolean queryLocalBrand) {
        this.queryLocalBrand = queryLocalBrand;
    }

    public Boolean getQueryUnitNameEnabled() {
        return queryUnitNameEnabled;
    }

    public void setQueryUnitNameEnabled(Boolean queryUnitNameEnabled) {
        this.queryUnitNameEnabled = queryUnitNameEnabled;
    }

    public Boolean getQueryLocalPropEnabled() {
        return queryLocalPropEnabled;
    }

    public void setQueryLocalPropEnabled(Boolean queryLocalPropEnabled) {
        this.queryLocalPropEnabled = queryLocalPropEnabled;
    }

    public String getEshopName() {
        return eshopName;
    }

    public void setEshopName(String eshopName) {
        this.eshopName = eshopName;
    }

    public String getPlatformNumId() {
        return platformNumId;
    }

    public void setPlatformNumId(String platformNumId) {
        this.platformNumId = platformNumId;
    }

    public String getPlatformFullName() {
        return platformFullName;
    }

    public void setPlatformFullName(String platformFullName) {
        this.platformFullName = platformFullName;
    }

    public String getPlatformXcode() {
        return platformXcode;
    }

    public void setPlatformXcode(String platformXcode) {
        this.platformXcode = platformXcode;
    }

    public String getPlatformSkuId() {
        return platformSkuId;
    }

    public void setPlatformSkuId(String platformSkuId) {
        this.platformSkuId = platformSkuId;
    }

    public String getPlatformPropertiesName() {
        return platformPropertiesName;
    }

    public void setPlatformPropertiesName(String platformPropertiesName) {
        this.platformPropertiesName = platformPropertiesName;
    }

    public String getPlatformUnitName() {
        return platformUnitName;
    }

    public void setPlatformUnitName(String platformUnitName) {
        this.platformUnitName = platformUnitName;
    }

    public Integer getPlatformStockState() {
        return platformStockState;
    }

    public void setPlatformStockState(Integer platformStockState) {
        this.platformStockState = platformStockState;
    }

    public String getPtypeName() {
        return ptypeName;
    }

    public void setPtypeName(String ptypeName) {
        this.ptypeName = ptypeName;
    }

    public String getXcode() {
        return xcode;
    }

    public void setXcode(String xcode) {
        this.xcode = xcode;
    }

    public String getPropValueNames() {
        return propValueNames;
    }

    public void setPropValueNames(String propValueNames) {
        this.propValueNames = propValueNames;
    }

    public String getStandard() {
        return standard;
    }

    public void setStandard(String standard) {
        this.standard = standard;
    }

    public String getPtypeType() {
        return ptypeType;
    }

    public void setPtypeType(String ptypeType) {
        this.ptypeType = ptypeType;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public Boolean getQueryStockRuleEnabled() {
        return queryStockRuleEnabled;
    }

    public void setQueryStockRuleEnabled(Boolean queryStockRuleEnabled) {
        this.queryStockRuleEnabled = queryStockRuleEnabled;
    }

    public List<BigInteger> getEshopProductSkuIds() {
        return eshopProductSkuIds;
    }

    public void setEshopProductSkuIds(List<BigInteger> eshopProductSkuIds) {
        this.eshopProductSkuIds = eshopProductSkuIds;
    }

    public List<EshopProductSkuPageData> getDataList() {
        return dataList;
    }

    public void setDataList(List<EshopProductSkuPageData> dataList) {
        this.dataList = dataList;
    }

    public List<String> getUniqueIds() {
        return uniqueIds;
    }

    public void setUniqueIds(List<String> uniqueIds) {
        this.uniqueIds = uniqueIds;
    }

    public ShopType getShopType() {
        return shopType;
    }

    public void setShopType(ShopType shopType) {
        this.shopType = shopType;
    }

    public Boolean getAutoSyncEnabled() {
        return autoSyncEnabled;
    }

    public void setAutoSyncEnabled(Boolean autoSyncEnabled) {
        this.autoSyncEnabled = autoSyncEnabled;
    }
}

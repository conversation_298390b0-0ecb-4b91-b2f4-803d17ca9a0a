package com.wsgjp.ct.sale.biz.eshoporder.entity.refund.freightintercept;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.refund.AfterSaleFreightInterceptStatus;

public class InterceptStatusConverter implements Converter<AfterSaleFreightInterceptStatus> {
    @Override
    public Class supportJavaTypeKey() {
        return AfterSaleFreightInterceptStatus.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public AfterSaleFreightInterceptStatus convertToJavaData(CellData cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (cellData.toString().equals("待拦截"))
        {
            return AfterSaleFreightInterceptStatus.WAIT_INTERCEPT;
        }
        if (cellData.toString().equals(AfterSaleFreightInterceptStatus.BEGIN_INTERCEPT.getName()))
        {
            return AfterSaleFreightInterceptStatus.BEGIN_INTERCEPT;
        }
        if (cellData.toString().equals(AfterSaleFreightInterceptStatus.FAIL_INTERCEPT.getName()))
        {
            return AfterSaleFreightInterceptStatus.FAIL_INTERCEPT;
        }
        if (cellData.toString().equals(AfterSaleFreightInterceptStatus.SUCCEED_ONLINE_INTERCEPT.getName()))
        {
            return AfterSaleFreightInterceptStatus.SUCCEED_ONLINE_INTERCEPT;
        }
        if (cellData.toString().equals(AfterSaleFreightInterceptStatus.SUCCEED_STOCK_INTERCEPT.getName()))
        {
            return AfterSaleFreightInterceptStatus.SUCCEED_STOCK_INTERCEPT;
        }

        return null;
    }

    @Override
    public CellData convertToExcelData(AfterSaleFreightInterceptStatus afterSaleFreightInterceptStatus, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new CellData(afterSaleFreightInterceptStatus.getName());
    }
}

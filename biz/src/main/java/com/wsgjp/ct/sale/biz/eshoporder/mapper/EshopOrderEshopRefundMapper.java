package com.wsgjp.ct.sale.biz.eshoporder.mapper;

import bf.datasource.page.Sort;
import bf.datasource.wings.provide.WingsCutSelect;
import com.wsgjp.ct.bill.core.handle.entity.*;
import com.wsgjp.ct.bill.core.handle.entity.dao.BillRelation;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.eshoporder.api.response.deliver.RefundAndDeliverMappingEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.RefundChangeConstantEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.RefundReasonType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.DayRefundCount;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderDetail;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.GoodsBatchEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.GoodsSerialEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.*;
import com.wsgjp.ct.sale.biz.eshoporder.service.aftersale.manager.request.AfterSaleListQueryRequest;
import com.wsgjp.ct.sale.biz.eshoporder.service.aftersale.manager.request.BillRelationRequest;
import com.wsgjp.ct.sale.biz.jarvis.dto.BillDeliverDTO;
import com.wsgjp.ct.sale.biz.jarvis.entity.BigData;
import com.wsgjp.ct.sale.biz.jarvis.entity.statistics.DataStatisticsDAO;
import com.wsgjp.ct.sale.biz.jarvis.entity.statistics.DataStatisticsEntity;
import com.wsgjp.ct.sale.biz.jarvis.entity.statistics.DataStatisticsRequest;
import com.wsgjp.ct.sale.biz.refund.refund.entity.RefundDuty;
import com.wsgjp.ct.sale.biz.refund.refund.entity.request.GetHistoryRefundNumbersRequest;
import com.wsgjp.ct.sale.biz.refund.refund.entity.request.GetReportDataRequest;
import com.wsgjp.ct.sale.biz.refund.refund.entity.request.NotifyRefundSendStateRequest;
import com.wsgjp.ct.sale.biz.refund.refund.entity.request.UpdateRefundDutyRequest;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.RefundReportDomain;
import com.wsgjp.ct.sale.common.entity.request.DeleteBillRequest;
import com.wsgjp.ct.sale.platform.entity.request.refund.DownloadRefundInfoParam;
import com.wsgjp.ct.sale.platform.enums.RefundTypeEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Repository
public interface EshopOrderEshopRefundMapper {

    /**
     * 查询售后单列表（主表信息）
     *
     * @param refundParameter 订单号、状态等
     * @return 售后单列表
     */
    @WingsCutSelect(entityLinked = "id", selectProp = "refund.id")
    List<EshopRefundEntity> queryRefundListQuick(QueryRefundParameter refundParameter);


    //    @WingsCutSelect(entityLinked = "id",selectProp = "refund.id")
    List<EshopRefundEntity> queryRefundList(QueryRefundParameter refundParameter);

    List<EshopRefundEntity> queryRefundListSimple(QueryRefundParameter refundParameter);

    /**
     * 查询售后单数量
     *
     * @param refundParameter 订单号、状态等
     * @return 售后单数量
     */
//    @WingsCutSelect
    int queryRefundListCount(QueryRefundParameter refundParameter);

    /**
     * 查询售后单列表数量
     *
     * @param refundParameter 订单号、状态等
     * @return 订单列表
     */
    int queryRefundCount(QueryRefundParameter refundParameter);

    /**
     * 查询90天内所有数据
     *
     * @param refundParameter 订单号、状态等
     * @return 售后单列表
     */
    int queryRefundCountAll(QueryRefundParameter refundParameter);

    /**
     * 查询每天线上下载订单数量
     *
     * @param param
     * @return
     */
    List<DayRefundCount> queryEveryDateLocalRefundCount(QueryRefundCountParam param);

    /**
     * 根据售后单的vchcode查询售后单上的部分需要记录日志的信息
     *
     * @param profileId 账套ID
     * @param id        售后单ID
     * @return 售后单
     */
    EshopRefundEntity queryRefundLogInfoByVchcode(@Param("profileId") BigInteger profileId, @Param("id") BigInteger id);

    List<EshopRefundApplyDetail> queryRefundDetailList(QueryRefundDetailParameter detailParameter);

    List<EshopRefundApplyDetail> queryRefundDetailListByVchcodes(@Param("profileId") BigInteger profileId, @Param("refundOrderIds") List<BigInteger> refundOrderIds, @Param("detailCreateType") Integer detailCreateType);


    /**
     * 优化后-查询总数据量
     */
    long queryRefundDetailVchcodesCount(@Param("profileId") BigInteger profileId, @Param("detailParameter") QueryRefundDetailInfoParameter detailParameter);

    /**
     * 优化后-查询需要的vchcode
     */
    List<BigInteger> queryRefundDetailVchcodes(@Param("profileId") BigInteger profileId, @Param("detailParameter") QueryRefundDetailInfoParameter detailParameter, @Param("sorts") List<Sort> sorts);

    /**
     * 优化后-查询真正需要的数据
     */
//    @WingsCutSelect(entityLinked = "vchcode",selectProp = "r.vchcode")
    List<EshopRefundApplyDetailInfo> queryRefundDetailInfoListNew(@Param("profileId") BigInteger profileId, @Param("refundOrderIds") List<BigInteger> refundOrderIds, @Param("sorts") List<Sort> sorts, @Param("size") int size, @Param("type") PtypeSearchType type, @Param("ptypeWord") String ptypeWord, @Param("skuIds") List<BigInteger> skuIds, @Param("ptypeLabelIds") List<BigInteger> ptypeLabelIds, @Param("ptypeClassIds") List<BigInteger> ptypeClassIds);


    List<EshopRefundApplyDetailGoodsInfo> queryRefundReceiveDetailInfoList(QueryRefundDetailInfoParameter detailParameter);

    int queryRefundDetailsByRefundNumber(@Param("profileId") BigInteger profileId, @Param("refundOrderId") BigInteger refundOrderId);

    List<EshopRefundApplyDetailCombo> queryRefundComboDetailList(QueryRefundDetailParameter detailParameter);

    List<EshopRefundApplyDetailCombo> queryRefundComboDetailListByVchcodes(@Param("profileId") BigInteger profileId, @Param("refundOrderIds") List<BigInteger> refundOrderIds);

    List<EshopRefundSendDetail> queryRefundSendDetails(QueryRefundDetailParameter detailParameter);

    List<EshopRefundSendDetailCombo> queryRefundSendDetailCombos(QueryRefundDetailParameter detailParameter);

    List<EshopRefundDetailSerialNo> queryRefundDetailSerialInfos(QueryRefundDetailParameter detailParameter);

    /**
     * @param profileId
     * @param refundOrderIds
     * @param tableName
     * @return java.util.List<com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundDetailSerialNo>
     * @Description 使用时表里必须有refund_order_id字段
     **/
    List<EshopRefundDetailSerialNo> queryRefundDetailSerialInfosByVchcodes(@Param("profileId") BigInteger profileId, @Param("refundOrderIds") List<BigInteger> refundOrderIds, @Param("tableName") String tableName);

    /**
     * 根据订单获取售后信息
     *
     * @param refundParameter
     * @return
     */
    EshopRefundEntity getRefundInfoFromOrder(QueryRefundParameter refundParameter);


    List<EshopSaleOrderDetail> getSaleorderDetail(QueryOrderDetailParameter parameter);

    List<EshopRefundApplyDetail> queryDeliverRefundDetails(QueryRefundDetailParameter detailParameter);

    List<EshopRefundApplyDetail> quereyDeliverRefundComboDetails(QueryRefundDetailParameter detailParameter);

    List<EshopRefundFreight> queryDeliverRefundFreightLists(@Param("profileId") BigInteger profileId, @Param("vchcodes") List<BigInteger> vchcodes);


    List<EshopRefundApplyDetail> queryEshopRefundDetails(QueryRefundDetailParameter detailParameter);

    List<EshopRefundApplyDetailCombo> queryEshopRefundComboDetails(QueryRefundDetailParameter detailParameter);

    EshopRefundConfig quereyRefundConfig(@Param("profileId") BigInteger profileId);

    List<EshopRefundConfigReason> quereyRefundConfigReason(@Param("profileId") BigInteger profileId, @Param("reasonType") RefundReasonType reasonType, @Param("reasonId") String reasonId);

    EshopRefundConfigReason quereyRefundConfigDefaultReason(@Param("profileId") BigInteger profileId);

    boolean modifyRefundConfigReason(EshopRefundConfigReason reason);

    List<EshopRefundPayDetail> quereyRefundPayDetails(@Param("profileId") BigInteger profileId, @Param("refundOrderId") BigInteger refundOrderId, @Param("detailId") BigInteger detailId);

    List<EshopRefundFreight> queryRefundFreights(@Param("profileId") BigInteger profileId, @Param("refundOrderId") BigInteger refundOrderId);

    List<EshopRefundFreight> queryRefundFreightLists(@Param("profileId") BigInteger profileId, @Param("refundOrderIds") List<BigInteger> refundOrderIds);

    List<EshopRefundDetailSerialNo> queryDeliverDetailSerialNos(QueryRefundDetailParameter detailParameter);

    boolean insertRefundInfo(EshopRefundEntity refund);

    int insertRefundDetails(@Param("details") List<EshopRefundApplyDetail> details);

    int insertRefundComboDetails(@Param("combos") List<EshopRefundApplyDetailCombo> combos);

    boolean insertRefundSendDetails(@Param("details") List<EshopRefundSendDetail> details);

    boolean updateRefundSendDetails(@Param("ptypeId") BigInteger ptypeId, @Param("skuId") BigInteger skuId, @Param("platformNumId") BigInteger platformNumId, @Param("platformPropertiesName") BigInteger platformPropertiesName);

    boolean insertRefundSendDetailCombos(@Param("combos") List<EshopRefundSendDetailCombo> combos);

    boolean insertRefundDetailSerials(@Param("serials") List<EshopRefundDetailSerialNo> serials, @Param("tableName") String tableName);

    boolean updateRefund(EshopRefundEntity refund);

    boolean updateRefundWithPostInfo(EshopRefundEntity refund);

    boolean updateRefundConfirmState(@Param("ids") List<BigInteger> ids, @Param("profileId") BigInteger profileId);

    int deleteRefunds(@Param("ids") List<BigInteger> ids, @Param("profileId") BigInteger profileId);

    boolean updateReceiveStateForDeleteRefunds(@Param("ids") List<BigInteger> ids, @Param("profileId") BigInteger profileId);

    boolean refundConfirmCheckIn(EshopRefundEntity refund);

    boolean updateRefundDetails(@Param("details") List<EshopRefundApplyDetail> details);

    boolean updateRefundSendDetail(@Param("details") List<EshopRefundSendDetail> details);

    boolean updateRefundComboDetails(@Param("combos") List<EshopRefundApplyDetailCombo> combos);

    int cancelRefundReceived(@Param("profileId") BigInteger profileId, @Param("refundIds") List<BigInteger> refundIds);

    int clearCheckinInfo(@Param("profileId") BigInteger profileId, @Param("vchcodes") List<BigInteger> vchcodes);

    void deleteCheckinDetails(@Param("profileId") BigInteger profileId, @Param("vchcodes") List<BigInteger> vchcodes);

    boolean deleteRefundInfo(EshopRefundEntity refundInfo);

    boolean deleteRefundDetails(@Param("profileId") BigInteger profileId, @Param("refundOrderId") BigInteger refundOrderId);

    boolean deleteRefundComboDetails(@Param("profileId") BigInteger profileId, @Param("refundOrderId") BigInteger refundOrderId);

    boolean deleteRefundSendDetails(@Param("profileId") BigInteger profileId, @Param("refundOrderId") BigInteger refundOrderId);

    boolean deleteRefundSendCombos(@Param("profileId") BigInteger profileId, @Param("refundOrderId") BigInteger refundOrderId);

    boolean deleteRefundSerials(@Param("profileId") BigInteger profileId, @Param("refundOrderId") BigInteger refundOrderId);

    List<GoodsSerialEntity> queryRefundDetailSerialno(@Param("profileId") BigInteger profileId, @Param("vchcode") BigInteger vchcode, @Param("detailId") BigInteger detailId);

    List<GoodsSerialEntity> queryRefundDetailSerialnos(@Param("profileId") BigInteger profileId, @Param("refundOrderIds") List<BigInteger> refundOrderIds);

    List<GoodsSerialEntity> queryRefundcheckinDetailSerialno(@Param("profileId") BigInteger profileId, @Param("vchcode") BigInteger vchcode, @Param("detailId") BigInteger detailId);


    boolean insertRefundFreights(@Param("freights") List<EshopRefundFreight> freights);

    boolean deleteRefundFreights(@Param("profileId") BigInteger profileId, @Param("refundOrderId") BigInteger refundOrderId);


    boolean insertRefundConfig(EshopRefundConfig config);

    boolean insertRefundConfigReason(@Param("reason") EshopRefundConfigReason reason);

    boolean updateRefundConfigReasons(@Param("reason") EshopRefundConfigReason reason);

    boolean insertRefundConfigReasonBySingle(EshopRefundConfigReason reason);

    BigInteger checkReasonExist(@Param("profileId") BigInteger profileId, @Param("reason") String reason);

    boolean deleteRefundConfig(@Param("profileId") BigInteger profileId);

    boolean replaceIntoRefundConfig(EshopRefundConfig config);

    boolean deleteRefundReason(@Param("profileId") BigInteger profileId);

    boolean deleteSingleRefundReason(@Param("profileId") BigInteger profileId, @Param("id") BigInteger id);

    boolean updateRefundConfig(EshopRefundConfig config);

    boolean updateRefundConfigReason(EshopRefundConfigReason reason);

    boolean insertRefundPayDetails(List<EshopRefundPayDetail> payDetails);

    boolean insertRefundPayDetail(EshopRefundPayDetail payDetail);

    boolean updateRefundPayDetail(EshopRefundPayDetail payDetail);

    GoodsBatchEntity getGoodsBatch(@Param("profileId") BigInteger profileId, @Param("ktypeId") BigInteger ktypeId, @Param("batchno") String batchno);

    List<BigInteger> getPostedDeliverBillVchcodes(@Param("profileId") BigInteger profileId, @Param("vchcodes") List<BigInteger> vchcodes);

    List<BigInteger> getPostedDeliverBillVchcodesByTradeId(@Param("profileId") BigInteger profileId, @Param("otypeId") BigInteger otypeId, @Param("tradeOrderId") String tradeOrderId);

    List<String> getPostedTradeId(@Param("profileId") BigInteger profileId, @Param("otypeId") BigInteger otypeId, @Param("tradeIdList") List<String> tradeOrderIds);

    List<EshopRefundEntity> getNeedReleaseQtyRefundList(QueryRefundParameter refundParameter);

    List<EshopRefundEntity> getRefundByBillVchcode(@Param("profileId") BigInteger profileId, @Param("billVchcode") BigInteger billVchcode);

    Boolean checkRefundReasonUsed(@Param("profileId") BigInteger profileId, @Param("reason") String reason);

    List<EshopRefundApplyDetail> queryUnRelationDetails(EshopRefundApplyDetailDo refundApplyDetailDo);

    List<EshopRefundApplyDetail> queryRefundDetails(QueryRefundParameter parameter);

    boolean updateRefundDetailFreightFee(EshopRefundApplyDetail detail);

    boolean updateRefundComboFreightFee(EshopRefundApplyDetail detail);

    boolean checkOrderExistRefund(QueryRefundParameter param);

    List<EshopSaleOrderDetail> queryEshopSaleOrderDetails(@Param("profileId") BigInteger profileId, @Param("eshopOrderId") BigInteger eshopOrderId);

    EshopSaleOrderEntity queryTempEshopSaleOrder(@Param("profileId") BigInteger profileId, @Param("eshopId") BigInteger eshopId, @Param("tradeOrderId") String tradeId);

    EshopRefundEntity getRefundByRefundIdAndProfileIdAndEshopId(QueryRefundParameter refundParameter);

    EshopRefundEntity getRefundByRefundIdAndProfileId(QueryRefundParameter refundParameter);

    List<EshopRefundEntity> getRefundByRefundIdListAndProfileId(QueryRefundParameter refundParameter);

    List<EshopSaleOrderVchcodeEntity> queryrefundSaleOrderVchocode(@Param("refundVchcodeList") List<BigInteger> refundVchcodeList);

    List<OrderProcessStatesEntity> queryOrderPorcessStatesByorderIdList(@Param("profileId") BigInteger profileId, @Param("tradeIdList") List<String> tradeIdList);

    int updateRefundDeleted(@Param("profileId") BigInteger profileId, @Param("ids") List<String> ids);

    /**
     * 更新售后明细的收货明细id
     *
     * @param refundDetailAndCheckInVO
     * @return
     */
    int updateRefundDetailCheckDetailId(RefundDetailAndCheckInVO refundDetailAndCheckInVO);

    List<BigInteger> queryCheckInVchcodeByCheckInDetailIdList(@Param("checkInDetaiIdlList") List<BigInteger> checkInDetaiIdlList);

    /**
     * 获取默认仓库
     *
     * @param profileId
     * @return
     */
    Ktype getDefaultKtype(@Param("profileId") BigInteger profileId, @Param("employeeId") BigInteger employeeId);

    /**
     * 获取默认的Etype
     *
     * @param profileId
     * @return
     */
    Etype getDefaultEtype(@Param("profileId") BigInteger profileId, @Param("employeeId") BigInteger employeeId);

    /**
     * 更新售后明细的收获记录编号信息
     *
     * @param profileId
     * @param refundIds
     * @return
     */
    int updateRefundApplyDetailCheckDetailId(@Param("profileId") BigInteger profileId, @Param("refundIds") List<BigInteger> refundIds);

    /**
     * @param profileId
     * @param comboRowIdList
     * @return
     */
    List<BigInteger> queryApplyDetailIdByComboRowIdList(@Param("profileId") BigInteger profileId, @Param("comboRowIdList") List<BigInteger> comboRowIdList);

    /**
     * @param profileId
     * @param comboRowIdList
     * @return
     */
    List<BigInteger> queryCheckInDetailIdByComboRowIdList(@Param("profileId") BigInteger profileId, @Param("comboRowIdList") List<BigInteger> comboRowIdList);

    /**
     * 根据收货明细ID获取 序列号数据
     *
     * @param profileId
     * @param checkInDetailId
     * @return
     */
    List<GoodsSerialEntity> queryRefundcheckinDetailSerialnoById(@Param("profileId") BigInteger profileId, @Param("checkInDetailId") BigInteger checkInDetailId);

    boolean getSaleOrderMappingState(EshopRefundEntity refundEntity);

    List<BigInteger> queryCheckInDetaiIdlListByComboIdList(@Param("comboRowIdList") List<BigInteger> comboRowIdList);

    List<String> getReceiveCheckInDetailList(@Param("refundOrderId") String refundOrderId, @Param("profileId") BigInteger profileId);

    /**
     * 售后管理局部刷新
     *
     * @param ids
     * @param profileId
     * @return
     */
    List<EshopRefundEntity> partialRefreshRefundList(@Param("ids") List<BigInteger> ids, @Param("profileId") BigInteger profileId);

    int editRefundType(@Param("profileId") BigInteger profileId, @Param("id") BigInteger id);

    int editRefundTypeList(@Param("profileId") BigInteger profileId, @Param("ids") List<BigInteger> ids);

    List<EshopMergeRefundApplyDetailInfo> queryMergeApplyDetailInfoByRefundIds(@Param("profileId") BigInteger profileId, @Param("refundIds") List<BigInteger> refundIds);

    int cancelRefundApplyDetailByCheckDetail(@Param("profileId") BigInteger profileId, @Param("idList") List<BigInteger> idList);

    int cancelRefundApplyDetailComboByCheckDetail(@Param("profileId") BigInteger profileId, @Param("comboRowIdList") List<BigInteger> comboRowIdList);

    int updateRefundApplyCheckComboRowId(@Param("profileId") BigInteger profileId, @Param("applyComboId") BigInteger applyComboId, @Param("checkInComboId") BigInteger checkInComboId, @Param("actualReceiptNumber") BigDecimal actualReceiptNumber);

    int updateRefundComboDetailIdByCombowRowId(@Param("profileId") BigInteger profileId, @Param("normalComboRowIdList") List<BigInteger> normalComboRowIdList);

    int updateRefundCheckDetailIdById(@Param("profileId") BigInteger profileId, @Param("normalIdList") List<BigInteger> normalIdList);

    /**
     * 更新售后单状态
     *
     * @param entity
     * @return
     */
    int updateRefundState(EshopRefundEntity entity);

    /**
     * 更新售后单完成时间
     *
     * @param entity
     * @return
     */
    int updateRefundFinishTime(EshopRefundEntity entity);

    EshopRefundEntity queryRefundInfoByBillVchcode(WriteBackRefundStateParameter parameter);

    List<EshopRefundEntity> queryRefundListByVchcodeList(@Param("profileId") BigInteger profileId, @Param("ids") List<BigInteger> ids);


    int updateRefundMappingState(EshopRefundEntity it);

    int updateRefundDetailState(EshopRefundEntity entity);


    List<EshopRefundConfigReason> getRefundConfigReasonListForEditRefund(@Param("profileId") BigInteger profileId, @Param("reasonId") String reasonId);

    /**
     * 更新reason 数据
     * update pl_eshop_refund t1,pl_eshop_refund_config_reason t2
     * set t1.refund_reason=t2.refund_reason  where  t1.refund_reason = t2.id and  t1.profile_id = xxx
     *
     * @param profileId
     * @return
     */
    List<EshopRefundConfigReason> getRefundConfigReasonListByRefund(@Param("profileId") BigInteger profileId, @Param("id") String id);

    BigInteger queryDefaultKtypeId(@Param("profileId") BigInteger profileId);

    boolean checkRefundReasonIsDefault(@Param("profileId") BigInteger profileId, @Param("id") String id);

    EshopRefundEntity queryRefundByVchcode(@Param("profileId") BigInteger profileId, @Param("id") BigInteger id);

    /**
     * @param profileId
     * @param id        - 售后单vchcode
     * @return java.math.BigInteger
     * @Description 获取售后主单上的ktypeid
     **/
    BigInteger queryKTypeIdByRefundNumber(@Param("profileId") BigInteger profileId, @Param("id") BigInteger id);

    /**
     * 判断单据是否核算
     */
    boolean checkBillIfPosted(@Param("profileId") BigInteger profileId, @Param("vchcode") BigInteger vchcode, @Param("postState") int postState);

    /**
     * 根据明细id获取已经选择的成本信息
     */
    List<RefundAndDeliverMappingEntity> querySelectedDetailCost(@Param("profileId") BigInteger profileId, @Param("detailId") BigInteger detailId);

    /**
     * 通过明细id更新售后明细信息
     */
    int updateRefundDetailByDetailId(@Param("profileId") BigInteger profileId, @Param("detail") UpdateRefundDetailCostEntity detail);

    /**
     * 新增或者修改售后明细对应出库成本库中的记录信息
     */
    int deleteAndInsertRefundDetailCost(@Param("profileId") BigInteger profileId, @Param("detail") UpdateRefundDetailCostEntity updateRefundDetail);

    /**
     * 批量插入售后明细对应的成本信息到表中
     **/
    int insertRefundDetailToCostTable(@Param("profileId") BigInteger profileId, @Param("details") List<EshopRefundApplyDetail> details);

    /**
     * 批量删除售后明细对应的成本信息表中的记录
     **/
    int deleteRefundCostTableRecordByRefundVchCode(@Param("profileId") BigInteger profileId, @Param("RefundVchCodes") List<BigInteger> RefundVchCodes);

    /**
     * 更新售后主单的成本状态
     **/
    int updateRefundCostState(EshopRefundEntity refund);

    /**
     * 更新售后明细的成本状态
     **/
    int updateRefundDetailCostState(@Param("profileId") BigInteger profileId, @Param("details") List<EshopRefundApplyDetail> details);

    /**
     * 更新售后明细的成本信息
     **/
    int updateRefundDetailCostInfo(@Param("profileId") BigInteger profileId, @Param("details") List<EshopRefundApplyDetail> details);

    /**
     * 清除vchcodes对应的售后单以及售后明细的成本状态
     **/
    int updateRefundAndDetailsCostInfoByVchcodes(@Param("profileId") BigInteger profileId, @Param("ids") List<BigInteger> ids);

    /**
     * 通过明细id更新收货记录明细信息
     **/
    int updateCheckinDetailByDetailId(@Param("profileId") BigInteger profileId, @Param("detail") UpdateRefundDetailCostEntity updateRefundDetail);

    /**
     * 通过售后单vchCode改售后单的退货仓库信息
     **/
    int updateRefundKTypeByVchcode(@Param("profileId") BigInteger profileId, @Param("ids") List<BigInteger> ids, @Param("ktypeId") BigInteger ktypeId);

    /**
     * 查询该账套是否拥有套餐商品
     **/
    List<BigInteger> queryComboPtypeId(@Param("profileId") BigInteger profileId);

    int closeOnlineRefund(@Param("profileId") BigInteger profileId, @Param("eshopId") BigInteger eshopId, @Param("tradeId") String tradeId, @Param("refundIds") List<String> refundIds);

    // RefundReceiveStatus
    int updateReceiveState(@Param("profileId") BigInteger profileId, @Param("id") BigInteger id, @Param("refundReceiveStatus") int refundReceiveStatus, @Param("receivetime") Date receivetime, @Param("receiveBuyerId") BigInteger receiveBuyerId);

    List<DownloadRefundInfoParam> getRefundIdsByTradeId(@Param("tradeId") String tradeId, @Param("profileId") BigInteger profileId);

    BillTasklPlanInfo getBillTasklPlanInfoByRefund(@Param("tradeOrderId") String tradeOrderId, @Param("profileId") BigInteger profileId);

    /**
     * 查询售后单物流信息列表
     *
     * @param eshopRefundEntity
     * @return
     */
    List<EshopRefundFreight> listRefundFreights(EshopRefundEntity eshopRefundEntity);

    /**
     * 更新售后单提交wms状态
     **/
    boolean updateRefundSubmitToWmsStatus(@Param("profileId") BigInteger profileId, @Param("id") BigInteger id, @Param("hasSubmitToWms") boolean hasSubmitToWms);

    int getUndeletedRefundCount(@Param("profileId") BigInteger profileId, @Param("tradeOrderId") String tradeOrderId);

    BigInteger getRefundIdByVchcode(WriteBackRefundStateParameter parameter);

    BigInteger getRefundIdByTargetVchcode(@Param("profileId") BigInteger profileId, @Param("targetVchcode") BigInteger targetVchcode);

    List<BillRelation> getRefundRelationByTargetVchcode(@Param("profileId") BigInteger profileId, @Param("targetVchcodes") List<BigInteger> targetVchcodes);

    void updateReceiveStateOnly(@Param("profileId") BigInteger profileId, @Param("id") BigInteger id, @Param("refundReceiveStatus") int refundReceiveStatus, @Param("receiveEtypeId") BigInteger employeeId, @Param("receiveTime") Date receiveTime);

    void updateReceiveStateOnlyBatch(@Param("profileId") BigInteger profileId, @Param("ids") List<BigInteger> ids, @Param("refundReceiveStatus") int refundReceiveStatus, @Param("receiveEtypeId") BigInteger employeeId, @Param("receiveTime") Date receiveTime);

    void deleteRefundDetailsByDetail(@Param("profileId") BigInteger profileId, @Param("id") BigInteger id);

    void deleteRefundDetailsByIds(@Param("profileId") BigInteger profileId, @Param("ids") List<BigInteger> ids);

    List<EshopRefundSendDetail> querySendUnRelationDetails(EshopRefundApplyDetailDo refundApplyDetailDo);

    List<EshopRefundApplyDetail> queryRefundDetailsByRefund(@Param("refundEntities") List<EshopRefundEntity> eshopRefundEntities, @Param("profileId") BigInteger profileId);

    List<EshopRefundSendDetail> queryRefundSendDetailsByRefund(@Param("refundEntities") List<EshopRefundEntity> eshopRefundEntities, @Param("profileId") BigInteger profileId);

    String getPlatformParentOrderId(QueryRefundDetailParameter param);

    void updateDetailRefundState(EshopRefundEntity refund);

    void updateCommitDistributor(@Param("refundList") List<EshopRefundEntity> refundList, @Param("profileId") BigInteger profileId, @Param("hasCommitToDistributor") int hasCommitToDistributor);

    List<BigInteger> getRefundIdsByMark(@Param("refundParameter") QueryRefundParameter refundParameter, @Param("profileId") BigInteger profileId);

    void notifyRefundSendState(@Param("requests") List<NotifyRefundSendStateRequest> requests);

    List<EshopRefundApplyDetail> queryRefundedDetail(@Param("refundApplyDetails") List<EshopRefundApplyDetail> refundApplyDetails, @Param("profileId") BigInteger profileId, @Param("tradeOrderId") String tradeOrderId);

    String getEtypeNameById(@Param("etypeId") BigInteger etypeId, @Param("profileId") BigInteger profileId);

    ArrayList<String> getIdsByPtypeInfo(@Param("param") QueryRefundDetailInfoParameter param);

    ArrayList<String> getNumIdsByBabyInfo(@Param("ptypeWord") String ptypeWord, @Param("profileId") BigInteger profileId);

    ArrayList<String> getSkuIdsByBabyInfo(@Param("ptypeWord") String ptypeWord, @Param("profileId") BigInteger profileId);

    ArrayList<String> getIdsByProductInfo(@Param("param") QueryRefundDetailInfoParameter param);


    List<String> getRefundNumberByTradeIds(@Param("profileId") BigInteger profileId, @Param("eshopId") BigInteger eshopId, @Param("tradeIds") List<String> tradeIds);

    void changePlatformState(EshopRefundEntity refund);

    void updateRefundApplyDetailMemo(@Param("profileId") BigInteger profileId, @Param("refundIds") List<BigInteger> refundIds);

    void updateRefundDetailQty(@Param("refundApplyDetails") List<EshopRefundApplyDetail> refundApplyDetails);

    String getPlatformStoreId(@Param("eshopOrderId") BigInteger eshopOrderId, @Param("profileId") BigInteger profileId);


    void updateRefundComboQty(@Param("eshopRefundApplyDetailCombos") List<EshopRefundApplyDetailCombo> eshopRefundApplyDetailCombos);

    BigDecimal getRefundCombowQty(@Param("comboRowId") BigInteger comboRowId, @Param("profileId") BigInteger profileId);

    BigDecimal getOrderCombowQty(@Param("comboRowId") BigInteger comboRowId, @Param("profileId") BigInteger profileId);

    List<EshopRefundEntity> listRefundSimple(@Param("param") QueryRefundParameter queryParams);

    EshopInfo getRefundTiming(@Param("otypeId") BigInteger otypeId, @Param("profileId") BigInteger profileId);

    void inUpRefundTiming(RefundTiming refundTiming);

    List<DataStatisticsEntity> getRefundAllCount(@Param("request") DataStatisticsRequest request);

    List<DataStatisticsDAO> getOverTimeRefundCount(@Param("request") DataStatisticsRequest request);

    List<DataStatisticsDAO> getAboutToOverTimeRefundCount(@Param("request") DataStatisticsRequest request);

    List<DataStatisticsDAO> getNotAuditRefundBillCount(@Param("request") DataStatisticsRequest request);

    List<DataStatisticsDAO> getAuditRefundBillCount(@Param("request") DataStatisticsRequest request);

    String getOnlineAddressId(@Param("profileId") BigInteger profileId, @Param("addressId") BigInteger addressId);

    int insertRelation(BillRelation relation);

    int insertRelationBatch(@Param("relations") List<BillRelation> relations);

    void updateRefundPorcessState(EshopRefundEntity refund);

    int getRelationCount(EshopRefundEntity refund);

    List<String> getHistoryRefundNumbers(GetHistoryRefundNumbersRequest request);

    List<RefundTiming> getTiming(EshopRefundEntity refund);

    List<BigInteger> getRefundIdsBySourceDetailId(@Param("list") List<BigInteger> list, @Param("profileId") BigInteger profileId);

    List<BigInteger> getEtypeIdsByEtypeName(@Param("fullname") String confirmEtypeName, @Param("profileId") BigInteger profileId);

    List<RefundDuty> getRefundDuty(BigInteger profileId);

    Integer addRefundDuty(RefundDuty refundDuty);

    Integer deleteRefundDuty(RefundDuty refundDuty);

    String getRefundDuties(@Param("profileId") BigInteger profileId, @Param("refundDutyIds") List<BigInteger> redfundDutyIds);

    List<BigInteger> getRefundDutyIdsByName(@Param("refundDuty") String refundDuty, @Param("profileId") BigInteger profileId);

    Integer updateRefundDuty(UpdateRefundDutyRequest request);

    List<String> getRefundDutyByIds(UpdateRefundDutyRequest request);

    String getRefundDutyNameById(@Param("profileId") BigInteger profileId, @Param("id") String id);

    /**
     * 查询售后责任方
     *
     * @param profileId
     * @param redfundDutyIds
     * @return
     */
    List<RefundDuty> getRefundDutyEntityByIds(@Param("profileId") BigInteger profileId, @Param("refundDutyIds") List<BigInteger> redfundDutyIds);

    int dutyBeUsed(RefundDuty refundDuty);

    List<BigInteger> getSaleBackBillVchcode(@Param("profileId") BigInteger profileId, @Param("id") BigInteger id);

    void deleteRefundDetailCombosByIds(@Param("profileId") BigInteger profileId, @Param("ids") List<BigInteger> ids);

    void updateRefundCheckinDetailActualNumber(@Param("refundApplyDetails") List<EshopRefundApplyDetail> refundApplyDetails);

    List<BigInteger> getSourceDetailIds(EshopRefundEntity item);

    List<BigInteger> getSourceDetailIdsBatch(@Param("ids") List<BigInteger> ids, @Param("profileId") BigInteger profileId);

    List<RefundWithDetailId> listRefundWithDetailIdBatch(@Param("ids") List<BigInteger> ids, @Param("profileId") BigInteger profileId);

    void updateRefundCheckinComboActualNumber(@Param("refundApplyDetails") List<EshopRefundApplyDetail> refundApplyDetails);

    void insertRefundInfoExtend(EshopRefundEntity refund);

    boolean hasOtherBill(@Param("vchcode") BigInteger vchcode, @Param("id") BigInteger id, @Param("profileId") BigInteger profileId);

    List<EshopRefundConfigReason> getRefundReasonList(EshopRefundConfigReason entity);

    List<BigInteger> getRefundReasonIdList(@Param("id") BigInteger id, @Param("profileId") BigInteger profileId);

    List<BigInteger> getRefundReasonIdListForSys(@Param("id") BigInteger id, @Param("profileId") BigInteger profileId);

    void saveRelateReason(EshopRefundConfigReason entity);

    void clearRelateReason(EshopRefundConfigReason entity);

    Boolean checkRefundReasonRelated(EshopRefundConfigReason reason);

    List<RefundReportDomain> getReportDataForRefundType(GetReportDataRequest request);

    List<RefundReportDomain> getReportDataForRefundReason(GetReportDataRequest request);

    String getAlreadyNumber(GetReportDataRequest request);

    String getAlmostNumber(GetReportDataRequest request);

    String getNoneNumber(GetReportDataRequest request);

    List<String> getReportDataForRefundDuty(GetReportDataRequest request);

    List<RefundReportDomain> getReturnListByQty(GetReportDataRequest request);

    List<RefundReportDomain> getReturnListByTotal(GetReportDataRequest request);

    BigInteger getBtypeIdByOtypeId(@Param("otypeId") BigInteger otypeId, @Param("profileId") BigInteger profileId);

    void updateRefundExtend(EshopRefundEntity refund);

    BigInteger getPayBillVchcode(@Param("profileId") BigInteger profileId, @Param("id") BigInteger id);


    List<BigInteger> getCheckinIdsByRefund(EshopRefundEntity refund);

    BigInteger getOtypdIdByRefundId(@Param("profileId") BigInteger profileId, @Param("id") BigInteger id);

    List<EshopRefundConfigReason> getRefundConfigReasonListNew(@Param("profileId") BigInteger profileId, @Param("reasonId") String reasonId);

    void updateRefundForPay(EshopRefundEntity refund);

    String getPlatformStoreIdByKtypeId(@Param("ktypeId") BigInteger ktypeId, @Param("profileId") BigInteger profileId, @Param("otypeId") BigInteger otypeId);

    EshopRefundApplyDetail getRefundDetail(RefundDetailAndCheckInVO vo);

    void updateCheckinDetail(@Param("detail") EshopRefundApplyDetail detail, @Param("vo") RefundDetailAndCheckInVO vo);

    EshopRefundApplyDetailCombo getRefundDetailCombo(RefundComboDetailAndCheckInVO it);

    void updateRefundCombo(@Param("combo") EshopRefundApplyDetailCombo combo, @Param("it") RefundComboDetailAndCheckInVO it);

    List<EshopRefundReceiveCheckInEntity> queryCheckinListByRefundId(EshopRefundEntity refundEntity);

    List<EshopRefundReceiveCheckInEntity> queryCheckinListByRefundList(@Param("profileId") BigInteger profileId, @Param("refundIds") List<BigInteger> refundIds);


    /**
     * 根据订单号查询发货单实体
     *
     * @param profileId 账套id
     * @param otypeId   网店id
     * @param tradeId   订单号
     * @return 发货单列表
     */
    List<BillDeliverDTO> queryDeliverByTradeId(@Param("profileId") BigInteger profileId, @Param("otypeId") BigInteger otypeId, @Param("tradeId") String tradeId);

    /**
     * 查询发货单是否打印物流单号
     *
     * @param profileId 账套id
     * @param vchcode   发货单id
     * @return 是否打印
     */
    List<Boolean> queryFrightPrintState(@Param("profileId") BigInteger profileId, @Param("vchcode") BigInteger vchcode);


    /**
     * 查询wms是否推单
     *
     * @param profileId 账套id
     * @param vchcode   发货单id
     * @return 推单状态
     */
    boolean queryWmsSendState(@Param("profileId") BigInteger profileId, @Param("vchcode") BigInteger vchcode);

    EshopBuyer getReceiveBuyer(EshopRefundEntity refund);

    void updateRefundActualQtyByAuto(EshopRefundEntity refund);

    void insertEshopRefundCheckinRelation(EshopRefundCheckinRelation relation);

    void insertEshopRefundCheckinRelationBatch(List<EshopRefundCheckinRelation> relation);


    ShopType getOtypeTypeByTradeId(@Param("profileId") BigInteger profileId, @Param("tradeId") String tradeId);

    List<BigInteger> getCheckinVchcodesByRefundId(EshopRefundEntity refundEntity);

    void clearRefundActQty(@Param("refundOrderId") BigInteger refundOrderId, @Param("profileId") BigInteger profileId);

    void deleteEshopRefundCheckinRelation(EshopRefundReceiveCheckInEntity entity);

    List<EshopInfo> showDoudianApply(@Param("profileId") BigInteger profileId);

    List<BigInteger> getRefundOrderIdByCheckinVchcode(EshopRefundReceiveCheckInEntity entity);

    void deleteEshopRefundCheckinRelationBatch(@Param("profileId") BigInteger profileId, @Param("refundId") BigInteger refundId, @Param("vchcodes") List<BigInteger> vchcodes);

    void deleteCheckinRelationBatch(@Param("profileId") BigInteger profileId, @Param("vchcodes") List<BigInteger> vchcodes);

    List<BigInteger> getCheckinVchcodebyFreight(EshopRefundQueryCheckInListByLogistics eshopRefundQueryCheckInListByLogistics);

    /*
     * 分销价格体系开始
     */
    void batchInsertPlEshopRefundDistributionBuyer(@Param("list") List<PlEshopRefundDistributionBuyer> list);

    void deletePlEshopRefundApplyDetailDistributionBuyer(EshopRefundEntity refund);

    void batchInsertPlEshopRefundApplyDetailDistributionBuyer(@Param("list") List<PlEshopRefundApplyDetailDistributionBuyer> list);

    void batchInsertPlEshopRefundApplyDetailComboDistributionBuyer(@Param("list") List<PlEshopRefundApplyDetailComboDistributionBuyer> list);

    void batchInsertPlEshopRefundSendDetailDistributionBuyer(@Param("list") List<PlEshopRefundSendDetailDistributionBuyer> list);

    void batchInsertPlEshopRefundSendDetailComboDistributionBuyer(@Param("list") List<PlEshopRefundSendDetailComboDistributionBuyer> list);

    void deletePlEshopRefundApplyDetailComboDistributionBuyer(EshopRefundEntity refund);

    void deletePlEshopRefundSendDetailDistributionBuyer(EshopRefundEntity refund);

    void deletePlEshopRefundSendDetailComboDistributionBuyer(EshopRefundEntity refund);

    List<BigInteger> getRefundIdByPtypeDetail(QueryRefundParameter refundParameter);

    List<BigInteger> getRefundIdByPtypeCombo(QueryRefundParameter refundParameter);

    List<BigInteger> getCheckinKtypeIdByRefundId(EshopRefundEntity refund);

    void updateRefundKtypeId(EshopRefundEntity refund);

    void updateCheckInComboApplyQty(EshopRefundReceiveCheckInCombo checkInCombo);

    void updateCheckInDetailApplyQty(EshopRefundReceiveCheckInDetail checkInDetail);

    List<BigInteger> getBtypeIdByBtypeName(@Param("freightName") String freightName, @Param("profileId") BigInteger profileId);

    void updateRefundForMoneyOnly(EshopRefundEntity refund);

    void changeRefundKtypeId(EshopRefundEntity refund);

    void updateRefundReturnSyncState(@Param("id") BigInteger id, @Param("profileId") BigInteger profileId, @Param("state") int state);

    void saveOrUpdateRefundSnapshot(EshopRefundEntitySnapshot eshopRefundEntitySnapshot);

    EshopRefundEntitySnapshot getRefundSnapshot(@Param("id") BigInteger id, @Param("profileId") BigInteger profileId);

    void updateSnapshotRefundType(EshopRefundEntitySnapshot snapshot);

    void insertChangeInfo(EshopRefundEntityChangeInfo changeInfo);

    void insertChangeInfoBatch(@Param("changeInfoList") List<EshopRefundEntityChangeInfo> changeInfoList);

    void deleteSnapshot(@Param("id") BigInteger id, @Param("profileId") BigInteger profileId);

    List<BigData> getChangeInfo(@Param("id") BigInteger id, @Param("profileId") BigInteger profileId, @Param("type") RefundChangeConstantEnum refundChangeConstantEnum);

    void updateRefundRefundYAN(EshopRefundEntity entity);

    void updateRefundEdit(EshopRefundEntity entity);


    List<BigDecimal> getCheckinDetailCostPrice(EshopRefundReceiveCheckInDetail firstCheckinDetail);

    List<BigDecimal> getCheckinDetailRetailPrice(EshopRefundReceiveCheckInDetail firstCheckinDetail);

    String getSysData(@Param("profileId") BigInteger profileId, @Param("subName") String subName);

    EshopRefundConfigReason getPlatformReason(EshopRefundEntity localRefund);

    void deleteRefundReasonById(@Param("profileId") BigInteger profileId, @Param("id") BigInteger id);

    List<EshopRefundEntity> getRefundEntityList(QueryRefundParameter parameter);

    List<String> needNextOperation(@Param("vchcode") BigInteger vchcode, @Param("profileId") BigInteger profileId);

    void deleteCheckinDetailAndInoutRecord(@Param("vchcode") BigInteger checkinVchcode, @Param("profileId") BigInteger profileId);

    List<BillInoutEntity> queryInoutByVchcode(@Param("vchcode") BigInteger billVchcode, @Param("profileId") BigInteger profileId);

    List<BillInoutDetail> queryInoutDetailByVchcode(@Param("inoutId") BigInteger inoutId, @Param("profileId") BigInteger profileId);

    List<BillInoutDetailCombo> queryInoutDetailComboByVchcode(@Param("vchcode") BigInteger vchcode, @Param("profileId") BigInteger profileId);

    List<BillDetailBatch> queryInoutBatchComboByVchcode(@Param("inoutId") BigInteger inoutId, @Param("profileId") BigInteger profileId);

    List<BillSerialno> queryInoutDetailSerialyVchcode(@Param("inoutId") BigInteger inoutId, @Param("profileId") BigInteger profileId);

  /*
    分销价格体系结束
     */

    boolean batchNoRequired(@Param("profileId") BigInteger profileId);

    List<String> getEshopIds(@Param("profileId") BigInteger profileId);

    void insertRefundIndex(EshopRefundIndexEntity indexEntity);

    List<BillCombo> queryBillDetailComboByVchcode(@Param("vchcode") BigInteger billVchcode, @Param("profileId") BigInteger profileId);

    void deleteRefundIndex(@Param("billVchcode") BigInteger billVchcode, @Param("profileId") BigInteger profileId);

    List<BillDetail> queryBillDetailByVchcode(@Param("id") BigInteger id, @Param("profileId") BigInteger profileId);

    void noPay(@Param("list") List<EshopRefundEntity> refundList, @Param("profileId") BigInteger profileId);

    void noNoPay(@Param("list") List<EshopRefundEntity> refundList, @Param("profileId") BigInteger profileId);

    List<ComboDetail> getOrderDetail(@Param("ids") List<BigInteger> ids, @Param("profileId") BigInteger profileId);

    RefundTypeEnum getRefundPlatformType(@Param("id") BigInteger refundId, @Param("profileId") BigInteger profileId);

    RefundTypeEnum getRefundType(@Param("id") BigInteger refundId, @Param("profileId") BigInteger profileId);

    List<Btype> getBtypeListByProfile(@Param("profileId") BigInteger profileId);

    void updateRefundExtendRefundOrderDetailSummary(EshopRefundEntity refund);

    List<BigInteger> getNotInRefundIdByPtypeDetail(QueryRefundParameter refundParameter);

    List<BigInteger> getNotRefundIdByPtypeCombo(QueryRefundParameter refundParameter);

    List<BigInteger> getRefundIdByPtypeComboForOnly(QueryRefundParameter refundParameter);

    int insertRefundDetailsExtend(@Param("details") List<EshopRefundApplyDetail> details);

    void deleteRefundDetailsExtend(@Param("profileId") BigInteger profileId, @Param("id") BigInteger refundId);

    EshopRefundReceiveCheckInEntity getCheckInFreightInfo(@Param("refund") EshopRefundEntity refund);

    void confirmUpdateRefund(@Param("refundList") List<EshopRefundEntity> refundList, @Param("confirmEtypeId") BigInteger confirmEtypeId);

    void updateRefundDetailByDetailIdBatch(@Param("profileId") BigInteger profileId, @Param("details") ArrayList<UpdateRefundDetailCostEntity> details);

    List<EshopRefundDetailSerialNo> queryRefundSerialInfos(@Param("parameter") QueryRefundDetailParameter parameter);

    List<EshopRefundEntity> queryRefundListForSaveCheckin(@Param("parameter") QueryRefundParameter refundParameter);

    int updateRefundCostStateBatch(@Param("refundList") List<EshopRefundEntity> refundList);

    boolean hasSaleReturnBill(@Param("id") BigInteger id, @Param("profileId") BigInteger profileId);

    BigInteger getOtypeDefaultKtype(@Param("otypeId") BigInteger otypeId, @Param("profileId") BigInteger profileId);

    boolean hasPayBill(@Param("id") BigInteger id, @Param("profileId") BigInteger profileId);

    List<BigInteger> getCheckinKtypeId(QueryRefundParameter parameter);


    List<SysData> getSysDataForDelete(@Param("profileId") BigInteger profileId, @Param("subName") String subName);

    void deleteSysData(@Param("profileId") BigInteger profileId, @Param("id") BigInteger id, @Param("subName") String refundDeleteSaleBackBill);

    List<DeleteBillRequest> listDeleteBillRequest(@Param("billVchcodes") List<String> billVchcodes, @Param("profileId") BigInteger profileId);

    List<BigInteger> getDeliverVchcodes(@Param("profileId") BigInteger profileId, @Param("tradeOrderId") BigInteger tradeOrderId);

    boolean deliverHasAccount(@Param("profileId") BigInteger profileId, @Param("vchcodes") List<BigInteger> vchcodes);

    List<EshopRefundBillReleationEntity> getBillReleationList(BillRelationRequest billRelationRequest);

    int checkTdBillRelation(@Param("profileId") BigInteger profileId, @Param("sourceVchcode") BigInteger sourceVchcode, @Param("targetVchcode") BigInteger targetVchcode);

    List<EshopRefundEntity> queryAfterSaleList(AfterSaleListQueryRequest refundParameter);

    int queryAfterSaleListCount(AfterSaleListQueryRequest param);

    List<EshopRefundEntity> getRefundMarkCalculateRefundList(@Param("profileId") BigInteger profileId, @Param("now") Date now, @Param("sevenDaysBefore") Date sevenDaysBefore ,@Param("lastSeeId")BigInteger lastSeeId);
}


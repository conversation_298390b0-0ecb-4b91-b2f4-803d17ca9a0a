<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopPtypeReationMapper">
    <update id="clearMappingByPtype">
        update pl_eshop_product_sku_mapping
        set ptype_id=0,
            sku_id=0.unit_id=0
        where profile_id=#{profileId}
          and eshop_id=#{eshopId}
          and
    </update>
    <select id="getSkuMappingByPtype"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select (case when (ifnull(ps.id,0)=0 or ifnull(pu.id,0)=0 or p.deleted =1 or p.stoped =1) then false else true
        end)
        as isbind,
        psm.id,
        pm.profile_id,
        pm.eshop_id,
        p.id as ptype_id,
        pm.category_id,
        psm.platform_pic_url as picUrl,
        psm.platform_num_id,
        pm.platform_alias as prop_alias,
        pm.default_sku_id,
        psm.platform_properties_name,
        pm.platform_fullname as platname,
        pm.platform_fullname as platFullName,
        pm.platform_fullname as fullName,
        psm.platform_xcode,
        psm.platform_xcode as platXcode,
        pm.platform_xcode as pmXcode,
        concat('',p.fullname) as ptypeName,
        psm.platform_sku_id,
        psm.platform_properties,
        psm.platform_properties as properties,
        p.pcategory as mappingDetailType,
        p.propenabled,
        p.pcategory,
        bpx.xcode,
        ifnull(pu.unit_rate,1) as unitRate,
        baseunit.id as baseUnitId,
        ifnull(p.update_time,now())as modifiedtime,pm.platform_stock_state as
        stockState,pu.unit_name,ps.propvalue_name1,psm.qty,expand.mark
        from pl_eshop_product_sku psm
        left join pl_eshop_product_sku_mapping mapping on mapping.profile_id = psm.profile_id and mapping.eshop_id = psm.eshop_id and mapping.unique_id =
        psm.unique_id
        left join pl_eshop_product_sku_expand expand on expand.profile_id = psm.profile_id and expand.eshop_id = psm.eshop_id and expand.unique_id =
        psm.unique_id
        left join pl_eshop_product pm on pm.platform_num_id=psm.platform_num_id and pm.profile_id=psm.profile_id
        and pm.eshop_id =psm.eshop_id
        left join base_ptype_sku ps on ps.id=mapping.sku_id and ps.profile_id=psm.profile_id
        left join base_ptype_fullbarcode pf on pf.profile_id=ps.profile_id and pf.sku_id=mapping.sku_id and pf.unit_id =
        mapping.unit_id and pf.defaulted=1
        left join base_ptype_unit pu on pu.profile_id=psm.profile_id and pu.id =mapping.unit_id and
        pu.ptype_id=ps.ptype_id
        left join base_ptype_unit baseunit on baseunit.profile_id=psm.profile_id and mapping.ptype_id=baseunit.ptype_id and
        baseunit.unit_code=1
        left join base_ptype p on p.id=mapping.ptype_id and p.profile_id=psm.profile_id
        left join base_ptype_xcode bpx on bpx.profile_id=psm.profile_id and bpx.ptype_id=mapping.ptype_id and
        bpx.sku_id=mapping.sku_id and bpx.unit_id=mapping.unit_id
        where mapping.profile_id=#{profileId} and mapping.eshop_id=#{eshopId} and (p.pcategory !=2 OR p.pcategory IS
        NULL)
        and mapping.ptype_id=#{ptypeId} and expand.mapping_type=0
        <if test="skuId!=null">
            and mapping.sku_id=#{skuId}
        </if>
        <if test="unitId!=null">
            and mapping.unitId=#{unitId}
        </if>
    </select>
    <select id="getSkuMappingByCombo"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select (case when (ifnull(p.id, 0) = 0 or p.deleted = 1 or p.stoped = 1) then false else true end) as isbind,
               psm.id,
               pm.profile_id,
               pm.eshop_id,
               pm.category_id,
               psm.platform_pic_url                                                                        as picUrl,
               psm.platform_num_id,
               pm.default_sku_id,
               psm.platform_properties_name,
               pm.platform_fullname                                                                                 as platName,
               pm.platform_fullname                                                                                 as platFullName,
               psm.platform_sku_id,
               psm.platform_xcode                                                                          as platformxcode,
               pm.platform_xcode                                                                                    as pmXcode,
               concat('', p.fullname)                                                                      as ptypeName,
               p.barcode                                                                                   as ptypeBarcode,
               p.barcode                                                                                   as skuBarcode,
               p.id                                                                                        as ptype_id,
               false                                                                                       as isptype,
               p.usercode                                                                                  as xcode,
               p.usercode                                                                                  as usercode,
               psm.platform_properties,
               psm.platform_properties                                                                     as properties,
               p.pcategory                                                                                 as mappingDetailType,
               p.pcategory,
               p.propenabled,
               ifnull(pu.unit_rate, 1)                                                                     as unitRate,
               ifnull(p.update_time, now())                                                                as modifiedtime,
               pm.platform_stock_state                                                                     as stockState,
               pu.unit_name,
               pu.id                                                                                       as unitId,
               null                                                                                        as skuid
        from pl_eshop_product_sku_mapping mapping
                 left join pl_eshop_product_sku psm
                           on psm.profile_id = mapping.profile_id and psm.unique_id = mapping.unique_id
                 left join pl_eshop_product_sku_expand expand
                           on expand.profile_id = psm.profile_id and expand.unique_id = psm.unique_id
                 left join pl_eshop_product pm
                           on pm.platform_num_id = psm.platform_num_id and pm.profile_id = psm.profile_id
                               and pm.eshop_id = psm.eshop_id
                 left join base_ptype p on p.id = mapping.ptype_id and p.profile_id = mapping.profile_id
                 left join base_ptype_unit pu
                           on pu.profile_id = mapping.profile_id and pu.id = mapping.unit_id and pu.ptype_id = p.id
        where mapping.profile_id = #{profileId}
          and mapping.eshop_id = #{eshopId}
          and p.pcategory = 2
          and expand.mapping_type = 0
          and mapping.ptype_id = #{ptypeId}
    </select>
    <select id="getSkuMappingByPtypeOpenXcode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select (case when (ifnull(ps.id,0)=0 or ifnull(px.id,0)=0 or p.deleted =1 or p.stoped =1) then false else true
        end) as isbind,
        psm.id,
        pm.profile_id,
        pm.eshop_id,
        pm.category_id,
        psm.platform_pic_url as picUrl,
        psm.platform_num_id,
        pm.default_sku_id,
        psm.platform_properties_name,
        pm.platform_fullname as fullname,
        pm.platform_fullname as platName ,
        pm.platform_fullname as platfullname,
        psm.platform_sku_id,
        psm.unique_id,
        psm.platform_xcode as platformxcode,
        pm.platform_xcode as pmXcode,
        concat('',p.fullname) as ptypeName,
        p.barcode as ptypeBarcode,
        p.barcode as skuBarcode,
        p.id as ptype_id,
        false as isptype,
        px.xcode,
        p.usercode as usercode,
        psm.platform_properties,
        psm.platform_properties as properties,
        p.pcategory as mappingDetailType,
        p.pcategory,
        p.propenabled,
        ifnull(pu.unit_rate,1) as unitRate,
        baseunit.id as baseUnitId,
        ifnull(p.update_time,now())as modifiedtime,
        pm.platform_stock_state as stockState,pu.unit_name,pu.id as unitId,px.sku_id as skuid,
        rule.id as syncRuleId
        from pl_eshop_product_sku psm
        left join pl_eshop_product_sku_expand expand on expand.profile_id = psm.profile_id and expand.unique_id =
        psm.unique_id
        left join pl_eshop_product pm on pm.platform_num_id=psm.platform_num_id and pm.profile_id=psm.profile_id
        and pm.eshop_id =psm.eshop_id
        left join pl_eshop_stock_sync_rule rule on rule.profile_id=psm.profile_id and rule.xcode=psm.platform_xcode  and rule.deleted=0
        left join base_ptype_xcode px on px.profile_id=psm.profile_id and psm.platform_xcode=px.xcode and px.info_type=0
        left join base_ptype p on p.id=px.ptype_id and p.profile_id=psm.profile_id and p.deleted=0 and p.stoped=0
        left join base_ptype_unit pu on pu.profile_id=psm.profile_id and pu.id =px.unit_id and pu.ptype_id=px.ptype_id
        left join base_ptype_unit baseunit on baseunit.profile_id=psm.profile_id and p.id=baseunit.ptype_id and
        baseunit.unit_code=1
        left join base_ptype_sku ps on ps.id=px.sku_id and ps.profile_id=psm.profile_id and (p.pcategory !=2 OR
        p.pcategory IS NULL)
        where psm.profile_id=#{profileId} and psm.eshop_id=#{eshopId} and expand.mapping_type=1
        <if test="xcode!=null">
            and px.xcode=#{xcode}
        </if>
        <if test="oldXcode!=null">
            and psm.platform_xcode=#{oldXcode}
        </if>
    </select>
    <select id="getSkuMappingByComboOpenXcode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select (case when (ifnull(p.id,0)=0 or p.deleted =1 or p.stoped =1) then false else true end) as isbind,
        psm.id,
        pm.profile_id,
        pm.eshop_id,
        pm.category_id,
        pm.default_sku_id,
        psm.platform_pic_url as picUrl,
        psm.platform_num_id,
        psm.platform_properties_name,
        pm.platform_fullname as platName ,
        pm.platform_fullname as platfullname,
        psm.platform_sku_id,
        psm.platform_xcode as platformxcode,
        pm.platform_xcode as pmXcode,
        concat('',p.fullname) as ptypeName,
        p.barcode as ptypeBarcode,
        p.barcode as skuBarcode,
        p.id as ptype_id,
        false as isptype,
        p.usercode as xcode,
        p.usercode as usercode,
        psm.platform_properties,
        psm.platform_properties as properties,
        p.pcategory as mappingDetailType,
        p.pcategory,
        p.propenabled,
        ifnull(p.update_time,now())as modifiedtime,
        pm.platform_stock_state as stockState,
        rule.id as syncRuleId
        from pl_eshop_product_sku psm
        left join pl_eshop_product_sku_expand expand on expand.profile_id = psm.profile_id and expand.unique_id =
        psm.unique_id
        left join base_ptype_xcode bpx on bpx.profile_id = psm.profile_id and bpx.xcode =psm.platform_xcode and bpx.info_type=1
        left join pl_eshop_product pm on pm.platform_num_id=psm.platform_num_id and pm.profile_id=psm.profile_id
        and pm.eshop_id = psm.eshop_id
        left join pl_eshop_stock_sync_rule rule on rule.profile_id=psm.profile_id and rule.xcode=psm.platform_xcode  and rule.deleted=0
        left join base_ptype p on p.profile_id=psm.profile_id and psm.platform_xcode=p.usercode and p.pcategory =2 and p.deleted=0 and p.stoped=0
        where psm.profile_id=#{profileId} and psm.eshop_id=#{eshopId} and expand.mapping_type=1
        <if test="xcode!=null">
            and bpx.xcode=#{xcode}
        </if>
        <if test="oldXcode!=null">
            and psm.platform_xcode=#{oldXcode}
        </if>
    </select>

    <select id="queryAllRelation" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.OrderRelationOperation">
        SELECT *
        from (select ps.profile_id,
                     ps.eshop_id,
                     ps.platform_num_id,
                     ps.platform_sku_id,
                     ps.platform_properties_name,
                     ps.platform_xcode,
                     map.ptype_id,
                     ifnull(map.sku_id, 0)     as skuId,
                     ifnull(map.unit_id, 0)    as unitId,
                     if(p.pcategory = 2, 2, 1) as mappingMark,
                     11                        as oprate,
                     p.fullname                as ptypeName,
                     p.pcategory               as expectSkuType,
                     bpx.xcode,
                     bpu.unit_name,
                     bpu.unit_rate,
                     baseBpu.unit_name         as baseUnitName,
                     baseBpu.id                as baseUnitId
              from pl_eshop_product_sku ps
                       left join pl_eshop_product_sku_expand exp
                                 on ps.profile_id = exp.profile_id and ps.eshop_id = exp.eshop_id and
                                    exp.unique_id = ps.unique_id
                       left join pl_eshop_product_sku_mapping map
                                 on map.profile_id = ps.profile_id and map.eshop_id = ps.eshop_id and
                                    ps.unique_id = map.unique_id
                       left join base_ptype p on p.profile_id = map.profile_id and p.id = map.ptype_id
                       left join base_ptype_xcode bpx
                                 on bpx.profile_id = map.profile_id and map.sku_id = bpx.sku_id and
                                    bpx.unit_id = map.unit_id and defaulted = 1
                       left join base_ptype_unit bpu
                                 on map.profile_id = bpu.profile_id and bpu.id = map.unit_id and
                                    bpu.ptype_id = map.ptype_id
                       left join base_ptype_unit baseBpu
                                 on baseBpu.profile_id = map.profile_id and baseBpu.ptype_id = map.ptype_id and
                                    baseBpu.unit_code = 1
              where ps.profile_id = #{profileId}
                and ps.eshop_id = #{eshopId}
                and exp.mapping_type = 0
                and map.id > 0
              union all
              select ps.profile_id,
                     ps.eshop_id,
                     ps.platform_num_id,
                     ps.platform_sku_id,
                     ps.platform_properties_name,
                     ps.platform_xcode,
                     ifnull(p.id, ifnull(bpx.ptype_id, 0)) as ptypeId,
                     ifnull(bpx.sku_id, 0)                 as skuId,
                     ifnull(bpx.unit_id, 0)                as unitId,
                     if(p.id > 0, 2, 1)                    as mappingMark,
                     11                                    as oprate,
                     ifnull(p.fullname, bp.fullname)       as ptypeName,
                     ifnull(p.pcategory, bp.pcategory)     as expectSkuType,
                     ps.platform_xcode                     as xcode,
                     bpu.unit_name,
                     bpu.unit_rate,
                     baseBpu.unit_name                     as baseUnitName,
                     baseBpu.id                            as baseUnitId
              from pl_eshop_product_sku ps
                       left join pl_eshop_product_sku_expand exp
                                 on ps.profile_id = exp.profile_id and ps.eshop_id = exp.eshop_id and
                                    exp.unique_id = ps.unique_id
                       left join base_ptype p
                                 on p.profile_id = ps.profile_id and p.usercode = ps.platform_xcode
                       left join base_ptype_xcode bpx
                                 on bpx.profile_id = ps.profile_id and bpx.xcode = ps.platform_xcode
                       left join base_ptype bp on bp.profile_id = bpx.profile_id and bp.id = bpx.ptype_id
                       left join base_ptype_unit bpu
                                 on bpx.profile_id = bpu.profile_id and bpu.id = bpx.unit_id and
                                    bpu.ptype_id = bpx.ptype_id
                       left join base_ptype_unit baseBpu
                                 on baseBpu.profile_id = bpx.profile_id and baseBpu.ptype_id = bpx.ptype_id and
                                    baseBpu.unit_code = 1
              where ps.profile_id = #{profileId}
                and ps.eshop_id = #{eshopId}
                and exp.mapping_type = 1
                and ps.platform_xcode != ''
                and ifnull(p.id, ifnull(bpx.id, 0)) > 0) tmp
        limit #{pageIndex}, #{pageSize}
    </select>

    <select id="queryAllRelationCount" resultType="int">
        select count(1)
        from (select ps.id
              from pl_eshop_product_sku ps
                       left join pl_eshop_product_sku_expand exp
                                 on ps.profile_id = exp.profile_id and ps.eshop_id = exp.eshop_id and
                                    exp.unique_id = ps.unique_id
                       left join pl_eshop_product_sku_mapping map
                                 on map.profile_id = ps.profile_id and map.eshop_id = ps.eshop_id and
                                    ps.unique_id = map.unique_id
              where ps.profile_id = #{profileId}
                and ps.eshop_id = #{eshopId}
                and exp.mapping_type = 0
                and map.id > 0
              union all
              select ps.id
              from pl_eshop_product_sku ps
                       left join pl_eshop_product_sku_expand exp
                                 on ps.profile_id = exp.profile_id and ps.eshop_id = exp.eshop_id and
                                    exp.unique_id = ps.unique_id
                       left join base_ptype p
                                 on p.profile_id = ps.profile_id and p.usercode = ps.platform_xcode
                       left join base_ptype_xcode bpx
                                 on bpx.profile_id = ps.profile_id and bpx.xcode = ps.platform_xcode
              where ps.profile_id = #{profileId}
                and ps.eshop_id = #{eshopId}
                and exp.mapping_type = 1
                and ps.platform_xcode != ''
                and ifnull(p.id, ifnull(bpx.id, 0)) > 0) tmp
    </select>
</mapper>
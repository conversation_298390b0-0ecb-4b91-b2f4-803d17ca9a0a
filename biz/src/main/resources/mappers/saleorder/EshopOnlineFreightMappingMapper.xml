<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopOnlineFreightMappingMapper">

    <select id="getEShopOnlineFreightMapping"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryShopFreightMappingParameter"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopOnlineFreightMapping">
        SELECT mp.`id`,
               b.profile_id,
               ifnull(mp.`shop_type`, #{shopType})          as `shop_type`,
               mp.`platform_freight_name`,
               mp.`platform_freight_code`,
               b.fullname                                   as localFreightName,
               b.usercode                                   as localFreightCode,
               ifnull(mp.`sys_freight_id`, b.`id`)          as `sys_freight_id`,
               mp.`create_time`,
               mp.`update_time`,
               IF(ifnull(mp.`sys_freight_id`, 0) = 0, 0, 1) as `mappingStatus`,
               IF(ifnull(mp.`sys_freight_id`, 0) = 0, 0, 1) as `mappingType`
        FROM base_btype b
                 JOIN base_btype_bcategory bb
                      on bb.profile_id = b.profile_id AND bb.btype_id = b.id
                 left JOIN pl_eshop_online_freight_mapping mp
                           on b.profile_id = mp.profile_id and b.id = mp.sys_freight_id and mp.`shop_type`=#{shopType}
        WHERE b.profile_id = #{profileId}
          and b.deleted = 0
          and b.stoped = 0
          AND b.classed = 0
          AND bb.bcategory = 2
        ORDER BY IF(mp.create_time is null, b.rowindex, mp.create_time) DESC
    </select>

    <insert id="insertOrUpdateEShopOnlineFreightMapping"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopOnlineFreightMapping">
        REPLACE INTO pl_eshop_online_freight_mapping
        (`id`, `profile_id`, `shop_type`, `platform_freight_name`, `platform_freight_code`, `sys_freight_id`,
         `create_time`, `update_time`)
        VALUES (#{id}, #{profileId}, #{shopType}, #{platformFreightName}, #{platformFreightCode}, #{sysFreightId},
                #{createTime}, #{updateTime})
    </insert>

    <delete id="deleteEShopOnlineFreightMapping">
        DELETE
        FROM pl_eshop_online_freight_mapping
        WHERE profile_id = #{profileId}
          AND shop_type = #{shopType}
    </delete>

    <delete id="deleteEShopOnlineFreightMappingById"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopOnlineFreightMapping">
        DELETE
        FROM pl_eshop_online_freight_mapping
        WHERE profile_id = #{profileId}
          AND shop_type = #{shopType}
          AND sys_freight_id = #{sysFreightId}
    </delete>
</mapper>
package com.wsgjp.ct.sale.biz.eshoporder.mapper;

import com.wsgjp.ct.sale.biz.eshoporder.entity.base.ComboDetail;
import com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopPlatformStoreMapping;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryProductMarkRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.stock.QueryProductSkuRuleConfigParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.stock.QueryRecordDetailParam;
import com.wsgjp.ct.sale.biz.eshoporder.entity.stock.*;
import com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Repository
public interface StockRuleMapper {

    void insertStockRule(StockSyncRule stockRuleEntity);

    void updateStockRule(StockSyncRule stockRuleEntity);

    int modifySyncRuleExpand(@Param("list") List<StockRuleExpandEntity> list);

    void deleteStockRule(StockSyncRule stockRuleEntity);

    void deleteStockRulePhysics(StockSyncRule stockRuleEntity);

    Integer getRuleCountByRuleName(StockSyncRule stockRuleEntity);

    List<StockSyncRule> getPtypeStockRules(QueryStockRuleParameter parameter);

    List<StockSyncRule> getAllStockRules(QueryStockRuleParameter parameter);

    List<StockSyncRule> queryDefaultRules(QueryStockRuleParameter parameter);


    List<StockSyncRule> queryCustomRuleList(QueryStockRuleParameter parameter);

    List<ProductStockSyncRule> queryProductRules(QueryProductRuleParameter parameter);

    List<ProductStockSyncRuleBase> queryProductBaseRules(QueryProductRuleParameter parameter);

    void addProductRules(@Param("list") List<ProductStockSyncRule> parameter);

    void deleteProductRules(@Param("list") List<ProductStockSyncRule> parameter);

    List<StockSyncRule> queryWarehouseRules(QueryStockRuleParameter parameter);

    List<StockSyncRule> queryLadderDefaultRules(QueryStockRuleParameter parameter);

    List<StockSyncRule> queryAutoSyncDefaultRules(@Param("profileId") BigInteger profileId, @Param("eshopIds") List<BigInteger> eshopIds, @Param("needAutoSync") Boolean needAutoSync);

    List<StockSyncRule> checkExistStockRules(QueryStockRuleParameter parameter);

    List<StockSyncRule> getComboRules(QueryStockRuleParameter parameter);


    List<ComboDetail> getChangeComboQty(@Param("profileId") BigInteger profileId, @Param("comboId") BigInteger comboId);

    List<StockRuleDetail> getRuleDetailList(QueryStockRuleParameter parameter);

    void insertKtypeRule(StockRuleDetail entity);

    void deleteRuleDetails(@Param("ruleId") BigInteger ruleId, @Param("profileId") BigInteger profileId);

    void insertDefaultRule(StockSyncRule stockRuleEntity);

    void saveWarehouseRule(StockSyncRule stockRule);

    void saveLadderDefaultRule(StockSyncRule stockRule);

    StockSyncRule getDefaultRule(QueryStockRuleParameter parameter);

    StockSyncRule getSimpleDefault(StockSyncRule rule);

    StockSyncRule getSimpleWarehouseRule(StockSyncRule rule);

    StockSyncRule getWarehouseRule(StockSyncRule rule);

    StockSyncRule getLadderDefaultRule(StockSyncRule rule);

    void saveProductSkuRuleConfig(EshopProductSkuRuleConfig config);

    /**
     * 删除网店商品的同步规则配置（对应关系变更或者删除的时候使用）
     *
     * @param profileId  账套ID
     * @param numId      网店商品Id
     * @param properties 属性名称
     */
    void deleteProductSkuRuleConfig(@Param("profileId") BigInteger profileId, @Param("numId") String numId, @Param("properties") String properties);

    /**
     * 查询网店商品同步配置列表
     *
     * @param parameter 查询参数
     * @return 返回网店商品同步配置列表
     */
    List<EshopProductSkuRuleConfig> getProductSkuRuleConfig(QueryProductSkuRuleConfigParameter parameter);

    int queryRuleRelationCount(@Param("profileId") BigInteger profileId, @Param("id") BigInteger id);

    /**
     * 根据商家编码查询按商家编码对应的对应关系
     *
     * @param profileId 账套id
     * @param eshopId   网店id
     * @param xcode     商家编码
     * @return 对应关系
     */
    List<EshopProductSkuMapping> querySkuMappingList(@Param("profileId") BigInteger profileId, @Param("eshopId") BigInteger eshopId, @Param("xcode") String xcode);

    /**
     * 根据规则id查询原单历史的可销售库存占用明细
     *
     * @param parameter 参数
     * @return 订单的占用明细
     */
    List<StockRecordOrderDetail> queryRecordDetails(QueryRecordDetailParam parameter);

    /**
     * 查询使用规则的店铺id
     *
     * @param profileId 账套id
     * @param ruleId    规则id
     * @return 网店id
     */
    List<BigInteger> queryUseRuleEshop(@Param("profileId") BigInteger profileId, @Param("ruleId") BigInteger ruleId);

    List<StockSyncConfig> getAllStockSyncConfigs(QueryStockConfigParameter parameter);

    int getAllStockSyncConfigsCount(QueryStockConfigParameter parameter);

    void insertStockSyncConfig(StockSyncConfig config);

    void updateStockSyncConfig(StockSyncConfig config);

    void deleteStockSyncConfigs(@Param("profileId") BigInteger profileId, @Param("ids") List<BigInteger> ids);

    List<StockSyncConfigApply> getAllStockSyncConfigApplys(QueryStockConfigParameter parameter);

    int batchInsertStockSyncConfigApplys(List<StockSyncConfigApply> applys);

    void insertStockSyncConfigApply(StockSyncConfigApply apply);

    void updateStockSyncConfigApply(StockSyncConfigApply apply);

    void deleteStockSyncConfigApplys(@Param("profileId") BigInteger profileId, @Param("uniqueIds") List<String> uniqueIds);

    void insertStockSyncConfigApplyLimitBatch(StockSyncConfigLimitBatch apply);

    int batchinsertStockSyncConfigApplyLimitBatchs(List<StockSyncConfigLimitBatch> applyBatchs);

    void updateStockSyncConfigApplyLimitBatch(StockSyncConfigLimitBatch apply);

    void deleteStockSyncConfigApplyLimitBatchs(@Param("profileId") BigInteger profileId, @Param("applyIds") List<BigInteger> applyIds);

    List<ApplyStockSyncConfig> getAllApplyStockConfigs(QueryStockConfigParameter parameter);

    void modifyAutoSyncState(@Param("profileId") BigInteger profileId, @Param("configId") BigInteger configId,
                             @Param("autoSyncEnabled") boolean autoSyncEnabled);

    List<PtypeXCodeEntity> getComboPtyeXcodeInfo(@Param("profileId") BigInteger profileId, @Param("ptypeIds") List<BigInteger> ptypeIds);

    List<PtypeSkuEntity> getSkuInfoByPtypeid(@Param("profileId") BigInteger profileId, @Param("ptypeIds") List<BigInteger> ptypeIds);

    List<ProductSkuMultiStockSyncSettingEntity> getProductSkuMultiStockSyncSetting(@Param("profileId") BigInteger profileId, @Param("uniqueIds") List<String> uniqueIds);

    List<EshopProductMark> queryProductMarkByUniqueids(QueryProductMarkRequest markRequest);

    List<EshopProductMark> queryProductMarkByNumids(QueryProductMarkRequest markRequest);

    void batchinsertMultiStockSyncSettings(List<ProductSkuMultiStockSyncSettingEntity> itemPageSave);

    List<StockSyncRule> queryCustomRules(QueryStockRuleParameter parameter);

    void deleteCustomRuleById(@Param("profileId") BigInteger profileId, @Param("id") BigInteger id);

    void batchDeleteCustomRules(@Param("profileId") BigInteger profileId, @Param("ids") List<BigInteger> ids);

    void deleteCustomRuleDetailByRuleId(@Param("profileId") BigInteger profileId, @Param("ruleId") BigInteger ruleId);

    void batchDeleteCustomRuleDetails(@Param("profileId") BigInteger profileId, @Param("ruleIds") List<BigInteger> ruleIds);

    void insertCustomRule(StockSyncRule rule);

    void batchInsertCustomRuleDetail(@Param("ruleDetailList") List<StockRuleDetail> ruleDetailList);

    List<StockSyncRule> getSyncRuleByRuleIds(QueryStockRuleParameter parameter);

    List<StockRuleDetail> getSyncRuleDetailByRuleIds(QueryStockRuleParameter parameter);


    List<MultiStockSyncDetail> getMultiStockSyncDetailList(@Param("profileId") BigInteger profileId, @Param("ruleIds") List<BigInteger> ruleIds);

    List<EshopPlatformStoreMapping> queryWarehousesSyncStoreByEshopId(@Param("profileId") BigInteger profileId, @Param("eshopIds") List<BigInteger> eshopIds);


    int updateAutoSyncStockEnabled(StockSyncRule rule);

    int batchDeleteCustomRule(@Param("profileId") BigInteger profileId, @Param("ruleIdList") List<BigInteger> ruleIdList);
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopRefundBillCoreMapper">

    <select id="findRefundOptLogOldState" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.refund.RefundOptLogEntity">
        SELECT refund_order_id, old_state
        FROM pl_eshop_refund_opt_log
        WHERE profile_id = #{profileId}
          AND refund_order_id = #{refundOrderId}
          AND state_type = #{stateType}
        ORDER BY create_time DESC LIMIT 1
    </select>
    <update id="updateRefundReceiveState">
        UPDATE `pl_eshop_refund`
        SET receive_state=#{recentlyState}
        WHERE profile_id = #{profileId}
          AND id = #{id}
    </update>
    <update id="updateRefundProcessState">
        UPDATE `pl_eshop_refund`
        SET refund_process_state=#{recentlyState},
            refund_process_time=null
        WHERE profile_id = #{profileId}
          AND id = #{id}
    </update>
    <update id="updateRefundPayState">
        UPDATE `pl_eshop_refund`
        SET pay_state=#{recentlyState},
            pay_time=null
        WHERE profile_id = #{profileId}
          AND id = #{id}
    </update>
    <update id="updateRefundReturnStorage">
        UPDATE pl_eshop_refund
        SET receive_state=#{recentlyState},
            receive_time=null,
            receive_buyer_id=0,
            bill_poseted=0,
            has_submit_to_wms=0,
            receive_remark=''
        WHERE profile_id = #{profileId}
          AND id = #{id} and refund_type != 0;
        UPDATE pl_eshop_refund_extend
        SET receive_etype_id=0
        WHERE profile_id = #{profileId}
          AND refund_order_id = #{id};
    </update>
    <update id="updateRefundReturnStorageForMoneyOnly">
        UPDATE pl_eshop_refund
        SET receive_state=#{recentlyState},
            receive_time=null,
            receive_buyer_id=0,
            bill_poseted=0,
            has_submit_to_wms=0,
            receive_remark=''
        WHERE profile_id = #{profileId}
          AND id = #{id};
        UPDATE pl_eshop_refund_extend
        SET receive_etype_id=0
        WHERE profile_id = #{profileId}
          AND refund_order_id = #{id}
    </update>
    <update id="updateRefundAccountingReturnStorage">
        UPDATE `pl_eshop_refund`
        SET receive_state=#{recentlyState}
        WHERE profile_id = #{profileId}
          AND id = #{id}
    </update>
</mapper>
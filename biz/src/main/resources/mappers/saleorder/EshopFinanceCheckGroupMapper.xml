<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopFinanceCheckGroupMapper">
    <select id="queryFinanceCheckGroup"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.FinanceCheckGroupRes">
        select id, profile_id, eshop_id, group_name, start_time, end_time, group_rule
        from pl_eshop_finance_check_group 
        where profile_id=#{profileId}
        <if test="groupName != '' and groupName != null">
            and group_name like CONCAT('%',#{groupName},'%')
        </if>
        <if test="groupRule != '' and groupRule != null">
            and group_rule like CONCAT('%',#{groupRule},'%')
        </if>
        <if test="eshopId != '' and eshopId != null">
            and eshop_id like CONCAT('%',#{eshopId},'%')
        </if>
    </select>
    <insert id="insertFinanceCheckGroup" >
        INSERT INTO pl_eshop_finance_check_group (id, profile_id, eshop_id, group_name, start_time, end_time, group_rule)
        value (#{id}, #{profileId}, #{eshopId}, #{groupName}, #{startTime}, #{endTime}, #{groupRule})
    </insert>
    <delete id="deleteFinanceCheckGroup">
        delete from pl_eshop_finance_check_group where profile_id = #{profileId} and group_rule = #{groupRule} and group_name = #{groupName}
    </delete>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>

<Page xmlns="Craba.UI" Title="${pageTitle}"
      ActionType="sale.shopsale.pages.store.StorePageAction, sale/shopsale/pages/store/StorePage.js"
      DataSource="${datasource}" CssClass='BasePage StorePage'>
    <Script Src="${mapSdkUrl}"></Script>
    <Style>
        .StorePage .Map {
        margin-left: 105px;
        position: relative;
        width: 605px;
        height: 600px
        }

        .StorePage .MapView {
        width: 100%;
        height: 100%;
        position: absolute;
        }

        .StorePage .MapTip {
        background-color: #fff;
        color: #333;
        box-shadow: 3px 4px 3px 0px silver;
        position: absolute;
        top: 20px;
        right: 20px;
        border-radius: 5px;
        overflow: hidden;
        line-height: 35px;
        }

        .StorePage .MapInput {
        height: 35px;
        border: 0;
        width: 280px;
        border-radius: 3px;
        outline: none;
        }

        .StorePage .Time {
        border-radius: 4px;
        height: 30px;
        }


        .StorePage .Time .EditBlock .SkinButton {
        border: none;
        }


    </Style>

    <HiddenField ID="profileId" DataField="profileId"/>
    <HiddenField ID="otypeId" DataField="otypeId"/>
    <HiddenField ID="ocategory" DataField="ocategory"/>
    <HiddenField ID="province" DataField="province"/>
    <HiddenField ID="city" DataField="city"/>
    <HiddenField ID="district" DataField="district"/>
    <HiddenField ID="longitude" DataField="addrLng"/>
    <HiddenField ID="latitude" DataField="addrLat"/>
    <HiddenField ID="businessType" DataField="businessType"/>

    <FlexColumn CssClass="oauto prl10">
        <FlexColumn CssClass="oauto prl10" Width="980">
            <Label Text="基本信息" CssClass='LayoutGroupCaption'/>
            <FlowPanel ColSpan='2' ItemLabelWidth="105">
                <TextEdit ID="orgUserCode" DataField="usercode" Label="门店编号"
                          LabelCssClass=" MustCharLeft" LabelStyle="justify-content: right"
                          Required="true" NullDisplayText="请填写门店编号"
                          MaxLength="30"/>
                <TextEdit ID="orgName" DataField="fullname" Label="门店名称"
                          LabelCssClass="MustCharLeft " LabelStyle="justify-content: right"
                          Required="true" NullDisplayText="请填写门店名称"
                          MaxLength="100"/>
                <SelectorEdit ShowMDI="true" Label="选择分类" DisplayField="parFullName"
                              DataField="partypeId" NullDisplayText="请选选择分类"
                              OnButtonClick="onClassInit" OnSelectorSelected="doClassSelect"/>
                <SelectorEdit Label="店长" LabelCssClass="MustCharLeft" DisplayField="shopOwner"
                              LabelStyle="justify-content: right"
                              DataField="ownerId" ID="edEtype" NullDisplayText="请选择店长"
                              OnButtonClick="onEtypeInit"
                              Business="{'Name':'shopOwner', 'showAdd':'false', 'showFilter':'true', 'stoped':'null'}"
                              OnSelectorSelected="doBaseInfoSelect" Required="true"/>

                <!--                <SelectorEdit ID="edEtype" Label="经手人:" DataField="etypeId" DisplayField="etypeName" Tag="etype" OnChange="doEtypeChange"-->
                <!--                              OnButtonClick="onEtypeInit" OnSelectorSelected="doBaseInfoSelect"/>-->
                <CustomControl Visible="true" ID="ccETypeDropdownSelector"
                               Src="sale/shopsale/control/ETypeDropdownSelector.gspx" Label="门店业务员:"/>
                <CustomControl Visible="true" ID="ccStoreCashRegister" LabelCssClass="MustCharLeft"
                               LabelStyle="justify-content: right"
                               Src="sale/shopsale/control/StoreCashRegister.gspx" Label="门店收银机:"/>
                <HBlock Label="营业时间" LabelCssClass="MustCharLeft" LabelStyle="justify-content: right"
                        CssClass="ComEditor  Time" ShowBorder="true">
                    <TimeEdit CssStyle="border:none;" Label="开始时间" ShowLabel="false" SelectOnly="true"
                              CssClass="FlexAuto" Required="true"
                              DataField="openTime"/>
                    <Label Text="~"></Label>
                    <TimeEdit CssStyle="border:none;" Label="结束时间" SelectOnly="true" Required="true"
                              CssClass="FlexAuto"
                              DataField="closeTime"
                    />
                </HBlock>
                <TextEdit ID="storeMobile" DataField="cellphone" Label="门店联系电话"
                          NullDisplayText="请填写门店联系电话"
                          MaxLength="12"/>
                <TextEdit ID="minDiscount" DataField="minDiscount" Label="手工最低折扣"
                          NullDisplayText="请填写手工最低折扣" OnDataBind="minDiscountBind" OnBlur="minDiscountOnBlur"/>
                <SelectorEdit ID="edBType" Label="默认往来单位" DataField="btypeId" MaxLength="100"
                              NullDisplayText="请选择往来单位"
                              OnButtonClick="onBtypeInit" DisplayField="btypeName"
                              OnSelectorSelected="doBaseInfoSelect"
                              Business="{'Name':'jxc.btype', 'showAdd':'false', 'showFilter':'true', 'stoped':'null'}"/>

                <CustomControl Visible="true" ID="payRelation" LabelCssClass="MustCharLeft"
                               LabelStyle="justify-content: right" _Point="StoreFunc"
                               excludePaywayType="8,9"
                               Src="sale/shopsale/control/PaywayRelation.gspx" Label="支付方式:"/>

                <SelectorEdit ID="edKType" Label="门店仓库" DataField="ktypeId" MaxLength="100"
                              NullDisplayText="请门店仓库"
                              Business="{'Name':'jxc.ktype', 'showAdd':'false', 'showFilter':'true', 'stoped':'false'}"
                              DisplayField="ktypeName"
                              OnSelectorSelected="doBaseInfoSelect"
                />
                <HBlock>
                </HBlock>
                <Block ColSpan='2' Label="门店LOGO" CssClass=" ImageBlock NoBorder">
                    <Block CssClass="fileIcon Flex0">
                        <Button Icon='aicon-uploader' Width="80" Height="80" CssStyle="border-style:dashed;"/>
                        <Image ID='imgShopLogoUrl' Width="80" Height="80" DataField="shopLogoUrl"
                               CssStyle="position:absolute;left:0px;top:0px;"/>
                        <FileEdit Multiple="true" CssClass="ButtonOpacity" CssStyle="width: 80px;
    height: 80px" OnChange="shopLogoUpload"
                                  Accept="image/jpeg,image/png,image/bmp,image/jpg"/>
                    </Block>

                    <Label CssClass='upTips' CssStyle="    color: #ccc;"
                           Text='图片仅支持jpg、jpeg、bmp、png格式,大小不超过2M'/>
                </Block>
                <Block ColSpan='2' Label="门店二维码" CssClass=" ImageBlock NoBorder">
                    <Block CssStyle="display: flex;align-items: end;">
                        <Block CssClass="fileIcon Flex0">
                            <Button Icon='aicon-uploader' Width="80" Height="80" CssStyle="border-style:dashed;"/>
                            <Image ID='imgShopScanUrl' Width="80" Height="80" DataField="shopScanUrl"
                                   CssStyle="position:absolute;left:0px;top:0px;"/>
                            <FileEdit Multiple="true" CssClass="ButtonOpacity" OnChange="shopScanUpload"
                                      Accept="image/jpeg,image/png,image/bmp,image/jpg"/>
                        </Block>
                        <TextEdit DataField="shopScanMemo"
                                  CssStyle="width: 100%;margin-left: 12px"
                                  NullDisplayText="请填写门店二维码说明"
                                  MaxLength="30"/>
                    </Block>
                    <Label CssClass='upTips' CssStyle="    color: #ccc;"
                           Text='图片仅支持jpg、jpeg、bmp、png格式,大小不超过2M'/>

                </Block>

                <TextEdit ColSpan='2' DataField="shopNotice" Label="门店公告" Width="665" MaxLength="300"
                          Height="50"
                          NullDisplayText="请填写门店公告（选填）"/>
                <TextEdit ColSpan='2' ID="memo" DataField="memo" Label="门店备注" Width="665" MaxLength="300"
                          Height="50"
                          NullDisplayText="请填写门店备注（选填）"/>

                <!--                <CheckBox ColSpan='2' Label="独立核算利润" Text="" ID="IndependentCheck" OnClick="doCheckChangeIndependent"-->
                <!--                          Visible="${isIndependentCheck}" DataField="independentCheck"/>-->

            </FlowPanel>
            <Label Text="地址信息" Tag="AddressTopic" CssClass='LayoutGroupCaption LabelTitle'/>
            <FlowPanel ColSpan='2' ItemLabelWidth="105">
                <Block ColSpan='2' CssClass="Map">
                    <Block CssClass="MapView" ID='mapDom'/>
                    <HPanel CssClass=" MapTip">
                        <TextEdit CssClass="MapInput" ID='keyword' NullDisplayText="请输入关键字：(选定后搜索)"/>
                    </HPanel>
                </Block>
                <TextEdit ID="districtAdd" DataField="districtAdd" Enabled="false" Required="false" Width="200"
                          Label="门店地址"
                ></TextEdit>

                <TextEdit ID="addressInfo" DataField="address"
                          ShowLabel="true" NullDisplayText="填写详细地址并在地图搜索确定准确位置"
                          MaxLength="100"/>
            </FlowPanel>
        </FlexColumn>

    </FlexColumn>
    <FlowPanel CssClass="BottomBlock">
        <Block CssClass='Flex1'/>
        <Button ID="btnSave" CssClass='SpecialButton' Text="保存" OnClick="doBtnSave"/>
        <CancelButton Text="关闭"/>
        <Block CssClass='Flex1'/>
    </FlowPanel>
</Page>
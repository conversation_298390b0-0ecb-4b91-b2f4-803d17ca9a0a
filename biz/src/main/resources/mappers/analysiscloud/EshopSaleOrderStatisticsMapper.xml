<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.analysiscloud.mapper.EshopSaleOrderStatisticsMapper">
    <select id="getEshop" resultType="com.wsgjp.ct.sale.biz.analysiscloud.entity.EshopInfoEntity">
        select bo.id as otypeId,bo.fullname from base_otype bo
        <include refid="com.wsgjp.ct.sale.template.limit-otype">
            <property name="limit-alias-id" value="bo.id"/>
            <property name="limit-alias-profileid" value="bo.profile_id"/>
        </include>
        <where>
            bo.profile_id=#{profileId}
            <include refid="com.wsgjp.ct.sale.template.limit-where-otype">
                <property name="limit-alias-id" value="bo.id"/>
            </include>
            and bo.deleted=0 and bo.classed=0
        </where>
        order by bo.rowindex
    </select>

    <sql id="odcTotal">
        <choose>
            <when test="query.sysGlobalEnabledTax==true">
                odc.dised_taxed_total
            </when>
            <otherwise>
                odc.trade_total
            </otherwise>
        </choose>
    </sql>
    <sql id="odcShopTotal">
        <choose>
            <when test="query.sysGlobalEnabledTax==true">
                ifnull(odc.dised_taxed_total,0)
            </when>
            <otherwise>
                ifnull(odc.total,0)
            </otherwise>
        </choose>
    </sql>
    <sql id="ocShopTotalNew">
        <choose>
            <when test="query.sysGlobalEnabledTax==true">
                ifnull(oc.dised_taxed_total,0)
            </when>
            <otherwise>
                ifnull(oc.total,0)
            </otherwise>
        </choose>
    </sql>
    <sql id="searchType">
        <choose>
            <when test="query.searchRangeType==0">
                pl_eshop_sale_order oc left join
                pl_eshop_sale_order_timing oct ON oc.id = oct.eshop_order_id AND oc.profile_id = oct.profile_id left
                join
                pl_eshop_sale_order_detail odc ON oc.id = odc.eshop_order_id AND odc.profile_id = oc.profile_id
                left join base_ptype_unit sbunit on sbunit.profile_id = odc.profile_id and sbunit.ptype_id =
                odc.ptype_id and sbunit.id = odc.unit
                <include refid="com.wsgjp.ct.sale.template.limit-etype">
                    <property name="limit-alias-id" value="oc.etype_id"/>
                    <property name="limit-alias-profileid" value="oc.profile_id"/>
                </include>
                <include refid="com.wsgjp.ct.sale.template.limit-ktype">
                    <property name="limit-alias-id" value="odc.ktype_id"/>
                    <property name="limit-alias-profileid" value="odc.profile_id"/>
                </include>

                <include refid="com.wsgjp.ct.sale.template.limit-otype">
                    <property name="limit-alias-id" value="odc.otype_id"/>
                    <property name="limit-alias-profileid" value="odc.profile_id"/>
                </include>
                <!--                <include refid="com.wsgjp.ct.sale.template.limit-btype">-->
                <!--                    <property name="limit-alias-id" value="odc.btype_id"/>-->
                <!--                    <property name="limit-alias-profileid" value="odc.profile_id"/>-->
                <!--                </include>-->
            </when>
            <otherwise>
                pl_eshop_sale_order oc left join
                pl_eshop_sale_order_timing oct ON oc.id = oct.eshop_order_id AND oc.profile_id = oct.profile_id left
                join
                pl_eshop_sale_order_detail odc ON oc.id = odc.eshop_order_id AND odc.profile_id = oc.profile_id
                <include refid="com.wsgjp.ct.sale.template.limit-etype">
                    <property name="limit-alias-id" value="oc.etype_id"/>
                    <property name="limit-alias-profileid" value="oc.profile_id"/>
                </include>
                <include refid="com.wsgjp.ct.sale.template.limit-ktype">
                    <property name="limit-alias-id" value="odc.ktype_id"/>
                    <property name="limit-alias-profileid" value="odc.profile_id"/>
                </include>

                <include refid="com.wsgjp.ct.sale.template.limit-otype">
                    <property name="limit-alias-id" value="odc.otype_id"/>
                    <property name="limit-alias-profileid" value="odc.profile_id"/>
                </include>
                <!--                <include refid="com.wsgjp.ct.sale.template.limit-btype">-->
                <!--                    <property name="limit-alias-id" value="odc.btype_id"/>-->
                <!--                    <property name="limit-alias-profileid" value="odc.profile_id"/>-->
                <!--                </include>-->
            </otherwise>
        </choose>
    </sql>
    <sql id="searchShopType">
        <choose>
            <when test="query.searchRangeType==0">
                pl_eshop_sale_order oc
                <include refid="com.wsgjp.ct.sale.template.limit-etype">
                    <property name="limit-alias-id" value="oc.etype_id"/>
                    <property name="limit-alias-profileid" value="oc.profile_id"/>
                </include>
                <include refid="com.wsgjp.ct.sale.template.limit-ktype">
                    <property name="limit-alias-id" value="oc.ktype_id"/>
                    <property name="limit-alias-profileid" value="oc.profile_id"/>
                </include>

                <include refid="com.wsgjp.ct.sale.template.limit-otype">
                    <property name="limit-alias-id" value="oc.otype_id"/>
                    <property name="limit-alias-profileid" value="oc.profile_id"/>
                </include>
                <include refid="com.wsgjp.ct.sale.template.limit-btype">
                    <property name="limit-alias-id" value="oc.btype_id"/>
                    <property name="limit-alias-profileid" value="oc.profile_id"/>
                </include>
            </when>
            <otherwise>
                pl_eshop_sale_order oc
                <include refid="com.wsgjp.ct.sale.template.limit-etype">
                    <property name="limit-alias-id" value="oc.etype_id"/>
                    <property name="limit-alias-profileid" value="oc.profile_id"/>
                </include>
                <include refid="com.wsgjp.ct.sale.template.limit-ktype">
                    <property name="limit-alias-id" value="oc.ktype_id"/>
                    <property name="limit-alias-profileid" value="oc.profile_id"/>
                </include>

                <include refid="com.wsgjp.ct.sale.template.limit-otype">
                    <property name="limit-alias-id" value="oc.otype_id"/>
                    <property name="limit-alias-profileid" value="oc.profile_id"/>
                </include>
                <include refid="com.wsgjp.ct.sale.template.limit-btype">
                    <property name="limit-alias-id" value="oc.btype_id"/>
                    <property name="limit-alias-profileid" value="oc.profile_id"/>
                </include>
            </otherwise>
        </choose>
    </sql>
    <sql id="searchTimeType">
        <choose>
            <when test="query.rangeDateType==0">
                oc.trade_create_time
            </when>
            <when test="query.rangeDateType==1">
                oc.create_time
            </when>
            <when test="query.rangeDateType==3">
                oc.trade_finish_time
            </when>
            <when test="query.rangeDateType==4">
                oct.sign_time
            </when>
            <otherwise>
                oc.trade_pay_time
            </otherwise>
        </choose>
    </sql>
    <sql id="selectColumn">
        SUM(CASE WHEN oc.local_refund_process_state != 5 THEN odc.qty ELSE 0 END) AS sale_qty,
        SUM(CASE WHEN oc.local_refund_process_state != 5 THEN odc.sub_qty ELSE 0 END) AS subQty,
        SUM(CASE WHEN oc.local_refund_process_state != 5 THEN <include refid="odcTotal"></include> ELSE 0 END) AS
        buyer_dised_taxed_total,
        SUM(CASE WHEN oc.local_refund_process_state != 5 THEN
        <include refid="odcTotal"></include>
        ELSE 0 END) / SUM(CASE WHEN oc.local_refund_process_state != 5 THEN odc.qty ELSE 0 END) AS
        buyer_dised_taxed_price,
        SUM(CASE WHEN oc.local_refund_process_state = 5 THEN odc.qty ELSE 0 END) AS returned_qty,
        SUM(CASE WHEN oc.local_refund_process_state = 5 THEN <include refid="odcTotal"></include> ELSE 0 END) AS
        returned_total,
        ROUND(100*SUM(CASE WHEN oc.local_refund_process_state = 5 THEN odc.qty ELSE 0 END) / SUM(CASE WHEN
        oc.local_refund_process_state != 5 THEN odc.qty ELSE 0 END) ,4) AS return_percent
    </sql>
    <sql id="selectWhere">
        oc.profile_id = #{profileId} and oc.deleted=0 and oc.local_trade_state!=5 and oc.mapping_state=1 and
        ((oc.local_refund_state=0 or oc.local_refund_state=2 or (oc.local_refund_state=3 and oc.local_trade_state in
        (3,4))) or (oc.local_refund_state=4 and oc.local_trade_state in (3,4)))
        and oc.create_type != 2
        and
        <include refid="searchTimeType"></include>
        between #{query.startDate} and #{query.endDate}
        <if test="query.ptypeId != null">
            and odc.ptype_id = #{query.ptypeId}
        </if>
        <if test="query.skuId != null">
            and odc.sku_id = #{query.skuId}
        </if>
        <if test="query.otypeId > 0">
            and oc.otype_id = #{query.otypeId}
        </if>
        <if test="query.billBusinessTypes != null and query.billBusinessTypes.size() > 0">
            and oc.business_type in
            <foreach collection="query.billBusinessTypes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.otypeIds != null and query.otypeIds.size() != 0">
            and oc.otype_id in
            <include refid="com.wsgjp.ct.sale.template.query-in">
                <property name="_list" value="query.otypeIds"/>
            </include>
        </if>
        <include refid="com.wsgjp.ct.sale.template.query-filter-or-refund">
            <property name="_list" value="query.filter"/>
        </include>
        <if test="query.ptypeSourceType == 1">
            and odc.combo_row_id = 0
        </if>
        <if test="query.ptypeSourceType == 2">
            and odc.combo_row_id > 0
        </if>
        <if test="query.orderCreateType == 1">
            and oc.create_type =0
        </if>
        <if test="query.orderCreateType == 2">
            and oc.create_type in (3,16)
        </if>
        <if test="query.orderCreateType == 3">
            and oc.create_type =1
        </if>
    </sql>

    <sql id="selectBody">
        <include refid="searchType"></include>
        LEFT JOIN base_otype otype ON oc.otype_id = otype.id and otype.profile_id=oc.profile_id
        left JOIN base_ptype p ON odc.ptype_id = p.id and p.profile_id=odc.profile_id
        LEFT JOIN base_ptype_pic pic ON p.id = pic.ptype_id and pic.`profile_id` = p.`profile_id` AND pic.rowindex = 1
        LEFT JOIN `base_brandtype` brand ON p.`brand_id` = brand.`id` AND brand.`profile_id` = p.`profile_id`
        LEFT JOIN `base_ptype_unit` unit ON p.`id` = unit.`ptype_id` AND unit.`profile_id` = p.`profile_id` AND
        unit.`unit_code` = 1
        Left join base_ptype_fullbarcode bpf on bpf.profile_id = oc.profile_id and bpf.ptype_id=p.id and
        bpf.sku_id=odc.sku_id and bpf.defaulted=1 AND unit.id=bpf.unit_id
        left join base_btype b on b.profile_id=oc.profile_id and oc.btype_id=b.id
        <if test="query.openSku">
            LEFT JOIN base_ptype_sku sku ON odc.`sku_id` = sku.`id` AND sku.`profile_id` = odc.profile_id
            left join base_ptype_xcode px on px.ptype_id=p.id and px.sku_id=sku.id and px.unit_id=unit.id and
            px.defaulted=1 and px.profile_id = p.profile_id
        </if>
    </sql>

    <sql id="selectBodyCount">
        <include refid="selectBody"></include>
    </sql>
    <sql id="selectPic">
        <choose>
            <when test="query.openSku">
                CASE WHEN sku.pic_url= '' THEN pic.pic_url ELSE sku.pic_url END AS pic_url
            </when>
            <otherwise>
                pic.pic_url
            </otherwise>
        </choose>
    </sql>
    <select id="listEshopSaleOrderStatistics"
            resultType="com.wsgjp.ct.sale.biz.analysiscloud.entity.EshopSaleOrderStatisticsEntity">
        SELECT <include refid="selectPic"></include>,p.id as ptype_id,p.`usercode`,p.fullname as
        p_fullname,p.`ptype_type`,
        p.`standard`,brand.`brand_name`,unit.`unit_name`,p.memo as
        ptype_memo,bpf.fullbarcode,oc.business_type,p.sub_unit as subUnit,
        <if test="query.openSku">
            sku.id as sku_id,px.xcode,
            CONCAT_WS(':',sku.prop_name1,sku.prop_name2,sku.prop_name3,sku.prop_name4,sku.prop_name5,sku.prop_name6) AS
            prop_names,
            CONCAT_WS(':',sku.propvalue_name1,sku.propvalue_name2,sku.propvalue_name3,sku.propvalue_name4,sku.propvalue_name5,sku.propvalue_name6)
            AS prop_values,
        </if>
        <include refid="selectColumn"></include>
        FROM
        <include refid="selectBody"></include>
        <where>
            <include refid="selectWhere"></include>
            and odc.process_state != 5
            AND (`oc`.order_sale_type  not in(3,6) OR (`oc`.order_sale_type=3 AND `oc`.platform_parent_order_id=''))
            <include refid="com.wsgjp.ct.sale.template.limit-where-etype">
                <property name="limit-alias-id" value="oc.etype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-ktype">
                <property name="limit-alias-id" value="odc.ktype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-otype">
                <property name="limit-alias-id" value="odc.otype_id"/>
            </include>
            and otype.ocategory!=10
            <!--            <include refid="com.wsgjp.ct.sale.template.limit-where-btype">-->
            <!--                <property name="limit-alias-id" value="odc.btype_id"/>-->
            <!--            </include>-->
        </where>

        <choose>
            <when test="query.openSku">
                GROUP BY odc.sku_id
            </when>
            <otherwise>
                GROUP BY odc.ptype_id
            </otherwise>
        </choose>
        <include refid="havingSum"></include>
        order by p.rowindex
    </select>

    <select id="listEshopSaleOrderStatistics_COUNT" resultType="java.lang.Integer">
        select count(0)
        from (
        <include refid="listCountTemp"></include>
        ) table_count
    </select>
    <sql id="listCountTemp">
        SELECT oc.id,oc.order_buyer_freight_fee,
        SUM(CASE WHEN oc.local_refund_process_state != 5 THEN odc.sub_qty ELSE 0 END) AS subQty
        FROM
        <include refid="selectBodyCount"></include>
        <where>
            <include refid="selectWhere"></include>
            and odc.process_state !=5
            <include refid="com.wsgjp.ct.sale.template.limit-where-etype">
                <property name="limit-alias-id" value="oc.etype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-ktype">
                <property name="limit-alias-id" value="odc.ktype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-otype">
                <property name="limit-alias-id" value="odc.otype_id"/>
            </include>
            <!--            <include refid="com.wsgjp.ct.sale.template.limit-where-btype">-->
            <!--                <property name="limit-alias-id" value="odc.btype_id"/>-->
            <!--            </include>-->
        </where>

        <choose>
            <when test="query.openSku">
                GROUP BY odc.sku_id
            </when>
            <otherwise>
                GROUP BY odc.ptype_id
            </otherwise>
        </choose>
        <include refid="havingSum"></include>

    </sql>
    <select id="listEshopSaleOrderStatisticsSummary"
            resultType="com.wsgjp.ct.sale.biz.analysiscloud.entity.EshopSaleOrderStatisticsEntity">
        select SUM(sale_qty) AS sale_qty,SUM(buyer_dised_taxed_total) buyer_dised_taxed_total,SUM(subQty) as subQty
        from(
        SELECT
        <include refid="selectColumn"></include>
        FROM
        <include refid="selectBody"></include>
        <where>
            <include refid="selectWhere"></include>
            and odc.process_state != 5
            AND (`oc`.order_sale_type  not in(3,6) OR (`oc`.order_sale_type=3 AND `oc`.platform_parent_order_id=''))
            <include refid="com.wsgjp.ct.sale.template.limit-where-etype">
                <property name="limit-alias-id" value="oc.etype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-ktype">
                <property name="limit-alias-id" value="odc.ktype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-otype">
                <property name="limit-alias-id" value="odc.otype_id"/>
            </include>
            and otype.ocategory!=10
            <!--            <include refid="com.wsgjp.ct.sale.template.limit-where-btype">-->
            <!--                <property name="limit-alias-id" value="odc.btype_id"/>-->
            <!--            </include>-->
        </where>
        <choose>
            <when test="query.openSku">
                GROUP BY odc.sku_id
            </when>
            <otherwise>
                GROUP BY odc.ptype_id
            </otherwise>
        </choose>
        <include refid="havingSum"></include>
        )a
    </select>
    <sql id="havingSum">
        <if test="query.filter != null">
            <trim prefix="having (" suffix=")">
                <foreach collection="query.filter" item="item" separator="and">
                    <if test="item.type == 3">
                        <choose>
                            <when test="item.dataField == 'sale_qty'">
                                <trim prefixOverrides="and" prefix="(" suffix=")">
                                    <if test="item.value1 != null ">
                                        SUM(CASE WHEN oc.local_refund_process_state != 5 THEN odc.qty ELSE 0 END)
                                        <![CDATA[>=]]> #{item.value1}
                                    </if>
                                    <if test="item.value2 != null ">
                                        and SUM(CASE WHEN oc.local_refund_process_state != 5 THEN odc.qty ELSE 0 END)
                                        <![CDATA[<=]]> #{item.value2}
                                    </if>
                                </trim>
                            </when>
                            <when test="item.dataField == 'buyer_dised_taxed_total'">
                                <trim prefixOverrides="and" prefix="(" suffix=")">
                                    <if test="item.value1 != null ">
                                        SUM(CASE WHEN oc.local_refund_process_state != 5 THEN <include
                                            refid="odcTotal"></include> ELSE 0 END) <![CDATA[>=]]> #{item.value1}
                                    </if>
                                    <if test="item.value2 != null ">
                                        and SUM(CASE WHEN oc.local_refund_process_state != 5 THEN <include
                                            refid="odcTotal"></include> ELSE 0 END) <![CDATA[<=]]> #{item.value2}
                                    </if>
                                </trim>
                            </when>
                            <when test="item.dataField == 'buyer_dised_taxed_price'">
                                <trim prefixOverrides="and" prefix="(" suffix=")">
                                    <if test="item.value1 != null ">
                                        SUM(CASE WHEN oc.local_refund_process_state != 5 THEN
                                        <include refid="odcTotal"></include>
                                        ELSE 0 END) / SUM(CASE WHEN oc.local_refund_process_state != 5 THEN odc.qty ELSE
                                        0
                                        END)  <![CDATA[>=]]> #{item.value1}
                                    </if>
                                    <if test="item.value2 != null ">
                                        and SUM(CASE WHEN oc.local_refund_process_state != 5 THEN
                                        <include refid="odcTotal"></include>
                                        ELSE 0 END) / SUM(CASE WHEN oc.local_refund_process_state != 5 THEN odc.qty ELSE
                                        0
                                        END)  <![CDATA[<=]]> #{item.value2}
                                    </if>
                                </trim>
                            </when>
                            <when test="item.dataField == 'sub_qty'">
                                <trim prefixOverrides="and" prefix="(" suffix=")">
                                    <if test="item.value1 != null ">
                                        SUM(CASE
                                        WHEN oc.local_refund_process_state != 5 THEN odc.sub_qty
                                        ELSE 0
                                        END) <![CDATA[>=]]> #{item.value1}
                                    </if>
                                    <if test="item.value2 != null ">
                                        and SUM(CASE
                                        WHEN oc.local_refund_process_state != 5 THEN odc.sub_qty
                                        ELSE 0
                                        END) <![CDATA[<=]]> #{item.value2}
                                    </if>
                                </trim>
                            </when>
                        </choose>
                    </if>
                </foreach>
            </trim>
        </if>
    </sql>
    <sql id="selectShopBodyNew">
        <include refid="searchShopType"></include>
        left JOIN base_otype o ON oc.otype_id = o.id and o.profile_id=oc.profile_id
        left JOIN pl_eshop e ON e.otype_id = o.id and e.`profile_id` = o.`profile_id`
        left join base_btype b on b.profile_id=oc.profile_id and b.id=oc.btype_id
        left join pl_eshop_sale_order_timing oct ON oc.id = oct.eshop_order_id AND oc.profile_id = oct.profile_id
    </sql>
    <sql id="selectShopBody">
        <include refid="searchShopType"></include>
        LEFT JOIN pl_eshop_sale_order_detail odc ON oc.id = odc.eshop_order_id AND odc.profile_id = oc.profile_id
        left JOIN base_otype o ON oc.otype_id = o.id and o.profile_id=oc.profile_id
        left JOIN pl_eshop e ON e.otype_id = o.id and e.`profile_id` = o.`profile_id`
        left join base_btype b on b.profile_id=oc.profile_id and b.id=oc.btype_id
        left join pl_eshop_sale_order_timing oct ON oc.id = oct.eshop_order_id AND oc.profile_id = oct.profile_id
    </sql>
    <sql id="selectShopColumn">
        o.id as otypeId, count(distinct oc.trade_order_id) as sale_count,
        sum(IFNULL(oc.order_buyer_freight_fee,0)) as order_buyer_freight_fee,
        sum(IFNULL(oc.tax_total,0)) as buyer_tax_total,sum(ifnull(odc.ptype_service_fee,0)) as buyer_ptype_service_fee,
        SUM(CASE WHEN oc.local_refund_process_state != 5 THEN <include refid="odcShopTotal"></include> ELSE 0 END) AS
        buyer_dised_taxed_total,
        SUM(CASE WHEN oc.local_refund_process_state != 5 THEN
        <include refid="odcShopTotal"></include>
        ELSE 0 END) / count(DISTINCT buyer_id) AS buyer_dised_taxed_price,b.usercode as btypeCode,b.fullname as
        btypeName,b.id as btypeId
    </sql>
    <sql id="selectShopColumnNew">
        o.id as otypeId, count(distinct oc.trade_order_id) as sale_count,
        sum(IFNULL(oc.order_buyer_freight_fee,0)) as order_buyer_freight_fee,
        sum(IFNULL(oc.tax_total,0)) as buyer_tax_total,sum(ifnull(oc.ptype_service_fee,0)) as buyer_ptype_service_fee,
        SUM(CASE WHEN oc.local_refund_process_state != 5 THEN <include refid="ocShopTotalNew"></include> ELSE 0 END) AS
        buyer_dised_taxed_total,
        SUM(CASE WHEN oc.local_refund_process_state != 5 THEN
        <include refid="ocShopTotalNew"></include>
        ELSE 0 END) / count(DISTINCT buyer_id) AS buyer_dised_taxed_price,b.usercode as btypeCode,b.fullname as
        btypeName,b.id as btypeId
    </sql>
    <select id="listEshopSaleOrderShopStatistics"
            resultType="com.wsgjp.ct.sale.biz.analysiscloud.entity.EshopSaleOrderStatisticsEntity">
        SELECT o.id as otypeId,o.fullname as eshop_name,o.ocategory,e.eshop_sale_platform,oc.business_type,
        <include refid="selectShopColumn"></include>
        FROM
        <include refid="selectShopBody"></include>
        <where>
            <include refid="selectWhere"></include>
            and oc.local_refund_process_state!= 5 and oc.deleted = 0
            AND (`oc`.order_sale_type  not in(3,6) OR (`oc`.order_sale_type=3 AND `oc`.platform_parent_order_id=''))
            and o.ocategory!=10
            <include refid="com.wsgjp.ct.sale.template.limit-where-etype">
                <property name="limit-alias-id" value="oc.etype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-ktype">
                <property name="limit-alias-id" value="oc.ktype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-otype">
                <property name="limit-alias-id" value="oc.otype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-btype">
                <property name="limit-alias-id" value="oc.btype_id"/>
            </include>
        </where>
        <if test="query.searchType==1">
            GROUP by o.id
        </if>
        <if test="query.searchType==2">
            GROUP by b.id
        </if>
        <include refid="havingShopSum"></include>
    </select>

    <select id="listEshopSaleOrderShopStatistics_COUNT" resultType="java.lang.Integer">
        select count(0)
        from (
        <include refid="listShopCountTemp"></include>
        ) table_count
    </select>
    <sql id="listShopCountTemp">
        SELECT oc.id,oc.order_buyer_freight_fee
        FROM
        <include refid="selectShopBody"></include>
        <where>
            <include refid="selectWhere"></include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-etype">
                <property name="limit-alias-id" value="oc.etype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-ktype">
                <property name="limit-alias-id" value="oc.ktype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-otype">
                <property name="limit-alias-id" value="oc.otype_id"/>
            </include>
        </where>
        <if test="query.searchType==1">
            GROUP by o.id
        </if>
        <if test="query.searchType==2">
            GROUP by b.id
        </if>
        <include refid="havingShopSum"></include>

    </sql>
    <select id="listEshopSaleOrderShopStatisticsSummary"
            resultType="com.wsgjp.ct.sale.biz.analysiscloud.entity.EshopSaleOrderStatisticsEntity">
        select a.otypeId,sum(sale_count) as sale_count,sum(order_buyer_freight_fee) as order_buyer_freight_fee,
        sum(buyer_tax_total) as buyer_tax_total,sum(buyer_ptype_service_fee) as
        buyer_ptype_service_fee,SUM(buyer_dised_taxed_total) buyer_dised_taxed_total
        from(
        SELECT
        <include refid="selectShopColumn"></include>
        FROM
        <include refid="selectShopBody"></include>
        <where>
            <include refid="selectWhere"></include>
            AND (`oc`.order_sale_type  not in(3,6) OR (`oc`.order_sale_type=3 AND `oc`.platform_parent_order_id=''))
            <include refid="com.wsgjp.ct.sale.template.limit-where-etype">
                <property name="limit-alias-id" value="oc.etype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-ktype">
                <property name="limit-alias-id" value="oc.ktype_id"/>
            </include>

            <include refid="com.wsgjp.ct.sale.template.limit-where-otype">
                <property name="limit-alias-id" value="oc.otype_id"/>
            </include>
        </where>
        GROUP by o.id
        <include refid="havingShopSum"></include>
        )a
    </select>
    <select id="listEshopSaleOrderShopStatisticsSummaryNew"
            resultType="com.wsgjp.ct.sale.biz.analysiscloud.entity.EshopSaleOrderStatisticsEntity">
        select a.otypeId,sum(sale_count) as sale_count,sum(order_buyer_freight_fee) as order_buyer_freight_fee,
        sum(buyer_tax_total) as buyer_tax_total,sum(buyer_ptype_service_fee) as
        buyer_ptype_service_fee,SUM(buyer_dised_taxed_total) as buyer_dised_taxed_total
        from(
        SELECT
        <include refid="selectShopColumnNew"></include>
        FROM
        <include refid="selectShopBodyNew"></include>
        <where>
            <include refid="selectWhere"></include>
            AND (`oc`.order_sale_type  not in(3,6) OR (`oc`.order_sale_type=3 AND `oc`.platform_parent_order_id=''))
            <include refid="com.wsgjp.ct.sale.template.limit-where-etype">
                <property name="limit-alias-id" value="oc.etype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-ktype">
                <property name="limit-alias-id" value="oc.ktype_id"/>
            </include>

            <include refid="com.wsgjp.ct.sale.template.limit-where-otype">
                <property name="limit-alias-id" value="oc.otype_id"/>
            </include>
            and o.ocategory!=10
        </where>
        GROUP by o.id
        <include refid="havingShopSum"></include>
        )a
    </select>
    <sql id="havingShopSum">
        <if test="query.filter != null">
            <trim prefix="having (" suffix=")">
                <foreach collection="query.filter" item="item" separator="and">
                    <if test="item.type == 3">
                        <choose>
                            <when test="item.dataField == 'sale_count'">
                                <trim prefixOverrides="and" prefix="(" suffix=")">
                                    <if test="item.value1 != null ">
                                        count(1) <![CDATA[>=]]> #{item.value1}
                                    </if>
                                    <if test="item.value2 != null ">
                                        and count(1) <![CDATA[<=]]> #{item.value2}
                                    </if>
                                </trim>
                            </when>

                            <when test="item.dataField == 'buyer_dised_taxed_total'">
                                <trim prefixOverrides="and" prefix="(" suffix=")">
                                    <if test="item.value1 != null ">
                                        SUM(CASE WHEN oc.local_refund_process_state != 5 THEN <include
                                            refid="odcShopTotal"></include> ELSE 0 END) <![CDATA[>=]]> #{item.value1}
                                    </if>
                                    <if test="item.value2 != null ">
                                        and SUM(CASE WHEN oc.local_refund_process_state != 5 THEN <include
                                            refid="odcShopTotal"></include> ELSE 0 END) <![CDATA[<=]]> #{item.value2}
                                    </if>
                                </trim>
                            </when>
                            <when test="item.dataField == 'buyer_dised_taxed_price'">
                                <trim prefixOverrides="and" prefix="(" suffix=")">
                                    <if test="item.value1 != null ">
                                        SUM(CASE WHEN oc.local_refund_process_state != 5 THEN
                                        <include refid="odcShopTotal"></include>
                                        ELSE 0 END) / count(DISTINCT buyer_id)  <![CDATA[>=]]> #{item.value1}
                                    </if>
                                    <if test="item.value2 != null ">
                                        and SUM(CASE WHEN oc.local_refund_process_state != 5 THEN
                                        <include refid="odcShopTotal"></include>
                                        ELSE 0 END) / count(DISTINCT buyer_id)  <![CDATA[<=]]> #{item.value2}
                                    </if>
                                </trim>
                            </when>
                            <when test="item.dataField == 'order_buyer_freight_fee'">
                                <trim prefixOverrides="and" prefix="(" suffix=")">
                                    <if test="item.value1 != null ">
                                        and order_buyer_freight_fee <![CDATA[>=]]> #{item.value1}
                                    </if>
                                    <if test="item.value2 != null ">
                                        and order_buyer_freight_fee <![CDATA[<=]]> #{item.value2}
                                    </if>
                                </trim>
                            </when>
                            <!--                            <when test="item.dataField == 'mall_fee'">-->
                            <!--                                <trim prefixOverrides="and" prefix="(" suffix=")">-->
                            <!--                                    <if test="item.value1 != null ">-->
                            <!--                                        sum(oc.mall_fee) <![CDATA[>=]]> #{item.value1}-->
                            <!--                                    </if>-->
                            <!--                                    <if test="item.value2 != null ">-->
                            <!--                                        and sum(oc.mall_fee) <![CDATA[<=]]> #{item.value2}-->
                            <!--                                    </if>-->
                            <!--                                </trim>-->
                            <!--                            </when>-->
                            <when test="item.dataField == 'buyer_ptype_service_fee'">
                                <trim prefixOverrides="and" prefix="(" suffix=")">
                                    <if test="item.value1 != null ">
                                        sum(oc.ptype_service_fee) <![CDATA[>=]]> #{item.value1}
                                    </if>
                                    <if test="item.value2 != null ">
                                        and sum(oc.ptype_service_fee) <![CDATA[<=]]> #{item.value2}
                                    </if>
                                </trim>
                            </when>
                            <when test="item.dataField == 'tax_total'">
                                <trim prefixOverrides="and" prefix="(" suffix=")">
                                    <if test="item.value1 != null ">
                                        sum(oc.tax_total) <![CDATA[>=]]> #{item.value1}
                                    </if>
                                    <if test="item.value2 != null ">
                                        and sum(oc.tax_total) <![CDATA[<=]]> #{item.value2}
                                    </if>
                                </trim>
                            </when>
                            <when test="item.dataField == 'business_type'">
                                <trim prefixOverrides="and" prefix="(" suffix=")">
                                    <if test="item.value1 != null ">
                                        oc.business_type = #{item.value1}
                                    </if>
                                </trim>
                            </when>
                        </choose>
                    </if>
                </foreach>
            </trim>
        </if>
    </sql>

    <select id="listEshopSaleOrderShopDetailStatistics"
            resultType="com.wsgjp.ct.sale.biz.analysiscloud.entity.EshopSaleOrderDetailStatisticsEntity">
        SELECT CASE WHEN sku.pic_url= '' THEN pic.pic_url ELSE sku.pic_url END AS pic_url,p.id as
        ptype_id,p.`usercode`,p.fullname as p_fullname,p.`ptype_type`,
        p.`standard`,brand.`brand_name`,unit.`unit_name`,p.memo as ptype_memo,bpf.fullbarcode,
        sku.id as sku_id,px.xcode,
        CONCAT_WS(':',sku.prop_name1,sku.prop_name2,sku.prop_name3,sku.prop_name4,sku.prop_name5,sku.prop_name6) AS
        prop_names,
        CONCAT_WS(':',sku.propvalue_name1,sku.propvalue_name2,sku.propvalue_name3,sku.propvalue_name4,sku.propvalue_name5,sku.propvalue_name6)
        AS prop_values,
        <include refid="selectDetailColumn"></include>
        FROM
        <include refid="selectDetailBody"></include>
        <where>
            <include refid="selectWhere"></include>
            AND (`oc`.order_sale_type  not in(3,6) OR (`oc`.order_sale_type=3 AND `oc`.platform_parent_order_id=''))
            <include refid="com.wsgjp.ct.sale.template.limit-where-etype">
                <property name="limit-alias-id" value="oc.etype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-ktype">
                <property name="limit-alias-id" value="odc.ktype_id"/>
            </include>

            <include refid="com.wsgjp.ct.sale.template.limit-where-otype">
                <property name="limit-alias-id" value="odc.otype_id"/>
            </include>
        </where>
        <include refid="havingShopDetailSum"></include>
        order by oc.create_time
    </select>
    <sql id="havingShopDetailSum">
        <if test="query.filter != null">
            <trim prefix="and (" suffix=")">
                <foreach collection="query.filter" item="item" separator="and">
                    <if test="item.type == 3">
                        <choose>
                            <when test="item.dataField == 'sale_total' or item.dataField == 'buyer_dised_taxed_total'">
                                <trim prefixOverrides="and" prefix="(" suffix=")">
                                    <if test="item.value1 != null ">
                                        CASE WHEN oc.local_refund_process_state != 5 THEN <include
                                            refid="odcTotal"></include> ELSE
                                        0 END <![CDATA[>=]]> #{item.value1}
                                    </if>
                                    <if test="item.value2 != null ">
                                        and CASE WHEN oc.local_refund_process_state != 5 THEN <include
                                            refid="odcTotal"></include> ELSE 0 END <![CDATA[<=]]> #{item.value2}
                                    </if>
                                </trim>
                            </when>
                            <when test="item.dataField == 'buyer_dised_taxed_price'">
                                <trim prefixOverrides="and" prefix="(" suffix=")">
                                    <if test="item.value1 != null ">
                                        (CASE WHEN oc.local_refund_process_state != 5 THEN <include
                                            refid="odcTotal"></include> ELSE 0 END)/ (CASE WHEN
                                        oc.local_refund_process_state
                                        != 5 THEN odc.unit_qty ELSE 0 END)  <![CDATA[>=]]> #{item.value1}
                                    </if>
                                    <if test="item.value2 != null ">
                                        and (CASE WHEN oc.local_refund_process_state != 5 THEN <include
                                            refid="odcTotal"></include> ELSE 0 END)/ (CASE WHEN
                                        oc.local_refund_process_state
                                        != 5 THEN odc.unit_qty ELSE 0 END)  <![CDATA[<=]]> #{item.value2}
                                    </if>
                                </trim>
                            </when>
                        </choose>
                    </if>
                </foreach>
            </trim>
        </if>
    </sql>
    <select id="listEshopSaleOrderShopDetailStatistics_COUNT" resultType="java.lang.Integer">
        select count(0)
        from (
        <include refid="listDetailCountTemp"></include>
        ) table_count
    </select>
    <sql id="listDetailCountTemp">
        SELECT oc.id,oc.order_buyer_freight_fee
        FROM
        <include refid="selectDetailBody"></include>
        <where>
            <include refid="selectWhere"></include>
            and odc.process_state !=5
            <include refid="com.wsgjp.ct.sale.template.limit-where-etype">
                <property name="limit-alias-id" value="oc.etype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-ktype">
                <property name="limit-alias-id" value="odc.ktype_id"/>
            </include>

            <include refid="com.wsgjp.ct.sale.template.limit-where-otype">
                <property name="limit-alias-id" value="odc.otype_id"/>
            </include>
        </where>
        <include refid="havingShopDetailSum"></include>
    </sql>

    <select id="listEshopSaleOrderShopDetailStatisticsSummary"
            resultType="com.wsgjp.ct.sale.biz.analysiscloud.entity.EshopSaleOrderDetailStatisticsEntity">
        select SUM(sale_qty) AS sale_qty,SUM(buyer_dised_taxed_total)
        buyer_dised_taxed_total,SUM(order_buyer_freight_fee) order_buyer_freight_fee
        from(
        SELECT
        <include refid="selectDetailColumn"></include>
        FROM
        <include refid="selectDetailBody"></include>
        <where>
            <include refid="selectWhere"></include>
            AND oc.local_refund_process_state=0
            AND (`oc`.order_sale_type  not in(3,6) OR (`oc`.order_sale_type=3 AND `oc`.platform_parent_order_id=''))
            <include refid="com.wsgjp.ct.sale.template.limit-where-etype">
                <property name="limit-alias-id" value="oc.etype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-ktype">
                <property name="limit-alias-id" value="odc.ktype_id"/>
            </include>

            <include refid="com.wsgjp.ct.sale.template.limit-where-otype">
                <property name="limit-alias-id" value="odc.otype_id"/>
            </include>
        </where>
        <include refid="havingShopDetailSum"></include>
        )a
    </select>
    <sql id="selectDetailColumn">
        oc.trade_order_id as bill_number,
        oc.order_buyer_freight_fee as order_buyer_freight_fee,
        CASE WHEN oc.local_refund_process_state != 5 THEN odc.unit_qty ELSE 0 END AS sale_qty,
        CASE WHEN oc.local_refund_process_state != 5 THEN <include refid="odcTotal"></include> ELSE 0 END AS
        buyer_dised_taxed_total,
        (CASE WHEN oc.local_refund_process_state != 5 THEN <include refid="odcTotal"></include> ELSE 0 END)/ (CASE WHEN
        oc.local_refund_process_state != 5 THEN odc.unit_qty ELSE 0 END) AS buyer_dised_taxed_price
    </sql>
    <sql id="selectDetailBody">
        <include refid="searchType"></include>
        left JOIN base_ptype p ON odc.ptype_id = p.id and p.profile_id=odc.profile_id
        LEFT JOIN base_ptype_pic pic ON p.id = pic.ptype_id and pic.`profile_id` = p.`profile_id` AND pic.rowindex = 1
        LEFT JOIN `base_brandtype` brand ON p.`brand_id` = brand.`id` AND brand.`profile_id` = p.`profile_id`
        LEFT JOIN `base_ptype_unit` unit ON p.`id` = unit.`ptype_id` AND unit.`profile_id` = p.`profile_id` AND
        unit.`id` = odc.unit
        Left join base_ptype_fullbarcode bpf on bpf.profile_id = oc.profile_id and bpf.ptype_id=p.id and
        bpf.sku_id=odc.sku_id and bpf.defaulted=1 AND unit.id=bpf.unit_id
        LEFT JOIN base_ptype_sku sku ON odc.`sku_id` = sku.`id` AND sku.`profile_id` = odc.profile_id
        left join base_ptype_xcode px on px.ptype_id=p.id and px.sku_id=sku.id and px.unit_id=unit.id and px.defaulted=1
        and px.profile_id=p.profile_id
        left join base_btype b on b.profile_id=oc.profile_id and b.id=oc.btype_id
    </sql>

    <select id="getOrderBuyerFreightFee"
            resultType="com.wsgjp.ct.sale.biz.analysiscloud.entity.EshopSaleOrderStatisticsEntity">
        SELECT IFNULL(sum(oc.order_buyer_freight_fee), 0) as order_buyer_freight_fee,IFNULL(sum(oc.tax_total), 0) as
        buyer_tax_total
        from pl_eshop_sale_order oc
        left JOIN base_otype o ON oc.otype_id = o.id and o.profile_id=oc.profile_id
        left JOIN pl_eshop e ON e.otype_id = o.id and e.`profile_id` = o.`profile_id`
        left join base_btype b on b.profile_id=oc.profile_id and b.id=oc.btype_id
        <include refid="com.wsgjp.ct.sale.template.limit-etype">
            <property name="limit-alias-id" value="oc.etype_id"/>
            <property name="limit-alias-profileid" value="oc.profile_id"/>
        </include>
        <include refid="com.wsgjp.ct.sale.template.limit-ktype">
            <property name="limit-alias-id" value="oc.ktype_id"/>
            <property name="limit-alias-profileid" value="oc.profile_id"/>
        </include>

        <include refid="com.wsgjp.ct.sale.template.limit-otype">
            <property name="limit-alias-id" value="oc.otype_id"/>
            <property name="limit-alias-profileid" value="oc.profile_id"/>
        </include>
        <where>
            oc.profile_id = #{profileId} and oc.deleted=0 and oc.local_trade_state!=5 and oc.mapping_state=1 and
            ((oc.local_refund_state=0 or oc.local_refund_state=2 or (oc.local_refund_state=3 and oc.local_trade_state in
            (3,4))) or (oc.local_refund_state=4 and oc.local_trade_state in (3,4)))
            and
            <include refid="searchTimeType"></include>
            between #{query.startDate} and #{query.endDate}
            <if test="query.otypeId > 0">
                and oc.otype_id = #{query.otypeId}
            </if>
            <if test="query.btypeId > 0">
                and b.id = #{query.btypeId}
            </if>
            <if test="query.billBusinessTypes != null and query.billBusinessTypes.size() > 0">
                and oc.business_type in
                <foreach collection="query.billBusinessTypes" index="index" item="item" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <include refid="com.wsgjp.ct.sale.template.query-filter-or-refund">
                <property name="_list" value="query.filter"/>
            </include>
            <if test="query.orderCreateType == 1">
                and oc.create_type =0
            </if>
            <if test="query.orderCreateType == 2">
                and oc.create_type in (3,16)
            </if>
            <if test="query.orderCreateType == 3">
                and oc.create_type =1
            </if>
            and oc.local_refund_process_state!= 5
            <include refid="com.wsgjp.ct.sale.template.limit-where-etype">
                <property name="limit-alias-id" value="oc.etype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-ktype">
                <property name="limit-alias-id" value="oc.ktype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-otype">
                <property name="limit-alias-id" value="oc.otype_id"/>
            </include>
        </where>
        <if test="query.searchType==1">
            GROUP by o.id
        </if>
        <if test="query.searchType==2">
            GROUP by b.id
        </if>
    </select>
    <select id="getOrderBuyerFreightFeeAll"
            resultType="com.wsgjp.ct.sale.biz.analysiscloud.entity.EshopSaleOrderStatisticsEntity">
        SELECT sum(b.order_buyer_freight_fee) as order_buyer_freight_fee,IFNULL(sum(b.buyer_tax_total), 0) as
        buyer_tax_total FROM (
        SELECT IFNULL(sum(oc.order_buyer_freight_fee), 0) as order_buyer_freight_fee,IFNULL(sum(oc.tax_total), 0) as
        buyer_tax_total
        from pl_eshop_sale_order oc
        left JOIN base_otype o ON oc.otype_id = o.id and o.profile_id=oc.profile_id
        left JOIN pl_eshop e ON e.otype_id = o.id and e.`profile_id` = o.`profile_id`
        left join base_btype b on b.profile_id=oc.profile_id and b.id=oc.btype_id
        <include refid="com.wsgjp.ct.sale.template.limit-etype">
            <property name="limit-alias-id" value="oc.etype_id"/>
            <property name="limit-alias-profileid" value="oc.profile_id"/>
        </include>
        <include refid="com.wsgjp.ct.sale.template.limit-ktype">
            <property name="limit-alias-id" value="oc.ktype_id"/>
            <property name="limit-alias-profileid" value="oc.profile_id"/>
        </include>

        <include refid="com.wsgjp.ct.sale.template.limit-otype">
            <property name="limit-alias-id" value="oc.otype_id"/>
            <property name="limit-alias-profileid" value="oc.profile_id"/>
        </include>
        <where>
            oc.profile_id = #{profileId} and oc.deleted=0 and oc.local_trade_state!=5 and oc.mapping_state=1 and
            ((oc.local_refund_state=0 or oc.local_refund_state=2 or (oc.local_refund_state=3 and oc.local_trade_state in
            (3,4))) or (oc.local_refund_state=4 and oc.local_trade_state in (3,4)))
            and
            <include refid="searchTimeType"></include>
            between #{query.startDate} and #{query.endDate}
            <if test="query.otypeId > 0">
                and oc.otype_id = #{query.otypeId}
            </if>
            <if test="query.otypeIds !=null and query.otypeIds.size()>0">
                and oc.otype_id in
                <foreach collection="query.otypeIds" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.btypeId > 0">
                and b.id = #{query.btypeId}
            </if>
            <if test="query.billBusinessTypes != null and query.billBusinessTypes.size() > 0">
                and oc.business_type in
                <foreach collection="query.billBusinessTypes" index="index" item="item" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </if>
            <include refid="com.wsgjp.ct.sale.template.query-filter-or-refund">
                <property name="_list" value="query.filter"/>
            </include>
            <if test="query.orderCreateType == 1">
                and oc.create_type =0
            </if>
            <if test="query.orderCreateType == 2">
                and oc.create_type in (3,16)
            </if>
            <if test="query.orderCreateType == 3">
                and oc.create_type =1
            </if>
            and oc.local_refund_process_state!= 5
            <include refid="com.wsgjp.ct.sale.template.limit-where-etype">
                <property name="limit-alias-id" value="oc.etype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-ktype">
                <property name="limit-alias-id" value="oc.ktype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-otype">
                <property name="limit-alias-id" value="oc.otype_id"/>
            </include>
        </where>
        <if test="query.searchType==1">
            GROUP by o.id
        </if>
        <if test="query.searchType==2">
            GROUP by b.id
        </if>
        ) b
    </select>
    <select id="listEshopSaleBtypeStatisticsSummary"
            resultType="com.wsgjp.ct.sale.biz.analysiscloud.entity.EshopSaleOrderStatisticsEntity">
        select SUM(buyer_dised_taxed_total) buyer_dised_taxed_total,sum(sale_count) as sale_count
        ,sum(order_buyer_freight_fee) as order_buyer_freight_fee
        ,sum(buyer_tax_total) AS buyer_tax_total
        ,sum(buyer_ptype_service_fee) as buyer_ptype_service_fee,concat(btypeId) as btypeIds
        from(
        SELECT o.fullname as eshop_name,o.ocategory,e.eshop_sale_platform,oc.business_type,
        <include refid="selectShopColumn"></include>
        FROM
        <include refid="selectShopBody"></include>
        <where>
            <include refid="selectWhere"></include>
            and oc.local_refund_process_state!= 5
            AND (`oc`.order_sale_type  not in(3,6) OR (`oc`.order_sale_type=3 AND `oc`.platform_parent_order_id=''))
            <include refid="com.wsgjp.ct.sale.template.limit-where-etype">
                <property name="limit-alias-id" value="oc.etype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-ktype">
                <property name="limit-alias-id" value="oc.ktype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-otype">
                <property name="limit-alias-id" value="oc.otype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-btype">
                <property name="limit-alias-id" value="oc.btype_id"/>
            </include>
        </where>
        <if test="query.searchType==1">
            GROUP by o.id
        </if>
        <if test="query.searchType==2">
            GROUP by b.id
        </if>
        <include refid="havingShopSum"></include>) a
    </select>
    <select id="listEshopSaleOrderShopStatisticsNew"
            resultType="com.wsgjp.ct.sale.biz.analysiscloud.entity.EshopSaleOrderStatisticsEntity">
        SELECT o.fullname as eshop_name,o.ocategory,e.eshop_sale_platform,oc.business_type,
        <include refid="selectShopColumnNew"></include>
        FROM
        <include refid="selectShopBodyNew"></include>
        <where>
            <include refid="selectWhere"></include>
            and oc.local_refund_process_state!= 5 and oc.deleted = 0
            AND (`oc`.order_sale_type  not in(3,6) OR (`oc`.order_sale_type=3 AND `oc`.platform_parent_order_id=''))
            <include refid="com.wsgjp.ct.sale.template.limit-where-etype">
                <property name="limit-alias-id" value="oc.etype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-ktype">
                <property name="limit-alias-id" value="oc.ktype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-otype">
                <property name="limit-alias-id" value="oc.otype_id"/>
            </include>
            <include refid="com.wsgjp.ct.sale.template.limit-where-btype">
                <property name="limit-alias-id" value="oc.btype_id"/>
            </include>
            and o.ocategory!=10
        </where>
        <if test="query.searchType==1">
            GROUP by o.id
        </if>
        <if test="query.searchType==2">
            GROUP by b.id
        </if>
        <include refid="havingShopSum"></include>
    </select>

</mapper>
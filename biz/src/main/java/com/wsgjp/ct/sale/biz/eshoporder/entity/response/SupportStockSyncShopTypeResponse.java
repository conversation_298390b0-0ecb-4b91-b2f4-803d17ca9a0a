package com.wsgjp.ct.sale.biz.eshoporder.entity.response;

import com.wsgjp.ct.common.enums.core.enums.ShopType;

import java.util.List;

public class SupportStockSyncShopTypeResponse {
    /**
     * 支持库存同步的店铺类型
     */
    private List<ShopType> generalStockSyncShopTypes;

    /**
     * 支持分仓同步的店铺类型
     */
    private List<ShopType> warehouseStockSyncShopTypes;

    public List<ShopType> getGeneralStockSyncShopTypes() {
        return generalStockSyncShopTypes;
    }

    public void setGeneralStockSyncShopTypes(List<ShopType> generalStockSyncShopTypes) {
        this.generalStockSyncShopTypes = generalStockSyncShopTypes;
    }

    public List<ShopType> getWarehouseStockSyncShopTypes() {
        return warehouseStockSyncShopTypes;
    }

    public void setWarehouseStockSyncShopTypes(List<ShopType> warehouseStockSyncShopTypes) {
        this.warehouseStockSyncShopTypes = warehouseStockSyncShopTypes;
    }
}

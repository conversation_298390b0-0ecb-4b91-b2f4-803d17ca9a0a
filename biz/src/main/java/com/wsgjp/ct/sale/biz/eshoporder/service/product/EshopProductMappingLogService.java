package com.wsgjp.ct.sale.biz.eshoporder.service.product;


import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.sale.biz.common.util.CurrentEtypeUtil;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.Pcategory;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.ProductOperateLogType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopConfig;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMappingLog;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductRelationEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuPageData;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.EshopProductLogQueryParams;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QuerySkuMappingRequest;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopBizMapper;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopProductMappingLogMapper;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.log.service.LogService;
import com.wsgjp.ct.support.thread.ThreadPool;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import ngp.utils.DateUtils;
import ngp.utils.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-02-04 13:50
 */
@Service
public class EshopProductMappingLogService {

    private final EshopProductMappingLogMapper productLogMapper;
    private final EshopBizMapper eshopBizMapper;
    private final EshopProductDataService productDataService;
    private static final Logger logger = LoggerFactory.getLogger(EshopProductMappingLogService.class);

    public EshopProductMappingLogService(EshopProductMappingLogMapper productLogMapper, EshopBizMapper eshopBizMapper, EshopProductDataService productDataService) {

        this.productLogMapper = productLogMapper;
        this.eshopBizMapper = eshopBizMapper;
        this.productDataService = productDataService;
    }

    public PageResponse<EshopProductMappingLog> queryProductLogList(PageRequest<EshopProductLogQueryParams> params) {
        return LogService.query(params);
    }

    public void insertLogWhenManualHandle(BigInteger eshopId, String platfromNumId, String platformSkuId, ProductOperateLogType operateType, String description, String fullname) {
        if (StringUtils.isNotEmpty(platfromNumId)) {
            List<EshopProductSkuMapping> skuMappings = productLogMapper.getProductSkuMappingList(CurrentUser.getProfileId(), eshopId, platfromNumId, platformSkuId);
            EshopConfig eshopConfigById = eshopBizMapper.getEshopConfigById(CurrentUser.getProfileId(), eshopId);
            if (skuMappings != null && skuMappings.size() > 0 && eshopConfigById != null) {
                for (EshopProductSkuMapping skuMapping : skuMappings) {
                    QuerySkuMappingRequest req = new QuerySkuMappingRequest();
                    req.setNumId(skuMapping.getPlatformNumId());
                    req.setOtypeId(eshopId);
                    req.setXcode(skuMapping.getPlatformXcode());
                    req.setPlatformSkuId(skuMapping.getPlatformSkuId());
                    req.setOpenXcode(null == skuMapping.getMappingType() ? eshopConfigById.getMappingType().getCode() == 1 : skuMapping.getMappingType().getCode() == 1);
                    EshopProductSkuMapping relationSkuMapping = productDataService.getSingleSkuMappingForRefreshProduct(req);
                    if (null == relationSkuMapping) {
                        continue;
                    }
                    if (operateType == ProductOperateLogType.ClearMapping) {
                        insertLogWhenManualClear(relationSkuMapping, operateType, description);
                    } else {
                        EshopProductMappingLog logEntity = buildEshopMappingLogEntity(relationSkuMapping);
                        logEntity.setFullname(StringUtils.isEmpty(fullname) ? logEntity.getFullname() : fullname);
                        logEntity.setOpreateType(operateType);
                        logEntity.setDescription(description);
                        doInsertLog(logEntity);
                    }

                }
            }
        }
    }

    public void insertLogWhenDownloadPtype(BigInteger eshopId, List<EshopProductSkuMapping> skuList, ProductOperateLogType operateType, String description, String fullname) {
        if (CollectionUtils.isEmpty(skuList)) {
            return;
        }
        Set<String> platformNumIds = skuList.stream().map(EshopProductSkuMapping::getPlatformNumId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Set<String> platformSkuIds = skuList.stream().map(EshopProductSkuMapping::getPlatformSkuId).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(platformNumIds) && CollectionUtils.isEmpty(platformSkuIds)) {
            return;
        }
        QuerySkuMappingRequest request = new QuerySkuMappingRequest();
        request.setPlatformNumIdList(new ArrayList<>(platformNumIds));
        request.setPlatformSkuIdList(new ArrayList<>(platformSkuIds));
        request.setProfileId(CurrentUser.getProfileId());
        request.setOtypeId(eshopId);

        //查询对应关系
        List<EshopProductSkuMapping> skuMappings = productDataService.loadEshopSkuAndComboMappingList(request);
        if (CollectionUtils.isEmpty(skuMappings)) {
            return;
        }
        if (operateType == ProductOperateLogType.ClearMapping) {
            List<EshopProductSkuMapping> relationList = skuMappings.stream().filter(EshopProductSkuMapping::isBind).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(relationList)) {
                relationList.forEach(x -> {
                    insertLogWhenManualClear(x, operateType, description);
                });
            }
            return;
        }
        skuMappings.forEach(eshopProductSkuMapping -> {
            EshopProductMappingLog logEntity = buildEshopMappingLogEntity(eshopProductSkuMapping);
            logEntity.setFullname(StringUtils.isEmpty(fullname) ? logEntity.getFullname() : fullname);
            logEntity.setOpreateType(operateType);
            logEntity.setDescription(description);
            doInsertLog(logEntity);
        });
    }

    public EshopProductSkuMapping getRelationEntityForLogManual(BigInteger eshopId, String platfromNumId, String platformSkuId) {
        QuerySkuMappingRequest req = new QuerySkuMappingRequest();
        req.setOpenXcode(false);
        req.setNumId(platfromNumId);
        req.setOtypeId(eshopId);
        req.setPlatformSkuId(platformSkuId);
        return productDataService.getSingleSkuMapping(req);
    }


    public void insertLogWhenStopPtype(EshopProductSkuMapping relation, ProductOperateLogType operateType, String description) {
        relation.setBind(false);
        EshopProductMappingLog logEntity = buildEshopMappingLogEntity(relation);
        logEntity.setOpreateType(operateType);
        logEntity.setDescription(description);
        doInsertLog(logEntity);
    }


    public void insertLogWhenDeleteProductSku(List<EshopProductSkuMapping> eshopProductSkuMapping, ProductOperateLogType operateType) {
        if (CollectionUtils.isNotEmpty(eshopProductSkuMapping)) {
            for (EshopProductSkuMapping productSkuMapping : eshopProductSkuMapping) {
                insertLog(productSkuMapping, operateType, operateType.getName());
            }
        }
    }


    public void insertLog(EshopProductSkuMapping newEntity, ProductOperateLogType operateType, String logStr) {
        EshopProductMappingLog log = buildEshopMappingLogEntity(newEntity);
        log.setDescription(logStr);
        log.setOpreateType(operateType);
        doInsertLog(log);
    }

    public void insertLogBatch(List<EshopProductMappingLog> logEntity) {
        ThreadPool test = ThreadPoolFactory.build("refresh-ptype");
        test.executeAsync(invoker -> {
            try {
                doInsertLog(logEntity);
            } catch (Exception ex) {
                logger.error("批量插入日志失败：" + ex.getMessage());
            }
        }, "批量插入日志");
    }


    public void insertLogWhenManualClear(EshopProductSkuMapping newEntity, ProductOperateLogType operateType, String desc) {
        newEntity.setSkuId(BigInteger.ZERO);
        newEntity.setUnitId(BigInteger.ZERO);
        newEntity.setPtypeName("");
        newEntity.setXcode("");
        newEntity.setBind(false);
        EshopProductMappingLog log = buildEshopMappingLogEntity(newEntity);
        log.setOpreateType(operateType);
        log.setDescription(desc);
        doInsertLog(log);
    }


    public void doInsertLog(EshopProductMappingLog log) {
        try {
            LogService.add(log);
        } catch (Exception ex) {
            String msg = ex.getMessage();
            logger.error("账套id：{}，插入对应关系变更日志出错：{}", CurrentUser.getProfileId(), msg, ex);
        }
    }

    private void doInsertLog(List<EshopProductMappingLog> logs) {
        if (logs == null || logs.size() == 0) {
            return;
        }
        for (EshopProductMappingLog log : logs) {
            doInsertLog(log);
        }
    }

    public EshopProductMappingLog buildEshopMappingLogEntity(EshopProductRelationEntity newEntity) {
        EshopProductMappingLog log = new EshopProductMappingLog();
        log.setXcode(newEntity.getXcode() == null ? "" : newEntity.getXcode());
        log.setFullname(newEntity.getPlatName());
        log.setEshopId(newEntity.getEshopId());
        log.setId(UId.newId());
        log.setEtypeId(CurrentUser.getEmployeeId());
        log.setOpreateTime(new Date());
        log.setPlatformFullPropertiesName(newEntity.getPlatformPropertiesName());
        log.setPlatformNumId(newEntity.getPlatformNumId());
        log.setPlatformSkuId(newEntity.getPlatformSkuId());
        log.setProfileId(CurrentUser.getProfileId());
        log.setPtypeFullname(newEntity.getPtypeName());
        log.setSkuId(newEntity.getSkuId());
        log.setUnitId(newEntity.getUnitId());
        log.setOldPcategory(newEntity.isBind() ? Pcategory.Ptype : Pcategory.UnRelation);
        log.setPlatformXcode(newEntity.getPlatXcode() == null ? "" : newEntity.getPlatXcode());
        log.setCurrentPcategory(newEntity.isBind() ? Pcategory.Ptype : Pcategory.UnRelation);
        log.setEtypeName(getEtypeName());
        log.setUpdateTime(new Date());
        log.setCreateTime(new Date());
        setNullEmpty(log);
        return log;
    }

    public EshopProductMappingLog buildEshopMappingLogEntity(EshopProductSkuMapping newEntity) {
        if (null == newEntity) {
            newEntity = new EshopProductSkuMapping();
        }
        EshopProductMappingLog log = new EshopProductMappingLog();
        log.setEshopId(newEntity.getEshopId() == null ? BigInteger.ZERO : newEntity.getEshopId());
        log.setXcode(newEntity.getXcode() == null ? "" : newEntity.getXcode());
        log.setFullname(getLogFullName(newEntity));
        log.setEshopId(newEntity.getEshopId());
        log.setId(UId.newId());
        log.setEtypeId(CurrentUser.getEmployeeId());
        log.setOpreateTime(new Date());
        log.setPlatformFullPropertiesName(newEntity.getPlatformPropertiesName());
        log.setPlatformNumId(newEntity.getPlatformNumId());
        log.setPlatformSkuId(newEntity.getPlatformSkuId());
        log.setProfileId(CurrentUser.getProfileId());
        log.setPtypeFullname(newEntity.getPtypeName());
        log.setSkuId(newEntity.getSkuId());
        log.setUnitId(newEntity.getUnitId());
        log.setOldPcategory(newEntity.isBind() ? Pcategory.Ptype : Pcategory.UnRelation);
        log.setPlatformXcode(newEntity.getPlatformXcode() == null ? "" : newEntity.getPlatformXcode());
        log.setCurrentPcategory(newEntity.isBind() ? Pcategory.Ptype : Pcategory.UnRelation);
        log.setEtypeName(getEtypeName());
        log.setUpdateTime(new Date());
        log.setCreateTime(new Date());
        setNullEmpty(log);
        return log;
    }

    private static String getLogFullName(EshopProductSkuMapping newEntity){
        if(StringUtils.isNotEmpty(newEntity.getFullname())){
            return newEntity.getFullname();
        }
        if(StringUtils.isNotEmpty(newEntity.getPlatfullname())){
            return newEntity.getPlatfullname();
        }
        return "";
    }

    private static void setNullEmpty(EshopProductMappingLog entity) {
        if (entity.getPtypeFullname() == null) {
            entity.setPtypeFullname("");
        }
        if (entity.getPlatformFullPropertiesName() == null) {
            entity.setPlatformFullPropertiesName("");
        }
        if (entity.getXcode() == null) {
            entity.setXcode("");
        }
        if (entity.getSkuId() == null) {
            entity.setSkuId(BigInteger.ZERO);
        }
        if (entity.getUnitId() == null) {
            entity.setUnitId(BigInteger.ZERO);
        }
        if (entity.getCurrentPcategory().getCode() == -1) {
            entity.setPtypeFullname("");
            entity.setXcode("");
            entity.setSkuId(BigInteger.ZERO);
            entity.setUnitId(BigInteger.ZERO);
        }
        if (entity.getDescription() == null) {
            entity.setDescription("");
        }
    }

    private String getEtypeName() {
        String etypeName = GetBeanUtil.getBean(CurrentEtypeUtil.class).getEtypeName();
        if (etypeName == null) {
            return "";
        }
        return etypeName;
    }

    public static List<EshopProductMappingLog> toProductMappingLogs(List<EshopProductSkuPageData> skus, ProductOperateLogType logType, String logMsg) {
        List<EshopProductMappingLog> logs = new ArrayList<>();
        if (CollectionUtils.isEmpty(skus)) {
            return new ArrayList<>();
        }
        for (EshopProductSkuPageData sku : skus) {
            logs.add(toMappingLog(sku, logType, logMsg));
        }
        return logs;
    }

    public static EshopProductMappingLog toMappingLog(EshopProductSkuPageData entity, ProductOperateLogType logType, String logMsg) {
        CurrentEtypeUtil bean = GetBeanUtil.getBean(CurrentEtypeUtil.class);
        String etypeName = bean.getEtypeName();
        EshopProductMappingLog log = new EshopProductMappingLog();
        log.setEshopId(entity.getOtypeId());
        log.setDescription(logMsg);
        log.setOpreateTime(DateUtils.getDate());
        log.setFullname(entity.getPlatformFullname());
        log.setEtypeName(etypeName == null ? "" : etypeName);
        log.setPlatformNumId(entity.getPlatformNumId());
        log.setPlatformSkuId(entity.getPlatformSkuId());
        log.setPlatformXcode(entity.getPlatformXcode());
        log.setPlatformFullPropertiesName(entity.getPlatformPropertiesName());
        log.setOpreateType(logType);
        log.setPtypeFullname(entity.getPtypeName());
        log.setUnitId(entity.getUnitId());
        log.setSkuId(entity.getSkuId());
        log.setXcode(entity.getXcode());
        setNullEmpty(log);
        return log;
    }

    public static EshopProductMappingLog toMappingLog(EshopProductSkuMapping entity, ProductOperateLogType logType, String logMsg) {
        CurrentEtypeUtil bean = GetBeanUtil.getBean(CurrentEtypeUtil.class);
        String etypeName = bean.getEtypeName();
        EshopProductMappingLog log = new EshopProductMappingLog();
        log.setEshopId(entity.getEshopId());
        log.setDescription(logMsg);
        log.setOpreateTime(DateUtils.getDate());
        log.setFullname(getLogFullName(entity));
        log.setEtypeName(etypeName == null ? "" : etypeName);
        log.setPlatformNumId(entity.getPlatformNumId());
        log.setPlatformSkuId(entity.getPlatformSkuId());
        log.setPlatformXcode(entity.getPlatformXcode());
        log.setPlatformFullPropertiesName(entity.getPlatformPropertiesName());
        log.setOpreateType(logType);
        log.setPtypeFullname(entity.getPtypeName());
        log.setUnitId(entity.getUnitId());
        log.setSkuId(entity.getSkuId());
        log.setXcode(entity.getXcode());
        setNullEmpty(log);
        return log;
    }


    public static EshopProductMappingLog toMappingLog(EshopProductSkuMapping entity, ProductOperateLogType logType) {
        String logMsg = buildLogMsg(entity, logType);
        return EshopProductMappingLogService.toMappingLog(entity, logType, logMsg);
    }

    public static List<EshopProductMappingLog> toMappingLogList(List<EshopProductSkuMapping> entityList, ProductOperateLogType logType) {
        List<EshopProductMappingLog> logList=new ArrayList<>();
        if(CollectionUtils.isEmpty(entityList)){
            return logList;
        }
        for (EshopProductSkuMapping skuMapping: entityList){
            String logMsg = buildLogMsg(skuMapping, logType);
            logList.add(toMappingLog(skuMapping, logType, logMsg));
        }
        return logList;
    }

    public static void doWriteStockSyncLog(List<EshopProductSkuPageData> skus) {
        if(CollectionUtils.isEmpty(skus)){
            return;
        }
        try {
            for (EshopProductSkuPageData sku: skus){
                String logMsg = "执行了手工同步库存";
                if(sku.getOldQty()!=null && sku.getQty().compareTo(sku.getOldQty())!=0){
                    logMsg = String.format("将库存从【%s】改为【%s】后执行了手工同步库存",sku.getOldQty(),sku.getQty());
                }
                EshopProductMappingLog log = toMappingLog(sku, ProductOperateLogType.STOCK_SYNC, logMsg);
                LogService.add(log);
            }
        }catch (Exception ex){
            logger.error("记录日志报错了，错误信息{}",ex.getMessage(), ex);
        }
    }

    private static String buildLogMsg(EshopProductSkuMapping mapping, ProductOperateLogType logType) {
        switch (logType) {
            case Refresh:
                return "执行了刷新网店商品";
            case ChangeLocalXcode:
                return String.format("修改前【%s】，修改后【%s】", mapping.getXcode(), mapping.getRealXcode());
            case DownloadProduct:
                return "新增 SKU";
            case BatchChangeOnlineXCode:
                return String.format("生成前的商家编码【%s】，生成新的商家编码【%s】", mapping.getReadySkuXcode(), mapping.getPlatformXcode());
            case ChangeXCode:
                return String.format("修改前的商家编码【%s】，修改后的商家编码【%s】", mapping.getReadySkuXcode(), mapping.getPlatformXcode());
            case BindMapping:
                return getBindingLog("绑定系统商品", mapping);
            case MANUAL_MAPPING_UNBIND:
                return getBindingLog("解除手工绑定的对应关系，原对应关系", mapping);
            case SYNC_RELATION_TO_BRANCH:
                return "同步商品对应关系到子店";
            default:
                return "";
        }
    }

    public static String getBindingLog(String pre, EshopProductSkuMapping mapping){
        String bindLog = String.format("%s：商品名称【%s】", pre, mapping.getPtypeName());
        if (StringUtils.isNotEmpty(mapping.getXcode())) {
            bindLog += String.format(",商家编码【%s】", mapping.getXcode());
        }
        if (StringUtils.isNotEmpty(mapping.getPropValues())) {
            bindLog += String.format(",商家属性【%s】", mapping.getPropValues());
        }
        return bindLog;
    }
}

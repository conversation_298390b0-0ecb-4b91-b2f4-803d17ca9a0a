package com.wsgjp.ct.sale.biz.eshoporder.service.stock;

import bf.datasource.page.PageDevice;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.wsgjp.ct.common.enums.core.enums.ProductMarkEnum;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.redis.process.message.bll.RedisMessageUtil;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopStockService;
import com.wsgjp.ct.sale.biz.bifrost.util.EshopUtils;
import com.wsgjp.ct.sale.biz.eshoporder.api.StockToolApi;
import com.wsgjp.ct.sale.biz.eshoporder.api.request.stock.NotifyMsgSyncParam;
import com.wsgjp.ct.sale.biz.eshoporder.config.EshopOrderConst;
import com.wsgjp.ct.sale.biz.eshoporder.config.SaleBizConfig;
import com.wsgjp.ct.sale.biz.eshoporder.config.ServiceConfig;
import com.wsgjp.ct.sale.biz.eshoporder.constant.NameConstantEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.ComboDetail;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Otype;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.EshopProductMarkEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.Pcategory;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.ProductOperateLogType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleTargetTypeEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMark;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuPageData;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.ProductSkuMultiStockSyncSettingEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryEShopParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryProductMarkRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.stock.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.stock.*;
import com.wsgjp.ct.sale.biz.eshoporder.impl.ProcessLoggerImpl;
import com.wsgjp.ct.sale.biz.eshoporder.log.EshopStockSyncActionLog;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopProductMapper;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopProductMarkMapper;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.StockSyncMapper;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopService;
import com.wsgjp.ct.sale.biz.eshoporder.service.product.EshopProductMappingLogService;
import com.wsgjp.ct.sale.biz.eshoporder.util.CommonUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.SysLogUtil;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.entity.stock.NotifyMessageStockSyncParam;
import com.wsgjp.ct.sale.common.entity.stock.StockSyncPubMessage;
import com.wsgjp.ct.sale.common.entity.stock.StockSyncRelationMessage;
import com.wsgjp.ct.sale.platform.dto.product.WareHouseStockSync;
import com.wsgjp.ct.sale.platform.entity.request.product.ModifyStockParam;
import com.wsgjp.ct.sale.platform.entity.request.product.ModifyStockQtyRequest;
import com.wsgjp.ct.sale.platform.entity.request.product.ModifyStockResult;
import com.wsgjp.ct.sale.platform.entity.request.product.StockValidityBatch;
import com.wsgjp.ct.sale.platform.entity.response.product.ProductModifyStockResponse;
import com.wsgjp.ct.sale.platform.enums.StockType;
import com.wsgjp.ct.sale.platform.feature.stock.EshopStockGeneralFeature;
import com.wsgjp.ct.sale.platform.feature.stock.EshopStockWarehouseFeature;
import com.wsgjp.ct.sale.sdk.stock.biz.QueryStockService;
import com.wsgjp.ct.sale.sdk.stock.entity.StockBatchLockQtyEntity;
import com.wsgjp.ct.sale.sdk.stock.entity.StockLockQtyEntity;
import com.wsgjp.ct.sale.sdk.stock.entity.StockSaleQtyBatchEntity;
import com.wsgjp.ct.sale.sdk.stock.entity.StockSaleQtyEntity;
import com.wsgjp.ct.sale.sdk.stock.parameter.QueryStockParameter;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.log.service.LogService;
import com.wsgjp.ct.support.thread.ThreadPool;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import ngp.idgenerator.UId;
import ngp.utils.CollectionUtils;
import ngp.utils.JsonUtils;
import ngp.utils.Md5Utils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 8/4/2020 下午 6:27
 */
@Service
public class ManualStockSyncService {

    private final EshopService eshopService;
    private final ServiceConfig serviceConfig;
    private final StockSyncMapper mapper;
    private final EshopProductMapper productMapper;
    private final EshopProductMarkMapper productmarkMapper;
    private final StockSyncBaseService stockBaseSvc;
    private final StockBuildService stockSvc;
    private final BifrostEshopStockService apiSvc;
    private final StockToolApi toolApi;
    private final SaleBizConfig config;
    private final QueryStockService qtyService;

    private static final Logger logger = LoggerFactory.getLogger(ManualStockSyncService.class);

    public ManualStockSyncService(EshopService eshopService, ServiceConfig serviceConfig, StockSyncMapper mapper, EshopProductMapper productMapper, EshopProductMarkMapper productmarkMapper, StockSyncBaseService stockBaseSvc, StockBuildService stockSvc, BifrostEshopStockService apiSvc, StockToolApi toolApi, SaleBizConfig config, QueryStockService qtyService) {
        this.eshopService = eshopService;
        this.serviceConfig = serviceConfig;
        this.mapper = mapper;
        this.productMapper = productMapper;
        this.productmarkMapper = productmarkMapper;
        this.stockBaseSvc = stockBaseSvc;
        this.stockSvc = stockSvc;
        this.apiSvc = apiSvc;
        this.toolApi = toolApi;
        this.config = config;
        this.qtyService = qtyService;
    }

    public int queryPageDataCount(QueryStockPageDataParameter parameter) {
        return mapper.queryStockSyncPageDataCount(parameter);
    }

    public List<StockSyncManagePageData> getStockPageDataByEshop(PageRequest<QueryStockPageDataParameter> query) {
        QueryStockPageDataParameter parameter = query.getQueryParams();
        parameter.setQueryTypeInt(parameter.getQueryType().getCode());
        int pageSize = config.getPageSize();
        int PageDataCount = mapper.queryStockSyncPageDataCount(parameter);
        List<StockSyncManagePageData> dataList = new ArrayList<>();
        if (PageDataCount > pageSize) {
            int pageCount = config.getPageCount(PageDataCount);
            for (int i = 0; i < pageCount; i++) {
                parameter.setPageNum(i * pageSize);
                parameter.setPageSize(pageSize);
                dataList.addAll(mapper.queryStockSyncPageDataLimit(parameter));
            }
        } else {
            dataList = mapper.queryStockSyncPageData(parameter);
        }
        if (CollectionUtils.isEmpty(dataList)) {
            return new ArrayList<>();
        }
        List<StockSyncManagePageData> noPropDetails = dataList.stream().filter(x -> !x.getHasProperties()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noPropDetails)) {
            List<String> numIds = noPropDetails.stream().map(StockSyncManagePageData::getPlatformNumId).collect(Collectors.toList());
            parameter.setPlatformNumIds(numIds);
            List<StockSyncManagePageData> details = queryPageDetail(parameter);
            initMainPageData(dataList, details);
        } else {
            initMainPageData(dataList, new ArrayList<>());
        }
        return dataList;
    }


    public PageResponse<StockSyncManagePageData> queryPageData(PageRequest<QueryStockPageDataParameter> query) {
        try {
            QueryStockPageDataParameter parameter = query.getQueryParams();
            initStockQueryParameter(parameter);
            PageDevice.initPageWithCount(query, false);
            List<StockSyncManagePageData> dataList = mapper.queryStockSyncPageData(parameter);
            if (CollectionUtils.isEmpty(dataList)) {
                return PageDevice.readPage(dataList);
            }
            PageResponse<StockSyncManagePageData> response = new PageResponse<>();
            response.setPageIndex(query.getPageIndex());
            response.setPageSize(query.getPageSize());
            response.setTotal(dataList.size());
            List<StockSyncManagePageData> noPropDetails = dataList.stream().filter(x -> !x.getHasProperties()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(noPropDetails)) {
                List<String> numIds = noPropDetails.stream().map(StockSyncManagePageData::getPlatformNumId).collect(Collectors.toList());
                parameter.setPlatformNumIds(numIds);
                List<StockSyncManagePageData> details = queryPageDetail(parameter);
                initMainPageData(dataList, details);
            } else {
                initMainPageData(dataList, new ArrayList<>());
            }
            response.setList(dataList);
            return response;
        } catch (Throwable ex) {
            throw new RuntimeException("查询库存同步数据报错:" + ex.getMessage(), ex);
        }
    }

    private void initMainPageData(List<StockSyncManagePageData> mainList, List<StockSyncManagePageData> detailList) {
        for (StockSyncManagePageData main : mainList) {
            if (main.getHasProperties()) {
                main.setWarehouseSyncEnabled(null);
                main.setAllowAutoSync(null);
                main.setLadderQty("");
            }
            if (CollectionUtils.isEmpty(detailList)) {
                continue;
            }
            Map<String, StockSyncManagePageData> detailMap = detailList.stream().collect(Collectors.toMap(StockSyncManagePageData::getPlatformNumId, x -> x, (numId1, numId2) -> numId2));
            if (!detailMap.containsKey(main.getPlatformNumId())) {
                continue;
            }
            StockSyncManagePageData first = detailMap.get(main.getPlatformNumId());
            if (main.getHasProperties() == false && main.getMainProduct()) {
                main.setMark(first.getMark());
            }
            main.setPlatformSkuIdOrigin(first.getPlatformSkuId());
            main.setReceSyncState(first.getReceSyncState());
            main.setRuleId(first.getRuleId());
            main.setRuleName(first.getRuleName());
            main.setCalculate(first.getCalculate());
            main.setFormula(first.getFormula());
            main.setStockList(first.getStockList());
            main.setKtypeNameList(first.getKtypeNameList());
            main.setSyncQty(first.getSyncQty());
            main.setSaleQty(first.getSaleQty());
            main.setRuleType(first.getRuleType());
            main.setPtypeId(first.getPtypeId());
            main.setPtypeName(first.getPtypeName());
            main.setSkuId(first.getSkuId());
            main.setUnitId(first.getUnitId());
            main.setUnitName(first.getUnitName());
            main.setLocalProps(first.getLocalProps());
            main.setXcode(first.getXcode());
            main.setBaseUnitId(first.getBaseUnitId());
            main.setUnitRate(first.getUnitRate());
            main.setPcategory(first.getPcategory());
            main.setStockSyncRule(first.getStockSyncRule());
            main.setComboDetails(first.getComboDetails());
            main.setSaleQty(first.getSaleQty());
            main.setSyncQty(first.getSyncQty());
            main.setWarehouseSyncEnabled(first.isWarehouseSyncEnabled());
            main.setAllowAutoSync(first.getAllowAutoSync());
            main.setTargetType(first.getTargetType());
            main.setMappingType(first.getMappingType());
            main.setBatchEnabled(first.getBatchEnabled());
            main.setProtectDays(first.getProtectDays());
        }
    }


    private void initStockQueryParameter(QueryStockPageDataParameter parameter) {
        parameter.setQueryString(parameter.getQueryString().replaceAll(":", "："));
        parameter.setQueryString(parameter.getQueryString().replaceAll("，", ","));
        parameter.setQueryTypeInt(parameter.getQueryType().getCode());
    }

    public List<StockSyncManagePageData> queryPageDetail(QueryStockPageDataParameter parameter) {
        initStockQueryParameter(parameter);
        List<StockSyncManagePageData> pageDataDetail = mapper.queryStockSyncPageDataDetail(parameter);
        QueryStockRuleParameter ruleParameter = initQueryParameter(parameter);
        doBuildPageData(pageDataDetail, ruleParameter);
        return pageDataDetail;
    }

    public List<StockSyncManagePageData> queryProductDetailForStockSync(QueryStockPageDataParameter parameter) {
        initStockQueryParameter(parameter);
        List<StockSyncManagePageData> pageDataDetail = mapper.queryStockSyncPageDataDetail(parameter);
        QueryStockRuleParameter ruleParameter = initQueryParameter(parameter);
        buildSyncRule(pageDataDetail, ruleParameter);
        buildLadderRule(pageDataDetail, ruleParameter);
        buildPageQtyInfo(pageDataDetail);
        return pageDataDetail;
    }

    public void refreshRowData(StockSyncManagePageData rowData) {
        BigInteger eshopId = rowData.getEshopId();
        List<StockSyncManagePageData> pageDataDetail = new ArrayList<>();
        pageDataDetail.add(rowData);
        QueryStockRuleParameter parameter = new QueryStockRuleParameter();
        parameter.setEshopId(eshopId);
        parameter.setContainsInitRule(true);
        parameter.setWarehouseCode(rowData.getWarehouseCode());
        parameter.setTargetType(rowData.getTargetType());
        doBuildPageData(pageDataDetail, parameter);
    }

    private void doBuildPageData(List<StockSyncManagePageData> pageDataDetail, QueryStockRuleParameter parameter) {
        if (CollectionUtils.isEmpty(pageDataDetail)) {
            return;
        }
        buildSyncRule(pageDataDetail, parameter);
        buildLadderRule(pageDataDetail, parameter);
        buildPageQtyInfo(pageDataDetail);
        buildProductMark(pageDataDetail, parameter);
    }

    private void buildProductMark(List<StockSyncManagePageData> pageDataDetail, QueryStockRuleParameter parameter) {
        List<String> numids = pageDataDetail.stream().map(StockSyncManagePageData::getPlatformNumId).collect(Collectors.toList());
        QueryProductMarkRequest markRequest = new QueryProductMarkRequest();
        markRequest.setEshopId(parameter.getEshopId());
        markRequest.setNumids(numids);
        List<EshopProductMark> productMarkList = productmarkMapper.queryProductMarkByNumids(markRequest);
        List<String> marks = Arrays.asList(serviceConfig.getSyncStockAllowShowMark().split(","));
        //以属性名称作为md5key
        Map<String, List<EshopProductMark>> productMarkMap = productMarkList.stream().filter(item -> marks.contains(item.getMarkCode() + "")).collect(Collectors.toMap((x -> Md5Utils.md5(String.format("%s%s", x.getPlatformNumId(), x.getPlatformPropertiesName()))), x -> {
            List<EshopProductMark> arr = new ArrayList<>();
            arr.add(x);
            return arr;
        }, (List<EshopProductMark> value1, List<EshopProductMark> value2) -> {
            value1.addAll(value2);
            return value1;
        }));
        for (StockSyncManagePageData stockSyncManagePageData : pageDataDetail) {
            String hashKey = Md5Utils.md5(String.format("%s%s", stockSyncManagePageData.getPlatformNumId(), stockSyncManagePageData.getPlatformPropertiesName()));
            List<EshopProductMark> productMarks = productMarkMap.get(hashKey);
            if (productMarks == null || productMarks.isEmpty()) {
                productMarks = new ArrayList<>();
            }
            StringBuilder sbBtn = new StringBuilder();
            sbBtn.append("[");
            for (EshopProductMark productMark : productMarks) {
                EshopProductMarkEnum enumByCode = EshopProductMarkEnum.getEnumByCode(productMark.getMarkCode());
                if (null != enumByCode) {
                    if (sbBtn.length() > 1) {
                        sbBtn.append(",");
                    }
                    HashMap<String, String> hp = new HashMap();
                    hp.put("clear", String.format("<font title='%s' color=\'white\'  style=\' background-color:%s;border-radius:3px;padding:2px;margin-top:2px\'>%s</font>", enumByCode.getBubble(), enumByCode.getColor(), enumByCode.getName()));
                    sbBtn.append(JsonUtils.toJson(hp));
                }
            }
            sbBtn.append("]");
            stockSyncManagePageData.setMark(sbBtn.toString());
        }
        //buildTimingSyncQtyInfo(pageDataDetail,productMarkList);
    }

    private void buildPageQtyInfo(List<StockSyncManagePageData> pageDataDetail) {
        List<BigInteger> ruleIds = getRuleIds(pageDataDetail);
        List<BigInteger> skuIds = getSkuIds(pageDataDetail);
        List<StockSaleQtyBatchEntity> saleQtyBatchList = stockSvc.getBatchSaleQtyList(skuIds);
        List<StockBatchLockQtyEntity> lockRecordBatchList = stockSvc.getBatchLockRecordList(ruleIds);
        List<StockSaleQtyEntity> qtyList = stockSvc.getSaleQtyList(skuIds);
        List<StockLockQtyEntity> recordList = stockSvc.getLockRecordList(ruleIds);
        Map<BigInteger, List<SafeSaleQtyConfigEntity>> safeQtyMap = stockSvc.buildSafeQtyMap();
        BigInteger profileId = CurrentUser.getProfileId();
        Map<BigInteger, List<SafeSaleQtyProductConfigEntity>> skuSafeQtyMap = stockSvc.buildSkuSafeQtyMap(profileId, skuIds);
        List<String> UniqueIds = pageDataDetail.stream().map(StockSyncManagePageData::getUniqueId).distinct().collect(Collectors.toList());
        List<ProductSkuMultiStockSyncSettingEntity> multiSyncSet = stockBaseSvc.getMultiStockSyncSetting(UniqueIds, pageDataDetail.get(0).getEshopId());
        for (StockSyncManagePageData data : pageDataDetail) {
            StockCalParameter parameter = new StockCalParameter();
            parameter.setLockRecord(recordList);
            parameter.setLockBatchRecord(lockRecordBatchList);
            parameter.setSaleQtyList(qtyList);
            parameter.setSaleQtyBatchList(saleQtyBatchList);
            parameter.setProfileId(profileId);
            parameter.setEshopId(data.getEshopId());
            parameter.setSkuSafeQtyMap(skuSafeQtyMap);
            parameter.setSafeQtyConfigMap(safeQtyMap);
            parameter.setMultiSyncSet(CollectionUtils.isEmpty(multiSyncSet) ? null : multiSyncSet.stream()
                    .filter(x -> x.getUniqueId().equals(data.getUniqueId())).sorted(Comparator.comparingLong(y ->
                            {
                                try {
                                    return Long.parseLong(y.getPlatformMultiId());
                                } catch (NumberFormatException e) {
                                    return 0;
                                }
                            }
                    )).collect(Collectors.toList()));
            doBuildPageQtyInfo(data, parameter);
        }
    }

    private void doBuildPageQtyInfo(StockSyncManagePageData data, StockCalParameter parameter) {
        StockSyncRule rule = data.getStockSyncRule();
        StockSyncRule ladderRule = data.getLadderSyncRule();
        BigInteger skuId = data.getSkuId();
        if (data.getPcategory() == Pcategory.Combo.getCode()) {
            StockCalByComboParameter comboParameter = new StockCalByComboParameter();
            comboParameter.setRule(rule);
            comboParameter.setCombos(data.getComboDetails());
            comboParameter.setEshopId(parameter.getEshopId());
            comboParameter.setProfileId(parameter.getProfileId());
            comboParameter.setSaleQtyList(parameter.getSaleQtyList());
            comboParameter.setLockRecord(parameter.getLockRecord());
            comboParameter.setSafeQtyConfigMap(parameter.getSafeQtyConfigMap());
            comboParameter.setSkuSafeQtyMap(parameter.getSkuSafeQtyMap());
            BigDecimal qtyByCombo = stockSvc.getSyncQtyByCombo(comboParameter);
            data.setSyncQty(qtyByCombo.toBigInteger().toString());
            data.setSaleQty(data.getSyncQty());
            if (ladderRule != null) {
                comboParameter.setRule(ladderRule);
                BigDecimal realQtyByCombo = stockSvc.getSyncQtyByCombo(comboParameter);
                data.setRealQty(data.getSyncQty());
                data.setLadderQty(realQtyByCombo.toBigInteger().toString());
            }
            stockBaseSvc.buildMultiTimeSyncTImeQty(parameter.getMultiSyncSet(), qtyByCombo);
            data.setMultiSetting(parameter.getMultiSyncSet());
            data.setHasMultiSyncSetting(true);
            return;
        }
        parameter.setRule(rule);
        BigDecimal syncQty = stockSvc.getSyncQty(skuId, parameter);
        data.setSyncQty(syncQty.toBigInteger().toString());
        data.setSaleQty(data.getSyncQty());
        if (ladderRule != null) {
            parameter.setRule(ladderRule);
            BigDecimal realQty = stockSvc.getSyncQty(skuId, parameter);
            data.setRealQty(data.getSyncQty());
            data.setLadderQty(realQty.toBigInteger().toString());
        }
        stockBaseSvc.buildMultiTimeSyncTImeQty(parameter.getMultiSyncSet(), syncQty);
        data.setMultiSetting(parameter.getMultiSyncSet());
        data.setHasMultiSyncSetting(true);
    }

    private List<BigInteger> getSkuIds(List<StockSyncManagePageData> pageDataDetail) {
        initComboDetail(pageDataDetail);
        List<BigInteger> skuIds = pageDataDetail.stream().map(StockSyncManagePageData::getSkuId).collect(Collectors.toList());
        List<List<ComboDetail>> comboDetails = pageDataDetail.stream().map(StockSyncManagePageData::getComboDetails).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(comboDetails)) {
            return skuIds;
        }
        for (List<ComboDetail> list : comboDetails) {
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            List<BigInteger> pageSkuIds = list.stream().map(ComboDetail::getSkuId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(pageSkuIds)) {
                continue;
            }
            skuIds.addAll(pageSkuIds);
        }
        return skuIds.stream().distinct().collect(Collectors.toList());
    }

    private void initComboDetail(List<StockSyncManagePageData> pageDataDetail) {
        List<StockSyncManagePageData> collect = pageDataDetail.stream().filter(x -> x.getPcategory() == 2).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return;
        }
        List<BigInteger> comboIds = collect.stream().map(StockSyncManagePageData::getPtypeId).collect(Collectors.toList());
        List<ComboDetail> comboDetails = stockBaseSvc.getComboDetails(comboIds);
        if (CollectionUtils.isEmpty(comboDetails)) {
            return;
        }
        for (StockSyncManagePageData pageData : pageDataDetail) {
            BigInteger ptypeId = pageData.getPtypeId();
            List<ComboDetail> details = comboDetails.stream().filter(x -> x.getComboId().equals(ptypeId)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(details)) {
                continue;
            }
            pageData.setComboDetails(details);
        }
    }

    private List<BigInteger> getRuleIds(List<StockSyncManagePageData> pageDataDetail) {
        List<BigInteger> ruleIdList = new ArrayList<>();
        List<StockSyncRule> ruleList = pageDataDetail.stream().map(StockSyncManagePageData::getStockSyncRule).collect(Collectors.toList());
        doAppendRuleIds(ruleList, ruleIdList);
        List<StockSyncRule> realRules = pageDataDetail.stream().map(StockSyncManagePageData::getLadderSyncRule).filter(Objects::nonNull).collect(Collectors.toList());
        doAppendRuleIds(realRules, ruleIdList);
        return ruleIdList;
    }

    private void doAppendRuleIds(List<StockSyncRule> ruleList, List<BigInteger> ruleIdList) {
        if (CollectionUtils.isEmpty(ruleList)) {
            return;
        }
        List<BigInteger> ruleIds = ruleList.stream().map(StockSyncRuleBase::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ruleIds)) {
            return;
        }
        ruleIdList.addAll(ruleIds);
    }

    private void buildSyncRule(List<StockSyncManagePageData> pageDataDetail, QueryStockRuleParameter parameter) {
        try {
            if (parameter.getTargetType().equals(StockRuleTargetTypeEnum.LADDER)) {
                return;
            }
            List<StockSyncRule> ruleList = getRuleListByData(pageDataDetail, parameter);
            for (StockSyncManagePageData pageData : pageDataDetail) {
                pageData.setWarehouseCode(parameter.getWarehouseCode());
                pageData.setTargetType(parameter.getTargetType());
                StockSyncRule skuRule = getSkuRule(pageData, ruleList);
                doBuildSyncRule(pageData, skuRule);
            }
        } catch (Exception ex) {
            throw new RuntimeException("构建同步规则报错-" + ex.getMessage(), ex);
        }
    }

    private StockSyncRule getSkuRule(StockSyncManagePageData pageData, List<StockSyncRule> ruleList) {
        List<StockSyncRule> tempRuleList = ruleList.stream()
                .filter(x -> x.getId().equals(pageData.getRuleId()))
                .collect(Collectors.toList());
        if (tempRuleList.size() > 0) {
            return tempRuleList.get(0);
        }
        return getDefaultRule(ruleList);
    }

    private StockSyncRule getDefaultRule(List<StockSyncRule> ruleList) {
        List<StockSyncRule> collect = ruleList.stream().filter(StockSyncRule::isDefaultRule).collect(Collectors.toList());
        if (collect.size() == 0) {
            throw new RuntimeException("没有查询到默认同步规则");
        }
        return collect.get(0);
    }

    private List<StockSyncRule> getRuleListByData(List<StockSyncManagePageData> pageDataDetail, QueryStockRuleParameter parameter) {
        List<StockSyncManagePageData> comboData = pageDataDetail.stream().filter(x -> x.getPcategory() == 2).collect(Collectors.toList());
        List<StockSyncManagePageData> skuData = pageDataDetail.stream().filter(x -> x.getPcategory() != 2).collect(Collectors.toList());
        QueryStockRuleParameter cloneParam = parameter.doClone();
        cloneParam.setPcategory(Pcategory.Combo);
        List<StockSyncRule> ruleList = new ArrayList<>(getRuleListByRuleId(comboData, cloneParam));
        cloneParam.setPcategory(Pcategory.Ptype);
        ruleList.addAll(getRuleListByRuleId(skuData, cloneParam));
        return ruleList;
    }

    private QueryStockRuleParameter initQueryParameter(QueryStockPageDataParameter pageDataParameter) {
        QueryStockRuleParameter parameter = new QueryStockRuleParameter();
        parameter.setContainsInitRule(true);
        parameter.setEshopId(pageDataParameter.getEshopId());
        parameter.setWarehouseCode(pageDataParameter.getWarehouseCode());
        parameter.setTargetType(pageDataParameter.getTargetType());
        return parameter;
    }

    private List<StockSyncRule> getRuleListByRuleId(List<StockSyncManagePageData> dataList, QueryStockRuleParameter parameter) {
        if (CollectionUtils.isEmpty(dataList)) {
            return new ArrayList<>();
        }
        List<BigInteger> ruleIds = dataList.stream().map(StockSyncManagePageData::getRuleId).collect(Collectors.toList());
        if (ruleIds.size() > 0) {
            parameter.setRuleIds(ruleIds);
        }
        return stockBaseSvc.getRuleList(parameter);
    }

    private void doBuildSyncRule(StockSyncManagePageData pageData, StockSyncRule rule) {
        pageData.setRuleId(rule.getId());
        pageData.setCalculate(rule.getCalculate());
        pageData.setKtypeNameList(rule.getKtypeNames());
        pageData.setRuleType(rule.getRuleType());
        pageData.setFormula(rule.getFormula());
        pageData.setStockList(rule.getStockRuleDetailList());
        pageData.setRuleName(rule.getRuleName());
        if (rule.isDefaultRule()) {
            pageData.setRuleName("");
        }
        pageData.setZeroQtySyncEnabled(rule.isZeroQtySyncEnabled());
        String json = JsonUtils.toJson(rule);
        StockSyncRule cloneObj = JsonUtils.toObject(json, StockSyncRule.class);
        cloneObj.setRuleCron(JsonUtils.toJson(rule.getCalculate()));
        if (cloneObj.getPcategory() == null) {
            cloneObj.setPcategory(Pcategory.getNameByIndex(pageData.getPcategory()));
        }
        cloneObj.setPtypeId(pageData.getPtypeId());
        cloneObj.setSkuId(pageData.getSkuId());
        cloneObj.setUnitId(pageData.getUnitId());
        cloneObj.setUnitRate(pageData.getUnitRate());
        pageData.setStockSyncRule(cloneObj);
    }

    private void buildLadderRule(List<StockSyncManagePageData> pageDataDetail, QueryStockRuleParameter parameter) {
        StockRuleTargetTypeEnum targetType = parameter.getTargetType();
        if (targetType != StockRuleTargetTypeEnum.LADDER) {
            return;
        }
        doBuildLadderRule(pageDataDetail, parameter);
    }

    private void doBuildLadderRule(List<StockSyncManagePageData> pageDataDetail, QueryStockRuleParameter parameter) {
        BigInteger eshopId = parameter.getEshopId();
        List<EshopProductSkuRuleConfig> ruleConfig = getLadderRuleConfig(eshopId, pageDataDetail);
        StockSyncRule ladderDefault = stockBaseSvc.getLadderRuleByEshopId(eshopId);
        StockSyncRule realDefault = stockBaseSvc.getDefaultRuleByEshopId(eshopId);
        if (ladderDefault == null) {
            throw new RuntimeException("查询库存报错：找不到阶梯默认同步规则");
        }
        List<StockSyncRule> result = stockBaseSvc.getRuleListByConfig(eshopId, ruleConfig);
        List<String> xcodeList = pageDataDetail.stream().map(StockSyncManagePageData::getPlatformXcode).collect(Collectors.toList());
        List<StockSyncRule> xcodeRules = stockBaseSvc.getRuleListByXcodeList(eshopId, xcodeList);
        for (StockSyncManagePageData pageData : pageDataDetail) {
            pageData.setLadderSyncRule(null);
            pageData.setStockSyncRule(null);
            pageData.setTargetType(StockRuleTargetTypeEnum.LADDER);
            if (pageData.getMappingType().equals(MappingType.XCODEMAPPING)) {
                Optional<StockSyncRule> first = xcodeRules.stream().filter(x -> x.getXcode().equals(pageData.getXcode()) && x.getPcategory().getCode() == pageData.getPcategory()).findFirst();
                first.ifPresent(pageData::setStockSyncRule);
            } else {
                BigInteger realRuleId = getRuleIdByConfig(ruleConfig, pageData, StockRuleTargetTypeEnum.NORMAL);
                doBuildRuleByIdAndType(pageData, result, realRuleId, StockRuleTargetTypeEnum.NORMAL);
            }
            BigInteger ladderRuleId = getRuleIdByConfig(ruleConfig, pageData, StockRuleTargetTypeEnum.LADDER);
            doBuildRuleByIdAndType(pageData, result, ladderRuleId, StockRuleTargetTypeEnum.LADDER);
            if (pageData.getLadderSyncRule() == null) {
                StockSyncRule cloneLadderRule = doGetDefaultRuleForPageData(pageData, ladderDefault);
                pageData.setLadderSyncRule(cloneLadderRule);
            }
            if (pageData.getStockSyncRule() == null) {
                StockSyncRule cloneLadderRule = doGetDefaultRuleForPageData(pageData, realDefault);
                pageData.setStockSyncRule(cloneLadderRule);
            }
            doBuildPageLadderRuleInfo(pageData);
        }
    }

    private void doBuildPageLadderRuleInfo(StockSyncManagePageData pageData) {
        StockSyncRule syncRule = pageData.getLadderSyncRule();
        pageData.setRuleId(syncRule.getId());
        pageData.setCalculate(syncRule.getCalculate());
        pageData.setKtypeNameList(syncRule.getKtypeNames());
        pageData.setRuleType(syncRule.getRuleType());
        pageData.setFormula(syncRule.getFormula());
        pageData.setStockList(syncRule.getStockRuleDetailList());
    }

    private BigInteger getRuleIdByConfig(List<EshopProductSkuRuleConfig> ruleConfig, StockSyncManagePageData pageData, StockRuleTargetTypeEnum targetType) {
        if (CollectionUtils.isEmpty(ruleConfig)) {
            return null;
        }
        Optional<EshopProductSkuRuleConfig> first = ruleConfig.stream().filter(x -> x.getPlatformNumId().equals(pageData.getPlatformNumId())
                && x.getPlatformProperties().equals(pageData.getPlatformPropertiesName())
                && x.getTargetType().equals(targetType)).findFirst();
        return first.map(EshopProductSkuRuleConfig::getRuleId).orElse(null);
    }

    private StockSyncRule doGetDefaultRuleForPageData(StockSyncManagePageData pageData, StockSyncRule defaultRule) {
        String jsonRule = JsonUtils.toJson(defaultRule);
        StockSyncRule cloneRule = JsonUtils.toObject(jsonRule, StockSyncRule.class);
        cloneRule.setUnitRate(pageData.getUnitRate());
        cloneRule.setUnitId(pageData.getUnitId());
        cloneRule.setSkuId(pageData.getSkuId());
        cloneRule.setPtypeId(pageData.getPtypeId());
        return cloneRule;
    }

    private void doBuildRuleByIdAndType(StockSyncManagePageData pageData, List<StockSyncRule> result, BigInteger ruleId, StockRuleTargetTypeEnum targetType) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        if (ruleId == null || ruleId.compareTo(BigInteger.ZERO) == 0) {
            return;
        }
        Optional<StockSyncRule> first = result.stream().filter(x -> x.getId().equals(ruleId)).findFirst();
        if (!first.isPresent()) {
            return;
        }
        StockSyncRule syncRule = first.get();
        if (targetType.equals(StockRuleTargetTypeEnum.LADDER)) {
            pageData.setLadderSyncRule(syncRule);
            pageData.setRuleName(syncRule.getRuleName());

        } else {
            pageData.setStockSyncRule(syncRule);
        }
    }

    private List<EshopProductSkuRuleConfig> getLadderRuleConfig(BigInteger eshopId, List<StockSyncManagePageData> pageDataDetail) {
        List<String> numIds = pageDataDetail.stream().map(StockSyncManagePageData::getPlatformNumId).distinct().collect(Collectors.toList());
        List<String> properties = pageDataDetail.stream().map(StockSyncManagePageData::getPlatformPropertiesName).distinct().collect(Collectors.toList());
        return stockBaseSvc.getLadderRuleConfig(eshopId, numIds, properties);
    }

    public String executeStockSync(ManualStockSyncParameter parameter) {
        DoStockSyncTask task = new DoStockSyncTask();
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        task.setTaskId(taskId);
        task.setWarehouseCode(parameter.getWarehouseCode());
        task.setEshopId(parameter.getEshopId());
        task.setSyncDataList(parameter.getSyncDataList());
        task.setQueryParameter(parameter.getQueryParameter());
        task.setProcessLogger(processLogger);
        ThreadPool threadPool = ThreadPoolFactory.build(NameConstantEnum.STOCK_SYNC_THREAD_NAME.getName());
        threadPool.executeAsync(this::execute, task);
        return taskId;
    }

    public String syncStockByNotifyMsg(NotifyMessageStockSyncParam syncParam) {
        String taskId = UId.newId().toString();
        ThreadPool threadPool = ThreadPoolFactory.build(NameConstantEnum.STOCK_SYNC_THREAD_NAME.getName());
        if (config.getStockSyncByToolEnabled()) {
            threadPool.executeAsync(x -> {
                List<StockSyncRelationMessage> messageList = buildMessageByResult(syncParam.getPubMessageList());
                NotifyMsgSyncParam param = new NotifyMsgSyncParam();
                param.setTaskId(taskId);
                param.setMessageList(messageList);
                toolApi.doSyncStockByNotifyMsg(param);
            }, null);
        } else {
            syncParam.setTaskId(taskId);
            threadPool.executeAsync(x -> {
                doExecuteSyncByNotify(syncParam);
            }, null);
        }
        return taskId;
    }

    private void doExecuteSyncByNotify(NotifyMessageStockSyncParam syncParam) {
        DoStockSyncTask task = new DoStockSyncTask();
        String taskId = syncParam.getTaskId();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        processLogger.appendMsg("开始执行库存同步");
        if (CollectionUtils.isEmpty(syncParam.getPubMessageList())) {
            processLogger.appendMsg("同步数据异常，本次同步结束");
            processLogger.doFinish();
            return;
        }
        task.setTaskId(taskId);
        task.setWarehouseCode(syncParam.getWarehouseCode());
        task.setEshopId(syncParam.getEshopId());
        task.setProcessLogger(processLogger);
        List<StockSyncManagePageData> dataList = buildSyncPageData(syncParam);
        task.setSyncDataList(dataList);
        execute(task);
    }

    private List<StockSyncManagePageData> buildSyncPageData(NotifyMessageStockSyncParam syncParam) {
        List<StockSyncPubMessage> messageList = syncParam.getPubMessageList();
        List<String> numIds = messageList.stream().map(StockSyncPubMessage::getPlatformNumId).distinct().collect(Collectors.toList());
        List<String> skuIds = messageList.stream().map(StockSyncPubMessage::getPlatformSkuId).distinct().collect(Collectors.toList());
        QueryStockPageDataParameter parameter = new QueryStockPageDataParameter();
        parameter.setEshopId(syncParam.getEshopId());
        parameter.setWarehouseCode(syncParam.getWarehouseCode());
        parameter.setTargetType(StockRuleTargetTypeEnum.getEnumType(syncParam.getTargetType()));
        parameter.setPlatformNumIds(numIds);
        parameter.setPlatformSkuIds(skuIds);
        return queryPageDetail(parameter);
    }

    private List<StockSyncRelationMessage> buildMessageByResult(List<StockSyncPubMessage> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return new ArrayList<>();
        }
        try {
            List<String> numIds = resultList.stream().map(StockSyncPubMessage::getPlatformNumId).distinct().collect(Collectors.toList());
            List<String> skuIds = resultList.stream().map(StockSyncPubMessage::getPlatformSkuId).distinct().collect(Collectors.toList());
            BigInteger eshopId = new BigInteger(resultList.get(0).getEshopId());
            QueryStockNotifyRelationMessage parameter = new QueryStockNotifyRelationMessage();
            parameter.setProfileId(CurrentUser.getProfileId());
            parameter.setEshopId(eshopId);
            parameter.setPlatformNumIdList(numIds);
            parameter.setPlatformSkuIdList(skuIds);
            return mapper.queryNotifySyncRelationMessage(parameter);
        } catch (Exception ex) {
            throw new RuntimeException(String.format("查询库存同步参数报错：%s", ex.getMessage()), ex);
        }
    }

    private void execute(DoStockSyncTask task) {
        ProcessLoggerImpl processLogger = task.getProcessLogger();
        buildTaskOtype(task);
        try {
            List<StockSyncManagePageData> syncDatas = task.getSyncDataList();
            processLogger.appendMsg("开始构建同步参数");
            if (CollectionUtils.isEmpty(syncDatas)) {
                throw new RuntimeException("没有需要同步的数据");
            }
            List<StockSyncManagePageData> syncDataList = checkWarehouseType(syncDatas);
            if (syncDataList.size() != syncDatas.size()) {
                processLogger.appendMsg("无需库存同步的网店商品已被过滤");
            }
            List<ModifyStockParam> modifyStockParams = buildModifyParam(task);
            processLogger.appendMsg("开始同步库存到网店后台");
            List<ModifyStockResult> results = doThreadSyncStock(task, modifyStockParams, processLogger);
            if (CollectionUtils.isEmpty(results)) {
                throw new RuntimeException("接口返回数据为空");
            }
            List<BusinessStockResult> businessResults = buildBizResult(results, syncDataList, task);
            showResult(businessResults, processLogger);
            processLogger.doFinish();
            stockBaseSvc.logManualResult(businessResults, task.getOtype());
        } catch (Exception ex) {
            String error = String.format("执行库存同步报错:%s", ex.getMessage());
            processLogger.appendMsg(error);
            processLogger.appendMsg("本次同步结束！");
            processLogger.doFinish();
        }
    }

    private List<ModifyStockResult> executeStockSyncLimit(DoStockSyncTask task, List<ModifyStockParam> modifyStockParams, ProcessLoggerImpl processLogger) {
        SaleBizConfig saleBizConfig = GetBeanUtil.getBean(SaleBizConfig.class);
        int doBatchExcuteSize = saleBizConfig.getDoBatchExcuteSize();
        int i = processLogger.appendMsg(String.format("网店[%s]本次选择%s条,已处理%s条",task.getOtype().getFullname(), modifyStockParams.size(), 0));
        int excuteSize = 0;
        List<ModifyStockResult> results = new ArrayList<>();
        if (modifyStockParams.size() > doBatchExcuteSize) {
            List<ModifyStockParam> modifyStockLimit = new ArrayList<>();
            for (ModifyStockParam modifyStockParam : modifyStockParams) {
                modifyStockLimit.add(modifyStockParam);
                if (modifyStockLimit.size() >= doBatchExcuteSize) {
                    results.addAll(doSyncStock(task, modifyStockLimit));
                    modifyStockLimit = new ArrayList<>();
                    excuteSize += doBatchExcuteSize;
                    processLogger.modifyMsg(String.format("网店[%s]本次选择%s条,已处理%s条",task.getOtype().getFullname(), modifyStockParams.size(), excuteSize), i);
                }
            }
            if (modifyStockLimit.size() > 0) {
                results.addAll(doSyncStock(task, modifyStockLimit));
                excuteSize += modifyStockLimit.size();
                processLogger.modifyMsg(String.format("网店[%s]本次选择%s条,已处理%s条", task.getOtype().getFullname(),modifyStockParams.size(), excuteSize), i);
            }
        } else {
            results.addAll(doSyncStock(task, modifyStockParams));
            processLogger.modifyMsg(String.format("网店[%s]本次选择%s条,已处理%s条",task.getOtype().getFullname(), modifyStockParams.size(), modifyStockParams.size()), i);

        }
        return results;
    }

    private List<BusinessStockResult> buildBizResult(List<ModifyStockResult> results, List<StockSyncManagePageData> syncDataList, DoStockSyncTask task) {
        try {
            ArrayList<BusinessStockResult> businessResults = new ArrayList<>();
            for (ModifyStockResult result : results) {
                Optional<StockSyncManagePageData> first = syncDataList.stream().filter(x -> x.getPlatformNumId().equals(result.getPlatformNumId()) && x.getPlatformSkuId().equals(result.getPlatformSkuId()) && !(x.getMainProduct() && x.getHasProperties())).findFirst();
                if (!first.isPresent()) {
                    continue;
                }
                StockSyncManagePageData syncData = first.get();
                BusinessStockResult businessStockResult = JsonUtils.toObject(JsonUtils.toJson(result), BusinessStockResult.class);
                businessStockResult.setSyncTime(result.getSyncTime());
                businessStockResult.setSyncConfig(syncData.getFormula());
                businessStockResult.setKtypeIdList(syncData.getKtypeNameList());
                businessStockResult.setSyncRuleId(syncData.getRuleId());
                businessStockResult.setEshopId(task.getEshopId());
                businessStockResult.setPtypeId(syncData.getPtypeId());
                businessStockResult.setSyncQty(new BigInteger(syncData.getSyncQty()));
                businessStockResult.setPlatformXcode(syncData.getPlatformXcode());
                if (result.getTargetType() == 2) {
                    businessStockResult.setSyncQty(new BigInteger(syncData.getRealQty()));
                }
                if (StringUtils.isEmpty(businessStockResult.getPlatformSkuId()) && StringUtils.isNotEmpty(syncData.getPlatformSkuIdOrigin())) {
                    businessStockResult.setPlatformSkuId(syncData.getPlatformSkuIdOrigin());
                }
                businessResults.add(businessStockResult);
            }
            return businessResults;
        } catch (Exception ex) {
            throw new RuntimeException("构建库存同步结果报错" + ex.getMessage(), ex);
        }
    }

    private List<ModifyStockResult> doThreadSyncStock(DoStockSyncTask task, List<ModifyStockParam> modifyStockParams, ProcessLoggerImpl processLogger) {
        //如果商品数量不满一页或者不使用多线程同步则走之前的方法
        if (!config.isManualStockSyncByThread() || modifyStockParams.size() <= config.getDoBatchExcuteSize()) {
            return executeStockSyncLimit(task, modifyStockParams, processLogger);
        }
        int i = processLogger.appendMsg(String.format("网店[%s]本次选择%s条,已处理%s条", task.getOtype().getFullname(),modifyStockParams.size(), 0));
        Map<String, List<ModifyStockParam>> numidSyncMap = modifyStockParams.stream().collect(Collectors.groupingBy(ModifyStockParam::getPlatformNumId));
        List<List<ModifyStockParam>> splitList = new ArrayList<>(numidSyncMap.values());
        List<ModifyStockResult> response = new ArrayList<>();
        AtomicInteger excuteSize = new AtomicInteger();
        ThreadPool threadPool = ThreadPoolFactory.build(NameConstantEnum.STOCK_SYNC_TO_PLATFORM_THREAD_NAME.getName());
        threadPool.submitTaskList(need -> {
            try {
                List<ModifyStockResult> apiResponse = doSyncStock(task, need);
                if (CollectionUtils.isEmpty(apiResponse)) {
                    return apiResponse;
                }
                synchronized (response) {
                    response.addAll(apiResponse);
                    excuteSize.addAndGet(need.size());
                    processLogger.modifyMsg(String.format("网店[%s]本次选择%s条,已处理%s条",task.getOtype().getFullname(), modifyStockParams.size(), excuteSize), i);
                }
                return apiResponse;
            } catch (Exception e) {
                logger.error(String.format("给平台同步库存出错:%s  request:%s", e.getMessage(), JsonUtils.toJson(need)));
                return new ArrayList<>();
            }
        }, splitList);
        return response;
    }

    private List<ModifyStockResult> doSyncStock(DoStockSyncTask task, List<ModifyStockParam> modifyStockParams) {
        Otype otype = task.getOtype();
        EshopInfo eshopInfo = otype.getEshopInfo();
        ModifyStockQtyRequest request = new ModifyStockQtyRequest();
        request.setShopId(eshopInfo.getOtypeId());
        request.setModifyStockParams(modifyStockParams);
        request.setProcessLogger(task.getProcessLogger());
        String warehouseCode = task.getWarehouseCode();
        if (warehouseCode != null && !"".equals(warehouseCode)) {
            request.setModifyStockParams(request.getModifyStockParams().stream().peek(item -> item.setStockType(StockType.Warehose)).collect(Collectors.toList()));
            return doSyncStockByWarehouse(eshopInfo, request);
        }
        request.setModifyStockParams(request.getModifyStockParams().stream().peek(item -> item.setStockType(StockType.Generall)).collect(Collectors.toList()));
        return doSyncStockByEshop(eshopInfo, request);
    }

    private List<ModifyStockResult> doSyncStockByEshop(EshopInfo eshopInfo, ModifyStockQtyRequest request) {
        boolean supported = EshopUtils.isFeatureSupported(EshopStockGeneralFeature.class, eshopInfo.getEshopType());
        if (!supported) {
            throw new RuntimeException(String.format("【%s】平台暂不支持库存同步", eshopInfo.getFullname()));
        }
        ProductModifyStockResponse response = apiSvc.modifyProductStockQty(request);
        if (!response.getSuccess() && response.getMessage() != null) {
            throw new RuntimeException(String.format("库存同步调用接口报错：%s", response.getMessage()));
        }
        return response.getResultList();
    }

    private List<ModifyStockResult> doSyncStockByWarehouse(EshopInfo eshopInfo, ModifyStockQtyRequest request) {
        boolean supported = EshopUtils.isFeatureSupported(EshopStockWarehouseFeature.class, eshopInfo.getEshopType());
        if (!supported) {
            throw new RuntimeException(String.format("【%s】平台暂不支持分仓库存同步", eshopInfo.getFullname()));
        }
        ProductModifyStockResponse response = apiSvc.modifyProductStockQty(request);
        if (!response.getSuccess() && response.getMessage() != null) {
            throw new RuntimeException(String.format("库存同步调用接口报错：%s", response.getMessage()));
        }
        return response.getResultList();
    }

    private void buildTaskOtype(DoStockSyncTask task) {
        try {

            BigInteger eshopId = task.getEshopId();
            Otype otype = eshopService.getOtypeById(eshopId);
            if (otype == null)
            {
                throw new RuntimeException("找不到需要同步库存的网店信息");
            }
            task.setOtype(otype);
        } catch (Exception e) {
            throw new RuntimeException("找不到需要同步库存的机构信息或者网店的配置有问题");
        }
    }

    private List<ModifyStockParam> buildModifyParam(DoStockSyncTask task) {
        List<ModifyStockParam> result = new ArrayList<>();
        List<StockSyncManagePageData> syncDataList = task.getSyncDataList();
        if (CollectionUtils.isEmpty(syncDataList)) {
            throw new RuntimeException("没有需要同步的数据");
        }
        List<String> numIds = syncDataList.stream().filter(x -> x.getHasProperties() && !x.isHasQueryDetails()).map(StockSyncManagePageData::getPlatformNumId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(numIds)) {
            QueryStockPageDataParameter queryParameter = task.getQueryParameter();
            queryParameter.setPlatformNumIds(numIds);
            List<StockSyncManagePageData> detailList = queryProductDetailForStockSync(queryParameter);
            syncDataList.addAll(detailList);
        }
        //先把所有批次查询出现，然后使用skuId作为key，方便以后获取，提高性能
        QueryStockParameter queryBatchParam = new QueryStockParameter();
        queryBatchParam.setProfileId(CurrentUser.getProfileId());
        List<BigInteger> skuIdList = syncDataList.stream().map(StockSyncManagePageData::getSkuId).collect(Collectors.toList());
        queryBatchParam.setSkuIdList(skuIdList);
        boolean useConfig = stockSvc.GetIsOpenOneStockTakingConfig();
        queryBatchParam.setNeedStockDetail(useConfig);
        queryBatchParam.setNeedQueryLockRecord(syncDataList.stream().anyMatch(x -> x.getRuleType() != null && x.getRuleType().equals(StockRuleType.FrozenStock)));
        queryBatchParam.setCheckStockLimited(false);
        List<StockSaleQtyBatchEntity> batchSaleQtyList = stockBaseSvc.getBatchEnabled() ? qtyService.queryBatchSaleQtyList(queryBatchParam) : new ArrayList<>();
        Map<BigInteger, List<StockSaleQtyBatchEntity>> batchStockMap = batchSaleQtyList.stream().collect(Collectors.toMap(StockSaleQtyBatchEntity::getSkuId, x -> {
            List<StockSaleQtyBatchEntity> arr = new ArrayList<>();
            arr.add(x);
            return arr;
        }, (List<StockSaleQtyBatchEntity> value1, List<StockSaleQtyBatchEntity> value2) -> {
            value1.addAll(value2);
            return value1;
        }));
        List<String> numidList = syncDataList.stream().map(StockSyncManagePageData::getPlatformNumId).distinct().collect(Collectors.toList());
        HashMap<String, Object> warehoseNumidMaps = config.getSyncStockNeedWarehouseShopTypeList().contains(task.getOtype().getShopType().getCode()) ?
                getProductWarehoseMarkList(numidList, task.getEshopId()) : new HashMap<>();
        HashMap<String, Object> agingStockUniqueIdMaps = getProductAgingStockMarkList(numidList, task.getOtype());
        for (StockSyncManagePageData data : syncDataList) {
            if (data.getHasProperties() && data.getMainProduct()) {
                continue;
            }
            ModifyStockParam stockParam = doBuildModifyStockParam(data, batchStockMap);
            if (warehoseNumidMaps.containsKey(stockParam.getPlatformNumId())) {
                stockParam.setProductMarkEnumList(Arrays.asList(ProductMarkEnum.WAREHOUSE));
            }
            if (agingStockUniqueIdMaps.containsKey(data.getUniqueId())) {
                stockParam.setProductMarkEnumList(Arrays.asList(ProductMarkEnum.AGING_STOCK));
            }
            stockParam.setOpenOneStockTaking(useConfig);
            result.add(stockParam);
        }

        return result;
    }

    private ModifyStockParam doBuildModifyStockParam(StockSyncManagePageData data, Map<BigInteger, List<StockSaleQtyBatchEntity>> batchStockMap) {
        doWriteSyncActionLog(data);
        doWriteLadderSyncActionLog(data);
        ModifyStockParam parameter = new ModifyStockParam();
        parameter.setLocalXcode(data.getXcode());
        parameter.setLocalSkuId(data.getSkuId());
        parameter.setLocalNumId(data.getPtypeId());
        parameter.setPtypeName(data.getPtypeName());
        parameter.setUnitId(data.getUnitId());
        parameter.setPlatformNumId(data.getPlatformNumId());
        parameter.setPlatformSkuId(data.getPlatformSkuId());
        parameter.setPlatformXcode(data.getPlatformXcode());
        parameter.setDefaultSkuId(data.getPlatformDefaultSkuId());
        parameter.setWarehouseCode(data.getWarehouseCode());
        parameter.setEshopWareHouseType(data.getWarehouseType());
        parameter.setMappingType(data.getPcategory());
        parameter.setPlatformJson(data.getPlatformJson());
        parameter.setTargetType(data.getTargetType().getCode());
        parameter.setSyncType(1);
        parameter.setPlatformMainXcode(data.getPmplatformXcode());
        parameter.setBarCode(Collections.singletonList(data.getFullbarcode()));
        if (data.getTargetType().equals(StockRuleTargetTypeEnum.LADDER)) {
            parameter.setSyncLadderQtyEnabled(true);
        }
        doBuildSyncQtyParam(parameter, data);
        StockSyncRule syncRule = data.getStockSyncRule();
        if (syncRule != null) {
            parameter.setEshopName(syncRule.getEshopName());
        }
        //feat 同步药帮忙批次库存（其他平台可能也需要）
        if (batchStockMap != null && !batchStockMap.isEmpty()) {
            List<BigInteger> ktypeids = syncRule.getKtypeIdList();
            List<StockSaleQtyBatchEntity> batchQtyEntityList = batchStockMap.get(parameter.getLocalSkuId());
            List<StockValidityBatch> batchQtyList = new ArrayList<>();
            if (batchQtyEntityList != null && !batchQtyEntityList.isEmpty()) {
                batchQtyEntityList.forEach(batchQtyEntity -> {
                    if (!batchQtyEntity.hasBatchInfo()) {
                        return;
                    }
                    if (CollectionUtils.isEmpty(ktypeids) || !ktypeids.contains(batchQtyEntity.getKtypeId())) {
                        return;
                    }
                    StockValidityBatch batchSingle = new StockValidityBatch();
                    batchSingle.setBatchCode(batchQtyEntity.getBatchno());
                    batchSingle.setQty(batchQtyEntity.getSaleQty());
                    batchSingle.setProductDate(batchQtyEntity.getProduceDate());
                    batchSingle.setExpireDate(batchQtyEntity.getExpireDate());
                    batchQtyList.add(batchSingle);
                });
            }
            parameter.setStockValidityBatch(batchQtyList);

        }

        parameter.setMultiTimeStocks(stockBaseSvc.getMultiTimeStock(data.getMultiSetting()));
        return parameter;
    }

    private void buildProductMarkLis(String platformNumId, BigInteger eshopId, ModifyStockParam parameter) {
        int isNeedMark = productMapper.queryEshopProductmarkByMarkCode(CurrentUser.getProfileId(), null, EshopProductMarkEnum.WAREHOUSE.getCode(), eshopId, platformNumId);
        parameter.setProductMarkEnumList(isNeedMark > 0 ? Arrays.asList(ProductMarkEnum.WAREHOUSE) : new ArrayList<ProductMarkEnum>());
    }

    private HashMap<String, Object> getProductWarehoseMarkList(List<String> numidList, BigInteger eshopId) {
        HashMap<String, Object> result = new HashMap<>();
        if (CollectionUtils.isEmpty(numidList)) {
            return result;
        }
        List<List<String>> numidPages = CommonUtil.splitList(numidList, 500);
        for (List<String> itemNumids : numidPages) {
            List<EshopProductMark> needMarks = productMapper.queryEshopProductmarkByMarkCodeAndNumid(CurrentUser.getProfileId(), Arrays.asList(EshopProductMarkEnum.WAREHOUSE.getCode()), eshopId, itemNumids);
            if (CollectionUtils.isEmpty(needMarks)) {
                continue;
            }
            for (EshopProductMark itemMark : needMarks) {
                if (!result.containsKey(itemMark.getPlatformNumId())) {
                    result.put(itemMark.getPlatformNumId(), null);
                }
            }
        }
        return result;
    }

    private HashMap<String, Object> getProductAgingStockMarkList(List<String> numidList, Otype otype) {
        HashMap<String, Object> result = new HashMap<>();
        if (CollectionUtils.isEmpty(numidList) || !otype.getShopType().equals(ShopType.Doudian)) {
            return result;
        }
        List<List<String>> numidPages = CommonUtil.splitList(numidList, 500);
        for (List<String> itemNumids : numidPages) {
            List<EshopProductMark> needMarks = productMapper.queryEshopProductmarkByMarkCodeAndNumid(CurrentUser.getProfileId(), Arrays.asList(EshopProductMarkEnum.AGING_STOCK.getCode()), otype.getId(), itemNumids);
            if (CollectionUtils.isEmpty(needMarks)) {
                continue;
            }
            for (EshopProductMark itemMark : needMarks) {
                if (!result.containsKey(itemMark.getUniqueId())) {
                    result.put(itemMark.getUniqueId(), null);
                }
            }
        }
        return result;
    }

    private void doBuildSyncQtyParam(ModifyStockParam parameter, StockSyncManagePageData data) {
        parameter.setSyncQty(buildQtyParam(data.getSyncQty()));
        if (!data.getTargetType().equals(StockRuleTargetTypeEnum.LADDER) || data.getRealQty() == null) {
            return;
        }
        BigInteger realQty = buildQtyParam(data.getRealQty());
        BigInteger ladderQty = buildQtyParam(data.getLadderQty());
        parameter.setSyncLadderQty(ladderQty);
        parameter.setSyncQty(realQty);
    }

    private BigInteger buildQtyParam(String qty) {
        if (StringUtils.isEmpty(qty)) {
            return BigInteger.ZERO;
        } else if (new BigDecimal(qty).compareTo(BigDecimal.ZERO) <= 0) {
            return BigInteger.ZERO;
        } else {
            return new BigInteger(qty);
        }
    }

    private void doWriteSyncActionLog(StockSyncManagePageData data) {
        if (data.getTargetType().equals(StockRuleTargetTypeEnum.LADDER)) {
            return;
        }
        String body = "执行了手工库存同步";
        if (data.getSyncQty().compareTo(data.getSaleQty()) != 0) {
            body = String.format("手工同步数量从【%s】改为【%s】进行了同步", data.getSaleQty(), data.getSyncQty());
        }
        EshopStockSyncActionLog log = SysLogUtil.buildStockSyncActionLog(data);
        log.setBody(body);
        LogService.add(log);
    }

    private void doWriteLadderSyncActionLog(StockSyncManagePageData data) {
        if (!data.getTargetType().equals(StockRuleTargetTypeEnum.LADDER)) {
            return;
        }
        StringBuilder sb = new StringBuilder();
        if (!data.getSyncQty().equals(data.getRealQty())) {
            sb.append(String.format("手工将现货库存从【%s】改为【%s】进行了同步;", data.getSyncQty(), data.getRealQty()));
        }
        if (data.getSaleQty().equals(data.getLadderQty())) {
            sb.append(String.format("手工将阶梯库存从【%s】改为【%s】进行了同步;", data.getSaleQty(), data.getLadderQty()));
        }
        String body = sb.toString();
        if (StringUtils.isEmpty(body)) {
            body = "执行了手工库存同步";
        }
        EshopStockSyncActionLog log = SysLogUtil.buildStockSyncActionLog(data);
        log.setBody(body);
        LogService.add(log);
    }

    private void showResult(List<BusinessStockResult> results, ProcessLoggerImpl processLogger) {
        if (results == null || results.size() == 0) {
            processLogger.appendMsg("本次同步结束！");
            return;
        }
        int errorCount = 0;
        for (BusinessStockResult result : results) {
            if (result.getSyncState() == 0) {
                errorCount++;
                processLogger.appendMsg(String.format("%s同步库存失败：%s", result.getPtypeName(), result.getErrorMsg()));
            }
        }
        int successCount = results.size() - errorCount;
        processLogger.appendMsg(String.format("本次同步结束,失败%d条，成功%d条", errorCount, successCount));
    }
    private void showEshopResult(List<BusinessStockResult> results, ProcessLoggerImpl processLogger,String eshopName) {
        if (results == null || results.size() == 0) {
            return;
        }
        int errorCount = 0;
        for (BusinessStockResult result : results) {
            if (result.getSyncState() == 0) {
                errorCount++;
                processLogger.appendMsg(String.format("商品:%s %s同步库存失败：%s", result.getPtypeName(),
                        StringUtils.isEmpty(result.getWarehouseCode()) ? "" : String.format("门店:%s ", result.getWarehouseCode()),
                        result.getErrorMsg()));
            }
        }
        int successCount = results.size() - errorCount;
        processLogger.appendMsg(String.format("网店[%s]同步结束,失败%d条，成功%d条", eshopName,errorCount, successCount));
    }

    public List<StockSyncManagePageData> checkWarehouseType(List<StockSyncManagePageData> rowData) {
        List<StockSyncManagePageData> afterChenckRowData = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(rowData)) {
            BigInteger eshopId = rowData.get(0).getEshopId();
            QueryEShopParameter parameter = new QueryEShopParameter();
            parameter.setEshopId(rowData.get(0).getEshopId());
            EshopInfo eshopInfoById = eshopService.getEshopInfoById(parameter);
            if (null != eshopInfoById && eshopInfoById.getEshopType() == ShopType.Lst) {
                QueryProductMarkRequest markRequest = new QueryProductMarkRequest();
                markRequest.setProfileId(CurrentUser.getProfileId());
                markRequest.setEshopIds(Arrays.asList(eshopId));
                markRequest.setMarkCode(EshopProductMarkEnum.NOT_SYNC_STOCK.getCode());
                List<EshopProductMark> productsMarkByMarkCode = productmarkMapper.getProductsMarkByMarkCode(markRequest);
                List<String> uniqueIdList = productsMarkByMarkCode.stream().map(EshopProductMark::getUniqueId).collect(Collectors.toList());
                for (StockSyncManagePageData data : rowData) {
                    if (!uniqueIdList.contains(data.getUniqueId())) {
                        afterChenckRowData.add(data);
                    }
                }
            } else {
                return rowData;
            }
            return afterChenckRowData;
        } else {
            return new ArrayList<>();
        }
    }


    public String executeStockSyncNew(List<EshopProductSkuPageData> syncdatas) {
        DoStockSyncTask task = new DoStockSyncTask();
        String taskId = UId.newId().toString();
        ProcessLoggerImpl processLogger = new ProcessLoggerImpl(taskId);
        task.setTaskId(taskId);
        task.setProcessLogger(processLogger);
        task.setSyncDatas(syncdatas);
        ThreadPool threadPool = ThreadPoolFactory.build(NameConstantEnum.STOCK_SYNC_THREAD_NAME.getName());
        threadPool.executeAsync(this::executeNew, task);
        return taskId;
    }

    private void executeNew(DoStockSyncTask task) {
        ProcessLoggerImpl processLogger = task.getProcessLogger();
        try {
            List<EshopProductSkuPageData> syncDatas = task.getSyncDatas();
            processLogger.appendMsg("开始构建同步参数");
            SyncStockPercentSet(processLogger,"10");
            if (CollectionUtils.isEmpty(syncDatas)) {
                throw new RuntimeException("没有需要同步的数据");
            }
            List<EshopProductSkuPageData> syncDataList = checkNotSyncStock(syncDatas);
            SyncStockPercentSet(processLogger,"20");
            if (syncDataList.size() != syncDatas.size()) {
                processLogger.appendMsg("无需库存同步的网店商品已被过滤");
            }
            EshopProductMappingLogService.doWriteStockSyncLog(syncDataList);
            stockSvc.queryStockByPageData(syncDataList,true);
            Map<BigInteger,List<EshopProductSkuPageData>> shopSkuMap =  syncDataList.stream().collect(Collectors.groupingBy(EshopProductSkuPageData::getOtypeId));
            SyncStockPercentSet(processLogger,"40");
            int syncedCount = 0;
            processLogger.appendMsg(String.format("开始同步库存到网店后台"));
            for (Map.Entry<BigInteger, List<EshopProductSkuPageData>> entry : shopSkuMap.entrySet()) {
                BigInteger key = entry.getKey();
                List<EshopProductSkuPageData> value = entry.getValue();
                task.setEshopId(key);
                task.setSyncDataList(PageDatasToSyncDataList(value));
                buildTaskOtype(task);
                List<ModifyStockParam> modifyStockParams = buildProductSkuSyncData(value);
                List<ModifyStockResult> results = doThreadSyncStock(task,modifyStockParams,processLogger);
                if (CollectionUtils.isEmpty(results)) {
                    throw new RuntimeException("接口返回数据为空");
                }
                List<BusinessStockResult> businessResults = buildBizResult(results, task.getSyncDataList(), task);
                showEshopResult(businessResults, processLogger,task.getOtype().getFullname());
                stockBaseSvc.logManualResult(businessResults, task.getOtype());
                SyncStockPercentSet(processLogger,String.valueOf((syncedCount++*60/shopSkuMap.size())+40));
            }
        } catch (Exception ex) {
            String error = String.format("执行库存同步报错:%s", ex.getMessage());
            processLogger.appendMsg(error);
        } finally {
            processLogger.appendMsg("同步结束！");
            processLogger.doFinish();
            SyncStockPercentSet(processLogger,"100");
        }
    }
    private void SyncStockPercentSet(ProcessLoggerImpl processLogger,String Percent)
    {
        RedisMessageUtil.set(String.format("%s%s", EshopOrderConst.DOWNLOAD_ORDER_PROCESS_PERCENT_PRE,
                        processLogger.getLoggerKey()),Percent, 60000);
    }

    private List<StockSyncManagePageData> PageDatasToSyncDataList( List<EshopProductSkuPageData>  datas)
    {
        List<StockSyncManagePageData> syncDataList = new ArrayList<>();
        for (EshopProductSkuPageData data : datas) {
            syncDataList.add(data.toStockSyncManagePageData());
        }
        return syncDataList;
    }

    public List<EshopProductSkuPageData> checkNotSyncStock(List<EshopProductSkuPageData> datas) {
        if (CollectionUtils.isEmpty(datas)) {
            return datas;
        }
        List<EshopProductSkuPageData> afterChenckRowData = new ArrayList<>();
        List<BigInteger> eshopIds = datas.stream().map(EshopProductSkuPageData::getOtypeId).collect(Collectors.toList());
        List<String> uniqueIds = datas.stream().map(EshopProductSkuPageData::getUniqueId).collect(Collectors.toList());
        QueryProductMarkRequest markRequest = new QueryProductMarkRequest();
        markRequest.setProfileId(CurrentUser.getProfileId());
        markRequest.setEshopIds(eshopIds);
        markRequest.setUniqueIds(uniqueIds);
        markRequest.setMarkCode(EshopProductMarkEnum.NOT_SYNC_STOCK.getCode());
        List<EshopProductMark> codeList = productmarkMapper.getProductsMarkByMarkCode(markRequest);
        if (CollectionUtils.isEmpty(codeList)) {
            return datas;
        }
        List<String> uniqueIdList = codeList.stream().map(EshopProductMark::getUniqueId).collect(Collectors.toList());
        for (EshopProductSkuPageData data : datas) {
            if (!uniqueIdList.contains(data.getUniqueId())) {
                afterChenckRowData.add(data);
            }
        }
        return afterChenckRowData;
    }

    public List<ModifyStockParam> buildProductSkuSyncData(List<EshopProductSkuPageData> datas) {
        List<ModifyStockParam> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(datas)) {
            return result;
        }
        boolean openOneStockTaking = stockSvc.GetIsOpenOneStockTakingConfig();
        Map<BigInteger, List<StockSaleQtyBatchEntity>> batchStockMap = stockBaseSvc.getBatchEnabled() ? stockSvc.getSaleQtyBatchListMap(datas ,openOneStockTaking) : new HashMap<>();
        for (EshopProductSkuPageData data : datas) {
            doWriteSyncActionLog(data.toStockSyncManagePageData());
            ModifyStockParam stockParam = BuildModifyStockParam(data);
            if (CollectionUtils.isNotEmpty(data.getMultiTimeStocks())) {
                stockParam.setMultiTimeStocks(data.getMultiTimeStocks());
                stockParam.setProductMarkEnumList(Arrays.asList(ProductMarkEnum.AGING_STOCK));
            }
            if (batchStockMap.size()>0 && batchStockMap.containsKey(data.getSkuId()))
            {
                stockParam.setStockValidityBatch(BuildSaleQtyBatch(batchStockMap.get(data.getSkuId())));
            }
            stockParam.setOpenOneStockTaking(openOneStockTaking);
            result.add(stockParam);
            if (CollectionUtils.isNotEmpty(data.getWareHouseStocks())) {
                result.addAll(BuildWarehouseModifyStockParam(data, data.getWareHouseStocks()));
            }
        }
        return result;
    }

    private List<StockValidityBatch> BuildSaleQtyBatch(List<StockSaleQtyBatchEntity> batchQtyEntityList) {
        List<StockValidityBatch> batchQtyList = new ArrayList<>();
        if (CollectionUtils.isEmpty(batchQtyEntityList)) {
            return batchQtyList;
        }
        batchQtyEntityList.forEach(batchQtyEntity -> {
            if (!batchQtyEntity.hasBatchInfo()) {
                return;
            }
            StockValidityBatch batchSingle = new StockValidityBatch();
            batchSingle.setBatchCode(batchQtyEntity.getBatchno());
            batchSingle.setQty(batchQtyEntity.getSaleQty());
            batchSingle.setProductDate(batchQtyEntity.getProduceDate());
            batchSingle.setExpireDate(batchQtyEntity.getExpireDate());
            batchQtyList.add(batchSingle);
        });
        return batchQtyList;
    }


    private ModifyStockParam BuildModifyStockParam(EshopProductSkuPageData data) {
        ModifyStockParam parameter = new ModifyStockParam();
        parameter.setLocalXcode(data.getXcode());
        parameter.setLocalSkuId(data.getSkuId());
        parameter.setLocalNumId(data.getPtypeId());
        parameter.setPtypeName(data.getPtypeName());
        parameter.setUnitId(data.getUnitId());
        parameter.setPlatformNumId(data.getPlatformNumId());
        parameter.setPlatformSkuId(data.getPlatformSkuId());
        parameter.setPlatformXcode(data.getPlatformXcode());
        parameter.setDefaultSkuId(data.getPlatformSkuId());
        parameter.setMappingType(data.getPcategory().getCode());
        parameter.setTargetType(StockRuleTargetTypeEnum.NORMAL.getCode());
        parameter.setSyncType(1);
        parameter.setPlatformMainXcode(data.getPmXcode());
        parameter.setBarCode(Arrays.asList(data.getSkuBarcode()));
        parameter.setEshopName(data.getEshopName());
        parameter.setStockType(StockType.Generall);
        parameter.setPlatformJson(data.getPlatformJson());
        parameter.setSyncQty(data.getQty().toBigInteger());
        return parameter;
    }

    private List<ModifyStockParam> BuildWarehouseModifyStockParam(EshopProductSkuPageData data, List<WareHouseStockSync> warehouses) {
        List<ModifyStockParam> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(warehouses)) {
            return result;
        }
        for (WareHouseStockSync warehouse : warehouses) {
            ModifyStockParam stockParam = BuildModifyStockParam(data);
            stockParam.setTargetType(StockRuleTargetTypeEnum.WAREHOUSE.getCode());
            stockParam.setWarehouseCode(warehouse.getWarehouseCode());
            stockParam.setEshopWareHouseType(warehouse.getWarehouseType());
            stockParam.setStockType(StockType.Warehose);
            stockParam.setSyncQty(BigInteger.valueOf(warehouse.getSyncQty()));
            if (CollectionUtils.isNotEmpty(data.getMarkList()) && data.getMarkList().stream().filter(x->x.getMarkCode() == ProductMarkEnum.WAREHOUSE.getCode()).findAny().isPresent())
            {
                stockParam.setProductMarkEnumList(Arrays.asList(ProductMarkEnum.WAREHOUSE));
            }
            result.add(stockParam);
        }
        return result;
    }

    static class DoStockSyncTask {
        private String taskId;
        private BigInteger eshopId;
        private Otype otype;
        private String warehouseCode;
        private List<StockSyncManagePageData> syncDataList;
        //新版本库存同步规则
        private List<EshopProductSkuPageData> syncDatas;
        private QueryStockPageDataParameter queryParameter;
        private ProcessLoggerImpl processLogger;

        public String getTaskId() {
            return taskId;
        }

        public void setTaskId(String taskId) {
            this.taskId = taskId;
        }

        public List<StockSyncManagePageData> getSyncDataList() {
            return syncDataList;
        }

        public void setSyncDataList(List<StockSyncManagePageData> syncDataList) {
            this.syncDataList = syncDataList;
        }

        public ProcessLoggerImpl getProcessLogger() {
            return processLogger;
        }

        public void setProcessLogger(ProcessLoggerImpl processLogger) {
            this.processLogger = processLogger;
        }

        public Otype getOtype() {
            return otype;
        }

        public void setOtype(Otype otype) {
            this.otype = otype;
        }

        public BigInteger getEshopId() {
            return eshopId;
        }

        public void setEshopId(BigInteger eshopId) {
            this.eshopId = eshopId;
        }

        public String getWarehouseCode() {
            return warehouseCode;
        }

        public void setWarehouseCode(String warehouseCode) {
            this.warehouseCode = warehouseCode;
        }

        public QueryStockPageDataParameter getQueryParameter() {
            return queryParameter;
        }

        public void setQueryParameter(QueryStockPageDataParameter queryParameter) {
            this.queryParameter = queryParameter;
        }

        public List<EshopProductSkuPageData> getSyncDatas() {
            return syncDatas;
        }

        public void setSyncDatas(List<EshopProductSkuPageData> syncDatas) {
            this.syncDatas = syncDatas;
        }
    }
}

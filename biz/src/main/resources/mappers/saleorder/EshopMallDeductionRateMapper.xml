<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopMallDeductionRateMapper">
    <sql id="main_mall_deduction">
        `eshop`.fullname as eshopFullName,
        `eshop`.otype_id,
        `eshop`.profile_id,
        `mall_deduction`.category_id as platform_class_id,
        ifnull(`mall_deduction`.category_name,`class`.platform_class_name) as platform_class_name,
        `mall_deduction`.id,
        `mall_deduction`.mall_deduction_rate,
        `mall_deduction`.full_category_name,
        `mall_deduction`.parent_category_id
    </sql>
    <sql id="mall_deduction_jointable">
        <PERSON><PERSON><PERSON> pl_eshop `eshop` ON `mall_deduction`.profile_id = `eshop`.profile_id AND `mall_deduction`.eshop_id = `eshop`.otype_id
        LEFT JOIN pl_eshop_product_class `class` ON `class`.profile_id = `mall_deduction`.profile_id AND `class`.eshop_id = `mall_deduction`.eshop_id AND
        `class`.platform_class_id = `mall_deduction`.category_id
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=`mall_deduction`.profile_id and object_type=3 and
            `mall_deduction`.eshop_id = bls.object_id and bls.etype_id = #{etypeId}
        </if>
    </sql>

    <sql id="mall_deduction_filter">
        <if test="filterParameter != null">
            <if test="filterParameter.eshopFullName != null and filterParameter.eshopFullName != ''">
                AND `eshop`.fullname like CONCAT('%',#{filterParameter.eshopFullName},'%')
            </if>
            <if test="filterParameter.platformClassName != null and filterParameter.platformClassName != '' ">
                AND (`mall_deduction`.category_name like CONCAT('%',#{filterParameter.platformClassName},'%')
                or `class`.platform_class_name like CONCAT('%',#{filterParameter.platformClassName},'%')
                )
            </if>
            <if test="filterParameter.platformClassId != null  and filterParameter.platformClassId != ''">
                AND `mall_deduction`.category_id = #{filterParameter.platformClassId}
            </if>
            <if test="filterParameter.fullCategoryName != null  and filterParameter.fullCategoryName != ''">
                AND `mall_deduction`.full_category_name like CONCAT('%', #{filterParameter.fullCategoryName},'%')
            </if>
        </if>
    </sql>

    <select id="queryMallDeductionRateConfigList"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryMallDeductionConfigParameter"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.malldeduction.MallDeductionConfigEntity">
        SELECT
        <include refid="main_mall_deduction"></include>
        FROM pl_eshop_mall_deduction_rate_config `mall_deduction`
        <include refid="mall_deduction_jointable"/>
        WHERE `mall_deduction`.profile_id = #{profileId}
        <if test="platformTypes !=null and platformTypes.size()>0">
            AND `eshop`.eshop_type IN
            <foreach collection="platformTypes" close=")" open="(" separator="," item="platformType">
                #{platformType}
            </foreach>
        </if>
        <if test="otypeIds !=null and otypeIds.size()>0">
            AND `mall_deduction`.eshop_id IN
            <foreach collection="otypeIds" close=")" open="(" separator="," item="otypeId">
                #{otypeId}
            </foreach>
        </if>
        <include refid="mall_deduction_filter"/>
    </select>

    <select id="queryMallDeductionRateConfigsCount"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryMallDeductionConfigParameter"
            resultType="java.lang.Integer">
        SELECT count(1)
        FROM pl_eshop_mall_deduction_rate_config `mall_deduction`
        <include refid="mall_deduction_jointable"/>
        WHERE `mall_deduction`.profile_id = #{profileId}
        <if test="platformTypes !=null and platformTypes.size()>0">
            AND `eshop`.eshop_type IN
            <foreach collection="platformTypes" close=")" open="(" separator="," item="platformType">
                #{platformType}
            </foreach>
        </if>
        <if test="otypeIds !=null and otypeIds.size()>0">
            AND `mall_deduction`.eshop_id IN
            <foreach collection="otypeIds" close=")" open="(" separator="," item="otypeId">
                #{otypeId}
            </foreach>
        </if>
        <include refid="mall_deduction_filter"/>
    </select>

    <select id="queryMallDeductionRateByEshopIdAndParams"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryMallDeductionConfigParameter"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.malldeduction.MallDeductionConfigEntity">
        SELECT profile_id,eshop_id,category_id as platformClassId,mall_deduction_rate,parent_category_id,category_name,full_category_name
        FROM
        pl_eshop_mall_deduction_rate_config
        WHERE profile_id = #{profileId} and eshop_id = #{otypeId}
        <if test="categoryIds !=null and categoryIds.size()>0">
            AND category_id IN
            <foreach collection="categoryIds" close=")" open="(" separator="," item="categoryId">
                #{categoryId}
            </foreach>
        </if>
        <if test="otypeIds !=null and otypeIds.size()>0">
            AND eshop_id IN
            <foreach collection="otypeIds" close=")" open="(" separator="," item="otypeId">
                #{otypeId}
            </foreach>
        </if>
    </select>

    <select id="queryExistMallDeductionRateByEshopIdAndParams"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryMallDeductionConfigParameter"
            resultType="java.lang.Integer">
        SELECT count(0) FROM
        pl_eshop_mall_deduction_rate_config
        WHERE profile_id = #{profileId}
        <if test="categoryIds !=null and categoryIds.size()>0">
            AND category_id IN
            <foreach collection="categoryIds" close=")" open="(" separator="," item="categoryId">
                #{categoryId}
            </foreach>
        </if>
        <if test="otypeIds !=null and otypeIds.size()>0">
            AND eshop_id IN
            <foreach collection="otypeIds" close=")" open="(" separator="," item="otypeId">
                #{otypeId}
            </foreach>
        </if>
        <if test="otypeId!=null and otypeId>0">
            AND eshop_id= #{otypeId}
        </if>
    </select>

    <insert id="batchAddMallDeductionConfigs">
        REPLACE INTO pl_eshop_mall_deduction_rate_config(id,profile_id,eshop_id,category_id,mall_deduction_rate,parent_category_id,category_name,full_category_name)
        VALUES
        <foreach item="item" index="index" collection="mallDeductionConfigs" separator=",">
            (#{item.id}, #{item.profileId}, #{item.otypeId}, #{item.platformClassId}, #{item.mallDeductionRate}, #{item.parentCategoryId}, #{item.platformClassName}, #{item.fullCategoryName})
        </foreach>
    </insert>


    <insert id="batchUpdateMallFeeRateConfig">
        INSERT INTO
        pl_eshop_mall_deduction_rate_config(id,profile_id,eshop_id,category_id,mall_deduction_rate,parent_category_id,category_name,full_category_name)
        VALUES
        <foreach item="item" index="index" collection="mallDeductionConfigs" separator=",">
            (#{item.id}, #{item.profileId}, #{item.otypeId}, #{item.platformClassId}, #{item.mallDeductionRate},
            #{item.parentCategoryId}, #{item.platformClassName}, #{item.fullCategoryName})
        </foreach>
        ON DUPLICATE KEY
        UPDATE
        `parent_category_id`=VALUES(parent_category_id),
        `category_name`=VALUES(category_name),
        `mall_deduction_rate`=IF(pl_eshop_mall_deduction_rate_config.mall_deduction_rate!=
        VALUES(mall_deduction_rate) and VALUES(mall_deduction_rate)>0, VALUES(mall_deduction_rate), pl_eshop_mall_deduction_rate_config.mall_deduction_rate),
        `full_category_name`=VALUES(full_category_name)
    </insert>

    <delete id="batchDeleteMallDeductionConfigs">
        delete from pl_eshop_mall_deduction_rate_config where profile_id=#{profileId}
        and id IN
        <foreach collection="mallFeeIds" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>

    </delete>
</mapper>
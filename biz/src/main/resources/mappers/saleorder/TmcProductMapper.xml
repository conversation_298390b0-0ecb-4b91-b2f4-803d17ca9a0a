<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.TmcProductMapper">
    <insert id="insertProductMessageChange">
        INSERT INTO `pl_eshop_product_notify_change` (`id`,
                                              `profile_id`,
                                              `eshop_id`,
                                              `content`,
                                              `type`)
        VALUES (#{id},
                #{profileId},
                #{eshopId},
                #{content},
                #{type});
    </insert>

    <select id="queryProductMessageChange" resultType="com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange">
        select `id`, `profile_id`,`eshop_id`, `content`,`type`,`create_time`
        from pl_eshop_product_notify_change
        where profile_id = #{profileId} and eshop_id = #{otypeId}
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<Page xmlns="Craba.UI" Title="${title}" MinHeight="720" MinWidth="720" CssClass='BasePage BaseInfo EshopAddPage pd0 mr0'
      OnClose="doFormClose"
      ActionType="sale.eshoporder.eshop.EshopAddAction, sale/eshoporder/eshop/EshopAdd.js" DataSource="${datasource}"
      AllowResize="false">
    <HiddenField ID="fieldInfos" Value="${fieldInfos}"/>
    <HiddenField ID="shopAccountName"/>
    <HiddenField ID="orderLink" DataField="orderLink"/>
    <HiddenField ID="authType" DataField="authType"/>
    <HiddenField ID="allowBuyerAccount"/>
    <HiddenField ID="typeId" Value="${typeId}"/>


    <FlexColumn>
        <FlexColumn ID="getShopType" CssClass="pd0">
            <HPanel CssClass="mr0 pd0"  CssStyle="margin-left:30px; margin-top:10px">
                <Label CssClass="FlexLeft"  Width="340" Height="30" Text="选择平台店铺类型" CssStyle="margin-left:5px;font-weight:bold;font-size:14px;"/>
                <Block CssClass='Flex1'/>
                <TextEdit CssClass="FlexRight" ID="keyWord" Width="300" OnEnterPress="btnQueryOnClick" OnTextChanged="btnQueryOnClick"
                          NullDisplayText="淘宝，京东，拼多多等"/>
            </HPanel>
            <VPanel Height="495" CssStyle="width:95%;margin-top:10px; margin-left:30px" VAlign="Top">
                <ListView ID="eshopView" CssClass="ListBlockItem NoBg Wrap mr0 pd0" OnItemClick="chooseShopType"
                          OnItemRendering="handleRendering">
                    <ListTemplate CssClass="SkinBorderHover">
                        <DataImage DataField="url"/>
                        <DataText DataField="name" CssClass="name" Width="90"/>
                    </ListTemplate>
                </ListView>
            </VPanel>
            <HBlock HAlign="Center">
                <Label Text="如未找到您要的平台，请"/>
                <Label CssClass="SkinColor" Text="前往反馈" OnClick="doFeedbackUrl"/>
            </HBlock>
        </FlexColumn>

        <FlexColumn ID="eshopConfig" Visible="false" CssClass="pd0 mr0 Flex0"  CssStyle="margin-left:20px;margin-right:20px">
            <FlowPanel ColSpan='2' ItemLabelWidth="125" Width="680" CssStyle="padding-top:16px;">
                <TextEdit ID="orgName" DataField="otypeFullname" Label="网店名称:" AllowTags='true'
                          LabelCssClass="MustCharLeft just-content-right"
                          Required="true" NullDisplayText="网店名称不能重复"
                          MaxLength="100"/>
                <SelectorEdit ID="edKType" Label="发货仓库:" DataField="ktypeId" MaxLength="100"
                              NullDisplayText="请选择发货仓库"
                              OnButtonClick="onKtypeInit" DisplayField="ktypeName" LabelCssClass="MustCharLeft just-content-right"
                              OnEnterPress="doEnterPress" OnSelectorSelected="doBaseInfoSelect" Required="true"
                              SelectOnly="true"/>
                <DropDownEdit ID="dpParTypeId" DataField="typeid" Label="选择分类:" LabelCssClass="MustCharLeft just-content-right"
                              DataSource="${partypes}" DataTextField="fullname" DataValueField="typeid"
                              DropDownStyle="DropDownList" Required="true" Visible="${allowShowEshopClass}"/>
                <TextEdit ID="xnshopAccount" DataField="eshopAccount" Label="网店账号" Required="true" AllowTags='true'
                          Visible="false"
                          LabelCssClass="MustCharLeft just-content-right" NullDisplayText="请填写网店账号"
                          MaxLength="100"/>
            </FlowPanel>
            <Label Visible="false" ID="remind"
                   Text="连锁网店，除网店名称外的网店设置，都将默认设置到子店，如果独立设置，请在网店列表选择子店的功能设置中修改。"
                   CssStyle="color:red; margin-left:10px" CssClass="MustCharLeft just-content-right"/>
        </FlexColumn>

        <FlexColumn ID="auth" Visible="false"  CssClass="pd0 mr0"  CssStyle="margin-left:20px;margin-right:20px">
            <Label Text="授权信息" CssClass='LabelTitle' FontSize="13"/>
            <FlowPanel ColSpan='2' ItemLabelWidth="125" Width="680" CssStyle="margin-top:10px">
                <DropDownEdit ID="platformauthType" Label="平台授权类型" LabelCssClass="MustCharLeft just-content-right"
                              DropDownStyle="DropDownList"
                              DataField="platformauthType" ListItems="0=单店授权,1=连锁网店授权"
                              Onchange="handlePlatformuthType"
                              DataTextField="text" DataValueField="value" Required="true"
                              Visible="false"/>
                <TextEdit ID="shopAccount" DataField="eshopAccount" Label="网店账号" Required="true" AllowTags='true'
                          Visible="${!powers.isWholesale}"
                          LabelCssClass="MustCharLeft just-content-right" NullDisplayText="请填写网店账号"
                          MaxLength="100" OnChange="doShopAccountChange"/>
                <DropDownEdit ID="platformEshopSnType" Label="序列号类型" LabelCssClass="MustCharLeft just-content-right"
                              DataField="platformEshopSnType" DataSource="${dropdownDataSource}"
                              DataTextField="text" DataValueField="value" Required="true"
                              Visible="false"/>
                <TextEdit ID="vendorId" DataField="vendorId" Label="供应商id" Required="true"
                          LabelCssClass="MustCharLeft just-content-right" Visible="false"
                          MaxLength="200"/>
                <TextEdit ID="platformEshopId" DataField="platformEshopId" Label="常态合作编码"
                          Visible="false" LabelCssClass="just-content-right"
                          MaxLength="250"/>
                <TextEdit ID="onlineEshopId" DataField="onlineEshopId" Label=""
                          Visible="false"  LabelCssClass="just-content-right"
                          MaxLength="250"/>
                <TextEdit Label="AppKey" ID="appKey" DataField="appKey" Required="true" Visible="true"
                          CssStyle="width: 312px"
                          LabelCssClass="MustCharLeft just-content-right" MaxLength="200"/>
                <TextEdit Label="AppSecret" ID="appSecret" DataField="appSecret" Required="true" CssStyle="width: 312px"
                          Visible="true"  MaxLength="1000"  LabelCssClass="just-content-right"/>
                <HBlock ID="tmallSpecialSale" Label='天猫超市特卖业务:'>
                    <RadioButton ID="radio1" Width="100" GroupName="isTmallSpecialSale" Checked="true"
                                 OnChange="showToken" Text="否"/>
                    <RadioButton ID="radio2" Width="100" GroupName="isTmallSpecialSale" OnChange="showToken" Text="是"/>
                </HBlock>
                <TextEdit ID="token" DataField="token" Label="授权码" Required="true"
                          LabelCssClass="MustCharLeft just-content-right" Visible="false"
                          MaxLength="200"/>
            </FlowPanel>

            <HBlock ID="usualAuth" CssClass="FlexWrap pd0 SkinColor SkinBorder ">
                <Block CssClass='Card pd10' ID="cbxOrderhp">
                    <HBlock CssClass="dflexv" CssStyle="padding:0; ">
                        <Label CssClass="LabelCardTitle dinggou" CssStyle="margin:0; " Text='订购'/>
                        <Label ID="lblAfterDGRemark" CssClass="afterdinggou"
                               Text="请使用该网店主账号登录服务市场进行订购"
                               Hint="请使用该网店主账号登录服务市场进行订购"/>
                    </HBlock>
                        <Label CssClass="LabelRemark" Text='将跳转销售平台的服务市场，在销售平台订购管家婆ERP后,支付成功后再授权'/>
                    <Button CssClass="LabelBtn SkinColor SkinBorder" Text='去订购' ID="btnOrder" OnClick="doOrder"/>
                </Block>
                <Block CssClass='Card pd10' CssStyle="margin-left:10px;" ID="cbxAuthhp">
                    <Label CssClass="LabelCardTitle shouquan"  Text='授权'/>
                    <Label CssStyle="position:absolute;right:5px;top:5px;color:#54bb15" CssClass="aicon-duigou"
                           Text="已授权" Visible="false" ID="iconAuth"/>
                    <Label CssClass="LabelRemark" Text='将跳转销售平台授权页面，输入网店主账号完成授权'/>
                    <Button CssClass="LabelBtn SkinColor SkinBorder" Text='去授权' ID="btnAuth" OnClick="doAuth"
                           Enabled="${needAuth}"/>
                </Block>
            </HBlock>
            <FlexBlock ID="threeStepAuth" CssClass="FlexWrap">
                <Block CssClass='Card' ID="cbxInfo">
                    <Label CssClass="LabelCardTitle dinggou" Text='发起授权申请'/>
                    <Label CssClass="LabelRemark" Text='复制京东秒送网店id给服务顾问，发起授权申请'/>
                </Block>
                <Block CssClass='Card' ID="cbxSendAuth">
                    <Label CssClass="LabelCardTitle shouquan" Text='建立授权'/>
                    <Label CssClass="LabelRemark"
                           Text='到京东秒送开放平台，同意我们发起的授权，并且向我们操作发送授权码。发送授权码后，请耐心等待成功'/>
                    <Button CssClass="LabelBtn SkinColor SkinBorder" Text='同意授权并发送授权码' ID="btnSendAuth" OnClick="doAuth"/>
                </Block>
                <Block CssClass='Card' ID="cbxSaveAuth">
                    <Label CssClass="LabelCardTitle shouquanEnd" Text='保存授权'/>
                    <Label CssStyle="position:absolute;right:5px;top:5px;color:#54bb15" CssClass="aicon-duigou"
                           Text="已授权" Visible="${auth}" ID="iconAuth2"/>
                    <Label CssClass="LabelRemark" Text='建立授权后，京东秒送平台需要额外保存授权'/>
                    <Button CssClass="LabelBtn SkinColor SkinBorder" Text='保存授权' ID="btnSendAuth2" OnClick="doInderectAuth"
                           Enabled="${needAuth}"/>
                </Block>
            </FlexBlock>
            <Block CssClass="selBox pd10" ID="selectAppkey" Visible="false">
                <Label CssClass="selTitle" Text='请选择要订购的应用'/>
                <Block CssClass="selBtns" ID="selectBtns">
                </Block>
                <Block CssClass='OtherTipBox' ID='Box'>
                    <Block CssClass='Arrow1' ID='arrow1'/>
                    <Block CssClass='Arrow2' ID='arrow2'/>

                    <Block CssClass="TipsBox">
                        <Label CssClass="TipsIcon" Text='*'/>
                        <Label CssClass="Tipsdescription" ID="selectdescription" Text=''/>
                    </Block>
                    <Block CssClass="Line"/>
                    <Block CssClass='ProcessBox'>
                        <Label CssClass="ProcessIcon" Text='①'/>
                        <Label CssClass="ProcessItem" Text='订购'/>
                        <Button CssClass="ProcessBtn SkinColor SkinBorder" Text='去订购' OnClick="doOrder" ID="btnUsualOrder"/>
                        <Label ID="lblAfterDingGouRemark"
                               CssStyle="position:absolute;left:175px;top:10px;color:#B22222;font-size:10px"
                               Text="请使用该网店主账号登录服务市场进行订购"
                               Hint="请使用该网店主账号登录服务市场进行订购"/>
                        <Label CssClass="ProcessLabel"
                               Text='将跳转销售平台的服务市场，在销售平台订购管家婆ERP后，支付成功后再授权'/>
                    </Block>
                    <Block CssClass='ProcessBox'>
                        <Label CssClass="ProcessIcon" Text='②'/>
                        <Label CssClass="ProcessItem" Text='授权'/>
                        <Button CssClass="ProcessBtn SkinColor SkinBorder" Text='${authText}' OnClick="doAuth"
                                Enabled="${needAuth}"/>
                        <Label CssStyle="position:absolute;right:5px;top:5px;color:#54bb15" CssClass="aicon-duigou"
                               Text="已授权" Visible="${auth}"/>
                        <Label CssClass="ProcessLabel" Text='将跳转销售平台授权页面，输入网店主账号完成授权'/>
                    </Block>
                </Block>
            </Block>
            <FlowPanel ID="BranchStorePanel" ColSpan='1' CssClass="pd10 mr10"
                       CssStyle="border: 0.5px solid;border-color: #********; margin-left:10px" LayoutDirection="Vert"
                       Visible="false">
                <Label ItemCssClass="mt-o" CssStyle="font-weight: bold;height: 12px;" CssClass="mr0"
                       Text="第三步：选择要授权的子店"/>
                <Label ItemCssClass="mt-o" Text="完成授权后，才能获取并选择" CssStyle="color:#********;height: 12px;"
                       CssClass="mr0"/>
                <HBlock>
                    <Button CssStyle="text-align: center; font-size:11px;border-radius:4px;" CssClass="mr0 pd0"
                            Text="启用" OnClick="doOpenEshop" Width="35" Height="20" Enabled="eshoporder.eshop.open"/>
                    <Button CssStyle="text-align: center; font-size:11px;border-radius:4px;margin-left:10px"
                            CssClass="mr10 pd0" Text="停用" OnClick="doStopEshop" Width="35" Height="20"
                            Enabled="eshoporder.eshop.stop"/>
                    <Button CssStyle="text-align: center; font-size:11px;border-radius:4px;margin-left:10px"
                            CssClass="mr10 pd0" Text="拉取子店" OnClick="getBranchEshop" Width="65" Height="20"/>
                </HBlock>
                <Grid ID="grid" PrimaryKey="eshopAccount" Height="150" Width="667" ReadOnly="false" NeedRowIndex="true">
                    <MultiSelectColumn ShowHeaderCheckBox="true" AllowConfig="false"
                                       AllowSort="false" AllowFilter="false" ReportVisible="false"/>
                    <SwitchButtonColumn DataField="enabled" AllowStretch="true" Caption="是否启用"
                                        CssClass="MySwitchColumn"
                                        CheckedValue='true'/>
                    <TextColumn Caption="平台商户ID" DataField="eshopAccount" ReadOnly="true" AllowStretch="true"
                                OnClick="doEditEshopAccount" Enabled="false"/>
                    <TextColumn Caption="平台商户名称" DataField="fullname" ReadOnly="true" AllowStretch="true"/>
                </Grid>
            </FlowPanel>
            <FlowPanel ID="BranchStorePanelMT" ColSpan='1' CssClass="pd10 mr10"
                       CssStyle="border: 0.5px solid;border-color: #********; margin-left:10px" LayoutDirection="Vert"
                       Visible="false">
                <Label ItemCssClass="mt-o" CssStyle="font-weight: bold;height: 12px;" CssClass="mr0"
                       Text="授权连锁的子店"/>
                <Label ItemCssClass="mt-o" Text="请添加后，完成订购并授权" CssStyle="color:#********;height: 12px;"
                       CssClass="mr0"/>
                <Button CssStyle="text-align: center; font-size:11px;border-radius:4px;" CssClass="mr0 pd0" Text="导入"
                        OnClick="doExport" Width="35" Height="20" Enabled="eshoporder.eshop.open"/>
                <Grid ID="MTgrid" Height="150" Width="667" DefaultRowCount="1" NeedRowIndex="true" ReadOnly="false">
                    <IconColumn Caption='操作' DataField='icon' ListItems='=aicon-zengjia,=aicon-jian'
                                OnClick="doIconColumnClick"/>
                    <TextColumn Caption="APP方门店ID" DataField="eshopAccount" AllowStretch="true" MaxLength="30"/>
                    <TextColumn Caption="子店名称" DataField="fullname" AllowStretch="true" MaxLength="100"/>
                    <DremindynamicButtonColumn Width="180" TextAlign="Left" Caption="订购" DataField="operation"
                                               OnButtonClick="doOrder"
                                               LayoutDirection="Horz" AllowStretch="true" ReadOnly="true"
                                               AllowSort="false"
                                               AllowConfig="false"
                    />
                    <DynamicButtonColumn Width="180" TextAlign="Left" Caption="授权" DataField="auth"
                                         OnButtonClick="doAuth"
                                         LayoutDirection="Horz" AllowStretch="true" ReadOnly="true" AllowSort="false"
                                         AllowConfig="false"
                    />
                </Grid>
            </FlowPanel>
            <Block CssClass='Flex1'/>
        </FlexColumn>
        <HBlock ID="shouQuanStep" CssClass="BottomBlock FlexRight">
            <HBlock CssClass="FlexLeft" CssStyle="margin-right:20px">
                <Label CssClass="SkinColor SkinBorder bicon-shouquanzhiyin" Width="120" Height="30"
                       CssStyle="border: 1px solid #2288FC;border-radius: 15px;padding-left:17px;font-size:12px;"
                       Text="授权教程" OnClick="openUrl"/>
            </HBlock>
            <Block CssClass='Flex1'/>
            <Button Text="上一步" OnClick="doback"/>
            <Button Text="下一步" CssClass="SpecialButton" OnClick="doNext"/>
        </HBlock>
        <FlexColumn ID="createSuccess" Visible="false">
            <FlexBlock ID="success" LayoutDirection="Vert" CssClass="FlexCenter pd10">
                <Image Url="sale/eshoporder/image/success.svg" Width="50" Height="50"/>
                <Label Text="网店添加成功" FontSize="16" CssStyle="margin-top:13px" BoldFont="true" CssClass="mr0 pd0"/>
                <Label Text="更多网店功能设置，可在【店铺设置】功能中开启" CssStyle="margin-top:10px" CssClass="mr0 pd0"
                       FontSize="12"/>
                <HBlock CssStyle="margin-top:10px">
                    <Block CssClass='Flex1'/>
                    <Button Text="店铺设置" OnClick="shopConfig"/>
                    <Button Text="完成" CssClass="SpecialButton" OnClick="doOk"/>
                </HBlock>
            </FlexBlock>
        </FlexColumn>

        <FlexColumn ID="XNShopStep" Visible="false">
            <Block CssClass='Flex1'/>
            <HBlock CssClass="BottomBlock FlexRight">
                <Block CssClass='Flex1'/>
                <Button Text="完成" CssClass="SpecialButton" OnClick="doOk"/>
            </HBlock>
        </FlexColumn>
    </FlexColumn>


    <CustomControl ID='import'>
        <Label Text="导入说明："/>
        <Label Text="1、导入文件需要录入 app 方门店 ID 和子网店名称。"/>
        <Label Text="2、app 方门店 ID 和子网店名称，任意一个已经存在了，都无法导入该条数据。"/>
        <SpeedButton CssStyle="text-align: left;" TextColor="Blue" Text="下载模板" OnClick="downloadRealtionModel"
                     Hint="下载系统默认支持的模板"/>
        <FileUpload WebMethod="sale/eshoporder/eshop/importBranchEshop">
            <HPanel ID="fileUpload">
                <FileEdit Label="选择文件：" ID="importFile" Accept=".xls,.xlsx" DataField="importFile" Width="200"/>
                <Button ID="submitBtn" Text="开始上传" OnClick="uploadFile" Tag="fileUpload"/>
            </HPanel>
        </FileUpload>
        <HBlock CssClass='BottomBlock'/>
        <HBlock>
            <ProgressBar ID="processBar" Value="0"/>
        </HBlock>
        <MemoEdit ID="info" Height="200" Width="100%" Rows="10000" ReadOnly="true" TabStop="true"
                  CssStyle="border: 0.5px solid;border-color: #********;text-indent:0;padding-left:5px"/>
    </CustomControl>
    <Style>
        .ListBlockItem{
        flex-grow:0
        }
        .ListBlockItem .UI_ListView{
        <!--        justify-content:space-between;-->
        }
        .ListBlockItem .UI_ListItem:last-child{
        margin-right:10px;}

        .ListBlockItem .UI_ListItem {
        height:100px;
        min-width:100px;
        border-radius: 8px;
        border: 1px solid #DDDDDD;
        align-items: center;
        justify-content: space-between;
        padding: 0;
        padding-top: 20px;
        overflow: hidden;
        position: relative;
        flex:0;
        }
        .pd16 {
        padding: 16px !important;
        }
        .pd20left {
        padding-left: 20px !important;
        padding: 5px;
        }
        .ListBlockItem .UI_ListItem.new::before{
        content: '新';
        display: flex;
        align-items: center;
        justify-content: center;
        position: absolute;
        left: 0;
        top: 0;
        width: 20px;
        height:20px;
        background: #00B51F;
        border-radius: 7px 0px 4px 0px;
        color: #fff;
        }

        .ListBlockItem .UI_ListItem .name {
        display: inline-block;
        font-size: 12px;
        width: 100%;
        text-align: center;
        margin-bottom: 12px;
        }

        .ListBlockItem .UI_ListItem img {
        width: 90px;
        height: 80px;
        margin-top: -10px;
        }


        .ProgressBar.Horz.hasItems .Item:before {
        margin-top: 0;
        margin-right: 6px;
        }

        .EshopAddPage .LabelTitle {
        margin: 0;
        font-size: 15px;
        font-weight:bold;
        }
        .EshopAddPage:first-child {
        margin-top: 10px;
        }
        .EshopAddPage .LabelRemark {
        font-size: 12px;
        -webkit-transform: scale(0.9);
        -o-transform: scale(1);
        display: inline-block;
        color: #787878;
        line-height: 14px;
        height: 14px;
        }
        .EshopAddPage .FlowItem .LabelRemark{
        margin: -20px 0px -20px 60px;
        }
        .EshopAddPage .Card{
        width:332px;
        height:125px;
        background-size:100% 100%;
        background-repeat:no-repeat;
        margin-bottom:10px;
        border: 0.5px solid;
        border-radius: 5px;
        border-color: #ECECEC;
        padding:10px 20px 10px 10px;
        position:relative;
        }
        .EshopAddPage .Card .Label {
        display:block;
        line-height:24px;
        height:24px;
        }
        .EshopAddPage .Card .LabelCardTitle {
        color: #333;
        display: inline-block;
        font-size: 14px;
        font-weight:bold;
        }
        .dinggou:before {
        content: "1";
        color: #fff;
        font-size: 12px;
        border-radius: 50px;
        width: 16px;
        text-align: center;
        height: 16px;
        line-height: 16px;
        display: inline-block;
        margin-right: 5px;
        }
        .afterdinggou {
        color: #eb3024;
        font-size: 10px;
        text-align: left;
        height: 12px !important;
        line-height: 16px !important;
        display: inline-block;
        margin: 0 0 0 5px;
        }
        .shouquan:before {
        content: "2";
        color: #fff;
        font-size: 12px;
        border-radius: 50px;
        width: 16px;
        text-align: center;
        height: 16px;
        line-height: 16px;
        display: inline-block;
        margin-right: 5px;
        }
        .shouquanEnd:before {
        content: "3";
        color: #fff;
        font-size: 12px;
        border-radius: 50px;
        width: 16px;
        text-align: center;
        height: 16px;
        line-height: 16px;
        display: inline-block;
        margin-right: 5px;
        }
        .EshopAddPage .Card .LabelBtn {
        display: inline-block;
        font-size: 12px;
        margin-top:30px;
        font-weight:bold;
        cursor: pointer;
        }
        .EshopAddPage .Card .LabelRemark {
        margin-left: -5px;
        -webkit-transform: scale(0.96);
        }
        .remarkTextArea{
        width: 586px;
        }
        .OtherTipBox { padding: 10px 10px 20px 20px; position: relative; margin-top: 20px; border: 1px dashed #7bcbff;
        min-width: 200px; min-height: 60px; box-shadow: 3px 4px 2px 4px #e2f1ff; border-radius: 4px; }
        .OtherTipBox .Arrow1 { content: ' '; border-top: 1px dashed #7bcbff; width: 16px; position: absolute; right:
        10%; top: -8px; height: 1px; transform: rotate(45deg); height: 12px; background: #fff; }
        .OtherTipBox .Arrow2{ content: ' '; border-top: 1px dashed #7bcbff; width: 16px; position: absolute; right: 10%;
        top: -8px; transform: rotate(-45deg); margin-right: 4px; background: #fff; height: 12px; }
        .OtherTipBox .Title { font-weight: bold; line-height: 40px; }
        .OtherTipBox .Label { color: #999; }
        .selBox .selTitle{
        max-width: 100%;
        margin-bottom: 5px;
        text-align: right;
        width: 130px;
        font-size: 14px;
        }
        .selBox .selBtns{
        margin-top: 10px;
        }
        .selBox .selBtn{
        margin-right:20px;
        border-radius:50px;
        padding: 0px 16px;
        height: 36px;
        font-size: 13px;
        border: 0.5px solid;
        }
        .selBox .usuallyBtn{
        border-color: transparent;
        background-color:#f5f6f8;
        color: #666
        }
        .selBox .checked{
        background-color: #ebf3ff;
        }
        .selBox .selBtn label{
        margin:0;
        }
        .selBox .OtherTipBox{
        border: 1px solid #e1e1e1;
        box-shadow: unset;
        position: relative;
        width: 800;
        margin-bottom: 30px;
        }
        .selBox .OtherTipBox .Arrow1 {
        border-top: 1px solid #e1e1e1;
        }
        .selBox .OtherTipBox .Arrow2 {
        border-top: 1px solid #e1e1e1;
        }
        .OtherTipBox .Line{
        background-color: #a5a5a5;
        padding: 0;
        margin: 0;
        font-size: 0;
        overflow: hidden;
        height: 20px;
        width: 1px;
        position: absolute;
        top: 90px;
        left: 25px;
        z-index: 0;
        }
        .OtherTipBox .TipsBox{
        background-color: #fef6eb;
        margin-bottom: 20px;
        min-width: 600px;
        padding: 0px 5px;
        border-radius: 5px;
        }
        .OtherTipBox .TipsIcon{
        color: red;
        margin-right: 5px;
        }
        .OtherTipBox .Tipsdescription{
        color: #2b2a28;
        }
        .OtherTipBox .ProcessBox:not(:last-child) {
        margin-bottom: 25px;
        }
        .OtherTipBox .ProcessBox {
        height: 50px;
        position: relative;
        }
        .ProcessBox .ProcessIcon{
        position: absolute;
        }
        .ProcessBox .ProcessItem{
        position: absolute;
        left: 20px;
        color: #2b2a28;
        font-size: 13px;
        }
        .ProcessBox .ProcessBtn{
        position: absolute;
        top: 3px;
        left: 105px;
        width: 64px;
        height: 28px;
        line-height: 25px;
        padding: 0px 5px;
        font-size: 13px;
        }
        .ProcessBox .ProcessLabel{
        position: absolute;
        left: 105px;
        top: 30px;
        }
        .EshopAddPage .FlowPanel .FlowLabel {
        font-size: 13px
        }
        .selBtns .ChkBoxStyle input {
        top: 10px;
        left: 15px;
        }
        .Padding110
        {
        padding:0px 110px;
        }
        .htmlTipBlock .p{display:block;line-height:30px;height:auto;color:#333}
        .htmlTipBlock .p:before{content:"
        ";width:6px;height:6px;border-radius:3px;background-color:#ccc;display:inline-block;margin-right:10px;vertical-align:middle;}

        .block_wrapper {
        display: flex;
        width: 100%;
        justify-content: space-between;
        }

        .block_wrapper .FlowPanel.vert {
        width: 50%;
        }

        .block_wrapper .memo-edit {
        flex: 1;
        }

        .block_wrapper .memo-edit .TextArea {
        height: 100% !important;
        }
        .AlertSelf.MessageBox .MessageContent:before{color:#FB8708;}
        .mt-0 {margin-bottom: 0px !important;}

        .just-content-right {
        justify-content: right !important;
        }
    </Style>
</Page>
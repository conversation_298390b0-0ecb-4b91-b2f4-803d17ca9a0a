Type.registerNamespace('sale.eshoporder.eshopsaleorder');

sale.eshoporder.eshopsaleorder.DownloadAction = function () {
    sale.eshoporder.eshopsaleorder.DownloadAction.initializeBase(this);
};

sale.eshoporder.eshopsaleorder.DownloadAction.prototype = {
    _openMulDownload: false,
    _customFilterColumn: [],
    _loadedAutoDownload:false,
    _isOldVersion:false,
    context: function (cb) {
        var parameter = {};
        this._openMulDownload = $common.ajaxSync({
            url: "sale/eshoporder/common/getSysData/" + "openMulDownloader",
            data: null,
            router: 'ngp',
            type: "get"
        }).data;
        var enableMulDownloadFilter = $common.ajaxSync({
            url: "sale/eshoporder/common/getSysData/" + "enableMulDownloadFilter",
            data: null,
            router: 'ngp',
            type: "get"
        }).data;

        this._isOldVersion = $common.ajaxSync({
            url: "sale/eshoporder/common/getSysData/" + "oldVersion",
            data: null,
            router: 'ngp',
            type: "get"
        }).data;
        var ptypeRelationVisible =  $saleUtils.getPower("eshoporder.product.view") || $saleUtils.getPower("eshoporder.eshopPtypeRelationPage.view");
        var ptypeRelationEnable =  this._isOldVersion ? $saleUtils.getPower("eshoporder.eshopDownloadOrderPage.autoDownload") : true;

        var showPael = $common.ajaxSync({url: "sale/eshoporder/order/showPanel", data: parameter, router: 'ngp'});
        var eshopList = this.initItemEshopList(this.get_form());
        var form = this.get_form();
        form.set_context("eshopList", eshopList);
        this.checkPageViewPower();
        this.get_service().get("sale/eshoporder/eshoplist/getMulEnumState/shopType", function (shopTypesRes) {
            if (shopTypesRes.code != 200) {
                $common.alert(shopTypesRes.message);
                return;
            }
            form.set_context("shopTypes", shopTypesRes.data);
            cb({
                "shopTypes": shopTypesRes.data,
                "title": '下载订单',
                "eshopList": eshopList,
                "ids": "",
                "enableMulDownloadFilter":enableMulDownloadFilter,
                "ptypeRelationVisible":ptypeRelationVisible,
                "ptypeRelationEnable":ptypeRelationEnable,
            });
        });
    },
    initialize: function DownloadAction$initialize() {
        sale.eshoporder.eshopsaleorder.DownloadAction.callBaseMethod(this, 'initialize');
        this._finished = true;
        var form = this.get_form();
        var myDate = new Date();
        var time = myDate.getTime() - (6 * 24 * 60 * 60 * 1000);
        myDate.setTime(time);
        this.get_form().dateRange.set_date(myDate.format("yyyy-MM-dd ") + "00:00:00", (new Date()).format("yyyy-MM-dd ") + "23:59:59");

        form.drStatus.set_value(2);
        form.drStatus.set_items([]);
        //样式设置
        var blocks = $common.getElementsByClassName(form.tradeIdPanel._element, "textAreaBlock", false);
        var textArea = $common.getElementsByClassName(form.tradeIdPanel._element, "ResizeHeight", false);
        this.setBoxMinHeight(blocks);
        this.setBoxMinHeight(textArea);

        this.checkDownloadOrderCache();
        this.mulOrAutoInit();
        this.checkPower();
        this.query(this);
        $saleUtils.loadJdNpsAuto();
        form.set_caption("下载订单");
    },

    checkPower: function () {
        //新版本没有这些权限点，不做这些权限效验
        if ($ms.ngpConfig.Sys.oldVersion != undefined && !$ms.ngpConfig.Sys.oldVersion)
        {
            return;
        }
        var form = this.get_form();
        var power = $saleUtils.getPower('eshoporder.eshopDownloadOrderPage.downloadById');
        if (!power) {
            form.drEshop.set_enabled(false);
            form.meTradeIds.set_enabled(false);
            form.btnDownloadByTid.set_enabled(false);
            form.tradeInfo.set_enabled(false);
        }

        var power = $saleUtils.getPower("eshoporder.eshopDownloadOrderPage.download");
        if (!power) {
            form.dateRange.set_enabled(false);
            form.drEshopList.set_enabled(false);
            form.drStatus.set_enabled(false);
            form.drFilterType.set_enabled(false);
            form.edFilter.set_enabled(false);
            form.btnDownload.set_enabled(false);
            form.progressGrid.set_enabled(false);
        }

        var power = $saleUtils.getPower("eshoporder.eshopDownloadOrderPage.autoDownload");
        if (!power) {
            form.button.set_enabled(false);
            form.grid.set_enabled(false);
        }
    },

    mulOrAutoInit: function (sender) {
        var form = this.get_form();
        var pageParam = form.get_pageParams();
        var pageForm = this.queryString("from");
        form.autoDownloadConfig.set_visible(false);
        if (pageForm && pageForm == "homePage") {
            form.control.set_selectedTabIndex(0);
        }
        if (pageForm && pageForm == "refund") {
            form.control.set_selectedTabIndex(0);
            $common.alert('售后单下载功能已经合并到了订单下载功能，下载订单时，将同步下载订单的售后单，请直接使用订单下载功能')
        }
    },

    setBoxMinHeight: function (ctrl) {
        if (ctrl == null || ctrl.length == 0)
            return;
        for (var i = 0; i < ctrl.length; i++) {
            ctrl[i].style["height"] = "";
            ctrl[i].style["minHeight"] = "300px";
        }
    },

    checkDownloadOrderCache: function () {
        var _this = this;
        var form = this.get_form();
        var pageIndex = form.control.get_selectedTabIndex();
        var param = "";
        var reqParam = "";
        if (pageIndex == 1) {
            param = "mulDownloadOrder"
            reqParam = "mulDownloadOrderParam"
        } else if (pageIndex == 2) {
            param = "downloadOrderById"
            reqParam = "downloadOrderByIdParam"
        }else {
            return;
        }
        var response = $common.ajaxSync({
            url: "sale/eshoporder/order/getDownLoadOrderCache/" + param,
            data: null,
            type: 'get',
            router: 'ngp'
        });
        if (response.data) {
            if (!_this._activeTimer) {
                _this._activeTimer = new Sys.Timer();
            }
            if (pageIndex == 0) {
                var taskList = JSON.parse(response.data);
                if (!taskList) {
                    $common.alert(response.message);
                    return;
                }
                var rsp = $common.ajaxSync({
                    url: "sale/eshoporder/order/getDownLoadOrderParamCache/" + reqParam,
                    data: null,
                    type: 'get',
                    router: 'ngp'
                });
                if (rsp.data) {
                    _this.chooseSelectedEshop(rsp.data);
                }
                if (_this.checkDeletedEshop(form, rsp.data)) {
                    return;
                }
                _this._finished = false;
                form.timePanel.set_enabled(false);
                form.btnDownload.set_enabled(false);
                form.tradeIdPanel.set_enabled(false);
                form.btnDownloadByTid.set_enabled(false);
                _this.doBind(taskList);
                _this._activeTimer.set_interval(2500);
                _this._activeTimer.set_enabled(true);
                _this._activeTimer.add_tick(Function.createDelegate(this, function () {
                    _this._mulGetMessage(form, taskList);
                }));
            } else if (pageIndex == 1) {
                var task = response.data;
                if (!task) {
                    $common.alert(response.message);
                    return;
                }
                _this._finished = false;
                form.timePanel.set_enabled(false);
                form.btnDownload.set_enabled(false);
                form.tradeIdPanel.set_enabled(false);
                form.btnDownloadByTid.set_enabled(false);
                var rsp = $common.ajaxSync({
                    url: "sale/eshoporder/order/getDownLoadOrderParamCache/" + reqParam,
                    data: null,
                    type: 'get',
                    router: 'ngp'
                });
                if (rsp.data) {
                    if (_this.checkDeletedEshop(form, rsp.data)) {
                        return;
                    }
                    var page = JSON.parse(rsp.data);
                    form.drEshop.set_value(page.otypeId);
                    form.drStatus.set_value(page.status);
                }
                _this._activeTimer.set_interval(2500);
                _this._activeTimer.set_enabled(true);
                _this._activeTimer.add_tick(Function.createDelegate(this, function () {
                    _this._getMessage(form, task);
                }));
            }
        }
    },

    chooseSelectedEshop: function (page) {
        var form = this.get_form();
        this.initEshopList(form);
        var otypeIds = page.otypeIds;
        form.drEshopList.set_value(otypeIds);
        var status = page.status;
        this.doEshopSelected(this);
        form.drStatus.set_value(status);
        var beginTime = page.beginTimeStr;
        var endTime = page.endTimeStr;
        form.dateRange.set_date(beginTime, endTime);
    },

    checkDeletedEshop: function (form, data) {
        var eshopList = JSON.stringify(form.drEshopList.get_dataSource());
        var deleted = false;
        if (data && data.otypeIds) {
            var otypeIds = data.otypeIds;
            for (var i = 0; i < otypeIds.length; i++) {
                if (eshopList.indexOf(otypeIds[i]) < 0) {
                    deleted = true;
                }
            }
            return deleted;
        }
    },

    partialQuery: function (sender) {
        var form = sender.get_form();
        if (!form || !form.grid) {
            return;
        }
        var eshopId = form.grid.get_selectedRowData().id;
        var index = form.grid.get_selectedRowIndex();
        $common.showLoading();
        var parms = {};
        var queryParams = {};
        queryParams.eshopId = eshopId;
        parms.queryParams = queryParams;
        var queryResponse = $common.ajaxSync({
            url: "sale/eshoporder/eshoplist/getAutoDownloadConfig",
            data: parms, waiting: "排序中,请稍后...", router: 'ngp'
        });
        if (queryResponse.code != 200 || !queryResponse.data.orgList || queryResponse.data.orgList.length == 0) {
            return;
        } else {
            var orgList = queryResponse.data.orgList;
            if ($saleUtils.getPower("eshoporder.eshop.auth").value == false) {
                for (var i = 0; i < orgList.length; i++) {
                    orgList[i].authTag = "";
                }
            }
            form.grid.batchModifyRowData(index, orgList);
        }
        $common.hideLoading();
    },

    query: function (sender) {
        var form = sender.get_form();
        if(form.control.get_selectedTabIndex() != 0 || this._loadedAutoDownload){
            return;
        }
        $common.showLoading();
        setTimeout(function () {
            form.grid.get_pager().refresh();
            $common.hideLoading();
        }, 1);
    },

    doSelected: function (sender,args) {
        var form = sender.get_form();
        if(form.control.get_selectedTabIndex() != 1 || this._loadedAutoDownload){
            return;
        }
        $common.showLoading();
        setTimeout(function () {
            form.grid.get_pager().refresh();
            $common.hideLoading();
        }, 1);
    },

    initItemEshopList: function (form) {
        var parameter = {};
        parameter.queryVirtual = false;
        parameter.queryStop = false;
        parameter.platformTypes = [];
        parameter.notAllowOCategorys = [10];
        var response = $common.ajaxSync({
            url: "sale/eshoporder/eshop/getEshopByPlatformTypes",
            data: parameter,
            type: 'post',
            router: 'ngp'
        });
        return response.data;
        // if (!response || !response.data || response.data.length === 0) {
        //     form.drEshop.set_items([]);
        //     form.drEshopList.set_items([]);
        //     return;
        // }
        // var list = [];
        // for (var i = 0; i < response.data.length; i++) {
        //     var eshop = response.data[i];
        //     list.push({otypeId: eshop.otypeId, fullname: eshop.fullname, eshopType: eshop.eshopType});
        // }
        // form.drEshop.set_items(list);
        // form.drEshopList.set_items(list);

    },


    initEshopList: function (form) {
        var parameter = {};
        parameter.queryVirtual = false;
        parameter.queryStop = false;
        var response = $common.ajaxSync({
            url: "sale/eshoporder/eshop/getEshopByPlatformTypes",
            data: parameter,
            type: 'post',
            router: 'ngp'
        });
        if (!response || !response.data || response.data.length === 0) {
            form.drEshopList.set_items([]);
            form.drStatus.set_items([]);
            return;
        }
        form.drEshopList.set_items(response.data);
        form.drStatus.set_items([]);
    },

    doPopTips: function (sender) {
        var form = sender.get_form();
        var tipPopup = form.popAutoDownloadTips;
        tipPopup.popupAt(sender);
    },
    doShopTypesSelected: function (sender) {
        var form = sender.get_form();
        this.initEshopList(form);
    },

    doEshopSelected: function (sender) {
        var form = sender.get_form();
        var shopIds = form.drEshopList.get_value();
        var response = $common.ajaxSync({
            url: "sale/eshoporder/eshop/getTradeStatusByEshopIds",
            data: shopIds,
            type: 'post',
            router: 'ngp'
        });
        if (!response || !response.data || response.data.length === 0) {
            form.drStatus.set_items([]);
            return;
        }
        form.drStatus.set_items(response.data);
        form.set_context("drStatus", response.data);
        form.drStatus.set_selectedIndex(0);
    },

    doSelectedEshopChange: function (sender) {
        var form = sender.get_form();
        var data = sender.get_selectedItem();
        if (!data) {
            return;
        }
        var eshopType = data.eshopType;
        if (this.selectedEshopType && this.selectedEshopType === eshopType) {
            return;
        }
        this.selectedEshopType = eshopType;
        var p = {
            intId: eshopType
        };
        this.get_service().post("sale/eshoporder/common/getOrderState", p, function (response) {
            if (response.code != 200) {
                $common.alert(response.message);
                return;
            }
            var data = response.data;
            form.drStatus.set_items(data);
            for (var i = 0; i < data.length; i++) {
                if (data[i].enable) {
                    form.drStatus.set_selectedIndex(i);
                }
            }
        });
    },

    doLocate: function (sender) {
        var form = sender.get_form();
        var finderStr = sender.get_value();
        if (!finderStr) {
            $common.showHint('请输入网店名称进行定位', sender);
            return;
        }
        var itemList = form.drEshopList.get_items();
        if (!itemList || itemList.length === 0) {
            $common.showHint('没有可定位的网店', sender);
            return;
        }
        var located = false;
        for (var i = 0; i < itemList.length; i++) {
            var temp = itemList[i];
            var text = temp.text;
            var value = temp.value;
            if (text.indexOf(finderStr) > -1) {
                located = true;
                form.drEshopList.set_value(value);
                break;
            }
        }
        if (!located) {
            sender.set_value("");
            $common.showHint('定位失败，请重新输入', sender);
        }
    },

    checkPageViewPower: function () {
        var power = $saleUtils.getPower("eshoporder.eshopsaleorder.downloadOrder");
        if (!power) {
            throw Error.message("您没有查看该页面的权限");
        }
    },

    doDownload: function (sender) {
        var form = sender.get_form();
        var index = form.control.get_selectedTabIndex();
        if (index === 1) {
            var eshopIds = form.drEshopList.get_value();
            if (!eshopIds || eshopIds.length === 0) {
                $common.alert("按时间/状态下载，请先选择需要下载的网店");
                console.log(eshopIds);
                return;
            }
            // form.info.set_value("");
            if (!this._checkTime(form)) {
                return;
            }
            this._doDownloadByParam(form);
        } else if (index === 2) {
            var startObj = null;
            if (window.$startTiming) {
                startObj = $startTiming('平台订单->下载订单->按订单号');
            }
            form.startObj = startObj;
            var eshopId = form.drEshop.get_value();
            if (!eshopId || eshopId === 0) {
                $common.alert("按订单号，请先选择需要下载的网店");
                console.log(eshopIds);
                return;
            }
            form.tradeInfo.set_value("");
            if (!this._checkTid(form)) {
                return;
            }
            this._doDownloadByTid(form);
        }
    },

    // 打开导入订单模态框
    doImportInit: function (sender) {
        var form = sender.get_form();
        var popForm = new Sys.UI.Form(sender, true);
        popForm.add_ok(function (pop) {
            form.meTradeIds.set_value(pop.listId);
        });
        popForm.showModal("sale/eshoporder/eshopsaleorder/EshopOrderIdsImport.gspx");
    },

    _doDownloadByParam: function (form) {
        var startObj = null;
        if (window.$startTiming) {
            startObj = $startTiming('平台订单->下载订单->手工下载');
        }
        if (!this._activeTimer) {
            this._activeTimer = new Sys.Timer();
        }
        var request = this._buildRequest(form);
        var _this = this;
        var url = "/sale/eshoporder/order/mulDownload";
        var response = $common.ajaxSync({url: url, data: request, router: 'ngp'});
        var taskList = response.data;
        if (!taskList) {
            $common.alert(response.message);
            return;
        }
        this._finished = false;
        this._disEnableForm(form, 0);
        this.doBind(taskList);
        _this._activeTimer.set_interval(2500);
        _this._activeTimer.set_enabled(true);
        _this._activeTimer.add_tick(Function.createDelegate(this, function () {
            _this._mulGetMessage(form, taskList, startObj);
        }));
    },

    _disEnableForm: function (form, index) {
        if (!form) {
            return;
        }
        if (index == 1) {
            form.dateRange.set_enabled(false);
            form.drEshopList.set_enabled(false);
            form.drStatus.set_enabled(false);
            form.drFilterType.set_enabled(false);
            form.edFilter.set_enabled(false);
            form.btnDownload.set_enabled(false);
            form.btnDownloadByTid.set_enabled(false);
            return;
        }
        form.drEshop.set_enabled(false);
        form.btnImport.set_enabled(false);
        form.meTradeIds.set_enabled(false);
        form.btnDownloadByTid.set_enabled(false);
        form.tradeInfo.set_enabled(false);
        form.btnDownload.set_enabled(false);
        form.btnDownloadByTid.set_enabled(false);
    },

    _doDownloadByTid: function (form) {
        if (!this._activeTimer) {
            this._activeTimer = new Sys.Timer();
        }
        var request = this._buildTidRequest(form);
        var _this = this;
        var url = "/sale/eshoporder/order/downloadById";
        var response = $common.ajaxSync({url: url, data: request, router: 'ngp'});
        var task = response.data;
        if (!task) {
            $common.alert(response.message);
            return;
        }
        this._finished = false;
        this._disEnableForm(form, 2);
        _this._activeTimer.set_interval(2500);
        _this._activeTimer.set_enabled(true);
        _this._activeTimer.add_tick(Function.createDelegate(this, function () {
            _this._getMessage(form, task);
        }));
    },

    _checkTime: function (form) {
        var dateTime = form.dateRange.get_date();
        var beginTime = dateTime[0];
        var endTime = dateTime[1];
        $common.checkTips(!beginTime, "请选择开始时间", form.dateRange);
        $common.checkTips(!endTime, "请选择结束时间", form.dateRange);
        $common.checkTips(beginTime > endTime, "开始时间大于结束时间", form.dateRange);

        /* if (beginTime > endTime) {
             $common.alert("开始时间不能大于结束时间");
             return false;
         }*/
        return true;
    },

    _checkTid: function (form) {
        var tradeIds = form.meTradeIds.get_value();
        if (!tradeIds) {
            $common.alert("必须输入线上订单编号才能下载！");
            return false;
        }
        var tradeList = this.getTradeList(tradeIds);
        if (!tradeList) {
            $common.alert("必须输入线上订单编号才能下载！");
            return false;
        }
        return true;
    },

    _buildRequest: function (form) {
        var dateTime = form.dateRange.get_date();
        var request = {
            beginTime: dateTime[0],
            endTime: dateTime[1],
            otypeIds: form.drEshopList.get_value(),
            filterStr: form.edFilter.get_value(),
            filterType: form.drFilterType.get_value(),
            downloadType: 0
        };
        var status = form.drStatus.get_value();
        if (status && status != 999) {
            request.status = status;
        }
        return request;
    },

    _buildTidRequest: function (form) {
        return {
            otypeId: form.drEshop.get_value(),
            filterStr: form.meTradeIds.get_value(),
            downloadType: 1
        };
    },

    _getMessage: function (form, taskId) {
        var url = "/sale/eshoporder/common/getProcessMsg";
        var _this = this;
        var parameter = {
            requestStr: taskId
        };
        this.get_service().post(url, parameter, function (response) {
            var data = response.data;
            var completed = data.completed;
            var message = data.message;
            if (message) {
                form.tradeInfo.set_value(message);
            }
            if (completed) {
                if (form.startObj != null) {
                    form.startObj.endTiming();// 触发上报
                }
                _this._finished = true;
                _this._activeTimer.dispose();
                _this._activeTimer.set_enabled(false);
                form.set_enabled(true);
                form.timePanel.set_enabled(true);
                form.btnDownload.set_enabled(true);
                form.tradeIdPanel.set_enabled(true);
                form.btnDownloadByTid.set_enabled(true);
                _this.checkPower();
            }
        });
    },

    _mulGetMessage: function (form, taskList, startObj) {
        var url = "/sale/eshoporder/common/getProcessMsg";
        var _this = this;
        var grid = form.progressGrid;
        var isCompleted = true;
        if (!taskList || taskList.length == 0) {
            return;
        }
        for (var i = 0; i < taskList.length; i++) {
            if (taskList[i].completed) {
                continue;
            }
            if (!taskList[i].taskId || taskList[i].taskId == null || taskList[i].taskId == 0) {
                continue;
            }
            var response = $common.ajaxSync({url: url, data: {requestStr: taskList[i].taskId}, router: 'ngp'});
            if (!response || !response.data || !response.data.message) {
                continue;
            }
            var data = response.data;
            var message = data.message;
            var percent = parseInt(data.percent) > 100 ? 100 : parseInt(data.percent);
            if (message) {
                taskList[i].message = this.getLastMessage(message);
                taskList[i].allMessage = message;
            }
            //form.info.set_value(taskList[i].allMessage);
            taskList[i].success = data.success;
            taskList[i].progress = (taskList[i].progress > percent) ? taskList[i].progress : percent;
            taskList[i].operation = this.getTaskOperation(data.submitType);
            if (data.completed) {
                taskList[i].completed = data.completed;
            }
            isCompleted = false;
            if (!grid || i < 0 || !taskList[i]) {
                continue;
            }
            try {
                grid.modifyRowData(i, taskList[i]);
            } catch (e) {
                //这里可能会引起数据下标越界
                //因为这里读取进度信息时，可能会有任务完成从列表中删除掉
            }
        }
        //this.doBind(taskList);
        if (isCompleted) {
            _this._finished = true;
            _this._activeTimer.dispose();
            _this._activeTimer.set_enabled(false);
            form.set_enabled(true);
            form.timePanel.set_enabled(true);
            form.btnDownload.set_enabled(true);
            form.tradeIdPanel.set_enabled(true);
            form.btnDownloadByTid.set_enabled(true);
            this.checkPower();
            if (startObj != null) {
                startObj.endTiming();// 触发上报
            }
        }
        if (!grid) {
            return;
        }
        var rowData = grid.get_selectedRowData();
        if (!rowData || !rowData.allMessage) {
            return;
        }
        //对一打开的页面判断进行实时重写
        _this.doRealTimeLog(_this, form);
        // form.info.set_value(rowData.allMessage);
    },

    _ajaxChainGetMessage: function (form, taskList) {
        var _this = this;
        var grid = form.progressGrid;
        var isCompleted = true;

        var apis = [];
        if (!taskList || taskList.length == 0) {
            return;
        }
        for (var i = 0; i < taskList.length; i++) {
            if (taskList[i].completed) {
                continue;
            }

            var options = new Object();
            options.url = "/sale/eshoporder/common/getProcessMsg";

        }

        var url = "/sale/eshoporder/common/getProcessMsg";

        for (var i = 0; i < taskList.length; i++) {
            if (taskList[i].completed) {
                continue;
            }
            if (!taskList[i].taskId || taskList[i].taskId == null || taskList[i].taskId == 0) {
                continue;
            }
            var response = $common.ajaxSync({url: url, data: {requestStr: taskList[i].taskId}, router: 'ngp'});
            var data = response.data;
            var message = data.message;
            var percent = parseInt(data.percent) > 100 ? 100 : parseInt(data.percent);
            if (message) {
                taskList[i].message = this.getLastMessage(message);
                taskList[i].allMessage = message;
            }
            taskList[i].success = data.success;
            taskList[i].progress = (taskList[i].progress > percent) ? taskList[i].progress : percent;
            taskList[i].operation = this.getTaskOperation(data.submitType);
            if (data.completed) {
                taskList[i].completed = data.completed;
            }
            isCompleted = false;
            grid.modifyRowData(i, taskList[i]);
        }
        if (isCompleted) {
            _this._finished = true;
            _this._activeTimer.dispose();
            _this._activeTimer.set_enabled(false);
            form.set_enabled(true);
            form.timePanel.set_enabled(true);
            form.btnDownload.set_enabled(true);
            form.tradeIdPanel.set_enabled(true);
            form.btnDownloadByTid.set_enabled(true);
        }
        var rowData = grid.get_selectedRowData();
        if (!rowData || !rowData.allMessage) {
            return;
        }
        //对一打开的页面判断进行实时重写
        _this.doRealTimeLog(_this, form);
    },

    doRealTimeLog: function (_this, form) {
        var grid = form.progressGrid;
        var rowData = grid.get_selectedRowData();
        if (!rowData || !rowData.allMessage) {
            return;
        }
        this._doCreateLog(form, rowData.allMessage)
    },

    getLastMessage: function (message) {
        if (!message) {
            return message;
        }
        var messageList = message.split('\n')
        return messageList[messageList.length - 1]
    },

    doCellRender: function (sender, eventArgs) {
        var rowIndex = eventArgs.get_rowIndex();
        var data = sender.findRowData(rowIndex);
        if (!data)
            return;

        var column = eventArgs.get_column();
        if (column.get_dataField() == 'progress') {
            if (data.completed != null && data.completed) {
                if (data.success) {
                    eventArgs.set_cssClass('blueValue');
                } else {
                    eventArgs.set_cssClass('RedValue');
                }
            }
        }
    },

    // doChange: function (sender, eventArgs) {
    //     var popup = this.get_form().logBlock;
    //     popup.hide(true);
    // },

    doOperateShowLog: function (sender, eventArgs) {
        if (eventArgs) {
            if (eventArgs._buttonValue == 1) {
                var showPage = $common.showPage(sender, "/sale/jarvis/DeliverBill/common/DeliverBillGuideForm.gspx?caption=订单审核&tag=AUDIT&closeType=url");
                showPage.guideQueryParams = {quickQueryName: "全部单据"};
            }
            if (eventArgs._buttonValue == 2) {
                $common.showPage(sender, "/sale/eshoporder/advance/EshopAdvanceSaleOrderList.gspx?caption=预售订单处理&tag=AUDIT&closeType=url");
            }
            if (eventArgs._buttonValue == 3) {
                //查看日志
                var form = sender.get_form();
                var grid = form.progressGrid;
                var rowData = grid.get_selectedRowData();
                if (!rowData || !rowData.allMessage) {
                    return;
                }
                var popup = this.get_form().logBlock;
                this._doCreateLog(form, rowData.allMessage)
                popup.popupAt(eventArgs.get_event().target);
                sender.set_hasPopup(true);
            }
        }
    },
    _doCreateLog: function (form, allMessage) {
        if (form.appendBlock) {
            form.removeControlById("appendBlock");
        }
        var block = $createControl('Block', {ID: "appendBlock"}, form);

        var messageList = allMessage.split('\n')
        for (var i = 0; i < messageList.length; i++) {
            $createControl('Label',
                {
                    Text: messageList[i],
                    CssClass: "logLabel"
                },
                form, block);
        }
        block.appendTo(form.logBlock);
    },

    getTaskOperation: function (submitType) {
        var operation = "[{3:\"<d>【进度信息】</d>\"}]";
        submitType = 1;
        if (!submitType || submitType == 0)
            return operation;

        var advanceShowPower = $saleUtils.getPower("eshoporder.eshopAdvanceSaleOrder.view");
        var billShowPower = $saleUtils.getPower("jarvis.deliverAudit.view");
        switch (submitType) {
            case 0:
                break;
            case 1:
                if (billShowPower) {
                    operation = "[{3:\"<d>【进度信息】</d>\"},{1:\"<d>【订单处理-待审核】</d>\"}]";
                }
                break;
            case 2:
                if (advanceShowPower) {
                    operation = "[{3:\"<d>【进度信息】</d>\"},{2:\"<d>【预售订单处理】</d>\"}]";
                }
                break;
            case 3:
                if (advanceShowPower && billShowPower) {
                    operation = "[{3:\"<d>【进度信息】</d>\"},{1:\"<d>【订单处理-待审核】</d>\"},{2:\"<d>【预售订单处理】</d>\"}]";
                } else if (billShowPower) {
                    operation = "[{3:\"<d>【进度信息】</d>\"},{1:\"<d>【订单处理-待审核】</d>\"}]";
                } else if (advanceShowPower) {
                    operation = "[{3:\"<d>【进度信息】</d>\"},{2:\"<d>【预售订单处理】</d>\"}]";
                }
                break;
            default:
        }
        return operation;
    },

    doBind: function (taskList) {
        var form = this.get_form();
        var grid = form.progressGrid;
        //grid.refresh(taskList);
        grid.dataBind(taskList);
        var rowData = grid.get_selectedRowData();
        if (!rowData || !rowData.allMessage) {
            return;
        }
        // form.info.set_value(rowData.allMessage);
    },

    doFormClose: function (sender, eventArgs) {
        var form = this.get_form();
        eventArgs.set_canClose(false);
        if (!this._finished) {
            var _this = this;
            $common.confirm("订单还在下载，是否现在退出？", function (r) {
                if (r) {
                    form.close(true);
                    if (this._activeTimer) {
                        _this._activeTimer.set_enabled(false);
                        _this._activeTimer.dispose();
                    }
                }
            }, _this);
        } else {
            form.close(true);
            if (this._activeTimer) {
                this._activeTimer.set_enabled(false);
                this._activeTimer.dispose();
            }
        }
    },
    btnAutoDownloadConfig: function (sender) {
        var updateParams = new Object();
        updateParams.title = "自动下载配置";
        var _this = this;
        var popForm = new Sys.UI.Form(sender, 0, 0);
        popForm.set_params(updateParams);
        popForm.showMDI("sale/eshoporder/eshopsaleorder/AutoDownloadOrderModal.gspx?a=0");
        popForm.add_closed(function (pop) {
            if (!pop.result) {
                return;
            }
            _this._doRefreshTreeGrid();
            _this.btnQueryOnClick();
            //_this._refreshTreeView(form, selectItem);
        });
    },


    _buildDownloadRequest: function (grid) {
        var rowData = grid.get_selectedRowData();
        $common.checkTips(!rowData.lastDownloadSuccessEndTime && !rowData.firstDownloadTime, "请选择开始下载时间");
        var request = {
            beginTime: rowData.lastDownloadSuccessEndTime ? rowData.lastDownloadSuccessEndTime : rowData.firstDownloadTime,
            endTime: Date.now(),
            otypeIds: [rowData.id],
            downloadType: 9
        };
        return request;
    },

    doOperation: function (sender, eventArgs) {
        var buttonValue = eventArgs.get_buttonValue();
        var form = sender.get_form();
        var grid = form.grid;
        var rowData = grid.get_selectedRowData();
        if (!rowData) return;
        if (buttonValue == "startDownload") {
            this.doDownloadNow(this);
        } else if (buttonValue == "auth") {
            $common.showPage(sender, "/sale/eshoporder/eshop/EShopList.gspx", {fullname: rowData.fullname}, SysConsts.CloseAll)
        } else if (buttonValue == "download") {
            var request = this._buildDownloadRequest(grid);
            var _this = this;
            var url = "/sale/eshoporder/order/mulDownload";
            var response = $common.ajaxSync({url: url, data: request, router: 'ngp'});
            if (response.code == "200") {
                $common.showInfo("下单任务创建成功！");
            } else {
                $common.alert(response.message);
            }
        } else if (buttonValue == "record") {
            var recordForm = new Sys.UI.Form(sender, false);
            recordForm.showMDI("sale/eshoporder/eshopsaleorder/AutoDownloadRecord.gspx", {
                eshopId: rowData.id,
                auth: rowData.auth
            })
        }
        this.partialQuery(this);
    },

    doDownloadNow: function (sender) {
        var form = sender.get_form();
        var grid = form.grid;
        var rowData = grid.get_selectedRowData();
        var rowIndex = grid.get_selectedRowIndex();
        var that = this;
        if (sender) {
            if (rowData.auth) {
                var newForm = new Sys.UI.Form(sender);
                newForm.showMDI("sale/eshoporder/common/Date.gspx");
                newForm.add_ok(function (data) {
                    if (!data || data == null || !data.dateRange.get_value()) {
                        $common.alert("开始下载时间为空");
                        return;
                    }
                    var params = {
                        profileId: rowData.profileId,
                        eshopId: rowData.id,
                        fullname: rowData.fullname,
                        beginTime: data.dateRange.get_value(),
                        shopType: rowData.eshopType
                    };
                    that.get_service().post('sale/eshoporder/eshoplist/addEshopAutoOrder', params, function (response) {
                        if (response.data.success != true) {
                            $common.alert("系统出错了");
                        } else {
                            $common.alert("自动下单开启成功");
                        }
                        that.partialQuery(that);
                    })
                })
            } else {
                $common.confirm("该网店未授权，请完成授权后，再开启自动下载订单", function (r) {
                    if (r) {
                        $common.showPage(sender, "/sale/eshoporder/eshop/EShopList.gspx", {fullname: rowData.fullname}, SysConsts.CloseAll);
                        $notify.bind('eshopAuthCallBack', function () {
                            that.partialQuery(that);
                        });
                    } else {
                        that.partialQuery(that);
                    }
                });
            }
        }
    },


    doCellBeginEdit: function (sender, args) {
        var form = this.get_form();
        var grid = form.grid;
        var rowData = grid.get_selectedRowData();
        var index = grid.get_selectedRowIndex();
        if (rowData == null) {
            return;
        }
        if (args.get_column().get_dataField() == "tmcEnabled") {
            return;
        }
        if (rowData.firstDownloadTime != null) {
            if (rowData.operation.indexOf("立即下载") > 0) {
                args.set_cancel(true)
            }
        }
    },

    doFilterRendering: function (sender, args) {
        var form = this.get_form();
        var column = sender;

        var dataField = column.get_dataField(); // 列名称

        var editID = dataField + "_edit";  // 获取编辑框控件的id，必须唯一
        var panel = $createControl(Craba.UI.Block, {CssClass: "FlexAuto"}, form);
        var bar = sender.get_grid()._filterBar;
        this._customFilterColumn.push(column);

        var authStateList = [];
        authStateList.push({code: 0, name: "已授权"});
        authStateList.push({code: 1, name: "未授权"});
        authStateList.push({code: 1, name: "授权过期"});

        var autoSyncState = [];
        autoSyncState.push({code: 0, name: "未开启"});
        autoSyncState.push({code: 1, name: "已开启"});

        var tmcEnabledState = [];
        tmcEnabledState.push({code: 0, name: "未开启"});
        tmcEnabledState.push({code: 1, name: "已开启"});

        switch (dataField) {
            case 'authState':
                //提醒
                $createControl('DropDownEdit',
                    {
                        ID: editID,
                        DataSource: authStateList,
                        DataField: "code",
                        DataTextField: "name",
                        DataValueField: "code",
                        CssClass: "FlexAuto",
                        DropDownStyle: "DropDownSearch",
                        TabOnEnter: false,
                        OnChange: $createDelegate(bar, bar.doFilter)
                    },
                    form,
                    panel);
                break;
            case 'autoSyncState':
                //提醒
                $createControl('DropDownEdit',
                    {
                        ID: editID,
                        DataSource: autoSyncState,
                        DataField: "code",
                        DataTextField: "name",
                        DataValueField: "code",
                        CssClass: "FlexAuto",
                        DropDownStyle: "DropDownSearch",
                        TabOnEnter: false,
                        OnChange: $createDelegate(bar, bar.doFilter)
                    },
                    form,
                    panel);
                break;
            case 'tmcEnabledState':
                //提醒
                $createControl('DropDownEdit',
                    {
                        ID: editID,
                        DataSource: tmcEnabledState,
                        DataField: "code",
                        DataTextField: "name",
                        DataValueField: "code",
                        CssClass: "FlexAuto",
                        DropDownStyle: "DropDownSearch",
                        TabOnEnter: false,
                        OnChange: $createDelegate(bar, bar.doFilter)
                    },
                    form,
                    panel);
                break;
            default:
                return;
        }
        args.set_control(panel); // 回传给平台的自定义编辑器
    },

    doGetOtherFilter: function (sender, args) {
        var filter = args.get_filter();

        var list = this._customFilterColumn;
        if (!list && list.length == 0) return;

        for (var i = 0; i < list.length; i++) {
            var dataField = list[i].get_dataField();
            var editID = dataField + "_edit";
            var form = this.get_form();
            var edit = form[editID]; // 取出筛选器控件
            if (!edit) continue;

            if (!filter) {
                //清空自定义筛选
                edit.set_value(null);
                continue;
            }

            var filterItem = {};

            switch (dataField) {
                case "authState":
                    filterItem.dataField = "eshopAuthMark";
                    break;
                case "autoSyncState":
                    filterItem.dataField = "autoSync";
                    break;
                case "tmcEnabledState":
                    filterItem.dataField = "tmcEnable";
                    break;
                default:
                    filterItem.dataField = dataField;
                    break;
            }

            filterItem.value = edit.get_value();
            filterItem.type = list[i].get_filterType();

            filter.items.push(filterItem);
        }
    },

    bindGridData: function (path, params, binData, failback) {
        var startObj = null;
        if (window.$startTiming) {
            startObj = $startTiming('网店-下载订单-自动下载（查询）');
        }
        var filter = !params.queryParams.gridFilter ? [] : params.queryParams.gridFilter;
        var paramsForQuery = params;
        paramsForQuery.queryParams = this.getQueryParams();
        if (filter && filter.length > 0) {
            for (var i = 0; i < filter.length; i++) {
                var filed = filter[i];
                if (filed.dataField == "eshopAuthMark") {
                    paramsForQuery.queryParams.eshopAuthMark = filed.value;
                } else if (filed.dataField == "autoSync") {
                    paramsForQuery.queryParams.autoSync = filed.value;
                } else if (filed.dataField == "tmcEnable") {
                    paramsForQuery.queryParams.tmcEnable = filed.value;
                } else if (filed.dataField == "fullname") {
                    paramsForQuery.queryParams.fullname = filed.value;
                }
            }
        }
        var queryResponse = $common.ajaxSync({
            url: "sale/eshoporder/eshoplist/getAutoDownloadConfig",
            data: paramsForQuery, waiting: "排序中,请稍后...", router: 'ngp'
        });
        if (queryResponse.code != 200 || !queryResponse.data.orgList || queryResponse.data.orgList.length == 0) {
            binData({
                itemList: [],
                itemCount: 0
            });
        } else {
            var orgList = queryResponse.data.orgList;
            if ($saleUtils.getPower("eshoporder.eshop.auth").value == false) {
                for (var i = 0; i < orgList.length; i++) {
                    orgList[i].authTag = "";
                }
            }
            binData({
                itemList: orgList,
                itemCount: queryResponse.data.total
            });
        }
        this._loadedAutoDownload = true;
        if (startObj != null) {
            startObj.endTiming();// 触发上报
        }
    },

    getQueryParams: function () {
        return {
            noplatform: 9,
            stoped: 0,
            queryVirtual: false,
            noQueryPifa: true,
            orderByCreateTime: true,
            notAllowOCategorys: [10],
        };
    },

    doClick: function (sender) {

        if ($ms.ngpConfig.Sys.oldVersion && $ms.ngpConfig.Sys.oldVersion == 1){
            $common.showPage(sender, "sale/eshoporder/eshopproduct/PtypeRelation.gspx", new Object(), SysConsts.CloseAll);
        }else {
            $common.showPage(sender, "sale/eshoporder/product/list.gspx", new Object(), SysConsts.CloseAll);
        }

        // form.close();
    },

    dispose: function () {
        sale.eshoporder.eshopsaleorder.DownloadAction.callBaseMethod(this, 'dispose');
    },

    getTradeList: function (trades) {
        trades = trades.replaceAll("，", ",");
        var that = this;
        return trades.split(",").filter(function (item) {
            return !that._isEmptyStr(item);
        });
    },

    _isEmptyStr: function (str) {
        if (str == null || !str) {
            return false;
        }
        var clearStr = str.replace(/(^\s*)|(\s*$)/g, '');
        return clearStr == "";
    },
    doChangeState: function (sender, args) {
        var form = sender.get_form();
        var rowData = form.grid.get_selectedRowData();
        rowData.otypeId = rowData.id;
        rowData.tmcEnabled = rowData.tmcEnabled ? 1 : 0;
        this.get_service().post('sale/eshoporder/eshop/registerTmc', rowData, function (response) {
            if (response && response.data) {
                var data = response.data;
                if (data.success) {
                    $common.showOk(rowData.tmcEnabled ? "已启用" : "已停用");
                } else {
                    rowData.tmcEnabled = !rowData.tmcEnabled;
                    sender.set_value(rowData.tmcEnabled);
                    $common.showTips(data.message);
                }
            }
        });
    }
};
sale.eshoporder.eshopsaleorder.DownloadAction.registerClass('sale.eshoporder.eshopsaleorder.DownloadAction', Sys.UI.PageAction);

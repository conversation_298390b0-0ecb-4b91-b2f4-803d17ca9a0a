package com.wsgjp.ct.sale.biz.eshoporder.service.eshop;

import bf.datasource.page.PageDevice;
import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.pm.enums.SysProfileModelKeyEnum;
import com.wsgjp.ct.pm.service.PermissionValiateService;
import com.wsgjp.ct.pm.service.SysProfileModelService;
import com.wsgjp.ct.redis.process.message.config.RedisMessageSys;
import com.wsgjp.ct.sale.biz.api.baseinfo.BaseInfoApi;
import com.wsgjp.ct.sale.biz.api.profile.ProfileApi;
import com.wsgjp.ct.sale.biz.api.profile.ProfileInfo;
import com.wsgjp.ct.sale.biz.api.profile.ProfilebaseInfoChangeNotifyRequest;
import com.wsgjp.ct.sale.biz.bifrost.config.BifrostQuickLoginConfig;
import com.wsgjp.ct.sale.biz.bifrost.config.BifrostTmcSupportConfig;
import com.wsgjp.ct.sale.biz.bifrost.entity.BaseInfoLog;
import com.wsgjp.ct.sale.biz.bifrost.mapper.BifrostEshopMapper;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopAddressMappingMapper;
import com.wsgjp.ct.sale.biz.bifrost.service.*;
import com.wsgjp.ct.sale.biz.bifrost.util.EshopUtils;
import com.wsgjp.ct.sale.biz.common.param.DeleteEshopRegisterParam;
import com.wsgjp.ct.sale.biz.eshoporder.api.PtypeApi;
import com.wsgjp.ct.sale.biz.eshoporder.api.request.BaseBtype;
import com.wsgjp.ct.sale.biz.eshoporder.api.request.BtypeQueryParameter;
import com.wsgjp.ct.sale.biz.eshoporder.api.request.BtypeQueryRequest;
import com.wsgjp.ct.sale.biz.eshoporder.api.response.BaseBtypeErrorResponse;
import com.wsgjp.ct.sale.biz.eshoporder.api.response.BaseBtypeResponse;
import com.wsgjp.ct.sale.biz.eshoporder.config.ServiceConfig;
import com.wsgjp.ct.sale.biz.eshoporder.config.SyncOrderConfig;
import com.wsgjp.ct.sale.biz.eshoporder.config.sysdata.EshopOrderGlobalConfig;
import com.wsgjp.ct.sale.biz.eshoporder.config.sysdata.EshopSysConfig;
import com.wsgjp.ct.sale.biz.eshoporder.config.sysdata.SysOtypeConfig;
import com.wsgjp.ct.sale.biz.eshoporder.constant.IntConstants;
import com.wsgjp.ct.sale.biz.eshoporder.constant.StringConstantEnum;
import com.wsgjp.ct.sale.biz.eshoporder.constant.eshop.EshopInfoConstants;
import com.wsgjp.ct.sale.biz.eshoporder.dto.eshop.EshopPlatformStoreMappingInDTO;
import com.wsgjp.ct.sale.biz.eshoporder.dto.order.modifyOrderIndexInDTO;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.ProductRefreshSupportTypeSource;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.base.QueryBaseTypeParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.openapi.ProductReleationRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.standardApi.otype.OtypeStandardApiResponse;
import com.wsgjp.ct.sale.biz.eshoporder.exception.DistributorException;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.*;
import com.wsgjp.ct.sale.biz.eshoporder.service.baseinfo.EshopOrderBaseInfoService;
import com.wsgjp.ct.sale.biz.eshoporder.support.DeliverApi;
import com.wsgjp.ct.sale.biz.eshoporder.util.CommonUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.RefundMd5Util;
import com.wsgjp.ct.sale.biz.jarvis.dto.request.BatchBaseInfoCheckRequest;
import com.wsgjp.ct.sale.biz.jarvis.dto.response.BaseInfoCheckResponse;
import com.wsgjp.ct.sale.biz.record.utils.RedisUtils;
import com.wsgjp.ct.sale.biz.shopsale.common.FeignResult;
import com.wsgjp.ct.sale.biz.shopsale.mapper.BaseInfoMapper;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.store.BaseStore;
import com.wsgjp.ct.sale.common.config.EshopOrderCommonConfig;
import com.wsgjp.ct.sale.common.config.SystemGlobalConfig;
import com.wsgjp.ct.sale.common.constant.PermissionSysConst;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.entity.eshop.AuthHelpEntity;
import com.wsgjp.ct.sale.common.entity.eshop.EshopRegisterNotify;
import com.wsgjp.ct.sale.common.entity.eshop.NotifyRegisterRequest;
import com.wsgjp.ct.sale.common.enums.MallType;
import com.wsgjp.ct.sale.common.enums.eshoporder.TradeStatus;
import com.wsgjp.ct.sale.common.processlogger.impl.ProcessLoggerImpl;
import com.wsgjp.ct.sale.common.syssecretinfo.BaseSysSecretInfo;
import com.wsgjp.ct.sale.common.syssecretinfo.SysSecretInfoService;
import com.wsgjp.ct.sale.monitor.MonitorService;
import com.wsgjp.ct.sale.monitor.entity.MonitorTypeEnum;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.config.PlatformBaseConfig;
import com.wsgjp.ct.sale.platform.constraint.PlatformConst;
import com.wsgjp.ct.sale.platform.dto.eshop.PlatformEshopSupport;
import com.wsgjp.ct.sale.platform.dto.eshop.PlatformEshoptypeSupport;
import com.wsgjp.ct.sale.platform.dto.eshop.PlatformSupport;
import com.wsgjp.ct.sale.platform.dto.order.entity.ShopRefundAddress;
import com.wsgjp.ct.sale.platform.dto.plugin.DropDownPlugin;
import com.wsgjp.ct.sale.platform.dto.plugin.EshopBusinessConfig;
import com.wsgjp.ct.sale.platform.dto.plugin.EshopTagInfo;
import com.wsgjp.ct.sale.platform.dto.product.EshopProductCategoryRateClass;
import com.wsgjp.ct.sale.platform.dto.shop.OnlineShopEntity;
import com.wsgjp.ct.sale.platform.dto.token.EshopAuthInfo;
import com.wsgjp.ct.sale.platform.dto.token.EshopFieldInfo;
import com.wsgjp.ct.sale.platform.entity.request.BaseRequest;
import com.wsgjp.ct.sale.platform.entity.request.auth.*;
import com.wsgjp.ct.sale.platform.entity.request.plugin.CommonRequest;
import com.wsgjp.ct.sale.platform.entity.request.product.ProductDownloadEnum;
import com.wsgjp.ct.sale.platform.entity.request.stock.EshopStockSyncAuthQueryRequest;
import com.wsgjp.ct.sale.platform.entity.response.auth.EshopAuthEncryptResponse;
import com.wsgjp.ct.sale.platform.entity.response.stock.EshopStockSyncAuthQueryResponse;
import com.wsgjp.ct.sale.platform.entity.response.tmc.TmcRegisterResponse;
import com.wsgjp.ct.sale.platform.enums.CheckAuthType;
import com.wsgjp.ct.sale.platform.enums.EshopBusinessConfigTypeEnum;
import com.wsgjp.ct.sale.platform.enums.SupportMappingType;
import com.wsgjp.ct.sale.platform.enums.TagAttribute;
import com.wsgjp.ct.sale.platform.factory.EshopFactoryManager;
import com.wsgjp.ct.sale.platform.feature.EshopFeature;
import com.wsgjp.ct.sale.platform.feature.EshopTmcRegisterFeature;
import com.wsgjp.ct.sale.platform.feature.invoice.EshopInvoiceFeature;
import com.wsgjp.ct.sale.platform.feature.product.EshopOpenProductUrlFeature;
import com.wsgjp.ct.sale.platform.feature.product.EshopProductSellerClassFeature;
import com.wsgjp.ct.sale.platform.feature.product.EshopProductShelfFeature;
import com.wsgjp.ct.sale.platform.feature.store.EshopOnlineStoreFeature;
import com.wsgjp.ct.sale.platform.feature.store.EshopPlatformWareHouseFeature;
import com.wsgjp.ct.sale.platform.sdk.util.PlatformCommonUtil;
import com.wsgjp.ct.sale.platform.support.EshopRefund;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.dao.entity.SysDataEntity;
import com.wsgjp.ct.support.dao.mapper.SysDataMapper;
import com.wsgjp.ct.support.global.GlobalConfig;
import com.wsgjp.ct.support.kms.KmsClient;
import com.wsgjp.ct.support.log.service.LogService;
import com.wsgjp.ct.support.redis.factory.CacheType;
import ngp.idgenerator.UId;
import ngp.loadbalancer.context.RouteThreadLocal;
import ngp.redis.RedisPoolFactory;
import ngp.starter.web.base.GeneralResult;
import ngp.utils.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedInputStream;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Currency;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-01-06 14:16
 */
@Service
public class EshopService {

    private EshopBizMapper mapper;
    private final RedisPoolFactory redisPoolFactory;
    private final EshopQicConfigMapper eshopQicConfigMapper;
    private EshopOrderBaseInfoMapper eshopOrderBaseInfoMapper;
    private final BaseInfoMapper baseInfoMapper;

    private DeliverApi deliverApi;
    private static final Logger logger = LoggerFactory.getLogger(EshopService.class);
    private SysOtypeConfig sysConfig;
    private final PtypeApi baseApi;
    private ProfileApi profileApi;
    private BaseOtypeMapper baseOtypeMapper;
    private PlatformCheckMapper platformCheckMapper;
    private ServiceConfig serviceConfig;
    private EshopOrderEshopRefundMapper refundMapper;
    private EshopOrderBaseInfoService eshopOrderBaseInfoService;
    private BifrostEshopPluginService eshopPluginService;
    private final MonitorService monitorService;
    private BifrostEshopAuthService bifrostEshopAuthService;
    private BifrostEshopRefundService eshopRefundService;
    private BifrostEshopService eshopService;
    private final BaseInfoApi baseInfoApi;
    private final BifrostEshopStockService bifrostEshopStockService;
    private final BaseKtypeMapper ktypeMapper;
    private final BaseAtypeMapper atypeMapper;
    private final BifrostEshopMapper eshopMapper;
    private final BifrostEshopRefundService bifrostEshopRefundService;
    private final EshopAddressMappingMapper addressMappingMapper;
    private final BifrostQuickLoginConfig quickLoginConfig;

    private final EshopNotifyService eshopNotifyService;

    private final BifrostTmcSupportConfig tmcSupportConfig;

    private final EshopOrderCommonConfig commonConfig;
    private final SysSecretInfoService sysSecretInfoService;
    private static PlatformBranchEshopListener platformBranchEshopListener;
    private final SyncOrderConfig syncOrderConfig;


    public EshopService(EshopBizMapper mapper, RedisPoolFactory redisPoolFactory, EshopQicConfigMapper eshopQicConfigMapper, BaseInfoMapper baseInfoMapper, ServiceConfig serviceConfig, EshopOrderBaseInfoMapper eshopOrderBaseInfoMapper,
                        DeliverApi deliverApi, PtypeApi baseApi,
                        ProfileApi profileApi, BaseOtypeMapper baseOtypeMapper, PlatformCheckMapper platformCheckMapper, EshopOrderEshopRefundMapper refundMapper, EshopOrderBaseInfoService eshopOrderBaseInfoService, MonitorService monitorService, BifrostEshopPluginService eshopPluginService, BifrostEshopAuthService bifrostEshopAuthService, BifrostEshopRefundService eshopRefundService, BifrostEshopService eshopService, BaseInfoApi baseInfoApi, BifrostEshopStockService bifrostEshopStockService, BaseKtypeMapper ktypeMapper, BaseAtypeMapper atypeMapper, BifrostEshopMapper eshopMapper, BifrostEshopRefundService bifrostEshopRefundService, EshopAddressMappingMapper addressMappingMapper, BifrostQuickLoginConfig quickLoginConfig, EshopNotifyService eshopNotifyService, BifrostTmcSupportConfig tmcSupportConfig, EshopOrderCommonConfig commonConfig, SysSecretInfoService sysSecretInfoService, SyncOrderConfig syncOrderConfig) {
        this.mapper = mapper;
        this.redisPoolFactory = redisPoolFactory;
        this.eshopQicConfigMapper = eshopQicConfigMapper;
        this.baseInfoMapper = baseInfoMapper;
        this.serviceConfig = serviceConfig;
        this.eshopOrderBaseInfoMapper = eshopOrderBaseInfoMapper;
        this.deliverApi = deliverApi;
        this.baseApi = baseApi;
        this.profileApi = profileApi;
        this.baseOtypeMapper = baseOtypeMapper;
        this.platformCheckMapper = platformCheckMapper;
        this.refundMapper = refundMapper;
        this.eshopOrderBaseInfoService = eshopOrderBaseInfoService;
        this.monitorService = monitorService;
        this.eshopPluginService = eshopPluginService;
        this.bifrostEshopAuthService = bifrostEshopAuthService;
        this.eshopRefundService = eshopRefundService;
        this.eshopService = eshopService;
        this.baseInfoApi = baseInfoApi;
        this.bifrostEshopStockService = bifrostEshopStockService;
        this.ktypeMapper = ktypeMapper;
        this.atypeMapper = atypeMapper;
        this.eshopMapper = eshopMapper;
        this.bifrostEshopRefundService = bifrostEshopRefundService;
        this.addressMappingMapper = addressMappingMapper;
        this.quickLoginConfig = quickLoginConfig;
        this.eshopNotifyService = eshopNotifyService;
        this.tmcSupportConfig = tmcSupportConfig;
        this.commonConfig = commonConfig;
        this.sysSecretInfoService = sysSecretInfoService;
        this.syncOrderConfig = syncOrderConfig;
    }

    public List<Otype> getAllOtypeList(BigInteger profileId) {
        QueryEShopParameter parameter = new QueryEShopParameter();
        parameter.setOrderField("bo.rowindex ");
        parameter.setProfileId(profileId);
        return getOtypeList(parameter);
    }

    public List<Otype> getOtypeList(QueryEShopParameter parameter) {
        List<Otype> orgList;
        sysConfig = GlobalConfig.get(SysOtypeConfig.class);
        SystemGlobalConfig config = GlobalConfig.get(SystemGlobalConfig.class);
        boolean isRetail = config.isRetailEshopOrderConfig();
        List<Integer> ocategorys = getOcategorys(sysConfig.getOcategorys());
        CommonUtil.initLimited(parameter);
        if (!isRetail) {
            ocategorys.add(OrganizationType.PFB_STORE.getCode());
        }
        orgList = mapper.getOtypeList(parameter, ocategorys);
        buildEshopInfos(orgList);
        return orgList;
    }

    public List<Otype> getAllOtypeListForBiz(BigInteger profileId) {
        List<Otype> list = mapper.getOtypeListForBiz(profileId);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }

        List<Otype> result = new ArrayList<>();
        Map<BigInteger, EshopInfo> eshopMap = getEshopMap(profileId);
        Map<BigInteger, EshopConfig> configMap = getEshopConfigMap(profileId);
        Map<BigInteger, List<EshopOrderSyncCondition>> conditionMap = getConditionMap(profileId);
        for (Otype otype : list) {
            BigInteger id = otype.getId();
            if (!eshopMap.containsKey(id)) {
                continue;
            }
            if (!configMap.containsKey(id)) {
                continue;
            }
            otype.setEshopInfo(eshopMap.get(id));
            otype.setEshopConfig(configMap.get(id));
            if (conditionMap.containsKey(id)) {
                otype.setCondition(conditionMap.get(id));
            }
            setEshopOrderSyncCondition(otype);
            result.add(otype);
        }
        return result;
    }

    private Map<BigInteger, EshopConfig> getEshopConfigMap(BigInteger profileId) {
        List<EshopConfig> configList = mapper.getAllEshopConfig(profileId);
        if (CollectionUtils.isEmpty(configList)) {
            throw new RuntimeException(String.format("账套%s网店配置信息查询为空", profileId));
        }
        Map<BigInteger, EshopConfig> map = new HashMap<>(configList.size());
        for (EshopConfig config : configList) {
            map.put(config.getEshopId(), config);
        }
        return map;
    }

    private Map<BigInteger, EshopInfo> getEshopMap(BigInteger profileId) {
        List<EshopInfo> eshopInfoList = mapper.getAllEshopInfo(profileId);
        if (CollectionUtils.isEmpty(eshopInfoList)) {
            throw new RuntimeException(String.format("账套%s网店信息查询为空", profileId));
        }
        Map<BigInteger, EshopInfo> map = new HashMap<>(eshopInfoList.size());
        for (EshopInfo eshopInfo : eshopInfoList) {
            map.put(eshopInfo.getOtypeId(), eshopInfo);
        }
        return map;
    }

    private Map<BigInteger, List<EshopOrderSyncCondition>> getConditionMap(BigInteger profileId) {
        List<EshopOrderSyncCondition> conditionList = mapper.getAllSyncCondition(profileId);
        if (CollectionUtils.isEmpty(conditionList)) {
            return new HashMap<>();
        }
        return conditionList.stream().collect(Collectors.groupingBy(EshopOrderSyncCondition::getEshopId));
    }

    public List<Otype> getOtypeListByType(EshopPlatformStoreMappingInDTO inDTO) {
        QueryEShopParameter parameter = new QueryEShopParameter();
        parameter.setOrderField("bo.rowindex ");
        parameter.setProfileId(CurrentUser.getProfileId());
        return getOtypeList(parameter, inDTO.getType());
    }

    public List<Otype> getOtypeListByTypeBack(EshopPlatformStoreMappingInDTO inDTO) {
        QueryEShopParameter parameter = new QueryEShopParameter();
        parameter.setOrderField("bo.rowindex ");
        parameter.setProfileId(CurrentUser.getProfileId());
        return getOtypeListBack(parameter, inDTO.getType());
    }

    private List<Otype> getOtypeList(QueryEShopParameter parameter, Integer type) {
        List<Otype> orgList;
        sysConfig = GlobalConfig.get(SysOtypeConfig.class);
        SystemGlobalConfig config = GlobalConfig.get(SystemGlobalConfig.class);
        boolean isRetail = config.isRetailEshopOrderConfig();
        List<Integer> ocategorys = getOcategorys(sysConfig.getOcategorys());
        if (!parameter.isQueryVirtual()) {
            ocategorys.remove(new Integer(OrganizationType.XN_SHOP.getCode()));
        }
        CommonUtil.initLimited(parameter);
        if (isRetail) {
            orgList = mapper.getOtypeList(parameter, ocategorys);

        } else {
            ocategorys.add(OrganizationType.PFB_STORE.getCode());
            orgList = mapper.getOtypeList(parameter, ocategorys);
        }
        //todo 只查询全渠道或者平台仓的需要直接return下方法
        buildEshopInfos(orgList, type);
//        if (StringUtils.isNotEmpty(parameter.getOrderField()) && parameter.getOrderField().contains("rowindex")) {
//            orgList.sort(Comparator.comparing(Otype::getRowindex));
//        }
        List<Otype> eshopList = orgList.stream().filter(x -> !x.isStoped()).collect(Collectors.toList());
        for (Otype otype : orgList) {
            if (!otype.isStoped()) {
                continue;
            }
            otype.setFullname(String.format("(停用)%s", otype.getFullname()));
            eshopList.add(otype);
        }
        return eshopList;
    }

    private List<Otype> getOtypeListBack(QueryEShopParameter parameter, Integer type) {
        List<Otype> orgList;
        sysConfig = GlobalConfig.get(SysOtypeConfig.class);
        SystemGlobalConfig config = GlobalConfig.get(SystemGlobalConfig.class);
        boolean isRetail = config.isRetailEshopOrderConfig();
        List<Integer> ocategorys = getOcategorys(sysConfig.getOcategorys());
        if (!parameter.isQueryVirtual()) {
            ocategorys.remove(new Integer(OrganizationType.XN_SHOP.getCode()));
        }
        CommonUtil.initLimited(parameter);
        if (isRetail) {
            orgList = mapper.getOtypeListBack(parameter, ocategorys);

        } else {
            ocategorys.add(OrganizationType.PFB_STORE.getCode());
            orgList = mapper.getOtypeListBack(parameter, ocategorys);
        }
        //todo 只查询全渠道或者平台仓的需要直接return下方法
        buildEshopInfos(orgList, type);
//        if (StringUtils.isNotEmpty(parameter.getOrderField()) && parameter.getOrderField().contains("rowindex")) {
//            orgList.sort(Comparator.comparing(Otype::getRowindex));
//        }
        List<Otype> eshopList = orgList.stream().filter(x -> !x.isStoped()).filter(x -> ShopType.MiddleGroundSupplier.getCode() != x.getEshopType()).filter(x -> ShopType.QiMenSupplier.getCode() != x.getEshopType()).collect(Collectors.toList());
        buildRefreshIsNotSupported(eshopList);
        for (Otype otype : orgList) {
            if (!otype.isStoped()) {
                continue;
            }
            otype.setFullname(String.format("(停用)%s", otype.getFullname()));
            eshopList.add(otype);
        }
        return eshopList;
    }

    private void buildRefreshIsNotSupported(List<Otype> eshopList) {
        if (CollectionUtils.isNotEmpty(eshopList)){
            for (Otype otype : eshopList) {
                boolean platformWarehouseSupported = EshopUtils.isFeatureSupported(EshopPlatformWareHouseFeature.class, ShopType.valueOf(otype.getEshopType()));
                boolean onlineStoreSupported = EshopUtils.isFeatureSupported(EshopOnlineStoreFeature.class, ShopType.valueOf(otype.getEshopType()));
                if (platformWarehouseSupported || onlineStoreSupported){
                    otype.setPlatformStoreSupport(true);
                }
            }
        }
    }

    private List<Otype> buildEshopInfos(List<Otype> otypeList, Integer type) {
        List<Otype> otypes = new ArrayList<>();
        if (otypeList == null || otypeList.isEmpty()) {
            return otypes;
        }

        for (Otype otype : otypeList) {
            buildOtypeEshopInfo(otype, type);
            otypes.add(otype);
        }
        return otypes;
    }

    private Otype buildOtypeEshopInfo(Otype otype, Integer type) {
        try {
            EshopInfo eshopInfo = mapper.queryEShopInfoByOrgId(otype.getProfileId(), otype.getId());
            if (eshopInfo != null) {
                if (ShopType.Other == eshopInfo.getEshopType()) {
                    return null;
                }
                otype.setEshopInfo(eshopInfo);
            } else {
                throw new RuntimeException("网店信息为空");
            }

            EshopConfig config = mapper.getEshopConfigById(otype.getProfileId(), otype.getId());
            if (config != null) {
                otype.setEshopConfig(config);
            } else {
                throw new RuntimeException("网店配置信息为空");
            }
            setEshopOrderSyncCondition(otype);
        } catch (Exception ex) {
            ex.printStackTrace();
            logger.error(ex.getMessage(), ex);
            throw new RuntimeException(String.format("查询网店报错:%s", ex.getMessage()));
        }
        return otype;
    }

    private void setEshopOrderSyncCondition(Otype otype) {
        List<EshopOrderSyncCondition> condition = mapper.getEshopOrderSyncCondition(otype.getProfileId(), otype.getId());
        if (condition != null && condition.size() > 0) {
            otype.setCondition(condition);
        } else {
            List<EshopOrderSyncCondition> conditions = new ArrayList<>();
            conditions.add(new EshopOrderSyncCondition(otype, TaskType.RefundTask));
            otype.setCondition(conditions);
        }
    }

    private void setEshopProductSyncCondition(Otype otype) {
        List<EshopOrderSyncCondition> condition = mapper.getEshopCondition(otype.getProfileId(), otype.getId());
        if (condition != null && condition.size() > 0) {
            otype.setCondition(condition);
        } else {
            List<EshopOrderSyncCondition> conditions = new ArrayList<>();
            conditions.add(new EshopOrderSyncCondition(otype, TaskType.RefundTask));
            otype.setCondition(conditions);
        }
    }


    private List<Integer> getOcategorys(String ocategorys) {
        List<Integer> arrays = new ArrayList<>();
        if (StringUtils.isEmpty(ocategorys)) {
            arrays.add(OrganizationType.ZY_SHOP.getCode());
            return arrays;
        }
        String[] strs = ocategorys.split(StringConstantEnum.COMMA.getSymbol());
        for (String str : strs) {
            arrays.add(Integer.parseInt(str));
        }
        return arrays;
    }

    public OrganizationPageResponse getOrgList(PageRequest<QueryEShopParameter> pageRequest) {
        QueryEShopParameter parameter = pageRequest.getQueryParams();
        if (parameter == null) {
            parameter = new QueryEShopParameter();
        }
        OrganizationPageResponse pageResponse = new OrganizationPageResponse();
        List<Otype> orgList = new ArrayList<>();
        sysConfig = GlobalConfig.get(SysOtypeConfig.class);
        SystemGlobalConfig config = GlobalConfig.get(SystemGlobalConfig.class);
        boolean isRetail = config.isRetailEshopOrderConfig();
        CommonUtil.initLimited(parameter);
        List<Integer> ocategorys = getQueryOcategoryList(parameter, isRetail);
        Integer total = mapper.getOtypePageListCount(parameter, ocategorys);
        total = null == total ? 0 : total;
        PageDevice.initPage(pageRequest);
        orgList = mapper.getOtypeList(parameter, ocategorys);
        pageResponse.setTotal(total);
        if (isRetail) {
            if (parameter.isQueryVirtual()) {
                pageResponse.setProductId(IntConstants.RETAIL_PRODUCT_ID);
            } else {
                pageResponse.setProductId(IntConstants.RETAIL_PRODUCT_ID);
            }
        } else {
            pageResponse.setProductId(IntConstants.WHOLESALE_PRODUCT_ID);
        }


        //展示有值的有效期
        for (Otype otype : orgList) {
            if (Objects.isNull(otype.getTokenExpireIn())) {
                otype.setTokenExpireIn(otype.getTokenR1ExpireIn());
            }
            buildAuthStatus(otype);
        }
        pageResponse.setOrgList(orgList);
        if (CollectionUtils.isEmpty(orgList)) {
            return pageResponse;
        }
        for (Otype org : orgList) {
            String operation = StringConstantEnum.BEGIN_SQUARE_BRACKETS.getSymbol();
            if (!ShopType.Other.equals(org.getShopType())) {
                EshopState state = new EshopState();
                state.setProductId(RouteThreadLocal.getRoute().getProductId());
                state.setMutiSelectAppkey(org.getMutiSelectAppkey());
                state.setShopType(org.getShopType());
                EshopTagInfo eshopTag = eshopPluginService.getEshopTag(state);
                if (eshopTag != null) {
                    logger.info("网店eshopTag:" + JSONObject.toJSONString(eshopTag));
                    logger.info("网店信息:" + JSONObject.toJSONString(org));
                } else {
                    logger.info("网店eshopTag为null,网店名称：" + JSONObject.toJSONString(org));
                }
                if (eshopTag != null) {
                    if (parameter.getHasAuth() != null && 1 == parameter.getHasAuth()) {
                        if (!eshopTag.isHasAuth()) {
                            continue;
                        }
                    } else if (parameter.getHasAuth() != null && 2 == parameter.getHasAuth()) {
                        if (eshopTag.isHasAuth()) {
                            continue;
                        }
                    }
                    if (ShopType.MiddleGroundDingHuo.equals(org.getShopType()) || ShopType.MiddleGround.equals(org.getShopType()) || ShopType.YaoShiBang.equals(org.getShopType())) {
                        operation += "{'publish':'<font CssClass=SkinColor>网店商品发布</font>'},";
                    }
                    if (eshopTag.isShowOrderLink() && !org.getOcategory().equals(OrganizationType.XN_SHOP)) {
                        String shopAccount = org.getEshopAccount().replace("'","\\'");
                        shopAccount = shopAccount.replace("\"","\\\"");
                        operation += "{'order':'<font CssClass=SkinColor title=请使用【" + shopAccount + "】主账号登录服务市场进行订购>订购</font>'},";
                        org.setOrderLink(eshopTag.getOrderLink());
                    }
                    if (eshopTag.isShowAuth() && !org.getOcategory().equals(OrganizationType.XN_SHOP) && org.isTmallSpecialSale() == false && !org.getOcategory().equals(OrganizationType.YDH_STORE)) {
                        operation += "{'auth':'<font CssClass=SkinColor>授权</font>'},";
                        org.setAuthType(eshopTag.getAuthType().getCode());
                    }
                    if (eshopTag.isShowRefundLink()) {
                        operation += "{'refund':'<font CssClass=SkinColor>退网店接口打通费</font>'},";
                        org.setRefundLink(eshopTag.getRefundLink());
                        org.setRefundShopType(eshopTag.getRefundShopType());
                    }
                }

            }
//            operation += "{'config':'<font color=blue>网店设置</font>'},";
            if (ShopType.Doudian.equals(org.getShopType())) {
                operation += "{'saleRegister':'<font CssClass=SkinColor>营销活动报名</font>'},";
            }
            operation += StringConstantEnum.END_SQUARE_BRACKETS.getSymbol();
            org.setOperation(operation);
            if (org.isMainEshop()) {
                org.setPid(BigInteger.ZERO);
            }
            if ((!org.isMainEshop()) && StringUtils.isNotEmpty(org.getGroupId())) {
                List<Otype> collect = orgList.stream().filter(x -> x.isMainEshop() && org.getGroupId().equals(x.getGroupId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    org.setPid(collect.get(0).getId());
                }
            }
//            List<EshopInfo> eshopBranchInfoByGroupId = mapper.getEshopBranchInfoByGroupId(CurrentUser.getProfileId(), org.getGroupId());
//            if (org.getEshopType() == ShopType.EleMeRetail.getCode() && (!org.isMainEshop()) && StringUtils.isNotEmpty(org.getGroupId()) && eshopBranchInfoByGroupId.size() > 0) {
//                if (eshopBranchInfoByGroupId.stream().anyMatch(e -> e.getOtypeId().compareTo(org.getId()) == 0)) {
//                    operation = "[{'config':'<font color=blue>网店设置</font>'}]";
//                }
//            }
//            if ((org.getEshopType() == ShopType.MeiTuan.getCode() || org.getEshopType() == ShopType.MeiTuanMaiYao.getCode()) && org.isMainEshop() && StringUtils.isNotEmpty(org.getGroupId())) {
//                operation = "[{'config':'<font color=blue>网店设置</font>'}]";
//            }
            org.setOperation(operation);
        }
        List<Integer> integerList = orgList.stream().map(Otype::getDeliverDuration).distinct().collect(Collectors.toList());
        integerList.addAll(Arrays.asList(-1, 12, 24, 48, 72));
        List<Integer> list = integerList.stream().distinct().sorted().collect(Collectors.toList());
        StringBuilder builder = new StringBuilder();
        for (Integer item : list) {
            if (item.equals(-1)) {
                builder.append(item).append("=无时效,");
            } else {
                builder.append(item).append("=").append(item).append(",");
            }
        }
        pageResponse.setDeliverDurationList(builder.substring(0, builder.length() - 1));
        return pageResponse;
    }

    public OrganizationPageResponse getOrgListByDownload(PageRequest<QueryEShopParameter> pageRequest) {
        QueryEShopParameter parameter = pageRequest.getQueryParams();
        if (parameter == null) {
            parameter = new QueryEShopParameter();
        }
        OrganizationPageResponse pageResponse = new OrganizationPageResponse();
        List<Otype> orgList = new ArrayList<>();
        sysConfig = GlobalConfig.get(SysOtypeConfig.class);
        SystemGlobalConfig config = GlobalConfig.get(SystemGlobalConfig.class);
        boolean isRetail = config.isRetailEshopOrderConfig();
        CommonUtil.initLimited(parameter);
        if (isRetail) {
            if (parameter.isQueryVirtual()) {
                List<Integer> ocategorys = getOcategorys(sysConfig.getOcategorys());
                ocategorys.add(OrganizationType.XN_SHOP.getCode());
                PageDevice.initPage(pageRequest);
                PageResponse<Otype> response = PageDevice.readPage(mapper.getOtypeList(parameter, ocategorys));
                orgList = response.getList();
                pageResponse.setTotal(Math.toIntExact(response.getTotal()));
            } else {
                List<Integer> ocategorys = getOcategorys(sysConfig.getOcategorys());
                if (!parameter.isQueryVirtual()) {
                    ocategorys.remove(new Integer(OrganizationType.XN_SHOP.getCode()));
                }
                PageDevice.initPage(pageRequest);
                PageResponse<Otype> response = PageDevice.readPage(mapper.getOtypeList(parameter, ocategorys));
                orgList = response.getList();
                pageResponse.setTotal(Math.toIntExact(response.getTotal()));
            }
        } else {
            List<Integer> ocategorys = getOcategorys(sysConfig.getOcategorys());
            if (!parameter.isNoQueryPifa()) {
                ocategorys.add(OrganizationType.PFB_STORE.getCode());
            }
            PageDevice.initPage(pageRequest);
            PageResponse<Otype> response = PageDevice.readPage(mapper.getOtypeList(parameter, ocategorys));
            orgList = response.getList();
            pageResponse.setTotal(Math.toIntExact(response.getTotal()));
        }

        //展示有值的有效期
        for (Otype otype : orgList) {
            if (Objects.isNull(otype.getTokenExpireIn())) {
                otype.setTokenExpireIn(otype.getTokenR1ExpireIn());
            }
            buildAuthStatus(otype);
        }
        pageResponse.setOrgList(orgList);
        return pageResponse;
    }

    /**
     * 获取网店设置网店列表查询的category参数
     *
     * @param parameter 查询参数
     * @param isRetail  是否零售
     * @return category参数
     */
    private List<Integer> getQueryOcategoryList(QueryEShopParameter parameter, boolean isRetail) {
        List<Integer> ocategorys = getOcategorys(sysConfig.getOcategorys());
        if (CollectionUtils.isNotEmpty(parameter.getOcategorys())) {
            ocategorys.addAll(parameter.getOcategorys());
        }
        if (isRetail) {
            if (parameter.isQueryVirtual()) {
                ocategorys.add(OrganizationType.XN_SHOP.getCode());

            } else if (!parameter.isQueryVirtual()) {
                ocategorys.remove(new Integer(OrganizationType.XN_SHOP.getCode()));
            }
        } else if (!parameter.isNoQueryPifa()) {
            ocategorys.add(OrganizationType.PFB_STORE.getCode());
        }
        ocategorys = ocategorys.stream().distinct().collect(Collectors.toList());
        return ocategorys;
    }

    public boolean processRefundOnline(EShopPageInfo eshopInfo) {
        EshopInfo info = eshopMapper.getEshopInfoByShopId(CurrentUser.getProfileId(), eshopInfo.getOtypeId());
        Otype org = new Otype();
        org.setShopType(info.getEshopType());
        org.setPlatformEshopSnType(info.getPlatformEshopSnType());
        return supportEshopTypes(org);
    }

    // 处理线上售后单 支持的网店
    private static boolean supportEshopTypes(Otype org) {
        ShopType shopType = org.getShopType();
        if (supportPlatformHandleShopType(shopType)) {
            return true;
        }
        if (org.getShopType().equals(ShopType.WeiMobEC)) {
            if (!StringUtils.isEmpty(org.getPlatformEshopSnType()) && "2".equals(org.getPlatformEshopSnType())) {
                return true;
            }
        }
        return false;
    }

    public static boolean supportPlatformHandleShopType(ShopType shopType) {
        return shopType.equals(ShopType.Doudian)
                || shopType.equals(ShopType.PinDuoDuo)
                || shopType.equals(ShopType.VipJitX)
                || shopType.equals(ShopType.KuaiShou)
                || shopType.equals(ShopType.TaoBao)
                || shopType.equals(ShopType.Tmall)
                || shopType.equals(ShopType.Weidian)
                || shopType.equals(ShopType.WeChatVideoShop)
                || shopType.equals(ShopType.MiddleGroundDingHuo)
                || shopType.equals(ShopType.DouDianInstantShopping)
                || shopType.equals(ShopType.MeiTuanDS)
                || shopType.equals(ShopType.MeiTuan)
                || shopType.equals(ShopType.Xiaohongshu)
                || shopType.equals(ShopType.QiMenSupplier)
                || shopType.equals(ShopType.TuHuYangChe)
                || shopType.equals(ShopType.HeLiang)
                || shopType.equals(ShopType.AlibabaSZXD)
                || shopType.equals(ShopType.MeiTuanMaiYao)
                || shopType.equals(ShopType.XiaoMang)
                || shopType.equals(ShopType.WanWuXinXuan)
                || shopType.equals(ShopType.YaoFangWang);
    }

    private String doBuildTagAttribute(Otype org, EshopTagInfo eshopTag) {
        StringBuilder attrOperation = new StringBuilder();
        List<TagAttribute> attributeList = eshopTag.getAttributeList();
        if (CollectionUtils.isEmpty(attributeList)) {
            return attrOperation.toString();
        }
        for (TagAttribute attribute : attributeList) {
            String desc = attribute.getName();
            String key = attribute.toString();
            String subName = String.format("%s_%s", key, org.getId());
            String oldData = GlobalConfig.get(subName);
            if (StringUtils.isNotEmpty(oldData) && "1".equals(oldData)) {
                attrOperation.append(String.format("{'callback':'<font color=blue>关闭%s</font>'},", desc));
            } else {
                attrOperation.append(String.format("{'callback':'<font color=blue>开启%s</font>'},", desc));
            }
        }
        return attrOperation.toString();
    }

    private void buildAuthStatus(Otype otype) {
        if (otype.getOcategory().equals(OrganizationType.YDH_STORE)) {
            return;
        }
        boolean auth = checkAuthTypeWithIn(otype);
        otype.setAuth(auth);
        StringBuilder sbBtn = new StringBuilder();
        sbBtn.append("[");
        HashMap<String, String> hp = new HashMap();
        Date expireTime = otype.getTokenExpireIn() != null ? otype.getTokenExpireIn() : otype.getTokenR1ExpireIn();
        Date now = new Date();
        boolean isExpire = false;
        if (null != expireTime) {
            isExpire = expireTime.getTime() < now.getTime();
        }
        if (isExpire) {
            hp.put("2", String.format("<font color=\'white\' style=\' background-color:#E44A3D;border-radius:3px;padding:2px;padding-left:4px;padding-right:4px\'>授权过期</font>"));
        }
        if (auth && !isExpire) {
            hp.put("0", String.format("<font color=\'white\' style=\' background-color:#3FBB00;border-radius:3px;padding:2px;padding-left:4px;padding-right:4px\'>已授权</font>"));
        }
        if (!auth && !isExpire) {
            hp.put("1", String.format("<font color=\'white\' style=\' background-color:#8B9197;border-radius:3px;padding:2px;padding-left:4px;padding-right:4px\'>未授权</font>"));
        }
        if (otype.getRealStockQtyEnabled() && otype.getShopType() == ShopType.JdongVC && auth && !isExpire) {
            hp.put("3", String.format("<font color=\'white\' title='已在京东授权京东查询真实库存，库存同步开启后，将限制部分库存同步功能的使用，并定时自动向京东同步库存。'  style=\' background-color:#fdc526;border-radius:3px;padding:2px;padding-left:4px;padding-right:4px\'>真实库存</font>"));
        }
        sbBtn.append(JsonUtils.toJson(hp));
        sbBtn.append("]");
        otype.setEshopAuthMark(sbBtn.toString());
        return;
    }

    public void checkEshopTypeQueryParameter(QueryEShopParameter parameter) {
        List<ShopType> shopTypes = EshopUtils.listSupportedAutoSyncOrderTypes();
        if (CollectionUtils.isEmpty(shopTypes)) {
            return;
        }
        List<Integer> types = shopTypes.stream().map(ShopType::getCode).collect(Collectors.toList());
        parameter.setShopTypes(types);
    }

    public boolean checkAuthType(BigInteger profileId, ShopType shopType, BigInteger shopId) {
        boolean isAuth;
        CommonRequest commonRequest = new CommonRequest();
        commonRequest.setShopType(shopType);
        CheckAuthType checkAuthType = eshopPluginService.getCheckAuthType(commonRequest);
        if (checkAuthType == CheckAuthType.TOKEN_EXPIRE) {
            isAuth = mapper.getEshopIsAuthByExpireTime(profileId, shopId);
        } else if (checkAuthType == CheckAuthType.TOKEN) {
            isAuth = mapper.getEshopIsAuthByToken(profileId, shopId);
        } else if (checkAuthType == CheckAuthType.ONLINESHOP_ID) {
            isAuth = mapper.getEshopIsAuthByOnlineShopId(profileId, shopId);
        } else {
            isAuth = mapper.getEshopIsAuthByAppkey(profileId, shopId);
        }
        return isAuth;
    }

    public boolean checkAuthTypeByEshopInfo(Otype otype) {
        boolean isAuth;
        CommonRequest commonRequest = new CommonRequest();
        commonRequest.setShopType(otype.getShopType());
        CheckAuthType checkAuthType = eshopPluginService.getCheckAuthType(commonRequest);
        if (checkAuthType == CheckAuthType.TOKEN_EXPIRE) {
            isAuth = null != otype.getTokenExpireIn() && otype.getTokenExpireIn().after(new Date());
        } else if (checkAuthType == CheckAuthType.TOKEN) {
            isAuth = StringUtils.isNotEmpty(otype.getToken());
        } else if (checkAuthType == CheckAuthType.ONLINESHOP_ID) {
            isAuth = StringUtils.isNotEmpty(otype.getOnlineEshopId());
        } else {
            isAuth = StringUtils.isNotEmpty(otype.getAppKey());
        }
        return isAuth;
    }

    public boolean checkAuthTypeWithIn(Otype otype) {
        boolean isAuth;
        CommonRequest commonRequest = new CommonRequest();
        commonRequest.setShopType(otype.getShopType());
        CheckAuthType checkAuthType = eshopPluginService.getCheckAuthType(commonRequest);
        if (checkAuthType == CheckAuthType.TOKEN_EXPIRE) {
            isAuth = null != otype.getTokenExpireIn() && otype.getTokenExpireIn().after(new Date());
        } else if (checkAuthType == CheckAuthType.TOKEN) {
            isAuth = StringUtils.isNotEmpty(otype.getToken());
        } else if (checkAuthType == CheckAuthType.ONLINESHOP_ID) {
            isAuth = StringUtils.isNotEmpty(otype.getOnlineEshopId());
        } else {
            isAuth = StringUtils.isNotEmpty(otype.getAppKey());
        }
        return isAuth;
    }

    private void buildEshopInfos(List<Otype> otypeList) {
        if (otypeList == null || otypeList.size() == 0) {
            return;
        }

        for (Otype otype : otypeList) {
            buildOtypeEshopInfo(otype);
        }
    }

    private void buildOtypeEshopInfo(Otype otype) {
        try {
            if (null == otype.getId() || otype.getId().compareTo(BigInteger.ZERO) == 0) {
                throw new RuntimeException("请传入网店相关信息");
            }
            EshopInfo eshopInfo = mapper.queryEShopInfoByOrgId(otype.getProfileId(), otype.getId());
            if (eshopInfo != null) {
                otype.setEshopInfo(eshopInfo);
            } else {
                throw new RuntimeException("网店信息为空");
            }

            EshopConfig config = mapper.getEshopConfigById(otype.getProfileId(), otype.getId());
            if (config != null) {
                otype.setEshopConfig(config);
                initBtypeId(otype);
            } else {
                throw new RuntimeException("网店配置信息为空");
            }
            setEshopOrderSyncCondition(otype);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            throw new RuntimeException(String.format("查询网店报错:%s", ex.getMessage()));
        }
    }

    private void buildOtypeEshopInfoByproductTool(Otype otype) {
        try {
            if (null == otype.getId() || otype.getId().compareTo(BigInteger.ZERO) == 0) {
                throw new RuntimeException("请传入网店相关信息");
            }
            EshopInfo eshopInfo = mapper.queryEShopInfoByOrgId(otype.getProfileId(), otype.getId());
            if (eshopInfo != null) {
                otype.setEshopInfo(eshopInfo);
            } else {
                throw new RuntimeException("网店信息为空");
            }

            EshopConfig config = mapper.getEshopConfigById(otype.getProfileId(), otype.getId());
            if (config != null) {
                otype.setEshopConfig(config);
                initBtypeId(otype);
            } else {
                throw new RuntimeException("网店配置信息为空");
            }
            setEshopProductSyncCondition(otype);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            throw new RuntimeException(String.format("查询网店报错:%s", ex.getMessage()));
        }
    }

    private void initBtypeId(Otype otype) {
        EshopConfig config = otype.getEshopConfig();
        BigInteger btypeId = otype.getBtypeId();
        GenerateBtypeType generateType = config.getBtypeGenerateType();
        if (!generateType.equals(GenerateBtypeType.GENERATE_BY_OTYPE)) {
            return;
        }
        if (btypeId != null && btypeId.compareTo(BigInteger.ZERO) > 0) {
            //addBaseBtype(otype, btypeId);
        } else {
            addBaseBtype(otype, btypeId);
        }

    }

    private void addBaseBtype(Otype otype, BigInteger btypeId) {
        Btype btype = eshopOrderBaseInfoMapper.getBaseBtypeById(CurrentUser.getProfileId(), btypeId);
        if (btype != null) {
            return;
        }
        try {
            String name = otype.getFullname();
            String code = otype.getUsercode();
            if (StringUtils.isEmpty(code)) {
                code = name;
            }
            BaseBtype baseBtype = new BaseBtype(name, code);
            GeneralResult<Object> generalResult = baseApi.saveBtype(baseBtype);
            if (generalResult.getCode() == Integer.parseInt(StringConstantEnum.SUCCESS_CODE.getSymbol())) {
                BigInteger id = JsonUtils.toObject(JsonUtils.toJson(generalResult.getData()), BigInteger.class);
                otype.setBtypeId(id);
                otype.setBtypeName(name);
                BaseOtype baseOtype = new BaseOtype();
                BeanUtils.copyProperties(otype, baseOtype);
                //存在事务风险
                baseOtypeMapper.updateByPrimaryKeySelective(baseOtype);
            } else {
                ArrayList<BaseBtypeErrorResponse> baseBtypeErrorResponses = JsonUtils.toList(JsonUtils.toJson(generalResult.getData()), BaseBtypeErrorResponse.class);
                throw new RuntimeException(baseBtypeErrorResponses.get(0).getMessage());
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            throw new RuntimeException("网店缺失默认往来单位,生成往来单位时报错：" + ex.getMessage());
        }
    }


    public <T extends EshopFeature> boolean isFunctionImplemented(EshopSystemParams systemParams, Class<T> feature) {
        return EshopUtils.isFeatureSupported(feature, systemParams.getShopType());
    }

    public Otype getOrganizationById(QueryEShopParameter parameter) {
        return mapper.getOrganizationById(parameter);
    }

    public Otype getOtypeByIdByProductTool(BigInteger otypeId) {
        QueryEShopParameter parameter = new QueryEShopParameter();
        parameter.setOtypeId(otypeId);
        Otype otype = mapper.getOrganizationById(parameter);
        buildOtypeEshopInfoByproductTool(otype);
        return otype;
    }

    public Otype getOtypeById(BigInteger otypeId) {
        QueryEShopParameter parameter = new QueryEShopParameter();
        parameter.setOtypeId(otypeId);
        Otype otype = mapper.getOrganizationById(parameter);
        buildOtypeEshopInfo(otype);
        return otype;
    }

    public Otype getOtypeByBtypeId(BigInteger btypeId, ShopType shopType) {
        QueryEShopParameter parameter = new QueryEShopParameter();
        parameter.setBtypeId(btypeId);
        parameter.setShopType(shopType);
        Otype otype = mapper.getOrganizationByBtypeId(parameter);
        if (null == otype) {
            return otype;
        }
        buildOtypeEshopInfo(otype);
        return otype;
    }

    public Otype getOtypeByFullName(String fullName) {
        QueryEShopParameter parameter = new QueryEShopParameter();
        parameter.setFullname(fullName);
        Otype otype = mapper.getOrganizationByFullName(parameter);
        if (null == otype || null == otype.getId() || otype.getId().compareTo(BigInteger.ZERO) == 0) {
            return null;
        }
        buildOtypeEshopInfo(otype);
        return otype;
    }

    public List<Otype> getOtypeByToken(String token) {
        QueryEShopParameter parameter = new QueryEShopParameter();
        parameter.setToken(token);
        List<Otype> otypeList = mapper.getOrganizationByToken(parameter);
        if (CollectionUtils.isEmpty(otypeList)) {
            return otypeList;
        }
        for (Otype otype : otypeList) {
            buildOtypeEshopInfo(otype);
        }
        return otypeList;
    }

    public EShopPageInfo queryEShopPageInfo(QueryEShopParameter parameter) {
        return mapper.queryEShopPageInfo(parameter);
    }

    public EshopPageCategoryClassResponse queryEShopPageResponse(QueryEShopParameter parameter) {
        EshopPageCategoryClassResponse response = new EshopPageCategoryClassResponse();
        EShopPageInfo eShopPageInfo = queryEShopPageInfo(parameter);
        response.setPageInfo(eShopPageInfo);
        if (eShopPageInfo == null) {
            return null;
        }
        EshopInfo eshop = getEshopInfo(parameter, eShopPageInfo);
        boolean isEnablePlatformCommission = false;
        boolean allowBuyerAccount = false;
        EshopState state = new EshopState();
        state.setProductId(RouteThreadLocal.getRoute().getProductId());
        state.setMutiSelectAppkey(eshop.getMutiSelectAppkey());
        state.setShopType(eshop.getEshopType());
        if (eshopPluginService.getEshopTag(state) != null) {
            EshopTagInfo tagInfo = eshopPluginService.getEshopTag(state);
            isEnablePlatformCommission = tagInfo.isEnablePlatformCommission();
            allowBuyerAccount = tagInfo.isAllowBuyerAccount();
        }
        List<EshopProductCategoryRateClass> productClasses = new ArrayList<>();
        response.setAllowPlatformCommission(isEnablePlatformCommission);
        response.setAllowBuyerAccount(allowBuyerAccount);
        response.setProductClasses(productClasses);

        return response;
    }

    public EShopPageInfo queryEshopPageInfoById(BigInteger eshopId) {
        QueryEShopParameter param = new QueryEShopParameter();
        param.setEshopId(eshopId);
        param.setOtypeId(eshopId);
        return queryEShopPageInfo(param);
    }

    public List<PlatformSupport> getCurrentUserSupportGetCategoryPlatformTypes() {
        List<EshopInfo> eshopInfos = eshopMapper.getEshopInfoByProfileId(CurrentUser.getProfileId());
        if (CollectionUtils.isEmpty(eshopInfos)) {
            return new ArrayList<>();
        }
        Set<PlatformSupport> platformSupportList = new HashSet<>();
        Set<Integer> curUserPlatformTypes = eshopInfos.stream().map(EshopInfo::getEshopSalePlatform).collect(Collectors.toSet());
        List<ShopType> shopTypes = EshopUtils.listFeatureSupportedShopTypes(EshopProductSellerClassFeature.class.getSimpleName());
        for (ShopType shopType : shopTypes) {
            if (curUserPlatformTypes.contains(shopType.getPlatformType())) {
                PlatformSupport platformSupport = new PlatformSupport();
                platformSupport.setPlatformType(shopType.getPlatformType());
                platformSupport.setName(shopType.getPlatformName());
                platformSupportList.add(platformSupport);
            }
        }
        return new ArrayList<>(platformSupportList);
    }

    public EshopPageResponse getEshopPageResponse(QueryEShopParameter parameter) {
        EshopPageResponse response = new EshopPageResponse();
        EShopPageInfo eShopPageInfo = buildEshopPageInfo(parameter);
        EshopInfo eshop = getEshopInfo(parameter, eShopPageInfo);
        CommonRequest commonRequest = new CommonRequest();
        commonRequest.setShopType(eshop.getEshopType());
        boolean isEnablePlatformCommission = false;
        boolean allowBuyerAccount = false;
        EshopState state = new EshopState();
        state.setShopType(eshop.getEshopType());
        state.setProductId(RouteThreadLocal.getRoute().getProductId());
        state.setMutiSelectAppkey(eshop.getMutiSelectAppkey());
        EshopTagInfo eshopTag = eshopPluginService.getEshopTag(state);
        if (eshopTag != null) {
            if (eShopPageInfo.getProcessType() == null) {
                eShopPageInfo.setProcessType(eshopTag.getProcess_type());

            }
            response.setAssignAccountType(eshopTag.getAssignAccountType());
            if (eshopTag.isShowOrderLink()) {
                eShopPageInfo.setNeedOrder(true);
                eShopPageInfo.setOrderLink(eshopTag.getOrderLink());
            }
            if (eshopTag.isShowAuth()) {
                boolean validate = false;
                try {
                    validate = PermissionValiateService.validate(PermissionSysConst.ESHOP_AUTH);
                } catch (RuntimeException e) {
                    if (!e.getMessage().contains("未查询到该权限点")) {
                        throw e;
                    }
                }
                eShopPageInfo.setNeedAuth(validate);
                eShopPageInfo.setAuthType(eshopTag.getAuthType());
            }
            eShopPageInfo.setHasAuth(eshopTag.isHasAuth());

            List<SupportMappingType> mappingTypes = eshopPluginService.getSupportMappingTypeList(commonRequest);
            if (mappingTypes != null && mappingTypes.size() == 1 && mappingTypes.contains(SupportMappingType.XCODE)) {
                eShopPageInfo.setXcodeMappingRequired(true);
            } else if (mappingTypes != null && mappingTypes.size() > 1 && eShopPageInfo.getMode() == EshopInfoConstants.ADD_MODE) {
                eShopPageInfo.setMappingType(MappingType.XCODEMAPPING);
            } else if (eShopPageInfo.getMode() == EshopInfoConstants.ADD_MODE) {
                eShopPageInfo.setMappingType(MappingType.NOMARL);
            }

            if (eShopPageInfo.isXcodeMappingRequired()) {
                eShopPageInfo.setMappingType(MappingType.XCODEMAPPING);
            }
            isEnablePlatformCommission = eshopTag.isEnablePlatformCommission();
            allowBuyerAccount = eshopTag.isAllowBuyerAccount();
            if (eshopTag.getMallType() >= 0) {
                MallType mallType = MallType.getMallType(eshopTag.getMallType());
                mallType = mallType == null ? MallType.NOMARL : mallType;
                eShopPageInfo.setMallType(mallType);
            }
            //appKey多选处理
            eShopPageInfo.setSubscribeApplications(eshopTag.getSubscribeApplications());
        } else {
            eShopPageInfo.setMappingType(MappingType.XCODEMAPPING);
            eShopPageInfo.setMallType(MallType.NOMARL);
        }

        if (eshopPluginService.getFiledInfo(commonRequest) != null) {
            List<EshopFieldInfo> filedInfos = eshopPluginService.getFiledInfo(commonRequest);
            eShopPageInfo.setFieldInfos(filedInfos);
        }
        boolean isAuth = checkAuthType(CurrentUser.getProfileId(), eshop.getEshopType(), eshop.getOtypeId());
        eShopPageInfo.setHasAuth(isAuth);
        response.setAllowBuyerAccount(allowBuyerAccount);
        response.setAllowPlatformCommission(isEnablePlatformCommission);
        buildEshopQicConfig(eShopPageInfo);
        response.setPageInfo(eShopPageInfo);
        if (!parameter.isNotQueryEshop()) {
            List<Atype> atypes = eshopOrderBaseInfoService.getAtypeList(parameter.getProfileId());
            List<Stock> stocks = eshopOrderBaseInfoMapper.getStockList(parameter.getProfileId(), null);
            List<Currency> currencies = eshopOrderBaseInfoMapper.getCurrencyList(parameter.getProfileId());
            String shopTypeSource = CommonUtil.getEnumSource("com.wsgjp.ct.common.enums.core.enums.ShopType");
            atypes = atypes == null ? new ArrayList<>() : atypes;
            stocks = atypes.size() == 0 ? new ArrayList<>() : stocks;
            currencies = atypes.size() == 0 ? new ArrayList<>() : currencies;
            shopTypeSource = CommonUtil.isNullOrEmpty(shopTypeSource) ? "" : shopTypeSource;
            shopTypeSource = shopTypeSource.replace("-1=错误的网店,", "");
            response.setAtypes(atypes);
            response.setStocks(stocks);
            response.setCurrencies(currencies);
            response.setShopTypeSource(shopTypeSource);
        }
        return response;
    }

    private void buildEshopQicConfig(EShopPageInfo eShopPageInfo) {
        List<EshopQicConfig> qualityOrgByOtypeId = eshopQicConfigMapper.getQualityOrgByOtypeId(CurrentUser.getProfileId(), eShopPageInfo.getOtypeId());
        eShopPageInfo.setEshopQicConfig(qualityOrgByOtypeId.stream().filter(x -> x.isPlatformQualityType() == false).findFirst().orElse(null));
        eShopPageInfo.setEshopBatsConfig(qualityOrgByOtypeId.stream().filter(x -> x.isPlatformQualityType()).findFirst().orElse(null));
    }


    public EshopInfo getEshopInfoById(BigInteger profileId, BigInteger shopId) {
        try {
            return mapper.getEshopInfoById(profileId, shopId);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            throw new RuntimeException("查询网店报错：" + ex.getMessage(), ex);
        }
    }

    public void updateEshopTokenSimple(BigInteger profileId, BigInteger shopId, String token) {
        try {
            mapper.updateEshopTokenSimple(profileId, shopId, token);
            String msgToken = StringUtils.isNotEmpty(token) ? "***" : "";
            logger.error("排查授权被清空，EshopService.updateEshopTokenSimple ，profileid: {}, eshopid:{},token:{}", CurrentUser.getProfileId(), shopId, msgToken);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            throw new RuntimeException("修改token：" + ex.getMessage(), ex);
        }
    }

    public EshopInfo getEshopInfoByshopAccount(BigInteger profileId, String shopAccount) {
        try {
            List<EshopInfo> eshopInfos = mapper.queryEShopByShopAccount(shopAccount, profileId);
            if (CollectionUtils.isNotEmpty(eshopInfos)) {
                return eshopInfos.get(0);
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            throw new RuntimeException("查询网店报错：" + ex.getMessage(), ex);
        }
        return null;
    }

    public EshopInfo queryEShopInfoByOrgId(QueryEShopParameter parameter) {
        return mapper.queryEShopInfoByOrgId(parameter.getProfileId(), parameter.getOtypeId());
    }

    public EshopConfig getEshopConfigById(BigInteger profileId, BigInteger shopId) {
        return mapper.getEshopConfigById(profileId, shopId);
    }

    public BaseResponse authCheck(QueryEShopParameter parameter) {
        try {
            BaseResponse response = new BaseResponse();
            //网点数验证
            if (!parameter.isAuth()) {
                NotifyRegisterRequest req = new NotifyRegisterRequest();
                req.setId(CurrentUser.getProfileId().toString());
                try {
                    Integer productId = commonConfig.getProductId();
                    req.setProductId((productId == null || productId == 0) ? CurrentUser.getProductId() : productId);
                } catch (Exception e) {
                    req.setProductId(CurrentUser.getProductId());
                }
                ProfileInfo profileAssets = profileApi.getProfileAssets(req);
                List<Integer> ocategorys = getOcategorys(sysConfig.getOcategorys());
                ocategorys.add(OrganizationType.XN_SHOP.getCode());
                List<Otype> orgList = mapper.getOtypeList(parameter, ocategorys);
                int allowEshopCount = profileAssets.getEshopCount();
                int authedEshopCount = 0;
                for (Otype o : orgList) {
                    boolean isAuth = checkAuthType(o.getProfileId(), ShopType.valueOf(o.getEshopType()), o.getId());
                    if (isAuth && !ShopType.MiddleGroundDistributor.equals(o.getShopType()) && !ShopType.MiddleGroundDingHuo.equals(o.getShopType()) && !ShopType.MiddleGroundSupplier.equals(o.getShopType())) {
                        authedEshopCount += 1;
                    }
                }
                //云订货不算占用网店数
                if (ShopType.MiddleGroundDingHuo.getCode() == parameter.getShopType().getCode()) {
                    authedEshopCount = -1;
                }
                if (allowEshopCount <= authedEshopCount) {
                    response.setSuccess(false);
                    response.setMessage("已授权网店数大于当前允许授权网店数" + allowEshopCount);
                    return response;
                }
            }
            //订购验证
            EshopInfo eshop = new EshopInfo();
            eshop.setProfileId(parameter.getProfileId());
            eshop.setOtypeId(parameter.getOtypeId());
            eshop.setEshopType(parameter.getShopType());
            EShopPageInfo eShopPageInfo = buildEshopPageInfo(parameter);
            CheckSubscribesRequest checkSubscribesRequest = new CheckSubscribesRequest();
            checkSubscribesRequest.setShopAccount(eShopPageInfo.getEshopAccount());
            checkSubscribesRequest.setShopType(parameter.getShopType());
            boolean validate = bifrostEshopAuthService.checkSubscribes(checkSubscribesRequest);
            if (!validate) {
                response.setSuccess(false);
                response.setMessage("授权以前，请先订购");
                return response;
            } else {
                response.setSuccess(true);
            }
            return response;
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            throw ex;
        }
    }


    public BaseResponse authorizeCheckNew(QueryEShopParameter parameter) {
        try {
            // 参数校验优化：使用更具体的异常类
            if (parameter == null) {
                throw new RuntimeException("参数不能为空");
            }
            BaseResponse response = new BaseResponse();

            if (!parameter.isAuth()) {
                response = checkAuthorization(parameter);
                if (!response.isSuccess()) {
                    return response;
                }
            }

            // 订购验证逻辑
            return checkSubscription(parameter, response);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            // 异常处理优化：返回错误响应而不是抛出异常
            BaseResponse errorResponse = new BaseResponse();
            errorResponse.setSuccess(false);
            errorResponse.setMessage("系统错误，请联系管理员");
            return errorResponse;
        }
    }


    public BaseResponse authorizeCheckByShoptype(QueryEShopParameter parameter) {
        try {
            // 参数校验优化：使用更具体的异常类
            if (parameter == null) {
                throw new RuntimeException("参数不能为空");
            }
            BaseResponse response = new BaseResponse();
            String checkDoAuthByWriteInfo = serviceConfig.getCheckDoAuthByWriteInfo();
            String[] split = checkDoAuthByWriteInfo.split(",");
            List<String> list = Arrays.asList(split);
            if (!list.contains(parameter.getShopType().getCode() + "")) {
                response.setSuccess(true);
                response.setMessage("");
                return response;
            }
            if (!parameter.isAuth()) {
                response = checkAuthorization(parameter);
                if (!response.isSuccess()) {
                    return response;
                }
            }
            return response;
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            // 异常处理优化：返回错误响应而不是抛出异常
            BaseResponse errorResponse = new BaseResponse();
            errorResponse.setSuccess(false);
            errorResponse.setMessage("系统错误，请联系管理员");
            return errorResponse;
        }
    }

    public BaseResponse checkAuthorization(QueryEShopParameter parameter) {
        BaseResponse response = new BaseResponse();
        response.setSuccess(true);
        if (null != parameter.getOcategory() && OrganizationType.XN_SHOP.equals(parameter.getOcategory())) {
            return response;
        }
        NotifyRegisterRequest req = new NotifyRegisterRequest();
        req.setId(CurrentUser.getProfileId().toString());

        setProductId(req);
        ProfileInfo profileAssets = profileApi.getProfileAssets(req);
        sysConfig = GlobalConfig.get(SysOtypeConfig.class);
        List<Integer> ocategorys = getOcategorys(sysConfig.getOcategorys());
        ocategorys.add(OrganizationType.XN_SHOP.getCode());
        List<Otype> orgList = mapper.getOtypeList(parameter, ocategorys);

        int authedEshopCount = calculateAuthedEshopCount(orgList, parameter.getOtypeId());

        // 云订货不算占用网店数
        if (ShopType.MiddleGroundDingHuo.getCode() == parameter.getShopType().getCode()) {
            authedEshopCount = -1;
        }

        boolean isUpdateMode = null != parameter.getMode() && EshopInfoConstants.UPDATE_MODE == parameter.getMode();
        boolean isExceedLimit = isUpdateMode
                ? profileAssets.getEshopCount() < authedEshopCount
                : profileAssets.getEshopCount() <= authedEshopCount;
        if (isExceedLimit) {
            response.setSuccess(false);
            response.setMessage("已授权网店数大于当前允许授权网店数: " + profileAssets.getEshopCount());
        }
        return response;
    }

    public BaseResponse checkAuthorizationByEshopInfo(QueryEShopParameter parameter, EshopInfo eshopInfo) {
        BaseResponse response = new BaseResponse();
        response.setSuccess(true);
        if ((null != parameter.getOcategory() && OrganizationType.XN_SHOP.equals(parameter.getOcategory())) ||
                null == eshopInfo || null == eshopInfo.getOtypeId() || BigInteger.ZERO.compareTo(eshopInfo.getOtypeId()) == 0) {
            return response;
        }
        NotifyRegisterRequest req = new NotifyRegisterRequest();
        req.setId(CurrentUser.getProfileId().toString());
        setProductId(req);
        ProfileInfo profileAssets = profileApi.getProfileAssets(req);
        List<Integer> ocategorys = getOcategorys(sysConfig.getOcategorys());
        ocategorys.add(OrganizationType.XN_SHOP.getCode());
        List<Otype> orgList = mapper.getOtypeList(parameter, ocategorys);
        //从db中排除外部传入的otype
        List<Otype> filterOtypeList = orgList.stream().filter(o -> o.getId().compareTo(eshopInfo.getOtypeId()) != 0).collect(Collectors.toList());
        filterOtypeList.add(buildOtypeByEshopInfo(eshopInfo));
        int authedEshopCount = calculateAuthedEshopCountByEshopInfo(filterOtypeList);
        // 云订货不算占用网店数
        if (ShopType.MiddleGroundDingHuo.getCode() == parameter.getShopType().getCode()) {
            authedEshopCount = -1;
        }
        if (profileAssets.getEshopCount() < authedEshopCount) {
            response.setSuccess(false);
            response.setMessage("已授权网店数大于当前允许授权网店数: " + profileAssets.getEshopCount());
        }
        return response;
    }

    private Otype buildOtypeByEshopInfo(EshopInfo eshopInfo) {
        Otype otype = new Otype();
        otype.setId(eshopInfo.getOtypeId());
        otype.setShopType(eshopInfo.getEshopType());
        otype.setProfileId(CurrentUser.getProfileId());
        otype.setToken(eshopInfo.getToken());
        otype.setTokenExpireIn(eshopInfo.getTokenExpireIn());
        otype.setOnlineEshopId(eshopInfo.getOnlineEshopId());
        otype.setAppKey(eshopInfo.getAppKey());
        return otype;
    }

    // 设置产品ID逻辑封装
    private void setProductId(NotifyRegisterRequest req) {
        try {
            Integer productId = commonConfig.getProductId();
            req.setProductId((productId == null || productId == 0) ? CurrentUser.getProductId() : productId);
        } catch (Exception e) {
            req.setProductId(CurrentUser.getProductId());
        }
    }

    // 计算已授权店铺数逻辑分离
    private int calculateAuthedEshopCountByEshopInfo(List<Otype> orgList) {
        int authedEshopCount = 0;
        for (Otype o : orgList) {
            boolean isAuth = checkAuthTypeByEshopInfo(o);
            if (isAuth &&
                    !ShopType.MiddleGroundDistributor.equals(o.getShopType()) &&
                    !ShopType.MiddleGroundDingHuo.equals(o.getShopType()) &&
                    !ShopType.MiddleGroundSupplier.equals(o.getShopType())) {
                authedEshopCount += 1;
            }
        }
        return authedEshopCount;
    }

    private int calculateAuthedEshopCount(List<Otype> orgList, BigInteger otypeId) {
        int authedEshopCount = 0;
        otypeId = otypeId == null ? BigInteger.ZERO : otypeId;
        for (Otype o : orgList) {
            if (o.getId().compareTo(otypeId) == 0) {
                continue;
            }
            boolean isAuth = checkAuthType(o.getProfileId(), ShopType.valueOf(o.getEshopType()), o.getId());
            if (isAuth && !ShopType.MiddleGroundDistributor.equals(o.getShopType()) && !ShopType.MiddleGroundDingHuo.equals(o.getShopType()) && !ShopType.MiddleGroundSupplier.equals(o.getShopType())) {
                authedEshopCount += 1;
            }
        }
        return authedEshopCount;
    }

    // 订购验证逻辑分离成独立方法
    private BaseResponse checkSubscription(QueryEShopParameter parameter, BaseResponse response) {
        if (StringUtils.isEmpty(parameter.getEshopAccount())) {
            EShopPageInfo eShopPageInfo = buildEshopPageInfo(parameter);
            parameter.setEshopAccount(eShopPageInfo.getEshopAccount());
        }

        CheckSubscribesRequest checkSubscribesRequest = new CheckSubscribesRequest();
        checkSubscribesRequest.setShopAccount(parameter.getEshopAccount());
        checkSubscribesRequest.setShopType(parameter.getShopType());

        boolean validate = bifrostEshopAuthService.checkSubscribes(checkSubscribesRequest);
        if (!validate) {
            response.setSuccess(false);
            response.setMessage("授权以前，请先订购");
        } else {
            response.setSuccess(true);
        }
        return response;
    }

    public String getRefundUrl(Otype otype) {
        Integer productId = commonConfig.getProductId();
        if (productId == null) {
            productId = CurrentUser.getProductId();
        }
        String refundProductId = "";
        switch (productId) {
            case IntConstants.RETAIL_PRODUCT_ID:
                refundProductId = "35";
                break;
            case IntConstants.WHOLESALE_PRODUCT_ID:
                refundProductId = "36";
                break;
            case IntConstants.SHORT_WHOLESALE_PRODUCT_ID:
                refundProductId = "37";
                break;
        }
        EshopState eshopState = new EshopState();
        eshopState.setShopType(otype.getShopType());
        eshopState.setMutiSelectAppkey(productId); //todo 确认一下这里为什么要传productId
        EshopTagInfo tag = eshopPluginService.getEshopTag(eshopState);
        int refundShopType = tag.getRefundShopType();
        String serverIP = CommonUtil.getIpAddress();
        BigInteger profileId = CurrentUser.getProfileId();
        ProfileInfo profileInfo = profileApi.getProfileInfoById(profileId.toString(), productId);
        String companyName = profileInfo.getCompanyName();
        String timestamp = DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
        if (serviceConfig.getUseNewRefundUrlEnabled()) {
            return getNewRefundUrl(refundProductId, companyName, serverIP, refundShopType, timestamp, otype);
        }
        String pStr = MessageFormat.format("productid={0}&companyname={1}&platformid={2}&ip={3}", refundProductId, companyName, String.valueOf(refundShopType), serverIP);
//        logger.info("申请平台退款:P：" + pStr);
        String p = RsaUtils.encryptWithCSharp(pStr, serviceConfig.getRsaKey());
        String sign = RefundMd5Util.createVerificationCode(refundProductId, companyName, String.valueOf(refundShopType), RefundMd5Util.GraspSignKey, timestamp);

        String refundUrl = serviceConfig.getRefundUrl();
        return MessageFormat.format("{0}?timestamp={1}&p={2}&sign={3}", refundUrl, timestamp, p, sign);
    }

    private String getNewRefundUrl(String refundProductId, String companyName, String serverIP, int refundShopType, String timestamp, Otype otype) {
        String pStr = MessageFormat.format("productid={0}&companyname={1}&platformid={2}&ip={3}&onlineeshopid={4}&eshopid={5}&shopname={6}", refundProductId, companyName, String.valueOf(refundShopType), serverIP, otype.getOnlineEshopId(), otype.getId().toString(), otype.getFullname());
        String p = RsaUtils.encryptWithCSharp(pStr, serviceConfig.getRsaKey());
        String sign = RefundMd5Util.createVerificationCode(refundProductId, companyName, String.valueOf(refundShopType), RefundMd5Util.GraspSignKey, timestamp);
        String refundUrl = serviceConfig.getRefundUrlNew();
        return MessageFormat.format("{0}?timestamp={1}&p={2}&sign={3}", refundUrl, timestamp, p, sign);
    }

    public String getAuthUrl(QueryEShopParameter parameter) throws UnsupportedEncodingException {
        Integer productId = commonConfig.getProductId();
        if (productId == null) {
            productId = CurrentUser.getProductId();
        }
        String domain = CurrentUser.getClientDomain();
        EshopState state = new EshopState();
        state.setProductId(productId);
        state.setState(parameter.getOtypeId().toString());
        state.setMutiSelectAppkey(parameter.getMutiSelectAppkey());
        state.setShopType(parameter.getShopType());
        state.setFullname(parameter.getFullname());
        String callBackStr = String.format("%s/sale/auth/authorize?eshopid=%s&shoptype=%d&productId=%d&mutiSelectAppkey=%d", domain, parameter.getOtypeId(), parameter.getShopType().getCode(), productId, parameter.getMutiSelectAppkey());
        state.setCallBackUrl(callBackStr);
        String authUrl = bifrostEshopAuthService.getAuthUrl(state);
        String call_back = URLEncoder.encode(callBackStr);
        if (parameter.isFromRefund()) {
            call_back = URLEncoder.encode(String.format("%s/sale/auth/authorize?eshopid=%s&shoptype=%d&productId=%d&mutiSelectAppkey=%d&fromRefund=%s&otypeId=%s", domain, parameter.getOtypeId(), parameter.getShopType().getCode(), productId, BigInteger.ZERO, "true", parameter.getOtypeId().toString()));
        }
        logger.info("构建之后的回调地址" + call_back);
        return StringUtils.replace(authUrl, PlatformConst.CALL_BACK, call_back);
    }

    public BaseResponse doAuthoriza(QueryEShopParameter parameter) {
        BaseResponse response = new BaseResponse();
        EShopPageInfo eshopPageInfo = buildEshopPageInfo(parameter);
        EshopInfo eshop = getEshopInfo(parameter, eshopPageInfo);
        if (eshop == null) {
            response.setSuccess(false);
            response.setMessage("网店实体为空");
            return response;
        }
        if (null == eshop.getFullname() || "".equals(eshop.getFullname())) {
            throw new RuntimeException("网店未保存过，请先保存一次");
        }
        if (StringUtils.isNotEmpty(eshop.getAppSecret())) {
            if (checkAppSecretIsAuth(eshop)) {
                response.setSuccess(false);
                response.setMessage("当前密钥已被用于授权");
                return response;
            }
        }
        CheckSubscribesRequest checkSubscribesRequest = new CheckSubscribesRequest();
        checkSubscribesRequest.setShopAccount(parameter.getEshopAccount());
        checkSubscribesRequest.setShopType(parameter.getShopType());
        boolean validateOrder = bifrostEshopAuthService.checkSubscribes(checkSubscribesRequest);
        if (validateOrder) {
            GetTokenRequest request = new GetTokenRequest();
            request.setUserName(parameter.getUserName());
            request.setPassword(parameter.getPassword());
            request.setShopId(eshop.getOtypeId());
            request.setShopType(parameter.getShopType());
            request.setMutiSelectAppkey(eshop.getMutiSelectAppkey());
            EshopAuthInfo authInfo = bifrostEshopAuthService.getToken(request);
            if (authInfo == null) {
                response.setSuccess(false);
                response.setMessage("授权失败.");
                monitorService.recordOPS(MonitorTypeEnum.PL_BS_AUTH.getTopic(), eshop.getEshopType(), "500");
                return response;
            } else if (StringUtils.isNotEmpty(authInfo.getErrorMessage())) {
                response.setSuccess(false);
                response.setMessage(authInfo.getErrorMessage());
                monitorService.recordOPS(MonitorTypeEnum.PL_BS_AUTH.getTopic(), eshop.getEshopType(), "500");
                return response;
            }
            if (eshop.isNewShop()) {
                CommonUtil.saveAuthInfoToRedis(authInfo, parameter.getOtypeId());
            } else {
                mapper.updateEshopToken(authInfo);
                logger.error("排查online_eshop_id设置为0，eshopService.doAuthoriza" + JSONObject.toJSONString(authInfo));
            }
            eshopNotifyService.buildAndInsertNotify(authInfo, eshop.getEshopType());
            response.setSuccess(true);
            //成功添加日志
            BaseInfoLog baseInfoLog = new BaseInfoLog();
            String message = "授权成功";
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            if (authInfo.getReExpiresIn() != null) {
                monitorService.recordOPS(MonitorTypeEnum.PL_BS_AUTH.getTopic(), eshop.getEshopType(), "200");
                message += ",授权有效期至" + formatter.format(authInfo.getReExpiresIn());
            } else if (authInfo.getExpiresIn() != null) {
                monitorService.recordOPS(MonitorTypeEnum.PL_BS_AUTH.getTopic(), eshop.getEshopType(), "200");
                message += ",授权有效期至" + formatter.format(authInfo.getExpiresIn());
            }
            baseInfoLog.setBody(message);
            baseInfoLog.setObjectId(parameter.getOtypeId());
            baseInfoLog.setObjectType("otype");
            baseInfoLog.setProfileId(CurrentUser.getProfileId());
            baseInfoLog.setEtypeId(CurrentUser.getEmployeeId());
            baseInfoLog.setId(UId.newId());
            Etype etype = eshopOrderBaseInfoMapper.getEtypeById(CurrentUser.getProfileId(), CurrentUser.getEmployeeId());
            baseInfoLog.setEfullname(etype.getFullname());
            baseInfoLog.setLogTime(DateUtils.getDate());
            baseInfoLog.setIp(IpUtils.getLocalHostIp());
            LogService.add(baseInfoLog);
            logger.info(String.format("尝试记录授权日志:%s", JsonUtils.toJson(baseInfoLog)));
        } else {
            response.setSuccess(false);
            response.setMessage("授权以前，请先订购");
        }

        return response;

    }

    public BaseResponse doAuthorizaNew(QueryEShopParameter parameter) {
        BaseResponse response = new BaseResponse();
        if (StringUtils.isNotEmpty(parameter.getAppSecret())) {
            EshopInfo eshop = new EshopInfo();
            eshop.setAppSecret(parameter.getAppSecret());
            eshop.setOtypeId(BigInteger.ZERO);
            if (checkAppSecretIsAuth(eshop)) {
                response.setSuccess(false);
                response.setMessage("当前密钥已被用于授权");
                return response;
            }
        }
        CheckSubscribesRequest checkSubscribesRequest = new CheckSubscribesRequest();
        checkSubscribesRequest.setShopAccount(parameter.getEshopAccount());
        checkSubscribesRequest.setShopType(parameter.getShopType());
        boolean validateOrder = bifrostEshopAuthService.checkSubscribes(checkSubscribesRequest);
        if (validateOrder) {
            GetTokenRequest request = new GetTokenRequest();
            request.setUserName(parameter.getUserName());
            request.setPassword(parameter.getPassword());
            request.setShopId(BigInteger.ZERO);
            request.setShopType(parameter.getShopType());
            request.setMutiSelectAppkey(parameter.getMutiSelectAppkey());
            EshopAuthInfo authInfo = bifrostEshopAuthService.getToken(request);
            if (authInfo == null) {
                response.setSuccess(false);
                response.setMessage("授权失败.");
                monitorService.recordOPS(MonitorTypeEnum.PL_BS_AUTH.getTopic(), parameter.getShopType(), "500");
                return response;
            } else if (StringUtils.isNotEmpty(authInfo.getErrorMessage())) {
                response.setSuccess(false);
                response.setMessage(authInfo.getErrorMessage());
                monitorService.recordOPS(MonitorTypeEnum.PL_BS_AUTH.getTopic(), parameter.getShopType(), "500");
                return response;
            }
            CommonUtil.saveAuthInfoToRedis(authInfo, parameter.getOtypeId());
            updatetempAuthInfo(authInfo, parameter.getShopType());
            eshopNotifyService.buildAndInsertNotify(authInfo, parameter.getShopType());
            response.setSuccess(true);
            //成功添加日志
            BaseInfoLog baseInfoLog = new BaseInfoLog();
            String message = "授权成功";
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            if (authInfo.getReExpiresIn() != null) {
                monitorService.recordOPS(MonitorTypeEnum.PL_BS_AUTH.getTopic(), parameter.getShopType(), "200");
                message += ",授权有效期至" + formatter.format(authInfo.getReExpiresIn());
            } else if (authInfo.getExpiresIn() != null) {
                monitorService.recordOPS(MonitorTypeEnum.PL_BS_AUTH.getTopic(), parameter.getShopType(), "200");
                message += ",授权有效期至" + formatter.format(authInfo.getExpiresIn());
            }
            baseInfoLog.setBody(message);
            baseInfoLog.setObjectId(parameter.getOtypeId());
            baseInfoLog.setObjectType("otype");
            baseInfoLog.setProfileId(CurrentUser.getProfileId());
            baseInfoLog.setEtypeId(CurrentUser.getEmployeeId());
            baseInfoLog.setId(UId.newId());
            Etype etype = eshopOrderBaseInfoMapper.getEtypeById(CurrentUser.getProfileId(), CurrentUser.getEmployeeId());
            baseInfoLog.setEfullname(etype.getFullname());
            baseInfoLog.setLogTime(DateUtils.getDate());
            baseInfoLog.setIp(IpUtils.getLocalHostIp());
            LogService.add(baseInfoLog);
            logger.info(String.format("尝试记录授权日志:%s", JsonUtils.toJson(baseInfoLog)));
        } else {
            response.setSuccess(false);
            response.setMessage("授权以前，请先订购");
        }

        return response;

    }

    private void updatetempAuthInfo(EshopAuthInfo tokenInfo, ShopType shopType) {
        String key = Md5Utils.md5(String.format("doAuth%s_%s_%s", CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), shopType));
        RedisPoolFactory redisPoolFactory = GetBeanUtil.getBean(RedisPoolFactory.class);
        StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
        String tokenJson = template.opsForValue().get(key);
        EshopAuthInfo authInfoRds = JsonUtils.toObject(tokenJson, EshopAuthInfo.class);
        if (authInfoRds != null && tokenInfo != null) {
            authInfoRds.setToken(tokenInfo.getToken());
            authInfoRds.setAppKey(tokenInfo.getAppKey() != null ? tokenInfo.getAppKey() : authInfoRds.getAppKey());
            authInfoRds.setAppSecret(tokenInfo.getAppSecret() != null ? tokenInfo.getAppSecret() : authInfoRds.getAppSecret());
            authInfoRds.setRefreshToken(tokenInfo.getRefreshToken());
            authInfoRds.setIsAuth(0);
            authInfoRds.setHasTokenExpired(tokenInfo.isHasTokenExpired());
            authInfoRds.setExpiresIn(null != tokenInfo.getExpiresIn() ? tokenInfo.getExpiresIn() : authInfoRds.getExpiresIn());
            authInfoRds.setReExpiresIn(null != tokenInfo.getReExpiresIn() ? tokenInfo.getReExpiresIn() : authInfoRds.getReExpiresIn());
            authInfoRds.setR1ExpireIn(null != tokenInfo.getR1ExpireIn() ? tokenInfo.getR1ExpireIn() : authInfoRds.getR1ExpireIn());
            authInfoRds.setOnlineShopId(null != tokenInfo.getOnlineShopId() ? tokenInfo.getOnlineShopId() : authInfoRds.getOnlineShopId());
        }
        template.opsForValue().set(key, JsonUtils.toJson(authInfoRds), 10, TimeUnit.MINUTES);
    }


    private boolean checkAppSecretIsAuth(EshopInfo eshop) {
        BigInteger profileId = CurrentUser.getProfileId();
        String appSecret = eshop.getAppSecret();
        List<EshopInfo> eshopInfoList = mapper.getEshopExcludeEshop(profileId, appSecret, eshop.getOtypeId());
        if (CollectionUtils.isNotEmpty(eshopInfoList)) {
            for (EshopInfo eshopInfo : eshopInfoList) {
                Integer eshopIsAuth = mapper.getEshopIsAuth(eshopInfo.getOtypeId());
                if (eshopIsAuth > 0) {
                    return true;
                }
            }
        }
        return false;
    }

    public List<EshopInfo> getAllEshopList() {
        QueryEShopParameter parameter = new QueryEShopParameter();
        CommonUtil.initLimited(parameter);
        sysConfig = GlobalConfig.get(SysOtypeConfig.class);
        List<Integer> ocategorys = getOcategorys(sysConfig.getOcategorys());
        return mapper.getAllEshopList(parameter, null, ocategorys);
    }


    private void doLogDefaultRuleChange(BigInteger otypeId, String body) {
        BaseInfoLog infoLog = new BaseInfoLog();
        BigInteger profileId = CurrentUser.getProfileId();
        BigInteger employeeId = CurrentUser.getEmployeeId();
        Etype etype = eshopOrderBaseInfoService.getEtypeById(profileId, employeeId);
        String hostIp = IpUtils.getLocalHostIp();
        infoLog.setId(UId.newId());
        infoLog.setLogTime(new Date());
        infoLog.setProfileId(profileId);
        infoLog.setIp(hostIp);
        infoLog.setEtypeId(employeeId);
        infoLog.setEfullname(etype.getFullname());
        infoLog.setObjectId(otypeId);
        infoLog.setObjectType(StringConstantEnum.DEFAULT_RULE_LOG_TYPE.getSymbol());
        infoLog.setBody(body);
        LogService.add(infoLog);
    }

    public List<EshopInfo> getEshopByShopType(QueryEShopParameter queryEShopParameter) {
        CommonUtil.initLimited(queryEShopParameter);
        sysConfig = GlobalConfig.get(SysOtypeConfig.class);
        SystemGlobalConfig config = GlobalConfig.get(SystemGlobalConfig.class);
        boolean isRetail = config.isRetailEshopOrderConfig();
        List<Integer> ocategorys = getOcategorys(sysConfig.getOcategorys());
        if (!queryEShopParameter.isQueryVirtual()) {
            ocategorys.remove(new Integer(OrganizationType.XN_SHOP.getCode()));
        }
        if (queryEShopParameter.getNoplatform() != null) {
            ocategorys.remove(queryEShopParameter.getNoplatform());
        }
        int shopType = queryEShopParameter.getShopTypeInt();
        if (shopType == ShopType.ErrorEshop.getCode()) {
            if (isRetail) {
                if (queryEShopParameter.isQueryVirtual()) {
                    ocategorys.add(OrganizationType.XN_SHOP.getCode());
                }
                if (queryEShopParameter.getStoreCheck() == 1) {
                    ocategorys = queryEShopParameter.getOcategorys();
                }
                List<EshopInfo> allEshopList = mapper.getAllEshopList(queryEShopParameter, null, ocategorys);

                //对停用的店铺名字后面加上标识
                List<EshopInfo> eshopList = addEshopStopSuffix(queryEShopParameter, allEshopList);
                filterEshopList(queryEShopParameter, eshopList);
                return eshopList;
            } else {
                if (!queryEShopParameter.isNoQueryPifa()) {
                    ocategorys.add(OrganizationType.PFB_STORE.getCode());
                }
                if (queryEShopParameter.getStoreCheck() == 1) {
                    ocategorys = queryEShopParameter.getOcategorys();
                }
                List<EshopInfo> allEshopList = mapper.getAllEshopList(queryEShopParameter, null, ocategorys);
                //对停用的店铺名字后面加上标识
                List<EshopInfo> eshopList = addEshopStopSuffix(queryEShopParameter, allEshopList);
                filterEshopList(queryEShopParameter, eshopList);
                return eshopList;
            }
        } else {
            if (isRetail) {
                if (queryEShopParameter.isQueryVirtual()) {
                    ocategorys.add(OrganizationType.XN_SHOP.getCode());
                }
                List<EshopInfo> allEshopList = mapper.getEshopByShopType(queryEShopParameter, shopType, ocategorys);
                //对停用的店铺名字后面加上标识
                List<EshopInfo> eshopList = addEshopStopSuffix(queryEShopParameter, allEshopList);
                filterEshopList(queryEShopParameter, eshopList);
                return eshopList;
            } else {
                List<EshopInfo> allEshopList = mapper.getEshopByShopType(queryEShopParameter, shopType, ocategorys);
                //对停用的店铺名字后面加上标识
                List<EshopInfo> eshopList = addEshopStopSuffix(queryEShopParameter, allEshopList);
                filterEshopList(queryEShopParameter, eshopList);
                return eshopList;
            }
        }

    }

    private void filterEshopList(QueryEShopParameter parameter, List<EshopInfo> eshopInfoList) {
        if (!parameter.isFilterRefund()) {
            return;
        }
        for (EshopInfo info : eshopInfoList) {
            if (parameter.isFilterRefund()) {
                boolean featureSupported = EshopUtils.isFeatureSupported(EshopRefund.class, info.getEshopType());
                if (!featureSupported) {
                    eshopInfoList.remove(info);
                }
            }
        }
    }

    public List<EshopInfo> queryEshopList(QueryEShopParameter parameter){
        List<EshopInfo> allEshopList = mapper.getEshopByShopTypes(parameter);
        if(CollectionUtils.isEmpty(allEshopList)){
            return new ArrayList<>();
        }
        return allEshopList;
    }

    public List<EshopInfo> getEshopByShopTypes(QueryEShopParameter queryEShopParameter) {
        List<EshopInfo> allEshopList = mapper.getEshopByShopTypes(queryEShopParameter);
        if (allEshopList == null || allEshopList.size() == 0) {
            return allEshopList;
        }
        queryEShopParameter.setAddStopSuffix(true);
        return addEshopStopSuffix(queryEShopParameter, allEshopList);
    }

    public String queryJdongEshopAnyEncryptAuth() {
        QueryEShopParameter parameter = new QueryEShopParameter();
        parameter.setProfileId(CurrentUser.getProfileId());
        parameter.setPlatformTypes(Arrays.asList(ShopType.JDong.getCode(), ShopType.JdongVC.getCode(), ShopType.JdongZGB.getCode(), ShopType.JdongFBP.getCode()));
        List<EshopInfo> jdongEshopList = mapper.getEffectiveJdongEshop(parameter);
        if (CollectionUtils.isEmpty(jdongEshopList)) {
            throw new RuntimeException("当前账套不存在京东系列有效授权的网店!");
        }
        Optional<EshopInfo> anyEshop = jdongEshopList.stream().findAny();
        if (!anyEshop.isPresent()) {
            throw new RuntimeException("当前账套不存在京东系列有效授权的网店!");
        }
        EshopAuthEncryptRequest request = new EshopAuthEncryptRequest();
        EshopInfo eshopInfo = anyEshop.get();
        request.setToken(eshopInfo.getToken());
        request.setShopId(eshopInfo.getOtypeId());
        EshopAuthEncryptResponse response = bifrostEshopAuthService.authEncrypt(request);
        if (response.getSuccess()) {
            return response.getEncryptedToken();
        } else {
            throw new RuntimeException(response.getMessage());
        }
    }

    private List<EshopInfo> addEshopStopSuffix(QueryEShopParameter parameter, List<EshopInfo> allEshopList) {
        if (!parameter.isAddStopSuffix()) {
            return allEshopList;
        }
        List<EshopInfo> eshopList = allEshopList.stream().filter(x -> !x.isStoped()).collect(Collectors.toList());
        for (EshopInfo eshop : allEshopList) {
            if (!eshop.isStoped()) {
                continue;
            }
            eshop.setFullname(String.format("(停用)%s", eshop.getFullname()));
            eshopList.add(eshop);
        }
        return eshopList;
    }

    @Transactional(rollbackFor = Exception.class)
    public BaseResponse saveEshopInfo(EShopPageInfo pageInfo) {
        BaseResponse response = new BaseResponse();
        ShopType shoptype = pageInfo.getEshopType();
        pageInfo.setEshopType(shoptype);
        EshopInfo eshopInfo = buildEshopInfo(pageInfo);
        //插入账套

        //判断是否有对应的网点类型的工厂
        if (!ShopType.Other.equals(eshopInfo.getEshopType())) {
            boolean supported = EshopUtils.isSupported(eshopInfo.getEshopType());
            if (!supported) {
                response.setSuccess(false);
                response.setMessage("暂不支持【" + eshopInfo.getEshopType().getName() + "】类型网店");
                return response;
            }
        }
        EshopConfig eshopConfig = buildEshopConfig(pageInfo);
        EshopAuthInfo authInfo = getEshopAuthInfoFromRds(pageInfo);
        try {
            if (pageInfo.getMode() != null && EshopInfoConstants.ADD_MODE == pageInfo.getMode()) {
                QueryEShopParameter param = new QueryEShopParameter();
                param.setShopType(eshopInfo.getEshopType());
                BaseResponse checkAuthorizationRequest = checkAuthorization(param);
                if (null != checkAuthorizationRequest && !checkAuthorizationRequest.isSuccess()) {
                    throw new RuntimeException(checkAuthorizationRequest.getMessage());
                }
                if (authInfo != null) {
                    buildAuthInfoToEshopInfo(eshopInfo, authInfo);
                }
                mapper.insertEShop(eshopInfo);
                mapper.insertEShopConfig(eshopConfig);
                saveAddEshopOptLog(eshopInfo.getOtypeId(), eshopInfo.getFullname());
            } else if ((pageInfo.getMode() != null && EshopInfoConstants.UPDATE_MODE == pageInfo.getMode())) {
                mapper.updateEShop(eshopInfo);
                logger.error("排查online_eshop_id设置为0，eshopService.saveEshopInfo" + JSONObject.toJSONString(eshopInfo));
                mapper.updateEshopConfig(eshopConfig);
                if (authInfo != null) {
                    mapper.updateEshopToken(authInfo);
                    logger.error("排查online_eshop_id设置为0，eshopService.saveEshopInfo2" + JSONObject.toJSONString(authInfo));
                }
            } else {
                authInfo = new EshopAuthInfo();
                authInfo.setEshopId(eshopInfo.getOtypeId());
//            mapper.updateEShop(eshopInfo);
                BeanUtils.copyProperties(eshopInfo, authInfo);
                authInfo.setR1ExpireIn(eshopInfo.getTokenR1ExpireIn());
                authInfo.setReExpiresIn(eshopInfo.getRefreshTokenExpireIn());
                authInfo.setExpiresIn(eshopInfo.getTokenExpireIn());
                mapper.updateEshopToken(authInfo);
                logger.error("排查online_eshop_id设置为0，eshopService.saveEshopInfo3" + JSONObject.toJSONString(authInfo));

            }
            response.setSuccess(true);
            return response;
        } catch (Exception e) {
            try {
                if (null == eshopInfo || null == eshopInfo.getOtypeId() || BigInteger.ZERO.compareTo(eshopInfo.getOtypeId()) == 0) {
                    logger.error(String.format("profileId:%s,快速登录创建网店失败%s", CurrentUser.getProfileId(), JSONObject.toJSONString(eshopInfo)), e);
                    response.setSuccess(false);
                    return response;
                }
                logger.error(String.format("profileId:%s,eshopId:%s,快速登录创建网店失败%s", CurrentUser.getProfileId(), eshopInfo.getOtypeId(), JSONObject.toJSONString(eshopInfo)), e);
                BaseInfoLog baseInfoLog = new BaseInfoLog();
                baseInfoLog.setObjectId(eshopInfo.getOtypeId());
                baseInfoLog.setObjectType("otype");
                baseInfoLog.setProfileId(CurrentUser.getProfileId());
                baseInfoLog.setEtypeId(CurrentUser.getEmployeeId());
                baseInfoLog.setId(UId.newId());
                baseInfoLog.setEfullname("系统");
                baseInfoLog.setBody("快速登录自动新增网店失败：" + eshopInfo.getFullname());
                baseInfoLog.setLogTime(DateUtils.getDate());
                baseInfoLog.setIp(IpUtils.getLocalHostIp());
                LogService.add(baseInfoLog);
            } catch (Exception ex) {
                logger.error("网店ID:{},记录新增网店操作日志失败,失败原因:{}", eshopInfo.getOtypeId(), ex.getMessage(), ex);
            }
            response.setSuccess(false);
            return response;
        }

    }

    public BaseResponse saveEshopInfoFromMiddleGround(EShopPageInfo pageInfo) {
        BaseResponse response = new BaseResponse();
        ShopType shoptype = pageInfo.getEshopType();
        pageInfo.setEshopType(shoptype);
        EshopInfo eshopInfo = buildEshopInfo(pageInfo);
        //插入账套

        //判断是否有对应的网点类型的工厂
        if (!ShopType.Other.equals(eshopInfo.getEshopType()) && !ShopType.QiMenSupplier.equals(eshopInfo.getEshopType())) {
            boolean supported = EshopUtils.isSupported(eshopInfo.getEshopType());
            if (!supported) {
                response.setSuccess(false);
                response.setMessage("暂不支持【" + eshopInfo.getEshopType().getName() + "】类型网店");
                return response;
            }
        }
        EshopConfig eshopConfig = buildEshopConfig(pageInfo);
        EshopAuthInfo authInfo = getEshopAuthInfoFromRds(pageInfo);
        if (pageInfo.getMode() != null && EshopInfoConstants.ADD_MODE == pageInfo.getMode()) {
            if (authInfo != null) {
                buildAuthInfoToEshopInfo(eshopInfo, authInfo);
            }
            mapper.insertEShop(eshopInfo);
            mapper.insertEShopConfig(eshopConfig);
            saveAddEshopOptLogFromMiddleGround(eshopInfo.getOtypeId(), eshopInfo.getFullname(), EshopInfoConstants.ADD_MODE);
        } else if ((pageInfo.getMode() != null && EshopInfoConstants.UPDATE_MODE == pageInfo.getMode())) {
            mapper.updateEShop(eshopInfo);
            mapper.updateEshopConfig(eshopConfig);
            if (authInfo != null) {
                mapper.updateEshopToken(authInfo);
                logger.error("排查online_eshop_id设置为0，eshopService.saveEshopInfoFromMiddleGround" + JSONObject.toJSONString(authInfo));
            }
            saveAddEshopOptLogFromMiddleGround(eshopInfo.getOtypeId(), eshopInfo.getFullname(), EshopInfoConstants.UPDATE_MODE);
        } else {
            authInfo = new EshopAuthInfo();
            authInfo.setEshopId(eshopInfo.getOtypeId());
//            mapper.updateEShop(eshopInfo);
            BeanUtils.copyProperties(eshopInfo, authInfo);
            authInfo.setR1ExpireIn(eshopInfo.getTokenR1ExpireIn());
            authInfo.setReExpiresIn(eshopInfo.getRefreshTokenExpireIn());
            authInfo.setExpiresIn(eshopInfo.getTokenExpireIn());
            mapper.updateEshopToken(authInfo);
            logger.error("排查online_eshop_id设置为0，eshopService.saveEshopInfoFromMiddleGround" + JSONObject.toJSONString(authInfo));
        }
        response.setSuccess(true);
        return response;
    }

    /**
     * 保存新建店铺操作日志
     *
     * @param otypeId
     * @param fullName
     */
    private void saveAddEshopOptLog(BigInteger otypeId, String fullName) {
        try {
            BaseInfoLog baseInfoLog = new BaseInfoLog();
            baseInfoLog.setObjectId(otypeId);
            baseInfoLog.setObjectType("otype");
            baseInfoLog.setProfileId(CurrentUser.getProfileId());
            baseInfoLog.setEtypeId(CurrentUser.getEmployeeId());
            baseInfoLog.setId(UId.newId());
            baseInfoLog.setEfullname("系统");
            baseInfoLog.setBody("快速登录自动新增网店：" + fullName);
            baseInfoLog.setLogTime(DateUtils.getDate());
            baseInfoLog.setIp(IpUtils.getLocalHostIp());
            LogService.add(baseInfoLog);
        } catch (Exception ex) {
            logger.error("网店ID:{},记录新增网店操作日志失败,失败原因:{}", otypeId, ex.getMessage(), ex);
        }
    }

    private void saveAddEshopOptLogFromMiddleGround(BigInteger otypeId, String fullName, int mode) {
        try {
            BaseInfoLog baseInfoLog = new BaseInfoLog();
            baseInfoLog.setObjectId(otypeId);
            baseInfoLog.setObjectType("otype");
            baseInfoLog.setProfileId(CurrentUser.getProfileId());
            baseInfoLog.setEtypeId(CurrentUser.getEmployeeId());
            baseInfoLog.setId(UId.newId());
            baseInfoLog.setEfullname("系统");
            baseInfoLog.setBody("ERP上下游授权自动新增网店：" + fullName);
            baseInfoLog.setBody(String.format("ERP上下游授权自动%s网店: %s", mode == (EshopInfoConstants.ADD_MODE) ? "新增" : "修改", fullName));
            baseInfoLog.setLogTime(DateUtils.getDate());
            baseInfoLog.setIp(IpUtils.getLocalHostIp());
            LogService.add(baseInfoLog);
        } catch (Exception ex) {
            logger.error("网店ID:{},ERP上下游授权自动操作日志失败,失败原因:{}", otypeId, ex.getMessage(), ex);
        }
    }

    private void buildAuthInfoToEshopInfo(EshopInfo eshopInfo, EshopAuthInfo authInfo) {
        if (StringUtils.isNotEmpty(authInfo.getAppKey())) {
            eshopInfo.setAppKey(authInfo.getAppKey());
        }
        if (StringUtils.isNotEmpty(authInfo.getAppSecret())) {
            eshopInfo.setAppSecret(authInfo.getAppSecret());
        }
        if (StringUtils.isNotEmpty(authInfo.getToken())) {
            eshopInfo.setToken(authInfo.getToken());
        }
        if (StringUtils.isNotEmpty(authInfo.getRefreshToken())) {
            eshopInfo.setRefreshToken(authInfo.getRefreshToken());
        }
        if (authInfo.getExpiresIn() != null) {
            eshopInfo.setTokenExpireIn(authInfo.getExpiresIn());
        }
        if (authInfo.getReExpiresIn() != null) {
            eshopInfo.setRefreshTokenExpireIn(authInfo.getReExpiresIn());
        }
        if (authInfo.getR1ExpireIn() != null) {
            eshopInfo.setTokenR1ExpireIn(authInfo.getR1ExpireIn());
        }
        if (StringUtils.isNotEmpty(authInfo.getOnlineShopId())) {
            eshopInfo.setOnlineEshopId(authInfo.getOnlineShopId());
        }
    }


    public void checkAndPushAuthToProfile(EShopPageInfo pageInfo) {
        try {
            EshopInfo eshopInfo = buildEshopInfo(pageInfo);
            List<String> platformUidList = eshopNotifyService.getEshopUid(eshopInfo);
            if (CollectionUtils.isEmpty(platformUidList)) {
                logger.info(String.format("平台 %s 获取账号id是空", eshopInfo.getFullname()));
                return;
            }
            for (String platUid : platformUidList) {
                NotifyRegisterRequest req = new NotifyRegisterRequest();
                req.setProductId(CurrentUser.getProductId());
                req.setCode(platUid);
                req.setType(eshopInfo.getEshopType().getCode());
                List<EshopRegisterNotify> notifyExist = profileApi.getNotifyMap(req);
                if (CollectionUtils.isEmpty(notifyExist)) {
                    EshopRegisterNotify rNotify = new EshopRegisterNotify();
                    rNotify.setCode(platUid);
                    rNotify.setType(eshopInfo.getEshopType().getCode());
                    rNotify.setId(eshopInfo.getOtypeId());
                    rNotify.setCreateTime(DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
                    rNotify.setProductId(CurrentUser.getProductId());
                    rNotify.setProfileId(CurrentUser.getProfileId());
                    rNotify.setUpdateTime(DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
                    profileApi.deleteNotifyMap(rNotify);
                    profileApi.insertNotifyMap(rNotify);
                }
            }
        } catch (Exception ex) {
            logger.error(String.format("账套%s,网店%s,授权信息写入账套服务失败", CurrentUser.getProfileId(), pageInfo.getOtypeId()), ex);
        }
    }

    public boolean checkOtypeDuplicate(EShopPageInfo pageInfo) {
        return mapper.checkDuplicateOtype(pageInfo);

    }

    public boolean updateEShopBusinessTime(EshopInfo shopInfo) {
        try {
            return mapper.updateEShopBusinessUpdateTime(shopInfo);
        } catch (Exception e) {
            throw new DistributorException(500, "最后上传至供应链中台时间更新失败，请确认！");
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public boolean deleteEShop(BigInteger profileId, BigInteger id) {
        mapper.cancelAuth(profileId, id);
        logger.error("profileid:{}, eshopId: {} 取消授权成功,方法：EshopService=>deleteEShop", profileId, id);
        return mapper.deleteEShop(profileId, id);
    }

    public void checkEshopListQuoted(EshopParameter params) {
        if (params == null || params.getOtypeIds() == null || params.getOtypeIds().size() == 0) {
            return;
        }
        BigInteger profileId = params.getProfileId();
        params.getOtypeIds().forEach(otypeId -> {
            checkEshopQuoted(profileId, otypeId);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    public BaseResponse deleteEShops(EshopParameter params) {
        BaseResponse response = new BaseResponse();
//        checkEshopListQuoted(params);
        cancelAuthDoNotify(params);
        eshopOrderBaseInfoMapper.deleteOtypes(params);
        mapper.deleteEShops(params);
        response.setSuccess(true);
        return response;
    }

    public void cancelAuthDoNotify(EshopParameter params) {
        for (BigInteger otypeid : params.getOtypeIds()) {
            cancelAuthDoNotify(CurrentUser.getProfileId(), otypeid);
        }
    }

    public boolean stopEShop(EshopInfo eshopInfo) {
        return mapper.stopEShop(eshopInfo);
    }

    public int updateEshopToken(EshopAuthInfo tokenInfo) {
        int result = mapper.updateEshopToken(tokenInfo);
        if (result > 0) {
            logger.error("排查授权被清空，EshopService.updateEshopToken {}", JSONObject.toJSONString(tokenInfo));
        }
        return result;
    }

    public int updateEshopTokenByShopType(EshopAuthInfo tokenInfo) {
        return mapper.updateEshopTokenByShopType(tokenInfo);
    }

    public BaseResponse registerTmc(EShopPageInfo pageInfo) {
        BaseResponse baseResponse = new BaseResponse();
        EshopOrderGlobalConfig config = GlobalConfig.get(EshopOrderGlobalConfig.class);
        ServiceConfig serviceConfig = GetBeanUtil.getBean(ServiceConfig.class);
        EshopSystemParams params = new EshopSystemParams();
        BigInteger profileId = CurrentUser.getProfileId();
        params.setProfileId(profileId);
        params.setShopType(pageInfo.getEshopType());
        params.seteShopId(pageInfo.getOtypeId());
        boolean isShoptypeSuooprt = ShopTypesSupport(pageInfo.getEshopType(), serviceConfig.getSupportTmcOrderShopTypes());
        //注册tmc
        try {
            if (pageInfo.isTmcEnabled()) {
                if (!config.isTmcEnabled() || !isShoptypeSuooprt) {
                    baseResponse.setSuccess(false);
                    baseResponse.setMessage("此平台暂不支持开启秒级同步！");
                    return baseResponse;
                }
                //如果平台支持注册tmc，则以注册为准，否则直接开启
                if (isFunctionImplemented(params, EshopTmcRegisterFeature.class)) {
                    params.setOpenTmc(true);
                    TmcRegisterRequest tmcRegister = new TmcRegisterRequest();
                    tmcRegister.setSystemParams(params);
                    tmcRegister.setShopId(pageInfo.getOtypeId());
                    tmcRegister.setShopType(pageInfo.getEshopType());
                    tmcRegister.setOpenTmc(true);
                    TmcRegisterResponse tmcResponse = bifrostEshopAuthService.registerTmc(tmcRegister);
                    if (tmcResponse.isSuccess()) {
                        mapper.updateTmcStatus(CurrentUser.getProfileId(), true, pageInfo.getOtypeId());
                        baseResponse.setSuccess(true);
                    } else {
                        baseResponse.setSuccess(false);
                        baseResponse.setMessage("调用平台接口开启秒级同步失败！");
                    }
                } else {
                    mapper.updateTmcStatus(CurrentUser.getProfileId(), true, pageInfo.getOtypeId());
                    baseResponse.setSuccess(true);
                }
                return baseResponse;
            } else {
                if (isFunctionImplemented(params, EshopTmcRegisterFeature.class)) {
/*                    BaseRequest req = new BaseRequest();
                    req.setShopId(pageInfo.getOtypeId());
                    req.setShopType(pageInfo.getEshopType());
                    req.setSystemParams(params);
                    boolean unRegisterRep = bifrostEshopAuthService.unRegisterNotify(req);
                    if (!unRegisterRep) {
                        baseResponse.setSuccess(false);
                        baseResponse.setMessage("调用平台接口取消秒级同步失败！");
                    } else {
                        baseResponse.setSuccess(true);
                    }*/
                    mapper.updateTmcStatus(CurrentUser.getProfileId(), false, pageInfo.getOtypeId());
                } else {
                    mapper.updateTmcStatus(CurrentUser.getProfileId(), false, pageInfo.getOtypeId());
                    baseResponse.setSuccess(true);
                }
            }
        } catch (Exception ex) {
            baseResponse.setSuccess(false);
            baseResponse.setMessage(String.format("操作失败，%s", ex.getMessage()));
        }

        return baseResponse;
    }

    public boolean ShopTypesSupport(ShopType shopType, String suooprtShoptypes) {

        String[] copShoptypes = suooprtShoptypes.split(",");
        if (copShoptypes.length > 0) {
            for (String shopTypeString : copShoptypes) {
                if (String.valueOf(shopType.getCode()).equals(shopTypeString)) {
                    return true;
                }
            }
        }
        return false;
    }

    public void updateEshopRds(EshopAuthInfo tokenInfo) {
        mapper.updateRdsStatus(tokenInfo);
    }

    public EshopInfo getEshopInfo(QueryEShopParameter parameter, EShopPageInfo eShopPageInfo) {
        EshopInfo eshop = new EshopInfo();
        if (eShopPageInfo == null) {
            return eshop;
        }
        Integer mode = eShopPageInfo.getMode();
        eshop.setNewShop(EshopInfoConstants.ADD_MODE == mode);
        eshop.setProfileId(eShopPageInfo.getProfileId());
        eshop.setOtypeId(eShopPageInfo.getOtypeId());
        eshop.setEshopType(eShopPageInfo.getEshopType());
        eshop.setUserName(parameter.getUserName());
        eshop.setPassword(parameter.getPassword());
        eshop.setAppKey(eShopPageInfo.getAppKey());
        eshop.setAppSecret(eShopPageInfo.getAppSecret());
        eshop.setEshopAccount(eShopPageInfo.getEshopAccount());
        eshop.setFullname(eShopPageInfo.getFullname());
        eshop.setMutiSelectAppkey(parameter.getMutiSelectAppkey() == null ? eShopPageInfo.getMutiSelectAppkey() : parameter.getMutiSelectAppkey());
        return eshop;
    }

    public EShopPageInfo buildEshopPageInfo(QueryEShopParameter parameter) {
        EShopPageInfo eShopPageInfo = null;
        if (parameter != null && parameter.getOtypeId() != null && !parameter.getOtypeId().equals(BigInteger.ZERO) && !parameter.isNotQueryEshop()) {
            eShopPageInfo = queryEShopPageInfo(parameter);
            doCheckinDecrypt(eShopPageInfo);
        }

        if (eShopPageInfo != null) {
            if (StringUtils.isNotEmpty(parameter.getAppKey())) {
                eShopPageInfo.setAppKey(parameter.getAppKey());
            }
            if (StringUtils.isNotEmpty(parameter.getAppSecret())) {
                eShopPageInfo.setAppSecret(parameter.getAppSecret());
            }
            if (StringUtils.isNotEmpty(parameter.getEshopAccount())) {
                eShopPageInfo.setEshopAccount(parameter.getEshopAccount());
            }
            eShopPageInfo.setAuthorizedAuth(StringUtils.isNotEmpty(eShopPageInfo.getToken()));
            eShopPageInfo.setMode(EshopInfoConstants.UPDATE_MODE);
            //获取分类列表
            getOrgParTypeList(eShopPageInfo, parameter.getProfileId());

            eShopPageInfo.setAllowShowEshopClass(isAllowShowEshopClass());
            return eShopPageInfo;
        }

        eShopPageInfo = new EShopPageInfo();
        eShopPageInfo.setMode(EshopInfoConstants.ADD_MODE);
        eShopPageInfo.setProfileId(CurrentUser.getProfileId());
        eShopPageInfo.setOtypeId(UId.newId());
        BigInteger maxOrderIndex = null;
        if (null != parameter) {
            eShopPageInfo.setEshopAccount(parameter.getEshopAccount());
            eShopPageInfo.setAppSecret(parameter.getAppSecret());
            if (parameter.getShopType() != null && parameter.getShopType() != ShopType.ErrorEshop) {
                eShopPageInfo.setEshopType(parameter.getShopType());
            }
            eShopPageInfo.setOcategory(parameter.getOcategory());
            maxOrderIndex = mapper.getMaxOtypeOrderIndex(parameter.getProfileId());
            //获取分类列表
            getOrgParTypeList(eShopPageInfo, parameter.getProfileId());
        }
        //TODO 去掉业务端做店铺类型的特殊判断
        eShopPageInfo.setEshopType(null == eShopPageInfo.getEshopType() ? ShopType.TaoBao : eShopPageInfo.getEshopType());

        maxOrderIndex = maxOrderIndex == null ? BigInteger.ZERO : maxOrderIndex.add(BigInteger.ONE);
        eShopPageInfo.setRowindex(maxOrderIndex);
        EShopPageInfo eShopPage = mapper.queryEShopPageInfo(parameter);
        if (Objects.isNull(eShopPage)) {
            eShopPageInfo.setAgEnabled(false);
            eShopPageInfo.setTmcEnabled(false);
        } else {
            eShopPageInfo.setAgEnabled(eShopPage.isAgEnabled());
            eShopPageInfo.setTmcEnabled(eShopPage.isTmcEnabled());
            eShopPageInfo.setAutoCreateBtypeEnabled(eShopPage.getAutoCreateBtypeEnabled());
            if (null != eShopPage.getProcessType()) {
                eShopPageInfo.setProcessType(eShopPage.getProcessType());
            }
            eShopPageInfo.setDeliverProcessUsetype(eShopPage.getDeliverProcessUsetype());
        }
        eShopPageInfo.setAllowShowEshopClass(isAllowShowEshopClass());
        return eShopPageInfo;
    }

    private void doCheckinDecrypt(EShopPageInfo entity) {
        if (null != entity.getDeliverInfoDecryptId()) {
            List<BaseSysSecretInfo> sysSecretInfos = sysSecretInfoService.queryBaseSysSecretInfoList(CurrentUser.getProfileId(), Arrays.asList(entity.getDeliverInfoDecryptId()));
            if (sysSecretInfos == null || sysSecretInfos.isEmpty()) {
                return;
            }
            for (BaseSysSecretInfo sysSecretInfo : sysSecretInfos) {
                sysSecretInfoService.decryptSysSecretInfoSingle(sysSecretInfo);
                if (entity.getDeliverInfoDecryptId().equals(sysSecretInfo.getId())) {
                    entity.setSenderName(StringUtils.isEmpty(sysSecretInfo.getFullname()) ? entity.getSenderName() : sysSecretInfo.getFullname());
                    entity.setMobile(sysSecretInfo.getPhone());
                    entity.setAddress(StringUtils.isEmpty(sysSecretInfo.getAddress()) ? entity.getAddress() : sysSecretInfo.getAddress());
                    entity.setProvince(StringUtils.isEmpty(sysSecretInfo.getProvince()) ? entity.getProvince() : sysSecretInfo.getProvince());
                    entity.setCity(StringUtils.isEmpty(sysSecretInfo.getCity()) ? entity.getCity() : sysSecretInfo.getCity());
                    entity.setDistrict(StringUtils.isEmpty(sysSecretInfo.getDistrict()) ? entity.getDistrict() : sysSecretInfo.getDistrict());
                    entity.setPhone(sysSecretInfo.getMobile());
                }

            }
        }
    }

    private void getOrgParTypeList(EShopPageInfo eShopPageInfo, BigInteger profileId) {
        List<Otype> parTypeList = mapper.getOrgParTypeList(profileId);
        Otype otype = new Otype();
        otype.setFullname("未分类");
        otype.setTypeid("00000");
        otype.setPartypeid("00000");
        parTypeList.add(otype);
        eShopPageInfo.setParTypeList(parTypeList);
    }

    public List<Otype> getOrgParTypeList(BigInteger profileId) {
        List<Otype> parTypeList = mapper.getOrgParTypeList(profileId);
        Otype otype = new Otype();
        otype.setFullname("未分类");
        otype.setTypeid("00000");
        otype.setPartypeid("00000");
        parTypeList.add(otype);
        return parTypeList;
    }

    private EshopInfo buildEshopInfo(EShopPageInfo pageInfo) {
        String appKey = pageInfo.getAppKey();
        String appSecret = pageInfo.getAppSecret();
        appKey = appKey == null ? "" : appKey;
        appSecret = appSecret == null ? "" : appSecret;

        EshopInfo eshopInfo = new EshopInfo();
        eshopInfo.setProfileId(pageInfo.getProfileId());
        eshopInfo.setOtypeId(pageInfo.getOtypeId());
        eshopInfo.setFullname(pageInfo.getOtypeFullname());
        eshopInfo.setEshopAccount(pageInfo.getEshopAccount());
        eshopInfo.setEshopSalePlatform(pageInfo.getEshopSalePlatform());
        eshopInfo.setEshopType(pageInfo.getEshopType());
        eshopInfo.setAppKey(appKey);
        eshopInfo.setAppSecret(appSecret);
        eshopInfo.setCreateTime(new Date());
        eshopInfo.setModifyTime(new Date());
        eshopInfo.setHasException(false);
        eshopInfo.setHasException(false);
        eshopInfo.setDeleted(false);
        eshopInfo.setStoped(false);
        eshopInfo.setDeliverDuration(-1);
        eshopInfo.setPromisedSignDuration(-1);
        eshopInfo.setPromisedCollectDuration(-1);
        eshopInfo.setSubscribeLogistics(false);
        eshopInfo.setOnlineEshopId(pageInfo.getOnlineEshopId() == null ? "" : pageInfo.getOnlineEshopId());
        eshopInfo.setVendorId(pageInfo.getVendorId() == null ? "" : pageInfo.getVendorId());
        eshopInfo.setToken(pageInfo.getToken() == null ? "" : pageInfo.getToken());
        eshopInfo.setRefreshToken(pageInfo.getRefreshToken() == null ? "" : pageInfo.getRefreshToken());
        eshopInfo.setPlatformEshopId(pageInfo.getPlatformEshopId() == null ? "" : pageInfo.getPlatformEshopId());
        eshopInfo.setTokenExpireIn(pageInfo.getTokenExpireIn());
        eshopInfo.setTokenR1ExpireIn(pageInfo.getTokenR1ExpireIn());
        eshopInfo.setHasTokenExpired(pageInfo.isHasTokenExpired());
        eshopInfo.setRefreshTokenExpireIn(pageInfo.getRefreshTokenExpireIn());
        eshopInfo.setRefundSysPromisedConfirmDuration(null == pageInfo.getRefundSysPromisedConfirmDuration() ? -1 : pageInfo.getRefundSysPromisedConfirmDuration());
        eshopInfo.setRefundPromisedAgreeDuration(null == pageInfo.getRefundPromisedAgreeDuration() ? -1 : pageInfo.getRefundPromisedAgreeDuration());
        eshopInfo.setRefundPromisedConfirmDuration(null == pageInfo.getRefundPromisedConfirmDuration() ? -1 : pageInfo.getRefundPromisedConfirmDuration());
        eshopInfo.setRefundPromisedDeliverDuration(null == pageInfo.getRefundPromisedDeliverDuration() ? -1 : pageInfo.getRefundPromisedDeliverDuration());
        eshopInfo.setRefundPromisedReceiveDuration(null == pageInfo.getRefundPromisedReceiveDuration() ? -1 : pageInfo.getRefundPromisedReceiveDuration());
        eshopInfo.setMentionDeliverDuration(pageInfo.getMentionDeliverDuration());
        eshopInfo.setPromisedSyncFreightDuration(null == pageInfo.getPromisedSyncFreightDuration() ? -1 : pageInfo.getPromisedSyncFreightDuration());
        eshopInfo.setGroupId(pageInfo.getGroupId());
        return eshopInfo;
    }

    private EshopConfig buildEshopConfig(EShopPageInfo pageInfo) {
        EshopConfig eshopConfig = new EshopConfig();
        eshopConfig.setEshopId(pageInfo.getOtypeId());
        eshopConfig.setRdsEnabled(false);
        eshopConfig.setCheckAccountType(pageInfo.getCheckAccountType());
        eshopConfig.setBtypeGenerateType(pageInfo.getBtypeGenerateType());
        eshopConfig.setMallType(pageInfo.getMallType());
        eshopConfig.setMappingType(pageInfo.getMappingType());
        eshopConfig.setXcodeMappingRequired(pageInfo.isXcodeMappingRequired());
        eshopConfig.setIndependentAccountingEnabled(pageInfo.getIndependentAccountingEnabled());
        eshopConfig.setTmcEnabled(pageInfo.isTmcEnabled());
        eshopConfig.setAgEnabled(pageInfo.isAgEnabled());
        eshopConfig.setHoldMinutes(pageInfo.getHoldMinutes());
        eshopConfig.setPlatformEshopSnType(pageInfo.getPlatformEshopSnType() == null ? "" : pageInfo.getPlatformEshopSnType());
        eshopConfig.setMutiSelectAppkey(pageInfo.getMutiSelectAppkey());
        eshopConfig.setSendProcessWay(buildSendProcessWay(pageInfo));
        EshopState state = new EshopState();
        state.setProductId(RouteThreadLocal.getRoute().getProductId());
        state.setShopType(pageInfo.getEshopType());
        EshopTagInfo tagInfo = eshopPluginService.getEshopTag(state);

        eshopConfig.setProcessType(null == tagInfo ? 1 : tagInfo.getProcess_type());
        eshopConfig.setTmallSpecialSale(pageInfo.isTmallSpecialSale());
        return eshopConfig;
    }

    private int buildSendProcessWay(EShopPageInfo pageInfo) {
        if (null == pageInfo.getSendProcessWay() || pageInfo.getSendProcessWay() == 0) {
            boolean wmsFuncSta = SysProfileModelService.checkCurrentEnabledByKeyEnum(SysProfileModelKeyEnum.WmsFunc);
            boolean eshopFuncSta = SysProfileModelService.checkCurrentEnabledByKeyEnum(SysProfileModelKeyEnum.OfflineBillToDeliverFunc);

            if (wmsFuncSta) {
                return 1;
            }
            if (eshopFuncSta) {
                return 2;
            }
        }
        return 0;
    }

    private EshopAuthInfo getEshopAuthInfoFromRds(EShopPageInfo pageInfo) {
        try {
            String key = MessageFormat.format("token_{0}_{1}", pageInfo.getProfileId().toString(), pageInfo.getOtypeId().toString());
            final ValueOperations<String, String> redis = RedisMessageSys.getRedis();
            String tokenJson = redis.get(key);
            if (!CommonUtil.isNullOrEmpty(tokenJson)) {
                EshopAuthInfo authInfo = JsonUtils.toObject(tokenJson, EshopAuthInfo.class);
                return authInfo;
            }
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return null;
        }

        return null;
    }

    public BaseResponse cancelAuth(BigInteger profileId, BigInteger otypeId) {
        BaseResponse response = new BaseResponse();
        try {
            cancelAuthDoNotify(profileId, otypeId);
            mapper.cancelAuth(profileId, otypeId);
            response.setSuccess(true);
            logger.error("profileid:{}, eshopId: {} 取消授权成功,方法：EshopService=>cancelAuth", profileId, otypeId);
        } catch (Exception ex) {
            logger.error("profileid:{}, eshopId: {} 取消授权失败,失败信息：{}", profileId, otypeId, ex.getMessage(), ex);
            response.setSuccess(false);
            response.setMessage(ex.getMessage());
        }
        return response;
    }

    public void writeEshopBaseLog(BigInteger profileId, BigInteger otypeId, BigInteger employeeId, String employeeName, String logOptionDesc) {
        try {
            BaseInfoLog baseInfoLog = new BaseInfoLog();
            baseInfoLog.setBody(logOptionDesc);
            baseInfoLog.setObjectId(otypeId);
            baseInfoLog.setObjectType("otype");
            baseInfoLog.setProfileId(profileId);
            baseInfoLog.setId(UId.newId());
            if (employeeId != null && employeeId.compareTo(BigInteger.ZERO) > 0) {
                baseInfoLog.setEtypeId(employeeId);
                if (StringUtils.isEmpty(employeeName)) {
                    Etype etype = eshopOrderBaseInfoService.getEtypeById(CurrentUser.getProfileId(), employeeId);
                    employeeName = etype.getFullname();
                }
                baseInfoLog.setEfullname(employeeName);
            } else {
                baseInfoLog.setEtypeId(BigInteger.ZERO);
                baseInfoLog.setEfullname("系统");
            }
            baseInfoLog.setLogTime(DateUtils.getDate());
            baseInfoLog.setIp(IpUtils.getLocalHostIp());
            LogService.add(baseInfoLog);
        } catch (Exception ex) {
            logger.error("profileId: {}，eshopId: {},操作：{}， 记录网店操作日志失败：{}", profileId, otypeId, logOptionDesc, ex.getMessage(), ex);
        }
    }

    public void cancelAuthDoNotify(BigInteger profileId, BigInteger otypeId) {
        EshopInfo eshop = mapper.getEshopInfoById(profileId, otypeId);
        List<String> uIds = eshopNotifyService.getEshopUid(eshop);
        if (eshop != null && uIds != null && uIds.size() > 0) {
            for (String uid : uIds) {
                EshopRegisterNotify eshopNotify = new EshopRegisterNotify();
                eshopNotify.setId(eshop.getOtypeId());
                eshopNotify.setProfileId(eshop.getProfileId());
                eshopNotify.setCode(uid);
                eshopNotify.setType(eshop.getEshopTypeInt());
                eshopNotify.setProductId(CurrentUser.getProductId());
                profileApi.deleteNotifyMap(eshopNotify);

            }
        }
        if (eshop != null && eshop.getOtypeId() != null) {
            //删除platform_eshop_register_info 映射关系
            DeleteEshopRegisterParam param = new DeleteEshopRegisterParam();
            param.setProfileId(eshop.getProfileId());
            param.setEshopId(eshop.getOtypeId());
            param.setProductId(BigInteger.valueOf(CurrentUser.getProductId()));
            profileApi.deletePlatformEshopRegisterInfo(param);
        }
    }

    public BaseResponse cancelEShopAuth(EshopParameter params) {
        BaseResponse response = new BaseResponse();
        try {
            List<EshopInfo> eshopInfoByIds = mapper.getEshopInfoByIds(CurrentUser.getProfileId(), params.getOtypeIds());
            if (CollectionUtils.isNotEmpty(eshopInfoByIds)) {
                List<BigInteger> collect = eshopInfoByIds.stream().filter(x -> x.getEshopType() != ShopType.EleMeRetail).map(EshopInfo::getOtypeId).collect(Collectors.toList());
                List<BigInteger> collectEleme = eshopInfoByIds.stream().filter(x -> x.getEshopType() == ShopType.EleMeRetail).map(EshopInfo::getOtypeId).collect(Collectors.toList());
                if (collect.size() > 0) {
                    params.setOtypeIds(collect);
                    mapper.cancelEShopAuth(params);
                    logger.error("排查授权被清空，EshopService.cancelEShopAuth ，profileid: {}, token:{}", CurrentUser.getProfileId(), JsonUtils.toJson(params));
                    mapper.updateEshopRealStockQtyEnabled(CurrentUser.getProfileId(), params.getOtypeIds());
                }
                if (collectEleme.size() > 0) {
                    params.setOtypeIds(collectEleme);
                    mapper.cancelEShopAuthForEleme(params);
                    logger.error("排查授权被清空，EshopService.cancelEShopAuth->cancelEShopAuthForEleme ，profileid: {}, token:{}", CurrentUser.getProfileId(), JsonUtils.toJson(params));
                    mapper.updateEshopRealStockQtyEnabled(CurrentUser.getProfileId(), params.getOtypeIds());
                }
                response.setSuccess(true);
            }

        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            response.setSuccess(false);
            response.setMessage(ex.getMessage());
        }
        return response;

    }


    public Btype getBtypeByName(BigInteger profileId, String btypeName) {
        Btype btype = eshopOrderBaseInfoService.getBtypeByName(profileId, btypeName);
        if (btype != null && !btype.getFreighted()) {
            return btype;
        }
        return null;
    }

    public void modifyCondition(EshopOrderSyncCondition condition) {
        try {
            mapper.modifyEshopCondition(condition);
        } catch (Exception ex) {
            logger.error(String.format("账套【%d】修改自动下单condition报错，错误信息：%s", condition.getProfileId(), ex.getMessage()), ex);
            throw ex;
        }
    }


    //获取当前账套下的所有店铺的自动下单配置
    public List<Otype> getAutoDownConfig(OrganizationPageResponse response, BigInteger profileId) {
        response.getOrgList().removeIf(org -> org.getEshopSalePlatform() == 9);
        List<Otype> otypes = new ArrayList<>();
        for (Otype org : response.getOrgList()) {
            if (org.getOcategory().equals(OrganizationType.YDH_STORE)) {
                continue;
            }
            org.setBtnChange(false);
            String operation = StringConstantEnum.BEGIN_SQUARE_BRACKETS.getSymbol();
            if (org.getEshopAuthMark().contains("已授权")) {
                org.setAuth(true);
                org.setAuthState("<font color=\'white\' style=\' background-color:#3FBB00;border-radius:10px;padding:2px;padding-left:4px;padding-right:4px\'> 已授权 </font>");
                org.setAuthTag("");
            } else if (org.getEshopAuthMark().contains("授权过期")) {
                org.setAuth(false);
                org.setAuthState("<font color=\'white\' style=\' background-color:#E44A3D;border-radius:10px;padding:2px;padding-left:4px;padding-right:4px\'>授权过期</font>");
            } else {
                org.setAuth(false);
                org.setAuthState("<font color=\'white\' style=\' background-color:#8B9197;border-radius:10px;padding:2px;padding-left:4px;padding-right:4px\'>未授权</font>");
            }

            if (Objects.isNull(org.getAutoSync())) {
                org.setAutoSync(0);
            }

            if (org.getAutoSync() == 0) {
                org.setBtnChange(false);
                org.setAutoSyncState(false);
                //"<font color=\'white\' style=\' background-color:#8B9197;border-radius:10px;padding:2px;padding-left:4px;padding-right:4px\'>未开启</font>"
                operation += "{'startDownload':'<font color=white style=\"border-radius:5px;padding:0px;padding-left:4px;padding-right:4px;margin-top:9px;height:22px;line-height:22px\" class=\"Button SpecialButton\">开启自动下载</font>'},";
            } else {
                org.setBtnChange(true);
                org.setAutoSyncState(true);
                //"<font color=\'white\' style=\' background-color:#3FBB00;border-radius:10px;padding:2px;padding-left:4px;padding-right:4px\'>已开启</font>"
                if (org.isAuth()) {
//                    operation += "{'download':'<font color=white style= background-color:#77b7ff;border-radius:5px;padding:2px;padding-left:4px;padding-right:4px>立即下载</font>'},";
                } else {
                    operation += "{'auth':'<font color=white style= background-color:#2288fc;border-radius:5px;padding:2px;padding-left:4px;padding-right:4px>重新授权</font>'},";
                }
            }

            org.setTmcEnabledState(org.getTmcEnabled() ?
                    "<font color=\'white\' style=\' background-color:#3FBB00;border-radius:10px;padding:2px;padding-left:4px;padding-right:4px\'>已开启</font>" :
                    "<font color=\'white\' style=\' background-color:#8B9197;border-radius:10px;padding:2px;padding-left:4px;padding-right:4px\'>未开启</font>");
            EshopOrderGlobalConfig config = GlobalConfig.get(EshopOrderGlobalConfig.class);
            boolean isShoptypeSuooprt = ShopTypesSupport(org.getShopType(), serviceConfig.getSupportTmcOrderShopTypes());
            if (!config.isTmcEnabled() || !isShoptypeSuooprt) {
                org.setTmcEnabled(false);
                org.setTmcEnabledState("<font color=\'white\' style=\' background-color:#8B9197;border-radius:10px;padding:2px;padding-left:4px;padding-right:4px\'>平台不支持</font>");
            }

            operation += "{'record':'<font color=white style= background-color:#8B9197;border-radius:5px;padding:2px;padding-left:4px;padding-right:4px;>下载记录</font>'},";
            operation += StringConstantEnum.END_SQUARE_BRACKETS.getSymbol();
            org.setOperation(operation);
            otypes.add(org);
        }
        return otypes;
    }

    //开启自动下单
    @Transactional(rollbackFor = Exception.class)
    public boolean addEshopAutoOrder(BigInteger profileId, BigInteger eshopId, Date beginTime) {
        mapper.updateEshopAutoOrder(1, profileId, eshopId);
        Integer countOrderSync = mapper.getCountOrderSync(profileId, eshopId);
        if (countOrderSync >= 1) {
            mapper.updateOrderSync(beginTime, profileId, eshopId);
        } else {
            for (int i = 0; i < 2; i++) {
                BigInteger id = UId.newId();
                mapper.insertOrderSync(id, profileId, eshopId, beginTime, i);
            }
        }
        return true;
    }

    //关闭自动下单
    public boolean updateEshopAutoOrder(BigInteger profileId, BigInteger eshopId) {
        mapper.updateEshopAutoOrder(0, profileId, eshopId);
        return true;
    }

    public void checkEshopQuoted(BigInteger profileId, BigInteger eshopId) {
        //赠品策略
        checkQuote(profileId, eshopId, EshopQuoteType.GIFT_RULE);
        //原单
        checkQuote(profileId, eshopId, EshopQuoteType.ESHOP_SALE_ORDER);
        //发货单
        checkQuote(profileId, eshopId, EshopQuoteType.DELIVER_ORDER);
        //售后单
        checkQuote(profileId, eshopId, EshopQuoteType.REFUND);
        //商品对应
        checkQuote(profileId, eshopId, EshopQuoteType.PTYPE);
        //商品打标
//        checkQuote(profileId, eshopId, EshopQuoteType.PRODUCT_MARK);

        //仓储策略
        checkQuote(profileId, eshopId, EshopQuoteType.KTYPE_RULE);

//        //库存同步策略
////        checkStrategyQuote(profileId,eshopId, EshopQuoteType.STOCK_UPLOAD_RULE);
////        //合并策略
////        checkStrategyQuote(profileId,eshopId, EshopQuoteType.MERGE_RULE);
////        //拆分
////        checkStrategyQuote(profileId,eshopId, EshopQuoteType.SPLIT_RULE);

    }

    private EshopQuoteRequest buildQuoteRequest(BigInteger profileId, BigInteger eshopId, EshopQuoteType type) {
        EshopQuoteRequest request = new EshopQuoteRequest();
        request.setOtypeId(eshopId);
        request.setQuoteType(type);
        return request;
    }

    public String checkQuote(BigInteger profileId, BigInteger eshopId, EshopQuoteType type) {
        EshopQuoteRequest request = buildQuoteRequest(profileId, eshopId, type);
        String msg = "";
        if (request.getCheckType().equals(EshopQuoteCheckType.API)) {//暂时屏蔽，todo
            checkQuotedFormApi(request);
        } else if (!CommonUtil.isNullOrEmpty(request.getTableName()) && request.getCheckType() != null) {
            if (type == EshopQuoteType.PTYPE) {
                boolean quoted = mapper.checkPtypeAndEshop(request);
                if (quoted) {
                    //现在对于关系取消检查 基础信息组会通知 或者至直接清理对应关系
//                    msg = String.format("被%s引用", request.getQuoteType().toString());
//                    return msg;
                } else {
                    return "";
                }
            } else {

                boolean quoted = mapper.checkEshopQuoted(request);
                if (!quoted) {
                    return "";
                }
                QueryEShopParameter queryEShopParameter = new QueryEShopParameter();
                queryEShopParameter.setOtypeId(eshopId);
                Otype otype = getOrganizationById(queryEShopParameter);


                if (type.getMsgType().equals(EshopQuoteMsgType.STRATEGY)) {
                    msg = String.format("%s 网店参与过%s，不允许删除", otype.getFullname(), request.getQuoteType().toString());
                } else {
                    msg = String.format("被%s引用", request.getQuoteType().toString());
                }
            }
        }
        return msg;
    }

    private void checkQuotedFormApi(EshopQuoteRequest request) {
        if (request == null) {
            return;
        }
        List<BigInteger> ids = new ArrayList<>();
        ids.add(request.getOtypeId());
        BatchBaseInfoCheckRequest checkRequest = new BatchBaseInfoCheckRequest();
        checkRequest.setId(request.getOtypeId());
        checkRequest.setIds(ids);
        checkRequest.setCheckType(2);
        GeneralResult<List<BaseInfoCheckResponse>> result = deliverApi.checkDeliverStrategyOfEshop(checkRequest);

        if (result.getCode() == 200L) {
            List<BaseInfoCheckResponse> responses = result.getData();
            for (BaseInfoCheckResponse baseInfoCheckResponse : responses) {
                if (!baseInfoCheckResponse.isSuccess()) {
                    throw new RuntimeException(String.format("该网店被%s引用", request.getQuoteType().toString()));
                }
            }
        } else {
            throw new RuntimeException(String.format("检查网店%s引用出错", request.getQuoteType().toString()));
        }

    }

    public boolean checkBtypeIn(QueryEShopParameter queryEShopParameter) {
        return mapper.checkBtypeIn(queryEShopParameter.getProfileId(), queryEShopParameter.getKtypeId());
    }

    public BigInteger getDefaultEshopId(QueryEShopParameter queryEShopParameter) {
        List<BigInteger> defaultEshopIds = mapper.getMorenEshopId(queryEShopParameter.getProfileId());
        if (Objects.isNull(defaultEshopIds) || defaultEshopIds.size() == 0) {
            return new BigInteger("0");
        }
        return defaultEshopIds.get(0);
    }


    public boolean updatePlEshopConfig(Otype otype) {
        if (otype.getId() == null || otype.getProfileId() == null) {
            return false;
        }
        return mapper.updatePlEshopConfig(otype);
    }

    @Transactional(rollbackFor = Exception.class)
    public void modifyOrderIndex(modifyOrderIndexInDTO inDTO) {
        BaseOtype current = new BaseOtype();
        current.setId(inDTO.getCurrentId());
        current.setRowindex(inDTO.getChangeIndex());
        BaseOtype change = new BaseOtype();
        change.setId(inDTO.getChangeId());
        change.setRowindex(inDTO.getCurrentIndex());
        baseOtypeMapper.updateOrderIndexByPrimaryKey(current);
        baseOtypeMapper.updateOrderIndexByPrimaryKey(change);
    }

    public List<DropDownPluginEntity> getTradeStatusList(BigInteger profileId, List<BigInteger> otypeIds) {
        List<DropDownPluginEntity> result = new ArrayList<>();
        try {
            if (otypeIds == null || otypeIds.isEmpty()) {
                return result;
            }
            List<ShopType> shopTypes = mapper.getEshopInfoByEshopIdList(profileId, otypeIds);
            if (shopTypes == null || shopTypes.isEmpty()) {
                return result;
            }
            result = initTradeStatusList();

            for (ShopType type : shopTypes) {
//                EshopFactory factory = EshopFactoryCreator.getInstance().createByType(type);
//                List<DropDownPluginEntity> statusList = factory.createEshopPlugin().orderDownloadSupport();
                CommonRequest commonRequest = new CommonRequest();
                commonRequest.setShopType(type);
                List<DropDownPlugin> dropDownPlugins = eshopPluginService.orderDownloadSupport(commonRequest);
                if (dropDownPlugins == null || dropDownPlugins.isEmpty()) {
                    result = new ArrayList<>();
                    continue;
                }
                Set<Integer> supportedStatus = new HashSet<>();
                dropDownPlugins.forEach(dropDownPlugin -> {
                    supportedStatus.add(dropDownPlugin.getValue());
                });
                result = result.stream().filter(dw -> {
                    return supportedStatus.contains(dw.getValue());
                }).collect(Collectors.toList());
            }
        } catch (Exception ex) {
            logger.error(String.format("获取网店支持的线上交易状态出错，错误原因：%s", ex.getMessage()), ex);
        }
        if (result.isEmpty()) {
            result.add(new DropDownPluginEntity(TradeStatus.All.getCode(), TradeStatus.All.getName()));
        }
        return result;
    }

    private List<DropDownPluginEntity> initTradeStatusList() {
        List<DropDownPluginEntity> tradeStatusList = new ArrayList<>();
        tradeStatusList.add(new DropDownPluginEntity(TradeStatus.All.getCode(), TradeStatus.All.getName()));
        tradeStatusList.add(new DropDownPluginEntity(TradeStatus.WAIT_BUYER_PAY.getCode(), TradeStatus.WAIT_BUYER_PAY.getName()));
        tradeStatusList.add(new DropDownPluginEntity(TradeStatus.WAIT_SELLER_SEND_GOODS.getCode(), TradeStatus.WAIT_SELLER_SEND_GOODS.getName(), true));
        tradeStatusList.add(new DropDownPluginEntity(TradeStatus.WAIT_BUYER_CONFIRM_GOODS.getCode(), TradeStatus.WAIT_BUYER_CONFIRM_GOODS.getName()));
        tradeStatusList.add(new DropDownPluginEntity(TradeStatus.TRADE_FINISHED.getCode(), TradeStatus.TRADE_FINISHED.getName()));
        tradeStatusList.add(new DropDownPluginEntity(TradeStatus.ALL_CLOSED.getCode(), TradeStatus.ALL_CLOSED.getName()));
        tradeStatusList.add(new DropDownPluginEntity(TradeStatus.SELLER_CONSIGNED_PART.getCode(), TradeStatus.SELLER_CONSIGNED_PART.getName()));
        tradeStatusList.add(new DropDownPluginEntity(TradeStatus.SELLER_PAY_PART.getCode(), TradeStatus.SELLER_PAY_PART.getName()));
        return tradeStatusList;
    }


    public BusinessBillDetailInfoResponse finBuinessDetailInfoByVchcode(ExpenseBillInfoRequest request) {
//        DataSourceManager.setName("10008");
        BusinessBillDetailInfoResponse response = new BusinessBillDetailInfoResponse();
        List<BusinessBillInfoData> data = new ArrayList<>();
        BusinessBillInfoData infoData = new BusinessBillInfoData();
        response.setMessage("success");
        //和他们沟通不 核算之后才会存储在这张表中 所以这个状态直接给1
        response.setStatus(1);
        List<BillDetailData> billRefundDetailData = new ArrayList<>();
        try {
            //售后生成费用查询
            List<EshopRefundEntity> refundByBillVchcode = refundMapper.getRefundByBillVchcode(request.getProfileId(), request.getVchcode());
            if (null != refundByBillVchcode && refundByBillVchcode.size() > 0) {
                billRefundDetailData = platformCheckMapper.querySaleOrderOutStoreByRefund(request.getProfileId(), refundByBillVchcode.stream().map(e -> e.getTradeOrderId()).collect(Collectors.toList()));
            }
            //流水生成费用查询
            List<BillDetailData> billDetailData = platformCheckMapper.querySaleOrderOutStore(request.getProfileId(), request.getVchcode());
            //费用科目查询
//            List<AtypeData> atypeData = platformCheckMapper.queryAtypeData(request.getProfileId(), request.getVchcode());
            List<AtypeData> atypeData = platformCheckMapper.queryAtypeDataByBillAccount(request.getProfileId(), request.getVchcode());
//            if (null != atypeData1) {
//                atypeData.addAll(atypeData1);
//            }
            if (null != billDetailData) {
                billRefundDetailData.addAll(billDetailData);
            }
            infoData.setBillDetailInfo(billRefundDetailData);
            infoData.setAtypeInfo(atypeData);
            data.add(infoData);
            response.setData(data);
            if (response.getData().size() > 0) {
                if (response.getData().get(0).getBillDetailInfo().size() < 1) {
                    response.setStatus(0);
                }
            }
            response.setPageCount(request.getPageSize());
            response.setAllCount(response.getData().size());
        } catch (Exception e) {
            e.printStackTrace();
            response.setMessage(e.getMessage());
        }
        return response;
    }

    public boolean checkShopAuth(BigInteger profileId, BigInteger eshopId, String onlineShopId, ShopType shopType) {
        int count = mapper.getSameAuthShopCount(profileId, eshopId, onlineShopId, shopType.getCode());
        return count == 0;
    }

    public EshopInfo getFirstOtype(QueryEShopParameter parameter) {
        return mapper.getFirstOtype(parameter);
    }

    public EshopInfo getEshopInfoById(QueryEShopParameter parameter) {
        return mapper.getEshopInfoById(parameter.getProfileId(), parameter.getEshopId());
    }

    public List<Stock> getAllStockList(BigInteger profileId, List<BigInteger> ids) {
        return eshopOrderBaseInfoMapper.getAllStockList(profileId, ids);
    }


    public boolean checkAtypeIsDelete(QueryBaseTypeParameter atypeId) {
        Atype atype = eshopOrderBaseInfoMapper.getAtype(atypeId);
        return atype == null || atype.getDeleted() == 1;
    }

    public Boolean openEshopAg(BigInteger profileId, BigInteger otypeId) {
        try {
            EshopInfo eshopInfo = getEshopInfoById(profileId, otypeId);
            CommonRequest commonRequest = new CommonRequest();
            commonRequest.setShopId(eshopInfo.getOtypeId());
            boolean result = eshopRefundService.checkAgEnabled(commonRequest);
            //修改config
            Otype otype = new Otype();
            otype.setId(otypeId);
            otype.setProfileId(profileId);
            otype.setAgEnabled(result);
            mapper.updatePlEshopConfigAG(otype);
            return result;
        } catch (Exception e) {
            throw new RuntimeException(String.format("网店开启极速退款失败，失败原因：%s", e.getMessage()), e);
        }
    }

    public boolean deleteAuthInfoRedis(EShopPageInfo eshopInfo) {
        try {
            String key = MessageFormat.format("token_{0}_{1}", eshopInfo.getProfileId().toString(), eshopInfo.getOtypeId().toString());
            final ValueOperations<String, String> redis = RedisMessageSys.getRedis();
            redis.set(key, "");
            return true;
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return false;
        }
    }

    /**
     * @param eshopInfo
     * @return
     */
    public BaseResponse checkIndependentAccounting(EShopPageInfo eshopInfo) {
        BigInteger profileId = CurrentUser.getProfileId();
        eshopInfo.setProfileId(profileId);
        BaseResponse response = new BaseResponse();
        response.setSuccess(true);
        response.setMessage("操作成功");
        String periodEnabled = getSysData(profileId, "accountingSysdataManualClosePeriodEnabled", "0");
        if ("1".equals(periodEnabled)) {
            boolean flag2 = mapper.checkGenerateAccounting(profileId, eshopInfo.getOtypeId());
            if (flag2) {
                response.setSuccess(false);
                response.setMessage("当前启用手工月结存，修改该配置涉及重新核算财务数据，如需修改，请关闭手工月结存。");
            }
        }
        return response;
    }

    public String getSysData(BigInteger profileId, String subName, String defaultValue) {
        List<String> sysData = mapper.getSysData(profileId, subName);
        return CollectionUtils.isEmpty(sysData) ? defaultValue : sysData.get(0);
    }

    public void updateEshopPageConfig(EshopConfig eshopConfig) {
        mapper.updateEshopPageConfig(eshopConfig);
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertOrUpdateEshopInfo(EshopInfo eshopInfo, EshopConfig eshopConfig, EshopAuthInfo authInfo, Integer mode) {
        buildByAppKeyAuthShop(eshopInfo);
        if (EshopInfoConstants.ADD_MODE == mode) {
            if (authInfo != null) {
                buildAuthInfoToEshopInfo(eshopInfo, authInfo);
            }
            rebuildEshopConfig(eshopConfig);
            mapper.insertEShop(eshopInfo);
            mapper.insertEShopConfig(eshopConfig);
            logger.error("排查授权信息被清空的问题，eshopService.insertOrUpdateEshopInfo" + JSONObject.toJSONString(eshopInfo));
        } else {
            mapper.updateEShop(eshopInfo);
            logger.error("排查online_eshop_id设置为0，eshopService.insertOrUpdateEshopInfo" + JSONObject.toJSONString(eshopInfo));
            mapper.updateEshopConfig(eshopConfig);
            if (authInfo != null) {
                mapper.updateEshopToken(authInfo);
                logger.error("排查授权信息被清空的问题，eshopService.insertOrUpdateEshopInfo" + JSONObject.toJSONString(authInfo));
            }
        }
    }

    private void rebuildEshopConfig(EshopConfig eshopConfig) {
        eshopConfig.setAgEnabled(eshopConfig.isAgEnabled() == null ? false : eshopConfig.isAgEnabled());
        eshopConfig.setBtypeGenerateType(eshopConfig.getBtypeGenerateType() == null ? GenerateBtypeType.GENERATE_BY_OTYPE : eshopConfig.getBtypeGenerateType());
    }

    private void buildByAppKeyAuthShop(EshopInfo eshopInfo) {
        CommonRequest commonRequest = new CommonRequest();
        commonRequest.setShopType(eshopInfo.getEshopType());
        CheckAuthType checkAuthType = eshopPluginService.getCheckAuthType(commonRequest);
        if (null != checkAuthType) {
            if (checkAuthType.getCode() == 2) {
                if (StringUtils.isNotEmpty(eshopInfo.getAppKey())) {
                    eshopInfo.setIsAuth(0);
                }
            }
            if (checkAuthType.getCode() == 0) {
                if (StringUtils.isNotEmpty(eshopInfo.getToken())) {
                    eshopInfo.setIsAuth(0);
                }
            }
        }
    }

    public EshopInfo getEshopByShopAccountOrId(ProductReleationRequest request) {
        EshopInfo eshop = null;
        if (request.getOtypeId() != null) {
            eshop = mapper.getEshopInfoById(request.getProfileId(), request.getOtypeId());
            if (eshop == null) {
                throw new RuntimeException("网店不存在,网店id:" + request.getOtypeId());
            }
        } else {
            List<EshopInfo> eshopPageInfos = mapper.queryEShopByShopAccount(request.getEshopAccount(), request.getProfileId());
            if (null == eshopPageInfos || eshopPageInfos.size() == 0) {
                throw new RuntimeException("shopAccount不存在，账套id" + request.getProfileId() + "shopAccount:" + request.getEshopAccount());
            }
            if (eshopPageInfos.size() > 1) {
                throw new RuntimeException("查询到有多个相同的shopAccount");
            }
            eshop = eshopPageInfos.get(0);
        }

        return eshop;
    }

    public boolean hasSupportOpenPtypeUrl(ShopType shopType) {
        try {
            if (shopType == null) {
                return false;
            }
            return EshopUtils.isFeatureSupported(EshopOpenProductUrlFeature.class, shopType);
        } catch (Exception ex) {
            logger.error(String.format("获取网店是否支持打开线上图片出错，错误原因：%s", ex.getMessage()), ex);
        }
        return false;
    }

    public List<OtypeStandardApiResponse> getEshopByStandardApi() {
        return mapper.getEshopByStandardApi(CurrentUser.getProfileId());
    }

    public boolean moveTop(BigInteger id) {
        return mapper.moveTop(id, UId.newId(), CurrentUser.getProfileId()) > 0;
    }

    public boolean moveBottom(BigInteger id) {
        return mapper.moveBottom(id, CurrentUser.getProfileId()) > 0;
    }

    public boolean moveUp(String id) {
        String[] split = id.split(",");
        BigInteger curr_rowindex = mapper.getRowIndex(new BigInteger(split[0]));
        BigInteger pre_rowindex = mapper.getRowIndex(new BigInteger(split[1]));
        mapper.updateRowIndex(new BigInteger(split[0]), pre_rowindex);
        mapper.updateRowIndex(new BigInteger(split[1]), curr_rowindex);
        return true;
    }


    public boolean moveDown(String id) {
        String[] split = id.split(",");
        BigInteger curr_rowindex = mapper.getRowIndex(new BigInteger(split[0]));
        BigInteger pre_rowindex = mapper.getRowIndex(new BigInteger(split[1]));
        mapper.updateRowIndex(new BigInteger(split[0]), pre_rowindex);
        mapper.updateRowIndex(new BigInteger(split[1]), curr_rowindex);
        return true;
    }

    public boolean eshopMove(EshopSortRequest request) {
        try {
            //拖拽行(位置下标)
            int dragIndex = request.getDragIndex();
            //目标行(位置下标)
            int rowIndex = request.getRowIndex();
            if (dragIndex == rowIndex) {
                return false;
            }
            List<Otype> sourceEshopList = request.getEshopList();
            if (CollectionUtils.isEmpty(sourceEshopList)) {
                return false;
            }
            if (sourceEshopList.size() == 1) {
                return true;
            }
            Otype dragOtype = sourceEshopList.get(dragIndex);
            Otype targetOtype = sourceEshopList.get(rowIndex);

            List<Otype> splitOtypes = new ArrayList<>();
            if (dragIndex > rowIndex) {
                splitOtypes = sourceEshopList.subList(rowIndex, dragIndex);
                Collections.reverse(splitOtypes);
            } else {
                splitOtypes = sourceEshopList.subList(dragIndex + 1, rowIndex + 1);
            }

            moveEshopTree(sourceEshopList, dragOtype, targetOtype.getRowindex());
            BigInteger otypeDrogRowindex = dragOtype.getRowindex();
            for (Otype item : splitOtypes) {
                if (item.getId().compareTo(dragOtype.getId()) == 0) {
                    otypeDrogRowindex = item.getRowindex();
                    continue;
                }
                moveEshopTree(sourceEshopList, item, otypeDrogRowindex);
                otypeDrogRowindex = item.getRowindex();


            }
        } catch (Exception ex) {
            logger.error("网店排序出现异常", ex);
            return false;
        }
        return true;
    }

    private void moveEshopTree(List<Otype> sourceEshopList, Otype dragOtype, BigInteger targetOtypeIndex) {
        mapper.updateRowIndex(dragOtype.getId(), targetOtypeIndex);
        if (StringUtils.isEmpty(dragOtype.getGroupId()) || !dragOtype.isMainEshop()) {
            return;
        }
        String groupId = dragOtype.getGroupId();
        List<Otype> needMoveEshopList = sourceEshopList.stream().filter(item -> item.getGroupId().equals(groupId) && item.isMainEshop() == false).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(needMoveEshopList)) {
            int i = 1;
            for (Otype otype : needMoveEshopList) {
                mapper.updateRowIndex(otype.getId(), targetOtypeIndex.subtract(BigInteger.valueOf(i)));
                i++;
            }
        }
    }

    public List<EshopInfo> queryEshopByOnlineEshopIdAndShopType(EshopInfo entity, List<ShopType> shopTypes) {
        return mapper.queryEshopByOnlineEshopIdAndShopType(entity, shopTypes);
    }

    public List<EshopInfo> queryEshopByOnlineFullNameAndShopType(EshopInfo entity, List<ShopType> shopTypes) {
        return mapper.queryEshopByOnlineFullNameAndShopType(entity, shopTypes);
    }

    /**
     * 后端保存一个网店
     *
     * @param eshop      店铺信息
     * @param eshop      店铺信息
     * @param onlyUpdate 是否只更新店铺授权
     */
    public BigInteger saveShopBack(EShopPageInfo eshop, boolean onlyUpdate) {
        /**
         * 校验pl_eshop,新增或者更新
         */
        eshop.setToken(KmsClient.encrypt(eshop.getToken()));
        eshop.setRefreshToken(KmsClient.encrypt(eshop.getRefreshToken()));
        EshopInfo eshopInfo = new EshopInfo();
        eshopInfo.setOnlineEshopId(eshop.getOnlineEshopId());
        eshopInfo.setFullname(eshop.getFullname());
        eshopInfo.setProfileId(eshop.getProfileId());
        eshopInfo.setEshopType(eshop.getEshopType());
        eshopInfo.setAppSecret(eshop.getAppSecret());
        eshopInfo.setOtypeId(eshop.getOtypeId());
        List<ShopType> multiShopTypes = null;
        Map<Integer, String> multiEshopTypeMapping = quickLoginConfig.getMultiEshopTypeMapping();
        if (CollectionUtils.isNotEmpty(multiEshopTypeMapping)) {
            String multiShopTypeStr = multiEshopTypeMapping.getOrDefault(eshop.getEshopType().getCode(), null);
            if (StringUtils.isNotBlank(multiShopTypeStr)) {
                multiShopTypes = new ArrayList<>();
                String[] multiShopTypeArr = multiShopTypeStr.split(",");
                for (String shopType : multiShopTypeArr) {
                    multiShopTypes.add(ShopType.valueOf(Integer.parseInt(shopType)));
                }
            }
        }
        // ngp是传过来otypeid了的
        List<EshopInfo> eshopInfos = null;
        if (eshop.getOtypeId() != null) {
            eshopInfos = queryEshopByOtypeIdAndShopType(eshopInfo, multiShopTypes);
        }
        if (CollectionUtils.isEmpty(eshopInfos)) {
            eshopInfos = queryEshopByOnlineEshopIdAndShopType(eshopInfo, multiShopTypes);
        }
        //处理取消授权找不到店铺,店铺名称又相同无法创建新店铺问题。
        if (CollectionUtils.isEmpty(eshopInfos)) {
            eshopInfos = queryEshopByOnlineFullNameAndShopType(eshopInfo, multiShopTypes);
        }
        if (CollectionUtils.isEmpty(eshopInfos)) {
            if (onlyUpdate) {
                return eshop.getOtypeId();
            }
            BigInteger btypeId = BigInteger.ZERO;
            eshop.setProfileId(CurrentUser.getProfileId());
            // 1.判重
            boolean b = checkOtypeDuplicate(eshop);
            if (!b) {
                try {
                    Btype btypeByName = getBtypeByName(eshop.getProfileId(), eshop.getFullname());
                    if (btypeByName == null) {
                        try {
                            BaseBtype baseBtype = new BaseBtype(eshop.getFullname(), UId.newId().toString());
                            GeneralResult<Object> generalResult = baseApi.saveBtype(baseBtype);
                            if (generalResult.getCode() == 200) {
                                btypeId = JsonUtils.toObject(JsonUtils.toJson(generalResult.getData()), BigInteger.class);
                            } else if (generalResult.getCode() == 5001001) {
                                btypeId = retryGetBtype(eshop.getFullname());
                            }

                        } catch (Exception e) {
                            logger.error("账套ID:{}，尝试根据网店名称创建往来单位失败,失败原因：{}", eshop.getProfileId(), e.getMessage(), e);
                            throw new RuntimeException("尝试根据网店名称创建往来单位失败，请稍后重试");
                        }
                    }
                } catch (Exception e) {
                    logger.error("账套ID:{},查询已有往来单位失败,失败原因：{}", eshop.getProfileId(), e.getMessage(), e);
                    throw new RuntimeException("查询已有往来单位失败，请稍后重试, 错误原因： " + e.getMessage());
                }
            } else {
                throw new RuntimeException("已存在相同名称的网店，请重新填写网店名称！");
            }
            // 构建和保存进销存otype
            BaseStore baseStore = buildBaseStore(eshop, btypeId);
            FeignResult<BigInteger> otypeResult = baseInfoApi.saveBaseOtype(baseStore);
            eshop.setOtypeId(baseStore.getId());
            if (otypeResult != null && otypeResult.getData() != null && otypeResult.getData().compareTo(BigInteger.ZERO) > 0) {
                //创建otype返回的id是销售机构的otypeid
                eshop.setOtypeId(otypeResult.getData());
            } else {
                String failedMsg = otypeResult == null ? "" : JsonUtils.toJson(otypeResult);
                //保存销售机构失败：
                logger.error("快速登录创建otype失败，profileid:{},参数：{}，失败信息：{}", eshop.getProfileId(), JsonUtils.toJson(eshop), failedMsg);
                return null;
            }
            //发货时效
            eshop.setDeliverDuration(48);
            //线上同意退货时效
            eshop.setRefundPromisedAgreeDuration(24);
            //线上同意退款时效
            eshop.setRefundPromisedConfirmDuration(24);
            eshop.setMode(EshopInfoConstants.ADD_MODE);
            eshop.setHasTokenExpired(false);
            eshop.setHasException(false);
            eshop.setDeleted(false);
            eshop.setStoped(false);
            eshop.setAutoRefreshProductEnabled(false);
            eshop.setCheckAccountType(CheckAccountType.PLATFORM_CHECK);
            eshop.setAutoSyncOrderEnabled(false);
            eshop.setAutoSyncStockEnabled(false);
            eshop.setMsgNotifyEnabled(false);
            eshop.setAgEnabled(false);
            eshop.setTmcEnabled(false);
            eshop.setMallType(MallType.NOMARL);
            boolean isYdh = eshop.getEshopType().equals(ShopType.MiddleGround) || eshop.getEshopType().equals(ShopType.MiddleGroundDingHuo)
                    || eshop.getEshopType().equals(ShopType.MiddleGroundDistributor) || eshop.getEshopType().equals(ShopType.MiddleGroundLocal)
                    || eshop.getEshopType().equals(ShopType.MiddleGroundSupplier);
            GenerateBtypeType btypeGenerateType = isYdh ?GenerateBtypeType.GENERATE_BY_FX : GenerateBtypeType.GENERATE_BY_OTYPE;
            eshop.setBtypeGenerateType(btypeGenerateType);
            eshop.setMappingType(MappingType.XCODEMAPPING);
            eshop.setXcodeMappingRequired(false);
            eshop.setIndependentAccountingEnabled(0);
            eshop.setNeedOrder(true);
            eshop.setNeedAuth(true);
            eshop.setMatchLocalSameBtypeEnable(true);
            //时效超时提醒=》默认：无时效
            eshop.setMentionDeliverDuration(-1);
            //订单的发货流程 默认仓储管理
            eshop.setDeliverProcessUsetype(1);
            //发货流程方式 默认仓储管理
            eshop.setSendProcessWay(1);
        } else {
            eshop.setOtypeId(eshopInfos.get(0).getOtypeId());
            eshop.setMode(EshopInfoConstants.UPDATE_MODE_BY_LOGIN);
        }
        eshop.setOtypeFullname(eshop.getFullname());
        if (StringUtils.isEmpty(eshop.getGroupId())) {
            eshop.setGroupId(eshop.getOtypeId().toString());
        }
        try {
            BaseResponse baseResponse = saveEshopInfo(eshop);
            if (eshop.getMode() == EshopInfoConstants.ADD_MODE && !baseResponse.isSuccess()) {
                baseInfoApi.deleteBaseOtype(Collections.singletonList(eshop.getOtypeId()));
            }
            if (eshop.getMode() == EshopInfoConstants.ADD_MODE && baseResponse.isSuccess()) {
                //添加店铺映射关系
                checkAndPushAuthToProfile(eshop);
            }
            if (baseResponse != null && !baseResponse.isSuccess()) {
                String msg = StringUtils.isEmpty(baseResponse.getMessage()) ? "" : baseResponse.getMessage();
                logger.error("profileid: {},eshopId:{} 快速登录保存网店信息失败,失败原因：{},参数：{}", eshop.getProfileId(), eshop.getOtypeId(), msg, JsonUtils.toJson(eshop));
            }
        } catch (Exception e) {
            if (eshop.getMode() == EshopInfoConstants.ADD_MODE) {
                baseInfoApi.deleteBaseOtype(Collections.singletonList(eshop.getOtypeId()));
            }
            String msg = StringUtils.isEmpty(e.getMessage()) ? "" : e.getMessage();
            logger.error("profileid: {},eshopId:{} 快速登录保存网店信息失败,失败原因：{},参数：{}", eshop.getProfileId(), eshop.getOtypeId(), msg, JsonUtils.toJson(eshop));
            throw new RuntimeException(e.getMessage());
        }
        return eshop.getOtypeId();
    }

    private List<EshopInfo> queryEshopByOtypeIdAndShopType(EshopInfo eshopInfo, List<ShopType> multiShopTypes) {
        return mapper.queryEshopByOtypeIdAndShopType(eshopInfo, multiShopTypes);
    }

    private BigInteger retryGetBtype(String name) {
        BtypeQueryRequest request = new BtypeQueryRequest();
        BtypeQueryParameter parameter = new BtypeQueryParameter(name);
        request.setQueryParams(parameter);
        GeneralResult<BaseBtypeResponse> result = baseApi.getBtype(request);
        if (result.getCode() != 200) {
            return BigInteger.ZERO;
        }
        BaseBtypeResponse data = result.getData();
        if (data == null || data.getTotal() != 1) {
            return BigInteger.ZERO;
        }
        BigInteger btypeId = data.getList().get(0).getId();
        return btypeId;
    }

    public BigInteger saveShopBackFromMiddleGround(EShopPageInfo eshop) {
        /**
         * 校验pl_eshop,新增或者更新
         */
        EshopInfo eshopInfo = new EshopInfo();
        eshopInfo.setOnlineEshopId(eshop.getOnlineEshopId());
        eshopInfo.setFullname(eshop.getFullname());
        eshopInfo.setProfileId(eshop.getProfileId());
        eshopInfo.setEshopType(eshop.getEshopType());
        eshopInfo.setAppSecret(eshop.getAppSecret());
        eshop.setBtypeId(eshop.getBtypeId());
        List<ShopType> multiShopTypes = null;
        Map<Integer, String> multiEshopTypeMapping = quickLoginConfig.getMultiEshopTypeMapping();
        if (CollectionUtils.isNotEmpty(multiEshopTypeMapping)) {
            String multiShopTypeStr = multiEshopTypeMapping.getOrDefault(eshop.getEshopType().getCode(), null);
            if (StringUtils.isNotBlank(multiShopTypeStr)) {
                multiShopTypes = new ArrayList<>();
                String[] multiShopTypeArr = multiShopTypeStr.split(",");
                for (String shopType : multiShopTypeArr) {
                    multiShopTypes.add(ShopType.valueOf(Integer.parseInt(shopType)));
                }
            }
        }
        List<EshopInfo> eshopInfos = queryEshopByOnlineEshopIdAndShopType(eshopInfo, multiShopTypes);
        //处理取消授权找不到店铺,店铺名称又相同无法创建新店铺问题。
        if (CollectionUtils.isEmpty(eshopInfos)) {
            eshopInfos = queryEshopByOnlineFullNameAndShopType(eshopInfo, multiShopTypes);
        }
        if (CollectionUtils.isEmpty(eshopInfos)) {
            // 1.判重
            if (!checkOtypeDuplicate(eshop)) {
                if (ShopType.QiMenSupplier == eshop.getEshopType()) {
                    try {
                        Btype btypeByName = getBtypeByName(eshop.getProfileId(), eshop.getFullname());
                        if (btypeByName == null) {
                            try {
                                BaseBtype baseBtype = new BaseBtype(eshop.getFullname(), UId.newId().toString());
                                baseApi.saveBtype(baseBtype);
                            } catch (Exception e) {
                                logger.error("账套ID:{}，尝试根据网店名称创建往来单位失败,失败原因：{}", eshop.getProfileId(), e.getMessage(), e);
                                throw new RuntimeException("尝试根据网店名称创建往来单位失败，请稍后重试");
                            }
                        } else {
                            eshop.setBtypeId(btypeByName.getId());
                        }
                    } catch (Exception e) {
                        logger.error("账套ID:{},查询已有往来单位失败,失败原因：{}", eshop.getProfileId(), e.getMessage(), e);
                        throw new RuntimeException("查询已有往来单位失败，请稍后重试, 错误原因： " + e.getMessage());
                    }
                }
            } else {
                throw new RuntimeException("已存在相同名称的网店，请重新填写网店名称！");
            }
            // 构建和保存进销存otype
            BaseStore baseStore = buildBaseStoreFromMiddleGround(eshop);
            baseInfoApi.saveBaseOtype(baseStore);
            eshop.setOtypeId(baseStore.getId());
            eshop.setHasTokenExpired(false);
            eshop.setHasException(false);
            eshop.setDeleted(false);
            eshop.setStoped(false);
            eshop.setAutoRefreshProductEnabled(false);
            eshop.setCheckAccountType(CheckAccountType.PLATFORM_CHECK);
            eshop.setAutoSyncOrderEnabled(false);
            eshop.setAutoSyncStockEnabled(false);
            eshop.setMsgNotifyEnabled(false);
            eshop.setAgEnabled(false);
            eshop.setTmcEnabled(false);
            eshop.setMallType(MallType.NOMARL);
            eshop.setBtypeGenerateType(GenerateBtypeType.GENERATE_BY_OTYPE);
            eshop.setMappingType(MappingType.XCODEMAPPING);
            eshop.setXcodeMappingRequired(false);
            eshop.setIndependentAccountingEnabled(0);
            eshop.setNeedOrder(true);
            eshop.setNeedAuth(true);
            eshop.setMode(EshopInfoConstants.ADD_MODE);
            if (eshop.getEshopType().equals(ShopType.QiMenSupplier)) {
                eshop.setBtypeGenerateType(GenerateBtypeType.GENERATE_BY_FX);
            }
        } else if (eshop.getEshopType().equals(ShopType.QiMenSupplier)) {
            BaseStore baseStore = buildBaseStoreFromMiddleGround(eshop);
            baseStore.setId(eshopInfos.get(0).getOtypeId());
            baseInfoApi.updateBaseOtype(baseStore);
            eshop.setOtypeId(eshopInfos.get(0).getOtypeId());
            eshop.setMode(EshopInfoConstants.UPDATE_MODE);
        } else {
            throw new RuntimeException("已存在相同名称的网店，请重新填写网店名称！");
        }
        eshop.setProfileId(CurrentUser.getProfileId());
        eshop.setOtypeFullname(eshop.getFullname());
        saveEshopInfoFromMiddleGround(eshop);
        return eshop.getOtypeId();
    }

    /**
     * 构建Otype
     *
     * @param eshop
     * @param btypeId
     * @return
     */
    public BaseStore buildBaseStore(EShopPageInfo eshop, BigInteger btypeId) {
        BaseStore baseStore = new BaseStore();
        baseStore.setId(UId.newId());
        baseStore.setFullname(eshop.getFullname());
        baseStore.setProfileId(eshop.getProfileId());
        baseStore.setOcategory(0);
        baseStore.setKtypeId(getKtypeByKtypeName(eshop.getProfileId(), "默认仓库"));
        baseStore.setBtypeId(btypeId);
        baseStore.setAtypeId(getAtypeIdByAtypeName(eshop.getProfileId(), "现金"));
        baseStore.setPartypeId("00000");
        baseStore.setAccountType(1);
        baseStore.setCheckAccountType(2);
        //发货时效
        baseStore.setDeliverDuration(48);
        baseStore.setIndependentCheck(0);
        return baseStore;
    }

    public BaseStore buildBaseStoreFromMiddleGround(EShopPageInfo eshop) {
        BaseStore baseStore = new BaseStore();
        baseStore.setId(UId.newId());
        baseStore.setFullname(eshop.getFullname());
        baseStore.setProfileId(eshop.getProfileId());
        baseStore.setOcategory(0);
        Stock ktype = GetBeanUtil.getBean(EshopOrderBaseInfoMapper.class).getAdminBaseKtypeByProfileId(CurrentUser.getProfileId());
        baseStore.setKtypeId(null == ktype ? BigInteger.ZERO : ktype.getId());
        baseStore.setBtypeId(eshop.getBtypeId());
        baseStore.setAtypeId(getAtypeIdByAtypeName(eshop.getProfileId(), "银行存款"));
        baseStore.setPartypeId("00000");
        baseStore.setAccountType(1);
        baseStore.setCheckAccountType(2);
        baseStore.setDeliverDuration(48);
        baseStore.setIndependentCheck(0);
        baseStore.setMobile(eshop.getMobile());
        baseStore.setPeople(eshop.getSenderName());
        baseStore.setCellphone(eshop.getMobile());
        return baseStore;
    }

    private BigInteger getKtypeByKtypeName(BigInteger profileId, String ktypeName) {
        return ktypeMapper.getKtypeByKtypeName(ktypeName, profileId).getId();
    }

    private BigInteger getAtypeIdByAtypeName(BigInteger profileId, String atypeName) {
        return atypeMapper.getAtypeByName(atypeName, profileId).getId();
    }


    public void checkAndRegisterRds(List<Otype> otypeList) {
        eshopService.checkAndRegisterRds(otypeList);
    }


    public boolean addEshopAddressMapping(EshopAddressMapping addressMapping) {
        List<EshopInfo> eshopInfoList = eshopMapper.getEshopInfoByProfileId(CurrentUser.getProfileId());
        for (EshopInfo info : eshopInfoList) {
            BaseRequest request = new BaseRequest();
            request.setShopType(info.getEshopType());
            request.setShopId(info.getOtypeId());
            List<ShopRefundAddress> shopRefundAddressList = null;
            try {
                shopRefundAddressList = bifrostEshopRefundService.getShopRefundAddressList(request);
                shopRefundAddressList = excludeEmptyRefundAddress(shopRefundAddressList);
            } catch (Exception e) {
                continue;
            }
            for (ShopRefundAddress address : shopRefundAddressList) {
                if (addressMapping.getOnlineProvinceName().contains(address.getProvinceName())) {
                    address.setProvinceName(addressMapping.getOnlineProvinceName());
                }
                if (addressMapping.getOnlineCityName().contains(address.getCityName())) {
                    address.setCityName(addressMapping.getOnlineCityName());
                }
                if (addressMapping.getOnlineDistrictName().contains(address.getDistrictName())) {
                    address.setDistrictName(addressMapping.getOnlineDistrictName());
                }
                addressMapping.setEshopId(info.getOtypeId());
                EshopAddressMapping addressMap = new EshopAddressMapping();
                if (addressMapping.getUniqueMark().equals(Md5Utils.md5(String.format("%s%s%s%s%s%s%s", CurrentUser.getProfileId(), info.getOtypeId(), address.getProvinceName(), address.getCityName(), address.getDistrictName(), address.getRefundAddress().replaceAll(" ", ""), address.getReceiverName())))) {
                    addressMap.setRefundAddressId(addressMapping.getRefundAddressId());
                }
                EshopAddressMapping eshopAddressMapping = addressMappingMapper.queryEshopAddressMapping(addressMapping);
                if (null != eshopAddressMapping) {
                    addressMappingMapper.updateEshopAddressMapping(addressMapping);
                    continue;
                }
                addressMap.setId(UId.newId());
                addressMap.setProfileId(CurrentUser.getProfileId());
                addressMap.setEshopId(info.getOtypeId());
                addressMap.setOnlineProvinceName(address.getProvinceName());
                addressMap.setOnlineCityName(address.getCityName());
                addressMap.setOnlineDistrictName(address.getDistrictName());
                addressMap.setOnlineFullAddress(address.getRefundAddress());
                addressMap.setOnlineRefundPhone(address.getReceiverPhone() == null ? "" : address.getReceiverPhone().length() > 200 ? address.getReceiverPhone().substring(0, 199) : address.getReceiverPhone());
                addressMap.setOnlineRefundTel(address.getReceiverTel() == null ? "" : address.getReceiverTel().length() > 200 ? address.getReceiverTel().substring(0, 199) : address.getReceiverTel());
                addressMap.setOnlineAddressId(address.getRefundAddressId());
                addressMap.setOnlineRefundName(address.getReceiverName());
                addressMappingMapper.insertEshopAddressMapping(addressMap);
            }
        }
        return false;
    }

    private List<ShopRefundAddress> excludeEmptyRefundAddress(List<ShopRefundAddress> shopRefundAddressList) {
        List<ShopRefundAddress> refundAddressList = new ArrayList<>();
        for (ShopRefundAddress shopRefundAddress : shopRefundAddressList) {
            if (StringUtils.isEmpty(shopRefundAddress.getProvinceName())) {
                continue;
            }
            if (StringUtils.isEmpty(shopRefundAddress.getCityName())) {
                continue;
            }
            if (StringUtils.isEmpty(shopRefundAddress.getDistrictName())) {
                continue;
            }
            if (StringUtils.isEmpty(shopRefundAddress.getRefundAddress())) {
                continue;
            }
            if (StringUtils.isEmpty(shopRefundAddress.getReceiverName())) {
                continue;
            }
            refundAddressList.add(shopRefundAddress);
        }
        return refundAddressList;
    }

    public void modifyDownloadOrderLastSuccessTime(EshopInfo eshop) {
        List<EshopOrderSyncCondition> eshopOrderSyncCondition = mapper.getEshopOrderSyncCondition(eshop.getProfileId(), eshop.getOtypeId());
        for (EshopOrderSyncCondition condition : eshopOrderSyncCondition) {
            if (condition.getTaskType() == TaskType.OrderTask && null != condition.getLastDownloadSuccessEndTime()) {
                EshopOrderSyncCondition conditionEntity = new EshopOrderSyncCondition();
                conditionEntity.setEshopId(eshop.getOtypeId());
                conditionEntity.setProfileId(eshop.getProfileId());
                conditionEntity.setLastDownloadEndTime(condition.getLastDownloadSuccessEndTime());
                conditionEntity.setTaskType(condition.getTaskType());
                mapper.modifyEshopCondition(conditionEntity);
                break;
            }
        }
    }

    public void baseInfoChangeNotify(String info) {
        logger.info("接收到对网店进行操作：" + info);
        ProfilebaseInfoChangeNotifyRequest request = new ProfilebaseInfoChangeNotifyRequest();
        try {
            request.setProfileId(CurrentUser.getProfileId());
            request.setType(0);
            request.setProductId(CurrentUser.getProductId());
            profileApi.baseInfoChangeNotify(request);
        } catch (Exception e) {
            String req = JSONObject.toJSONString(request);
            logger.error("网店变动信息推送账套服务失败,参数:" + req + "失败信息：" + e);
        }
    }

    public void updateEshopOrderCondition(EshopInfo eshop) {
        List<EshopOrderSyncCondition> eshopOrderSyncConditions = mapper.getEshopOrderSyncCondition(eshop.getProfileId(), eshop.getOtypeId());
        List<EshopOrderSyncCondition> syncConditions = eshopOrderSyncConditions.stream().filter(x -> x.getTaskType() == TaskType.OrderTask).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(syncConditions)) {
            Date nowDate = DateUtils.getDate();
            EshopOrderSyncCondition eshopOrderSyncCondition = syncConditions.get(0);
            if (eshopOrderSyncCondition.getSliceTime() == null) {
                //时间为空，说明没有进行过自动下单，则忽略
                return;
            }
            Date lastDownloadSuccessEndTime = eshopOrderSyncCondition.getLastDownloadSuccessEndTime();
            Date sliceTime;
            if (lastDownloadSuccessEndTime == null ||
                    (DateUtils.addHours(lastDownloadSuccessEndTime, syncOrderConfig.getAutoStopSyncHour()).before(nowDate) &&
                            nowDate.getTime() - lastDownloadSuccessEndTime.getTime() > syncOrderConfig.getMaxAllowDownloadTime())) {
                //1、最后一次下载时间为空,说明没有下载成功过
                //2、当前时间-最后一次下载成功时间>自动下单的最大间隔时间(7天--配置)
                sliceTime = new Date(nowDate.getTime() - syncOrderConfig.getMaxAllowDownloadTime());
            } else {
                sliceTime = lastDownloadSuccessEndTime;
            }
            EshopOrderSyncCondition condition = new EshopOrderSyncCondition();
            condition.setProfileId(eshop.getProfileId());
            condition.setEshopId(eshop.getOtypeId());
            condition.setTaskType(eshopOrderSyncCondition.getTaskType());
            condition.setSliceTime(sliceTime);
            condition.setIncreaseSliceTime(sliceTime);
            condition.setFullSliceTime(sliceTime);
            condition.setLastDownloadEndTime(nowDate);
            condition.setLastDownloadSuccessEndTime(nowDate);
            mapper.modifyEshopCondition(condition);
        }
    }

    public int queryEshopCountByShopType(BigInteger profileId, int shopType) {
        return mapper.queryEshopCountByShopType(profileId, shopType);
    }

    public List<EshopInfo> getEshopListByShopType(BigInteger profileId, int shopType) {
        BigInteger employeeId = CurrentUser.getEmployeeId();
        boolean otypeLimited = PermissionValiateService.isOtypeLimited();
        if (!otypeLimited) {
            employeeId = null;
        }
        return mapper.getEshopListByShopType(profileId, shopType, employeeId);
    }

    public boolean addEshopAddressMappingNew(EshopAddressMapping addressMapping) {
        List<EshopInfo> eshopInfoList = eshopMapper.getEshopInfoByProfileId(CurrentUser.getProfileId());
        for (EshopInfo info : eshopInfoList) {
            BaseRequest request = new BaseRequest();
            request.setShopType(info.getEshopType());
            request.setShopId(info.getOtypeId());

            List<ShopRefundAddress> shopRefundAddressList = getShopRefundAddressListSafe(request);
            if (shopRefundAddressList == null) {
                continue;
            }
            shopRefundAddressList = excludeEmptyRefundAddress(shopRefundAddressList);

            for (ShopRefundAddress address : shopRefundAddressList) {
                updateAddressInfo(addressMapping, address, info);
            }
        }
        return false;
    }

    private List<ShopRefundAddress> getShopRefundAddressListSafe(BaseRequest request) {
        try {
            return bifrostEshopRefundService.getShopRefundAddressList(request);
        } catch (Exception e) {
            // Log the exception for troubleshooting.
            // LOGGER.error("Failed to fetch shop refund address list", e);
            return null;
        }
    }

    private void updateAddressInfo(EshopAddressMapping addressMapping, ShopRefundAddress address, EshopInfo info) {
        if (addressMapping.getOnlineProvinceName().contains(address.getProvinceName())) {
            address.setProvinceName(addressMapping.getOnlineProvinceName());
        }
        if (addressMapping.getOnlineCityName().contains(address.getCityName())) {
            address.setCityName(addressMapping.getOnlineCityName());
        }
        if (addressMapping.getOnlineDistrictName().contains(address.getDistrictName())) {
            address.setDistrictName(addressMapping.getOnlineDistrictName());
        }
        addressMapping.setEshopId(info.getOtypeId());
        EshopAddressMapping eshopAddressMapping = addressMappingMapper.queryEshopAddressMapping(addressMapping);
        if (eshopAddressMapping != null) {
            addressMappingMapper.updateEshopAddressMapping(addressMapping);
            return;
        }

        EshopAddressMapping newAddressMapping = new EshopAddressMapping();
        initNewAddressMapping(addressMapping, address, info, newAddressMapping);
        addressMappingMapper.insertEshopAddressMapping(newAddressMapping);
    }

    private void initNewAddressMapping(EshopAddressMapping addressMapping, ShopRefundAddress address, EshopInfo info, EshopAddressMapping newAddressMapping) {
        newAddressMapping.setId(UId.newId());
        newAddressMapping.setProfileId(CurrentUser.getProfileId());
        newAddressMapping.setEshopId(info.getOtypeId());
        newAddressMapping.setOnlineProvinceName(address.getProvinceName());
        newAddressMapping.setOnlineCityName(address.getCityName());
        newAddressMapping.setOnlineDistrictName(address.getDistrictName());
        newAddressMapping.setOnlineFullAddress(sanitizeString(address.getRefundAddress(), 200));
        newAddressMapping.setOnlineRefundPhone(sanitizeString(address.getReceiverPhone(), 200));
        newAddressMapping.setOnlineRefundTel(sanitizeString(address.getReceiverTel(), 200));
        newAddressMapping.setOnlineAddressId(address.getRefundAddressId());
        newAddressMapping.setOnlineRefundName(address.getReceiverName());
    }

    private String sanitizeString(String input, int maxLength) {
        return input == null ? "" : input.length() > maxLength ? input.substring(0, maxLength) : input;
    }

    public List<EshopInfo> queryEshopByOnline(BigInteger profileId, String onlineShopId) {
        List<EshopInfo> eshopInfos = mapper.queryEshopByOnlineEshopId(profileId, onlineShopId);
        return eshopInfos;
    }

    public int getEshopInfoByAppKey(EshopInfo eshopInfo) {
        return mapper.queryEshopCountByAppkey(CurrentUser.getProfileId(), eshopInfo.getEshopType().getCode(), eshopInfo.getAppKey());
    }

    public List<Integer> getnotSupportShoptype() {
        String[] split = serviceConfig.getNotSupportCreateEshopCorrespondShopType().split(",");
        List<Integer> shoptypeList = Arrays.stream(split).map(Integer::parseInt).collect(Collectors.toList());
        return shoptypeList;
    }

    public List<EshopInfo> queryEshopInfoByAppkey(EshopInfo eshopInfo) {
        return mapper.queryEshopInfoByAppkey(CurrentUser.getProfileId(), eshopInfo.getEshopType().getCode(), eshopInfo.getAppKey());
    }


    public void autoSaveMappingRelation(EShopPageInfo info) {
        info.setProfileId(CurrentUser.getProfileId());
        mapper.autoSaveMappingRelation(info);
    }

    public List<PlatformEshoptypeSupport> getPlatformTypeFiles(EShopPageInfo pageInfo) {
        String cacheKey = "platformTypeFiles_" + CurrentUser.getProfileId() + serviceConfig.getShopTypeFilesRedisVersion();
        List<PlatformEshoptypeSupport> platformTypeFiles = getPlatformTypeFilesByRedis(cacheKey);
        if(CollectionUtils.isNotEmpty(platformTypeFiles)){
            return filterByKeyWorlds(platformTypeFiles, pageInfo.getKey());
        }

        String newEshopSign = serviceConfig.getNewEshopSign();
        String getShoptypeHasSort = serviceConfig.getGetShoptypeHasSort();
        List<String> newSignEshopType = new ArrayList<>();
        List<String> getShoptypeHasSortList = new ArrayList<>();
        if (StringUtils.isNotEmpty(newEshopSign)) {
            newSignEshopType = Arrays.asList(newEshopSign.split(","));
        }
        if (StringUtils.isNotEmpty(getShoptypeHasSort)) {
            getShoptypeHasSortList = Arrays.asList(getShoptypeHasSort.split(","));
        }
        List<PlatformEshopSupport> supportEshopTypes = EshopUtils.getSupportEShopTypeByPlatformTypeIsOther(-9999);

        List<PlatformEshoptypeSupport> eshoptypeSupports = new ArrayList<>();
        List<PlatformEshoptypeSupport> eshoptypeSupportHasSort = new ArrayList<>();
        List<PlatformEshoptypeSupport> newEshoptypeSupports = new ArrayList<>();
        for (PlatformEshopSupport supportEshopType : supportEshopTypes) {
            if (supportEshopType.getType() == 890) {
                continue;
            }
            if (pageInfo.getOcategory() != OrganizationType.XN_SHOP) {
                if (supportEshopType.getType() == 9999) {
                    continue;
                }
            }
            if (pageInfo.getOcategory() == OrganizationType.XN_SHOP) {
                newSignEshopType = new ArrayList<>();
                if (supportEshopType.getType() != 9999) {
                    continue;
                }
            }
            PlatformEshoptypeSupport support = new PlatformEshoptypeSupport();
            support.setName(supportEshopType.getName());
            support.setPlatformType(supportEshopType.getPlatformType());
            support.setShoptype(supportEshopType.getType());
            support.setType(supportEshopType.getType());
            support.setShowAppKey(supportEshopType.getShowAppKey());
            support.setUrl(String.format("/sale/eshoporder/image/eshoplogo/%s_logo.png", supportEshopType.getType()));
            buildAuthType(support);
            if (newSignEshopType.contains(supportEshopType.getType() + "")) {
                support.setSign(true);
                newEshoptypeSupports.add(support);
                continue;
            }
            support.setSign(false);
            eshoptypeSupports.add(support);
        }
        eshoptypeSupportHasSort = buildHasSortEshopTypeList(eshoptypeSupports, getShoptypeHasSortList);
        newEshoptypeSupports.addAll(eshoptypeSupportHasSort);
        newEshoptypeSupports = newEshoptypeSupports.stream().distinct().collect(Collectors.toList());
        writePlatformTypeFilesToRedis(cacheKey, newEshoptypeSupports);
        return filterByKeyWorlds(newEshoptypeSupports, pageInfo.getKey());
    }

    private List<PlatformEshoptypeSupport> buildHasSortEshopTypeList(List<PlatformEshoptypeSupport> eshoptypeSupports, List<String> getShoptypeHasSortList) {
        List<PlatformEshoptypeSupport> hasSortList = new ArrayList<>();
        for (String shoptype : getShoptypeHasSortList) {
            for (PlatformEshoptypeSupport eshoptypeSupport : eshoptypeSupports) {
                if (shoptype.equals(eshoptypeSupport.getShoptype() + "")) {
                    hasSortList.add(eshoptypeSupport);
                }
            }
        }

        for (PlatformEshoptypeSupport eshoptypeSupport : eshoptypeSupports) {
            List<PlatformEshoptypeSupport> collect = hasSortList.stream().filter(x -> x.getShoptype() == eshoptypeSupport.getShoptype()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(collect)) {
                hasSortList.add(eshoptypeSupport);
            }
        }

        return hasSortList;
    }

    private void buildAuthType(PlatformEshoptypeSupport support) {
        EshopState state = new EshopState();
        state.setShopType(ShopType.valueOf(support.getType()));
        EshopTagInfo eshopTag = eshopPluginService.getEshopTag(state);
        if (eshopTag == null) {
            return;
        }
        support.setAuthType(eshopTag.getAuthType().getCode());
//        support.setShowAppKey(eshopTag.isShowAppKey());
        if (!eshopTag.isShowAuth() && !eshopTag.isShowOrderLink() && !eshopTag.isShowAppKey()) {
            support.setShowAppKey(EshopShowTypeEnum.NO_SHOW_AUTH.getCode());
        }
        if (!eshopTag.isShowAuth() && !eshopTag.isShowOrderLink() && eshopTag.isShowAppKey()) {
            support.setShowAppKey(EshopShowTypeEnum.SHOW_APPKEY_NO_AUTH.getCode());
        }
        if (eshopTag.isShowAuth() && eshopTag.isShowOrderLink() && !eshopTag.isShowAppKey()) {
            support.setShowAppKey(EshopShowTypeEnum.SHOW_ORDER_AND_AUTH.getCode());
        }
        if (eshopTag.isShowAuth() && !eshopTag.isShowOrderLink() && eshopTag.isShowAppKey()) {
            support.setShowAppKey(EshopShowTypeEnum.SHOW_APPKEY_AND_AUTH.getCode());
        }
        if (eshopTag.isShowAuth() && !eshopTag.isShowOrderLink() && !eshopTag.isShowAppKey()) {
            support.setShowAppKey(EshopShowTypeEnum.SHOW_AUTH_NO_ORDER.getCode());
        }
        if (eshopTag.isShowAuth() && eshopTag.isShowOrderLink() && eshopTag.isShowAppKey()) {
            support.setShowAppKey(EshopShowTypeEnum.SHOW_ALL.getCode());
        }
    }

    private List<PlatformEshoptypeSupport> filterByKeyWorlds(List<PlatformEshoptypeSupport> eshoptypeSupports, String key) {
        if (StringUtils.isNotEmpty(key)) {
            List<PlatformEshoptypeSupport> filterEshoptypeSupportList = new ArrayList<>();
            Pattern pattern = Pattern.compile(key.toUpperCase());

            for (PlatformEshoptypeSupport support : eshoptypeSupports) {
                Matcher matcher = pattern.matcher(support.getName().toUpperCase());
                if (matcher.find()) {
                    filterEshoptypeSupportList.add(support);
                }
            }
            return filterEshoptypeSupportList;
        } else {
            return eshoptypeSupports;
        }
    }

    public void useNewAuth(ShopType shopType, EshopAuthInfo tokenInfo, AuthCheckResult authCheckResult) {
        try {
            if (null != tokenInfo) {
                tokenInfo.setAuthCheckResult(authCheckResult != null ? authCheckResult.getCode() : null);
                StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
                String key = Md5Utils.md5(String.format("doAuth%s_%s_%s", CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), shopType));
                template.opsForValue().set(key, JsonUtils.toJson(tokenInfo), 10, TimeUnit.MINUTES);
            }
        } catch (Exception e) {
            logger.error("授权失败", e);
        }
    }

    public BaseResponse saveOtype(BaseStore baseStore) {
        BaseResponse res = new BaseResponse(false);
        String message = "";
        String parOtypeId = "";
        //检查是否有相同名称的网店
        try {
            List<PlatformBranchEshopResult> eshopBranchList = baseStore.getEshopBranch();
            if (CollectionUtils.isNotEmpty(eshopBranchList)) {
                for (PlatformBranchEshopResult result : eshopBranchList) {
                    BaseStore mainBaseStore = JsonUtils.toObject(JsonUtils.toJson(baseStore), BaseStore.class);
                    mainBaseStore.setFullname(result.getFullname());
                    List<Otype> otypes = mapper.checkDuplicateNameOtype(CurrentUser.getProfileId(), mainBaseStore.getFullname());
                    if (CollectionUtils.isEmpty(otypes)) {
                        mainBaseStore.setId(UId.newId());
                        mainBaseStore.setStoped(!result.isEnabled());
                        checkAndSaveOtype(mainBaseStore);
                    }
                }
            }

            List<Otype> otypes = mapper.checkDuplicateNameOtype(CurrentUser.getProfileId(), baseStore.getFullname());
            if (CollectionUtils.isEmpty(otypes)) {
                BigInteger id = UId.newId();
                baseStore.setId(id);
                parOtypeId = id.toString();
                checkAndSaveOtype(baseStore);
            } else {
                res.setSuccess(false);
                res.setMessage(String.format("主网店名称相同的网店名称:%s，保存失败。", baseStore.getFullname()));
                return res;
            }
            res.setSuccess(true);
            res.setMessage(parOtypeId);
        } catch (Exception e) {
            res.setSuccess(false);
            res.setMessage(String.format("保存otype网店出错，错误信息：%s", e.getMessage()));
        }
        return res;
    }

    public void checkAndSaveOtype(BaseStore baseStore) {
        FeignResult<BigInteger> bigIntegerFeignResult = baseInfoApi.saveBaseOtype(baseStore);
        if ("200".equals(bigIntegerFeignResult.getCode())) {
            bigIntegerFeignResult.getData();
        } else {
            logger.error("保存Otype网店出错，错误信息：{}", bigIntegerFeignResult.getMessage());
        }
    }

    public String getUrl(int shopType) {
        String authHelperUrlList = serviceConfig.getAuthHelperUrlList();
        if (StringUtils.isEmpty(authHelperUrlList)) {
            return "";
        } else {
            ArrayList<AuthHelpEntity> list = JsonUtils.toList(authHelperUrlList, AuthHelpEntity.class);
            for (AuthHelpEntity authHelpEntity : list) {
                if (authHelpEntity.getShopType() == shopType) {
                    return authHelpEntity.getAuthHelpUrl();
                }
            }
        }
        return "";
    }

    public String saveShopAccounttoRedis(QueryEShopParameter parameter) {
        String key = Md5Utils.md5(String.format("shopAccount%s_%s_%s", CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), parameter.getShopType()));
        RedisPoolFactory redisPoolFactory = GetBeanUtil.getBean(RedisPoolFactory.class);
        StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
        template.opsForValue().set(key, parameter.getEshopAccount(), 5, TimeUnit.MINUTES);
        return "";
    }

    public boolean getRealStockEnable(BigInteger profileId) {
        List<EshopConfig> allEshopConfig = mapper.getAllEshopConfig(profileId);
        for (EshopConfig eshopConfig : allEshopConfig) {
            if (eshopConfig.isRealStockQtyEnabled()) {
                return true;
            }
        }
        return false;
    }

    public void updateRealStockQtyEnabled(boolean realStockQtyEnabled, boolean autoReportStockQtyEnabled, BigInteger eshopId, BigInteger profileId, Integer jdAuthDocType, String jdAuthDocLink) {
        mapper.updateRealStockQtyEnabled(profileId, eshopId, autoReportStockQtyEnabled, realStockQtyEnabled, jdAuthDocType, jdAuthDocLink);
    }

    public void checkRealStockQtyEnabled(EshopSystemParams params) {
        if (params.getShopType() != ShopType.JdongVC) {
            return;
        }
        boolean openRealStockQty = serviceConfig.isOpenRealStockQty();
        String sysData = getSysData(CurrentUser.getProfileId(), "openRealStockQty", "0");
        if ("2".equals(sysData)) {
            logger.error(CurrentUser.getProfileId() + ",获取授权书失败，未开启账套级别配置,目前配置为强制关闭。");
            return;
        }
        if (!openRealStockQty) {
            logger.debug(CurrentUser.getProfileId() + ",获取授权书失败，请检查账部署配置是否开启。");
            return;
        }
        try {
            EshopStockSyncAuthQueryRequest request = new EshopStockSyncAuthQueryRequest();
            request.setShopId(params.geteShopId());
            request.setShopType(params.getShopType());
            EshopStockSyncAuthQueryResponse eshopStockSyncAuthQueryResponse = bifrostEshopStockService.stockSyncAuthQuery(request);
            if (eshopStockSyncAuthQueryResponse.getSuccess()) {
                if (eshopStockSyncAuthQueryResponse.isHasAuth()) {
                    if (eshopStockSyncAuthQueryResponse.getStockAuthDocUrl().length() > 500) {
                        eshopStockSyncAuthQueryResponse.setStockAuthDocUrl(eshopStockSyncAuthQueryResponse.getStockAuthDocUrl().substring(0, 499));
                    }
                    updateRealStockQtyEnabled(true, true, params.geteShopId(), CurrentUser.getProfileId(), eshopStockSyncAuthQueryResponse.getDocType(), eshopStockSyncAuthQueryResponse.getStockAuthDocUrl());
                }
            } else {
                updateRealStockQtyEnabled(false, false, params.geteShopId(), CurrentUser.getProfileId(), 0, "");
                logger.error(String.format("%s获取授权书失败，关闭同步真实库存同步平台eshopId:%s,错误信息：%s", CurrentUser.getProfileId(), params.geteShopId(), eshopStockSyncAuthQueryResponse.getMessage()));
            }
        } catch (Exception e) {
            logger.error(String.format("%s获取授权书异常，错误信息：%s", CurrentUser.getProfileId(), e.getMessage()));
            logger.error(String.format("%s获取授权书异常，错误信息：%s", CurrentUser.getProfileId(), e));
        }
    }

    public void setDeliverDurationList(boolean isRetail, OrganizationAddOnResponse response, PageRequest<QueryEShopParameter> pageRequest) {
        QueryEShopParameter parameter = pageRequest.getQueryParams();
        List<Otype> orgList = new ArrayList<>();

        if (isRetail) {
            if (parameter.isQueryVirtual()) {
                List<Integer> ocategorys = getOcategorys(sysConfig.getOcategorys());
                ocategorys.add(OrganizationType.XN_SHOP.getCode());
                PageDevice.initPage(pageRequest);
                orgList = mapper.getOtypeList(parameter, ocategorys);
            } else {
                List<Integer> ocategorys = getOcategorys(sysConfig.getOcategorys());
                if (!parameter.isQueryVirtual()) {
                    ocategorys.remove(new Integer(OrganizationType.XN_SHOP.getCode()));
                }
                PageDevice.initPage(pageRequest);
                orgList = mapper.getOtypeList(parameter, ocategorys);
            }
        } else {
            List<Integer> ocategorys = getOcategorys(sysConfig.getOcategorys());
            if (!parameter.isNoQueryPifa()) {
                ocategorys.add(OrganizationType.PFB_STORE.getCode());
            }
            PageDevice.initPage(pageRequest);
            orgList = mapper.getOtypeList(parameter, ocategorys);
        }
        List<Integer> integerList = orgList.stream().map(Otype::getDeliverDuration).distinct().collect(Collectors.toList());
        integerList.addAll(Arrays.asList(-1, 12, 24, 48, 72));
        List<Integer> list = integerList.stream().distinct().sorted().collect(Collectors.toList());
        StringBuilder builder = new StringBuilder();
        for (Integer item : list) {
            if (item.equals(-1)) {
                builder.append(item).append("=无时效,");
            } else {
                builder.append(item).append("=").append(item).append(",");
            }
        }
        response.setDeliverDurationList(builder.substring(0, builder.length() - 1));
    }

    public void insertOrUpdateEshopQicConfig(EShopPageInfo pageInfo) {
        EshopQicConfig eshopQicConfig = pageInfo.getEshopQicConfig();
        if (null != eshopQicConfig) {
            if (eshopQicConfig.getId() == null) {
                eshopQicConfig.setId(UId.newId());
                eshopQicConfig.setEshopId(pageInfo.getOtypeId());
                eshopQicConfig.setPlatformQualityBtypeName(eshopQicConfig.getPlatformQualityBtypeName() == null ? "" : eshopQicConfig.getPlatformQualityBtypeName());
                eshopQicConfig.setPlatformQualityBtypeNameBackup(eshopQicConfig.getPlatformQualityBtypeNameBackup() == null ? "" : eshopQicConfig.getPlatformQualityBtypeNameBackup());
                eshopQicConfig.setPlatformQualityComment(eshopQicConfig.getPlatformQualityComment() == null ? "" : eshopQicConfig.getPlatformQualityComment());
                eshopQicConfig.setPlatformQualityBtypeBackupProduct(eshopQicConfig.getPlatformQualityBtypeBackupProduct() == null ? "" : eshopQicConfig.getPlatformQualityBtypeBackupProduct());
                eshopQicConfig.setPlatformQualityBtypeProduct(eshopQicConfig.getPlatformQualityBtypeProduct() == null ? "" : eshopQicConfig.getPlatformQualityBtypeProduct());
                eshopQicConfig.setPlatformQualityComment(eshopQicConfig.getPlatformQualityComment() == null ? "" : eshopQicConfig.getPlatformQualityComment());
                eshopQicConfig.setPlatformQualityBtypeBackupInsureType(eshopQicConfig.getPlatformQualityBtypeBackupInsureType() == null ? "" : eshopQicConfig.getPlatformQualityBtypeBackupInsureType());
                eshopQicConfig.setPlatformQualityBtypeBackupInsureTotal(eshopQicConfig.getPlatformQualityBtypeBackupInsureTotal() == null ? BigDecimal.ZERO : eshopQicConfig.getPlatformQualityBtypeBackupInsureTotal());
                eshopQicConfig.setPlatformQualityBtypeNameBackup(eshopQicConfig.getPlatformQualityBtypeNameBackup() == null ? "" : eshopQicConfig.getPlatformQualityBtypeNameBackup());
                eshopQicConfig.setPlatformQualityBtypeCodeBackup(eshopQicConfig.getPlatformQualityBtypeCodeBackup() == null ? "" : eshopQicConfig.getPlatformQualityBtypeCodeBackup());
                eshopQicConfig.setPlatformQualityBtypeInsureType(eshopQicConfig.getPlatformQualityBtypeInsureType() == null ? "" : eshopQicConfig.getPlatformQualityBtypeInsureType());
                eshopQicConfig.setPlatformQualityBtypeInsureTotal(eshopQicConfig.getPlatformQualityBtypeInsureTotal() == null ? BigDecimal.ZERO : eshopQicConfig.getPlatformQualityBtypeInsureTotal());
                eshopQicConfig.setPlatformQualityBtypeName(eshopQicConfig.getPlatformQualityBtypeName() == null ? "" : eshopQicConfig.getPlatformQualityBtypeName());
                eshopQicConfig.setPlatformQualityBtypeCode(eshopQicConfig.getPlatformQualityBtypeCode() == null ? "" : eshopQicConfig.getPlatformQualityBtypeCode());
                eshopQicConfig.setPlatformQualityReceiveAddress(eshopQicConfig.getPlatformQualityReceiveAddress() == null ? "" : eshopQicConfig.getPlatformQualityReceiveAddress());
                eshopQicConfig.setPlatformQualityRefundInterceptionCode(eshopQicConfig.getPlatformQualityRefundInterceptionCode() == null ? "" : eshopQicConfig.getPlatformQualityRefundInterceptionCode());
                eshopQicConfig.setPlatformQualityWarehouseCode(eshopQicConfig.getPlatformQualityWarehouseCode() == null ? "" : eshopQicConfig.getPlatformQualityWarehouseCode());
                eshopQicConfig.setPlatformQualityWarehouseName(eshopQicConfig.getPlatformQualityWarehouseName() == null ? "" : eshopQicConfig.getPlatformQualityWarehouseName());
                eshopQicConfig.setPlatformQualityOrgId(eshopQicConfig.getPlatformQualityOrgId() == null ? "" : eshopQicConfig.getPlatformQualityOrgId());
                eshopQicConfig.setPlatformQualityOrgName(eshopQicConfig.getPlatformQualityOrgName() == null ? "" : eshopQicConfig.getPlatformQualityOrgName());
                eshopQicConfig.setQicAddressId(eshopQicConfig.getQicAddressId() == null ? BigInteger.ZERO : eshopQicConfig.getQicAddressId());
                eshopQicConfigMapper.insertQualityOrgByOtypeId(eshopQicConfig);
            } else {
                eshopQicConfigMapper.updateQualityOrgByOtypeId(eshopQicConfig);
            }
        }
    }
//    public void insertOrUpdateEshopQicConfigForDoudian(EShopPageInfo pageInfo) {
//        EshopQicConfig eshopQicConfig = pageInfo.getEshopBatsConfig();
//        if (eshopQicConfig.getId() == null) {
//            eshopQicConfig.setId(UId.newId());
//            eshopQicConfig.setEshopId(pageInfo.getOtypeId());
//            eshopQicConfig.setPlatformQualityBtypeName(eshopQicConfig.getPlatformQualityBtypeName() == null ? "" : eshopQicConfig.getPlatformQualityBtypeName());
//            eshopQicConfig.setPlatformQualityBtypeNameBackup(eshopQicConfig.getPlatformQualityBtypeNameBackup() == null ? "" : eshopQicConfig.getPlatformQualityBtypeNameBackup());
//            eshopQicConfig.setPlatformQualityComment(eshopQicConfig.getPlatformQualityComment() == null ? "" : eshopQicConfig.getPlatformQualityComment());
//            eshopQicConfig.setPlatformQualityBtypeBackupProduct(eshopQicConfig.getPlatformQualityBtypeBackupProduct() == null ? "" : eshopQicConfig.getPlatformQualityBtypeBackupProduct());
//            eshopQicConfig.setPlatformQualityBtypeProduct(eshopQicConfig.getPlatformQualityBtypeProduct() == null ? "" : eshopQicConfig.getPlatformQualityBtypeProduct());
//            eshopQicConfig.setPlatformQualityComment(eshopQicConfig.getPlatformQualityComment() == null ? "" : eshopQicConfig.getPlatformQualityComment());
//            eshopQicConfig.setPlatformQualityBtypeBackupInsureType(eshopQicConfig.getPlatformQualityBtypeBackupInsureType() == null ? "" : eshopQicConfig.getPlatformQualityBtypeBackupInsureType());
//            eshopQicConfig.setPlatformQualityBtypeBackupInsureTotal(eshopQicConfig.getPlatformQualityBtypeBackupInsureTotal() == null ? BigDecimal.ZERO : eshopQicConfig.getPlatformQualityBtypeBackupInsureTotal());
//            eshopQicConfig.setPlatformQualityBtypeNameBackup(eshopQicConfig.getPlatformQualityBtypeNameBackup() == null ? "" : eshopQicConfig.getPlatformQualityBtypeNameBackup());
//            eshopQicConfig.setPlatformQualityBtypeCodeBackup(eshopQicConfig.getPlatformQualityBtypeCodeBackup() == null ? "" : eshopQicConfig.getPlatformQualityBtypeCodeBackup());
//            eshopQicConfig.setPlatformQualityBtypeInsureType(eshopQicConfig.getPlatformQualityBtypeInsureType() == null ? "" : eshopQicConfig.getPlatformQualityBtypeInsureType());
//            eshopQicConfig.setPlatformQualityBtypeInsureTotal(eshopQicConfig.getPlatformQualityBtypeInsureTotal() == null ? BigDecimal.ZERO : eshopQicConfig.getPlatformQualityBtypeInsureTotal());
//            eshopQicConfig.setPlatformQualityBtypeName(eshopQicConfig.getPlatformQualityBtypeName() == null ? "" : eshopQicConfig.getPlatformQualityBtypeName());
//            eshopQicConfig.setPlatformQualityBtypeCode(eshopQicConfig.getPlatformQualityBtypeCode() == null ? "" : eshopQicConfig.getPlatformQualityBtypeCode());
//            eshopQicConfig.setPlatformQualityReceiveAddress(eshopQicConfig.getPlatformQualityReceiveAddress() == null ? "" : eshopQicConfig.getPlatformQualityReceiveAddress());
//            eshopQicConfig.setPlatformQualityRefundInterceptionCode(eshopQicConfig.getPlatformQualityRefundInterceptionCode() == null ? "" : eshopQicConfig.getPlatformQualityRefundInterceptionCode());
//            eshopQicConfig.setPlatformQualityWarehouseCode(eshopQicConfig.getPlatformQualityWarehouseCode() == null ? "" : eshopQicConfig.getPlatformQualityWarehouseCode());
//            eshopQicConfig.setPlatformQualityWarehouseName(eshopQicConfig.getPlatformQualityWarehouseName() == null ? "" : eshopQicConfig.getPlatformQualityWarehouseName());
//            eshopQicConfig.setPlatformQualityOrgId(eshopQicConfig.getPlatformQualityOrgId() == null ? "" : eshopQicConfig.getPlatformQualityOrgId());
//            eshopQicConfig.setPlatformQualityOrgName(eshopQicConfig.getPlatformQualityOrgName() == null ? "" : eshopQicConfig.getPlatformQualityOrgName());
//            eshopQicConfig.setQicAddressId(eshopQicConfig.getQicAddressId() == null ? BigInteger.ZERO : eshopQicConfig.getQicAddressId());
//            eshopQicConfig.setPlatformBatsQualityStatus(eshopQicConfig.isPlatformBatsQualityStatus());
//            eshopQicConfig.setPlatformBatsQualityDefaultInfoStatus(eshopQicConfig.isPlatformBatsQualityDefaultInfoStatus());
//            eshopQicConfig.setPlatformQualityType(true);
//            eshopQicConfigMapper.insertQualityOrgByOtypeId(eshopQicConfig);
//        } else {
//            if(null != eshopQicConfig) {
//                eshopQicConfigMapper.updateQualityOrgByOtypeId(eshopQicConfig);
//            }
//        }
//    }

    public boolean needCheckShopAccountByShoptype(ShopType eshopType) {
        String needCheckShopAccountShoptype = serviceConfig.getNeedCheckShopAccountShoptype();
        String[] allowShoptype = needCheckShopAccountShoptype.split(",");
        if (Arrays.asList(allowShoptype).contains(eshopType.getCode() + "")) {
            return true;
        }
        return false;
    }

    public void importBranchEshop(BufferedInputStream byteArrayOutputStream, ProcessLoggerImpl processLogger) {
        List dataList = new ArrayList();
        processLogger.appendMsg("开始导入子网店数据");
        try {
            platformBranchEshopListener = new PlatformBranchEshopListener(processLogger, dataList, mapper);
            InputStream inputStream = byteArrayOutputStream;
            EasyExcel.read(inputStream, PlatformBranchEshop.class, platformBranchEshopListener).sheet().doRead();
            saveBranchEshopToRedis(platformBranchEshopListener.localList, ShopType.MeiTuanMaiYao);
        } catch (Exception e) {
            processLogger.appendMsg("文件IO读取异常" + e.getMessage());
            logger.error(e.getMessage(), e);
        }
    }

    private void saveBranchEshopToRedis(List<PlatformBranchEshop> localList, ShopType meiTuanMaiYao) {
        String key = Md5Utils.md5(String.format("%s_%s_%s_%s", meiTuanMaiYao.getName(), CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), meiTuanMaiYao.getCode()));
        RedisPoolFactory redisPoolFactory = GetBeanUtil.getBean(RedisPoolFactory.class);
        StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
        template.opsForValue().set(key, JsonUtils.toJson(localList), 5, TimeUnit.MINUTES);
    }

    public List<PlatformBranchEshopResult> getMeituanBrachEshop() {
        String key = Md5Utils.md5(String.format("%s_%s_%s_%s", ShopType.MeiTuanMaiYao.getName(), CurrentUser.getProfileId(), CurrentUser.getEmployeeId(), ShopType.MeiTuanMaiYao.getCode()));
        RedisPoolFactory redisPoolFactory = GetBeanUtil.getBean(RedisPoolFactory.class);
        StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
        String branchEshop = template.opsForValue().get(key);
        List<PlatformBranchEshop> platformBranchEshopResults = JSONArray.parseArray(branchEshop, PlatformBranchEshop.class);
        List<PlatformBranchEshopResult> resultList = new ArrayList<>();
        for (PlatformBranchEshop platformBranchEshopResult : platformBranchEshopResults) {
            PlatformBranchEshopResult result = new PlatformBranchEshopResult();
            result.setEshopAccount(platformBranchEshopResult.getAppStoreId());
            result.setFullname(platformBranchEshopResult.getAppStoreName());
            result.setEnabled(true);
//            result.setOperation(String.format("<font color=\'white\' style=\' color:#2c57a0;border-radius:3px;padding:2px;padding-left:4px;padding-right:4px\'>订购</font>"));
            result.setAuth(String.format("<font color=\'white\' style=\' color:#2c57a0;border-radius:3px;padding:2px;padding-left:4px;padding-right:4px\'>授权</font>"));
            resultList.add(result);
        }
        return resultList;
    }

    public List<PlatformBranchEshopResult> getBrachEshopByGroupId(String groupId) {
        List<EshopInfo> eshopInfoByGroupId = mapper.getEshopBranchInfoByGroupId(CurrentUser.getProfileId(), groupId);
        List<PlatformBranchEshopResult> resultList = new ArrayList<>();
        for (EshopInfo eshopInfo : eshopInfoByGroupId) {
            PlatformBranchEshopResult result = new PlatformBranchEshopResult();
            result.setEshopAccount(eshopInfo.getEshopAccount());
            result.setOtypeId(eshopInfo.getOtypeId());
            result.setEnabled(!eshopInfo.isEnabled());
            result.setFullname(eshopInfo.getFullname());
            if (eshopInfo.getEshopType().equals(ShopType.EleMeRetail)) {
                result.setOperation(String.format("<font color=\'white\' style=\' color:#2c57a0;border-radius:3px;padding:2px;padding-left:4px;padding-right:4px\'>订购</font>"));
            }
            if (!eshopInfo.getEshopType().equals(ShopType.YaoFangWang)) {
                result.setAuth(String.format("<font color=\'white\' style=\' color:#2c57a0;border-radius:3px;padding:2px;padding-left:4px;padding-right:4px\'>授权</font>"));
            }
//            EshopTagInfo tagInfo = getEshopTagInfo(eshopInfo);
//            if (tagInfo.isShowOrderLink())
//                result.setOperation(String.format("<font color=\'white\' style=\' color:#2c57a0;border-radius:3px;padding:2px;padding-left:4px;padding-right:4px\'>订购</font>"));
//            if (tagInfo.isShowAuth())
//                result.setAuth(String.format("<font color=\'white\' style=\' color:#2c57a0;border-radius:3px;padding:2px;padding-left:4px;padding-right:4px\'>授权</font>"));
            resultList.add(result);
        }
        if (resultList.size() == 0) {
            PlatformBranchEshopResult result = new PlatformBranchEshopResult();
            result.setAuth(String.format("<font color=\'white\' style=\' color:#2c57a0;border-radius:3px;padding:2px;padding-left:4px;padding-right:4px\'>授权</font>"));
            resultList.add(result);
        }
        return resultList;
    }

    private EshopTagInfo getEshopTagInfo(EshopInfo eshopInfo) {
        EshopState state = new EshopState();
        state.setProductId(RouteThreadLocal.getRoute().getProductId());
        state.setMutiSelectAppkey(eshopInfo.getMutiSelectAppkey());
        state.setShopType(eshopInfo.getEshopType());
        return eshopPluginService.getEshopTag(state);
    }

    public boolean checkHasJDAuthShop() {
        try {
            QueryEShopParameter parameter = new QueryEShopParameter();
            parameter.setProfileId(CurrentUser.getProfileId());
            parameter.setPlatformTypes(Arrays.asList(ShopType.JDong.getCode(), ShopType.JdongVC.getCode(), ShopType.JdongFBP.getCode(), ShopType.JdongZGB.getCode(), ShopType.JdongGX.getCode()));
            return CollectionUtils.isNotEmpty(mapper.getEffectiveJdongEshop(parameter));
        } catch (Exception e) {
            return false;
        }
    }

    public void updateGroupEshopToken(EshopAuthInfo tokenInfo) {
        EshopInfo eshopInfoById = eshopService.getEshopInfoById(CurrentUser.getProfileId(), tokenInfo.getEshopId());
        if (eshopInfoById != null && eshopInfoById.getEshopType() == ShopType.EleMeRetail) {
            if (eshopInfoById.isMainEshop()) {
                List<EshopInfo> eshopBranchInfoByGroupId = mapper.getEshopBranchInfoByGroupId(CurrentUser.getProfileId(), eshopInfoById.getGroupId());
                EshopAuthInfo tokenInfoCopy = JsonUtils.toObject(JsonUtils.toJson(tokenInfo), EshopAuthInfo.class);
                tokenInfoCopy.setOnlineShopId(null);
                if (CollectionUtils.isNotEmpty(eshopBranchInfoByGroupId)) {
                    for (EshopInfo eshopInfo : eshopBranchInfoByGroupId) {
                        tokenInfoCopy.setEshopId(eshopInfo.getOtypeId());
                        tokenInfoCopy.setOnlineShopId(null);
                        updateEshopToken(tokenInfoCopy);
                    }
                }

            }
        }
    }

    public List<BigInteger> maeshopBranchInfoByGroupId(EshopParameter params) {
        List<EshopInfo> eshopInfoByIds = mapper.getEshopInfoByIds(CurrentUser.getProfileId(), params.getOtypeIds());
        List<EshopInfo> collect = eshopInfoByIds.stream().filter(x -> x.isMainEshop() && StringUtils.isNotBlank(x.getGroupId())).collect(Collectors.toList());
        List<EshopInfo> groupEshopInfo = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(collect)) {
            for (EshopInfo eshopInfo : collect) {
                List<EshopInfo> eshopBranchInfoByGroupId = mapper.getEshopBranchInfoByGroupId(CurrentUser.getProfileId(), eshopInfo.getGroupId());
                if (CollectionUtils.isNotEmpty(eshopBranchInfoByGroupId)) {
                    groupEshopInfo.addAll(eshopBranchInfoByGroupId);
                }
            }
        }
        return groupEshopInfo.stream().map(EshopInfo::getOtypeId).collect(Collectors.toList());
    }

    public void checkAndDeleteEShop(BigInteger profileId, BigInteger otypeId) {
        EshopInfo eshop = mapper.getEshopInfoById(profileId, otypeId);
        if (eshop != null && eshop.isMainEshop() && StringUtils.isNotEmpty(eshop.getGroupId())) {
            List<EshopInfo> eshopBranchInfoByGroupId = mapper.getEshopBranchInfoByGroupId(profileId, eshop.getGroupId());
            if (CollectionUtils.isNotEmpty(eshopBranchInfoByGroupId)) {
                EshopParameter params = new EshopParameter();
                params.setProfileId(profileId);
                params.setOtypeIds(eshopBranchInfoByGroupId.stream().map(EshopInfo::getOtypeId).collect(Collectors.toList()));
                deleteEShops(params);
                cancelAuthDoNotify(params);
                baseInfoMapper.deleteOtypes(profileId, params.getOtypeIds());
            }
        }

    }

    public boolean getEshopInfoByFullNameAndShopAccount(OnlineShopEntity en) {
        boolean isExist = mapper.getEshopInfoByFullnameAndShopAccount(CurrentUser.getProfileId(), en.getShopName(), en.getShopId());
        return isExist;
    }

    public boolean checkEshopSupportAutoShelfOnOrOff(BigInteger profileId, BigInteger otypeId, ShopType shopType) {
        boolean callBackSupport = EshopUtils.isFeatureSupported(EshopProductShelfFeature.class, shopType);
        if (!callBackSupport || !shopType.equals(ShopType.WeiMobEC)) {
            return callBackSupport;
        }
        EshopConfig eshopConfig = getEshopConfigById(profileId, otypeId);
        if (eshopConfig != null && "2".equals(eshopConfig.getPlatformEshopSnType())) {
            callBackSupport = false;
        }
        return callBackSupport;
    }

    public boolean checkEshopSupportAoXiang(ShopType curShopType) {
        List<ShopType> warehouseBizFeedbackSupportedList = PlatformCommonUtil.getWarehouseBizFeedbackSupportedList();
        if (CollectionUtils.isEmpty(warehouseBizFeedbackSupportedList)) {
            return false;
        }
        for (ShopType shopType : warehouseBizFeedbackSupportedList) {
            if (shopType.getCode() == curShopType.getCode()) {
                return true;
            }
        }
        return false;
    }

    public boolean checkEshopSupportAG(ShopType curShopType) {
        CommonRequest commonRequest = new CommonRequest();
        commonRequest.setShopType(curShopType);
        List<EshopBusinessConfig> eshopBusinessConfigList = eshopPluginService.getEshopBusinessConfigList(commonRequest);
        if (CollectionUtils.isNotEmpty(eshopBusinessConfigList)) {
            for (EshopBusinessConfig eshopBusinessConfigEntity : eshopBusinessConfigList) {
                if (EshopBusinessConfigTypeEnum.AG.equals(eshopBusinessConfigEntity.getBusinessConfigType())) {
                    return true;
                }
            }
        }
        return false;
    }

    public List<ProductRefreshSupportTypeSource> getProductDownLoadByParams(ShopType curShopType) {
        CommonRequest commonRequest = new CommonRequest();
        commonRequest.setShopType(curShopType);
        List<ProductDownloadEnum> resultList = eshopPluginService.getProductDownLoadByParams(commonRequest);
        if (CollectionUtils.isEmpty(resultList)) {
            return null;
        }
        List<ProductRefreshSupportTypeSource> productRefreshSupportTypeSourceList = new ArrayList<>();
        for (ProductDownloadEnum productDownloadEnum : resultList) {
            ProductRefreshSupportTypeSource productRefreshSupportTypeSource = new ProductRefreshSupportTypeSource();
            productRefreshSupportTypeSource.setDownloadType(productDownloadEnum.getCode());
            productRefreshSupportTypeSource.setDownloadTypeName(productDownloadEnum.getName().replace("下载", ""));
            productRefreshSupportTypeSource.setDescription(productDownloadEnum.getMessage());
            productRefreshSupportTypeSourceList.add(productRefreshSupportTypeSource);
        }
        return productRefreshSupportTypeSourceList;
    }


    public List<String> getSupportCreateInitStockShopType() {
        String allowEshopProductUpDown = serviceConfig.getSupportCreateInitStockShopType();
        String[] split = allowEshopProductUpDown.split(",");
        return Arrays.asList(split);
    }

    public boolean getShopTypeSupportDownloadSkuMemo(ShopType curShopType) {
        PlatformBaseConfig config = EshopFactoryManager.getConfig(curShopType);
        return config != null && config.isSupportDownloadSkuMemo();
    }


    public StateSubsidiesEntity getStateSupport() {
        StateSubsidiesEntity response = new StateSubsidiesEntity();
        try {
            List<ShopType> guobuSupport = new ArrayList<>();
            QueryEShopParameter parameter = new QueryEShopParameter();
            parameter.setProfileId(CurrentUser.getProfileId());
            List<Integer> ocategorys = getOcategorys(GlobalConfig.get(SysOtypeConfig.class).getOcategorys());
            CommonUtil.initLimited(parameter);
            List<Otype> orgList = mapper.getOtypeList(parameter, ocategorys);
            if (CollectionUtils.isEmpty(orgList)) {
                return response;
            }
            for (Otype o : orgList) {
                boolean isAuth = checkAuthType(o.getProfileId(), ShopType.valueOf(o.getEshopType()), o.getId());
                if (!isAuth) {
                    continue;
                }
                if (guobuSupport.contains(o.getShopType())) {
                    continue;
                }
                guobuSupport.add(o.getShopType());
            }
            for (ShopType shopType : guobuSupport) {
                if (shopType.equals(ShopType.TaoBao) || shopType.equals(ShopType.Tmall) || shopType.equals(ShopType.TaoBaoFenxiao) || shopType.equals(ShopType.TmallSuperMarket) || shopType.equals(ShopType.TmallInter))
                    response.setVisTaoBao(true);
                if (shopType.equals(ShopType.JDong) || shopType.equals(ShopType.JdongVC) || shopType.equals(ShopType.JdongGX))
                    response.setVisJinDong(true);
                if (shopType.equals(ShopType.Doudian) || shopType.equals(ShopType.DouDianInstantShopping) || shopType.equals(ShopType.DouDianSupermarket))
                    response.setVisDouDian(true);
                if (shopType.equals(ShopType.PinDuoDuo)) response.setVisPinduoduo(true);
                if (shopType.equals(ShopType.DeWu)) response.setVisDeWu(true);
                if (shopType.equals(ShopType.VipMp) || shopType.equals(ShopType.VipJitX)) response.setVisVip(true);
            }
            SysOtypeConfig otypeConfig = GlobalConfig.get(SysOtypeConfig.class);
            response.setVipGuoBuCHecked(otypeConfig.getVipStateSubsidiesEnabled().equals("1") || otypeConfig.getVipStateSubsidiesEnabled().equals("true"));
        } catch (RuntimeException ex) {
            logger.error(String.format("profileid:%s getStateSupportError:%s", CurrentUser.getProfileId(), ex.getMessage()), ex);
        }
        return response;
    }

    public void saveStateSupport(StateSubsidiesEntity request) {
        SysDataMapper sysMapper = GetBeanUtil.getBean(SysDataMapper.class);
        SysDataEntity sysData = new SysDataEntity();
        sysData.setId(UId.newId());
        sysData.setProfileId(CurrentUser.getProfileId());
        sysData.setSubName("eshoporderVipStateSubsidiesEnabled");
        sysData.setSubValue(request.isVipGuoBuCHecked() ? "1" : "0");
        sysData.setDescription("唯品会支持国补业务");
        sysMapper.putRange(Arrays.asList(sysData));
    }

    public Atype getEshopAtypeInfo(String otypeId) {
        try {
            if (StringUtils.isEmpty(otypeId))
            {
                return null;
            }
            return mapper.queryEshopAtypeInfo(CurrentUser.getProfileId(), new BigInteger(otypeId));
        }catch(RuntimeException ex)
        {
            logger.error(String.format("getEshopAtypeId Error:%s  otypeId:%s profileid:%s",ex.getMessage(),otypeId,CurrentUser.getProfileId()),ex);
            throw new RuntimeException("获取网店默认收款账户报错：" + ex.getMessage(), ex);
        }
     }

    public boolean isAllowShowEshopClass() {
        String allowShowEshopClass = GlobalConfig.get("sys.func.eshop.group.enabled");
        return StringUtils.isNotEmpty(allowShowEshopClass) && "true".equals(allowShowEshopClass.trim());
    }

    public boolean isOldVersion() {
        final String VERSION_1 = "1";
        String oldVersion = GlobalConfig.get("oldVersion");
        return StringUtils.isEmpty(oldVersion) || (VERSION_1.equals(oldVersion.trim()));
    }

    public boolean checkSubscribe(QueryEShopParameter parameter) {
        try {
            String supportCheckSubscribeShopTypes = serviceConfig.getSupportCheckSubscribeShopTypes();
            if (StringUtils.isEmpty(supportCheckSubscribeShopTypes)) {
                return false;
            }
            String[] supportShopTypes = supportCheckSubscribeShopTypes.split( ",");
            boolean support = Arrays.stream(supportShopTypes).anyMatch(x->x.equals(String.valueOf(parameter.getShopType().getCode())));
            if(!support){
                return false;
            }
            CheckSubscribesRequest checkSubscribesRequest = new CheckSubscribesRequest();
            checkSubscribesRequest.setShopAccount(parameter.getEshopAccount());
            checkSubscribesRequest.setShopType(parameter.getShopType());
            return bifrostEshopAuthService.checkSubscribes(checkSubscribesRequest);
        } catch (Exception ex) {
            String errMsg = ex.getMessage() == null ? "null" : ex.getMessage();
            logger.error("profileId:{} 检查订购状态失败=》 checkSubscribe Error:{}", CurrentUser.getProfileId(), errMsg, ex);
        }
        return false;
    }

    private List<PlatformEshoptypeSupport> getPlatformTypeFilesByRedis(String cacheKey){
        return RedisUtils.getDataListFromRedis(cacheKey, PlatformEshoptypeSupport.class);
    }

    private void writePlatformTypeFilesToRedis(String cacheKey, List<PlatformEshoptypeSupport> platformEshoptypeSupports){
        RedisUtils.writeInfoToRedis(cacheKey, JsonUtils.toJson(platformEshoptypeSupports),10);
    }
}

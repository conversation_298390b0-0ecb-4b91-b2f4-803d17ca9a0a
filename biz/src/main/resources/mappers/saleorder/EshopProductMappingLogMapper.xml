<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopProductMappingLogMapper">
    <insert id="insertMappingLog">
        insert  into pl_eshop_product_mapping_log
        values (
        #{id},
        #{profileId},
        #{eshopId},
        #{etypeId},
        #{opreateTime},
        #{opreateType},
        #{fullname},
        #{platformNumId},
        #{platformSkuId},
        #{platformFullPropertiesName},
        #{platformXcode},
        #{ptypeFullname},
        #{xcode},
        #{oldPcategory},
        #{skuId},
        #{unitId},
        #{currentPcategory},
        #{description},
        #{createTime},
        #{updateTime}
        )
    </insert>
    <select id="getProductSkuMappingLog"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMappingLogPage">

        select pl.*,
        etype.fullname as etypeName from pl_eshop_product_mapping_log pl
        left join base_etype etype on etype.id=pl.etype_id and etype.profile_id=pl.profile_id
        <where>
           pl.platform_num_id=#{platformNumId} and pl.profile_id=#{profileId} and pl.eshop_id=#{eshopId}
        </where>
        <if test="filterStr!=null and filterStr!=''">
            <if test="filterMode='quickQuery'">
                and pl.ptype_fullname like "%"#{filterStr}"%" or pl.platform_full_properties_name like "%"#{filterStr}"%" or pl.xcode like "%"#{filterStr}"%"
                or pl.fullname like "%"#{filterStr}"%"
            </if>
            <if test="filterMode='platformName'">
                and pl.fullname like "%"#{filterStr}"%"
            </if>
            <if test="filterMode='fullPropertiesName'">
                and pl.platform_full_properties_name like "%"#{filterStr}"%"
            </if>
            <if test="filterMode='xcode'">
                and pl.platform_xcode like "%"#{filterStr}"%"
            </if>
            <if test="filterMode='userCode'">
                and pl.xcode like "%"#{filterStr}"%"
            </if>
            <if test="filterMode='fullName'">
                and pl.ptype_fullname like "%"#{filterStr}"%"
            </if>
        </if>

        order by opreate_time desc


    </select>
    <select id="getProductSkuMappingList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select psm.*,(case when (p.deleted =1 or p.stoped =1) then false else true end)
        as isbind,pm.platform_fullname as fullName,p.fullname as ptypeName,expand.mapping_type,
        mapping.ptype_id,ifnull(unit.unit_rate,1) as unitRate, bpx.xcode,baseunit.id as baseUnitId
        from pl_eshop_product_sku psm
        left join pl_eshop_product_sku_expand expand on expand.unique_id = psm.unique_id and expand.profile_id= psm.profile_id and expand.eshop_id = psm.eshop_id
        left join pl_eshop_product_sku_mapping mapping on psm.unique_id = mapping.unique_id and psm.profile_id= mapping.profile_id and mapping.eshop_id = psm.eshop_id
        left join pl_eshop_product pm on pm.profile_id=psm.profile_id and pm.eshop_id=psm.eshop_id and pm.platform_num_id=psm.platform_num_id
        left join base_ptype_unit unit on unit.id=mapping.unit_id and unit.profile_id=psm.profile_id and unit.unit_code=1
        left join base_ptype_price baseunit on baseunit.profile_id=psm.profile_id and mapping.ptype_id=baseunit.ptype_id
        left join base_ptype_xcode bpx on psm.profile_id = bpx.profile_id and mapping.unit_id=bpx.unit_id and mapping.sku_id=bpx.sku_id and bpx.defaulted=1
        left join base_ptype p on p.profile_id=psm.profile_id and p.id=mapping.ptype_id
        where psm.profile_id=#{profileId}
        <if test="eshopId!=null">
            and psm.eshop_id=#{eshopId}
        </if>
        <if test="platformNumId!=null and platformNumId!=''">
            and psm.platform_num_id=#{platformNumId}
        </if>
        <if test="platformSkuId!=null and platformSkuId!=''">
            and psm.platform_sku_id=#{platformSkuId}
        </if>
    </select>
    <select id="getProductSkuMappingListByPtype"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">

        select psm.*,(case when ( IFNULL(bpx.id,0) =0 or  p.deleted =1 or p.stoped =1) then false else true end)
        as isbind,pm.fullName,p.fullname as ptypeName,
        pm.ptype_id,ifnull(unit.unit_rate,1) as unitRate, bpx.xcode,baseunit.id as baseUnitId
        from pl_eshop_product_sku_mapping psm
        left join pl_eshop_product_mapping pm on pm.profile_id=psm.profile_id and pm.eshop_id=psm.eshop_id and pm.platform_num_id=psm.platform_num_id
        left join base_ptype_unit unit on unit.id=psm.unit_id and unit.profile_id=psm.profile_id
        left join base_ptype_unit baseunit on baseunit.profile_id=psm.profile_id and pm.ptype_id=baseunit.ptype_id and baseunit.unit_code=1
        left join base_ptype_xcode bpx on psm.profile_id = bpx.profile_id and psm.unit_id=bpx.unit_id and psm.sku_id=bpx.sku_id and bpx.defaulted=1
        left join base_ptype p on p.profile_id=psm.profile_id and p.id=psm.ptype_id
        where psm.profile_id=#{profileId}
        <if test="ptypeId!=null ">
            and psm.pptypeId=#{ptypeId}
        </if>
        <if test="skuId!=null">
            and psm.platform_sku_id=#{skuId}
        </if>
    </select>
    <select id="getEshopSkuMappingListOpenXcode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping">
        select psm.*,(case when ( p.deleted =1 or p.stoped =1) then false else true end)
        as isbind,pm.fullName,p.fullname as ptypeName,pm.ptype_id,bpx.xcode,'' as usercode
        from pl_eshop_product_sku_mapping psm
        left join pl_eshop_product_mapping pm on pm.profile_id=psm.profile_id and pm.eshop_id=psm.eshop_id and pm.platform_num_id=psm.platform_num_id
        left join base_ptype_unit unit on unit.id=psm.unit_id and unit.profile_id=psm.profile_id
        left join base_ptype_xcode bpx on psm.profile_id = bpx.profile_id and psm.platform_xcode=bpx.xcode  and bpx.defaulted=1
        left join base_ptype p on p.profile_id=psm.profile_id and p.id=psm.ptype_id
        left join base_ptype combo on psm.profile_id=combo.profile_id and psm.platform_xcode=combo.usercode
        where psm.profile_id=#{profileId}
        <if test="eshopIds!=null ">
            <foreach collection="eshopIds" item="eshopId" separator="," index="i" close=")" open="(">
                and psm.eshop_id=#{eshopid}
            </foreach>
        </if>
    </select>
    <select id="getProductSkuMappingLogByParam"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMappingLogPage">
        select pl.*,
        etype.fullname as etypeName from pl_eshop_product_mapping_log pl
        left join base_etype etype on etype.id=pl.etype_id and etype.profile_id=pl.profile_id
        <where>
            pl.platform_num_id=#{platformNumId} and pl.profile_id=#{profileId} and pl.eshop_id=#{eshopId}
        </where>
        <if test="filterStr!=null and filterStr!=''">
            <if test="filterMode='quickQuery'">
                and pl.ptype_fullname like "%"#{filterStr}"%" or pl.platform_full_properties_name like "%"#{filterStr}"%" or pl.xcode like "%"#{filterStr}"%"
                or pl.fullname like "%"#{filterStr}"%"
            </if>


        </if>
        <if test="beginTime!=null">
            <![CDATA[ and pl.opreate_time >= #{beginTime}  ]]>
        </if>
        <if test="endTime!=null">
            <![CDATA[  and pl.opreate_time <= #{endTime}  ]]>

        </if>
        <if test="etypeName!=null">
            and etype.fullname like "%"#{etypeName}"%"
        </if>
        <if test="opreateType!=null">
            and pl.opreate_type=#{opreateType}
        </if>
        <if test="fullname!=null">
            and pl.fullname like "%"#{fullname}"%"
        </if>
        <if test="platformFullPropertiesName!=null">
            and pl.platform_full_properties_name like "%"#{platformFullPropertiesName}"%"
        </if>
        <if test="platformXcode!=null">
            and pl.platform_xcode like "%"#{platformXcode}"%"
        </if>
        <if test="ptypeFullname!=null">
            and pl.ptype_fullname like "%"#{ptypeFullname}"%"
        </if>
        <if test="xcode!=null">
            and pl.xcode like "%"#{xcode}"%"
        </if>
        <if test="currentPcategory!=null">
            and pl.current_pcategory =#{currentPcategory}
        </if>

        order by opreate_time desc
    </select>
</mapper>
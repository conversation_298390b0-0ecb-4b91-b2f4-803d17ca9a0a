package com.wsgjp.ct.sale.biz.eshoporder.entity.product;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.MappingType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.Pcategory;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleTargetTypeEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncManagePageData;
import com.wsgjp.ct.sale.platform.dto.product.MultiTimeStock;
import com.wsgjp.ct.sale.platform.dto.product.WareHouseStockSync;
import com.wsgjp.ct.sale.platform.enums.StockState;
import lombok.Getter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Arrays;
import java.util.List;


/**
 * <AUTHOR>
 */
public class EshopProductSkuPageData implements Serializable {
    private BigInteger eshopProductSkuId;
    private Boolean selected = false;
    /**
     * 界面的rowIndex,用于快速修改grid的行数据，后端业务不要赋值
     */
    private int index;
    private BigInteger profileId;
    private BigInteger otypeId;
    private String eshopName;
    private ShopType eshopType;
    private String platformNumId;
    private String platformSkuId;
    private String platformXcode;
    private String platformPicUrl;
    private String platformFullname;
    private BigDecimal platformPrice;
    private String platformPropertiesName;
    private String platformFullProperties;
    private String platformProperties;
    private String warehouseCode;
    private String ptypeName;
    private String xcode;
    private String unitName;
    private BigInteger unitId;
    private BigInteger skuId;
    private BigInteger ptypeId;
    private String localPicUrl;

    private String brandName;

    private String standard;
    private BigDecimal costPrice;
    private String ptypeType;
    private String propNames;
    private String propValueNames;
    private int mappingType;
    private String operation;
    private String mark;
    private List<EshopProductMark> markList;
    private BigDecimal qty;
    private BigDecimal saleQty;
    private BigDecimal oldQty;
    private Boolean autoSyncEnabled;

    private BigInteger syncRuleId;

    private Boolean warehouseStockSyncEnabled;

    private Boolean eshopMultiStockSyncEnabled;

    private boolean hasProperties;

    private String oldPlatXCode;

    private String pmXcode;
    private String uniqueId;

    private Pcategory pcategory;

    private String ruleName;

    private Boolean qtyHasModified = false;

    private List<MultiTimeStock> multiTimeStocks;

    private List<WareHouseStockSync> wareHouseStocks;
    private StockState platformStockState;
    private String downloadOrderIntervalDay;
    private String refreshProductIntervalDay;
    private String platformUnitName;
    private String skuBarcode;
    @Getter
    private String platformJson;
    private String formula;
    private String ktypeNameList;

    public BigInteger getProfileId() {
        return profileId;
    }

    public void setProfileId(BigInteger profileId) {
        this.profileId = profileId;
    }

    public BigInteger getOtypeId() {
        return otypeId;
    }

    public void setOtypeId(BigInteger otypeId) {
        this.otypeId = otypeId;
    }

    public String getPlatformNumId() {
        return platformNumId;
    }

    public void setPlatformNumId(String platformNumId) {
        this.platformNumId = platformNumId;
    }

    public String getPlatformSkuId() {
        return platformSkuId;
    }

    public void setPlatformSkuId(String platformSkuId) {
        this.platformSkuId = platformSkuId;
    }

    public String getPlatformXcode() {
        return platformXcode;
    }

    public void setPlatformXcode(String platformXcode) {
        this.platformXcode = platformXcode;
    }

    public String getPlatformFullname() {
        return platformFullname;
    }

    public void setPlatformFullname(String platformFullname) {
        this.platformFullname = platformFullname;
    }

    public String getPlatformPropertiesName() {
        return platformPropertiesName;
    }

    public void setPlatformPropertiesName(String platformPropertiesName) {
        this.platformPropertiesName = platformPropertiesName;
    }

    public String getPlatformProperties() {
        return platformProperties;
    }

    public void setPlatformProperties(String platformProperties) {
        this.platformProperties = platformProperties;
    }

    public String getWarehouseCode() {
        return warehouseCode;
    }

    public void setWarehouseCode(String warehouseCode) {
        this.warehouseCode = warehouseCode;
    }

    public String getEshopName() {
        return eshopName;
    }

    public void setEshopName(String eshopName) {
        this.eshopName = eshopName;
    }

    public Boolean getSelected() {
        return selected;
    }

    public void setSelected(Boolean selected) {
        this.selected = selected;
    }

    public String getPlatformPicUrl() {
        return platformPicUrl;
    }

    public void setPlatformPicUrl(String platformPicUrl) {
        this.platformPicUrl = platformPicUrl;
    }

    public String getPtypeName() {
        return ptypeName;
    }

    public void setPtypeName(String ptypeName) {
        this.ptypeName = ptypeName;
    }

    public String getXcode() {
        return xcode;
    }

    public void setXcode(String xcode) {
        this.xcode = xcode;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public BigInteger getUnitId() {
        return unitId;
    }

    public void setUnitId(BigInteger unitId) {
        this.unitId = unitId;
    }

    public BigInteger getSkuId() {
        return skuId;
    }

    public void setSkuId(BigInteger skuId) {
        this.skuId = skuId;
    }

    public BigInteger getPtypeId() {
        if (ptypeId == null) {
            ptypeId = BigInteger.ZERO;
        }
        return ptypeId;
    }

    public void setPtypeId(BigInteger ptypeId) {
        this.ptypeId = ptypeId;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public BigDecimal getPlatformPrice() {
        return platformPrice;
    }

    public void setPlatformPrice(BigDecimal platformPrice) {
        this.platformPrice = platformPrice;
    }

    public BigDecimal getQty() {
        return qty;
    }

    public void setQty(BigDecimal qty) {
        this.qty = qty;
    }

    public BigDecimal getSaleQty() {
        return saleQty;
    }

    public void setSaleQty(BigDecimal saleQty) {
        this.saleQty = saleQty;
    }

    public Boolean getAutoSyncEnabled() {
        return autoSyncEnabled;
    }

    public void setAutoSyncEnabled(Boolean autoSyncEnabled) {
        this.autoSyncEnabled = autoSyncEnabled;
    }

    public BigInteger getSyncRuleId() {
        return syncRuleId;
    }

    public void setSyncRuleId(BigInteger syncRuleId) {
        this.syncRuleId = syncRuleId;
    }

    public Boolean getWarehouseStockSyncEnabled() {
        return warehouseStockSyncEnabled;
    }

    public void setWarehouseStockSyncEnabled(Boolean warehouseStockSyncEnabled) {
        this.warehouseStockSyncEnabled = warehouseStockSyncEnabled;
    }

    public Boolean getEshopMultiStockSyncEnabled() {
        return eshopMultiStockSyncEnabled;
    }

    public void setEshopMultiStockSyncEnabled(Boolean eshopMultiStockSyncEnabled) {
        this.eshopMultiStockSyncEnabled = eshopMultiStockSyncEnabled;
    }

    public int getMappingType() {
        return mappingType;
    }

    public void setMappingType(int mappingType) {
        this.mappingType = mappingType;
    }

    public String getPropValueNames() {
        return propValueNames;
    }

    public void setPropValueNames(String propValueNames) {
        this.propValueNames = propValueNames;
    }

    public String getPropNames() {
        return propNames;
    }

    public void setPropNames(String propNames) {
        this.propNames = propNames;
    }

    public String getLocalPicUrl() {
        return localPicUrl;
    }

    public void setLocalPicUrl(String localPicUrl) {
        this.localPicUrl = localPicUrl;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getStandard() {
        return standard;
    }

    public void setStandard(String standard) {
        this.standard = standard;
    }

    public String getPtypeType() {
        return ptypeType;
    }

    public void setPtypeType(String ptypeType) {
        this.ptypeType = ptypeType;
    }

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public boolean isHasProperties() {
        return hasProperties;
    }

    public void setHasProperties(boolean hasProperties) {
        this.hasProperties = hasProperties;
    }

    public String getOldPlatXCode() {
        return oldPlatXCode;
    }

    public void setOldPlatXCode(String oldPlatXCode) {
        this.oldPlatXCode = oldPlatXCode;
    }

    public boolean isBind() {
        return getPtypeId().compareTo(BigInteger.ZERO) > 0;
    }

    public String getPmXcode() {
        return pmXcode;
    }

    public void setPmXcode(String pmXcode) {
        this.pmXcode = pmXcode;
    }

    public String getUniqueId() {
        return uniqueId;
    }

    public void setUniqueId(String uniqueId) {
        this.uniqueId = uniqueId;
    }

    public String getPlatformFullProperties() {
        return platformFullProperties;
    }

    public void setPlatformFullProperties(String platformFullProperties) {
        this.platformFullProperties = platformFullProperties;
    }

    public Pcategory getPcategory() {
        return pcategory;
    }

    public void setPcategory(Pcategory pcategory) {
        this.pcategory = pcategory;
    }

    public ShopType getEshopType() {
        return eshopType;
    }

    public void setEshopType(ShopType eshopType) {
        this.eshopType = eshopType;
    }

    public String getEshopTypeName() {
        return getEshopType().getName();
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public Boolean getQtyHasModified() {
        return qtyHasModified;
    }

    public void setQtyHasModified(Boolean qtyHasModified) {
        this.qtyHasModified = qtyHasModified;
    }

    public String getMark() {
        return mark;
    }

    public void setMark(String mark) {
        this.mark = mark;
    }

    public List<EshopProductMark> getMarkList() {
        return markList;
    }

    public void setMarkList(List<EshopProductMark> markList) {
        this.markList = markList;
    }


    public List<MultiTimeStock> getMultiTimeStocks() {
        return multiTimeStocks;
    }

    public void setMultiTimeStocks(List<MultiTimeStock> multiTimeStocks) {
        this.multiTimeStocks = multiTimeStocks;
    }

    public List<WareHouseStockSync> getWareHouseStocks() {
        return wareHouseStocks;
    }

    public void setWareHouseStocks(List<WareHouseStockSync> wareHouseStocks) {
        this.wareHouseStocks = wareHouseStocks;
    }

    public void setPlatformJson(String platformJson) {
        this.platformJson = platformJson;
    }

    public StockState getPlatformStockState() {
        return platformStockState;
    }

    public void setPlatformStockState(StockState platformStockState) {
        this.platformStockState = platformStockState;
    }

    public String getDownloadOrderIntervalDay() {
        return downloadOrderIntervalDay;
    }

    public void setDownloadOrderIntervalDay(String downloadOrderIntervalDay) {
        this.downloadOrderIntervalDay = downloadOrderIntervalDay;
    }

    public String getRefreshProductIntervalDay() {
        return refreshProductIntervalDay;
    }

    public void setRefreshProductIntervalDay(String refreshProductIntervalDay) {
        this.refreshProductIntervalDay = refreshProductIntervalDay;
    }

    public String getPlatformUnitName() {
        return platformUnitName;
    }

    public void setPlatformUnitName(String platformUnitName) {
        this.platformUnitName = platformUnitName;
    }

    public String getPlatformJson() {
        return platformJson;
    }

    public EshopProductSkuMapping toSkuMapping() {
        EshopProductSkuMapping skuMapping = new EshopProductSkuMapping();
        skuMapping.setUniqueId(this.getUniqueId());
        skuMapping.setBind(this.isBind());
        skuMapping.setXcode(this.getXcode());
        skuMapping.setSkuId(this.getSkuId());
        skuMapping.setPlatformXcode(this.getPlatformXcode());
        skuMapping.setPlatformNumId(this.getPlatformNumId());
        skuMapping.setPlatformSkuId(this.getPlatformSkuId());
        skuMapping.setPtypeId(this.getPtypeId());
        skuMapping.setPtypeName(this.getPtypeName());
        skuMapping.setUnitId(this.getUnitId());
        skuMapping.setEshopId(this.getOtypeId());
        skuMapping.setProfileId(this.getProfileId());
        skuMapping.setPlatformPropertiesName(this.getPlatformPropertiesName());
        skuMapping.setPlatformFullPropertiesName(this.getPlatformFullProperties());
        skuMapping.setPlatfullname(this.getPlatformFullname());
        skuMapping.setPcategory(this.getPcategory());
        skuMapping.setPropValues(this.getPropValueNames());
        return skuMapping;
    }

    public EshopProductSkuExpand toSkuExpand() {
        EshopProductSkuExpand expand = new EshopProductSkuExpand();
        expand.setMappingType(MappingType.NOMARL);
        expand.setUniqueId(this.getUniqueId());
        expand.setProfileId(this.getProfileId());
        expand.setEshopId(this.getOtypeId());
        return expand;
    }

    public StockSyncManagePageData toStockSyncManagePageData() {
        StockSyncManagePageData stockSyncManagePageData = new StockSyncManagePageData();
        stockSyncManagePageData.setPlatformNumId(this.getPlatformNumId());
        stockSyncManagePageData.setPlatformSkuId(this.getPlatformSkuId());
        stockSyncManagePageData.setPlatformXcode(this.getPlatformXcode());
        stockSyncManagePageData.setEshopId(this.getOtypeId());
        stockSyncManagePageData.setTargetType(StockRuleTargetTypeEnum.NORMAL);
        stockSyncManagePageData.setWarehouseCode(this.getWarehouseCode());
        stockSyncManagePageData.setSyncQty(this.getQty().toString());
        stockSyncManagePageData.setSaleQty(this.getSaleQty().toString());
        stockSyncManagePageData.setMainProduct(true);
        stockSyncManagePageData.setHasProperties(false);
        stockSyncManagePageData.setUniqueId(this.getUniqueId());
        stockSyncManagePageData.setPtypeId(this.getPtypeId());
        stockSyncManagePageData.setSkuId(this.getSkuId());
        stockSyncManagePageData.setUnitId(this.getUnitId());
        stockSyncManagePageData.setXcode(this.getXcode());
        stockSyncManagePageData.setRuleId(this.getSyncRuleId());
        stockSyncManagePageData.setFormula(this.getFormula());
        stockSyncManagePageData.setKtypeNameList(this.getKtypeNameList());
        stockSyncManagePageData.setMappingType(Arrays.stream(MappingType.values()).filter(x->x.getCode() == this.getMappingType()).findFirst().orElse(MappingType.NOMARL));
        return stockSyncManagePageData;
    }

    public String getSkuBarcode() {
        return skuBarcode;
    }

    public void setSkuBarcode(String skuBarcode) {
        this.skuBarcode = skuBarcode;
    }

    public String getFormula() {
        return formula;
    }

    public void setFormula(String formula) {
        this.formula = formula;
    }

    public String getKtypeNameList() {
        return ktypeNameList;
    }

    public void setKtypeNameList(String ktypeNameList) {
        this.ktypeNameList = ktypeNameList;
    }

    public BigInteger getEshopProductSkuId() {
        return eshopProductSkuId;
    }

    public void setEshopProductSkuId(BigInteger eshopProductSkuId) {
        this.eshopProductSkuId = eshopProductSkuId;
    }

    public BigDecimal getOldQty() {
        return oldQty;
    }

    public void setOldQty(BigDecimal oldQty) {
        this.oldQty = oldQty;
    }
}

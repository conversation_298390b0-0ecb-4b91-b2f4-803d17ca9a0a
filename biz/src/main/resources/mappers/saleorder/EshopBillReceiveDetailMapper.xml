<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopBillReceiveDetailMapper">
    <insert id="addBillReceiveDetail">
        insert into pl_eshop_bill_receive_detail
        (id,
         profile_id,
         otype_id,
         vchcode,
         check_time,
         check_status,
         payway_id,
         need_payment,
         has_payment,
         check_diff,
         check_etype_id,
         bill_account_id)
        values
            (#{id},#{profileId},#{otypeId},#{vchcode},#{checkTime},
             #{checkStatus},
             #{paywayId},
             #{needPayment},
             #{hasPayment},
             #{checkDiff},
             #{checkEtypeId},
             #{billAccountId}
            )
    </insert>

    <delete id="deleteBillReceiveDetail">
        delete from pl_eshop_bill_receive_detail where profile_id=#{profileId} and vchcode =#{vchcode}
        <if test="billAccountId !=null">
            and bill_account_id =#{billAccountId}
        </if>
        <if test="needPayment !=null">
            and need_payment =#{needPayment}
        </if>
    </delete>

    <select id="queryBillReceiveDetail"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.BillReceiveDetailEntity">
        select id,
        profile_id,
        otype_id,
        vchcode,
        check_time,
        check_status,
        payway_id,
        need_payment,
        has_payment,
        check_diff,
        bill_account_id,
        check_etype_id from pl_eshop_bill_receive_detail where profile_id=#{profileId}
        <if test="vchcode!=null">
            and vchcode =#{vchcode}
        </if>
        <if test="vchcodes!=null and vchcodes.size()>0">
            and vchcode in
            <foreach collection="vchcodes" item="vchcode"  index="index" open="(" separator="," close=")" >
                #{vchcode}
            </foreach>
        </if>
        <if test="billAccountIds!=null and billAccountIds.size()>0">
            and bill_account_id in
            <foreach collection="billAccountIds" item="billAccountId"  index="index" open="(" separator="," close=")" >
                #{billAccountId}
            </foreach>
        </if>
        <if test="billAccountId!=null">
            and bill_account_id =#{billAccountId}
        </if>
    </select>

    <select id="queryBillReceiveDetailByVchcodes"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.BillReceiveDetailEntity">
        select
        d.id,
        d.profile_id,
        d.otype_id,
        d.vchcode,
        d.check_time,
        d.check_status,
        d.payway_id,
        d.need_payment,
        d.has_payment,
        d.check_diff,
        d.bill_account_id,
        d.check_etype_id,
        r.flow_id
        from pl_eshop_bill_receive_detail d
        left join pl_eshop_flow_bill_relation r on r.vchcode=d.vchcode and r.profile_id=d.profile_id and r.bill_account_id = d.bill_account_id
        where d.profile_id=#{profileId}
        <if test="vchcodes!=null and vchcodes.size()>0">
            and d.vchcode in
            <foreach collection="vchcodes" item="vchcode" index="index" open="(" separator="," close=")">
                #{vchcode}
            </foreach>
        </if>
        <if test="billAccountId!=null">
            and d.bill_account_id =#{billAccountId}
        </if>
    </select>

    <select id="queryBillReceiveDetailByVchcodesAndAccountId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.BillReceiveDetailEntity">
        select id,
        profile_id,
        otype_id,
        vchcode,
        check_time,
        check_status,
        payway_id,
        need_payment,
        has_payment,
        check_diff,
        bill_account_id,
        check_etype_id from pl_eshop_bill_receive_detail where profile_id=#{profileId}
        <if test="vchcodes!=null and vchcodes.size()>0">
             and vchcode in
            <foreach collection="vchcodes" item="vchcode" index="index" open="(" separator="," close=")">
               #{vchcode}
            </foreach>
        </if>
        <if test="billAccountIds!=null">
            and bill_account_id in
            <foreach collection="billAccountIds" item="billAccountId" index="index" open="(" separator="," close=")">
                #{billAccountId}
            </foreach>
        </if>
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopFlowBillRelationMapper">
    <insert id="addFlowBillRelation">
        REPLACE INTO pl_eshop_flow_bill_relation
        (id,profile_id,flow_id,vchcode,vchtype,btype_id,payway_id,unique_id,bill_account_id)
        values
            (#{id},#{profileId},#{flowId},#{vchcode},#{vchtype},
             #{btypeId},
             #{paywayId},#{uniqueId},#{billAccountId}
            )
    </insert>

    <delete id="deleteFlowBillRelation">
        delete from pl_eshop_flow_bill_relation where profile_id=#{profileId} and vchcode =#{vchcode}
        <if test="flowId != null">
            and flow_id = #{flowId}
        </if>
    </delete>

    <select id="queryFlowBillRelation"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.EshopFlowBillRelation">
        select rel.id,rel.profile_id,rel.flow_id,rel.vchcode,rel.vchtype,rel.btype_id,rel.payway_id,ifnull(abc.bill_total,tbc.bill_total) as billToltal,ifnull(abc.post_state,tbc.post_state) as postState,rel.bill_account_id
        from pl_eshop_flow_bill_relation rel
        left join acc_bill_core abc on abc.profile_id=rel.profile_id and abc.vchcode=rel.vchcode
        left join td_bill_core tbc on tbc.profile_id=rel.profile_id and tbc.vchcode=rel.vchcode
        where rel.profile_id=#{profileId}
        <if test="vchcode!=null">
            and rel.vchcode =#{vchcode}
        </if>
        <if test="flowId != null">
            and rel.flow_id = #{flowId}
        </if>
    </select>

    <select id="queryFlowMatchStatus"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.EshopPaymentFlowEntity">
        select id,flow_match_status,entry_state from pl_eshop_payment_flow where
        profile_id=#{profileId}
        <if test="flows!=null and flows.size()>0">
            and id in
            <foreach collection="flows" item="flow" index="index" open="(" close=")" separator=",">
                #{flow}
            </foreach>
        </if>
    </select>
    <select id="queryFlowIdByVchcode" resultType="java.math.BigInteger">
        select rel.flow_id
        from pl_eshop_flow_bill_relation rel
        where rel.profile_id=#{profileId}
        and rel.vchcode in
            <foreach collection="vchcodes" item="vchcode" index="index" open="(" close=")" separator=",">
                #{vchcode}
            </foreach>
    </select>
    <select id="queryFrelationByFlowId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.EshopFlowBillRelation">
        select rel.id,rel.profile_id,rel.flow_id,rel.vchcode,rel.vchtype,rel.btype_id,rel.payway_id,rel.bill_account_id
        from pl_eshop_flow_bill_relation rel
        where rel.profile_id=#{profileId}
        and rel.flow_id in
        <foreach collection="flowIds" item="flowId" index="index" open="(" close=")" separator=",">
            #{flowId}
        </foreach>

    </select>

    <update id="updateFlowBillRelationBillAccountId">
        update pl_eshop_flow_bill_relation set bill_account_id=#{billAccountId} where profile_id=#{profileId} and vchcode=#{vchcode} and flow_id=#{flowId}
    </update>

    <update id="updatereceiveDetailBillAccountId">
        update pl_eshop_bill_receive_detail set bill_account_id=#{billAccountId} where profile_id=#{profileId} and vchcode=#{vchcode}
    </update>
</mapper>
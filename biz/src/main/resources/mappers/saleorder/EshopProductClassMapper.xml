<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopProductClassMapper">
    <insert id="insertEshopSellerClasses">
        insert into pl_eshop_product_class (id,
        platform_class_id,
        platform_class_name,
        par_id,
        par_platform_class_id,
        concat_par_son_id,
        concat_par_son__platform_class_id,
        full_platform_class_id,
        full_class_id,
        has_son,
        eshop_id,
        profile_id,
        platform_comminsion
        )  values
        <foreach collection="classes" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.platformClassId},
            #{item.platformClassName},
            #{item.parId},
            #{item.parPlatformClassId},
            #{item.concatParSonId},
            #{item.concatParSonPlatformClassId},
            #{item.fullPlatformClassId},
            #{item.fullClassId},
            #{item.hasSon},
            #{item.eshopId},
            #{item.profileId},
            #{item.platformComminsion}
            )
        </foreach>
    </insert>


    <delete id="deleteEshopSellerClass">
        delete  from pl_eshop_product_class where profile_id=#{profileId} and eshop_id=#{eshopId}
    </delete>


    <select id="getEshopSellerClassCount" resultType="java.lang.Integer">
        select count(*) from pl_eshop_product_class where profile_id=#{profileId} and eshop_id=#{eshopId}
    </select>
    <select id="getEshopSellerClass" resultType="com.wsgjp.ct.sale.platform.dto.product.PlatformProductCategoryEntity">
        select *,platform_comminsion as prePlatformcomminsion from pl_eshop_product_class where profile_id=#{profileId} and eshop_id=#{eshopId}
    </select>
    <select id="getEshopSellerClassByLimit" resultType="com.wsgjp.ct.sale.platform.dto.product.PlatformProductCategoryEntity">
        select *,platform_comminsion as prePlatformcomminsion from pl_eshop_product_class where profile_id=#{profileId} and eshop_id=#{eshopId} limit #{pageNum}, #{pageSize}
    </select>
    <select id="getEshopSellerClassById"
            resultType="com.wsgjp.ct.sale.platform.dto.product.PlatformProductCategoryEntity">
        select * from pl_eshop_product_class where profile_id=#{profileId} and eshop_id=#{eshopId} and id=#{classId}
    </select>



    <select id="getEshopCategoryList" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductCategoryEntity">
        select * from pl_eshop_product_class where profile_id=#{profileId} and eshop_id=#{eshopId}
    </select>
</mapper>
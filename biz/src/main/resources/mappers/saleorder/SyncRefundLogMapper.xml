<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.SyncRefundLogMapper">
    <insert id="insertTaskLog">
        insert into pl_eshop_refund_sync_task_log
        (`id`,`profile_id`,`eshop_id`,`sync_state`,`sync_type`,`sync_task_begin_time`,`sync_task_end_time`,`sync_task_time_delay`,
        `sync_exec_begin_time`,`sync_exec_end_time`,`sync_exec_time_consuming`,`sync_order_count`,`sync_order_insert_count`,
        `sync_order_update_count`,`error_message`,`retry_count`)
        value
        (#{id},#{profileId},#{eshopId},#{syncState},#{syncType},#{syncTaskBeginTime},#{syncTaskEndTime},#{syncTaskTimeDelay},#{syncExecBeginTime},
        #{syncExecEndTime},#{syncExecTimeConsuming},#{syncRefundCount},#{syncRefundInsertCount},#{syncRefundUpdateCount},#{errorMessage},#{retryCount})
    </insert>

    <update id="modifyTaskLog">
        UPDATE`pl_eshop_refund_sync_task_log`
        SET
            `sync_state` = #{syncState},
            `sync_type` = #{syncType},
            `sync_task_time_delay` = #{syncTaskTimeDelay},
            <if test="syncExecBeginTime!=null">
                `sync_exec_begin_time` = #{syncExecBeginTime},
            </if>
            <if test="syncExecEndTime!=null">
                `sync_exec_end_time` = #{syncExecEndTime},
            </if>
            `sync_exec_time_consuming` = #{syncExecTimeConsuming},
            `sync_order_count` = #{syncRefundCount},
            `sync_order_insert_count` = #{syncRefundInsertCount},
            `sync_order_update_count` = #{syncRefundUpdateCount},
            `error_message` = #{errorMessage},
            `retry_count` = #{retryCount}
        WHERE id = #{id} and profile_id=#{profileId}
    </update>

</mapper>
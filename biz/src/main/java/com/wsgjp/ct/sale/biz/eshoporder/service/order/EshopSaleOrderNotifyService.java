package com.wsgjp.ct.sale.biz.eshoporder.service.order;

import com.alibaba.fastjson.JSONObject;
import com.wsgjp.ct.bill.core.handle.entity.dao.BillRelation;
import com.wsgjp.ct.common.enums.core.entity.BaseMarkBigDataEntity;
import com.wsgjp.ct.common.enums.core.entity.DeliverNewMark;
import com.wsgjp.ct.common.enums.core.enums.BaseOrderMarkEnum;
import com.wsgjp.ct.common.enums.core.enums.QtyChangeSourceType;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopOrderService;
import com.wsgjp.ct.sale.biz.bifrost.util.EshopUtils;
import com.wsgjp.ct.sale.biz.eshoporder.api.response.DeliverNotifyResponse;
import com.wsgjp.ct.sale.biz.eshoporder.config.EshopOrderConst;
import com.wsgjp.ct.sale.biz.eshoporder.config.ServiceConfig;
import com.wsgjp.ct.sale.biz.eshoporder.config.sysdata.EshopOrderSysDataConfig;
import com.wsgjp.ct.sale.biz.eshoporder.dao.EshopAdvanceSaleOrderDao;
import com.wsgjp.ct.sale.biz.eshoporder.dao.EshopSaleOrderDao;
import com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderDetail;
import com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderPlaform;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.order.OrderCreateType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.params.NotifySaleOrderSendStateRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.refund.EshopRefundFreight;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.order.EshopOrderMarkParameter;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.*;
import com.wsgjp.ct.sale.biz.eshoporder.support.DeliverApi;
import com.wsgjp.ct.sale.biz.eshoporder.util.*;
import com.wsgjp.ct.sale.biz.jarvis.dto.deliverbillupdate.DeliverBillUpdateResult;
import com.wsgjp.ct.sale.biz.jarvis.dto.deliverbillupdate.InterceptStatus;
import com.wsgjp.ct.sale.biz.jarvis.dto.deliverbillupdate.RefundOrderDAO;
import com.wsgjp.ct.sale.biz.jarvis.open.dto.RefundUpdateResponse;
import com.wsgjp.ct.sale.biz.jarvis.state.FastRefundTypeEnum;
import com.wsgjp.ct.sale.biz.jarvis.state.RefundStateEnum;
import com.wsgjp.ct.sale.biz.refund.refund.entity.request.NotifyRefundSendStateRequest;
import com.wsgjp.ct.sale.biz.refund.refund.entity.request.impl.RefundApiImpl;
import com.wsgjp.ct.sale.common.enums.*;
import com.wsgjp.ct.sale.common.enums.eshoporder.*;
import com.wsgjp.ct.sale.common.notify.entity.*;
import com.wsgjp.ct.sale.common.notify.request.DeliverBillPlatformMarkUpdateDao;
import com.wsgjp.ct.sale.common.notify.request.DeliverBillUpdateRequest;
import com.wsgjp.ct.sale.platform.dto.order.entity.EshopOrderDetailFreight;
import com.wsgjp.ct.sale.platform.dto.order.entity.EshopSaleOrderFreight;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.entity.request.buyer.BuyerInfoCheckRequest;
import com.wsgjp.ct.sale.platform.enums.InvoiceState;
import com.wsgjp.ct.sale.platform.enums.RefundStatus;
import com.wsgjp.ct.sale.platform.enums.RefundStatusType;
import com.wsgjp.ct.sale.platform.enums.TradeTypeEnum;
import com.wsgjp.ct.sale.platform.feature.upload.EshopBuyerInfoChangeCheckFeature;
import com.wsgjp.ct.sale.sdk.stock.biz.StockChangeService;
import com.wsgjp.ct.sale.sdk.stock.enums.StockChangeTypeEnum;
import com.wsgjp.ct.sale.sdk.stock.parameter.StockChangeQueueDto;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.global.GlobalConfig;
import com.wsgjp.ct.support.thread.ThreadPool;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import ngp.idgenerator.UId;
import ngp.redis.RedisPoolFactory;
import ngp.starter.web.base.GeneralResult;
import ngp.utils.*;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 * @date 1/6/2020 下午 4:55
 */
@Service
public class EshopSaleOrderNotifyService {

    private final DeliverApi deliverApi;
    private final EshopSaleOrderDao orderDao;
    private final EshopSaleOrderNotifyMapper mapper;
    private final TmcEshopNotifyChangeMapper changeMapper;
    private final EshopAdvanceSaleOrderDao advanceDao;
    private final StockChangeService stockChangeService;
    private final ServiceConfig serviceConfig;

    private final RefundApiImpl refundApi;

    private final EshopSaleOrderRelationService relationService;
    private final BifrostEshopOrderService bifrostEshopOrderService;
    private final EshopSaleOrderMapper saleOrderMapper;
    private final RedisPoolFactory factory;


    private static final Logger logger = LoggerFactory.getLogger(EshopSaleOrderNotifyService.class);

    /**
     * 参数1,refundInterceptStatus 物流拦截状态 0 = 无需拦截  1 = 拦截中  2 = 拦截成功  3 = 拦截失败
     * 参数2，notifyInterceptStatus
     * NONE(0,"无"),
     * INTERCEPT_ING(1,"拦截中"),
     * INTERCEPT_PART(2,"部分拦截成功"),
     * INTERCEPT_SUCCEED(3,"拦截成功"),
     * INTERCEPT_FAILED(5,"拦截失败"),
     */
    private HashMap<Integer, InterceptStatus> interceptNotifyMap;

    public EshopSaleOrderNotifyService(
            DeliverApi deliverApi,
            EshopSaleOrderDao orderDao,
            EshopSaleOrderNotifyMapper mapper,
            TmcEshopNotifyChangeMapper changeMapper,
            EshopAdvanceSaleOrderDao advanceDao,
            StockChangeService stockChangeService, ServiceConfig serviceConfig, RefundApiImpl refundApi, EshopSaleOrderRelationService relationService, BifrostEshopOrderService bifrostEshopOrderService, EshopSaleOrderMapper saleOrderMapper, RedisPoolFactory factory) {

        this.deliverApi = deliverApi;
        this.orderDao = orderDao;
        this.mapper = mapper;
        this.changeMapper = changeMapper;
        this.advanceDao = advanceDao;
        this.stockChangeService = stockChangeService;
        this.serviceConfig = serviceConfig;
        this.refundApi = refundApi;
        this.relationService = relationService;
        this.bifrostEshopOrderService = bifrostEshopOrderService;
        this.saleOrderMapper = saleOrderMapper;
        this.factory = factory;
        interceptNotifyMap = new HashMap<>();
        interceptNotifyMap.put(0, InterceptStatus.NONE);
        interceptNotifyMap.put(1, InterceptStatus.INTERCEPT_ING);
        interceptNotifyMap.put(2, InterceptStatus.INTERCEPT_SUCCEED);
        interceptNotifyMap.put(3, InterceptStatus.INTERCEPT_FAILED);
    }

    public DeliverBillUpdateRequest buildBaseChangeInfo(EshopSaleOrderEntity order) {
        DeliverBillUpdateRequest changeInfo = new DeliverBillUpdateRequest();
        changeInfo.setId(UId.newId());
        changeInfo.setProfileId(order.getProfileId());
        changeInfo.setTradeOrderId(order.getTradeOrderId());
        changeInfo.setEshopOrderId(order.getId());
        changeInfo.setOtypeId(order.getOtypeId());
        changeInfo.setModifyTime(new Date());
        changeInfo.setOid("");
        changeInfo.setProducer(ProducerTypeEnum.SALE_ORDER);
        changeInfo.setTradeType(order.getOrderSaleType().getCode());
        if (1 == GlobalConfig.getUserConfig(EshopOrderSysDataConfig.class).getUseNewOrderNotify() || serviceConfig.isUseNewOrderNotify()){
            if (CollectionUtils.isNotEmpty(order.getAdvanceOrderIds()) && null != order.getAdvanceOrderIds().get(0) && BigInteger.ZERO.compareTo(order.getAdvanceOrderIds().get(0)) != 0){
                changeInfo.setEshopOrderId(order.getAdvanceOrderIds().get(0));
            }
        }
        return changeInfo;
    }

    private DeliverBillUpdateRequest getItemDetailChangeInfo(EshopSaleOrderEntity local, String changeOid, OrderDetailChangeType detailChangeType) {
        DeliverBillUpdateRequest info = buildBaseChangeInfo(local);
        DetailChangeDto dto = new DetailChangeDto();
        dto.setOnlineDetailIds(Collections.singletonList(changeOid));
        dto.setDetailChangeType(detailChangeType);
        info.setContent(JsonUtils.toJson(dto));
        info.setSubChangeType(UpdateTypeEnum.GOODS);
        info.setChangeType(UpdateBillTypeEnum.DETAIL);
        info.setCustomer(CustomerTypeEnum.DELIVER_BILL);
        info.setOid(changeOid);
        return info;
    }


    public DeliverBillUpdateRequest getBuyerChangeInfo(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        boolean normalAddressUnChange = checkNormalAddressUnChangeByBuyerUniqueMark(local, online);
        if (normalAddressUnChange) {
            return null;
        }
        String log = String.format("KEYLOG,addressChange: 【%s】,【%s】,【%s】,【%s】,【%s】,【%s】,【%s】,【%s】",
                online.getEshopBuyer().getCustomerShopAccount(), online.getEshopBuyer().getCustomerReceiver(),
                online.getEshopBuyer().getCustomerReceiverMobile(), online.getEshopBuyer().getCustomerReceiverFullAddress(),
                online.getEshopBuyer().getAi(), online.getEshopBuyer().getRi(),
                online.getEshopBuyer().getMi(), online.getEshopBuyer().getAddri());
        CommonUtil.doLogByEnable(logger, LogLevelEnum.ERROR, String.format("profileId:%s,tradeOrderId:%s," +
                        "localBuyerId:%s,localAi:%s,localRi:%s,localMi:%s,localAddri:%s," +
                        "onlineBuyerId:%s,onlineAi:%s,onlineRi:%s,onlineMi:%s,onlineAddri:%s",
                local.getProfileId(), local.getTradeOrderId(),
                local.getBuyerId(), local.getEshopBuyer().getAi(), local.getEshopBuyer().getRi(), local.getEshopBuyer().getMi(), local.getEshopBuyer().getAddri(),
                online.getBuyerId(), online.getEshopBuyer().getAi(), online.getEshopBuyer().getRi(), online.getEshopBuyer().getMi(), online.getEshopBuyer().getAddri()));
        SysLogUtil.buildAndAddKeyLog(online, OrderOpreateType.AUTO_DOWNLOAD, log);
        DeliverBillUpdateRequest info = buildBaseChangeInfo(local);
        info.setSubChangeType(UpdateTypeEnum.RECEIVER);
        info.setContent(JsonUtils.toJson(online.getEshopBuyer()));
        info.setChangeType(UpdateBillTypeEnum.BILL);
        info.setCustomer(CustomerTypeEnum.DELIVER_BILL);
        return info;
    }

    private DeliverBillUpdateRequest getPickUpAddressChangeInfo(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        boolean normalPickUpAddressUnChange = checkNormalPickUpAddressUnChange(local, online);
        if (normalPickUpAddressUnChange) {
            return null;
        }
        DeliverBillUpdateRequest info = buildBaseChangeInfo(local);
        info.setSubChangeType(UpdateTypeEnum.RECEIVER);
        info.setContent(JsonUtils.toJson(online.getExtend().getSelfPickUpInfo()));
        info.setChangeType(UpdateBillTypeEnum.BILL);
        info.setCustomer(CustomerTypeEnum.DELIVER_BILL);
        return info;
    }

    public boolean checkNormalAddressUnChangeByBuyerUniqueMark(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        return checkNormalAddressUnChange(local, online);
        //本地或者线上没用这个买家信息唯一标识的时候
        //需要用原有逻辑判断
//        if (StringUtils.isEmpty(localBuyerUniqueMark) || StringUtils.isEmpty(onlineBuyerUniqueMark)){
//            return checkNormalAddressUnChange(local,online);
//        }
//        return localBuyerUniqueMark.equals(onlineBuyerUniqueMark);
    }

    /**
     * 买家信息比对
     * 4.9补丁新增了BuyerUniqueMark字段进行比对，但是要兼容没用这个字段的情况
     * 所以两个版本之后，这个方法可以移除
     *
     * @param local
     * @param online
     * @return boolean
     */
    public boolean checkNormalAddressUnChange(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        //周期购订单，线上已发无需通知地址变更
        if(TradeTypeEnum.CYCLE_PURCHASE == online.getOrderSaleType() && TradeStatus.WAIT_BUYER_CONFIRM_GOODS == online.getLocalTradeState()){
            return true;
        }
        ShopType shopType = online.getShopType();
        //有Feature优先判断
        if (EshopUtils.isFeatureSupported(EshopBuyerInfoChangeCheckFeature.class, shopType) &&
                OrderCreateType.FROM_DOWNLOAD_ORDER == local.getCreateType()) {
            BuyerInfoCheckRequest request = new BuyerInfoCheckRequest();
            request.setShopId(online.getOtypeId());
            request.setShopType(shopType);
            request.setOldBuyer(local.getEshopBuyer());
            request.setNewBuyer(online.getEshopBuyer());
            return !bifrostEshopOrderService.buyerInfoChangeCheck(request);
        }
        if (CommonUtil.shopTypesSupport(shopType, serviceConfig.getBuyerInfoNotifyByOpenAddressIdShopTypes())) {
            return online.getEshopBuyer().getOpenAddressId().equals(local.getEshopBuyer().getOpenAddressId());
        }
        boolean normalAddressUnChange = online.getEshopBuyer().getCompareHashKey().equals(local.getEshopBuyer().getCompareHashKey()) || online.getFuzzySensitiveData();
        if (online.getEshopBuyer().getDi().startsWith("NEWDD") && local.getEshopBuyer().getDi().startsWith("DD")) {
            //抖店改造，模糊化信息没变的情况，认为地址未变更
            if (online.getFuzzySensitiveData().equals(local.getFuzzySensitiveData())) {
                normalAddressUnChange = true;
            }
        }
        //当平台订单的收货地址的【详细地址】，从有更新到无时，交易单和发货单，不打打标【址】
        //小红书打印物流单同步发货后收货信息就没有详细地址，只有省市区，系统会判定为地址变更，从而使订单标记会有个址的提示，这样客户会误以为地址有修改
        if (StringUtils.isNotEmpty(local.getEshopBuyer().getCustomerReceiverAddress()) && StringUtils.isEmpty(online.getEshopBuyer().getCustomerReceiverAddress())) {
            normalAddressUnChange = true;
        }
        return normalAddressUnChange;
    }

    public boolean checkNormalPickUpAddressUnChange(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        if ((local.getExtend().getSelfPickUpInfo() == null && online.getExtend().getSelfPickUpInfo() != null) ||
                (local.getExtend().getSelfPickUpInfo() != null && online.getExtend().getSelfPickUpInfo() == null)) {
            return false;
        }
        return online.getExtend().getSelfPickUpInfo().getCompareHashKey().equals(local.getExtend().getSelfPickUpInfo().getCompareHashKey()) || online.getFuzzySensitiveData();
    }

    public DeliverBillUpdateRequest getMemoChangeInfo(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        boolean memo = !local.getSellerMemo().equals(online.getSellerMemo());
        boolean flag = local.getSellerFlag() != online.getSellerFlag();
        boolean flagMemo = !local.getExtend().getSellerFlagMemo().equals(online.getExtend().getSellerFlagMemo());
        boolean msg = !local.getBuyerMessage().equals(online.getBuyerMessage());
        if (memo || flag || msg || flagMemo) {
            return buildMemoChangeInfo(local, online);
        }
        return null;
    }

    public void notifyLocal(EshopSaleOrderEntity local) {

        if (local.getProcessState().getCode() == ProcessState.NoSubmit.getCode()) {
            return;
        }
        List<EshopSaleOrderDetail> orderDetails = local.getOrderDetails();
        if (orderDetails == null || orderDetails.isEmpty()) {
            return;
        }
        Map<DetailProcessState, List<EshopSaleOrderDetail>> processStateMaps
                = orderDetails.stream().collect(Collectors.groupingBy(EshopSaleOrderDetail::getProcessState));
        processStateMaps.forEach((key, value) -> {
            switch (key) {
                case Submit:
                    List<DeliverBillUpdateRequest> changeInfoList = new ArrayList<>();
                    DeliverBillUpdateRequest info = buildMemoChangeInfo(local, local);
                    changeInfoList.add(info);
                    notify(changeInfoList);
                    break;
                case SubmitAdvance:
                    advanceDao.NotifySellerMemo(local);
                    break;
                default:
                    break;
            }
        });
    }

    public void notify(List<DeliverBillUpdateRequest> changeInfoList) {
        if (changeInfoList == null || changeInfoList.isEmpty()) {
            return;
        }
        doInsertChange(changeInfoList);
        GeneralResult<DeliverBillUpdateResult> notify = deliverApi.notify(changeInfoList);
        if (notify.getCode() != 200) {
            logger.error(String.format("profileId:%s 通知发货单变更报错：%s", CurrentUser.getProfileId(), JsonUtils.toJson(notify.getData())));
            notify = deliverApi.notify(changeInfoList);
        }
        logger.info(String.format("saleOrder Mark Change request:%s, response :%s",
                JsonUtils.toJson(changeInfoList), JsonUtils.toJson(notify.getData())));
    }

    @NotNull
    private DeliverBillUpdateRequest buildMemoChangeInfo(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        DeliverBillUpdateRequest info = buildBaseChangeInfo(local);
        SellerMemo sellerMemo = new SellerMemo();
        sellerMemo.setBuyerMessage(online.getBuyerMessage());
        sellerMemo.setSellerFlag(online.getSellerFlag().getCode());
        sellerMemo.setSellerMemo(online.getSellerMemo());
        sellerMemo.setSellerFlagMemo(online.getExtend().getSellerFlagMemo());
        info.setContent(JsonUtils.toJson(sellerMemo));
        info.setSubChangeType(UpdateTypeEnum.SELLER_MEMO);
        info.setChangeType(UpdateBillTypeEnum.BILL);
        info.setCustomer(CustomerTypeEnum.DELIVER_BILL);
        return info;
    }


    private DeliverBillUpdateRequest getInvoiceChangeInfo(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        EshopSaleOrderInvoiceInfo invoiceInfo = online.getInvoiceInfo();
        EshopSaleOrderInvoiceInfo oldInvoice = local.getInvoiceInfo();
        boolean equals = oldInvoice.getInvoiceTitle().equals(invoiceInfo.getInvoiceTitle());
        if (!equals) {
            InvoiceDto dto = new InvoiceDto();
            DeliverBillUpdateRequest info = buildBaseChangeInfo(local);
            dto.setInvoiceState(invoiceInfo.getInvoiceState().equals(InvoiceState.INVOICED) ? 1 : 0);
            dto.setInvoiceTax(invoiceInfo.getInvoiceCode());
            dto.setInvoiceTitle(invoiceInfo.getInvoiceTitle());
            dto.setInvoiceType(invoiceInfo.getInvoiceCategory().getCode());
            dto.setInvoiceUserType(invoiceInfo.getInvoiceType().getCode());
            dto.setMemo(invoiceInfo.getInvoiceRemark());
            dto.setNeedInvoice(invoiceInfo.isInvoiceRequired());
            dto.setAddress(invoiceInfo.getInvoiceRegisterAddr());
            dto.setPhone(invoiceInfo.getInvoiceRegisterPhone());
            dto.setBank(invoiceInfo.getInvoiceBank());
            dto.setBankAccount(invoiceInfo.getInvoiceBankAccount());
            dto.setMemo(invoiceInfo.getInvoiceRemark());
            info.setContent(JsonUtils.toJson(dto));
            info.setSubChangeType(UpdateTypeEnum.INVOICE);
            info.setChangeType(UpdateBillTypeEnum.BILL);
            info.setCustomer(CustomerTypeEnum.DELIVER_BILL);
            return info;
        }
        return null;
    }

    private DeliverBillUpdateRequest getQICQualityChangeInfo(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        EshopSaleOrderExtendEntity localExtend = local.getExtend();
        EshopSaleOrderExtendEntity onlineExtend = online.getExtend();
        if (null == onlineExtend) {
            return null;
        }
        if (localExtend.getPlatformQualityOrgId().equals(onlineExtend.getPlatformQualityOrgId()) &&
                localExtend.getPlatformQualityOrgName().equals(onlineExtend.getPlatformQualityOrgName()) &&
                localExtend.getPlatformQualityWarehouseCode().equals(onlineExtend.getPlatformQualityWarehouseCode()) &&
                localExtend.getPlatformQualityWarehouseName().equals(onlineExtend.getPlatformQualityWarehouseName())) {
            return null;
        }
        NotifyMainAndDetailChangeDto dto = new NotifyMainAndDetailChangeDto();
        DeliverBillUpdateRequest info = buildBaseChangeInfo(local);
        dto.setPlatformQualityOrgId(onlineExtend.getPlatformQualityOrgId());
        dto.setPlatformQualityOrgName(onlineExtend.getPlatformQualityOrgName());
        dto.setPlatformQualityWarehouseCode(onlineExtend.getPlatformQualityWarehouseCode());
        dto.setPlatformQualityWarehouseName(onlineExtend.getPlatformQualityWarehouseName());
        info.setContent(JsonUtils.toJson(dto));
        info.setSubChangeType(UpdateTypeEnum.QIC_QUALITY);
        info.setChangeType(UpdateBillTypeEnum.BILL);
        info.setCustomer(CustomerTypeEnum.DELIVER_BILL);
        return info;
    }

    public List<DeliverBillUpdateRequest> getDetailsChangeInfo(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        List<DeliverBillUpdateRequest> result = new ArrayList<>();
        Map<String, List<EshopSaleOrderDetail>> onlineDetailsMap = online.getOrderDetails().stream()
                .filter(x -> x.getProcessState().compareTo(DetailProcessState.NoSubmit) != 0)
                .collect(Collectors.groupingBy(EshopSaleOrderDetail::getTradeOrderDetailId));
        if (onlineDetailsMap.isEmpty()) {
            return result;
        }

        for (String onlineOid : onlineDetailsMap.keySet()) {
            List<EshopSaleOrderDetail> orderDetails = onlineDetailsMap.get(onlineOid);
            EshopSaleOrderDetail onlineItem = orderDetails.get(0);
            //如果线上信息发生变更，优先处理线上订单标记
            List<OrderDetailChangeType> changeTypes = onlineItem.getDetailChangeTypes();
            if (CollectionUtils.isEmpty(changeTypes)){
                continue;
            }
            for (OrderDetailChangeType type : changeTypes) {
                if (type.equals(OrderDetailChangeType.NORMAL)) {
                    continue;
                }
                if (type.equals(OrderDetailChangeType.PRICE_CHANGE)){
                    DeliverBillUpdateRequest info = buildBaseChangeInfo(local);
                    DetailChangeDto dto = new DetailChangeDto();
                    dto.setOnlineDetailIds(Collections.singletonList(onlineOid));
                    dto.setDetailChangeType(type);
                    dto.setTotal(BigDecimal.valueOf(orderDetails.stream().mapToDouble(x -> x.getTotal().doubleValue()).sum()));
                    dto.setDisedInitialTotal(BigDecimal.valueOf(orderDetails.stream().mapToDouble(x -> x.getDisedInitialTotal().doubleValue()).sum()));
                    dto.setDisedTaxedTotal(BigDecimal.valueOf(orderDetails.stream().mapToDouble(x -> x.getDisedTaxedTotal().doubleValue()).sum()));
                    dto.setDisedTotal(BigDecimal.valueOf(orderDetails.stream().mapToDouble(x -> x.getDisedTotal().doubleValue()).sum()));
                    dto.setTaxTotal(BigDecimal.valueOf(orderDetails.stream().mapToDouble(x -> x.getTaxTotal().doubleValue()).sum()));
                    dto.setPtypePreferentialTotal(BigDecimal.valueOf(orderDetails.stream().mapToDouble(x -> x.getPtypePreferentialTotal().doubleValue()).sum()));
                    dto.setPlatformPtypePreferentialTotal(BigDecimal.valueOf(orderDetails.stream().mapToDouble(x -> x.getPlatformPtypePreferentialTotal().doubleValue()).sum()));
                    dto.setOrderPreferentialAllotTotal(BigDecimal.valueOf(orderDetails.stream().mapToDouble(x -> x.getOrderPreferentialAllotTotal().doubleValue()).sum()));
                    dto.setPlatformOrderPreferentialTotal(BigDecimal.valueOf(orderDetails.stream().mapToDouble(x -> x.getPlatformOrderPreferentialTotal().doubleValue()).sum()));
                    dto.setAnchorOrderPreferentialTotal(BigDecimal.valueOf(orderDetails.stream().map(EshopSaleOrderDetail::getExtend).mapToDouble(e -> e.getAnchorOrderPreferentialTotal().doubleValue()).sum()));
                    dto.setAnchorPtypePreferentialTotal(BigDecimal.valueOf(orderDetails.stream().map(EshopSaleOrderDetail::getExtend).mapToDouble(e -> e.getAnchorPtypePreferentialTotal().doubleValue()).sum()));
                    dto.setPlatformOrderSubsidyTotal(BigDecimal.valueOf(orderDetails.stream().map(EshopSaleOrderDetail::getExtend).mapToDouble(e -> e.getPlatformOrderSubsidyTotal().doubleValue()).sum()));
                    dto.setPlatformPtypeSubsidyTotal(BigDecimal.valueOf(orderDetails.stream().map(EshopSaleOrderDetail::getExtend).mapToDouble(e -> e.getPlatformPtypeSubsidyTotal().doubleValue()).sum()));
                    dto.setNationalSubsidyTotal(BigDecimal.valueOf(orderDetails.stream().map(EshopSaleOrderDetail::getExtend).mapToDouble(e -> e.getNationalSubsidyTotal().doubleValue()).sum()));
                    dto.setPtypeServiceFee(BigDecimal.valueOf(orderDetails.stream().mapToDouble(x -> x.getPtypeServiceFee().doubleValue()).sum()));
                    info.setContent(JsonUtils.toJson(dto));
                    info.setSubChangeType(UpdateTypeEnum.PRICE);
                    info.setChangeType(UpdateBillTypeEnum.DETAIL);
                    info.setCustomer(CustomerTypeEnum.DELIVER_BILL);
                    info.setOid(onlineOid);
                    result.add(info);
                    continue;
                }
                result.add(getItemDetailChangeInfo(local, onlineOid, type));
            }
        }

        //本地明细被删除通知
//        Map<String, List<EshopSaleOrderDetail>> localDetailsMap = local.getOrderDetails().stream()
//                .filter(x -> x.getProcessState().compareTo(DetailProcessState.Submit) == 0)
//                .collect(Collectors.groupingBy(EshopSaleOrderDetail::getTradeOrderDetailId));
//        if (localDetailsMap.isEmpty()) {
//            return result;
//        }
//        for (String localOid : localDetailsMap.keySet()) {
//            EshopSaleOrderDetail deleteItems = localDetailsMap.get(localOid).get(0);
//            if (deleteItems.getOrderSaleType().equals(TradeTypeEnum.NORMAL) &&
//                    deleteItems.getDetailChangeType() != null &&
//                    deleteItems.getDetailChangeType().equals(OrderDetailChangeType.DELETE_DETAIL)) {
//                result.add(getItemDetailChangeInfo(local, localOid, deleteItems.getDetailChangeType()));
//            }
//        }
        return result;
    }

    public DeliverBillUpdateRequest getTradeSateChange(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        if (!CheckTradeStateHasChange(local, online)) {
            return null;
        }
        BigInteger vchcode = local.getId();
        NotifyMainAndDetailChangeDto dto = new NotifyMainAndDetailChangeDto();
        dto.setTradeState(online.getLocalTradeState());
        dto.setVchcode(vchcode);
        dto.setTradeId(local.getTradeOrderId());
        dto.setPayTime(online.getTradePayTime());
        dto.setFinishTime(online.getTradeFinishTime());
        buildTradeStateChangeDetail(dto, online);
        DeliverBillUpdateRequest info = buildBaseChangeInfo(local);
        info.setSubChangeType(UpdateTypeEnum.TRADE_STATE);
        info.setContent(JsonUtils.toJson(dto));
        info.setChangeType(UpdateBillTypeEnum.BILL);
        info.setCustomer(CustomerTypeEnum.DELIVER_BILL);
        return info;
    }

    private DeliverBillUpdateRequest getPromisedSendTimeChange(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        if (!checkPromisedSendTimeHasChange(local, online)) {
            return null;
        }
        BigInteger vchcode = local.getId();
        NotifyMainAndDetailChangeDto dto = new NotifyMainAndDetailChangeDto();
        dto.setVchcode(vchcode);
        dto.setTradeId(local.getTradeOrderId());
        dto.setPromisedSendTime(online.getTiming().getSendTime());
        buildPromisedSendTimeChangeDetail(dto, online);
        DeliverBillUpdateRequest info = buildBaseChangeInfo(local);
        info.setSubChangeType(UpdateTypeEnum.MODIFY_MAIN_OR_DETAIL_TIMING);
        info.setContent(JsonUtils.toJson(dto));
        info.setChangeType(UpdateBillTypeEnum.BILL);
        info.setCustomer(CustomerTypeEnum.DELIVER_BILL);
        return info;
    }

    private DeliverBillUpdateRequest getPlatformQcAndIdentifyResultChange(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        if (!CheckPlatformQcAndIdentifyResultHasChange(local, online)) {
            return null;
        }
        BigInteger vchcode = local.getId();
        NotifyMainAndDetailChangeDto dto = new NotifyMainAndDetailChangeDto();
        dto.setVchcode(vchcode);
        dto.setTradeId(local.getTradeOrderId());
        buildPlatformQcAndIdentifyResultChangeDetail(dto, online);
        DeliverBillUpdateRequest info = buildBaseChangeInfo(local);
        info.setSubChangeType(UpdateTypeEnum.PLATFORM_QC_AND_IDENTIFY_RESULT);
        info.setContent(JsonUtils.toJson(dto));
        info.setChangeType(UpdateBillTypeEnum.BILL);
        info.setCustomer(CustomerTypeEnum.DELIVER_BILL);
        return info;
    }

    private boolean CheckPlatformQcAndIdentifyResultHasChange(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        List<EshopSaleOrderDetail> localDetails = local.getOrderDetails();
        List<EshopSaleOrderDetail> onlineDetails = online.getOrderDetails();
        for (EshopSaleOrderDetail oldDetail : localDetails) {
            if (oldDetail.getProcessState().compareTo(DetailProcessState.NoSubmit) == 0) {
                continue;
            }
            Optional<EshopSaleOrderDetail> detailOpt = onlineDetails.stream().filter(x -> x.getTradeOrderDetailId().equals(oldDetail.getTradeOrderDetailId()))
                    .collect(Collectors.toList()).stream().findFirst();
            if (!detailOpt.isPresent()) {
                continue;
            }
            EshopSaleOrderDetail newDetail = detailOpt.get();
            if (!newDetail.getPlatformQcResult().equals(oldDetail.getPlatformQcResult()) ||
                    !newDetail.getPlatformQcResultDesc().equals(oldDetail.getPlatformQcResultDesc()) ||
                    !newDetail.getPlatformIdentifyResult().equals(oldDetail.getPlatformIdentifyResult()) ||
                    !newDetail.getPlatformIdentifyResultDesc().equals(oldDetail.getPlatformIdentifyResultDesc())) {
                return true;
            }
        }
        return false;
    }

    private boolean CheckTradeStateHasChange(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        if (!local.getLocalTradeState().equals(online.getLocalTradeState())) {
            return true;
        }
        List<EshopSaleOrderDetail> localDetails = local.getOrderDetails();
        List<EshopSaleOrderDetail> onlineDetails = online.getOrderDetails();
        for (EshopSaleOrderDetail oldDetail : localDetails) {
            if (oldDetail.getProcessState().compareTo(DetailProcessState.NoSubmit) == 0) {
                continue;
            }
            Optional<EshopSaleOrderDetail> detailOpt = onlineDetails.stream().filter(x -> x.getTradeOrderDetailId().equals(oldDetail.getTradeOrderDetailId()))
                    .collect(Collectors.toList()).stream().findFirst();
            if (!detailOpt.isPresent()) {
                continue;
            }
            EshopSaleOrderDetail newDetail = detailOpt.get();
            if (!newDetail.getPlatformDetailTradeState().equals(oldDetail.getPlatformDetailTradeState())) {
                return true;
            }
        }
        return false;
    }

    private boolean checkPromisedSendTimeHasChange(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        List<EshopSaleOrderDetail> localDetails = local.getOrderDetails();
        List<EshopSaleOrderDetail> onlineDetails = online.getOrderDetails();
        for (EshopSaleOrderDetail oldDetail : localDetails) {
            if (oldDetail.getProcessState().compareTo(DetailProcessState.NoSubmit) == 0) {
                continue;
            }
            Optional<EshopSaleOrderDetail> detailOpt = onlineDetails.stream().filter(x -> x.getTradeOrderDetailId().equals(oldDetail.getTradeOrderDetailId()))
                    .collect(Collectors.toList()).stream().findFirst();
            if (!detailOpt.isPresent()) {
                continue;
            }
            EshopSaleOrderDetail newDetail = detailOpt.get();
            EshopSaleOrderDetailTiming newDetailTiming = newDetail.getTiming();
            EshopSaleOrderDetailTiming oldDetailTiming = oldDetail.getTiming();
            if ((null != newDetailTiming && null == oldDetailTiming) ||
                    (null == newDetailTiming && null != oldDetailTiming)) {
                return true;
            }
            if (null != newDetailTiming) {
                Date newPromisedSendTime = newDetailTiming.getPromisedSendTime();
                Date oldPromisedSendTime = oldDetailTiming.getPromisedSendTime();
                if ((null != newPromisedSendTime && null == oldPromisedSendTime) ||
                        (null == newPromisedSendTime && null != oldPromisedSendTime)) {
                    return true;
                }
                if (null != newPromisedSendTime) {
                    if ((DateUtils.compare(newPromisedSendTime, oldPromisedSendTime) > 0)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private boolean checkKtypeIdHasChange(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        if (local.getKtypeId().compareTo(online.getKtypeId()) != 0) {
            return true;
        }
        List<EshopSaleOrderDetail> localDetails = local.getOrderDetails();
        List<EshopSaleOrderDetail> onlineDetails = online.getOrderDetails();
        for (EshopSaleOrderDetail oldDetail : localDetails) {
            if (oldDetail.getProcessState().compareTo(DetailProcessState.NoSubmit) == 0) {
                continue;
            }
            Optional<EshopSaleOrderDetail> detailOpt = onlineDetails.stream().filter(x -> x.getTradeOrderDetailId().equals(oldDetail.getTradeOrderDetailId()))
                    .collect(Collectors.toList()).stream().findFirst();
            if (!detailOpt.isPresent()) {
                continue;
            }
            EshopSaleOrderDetail newDetail = detailOpt.get();
            if (newDetail.getKtypeId().compareTo(oldDetail.getKtypeId()) != 0) {
                return true;
            }
        }
        return false;
    }

    public void buildTradeStateChangeDetail(NotifyMainAndDetailChangeDto dto, EshopSaleOrderEntity online) {
        List<EshopSaleOrderDetail> details = online.getOrderDetails();
        if (details == null || details.size() == 0) {
            return;
        }
        List<DeliverBillDetailTradeStatusDTO> dtoDetails = new ArrayList<>();
        for (EshopSaleOrderDetail detail : details) {
            if (detail.getProcessState().compareTo(DetailProcessState.NoSubmit) == 0) {
                continue;
            }
            DeliverBillDetailTradeStatusDTO detailDto = new DeliverBillDetailTradeStatusDTO();
            detailDto.setTradeState(detail.getPlatformDetailTradeState());
            detailDto.setOrderDetailId(detail.getId());
            detailDto.setOid(detail.getTradeOrderDetailId());
            dtoDetails.add(detailDto);
        }
        if (dtoDetails.size() == 0) {
            return;
        }
        dto.setDetails(dtoDetails);
    }

    private void buildPromisedSendTimeChangeDetail(NotifyMainAndDetailChangeDto dto, EshopSaleOrderEntity online) {
        List<EshopSaleOrderDetail> details = online.getOrderDetails();
        if (details == null || details.isEmpty()) {
            return;
        }
        List<DeliverBillDetailTradeStatusDTO> dtoDetails = new ArrayList<>();
        for (EshopSaleOrderDetail detail : details) {
            if (detail.getProcessState().compareTo(DetailProcessState.NoSubmit) == 0) {
                continue;
            }
            DeliverBillDetailTradeStatusDTO detailDto = new DeliverBillDetailTradeStatusDTO();
            detailDto.setOrderDetailId(detail.getId());
            detailDto.setOid(detail.getTradeOrderDetailId());
            if (null == detail.getTiming()) {
                detailDto.setPromisedSendTime(null);
            } else {
                detailDto.setPromisedSendTime(detail.getTiming().getPromisedSendTime());
            }
            dtoDetails.add(detailDto);
        }
        if (dtoDetails.isEmpty()) {
            return;
        }
        dto.setDetails(dtoDetails);
    }

    private void buildKtypeIdChangeDetail(NotifyMainAndDetailChangeDto dto, EshopSaleOrderEntity online) {
        List<EshopSaleOrderDetail> details = online.getOrderDetails();
        if (details == null || details.isEmpty()) {
            return;
        }
        List<DeliverBillDetailTradeStatusDTO> dtoDetails = new ArrayList<>();
        for (EshopSaleOrderDetail detail : details) {
            if (detail.getProcessState().compareTo(DetailProcessState.NoSubmit) == 0) {
                continue;
            }
            DeliverBillDetailTradeStatusDTO detailDto = new DeliverBillDetailTradeStatusDTO();
            detailDto.setOrderDetailId(detail.getId());
            detailDto.setOid(detail.getTradeOrderDetailId());
            detailDto.setKtypeId(detail.getKtypeId());
            dtoDetails.add(detailDto);
        }
        if (dtoDetails.isEmpty()) {
            return;
        }
        dto.setDetails(dtoDetails);
    }


    private void buildPlatformQcAndIdentifyResultChangeDetail(NotifyMainAndDetailChangeDto dto, EshopSaleOrderEntity online) {
        List<EshopSaleOrderDetail> details = online.getOrderDetails();
        if (details == null || details.isEmpty()) {
            return;
        }
        List<DeliverBillDetailTradeStatusDTO> dtoDetails = new ArrayList<>();
        for (EshopSaleOrderDetail detail : details) {
            if (detail.getProcessState().compareTo(DetailProcessState.NoSubmit) == 0) {
                continue;
            }
            DeliverBillDetailTradeStatusDTO detailDto = new DeliverBillDetailTradeStatusDTO();
            detailDto.setOrderDetailId(detail.getId());
            detailDto.setOid(detail.getTradeOrderDetailId());
            detailDto.setPlatformQcResult(detail.getPlatformQcResult());
            detailDto.setPlatformQcResultDesc(detail.getPlatformQcResultDesc());
            detailDto.setPlatformIdentifyResult(detail.getPlatformIdentifyResult());
            detailDto.setPlatformIdentifyResultDesc(detail.getPlatformIdentifyResultDesc());
            dtoDetails.add(detailDto);
        }
        if (dtoDetails.isEmpty()) {
            return;
        }
        dto.setDetails(dtoDetails);
    }


    public DeliverBillUpdateRequest getBuyerFreightFeeChange(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        if (local.getProcessState().equals(ProcessState.NoSubmit)) {
            return null;
        }
        if (local.getOrderBuyerFreightFee().compareTo(online.getOrderBuyerFreightFee()) == 0) {
            return null;
        }
        DeliverBillUpdateRequest info = buildBaseChangeInfo(local);
        info.setSubChangeType(UpdateTypeEnum.FREIGHT_PRICE_CHANGE);
        info.setOrderBuyerFreightFee(online.getOrderBuyerFreightFee());
        info.setChangeType(UpdateBillTypeEnum.BILL);
        info.setCustomer(CustomerTypeEnum.DELIVER_BILL);
        info.setContent(JsonUtils.toJson(info));
        return info;
    }

    private void batchAddChangeInfo(List<DeliverBillUpdateRequest> changeInfoList, List<DeliverBillUpdateRequest> changeInfo) {
        if (changeInfo == null || changeInfo.isEmpty()) {
            return;
        }
        for (DeliverBillUpdateRequest change : changeInfo
        ) {
            addChangeInfo(changeInfoList, change);
        }
    }

    private void addChangeInfo(List<DeliverBillUpdateRequest> changeInfoList, DeliverBillUpdateRequest changeInfo) {
        if (changeInfo == null) {
            return;
        }
        changeInfoList.add(changeInfo);
    }


    public void doNotifyChange(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        List<EshopSaleOrderDetail> orderDetails = online.getOrderDetails();
        if (orderDetails == null || orderDetails.isEmpty()) {
            return;
        }
        boolean useNewOrderNotify = (1 == GlobalConfig.getUserConfig(EshopOrderSysDataConfig.class).getUseNewOrderNotify() || serviceConfig.isUseNewOrderNotify());
        QueryAdvanceDetailParameter detailParameter = new QueryAdvanceDetailParameter();
        detailParameter.setProfileId(local.getProfileId());
        detailParameter.setSaleOrderId(local.getId());
        List<EshopAdvanceOrderDetail> oldAdvanceDetails = advanceDao.querySimpleAdvanceDetails(detailParameter);
        BigInteger advanceOrderId = advanceDao.queryAdvanceOrderId(local.getProfileId(), local.getOtypeId(), local.getTradeOrderId());
        online.setAdvanceOrderIds(Collections.singletonList(advanceDao.queryAdvanceOrderId(local.getProfileId(), local.getOtypeId(), local.getTradeOrderId())));
        local.setAdvanceOrderIds(online.getAdvanceOrderIds());
        Map<DetailProcessState, List<EshopSaleOrderDetail>> processStateMaps
                = orderDetails.stream().collect(Collectors.groupingBy(EshopSaleOrderDetail::getProcessState));
        processStateMaps.forEach((key, value) -> {
            EshopSaleOrderEntity notifyOrder = JsonUtils.toObject(JsonUtils.toJson(online), EshopSaleOrderEntity.class);
            notifyOrder.setEshopBuyer(online.getEshopBuyer());
            notifyOrder.getExtend().setSelfPickUpInfo(online.getExtend().getSelfPickUpInfo());
            notifyOrder.setOrderDetails(value);
            switch (key) {
                case Submit:
                    doDeliverNotifyChange(local, notifyOrder);
                    break;
                case SubmitAdvance:
                    if (!useNewOrderNotify){
                        doNotifyAdvanceChange(local,notifyOrder);
                        break;
                    }
                    doUseNewOrderNotify(local, value, oldAdvanceDetails, notifyOrder);
                default:
                    break;
            }
        });
    }

    private void doUseNewOrderNotify(EshopSaleOrderEntity local, List<EshopSaleOrderDetail> value, List<EshopAdvanceOrderDetail> oldAdvanceDetails, EshopSaleOrderEntity notifyOrder) {
        if (CollectionUtils.isEmpty(oldAdvanceDetails)){
            return;
        }
        List<EshopSaleOrderDetail> needDeliverNotifyDetails = new ArrayList<>();
        List<EshopSaleOrderDetail> needAdvanceNotifyDetails = new ArrayList<>();
        for (EshopSaleOrderDetail detail : value) {
            boolean advanceDetailHasSubmit = oldAdvanceDetails.stream().anyMatch(o -> o.getPlatform().getTradeOrderDetailId().equals(detail.getTradeOrderDetailId())
                    && DetailProcessState.Submit == o.getPlatform().getProcessState());
            if(advanceDetailHasSubmit){
                needDeliverNotifyDetails.add(detail);
            }else {
                needAdvanceNotifyDetails.add(detail);
            }
        }
        if (CollectionUtils.isNotEmpty(needDeliverNotifyDetails)){
            notifyOrder.setOrderDetails(needDeliverNotifyDetails);
            doDeliverNotifyChange(local, notifyOrder);
            doNotifyAdvanceChange(local, notifyOrder);
            return;
        }
        if (CollectionUtils.isNotEmpty(needAdvanceNotifyDetails)){
            notifyOrder.setOrderDetails(needAdvanceNotifyDetails);
            doNotifyAdvanceChange(local, notifyOrder);
            return;
        }
    }


    public void doDeliverNotifyChange(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        try {
            SysLogUtil.buildAndAddKeyLog(OrderOpreateType.AUTO_DOWNLOAD, local, "notifyLocal");
            SysLogUtil.buildAndAddKeyLog(OrderOpreateType.AUTO_DOWNLOAD, online, "notifyOnline");
            List<DeliverBillUpdateRequest> changeInfoList = new ArrayList<>();
            addChangeInfo(changeInfoList, getMemoChangeInfo(local, online));
            addChangeInfo(changeInfoList, getBuyerChangeInfo(local, online));
            if (local.getCreateType()==OrderCreateType.INPUT)
            {
                addChangeInfo(changeInfoList,getBtypeChange(local,online));
            }
//            addChangeInfo(changeInfoList, getPickUpAddressChangeInfo(local, online));
            addChangeInfo(changeInfoList, getInvoiceChangeInfo(local, online));
            addChangeInfo(changeInfoList, getBuyerFreightFeeChange(local, online));
            addChangeInfo(changeInfoList, getTradeSateChange(local, online));
            //明细最晚发货时间
            addChangeInfo(changeInfoList, getPromisedSendTimeChange(local, online));
            addChangeInfo(changeInfoList, getPlatformQcAndIdentifyResultChange(local, online));
//            addChangeInfo(changeInfoList, getKtypeIdChange(local, online));
            addChangeInfo(changeInfoList, getFreightInfoChange(local, online));
            addChangeInfo(changeInfoList, getTimingChange(local, online));
//            addChangeInfo(changeInfoList, getSignTimingChange(local, online));
            addChangeInfo(changeInfoList, getConfirmInfoChange(local, online));
            addChangeInfo(changeInfoList, getGroupHearNameChange(local, online));
            addChangeInfo(changeInfoList, getQICQualityChangeInfo(local, online));
            batchAddChangeInfo(changeInfoList, getDetailsChangeInfo(local, online));
            notify(changeInfoList);
            doNotifyRefundByOrderState(local, online);
            doNotifyOrderMark(local, online);
        } catch (RuntimeException ex) {
            logger.error(String.format("tradeid:%s  doDeliverNotifyChange Error:%s", online.getTradeOrderId(), ex.getMessage()), ex);
            throw ex;
        }
    }

    private DeliverBillUpdateRequest getBtypeChange(EshopSaleOrderEntity local, EshopSaleOrderEntity online)
    {
        if (!local.getBtypeId().equals(online.getBtypeId()))
        {
            DeliverBillUpdateRequest changeInfo = new DeliverBillUpdateRequest();
            changeInfo.setBtypeId(online.getBtypeId());
            changeInfo.setPayBtypeId(online.getPayBtypeId());

            changeInfo.setProfileId(online.getProfileId());
            changeInfo.setProducer(ProducerTypeEnum.SALE_ORDER);
            changeInfo.setCustomer(CustomerTypeEnum.DELIVER_BILL);
            changeInfo.setSubChangeType(UpdateTypeEnum.PAY_BTYPE_ID_CHANGE);

            changeInfo.setChangeType(UpdateBillTypeEnum.BILL);
            changeInfo.setTradeOrderId(online.getTradeOrderId());
            return changeInfo;
        }
        return null;
    }
    private DeliverBillUpdateRequest getKtypeIdChange(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        if (!checkKtypeIdHasChange(local, online)) {
            return null;
        }
        BigInteger vchcode = local.getId();
        NotifyMainAndDetailChangeDto dto = new NotifyMainAndDetailChangeDto();
        dto.setVchcode(vchcode);
        dto.setTradeId(local.getTradeOrderId());
        dto.setKtypeId(online.getKtypeId());
        buildKtypeIdChangeDetail(dto, online);
        DeliverBillUpdateRequest info = buildBaseChangeInfo(local);
        info.setSubChangeType(UpdateTypeEnum.KTYPE_ID_CHANGE);
        info.setContent(JsonUtils.toJson(dto));
        info.setChangeType(UpdateBillTypeEnum.BILL);
        info.setCustomer(CustomerTypeEnum.DELIVER_BILL);
        return info;
    }

    public void doRefundFreightInterceptSaleorderNotify(List<EshopRefundFreight> freightInfo, BigInteger eshopOrderId) {
        if (freightInfo == null || freightInfo.isEmpty()) {
            return;
        }
        try {
            for (EshopRefundFreight refundFreight : freightInfo) {
                EshopSaleOrderFreight saleOrderFreight = refundFreight.toEshopSaleOrderFreight(eshopOrderId);
                EshopOrderFreightParam param = new EshopOrderFreightParam();
                param.setProfileId(refundFreight.getProfileId());
                param.setEshopOrderId(eshopOrderId);
                param.setFreightName(refundFreight.getFreightName());
                param.setFreightNo(refundFreight.getFreightNo());
                param.setFreightCode(refundFreight.getFreightCode());
                EshopSaleOrderFreight existFreight = saleOrderMapper.getEshopOrderFreight(param);
                if (existFreight != null) {
                    existFreight.setFreightInterceptStatus(saleOrderFreight.getFreightInterceptStatus());
                    saleOrderMapper.updateEshopOrderFreight(existFreight);
                } else {
                    if (StringUtils.isEmpty(saleOrderFreight.getFreightCode()) &&
                            StringUtils.isEmpty(saleOrderFreight.getFreightName()) &&
                            StringUtils.isEmpty(saleOrderFreight.getFreightNo())) {
                        continue;
                    }
                    saleOrderMapper.insertEshopOrderFreight(saleOrderFreight);
                }
            }
        } catch (Exception ex) {
            logger.error(String.format("更新原始订单物流拦截信息异常：%s", eshopOrderId), ex);
        }

    }

    public void doRefundFreightInterceptDeliverNotify(EshopRefundFreight freightInfo, String tradeId, BigInteger eshopOrderId, BigInteger otypeId) {
        try {
            DeliverBillUpdateRequest changeInfo = new DeliverBillUpdateRequest();
            changeInfo.setId(UId.newId());
            changeInfo.setProfileId(freightInfo.getProfileId());
            changeInfo.setTradeOrderId(tradeId);
            changeInfo.setEshopOrderId(eshopOrderId);
            changeInfo.setOtypeId(otypeId);
            changeInfo.setModifyTime(new Date());
            changeInfo.setOid("");
            changeInfo.setProducer(ProducerTypeEnum.REFUND_ORDER);
            com.wsgjp.ct.sale.biz.jarvis.dto.deliverbillupdate.FreightInfoDto freightNotifyInfo = new com.wsgjp.ct.sale.biz.jarvis.dto.deliverbillupdate.FreightInfoDto();
            freightNotifyInfo.setFreightCode(freightInfo.getFreightCode());
            freightNotifyInfo.setFreightName(freightInfo.getFreightName());
            freightNotifyInfo.setFreightBillNo(freightInfo.getFreightNo());
            freightNotifyInfo.setInterceptStatus(interceptNotifyMap.get(freightInfo.getFreightInterceptStatus().getCode()));
            changeInfo.setContent(JsonUtils.toJson(freightNotifyInfo));
            changeInfo.setSubChangeType(UpdateTypeEnum.FREIGHT_INFO_INTERCEPT);
            changeInfo.setChangeType(UpdateBillTypeEnum.BILL);
            changeInfo.setCustomer(CustomerTypeEnum.DELIVER_BILL);
            changeInfo.setEshopOrderId(eshopOrderId);
            notify(Collections.singletonList(changeInfo));
        } catch (Exception ex) {
            logger.error(String.format("tradeid:%s  doDeliverNotifyChange Error:%s", tradeId, ex.getMessage()), ex);
            throw ex;
        }

    }

    private DeliverBillUpdateRequest getGroupHearNameChange(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        boolean groupHeaderNameChange = local.getExtend().getGroupHeaderName().equals(online.getExtend().getGroupHeaderName());
        if (!groupHeaderNameChange) {
            DeliverBillUpdateRequest info = buildBaseChangeInfo(local);
            info.setContent(online.getExtend().getGroupHeaderName());
            info.setSubChangeType(UpdateTypeEnum.GROUP_HEADER_NAME_INFO);
            info.setChangeType(UpdateBillTypeEnum.BILL);
            info.setCustomer(CustomerTypeEnum.DELIVER_BILL);
            info.setEshopOrderId(local.getId());
            return info;
        }
        return null;
    }

    private DeliverBillUpdateRequest getConfirmInfoChange(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        boolean pickupCodeChange = !local.getExtend().getPickupCode().equals(online.getExtend().getPickupCode());
        boolean confirmStatusChange = !local.getExtend().getConfirmStatus().equals(online.getExtend().getConfirmStatus());
        boolean logisticsStatusChange = !local.getExtend().getLogisticsStatus().equals(online.getExtend().getLogisticsStatus());
        boolean platformDispatcherNameChange = !local.getExtend().getPlatformDispatcherName().equals(online.getExtend().getPlatformDispatcherName());
        boolean platformDispatherMobileChange = !local.getExtend().getPlatformDispatherMobile().equals(online.getExtend().getPlatformDispatherMobile());
        if (pickupCodeChange || confirmStatusChange || logisticsStatusChange || platformDispatcherNameChange || platformDispatherMobileChange) {
            DeliverBillUpdateRequest info = buildBaseChangeInfo(local);
            ConfirmInfo confirmInfo = new ConfirmInfo();
            confirmInfo.setConfirmStatus(online.getExtend().getConfirmStatus());
            confirmInfo.setLogisticsStatus(online.getExtend().getLogisticsStatus());
            confirmInfo.setPickupCode(online.getExtend().getPickupCode());
            confirmInfo.setPlatformDispatherMobile(online.getExtend().getPlatformDispatherMobile());
            confirmInfo.setPlatformDispatcherName(online.getExtend().getPlatformDispatcherName());
            info.setContent(JsonUtils.toJson(confirmInfo));
            info.setSubChangeType(UpdateTypeEnum.CONFIRM_INFO);
            info.setChangeType(UpdateBillTypeEnum.BILL);
            info.setCustomer(CustomerTypeEnum.DELIVER_BILL);
            info.setEshopOrderId(local.getId());
            return info;
        }
        return null;
    }

    public boolean checkOrderFreightListHasChange(EshopSaleOrderEntity local, EshopSaleOrderEntity online){
        if (null == local || null==online){
            return false;
        }
        List<EshopSaleOrderFreight> localFreights = local.getFreights();
        List<EshopSaleOrderFreight> onlineFreights = online.getFreights();
        orderDao.sortedFreights(localFreights);
        orderDao.sortedFreights(onlineFreights);
        boolean localEmpty = CollectionUtils.isEmpty(localFreights);
        boolean onlineEmpty = CollectionUtils.isEmpty(onlineFreights);
        if (localEmpty && onlineEmpty) {
            return false;
        }
        if (localEmpty || onlineEmpty) {
            return true;
        }
        String localCompareKey = Md5Utils.md5(JsonUtils.toJson(localFreights.stream().map(EshopSaleOrderFreight::getCompareKey).sorted().collect(Collectors.toList())));
        String onlineCompareKey = Md5Utils.md5(JsonUtils.toJson(onlineFreights.stream().map(EshopSaleOrderFreight::getCompareKey).sorted().collect(Collectors.toList())));
        return  !localCompareKey.equals(onlineCompareKey);
    }

    public DeliverBillUpdateRequest getFreightInfoChange(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        if (null == local && null == online){
            return null;
        }
        List<EshopOrderDetailFreight> localDetailFreightList = (null == local.getOrderDetails().stream().map(EshopSaleOrderDetail::getFreightList).flatMap(List::stream).collect(Collectors.toList()) ?
                new ArrayList<>() : local.getOrderDetails().stream().map(EshopSaleOrderDetail::getFreightList).flatMap(List::stream).collect(Collectors.toList())) ;
        List<EshopOrderDetailFreight> onlineDetailFreightList = (null == online.getOrderDetails().stream().map(EshopSaleOrderDetail::getFreightList).flatMap(List::stream).collect(Collectors.toList()) ?
                new ArrayList<>() : online.getOrderDetails().stream().map(EshopSaleOrderDetail::getFreightList).flatMap(List::stream).collect(Collectors.toList())) ;
        if ((CollectionUtils.isEmpty(localDetailFreightList) && CollectionUtils.isEmpty(onlineDetailFreightList)) || CollectionUtils.isEmpty(onlineDetailFreightList)){
            return null;
        }
        DeliverBillUpdateRequest info = buildBaseChangeInfo(local);
        info.setSubChangeType(UpdateTypeEnum.FREIGHT_INFO_CHANGE);
        info.setChangeType(UpdateBillTypeEnum.BILL);
        info.setCustomer(CustomerTypeEnum.DELIVER_BILL);
        info.setEshopOrderId(local.getId());
        List<FreightInfo> freightInfoList = new ArrayList<>();
        for (EshopOrderDetailFreight onlineFreight : onlineDetailFreightList) {
            String onlineHashKey = onlineFreight.getHashKey();
            boolean match = localDetailFreightList.stream().anyMatch(l -> l.getHashKey().equals(onlineHashKey));
            if (!match){
                FreightInfo freightInfo = new FreightInfo();
                freightInfo.setFreightCode(onlineFreight.getFreightCode());
                freightInfo.setFreightName(onlineFreight.getFreightName());
                freightInfo.setFreightBillNo(onlineFreight.getFreightBillNo());
                freightInfo.setTradeOrderDetailId(onlineFreight.getTradeOrderDetailId());
                freightInfoList.add(freightInfo);
            }
        }
        if (CollectionUtils.isEmpty(freightInfoList)){
            return null;
        }
        info.setContent(JsonUtils.toJson(freightInfoList));
        return info;
    }

    private static List<FreightInfo> buildFreightInfoList(List<EshopSaleOrderFreight> onlineFreights) {
        List<FreightInfo> freightInfos = new ArrayList<>();
        for (EshopSaleOrderFreight onlineFreight : onlineFreights) {
            FreightInfo freightInfo = new FreightInfo();
            freightInfo.setFreightCode(onlineFreight.getFreightCode());
            freightInfo.setFreightName(onlineFreight.getFreightName());
            freightInfo.setFreightBillNo(onlineFreight.getFreightNo());
            freightInfo.setInterceptStatus(onlineFreight.getFreightInterceptStatus().getCode());
            freightInfos.add(freightInfo);
        }
        return freightInfos;
    }

    private DeliverBillUpdateRequest getSignTimingChange(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        //签收时间
        Date localSignTime = local.getTiming().getSignTime();
        Date onlineSignTime = online.getTiming().getSignTime();
        boolean signTimeChange;

        if (localSignTime == null && onlineSignTime == null) {
            signTimeChange = false;
        } else {
            signTimeChange = null == localSignTime || null == onlineSignTime || DateUtils.compare(localSignTime, onlineSignTime) != 0;
        }

        if (signTimeChange) {
            return buildSaleOrderSignTimingChange(local, online);
        }
        return null;
    }

    public boolean checkTimingChange(Date localTime, Date onlineTime){
        if (null == localTime && null == onlineTime){
            return false;
        }
        return null == localTime || null == onlineTime || DateUtils.compare(localTime, onlineTime) != 0;
    }

    /**
     * 判断订单的时效是否发生变化
     * @param online
     * @param local
     * @param filed
     * @return boolean true--变化   false--没变化
     */
    public boolean checkOrderTimingChange(EshopSaleOrderEntity online, EshopSaleOrderEntity local,String filed) {
        try {
            if (StringUtils.isEmpty(filed)){
                return false;
            }
            if (null == online || null == local){
                return false;
            }
            EshopSaleOrderTiming onlineTiming = online.getTiming();
            EshopSaleOrderTiming localTiming = local.getTiming();
            Field field = EshopSaleOrderTiming.class.getDeclaredField(filed);
            if (null == field){
                return false;
            }
            field.setAccessible(true);
            Date onlineDate = (Date) field.get(onlineTiming);
            Date localDate = (Date) field.get(localTiming);
            if (null == onlineDate && null == localDate){
                return false;
            }
            if (null == onlineDate || null == localDate){
                return true;
            }
            return DateUtils.compare(onlineDate,localDate) != 0;
        }catch (Exception e){
            CommonUtil.doLogByEnable(logger,LogLevelEnum.ERROR,String.format("profileId:%s,build order log timing change error",CurrentUser.getProfileId()),e);
            return false;
        }
    }


    private DeliverBillUpdateRequest getTimingChange(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        //预计发货时间
        boolean planSendTimeChange = checkOrderTimingChange(online,local,"planSendTime");
        //预约签收/送达时间
        boolean planSignTimeChange = checkOrderTimingChange(online,local,"planSignTime");
        //最晚发货时间
        boolean sendTimeChange = checkOrderTimingChange(online,local,"sendTime");
        //最晚揽收时间
        boolean promisedCollectTimeChange = checkOrderTimingChange(online,local,"promisedCollectTime");
        //最晚签收时间
        boolean promisedSignTimeChange = checkOrderTimingChange(online,local,"promisedSignTime");
        //签收时间
        boolean signTimeChange = checkOrderTimingChange(online,local,"signTime");
        //最早签收时间
        boolean promisedSignStartTimeChange = checkOrderTimingChange(online,local,"promisedSignStartTime");
        //交易完成时间
        boolean tradeFinishTimeChange = !Objects.equals(local.getTradeFinishTime(), online.getTradeFinishTime());

        if (planSendTimeChange || planSignTimeChange || sendTimeChange ||
                promisedCollectTimeChange || promisedSignTimeChange || signTimeChange ||
                promisedSignStartTimeChange || tradeFinishTimeChange) {
            return buildSaleOrderTimingChange(local, online);
        }
        return null;
    }

    @NotNull
    private DeliverBillUpdateRequest buildSaleOrderTimingChange(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        DeliverBillUpdateRequest info = buildBaseChangeInfo(local);
        TimingInfoDto timingInfoDto = new TimingInfoDto();
        timingInfoDto.setPlanSendTime(online.getTiming().getPlanSendTime());
        timingInfoDto.setPlanSignTime(online.getTiming().getPlanSignTime());
        timingInfoDto.setSendTime(online.getTiming().getSendTime());
        timingInfoDto.setPromisedCollectTime(online.getTiming().getPromisedCollectTime());
        timingInfoDto.setPromisedSignTime(online.getTiming().getPromisedSignTime());
        timingInfoDto.setSignTime(online.getTiming().getSignTime());
        timingInfoDto.setPromisedSignStartTime(online.getTiming().getPromisedSignStartTime());
        timingInfoDto.setTradeFinishTime(online.getTradeFinishTime());
        info.setContent(JsonUtils.toJson(timingInfoDto));
        info.setSubChangeType(UpdateTypeEnum.MODIFY_TIMING);
        info.setChangeType(UpdateBillTypeEnum.BILL);
        info.setCustomer(CustomerTypeEnum.DELIVER_BILL);
        info.setEshopOrderId(local.getId());
        return info;
    }

    @NotNull
    private DeliverBillUpdateRequest buildSaleOrderSignTimingChange(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        DeliverBillUpdateRequest info = buildBaseChangeInfo(local);
        TimingInfoDto timingInfoDto = new TimingInfoDto();
        timingInfoDto.setSignTime(online.getTiming().getSignTime());
        info.setContent(JsonUtils.toJson(timingInfoDto));
        info.setSubChangeType(UpdateTypeEnum.SIGN_TIME);
        info.setChangeType(UpdateBillTypeEnum.BILL);
        info.setCustomer(CustomerTypeEnum.DELIVER_BILL);
        info.setEshopOrderId(local.getId());
        return info;
    }

    public void doNotifyAdvanceChange(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        advanceDao.doModifyAdvanceOrderFromOrder(local,online);
    }

    public void doNotifyRefundByOrderState(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        if (!checkOrderDetailRefundChange(local, online)){
            return;
        }
        List<RefundOrderDetailDAO> refundDetails = buildDetailRefundChange(online);
        if (CollectionUtils.isEmpty(refundDetails)) {
            return;
        }
        RefundOrderDAO dao = new RefundOrderDAO();
        dao.setRefundState(buildRefundStateEnum(online.getLocalRefundState()));
        dao.setSaleOrderVchcode(local.getId());
        dao.setTradeOrderId(local.getTradeOrderId());
        dao.setProfileId(local.getProfileId());
        dao.setDetails(refundDetails);
        dao.setVchcode(online.getId());
        List<RefundOrderDAO> refunds = new ArrayList<>();
        refunds.add(dao);
        notifyRefundChange(refunds);
    }

    private boolean checkOrderDetailRefundChange(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        List<EshopSaleOrderDetail> localDetails = local.getOrderDetails();
        List<EshopSaleOrderDetail> onlineDetails = online.getOrderDetails();
        if (CollectionUtils.isEmpty(localDetails) || CollectionUtils.isEmpty(onlineDetails)){
            return true;
        }
        for (EshopSaleOrderDetail localDetail : localDetails) {
            Optional<EshopSaleOrderDetail> first = onlineDetails.stream().filter(o -> o.getTradeOrderDetailId().equals(localDetail.getTradeOrderDetailId())).findFirst();
            if (!first.isPresent()){
                return true;
            }
            if (!first.get().getLocalRefundState().equals(localDetail.getLocalRefundState()) ||
            !first.get().getReSendState().equals(localDetail.getReSendState())){
                return true;
            }
        }
        return false;
    }

    /**
     * 特殊标记通知业务
     *
     * @param markUpdateDaoRequest
     */
    public void doNotifyOrderBillMarkSpecial(List<DeliverBillPlatformMarkUpdateDao> markUpdateDaoRequest) {
        if (null == markUpdateDaoRequest || markUpdateDaoRequest.isEmpty()) {
            return;
        }
        doInsertMarkChange(markUpdateDaoRequest, ProducerTypeEnum.REFUND_ORDER);
        GeneralResult<com.wsgjp.ct.sale.biz.jarvis.open.dto.DeliverBillUpdateResponse> response = deliverApi.updateBillPlatformMark(markUpdateDaoRequest);
        logger.info(String.format("特殊标记通知saleOrder Mark Change request:%s, response :%s",
                JsonUtils.toJson(markUpdateDaoRequest), JsonUtils.toJson(response.getData())));
    }

    /**
     * 极速退款成功后，标记原始订单和交易单是极速退款
     *
     * @param refundEntity
     */
    public void notifyFastRefundToOrder(EshopRefundEntity refundEntity) {

        try {
            List<EshopOrderMarkEntity> markEntityList = new ArrayList<>();
            EshopOrderMarkEntity entity = new EshopOrderMarkEntity();
            entity.setCreateType(MarkCreateType.SystemCalc);
            entity.setBubble(BaseOrderMarkEnum.FAST_REFUND.getBubble());
            entity.setId(UId.newId());
            entity.setMarkCode(BigInteger.valueOf(BaseOrderMarkEnum.FAST_REFUND.getCode()));
            entity.setCreateTime(DateUtils.getDate());
            entity.setMarkTarget(OrderMarkType.Main);
            entity.setOrderId(refundEntity.getEshopOrderId());
            entity.setOrderType(OrderSourceType.SaleOrder);
            entity.setProfileId(refundEntity.getProfileId());
            entity.setShowType(MarkShowType.All);
            markEntityList.add(entity);
            //生成的控制标记
            List<BaseOrderMarkEnum> mainMark = BaseOrderMarkEnum.buildControllerMarks(entity.getMarkCode().intValue());
            //构建控制标记的数据库实体
            List<EshopOrderMarkEntity> newMainMarkEntity = EShopOrderMarkUtil.buildEshopOrderMarkEntity(entity, mainMark);
            markEntityList.addAll(newMainMarkEntity);
            DeliverBillPlatformMarkUpdateDao markUpdateDao = new DeliverBillPlatformMarkUpdateDao();
            markUpdateDao.setTradeOrderId(refundEntity.getTradeOrderId());
            markUpdateDao.setVchcode(refundEntity.getEshopOrderId());
            markUpdateDao.setProfileId(refundEntity.getProfileId());
            List<DeliverNewMark> marks = new ArrayList<>();
            markEntityList.forEach(x -> {
                DeliverNewMark mark = new DeliverNewMark();
                mark.setBubble(x.getBubble());
                mark.setMarkCode(new BigInteger(String.valueOf(x.getMarkCode())));
                marks.add(mark);
            });
            markUpdateDao.setNewPlatformMarks(marks);
            doNotifyOrderBillMarkSpecial(Collections.singletonList(markUpdateDao));
            doNotifyEshopOrderMarkSpecial(markEntityList);
        } catch (Exception ex) {
            logger.error("标记极速退款异常", ex);
        }

    }

    /**
     * 特殊标记通知业务
     *
     * @param markUpdateDaoRequest
     */
    public void doNotifyEshopOrderMarkSpecial(List<EshopOrderMarkEntity> markUpdateDaoRequest) {
        orderDao.InsertEshopOrderMarkNotExist(markUpdateDaoRequest);
    }

    private void doNotifyOrderMark(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        if (local == null) {
            return;
        }
        //不需要通知的标记
        String noNeedNotifyOrderMarks = serviceConfig.getNoNeedNotifyOrderMarks();
        List<DeliverBillPlatformMarkUpdateDao> markUpdateDaoRequest = new ArrayList<>();
        DeliverBillPlatformMarkUpdateDao notifyMark = new DeliverBillPlatformMarkUpdateDao();
        notifyMark.setProfileId(online.getProfileId());
        notifyMark.setTradeOrderId(online.getTradeOrderId());
        notifyMark.setEshopOrderId(local.getId());
        notifyMark.setVchcode(local.getId());
        notifyMark.setTradeType(local.getOrderSaleType().getCode());
        notifyMark.setNewPlatformMarks(GetDifferList(noNeedNotifyOrderMarks, online.getOrderMarks(), local.getOrderMarks()));
        notifyMark.setDeletedPlatformMarks(GetDifferList(noNeedNotifyOrderMarks, local.getOrderMarks(), online.getOrderMarks()));
        doAddSpecialMark(notifyMark.getNewPlatformMarks(),online.getOrderMarks(),local.getOrderMarks());
        logger.error(String.format("标记通知,账套%s,订单%s,新增标记%s,删除标记%s", online.getProfileId(), online.getTradeOrderId(), JsonUtils.toJson(notifyMark.getNewPlatformMarks()), JsonUtils.toJson(notifyMark.getDeletedPlatformMarks())));

        AtomicBoolean hasDetailChange = new AtomicBoolean(false);
        List<DeliverBillDetailPlatformMarkUpdateDao> detailDeliverMarks = new ArrayList<>();
        Map<String, List<EshopSaleOrderDetail>> localOidMap = local.getOrderDetails().stream().collect(Collectors.groupingBy(EshopSaleOrderDetail::getTradeOrderDetailId));
        Map<String, List<EshopSaleOrderDetail>> onlineOidMap = online.getOrderDetails().stream().collect(Collectors.groupingBy(EshopSaleOrderDetail::getTradeOrderDetailId));
        onlineOidMap.forEach((key, value) -> {
            DeliverBillDetailPlatformMarkUpdateDao itemDetailDeliverMarks = new DeliverBillDetailPlatformMarkUpdateDao();
            List<EshopOrderMarkEntity> onlineItemMarks = value.get(0).getOrderDetailMarks();
            List<EshopOrderMarkEntity> localItemMarks = localOidMap.containsKey(key) ? localOidMap.get(key).get(0).getOrderDetailMarks() : null;
            itemDetailDeliverMarks.setNewPlatformMarks(GetDifferList(noNeedNotifyOrderMarks, onlineItemMarks, localItemMarks));
            itemDetailDeliverMarks.setDeletedPlatformMarks(GetDifferList(noNeedNotifyOrderMarks, localItemMarks, onlineItemMarks));
            itemDetailDeliverMarks.setOid(key);
            itemDetailDeliverMarks.setTradeOrderId(online.getTradeOrderId());
            doAddSpecialMark(itemDetailDeliverMarks.getNewPlatformMarks(),onlineItemMarks,localItemMarks);
            //过滤KTT_PARTIAL_REFUND
            Optional<EshopOrderMarkEntity> first = onlineItemMarks.stream().filter(o -> 90870003 == o.getMarkCode().intValue()).findFirst();
            first.ifPresent(eshopOrderMarkEntity -> itemDetailDeliverMarks.setComment(value.get(0).getKttComment()));
            if (!itemDetailDeliverMarks.getNewPlatformMarks().isEmpty() || !itemDetailDeliverMarks.getDeletedPlatformMarks().isEmpty()) {
                hasDetailChange.set(true);
            }
            detailDeliverMarks.add(itemDetailDeliverMarks);
        });
        notifyMark.setDetails(detailDeliverMarks);
        markUpdateDaoRequest.add(notifyMark);
        if (notifyMark.getNewPlatformMarks().isEmpty()
                && notifyMark.getDeletedPlatformMarks().isEmpty()
                && !hasDetailChange.get()) {
            return;
        }
        doInsertMarkChange(markUpdateDaoRequest, ProducerTypeEnum.SALE_ORDER);
        GeneralResult<com.wsgjp.ct.sale.biz.jarvis.open.dto.DeliverBillUpdateResponse> response = deliverApi.updateBillPlatformMark(markUpdateDaoRequest);
        logger.error(String.format("saleOrder Mark Change request:%s, response :%s",
                JsonUtils.toJson(markUpdateDaoRequest), JsonUtils.toJson(response.getData())));
    }

    public void doAddSpecialMark(List<DeliverNewMark> newPlatformMarks, List<EshopOrderMarkEntity> online, List<EshopOrderMarkEntity> local) {
        if (CollectionUtils.isEmpty(online)){
            return;
        }
        boolean onlineHas = online.stream().anyMatch(n -> BigInteger.valueOf(BaseOrderMarkEnum.DD_BIC.getCode()).compareTo(n.getMarkCode()) == 0);
        boolean localHasNot = (null == local ||  local.stream().noneMatch(n -> BigInteger.valueOf(BaseOrderMarkEnum.DD_BIC.getCode()).compareTo(n.getMarkCode()) == 0));
        if (onlineHas && localHasNot){
            DeliverNewMark deliverMark = new DeliverNewMark();
            deliverMark.setMarkCode(BigInteger.valueOf(BaseOrderMarkEnum.PLATFORM_LOCK_LOCKED_MANUAL.getCode()));
            deliverMark.setBubble("订单从普通变更成QIC业务，存在发货错误风险，建议还原后重新处理");
            newPlatformMarks.add(deliverMark);
        }
    }

    public void doInsertMarkChange(List<DeliverBillPlatformMarkUpdateDao> markUpdates, ProducerTypeEnum producerType) {
        List<DeliverBillUpdateRequest> changeInfoList = new ArrayList<>();
        for (DeliverBillPlatformMarkUpdateDao mark : markUpdates) {
            DeliverBillUpdateRequest changeInfo = new DeliverBillUpdateRequest();
            changeInfo.setProfileId(mark.getProfileId());
            changeInfo.setEshopOrderId(mark.getVchcode());
            changeInfo.setModifyTime(new Date());
            changeInfo.setId(UId.newId());
            changeInfo.setTradeOrderId(mark.getTradeOrderId());
            changeInfo.setOtypeId(BigInteger.ZERO);
            changeInfo.setModifyTime(new Date());
            changeInfo.setContent(JSONObject.toJSONString(mark));
            changeInfo.setChangeType(UpdateBillTypeEnum.BILL);
            changeInfo.setCustomer(CustomerTypeEnum.DELIVER_BILL);
            changeInfo.setProducer(null == producerType ? ProducerTypeEnum.SALE_ORDER : producerType);
            changeInfo.setOid("");
            changeInfo.setSubChangeType(UpdateTypeEnum.MARK_CHANGE);
            changeInfoList.add(changeInfo);
        }
        doInsertChange(changeInfoList);
    }


    public List<DeliverNewMark> GetDifferList(String noNeedNotifyOrderMarks, List<EshopOrderMarkEntity> originalList,
                                              List<EshopOrderMarkEntity> targetList) {
        List<DeliverNewMark> deliverMarkList = new ArrayList<>();
        if (originalList == null || originalList.isEmpty()) {
            return deliverMarkList;
        }
        List<EshopOrderMarkEntity> diffMarkList = new ArrayList<>();
        originalList.forEach(x -> {
            String bigDataMd5Key = CommonUtil.getBigDataMd5Key(x.getBigData());
            if (targetList != null && !targetList.isEmpty() &&
                    //判断bigData变更
                    (targetList.stream().anyMatch(y -> y.getMarkCode().compareTo(x.getMarkCode()) == 0 && bigDataMd5Key.equals(CommonUtil.getBigDataMd5Key(y.getBigData()))))) {
                return;
            }
            diffMarkList.add(x);
        });

        if (diffMarkList.isEmpty()) {
            return deliverMarkList;
        }

        List<String> noNeedNotifyList = new ArrayList<>();
        if (StringUtils.isNotEmpty(noNeedNotifyOrderMarks)) {
            noNeedNotifyList = Arrays.stream(noNeedNotifyOrderMarks.split(",")).collect(Collectors.toList());
        }

        for (EshopOrderMarkEntity orderMark : diffMarkList) {
            if (noNeedNotifyList.contains(String.valueOf(orderMark.getMarkCode()))) {
                continue;
            }
            DeliverNewMark deliverMark = new DeliverNewMark();
            deliverMark.setMarkCode(orderMark.getMarkCode());
            deliverMark.setBubble(orderMark.getBubble());
            deliverMark.setMarkDataId(orderMark.getMarkDataId());
            //这里try的作用是：接口组传入的markData可能不符合BaseMarkBigDataEntity的结构，发现了需要让他们修改
            try {
                deliverMark.setBaseMarkBigData(JsonUtils.toObject(orderMark.getBigData(), BaseMarkBigDataEntity.class));
            } catch (Exception e) {
                deliverMark.setBaseMarkBigData(null);
                CommonUtil.doLogByEnable(logger, LogLevelEnum.ERROR, "convert bigData error：" + JsonUtils.toJson(orderMark), e);
            }
            deliverMarkList.add(deliverMark);
        }
        return deliverMarkList;
    }

    private RefundStateEnum buildRefundStateEnum(ReturnState status) {
        switch (status) {
            case SECTION_REFUND_SUCCESS:
                return RefundStateEnum.FINISH_PART;
            case SECTION_REFUNDING:
                return RefundStateEnum.REFUNDING_PART;
            case SUCCESS:
                return RefundStateEnum.FINISH;
            case REFUNDING:
                return RefundStateEnum.REFUNDING;
            default:
                return RefundStateEnum.NO_REFUND;
        }
    }

    private RefundStatusEnum buildRefundStatusEnum(ReturnState status) {
        switch (status) {
            case NONE:
                return RefundStatusEnum.NO_REFUND;
            case REFUNDING:
                return RefundStatusEnum.REFUNDING;
            case SUCCESS:
                return RefundStatusEnum.FINISH;
            default:
                return RefundStatusEnum.NO_REFUND;
        }
    }

    private List<RefundOrderDetailDAO> buildDetailRefundChange(EshopSaleOrderEntity online) {
        List<RefundOrderDetailDAO> returnDetails = new ArrayList<>();
        List<EshopSaleOrderDetail> details = online.getOrderDetails();
        if (details == null || details.size() == 0) {
            return returnDetails;
        }
        for (EshopSaleOrderDetail itemDetail : details) {
            if (itemDetail.getProcessState().compareTo(DetailProcessState.NoSubmit) == 0) {
                continue;
            }
            RefundOrderDetailDAO changeDetail = new RefundOrderDetailDAO();
            changeDetail.setVchcode(itemDetail.getEshopOrderId());
            changeDetail.setId(BigInteger.ZERO);
            changeDetail.setProfileId(itemDetail.getProfileId());
            changeDetail.setOid(itemDetail.getTradeOrderDetailId());
            changeDetail.setTradeOrderId(online.getTradeOrderId());
            changeDetail.setOrderDetailId(itemDetail.getId());
            changeDetail.setRefundState(buildRefundStatusEnum(itemDetail.getLocalRefundState()));
            changeDetail.setReSendState(itemDetail.getReSendState());
            returnDetails.add(changeDetail);
        }
        return returnDetails;
    }


    public void doNotifyOrderClose(List<EshopSaleOrderEntity> orders) {
        List<DeliverBillUpdateRequest> changeInfoList = new ArrayList<>();
        for (EshopSaleOrderEntity order : orders) {
            BigInteger vchcode = order.getId();
            NotifyMainAndDetailChangeDto dto = new NotifyMainAndDetailChangeDto();
            dto.setTradeState(TradeStatus.ALL_CLOSED);
            dto.setVchcode(vchcode);
            dto.setPayTime(null);
            dto.setTradeId(order.getTradeOrderId());
            QueryOrderDetailParameter parameter = new QueryOrderDetailParameter();
            parameter.setEshopOrderId(vchcode);
            List<EshopSaleOrderDetail> details = orderDao.getSimpleDetails(parameter);
            orderDao.doMarkStockDeleteNew(order);
            List<DeliverBillDetailTradeStatusDTO> dtoDetails = new ArrayList<>();
            for (EshopSaleOrderDetail detail : details) {
                DeliverBillDetailTradeStatusDTO detailDto = new DeliverBillDetailTradeStatusDTO();
                detailDto.setTradeState(TradeStatus.ALL_CLOSED);
                detailDto.setOid(detail.getTradeOrderDetailId());
                detailDto.setOrderDetailId(detail.getId());
                dtoDetails.add(detailDto);
            }
            dto.setDetails(dtoDetails);
            DeliverBillUpdateRequest info = buildBaseChangeInfo(order);
            info.setSubChangeType(UpdateTypeEnum.TRADE_STATE);
            info.setContent(JsonUtils.toJson(dto));
            info.setChangeType(UpdateBillTypeEnum.BILL);
            info.setCustomer(CustomerTypeEnum.DELIVER_BILL);
            changeInfoList.add(info);
        }
        GeneralResult<DeliverBillUpdateResult> notify = deliverApi.notify(changeInfoList);
        if (notify.getCode() != 200) {
            logger.error(String.format("通知发货单变更报错：%s", notify.getMessage()));
        }
        doInsertChange(changeInfoList);
    }

    private void doInsertChange(List<DeliverBillUpdateRequest> changeInfoList) {
        for (DeliverBillUpdateRequest changeInfo : changeInfoList) {
            try {
                mapper.insertChangeInfo(changeInfo);
            } catch (Exception ex) {
                logger.error(String.format("保存订单【%s】变更数据报错：%s", changeInfo.getTradeOrderId(), ex.getMessage()), ex);
            }
        }
    }

    public void receiveRefundNotify(List<EshopSaleOrderEntity> orders, List<RefundOrderDAO> refunds) {
        try {
            if (CollectionUtils.isEmpty(refunds)) {
                return;
            }
            //  异步通知交易单
            List<RefundOrderDAO> delivers = refunds.stream().filter(item -> {
                return !item.getDetails().isEmpty() || FastRefundTypeEnum.CASH.equals(item.getFastRefundTypeEnum());
            }).collect(Collectors.toList());
            notifyRefundChange(delivers);
            //  回写原始订单售后状态
            for (EshopSaleOrderEntity order : orders) {
                List<EshopSaleOrderDetail> orderDetails = order.getOrderDetails();
                for (EshopSaleOrderDetail orderDetail : orderDetails) {
                    List<EshopOrderMarkEntity> orderDetailMarks = orderDetail.getOrderDetailMarks();
                    for (EshopOrderMarkEntity orderDetailMark : orderDetailMarks) {
                        orderDetailMark.setId(UId.newId());
                    }
                }
            }
            batchSaveOrderRefundState(orders);
        } catch (RuntimeException ex) {
            String format = String.format("通知标记售后报错：%s", ex.getMessage());
            logger.error(format, ex);
            throw new RuntimeException(format, ex);
        }
    }

    private String getKey(String tradeOrderId, String type) {
        return tradeOrderId + type;
    }

    private void doInsertRefundChange(List<RefundOrderDAO> refunds) {
        List<DeliverBillUpdateRequest> changeInfoList = new ArrayList<>();
        for (RefundOrderDAO refund : refunds) {
            DeliverBillUpdateRequest changeInfo = new DeliverBillUpdateRequest();
            changeInfo.setProfileId(refund.getProfileId());
            changeInfo.setEshopOrderId(refund.getVchcode());
            changeInfo.setModifyTime(new Date());
            changeInfo.setId(UId.newId());
            changeInfo.setTradeOrderId(refund.getTradeOrderId());
            changeInfo.setOtypeId(BigInteger.ZERO);
            changeInfo.setModifyTime(new Date());
            changeInfo.setSubChangeType(UpdateTypeEnum.REFUND);
            changeInfo.setContent(JsonUtils.toJson(refund));
            changeInfo.setChangeType(UpdateBillTypeEnum.BILL);
            changeInfo.setCustomer(CustomerTypeEnum.DELIVER_BILL);
            changeInfo.setProducer(ProducerTypeEnum.REFUND_ORDER);
            changeInfo.setOid("");
            changeInfoList.add(changeInfo);
        }
        doInsertChange(changeInfoList);
    }

    public void receiveSaleOrderQtyChangeNotify(EshopSaleOrderEntity order, QtyChangeSourceType sourceType) {
        try {

            markSaleQtyChangeNew(order, sourceType);
        } catch (Exception ex) {
            logger.error(String.format("帐套%d,订单【%s】,操作【%s】原单标记库存变更报错：%s",
                    order.getProfileId(),
                    order.getTradeOrderId(),
                    sourceType.getName(),
                    ex.getMessage()), ex);
        }
    }

    public void notifyRefundChange(List<RefundOrderDAO> refunds) {
        GeneralResult<DeliverNotifyResponse> notify = new GeneralResult<>();
        if (refunds == null || refunds.isEmpty()) {
            return;
        }
        ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.ORDER_NOTIFY);
        threadPool.executeAsync(itemRefundList -> {
            doInsertRefundChange(itemRefundList);
            //busHelperService.pushRefundNoticeDeliverTask(itemRefundList);
            GeneralResult<RefundUpdateResponse> result = deliverApi.refundUpdate(itemRefundList);
            if (result.getCode() != 200) {
                String format = String.format("通知发货单标记售后报错：%s", notify.getMessage());
                logger.error(format);
            }
        }, refunds);
    }

    public void batchSaveOrderRefundState(List<EshopSaleOrderEntity> orders) {
        if (orders == null || orders.isEmpty()) {
            return;
        }
        //需求  业务总线，暂时先不接入，使用以前的方法直接处理
        //原因：原始订单通知用的saleorderEntity，实体太大担心总线出问题；改实体代价偏高，暂时不处理
        //busHelperService.pushRefundNoticeSaleOrderTask(orders);// 这行代码请不要删除
        for (EshopSaleOrderEntity order : orders) {
            EshopSaleOrderNotifyService notifyS = (EshopSaleOrderNotifyService) AopContext.currentProxy();
            notifyS.SaveOrderRefundState(order);
        }
    }

    private void doSaveMainAdvanceRefundMark(EshopAdvanceOrderEntity advance, EshopSaleOrderEntity order) {
        EshopOrderMarkParameter parameter = new EshopOrderMarkParameter();
        parameter.setEshopOrderIds(Collections.singletonList(advance.getVchcode()));
        parameter.setMarkTarget(0);
        List<BigInteger> markCodes = new ArrayList<>();
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.REFUND.getCode()));
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.REFUND_RETURN.getCode()));
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.NO_SEND_PART_REFUND.getCode()));
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.NO_SEND_FULL_REFUND.getCode()));
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.EXCHANGING_REPLENISHING.getCode()));
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.EXCHANGED_REPLENISHED.getCode()));
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.NO_SEND_ONLY_REFUND.getCode()));
        parameter.setMarkCodes(markCodes);
        parameter.setCreateType(MarkCreateType.SystemCalc);
        parameter.setProfileId(CurrentUser.getProfileId());
        orderDao.deleteEshopOrderMark(parameter);
        List<EshopOrderMarkEntity> list = new ArrayList<>();
        if (order.getMarks() != null) {
            for (BaseOrderMarkEnum mark : order.getMarks()) {
                EshopOrderMarkEntity entity = new EshopOrderMarkEntity();
                entity.setId(UId.newId());
                entity.setOrderId(advance.getVchcode());
                entity.setBubble(mark.getBubble());
                entity.setMarkCode(BigInteger.valueOf(mark.getCode()));
                entity.setMarkTarget(OrderMarkType.Main);
                entity.setProfileId(CurrentUser.getProfileId());
                entity.setOrderType(OrderSourceType.Advance);
                entity.setMarkUseType(MarkUseType.SystemMark);
                entity.setCreateType(MarkCreateType.SystemCalc);
                list.add(entity);
            }
        }
        orderDao.insertEshopOrderMark(list);
    }


    private void buildAdvanceRefundMark(EshopSaleOrderDetail newItemDetail, EshopAdvanceOrderDetail oldItemDetail) {
        List<EshopOrderMarkEntity> detailMark = newItemDetail.getOrderDetailMarks();
        if (detailMark.stream().anyMatch(x -> BaseOrderMarkEnum.NO_SEND_FULL_REFUND.equals(x.getMarkCode()))) {
            newItemDetail.setDeliverRequired(false);
            EShopOrderMarkUtil.doBuildAndAddToMarkList(detailMark, oldItemDetail.getVchcode(),
                    oldItemDetail.getId(), oldItemDetail.getProfileId(), BaseOrderMarkEnum.CANT_SEND_GOODS);
        } else {
            newItemDetail.setDeliverRequired(true);
        }
        for (EshopOrderMarkEntity itemMark : detailMark
        ) {
            itemMark.setCreateType(MarkCreateType.SystemCalc);
            itemMark.setMarkTarget(OrderMarkType.Detail);
            itemMark.setOrderType(OrderSourceType.Advance);
            itemMark.setDetailId(oldItemDetail.getDetailId());
            itemMark.setOrderId(oldItemDetail.getVchcode());
            itemMark.setProfileId(CurrentUser.getProfileId());
            itemMark.setId(UId.newId());
        }
        oldItemDetail.setMarks(detailMark);
    }

    private void updateAdvanceRefund(EshopSaleOrderEntity order) {
        QueryAdvanceOrderParameter parameter = new QueryAdvanceOrderParameter();
        parameter.setProfileId(CurrentUser.getProfileId());
        parameter.setSaleOrderId(order.getId());
        List<EshopAdvanceOrderEntity> list = advanceDao.querySimpleEshopAdvanceOrderList(parameter);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<EshopSaleOrderDetail> details = order.getOrderDetails();
        if (details == null || details.size() == 0) {
            return;
        }
        for (EshopAdvanceOrderEntity entity : list) {
            doSaveMainAdvanceRefundMark(entity, order);
            QueryAdvanceDetailParameter detailParameter = new QueryAdvanceDetailParameter();
            detailParameter.setEshopOrderId(entity.getVchcode());
            List<EshopAdvanceOrderDetail> oldDetails = advanceDao.querySimpleAdvanceDetails(detailParameter);
            entity.setDetail(oldDetails);
            for (EshopAdvanceOrderDetail detailItem : oldDetails) {
                List<EshopSaleOrderDetail> newDetails = details.stream().filter(x -> x.getTradeOrderDetailId().equals(detailItem.getPlatform().getTradeOrderDetailId())).collect(Collectors.toList());
                if (newDetails.isEmpty()) {
                    continue;
                }
                EshopSaleOrderDetail newItemDetail = newDetails.get(0);
                detailItem.getPlatform().setLocalRefundState(newItemDetail.getLocalRefundState());
                detailItem.getPlatform().setLocalRefundProcessState(newItemDetail.getLocalRefundProcessState());
                detailItem.getPlatform().setReSendState(newItemDetail.getReSendState());
                buildAdvanceRefundMark(newItemDetail, detailItem);
                mapper.modifyAdvanceDetailState(detailItem);
                doSaveOrderRefundMark(detailItem);
            }
            entity.getPlatform().setLocalRefundState(order.getLocalRefundState());
            entity.getPlatform().setReSendState(order.getReSendState());
            advanceDao.buildOrderRefundMentionType(entity);
            mapper.modifyAdvanceState(entity);
            advanceDao.doMarkStockAdd(entity);
            String logComment = "成功，更新数据：退款状态：" + entity.getPlatform().getLocalRefundState().getName();
            SysLogUtil.add(SysLogUtil.buildAdvanceOrderLog(entity, AdvanceOrderOperateTypeEnum.UPDATE_ADVANCE_REFUND, logComment));
            ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.ORDER_RELATION);
            threadPool.executeAsync(SaleOrderExStatusAsyncHandleHelper::notify, entity.getVchcode());
        }
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void SaveOrderRefundState(EshopSaleOrderEntity order) {
        if (order == null) {
            return;
        }
        //更新原始订单售后状态
        doSaveAndSaveState(order);
        //更新预售单售后状态
        updateAdvanceRefund(order);
        //更新原始订单周期购主单售后状态
        doBuildAndSaveCycleMainOrderState(order);
        //更新预售单周期购主单售后状态
        doBuildAndSaveCycleMainAdvanceState(order);
    }

    private void doBuildAndSaveCycleMainAdvanceState(EshopSaleOrderEntity order) {
        if (null == order || TradeTypeEnum.CYCLE_PURCHASE != order.getOrderSaleType() ||
                StringUtils.isEmpty(order.getPlatformParentOrderId())){
            return;
        }
        EshopAdvanceSaleOrderMapper advanceMapper = GetBeanUtil.getBean(EshopAdvanceSaleOrderMapper.class);
        EshopAdvanceSaleOrderService advaceService = GetBeanUtil.getBean(EshopAdvanceSaleOrderService.class);
        QueryAdvanceOrderParameter subParam = new QueryAdvanceOrderParameter();
        subParam.setOtypeIds(Collections.singletonList(order.getOtypeId()));
        subParam.setPlatformParentOrderIds(Collections.singletonList(order.getPlatformParentOrderId()));
        subParam.setQueryCyclePurchaseType(1);
        //获取所有子单
        List<EshopAdvanceOrderEntity> cyclePurchaseOrders = advanceMapper.queryAllAdvanceOrderFields(subParam);
        if (CollectionUtils.isEmpty(cyclePurchaseOrders)){
            return;
        }
        advanceDao.BuildEshopOrderMainMark(cyclePurchaseOrders,false);
        QueryAdvanceDetailParameter querySubOrderDetailParameter = new QueryAdvanceDetailParameter();
        querySubOrderDetailParameter.setEshopOrderIds(cyclePurchaseOrders.stream().map(EshopAdvanceOrderEntity::getVchcode).distinct().collect(Collectors.toList()));
        List<EshopAdvanceOrderDetail> allSubDetail = advaceService.quereyAllSaleOrderDetailFields(querySubOrderDetailParameter);
        QueryAdvanceOrderParameter mainParam = new QueryAdvanceOrderParameter();
        mainParam.setOtypeIds(Collections.singletonList(order.getOtypeId()));
        mainParam.setTradeOrderId(order.getPlatformParentOrderId());
        mainParam.setQueryCyclePurchaseType(0);
        //获取主单
        List<EshopAdvanceOrderEntity> cyclePurchaseMainOrderList = advanceMapper.queryAllAdvanceOrderFields(mainParam);
        advanceDao.BuildEshopOrderMainMark(cyclePurchaseMainOrderList,false);
        if (CollectionUtils.isEmpty(cyclePurchaseMainOrderList)){
            return;
        }
        EshopAdvanceOrderEntity mainOrder = cyclePurchaseMainOrderList.get(0);
        QueryAdvanceDetailParameter queryOrderDetailParameter = new QueryAdvanceDetailParameter();
        queryOrderDetailParameter.setEshopOrderIds(Collections.singletonList(mainOrder.getVchcode()));
        List<EshopAdvanceOrderDetail> mainDetails = advaceService.quereyAllSaleOrderDetailFields(queryOrderDetailParameter);
        mainOrder.setDetail(mainDetails);
        if (CollectionUtils.isEmpty(allSubDetail)){
            return;
        }
        Map<String, List<EshopAdvanceOrderDetail>> map = allSubDetail.stream().collect(Collectors.groupingBy(a->a.getPlatform().getTradeOrderDetailId()));
        for (Map.Entry<String, List<EshopAdvanceOrderDetail>> entry : map.entrySet()) {
            String tradeOrderDetailId = entry.getKey();
            List<EshopAdvanceOrderDetail> details = entry.getValue();
            if (StringUtils.isEmpty(tradeOrderDetailId) || CollectionUtils.isEmpty(details)){
                continue;
            }
            List<EshopAdvanceOrderDetail> orderDetails = mainDetails.stream().filter(m -> m.getPlatform().getTradeOrderDetailId().equals(tradeOrderDetailId)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orderDetails)){
                continue;
            }
            //计算主单明细的退款和换补状态
            EshopAdvanceOrderEntity temp = new EshopAdvanceOrderEntity();
            temp.setPlatform(new EshopAdvanceOrderPlaform());
            temp.setDetail(details);
            advanceDao.buildOrderRefundMentionType(temp);
            List<EshopOrderMarkEntity> detailMarkList = details.stream().map(d -> d.getMarks()).flatMap(List::stream).collect(Collectors.toList());
            for (EshopAdvanceOrderDetail detail : orderDetails) {
                detail.getPlatform().setLocalRefundState(temp.getPlatform().getLocalRefundState());
                detail.getPlatform().setReSendState(temp.getPlatform().getReSendState());
                mapper.modifyAdvanceDetailState(detail);
                if (CollectionUtils.isEmpty(detailMarkList)){
                    continue;
                }
                List<EshopOrderMarkEntity> newMarkList = new ArrayList<>();
                List<BigInteger> markCodes = detailMarkList.stream().map(EshopOrderMarkEntity::getMarkCode).distinct().collect(Collectors.toList());
                for (BigInteger markCode : markCodes) {
                    if (!isRefundMark(markCode)){
                        continue;
                    }
                    EshopOrderMarkEntity newMark = new EshopOrderMarkEntity();
                    newMark.setId(UId.newId());
                    newMark.setCreateType(MarkCreateType.SystemCalc);
                    newMark.setMarkTarget(OrderMarkType.Detail);
                    newMark.setOrderType(OrderSourceType.Advance);
                    newMark.setDetailId(detail.getDetailId());
                    newMark.setOrderId(detail.getVchcode());
                    newMark.setProfileId(CurrentUser.getProfileId());
                    newMark.setMarkCode(markCode);
                    newMarkList.add(newMark);
                }
                detail.setMarks(newMarkList);
                doSaveOrderRefundMark(detail);
            }
        }
        //计算主单
        advanceDao.buildOrderRefundMentionType(mainOrder);
        List<EshopAdvanceOrderEntity> localAdvance = advanceMapper.queryAllAdvanceOrderFields(mainParam);
        if (localAdvance != null && null != localAdvance.get(0) && !localAdvance.get(0).getPlatform().getLocalRefundState().equals(mainOrder.getPlatform().getLocalRefundState())) {
            SysLogUtil.add(SysLogUtil.buildAdvanceOrderLog(mainOrder, AdvanceOrderOperateTypeEnum.REFUND_NOTIFY, String.format("原售后状态 %s,新售后状态 %s",
                    localAdvance.get(0).getPlatform().getLocalRefundState().getName(), mainOrder.getPlatform().getLocalRefundState().getName())));
        }
        if (localAdvance != null && null != localAdvance.get(0) && !localAdvance.get(0).getPlatform().getReSendState().equals(mainOrder.getPlatform().getReSendState())) {
            SysLogUtil.add(SysLogUtil.buildAdvanceOrderLog(mainOrder, AdvanceOrderOperateTypeEnum.REFUND_NOTIFY, String.format("原换/补状态 %s,新换/补状态 %s",
                    localAdvance.get(0).getPlatform().getReSendState().getName(), mainOrder.getPlatform().getReSendState().getName())));
        }
        mapper.modifyAdvanceState(mainOrder);
        List<EshopOrderMarkEntity> allSubMarks = cyclePurchaseOrders.stream().map(EshopAdvanceOrderEntity::getMarks).flatMap(List::stream).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allSubMarks)){
            return;
        }
        List<BigInteger> allSubMarkCodes = allSubMarks.stream().map(EshopOrderMarkEntity::getMarkCode).distinct().collect(Collectors.toList());
        List<BaseOrderMarkEnum> newMainMarkList = new ArrayList<>();
        for (BigInteger markCode : allSubMarkCodes) {
            if (!isRefundMark(markCode)){
                continue;
            }
            newMainMarkList.add(BaseOrderMarkEnum.getBaseOrderMarkEnumByCode(markCode));
        }
        EshopSaleOrderEntity temp = new EshopSaleOrderEntity();
        temp.setMarks(newMainMarkList);
        doSaveMainAdvanceRefundMark(localAdvance.get(0),temp);
    }

    private void doBuildAndSaveCycleMainOrderState(EshopSaleOrderEntity order) {
        if (null == order || TradeTypeEnum.CYCLE_PURCHASE != order.getOrderSaleType() ||
                StringUtils.isEmpty(order.getPlatformParentOrderId())){
            return;
        }
        EshopSaleOrderService orderService = GetBeanUtil.getBean(EshopSaleOrderService.class);
        QueryOrderParameter subParam = new QueryOrderParameter();
        subParam.setOtypeIds(Collections.singletonList(order.getOtypeId()));
        subParam.setPlatformParentOrderIds(Collections.singletonList(order.getPlatformParentOrderId()));
        subParam.setQueryCyclePurchaseMainOrder(false);
        //获取所有子单
        List<EshopSaleOrderEntity> cyclePurchaseOrders = orderService.getCyclePurchaseSubOrders(subParam);
        orderDao.BuildEshopOrderMainMark(cyclePurchaseOrders, false);
        QueryOrderParameter mainParam = new QueryOrderParameter();
        mainParam.setOtypeIds(Collections.singletonList(order.getOtypeId()));
        mainParam.setTradeOrderIds(Collections.singletonList(order.getPlatformParentOrderId()));
        mainParam.setQueryCyclePurchaseMainOrder(true);
        //获取主单
        List<EshopSaleOrderEntity> cyclePurchaseMainOrderList = saleOrderMapper.queryAllSaleOrderFields(mainParam);
        if (CollectionUtils.isEmpty(cyclePurchaseOrders) || CollectionUtils.isEmpty(cyclePurchaseMainOrderList)){
            return;
        }
        EshopSaleOrderEntity mainOrder = cyclePurchaseMainOrderList.get(0);
        QueryOrderDetailParameter queryOrderDetailParameter = new QueryOrderDetailParameter();
        queryOrderDetailParameter.setEshopOrderIds(Collections.singletonList(mainOrder.getId()));
        List<EshopSaleOrderDetail> mainDetails = orderService.queryOrderDetails(queryOrderDetailParameter);
        mainOrder.setOrderDetails(mainDetails);
        List<EshopSaleOrderDetail> allSubDetail = cyclePurchaseOrders.stream().map(EshopSaleOrderEntity::getOrderDetails).flatMap(List::stream).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allSubDetail)){
            return;
        }
        EshopSaleOrderBuilder builder = EshopSaleOrderBuilder.getSingleton();
        Map<String, List<EshopSaleOrderDetail>> map = allSubDetail.stream().collect(Collectors.groupingBy(EshopSaleOrderDetail::getTradeOrderDetailId));
        for (Map.Entry<String, List<EshopSaleOrderDetail>> entry : map.entrySet()) {
            String tradeOrderDetailId = entry.getKey();
            List<EshopSaleOrderDetail> details = entry.getValue();
            if (StringUtils.isEmpty(tradeOrderDetailId) || CollectionUtils.isEmpty(details)){
                continue;
            }
            List<EshopSaleOrderDetail> orderDetails = mainDetails.stream().filter(m -> m.getTradeOrderDetailId().equals(tradeOrderDetailId)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(orderDetails)){
                continue;
            }
            //计算主单明细的退款和换补状态
            EshopSaleOrderEntity temp = new EshopSaleOrderEntity();
            temp.setOrderDetails(details);
            builder.buildOrderRefundMentionType(temp);
            List<EshopOrderMarkEntity> detailMarkList = details.stream().map(EshopSaleOrderDetail::getOrderDetailMarks).flatMap(List::stream).collect(Collectors.toList());
            for (EshopSaleOrderDetail detail : orderDetails) {
                detail.setLocalRefundState(temp.getLocalRefundState());
                detail.setReSendState(temp.getReSendState());
                mapper.modifyDetailState(detail);
                if (CollectionUtils.isEmpty(detailMarkList)){
                    continue;
                }
                List<EshopOrderMarkEntity> newMarkList = new ArrayList<>();
                List<BigInteger> markCodes = detailMarkList.stream().map(EshopOrderMarkEntity::getMarkCode).distinct().collect(Collectors.toList());
                for (BigInteger markCode : markCodes) {
                    if (!isRefundMark(markCode)){
                        continue;
                    }
                    EshopOrderMarkEntity newMark = new EshopOrderMarkEntity();
                    newMark.setId(UId.newId());
                    newMark.setCreateType(MarkCreateType.SystemCalc);
                    newMark.setMarkTarget(OrderMarkType.Detail);
                    newMark.setOrderType(OrderSourceType.SaleOrder);
                    newMark.setDetailId(detail.getId());
                    newMark.setOrderId(detail.getEshopOrderId());
                    newMark.setProfileId(CurrentUser.getProfileId());
                    newMark.setMarkCode(markCode);
                    newMarkList.add(newMark);
                }
                detail.setOrderDetailMarks(newMarkList);
                doSaveOrderRefundMark(mainOrder, Collections.singletonList(detail));
            }
        }
        //计算主单
        builder.buildOrderRefundMentionType(mainOrder);
        QueryOrderParameter orderParam = new QueryOrderParameter();
        orderParam.setTradeOrderId(order.getTradeOrderId());
        orderParam.setOtypeIds(Collections.singletonList(order.getOtypeId()));
        orderParam.setProfileId(CurrentUser.getProfileId());
        EshopSaleOrderEntity localOrder = orderDao.getOrderStates(orderParam);
        if (localOrder != null && !localOrder.getLocalRefundState().equals(mainOrder.getLocalRefundState())) {
            SysLogUtil.add(SysLogUtil.buildLog(mainOrder, OrderOpreateType.REFUND_NOTIFY, String.format("原售后状态 %s,新售后状态 %s",
                    localOrder.getLocalRefundState().getName(), mainOrder.getLocalRefundState().getName())));
        }
        if (localOrder != null && !localOrder.getReSendState().equals(mainOrder.getReSendState())) {
            SysLogUtil.add(SysLogUtil.buildLog(mainOrder, OrderOpreateType.REFUND_NOTIFY, String.format("原换/补状态 %s,新换/补状态 %s",
                    localOrder.getReSendState().getName(), mainOrder.getReSendState().getName())));
        }
        mapper.modifyOrderState(mainOrder);
        List<EshopOrderMarkEntity> allSubMarks = cyclePurchaseOrders.stream().map(EshopSaleOrderEntity::getOrderMarks).flatMap(List::stream).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(allSubMarks)){
            return;
        }
        List<BigInteger> allSubMarkCodes = allSubMarks.stream().map(EshopOrderMarkEntity::getMarkCode).distinct().collect(Collectors.toList());
        List<BaseOrderMarkEnum> newMainMarkList = new ArrayList<>();
        for (BigInteger markCode : allSubMarkCodes) {
            if (!isRefundMark(markCode)){
                continue;
            }
            newMainMarkList.add(BaseOrderMarkEnum.getBaseOrderMarkEnumByCode(markCode));
        }
        mainOrder.setMarks(newMainMarkList);
        doSaveMainOrderRefundMark(mainOrder);
    }

    private boolean isRefundMark(BigInteger markCode) {
        List<BigInteger> needMarkCodes = new ArrayList<>();
        needMarkCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.REFUND.getCode()));
        needMarkCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.REFUND_RETURN.getCode()));
        needMarkCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.NO_SEND_PART_REFUND.getCode()));
        needMarkCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.NO_SEND_FULL_REFUND.getCode()));
        needMarkCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.NO_SEND_ONLY_REFUND.getCode()));
        needMarkCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.EXCHANGING_REPLENISHING.getCode()));
        needMarkCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.EXCHANGED_REPLENISHED.getCode()));
        return needMarkCodes.contains(markCode);
    }

    private void doSaveAndSaveState(EshopSaleOrderEntity order) {
        List<EshopSaleOrderDetail> details = order.getOrderDetails();
        if (details == null || details.isEmpty()) {
            return;
        }
        buildAndSaveOrderDetailState(order);
        EshopSaleOrderBuilder builder = EshopSaleOrderBuilder.getSingleton();
        builder.buildOrderRefundMentionType(order);
        doSaveMainOrderRefundMark(order);

        QueryOrderParameter orderParam = new QueryOrderParameter();
        orderParam.setTradeOrderId(order.getTradeOrderId());
        orderParam.setOtypeIds(Collections.singletonList(order.getOtypeId()));
        orderParam.setProfileId(CurrentUser.getProfileId());
        EshopSaleOrderEntity localOrder = orderDao.getOrderStates(orderParam);
        mapper.modifyOrderState(order);
        if (localOrder != null && !localOrder.getLocalRefundState().equals(order.getLocalRefundState())) {
            SysLogUtil.add(SysLogUtil.buildLog(order, OrderOpreateType.REFUND_NOTIFY, String.format("原售后状态 %s,新售后状态 %s",
                    localOrder.getLocalRefundState().getName(), order.getLocalRefundState().getName())));
        }
        if (localOrder != null && !localOrder.getReSendState().equals(order.getReSendState())) {
            SysLogUtil.add(SysLogUtil.buildLog(order, OrderOpreateType.REFUND_NOTIFY, String.format("原换/补状态 %s,新换/补状态 %s",
                    localOrder.getReSendState().getName(), order.getReSendState().getName())));
        }
        ThreadPool threadPool = ThreadPoolFactory.build(EshopOrderConst.ORDER_RELATION);
        threadPool.executeAsync(SaleOrderExStatusAsyncHandleHelper::notify, order.getId());
    }

    private void buildAndSaveOrderDetailState(EshopSaleOrderEntity order) {
        List<EshopSaleOrderDetail> orderDetails = order.getOrderDetails();
        ReturnState returnState = order.getLocalRefundState();
        order.setLocalRefundState(ReturnState.NONE);
        order.setLocalRefundState(returnState);
        EshopOrderSysDataConfig sysDataConfig = GlobalConfig.get(EshopOrderSysDataConfig.class);
        boolean insertUnRelationByNormalOrder = null != sysDataConfig && 1 == sysDataConfig.getInsertUnRelationByNormalOrder();
        for (EshopSaleOrderDetail detail : orderDetails) {
            doBuildDetailInfo(detail);
            //售后通知，可能通知明细退款状态变更
            //如果有变更的情况下，就应该重新检查退款网店商品
            if (insertUnRelationByNormalOrder && !detail.isMappingState()) {
                //这里不能传入order记录日志，要不然售后通知会重复记录日志
                relationService.insertUnRelationItem(detail, null, null, null);
            }
            mapper.modifyDetailState(detail);
        }
        doSaveOrderRefundMark(order, orderDetails);
        checkAndMarkQtyChangeNew(order);
    }

    private void doSaveOrderRefundMark(EshopSaleOrderEntity order, List<EshopSaleOrderDetail> details) {
        EshopOrderMarkParameter parameter = new EshopOrderMarkParameter();
        parameter.setEshopOrderIds(Arrays.asList(order.getId()));
        List<BigInteger> ids = details.stream().map(x -> x.getId()).collect(Collectors.toList());
        List<EshopOrderMarkEntity> marks = new ArrayList<>();
        for (EshopSaleOrderDetail detail : details) {
            marks.addAll(detail.getOrderDetailMarks());
        }
        parameter.setEshopOrderDetailIds(ids);
        parameter.setMarkTarget(1);
        parameter.setCreateType(MarkCreateType.SystemCalc);
        List<BigInteger> markCodes = new ArrayList<>();
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.REFUND.getCode()));
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.REFUND_RETURN.getCode()));
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.NO_SEND_PART_REFUND.getCode()));
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.NO_SEND_FULL_REFUND.getCode()));
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.EXCHANGING_REPLENISHING.getCode()));
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.EXCHANGED_REPLENISHED.getCode()));
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.NO_SEND_ONLY_REFUND.getCode()));
        parameter.setMarkCodes(markCodes);
        parameter.setProfileId(CurrentUser.getProfileId());
        orderDao.deleteEshopOrderMark(parameter);
        orderDao.insertEshopOrderMark(marks);
    }

    private void doSaveOrderRefundMark(EshopAdvanceOrderDetail detail) {
        EshopOrderMarkParameter parameter = new EshopOrderMarkParameter();
        parameter.setEshopOrderIds(Collections.singletonList(detail.getVchcode()));
        parameter.setEshopOrderDetailIds(Collections.singletonList(detail.getDetailId()));
        parameter.setMarkTarget(1);
        parameter.setCreateType(MarkCreateType.SystemCalc);
        List<BigInteger> markCodes = new ArrayList<>();
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.REFUND.getCode()));
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.REFUND_RETURN.getCode()));
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.NO_SEND_PART_REFUND.getCode()));
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.NO_SEND_FULL_REFUND.getCode()));
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.EXCHANGING_REPLENISHING.getCode()));
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.EXCHANGED_REPLENISHED.getCode()));
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.NO_SEND_ONLY_REFUND.getCode()));
        //markCodes.add(BaseOrderMarkEnum.CANT_SEND_GOODS.getCode());
        parameter.setMarkCodes(markCodes);
        parameter.setProfileId(CurrentUser.getProfileId());
        orderDao.deleteEshopOrderMark(parameter);
        orderDao.insertEshopOrderMark(detail.getMarks());
    }

    private void doSaveMainOrderRefundMark(EshopSaleOrderEntity eshopOrder) {
        EshopOrderMarkParameter parameter = new EshopOrderMarkParameter();
        parameter.setEshopOrderIds(Collections.singletonList(eshopOrder.getId()));
        parameter.setMarkTarget(0);
        List<BigInteger> markCodes = new ArrayList<>();
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.REFUND.getCode()));
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.REFUND_RETURN.getCode()));
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.NO_SEND_PART_REFUND.getCode()));
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.NO_SEND_FULL_REFUND.getCode()));
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.NO_SEND_ONLY_REFUND.getCode()));
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.EXCHANGING_REPLENISHING.getCode()));
        markCodes.add(BigInteger.valueOf(BaseOrderMarkEnum.EXCHANGED_REPLENISHED.getCode()));
        //markCodes.add(BaseOrderMarkEnum.CANT_SEND_GOODS.getCode());
        parameter.setMarkCodes(markCodes);
        parameter.setCreateType(MarkCreateType.SystemCalc);
        parameter.setProfileId(CurrentUser.getProfileId());
        orderDao.deleteEshopOrderMark(parameter);
        List<EshopOrderMarkEntity> list = new ArrayList<>();
        if (eshopOrder.getMarks() != null) {
            for (BaseOrderMarkEnum mark : eshopOrder.getMarks()) {
                EshopOrderMarkEntity entity = new EshopOrderMarkEntity();
                entity.setId(UId.newId());
                entity.setOrderId(eshopOrder.getId());
                entity.setBubble(mark.getBubble());
                entity.setMarkCode(BigInteger.valueOf(mark.getCode()));
                entity.setMarkTarget(OrderMarkType.Main);
                entity.setProfileId(CurrentUser.getProfileId());
                entity.setOrderType(OrderSourceType.SaleOrder);
                entity.setMarkUseType(MarkUseType.SystemMark);
                entity.setCreateType(MarkCreateType.SystemCalc);
                list.add(entity);
            }
        }
        orderDao.insertEshopOrderMark(list);
    }

    private void doBuildDetailInfo(EshopSaleOrderDetail detail) {
        detail.setLocalRefundState(ReturnState.NONE);
        boolean refunding = detail.getLocalRefundProcessState().equals(RefundStatus.SELLER_AGREE) ||
                detail.getLocalRefundProcessState().equals(RefundStatus.WAIT_SELLER_AGREE) ||
                detail.getLocalRefundProcessState().equals(RefundStatus.WAIT_BUYER_SETTLE);
        boolean refunded = detail.getLocalRefundProcessState().equals(RefundStatus.SUCCESS);
        if (refunding && RefundStatusType.PART.equals(detail.getRefundStatusType())) {
            detail.setLocalRefundState(ReturnState.SECTION_REFUNDING);
        } else if (refunding) {
            detail.setLocalRefundState(ReturnState.REFUNDING);
        } else if (refunded && RefundStatusType.PART.equals(detail.getRefundStatusType())) {
            detail.setLocalRefundState(ReturnState.SECTION_REFUND_SUCCESS);
        } else if (refunded) {
            detail.setLocalRefundState(ReturnState.SUCCESS);
        }
        List<EshopOrderMarkEntity> detailMark = detail.getOrderDetailMarks();
        if (detailMark.stream().anyMatch(x -> BaseOrderMarkEnum.NO_SEND_FULL_REFUND.equals(x.getMarkCode()))) {
            detail.setDeliverRequired(false);
            EShopOrderMarkUtil.doBuildAndAddToMarkList(detailMark, detail.getEshopOrderId(),
                    detail.getId(), detail.getProfileId(), BaseOrderMarkEnum.CANT_SEND_GOODS);
        }
        for (EshopOrderMarkEntity itemMark : detailMark) {
            itemMark.setCreateType(MarkCreateType.SystemCalc);
            itemMark.setMarkTarget(OrderMarkType.Detail);
            itemMark.setOrderType(OrderSourceType.SaleOrder);
            itemMark.setDetailId(detail.getId());
            itemMark.setOrderId(detail.getEshopOrderId());
            itemMark.setProfileId(CurrentUser.getProfileId());
        }
    }

    private void checkAndMarkQtyChangeNew(EshopSaleOrderEntity order) {
        if (order == null || ProcessState.Submit.equals(order.getProcessState())) {
            return;
        }
        StockChangeQueueDto dto = new StockChangeQueueDto();
        dto.setProfileId(CurrentUser.getProfileId());
        dto.setSourceType(StockChangeTypeEnum.SALE_ORDER);
        dto.setSourceId(order.getId());
        dto.setSourceOperation(QtyChangeSourceType.DETAIL_REFUNDED.getName());
        stockChangeService.insertChange(dto);

    }


    private void markSaleQtyChangeNew(EshopSaleOrderEntity order, QtyChangeSourceType sourceType) {
        StockChangeQueueDto dto = new StockChangeQueueDto();
        dto.setProfileId(CurrentUser.getProfileId());
        dto.setSourceType(StockChangeTypeEnum.SALE_ORDER);
        dto.setSourceId(order.getId());
        dto.setSourceOperation(sourceType.getName());
        stockChangeService.insertChange(dto);
    }


    public void notifyOrderFinish(EshopSaleOrderEntity order) {
        QueryOrderParameter orderParam = new QueryOrderParameter();
        orderParam.setTradeOrderId(order.getTradeOrderId());
        orderParam.setOtypeIds(Collections.singletonList(order.getOtypeId()));
        orderParam.setProfileId(order.getProfileId());
        EshopSaleOrderEntity oldOrder = orderDao.getSimpleOrder(orderParam);
        order.setId(oldOrder.getId());
        //修改原始订单交易状态
        mapper.doOrderFinish(order);
        mapper.doOrderDetailFinish(order);
        SysLogUtil.add(SysLogUtil.buildLog(oldOrder, OrderOpreateType.SEND_ONLINE, "线上交易状态修改为交易成功"));
        //修改预售单交易状态
        QueryAdvanceOrderParameter parameter = new QueryAdvanceOrderParameter();
        parameter.setProfileId(CurrentUser.getProfileId());
        parameter.setOtypeIds(Collections.singletonList(order.getOtypeId()));
        parameter.setTradeOrderId(order.getTradeOrderId());
        List<EshopAdvanceOrderEntity> advance = advanceDao.querySimpleEshopAdvanceOrderList(parameter);
        if (advance == null || advance.size() == 0) {
            return;
        }
        for (EshopAdvanceOrderEntity itemAdvance : advance
        ) {
            order.setId(itemAdvance.getVchcode());
            mapper.doAdvanceOrderFinish(order);
            mapper.doAdvanceOrderDetailFinish(order);
            SysLogUtil.add(SysLogUtil.buildAdvanceOrderLog(itemAdvance, AdvanceOrderOperateTypeEnum.SEND_ONLINE, "线上交易状态修改为交易成功"));
        }
    }

    public void modifyDeliverSendState(EshopSaleOrderExtendEntity extendEntity) {
        mapper.modifyDeliverSendState(extendEntity);
    }

    public List<EshopNotifyChange> queryEshopNotityChange(BigInteger profileId, List<String> tradeIds, BigInteger otypeId) {
        try {
            if (CollectionUtils.isEmpty(tradeIds)) {
                return null;
            }
            List<List<String>> listList = CommonUtil.splitList(tradeIds, 2000);
            List<EshopNotifyChange> result = new ArrayList<>();
            for (List<String> list : listList) {
                List<EshopNotifyChange> pageData = changeMapper.queryMessageChange(profileId, list, otypeId);
                if (CollectionUtils.isNotEmpty(pageData)) {
                    result.addAll(pageData);
                }
            }
            return result;
        } catch (RuntimeException ex) {
            logger.error(String.format("查询原始订单变更信息报错：%s", ex.getMessage()), ex);
        }
        return null;
    }


    private List<OrderChangeType> BuildToAdvanceChangeType(EshopSaleOrderEntity local, EshopSaleOrderEntity online) {
        List<OrderChangeType> changeTypes = new ArrayList<>();
        if (local.getOrderBuyerFreightFee().compareTo(online.getOrderBuyerFreightFee()) != 0) {
            changeTypes.add(OrderChangeType.FREIGHT_PRICE_CHANGE);
        }
        return changeTypes;
    }

    /**
     * 售后补发生成原单之后，提供给交易单通知原单，换补完成的接口
     * 交易单通知原单换补完成---->原单通知售后换补完成---->售后通知原单换补完成，售后通知交易单换补完成
     *
     * @param requests
     */
    public void doAcceptDeliverReSendState(List<NotifySaleOrderSendStateRequest> requests) {
        if (CollectionUtils.isEmpty(requests)) {
            return;
        }
        List<NotifyRefundSendStateRequest> refundRequests = new ArrayList<>();
        EshopOrderEshopRefundMapper refundMapper = GetBeanUtil.getBean(EshopOrderEshopRefundMapper.class);
        List<BigInteger> eshopOrderIds=requests.stream().map(NotifySaleOrderSendStateRequest::getEshopOrderId).collect(Collectors.toList());
        List<BillRelation> relations=refundMapper.getRefundRelationByTargetVchcode(requests.get(0).getProfileId(),eshopOrderIds);
        requests.forEach(r -> {
            NotifyRefundSendStateRequest refundRequest = new NotifyRefundSendStateRequest();
            BigInteger profileId = r.getProfileId();
            refundRequest.setProfileId(profileId);
            refundRequest.setTradeOrderId(r.getTradeOrderId());
            refundRequest.setTradeOrderDetailId(r.getTradeOrderDetailId());
            refundRequest.setReSendState(r.getReSendState());
            BillRelation relation=relations.stream().filter(x->r.getEshopOrderId().equals(x.getTargetVchcode())).findFirst().orElse(null);
            if (relation!=null)
            {
                refundRequest.setRefundOrderId(relation.getSourceVchcode());
                refundRequests.add(refundRequest);
            }
        });
        refundApi.notifyRefundSendState(refundRequests);
    }
}

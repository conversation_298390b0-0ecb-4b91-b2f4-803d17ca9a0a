package com.wsgjp.ct.sale.biz.api.response.openwms;

import java.math.BigInteger;

public class OnlineFreightInterceptResponse {

    public BigInteger getFreightInfoId() {
        return freightInfoId;
    }

    public void setFreightInfoId(BigInteger freightInfoId) {
        this.freightInfoId = freightInfoId;
    }

    public boolean isSueecess() {
        return sueecess;
    }

    public void setSueecess(boolean sueecess) {
        this.sueecess = sueecess;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    private BigInteger freightInfoId;

    private boolean sueecess;

    private String errorMsg;
}

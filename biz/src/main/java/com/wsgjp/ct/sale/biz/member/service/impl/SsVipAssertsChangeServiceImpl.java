package com.wsgjp.ct.sale.biz.member.service.impl;


import com.wsgjp.ct.framework.enums.AssertsSourceOperation;
import com.wsgjp.ct.sale.biz.member.common.*;
import com.wsgjp.ct.sale.biz.member.mapper.SsVipMapper;
import com.wsgjp.ct.sale.biz.member.model.dto.card.GetCardRequest;
import com.wsgjp.ct.sale.biz.member.model.dto.vip.*;
import com.wsgjp.ct.sale.biz.member.model.entity.rights.MemberEquityValue;
import com.wsgjp.ct.sale.biz.member.model.entity.rights.SsEquityValueDetail;
import com.wsgjp.ct.sale.biz.member.model.entity.store.SsVipAsserts;
import com.wsgjp.ct.sale.biz.member.model.entity.vip.*;
import com.wsgjp.ct.sale.biz.member.model.vo.card.VipGetCard;
import com.wsgjp.ct.sale.biz.member.model.vo.vip.VipStoreConsumptionResponse;
import com.wsgjp.ct.sale.biz.member.service.*;
import com.wsgjp.ct.sale.biz.member.utils.BeanUtils;
import com.wsgjp.ct.sale.biz.member.utils.StringUtils;
import com.wsgjp.ct.sale.biz.wx.common.WxTemplateMessageEnum;
import com.wsgjp.ct.sale.biz.wx.entity.SendWxMpTemplateMessageDto;
import com.wsgjp.ct.sale.biz.wx.entity.WxVipAssert;
import com.wsgjp.ct.sale.biz.wx.service.NgpWxOpenService;
import com.wsgjp.ct.sale.biz.wx.utils.AssertUtil;
import com.wsgjp.ct.sale.sdk.pos.biz.MemberAssertsChangeService;
import com.wsgjp.ct.sale.sdk.pos.entity.asserts.MemberAssert;
import com.wsgjp.ct.sale.sdk.pos.entity.asserts.MemberAssertsChange;
import com.wsgjp.ct.sale.sdk.pos.entity.asserts.VipAsserts;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.thread.ThreadPool;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import com.wsgjp.ct.support.utils.RedisBizUtils;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import ngp.idgenerator.UId;
import ngp.utils.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SsVipAssertsChangeServiceImpl implements ISsVipAssertsChangeService {

    private static final Logger log = LoggerFactory.getLogger(SsVipAssertsChangeServiceImpl.class);

    @Autowired
    private ISsCardService cardService;
    @Autowired
    private IVipGrowthRuleService growthRuleService;
    @Autowired
    private SsVipMapper ssVipMapper;
    @Autowired
    @Lazy
    private NgpWxOpenService wxOpenService;
    @Autowired
    private MemberAssertsChangeService memberAssertsChangeService;


    @Override
    public Integer scoreCompute(BigInteger vipId, BigDecimal money, ScoreStrategyType type) {
        if (vipId == null) {
            throw new RuntimeException("会员id不能为空");
        }
        if (money == null) {
            throw new RuntimeException("消费/充值金额不能为空");
        }
//        if (money.compareTo(BigDecimal.ZERO) < 0) {
//            throw new RuntimeException("消费/充值金额只能传正值");
//        }
        if (type == null) {
            throw new RuntimeException("使用规则不能为空");
        }
        if (!type.equals(ScoreStrategyType.consumption) && !type.equals(ScoreStrategyType.recharge)) {
            throw new RuntimeException("请选择正确的规则");
        }
        // 要赠送的积分
        BigDecimal score = null;
        if (type.equals(ScoreStrategyType.consumption)) {
            // 消费
            // 等级权益卡消费赠送积分
            // 获取该会员当前等级权益卡
            GetCardRequest cardRequest = new GetCardRequest();
            cardRequest.setProfileId(CurrentUser.getProfileId());
            cardRequest.setVipId(vipId);
            List<Integer> cardTypeList = new ArrayList<>();
            cardTypeList.add(0);
            cardRequest.setCardType(cardTypeList);
            List<VipGetCard> cardByVipId = cardService.getCardByVipId(cardRequest);
            VipGetCard vipGetCard = cardByVipId.get(0);
            // 付费会员过期了也不能用权益
            BigInteger id = ssVipMapper.checkPaidVipExpire(vipId, CurrentUser.getProfileId());
            if (id == null) {
                // 判断有没有送积分的权益
                List<MemberEquityValue> memberEquityValues = vipGetCard.getMemberEquityValues();
                if (memberEquityValues != null && !memberEquityValues.isEmpty()) {
                    for (MemberEquityValue memberEquityValue : memberEquityValues) {
                        if (memberEquityValue == null) {
                            throw new RuntimeException("等级权益卡信息错误");
                        }
                        if (EquityValueType.scoreMultiple.name().equals(memberEquityValue.getValueType())) {
                            if (memberEquityValue.getDetailList() == null || memberEquityValue.getDetailList().isEmpty()) {
                                throw new RuntimeException("等级权益卡信息错误");
                            }
                            SsEquityValueDetail ssEquityValueDetail = memberEquityValue.getDetailList().get(0);
                            // 消费金额指数
                            BigDecimal valueCondition = ssEquityValueDetail.getValueCondition();
                            // 赠送积分指数
                            BigDecimal valueDetail = ssEquityValueDetail.getValueDetail();
                            score = money.divide(valueCondition, 0, RoundingMode.DOWN).multiply(valueDetail);
                        }
                    }
                }
            }
            // 积分必须有值才能倍送
            if (score != null) {
                // 生日倍送
                SsVip ssVip = ssVipMapper.selectVipById(vipId, CurrentUser.getProfileId());
                BigDecimal scoreRule = growthRuleService.vipScoreRule(ssVip);
                score = score.multiply(scoreRule);
            }
        }
        return score == null ? 0 : score.intValue();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Integer growthCompute(BigInteger vipId, BigDecimal money, ScoreStrategyType type) {
        if (vipId == null) {
            throw new RuntimeException("会员id不能为空");
        }
        if (money == null) {
            throw new RuntimeException("消费/充值金额不能为空");
        }
        if (type == null) {
            throw new RuntimeException("使用规则不能为空");
        }
        if (!type.equals(ScoreStrategyType.consumption) && !type.equals(ScoreStrategyType.recharge)) {
            throw new RuntimeException("请选择正确的规则");
        }
        // 要赠送的成长值
        Integer growth = 0;
        // 成长值规则
        VipGrowthRule vipGrowthRule = growthRuleService.getVipGrowthRule();
//        // 消费记录
//        ssVipConsumptionRecord vipConsumptionRecord = ssVipMapper.getVipConsumptionRecord(vipId, CurrentUser.getProfileId());
//        if (Objects.isNull(vipConsumptionRecord)) {
//            vipConsumptionRecord = new ssVipConsumptionRecord(UId.newId(), CurrentUser.getProfileId(), vipId, 0, 0, 0, 0);
//        }
        if (vipGrowthRule != null) {
            if (type.equals(ScoreStrategyType.consumption)) {
                // 每消费多少元赠送成长值
                if (vipGrowthRule.getConsumption() == 1) {
                    if (money.compareTo(vipGrowthRule.getConsumptionRmb()) >= 0) {
                        growth += money.divide(vipGrowthRule.getConsumptionRmb(), 0, RoundingMode.DOWN).intValue() * (vipGrowthRule.getConsumptionGrowth());
                    }
                }
//                // 每完成多少笔订单送成长值
//                // 只有在规则开启的情况下才记录订单数
//                if (vipGrowthRule.getCompleteOrder() == 1 && !allowSaleChangeBill) {
//                    vipConsumptionRecord.setOrderNum(vipConsumptionRecord.getOrderNum() + 1);
//                    if (vipConsumptionRecord.getOrderNum() >= vipGrowthRule.getCompleteOrderNum()) {
//                        // 满足条件，送成长值，在后边资产变动接口中再把记录清零
//                        growth += vipGrowthRule.getCompleteOrderGrowth();
//                        vipConsumptionRecord.setOrderNum(0);
//                    }
//                    // 目前只有这里要修改消费记录
//                    ssVipMapper.updateConsumptionRecord(vipConsumptionRecord);
//                }
            }
            if (type.equals(ScoreStrategyType.recharge)) {
                // 单笔充值多少元送成长值
                if (vipGrowthRule.getPay() == 1) {
                    if (money.compareTo(vipGrowthRule.getPayRmb()) >= 0) {
                        growth += vipGrowthRule.getPayGrowth();
                    }
                }
            }
        }
        if (growth < 0) {
            // 当成长值为负数时就代表超过integer最大值了，其他情况下不会为负数，这时成长值不再长
            growth = Integer.MAX_VALUE;
        }
        return growth;
    }

    @Override
    public void wxVipSyncList(List<BigInteger> vipIds, BigInteger profileId) throws Exception {
        if (profileId == null || profileId.equals(BigInteger.ZERO)) {
            profileId = CurrentUser.getProfileId();
        }
        log.info("开始同步{}个微信会员", vipIds.size());
        // 定义批次大小
        int batchSize = 100;
        List<List<BigInteger>> batches = new ArrayList<>();
        // 分割vipIds为多个批次
        for (int i = 0; i < vipIds.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, vipIds.size());
            batches.add(vipIds.subList(i, endIndex));
        }
        // 创建线程池 （pos-submit-bill 目前只有微信会员同步在用）
        ThreadPool threadPool = ThreadPoolFactory.build("pos-submit-bill");
        // 提交每个批次到线程池
        if (!batches.isEmpty()) {
            for (List<BigInteger> batch : batches) {
                String localProcessKey = RedisBizUtils.getRequestId();
                BigInteger finalProfileId = profileId;
                threadPool.executeAsync(processKey -> {
                    try {
                        wxOpenService.getNgpWxMapService().wxVipSyncList(batch, finalProfileId);
                    } catch (Exception e) {
                        log.error("同步微信会员失败 vipIds={}, profileId={}", batch, finalProfileId, e);
                    }
                }, localProcessKey);
            }
        }
    }

    @Override
    public void wxVipAsync(BigInteger vipId, BigInteger profileId) {
        if (!TransactionSynchronizationManager.isSynchronizationActive()) {
            // 非事务环境
            try {
                wxVipSyncList(Collections.singletonList(vipId), profileId);
            } catch (Exception e) {
                log.error("同步微信会员异常：{}", e.getMessage());
            }
        } else {
            // 事务环境
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    try {
                        wxVipSyncList(Collections.singletonList(vipId), profileId);
                    } catch (Exception e) {
                        log.error("同步微信会员异常：{}", e.getMessage());
                    }
                }
            });
        }
    }

    @Override
    public void sendMessageList(List<VipSendMessageDto> dtos) {
        if (dtos == null || dtos.isEmpty()) {
            return;
        }
        List<BigInteger> vipIds = dtos.stream().map(VipSendMessageDto::getVipId).collect(Collectors.toList());
        List<WxVipAssert> vipAsserts = ssVipMapper.getVipAssertByVipIds(vipIds, CurrentUser.getProfileId());
        if (vipAsserts == null || vipAsserts.isEmpty()) {
            return;
        }
        // 创建临时映射存储查询结果
        Map<BigInteger, WxVipAssert> assertsMap = new HashMap<>();
        // 处理查询结果：过滤空值并处理重复键
        Optional.of(vipAsserts)
                .orElseGet(Collections::emptyList)
                .stream()
                .filter(Objects::nonNull)
                .forEach(record ->
                        assertsMap.put(record.getVipId(), record)
                );
        // 要发送的消息
        List<SendWxMpTemplateMessageDto> sendWxMpTemplateMessageDtos = new ArrayList<>();
        for (VipSendMessageDto dto : dtos) {
            // 更新完后发送模板消息，目前只能发送消费成功、充值成功的消息
            // 判断是消费还是充值
            BigDecimal money = dto.getMoney();
            BigDecimal storedValue = dto.getStoredValue();
            BigDecimal giveStoredValue = dto.getGiveStoredValue();
            if (dto.getSourceOperation() == AssertsSourceOperation.CONSUMPTION || dto.getSourceOperation() == AssertsSourceOperation.REFUND) {
                WxVipAssert vipAssert = assertsMap.get(dto.getVipId());
                // 是否绑定微信会员
                if (StringUtils.isNotBlank(vipAssert.getUserCardCode())) {
                    SendWxMpTemplateMessageDto sendWxMpTemplateMessageDto = new SendWxMpTemplateMessageDto();
                    sendWxMpTemplateMessageDto.setVipId(dto.getVipId());
                    sendWxMpTemplateMessageDto.setTemplateMessageEnum(WxTemplateMessageEnum.SUCCESSFUL_CONSUMPTION);
                    List<WxMpTemplateData> list = new ArrayList<>();
                    WxMpTemplateData data1 = new WxMpTemplateData();
                    data1.setName(WxTemplateMessageEnum.SUCCESSFUL_CONSUMPTION.getValue1());
                    data1.setValue(vipAssert.getPhone());
                    list.add(data1);
                    WxMpTemplateData data2 = new WxMpTemplateData();
                    data2.setName(WxTemplateMessageEnum.SUCCESSFUL_CONSUMPTION.getValue2());
                    data2.setValue(money.toString());
                    list.add(data2);
                    WxMpTemplateData data3 = new WxMpTemplateData();
                    data3.setName(WxTemplateMessageEnum.SUCCESSFUL_CONSUMPTION.getValue3());
                    data3.setValue(DateUtils.formatDateTime(new Date()));
                    list.add(data3);
                    WxMpTemplateData data4 = new WxMpTemplateData();
                    data4.setName(WxTemplateMessageEnum.SUCCESSFUL_CONSUMPTION.getValue4());
                    data4.setValue(vipAssert.getTotal().toString());
                    list.add(data4);
                    WxMpTemplateData data5 = new WxMpTemplateData();
                    data5.setName(WxTemplateMessageEnum.SUCCESSFUL_CONSUMPTION.getValue5());
                    data5.setValue(vipAssert.getScore().toString());
                    list.add(data5);
                    sendWxMpTemplateMessageDto.setData(list);
                    sendWxMpTemplateMessageDtos.add(sendWxMpTemplateMessageDto);
                }

            }
            if (dto.getSourceOperation() == AssertsSourceOperation.VIP_RECHARGE || dto.getSourceOperation() == AssertsSourceOperation.RECHARGE_REFUND) {
                WxVipAssert vipAssert = assertsMap.get(dto.getVipId());
                // 是否绑定微信会员
                if (StringUtils.isNotBlank(vipAssert.getUserCardCode())) {
                    SendWxMpTemplateMessageDto sendWxMpTemplateMessageDto = new SendWxMpTemplateMessageDto();
                    sendWxMpTemplateMessageDto.setVipId(dto.getVipId());
                    sendWxMpTemplateMessageDto.setTemplateMessageEnum(WxTemplateMessageEnum.SUCCESSFUL_RECHARGE);
                    List<WxMpTemplateData> list = new ArrayList<>();
                    WxMpTemplateData data1 = new WxMpTemplateData();
                    data1.setName(WxTemplateMessageEnum.SUCCESSFUL_RECHARGE.getValue1());
                    data1.setValue(vipAssert.getPhone());
                    list.add(data1);
                    WxMpTemplateData data2 = new WxMpTemplateData();
                    data2.setName(WxTemplateMessageEnum.SUCCESSFUL_RECHARGE.getValue2());
                    data2.setValue(storedValue.toString());
                    list.add(data2);
                    WxMpTemplateData data3 = new WxMpTemplateData();
                    data3.setName(WxTemplateMessageEnum.SUCCESSFUL_RECHARGE.getValue3());
                    data3.setValue(giveStoredValue == null ? "0" : giveStoredValue.toString());
                    list.add(data3);
                    WxMpTemplateData data4 = new WxMpTemplateData();
                    data4.setName(WxTemplateMessageEnum.SUCCESSFUL_RECHARGE.getValue4());
                    data4.setValue(vipAssert.getTotal().toString());
                    list.add(data4);
                    WxMpTemplateData data5 = new WxMpTemplateData();
                    data5.setName(WxTemplateMessageEnum.SUCCESSFUL_RECHARGE.getValue5());
                    data5.setValue(DateUtils.formatDateTime(new Date()));
                    list.add(data5);
                    sendWxMpTemplateMessageDto.setData(list);
                    sendWxMpTemplateMessageDtos.add(sendWxMpTemplateMessageDto);
                }
            }
        }
        if (!sendWxMpTemplateMessageDtos.isEmpty()) {
            try {
                wxOpenService.getNgpWxMapService().sendTemplateMsgList(sendWxMpTemplateMessageDtos);
            } catch (Exception e) {
                log.error("发送模板消息出错：" + e.getMessage());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Integer accumulatedGrowthCompute(BigInteger vipId, ScoreStrategyType type, boolean allowSaleChangeBill) {
        if (vipId == null) {
            throw new RuntimeException("会员id不能为空");
        }
        if (type == null) {
            throw new RuntimeException("使用规则不能为空");
        }
        if (!type.equals(ScoreStrategyType.consumption) && !type.equals(ScoreStrategyType.recharge)) {
            throw new RuntimeException("请选择正确的规则");
        }
        // 要赠送的成长值
        Integer growth = 0;
        // 成长值规则
        VipGrowthRule vipGrowthRule = growthRuleService.getVipGrowthRule();
        // 消费记录
        ssVipConsumptionRecord vipConsumptionRecord = ssVipMapper.getVipConsumptionRecord(vipId, CurrentUser.getProfileId());
        if (Objects.isNull(vipConsumptionRecord)) {
            vipConsumptionRecord = new ssVipConsumptionRecord(UId.newId(), CurrentUser.getProfileId(), vipId, 0, 0, 0, 0);
        }
        if (vipGrowthRule != null) {
            if (type.equals(ScoreStrategyType.consumption)) {
                // 每完成多少笔订单送成长值
                // 只有在规则开启的情况下才记录订单数
                if (vipGrowthRule.getCompleteOrder() == 1 && !allowSaleChangeBill) {
                    vipConsumptionRecord.setOrderNum(vipConsumptionRecord.getOrderNum() + 1);
                    if (vipConsumptionRecord.getOrderNum() >= vipGrowthRule.getCompleteOrderNum()) {
                        // 满足条件，送成长值，在后边资产变动接口中再把记录清零
                        growth += vipGrowthRule.getCompleteOrderGrowth();
                    }
                }
            }
        }
        if (growth < 0) {
            // 当成长值为负数时就代表超过integer最大值了，其他情况下不会为负数，这时成长值不再长
            growth = Integer.MAX_VALUE;
        }
        return growth;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void vipAssertsChange(VipAssertsChange dto) {
        try {
            log.info("开始处理会员资产变动，vipId: {}, sourceOperation: {}", dto.getVipId(), dto.getSourceOperation());
            MemberAssertsChange memberAssertsChange = BeanUtils.copyProperties(dto, MemberAssertsChange.class);
            List<VipAsserts> vipAsserts = new ArrayList<>();
            VipAsserts vipAssertsDto = new VipAsserts();
            vipAssertsDto.setVipId(dto.getVipId());
            vipAssertsDto.setMoney(dto.getMoney());
            List<MemberAssert> memberAssertList = Optional.ofNullable(dto.getVipAsserts())
                    .orElseGet(Collections::emptyList)
                    .stream()
                    .map(anAssert -> MemberAssert.createData(
                            anAssert.getTyped(),
                            anAssert.getQty(),
                            anAssert.getMemo(),
                            anAssert.getAssertId(),
                            anAssert.getCardTemplateId(),
                            anAssert.getChangeType()
                    ))
                    .collect(Collectors.toList());
            vipAssertsDto.setVipAssert(memberAssertList);
            vipAsserts.add(vipAssertsDto);
            memberAssertsChange.setVipAsserts(vipAsserts);

            log.info("调用SDK会员资产变动服务，vipId: {}", dto.getVipId());
            memberAssertsChangeService.vipAssertsChange(memberAssertsChange);
            log.info("会员资产变动处理完成，vipId: {}", dto.getVipId());
        } catch (Exception e) {
            log.error("会员资产变动处理失败，vipId: {}, error: {}", dto.getVipId(), e.getMessage(), e);
            throw new RuntimeException("会员资产变动处理失败: " + e.getMessage(), e);
        }
    }

    @Override
    public VipStoreConsumptionResponse vipStoreConsumption(VipStoreConsumptionDTO dto) {
        // 数据校验
        AssertUtil.falseTrw(dto.getVipId() != null && !Objects.equals(dto.getVipId(), BigInteger.ZERO), "会员id不能为空");
        AssertUtil.falseTrw(dto.getStoreTotal() != null && dto.getStoreTotal().compareTo(BigDecimal.ZERO) > 0, "储值支付金额不能小于等于0");
        AssertUtil.falseTrw(dto.getSourceOperation() != null, "操作来源不能为空");
        // 计算要扣的本金、赠金
        SsVipAsserts ssVipAsserts = ssVipMapper.getSsVipAsserts(dto.getVipId(), CurrentUser.getProfileId());
        if (ssVipAsserts == null) {
            throw new RuntimeException("未获取到会员资产信息");
        }
        // 储值之和
        BigDecimal total = ssVipAsserts.getChargeTotal().add(ssVipAsserts.getGiftTotal());
        AssertUtil.falseTrw(dto.getStoreTotal().compareTo(total) <= 0, "储值不足");
        // 后面不会出现本金+赠金都不够扣的情况
        // 本金
        BigDecimal chargeTotal = dto.getStoreTotal().multiply(
                        ssVipAsserts.getChargeTotal().divide(total, 2, RoundingMode.HALF_UP))
                .setScale(2, RoundingMode.HALF_UP);
        // 由于本金计算有乘除，所以会存在精度问题，计算出来的本金可能比库里的本金大零点几，所以这里判断一下
        if (chargeTotal.compareTo(ssVipAsserts.getChargeTotal()) > 0) {
            chargeTotal = ssVipAsserts.getChargeTotal();
        }
        // 赠金
        BigDecimal giftTotal = dto.getStoreTotal().subtract(chargeTotal);
        // 本金计算有精度问题，可能会少一部分，比如库里本金100，赠金33，总共要扣133，计算出来的本金就是99.75，少的0.25就多扣到了赠金上，导致赠金不足
        if (giftTotal.compareTo(ssVipAsserts.getGiftTotal()) > 0) {
            giftTotal = ssVipAsserts.getGiftTotal();
            chargeTotal = dto.getStoreTotal().subtract(giftTotal);
        }
        // 想不到这里能报错的情况，加一条防止意外
        AssertUtil.falseTrw(chargeTotal.add(giftTotal).compareTo(dto.getStoreTotal()) == 0, "储值计算有误");
        // 资产变动
        MemberAssertsChange vipAssertsChange = new MemberAssertsChange();
        vipAssertsChange.setVchcode(dto.getVchcode() == null ? BigInteger.ZERO : dto.getVchcode());
        vipAssertsChange.setVchtype(dto.getVchtype());
        vipAssertsChange.setTradeId(dto.getTradeId());
        vipAssertsChange.setMemo(dto.getSourceOperation().getName());
        vipAssertsChange.setSourceOperation(dto.getSourceOperation());
        vipAssertsChange.setSaveVipAsserts(true);

        List<VipAsserts> vipAsserts = new ArrayList<>();
        VipAsserts vipAssert = new VipAsserts();
        vipAssert.setVipId(dto.getVipId());
        List<MemberAssert> value = new ArrayList<>();
        if (chargeTotal.compareTo(BigDecimal.ZERO) != 0) {
            value.add(MemberAssert.createData(1,
                    chargeTotal.negate(),
                    VipAssertsBillDetailDto.getTypedMemo(1, chargeTotal),
                    null,
                    null,
                    null)); // 云订货没有对接，changeType不知道给啥，对接了再说
        }
        if (giftTotal.compareTo(BigDecimal.ZERO) != 0) {
            value.add(MemberAssert.createData(2,
                    giftTotal.negate(),
                    VipAssertsBillDetailDto.getTypedMemo(2, giftTotal),
                    null,
                    null,
                    null)); // 云订货没有对接，changeType不知道给啥，对接了再说
        }
        vipAssert.setMoney(chargeTotal.add(giftTotal));
        vipAssert.setVipAssert(value);
        vipAsserts.add(vipAssert);
        vipAssertsChange.setVipAsserts(vipAsserts);
        memberAssertsChangeService.vipAssertsChange(vipAssertsChange);
        return new VipStoreConsumptionResponse(giftTotal, chargeTotal);
    }

    @Override
    public void vipStoreChange(VipStoreChangeDTO dto) {
        // 数据校验
        AssertUtil.falseTrw(dto.getVipId() != null && !Objects.equals(dto.getVipId(), BigInteger.ZERO), "会员id不能为空");
        AssertUtil.falseTrw(dto.getSourceOperation() != null, "操作来源不能为空");
        AssertUtil.falseTrw(dto.getStoreTotal() != null, "本金变动金额不能为空");
        AssertUtil.falseTrw(dto.getGiftTotal() != null, "赠金变动金额不能为空");
        BigDecimal storeTotal = dto.getStoreTotal();
        BigDecimal giftTotal = dto.getGiftTotal();
        // 资产变动
        MemberAssertsChange vipAssertsChange = new MemberAssertsChange();
        vipAssertsChange.setVchcode(dto.getVchcode() == null ? BigInteger.ZERO : dto.getVchcode());
        vipAssertsChange.setVchtype(dto.getVchtype());
        vipAssertsChange.setTradeId(dto.getTradeId());
        vipAssertsChange.setMemo(dto.getSourceOperation().getName());
        vipAssertsChange.setSourceOperation(dto.getSourceOperation());
        vipAssertsChange.setSaveVipAsserts(true);
        List<VipAsserts> vipAsserts = new ArrayList<>();
        VipAsserts vipAssert = new VipAsserts();
        vipAssert.setVipId(dto.getVipId());
        List<MemberAssert> value = new ArrayList<>();
        if (storeTotal.compareTo(BigDecimal.ZERO) != 0) {
            value.add(MemberAssert.createData(1,
                    storeTotal,
                    VipAssertsBillDetailDto.getTypedMemo(1, storeTotal),
                    null,
                    null,
                    null)); // 云订货没有对接，changeType不知道给啥，对接了再说
        }
        if (giftTotal.compareTo(BigDecimal.ZERO) != 0) {
            value.add(MemberAssert.createData(2,
                    giftTotal,
                    VipAssertsBillDetailDto.getTypedMemo(2, giftTotal),
                    null,
                    null,
                    null)); // 云订货没有对接，changeType不知道给啥，对接了再说
        }
        vipAssert.setMoney(storeTotal.add(giftTotal));
        vipAssert.setVipAssert(value);
        vipAsserts.add(vipAssert);
        vipAssertsChange.setVipAsserts(vipAsserts);
        memberAssertsChangeService.vipAssertsChange(vipAssertsChange);
    }
}

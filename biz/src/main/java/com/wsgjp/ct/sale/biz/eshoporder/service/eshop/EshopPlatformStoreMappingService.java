package com.wsgjp.ct.sale.biz.eshoporder.service.eshop;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopStoreService;
import com.wsgjp.ct.sale.biz.bifrost.util.EshopUtils;
import com.wsgjp.ct.sale.biz.eshoporder.constant.StringConstantEnum;
import com.wsgjp.ct.sale.biz.eshoporder.constant.eshop.PlatformStoreMappingConstants;
import com.wsgjp.ct.sale.biz.eshoporder.dto.eshop.EshopPlatformStoreMappingInDTO;
import com.wsgjp.ct.sale.biz.eshoporder.dto.eshop.RefreshOnlineStoreParam;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.EshopStoreTreeData;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Otype;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.Stock;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.PlatformStoreMappingTypeEnum;
import com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryEShopParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.response.BaseResponse;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopBizMapper;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopOrderBaseInfoMapper;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopPlatformStoreMappingMapper;
import com.wsgjp.ct.sale.biz.eshoporder.service.baseinfo.EshopOrderBaseInfoService;
import com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopSaleOrderService;
import com.wsgjp.ct.sale.biz.eshoporder.util.CommonUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.processlogger.impl.ProcessLoggerImpl;
import com.wsgjp.ct.sale.platform.dto.warehouse.EshopOnlineStoreInfo;
import com.wsgjp.ct.sale.platform.dto.warehouse.EshopPlatformWareHouse;
import com.wsgjp.ct.sale.platform.entity.request.store.GetOnlineStoreRequest;
import com.wsgjp.ct.sale.platform.entity.request.store.PlatformWarehouseRequest;
import com.wsgjp.ct.sale.platform.enums.WareHouseBusinessType;
import com.wsgjp.ct.sale.platform.feature.store.EshopOnlineStoreFeature;
import com.wsgjp.ct.sale.platform.feature.store.EshopPlatformWareHouseFeature;
import com.wsgjp.ct.support.context.CurrentUser;
import ngp.idgenerator.UId;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 全渠道和平台仓相关业务处理类
 */
@Service
public class EshopPlatformStoreMappingService {
    private EshopPlatformStoreMappingMapper platformStoreMappingMapper;
    private EshopBizMapper eshopMapper;
    private EshopOrderBaseInfoMapper eshopOrderBaseInfoMapper;
    private EshopOrderBaseInfoService baseSvc;
    private static final Logger logger = LoggerFactory.getLogger(EshopPlatformStoreMappingService.class);
    private static PlatformStoreMappingListener listener;
    private static PlatformOnlineStoreMappingListener listenerNew;

    private static PlatformWarehouseCorrespondListener warehouseCorrespondListener;
    private final BifrostEshopStoreService eshopStoreService;
    private final EshopBusinessService eshopBusinessService;

    public EshopPlatformStoreMappingService(EshopPlatformStoreMappingMapper platformStoreMappingMapper, EshopBizMapper eshopMapper
            , EshopOrderBaseInfoMapper eshopOrderBaseInfoMapper, EshopOrderBaseInfoService baseSvc, BifrostEshopStoreService eshopStoreService, EshopBusinessService eshopBusinessService) {
        this.platformStoreMappingMapper = platformStoreMappingMapper;
        this.eshopMapper = eshopMapper;
        this.eshopOrderBaseInfoMapper = eshopOrderBaseInfoMapper;
        this.baseSvc = baseSvc;
        this.eshopStoreService = eshopStoreService;
        this.eshopBusinessService = eshopBusinessService;
    }

    public List<EshopPlatformStoreMapping> getPlatformStoreMappingByEshopId(BigInteger profileId, BigInteger eshopId) {
        List<EshopPlatformStoreMapping> storeMappings = platformStoreMappingMapper.getPlatformStoreMappingByEshopId(profileId, eshopId);
        if (storeMappings == null) {
            storeMappings = new ArrayList<>();
        }
        return storeMappings;
    }

    public List<EshopStoreTreeData> queryEshopStoreTreeData(BigInteger eshopId) {
        try {
            return eshopMapper.queryEshopStoreTree(CurrentUser.getProfileId(), eshopId);
        } catch (Exception ex) {
            throw new RuntimeException("查询网店数据报错", ex);
        }
    }
    public List<EshopStoreTreeData> queryEshopStoreForWarehouseStock(BigInteger eshopId) {
        try {
            return eshopMapper.queryEshopStoreForWarehouseStock(CurrentUser.getProfileId(), eshopId);
        } catch (Exception ex) {
            throw new RuntimeException("查询网店数据报错", ex);
        }
    }

    public BaseResponse savePlatformStore(EshopPlatformStoreMapping inDTO) {
        BaseResponse response = new BaseResponse();
        response.setSuccess(true);
        inDTO.setId(UId.newId());
        inDTO.setProfileId(CurrentUser.getProfileId());
        inDTO.setCorrespondFlag(true);
        inDTO.setSource(1);
        try {
            int count = platformStoreMappingMapper.queryPlatformStoreByUnique(inDTO);
            if(count == 0 ) {
                platformStoreMappingMapper.addPlatformStoreMappingByAdd(inDTO);
                GetBeanUtil.getBean(EshopSaleOrderService.class).updateOrdersByStoreMapping(Collections.singletonList(inDTO));
            }else {
                response.setSuccess(false);
                String errorMsg=String.format("网点名称：%s,门店/网店ID重复，请核实后再增加",inDTO.getPlatformStoreName());

                response.setMessage(errorMsg);
            }
        }catch (Exception e){
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    public BaseResponse platformStoreEdit(EshopPlatformStoreMapping inDTO) {
        BaseResponse response = new BaseResponse();
        response.setSuccess(true);
        try {
            if (inDTO.isUpdateUnique()) {
                int count = platformStoreMappingMapper.queryPlatformStoreByUnique(inDTO);
                if (count == 0) {
                    platformStoreMappingMapper.updateStoreInfo(inDTO);
                    GetBeanUtil.getBean(EshopSaleOrderService.class).updateOrdersByStoreMapping(Collections.singletonList(inDTO));
                } else {
                    response.setSuccess(false);
                    response.setMessage("门店/网店ID重复");
                }
            } else {
                platformStoreMappingMapper.updateStoreInfo(inDTO);
                GetBeanUtil.getBean(EshopSaleOrderService.class).updateOrdersByStoreMapping(Collections.singletonList(inDTO));
            }
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    public BaseResponse doDelete(EshopPlatformStoreMapping inDTO) {
        BaseResponse response = new BaseResponse();
        response.setSuccess(true);
        try {
            platformStoreMappingMapper.deleteById(inDTO.getId(),CurrentUser.getProfileId());
        }catch (Exception e){
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    public BaseResponse doBatchDelete(List<EshopPlatformStoreMapping> inDTO) {
        BaseResponse response = new BaseResponse();
        response.setSuccess(true);
        if (CollectionUtils.isNotEmpty(inDTO)){
            List<BigInteger> ids = inDTO.stream().map(x -> x.getId()).collect(Collectors.toList());
            try {
                platformStoreMappingMapper.deleteByIds(ids,CurrentUser.getProfileId());
            }catch (Exception e){
                response.setSuccess(false);
                response.setMessage(e.getMessage());
            }
        }

        return response;
    }

    public List<EshopPlatformStoreMapping> queryListByEshopId(EshopPlatformStoreMappingInDTO inDTO) {
        EshopPlatformStoreMapping request = new EshopPlatformStoreMapping();
        BeanUtils.copyProperties(inDTO, request);
        if (inDTO.getCorrespondFlag() == PlatformStoreMappingConstants.NO_CORRESPOND) {
            request.setCorrespondFlag(false);
        } else if (inDTO.getCorrespondFlag() == PlatformStoreMappingConstants.CORRESPOND) {
            request.setCorrespondFlag(true);
        }
        request.setProfileId(CurrentUser.getProfileId());
        request.setDeleted(false);
        if (inDTO.isPlatformStoreNew()){
            request.setQuickfilter(inDTO.getPlatformStoreStockId());
            request.setPlatformStoreStockId(null);
        }
        List<EshopPlatformStoreMapping> storeMappings = platformStoreMappingMapper.queryListByEshopId(request);
        // 将原来的老数据转换为新的仓类型。
        if (ngp.utils.CollectionUtils.isNotEmpty(storeMappings) && inDTO.isPlatformStoreNew()){
            storeMappings.forEach(x -> {
                if (x.getType() != null && x.getType() == 0){
                    x.setPlatformStoreType(1);
                } else if (x.getType() != null && x.getType() == 1) {
                    if (x.getPlatformStoreType() != null && x.getPlatformStoreType() == 1){
                        x.setPlatformStoreType(3);
                    }else {
                        x.setPlatformStoreType(2);
                    }
                }
            });
        }
        return platformStoreMappingMapper.queryListByEshopId(request);
    }

    public BaseResponse platformStoreNameEdit(EshopPlatformStoreMappingInDTO inDTO) {
        BaseResponse response = new BaseResponse();
        response.setSuccess(true);
        try {
            EshopPlatformStoreMapping request = new EshopPlatformStoreMapping();
            request.setProfileId(CurrentUser.getProfileId());
            request.setId(inDTO.getId());
            request.setPlatformStoreName(inDTO.getPlatformStoreName());
            platformStoreMappingMapper.updateStoreInfo(request);
            GetBeanUtil.getBean(EshopSaleOrderService.class).updateOrdersByStoreMapping(Collections.singletonList(request));
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
        }
        return response;
    }

    public BaseResponse refreshPlatformStore(EshopPlatformStoreMappingInDTO inDTO) {
        BaseResponse response = new BaseResponse(true);
        if (inDTO == null || inDTO.getEshopId() == null) {
            response.setSuccess(false);
            response.setMessage("入参为空！");
            return response;
        }
        Integer type = inDTO.getType();
        logger.info("刷新入参：" + JsonUtils.toJson(inDTO));
        try {
            EshopInfo eshopInfo = eshopMapper.queryEShopInfoByOrgId(CurrentUser.getProfileId(), inDTO.getEshopId());
            if (eshopInfo == null) {
                response.setSuccess(false);
                response.setMessage("网点信息为空！");
                return response;
            }
            List<EshopPlatformStoreMapping> onlineList = new ArrayList<>();
            if (type == PlatformStoreMappingTypeEnum.STORE_TYPE.getCode()) {
                boolean supported = EshopUtils.isFeatureSupported(EshopPlatformWareHouseFeature.class, eshopInfo.getEshopType());
                if (!supported) {
                    response.setSuccess(false);
                    response.setMessage(PlatformStoreMappingConstants.NO_SUPPORT_REFRESH);
                    return response;
                }
//                List<EshopPlatformWareHouse> platformStoreList = feature.getPlatformWarehouse(null);
                PlatformWarehouseRequest request = new PlatformWarehouseRequest();
                request.setShopId(eshopInfo.getOtypeId());
                List<EshopPlatformWareHouse> platformStoreList = eshopStoreService.getPlatformWarehouse(request);
                if (CollectionUtils.isEmpty(platformStoreList)) {
                    response.setSuccess(false);
                    response.setMessage(PlatformStoreMappingConstants.NO_STORE_INFO);
                    return response;
                }
                logger.info("该网店线上数据条数：{}，数据：{}", platformStoreList.size(), JsonUtils.toJson(platformStoreList));
                onlineList = buildPlatformStoreMappingList(platformStoreList, eshopInfo);
            } else if (type == PlatformStoreMappingTypeEnum.PLATFORM_TYPE.getCode()) {
                boolean featureSupported = EshopUtils.isFeatureSupported(EshopOnlineStoreFeature.class, eshopInfo.getEshopType());
                if (!featureSupported) {
                    response.setSuccess(false);
                    response.setMessage(PlatformStoreMappingConstants.NO_SUPPORT_REFRESH);
                    return response;
                }
                GetOnlineStoreRequest request = new GetOnlineStoreRequest();
                request.setShopId(eshopInfo.getOtypeId());
                List<EshopOnlineStoreInfo> onlineStore = eshopStoreService.getOnlineStore(request);
                if (CollectionUtils.isEmpty(onlineStore)) {
                    response.setSuccess(false);
                    response.setMessage(PlatformStoreMappingConstants.NO_STORE_INFO);
                    return response;
                }
                logger.info("该网店线上数据条数：{}，数据：{}", onlineStore.size(), JsonUtils.toJson(onlineStore));
                onlineList = buildPlatformWarehouseCorrespondList(onlineStore, eshopInfo);
            }

            Map<String, EshopPlatformStoreMapping> onlineMap = onlineList.stream()
                    .collect(Collectors.toMap(EshopPlatformStoreMapping::getPlatformStoreStockId, v -> v));
            List<EshopPlatformStoreMapping> localList = getDefaultPlatformStoreMappingsList(inDTO.getEshopId(), inDTO.getType());
            List<EshopPlatformStoreMapping> updateList = new ArrayList<>();
            Map<String, EshopPlatformStoreMapping> deleteMap = new HashMap<>();
            for (EshopPlatformStoreMapping local : localList) {
                String key = local.getPlatformStoreStockId();
                if (onlineMap.containsKey(key)) {
                    EshopPlatformStoreMapping online = onlineMap.get(key);
                    onlineList.remove(online);
                    online.setId(local.getId());
                    online.setKtypeId(local.getKtypeId());
                    online.setCorrespondFlag(local.getCorrespondFlag());
                    updateList.add(online);
                } else {
                    if (local.getSource() == PlatformStoreMappingConstants.ONLINE_SOURCE) {
                        if (eshopInfo.getEshopType() == ShopType.TaoBao
                                && local.getPlatformStoreType() == WareHouseBusinessType.ONLINE_STORE.getCode()) {
                            local.setDeleted(true);
                        } else if (eshopInfo.getEshopType() != ShopType.TaoBao) {
                            local.setDeleted(true);
                        }

                        deleteMap.put(key, local);
                    }
                }

            }
            List<EshopPlatformStoreMapping> deleteList = new ArrayList<>(deleteMap.values());
            if (CollectionUtils.isNotEmpty(deleteList)) {
                updateList.addAll(deleteList);
                logger.info("线上比本地少了条数：{}，数据：{}", deleteList.size(), JsonUtils.toJson(deleteList));
            }
            eshopBusinessService.updateRefreshPlatformStore(onlineList, updateList,null);
            return response;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            response.setSuccess(false);
            String error = getRefreshErrorMsg(type);
            response.setMessage(error + e.getMessage());
            return response;
        }
    }

    private String getRefreshErrorMsg(Integer type) {
        String error = "";
        if (type == PlatformStoreMappingTypeEnum.STORE_TYPE.getCode()) {
            error = "刷新门店/网点失败，原因：";
        } else if (type == PlatformStoreMappingTypeEnum.PLATFORM_TYPE.getCode()) {
            error = "刷新平台仓库失败，原因：";
        }
        return error;
    }

    private List<EshopPlatformStoreMapping> buildPlatformWarehouseCorrespondList(List<EshopOnlineStoreInfo> onlineStore, EshopInfo eshopInfo) {
        List<EshopPlatformStoreMapping> list = new ArrayList<>();
        for (EshopOnlineStoreInfo store : onlineStore) {
            EshopPlatformStoreMapping platformStoreMapping = new EshopPlatformStoreMapping();
            platformStoreMapping.setProfileId(CurrentUser.getProfileId());
            platformStoreMapping.setEshopId(eshopInfo.getOtypeId());
            platformStoreMapping.setId(UId.newId());
            platformStoreMapping.setPlatformStoreStockId(store.getStoreCode());
            platformStoreMapping.setKtypeId(BigInteger.ZERO);
            platformStoreMapping.setCreateTime(new Date());
            platformStoreMapping.setPlatformStoreName(StringUtils.isEmpty(store.getStoreName())?store.getStoreCode():store.getStoreName());
            StringBuffer fullAddress = getFullAddress1(store);
            platformStoreMapping.setPlatformStoreAddress(fullAddress.toString());
            platformStoreMapping.setPlatformStoreType(store.getStoreType());
            platformStoreMapping.setBusinessLabel(store.getLabel());
            platformStoreMapping.setCorrespondFlag(false);
            platformStoreMapping.setDeleted(false);
            platformStoreMapping.setSource(PlatformStoreMappingConstants.ONLINE_SOURCE);
            platformStoreMapping.setType(PlatformStoreMappingTypeEnum.PLATFORM_TYPE.getCode());
            list.add(platformStoreMapping);
        }
        return list;
    }

    private StringBuffer getFullAddress1(EshopOnlineStoreInfo store) {
        StringBuffer fullAddress = new StringBuffer();
        if (StringUtils.isNotEmpty(store.getProvince())
                && StringUtils.isNotEmpty(store.getCity())
                && StringUtils.isNotEmpty(store.getDistrict())) {
            fullAddress.append(store.getProvince());
            fullAddress.append(store.getCity());
            fullAddress.append(store.getDistrict());
            fullAddress.append(store.getAddress());
        } else {
            fullAddress.append(store.getAddress());
        }
        return fullAddress;
    }

    private StringBuffer getFullAddress2(EshopPlatformWareHouse store) {
        StringBuffer fullAddress = new StringBuffer();
        if (StringUtils.isNotEmpty(store.getProvince())
                && StringUtils.isNotEmpty(store.getCity())
                && StringUtils.isNotEmpty(store.getDistrict())) {
            fullAddress.append(store.getProvince());
            fullAddress.append(store.getCity());
            fullAddress.append(store.getDistrict());
            fullAddress.append(store.getAddress());
        } else {
            fullAddress.append(store.getAddress());
        }
        return fullAddress;
    }

    private List<EshopPlatformStoreMapping> buildPlatformStoreMappingList
            (List<EshopPlatformWareHouse> platformStoreList
                    , EshopInfo eshopInfo) {
        List<EshopPlatformStoreMapping> list = new ArrayList<>();
        for (EshopPlatformWareHouse platformWareHouse : platformStoreList) {
            EshopPlatformStoreMapping platformStoreMapping = new EshopPlatformStoreMapping();
            platformStoreMapping.setId(UId.newId());
            platformStoreMapping.setProfileId(CurrentUser.getProfileId());
            platformStoreMapping.setCreateTime(new Date());
            platformStoreMapping.setOtypeName(eshopInfo.getFullname());
            platformStoreMapping.setEshopId(eshopInfo.getOtypeId());
            platformStoreMapping.setKtypeId(BigInteger.ZERO);
            platformStoreMapping.setPlatformStoreStockId(platformWareHouse.getStoreCode());
            platformStoreMapping.setPlatformStoreName(platformWareHouse.getStoreName());
            StringBuffer fullAddress = getFullAddress2(platformWareHouse);
            platformStoreMapping.setPlatformStoreAddress(fullAddress.toString());
            platformStoreMapping.setPlatformStoreType(platformWareHouse.getBusinessType().getCode());
            platformStoreMapping.setType(PlatformStoreMappingTypeEnum.STORE_TYPE.getCode());
            platformStoreMapping.setCorrespondFlag(false);
            platformStoreMapping.setBusinessLabel("");
            platformStoreMapping.setDeleted(false);
            platformStoreMapping.setSource(PlatformStoreMappingConstants.ONLINE_SOURCE);
            list.add(platformStoreMapping);
        }
        return list;
    }

    public List<Stock> getBaseKtypeListByProfileId() {
        QueryEShopParameter parameter = new QueryEShopParameter();
        parameter.setProfileId(CurrentUser.getProfileId());
        CommonUtil.initLimited(parameter);
        return eshopOrderBaseInfoMapper.getBaseKtypeListByProfileId(parameter);
    }

    @Transactional(rollbackFor = Exception.class)
    public List<PlatformStoreImportErrorData> uploadFile(MultipartFile importFile, BigInteger eshopId,
                                                         String eshopName, BigInteger ktypeId, Integer type) {
        String name = importFile.getOriginalFilename();
        if (!StringUtils.endsWithIgnoreCase(name, StringConstantEnum.XLS.getSymbol())
                && !StringUtils.endsWith(name, StringConstantEnum.XLSX.getSymbol())) {
            throw new RuntimeException("文件格式错误，目前只支持 xls，xlsx 格式的文件进行导入");
        }
        if (ktypeId != null) {
            boolean ktypeDeleted = baseSvc.checkKtypeDeleted(CurrentUser.getProfileId(), ktypeId);
            if (ktypeDeleted) {
                throw new RuntimeException(PlatformStoreMappingConstants.KTYPE_IS_DELETE_OR_STOP);
            }
        }
        List<EshopPlatformStoreMapping> mappingList = getDefaultPlatformStoreMappingsList(eshopId, null);
        listener = new PlatformStoreMappingListener(eshopId, eshopName, ktypeId, platformStoreMappingMapper,
                mappingList);
        try {
            ZipSecureFile.setMinInflateRatio(-1.0d);
            EasyExcel.read(importFile.getInputStream(), PlatformStoreMappingData.class, listener).sheet().doRead();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            if (e instanceof ExcelAnalysisException) {
                throw new RuntimeException("Excel模板格式不匹配！");
            }
            if (e instanceof RuntimeException) {
                throw new RuntimeException(e.getMessage());
            }
            throw new RuntimeException("文件IO读取异常");
        }
        return listener.errorDataList;
    }

    @Transactional(rollbackFor = Exception.class)
    public List<PlatformStoreImportErrorData> uploadFileNew(MultipartFile importFile, List<Otype> eshoptypes,
                                                            String eshopName, BigInteger ktypeId, Integer type,ProcessLoggerImpl processLogger) {
        String name = importFile.getOriginalFilename();
        if (!StringUtils.endsWithIgnoreCase(name, StringConstantEnum.XLS.getSymbol())
                && !StringUtils.endsWith(name, StringConstantEnum.XLSX.getSymbol())) {
            processLogger.appendMsg("文件格式错误，目前只支持 xls，xlsx 格式的文件进行导入");
            throw new RuntimeException("文件格式错误，目前只支持 xls，xlsx 格式的文件进行导入");
        }
        if (ktypeId != null) {
            boolean ktypeDeleted = baseSvc.checkKtypeDeleted(CurrentUser.getProfileId(), ktypeId);
            if (ktypeDeleted) {
                processLogger.appendMsg("该本地仓库已停用或删除，请刷新当前页面！");
                throw new RuntimeException(PlatformStoreMappingConstants.KTYPE_IS_DELETE_OR_STOP);
            }
        }
        if (type !=null && type == 99 ){
            listenerNew = new PlatformOnlineStoreMappingListener(eshoptypes, eshopName, ktypeId, platformStoreMappingMapper,processLogger,this);
            try {
                ZipSecureFile.setMinInflateRatio(-1.0d);
                EasyExcel.read(importFile.getInputStream(), PlatformOnlineStoreMappingData.class, listenerNew).sheet().doRead();
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
                if (e instanceof ExcelAnalysisException) {
                    processLogger.appendMsg("Excel模板格式不匹配！");
                    throw new RuntimeException("Excel模板格式不匹配！");
                }
                if (e instanceof RuntimeException) {
                    processLogger.appendMsg("解析出错:" + e.getMessage());
                    throw new RuntimeException(e.getMessage());
                }
                processLogger.appendMsg("文件IO读取异常");
                throw new RuntimeException("文件IO读取异常");
            }
            return listenerNew.errorDataList;
        }
        throw new RuntimeException("暂不支持除网店仓库对应页面以外的使用");

    }


    @Transactional(rollbackFor = Exception.class)
    public List<PlatformStoreImportErrorData> uploadFileWarehouseCorrespond(MultipartFile importFile, BigInteger eshopId,
                                                                            String eshopName, BigInteger ktypeId) {
        String name = importFile.getOriginalFilename();
        if (!StringUtils.endsWithIgnoreCase(name, StringConstantEnum.XLS.getSymbol())
                && !StringUtils.endsWith(name, StringConstantEnum.XLSX.getSymbol())) {
            throw new RuntimeException("文件格式错误，目前只支持 xls，xlsx 格式的文件进行导入");
        }
        if (ktypeId != null) {
            boolean ktypeDeleted = baseSvc.checkKtypeDeleted(CurrentUser.getProfileId(), ktypeId);
            if (ktypeDeleted) {
                throw new RuntimeException(PlatformStoreMappingConstants.KTYPE_IS_DELETE_OR_STOP);
            }
        }
        List<EshopPlatformStoreMapping> mappingList = getDefaultPlatformStoreMappingsList(eshopId, null);
        warehouseCorrespondListener = new PlatformWarehouseCorrespondListener(eshopId, eshopName, ktypeId, platformStoreMappingMapper,
                mappingList);
        try {
            ZipSecureFile.setMinInflateRatio(-1.0d);
            EasyExcel.read(importFile.getInputStream(), PlatformWarehouseCorrespondMappingData.class, warehouseCorrespondListener).sheet().doRead();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            if (e instanceof ExcelAnalysisException) {
                throw new RuntimeException("Excel模板格式不匹配！");
            }
            if (e instanceof RuntimeException) {
                throw new RuntimeException(e.getMessage());
            }
            throw new RuntimeException("文件IO读取异常");
        }
        return warehouseCorrespondListener.errorDataList;
    }

    protected List<EshopPlatformStoreMapping> getDefaultPlatformStoreMappingsList(BigInteger eshopId, Integer type) {
        EshopPlatformStoreMapping mapping = new EshopPlatformStoreMapping();
        mapping.setProfileId(CurrentUser.getProfileId());
        mapping.setEshopId(eshopId);
        if (type != null) {
            mapping.setType(type);
        } else {
            mapping.setType(PlatformStoreMappingTypeEnum.PLATFORM_TYPE.getCode());
        }
        if (type != null && type == 99){
            mapping.setType(null);
        }
        mapping.setDeleted(false);
        return platformStoreMappingMapper.queryListByEshopId(mapping);
    }

    public BaseResponse updateById(EshopPlatformStoreMappingInDTO inDTO) {
        BaseResponse response = new BaseResponse(true);
        try {
            if (inDTO.getKtypeId() != null) {
                boolean ktypeDeleted = baseSvc.checkKtypeDeleted(CurrentUser.getProfileId(), inDTO.getKtypeId());
                if (ktypeDeleted) {
                    response.setSuccess(false);
                    response.setMessage(PlatformStoreMappingConstants.KTYPE_IS_DELETE_OR_STOP);
                    return response;
                }
            }
            EshopPlatformStoreMapping request = new EshopPlatformStoreMapping();
            BeanUtils.copyProperties(inDTO, request);
            if (inDTO.getCorrespondFlag() != null && inDTO.getCorrespondFlag() == PlatformStoreMappingConstants.NO_CORRESPOND) {
                request.setCorrespondFlag(false);
            } else if (inDTO.getCorrespondFlag() != null && inDTO.getCorrespondFlag() == PlatformStoreMappingConstants.CORRESPOND) {
                request.setCorrespondFlag(true);
            }
            request.setProfileId(CurrentUser.getProfileId());
            if (StringUtils.isEmpty(inDTO.getPlatformStoreAddress())) {
                BigInteger tempId = request.getId();
                request.setId(null);
                EshopPlatformStoreMapping store = platformStoreMappingMapper.queryPlatformStoreByEshopId(request);
                if (store != null) {
                    response.setSuccess(false);
                    response.setMessage("修改失败：已有该网店的平台仓库ID。");
                    return response;
                }
                request.setId(tempId);
            }
            request.setDeleted(null);
            platformStoreMappingMapper.update(request);
            GetBeanUtil.getBean(EshopSaleOrderService.class).updateOrdersByStoreMapping(Collections.singletonList(request));
            return response;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            response.setSuccess(false);
            response.setMessage("修改失败，原因：" + e.getMessage());
            return response;
        }

    }

    public List<Stock> getBaseKtypeListByProfileIdForRefund() {
        QueryEShopParameter parameter = new QueryEShopParameter();
        parameter.setProfileId(CurrentUser.getProfileId());
        CommonUtil.initLimited(parameter);
        return eshopOrderBaseInfoMapper.getBaseKtypeListByProfileIdForRefund(parameter);
    }

    public void refreshOnlineStore(RefreshOnlineStoreParam param) {
        ProcessLoggerImpl processLogger = param.getProcessLogger();
        processLogger.appendMsg("开始刷新");
        if (param == null || param.getEshopId() == null) {
            processLogger.appendMsg("该平台未获取到店铺id,请联系技术处理!");
            processLogger.doFinish();
            return;
        }
        boolean platformWarehouseSupported = EshopUtils.isFeatureSupported(EshopPlatformWareHouseFeature.class, ShopType.valueOf(param.getEshopType()));
        boolean onlineStoreSupported = EshopUtils.isFeatureSupported(EshopOnlineStoreFeature.class, ShopType.valueOf(param.getEshopType()));
        if (!platformWarehouseSupported && !onlineStoreSupported) {
            processLogger.appendMsg("该平台不支持刷新客户/供应商");
            processLogger.doFinish();
            return;
        }

        try {
            EshopInfo eshopInfo = eshopMapper.queryEShopInfoByOrgId(CurrentUser.getProfileId(), param.getEshopId());
            if (eshopInfo == null) {
                processLogger.appendMsg("该平台店铺信息,请联系技术处理!");
                processLogger.doFinish();
                return;
            }
            List<EshopPlatformStoreMapping> onlineList = new ArrayList<>();
            if (platformWarehouseSupported) {
                PlatformWarehouseRequest request = new PlatformWarehouseRequest();
                request.setShopId(eshopInfo.getOtypeId());
                processLogger.appendMsg("开始下载平台仓库.....");
                try {
                    List<EshopPlatformWareHouse> platformStoreList = eshopStoreService.getPlatformWarehouse(request);
                    if (CollectionUtils.isNotEmpty(platformStoreList)) {
                        processLogger.appendMsg(String.format("从接口下载到%s条平台仓库信息", platformStoreList.size()));
                        onlineList = buildPlatformStoreMappingList(platformStoreList, eshopInfo);
                        processLogger.appendMsg(String.format("构建了%s条平台仓库信息", onlineList.size()));
                    }
                } catch (Exception e) {
                    logger.error("账套id:{},店铺id:{},下载平台仓库报错:{}", CurrentUser.getProfileId(), eshopInfo.getOtypeId(), e.getMessage(),e);
                    processLogger.appendMsg("下载平台仓库报错:"+ e.getMessage());
                }
            }
            if (onlineStoreSupported) {
                GetOnlineStoreRequest request = new GetOnlineStoreRequest();
                request.setShopId(eshopInfo.getOtypeId());
                try {
                    List<EshopOnlineStoreInfo> onlineStore = eshopStoreService.getOnlineStore(request);
                    if (CollectionUtils.isNotEmpty(onlineStore)) {
                        processLogger.appendMsg(String.format("从接口下载到%s条门店仓库信息", onlineStore.size()));
                        List<EshopPlatformStoreMapping> list = buildPlatformWarehouseCorrespondList(onlineStore, eshopInfo);
                        processLogger.appendMsg(String.format("构建了%s条门店仓库信息", onlineList.size()));
                        if (CollectionUtils.isNotEmpty(list)){
                            onlineList.addAll(list);
                        }
                    }
                }catch (Exception e) {
                    logger.error("账套id:{},店铺id:{},下载门店仓库报错:{}", CurrentUser.getProfileId(), eshopInfo.getOtypeId(), e.getMessage(),e);
                    processLogger.appendMsg("下载门店仓库报错:"+ e.getMessage());
                }

            }
            if (CollectionUtils.isEmpty(onlineList)){
                processLogger.appendMsg("没有获取到线上网店仓库...");
                processLogger.appendMsg("刷新结束");
                processLogger.doFinish();
                return;
            }
            Map<String, EshopPlatformStoreMapping> onlineMap = onlineList.stream()
                    .collect(Collectors.toMap(EshopPlatformStoreMapping::getPlatformStoreStockId, v -> v));
            List<EshopPlatformStoreMapping> localList = getDefaultPlatformStoreMappingsList(param.getEshopId(), 99);
            List<EshopPlatformStoreMapping> updateList = new ArrayList<>();
            Map<String, EshopPlatformStoreMapping> deleteMap = new HashMap<>();
            for (EshopPlatformStoreMapping local : localList) {
                String key = local.getPlatformStoreStockId();
                if (onlineMap.containsKey(key)) {
                    EshopPlatformStoreMapping online = onlineMap.get(key);
                    onlineList.remove(online);
                    online.setId(local.getId());
                    online.setKtypeId(local.getKtypeId());
                    online.setCorrespondFlag(local.getCorrespondFlag());
                    updateList.add(online);
                } else {
                    if (local.getSource() == PlatformStoreMappingConstants.ONLINE_SOURCE) {
                        if (eshopInfo.getEshopType() == ShopType.TaoBao
                                && local.getPlatformStoreType() == WareHouseBusinessType.ONLINE_STORE.getCode()) {
                            local.setDeleted(true);
                        } else if (eshopInfo.getEshopType() != ShopType.TaoBao) {
                            local.setDeleted(true);
                        }

                        deleteMap.put(key, local);
                    }
                }

            }
            List<EshopPlatformStoreMapping> deleteList = new ArrayList<>(deleteMap.values());
            if (CollectionUtils.isNotEmpty(deleteList)) {
                updateList.addAll(deleteList);
                logger.info("线上比本地少了条数：{}，数据：{}", deleteList.size(), JsonUtils.toJson(deleteList));
            }
            eshopBusinessService.updateRefreshPlatformStore(onlineList, updateList,processLogger);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            processLogger.appendMsg("刷新网店仓库报错:"+ e.getMessage());
        }
        processLogger.appendMsg("刷新结束");
        processLogger.doFinish();
    }
}

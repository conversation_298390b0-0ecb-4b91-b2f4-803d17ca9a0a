<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.member.mapper.SsCardAssertBillMapper">
    <insert id="saveBatchBill">
        insert into ss_card_assert_bill
        ( id,etype_id,vchtype,vchcode,memo,statused,profile_id,source_operation,create_time,tradeId,vip_id, billNumber, source_type)
        values
        <foreach collection="dto" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.etypeId},
            #{item.vchtype},
            #{item.vchcode},
            #{item.memo},
            #{item.statused},
            #{item.profileId},
            #{item.sourceOperation},
            #{item.createTime},
            #{item.tradeId},
            #{item.vipId},
            #{item.billNumber},
            #{item.sourceType}
            )
        </foreach>
    </insert>

    <resultMap id="vipAssertsChangeDtoResult" type="com.wsgjp.ct.sale.biz.member.model.dto.vip.VipAssertsBillDto">
        <id column="scab_id" property="id"/>
        <result column="etype_id" property="etypeId"/>
        <result column="vchtype" property="vchtype"/>
        <result column="vchcode" property="vchcode"/>
        <result column="scab_memo" property="memo"/>
        <result column="statused" property="statused"/>
        <result column="vip_id" property="vipId"/>
        <result column="billNumber" property="billNumber"/>
        <result column="create_time" property="createTime"/>
        <result column="source_operation" property="sourceOperation"/>
        <collection property="assertsBillDetailDtoList"
                    ofType="com.wsgjp.ct.sale.biz.member.model.dto.vip.VipAssertsBillDetailDto">
            <id column="scabd_id" property="id"/>
            <result column="qty" property="qty"/>
            <result column="typed" property="typed"/>
            <result column="scabd_memo" property="memo"/>
            <result column="assert_id" property="assertId"/>
            <result column="cardTemplateId" property="cardTemplateId"/>
            <result column="card_assert_bill_id" property="cardAssertBillId"/>
            <result column="card_type" property="cardType"/>
            <result column="change_type" property="changeType"/>
        </collection>
    </resultMap>

    <select id="selectAssertBill" resultMap="vipAssertsChangeDtoResult">
        select
        scab.id as scab_id,
        scab.etype_id,
        scab.vchtype,
        scab.vchcode,
        scab.memo as scab_memo,
        scab.statused,
        scab.billNumber,
        scab.create_time,
        scab.source_operation,
        scabd.id as scabd_id,
        scabd.qty,
        scabd.typed,
        scabd.memo as scabd_memo,
        scabd.assert_id,
        ifnull(sc.card_template_id, sct.id)  as cardTemplateId,
        scabd.card_assert_bill_id,
        scabd.change_type,
        scab.vip_id,
        ifnull(sc.card_type, sct.card_type) as card_type
        from ss_card_assert_bill scab
        left join ss_card_assert_bill_detail scabd on
        scab.id = scabd.card_assert_bill_id
            and scabd.profile_id = #{profileId}
        left join ss_card sc on
        sc.id = scabd.assert_id
            and sc.profile_id = #{profileId}
        left join ss_card_template sct on
            sct.id = scabd.assert_id
                and sct.profile_id = #{profileId}
        where scab.vchcode = #{vchcode}
        and scab.profile_id = #{profileId}
        and scab.deleted = 0
        and scab.create_time =
            (select max(create_time) from ss_card_assert_bill where profile_id = #{profileId} and vchcode = #{vchcode} and deleted = 0)
    </select>

    <select id="selectAssertBillList" resultMap="vipAssertsChangeDtoResult">
        select
            scab.id as scab_id,
            scab.etype_id,
            scab.vchtype,
            scab.vchcode,
            scab.memo as scab_memo,
            scab.statused,
            scab.billNumber,
            scabd.id as scabd_id,
            scabd.qty,
            scabd.typed,
            scabd.memo as scabd_memo,
            scabd.assert_id,
            scabd.card_assert_bill_id,
            scabd.change_type,
            ifnull(sc.card_type, sct.card_type) as card_type
        from ss_card_assert_bill scab
                 left join ss_card_assert_bill_detail scabd on
            scab.id = scabd.card_assert_bill_id
                and scabd.profile_id = #{profileId}
                 left join ss_card sc on
            sc.id = scabd.assert_id
                and sc.profile_id = #{profileId}
                 left join ss_card_template sct on
            sct.id = scabd.assert_id
                and sct.profile_id = #{profileId}
        where scab.profile_id = #{profileId}
          and scab.deleted = 0
          and scab.vchcode in
        <foreach collection="vchcodes" item="vchcode" open="(" close=")" separator=",">
            #{vchcode}
        </foreach>
    </select>

    <select id="selectVipAssertChangeByVipId"
            resultType="com.wsgjp.ct.sale.biz.member.model.entity.vip.VipAssertChange">
        select
            scabd.create_time as createTime,
            scabd.qty,
            scabd.typed,
            scabd.change_type,
            ifnull(sca.card_type, sct.card_type) as card_type,
            sct.fullname as cardName,
            scab.source_operation as sourceOperation,
            scab.source_type
        from
            ss_card_assert_bill scab
                left join ss_card_assert_bill_detail scabd on
                scab.id = scabd.card_assert_bill_id
                    and scabd.profile_id = #{profileId}
                left join ss_card sca on
                scabd.assert_id = sca.id
                    and sca.profile_id = #{profileId}
                left join ss_card_template sct on
                sca.card_template_id  = sct.id
                    and sct.profile_id = #{profileId}
        where
            scab.profile_id = #{profileId}
          and scab.vip_id = #{vipId}
          and scab.deleted = 0
          and scabd.typed != 5
          and scab.statused != 1
        order by
            scabd.create_time desc,
            scabd.id
    </select>
</mapper>

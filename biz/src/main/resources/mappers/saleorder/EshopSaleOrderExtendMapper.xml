<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopSaleOrderExtendMapper">
    <select id="queryByProfileAndVchcode" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderExtendEntity">
        select re.eshop_order_id,re.export_count,re.order_number,re.otype_id,re.profile_id,re.create_time,re.update_time
        from pl_eshop_sale_order_extend re where profile_id = #{profileId} and eshop_order_id = #{eshopOrderId}
    </select>

    <select id="queryAdvanceByProfileAndVchcode" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.advance.EshopAdvanceOrderPlaform">
        select re.vchcode,re.export_count,re.profile_id,re.create_time,re.update_time
        from td_orderbill_platform re where profile_id = #{profileId} and vchcode = #{eshopOrderId}
    </select>

    <update id="updateExportCountByProfileAndVchcode">
        update pl_eshop_sale_order_extend set export_count = #{exportCount} where profile_id = #{profileId} and eshop_order_id = #{eshopOrderId}
    </update>

    <update id="updateAdvanceExportCountByProfileAndVchcode">
        update td_orderbill_platform set export_count = #{exportCount} where profile_id = #{profileId} and vchcode = #{eshopOrderId}
    </update>

    <update id="updateMarkInfoByOrderNo">
        update pl_eshop_sale_order_extend set mark_info = #{markInfo} where profile_id = #{profileId}
        <if test="eshopOrderId!=null">
        and eshop_order_id in
        <foreach collection="eshopOrderId" index="index" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        </if>
    </update>

    <select id="queryCount" resultType="java.lang.Integer">
        select COUNT(*) from pl_eshop_sale_order_settlement where profile_id = #{profileId} and eshop_order_id = #{eshopOrderId}
    </select>

    <update id="updateCheckAccount">
        update pl_eshop_sale_order_settlement set check_accounts_status = #{status} where profile_id = #{profileId}
        <if test="eshopOrderId!=null">
        and eshop_order_id in
        <foreach collection="eshopOrderId" index="index" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        </if>
    </update>

    <insert id="insertEshopRefundExtendEntity">
        insert into pl_eshop_sale_order_extend ( `eshop_order_id`,
                                                `export_count`,
                                                `order_number`,
                                                `otype_id`,
                                                `profile_id`)
        VALUES (#{eshopOrderId},
                #{exportCount},
                #{orderNumber},
                #{otypeId},
                #{profileId})
    </insert>

    <insert id="insertEshopAdvanceExtendEntity">
        insert into pl_eshop_sale_order_advance_extend ( `eshop_order_id`,
                                                `export_count`,
                                                `order_number`,
                                                `otype_id`,
                                                `profile_id`)
        VALUES (#{eshopOrderId},
                #{exportCount},
                #{orderNumber},
                #{otypeId},
                #{profileId})
    </insert>

    <update id="updateGatherStatusByEshopOrderId">
        update pl_eshop_sale_order_extend set gather_status = #{gatherStatus} where profile_id = #{profileId}
        <if test="eshopOrderId!=null">
            and eshop_order_id in
            <foreach collection="eshopOrderId" index="index" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
    </update>

    <update id="updatePayStateByRefundNumber">
        update pl_eshop_refund set pay_state = #{payState},pay_time=#{payTime}
        where profile_id = #{profileId}
        <if test="refundNumber!=null">
            and trade_refund_order_number in
            <foreach collection="refundNumber" index="index" item="number" open="(" separator="," close=")">
                #{number}
            </foreach>
        </if>
    </update>

    <update id="updateSaleOrderGatherInfo">
        <foreach collection="list" item="item" separator=";">
            update pl_eshop_sale_order_extend
            set gather_status = #{item.gatherStatus},gathered_total = #{item.gatheredTotal}
            where profile_id=#{item.profileId} and eshop_order_id=#{item.eshopOrderId}
        </foreach>
    </update>

    <select id="GetSaleOrderGatherInfo" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderExtendEntity">
        select profile_id,eshop_order_id,gather_status,gathered_total
        from pl_eshop_sale_order_extend
         where profile_id=#{profileId}
        and eshop_order_id in
        <foreach collection="eshopOrderId" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

</mapper>
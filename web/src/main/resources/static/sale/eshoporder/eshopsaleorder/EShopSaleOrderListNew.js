Type.registerNamespace('sale.eshoporder.eshopsaleorder');
sale.eshoporder.eshopsaleorder.EShopSaleOrderListNewAction = function () {
    sale.eshoporder.eshopsaleorder.EShopSaleOrderListNewAction.initializeBase(this);
};


sale.eshoporder.eshopsaleorder.EShopSaleOrderListNewAction.prototype = {
    _copyText: "",
    _maingridSelectedAction: null,
    _maingridSelectedChangedDelay: 500,
    _finalqueryCode: "eshop_sale_order_query",
    markTypeList: [],
    flagsList: [],
    refreshExceptionStatusEventName: null,
    refreshExceptionStatusNotifyId: null,
    _customFilterColumn: [],
    _listeners: null,
    queryConfig: null,
    customerQuerys: null,
    _queryHideParams: [],
    isFirstQueryInit: false,
    hasJDAuthShop: false,
    customerQueryId: null,
    context: function (cb) {
        var myDate = new Date();
        var time = myDate.getTime() - (6 * 24 * 60 * 60 * 1000);
        myDate.setTime(time);
        var typeKey = "timeType,shopType,invoiceState,invoiceType,invoiceCategory,tradeStatus,filterKeyTypeNew,businessType,processState," +
            "customRefundStatus,orderType,createType,payType,sellerFlag,quickFilterType,deliverProcessStatus,orderTimingType," +
            "tradeType,selfDeliveryMode,gatherStatus,deliverTypeSource,paymentMode,detailProcessState,modeOfPayment,payTimeType,reSendState," +
            "platformConfirmStateByOrder,logisticsState,platformQcResult,platformIdentifyResult,deleteState,exportState,conditionDataSource,freightInterceptStatus";
        var mapData = $common.ajaxSync({
            url: "/sale/eshoporder/enum/getEnumStateMap/" + typeKey,
            data: null,
            type: 'get',
            router: 'ngp'
        });
        if (mapData.code != "200") {
            Sys.UI.MessageBox.alert("服务请求出错,请稍后重试[" + mapData.message + "]");
            return;
        }
        var createType = this.filterCreateType(mapData.data.createType);
        this.flagsList = mapData.data.sellerFlag;
        var parameter = {};
        parameter.queryVirtual = true;
        parameter.notAllowOCategorys=[10];
        var eshopDatas = $common.ajaxSync({
            url: "sale/eshoporder/eshop/getEshopByPlatformTypes",
            data: parameter,
            type: 'post',
            router: 'ngp'
        });
        var customerQuerys = $common.ajaxSync({
            url: "sale/eshoporder/customerQuery/getList",
            data: this._finalqueryCode,
            type: 'post',
            router: 'ngp'
        });
        var markType = $common.ajaxSync({
            url: "sale/eshoporder/common/getEshopOrderMarkItems/ALL",
            data: null,
            type: 'get',
            router: 'ngp'
        });
        this.markTypeList = markType.data;
        var parameter = {};
        var config = $common.ajaxSync({url: "sale/eshoporder/order/initConfig", data: parameter, router: 'ngp'});
        var industryConfig = $common.ajaxSync({
            url: "sale/eshoporder/order/industryConfig",
            data: parameter,
            router: 'ngp'
        });
        var pageInitInfo = $common.ajaxSync({
            url: "sale/eshoporder/order/getPageInitInfo/",
            data: null,
            type: 'get',
            router: 'ngp'
        });
        if (pageInitInfo && pageInitInfo.data) {
            this.hasJDAuthShop = pageInitInfo.data.hasJDAuthShop;
        }
        var showPael = $common.ajaxSync({url: "sale/eshoporder/order/showPanel", data: parameter, router: 'ngp'});
        var importOperate = $saleUtils.getPower("eshoporder.eshopImportOrderPage.view");
        var downloadOrderOperate = $saleUtils.getPower("eshoporder.eshopsaleorder.downloadOrder");
        var updateOrderAllDataOperate = $saleUtils.getPower("eshoporder.eshopsaleorder.updateOrderAllData");
        var showGather = $msModule.checkPoint('PlatformReconciliationFunc') && $saleUtils.getPower("eshoporder.eshopordergather.view");

        var data = {
            "beginTime": myDate.format("yyyy-MM-dd ") + "00:00:00",
            "endTime": (new Date()).format("yyyy-MM-dd ") + "23:59:59",
            "queryTimeType": mapData.data.timeType,
            "queryShopType": mapData.data.shopType,
            "tradeStateType": this.removeState(mapData.data.tradeStatus),
            "filterKeyTypeNew": mapData.data.filterKeyTypeNew,
            "businessTypes": mapData.data.businessType,
            "processState": mapData.data.processState,
            "invoiceState": mapData.data.invoiceState,
            "invoiceCategory": mapData.data.invoiceCategory,
            "refundStatus": mapData.data.customRefundStatus,
            "exportState": mapData.data.exportState,
            "conditionDataSource": mapData.data.conditionDataSource,
            "deleteState": mapData.data.deleteState,
            "orderSourceType": mapData.data.orderType,
            "reSendState": mapData.data.reSendState,
            "platformConfirmStateByOrder": mapData.data.platformConfirmStateByOrder,
            "logisticsState": mapData.data.logisticsState,
            "platformQcResult": mapData.data.platformQcResult,
            "platformIdentifyResult": mapData.data.platformIdentifyResult,
            "createType": createType,
            "orderTimingType": mapData.data.orderTimingType,
            "deliverProcessStatus": mapData.data.deliverProcessStatus,
            "payType": mapData.data.payType,
            "eshops": eshopDatas.data,
            "flagsSource": mapData.data.sellerFlag,
            "markTypeList": markType.data,
            "orderSaleType": mapData.data.tradeType,
            "selfDeliveryMode": mapData.data.selfDeliveryMode,
            "gatherStatus": mapData.data.gatherStatus,
            "freightInterceptStatus": mapData.data.freightInterceptStatus,
            "qtyLength": $ms.ngpConfig.Sys.sysDigitalQty,
            "totalLength": $ms.ngpConfig.Sys.sysDigitalTotal,
            "priceLength": $ms.ngpConfig.Sys.sysDigitalPrice,
            "taxLength": $ms.ngpConfig.Sys.sysDigitalTax,
            "maxQtyLen": $ms.ngpConfig.Sys.sysDigitalQty + 6,
            "maxTotalLen": $ms.ngpConfig.Sys.sysDigitalTotal + 14,
            "enabledTax": config.data.enabledTax,
            "enabledProps": industryConfig.data.enabledProps,
            "enableSub": $ms.ngpConfig.Sys.sysBusinessEnableSubUnitConfig,
            "showPanel": showPael.data.retailShow,
            "title": showPael.data.saleOrderTitle,
            "deliverType": mapData.data.deliverTypeSource,
            "paymentModes": mapData.data.paymentMode,
            "payTimeType": mapData.data.payTimeType,
            "disedTaxedTotalCaption": config.data.enabledTax ? "折后含税金额" : "折后金额",
            "disedTaxedPriceCaption": config.data.enabledTax ? "折后含税单价" : "折后单价",
            "disTaxedPriceCaption": config.data.enabledTax ? "买家结算含税单价" : "买家结算单价",
            "disTaxedTotalCaption": config.data.enabledTax ? "买家结算含税金额" : "买家结算金额",
            "detailProcessState": mapData.data.detailProcessState,
            "submitShowHtml": "不允许手工提交的订单：<span class='SkinColor'>网店发货流程方式=不走发货流程、网店=代运营网店、线上交易状态=交易关闭、整单退款成功、网店商品标记【不发货不记账】、网店商品未对应、订单已删除、仓库未对应</span>",
            "autoSubmitShowHtml": "不允许自动提交的订单：<span class='SkinColor'>线上交易状态=未付款/已发货/交易成功、订单已发货或交易成功，且发货方不是【平台发货】、订单未付款且无【未付款允许发货】标记、订单已删除</span>",
            "platformSendShowHtml": "平台仓发货订单：<span class='SkinColor'>订单无需人工处理将自动流转，订单在平台仓发货后（线上交易状态为已发货时），订单自动提交，并自动完成订单核算</span>",
            "jDHtml": "<p>当查询订单确定为非京东系平台订单时，</p><p>建议不选查询京东系订单，</p><p>否则会消耗京东平台的解密额度（平台解密规则原因）</p>",
            "modeOfPayment": mapData.data.modeOfPayment,
            "isWholeSale": $eshoppower.isWholesale(),
            "orderOperate": importOperate && downloadOrderOperate && updateOrderAllDataOperate,
            "showBatch": $ms.ngpConfig.Sys.sysIndustryEnabledBatch,
            "deleteOrRecoveryOrder": $saleUtils.getPower("eshoporder.eshopsaleorder.deleteOrRecoveryOrder"),
            "ptypeLabels": !pageInitInfo || !pageInitInfo.data ? null : pageInitInfo.data.ptypeLabels,
            "showLazy": $ms.ngpConfig.Sys.enabledGridShowLazy && $ms.ngpConfig.Sys.enabledGridShowLazy == 1 ? true : false,
            "showGather":showGather
        };
        setTimeout(function () {
            data.enabledTax = config.data.enabledTax;
        }, 1);
        this._rebuildGridExpandColumnDefaultExpandValue(data);
        cb(data);
    },

    initialize: function initialize() {
        sale.eshoporder.eshopsaleorder.EShopSaleOrderListNewAction.callBaseMethod(this, 'initialize');
        var form = this.get_form();
        var pageForm = this.queryString("form");
        var tradeOrderId;
        var pageName = "平台原始订单池";
        if (this && this.get_form() && this.get_form().get_caption()) {
            pageName = this.get_form().get_caption();
        }
        $analysisCloud.Util.addPubSystemLog("进入【" + pageName + "】");
        if (pageForm == "homePage") {
            this.initDateRangeFromHomePage(form);
            this.initKscxListViw(form)
            var type = this.queryString("type");
            this.isFirstQueryInit = false;
            switch (type) {
                case "yfk":
                    form.createType.set_value([1]);
                    form.tradeStatus.set_value([2, 3, 4, 6, 7]);
                    form.refundState.set_value([0, 1, 2, 3]);
                    break;
                case "wfk":
                    form.createType.set_value([1]);
                    form.tradeStatus.set_value([1]);
                    break;
                case "other":
                    form.createType.set_value([0, 2, 16]);
                    break;
                case "waitSubmit":
                    this.isFirstQueryInit = true;
                    form.quickQueryList.set_selectAll(false);
                    form.all_quickQueryDetails.set_selectAll(false);
                    form.all_quickQueryDetails.set_selectedRowIndex(0);
                    break;
                case "hasRefund":
                    form.mark.set_value(['200064', '200063']);
                    break;
                case "unRelation":
                    this.isFirstQueryInit = true;
                    form.quickQueryList.set_selectAll(false);
                    form.all_quickQueryDetails.set_selectAll(false);
                    form.all_quickQueryDetails.set_selectedRowIndex(1);
                    break;
                case "noKtype":
                    this.isFirstQueryInit = true;
                    form.mark.set_value(['90790005']);
                    break;
                default:
                    break;
            }
            this.query(form);
            this.initPageNecessaryInfo(form);
            return;
        }
        if (pageForm == "refund" || pageForm == "addEshopOrder") {
            tradeOrderId = this.queryString("tradeOrderId");
        }
        var myDate = new Date();
        var time = myDate.getTime() - (2 * 24 * 60 * 60 * 1000);
        if ($eshoppower.isSpeedMode()) {
            time = myDate.getTime();
            form.dateRange.set_selectedRowIndex(-1);
        }
        myDate.setTime(time);
        form.dateRange.set_date(myDate.format("yyyy-MM-dd ") + "00:00:00", (new Date()).format("yyyy-MM-dd ") + "23:59:59");
        this.initKscxListViw(form)
        if (tradeOrderId) {
            form.keyWord.set_value(tradeOrderId);
            this.isFirstQueryInit = false;
            this.query(form);
            return;
        }
        this.query(form);
        this.initPageNecessaryInfo(form);
        $saleUtils.loadJdNpsAuto();
        $jarviscommon.pddRiskControl();
    },

    initKscxListViw:function (form) {
        this.buildKscx();
        this.buildCustomerQueryDetail();
        form.quickQueryList.set_selectAll(true);
        form.all_quickQueryDetails.set_selectAll(false);
        if ($userCache.get('EshopSaleOrderCustomQuery')){
            this.doShowCustomerQuery(form);
        }
    },

    _rebuildGridExpandColumnDefaultExpandValue: function (contextData) {
        try {
            var defaultExpandValueKey = "saleOrderDetailExpand";
            defaultExpandValueKey = $saleUtils.getDetailCacheKey(defaultExpandValueKey);
            var defaultExpandValue = $saleUtils.getUserCache(defaultExpandValueKey);
            if (defaultExpandValue == null) {
                $saleUtils.setUserCache(defaultExpandValueKey, true);
                defaultExpandValue = true;
            }
            contextData["expandAll"] = defaultExpandValue;
        }catch (e) {
            contextData["expandAll"] = true;
        }
    },

    doExpandChange:function(sender,args){
        try {
            var form=sender.get_form();
            var defaultExpandValueKey = "saleOrderDetailExpand";
            defaultExpandValueKey = $saleUtils.getDetailCacheKey(defaultExpandValueKey);
            var expandAllValue=args.get_value() == 1? true:false;
            $saleUtils.setUserCache(defaultExpandValueKey, expandAllValue);
            var detailGrid = form.details;
            detailGrid.setExpandAllRows(expandAllValue);
            detailGrid.refresh();
            contextData["expandAll"]=expandAllValue;
        }catch (e) {

        }
    },

    doShowCustomerQuery: function (sender) {
        var form = sender.get_form();
        // if (form.customQueryList) {
        //     form.customQueryList.set_visible(!form.customQueryList.get_visible());
        //     form.queryButton.set_rightIcon(form.customQueryList.get_visible() ? 'aicon-up' : 'aicon-down');
        //     if (form.customQueryList.get_visible()){
        //         $userCache.set('EshopSaleOrderCustomQuery',form.customQueryList.get_visible());
        //     }else {
        //         $userCache.deleteCache('EshopSaleOrderCustomQuery');
        //     }
        // }
    },

    doQuickQueryItemRenderx: function(sender, args) {
        var data = args.get_data();
        var item = args.get_item();
        var form = this.get_form();
        if (!data.list || data.list.length == 0) {
            item.expand.set_visible(false);
        } else if (!Object.isEmpty(data.list)) {
            var cName = 'Radio ButtonView';
            if (data.detailClass) {
                cName = $skin.joinCssClass(cName, data.detailClass); // 单选
            }
            var id = data.key + "_quickQueryDetails";
            var lv = $createControl('ListView', {ID: id, DataSource: data.list, SelectField: id , CssClass: cName, UseSelect: true, OnItemClick: $createDelegate(this, this.doQuickQueryDetailClick),DataKeyField: 'id'}, form);
            var lt = $createControl('ListTemplate', {}, form, lv);
            $createControl('DataText', { DataField: 'text', CssClass: 'text ellipsis', DataCssField: 'textClass' }, form, lt);
            $createControl('DataText', { DataField: 'value', CssClass: 'value', DataCssField: 'valueClass' }, form, lt);
            lv.insertAfter(item);
            form.leftPanel.clear();
            item.addControl(form[id], false, true); // 自动释放
        }
    },

    buildKscx: function(waitSubmitCount,notMappingCount,refundingCount) {
            var list = [
                {
                    text: '全部',
                    key: 'all',
                    id:'all',
                    list: [
                        { id: 1, text: '待提交', value: waitSubmitCount ? (waitSubmitCount > 999 ? "999+" : waitSubmitCount) : "",key:'dtj',valueClass: 'redcolor'},
                        { id: 2, text: '未对应' , value: notMappingCount ? (notMappingCount > 999 ? "999+" : notMappingCount): "" ,key:'wdy',valueClass: 'redcolor'},
                        { id: 3, text: '退款中', value: refundingCount ? (refundingCount > 999 ? "999+" : refundingCount) : "",key:'tkz' ,valueClass: 'redcolor'}
                    ]
                }];
            var form = this.get_form();
            var saveData = form.leftPanel.saveData();
            var date = form.dateRange.get_date();
            var createType = form.createType.get_value();
            form.quickQueryList.refresh(list);
            form.leftPanel.dataBind(saveData);
            form.dateRange.set_date(date[0],date[1]);
            form.createType.set_value(createType);
            if (saveData.all_quickQueryDetails && saveData.customQueryDetail &&
                saveData.all_quickQueryDetails.length == 0 && saveData.customQueryDetail.length == 0){
                form.quickQueryList.set_selectAll(true);
            }
    },

    buildCustomerQueryDetail: function() {
        var customQueryConfig = $common.ajaxSync({
            url: "sale/eshoporder/customerQuery/getList",
            data: this._finalqueryCode,
            type: 'post',
            router: 'ngp'
        });
        var list = customQueryConfig.data;
        var arr = [];
        var obj = {};
        var detailArr = [];
        for (var i = 0; i < list.length; i++) {
            var listElement = list[i];
            var detail={};
            detail.id= listElement.customerQuery.id;
            detail.text= listElement.customerQuery.title;
            detail.params= listElement.details;
            detailArr.push(detail);
        }
        obj.details = detailArr;
        arr.push(obj)
        var form = this.get_form();
        form.customQueryList.refresh(arr);
    },

    doShowEshopOrderAutoSubmitConfig:function (sender) {
        var _this=this;
        var modifyForm = new Sys.UI.Form(sender, true);
        modifyForm.set_allowMove(false);
        modifyForm.showMDI("/sale/eshoporder/eshopsaleorder/EshopOrderAutoSubmitConfig.gspx", {});
    },

    doCustomQueryItemRenderx:function (sender, args) {
        var data = args.get_data();
        var item = args.get_item();
        var form = this.get_form();
        if (!data.details || data.details.length == 0) {5360
            item.expand.set_visible(false);
        } else if (!Object.isEmpty(data.details)) {
            if (form.customQueryDetail){
                form.removeControl(form.customQueryDetail, true);
            }
            var cName = 'Radio ButtonView';
            if (data.detailClass) {
                cName = $skin.joinCssClass(cName, data.detailClass); // 单选
            }
            var lv = $createControl('ListView', { ID:'customQueryDetail',DataSource: data.details,SelectField:'customQueryDetail', CssClass: cName, UseSelect: true,OnItemClick: $createDelegate(this, this.doCustomQueryClick), DataKeyField: 'id'}, form);
            var lt = $createControl('ListTemplate', {}, form, lv);
            $createControl('DataText', { DataField: 'text', CssClass: 'text ellipsis', DataCssField: 'textClass' }, form, lt);
            var hb = $createControl('HBlock', { CssClass: 'btns' }, form, lt);
            $createControl('DataText', { CssClass: 'aicon-close', OnClick: 'doRemoveQuickQueryItem' }, form, hb);
            lv.insertAfter(item);
            form.leftPanel.clear();
        }
    },

    doRemoveQuickQueryItem: function (sender, args, successFunc) {
        var data = args.get_data();
        var form = sender.get_form();
        args.get_event().cancel(); // 防止点图标选中
        var _this = this;
        Sys.UI.MessageBox.confirm("确认删除此查询条件？", function (result) {
            if (!result) {
                return;
            }
            var queryId = data.id;
            var result = $common.ajaxSync({
                type: 'get',
                url: "sale/eshoporder/customerQuery/delete/" + queryId,
                router: 'ngp'
            });
            if (result.message) {
                if (result.code != 200) {
                    $common.alert(result.message);
                    return;
                }
                $common.showOk('删除成功');
                _this.buildCustomerQueryDetail();
            }
        });
    },

    doQuickQueryAllClick: function (sender, args) {
        var data = args.get_data();
        var form = sender.get_form();
        var item = args.get_item();
        // form.all_quickQueryDetails.set_selectAll(false);
        form.quickQueryList.set_selectAll(true);
        // form.customQueryDetail.set_selectAll(false);
        this.query(sender);
    },

    doQuickQueryDetailClick: function (sender, args) {
        var data = args.get_data();
        var form = sender.get_form();
        var item = args.get_item();
        form.quickQueryList.set_selectAll(false);
        form.customQueryDetail.set_selectAll(false);
        this.query(sender);
    },

    initDateRangeFromHomePage: function (form) {
        var beginTime = this.queryString("beginTime");
        var dateFromHomePage = true;
        if (!beginTime) {
            var myDate = new Date();
            var time = myDate.getTime() - (29 * 24 * 60 * 60 * 1000);
            myDate.setTime(time);
            beginTime = myDate.format("yyyy-MM-dd ") + "00:00:00";
            dateFromHomePage = false;
        }
        form.dateRange.set_date(beginTime, (new Date()).format("yyyy-MM-dd ") + "23:59:59");
        form.quickDate.set_selectedIndex(-1);
        if(!dateFromHomePage){
            return;
        }
        var index = this.cheackTimeToQuickTime();
        form.quickDate.set_selectedIndex(index);
    },

    initPageNecessaryInfo: function (form) {
        //绑定基础配置中的支持基本信息字段
        form.details.enableDynamicColumn({
            subType: 5001,
            usedType: 1,
            mapping: {
                //商品编号
                "usercode": "ptypeCode",
                //商品名称
                "fullname": "ptypeName",
                //品牌
                "brandId": "brandName",
                //规格
                "standard": "ptypeStandard",
                //型号
                "ptypeType": "ptypeType",
                //简名 - 商品简名
                "shortname": "ptypeShortName",
                //拼音码
                //产地
                "ptypeArea": "ptypeArea",
                //商品税率(%) - 税率(%)
                "taxRate": "taxRate",
                //重量
                //长宽高
                //备注 - 商品备注
                "memo": "ptypeMemo",
                //图片
                "ptypePic": "showPicUrl"
            }
        });

        //移入弹窗绑定
        $common.addMouseEnterHandler(form.SubmitConfig.get_element(), $createDelegate(this, this.showSubmitConfigView));
        $common.addMouseLeaveHandler(form.SubmitConfig.get_element(), $createDelegate(this, this.hideSubmitConfigView));

        // form.DownBody._element.style["height"] = "240px";

        //
        var _this = this;
        _this.get_form().notify('refresh_from_add', function (data) {
            // 新增页面保存后按照原有条件刷新页面，如不需要只保留form.refresh()即可
            var queryParams = _this._buildQueryParams();
            if (queryParams) {
                $common.showLoading();
                setTimeout(function () {
                    var pager = _this.get_form().c_grid_Audit.get_pager();
                    pager.set_queryParams(queryParams);
                    pager.refresh({});
                    $common.hideLoading();
                }, 1);
            } else {
                form.refresh();
            }
        }, this)

        //异常单据徽标(徽标开关关闭，则不计算徽标)
        if (_this._getCalculateExceptionStatusConfig()) {
            // 绑定事件接收
            this.refreshExceptionStatusEventName = 'SaleOrderExceptionStatusRefreshEvent';
            this.refreshExceptionStatusNotifyId = $notify.bind(this.refreshExceptionStatusEventName, function (countExceptionMap) {
                _this.receiveExceptionStatusMessage(_this, countExceptionMap);
            });
            if (!window.$SaleEShopSaleOrderListNewExceptionStatusTimer) {
                window.$SaleEShopSaleOrderListNewExceptionStatusTimer = new EshopSaleOrderExceptionStatusTimer();
            }
            // 打开一个 DeliverBillList.gspx， pageCount + 1
            window.$SaleEShopSaleOrderListNewExceptionStatusTimer.incrementPageCount(this.refreshExceptionStatusEventName, this);
        }


        _this.doChangePtypeSelectType(form.ptypeType);
    },

    receiveExceptionStatusMessage: function (that, countExceptionMap) {
        if (!countExceptionMap){
            return;
        }
        that.buildKscx(countExceptionMap.get('19') , countExceptionMap.get('5') ,countExceptionMap.get('20') );
    },

    initComboRowSubQty: function (sender, args) {
        var form = sender.get_form();
        var grid = form.details;
        var rowIndex = args.get_rowIndex();
        var rowData = grid.findRowData(rowIndex);
        if (rowData && rowData.__haschild === 1) {
            args.set_text(null);
        }

    },

    updateExceptionNumber: function (node, number, isParent) {
        if (number) {
            if (!isParent && number > 999) {
                number = '999+';
            }
            // 如果没有创建过红点，创建一个
            if (!node.numberSpan) {
                var span = $common.createClassSpan('circle'); // 创建 span 标签
                node.numberSpan = span; // 存到属性里，后面方便更改
                span.innerText = isParent ? '!' : number;
                var nodeElement = node.get_nodeElement();
                nodeElement.appendChild(span);
            } else {
                // 创建过直接修改数值
                node.numberSpan.innerText = isParent ? '!' : number;
            }
        }
        // 没有异常并且之前创建过，移除
        else if (node.numberSpan) {
            // 不存在移除红点
            $removeNode(node.numberSpan);
            delete node.numberSpan;
        }
    },

    showSubmitConfigView: function (sender, e) {
        if (!e) e = sender; // hover的情况
        this.get_form().SubmitConfigView.appendAt(e.target);
    },
    hideSubmitConfigView: function (sender, e) {
        this.get_form().SubmitConfigView.close();
    },
    filterCreateType: function (createtype) {
        var arry = [];
        for (var i = 0; i < createtype.length; i++) {
            if (createtype[i].code == 0 || createtype[i].code == 1 || createtype[i].code == 16 || createtype[i].code == 2) {
                arry.push(createtype[i]);
            }
        }
        return arry;
    },

    areasSelectorInit: function (sender) {
        var form = sender.get_form();
        var pageparams = new Object();
        pageparams.selectvalue = form.areas.get_value() == null ? "" : form.areas.get_value().areasInfo;
        var url = "jarvis/common/AreaSelector.gspx";
        sender.set_selectorPage(url);
        sender.set_selectorPageParams(pageparams);
    },

    _getCalculateExceptionStatusConfig: function () {
        var result = $common.ajaxSync({
            url: "sale/jarvis/config/getCalculateExceptionStatusConfig",
            data: {},
            router: 'ngp'
        });
        if (result.code != 200) {
            $common.alert('查询徽标配置开关失败，请刷新页面重试...');
        } else {
            return result.data ? true : false;
        }
    },

    doShowQueryConfig: function (sender) {
        var _this = this;
        var form = sender.get_form();
        var queryConfigNodes = [];
        var queryConfigId = _this.queryConfig ? _this.queryConfig.id : 0;
        var queryConfig = _this.queryConfig;
        var customerQuerys = _this.customerQuerys;
        if (queryConfig) {
            Object.copyTo(queryConfig.nodes, queryConfigNodes);
            for (var i = 0; i < queryConfigNodes.length; i++) {
                if (queryConfigNodes[i].show) {
                    if (queryConfigNodes[i].customerQuery) {
                        var tempQuery = customerQuerys.filter(function (c) {
                            return c.customerQuery.id == queryConfigNodes[i].id;
                        });
                        queryConfigNodes[i].queryParams = tempQuery[0].details;
                    }
                }
            }
        } else {
            queryConfigNodes = form.quickQuery.saveDatas().filter(
                function (node) {
                    if (node && node.pid == 0) {
                        return true;
                    } else {
                        return false;
                    }
                }
            );
            queryConfigNodes.forEach(function (item) {
                item.show = true;
            });
        }
        var queryConfigForm = new Sys.UI.Form(sender, true);
        queryConfigForm.processType = "eshop_sale_order_query";
        queryConfigForm.queryConfigId = queryConfigId;
        queryConfigForm.add_loaded(function () {
            queryConfigForm.queryConfigGrid.dataBind(queryConfigNodes);
        });
        queryConfigForm.add_ok(function (data) {
            _this._doRefreshQuickQuery();
        });
        queryConfigForm.showMDI("/sale/eshoporder/common/CustomQueryConfig.gspx", {}, true);
    },

    _doRefreshQuickQuery: function () {
        var _this = this;
        var form = this.get_form();
        var typeKey = "quickFilterType";
        var mapData = $common.ajaxSync({
            url: "/sale/eshoporder/enum/getEnumStateMap/" + typeKey,
            data: null,
            type: 'get',
            router: 'ngp'
        });
        var customerQuerys = $common.ajaxSync({
            url: "sale/eshoporder/customerQuery/getList",
            data: "eshop_sale_order_query",
            type: 'post',
            router: 'ngp'
        });
        var customQueryConfig = $common.ajaxSync({
            url: "sale/eshoporder/customerQuery/queryPageConfigByPageName/" + "eshop_sale_order_query",
            data: null,
            type: 'get',
            router: 'ngp'
        });
        var quickStatus = $common.toTreeData(_this.getQueryNodes(mapData.data.quickFilterType, customQueryConfig.data, customerQuerys.data), 'name', 'code', 'pid', -1);
        form.quickQuery.set_dataSource(quickStatus);
        var rootNode = form.quickQuery.get_rootNode();
        form.quickQuery.set_selectedNode(rootNode.get_children()[0].get_children()[0]);
        var param = {
            sourceType: "ESHOP_ORDER",
            usePhase: "ORDER_QUERY"
        }
        window.$SaleEShopSaleOrderListNewExceptionStatusTimer.refreshOnce(this.refreshExceptionStatusEventName, param);
    },

    doQuickDateQuery: function (sender) {
        var form = sender.get_form();
        var day = form.quickDate.get_value();
        var dates = $craba.calcDate(day); // 按当前日期开始计算，返回参数（多少天）以前的区间。0表示1天（当前0点到24点）
        form.dateRange.set_date(dates[0], dates[1]);
        this.isFirstQueryInit = false;
        this.query(sender);
    },

    //时间范围改变触发查询
    timeRangeChange: function (sender) {
        var form = sender.get_form();
        form.quickDate.set_selectedIndex( this.cheackTimeToQuickTime());
        this.isFirstQueryInit = false;
        this.query(sender);
    },

    cheackTimeToQuickTime: function () {
        var result = -1;
        var form = this.get_form();
        var dateTime = form.dateRange.get_date();
        $common.checkTips(!dateTime[0], "请选择开始时间", form.dateRange);
        $common.checkTips(!dateTime[1], "请选择结束时间", form.dateRange);
        $common.checkTips(dateTime[0] > dateTime[1], "开始时间大于结束时间", form.dateRange);
        var beginTime = this.dateFormat("yyyy-MM-dd hh:mm:ss", dateTime[0]);
        var endTime = this.dateFormat("yyyy-MM-dd hh:mm:ss", dateTime[1]);
        var reduceTime = dateTime[1].getTime() / 1000 - parseInt(dateTime[0].getTime() / 1000);
        var reduceDay = parseInt(reduceTime / 60 / 60 / 24);        //相差天数
        switch (reduceDay) {
            case 0:
                result = this.checkThisTime($craba.calcDate(reduceDay), beginTime, endTime) ? 0 : -1;
                break;
            case 3:
                result = this.checkThisTime($craba.calcDate(reduceDay), beginTime, endTime) ? 1 : -1;
                break;
            default:
                result = this.checkThisTime($craba.calcDate(reduceDay), beginTime, endTime) ? 2 : -1;
                break;
        }
        return result;
    },
    checkThisTime: function (dates, beginTime, endTime) {
        var checkBeginTime = this.dateFormat("yyyy-MM-dd hh:mm:ss", dates[0]);
        var checkEndTime = this.dateFormat("yyyy-MM-dd hh:mm:ss", dates[1]);
        return (checkBeginTime == beginTime && checkEndTime == endTime);
    },

    getItemNodeParams: function (form, queryParams) {
        var params = {};
        if (!queryParams) {
            return params;
        }
        Array.forEach(queryParams, function (param) {
            switch (param.fieldValue) {
                case "businessTypes":
                    params["businessType"] = param.fieldText;
                    break;
                case "customerAreas":
                    params["areas"] = param.fieldText;
                    break;
                case "intSellerFlag":
                    params["sellerFlag"] = param.fieldText;
                    break;
                case "mentionDropItems":
                    break;
                case "mentionValues":
                    params["mentionValues"] = param.fieldText;
                    break;
                case "otypeIds":
                    params["otypeId"] = param.fieldText;
                    break;
                case "refundStates":
                    params["refundState"] = param.fieldText;
                    break;
                case "timeType":
                    params["timeType"] = param.fieldText;
                    break;
                default:
                    params[param.fieldValue] = param.fieldText;
            }
        });
        return params;
    },

    loadCustomerQuery: function (sender) {
        var form = this.get_form();
        var btn = $common.getBounds(form.customerSaveBtn._element); // 按钮的位置
        // (btn.x + "px"), ((btn.y - 40)  + "px")
        form.saveQuickQueryMenu.popupAt(form.customerSaveBtn, false, true);
        form.saveQuickQueryMenu._element.style["top"] = (btn.y - 145) + "px";
    },

    //保存自定义查询条件
    saveCustomerQuery: function (sender) {
        var form = sender.get_form();
        var saveData = form.leftPanel.saveData();
        var title = form.customerQueryTitle.get_text();
        if (!title) {
            $common.showHint("请输入自定义查询条件名称", form.customerQueryTitle);
            return;
        }
        var data = this._buildModifyQuickQueryParams(sender, false);
        var request = {};
        request.title = title;
        request.queryCode = this._finalqueryCode;
        request.queryParams = data;
        var url = "sale/eshoporder/customerQuery/add";
        var dataSource = form.customQueryList.get_dataSource();
        if (dataSource[0].details && dataSource[0].details.length>0){
            for (var i = 0; i < dataSource[0].details.length; i++) {
                var item = dataSource[0].details[i];
                if (item.text == request.title) {
                    $common.showHint("自定义查询条件名称已存在", form.updateQueryName);
                    return;
                }
            }
        }

        var result = $common.ajaxSync({url: url, data: request, router: 'ngp'});
        form.customerQueryTitle.set_text("");
        form.saveQuickQueryMenu.hide();
        if (result.code != 200) {
            $common.showError("保存失败," + result.message);
            return;
        }
        $common.showOk("自定义查询保存成功");
        this.buildCustomerQueryDetail();
    },

    cancelCustomerQuery: function (sender) {
        var form = sender.get_form();
        form.customerQueryTitle.set_text("");
        form.saveQuickQueryMenu.hide();
    },

    doCustomQueryClick:function (sender, args){
        var data = args.get_data();
        var form = sender.get_form();
        form.all_quickQueryDetails.set_selectAll(false);
        form.quickQueryList.set_selectAll(false);
        var saveData = form.leftPanel.saveData();
        if (!data) {
            return;
        }
        var params = this.getItemNodeParams(form, data.params);
        var dateTime = form.dateRange.get_date();
        params["beginTime"] = dateTime[0].format("yyyy-MM-dd HH:mm:ss");
        params["endTime"] = dateTime[1].format("yyyy-MM-dd HH:mm:ss");
        params["filterKeyTypeNew"] = form.filterKeyTypeNew.get_value();
        params["keyWord"] = form.keyWord.get_value();
        params["advanceTimeType"] = form.timeType.get_value();
        params["localRefundProcessState"] = params["refundState"];
        params["mentionValues"] = params["mark"];
        params["sellerFlag"] = params["flag"];
        params["ptypeType"] = params["ptypeType"];
        form.leftPanel.dataBind(params);
        form.customQueryDetail.dataBind(saveData.customQueryDetail);
        form.draftState.set_value(JSON.parse(!params["isDraft"] ? null : params["isDraft"]))
        switch (params["ptypeType"]) {
            case "0":
                form.ptypeFullnameForModify.set_value(JSON.parse(!params["skuIds"] ? null : params["skuIds"]))
                break;
            case "1":
                form.ptypeLabel.set_value(JSON.parse(params["ptypeLabelIds"]));
                break;
            case "2":
                if (params["ptypeClassIds"] && Array.isArray(params["ptypeClassIds"]) && params["ptypeClassIds"].length == 0) {
                    break;
                }
                form.ptypeClass.set_value(JSON.parse(!params["ptypeClassIds"] ? null : params["ptypeClassIds"]));
                break;
        }
        form.timeType.set_value(params["timeType"])
        this.doChangePtypeSelectType(form.ptypeType);
        this.setEditEnabled(form.buyerMessage);
        this.setEditEnabled(form.sellerMemo);
        this.setEditEnabled(form.memo);
        this.filterKeyChange(form);
        this.query(sender);
        this.customerQueryId = data.id;
        // this.onQuickChanged();
    },


    modifyQuickQuery: function (sender) {
        var form = this.get_form();
        var eventArgs = form.nowEventArgs.get_value();
        eventArgs.set_cancel(true);

        var node = eventArgs.get_node();
        var params = this.getItemNodeParams(form, node.get_data().queryParams);
        params.localRefundProcessState = params.refundState;
        params.mentionValues = params.mark;
        params.sellerFlag = params.flag;
        // params.updateMarkCondition = params.markCondition;
        params.skuIds = JSON.parse(!params.skuIds ? null : params.skuIds);
        params.ptypeLabelIds = params.ptypeLabelIds;
        params.ptypeClassIds = params.ptypeClassIds;
        params.draftState = JSON.parse(!params.isDraft ? null : params.isDraft);
        form.updateCustomerQueryPanel.dataBind(params);
        switch (params.ptypeType) {
            case "0":
                params.skuIds = JSON.parse(!params.skuIds ? null : params.skuIds);
                form.updatePtypeFullnameForModify.set_value(params.skuIds)
                break;
            case "1":
                params.ptypeLabelIds = JSON.parse(!params.ptypeLabelIds ? null : params.ptypeLabelIds);
                form.updatePtypeLabel.set_value(params.ptypeLabelIds)
                break;
            case "2":
                params.ptypeClassIds = JSON.parse(!params.ptypeClassIds || (Array.isArray(params.ptypeClassIds) && params.ptypeClassIds.length == 0) ? null : params.ptypeClassIds);
                form.updatePtypeClass.set_value(params.ptypeClassIds)
                break;
        }
        // 设置自定义查询条件名称标题
        form.updateQueryName.set_text(node.get_data().name);
        form.quickQueryId.set_value(node._value._data.id);
        this.doChangeUpdatePtypeSelectType(form.updatePtypeType);
        this.setEditEnabled(form.updateBuyerMessage);
        this.setEditEnabled(form.updateSellerMemo);
        this.setEditEnabled(form.updateMemo);
        //设置旗帜提醒
        form.updatePop.popupAt(node, true);
        form.updatePop.get_element().style.top = '58px';
        form.updatePop.get_element().style.height = ($common.getMainPanel().get_element().offsetHeight - 100) + 'px';
    },

    saveQuickQuery: function (sender) {
        var form = sender.get_form();
        var title = form.updateQueryName.get_text();
        if (!title) {
            $common.showHint("请输入自定义查询条件名称", form.updateQueryName);
            return;
        }
        var data = this._buildModifyQuickQueryParams(sender, true);
        var node = form.quickQuery.get_rootNode()._children[0]._children[form.quickQuery.get_rootNode()._children[0]._children.length - 1];
        var childs = node._children;
        var request = {};
        request.title = title;
        request.queryCode = this._finalqueryCode;
        request.queryParams = data;
        request.queryId = form.quickQueryId.get_value();
        var childNode;
        for (var i = 0; i < childs.length; i++) {
            if (request.queryId == childs[i].get_value()._data.id) {
                childNode = childs[i];
                continue;
            }
            if (childs[i].get_text() == request.title) {
                $common.showHint("自定义查询条件名称已存在", form.updateQueryName);
                return;
            }
        }
        var result = $common.ajaxSync({url: "sale/eshoporder/customerQuery/update", data: request, router: 'ngp'});
        form.updatePop.hide();
        if (result.code != 200 || result.data != "") {
            $common.alert($language.get("updateCustomerQuery", result.data));
            return;
        }
        childNode.set_text(request.title);
        var nodeQueryData = Object.clone(childNode.get_data());
        var customerData = [];
        for (var key in data) {
            customerData.push({
                "fieldText": data[key],
                "fieldValue": key
            });
        }
        nodeQueryData.name = request.title;
        nodeQueryData.queryParams = customerData;
        childNode.set_data(nodeQueryData);
        $common.showOk("自定义查询更新成功");
        var node = form.quickQuery.get_selectedNode();
        var nodeData = node._value._data;
        var params = this.getItemNodeParams(form, nodeData.queryParams);
        var dateTime = form.dateRange.get_date();
        params["beginTime"] = dateTime[0].format("yyyy-MM-dd HH:mm:ss");
        params["endTime"] = dateTime[1].format("yyyy-MM-dd HH:mm:ss");
        params["filterKeyTypeNew"] = form.filterKeyTypeNew.get_value();
        params["keyWord"] = form.keyWord.get_value();
        params["advanceTimeType"] = form.timeType.get_value();
        params["localRefundProcessState"] = params["refundState"];
        params["mentionValues"] = params["mark"];
        params["sellerFlag"] = params["flag"];
        params["ptypeType"] = params["ptypeType"];
        form.customerQueryFlexColumn.dataBind(params);
        form.draftState.set_value(JSON.parse(!params["isDraft"] ? null : params["isDraft"]))
        switch (params["ptypeType"]) {
            case "0":
                form.ptypeFullnameForModify.set_value(JSON.parse(!params["skuIds"] ? null : params["skuIds"]))
                break;
            case "1":
                form.ptypeLabel.set_value(JSON.parse(params["ptypeLabelIds"]));
                break;
            case "2":
                if (params["ptypeClassIds"] && Array.isArray(params["ptypeClassIds"]) && params["ptypeClassIds"].length == 0) {
                    break;
                }
                form.ptypeClass.set_value(JSON.parse(!params["ptypeClassIds"] ? null : params["ptypeClassIds"]));
                break;
        }
        form.timeType.set_value(params["timeType"])
        this.isFirstQueryInit = false;
        this.query(sender);
    },

    _buildModifyQuickQueryParams: function (sender, isModify) {
        var params = Object();
        var form = sender.get_form();
        if (isModify) {
            params.timeType = form.updateTimeType.get_value();
            params.otypeIds = form.updateEshop.get_value();
            params.createType = form.updatecreateType.get_value();
            params.businessTypes = form.updateBusinessType.get_value();
            params.tradeStatus = form.updateTradeStatus.get_value();
            params.refundStates = form.updateRefundState.get_value();
            params.deleteState = form.updateDeleteState.get_value();
            params.exportState = form.updateExportState.get_value();
            params.mark = form.updateMark.get_value();
            // params.markCondition = form.updateMarkCondition.get_value();
            params.flag = form.updateFlag.get_value();
            params.ptypeFullnameForModify = form.updatePtypeFullnameForModify.get_value();
            params.freightName = form.updateFreightName.get_value();
            params.freightBillNo = form.updateFreightBillNo.get_value();
            params.buyerMessage = form.updateBuyerMessage.get_value();
            params.buyerMessageMemo = form.updateBuyerMessageMemo.get_value();
            params.sellerMemo = form.updateSellerMemo.get_value();
            params.sellerMemoMessage = form.updateSellerMemoMessage.get_value();
            params.memo = form.updateMemo.get_value();
            params.memoMessage = form.updateMemoMessage.get_value();
            params.skuIds = JSON.stringify(form.updatePtypeFullnameForModify.get_value());
            params.ptypeType = form.updatePtypeType.get_value();
            params.ptypeLabelIds = form.updatePtypeLabel.get_value() ? JSON.stringify(form.updatePtypeLabel.get_value()) : null;
            params.ptypeClassIds = form.updatePtypeClass.get_value() ? JSON.stringify(form.updatePtypeClass.get_value()) : null;
            params.isDraft = JSON.stringify(form.updateDraftState.get_value());
            // params.platformQualityOrgName = form.updatePlatformQualityOrgNameQuery.get_value();
            params.gatherStatus = form.gatherStatus.get_value();
            return params;
        }
        params.timeType = form.timeType.get_value();
        params.otypeIds = form.eshop.get_value();
        params.createType = form.createType.get_value();
        params.businessTypes = form.businessType.get_value();
        params.tradeStatus = form.tradeStatus.get_value();
        params.refundStates = form.refundState.get_value();
        params.deleteState = form.deleteState.get_value();
        params.exportState = form.exportState.get_value();
        params.mark = form.mark.get_value();
        // params.markCondition = form.markCondition.get_value();
        params.flag = form.flag.get_value();
        params.ptypeFullnameForModify = form.ptypeFullnameForModify.get_value();
        params.freightName = form.freightName.get_value();
        params.freightBillNo = form.freightBillNo.get_value();
        params.buyerMessage = form.buyerMessage.get_value();
        params.buyerMessageMemo = form.buyerMessageMemo.get_value();
        params.sellerMemo = form.sellerMemo.get_value();
        params.sellerMemoMessage = form.sellerMemoMessage.get_value();
        params.memo = form.memo.get_value();
        params.memoMessage = form.memoMessage.get_value();
        params.skuIds = JSON.stringify(form.ptypeFullnameForModify.get_value());
        params.ptypeType = form.ptypeType.get_value();
        params.ptypeLabelIds = JSON.stringify(form.ptypeLabel.get_value());
        params.ptypeClassIds = JSON.stringify(form.ptypeClass.get_value());
        params.isDraft = JSON.stringify(form.draftState.get_value());
        // params.platformQualityOrgName = form.platformQualityOrgNameQuery.get_value();
        params.gatherStatus = form.gatherStatus.get_value();

        return params;
    },

    cancelUpdateQuickQuery: function (sender) {
        var form = sender.get_form();
        form.updatePop.hide();
    },

    clearArea: function (sender) {
        if (!sender.get_value()) {
            sender.set_value({areasInfo: "", areas: null});
        }
    },

    selectArea: function (sender, eventArgs) {
        var data = eventArgs.get_form().areaData;
        if (data == undefined) {
            sender.set_value({areasInfo: "", areas: null});
            return;
        }
        var areasInfo = [];
        var areas = [];
        for (var i = 0; i < data.length; i++) {
            areasInfo.push(data[i].province + data[i].city + data[i].district);
            areas.push({
                "customerReceiverProvince": data[i].province,
                "customerReceiverCity": data[i].city,
                "customerReceiverDistrict": data[i].district
            });
        }
        sender.set_value({areasInfo: areasInfo.join(","), areas: areas});
    },

    doSubmitConfigView: function (sender) {
        var popForm = new Sys.UI.Form(sender, true);
        popForm.showModal("sale/eshoporder/eshopsaleorder/AutoSubmitConfig.gspx");
    },

    removeState: function (arr) {
        var arry = [];
        for (var i = 0; i < arr.length; i++) {
            var data = arr[i];
            if (data.code == 8) {
                continue;
            }
            arry.push(data);
        }
        return arry;
    },



    query: function (sender) {
        var form = sender.get_form();
        var queryParams = this._buildQueryParams(sender);
        $common.showLoading();
        setTimeout(function () {
            if (form.c_grid_Audit) {
                var pager = form.c_grid_Audit.get_pager();
                pager.set_queryParams(queryParams);
                pager.refresh({});
                $common.hideLoading();
            }
        }, 1);
        $saleUtils.loadJdNpsAuto();
    },
    doJdJosNps: function () {
        var int = self.setInterval(function () {
            $saleUtils.loadJdNpsAuto();
        }, 1000);
    },

    _buildExportParams: function (queryParams) {
        var form = this.get_form();
        var selectItems = form.c_grid_Audit.get_selectedItems();
        var keyWord = "";
        var eshopOrderIdList = [];
        if (selectItems.length > 0) {
            for (var i = 0; i < selectItems.length; i++) {
                keyWord += selectItems[i].tradeOrderId + ",";
                eshopOrderIdList.push(selectItems[i].id);
            }
            queryParams.eshopOrderIds = eshopOrderIdList;
            queryParams.sensitiveVchcodes = eshopOrderIdList;
        }
        var filter = form.c_grid_Audit.findPager().getLastFilter();
        if (filter && filter.items.length > 0) {
            queryParams.filterParameter = {};
            for (var i = 0; i < filter.items.length; i++) {
                this.buildFilterQueryParam(queryParams, filter.items[i]);
            }
        }
    },

    openPtypeSelect: function (sender) {
        sender.set_selectorPageParams({
            selectType: "Sku",
            // queryStr: sender.get_text(),
            inputQty: false,
            showSelect: false,
            showAdd: false,
            showOnlyBaseunit: false,
            existedSku: null
        });
    },

    selectPtype: function (sender, args) {
        var form = sender.get_form();
        var ptypes = args.get_form().dataList;
        var ptypeFullNameList = [];
        var skuIds = [];
        // 套餐行需要保存套餐的ptypeId作为查询条件
        for (var i = 0; i < ptypes.length; i++) {
            ptypeFullNameList.push(ptypes[i].fullname);
            if (ptypes[i].ptypeCombo) {
                skuIds.push(ptypes[i].id);
            } else {
                skuIds.push(ptypes[i].sku.id);
            }
        }
        var selectorId = sender.get_idPart();
        if (selectorId == "ptypeFullname") {
            form.ptypeFullname.set_value({skuIds: skuIds, ptypeNames: ptypeFullNameList.join(",")});
        } else if (selectorId == "ptypeFullnameForModify") {
            form.ptypeFullnameForModify.set_value({skuIds: skuIds, ptypeNames: ptypeFullNameList.join(",")});
        } else {
            // 如果是商品联想框选中的商品，分别赋值给查询、修改的商品框
            if (form.updatePop.isShow()) {
                form.updatePtypeFullnameForModify.set_value({skuIds: skuIds, ptypeNames: ptypeFullNameList.join(",")});
            } else {
                form.ptypeFullnameForModify.set_value({skuIds: skuIds, ptypeNames: ptypeFullNameList.join(",")});
            }
        }
    },

    setEditEnabled: function (sender, args) {
        var form = sender.get_form();
        var tag = sender.get_tag();
        var control = form.getControl(tag)
        control.set_enabled((sender.get_value() === 2 || sender.get_value() === 3));
    },

    clearQuickFliterOther: function (queryParams) {
        if (!queryParams) {
            return;
        }
        queryParams.filterKeyTypeNew = null;
        queryParams.keyWord = null;
        queryParams.tradeStatus = null;
        queryParams.refundStates = null;
        queryParams.createType = null;
        queryParams.otypeIds = null;
        queryParams.customerAreas = null;
        queryParams.intSellerFlag = null;
        queryParams.businessTypes = null;
        queryParams.mentionDropItems = null;
        queryParams.marks = null;
    },

    doExport: function (sender) {
        var form = sender.get_form();
        var grid = form.c_grid_Audit;
        var rowCount = form.c_grid_Audit.get_pager().get_itemCount();
        if (rowCount <= 0) {
            $common.alert("没有数据可以导出，请更换查询条件再试");
            return;
        }
        var queryParams = this._buildQueryParams(form);
        this._buildExportParams(queryParams);
        var exportConfirmForm = new Sys.UI.Form(sender, true);
        var header = this.getExportHeader(sender);
        header.needImage = true;
        // 获取当前排序条件，放入header中
        header.sortFields = grid.getSortedOrders();
        var sessionKey = this.getPageName(form) + "_needSms";
        var needSms = sessionStorage.getItem(sessionKey);
        if (needSms == null) {
            needSms = true;
        } else {
            needSms = JSON.parse(needSms);
        }
        var myDate = new Date();
        var year = myDate.getFullYear() - 1;
        var startTimes = year + '-01-01 00:00:00';
        var endTimes = this.dateFormat("yyyy-MM-dd hh:mm:ss", myDate);
        var exportRequest = {
            reportName: "sale-export|eshopsale-order",
            source: this.getPageName(form),
            params: JSON.stringify(queryParams),
            header: JSON.stringify(header),
            startTime: queryParams.beginTime,
            endTime: queryParams.endTime,
            //短信验证
            needSms: needSms,
            //导出数据单选组
            exportData: true,
            //内容涵盖单选组
            contentContain: true,
            //套餐导出单选组
            comboExport: true,
            //内容涵盖单选组中是否需要【导出商品信息】按钮
            contentContainOnlyDetails: false
        };
        exportConfirmForm.showModal("/exportcenter/ui/ExportConfirm.gspx?title=" + form.get_caption(), exportRequest);

        $notify.bind('CallbackBeforeTaskCreate', function (param) {
            console.log('focus param', param);
            var params = JSON.parse(param.params);
            if (params.contentContain == 2) {
                param.reportName = 'sale-export|eshoporder-ptype';
            }
        });
    },

    doExportList: function (sender, args) {
        var exportListForm = new Sys.UI.Form(sender, true);
        exportListForm.showModal("/exportcenter/ui/ExportLogList.gspx?source=" + this.getPageName(sender.get_form()));
    },
    getExportHeader: function (sender) {
        var form = sender.get_form();
        var grid = form.c_grid_Audit;
        var detailGrid = form.details;
        var dataObj = {};
        dataObj.reportName = form.get_caption();
        dataObj.masterFields = [];
        Sys.UI._Exporter.initMasterFields(form, grid, dataObj.masterFields); // 最后一个参数是告诉平台，只构造reportTab这个控件集合内的子控件；不遍历PopupBlock下面其他控件，避免同名DataField的污染
        if (grid) {
            dataObj.detailFields = [];
            Sys.UI._Exporter.initDetailFields(grid, dataObj.detailFields);
            for (var i = 0; i < dataObj.detailFields.length; i++) {
                dataObj.detailFields[i].detailColumn = false; // 增加主表或明细标识
            }
        }
        if (detailGrid) {
            var detailFields = [];
            Sys.UI._Exporter.initDetailFields(detailGrid, detailFields);
            for (var i = 0; i < detailFields.length; i++) {
                // 第一个是行号
                if (i == 0) continue;
                detailFields[i].detailColumn = true; // 增加主表或明细标识
                dataObj.detailFields.push(detailFields[i]);
            }
        }
        for (var i = 0; i < dataObj.masterFields.length; i++) {
            if (dataObj.masterFields[i].name == "旗帜") {
                var flags = form.flag.get_value();
                var flagText = [];
                for (var j = 0; j < flags.length; j++) {
                    if (flags[j] == 0) {
                        flagText.push("空");
                    }
                    if (flags[j] == 1) {
                        flagText.push("红旗");
                    }
                    if (flags[j] == 2) {
                        flagText.push("黄旗");
                    }
                    if (flags[j] == 3) {
                        flagText.push("绿旗");
                    }
                    if (flags[j] == 4) {
                        flagText.push("蓝旗");
                    }
                    if (flags[j] == 5) {
                        flagText.push("紫旗");
                    }
                    if (flags[j] == 6) {
                        flagText.push("灰旗");
                    }
                    if (flags[j] == 7) {
                        flagText.push("橙旗");
                    }
                    if (flags[j] == 8) {
                        flagText.push("青旗");
                    }
                }
                dataObj.masterFields[i].value = flagText.toString();
                continue;
            }

            if (dataObj.masterFields[i].dataField == "keyWord") {
                dataObj.masterFields[i].name = form.filterKeyTypeNew.get_text();
                continue;
            }

            if (dataObj.masterFields[i].dataField == "otypeId") {
                dataObj.masterFields[i].value = form.eshop.get_text();
                continue;
            }

            if (dataObj.masterFields[i].dataField == "createType") {
                dataObj.masterFields[i].value = form.createType.get_text();
                continue;
            }

            if (dataObj.masterFields[i].dataField == "businessType") {
                dataObj.masterFields[i].value = form.businessType.get_text();
                continue;
            }

            if (dataObj.masterFields[i].dataField == "tradeStatus") {
                dataObj.masterFields[i].value = form.tradeStatus.get_text();
                continue;
            }

            if (dataObj.masterFields[i].dataField == "localRefundProcessState") {
                dataObj.masterFields[i].value = form.refundState.get_text();
                continue;
            }

            if (dataObj.masterFields[i].dataField == "mentionValues") {
                dataObj.masterFields[i].value = form.mark.get_text();
                continue;
            }

        }

        if (dataObj.detailFields.length > 0) {
            for (var i = 0; i < dataObj.detailFields.length; i++) {
                if (dataObj.detailFields[i].dataField == 'sellerFlag') {
                    var items = {
                        "0": "",
                        "1": "红旗",
                        "2": "黄旗",
                        "3": "绿旗",
                        "4": "蓝旗",
                        "5": "紫旗",
                        "6": "灰旗",
                        "7": "橙旗",
                        "8": "青旗",
                        "9": "玫红旗",
                        "10": "秋香绿旗",
                        "11": "桃红旗"
                    };
                    dataObj.detailFields[i].itemData = items;
                    dataObj.detailFields[i].columnType = 'DropDown';
                }
            }
            var selfPickUpFullInfoField = {
                caption: "自提点",
                columnWidth: 110,
                dataField: "extend.selfPickUpInfo.fullBuyerInfo",
                detailColumn: false,
                name: "自提点"
            }
            var realBuyerFullInfoNameField = {
                caption: "买家信息",
                columnWidth: 217,
                dataField: "extend.realBuyer.fullBuyerInfo",
                detailColumn: false,
                name: "买家信息"
            }

            dataObj.detailFields.push(realBuyerFullInfoNameField);
            dataObj.detailFields.push(selfPickUpFullInfoField);
        }
        return dataObj;
    },

    getPageName: function (form) {
        return "jarvis." + form.get_caption();
    },

    doGetFieldText: function (sender, eventArgs) {
        var form = sender.get_form();
        if (!form.c_grid_Audit.get_selectedRowData())
            return "";
        var _rowIndex = eventArgs._rowIndex;
        var column = eventArgs.get_column();
        if (!column) {
            return "";
        }
        if (column.get_dataField() === "sellerFlag" || column.get_dataField() === "orderTags") {
            this._copyText = "";
            return "";
        }
        this._copyText = eventArgs.get_column().getCellText(_rowIndex);
    },

    doCopy: function () {
        $common.copyText(this._copyText.toString());
    },

    doBatchCopyTradeOrderId: function () {
        var form = this.get_form();
        var grid = form.c_grid_Audit;
        var rowDatas = grid.get_selectedItems();
        if (!rowDatas || rowDatas.length == 0) {
            $common.showError("没有勾选订单");
            return;
        }
        var tradeOrderIds = "";
        for (var i = 0; i < rowDatas.length; i++) {
            var row = rowDatas[i];
            if (row.tradeOrderId && row.tradeOrderId != "") {
                tradeOrderIds = tradeOrderIds + row.tradeOrderId + ",";
            }
        }
        if (tradeOrderIds.substring(tradeOrderIds.length - 1, tradeOrderIds.length - 0) == ",") {
            tradeOrderIds = tradeOrderIds.substring(0, tradeOrderIds.length - 1);
        }
        if (tradeOrderIds == "") {
            $common.showError("没有可供复制的订单编号");
            return;
        }
        $common.copyText(tradeOrderIds);
    },

    doBatchCopyDeliverBillNo: function () {
        var form = this.get_form();
        var grid = form.c_grid_Audit;
        var rowDatas = grid.get_selectedItems();
        if (!rowDatas || rowDatas.length == 0) {
            $common.showError("没有勾选订单");
            return;
        }
        var deliverBillNos = "";
        for (var i = 0; i < rowDatas.length; i++) {
            var row = rowDatas[i];
            if (row.localFreightBillNo && row.localFreightBillNo != "") {
                deliverBillNos = deliverBillNos + row.localFreightBillNo + ",";
            }
        }
        if (deliverBillNos.substring(deliverBillNos.length - 1, deliverBillNos.length - 0) == ",") {
            deliverBillNos = deliverBillNos.substring(0, deliverBillNos.length - 1);
        }
        if (deliverBillNos == "") {
            $common.showError("没有可供复制的物流单号");
            return;
        }
        $common.copyText(deliverBillNos);
    },

    _refreshEditBtn: function (form, rowdata) {
        form.btnEdit.set_visible(false);
        // form.updateEdit.set_enabled(false);
        form.updateEditAllData.set_visible(false);
        form.btnViewEshopOrder.set_visible(false);
        var updatePower = $saleUtils.getPower("eshoporder.eshopsaleorder.updateOrder");
        if (rowdata.createType === 1 && updatePower) {
            // form.updateEdit.set_enabled(true);
        }
        var updateAllDataPower = $saleUtils.getPower("eshoporder.eshopsaleorder.updateOrderAllData");
        if (rowdata.createType === 1 && updateAllDataPower) {
            form.updateEditAllData.set_visible(true);
        }

        form.deleteOrder.set_visible(false);
        form.deleteRecovery.set_visible(false);
        var deleteOrRecoveryOrderPower = $saleUtils.getPower("eshoporder.eshopsaleorder.deleteOrRecoveryOrder");
        if (deleteOrRecoveryOrderPower) {
            if (!rowdata.deleted) {
                form.deleteOrder.set_visible(true);
                form.deleteRecovery.set_visible(false);
            }
            if (rowdata.deleted) {
                form.deleteOrder.set_visible(false);
                form.deleteRecovery.set_visible(true);
            }
        }
        var showBindBtype = $saleUtils.getPower("eshoporder.eshopsaleorder.platformBtypeMapBind");
        form.btnBindBtype.set_visible(showBindBtype);
        var request = {};
        request.otypeId = rowdata.otypeId;
        request.shopType = rowdata.shopType;
        var viewPower = $saleUtils.getPower("eshoporder.eshopsaleorder.viewOnlineOrder");
        this.get_service().post("sale/eshoporder/order/supportViewOpenOrder", request, function (res) {
            if (rowdata.createType === 1 && res.data && viewPower) {
                form.btnViewEshopOrder.set_visible(true);
            }
        });
        if ($saleUtils.getPower("eshoporder.eshopEditOrderPage.view") && rowdata.createType === 0) {
            form.btnEdit.set_visible(true);
        }
        if (rowdata.createType === 1) {
            form.updateEditAllData.set_visible(true);
        }
    },

    doDeleteOrders: function (sender) {
        var form = sender.get_form();
        var selectedItems = form.c_grid_Audit.get_selectedItems();
        if (selectedItems.length == 0) {
            var data = form.c_grid_Audit.get_selectedRowData();
            if (!data) {
                Sys.UI.MessageBox.alert("请先选择订单");
                return;
            }
            selectedItems.push(data);
        }
        var _this = this;
        Sys.UI.MessageBox.confirm("是否删除勾选的订单", function (result) {
            if (!result) {
                return;
            }
            var ids = [];
            var parameter = {};
            for (var i = 0; i < selectedItems.length; i++) {
                var item = selectedItems[i];
                if (item.processState > 0 || item.deleted == 1) {
                    continue;
                }
                ids.push(item.eshopOrderId);
            }
            if (ids && ids.length === 0) {
                Sys.UI.MessageBox.alert("没有需要删除的订单,过滤已提交/删除的订单");
                return;
            }
            parameter.ids = ids;
            _this.get_service().post("/sale/eshoporder/order/deleteOrders", parameter, function (result) {
                if (result.data) {
                    Sys.UI.MessageBox.alert("删除成功");
                    _this.partialRefresh(sender);
                    return;
                }
                Sys.UI.MessageBox.alert("删除失败:" + result.message + "");
                _this.partialRefresh(sender);
            })
        })
    },

    doModifySellerMessage: function (sender) {
        this.doModifyMessage(sender, "sale/eshoporder/eshopsaleorder/BatchUpdateSellerMessage.gspx")
    },

    doModifyRemark: function (sender) {
        this.doModifyMessage(sender, "sale/eshoporder/eshopsaleorder/BatchUpdateRemark.gspx");
    },

    doCloseOrder: function (sender) {
        var form = sender.get_form();
        var selectedItems = form.c_grid_Audit.get_selectedItems();
        if (selectedItems.length == 0) {
            var data = form.c_grid_Audit.get_selectedRowData();
            if (!data) {
                $common.alert("请选择需要关闭的订单!");
                return;
            }
            selectedItems.push(data);
        }
        var shopType = selectedItems[0].shopType;
        var otypeId = selectedItems[0].otypeId;
        var tradeList = [];
        var needConfirm = false;
        for (var i = 0; i < selectedItems.length; i++) {
            var item = selectedItems[i];
            if (item.createType != 1 || item.localTradeState != 1) {
                needConfirm = true;
                continue;
            }
            if (i != 0 && item.shopType != shopType) {
                $common.alert("只支持同一类型的网店订单执行此操作！");
                return;
            }
            tradeList.push(item);
        }
        if (tradeList.length == 0) {
            $common.alert("没有可以关闭交易的订单!\r\n注:只有线上订单且线上交易状态为【未付款】才能关闭！");
            return;
        }
        if (needConfirm) {
            var that = this;
            $common.confirm("线上交易状态不为【未付款】的订单将会被过滤，是否继续？", function (r) {
                if (r) {
                    that._filterCloseReason(sender, shopType, tradeList, otypeId);
                }
            });
        } else {
            this._filterCloseReason(sender, shopType, tradeList, otypeId);
        }
    },

    _filterCloseReason: function (sender, shopType, tradeList, otypeId) {
        var checkUrl = "/sale/eshoporder/enum/checkSupport";
        var checkParameter = {
            key: "closeOrder",
            shopType: shopType
        };
        var that = this;
        this.get_service().post(checkUrl, checkParameter, function (response) {
            if (response.code != 200) {
                $common.alert(response.message);
            } else if (!response.data) {
                $common.alert("该平台不支持关闭交易接口！");
            } else {
                that._doCloseOrder(sender, tradeList, otypeId);
            }
        });
    },

    _doCloseOrder: function (sender, tradeList, otypeId) {
        var reasonForm = new Sys.UI.Form(this);
        var that = this;
        reasonForm.add_ok(function (p) {
            var reason = p.drReason.get_text();
            that._doCloseOrderFunc(tradeList, otypeId, reason, sender, that);
        }, this);
        reasonForm.set_params({tradeList: tradeList, otypeId: otypeId});
        reasonForm.showModal("/sale/eshoporder/eshopsaleorder/CloseOrderReason.gspx");
    },

    _doCloseOrderFunc: function (tradeList, otypeId, reason, sender, that) {
        var url = "/sale/eshoporder/order/closeOrder";
        var parameter = {
            orderList: tradeList,
            closeReason: reason,
            otypeId: otypeId
        };
        $ms.get_service().post(url, parameter, function (response) {
            if (response.code != 200) {
                $common.alert(response.message);
            } else {
                var data = response.data;
                if (!data.success) {
                    $common.alertError("关闭订单失败：" + data.message);
                } else {
                    $common.alert("关闭成功", function () {
                        that.partialRefresh(sender);
                    });
                }
            }
        });
    },

    doModifyMessage: function (sender, url) {
        var form = sender.get_form();
        var selectedItems = form.c_grid_Audit.get_selectedItems();
        if (selectedItems.length == 0) {
            var data = form.c_grid_Audit.get_selectedRowData();
            if (!data) {
                Sys.UI.MessageBox.alert("请先选择订单");
                return;
            }
            selectedItems.push(data);
        }
        var updateParams = [];
        for (var i = 0; i < selectedItems.length; i++) {
            var item = selectedItems[i];
            if (item.tradefrom == "天猫经销") {
                continue;
            }
            var itemOrder = {};
            itemOrder.tradeOrderId = item.tradeOrderId;
            itemOrder.eshopId = item.otypeId;
            itemOrder.eshopOrderId = item.id;
            itemOrder.sellerMemo = item.sellerMemo;
            itemOrder.remark = item.remark;
            itemOrder.flagid = item.sellerFlag;
            itemOrder.online = item.createType === 1;
            itemOrder.tradeeshoptype = item.tradefromtype;
            itemOrder.shopType = item.shopType;
            updateParams.push(itemOrder)
        }
        var pageParams = this._buildPagepamas(updateParams);
        var popForm = new Sys.UI.Form(form);
        var _this = this;
        popForm.set_params(pageParams);
        popForm.add_ok(function () {
            _this.partialRefresh(sender);
        });
        popForm.showModal(url);
    },

    clearQuickQuery: function (sender) {
        if (sender._selectedTab._activeControl == "customerQueryPanel") {
            var form = sender.get_form();
            // this.initCommonValue(form);
            //form.filterKeyType.set_value(0);
            //form.quickQuery.set_selectedNode(null);
        }
    },


    initCommonValue: function (form) {
        form.timeType.set_value(0)
        form.filterKeyTypeNew.set_value(0);
        form.keyWord.set_value("");
        form.tradeStatus.set_value(null);
        form.refundState.set_value(null);
        form.areas.set_text("");
        form.areas.set_value(null);
        form.eshop.set_value(null);
        form.mark.set_value(null);
        form.flag.set_value(null);
        form.businessType.set_value(null);
        form.createType.set_value(null);
        form.deleteState.set_value(null);
        form.exportState.set_value(null);
        // form.markCondition.set_value(0);
        form.ptypeFullnameForModify.set_value(null);
        form.freightName.set_value("");
        form.freightBillNo.set_value("");
        form.buyerMessage.set_value(0);
        form.buyerMessageMemo.set_value("");
        form.buyerMessageMemo.set_enabled(false);
        form.sellerMemo.set_value(0);
        form.sellerMemoMessage.set_value("");
        form.sellerMemoMessage.set_enabled(false);
        form.memo.set_value(0);
        form.memoMessage.set_value("");
        form.memoMessage.set_enabled(false);
        form.ptypeType.set_value(0);
        form.ptypeFullnameForModify.set_value(null);
        form.ptypeLabel.set_value(null);
        form.ptypeClass.set_value(null);
        form.draftState.set_value(null);
        // form.platformQualityOrgNameQuery.set_value(null);
        this.doChangePtypeSelectType(form.ptypeType);
        this.filterKeyChange(form);
    },

    doEdit: function (sender) {
        var form = sender.get_form();
        var rowData = form.c_grid_Audit.get_selectedRowData();
        if (!rowData || !rowData.id) {
            return false;
        }
        if (rowData.deleted) {
            Sys.UI.MessageBox.alert("该订单已被删除，不能编辑!");
            return false;
        }
        if (!rowData.mappingState) {
            Sys.UI.MessageBox.alert("该订单未完成商品对应，不能编辑!");
            return false;
        }
        if (rowData.businessType != 'SaleNormal' && rowData.businessType != 'SaleProxy') {
            Sys.UI.MessageBox.alert("只能编辑普通销售或代销业务的订单!");
            return false;
        }
        if (rowData.createType == 0 ) {
            this._doOpenEdit(sender, rowData);
        }
    },

    _doOpenEdit: function (sender, rowData) {
        var pageFrom = "sale/eshoporder/eshopsaleorder/EShopSaleOrderList.gspx";
        var params = {
            modeStr: "MODIFY",
            id: rowData.id,
            pageFrom: pageFrom,
            businessType: rowData.businessType
        };
        var url = "jxc/recordsheet/stock/SupplyMarketingBillNew.gspx?vchtype=OriginalSaleOrder&isEditBill=true&vchcode=" + params.id + "&a=" + Math.random(1000);
        $common.showPage(sender, url, params);
    },

    doCreateBill: function (sender) {
        var param = {};
        param.modeStr = "NEW";
        param.pageFrom = "sale/eshoporder/eshopsaleorder/EShopSaleOrderList.gspx";
        var url = "/jxc/recordsheet/stock/SupplyMarketingBillNew.gspx?vchtype=OriginalSaleOrder" + "&a=" + Math.random(1000);
        $common.showPage(sender, url, param);

    },

    _checkUpdateFilter: function (selectedItems) {
        var filterFlag = false;
        for (var i = 0; i < selectedItems.length; i++) {
            var item = selectedItems[i];
            if (item.createtype) {
                filterFlag = true;
                break;
            }
        }
        return filterFlag;
    },

    _getUpdateOrdersMsg: function (item) {
        var msg = {};
        msg.tradeOrderId = item.tradeOrderId;
        if (item.createType !== 1) {
            msg.msg = "线下订单不支持更新";
            return msg;
        }
        return msg;
    },

    updateOrders: function (sender) {
        var updateParams = new Array();
        var form = sender.get_form();
        var selectedItems = form.c_grid_Audit.get_selectedItems();
        if (selectedItems.length == 0) {
            var data = form.c_grid_Audit.get_selectedRowData();
            if (!data) {
                Sys.UI.MessageBox.alert("请先选择订单");
                return;
            }
            selectedItems.push(data);
        }
        var param = sender.get_param();
        var _this = this;
        var msg = new Array();
        for (var i = 0; i < selectedItems.length; i++) {
            var item = selectedItems[i];
            var tipMsg = _this._getUpdateOrdersMsg(item);
            if (tipMsg && tipMsg.msg) {
                msg.push(tipMsg);
                continue;
            }
            var itemorder = {};
            itemorder.tradeOrderId = item.tradeOrderId;
            itemorder.eshopId = item.otypeId;
            itemorder.eshopOrderId = item.eshopOrderId;
            itemorder.tradeForm = item.tradeForm;
            itemorder.platformStoreId = item.platformStoreId;
            itemorder.downloadType = param;
            updateParams.push(itemorder);
        }

        if (msg.length != 0) {
            var cancontinue = (updateParams.length == 0) ? false : true;

            var errorForm = new Sys.UI.Form(sender);
            // errorForm.add_loaded(function () {
            //     errorForm.grid.dataBind(msg);
            //     /*              if (updateParams.length == 0) {
            //                       errorForm.canadd.set_visible(false);
            //                   }*/
            // });
            errorForm.add_ok(function (arg) {
                if (arg.ok) {
                    _this.doUpdateBill(form, sender, updateParams);
                }
            });
            var pageParam = {
                errorData: msg
            };
            errorForm.showModal("sale/eshoporder/eshopsaleorder/AuditFilterInfo.gspx?cancontinue=" + cancontinue, pageParam);
        } else {
            _this.doUpdateBill(form, sender, updateParams);
        }
    },


    _buildPagepamas: function (updateParams) {
        var dic = [];
        var eshopCount = 0;
        for (var i = 0; i < updateParams.length; i++) {
            var item = updateParams[i];
            if (dic[item.eshopId]) {
                continue;
            }
            dic[item.eshopId] = this._getValues(item.eshopId, updateParams);
            eshopCount += 1;
        }
        var pagepamas = {};
        pagepamas.eshopCount = eshopCount;
        pagepamas.orderCount = updateParams.length;
        pagepamas.datasource = updateParams;
        return pagepamas;
    },

    doUpdateBill: function (form, sender, updateParams) {
        var pagepamas = this._buildPagepamas(updateParams);
        if (pagepamas.eshopCount == 0) {
            $common.alert("请选择平台下载的订单进行更新操作");
            return;
        }
        pagepamas.processType = "updateOrder";
        var popForm = new Sys.UI.Form(sender);
        var _this = this;
        popForm.add_closed(function () {
            _this.partialRefresh(sender);
        });
        popForm.set_params(pagepamas);
        popForm.showMDI("sale/eshoporder/eshopsaleorder/ProcessForm.gspx");
    },

    _getValues: function (eshopid, updateParams) {
        var values = [];
        for (var i = 0; i < updateParams.length; i++) {
            var data = updateParams[i];
            if (data.eshopid == eshopid) {
                values.push(data.tradeOrderId);
            }
        }
        return values;
    },

    doUpdateOneBill: function (sender) {
        var form = sender.get_form();
        setTimeout(function () {
            form.popCopy.set_visible(false);
        }, 0);
        this.updateOrders(sender);
    },

    deleteOrder: function () {
        this.doDeleteOrRecoveryOrder(this, "/sale/eshoporder/order/deleteOrder",null,null,"网店->平台原始订单池->删除订单")
    },

    deleteRecovery: function () {
        this.doDeleteOrRecoveryOrder(this, "/sale/eshoporder/order/deleteRecovery",null,null,"网店->平台原始订单池->删除恢复")
    },

    deleteOrderBatch: function () {
        this.doDeleteOrRecoveryOrder(this, "/sale/eshoporder/order/deleteOrder", "确定批量删除订单？", 0,"网店->平台原始订单池->删除订单")
    },

    deleteRecoveryBatch: function () {
        this.doDeleteOrRecoveryOrder(this, "/sale/eshoporder/order/deleteRecovery", "确定批量撤销删除订单？", 1,"网店->平台原始订单池->删除恢复")
    },


    doDeleteOrRecoveryOrder: function (sender, url, msg, mode,key) {
        var startObj = null;
        if (window.$startTiming) {
            startObj = $startTiming(key);
        }
        var form = sender.get_form();
        setTimeout(function () {
            form.popCopy.set_visible(false);
        }, 0);
        var params = {};
        var eshopOrderIds = [];
        var form = sender.get_form();
        var selectedItems = form.c_grid_Audit.get_selectedItems();
        if (selectedItems.length == 0) {
            var data = form.c_grid_Audit.get_selectedRowData();
            if (!data) {
                Sys.UI.MessageBox.alert("请先选择订单");
                return;
            }
            selectedItems.push(data);
        }

        var _this = this;

        function extracted() {
            var errorMsg = [];
            var eshopOrderIds = [];
            var checkDeleteOrder = $common.ajaxSync({
                url: "sale/eshoporder/order/checkDeleteOrder",
                data: selectedItems,
                type: 'post',
                router: 'ngp'
            });
            if(checkDeleteOrder && checkDeleteOrder.data){
                errorMsg = checkDeleteOrder.data.tipMsgs;
                eshopOrderIds = checkDeleteOrder.data.eshopOrderIds;
            }else{
                for (var i = 0; i < selectedItems.length; i++) {
                    var item = selectedItems[i];
                    eshopOrderIds.push(item.id);
                }
            }
            params.eshopOrderIds = eshopOrderIds;
            if (errorMsg.length != 0) {
                var errorForm = new Sys.UI.Form(sender);
                errorForm.add_ok(function (arg) {
                    if (arg.ok) {
                        $common.showLoading();
                        _this.get_service().post(url, params, function (res) {
                            if (res.code == "200") {
                                _this.partialRefresh(sender);
                                $common.showOk("操作成功");
                                return;
                            }
                            Sys.UI.MessageBox.alert("操作失败:" + res.message);
                        });
                        $common.hideLoading();
                    }
                });
                errorForm.add_closed(function (arg) {
                    _this.partialRefresh(sender);
                    return;
                });
                var pageParam = {
                    errorData: errorMsg
                };
                errorForm.showModal("sale/eshoporder/eshopsaleorder/AuditFilterInfo.gspx?cancontinue=" + true, pageParam);
                return;
            }
            $common.showLoading();
            _this.get_service().post(url, params, function (res) {
                if (res.code == "200") {
                    _this.partialRefresh(sender);
                    $common.showOk("操作成功");
                    return;
                }
                Sys.UI.MessageBox.alert("操作失败:" + res.message);
            });
            $common.hideLoading();
            if (startObj != null) {
                startObj.endTiming();// 触发上报
            }
        }

        if (msg) {
            Sys.UI.MessageBox.confirm(msg, function (result) {
                if (!result) {
                    return;
                }
                extracted.call(_this);
            });
        } else {
            extracted.call(_this);
        }
    },

    _doUpdetaOrder: function (updateParams, sender) {
        var _this = this;
        $common.showLoading();
        this.get_service().post("/sale/eshoporder/order/updateOrders", updateParams, function (res) {
            var data = res.data;
            if (data) {
                _this.partialRefresh(sender);
                $common.showOk("更新成功");
                return;
            }
            Sys.UI.MessageBox.alert("更新失败:" + res.message);
        });
        $common.hideLoading();
        /* var res = $common.ajaxSync({url: "", data: updateParams, router: 'ngp'}); */
    },

    doCopyAdd: function (sender) {
        var form = sender.get_form();
        var rowData = form.c_grid_Audit.get_selectedRowData();
        if (!rowData.eshopOrderId) {
            return false;
        }
        if (rowData.mappingState === 0) {
            return false;
        }
        var params = {modeStr: "COPYADD", eshopOrderId: rowData.eshopOrderId};
        $common.showPage(sender, "sale/eshoporder/eshopsaleorder/EditForm.gspx", params);
    },


    onPop: function () {
        var form = this.get_form();
        var rowdata = form.c_grid_Audit.get_selectedRowData();
        form.popCopy.set_visible(true);
        if (!rowdata) {
            form.popCopy.set_visible(false);
            return;
        }
        this._copyItemEnable(this);
        this._refreshEditBtn(form, rowdata);
    },

    _copyItemEnable: function (sender) {
        var form = sender.get_form();
        var grid = form.c_grid_Audit;
        var selectItem = grid.get_selectedRowData();
        form.popCopyItem.set_enabled(false);
        // form.popBatchCopyTradeOrderId.set_enabled(false);
        // form.popBatchCopyDeliverBillNo.set_enabled(false);
        if (!selectItem) {
            return;
        }
        var power = $saleUtils.getPower("eshoporder.eshopsaleorder.copy");
        if (power) {
            form.popCopyItem.set_enabled(true);
            // form.popBatchCopyTradeOrderId.set_enabled(true);
            // form.popBatchCopyDeliverBillNo.set_enabled(true);
        }
        var ignoreColumns = this._getIgnoreColumns();
        var activeColumn = grid.get_activeColumn();
        if (!activeColumn) return;
        if (power && power.value && this._copyText.toString() !== "" && ignoreColumns.indexOf(activeColumn.get_dataField()) == -1) {
            form.popCopyItem.set_enabled(true);
        }
    },

    _getIgnoreColumns: function () {
        return ["selected", "orderTags", "sellerFlag", "invoiceInfo.invoiceRequired"];
    },



    partialRefresh: function (sender) {
        var form = sender.get_form();
        var grid = form.c_grid_Audit;
        var list = this._getPartialRefreshRequest(grid);
        var _this = this;
        //fix bug #104993
        grid.get_pager().partialRefresh(list, function (_res) {
            $craba.asyncFun(_this, function () {
                if (grid.get_isDisposed()) return;
                _this.loadDetails(sender);
            });
        });
    },

    buildSortsQueryParam: function (queryParam, filed) {
        if (!filed) return;
        if (filed.dataField == "orderTags") {//标记提醒
            queryParam.filterParameter.mentionValue = filed.value;
        }
    },

    bindGridData: function (path, params, binData, failback) {
        var startObj = null;
        if (window.$startTiming) {
            startObj = $startTiming('平台订单->平台原始订单池->查询');
        }
        var filter = !params.queryParams.gridFilter ? [] : params.queryParams.gridFilter;
        params.queryParams.filterParameter = Object();
        params.queryParams.filterParameter.defaultSort = (!params.sorts || params.sorts.length == 0) ? true : false;
        if (filter && filter.length > 0) {
            for (var i = 0; i < filter.length; i++) {
                this.buildFilterQueryParam(params.queryParams, filter[i]);
            }
        }

        if (path === "GetPartialPagerData") {
            //同步刷新数据时，遇到数据量比较大的情况会卡死，改为异步查询
            $common.showLoading();
            this.get_service().post("/sale/eshoporder/order/partialRefresh", params.queryParams, function (res) {
                if (res.code != 200 || !res.data) {
                    //return;
                } else {
                    binData({
                        itemList: res.data.list
                    });
                }
                $common.hideLoading();
            });
            return;
        }
        if (path === "GetRealPagerSize") {
            var response = $common.ajaxSync({
                url: "sale/eshoporder/order/queryListOrdersCount",
                data: params,
                router: 'ngp'
            });
            binData(response.data);
            return;
        }
        if (path === "GetPagerData") {
            var queryResponse = $common.ajaxSync({
                url: "sale/eshoporder/order/queryListOrders",
                data: params,
                waiting: "排序中,请稍后...",
                router: 'ngp'
            });
            if (queryResponse.code != 200 && queryResponse.message) {
                Sys.UI.MessageBox.alert(queryResponse.message);
                binData({itemList: [], itemCount: 0});
                return;
            }
            if (queryResponse.code != 200 || !queryResponse.data) {
                binData({itemList: [], itemCount: 0});
                return;
            }
            // var idsResponse = $common.ajaxSync({
            //     url: "sale/eshoporder/order/queryListOrderIds",
            //     data: params,
            //     router: 'ngp'
            // });
            // if (idsResponse.code == 200) {
            //     this.set_context("eshopOrderIds", idsResponse.data);
            // }
            var _this = this;
            if (this._getCalculateExceptionStatusConfig()) {
                if (window.$SaleEShopSaleOrderListNewExceptionStatusTimer) {
                    var param = {
                        sourceType: "ESHOP_ORDER",
                        usePhase: "ORDER_QUERY"
                    }
                    window.$SaleEShopSaleOrderListNewExceptionStatusTimer.refreshOnce(_this.refreshExceptionStatusEventName, param);
                }
            }
            binData({
                itemList: queryResponse.data.list,
                itemCount: queryResponse.data.total
            });
            if (startObj != null) {
                startObj.endTiming();// 触发上报
            }
        }
        this.isFirstQueryInit = false;
    },

    _getPartialRefreshRequest: function (grid) {
        var selectedItems = grid.get_selectedItems();
        if (selectedItems.length === 0) {
            var data = grid.get_selectedRowData();
            if (!data) {
                return;
            }
            selectedItems.push(data);
        }
        var list = [];
        for (var i = 0; i < selectedItems.length; i++) {
            var item = selectedItems[i];
            var obj = {};
            obj.id = item.id;
            obj.selected = item.selected;
            list.push(obj);
        }
        return list;
    },

    //同步做数据刷新
    /*    _doAjaxSyncPartialRefresh:function(sender) {
            var form = sender.get_form();
            var grid = form.c_grid_Audit;
            var queryParams = new Object();
            var list = this._getPartialRefreshRequest(grid);
            queryParams.vchcode = list;
            var res = $common.ajaxSync({
                url: "sale/eshoporder/order/partialRefresh",
                data: queryParams,
                router: 'ngp'
            });
            if (res.code != 200 || !res.data) {
                return;
            }
            // grid.dataBind(res.data.list);
            grid.get_pager().partialRefresh(res.data.list)
        },*/

    _partialRefresh: function (sender, callback, callbackParams) {
        var form = sender.get_form();
        var grid = form.c_grid_Audit;
        var list = this._getPartialRefreshRequest(grid);
        var _this = this;
        //局部刷新，每次刷新一行都会调用回调函数
        //调用过多导致页面卡死
        //不刷新会导致页面数据不对，对应之后，读取的还是未对应的状态
        //防抖函数，50ms之内不会触发
        grid.get_pager().partialRefresh(list, function (_res, rowIndex) {
            $craba.asyncFun(_this, function () {
                if (grid.get_isDisposed()) return;
                //业务执行
                callbackParams.selectedItems = _this.getSelectItems(sender);
                callback(callbackParams.sender, callbackParams.selectedItems, callbackParams.service, callbackParams._this);
            });
        });
    },

    buildFilterQueryParam: function (queryParam, filed) {
        if (!filed) return;
        if (filed.dataField == "orderTags") {//标记提醒
            queryParam.filterParameter.mentionValue = filed.value;
        } else if (filed.dataField == "localRefundState") {
            queryParam.filterParameter.localRefundState = filed.value;
        } else if (filed.dataField == "sellerFlag") {//旗帜
            queryParam.filterParameter.intSellerFlag = filed.value;
        } else if (filed.dataField === "tradeOrderId") {
            queryParam.filterParameter.tradeOrderId = filed.value === "%" ? "[%]" : filed.value;
        } else if (filed.dataField == "otypeName") {
            queryParam.filterParameter.otypeName = filed.value === "%" ? "[%]" : filed.value;
        } else if (filed.dataField == "otypeId") {
            queryParam.filterParameter.otypeId = filed.value;
        } else if (filed.dataField == "ocategory") {
            queryParam.filterParameter.ocategory = filed.value;
        } else if (filed.dataField == "orderSourceType") {
            queryParam.filterParameter.orderSourceType = filed.value;
        } else if (filed.dataField == "businessType") {
            queryParam.filterParameter.businessType = filed.value;
        } else if (filed.dataField == "reSendState") {
            queryParam.filterParameter.reSendState = filed.value;
        } else if (filed.dataField == "deliverType") {
            queryParam.filterParameter.deliverType = filed.value;
        } else if (filed.dataField == "createType") {
            queryParam.filterParameter.createType = filed.value;
        } else if (filed.dataField == "createTime") {
            queryParam.filterParameter.createBeginTime = filed.value1;
            queryParam.filterParameter.createEndTime = filed.value2;
        } else if (filed.dataField == "tradeCreateTime") {
            queryParam.filterParameter.tradeCreateBeginTime = filed.value1;
            queryParam.filterParameter.tradeCreateEndTime = filed.value2;
        } else if (filed.dataField == "tradePayTime") {
            queryParam.filterParameter.tradePayBeginTime = filed.value1;
            queryParam.filterParameter.tradePayEndTime = filed.value2;
        } else if (filed.dataField == "tradeFinishTime") {
            queryParam.filterParameter.tradeFinishBeginTime = filed.value1;
            queryParam.filterParameter.tradeFinishEndTime = filed.value2;
        } else if (filed.dataField == "extend.collectTime") {
            queryParam.filterParameter.collectBeginTime = filed.value1;
            queryParam.filterParameter.collectEndTime = filed.value2;
        } else if (filed.dataField == "extend.collectCustomer") {
            queryParam.filterParameter.collectCustomer = filed.value === "%" ? "[%]" : filed.value;
        } else if (filed.dataField == "salesman") {
            queryParam.filterParameter.salesman = filed.value === "%" ? "[%]" : filed.value;
        } else if (filed.dataField == "extend.installationServiceProvider") {
            //安装服务商
            queryParam.filterParameter.installationServiceProvider = filed.value === "%" ? "[%]" : filed.value;
        } else if (filed.dataField == "extend.paymentMode") {
            queryParam.filterParameter.paymentMode = filed.value;
        } else if (filed.dataField == "buyerMessage") {
            queryParam.filterParameter.buyerMessage = filed.value === "%" ? "[%]" : filed.value;
        } else if (filed.dataField == "sellerMemo") {
            queryParam.filterParameter.sellerMemo = filed.value === "%" ? "[%]" : filed.value;
        } else if (filed.dataField == "btypeName") {
            queryParam.filterParameter.btypeName = filed.value === "%" ? "[%]" : filed.value;
        } else if (filed.dataField == "ktypeName") {
            queryParam.filterParameter.ktypeName = filed.value === "%" ? "[%]" : filed.value;
        } else if (filed.dataField == "etypeName") {
            queryParam.filterParameter.etypeName = filed.value === "%" ? "[%]" : filed.value;
        } else if (filed.dataField == "localFreightName") {
            queryParam.filterParameter.localFreightName = filed.value === "%" ? "[%]" : filed.value;
        } else if (filed.dataField == "localFreightBillNo") {
            queryParam.filterParameter.localFreightBillNo = filed.value === "%" ? "[%]" : filed.value;
        } else if (filed.dataField == "customerExpectedFreightName") {
            queryParam.filterParameter.customerExpectedFreightName = filed.value === "%" ? "[%]" : filed.value;
        } else if (filed.dataField == "remark") {
            queryParam.filterParameter.remark = filed.value === "%" ? "[%]" : filed.value;
        } else if (filed.dataField == "platformStockId") {
            queryParam.filterParameter.platformStockId = filed.value === "%" ? "[%]" : filed.value;
        } else if (filed.dataField == "platformStockName") {
            queryParam.filterParameter.platformStockName = filed.value === "%" ? "[%]" : filed.value;
        } else if (filed.dataField == "processState") {
            queryParam.filterParameter.processState = filed.value;
        } else if (filed.dataField == "customTradeStatus") {
            queryParam.filterParameter.customTradeStatus = filed.value;
        } else if (filed.dataField == "exportState") {
            queryParam.filterParameter.exportState = filed.value;
        } else if (filed.dataField == "orderPreferentialAllotTotal") {
            queryParam.filterParameter.sellerPreferentialMinTotal = filed.value1;
            queryParam.filterParameter.sellerPreferentialMaxTotal = filed.value2;
        } else if (filed.dataField == "timing.promisedCollectTime") {
            queryParam.filterParameter.promisedCollectBeginTime = filed.value1;
            queryParam.filterParameter.promisedCollectEndTime = filed.value2;
        } else if (filed.dataField == "timing.promisedSignTime") {
            queryParam.filterParameter.promisedSignBeginTime = filed.value1;
            queryParam.filterParameter.promisedSignEndTime = filed.value2;
        } else if (filed.dataField == "timing.signTime") {
            queryParam.filterParameter.signBeginTime = filed.value1;
            queryParam.filterParameter.signEndTime = filed.value2;
        } else if (filed.dataField == "timing.planSignTime") {
            queryParam.filterParameter.planSignTimeBeginTime = filed.value1;
            queryParam.filterParameter.planSignTimeEndTime = filed.value2;
        } else if (filed.dataField == "timing.promisedSignStartTime") {
            queryParam.filterParameter.promisedSignStartTimeBeginTime = filed.value1;
            queryParam.filterParameter.promisedSignStartTimeEndTime = filed.value2;
        } else if (filed.dataField == "platformOrderPreferentialTotal") {
            queryParam.filterParameter.platformPreferentialMinTotal = filed.value1;
            queryParam.filterParameter.platformPreferentialMaxTotal = filed.value2;
        } else if (filed.dataField == "extend.anchorOrderPreferentialTotal") {
            queryParam.filterParameter.anchorOrderPreferentialTotalMinTotal = filed.value1;
            queryParam.filterParameter.anchorOrderPreferentialTotalMaxTotal = filed.value2;
        } else if (filed.dataField == "extend.platformOrderSubsidyTotal") {
            queryParam.filterParameter.platformOrderSubsidyTotalMinTotal = filed.value1;
            queryParam.filterParameter.platformOrderSubsidyTotalMaxTotal = filed.value2;
        } else if (filed.dataField == "orderBuyerFreightFee") {
            queryParam.filterParameter.buyerFreightMinFee = filed.value1;
            queryParam.filterParameter.buyerFreightMaxFee = filed.value2;
        } else if (filed.dataField == "ptypeServiceFee") {
            queryParam.filterParameter.serviceMinFee = filed.value1;
            queryParam.filterParameter.serviceMaxFee = filed.value2;
        } else if (filed.dataField == "disedTaxedTotal") {
            queryParam.filterParameter.disedTaxedMinTotal = filed.value1;
            queryParam.filterParameter.disedTaxedMaxTotal = filed.value2;
        } else if (filed.dataField == "buyerTaxTotal") {
            queryParam.filterParameter.taxMinTotal = filed.value1;
            queryParam.filterParameter.taxMinTotal = filed.value2;
        } else if (filed.dataField == "timing.planSendTime") {
            queryParam.filterParameter.planSendBeginTime = filed.value1;
            queryParam.filterParameter.planSendEndTime = filed.value2;
        } else if (filed.dataField == "timing.sendTime") {
            queryParam.filterParameter.sendBeginTime = filed.value1;
            queryParam.filterParameter.sendEndTime = filed.value2;
        } else if (filed.dataField == "invoiceInfo.invoiceRequired") {
            queryParam.filterParameter.invoiceRequired = filed.value;
        } else if (filed.dataField == "invoiceInfo.invoiceState") {
            //开票状态
            queryParam.filterParameter.invoiceState = filed.value;
        } else if (filed.dataField == "eshopBuyer.customerShopAccount") {
            queryParam.filterParameter.customerShopAccount = filed.value === "%" ? "[%]" : filed.value;
        } else if (filed.dataField == "eshopBuyer.fullBuyerInfo") {
            queryParam.filterParameter.fullBuyerInfo = filed.value === "%" ? "[%]" : filed.value;
        } else if (filed.dataField == "selfDeliveryMode") {
            //发货方式
            queryParam.filterParameter.selfDeliveryMode = filed.value;
        } else if (filed.dataField == "payTimeType") {
            queryParam.filterParameter.payTimeType = filed.value;
        } else if (filed.dataField == "buyerTradeTotal") {
            queryParam.filterParameter.buyerTradeMinTotal = filed.value1;
            queryParam.filterParameter.buyerTradeMaxTotal = filed.value2;
        } else if (filed.dataField == "buyerPaidTotal") {
            queryParam.filterParameter.buyerPaidMinTotal = filed.value1;
            queryParam.filterParameter.buyerPaidMaxTotal = filed.value2;
        } else if (filed.dataField == "buyerUnpaidTotal") {
            queryParam.filterParameter.buyerUnpaidMinTotal = filed.value1;
            queryParam.filterParameter.buyerUnpaidMaxTotal = filed.value2;
        } else if (filed.dataField == "extend.advanceTotal") {
            queryParam.filterParameter.advanceTotalMinTotal = filed.value1;
            queryParam.filterParameter.advanceTotalMaxTotal = filed.value2;
        } else if (filed.dataField == "distributionDisedTaxedTotal") {
            queryParam.filterParameter.distributionDisedTaxedMinTotal = filed.value1;
            queryParam.filterParameter.distributionDisedTaxedMaxTotal = filed.value2;
        } else if (filed.dataField == "extend.groupHeaderName") {
            queryParam.filterParameter.groupHeaderName = filed.value === "%" ? "[%]" : filed.value;
        } else if (filed.dataField == "extend.flowChannel") {
            queryParam.filterParameter.flowChannel = filed.value === "%" ? "[%]" : filed.value;
        } else if (filed.dataField == "localTradeState") {
            queryParam.filterParameter.localTradeState = filed.value;
        } else if (filed.dataField == "platformParentOrderId") {
            queryParam.filterParameter.platformParentOrderId = filed.value;
        } else if (filed.dataField == "extend.confirmStatus") {
            queryParam.filterParameter.confirmStatus = filed.value;
        } else if (filed.dataField == "extend.logisticsStatus") {
            queryParam.filterParameter.logisticsStatus = filed.value;
        } else if (filed.dataField == "extend.platformQcResult") {
            queryParam.filterParameter.platformQcResult = filed.value;
        } else if (filed.dataField == "extend.platformIdentifyResult") {
            queryParam.filterParameter.platformIdentifyResult = filed.value;
        } else if (filed.dataField == "distribution.distributionBuyerTradeId") {
            queryParam.filterParameter.distributionBuyerTradeId = filed.value;
        } else if (filed.dataField == "extend.sellerFlagMemo") {
            queryParam.filterParameter.sellerFlagMemo = filed.value;
        } else if (filed.dataField == "extend.gatherStatus") {
            queryParam.filterParameter.gatherStatus = filed.value;
        } else if (filed.dataField == "extend.groupTitle") {
            queryParam.filterParameter.groupTitle = filed.value;
        } else if (filed.dataField == "extend.isDraft") {
            queryParam.filterParameter.isDraft = filed.value;
        } else if (filed.dataField == "creatorName") {
            queryParam.filterParameter.creatorName = filed.value;
        } else if (filed.dataField == "extend.nationalSubsidyTotal") {
            queryParam.filterParameter.nationalSubsidyMinTotal = filed.value1;
            queryParam.filterParameter.nationalSubsidyMaxTotal = filed.value2;
        }
    },

    _buildQueryParams: function (sender) {
        var requestParams = Object();
        var form = this.get_form();
        if (!form) {
            return requestParams;
        }
        requestParams.timeType = form.timeType.get_value();
        var dateTime = form.dateRange.get_date();
        var beginTime = dateTime[0];
        var endTime = dateTime[1];
        $common.checkTips(!beginTime || beginTime == '', "请选择开始时间", form.dateRange);
        $common.checkTips(!endTime || beginTime == '', "请选择结束时间", form.dateRange);
        requestParams.beginTime = this.dateFormat("yyyy-MM-dd hh:mm:ss", beginTime);
        requestParams.endTime = this.dateFormat("yyyy-MM-dd hh:mm:ss", endTime);
        $common.checkTips(requestParams.beginTime > requestParams.endTime, "开始时间大于结束时间", form.dateRange);
        requestParams.filterKeyTypeNew = form.filterKeyTypeNew.get_value();
        requestParams.keyWord = form.keyWord.get_value();
        requestParams.tradeStatus = form.tradeStatus.get_value();
        requestParams.refundStates = form.refundState.get_value();
        requestParams.createType = form.createType.get_value();
        requestParams.otypeIds = form.eshop.get_value();
        requestParams.customerAreas = form.areas.get_value() == null ? [] : form.areas.get_value().areas;
        requestParams.intSellerFlags = form.flag.get_value();
        requestParams.businessTypes = form.businessType.get_value();
        requestParams.mentionDropItems = form.mark.get_selectedItems();
        requestParams.marks = form.mark.get_value();
        requestParams.deleteState = form.deleteState.get_value();
        requestParams.exportState = form.exportState.get_value();
        requestParams.skuIds = form.ptypeFullnameForModify.get_value() ? form.ptypeFullnameForModify.get_value().skuIds : [];
        requestParams.freightName = form.freightName.get_value();
        requestParams.freightBillNo = form.freightBillNo.get_value();
        requestParams.buyerMessage = form.buyerMessageMemo.get_value();
        requestParams.sellerMemo = form.sellerMemoMessage.get_value();
        requestParams.memo = form.memoMessage.get_value();
        requestParams.buyerMessageCondition = form.buyerMessage.get_value();
        requestParams.sellerMemoCondition = form.sellerMemo.get_value();
        requestParams.memoCondition = form.memo.get_value();
        requestParams.ptypeType = form.ptypeType.get_value();
        requestParams.ptypeLabelIds = form.ptypeLabel.get_value() ? form.ptypeLabel.get_value() : [];
        requestParams.ptypeClassIds = form.ptypeClass.get_value() ? form.ptypeClass.get_value().ptypeClassIds : [];
        requestParams.isDraft = form.draftState.get_value();
        // requestParams.platformQualityOrgName = form.platformQualityOrgNameQuery.get_value();
        requestParams.contanisJdQuery = form.contanisJdQuery.get_visible() && form.contanisJdQuery.get_value();
        requestParams.gatherStatus = form.gatherStatus.get_value();

        var saveData = form.leftPanel.saveData();
        if(saveData.all_quickQueryDetails.length != 0){
            var index = form.all_quickQueryDetails.get_selectedRowIndex();
            switch (index){
                case 0:
                    requestParams.quickFilterType = 19;
                    break;
                case 1:
                    requestParams.quickFilterType = 5;
                    break;
                case 2:
                    requestParams.quickFilterType = 20;
                    break;
                default:
                    requestParams.quickFilterType = 0;
                    break;
            }
        }else{
            requestParams.quickFilterType = 0;
        }
        return requestParams;
    },

    doShowEShopOrder: function (sender) {
        var form = sender.get_form();
        var rowData = form.c_grid_Audit.get_selectedRowData();
        if (!rowData) {
            return;
        }
        var request = {};
        request.tradeOrderId = rowData.tradeOrderId;
        request.otypeId = rowData.otypeId;
        this.get_service().post("sale/eshoporder/order/openOnlineOrderUrl", request, function (res) {
            if (!res.data) {
                $common.alert("平台不支持查看网店订单");
                return;
            }
            $common.openwin(res.data);
        });
        /* $common.showLoading();
         var res = $common.ajaxSync({url: "", data: request, router: 'ngp'});
         $common.hideLoading();*/
    },
    doGridCellBeginEdit: function (sender, eventArgs) {
        var form = sender.get_form();
        var grid = form.c_grid_Audit;
        var selectedRowData = grid.get_selectedRowData();
        var relationPower = $saleUtils.getPower("eshoporder.eshopsaleorder.relation");
        if (!selectedRowData) {
            eventArgs.set_cancel(true);
            return;
        }
        if (eventArgs.get_column().get_dataField() === 'sellerFlag') {
            eventArgs.set_cancel(true);
        }
        if (eventArgs.get_column().get_dataField() == "ptypeCode") {
            var rowIndex = eventArgs.get_rowIndex();
            var data = sender.findRowData(rowIndex);
            if (data && (data.mappingState || data.comboRow)) {
                eventArgs.set_cancel(true);
            }
            if (!relationPower) {
                eventArgs.set_cancel(true);
            }
        }
    },

    openUrl: function (sender) {
        var form = sender.get_form();
        var rowMainData = form.c_grid_Audit.get_selectedRowData();
        if (!rowMainData) {
            return;
        }
        if (rowMainData.createType !== 1) {
            return;
        }
        var rowData = form.details.get_selectedRowData();
        if (!rowData) {
            return;
        }
        var shoptype = rowMainData.shopType;
        var response = $common.ajaxSync({
            url: "sale/eshoporder/eshoplist/hasSupportOpenPtypeUrl/" + shoptype,
            data: null,
            type: 'get',
            router: 'ngp'
        });
        if (!response.data) {
            return;
        }
        var request = {};
        request.platformSkuInfo = rowData.platformPtypeId;
        request.platformSkuId = rowData.platformSkuId;
        request.eshopId = rowMainData.otypeId;
        this.get_service().post("sale/eshoporder/order/openUrl", request, function (res) {
            if (res.code != "200" && res.message) {
                $common.alert(res.message);
                return;
            }
            if (!res.data) {
                $common.alert("该平台暂不支持查看");
                return;
            }
            $common.openwin(res.data);
        });
        /* $common.showLoading();
         var res = $common.ajaxSync({url: "", data: request, router: 'ngp'});
         $common.hideLoading();
 */
    },

    filterKeyChange: function (sender) {
        var that = this;
        var form = sender.get_form();
        var text = form.filterKeyTypeNew.get_text();
        if ("网店商品名称/属性/商家编码(单个查询)" == text) {
            form.keyWord.set_nullDisplayText("支持复制" + text);
        } else if ("订单号" == text) {
            form.keyWord.set_nullDisplayText("支持复制多个订单编号，分隔符使用逗号','，空格或回车")
        } else {
            form.keyWord.set_nullDisplayText("支持复制多个" + text + ",分隔符使用逗号','")
        }
        var eshopList = form.eshop.get_value();
        var eshopSource = form.eshop.get_dataSource();
        if (("收货人手机号" == text || "买家账号" == text || "收货人姓名" == text) &&
            (eshopList.length == 0 && that.hasJDAuthShop == true)) {
            form.contanisJdQueryBlock.set_visible(true);
            form.contanisJdQuery.set_value(!localStorage.getItem("contanisJdQuery") ? true : localStorage.getItem("contanisJdQuery") == "true");
        } else {
            form.contanisJdQueryBlock.set_visible(false);
        }
    },

    dateFormat: function (fmt, date) {
        var o = {
            "M+": date.getMonth() + 1, // 月份
            "d+": date.getDate(), // 日
            "h+": date.getHours(), // 小时
            "m+": date.getMinutes(), // 分
            "s+": date.getSeconds(), // 秒
            "q+": Math.floor((date.getMonth() + 3) / 3), // 季度
            "S": date.getMilliseconds() // 毫秒
        };
        if (/(y+)/.test(fmt))
            fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (var k in o)
            if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return fmt;
    }
    ,
    doDownload: function (sender) {
        $common.showPage(sender, "sale/eshoporder/eshopsaleorder/Download.gspx");
    },

    doOrderModify: function (sender) {
        var data = this.get_form().c_grid_Audit.get_selectedRowData();
        if (!data || !data.eshopOrderId) {
            return;
        }
        if (data.processState !== "0") {
            return;
        }
        if (data.createType !== "0") {
            return;
        }
        var params = {modeStr: "MODIFY", eshopOrderId: data.eshopOrderId};
        $common.showPage(sender, "sale/eshoporder/eshopsaleorder/EditForm.gspx", params);
    },


    doAuditCreate: function (sender, ids, callback, callbackParams) {
        var queryParams = {};
        queryParams.eshopOrderIds = ids;
        queryParams.showQuery = false;
        queryParams.submit = true;
        var _this = this;
        var modifyFlagForm = new Sys.UI.Form(sender);
        modifyFlagForm.showMDI("sale/eshoporder/eshopsaleorder/ManualRelation.gspx", queryParams);
        modifyFlagForm.add_ok(function (pop) {
            if (pop.close()) {
                if (callback) {
                    if (parseInt(pop.isMatchCount) > 0) {
                        Sys.UI.MessageBox.alert("存在订单的明细未做对应，本次勾选订单都不进行提交");
                        return;
                    }
                    _this._partialRefresh(sender, callback, callbackParams);
                    //_this.partialRefresh(sender);
                    //_this._doAjaxSyncPartialRefresh(sender)
                    //callbackParams.selectedItems = _this.getSelectItems(sender);
                    //callback(callbackParams.sender, callbackParams.selectedItems, callbackParams.service, callbackParams._this);
                }
            }
        })
        modifyFlagForm.add_closed(function (pop) {
            if (pop.close()) {
                if (callback) {
                    if (parseInt(pop.isMatchCount) > 0) {
                        Sys.UI.MessageBox.alert("存在订单的明细未做对应，本次勾选订单都不进行提交");
                        return;
                    }
                    _this._partialRefresh(sender, callback, callbackParams);
                }
            }
        })
    }
    ,

    getSelectItems: function (sender) {
        var form = sender.get_form();
        var selectedItems = form.c_grid_Audit.get_selectedItems();
        if (selectedItems == null || selectedItems.length == 0) {
            var data = form.c_grid_Audit.get_selectedRowData();
            if (!data) {
                Sys.UI.MessageBox.alert("请先选择订单");
                return;
            }
            selectedItems.push(data);
        }
        return selectedItems;
    },


    _afterRelation: function (callback, callbackParams) {

    }
    ,

    doAuditTrade: function (sender, selectedItems, service, _this) {
        // if (selectedItems.length > 200){
        //     $common.alert("手工提交最多支持一批200张订单！");
        //     return;
        // }
        var form = _this.get_form();
        var msg = new Array();
        var parameter = {};
        parameter.eshopOrderIdList = [];
        var dateTime = form.dateRange.get_date();
        parameter.beginTime = dateTime[0].format("yyyy-MM-dd HH:mm:ss");
        parameter.endTime = dateTime[1].format("yyyy-MM-dd HH:mm:ss");
        var timeIndex = form.timeType.get_value();
        switch (timeIndex) {
            case 0:
                parameter.timeType = 'TRADE_CREATE_TIME';
                break;
            case 1:
                parameter.timeType = 'PAY_TIME';
                break;
            case 2:
                parameter.timeType = 'CREATE_TIME';
                break;
            default :
                break;
        }
        for (var i = 0; i < selectedItems.length; i++) {
            var data = selectedItems[i];
            var tipMsg = _this._getMsg(data, parameter.eshopOrderIdList);
            if (tipMsg && tipMsg.msg) {
                msg.push(tipMsg);
            } else {
                parameter.eshopOrderIdList.push(data.id);
            }
        }
        if (msg.length != 0) {
            var cancontinue = (parameter.eshopOrderIdList.length == 0) ? false : true;
            var errorForm = new Sys.UI.Form(sender);
            // errorForm.add_loaded(function () {
            //     errorForm.grid.dataBind(msg);
            //     /*                if (parameter.vchcodeList.length == 0) {
            //                         errorForm.canadd.set_visible(false);
            //                     }*/
            // });
            errorForm.add_ok(function (arg) {
                if (arg.ok) {
                    _this.doAudit(sender, parameter);
                }
            });
            errorForm.add_closed(function (arg) {
                _this.partialRefresh(sender);
                return;
            });
            var pageParam = {
                errorData: msg
            };
            errorForm.showModal("sale/eshoporder/eshopsaleorder/AuditFilterInfo.gspx?cancontinue=" + cancontinue, pageParam);
            return;
        }
        _this.doAudit(sender, parameter);
    },


    onBodyFlagChanged: function (sender) {
        var form = sender.get_form();
        var grid = form.c_grid_Audit;
        var data = grid.get_selectedRowData();
        if (!data.eshopOrderId) return;
        var request = {};
        var updatePower = $saleUtils.getPower("eshoporder.eshopsaleorder.updateBuyerMessage");
        if (!updatePower) {
            $ms.alert("没有修改旗帜权限,请先添加此权限");
            return;
        }
        request.eshopOrderId = data.eshopOrderId;
        request.intSellerFlag = sender.get_value();
        this.get_service().post("sale/eshoporder/order/updateOrderFlag", request, function (res) {

        });
    },

    checkIsBicOrder: function (selectedItems) {
        for (var j = 0; j < selectedItems.length; j++) {
            var marks = selectedItems[j].orderMarks;
            if (marks && marks.length > 0) {
                for (var i = 0; i < marks.length; i++) {
                    if (marks[i].markCode == 90520001) {
                        if (marks[i].bigData) {

                            var bigdata = JSON.parse(marks[i].bigData.replace("'", ""));
                            if (bigdata.orderCode) {
                                return false;
                            } else {
                                return true
                            }
                        } else {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    },

    checkOrderMark: function (order, markCode) {
        if (!order || !markCode || markCode == 0) {
            return false;
        }
        var marks = order.orderMarks;
        if (marks && marks.length > 0) {
            for (var i = 0; i < marks.length; i++) {
                if (marks[i].markCode == markCode) {
                    return true;
                }
            }
        }
        return false;
    },

    doSubmit: function (sender) {
        //this.partialRefresh(sender);
        var selectedItems = this.getSelectItems(sender);
        if (!selectedItems) return;
        var service = this.get_service();
        var _this = this;
        _this.submitWay(selectedItems, sender, service);
    },

    //提了一个提交的公共方法
    submitWay: function (selectedItems, sender, service) {
        var _this = this;
        var noMappingCanSubmit = $ms.ngpConfig.Sys.sysGlobalNoMappingSubmit;
        var unRelationOrders = new Array();
        for (var i = 0; i < selectedItems.length; i++) {
            var data = selectedItems[i];
            if (data.mappingState == 0 && data.orderDeliverRequired == 1) {
                unRelationOrders.push(data.id);
            }
        }
        if (unRelationOrders.length > 0 && !noMappingCanSubmit) {
            var callbackParam = new Object();
            callbackParam.selectedItems = selectedItems;
            callbackParam.sender = sender;
            callbackParam.service = service;
            callbackParam._this = _this;
            $common.confirm("选中的订单发现有网店商品未对应,是否手动对应?", function (r) {
                if (r) {
                    _this.doAuditCreate(sender, unRelationOrders, _this.doAuditTrade, callbackParam);
                } else {
                    _this.doAuditTrade(sender, selectedItems, service, _this);
                }
            });
        } else {
            this.doAuditTrade(sender, selectedItems, service, _this);
        }
    },

    doAudit: function (sender, parameter) {
        var frm = new Sys.UI.Form(sender);
        var _this = this;
        var form = sender.get_form();
        parameter.processType = "submit";
        frm.set_params(parameter);
        frm.showModal("sale/eshoporder/eshopsaleorder/ProcessForm.gspx");
        frm.add_closed(function (pop) {
            //form.quickQuery.set_selectedNode(0);
            //_this.query(sender);
            _this.partialRefresh(sender);
        })
    },

    doRelation: function (sender) {
        var form = sender.get_form();
        var mainGrid = form.c_grid_Audit;
        var items = mainGrid.get_selectedItems();
        var params = {};
        var eshopOrderIds = [];
        if (!items || items.length == 0) {
            params.showQuery = true;
        } else {
            for (var i = 0; i < items.length; i++) {
                if (!items[i].mappingState && !items[i].deleted && items[i].orderDeliverRequired) {
                    eshopOrderIds.push(items[i].id);
                }
            }
            if (!eshopOrderIds || eshopOrderIds.length == 0) {
                $common.showTips("没有需要对应的网店商品！");
                return;
            }
            params.showQuery = false;
        }
        params.eshopOrderIds = eshopOrderIds;
        var _this = this;
        var frm = new Sys.UI.Form(sender);
        if ($ms.ngpConfig.Sys.oldVersion && $ms.ngpConfig.Sys.oldVersion == 1){
            frm.showMDI("sale/eshoporder/eshopsaleorder/ManualRelation.gspx", params);
        }else {
            frm.showMDI("sale/eshoporder/eshopsaleorder/ManualRelation.gspx", params);
        }
        frm.add_ok(function (pop) {
            _this.query(sender);
        })
        frm.add_closed(function (pop) {
            _this.query(sender);
        })
    }
    ,

    _getMsg: function (item, result) {
        var msg = {};
        msg.tradeOrderId = item.tradeOrderId;

        if (item.mappingState == 0 && !$ms.ngpConfig.Sys.sysGlobalNoMappingSubmit) {
            msg.msg = "网店商品未对应的订单不允许提交！";
            return msg;
        }
        if (item.localTradeState == 5) {
            msg.msg = "交易关闭的订单不允许提交";
            return msg;
        }
        if (item.orderSaleType == 6) {
            msg.msg = "京东送礼物的母单不支持提交发货，请处理对应的收礼物订单的发货";
            return msg;
        }
        if (item.orderDeliverRequired == 0) {
            msg.msg = "不发货不记账的订单不允许提交";
            return msg;
        }
        if (item.businessType != "SaleNormal" &&
            item.businessType != "SaleDistribution" &&
            item.businessType != "SaleProxy" &&
            item.businessType != "Leakage" &&
            item.businessType != "Breakage") {
            msg.msg = "非普通销售，分销业务，代销业务，漏发补发，破损补发的网店订单不允许提交";
            return msg;
        }
        if (item.deleted == 1) {
            msg.msg = "已删除的订单不允许提交";
            return msg;
        }
        if (item.processState == 1) {
            msg.msg = "已提交的订单不允许再次提交";
            return msg;
        }
        // if (item.processState == 2 && item.deleted == 0) {
        //     msg.msg = "过滤已提交预售";
        //     return msg;
        // }
        if (item.localTradeState != 3 && item.localTradeState != 4 && item.localRefundState == 4) {
            msg.msg = "未发货状态下退款成功的订单不允许提交";
            return msg;
        }
        if (!item.ktypeName || !item.ktypeId || item.ktypeId == 0) {
            msg.msg = "无仓库的订单不允许提交";
            return msg;
        }
        if (item.orderSaleType == 3) {
            var checkCycleOrderSubmit = $common.ajaxSync({
                url: "sale/eshoporder/order/checkCycleOrderSubmit",
                type: 'get',
                router: 'ngp'
            });
            if (!checkCycleOrderSubmit || !checkCycleOrderSubmit.data){
                msg.msg = "您好，判断到您的订单属于周期购业务，该业务目前处于共建状态，如果需要继续处理该业务请联系客服辅助您使用";
                return msg;
            }
        }
        if (item.localTradeState == 0) {
            msg.msg = "线上交易状态未知的订单";
            result.push(item.id);
            return msg;
        }
        if (item.extend.holdTime && new Date(item.extend.holdTime).getTime() > new Date().getTime()) {
            msg.msg = "平台HOLD的订单，预计自动提交时间" + new Date(item.extend.holdTime).format("yyyy-MM-dd HH:mm:ss");
            result.push(item.id);
            return msg;
        }
        if (item.localTradeState == 1) {
            msg.msg = "未付款的订单";
            result.push(item.id);
            return msg;
        }
        if (item.localTradeState == 4) {
            msg.msg = "交易成功的订单";
            result.push(item.id);
            return msg;
        }
        if (item.localTradeState == 6) {
            msg.msg = "部分发货的订单";
            result.push(item.id);
            return msg;
        }
        if (item.localTradeState == 3) {
            msg.msg = "网店已发货的订单";
            result.push(item.id);
            return msg;
        }
        if (item.deliverProcessType == 0) {
            msg.msg = "不走发货流程的订单，线上发货后会自动提交，请确认是否提交？";
            result.push(item.id);
            return msg;
        }
        return msg;
    },

    doOrderLogFilter: function (sender, args) {
        var form = this.get_form();
        var logGrid = form.c_gridLogs;
        var mainGrid = form.c_grid_Audit;
        var bill = mainGrid.get_selectedRowData();
        if (!bill) {
            return;
        }
        var filter = args.get_filter();
        var request = {};
        request.queryParams = {};
        if (filter) {
            var items = filter.items;
            for (var i = 0; i < items.length; i++) {
                var param = items[i].dataField;
                if (param == "opreateTime") {
                    var times = items[i].value.split("至");
                    request.queryParams.opreateStartTime = times[0];
                    request.queryParams.opreateEndTime = times[1];
                    continue;
                }
                if (param == "comment") {
                    request.queryParams.comment = items[i].value;
                    continue;
                }
                if (param == "processState") {
                    request.queryParams.processState = items[i].value;
                    continue;
                }
                if (param == "etypeName") {
                    request.queryParams.etypeName = items[i].value;
                    continue;
                }
            }
        }
        request.pageIndex = 0;
        request.pageSize = 500;
        request.queryParams.vchcode = bill.id;
        request.queryParams.eshopId = bill.otypeId;
        this.get_service().post("sale/eshoporder/order/queryLogs", request, function (logs) {
            logGrid.refresh(logs.data.list);
        })
    },

    loadDetails: function (sender) {
        var form = sender.get_form();
        var logGrid = form.c_gridLogs;
        logGrid.clearFilter(true);
        var _this = this;
        clearTimeout(_this._maingridSelectedAction);
        _this._maingridSelectedAction = setTimeout(function () {
            if (form.get_controls() === undefined) {
                return;
            }
            var mainGrid = form.c_grid_Audit;
            var detailGrid = form.details;
            var billGrid = form.c_grid_bills;
            var deliverGrid = form.c_gridDelivers;
            var logGrid = form.c_gridLogs;
            var freightGrid = form.c_grid_freight;
            var qualityGrid = form.c_grid_quality;
            var cyclePurchaseGird = form.cyclePurchaseGird;
            detailGrid.dataBind(null);
            deliverGrid.dataBind(null);
            logGrid.dataBind(null);
            freightGrid.dataBind(null);
            cyclePurchaseGird.dataBind(null);
            detailGrid.refresh();
            detailGrid.set_readOnly(false);
            var bill = mainGrid.get_selectedRowData();
            form.ReceiverInfoFlowPanel.dataBind(null);
            form.selfPickUpFlowPanel.dataBind(null);
            form.realBuyerFlowPanel.dataBind(null);
            form.invoiceInfoFlowPanel.dataBind(null);
            form.deliveryInfoFlowPanel.dataBind(null);
            form.qualityFlowPanel.dataBind(null);
            form.deliveryInfoFlowPanel.set_visible(false);
            form.qualityFlowPanel.set_visible(false);
            form.cyclePurchaseFlowPanel.set_visible(false);
            form.realBuyerB.set_visible(false);
            form.selfPickUpB.set_visible(false);
            form.realBuyerL.set_visible(false);
            form.selfPickUpL.set_visible(false);
            if (bill) {
                if (bill.selfDeliveryMode == 2) {
                    form.deliveryInfoFlowPanel.set_visible(true);
                }
                if (_this.checkOrderMark(bill, 90520001)) {
                    form.qualityFlowPanel.set_visible(true);
                }
                if (_this.checkOrderMark(bill, 90870002)) {
                    form.realBuyerB.set_visible(true);
                    form.realBuyerL.set_visible(true);
                }
                if (_this.checkOrderMark(bill, 90870002) || bill.selfDeliveryMode==3) {
                    form.selfPickUpB.set_visible(true);
                    form.selfPickUpL.set_visible(true);
                }
                if (bill.orderSaleType == 3) {
                    form.cyclePurchaseFlowPanel.set_visible(true);
                }
                form.deliveryInfoFlowPanel.dataBind(bill.extend);
                form.ReceiverInfoFlowPanel.dataBind(bill.eshopBuyer);
                form.selfPickUpFlowPanel.dataBind(bill.extend.selfPickUpInfo);
                form.qualityFlowPanel.dataBind(bill.extend);
                form.qualityAddress.set_value(bill.eshopBuyer.customerReceiverFullAddress);
                form.realBuyerFlowPanel.dataBind(bill.extend.realBuyer);
                var hideBuyer = Object.clone(bill.eshopBuyer);
                var hideInvoiceInfo = Object.clone(bill.invoiceInfo);
                hideBuyer.invoiceBankAccount = bill.invoiceInfo.invoiceBankAccount;
                form.hideBuyerInfo.set_value(hideBuyer);
                form.hideInvoiceInfo.set_value(hideInvoiceInfo);
                form.notRequired.set_visible(false);
                form.showInvoice.set_visible(false);
                form.showInvoiceInfo.set_visible(false);
                form.resetInvoiceInfo.set_visible(false);
                _this._refreshInvoiceInfo(form, bill);
            }
            if (bill == null)
                detailGrid.set_readOnly(true);
            else {
                var requestParams = Object();
                requestParams.eshopOrderId = bill.id;
                requestParams.tradeOrderId = bill.tradeOrderId;
                requestParams.otypeIds = [];
                requestParams.otypeIds.push(bill.otypeId);
                _this.get_service().post("sale/eshoporder/order/queryOrderDetails", requestParams, function (details) {
                    var detailList = _this._doBuildOrderDetailPic(details.data);
                    detailGrid.dataBind(detailList);
                    _this.initProductOpenUrlHint(bill, detailGrid);
                });
                _this.get_service().post("sale/eshoporder/order/queryOrderFreights", requestParams, function (freights) {
                    freightGrid.dataBind(freights.data);
                });
                var deliverParams = Object();
                deliverParams.orderId = bill.id;
                deliverParams.tradeOrderId = bill.tradeOrderId;
                deliverParams.otypeId = bill.otypeId;
                deliverParams.otypeIds = [bill.otypeId];
                _this.get_service().post("sale/eshoporder/order/getSaleOrderBillByOrderId", deliverParams, function (details) {
                    deliverGrid.dataBind(details.data);
                });
                var request = {};
                request.queryParams = {};
                request.pageIndex = 0;
                request.pageSize = 500;
                request.queryParams.vchcode = requestParams.eshopOrderId;
                _this.get_service().post("sale/eshoporder/order/queryLogs", request, function (logs) {
                    if (!logs.data || !logs.data.list) return;
                    logGrid.dataBind(logs.data.list);
                })
                _this.get_service().post("sale/eshoporder/order/getCycleOrder", deliverParams, function (result) {
                    cyclePurchaseGird.dataBind(result.data);
                })
            }
        }, _this._maingridSelectedChangedDelay);

    },


    initProductOpenUrlHint: function (bill, detailGrid) {
        var shoptype = bill.shopType;
        var hasSupport = $common.ajaxSync({
            url: "sale/eshoporder/eshoplist/hasSupportOpenPtypeUrl/" + shoptype,
            data: null,
            type: 'get',
            router: 'ngp'
        });
        if (hasSupport.data) {
            detailGrid.findColumn("platformPtypeName").set_hint("双击可以直接查询网店网店商品信息");
            detailGrid.findColumn("platformPropertiesName").set_hint("双击可以直接查询网店网店商品信息");
            detailGrid.findColumn("platformPtypeXcode").set_hint("双击可以直接查询网店网店商品信息");
            return;
        }
        detailGrid.findColumn("platformPtypeName").set_hint("");
        detailGrid.findColumn("platformPropertiesName").set_hint("");
        detailGrid.findColumn("platformPtypeXcode").set_hint("");
    },

    doShowInvoice:function (sender){
        var form = this.get_form()
        var frm = new Sys.UI.Form(sender);
        var mainGrid = form.c_grid_Audit;
        var data = mainGrid.get_selectedRowData();
        var param = {
            shopId:data.otypeId,
            tradeId:data.tradeOrderId
        }
        frm.showModal(" sale/eshoporder/common/OrderInvoiceInfo.gspx",param);
    },

    _refreshInvoiceInfo: function (form, bill) {
        form.invoiceInfoFlowPanel.set_visible(true);
        form.invoiceInfoFlowPanel.dataBind(null);
        if (!bill.invoiceInfo.invoiceRequired) {
            form.notRequired.set_visible(true);
            form.needInvoiceNotRequired.set_value(bill.invoiceInfo.invoiceRequired);
            form.invoiceInfoFlowPanel.set_visible(false);
            return;
        }
        form.invoiceInfoFlowPanel.dataBind(bill);
        if (bill.shopType == 52 && bill.createType==1) {
            form.notRequired.set_visible(true);
            form.showInvoice.set_visible(true);
            form.needInvoiceNotRequired.set_value(bill.invoiceInfo.invoiceRequired);
            form.invoiceInfoFlowPanel.set_visible(false);
            return;
        }
        if (bill.invoiceInfo.invoiceType == 0) {
            form.edInvoiceRemark.set_visible(false);
            form.edInvoiceRegisterAddr.set_visible(false);
            form.edInvoiceRegisterPhone.set_visible(false);
            form.edInvoiceBankAccount.set_visible(false);
            form.edInvoiceBank.set_visible(false);
            form.edInvoiceSpecialCode.set_visible(false);
        } else {
            form.edInvoiceRemark.set_visible(true);
            form.edInvoiceRegisterAddr.set_visible(true);
            form.edInvoiceRegisterPhone.set_visible(true);
            form.edInvoiceBankAccount.set_visible(true);
            form.edInvoiceBank.set_visible(true);
            form.edInvoiceSpecialCode.set_visible(true);
        }
        form.showInvoiceInfo.set_visible(true);
        form.resetInvoiceInfo.set_visible(true);
    },

    _doBuildOrderDetailPic: function (details) {
        if (details == null || details.length == 0) {
            return details;
        }
        var priceLength = $ms.ngpConfig.Sys.sysDigitalPrice;
        var totalLength = $ms.ngpConfig.Sys.sysDigitalTotal
        for (var i = 0; i < details.length; i++) {
            details[i].distributionBalanceTaxedPrice = Math.round(details[i].distributionBalanceTaxedPrice * Math.pow(10, priceLength)) / Math.pow(10, priceLength);
            details[i].distributionDisedTaxedTotal = Math.round(details[i].distributionDisedTaxedTotal * Math.pow(10, totalLength)) / Math.pow(10, totalLength);
            if (!details[i].purchase) {
                details[i].purchase = new Object();
            }
            details[i].purchase.purchasePrice = Math.round(details[i].purchase.purchasePrice * Math.pow(10, priceLength)) / Math.pow(10, priceLength);
            details[i].purchase.purchaseTotal = Math.round(details[i].purchase.purchaseTotal * Math.pow(10, totalLength)) / Math.pow(10, totalLength);
        }
        if ($eshoppower.getSysdata("jarvisDeliverBillDetailShowEnabled")             //默认线上图片优先
            || $eshoppower.getSysdata("jarvisDeliverBillShowOnlineDetailPicturePriority")) { //线上图片优先
            for (var i = 0; i < details.length; i++) {
                details[i].platformPtypePicUrl = details[i].platformPtypePicUrl ? details[i].platformPtypePicUrl : details[i].picUrl;
            }
            return details;
        }
        for (var i = 0; i < details.length; i++) {
            details[i].platformPtypePicUrl = details[i].picUrl ? details[i].picUrl : details[i].platformPtypePicUrl;
        }
        return details;
    },

    receiverDbClick: function (sender) {
        var form = this.get_form();
        var grid = form.c_grid_Audit;
        var data = grid.get_selectedRowData();
        if (!data) {
            return;
        }
        if (data.length == 0) {
            return;
        }
        var canDecrypt = $ms.getPower("sale.buyer.cryptograph.view");
        if (!canDecrypt) {
            return;
        }
        var buyerData = data.eshopBuyer;
        var idField = sender.get_dataField().replace("eshopBuyer.", "");
        var rowindex = grid.get_selectedRowIndex();
        var requestParams = Object();
        requestParams.eshopOrderId = data.eshopOrderId;
        var index = grid.get_selectedRowIndex();
        var eshopBuyer = $eshoporder.orderDecrypt.doBatchDecrypt([
            {
                "di": data.eshopBuyer.di,
                "shopId": data.otypeId,
                "scene": 0,
                "address": data.eshopBuyer.customerReceiverFullAddress
            }
        ], buyerData, "", this, this.fullDecryptedCallBack);
        if (!eshopBuyer) {
            return;
        }
        data.eshopBuyer = eshopBuyer;
        grid.modifyRowData(rowindex, data);
        form.ReceiverInfoFlowPanel.dataBind(eshopBuyer);
        data.invoiceInfo.invoiceBankAccount = this.getInvoiceBankAccount(data.invoiceInfo.invoiceBankAccount, eshopBuyer.invoiceBankAccount);
        form.invoiceInfoFlowPanel.dataBind(data);

        // grid.modifyCellValue(index, grid.getColumn("eshopBuyer.customerShopAccount"), eshopBuyer.customerShopAccount);
        // grid.modifyCellValue(index, grid.getColumn("eshopBuyer.fullBuyerInfo"), eshopBuyer.fullBuyerInfo);
    },
    doSignReceiveInfo: function (sender) {
        var form = this.get_form();
        var grid = form.c_grid_Audit;
        var data = grid.get_selectedRowData();
        if (!data) {
            return;
        }
        if (data.length == 0) {
            return;
        }
        var buyerData = data.eshopBuyer;
        var idField = sender.get_dataField().replace("eshopBuyer.", "");
        var rowindex = grid.get_selectedRowIndex();
        var requestParams = Object();
        requestParams.eshopOrderId = data.eshopOrderId;
        var result = $eshoporder.orderDecrypt.doBatchDecrypt([
            {
                "di": buyerData.di,
                "shopId": data.otypeId,
                "scene": 0,
                "address": buyerData.customerReceiverFullAddress
            }
        ], buyerData, "", this, this.fullDecryptedCallBack)
        data.eshopBuyer[idField] = result[idField];
        grid.modifyRowData(rowindex, data);
        form.ReceiverInfoFlowPanel.dataBind(data.eshopBuyer);
        data.invoiceInfo.invoiceBankAccount = this.getInvoiceBankAccount(data.invoiceInfo.invoiceBankAccount, result.invoiceBankAccount);
        form.invoiceInfoFlowPanel.dataBind(data);

    },
    fullDecryptedCallBack: function (_this, result) {
        var form = _this.get_form();
        var grid = form.c_grid_Audit;
        var bill = grid.get_selectedRowData();
        var index = grid.get_selectedRowIndex();
        form.ReceiverInfoFlowPanel.dataBind(result);
        bill.invoiceInfo.invoiceBankAccount = _this.getInvoiceBankAccount(bill.invoiceInfo.invoiceBankAccount, result.invoiceBankAccount);
        form.invoiceInfoFlowPanel.dataBind(bill);
        bill.eshopBuyer = result;
        grid.modifyRowData(index, bill);
    },

    fullRealBuyerDecryptedCallBack: function (_this, result) {
        var form = _this.get_form();
        var grid = form.c_grid_Audit;
        var bill = grid.get_selectedRowData();
        var index = grid.get_selectedRowIndex();
        form.realBuyerFlowPanel.dataBind(result);
        bill.extend.realBuyer = result;
        grid.modifyRowData(index, bill);
    },

    fullSelfPickUpInfoDecryptedCallBack: function (_this, result) {
        var form = _this.get_form();
        var grid = form.c_grid_Audit;
        var bill = grid.get_selectedRowData();
        var index = grid.get_selectedRowIndex();
        form.selfPickUpFlowPanel.dataBind(result);
        bill.extend.selfPickUpInfo = result;
        grid.modifyRowData(index, bill);
    },

    doCopySelfPickUpInfo: function (sender) {
        var form = sender.get_form();
        var grid = form.c_grid_Audit;
        var selectedRowData = grid.get_selectedRowData();
        if (!selectedRowData) {
            return;
        }
        var selfPickUpInfo = selectedRowData.extend.selfPickUpInfo;
        if (!selectedRowData)
            return;
        if (!selfPickUpInfo) {
            $common.showWarn('自提点为空');
            return;
        }
        var power = $saleUtils.getPower('eshoporder.eshopsaleorder.copyBuyerInfo');
        if (!power) {
            $common.showWarn('无权限操作');
            return;
        }
        var decryptResult = $eshoporder.orderDecrypt.doBatchDecrypt([{
            "di": selfPickUpInfo.di,
            "shopId": selectedRowData.otypeId,
            "scene": 0,
            "address": selfPickUpInfo.customerReceiverFullAddress
        }]);
        if (!decryptResult) {
            return;
        }
        var txt = "";
        var result = decryptResult[selfPickUpInfo.di];
        if (result == undefined || Object.getOwnPropertyNames(result).length == 0) {
            // 平台解密方法会提示错误信息
            // $common.showWarn('调用平台解密接口失败，无法复制');
            return;
        }
        if (result.account) {
            txt += result.account + ",";
        } else if (selfPickUpInfo.customerShopAccount) {
            txt += selfPickUpInfo.customerShopAccount + ",";
        }
        if (result.contactor) {
            txt += result.contactor + ",";
        } else if (selfPickUpInfo.customerReceiver) {
            txt += selfPickUpInfo.customerReceiver + ",";
        }
        if (result.mobile) {
            txt += result.mobile + ",";
        } else if (selfPickUpInfo.customerReceiverMobile) {
            txt += selfPickUpInfo.customerReceiverMobile + ",";
        }
        if (result.phone) {
            txt += result.phone + ",";
        } else if (selfPickUpInfo.customerReceiverPhone) {
            txt += selfPickUpInfo.customerReceiverPhone + ",";
        }
        var address = "";
        if (result.address) {
            address = result.address;
        } else {
            address = selfPickUpInfo.customerReceiverAddress;
        }
        txt += selfPickUpInfo.customerReceiverCountry + selfPickUpInfo.customerReceiverProvince +
            selfPickUpInfo.customerReceiverCity + selfPickUpInfo.customerReceiverDistrict +
            selfPickUpInfo.customerReceiverTown + address;
        $common.copyText(txt);
    },

    doCopyRealBuyerInfo: function (sender) {
        var form = sender.get_form();
        var grid = form.c_grid_Audit;
        var selectedRowData = grid.get_selectedRowData();
        if (!selectedRowData) {
            return;
        }
        var realBuyerInfo = selectedRowData.extend.realBuyer;
        if (!realBuyerInfo) {
            $common.showWarn('买家信息为空');
            return;
        }
        var power = $saleUtils.getPower('eshoporder.eshopsaleorder.copyBuyerInfo');
        if (!power) {
            $common.showWarn('无权限操作');
            return;
        }
        var decryptResult = $eshoporder.orderDecrypt.doBatchDecrypt([{
            "di": realBuyerInfo.di,
            "shopId": selectedRowData.otypeId,
            "scene": 0,
            "address": realBuyerInfo.customerReceiverFullAddress
        }]);
        if (!decryptResult) {
            return;
        }
        var txt = "";
        var result = decryptResult[realBuyerInfo.di];
        if (result == undefined || Object.getOwnPropertyNames(result).length == 0) {
            // 平台解密方法会提示错误信息
            // $common.showWarn('调用平台解密接口失败，无法复制');
            return;
        }
        if (result.contactor) {
            txt += result.contactor + ",";
        } else if (realBuyerInfo.customerReceiver) {
            txt += realBuyerInfo.customerReceiver + ",";
        }
        if (result.mobile) {
            txt += result.mobile + ",";
        } else if (realBuyerInfo.customerReceiverMobile) {
            txt += realBuyerInfo.customerReceiverMobile + ",";
        }
        if (result.phone) {
            txt += result.phone + ",";
        } else if (realBuyerInfo.customerReceiverPhone) {
            txt += realBuyerInfo.customerReceiverPhone + ",";
        }
        var address = "";
        if (result.address) {
            address = result.address;
        } else {
            address = realBuyerInfo.customerReceiverAddress;
        }
        txt += realBuyerInfo.customerReceiverCountry + realBuyerInfo.customerReceiverProvince +
            realBuyerInfo.customerReceiverCity + realBuyerInfo.customerReceiverDistrict +
            realBuyerInfo.customerReceiverTown + address;
        $common.copyText(txt);
    },

    doCopyReceiveInfo: function (sender) {
        var form = sender.get_form();
        var grid = form.c_grid_Audit;
        var selectedRowData = grid.get_selectedRowData();
        if (!selectedRowData) {
            return;
        }
        var eshopBuyer = selectedRowData.eshopBuyer;
        if (!eshopBuyer) {
            $common.showWarn('收货信息为空');
            return;
        }
        var power = $saleUtils.getPower('eshoporder.eshopsaleorder.copyBuyerInfo');
        if (!power) {
            $common.showWarn('无权限操作');
            return;
        }
        var decryptResult = $eshoporder.orderDecrypt.doBatchDecrypt([{
            "di": eshopBuyer.di,
            "shopId": selectedRowData.otypeId,
            "scene": 0,
            "address": eshopBuyer.customerReceiverFullAddress
        }]);
        if (!decryptResult) {
            return;
        }
        var txt = "";
        var result = decryptResult[eshopBuyer.di];
        if (result == undefined || Object.getOwnPropertyNames(result).length == 0) {
            // 平台解密方法会提示错误信息
            // $common.showWarn('调用平台解密接口失败，无法复制');
            return;
        }
        if (result.contactor) {
            txt += result.contactor + ",";
        } else if (eshopBuyer.customerReceiver) {
            txt += eshopBuyer.customerReceiver + ",";
        }
        if (result.mobile) {
            txt += result.mobile + ",";
        } else if (eshopBuyer.customerReceiverMobile) {
            txt += eshopBuyer.customerReceiverMobile + ",";
        }
        if (result.phone) {
            txt += result.phone + ",";
        } else if (eshopBuyer.customerReceiverPhone) {
            txt += eshopBuyer.customerReceiverPhone + ",";
        }
        var address = "";
        if (result.address) {
            address = result.address;
        } else {
            address = eshopBuyer.customerReceiverAddress;
        }
        txt += eshopBuyer.customerReceiverCountry + eshopBuyer.customerReceiverProvince +
            eshopBuyer.customerReceiverCity + eshopBuyer.customerReceiverDistrict +
            eshopBuyer.customerReceiverTown + address;
        $common.copyText(txt);
    },

    doShowInvoiceInfo: function (sender) {
        var form = sender.get_form();
        var bill = form.c_grid_Audit.get_selectedRowData();
        if (!bill) {
            return;
        }
        var grid = form.c_grid_Audit;
        var index = grid.get_selectedRowIndex();
        $common.showLoading();
        var result = $common.ajaxSync({
            url: "sale/eshoporder/order/getOrderInvoiceInfo",
            data: bill.invoiceInfo,
            type: 'post',
            router: 'ngp'
        });
        $common.hideLoading();
        if (!result) {
            return;
        }
        bill.invoiceInfo = result.data;
        form.invoiceInfoFlowPanel.dataBind(bill);
    },

    doShowReceiveInfo: function (sender) {
        var form = sender.get_form();
        var bill = form.c_grid_Audit.get_selectedRowData();
        if (!bill) {
            return;
        }
        var grid = form.c_grid_Audit;
        var index = grid.get_selectedRowIndex();
        var requestParams = {};
        requestParams.eshopOrderId = bill.eshopOrderId;
        $common.showLoading();
        var result = $eshoporder.orderDecrypt.doBatchDecrypt([
            {
                "di": bill.eshopBuyer.di,
                "shopId": bill.otypeId,
                "scene": 0,
                "address": bill.eshopBuyer.customerReceiverFullAddress
            }
        ], bill.eshopBuyer, "", this, this.fullDecryptedCallBack);
        //var result = $eshoporder.orderDecrypt.DecrytWholeInfoByVchcode(this, bill.vchcode, bill.eshopBuyer.di);
        $common.hideLoading();
        if (!result) {
            return;
        }
        var grid = form.c_grid_Audit;
        var bill = grid.get_selectedRowData();
        var index = grid.get_selectedRowIndex();
        form.ReceiverInfoFlowPanel.dataBind(result);
        bill.invoiceInfo.invoiceBankAccount = this.getInvoiceBankAccount(bill.invoiceInfo.invoiceBankAccount, result.invoiceBankAccount);
        form.invoiceInfoFlowPanel.dataBind(bill);
        bill.eshopBuyer = result;
        grid.modifyRowData(index, bill);
        // var pattern = /[-]\d{4}$/;
        // if (pattern.test(bill.eshopBuyer.customerReceiverMobile)) {
        //     form.explain.set_enabled(true);
        //     form.explain.set_visible(true);
        //     form.virtualNumber1Message.set_text("当前联系人手机号为虚拟号，到期时间："+this.dateFormat2(result.secretNoExpireTime)+"，<a href=\"#\" onclick='recharge'>点我进行虚拟号租用续期</a>。关于虚拟号更多介绍请点击<a href=\"https://huodong.taobao.com/wow/z/mt/default/HmZpDk4CTanZyJKDeEps?spm=a1zfx5.my_create_page.0.0.fde22251cebrhn\" target='_blank'>什么是虚拟号？</a>");
        // }
    },

    doShowRealBuyerInfo: function (sender) {
        var form = sender.get_form();
        var bill = form.c_grid_Audit.get_selectedRowData();
        if (!bill) {
            return;
        }
        var realBuyerInfo = bill.extend.realBuyer;
        if (!bill || !realBuyerInfo) {
            $common.showWarn('买家信息为空');
            return;
        }
        var requestParams = {};
        requestParams.eshopOrderId = bill.eshopOrderId;
        $common.showLoading();
        var result = $eshoporder.orderDecrypt.doBatchDecrypt([
            {
                "di": realBuyerInfo.di,
                "shopId": bill.otypeId,
                "scene": 0,
                "address": realBuyerInfo.customerReceiverFullAddress
            }
        ], bill.extend.realBuyer, "", this, this.fullRealBuyerDecryptedCallBack);
        $common.hideLoading();
        if (!result) {
            return;
        }
        var grid = form.c_grid_Audit;
        var bill = grid.get_selectedRowData();
        form.realBuyerFlowPanel.dataBind(result);
        bill.extend.realBuyer = result;
    },

    doShowSelfPickUpInfo: function (sender) {
        var form = sender.get_form();
        var bill = form.c_grid_Audit.get_selectedRowData();
        if (!bill) {
            return;
        }
        var selfPickUpInfo = bill.extend.selfPickUpInfo;
        if (!bill || !selfPickUpInfo) {
            $common.showWarn('自提点信息为空');
            return;
        }
        var requestParams = {};
        requestParams.eshopOrderId = bill.eshopOrderId;
        $common.showLoading();
        var result = $eshoporder.orderDecrypt.doBatchDecrypt([
            {
                "di": selfPickUpInfo.di,
                "shopId": bill.otypeId,
                "scene": 0,
                "address": selfPickUpInfo.customerReceiverFullAddress
            }
        ], bill.extend.selfPickUpInfo, "", this, this.fullSelfPickUpInfoDecryptedCallBack);
        $common.hideLoading();
        if (!result) {
            return;
        }
        var grid = form.c_grid_Audit;
        var bill = grid.get_selectedRowData();
        form.selfPickUpFlowPanel.dataBind(result);
        bill.extend.realBuyer = result;
    },

    recharge: function (sender) {
        var form = sender.get_form();
        var grid = form.deliverMainGrid;
        var item = grid.get_selectedRowData();
        form.get_service().recharge(item.eshopOrderId, function (result) {
            form.virtualNumber1Message.set_text("当前联系人手机号为虚拟号，到期时间：" + this.dateFormat2(result.secretNoExpireTime) + "，<a href=\"#\" onclick='recharge'>点我进行虚拟号租用续期</a>。关于虚拟号更多介绍请点击<a href=\"https://huodong.taobao.com/wow/z/mt/default/HmZpDk4CTanZyJKDeEps?spm=a1zfx5.my_create_page.0.0.fde22251cebrhn\" target='_blank'>什么是虚拟号？</a>");
            alert("续期成功");
        });
    },

    dateFormat2: function (date) {
        if (date == null || '' == date) {
            return
        }
        date.replace("T", " ");
        return date.split(".")[0];
    },


    reset: function (sender) {
        var form = sender.get_form();
        var grid = form.c_grid_Audit;
        var bill = grid.get_selectedRowData();
        if (!bill)
            return;
        var buyerInfo = (form.hideBuyerInfo && form.hideBuyerInfo.get_value()) ? Object.clone(form.hideBuyerInfo.get_value()) : bill.eshopBuyer;
        form.ReceiverInfoFlowPanel.dataBind(buyerInfo);
        bill.invoiceInfo.invoiceBankAccount = buyerInfo.invoiceBankAccount;
        form.invoiceInfoFlowPanel.dataBind(bill);
        var index = grid.get_selectedRowIndex();
        bill.eshopBuyer = buyerInfo;
        grid.modifyRowData(index, bill);
    },

    resetInvoice: function (sender) {
        var form = sender.get_form();
        var grid = form.c_grid_Audit;
        var bill = grid.get_selectedRowData();
        if (!bill)
            return;
        var invoiceInfo = (form.hideInvoiceInfo && form.hideInvoiceInfo.get_value()) ? Object.clone(form.hideInvoiceInfo.get_value()) : bill.invoiceInfo;
        bill.invoiceInfo = invoiceInfo;
        form.invoiceInfoFlowPanel.dataBind(bill);
    },

    getInvoiceBankAccount: function (beforeBankAccount, afterBankAccount) {
        return $eshoporder.orderDecrypt.strIsEmpty(afterBankAccount) ? beforeBankAccount : afterBankAccount;
    },

    doShowImport: function (sender) {
        var frm = new Sys.UI.Form(sender);
        var that = this;
        frm.set_params({
            reportName: 'sale-export|sale-order-import',
            title: '原始订单导入'
        });
        frm.showModal("exportcenter/ui/ImportNew.gspx");
        frm.add_loaded(function () {

            // 动态创建组件
            var panel = $createControl("FlowPanel", {
                ID: "otypeSelectPanel",
                ItemLabelWidth:100,
                CssStyle: 'margin-top:20px;'
            }, frm)

            $createControl("SelectorEdit", {
                ID: "seEShopName",
                Label: "网店:", // 这里面 有Lable，必须用HPanel或FlowPanel才行，不能用HBlock
                DataField: "otypeId",
                DisplayField: "fullname",
                NullDisplayText: "仅支持单选",
                OnEnterPress: that.doEshopSelectorEnterPress,
                OnButtonClick: that.doEshopInit,
                OnSelectorSelected: that.doEshopSelectorSelected,
                Onchange: that.notifyImportCenter,
                Required: true,
                LabelCssClass: "MustCharLeft"
            }, frm, panel);

            $createControl("DropDownEdit", {
                ID: "edBusinessType",
                Label: "业务类型:",
                ListItems: "201=普通销售,204=代销业务,203=分销业务",
                DataField: "businessType",
                DropDownStyle: "DropDownList",
                NullDisplayText: "仅支持单选",
                Onchange: that.notifyImportCenter,
                SelectedIndex: 0,
                Required: true
            }, frm, panel);

            var hblock = $createControl("HBlock", {
                CssClass: 'dflex'
            }, frm, panel);
            $createControl("Label", {
                ID: "label",
                Text: "导入说明",
                CssClass: "aicon-shuoming Button SpeedButton",
                OnClick: that.doShowImportConfig
            }, frm, hblock);

            $createControl("DropDownEdit ", {
                ID: "btypeConfig",
                Label: "往来单位:",
                ListItems: "0=不存在时过滤,1=不存在时新增",
                DataField: "btypeConfig",
                DropDownStyle: "DropDownList",
                NullDisplayText: "仅支持单选",
                Onchange: that.notifyImportCenter,
                SelectedIndex: 0,
                Required: true
            }, frm, panel);

            $createControl("CheckBox ", {
                ID: "payType",
                Text: "结算价只按客户价格本价格策略取值",
                Checked: false,
                DataField: "settleByPrice",
                CssStyle: "margin-left:25px",
                Onchange: that.notifyImportCenter,
                Visible: false,
                Required: true
            }, frm, panel);

            $createControl("DropDownEdit ", {
                ID: "orderProcessType",
                Label: "重复订单处理方式:",
                ListItems:"0=不导入该订单,1=更新订单",
                DataField:"orderProcessType",
                DropDownStyle:"DropDownList",
                NullDisplayText:"仅支持单选",
                Onchange:that.notifyImportCenter,
                SelectedIndex:$userCache.get('EshopSaleOrderImport-orderProcessType') == undefined ? 0 : $userCache.get('EshopSaleOrderImport-orderProcessType'),
                Required:true}, frm,panel);

            panel.insertAfter(frm.customComponent); // customComponent这个外面是FlowPanel，panel有是FlowPanel，干脆插到后面去。不然<FlowPanel><FlowPanel>了

            that.notifyImportCenter(frm);

            // var parameter = {};
            // parameter.queryVirtual = true;
            // var eshopDatas = $common.ajaxSync({
            //     url: "sale/eshoporder/eshop/getEshopByPlatformTypes",
            //     data: parameter,
            //     type: 'post',
            //     router: 'ngp'
            // });
            // frm.seEShopName.set_dataSource(eshopDatas.data)
        });
    },

    notifyImportCenter: function (sender) {
        if (sender.get_form().edBusinessType.get_value() == 203) {
            sender.get_form().payType.set_visible(true);
        } else {
            sender.get_form().payType.set_visible(false);
        }
        var orderProcessType = sender.get_form().orderProcessType.get_value();
        $userCache.set('EshopSaleOrderImport-orderProcessType',orderProcessType);
        var emitParm = {
            businessType: buildBusinessTypeMapping(sender.get_form().edBusinessType.get_value()),
            enableSubQty: $ms.ngpConfig.Sys.sysBusinessEnableSubUnitConfig,
            eshopId: sender.get_form().seEShopName.get_value(),
            settleByPrice: sender.get_form().payType.get_value(),
            btypeConfig: sender.get_form().btypeConfig.get_value(),
            orderProcessType: orderProcessType
        }
        $notify.emit("refresh-import-template", emitParm);

        function buildBusinessTypeMapping(code) {
            switch (code) {
                case 201:
                    return "SaleNormal";
                case 204:
                    return "SaleProxy";
                case 203:
                    return "SaleDistribution";
                default :
                    return "SaleNormal";
            }
        }
    },

    doOpenUrl: function (sender) {
        $common.openwin("https://help.wsgjp.com/5e03/d4b3/a3c1/1752/db3d/60ca");
    },
    doShowImportConfig: function (sender) {
        var frm = new Sys.UI.Form(sender);
        frm.showModal("sale/eshoporder/eshopsaleorder/SaleOrderImportConfig.gspx");
    },

    doEshopSelectorSelected: function (sender, eventArgs) {
        var selector = eventArgs.get_form();
        var result = selector.selectedData;
        sender.set_value(result.id);
        sender.set_text(result.fullname);
        var req = {
            id: 4,
            eshopId: result.id
        };
    },

    doEshopInit: function (sender) {
        var filterStr = sender.get_text();
        var parameter = {
            showadd: false,
            ocategorys: [0, 1, 2, 3],
            notineshoptypes: [890, 891]
        };
        if (filterStr && sender.get_textChanged()) {
            parameter.filtervalue = filterStr;
        }
        var url = "/jxc/baseinfo/selector/OtypeSelector.gspx";
        sender.set_selectorPageParams(parameter);
        sender.set_selectorPage(url);
        sender.set_showMDI(true);
    },

    doEshopSelectorEnterPress: function (sender, eventArgs) {
        if (sender.get_value() && !sender.get_textChanged()) {
            eventArgs.set_cancel(true);
        }
    },

    dispose: function () {
        sale.eshoporder.eshopsaleorder.EShopSaleOrderListNewAction.callBaseMethod(this, 'dispose');
        if (window.$SaleEShopSaleOrderListNewExceptionStatusTimer) {
            // 通过本页面的消息id，删除本页面的定时器
            window.$SaleEShopSaleOrderListNewExceptionStatusTimer.decrementPageCount(this.refreshExceptionStatusEventName, this.refreshExceptionStatusNotifyId);
        }
    }
    ,
    doQueryOrderLog: function (sender) {
        var form = sender.get_form();
        var grid = form.c_grid_Audit;
        var rowData = grid.get_selectedRowData() ? grid.get_selectedRowData() : {};
        var updateParams = new Object();
        updateParams.title = "原单日志查询";
        var _this = this;
        var popForm = new Sys.UI.Form(sender);
        popForm.set_params(updateParams);
        var eshopOrderId = rowData.eshopOrderId ? rowData.eshopOrderId : 0;
        popForm.showModal("sale/eshoporder/eshopsaleorder/EshopSaleOrderLog.gspx", {eshopOrderId: eshopOrderId});
        popForm.add_closed(function (pop) {
            if (!pop.result) {
                return;
            }
            _this._doRefreshTreeGrid();
            _this.btnQueryOnClick();
            //_this._refreshTreeView(form, selectItem);
        });
    },
    doGetFullNameText: function (sender, args) {
        var str = args.get_text();
        var grid = sender.get_grid();
        var rowIndex = args.get_rowIndex();
        var rowData = grid.findRowData(rowIndex);
        if (!rowData) {
            return;
        }
        if ((!rowData.ptypeName || rowData.ptypeName == '') && args._column._displayField == 'ptypeName') {
            return;
        }
        if ((!rowData.platformPtypeName || rowData.platformPtypeName == '') && args._column._dataField == 'platformPtypeName') {
            return;
        }
        var text = "<div  class=\"dflex pr10 overhidden\"><span  class=\"ellipsis\">" + str + "</span>";

        if (rowData.extend && rowData.extend.gift && (args._column._dataField == 'platformPtypeName' || args._column._displayField == 'ptypeName')) {
            text += '&nbsp;<span class="ExpandTipText ExpandTipZeng">赠</span>'
        }
        if (rowData.comboRow && (args._column._dataField != 'platformPtypeName')) {
            text += '&nbsp;<span class="ExpandTipText ExpandTipTao">套</span>'
        }
        if (rowData.batchenabled && (args._column._dataField != 'platformPtypeName')) {
            text += '&nbsp;<span class="ExpandTipText ExpandTipPi">批</span>'
        }
        if (rowData.snenabled && (args._column._dataField != 'platformPtypeName')) {
            text += '&nbsp;<span class="ExpandTipText ExpandTipXu">序</span>'
        }
        if (rowData.propenabled && (args._column._dataField != 'platformPtypeName')) {
            text += '&nbsp;<span class="ExpandTipText ExpandTipShu">属</span>'
        }
        if (rowData.pcategory == 1 && (args._column._dataField != 'platformPtypeName')) {
            text += '&nbsp;<span class="ExpandTipText ExpandTipFU">服</span>'
        }
        text += "</div>";
        args.set_text(text);
        args._hint = str;
    },
    doShowFilter: function (sender) {
        var grid = this.get_form().c_grid_Audit;
        grid.set_allowFilter(!grid.get_allowFilter());
    },
    doFilterRendering: function (sender, args) {
        var form = this.get_form();
        var column = sender;

        var dataField = column.get_dataField(); // 列名称

        var editID = dataField + "_edit";  // 获取编辑框控件的id，必须唯一
        var panel = $createControl(Craba.UI.Block, {CssClass: "FlexAuto"}, form);
        var bar = sender.get_grid()._filterBar;
        this._customFilterColumn.push(column);

        switch (dataField) {
            case 'orderTags':
                //提醒
                $createControl('DropDownEdit',
                    {
                        ID: editID,
                        DataSource: this.markTypeList,
                        DataField: "code",
                        DataTextField: "name",
                        DataValueField: "code",
                        CssClass: "FlexAuto",
                        DropDownStyle: "DropDownSearch",
                        TabOnEnter: false,
                        OnChange: $createDelegate(bar, bar.doFilter)
                    },
                    form,
                    panel);
                break;
            case 'sellerFlag':
                //旗帜
                $createControl('ImageDropDownEdit',
                    {
                        ID: editID,
                        DataSource: this.flagsList,
                        DataField: "sellerFlag",
                        DataTextField: "name",
                        DataValueField: "code",
                        CssClass: "FlexAuto",
                        DropDownStyle: "DropDownSearch",
                        OnChange: $createDelegate(bar, bar.doFilter)
                    },
                    form,
                    panel);
                break;
            case 'exportState':
                //导出状态
                var data = [
                    {name: '未导出', code: 0},
                    {name: '已导出', code: 1},
                ]
                $createControl('DropDownEdit',
                    {
                        ID: editID,
                        DataSource: data,
                        DataField: "code",
                        DataTextField: "name",
                        DataValueField: "code",
                        CssClass: "FlexAuto",
                        DropDownStyle: "DropDownSearch",
                        OnChange: $createDelegate(bar, bar.doFilter)
                    },
                    form,
                    panel);
                break;
            default:
                return;
        }
        args.set_control(panel); // 回传给平台的自定义编辑器
    },
    doGetOtherFilter: function (sender, args) {
        var filter = args.get_filter();

        var list = this._customFilterColumn;
        if (!list && list.length == 0) return;

        for (var i = 0; i < list.length; i++) {
            var dataField = list[i].get_dataField();
            var editID = dataField + "_edit";
            var form = this.get_form();
            var edit = form[editID]; // 取出筛选器控件
            if (!edit) continue;

            if (!filter) {
                //清空自定义筛选
                edit.set_value(null);
                continue;
            }

            var filterItem = {};
            filterItem.dataField = dataField;
            filterItem.value = edit.get_value();
            filterItem.type = list[i].get_filterType();

            filter.items.push(filterItem);
        }
    },
    doRenderTextIcon: function (sender, eventArgs) {
        var node = eventArgs.get_node();
        // 快速过滤根节点设置未不可勾选，并设置参数值
        if (node.get_data().name == "全部订单") {
            $dom.addCssClass(node.get_nodeElement(), 'hideCheckBox');
        }
    },
    doShowExportOperationPopup: function (sender, e) {
        var form = this.get_form();
        var btn = sender.get_bounds();
        form.menu3.popup(e);
        form.menu3._element.style["top"] = (btn.y + 35) + "px";
    },

    /**
     * 此方法勾选、取消勾选都会触发此事件
     */
    doTreeNodeCheckedChanged: function (sender, evg) {
        var form = sender.get_form();
        var nodes = form.quickQuery.get_checkedNodes();
        // 取消之前点击的节点，避免混淆
        form.quickQuery.clearSelect();
        // 整合所有勾选节点的查询参数（全部节点除外，此节点没有勾选框但是勾选状态）
        var allParams = this._doBuildAllParamsByNodes(nodes);

        // 将参数填充到查询面板中
        this._doFillQueryPanelByParams(form, allParams);
    },

    _doFillQueryPanelByParams: function (form, params) {
        if (!params || params.length == 0 ||
            (!params.otypeIds && !params.businessTypes && !params.refundStates && !params.intSellerFlag && !params.timeType)) {
            return;
        }
        params.otypeId = params.otypeIds;
        params.businessType = params.businessTypes;
        params.localRefundProcessState = params.refundStates;
        params.sellerFlag = params.intSellerFlag;
        // 设置自定义查询节点保存的时间类型
        if (params.timeType) {
            form.timeType.set_selectedIndex(params.timeType);
        }
        // 点击节点直接覆盖查询面板
        form.customerQueryPanel.dataBind(params);
        // form.filterKeyType.set_selectedIndex(0);
        // // 重置批量条件下输入框的提示信息
        // form.keyWord.set_nullDisplayText("支持复制多个订单编号，分隔符使用逗号','，空格或回车");
    },

    _doBuildAllParamsByNodes: function (nodes) {
        // 无论查询还是勾选，都清空之前的隐藏参数
        this._queryHideParams = [];
        var allParams = {};
        for (var i = 0; i < nodes.length; i++) {
            var node = nodes[i].get_value();
            if (node._text != "全部订单") {
                var nodeParam = node._data.queryParams;
                if (!nodeParam) {
                    continue;
                }
                for (var j = 0; j < nodeParam.length; j++) {
                    if (nodeParam[j].fieldValue) {
                        // 普通条件赋值处理-查询面板可以赋值的条件
                        if (!allParams[nodeParam[j].fieldValue]) {
                            // 参数中无此条件直接填充
                            allParams[nodeParam[j].fieldValue] = nodeParam[j].fieldText;
                        } else {
                            // 如果条件有冲突，需要额外处理
                            if (nodeParam[j].fieldText && nodeParam[j].fieldText != '') {
                                // 留言备注需要额外处理，同时包含1，2则置为0(不过滤)
                                if (nodeParam[j].fieldValue == "timeType") {
                                    allParams[nodeParam[j].fieldValue] = nodeParam[j].fieldText;
                                } else {
                                    allParams[nodeParam[j].fieldValue] = allParams[nodeParam[j].fieldValue] + "," + nodeParam[j].fieldText;
                                }
                            }
                        }
                    } else {
                        // 特殊无法带入查询面板的条件额外处理
                        if (nodeParam[j].hideParamsValue) {
                            this._queryHideParams.push(nodeParam[j]);
                        }
                    }
                }
            }
        }
        return allParams;
    },

    doOpen: function (sender) {
        var popForm = new Sys.UI.Form(sender, true);
        var param = {
            btypeId: "1319703666055213412"
        }
        popForm.showModal("sale/eshoporder/eshopsaleorder/RetailPartHomeData.gspx", null);
    },

    explainVirtualNumber: function (sender) {
        var form = this.get_form();
        form.explainVirtualNumber1.popupAt(sender);
    },



    doShowBindBtype: function (sender) {
        var form = sender.get_form();
        var grid = form.c_grid_Audit;
        var power = $saleUtils.getPower("eshoporder.platformBtypeMap.view");
        if (!power) {
            $msg.alert("职员没有客户/供应商对应查看权限");
            return;
        }
        var rowData = grid.get_selectedRowData();
        if (rowData.platformDistributorName == "" && rowData.eshopBuyer.customerShopAccount == "") {
            $msg.alert("平台分销商名称和买家账号同时为空，不能做客户/供应商对应！");
            return;
        }
        var url = "sale/eshoporder/basic/BtypeMappingEdit.gspx";
        var popForm = new Sys.UI.Form(form);
        var param = {
            mode: "add",
            from: "saleOrder",
            tradeId: rowData.tradeOrderId,
            otypeId: rowData.otypeId
        };
        var _this = this;
        popForm.set_params(param);
        popForm.showModal(url);
        popForm.add_close(function () {
            _this.partialRefresh(sender);
        });
    },

    doPtypeColumnButtonClick: function (sender, args) {
        var grid = sender.get_column ? sender.get_column().get_grid() : sender.get_grid();
        var rowData = grid.get_selectedRowData();
        args.methodContext = {
            multiSelect: true,
            ktypeInfo: {
                ktypeId: null,
                subQtyVisible: false,
                inputPrice: false
            }
        }
    },

    // 没有调用，但是留在这里，这是通过js控制grid某行某列是否禁用的方法
    doGridAfterDataBind: function (sender) {
        // var grid = this.get_form().details;
        // var items = sender.getData();
        // for (var i = 0; i < items.length; i++) {
        //     var cell = grid.getRowCell(i, "ptypeCode")
        //     if (items[i] && items[i].mappingState) {
        //         cell.textContent = '';
        //     }
        // }
    },


    doColumnChanged: function (sender, args) {
        if (args._value !== undefined && args._oldValue == args._value || args._oldValue == '' && args._value === null) {
            return;
        }
        $eshoppower.checkPowerFalse("eshoporder.eshopPtypeRelationPage.batchBind");
        //通过删除批次触发，会导致序列号直接被清空，这里不需要让它继续，后续通过主动发起处理
        if (!args.column && !args._value && sender._activeColumn.get_dataField() == 'batchNo') {
            return;
        }
        this._checkChanged = true;
        if (args.column) {//列业务控件发起的变动
            this.doGridChanged(args.column, {
                get_data: function () {
                    return args.data;
                },
                get_triggerData: function () {//原始数据，表示业务控件没有做数据处理，直接让前端js来处理业务
                    return args.triggerData;
                }
            })
        } else {
            //自己的变动事件
            var rowIndex = args._rowIndex;
            var rowData = sender.findRowData(rowIndex);
            if (!rowData) {
                return;
            }
            rowData.modify = true;
            var data = [{
                rowIndex: rowIndex,
                rowData: rowData
            }]

            this.doGridChanged(args._column, {
                get_data: function () {
                    return data;
                },
                get_triggerData: function () {//原始数据，表示业务控件没有做数据处理，直接让前端js来处理业务
                    return args.triggerData;
                }
            })
        }
    },

    doGridChanged: function (sender, args) {
        var form = this.get_form();
        var newData = args.get_data();
        var grid = sender.get_grid();
        var dataField = sender.get_dataField();
        var defaultFields = args.get_defaultFields ? args.get_defaultFields() : [];
        defaultFields.push(dataField)
        var recordsheetLinkage = new recordsheet.linkage(grid, newData, defaultFields);
        var _this = this;
        if (dataField == "ptypeCode") {
            this.gridPtypesChanged(sender, args);//sku必然改变，才会进入，包含没有使用批次的商品，加载成本
        }
    },

    gridPtypesChanged: function (sender, args) {
        var triggerData = args.get_triggerData ? args.get_triggerData() : null;
        if (triggerData && triggerData.dataList.length > 0) {
            var rowData = triggerData.dataList;
            this.buildPropAndPorpvalue(rowData);
            this.doPtypeNameSelected(sender, args, triggerData.dataList);
        }
    },

    buildPropAndPorpvalue: function (rowData) {
        var sku = rowData[0].sku;
        var localPropertiesName = "";
        var localProperties = "";
        if (sku) {
            if (sku.propName1) {
                localPropertiesName = sku.propName1;
            }
            if (sku.propName2) {
                localPropertiesName += ":" + sku.propName2;
            }
            if (sku.propName3) {
                localPropertiesName += ":" + sku.propName3;
            }
            if (sku.propName4) {
                localPropertiesName += ":" + sku.propName4;
            }
            if (sku.propName5) {
                localPropertiesName += ":" + sku.propName5;
            }
            if (sku.propName6) {
                localPropertiesName += ":" + sku.propName6;
            }
            if (sku.propvalueName1) {
                localProperties = sku.propvalueName1
            }
            if (sku.propvalueName2) {
                localProperties += ":" + sku.propvalueName2
            }
            if (sku.propvalueName3) {
                localProperties += ":" + sku.propvalueName3
            }
            if (sku.propvalueName4) {
                localProperties += ":" + sku.propvalueName4
            }
            if (sku.propvalueName5) {
                localProperties += ":" + sku.propvalueName5
            }
            if (sku.propvalueName6) {
                localProperties += ":" + sku.propvalueName6
            }
        }
        rowData[0].localPropertiesName = localPropertiesName;
        rowData[0].localProperties = localProperties;
    },

    doPtypeNameSelected: function (sender, eventArgs, rowData) {
        var _this = this;
        var dataList = [];
        if (null == rowData) {
            var selector = eventArgs.get_form();
            dataList = selector.dataList;
        } else {
            dataList = rowData;
        }

        if (!dataList || dataList.length === 0) {
            return;
        }
        var form = this.get_form();
        var grid = form.details;
        var selectedRowData = grid.get_selectedRowData();
        var index = grid.get_selectedRowIndex();
        var data = dataList[0];
        var dataCombos = [];
        var comboName = selectedRowData.platformPropertiesName.length > 0 ? selectedRowData.platformPropertiesName + "-" + selectedRowData.platformPtypeName : selectedRowData.platformPtypeName;
        if (dataList.length > 1 || data.unitQty > 1) {
            $eshoppower.checkPowerFalse("baseinfo.combo.add");
            for (var i = 0; i < dataList.length; i++) {
                var item = dataList[i];
                if (!item.sku) {
                    $common.showError("选择有属性商品进行临时对应时，必须选择sku商品");
                    return;
                }
                item.sourceUsercode = selectedRowData.platformPtypeXcode;
                // if (!item.price) {
                //     $common.showInfo("请填写商品单价");
                //     return;
                // }
                item.price = !item.price ? 0 : item.price;
                var type = typeof (item.price);
                if (type === 'object') {
                    item.price = 1;
                }
                item.comboName = comboName;
                item.comboxcode = selectedRowData.platformPtypeXcode;
                dataCombos.push(item);
            }
            var comboResult = $common.ajaxSync({
                url: "sale/eshoporder/relation/buildCombo",
                data: dataCombos,
                router: 'ngp'
            });
            if(comboResult.code!='200' && comboResult.message){
                $common.showError( comboResult.message);
                return;
            }
            var res = comboResult.data;
            if (res && (res.code == "200" || res.code == "201")) {
                var combodata = res.data;
                if (combodata.stoped == 1 || combodata.deleted == 1) {
                    $common.showInfo("该套餐已停用或已被删除");
                    return;
                }
                data = res.data;
                var oldrowData = Object.clone(selectedRowData);
                if (res.code == "201") {
                    $common.confirm(comboResult.data.message, function (result) {
                        if (!result) {
                            grid.modifyRowData(index, oldrowData);
                            return false;
                        } else {
                            return buildSelectedRowDataAndSave();
                        }
                    });
                } else {
                    return buildSelectedRowDataAndSave();
                }
            } else if(res && res.message) {
                $common.showError("系统自动创建套餐失败！原因：" + res.message);
            }
        } else {
            return buildSelectedRowDataAndSave();
        }

        function buildSelectedRowDataAndSave() {
            selectedRowData.ptypeId = data.id;
            selectedRowData.ptypeName = data.fullname;
            selectedRowData.ptypeCode = data.usercode;
            selectedRowData.taxRate = data.taxRate;
            if (data.ptype && data.ptype.pcategory == 2) {
                selectedRowData.ptypeId = data.ptype.id;
                selectedRowData.ptypeName = data.ptype.fullname;
                selectedRowData.ptypeCode = data.ptype.usercode;
                selectedRowData.taxRate = data.ptype.taxRate;
                if (!data.ptypeCombo) {
                    _this.buildtypeComboInfo(data);
                }
            }
            if (data.ptypeCombo) {
                var comboSkuInfo = _this.getComboInfo(data);
                selectedRowData.mappingMark = 2;
                selectedRowData.unit = 0;
                selectedRowData.unitName = "";
                selectedRowData.baseUnitName = "";
                selectedRowData.xcode = comboSkuInfo == null ? "" : comboSkuInfo.xcode;
                selectedRowData.usercode = data.usercode;
                selectedRowData.prop = "";
                selectedRowData.localProperties = "";
                selectedRowData.localPropertiesName = "";
                selectedRowData.ptypeId = comboSkuInfo == null ? selectedRowData.ptypeId : comboSkuInfo.ptypeId;
                selectedRowData.skuId = comboSkuInfo == null ? "" : comboSkuInfo.skuId;
                selectedRowData.unitId = comboSkuInfo == null ? "" : comboSkuInfo.unitId;
                selectedRowData.barcode = data.barcode;
                selectedRowData.saleType = data.ptypeCombo.saleType;
            } else {
                var unit = data.unit;
                selectedRowData.mappingMark = 1;
                selectedRowData.unit = unit.id;
                selectedRowData.unitId = unit.id;
                selectedRowData.unitName = unit.unitName;
                selectedRowData.unitRate = unit.unitRate;
                selectedRowData.xcode = data.xcode;
                selectedRowData.usercode = data.usercode;
                selectedRowData.prop = data.prop;
                selectedRowData.localProperties = data.propValues;
                selectedRowData.localPropertiesName = data.propFormat;
                if (data.sku) {
                    selectedRowData.skuId = data.sku.id;
                    selectedRowData.fullbarcode = data.fullbarcode; // 如果网店商品对应以后有选套餐的情况需要让jxc在sku里加上fullbarcode字段
                } else {
                    selectedRowData.ptypeName = '';
                    selectedRowData.ptypeCode = '';
                    $common.showError("选择有属性商品进行临时对应时，必须选择sku商品");
                    return;
                }
                if (!selectedRowData.ptypeId) {
                    selectedRowData.ptypeId = data.ptypeId
                    selectedRowData.usercode = data.usercode
                }
                $common.setCookie("selectType", "Sku");
            }
            if (!selectedRowData) {
                return;
            }
            _this.doSaveRelation(_this, selectedRowData)
        }

    },

    buildtypeComboInfo: function (data) {
        var ptypeCombo = new Object();
        ptypeCombo.saleType = 0;
        selectedRowData.ptypeCombo = ptypeCombo;
    },

    doShowConfig: function(sender) {
        var panel = this.get_form().leftPanel;
        panel.showConfig(panel);  // 自定义设置按钮，启动默认编辑模式（拖动模式）
        panel.showPopupConfig(panel, { mode: 'right' }); // 点设置的时候，面板显示到指定元素的右侧（启动菜单模式）
    },

    doSaveRelation: function (sender, rowData) {
        var form = sender.get_form();
        var grid = form.details;
        var bindItems = [];
        rowData.eshopId = rowData.otypeId;
        bindItems.push(rowData);
        var that = this;
        if (!bindItems || bindItems.length === 0) {
            return;
        } else {
            var frm = new Sys.UI.Form(sender);
            frm.showModal("sale/eshoporder/eshopsaleorder/ManualRelationProcess.gspx", {bindItems: bindItems});
            frm.add_close(function () {
                that.partialRefresh(sender);
                return;
            });
        }
    },

    getComboInfo: function (data) {
        if (data.xcodes && data.xcodes.length > 0) {
            for (var i = 0; i < data.xcodes.length; i++) {
                var xcode = data.xcodes[i];
                if (xcode.xcode && xcode.defaulted == true) {
                    return xcode;
                }
            }
            return null;
        }
        return null;
    },

    doChangePtypeSelectType: function (sender) {
        var value = sender.get_value();
        var form = sender.get_form();
        if (value == 0) {
            form.ptypeFullnameForModify.set_visible(true);
            form.ptypeLabel.set_visible(false);
            form.ptypeClass.set_visible(false);
            return;
        }
        if (value == 1) {
            form.ptypeFullnameForModify.set_visible(false);
            form.ptypeLabel.set_visible(true);
            form.ptypeClass.set_visible(false);
            return;
        }
        if (value == 2) {
            form.ptypeFullnameForModify.set_visible(false);
            form.ptypeLabel.set_visible(false);
            form.ptypeClass.set_visible(true);
            return;
        }
    },

    openPtypeClassSelect: function (sender) {
        sender.set_selectorPage("/jxc/baseinfo/selector/ClassSelector.gspx");
        sender.set_selectorPageParams({
            notUpdateParentCheck: true,
            addEnabled: false,
            basicName: 'Ptype',
            multiselect: true,
            tipText: "商品分类"
        });
    },

    selectPtypeClass: function (sender, args) {
        var selectForm = args.get_form();
        var classes = selectForm.selectedDataList;
        var ids = [];
        var names = [];
        for (var i = 0; i < classes.length; i++) {
            if (classes[i].id == 0 || classes[i].typeid == "00000") {
                continue;
            }
            names.push(classes[i].fullname);
            ids.push(classes[i].typeid);
        }
        var form = sender.get_form();
        var ptypeClass = form.ptypeClass;
        ptypeClass.set_value({ptypeClassIds: ids, classNames: names.join(",")});
    },

    selectUpdatePtypeClass: function (sender, args) {
        var selectForm = args.get_form();
        var classes = selectForm.selectedDataList;
        var ids = [];
        var names = [];
        for (var i = 0; i < classes.length; i++) {
            if (classes[i].id == 0 || classes[i].typeid == "00000") {
                continue;
            }
            names.push(classes[i].fullname);
            ids.push(classes[i].typeid);
        }
        var form = sender.get_form();
        var updatePtypeClass = form.updatePtypeClass;
        updatePtypeClass.set_value({ptypeClassIds: ids, classNames: names.join(",")});
    },

    doChangeUpdatePtypeSelectType: function (sender) {
        var value = sender.get_value();
        var form = sender.get_form();
        if (value == 0) {
            form.updatePtypeFullnameForModify.set_visible(true);
            form.updatePtypeLabel.set_visible(false);
            form.updatePtypeClass.set_visible(false);
            return;
        }
        if (value == 1) {
            form.updatePtypeFullnameForModify.set_visible(false);
            form.updatePtypeLabel.set_visible(true);
            form.updatePtypeClass.set_visible(false);
            return;
        }
        if (value == 2) {
            form.updatePtypeFullnameForModify.set_visible(false);
            form.updatePtypeLabel.set_visible(false);
            form.updatePtypeClass.set_visible(true);
            return;
        }
    },


    buildtypeComboInfo: function (data) {
        var ptypeCombo = new Object();
        ptypeCombo.saleType = 0;
        selectedRowData.ptypeCombo = ptypeCombo;
    },

    doSaveRelation: function (sender, rowData) {
        var form = sender.get_form();
        var grid = form.details;
        var bindItems = [];
        rowData.eshopId = rowData.otypeId;
        bindItems.push(rowData);
        var that = this;
        if (!bindItems || bindItems.length === 0) {
            return;
        } else {
            var frm = new Sys.UI.Form(sender);
            frm.showModal("sale/eshoporder/eshopsaleorder/ManualRelationProcess.gspx", {bindItems: bindItems});
            frm.add_close(function () {
                that.partialRefresh(sender);
                return;
            });
        }
    },

    getComboInfo: function (data) {
        if (data.xcodes && data.xcodes.length > 0) {
            for (var i = 0; i < data.xcodes.length; i++) {
                var xcode = data.xcodes[i];
                if (xcode.xcode && xcode.defaulted == true) {
                    return xcode;
                }
            }
            return null;
        }
        return null;
    },

    doChangePtypeSelectType: function (sender) {
        var value = sender.get_value();
        var form = sender.get_form();
        if (value == 0) {
            form.ptypeFullnameForModify.set_visible(true);
            form.ptypeLabel.set_visible(false);
            form.ptypeClass.set_visible(false);
            return;
        }
        if (value == 1) {
            form.ptypeFullnameForModify.set_visible(false);
            form.ptypeLabel.set_visible(true);
            form.ptypeClass.set_visible(false);
            return;
        }
        if (value == 2) {
            form.ptypeFullnameForModify.set_visible(false);
            form.ptypeLabel.set_visible(false);
            form.ptypeClass.set_visible(true);
            return;
        }
    },

    openPtypeClassSelect: function (sender) {
        sender.set_selectorPage("/jxc/baseinfo/selector/ClassSelector.gspx");
        sender.set_selectorPageParams({
            notUpdateParentCheck: true,
            addEnabled: false,
            basicName: 'Ptype',
            multiselect: true
        });
    },

    selectPtypeClass: function (sender, args) {
        var selectForm = args.get_form();
        var classes = selectForm.selectedDataList;
        var ids = [];
        var names = [];
        for (var i = 0; i < classes.length; i++) {
            names.push(classes[i].fullname);
            ids.push(classes[i].typeid);
        }
        var form = sender.get_form();
        var ptypeClass = form.ptypeClass;
        ptypeClass.set_value({ptypeClassIds: ids, classNames: names.join(",")});
    },

    doChangeUpdatePtypeSelectType: function (sender) {
        var value = sender.get_value();
        var form = sender.get_form();
        if (value == 0) {
            form.updatePtypeFullnameForModify.set_visible(true);
            form.updatePtypeLabel.set_visible(false);
            form.updatePtypeClass.set_visible(false);
            return;
        }
        if (value == 1) {
            form.updatePtypeFullnameForModify.set_visible(false);
            form.updatePtypeLabel.set_visible(true);
            form.updatePtypeClass.set_visible(false);
            return;
        }
        if (value == 2) {
            form.updatePtypeFullnameForModify.set_visible(false);
            form.updatePtypeLabel.set_visible(false);
            form.updatePtypeClass.set_visible(true);
            return;
        }
    },

    JdQueryChange: function (sender) {
        var form = sender.get_form();
        var contanisJdQuery = form.contanisJdQuery.get_value();
        localStorage.setItem("contanisJdQuery", contanisJdQuery);
    },

    doOrderGather: function (sender) {
        var form = this.get_form();
        var selectedItems = form.c_grid_Audit.get_selectedItems();
        if (selectedItems == null || selectedItems.length == 0) {
            $common.showInfo("请先选择需要收款的订单");
            return;
        }
        var otypeId, btypeId;
        var gatherTotal = 0, orderCount = 0;
        var tipMsg = new Array();
        var orderInfo =new Array();
        for (var i = 0; i < selectedItems.length; i++) {
            if (i == 0)
            {
                otypeId =selectedItems[i].otypeId;
                btypeId =selectedItems[i].btypeId;
            }else if(selectedItems[i].otypeId != otypeId){
                $common.showError("每次只能对同一个网店进行收款");
                return;
            } else if(selectedItems[i].btypeId != btypeId){
                $common.showError("每次只能对同一个往来单位进行收款");
                return;
            }
            if (selectedItems[i].extend.gatherStatus==1) {
                var msg = new Object();
                msg.tradeOrderId = selectedItems[i].tradeOrderId;
                msg.msg = "已完成收款的订单不允许收款";
                tipMsg.push(msg);
                continue;
            }
            if (selectedItems[i].deleted) {
                var msg = new Object();
                msg.tradeOrderId = selectedItems[i].tradeOrderId;
                msg.msg = "删除的订单不允许收款";
                tipMsg.push(msg);
                continue;
            }
            if (selectedItems[i].btypeId == 0) {
                var msg = new Object();
                msg.tradeOrderId = selectedItems[i].tradeOrderId;
                msg.msg = "订单没有往来单位不允许收款";
                tipMsg.push(msg);
                continue;
            }
            var itemOrder = this._doBuildItemGatherOrder(selectedItems[i]);
            if (itemOrder.gatherTotal <= 0) {
                var msg = new Object();
                msg.tradeOrderId = selectedItems[i].tradeOrderId;
                msg.msg = "剩余收款金额小于等于0，不允许收款 ";
                tipMsg.push(msg);
                continue;
            }
            if (selectedItems[i].localTradeState == 1) {
                var msg = new Object();
                msg.tradeOrderId = selectedItems[i].tradeOrderId;
                msg.msg = "未付款的订单";
                tipMsg.push(msg);
            }
            if (selectedItems[i].localTradeState == 5) {
                var msg = new Object();
                msg.tradeOrderId = selectedItems[i].tradeOrderId;
                msg.msg = "交易关闭的订单";
                tipMsg.push(msg);
            }
            gatherTotal +=itemOrder.gatherTotal;
            orderCount++;
            orderInfo.push(itemOrder);
        }
        var _this = this;
        var gatherParams = new Object();
        gatherParams.orderInfo = orderInfo;
        gatherParams.btypeId = btypeId;
        gatherParams.otypeId = otypeId;
        gatherParams.gatherTotal = gatherTotal;
        gatherParams.orderCount = orderCount;
        var atypeInfo = this._getEshopAtypeInfo(otypeId);
        if (atypeInfo){
            gatherParams.atypeId =atypeInfo.id;
            gatherParams.atypeTypeId =atypeInfo.typeId;
            gatherParams.atypeName =atypeInfo.fullname;
        }
        if (tipMsg.length != 0) {
            var cancontinue = (orderInfo.length == 0) ? false : true;
            var errorForm = new Sys.UI.Form(sender);
            errorForm.add_ok(function (arg) {
                if (arg.ok) {
                    _this.doShowGatherPage(_this, sender, gatherParams);
                }
            });
            var pageParam = {
                errorData: tipMsg
            };
            errorForm.showModal("sale/eshoporder/eshopsaleorder/AuditFilterInfo.gspx?cancontinue=" + cancontinue, pageParam);
        } else {
            _this.doShowGatherPage(_this, sender, gatherParams);
        }
    },
    _doBuildItemGatherOrder: function (order) {
        var itemOrder = new Object();
        itemOrder.id = order.id;
        itemOrder.tradeOrderId = order.tradeOrderId;
        itemOrder.otypeId = order.otypeId;
        itemOrder.btypeId = order.btypeId;
        itemOrder.nationalSubsidyTotal=order.extend.nationalSubsidyTotal?order.extend.nationalSubsidyTotal:0;
        itemOrder.disedTaxedTotal=order.disedTaxedTotal;
        itemOrder.fullname=order.otypeName;
        itemOrder.btypeName=order.btypeName;
        itemOrder.orderBuyerFreightFee=order.orderBuyerFreightFee?order.orderBuyerFreightFee:0;
        itemOrder.ptypeServiceFee=order.ptypeServiceFee?order.ptypeServiceFee:0;
        itemOrder.localTradeState=order.localTradeState;
        itemOrder.checkStatus= order.checkStatus?order.checkStatus:0  ;
        itemOrder.gatheredTotal=order.extend.gatheredTotal?order.extend.gatheredTotal:0;
        itemOrder.gatherTotal = itemOrder.disedTaxedTotal + itemOrder.orderBuyerFreightFee + itemOrder.ptypeServiceFee -
            itemOrder.gatheredTotal;
        return itemOrder;
    },
    _getEshopAtypeInfo: function (otypeId) {
        var response = $common.ajaxSync({
            url: "/sale/eshoporder/eshop/getEshopAtype/" + otypeId,
            data: null,
            type: 'get',
            router: 'ngp'
        });
        return response.data;
    },

    doShowGatherPage: function (_this, sender, pageparams) {
        var popForm = new Sys.UI.Form(sender);
        popForm.add_closed(function () {
            _this.partialRefresh(sender);
        });
        popForm.showMDI("sale/eshoporder/eshopplatformcheck/OrderGatherInfo.gspx",pageparams);
    },

    doImportGather: function (sender) {
        var popForm = new Sys.UI.Form(sender, true);
        popForm.showModal("sale/eshoporder/eshopplatformcheck/GatherOrderImport.gspx");
    },

};
sale.eshoporder.eshopsaleorder.EShopSaleOrderListNewAction.registerClass('sale.eshoporder.eshopsaleorder.EShopSaleOrderListNewAction', Sys.UI.PageAction);

function EshopSaleOrderExceptionStatusTimer() {
    // incrementPageCount时，pageCount = 0初始化定时器；decrementPageCount，pageCount = 0删除定时器
    this.pageCount = 0;
    this.timer = null;
    this.task = [];

    this.incrementPageCount = function (eventName, _this) {
        this.task.push({eventName: eventName, param: _this});
        if (this.pageCount++ === 0 && this.timer == null) {
            var that = this;
            this.timer = setInterval(function () {
                for (var i = 0; i < that.task.length; i++) {
                    var param = {
                        sourceType: "ESHOP_ORDER",
                        usePhase: "ORDER_QUERY"
                    }
                    that.refreshOnce(that.task[i].eventName, param);
                }
            }, 20000);
        }
    }

    this.decrementPageCount = function (eventName, notifyId) {
        $notify.remove(notifyId);
        for (var i = 0; i < this.task.length; i++) {
            if (this.task[i].eventName === eventName) {
                this.task.splice(i, 1);
            }
        }
        if (--this.pageCount === 0 && this.timer != null) {
            clearInterval(this.timer);
            $notify.remove(eventName);
        }
    }

    this.refreshOnce = function (eventName, param) {
        try {
            // 异步执行，不影响订单查询
            $saleJarvisUtils.postNoMsg('sale/eshoporder/exceptionStatus/countExceptionStatus', param, function (res) {
                var countExceptionMap = new Map();
                if (res && res.code == '200' && res.data) {
                    for (var key in res.data) {
                        countExceptionMap.set(key, res.data[key]);
                    }
                    $notify.emit(eventName, countExceptionMap);
                } else {
                    $notify.emit(eventName, null);
                }
            });
        } catch (e) {
            console.log('徽标error');
        }
    }
}
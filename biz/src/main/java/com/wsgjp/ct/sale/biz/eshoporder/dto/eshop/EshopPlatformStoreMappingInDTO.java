package com.wsgjp.ct.sale.biz.eshoporder.dto.eshop;


import com.wsgjp.ct.sale.biz.eshoporder.entity.params.BaseQuery;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/30 15:55
 */
public class EshopPlatformStoreMappingInDTO extends BaseQuery {
    /**
     * 网店id
     */
    private BigInteger eshopId;


    /**
     * id
     */
    private BigInteger id;
    /**
     * 线上仓库/门店id|平台仓库id
     */
    private String platformStoreStockId;
    /**
     * 本地仓库id
     */
    private BigInteger ktypeId;

    /**
     * 门店/网点名称|平台仓库名称
     */
    private String platformStoreName;
    /**
     * 门店/网点地址|平台仓库地址
     */
    private String platformStoreAddress;
    /**
     * 门店/网点配送类型|平台仓库类型
     */
    private Integer platformStoreType;
    /**
     * 业务标签
     */
    private String businessLabel;
    /**
     * 对应关系（0为对应1已对应2全部）
     */
    private Integer correspondFlag;
    /**
     * 删除标识（1为已删除）
     */
    private boolean deleted;

    /**
     * 区分门店/网点|平台类型
     */
    private Integer type;
    /**
     * 网店名称
     */
    private String otypeName;
    /**
     * 本地仓库编码
     */
    private String btypeUserCode;
    /**
     * 本地仓库地址
     */
    private String btypeAddress;

    /**
     * 自定义筛选列查询
     */
    private Integer platformStoreTypeFilter;

    private BigInteger otypeFilter;

    private String storeIdFilter;

    private String storeNameFilter;

    private String storeAddressFilter;

    private int storeFlagFilter = 2;

    private BigInteger ktypeFilter;

    private List<BigInteger> otypeIds;

    private String quickfilter;

    private boolean platformStoreNew;


    public boolean isPlatformStoreNew() {
        return platformStoreNew;
    }

    public void setPlatformStoreNew(boolean platformStoreNew) {
        this.platformStoreNew = platformStoreNew;
    }

    public BigInteger getEshopId() {
        return eshopId;
    }

    public void setEshopId(BigInteger eshopId) {
        this.eshopId = eshopId;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getPlatformStoreStockId() {
        return platformStoreStockId;
    }

    public void setPlatformStoreStockId(String platformStoreStockId) {
        this.platformStoreStockId = platformStoreStockId;
    }

    public BigInteger getKtypeId() {
        return ktypeId;
    }

    public void setKtypeId(BigInteger ktypeId) {
        this.ktypeId = ktypeId;
    }

    public String getPlatformStoreName() {
        return platformStoreName;
    }

    public void setPlatformStoreName(String platformStoreName) {
        this.platformStoreName = platformStoreName;
    }

    public String getPlatformStoreAddress() {
        return platformStoreAddress;
    }

    public void setPlatformStoreAddress(String platformStoreAddress) {
        this.platformStoreAddress = platformStoreAddress;
    }

    public Integer getPlatformStoreType() {
        return platformStoreType;
    }

    public void setPlatformStoreType(Integer platformStoreType) {
        this.platformStoreType = platformStoreType;
    }

    public String getBusinessLabel() {
        return businessLabel;
    }

    public void setBusinessLabel(String businessLabel) {
        this.businessLabel = businessLabel;
    }

    public Integer getCorrespondFlag() {
        return correspondFlag;
    }

    public void setCorrespondFlag(Integer correspondFlag) {
        this.correspondFlag = correspondFlag;
    }

    public boolean isDeleted() {
        return deleted;
    }

    public void setDeleted(boolean deleted) {
        this.deleted = deleted;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getOtypeName() {
        return otypeName;
    }

    public void setOtypeName(String otypeName) {
        this.otypeName = otypeName;
    }

    public String getBtypeUserCode() {
        return btypeUserCode;
    }

    public void setBtypeUserCode(String btypeUserCode) {
        this.btypeUserCode = btypeUserCode;
    }

    public String getBtypeAddress() {
        return btypeAddress;
    }

    public void setBtypeAddress(String btypeAddress) {
        this.btypeAddress = btypeAddress;
    }

    public Integer getPlatformStoreTypeFilter() {
        return platformStoreTypeFilter;
    }

    public void setPlatformStoreTypeFilter(Integer platformStoreTypeFilter) {
        this.platformStoreTypeFilter = platformStoreTypeFilter;
    }

    public BigInteger getOtypeFilter() {
        return otypeFilter;
    }

    public void setOtypeFilter(BigInteger otypeFilter) {
        this.otypeFilter = otypeFilter;
    }

    public int getStoreFlagFilter() {
        return storeFlagFilter;
    }

    public void setStoreFlagFilter(int storeFlagFilter) {
        this.storeFlagFilter = storeFlagFilter;
    }

    public BigInteger getKtypeFilter() {
        return ktypeFilter;
    }

    public void setKtypeFilter(BigInteger ktypeFilter) {
        this.ktypeFilter = ktypeFilter;
    }

    public String getStoreAddressFilter() {
        return storeAddressFilter;
    }

    public void setStoreAddressFilter(String storeAddressFilter) {
        this.storeAddressFilter = storeAddressFilter;
    }

    public String getStoreNameFilter() {
        return storeNameFilter;
    }

    public void setStoreNameFilter(String storeNameFilter) {
        this.storeNameFilter = storeNameFilter;
    }

    public String getStoreIdFilter() {
        return storeIdFilter;
    }

    public void setStoreIdFilter(String storeIdFilter) {
        this.storeIdFilter = storeIdFilter;
    }

    public List<BigInteger> getOtypeIds() {
        return otypeIds;
    }

    public void setOtypeIds(List<BigInteger> otypeIds) {
        this.otypeIds = otypeIds;
    }

    public String getQuickfilter() {
        return quickfilter;
    }

    public void setQuickfilter(String quickfilter) {
        this.quickfilter = quickfilter;
    }
}

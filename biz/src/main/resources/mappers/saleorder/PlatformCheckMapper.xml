<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.PlatformCheckMapper">

    <select id="queryPaymentFlowAtypeMapList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.PaymentFlowAtypeMapEntity">
        select `id`, `business_type`, `business_name`, `atype_id`, `atype_fullname`, `atype_type_id`,
        `top_atype_usercode`,
        `top_atype_fullname`, `profile_id`, `in_out_type`, `create_time`, `update_time`,`reocrd_memo`
        from pl_eshop_payment_flow_atype_map
        where profile_id = #{profileId}
        <if test="businessType!=null and businessType!=''">
            and business_type=#{businessType}
        </if>
        <if test="atypeId!=0">
            and atype_id=#{atypeId}
        </if>
        and eshop_sale_platform=#{platformType}
    </select>

    <select id="queryPaymentFlowAtypeMap"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.PaymentFlowAtypeMapEntity">
        select `id`,
               `business_type`,
               `business_name`,
               `atype_id`,
               `atype_fullname`,
               `atype_type_id`,
               `top_atype_usercode`,
               `top_atype_fullname`,
               `profile_id`,
               `in_out_type`,
               `create_time`,
               `update_time`,
               `reocrd_memo`
        from pl_eshop_payment_flow_atype_map
        where profile_id = #{profileId} and eshop_sale_platform=#{shoptype}
          and business_name = #{businessName} limit 1
    </select>

    <insert id="insertPaymentFlowAtypeMapList">
        insert into pl_eshop_payment_flow_atype_map
        (`id`, `business_type`, `business_name`, `atype_id`, `atype_fullname`, `atype_type_id`, `top_atype_usercode`,
         `top_atype_fullname`, `profile_id`, `in_out_type`, `create_time`, `update_time`, `reocrd_memo`,
         `create_status`, `eshop_sale_platform`)
        values (#{id}, #{businessType}, #{businessName}, #{atypeId}, #{atypeFullname}, #{atypeTypeId},
                #{topAtypeUsercode},
                #{topAtypeFullname}, #{profileId}, #{inOutType}, #{createTime}, #{updateTime}, #{reocrdMemo},
                #{createStatus}, #{eshopPlatform})
    </insert>

    <insert id="insertFlowCondition">
        insert into pl_eshop_sync_flow_condition
        (id,
         profile_id,
         eshop_id,
         start_time,
         end_time,
         excute_status)
        values (#{id}, #{profileId}, #{eshopId}, #{startTime}, #{endTime}, #{excuteStatus})
    </insert>
    <update id="updateFlowCondition">
        update pl_eshop_sync_flow_condition
        set excute_status=#{excuteStatus}
        where profile_id = #{profileId}
          and id = #{id}

    </update>

    <select id="queryPaymentFlowAtypeMapByBusiness"
            resultType="java.lang.Integer">
        select count(*)
        from pl_eshop_payment_flow_atype_map
        where profile_id = #{profileId}
          and eshop_sale_platform = #{platform}
          and (business_name = #{reocrdMemo} or reocrd_memo = #{reocrdMemo})
    </select>
    <select id="queryMaxBusinessType"
            resultType="java.lang.Integer">
        select MAX(business_type)
        FROM pl_eshop_payment_flow_atype_map
        WHERE profile_id = #{profileId}
    </select>

    <select id="queryrepeat"
            resultType="java.lang.Integer">
        select count(*)
        from pl_eshop_payment_flow
        where profile_id = #{profileId}
          and payment_number = #{id}
          and business_name = #{checkType}
    </select>

    <select id="queryBillVchtype"
            resultType="java.lang.Integer">
        select vchtype
        from acc_bill_core
        where profile_id = #{profileId}
          and vchcode = #{vchcode}
        union
        select vchtype
        from td_bill_core
        where profile_id = #{profileId}
          and vchcode = #{vchcode}
    </select>

    <select id="queryBillNumber"
            resultType="java.math.BigInteger">
        select vchcode
        from acc_bill_core
        where profile_id = #{profileId}
          and bill_number = #{billNumber}
        union
        select vchcode
        from td_bill_core
        where profile_id = #{profileId}
          and bill_number = #{billNumber}
    </select>

    <select id="queryBillTotal"
            resultType="java.math.BigDecimal">
        select distinct billTotal
        from (
                 select bill_total as billTotal
              from acc_bill_core
              where profile_id = #{profileId}
                and vchcode = #{vchcode}
              union
                 select bill_total as billTotal
              from td_bill_core
              where profile_id = #{profileId}
                and vchcode = #{vchcode}) tmp
    </select>

    <select id="queryBillTotalIsOut"
            resultType="java.math.BigDecimal">
        select distinct billTotal
        from (
                 select if(vchtype in(2100,4002,4005,4014),-1*bill_total,bill_total) as billTotal
                 from acc_bill_core
                 where profile_id = #{profileId}
                   and vchcode = #{vchcode}
                 union
                 select if(vchtype in(2100,4002,4005,4014),-1*bill_total,bill_total) as billTotal
                 from td_bill_core
                 where profile_id = #{profileId}
                   and vchcode = #{vchcode})tmp
    </select>

    <select id="qeuryBtypeAccType"
            resultType="java.lang.Boolean">
        select acc_type
        from (select if(b.acc_type = 0, true, false) as acc_type
              from acc_bill_core c
                       left join base_btype b on b.id = c.btype_id and b.profile_id = c.profile_id
              where c.profile_id = #{profileId}
                and c.vchtype = 2200
                and c.vchcode = #{vchcode}
              union
              select if(b.acc_type = 0, true, false) as acc_type
              from td_bill_core c
                       left join base_btype b on b.id = c.btype_id and b.profile_id = c.profile_id
              where c.profile_id = #{profileId}
                and c.vchtype = 2200
                and c.vchcode = #{vchcode}) tmp limit 1
    </select>

    <select id="queryAccBillTotalByVchcodes"
            resultType="java.math.BigDecimal">
        select billTotal
        from (
        select if(vchtype in(2100,4002,4005,4014),-1*bill_total,bill_total) as billTotal
        from acc_bill_core
        where profile_id = #{profileId} and vchcode in
        <foreach collection="vchcodes" item="vchcode" close=")" open="(" separator=",">
            #{vchcode}
        </foreach>
        )tmp
    </select>

    <select id="queryTdBillTotalByVchcodes"
            resultType="java.math.BigDecimal">
        select billTotal
        from (
        select if(vchtype in(2100,4002,4005,4014),-1*bill_total,bill_total) as billTotal
        from td_bill_core
        where profile_id = #{profileId} and vchcode in
        <foreach collection="vchcodes" item="vchcode" close=")" open="(" separator=",">
            #{vchcode}
        </foreach>
        )tmp
    </select>

    <select id="queryrepeatByBusinessType"
            resultType="java.lang.Integer">
        select count(*)
        from pl_eshop_payment_flow
        where profile_id = #{profileId}
          and payment_number = #{id}
          and business_type = #{businessType}
    </select>

    <select id="queryrepeatByflow"
            resultType="java.lang.Integer">
        select count(*)
        from pl_eshop_payment_flow
        where profile_id = #{profileId}
          and payment_number = #{id}
    </select>

    <select id="queryAtypeIdByPaymentNumber"
            resultType="java.math.BigInteger">
        select atype_id
        from pl_eshop_payment_flow
        where profile_id = #{profileId}
          and payment_number = #{id}
          and merchant_order_number = #{orderNumber} limit 1
    </select>

    <select id="getFinanceAlipayRecordUniqueMarks" resultType="java.lang.String">
        select `unique_mark`
        from pl_eshop_payment_flow
        where profile_id = #{profileId}
        AND otype_id = #{otypeId}
        <if test="uniqueMarks != null and uniqueMarks.size()>0">
            and unique_mark in
            <foreach collection="uniqueMarks" item="mark" separator="," index="i" close=")" open="(">
                #{mark}
            </foreach>
        </if>
    </select>

    <insert id="insertEshopPaymentFlow">
        INSERT INTO pl_eshop_payment_flow
        (`id`, `payment_number`, `merchant_order_number`, `merchant_payment_account`, `opposite_payment_account`,
         `platform_business_type`, `platform_business_sub_type`,
         `business_type`, `business_name`, `trade_order_id`, `in_amount`, `out_amount`,
         `balance_total`, `payment_remark`, `atype_id`, `create_time`, `download_time`,
         `update_time`, `bill_vchcode`, `bill_vchtype`, `otype_id`, `profile_id`,
         `eshop_order_id`, `unique_mark`, `create_type`, `process_state`, `etype_id`, `bill_number`,
         `vchcode`, `audit_time`, `annex_url`)
        VALUES (#{id}, #{paymentNumber}, #{merchantOrderNumber}, #{merchantPaymentAccount}, #{oppositePaymentAccount},
                #{platformBusinessType}, #{platformBusinessSubType},
                #{businessType}, #{businessName}, #{tradeOrderId}, #{inAmount}, #{outAmount},
                #{balanceTotal}, #{paymentRemark}, #{atypeId}, #{createTime}, #{downloadTime},
                #{updateTime}, #{billVchcode}, #{billVchtype}, #{otypeId}, #{profileId},
                #{eshopOrderId}, #{uniqueMark}, #{createType}, #{processState},
                #{etypeId}, #{billNumber}, #{vchcode}, #{auditTime}, #{annexUrl})
    </insert>


    <insert id="batchInsertEshopPaymentFlow">
        INSERT ignore INTO pl_eshop_payment_flow
        (`id`, `payment_number`, `merchant_order_number`, `merchant_payment_account`, `opposite_payment_account`,
        `platform_business_type`, `platform_business_sub_type`,
        `business_type`, `business_name`, `trade_order_id`, `in_amount`, `out_amount`,
        `balance_total`, `payment_remark`, `atype_id`, `create_time`, `download_time`,
        `update_time`, `bill_vchcode`, `bill_vchtype`, `otype_id`, `profile_id`,
        `eshop_order_id`, `unique_mark`, `create_type`, `process_state`,`etype_id`,`bill_number`,
        `vchcode`,`audit_time`,`annex_url`,`source_type`,`payment_mode`,payway_id,payment_name,entry_state,`btype_id`,in_out_type,bill_check_time,flow_create_time,pay_platform,user_pay_way,origin_payment_number)
        VALUES
        <foreach collection="flowList" item="item" separator=",">
            (#{item.id}, #{item.paymentNumber}, #{item.merchantOrderNumber}, #{item.merchantPaymentAccount},
            #{item.oppositePaymentAccount},
            #{item.platformBusinessType}, #{item.platformBusinessSubType},
            #{item.businessType}, #{item.businessName}, #{item.tradeOrderId}, #{item.inAmount}, #{item.outAmount},
            #{item.balanceTotal}, #{item.paymentRemark}, #{item.atypeId}, #{item.createTime}, #{item.downloadTime},
            #{item.updateTime}, #{item.billVchcode}, #{item.billVchtype}, #{item.otypeId}, #{item.profileId},
            #{item.eshopOrderId}, #{item.uniqueMark}, #{item.createType}, #{item.processState},
            #{item.etypeId}, #{item.billNumber}, #{item.vchcode}, #{item.auditTime}, #{item.annexUrl},#{item.sourceType},#{item.paymentMode},#{item.paywayId},#{item.paymentName},#{item.entryState},#{item.btypeId},#{item.inOutType},#{item.billCheckTime},#{item.flowCreateTime},
            #{item.payPlatform},#{item.userPayWay},#{item.originPaymentNumber})
        </foreach>
    </insert>


    <insert id="batchInsertEshopPaymentFlowByIgnore">
        REPLACE INTO pl_eshop_payment_flow
        (`id`, `payment_number`, `merchant_order_number`, `merchant_payment_account`, `opposite_payment_account`,
        `platform_business_type`, `platform_business_sub_type`,
        `business_type`, `business_name`, `trade_order_id`, `in_amount`, `out_amount`,
        `balance_total`, `payment_remark`, `atype_id`, `create_time`, `download_time`,
        `update_time`, `bill_vchcode`, `bill_vchtype`, `otype_id`, `profile_id`,
        `eshop_order_id`, `unique_mark`, `create_type`, `process_state`,`etype_id`,`bill_number`,
        `vchcode`,`audit_time`,`annex_url`,`source_type`,`payment_mode`,payway_id,payment_name,entry_state,`btype_id`,`in_out_type`,bill_check_time,flow_create_time)
        VALUES
        <foreach collection="flowList" item="item" separator=",">
            (#{item.id}, #{item.paymentNumber}, #{item.merchantOrderNumber}, #{item.merchantPaymentAccount},
            #{item.oppositePaymentAccount},
            #{item.platformBusinessType}, #{item.platformBusinessSubType},
            #{item.businessType}, #{item.businessName}, #{item.tradeOrderId}, #{item.inAmount}, #{item.outAmount},
            #{item.balanceTotal}, #{item.paymentRemark}, #{item.atypeId}, #{item.createTime}, #{item.downloadTime},
            #{item.updateTime}, #{item.billVchcode}, #{item.billVchtype}, #{item.otypeId}, #{item.profileId},
            #{item.eshopOrderId}, #{item.uniqueMark}, #{item.createType}, #{item.processState},
            #{item.etypeId}, #{item.billNumber}, #{item.vchcode}, #{item.auditTime}, #{item.annexUrl},#{item.sourceType},#{item.paymentMode},#{item.paywayId},#{item.paymentName},#{item.entryState},#{item.btypeId},#{item.inOutType},#{item.billCheckTime },#{item.flowCreateTime})
        </foreach>
    </insert>


    <select id="queryEshopSaleorderMapList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity">
        SELECT `id`,`trade_order_id`,`otype_id`,`profile_id`,`platform_trade_state`,`order_source_type`,`pay_no`,
        `dised_taxed_total`,`total`,`ptype_service_fee`,`order_buyer_freight_fee`
        FROM `pl_eshop_sale_order`
        WHERE profile_id = #{profileId}
        AND otype_id = #{otypeId}
        AND order_sale_type  not in(3,6)
        <if test="tradeOrderIds != null and tradeOrderIds.size()>0">
            and trade_order_id in
            <foreach collection="tradeOrderIds" item="tradeOrderId" separator="," index="i" close=")" open="(">
                #{tradeOrderId}
            </foreach>
        </if>
        <if test="payNo != null">
            and pay_no = #{payNo}
        </if>
    </select>

    <select id="queryEshopSaleorderDetailList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderDetail">
        SELECT *
        FROM `pl_eshop_sale_order_detail`
        WHERE profile_id = #{profileId}
          AND id = #{id}
    </select>

    <update id="updateEshopPaymentFlow">
        update pl_eshop_payment_flow
        SET `eshop_order_id` =#{eshopOrderId},
            `trade_order_id` =#{tradeOrderId},
            `process_state`  =#{processState},
            `etype_id`       =#{etypeId},
            `audit_time`     =#{auditTime},
            `business_type`=#{businessType},
            `business_name`=#{businessName}
        where profile_id = #{profileId}
          and otype_id = #{otypeId}
          and unique_mark = #{uniqueMark}
    </update>

    <select id="queryAccounts"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.PlatformCheckAccountsRequest"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.PlatformCheckAccountsPageData">
        SELECT 0 as selected,
        flow.`id`, flow.`payment_number`, flow.`merchant_order_number`, flow.`merchant_payment_account`,
        flow.`opposite_payment_account`,flow.`platform_business_type`, flow.`platform_business_sub_type`,
        flow.`business_type`, flow.`business_name`, flow.`in_amount`,flow.`out_amount`,
        flow.`balance_total`, flow.`payment_remark`, flow.`create_time`, flow.`download_time`,flow.`update_time`,
        flow.`otype_id`, flow.`profile_id`,o.`id` as eshop_order_id, o.trade_order_id,
        flow.`unique_mark`,flow.`process_state`,flow.`create_type`,flow.`audit_time`,flow.`annex_url`,flow.`etype_id`,
        IFNULL(a.`id`,fa.id) AS atypeId, IFNULL(a.fullname,fa.fullname) AS atypeFullname,
        IFNULL(a.usercode,fa.usercode) AS atypeTypeId,
        map.`top_atype_usercode`,map.`top_atype_fullname`,map.`in_out_type`,
        ifnull(e.fullname,'') as eshopFullname,etype.fullname as 'auditName',
        ifnull(acc.vchcode,bill.vchcode) as billVchcode , ifnull(acc.vchtype,bill.vchtype) as billVchtype,
        if(ifnull(acc.vchcode,bill.vchcode) is null,0,1) as billStatus,
        ifnull(acc.bill_number,bill.bill_number) as billNumber, ifnull(acc.create_time,bill.create_time) as
        billCreateTime,ot.atype_id as otypeAtypeid
        FROM pl_eshop_payment_flow flow
        LEFT JOIN pl_eshop_sale_order o on o.profile_id= flow.profile_id and o.id = flow.eshop_order_id
        LEFT JOIN pl_eshop e ON e.profile_id = flow.profile_id AND e.otype_id = flow.otype_id
        LEFT JOIN base_otype ot ON ot.profile_id = flow.profile_id AND ot.id = flow.otype_id
        LEFT JOIN pl_eshop_payment_flow_atype_map map ON map.profile_id = flow.profile_id
        AND map.business_type = flow.business_type and e.eshop_type = map.eshop_sale_platform
        LEFT JOIN base_atype a ON map.profile_id = a.profile_id AND map.atype_id = a.id AND a.stoped = 0 And a.deleted=0
        LEFT JOIN base_atype fa ON flow.profile_id = fa.profile_id AND flow.atype_id = fa.id AND fa.stoped = 0 And
        fa.deleted=0
        LEFT JOIN acc_bill_core acc ON acc.profile_id = flow.profile_id AND acc.vchcode = flow.bill_vchcode AND
        acc.deleted =0
        LEFT JOIN td_bill_core bill ON bill.profile_id = flow.profile_id AND bill.vchcode = flow.bill_vchcode AND
        bill.deleted =0
        LEFT JOIN base_etype etype ON flow.profile_id = etype.profile_id AND flow.etype_id = etype.id
        <if test="otypeLimited">
            inner join base_limit_scope bls on bls.profile_id=flow.profile_id and object_type=3 and
            flow.otype_id=bls.object_id and bls.etype_id=#{etypeId}
        </if>
        WHERE flow.profile_id=#{profileId} and flow.create_time BETWEEN #{beginTime} AND #{endTime} and
        flow.source_type=0
        <if test="filterKeyType== 1 and   keyWordList != null and keyWordList.size()>0">
            AND flow.payment_number IN
            <foreach collection="keyWordList" close=")" open="(" separator="," item="keyWord">
                #{keyWord}
            </foreach>
        </if>
        <if test="createType != null and createType!=-1">
            and flow.create_type = #{createType}
        </if>
        <if test="filterKeyType== 2 and   keyWordList != null and keyWordList.size()>0">
            AND o.trade_order_id IN
            <foreach collection="keyWordList" close=")" open="(" separator="," item="keyWord">
                #{keyWord}
            </foreach>
        </if>
        <if test="filterKeyType== 3 and   keyWordList != null and keyWordList.size()>0">
            AND (bill.bill_number IN
            <foreach collection="keyWordList" close=")" open="(" separator="," item="keyWord">
                #{keyWord}
            </foreach>
            or acc.bill_number in
            <foreach collection="keyWordList" close=")" open="(" separator="," item="keyWord">
                #{keyWord}
            </foreach>)
        </if>
        <if test="platformAccountType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.PlatfromAccountType@TaoBao">
            AND e.eshop_sale_platform in (0,1,3,4)
        </if>
        <if test="platformAccountType==@com.wsgjp.ct.sale.biz.eshoporder.entity.enums.PlatfromAccountType@Other">
            AND e.eshop_sale_platform not in (0,1,3,4)
        </if>
        <if test="otypeId!=null and otypeId.size()>0">
            AND flow.otype_id IN
            <foreach collection="otypeId" close=")" open="(" separator="," item="otype">
                #{otype}
            </foreach>
        </if>
        <if test="businessType!=null and businessType.size()>0">
            AND flow.business_type IN
            <foreach collection="businessType" close=")" open="(" separator="," item="type">
                #{type}
            </foreach>
        </if>
        <if test="billStatus != null and billStatus == 0 ">
            AND (bill.vchcode is null and acc.vchcode is null)
        </if>
        <if test="billStatus != null and billStatus == 1 ">
            AND (bill.vchcode > 0 or acc.vchcode > 0)
        </if>
        <if test="fieldEshopFullname!=null and fieldEshopFullname!=''">
            and e.fullname like CONCAT('%',#{fieldEshopFullname},'%')
        </if>
        <if test="fieldPaymentNumber!=null and fieldPaymentNumber!=''">
            and flow.`payment_number` like CONCAT('%',#{fieldPaymentNumber},'%')
        </if>
        <if test="fieldTradeId!=null and fieldTradeId!=''">
            and o.`trade_order_id` like CONCAT('%',#{fieldTradeId},'%')
        </if>
        <if test="billBeginTime!=null">
            and  ifnull(acc.create_time,bill.create_time) &gt;= #{billBeginTime}
        </if>
        <if test="billEndTime!=null  ">
            and ifnull(acc.create_time,bill.create_time) &lt;= #{billEndTime}
        </if>
        <if test="fieldTimeStart!=null">
            and flow.create_time &gt;= #{fieldTimeStart}
        </if>
        <if test="fieldTimeEnd!=null  ">
            and flow.create_time &lt;= #{fieldTimeEnd}
        </if>
        <if test="fieldInAmount1 !=null ">
            and flow.in_amount &gt;= #{fieldInAmount1}
        </if>
        <if test="fieldInAmount2 !=null">
            and flow.in_amount &lt;= #{fieldInAmount2}
        </if>
        <if test="fieldOutAmount1 != null">
            and flow.out_amount &gt;= #{fieldOutAmount1}
        </if>
        <if test="fieldOutAmount2 !=null">
            and flow.out_amount &lt;= #{fieldOutAmount2}
        </if>
        <if test="fieldBusinessName!=null and fieldBusinessName!=''">
            and flow.`business_name` like CONCAT('%',#{fieldBusinessName},'%')
        </if>
        <if test="fieldAtypeFullname!=null and fieldAtypeFullname!=''">
            and (a.fullname like CONCAT('%',#{fieldAtypeFullname},'%') or
            fa.fullname like CONCAT('%',#{fieldAtypeFullname},'%'))
        </if>
        <if test="fieldPaymentRemark!=null and fieldPaymentRemark!=''">
            and flow.`payment_remark` like CONCAT('%',#{fieldPaymentRemark},'%')
        </if>
        <if test="fieldBillNumber!=null and fieldBillNumber!=''">
            and (acc.bill_number like CONCAT('%',#{fieldBillNumber},'%')
            or bill.bill_number like CONCAT('%',#{fieldBillNumber},'%') )
        </if>
        <if test="fieldBillVchtype!=null">
            and (acc.vchtype =#{fieldBillVchtype} or bill.vchtype =#{fieldBillVchtype})
        </if>
        <if test="fieldProcessState !=null">
            and flow.process_state = #{fieldProcessState}
        </if>
        <if test="fieldAuditName!=null and fieldAuditName!=''">
            and etype.fullname like CONCAT('%',#{fieldAuditName},'%')
        </if>
        <if test="fieldAuditTimeBegin != null and fieldAuditTimeEnd != null ">
            and flow.audit_time between #{fieldAuditTimeBegin} and #{fieldAuditTimeEnd}
        </if>
        <if test="fieldCreateType!=null">
            and flow.create_type = #{fieldCreateType}
        </if>
        order by flow.`create_time` , flow.`download_time` desc

    </select>

    <select id="queryAccountMapInfos"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.PaymentFlowAtypesMapRequest"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.PaymentFlowAtypeMapEntity">
        SELECT map.`id`, map.`business_type`, map.`business_name`, IFNULL(a.`id`,0) AS atypeId, IFNULL(a.fullname,'') AS
        atypeFullname,map.eshop_sale_platform as eshopPlatform,
        IFNULL(a.usercode,'') AS atypeTypeId, map.`top_atype_usercode`,
        map.`top_atype_fullname`, map.`profile_id`, map.`in_out_type`, map.`create_time`, map.`update_time`,
        ifnull(a.stoped,true) AS atypeStoped,ifnull(a.deleted,true ) AS atypeDeleted,map.reocrd_memo,map.create_status
        FROM pl_eshop_payment_flow_atype_map map
        LEFT JOIN base_atype a ON map.profile_id = a.profile_id AND map.atype_id = a.id and a.stoped=0 and a.deleted=0
        where map.profile_id = #{profileId}
        <if test="keyWord!=null and keyWord!='' and filterType==1 ">
            and in_out_type like CONCAT('%',#{keyWord},'%')
        </if>
        <if test="keyWord!=null and keyWord!='' and filterType==2 ">
            and map.business_name like CONCAT('%',#{keyWord},'%')
        </if>
        <if test="keyWord!=null and keyWord!='' and filterType==3 ">
            and a.usercode like CONCAT('%',#{keyWord},'%')
        </if>
        <if test="keyWord!=null and keyWord!='' and filterType==4 ">
            and a.fullname like CONCAT('%',#{keyWord},'%')
        </if>
        <if test="reocrdMemo!=null and reocrdMemo!=''">
            and map.reocrd_memo like CONCAT('%',#{reocrdMemo},'%')
        </if>
        <if test="businessName!=null and businessName!=''">
            and map.business_name like CONCAT('%',#{businessName},'%')
        </if>
        <if test="atypeTypeId!=null and atypeTypeId!=''">
            and a.usercode like CONCAT('%',#{atypeTypeId},'%')
        </if>
        <if test="atypeName!=null and atypeName!=''">
            and a.fullname like CONCAT('%',#{atypeName},'%')
        </if>
        <if test="eshopPlatform!=null">
            and map.eshop_sale_platform =#{eshopPlatform}
        </if>

        <if test="notAllowEshopPlatform!=null and notAllowEshopPlatform.size()>0">
            and map.eshop_sale_platform  not in
            <foreach collection="notAllowEshopPlatform" close=")" open="(" separator="," item="platformtype">
                #{platformtype}
            </foreach>
        </if>
        order by eshop_sale_platform
    </select>

    <update id="updatePaymentFlowAtypeMap">
        update pl_eshop_payment_flow_atype_map
        SET `atype_id`          =#{atypeId},
            `atype_fullname`= #{atypeFullname},
            `atype_type_id`     =#{atypeTypeId},
            `top_atype_usercode`=#{topAtypeUsercode},
            `top_atype_fullname`=#{topAtypeFullname}
        where profile_id = #{profileId}
          and id = #{id}
    </update>

    <select id="queryBalanceAccountInfo"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.PaymentFlowBalanceInfo">
        SELECT
        flow.`id`, flow.`payment_number`, flow.`merchant_order_number`,flow.`create_time`,
        flow.`business_type`, flow.`business_name`, ifnull(o.`trade_order_id`,"") as tradeId, flow.`in_amount`,
        flow.`out_amount`,flow.`payment_remark`, ifnull(flow.`bill_vchcode`,0) as billVchcode,
        ifnull(flow.`bill_vchtype`,0) as billVchtype,flow.`otype_id`, flow.`profile_id`,
        flow.`eshop_order_id`,
        flow.`unique_mark`,ifnull(o.local_trade_state,0) as customTradeStatus,
        ifnull(o.etype_id,0) as orderEtypeId,
        ifnull(a.`id`,0) as atypeId, ifnull(a.`fullname` ,"")as `atypeFullname`, ifnull(a.`typeid`,"") as `atypeTypeId`,
        ifnull(map.`in_out_type`,-1) as inOutType,
        b.`id` as orderBtypeId,bo.atype_id as otypeAtypeid,ifnull(bo.fullname,"") as
        otypeName,o.create_time as tradeCreateTime
        FROM `pl_eshop_payment_flow` flow
        LEFT JOIN `pl_eshop_payment_flow_atype_map` map ON map.profile_id = flow.profile_id AND map.business_type =
        flow.business_type
        LEFT JOIN `base_atype` a ON a.profile_id = map.profile_id AND a.id = map.atype_id AND a.deleted =0 AND a.stoped
        = 0
        LEFT JOIN `base_otype` bo ON bo.profile_id = flow.profile_id AND bo.id = flow.otype_id
        LEFT JOIN `pl_eshop_sale_order` o On flow.profile_id = o.profile_id AND flow.eshop_order_id = o.id
        LEFT JOIN base_btype b ON b.profile_id = o.profile_id AND b.id = o.btype_id
        WHERE flow.profile_id=#{profileId}
        <if test="alipayIds!=null and alipayIds.size()>0">
            AND flow.id IN
            <foreach collection="alipayIds" close=")" open="(" separator="," item="alipayid">
                #{alipayid}
            </foreach>
        </if>
    </select>

    <select id="queryStoreFinanceCheck"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.StoreFinanceCheckRequest"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.response.StoreFinanceCheckPageData">
        select '' as id,abc.otype_id,abc.vchcode billcoreVchcode,
        abc.business_type,
        abc.vchtype,
        abc.bill_number,
        currency_bill_total as billTotal,
        spb.preferential_total as giftTotal, (m.currency_balance_total - m.currency_balance_remain) as balance_settled,
        abc.memo as paymentRemark,rbe.fullname as check_name,
        efcr.check_time,efcr.check_status
        from acc_bill_core abc
        left join acc_bill_assinfo b on abc.vchcode = b.vchcode and b.profile_id = abc.profile_id
        left join ss_preferential_bill spb
        on spb.vchcode = abc.vchcode and spb.profile_id = abc.profile_id and spb.preferential_type = 6
        left join acc_bill_balance_info m
        on abc.vchcode = m.vchcode and m.profile_id = abc.profile_id and m.balance_business_type = 0
        LEFT JOIN pl_eshop_finance_check_result efcr ON efcr.profile_id = abc.profile_id and efcr.vchcode = abc.vchcode
        LEFT JOIN base_etype rbe ON rbe.profile_id = efcr.profile_id and efcr.check_etype_id = rbe.id
        where abc.order_sale_mode=6 and abc.vchtype in(2000,2100)
        and abc.profile_id =#{profileId} and abc.bill_date between #{beginTime} and #{endTime}
        <if test="otypeIds != null and otypeIds.size()>0">
            and abc.otype_id in
            <foreach collection="otypeIds" item="otypeid" close=")" open="(" separator=",">
                #{otypeid}
            </foreach>
        </if>
        <if test="keyword != null and keyword!=''">
            and abc.bill_number =#{keyword}
        </if>
        <if test="checkStatus != null and checkStatus == 1 and checkStatus!=500">
            and efcr.check_status >=1
        </if>
        <if test="checkStatus != null and checkStatus != 1 and checkStatus!=500">
            and efcr.check_status is null
        </if>
        <if test="otypeId != null">
            and sa.otype_id =#{otypeId}
        </if>
        <if test="billBusinessType != null">
            and abc.business_type =#{billBusinessType}
        </if>
        <if test="vchtype != null">
            and abc.vchtype =#{vchtype}
        </if>
        <if test="billNumber != null and billNumber!=''">
            and abc.bill_number =#{billNumber}
        </if>

        union

        select '' as id,abc.otype_id,abc.vchcode billcoreVchcode,
        abc.business_type,
        abc.vchtype,
        abc.bill_number,
        currency_bill_total as billTotal,
        spb.preferential_total as giftTotal, (m.currency_balance_total - m.currency_balance_remain) as balance_settled,
        abc.memo as paymentRemark,rbe.fullname as check_name,
        efcr.check_time,efcr.check_status
        from td_bill_core abc
        left join td_bill_assinfo b on abc.vchcode = b.vchcode and b.profile_id = abc.profile_id
        left join ss_preferential_bill spb
        on spb.vchcode = abc.vchcode and spb.profile_id = abc.profile_id and spb.preferential_type = 6
        left join acc_bill_balance_info m
        on abc.vchcode = m.vchcode and m.profile_id = abc.profile_id and m.balance_business_type = 0
        LEFT JOIN pl_eshop_finance_check_result efcr ON efcr.profile_id = abc.profile_id and efcr.vchcode = abc.vchcode
        LEFT JOIN base_etype rbe ON rbe.profile_id = efcr.profile_id and efcr.check_etype_id = rbe.id
        where abc.order_sale_mode=6 and abc.vchtype in(2000,2100)
        and abc.profile_id =#{profileId} and abc.bill_date between #{beginTime} and #{endTime}
        <if test="otypeIds != null and otypeIds.size()>0">
            and abc.otype_id in
            <foreach collection="otypeIds" item="otypeid" close=")" open="(" separator=",">
                #{otypeid}
            </foreach>
        </if>
        <if test="keyword != null and keyword!=''">
            and abc.bill_number =#{keyword}
        </if>
        <if test="checkStatus != null and checkStatus == 1 and checkStatus!=500">
            and efcr.check_status >=1
        </if>
        <if test="checkStatus != null and checkStatus != 1 and checkStatus!=500">
            and efcr.check_status is null
        </if>
        <if test="otypeId != null">
            and sa.otype_id =#{otypeId}
        </if>
        <if test="billBusinessType != null">
            and abc.business_type =#{billBusinessType}
        </if>
        <if test="vchtype != null">
            and abc.vchtype =#{vchtype}
        </if>
        <if test="billNumber != null and billNumber!=''">
            and abc.bill_number =#{billNumber}
        </if>
    </select>

    <select id="checkIsAllConfirm" resultType="java.lang.Integer">
        select count(*)
        from pl_eshop_refund
        where profile_id = #{profileId}
          and otype_id = #{otypeId}
          and refund_type!=5 and  deleted=0 and trade_order_id =#{tradeOrderId}
          and confirm_state &lt;&gt; 1
    </select>


    <select id="querySKBillTotal" resultType="java.math.BigDecimal">
        select sum(bill_total)
        from (select sum(tbbd.currency_payment_total) as bill_total
              from pl_eshop_sale_order o
                       LEFT JOIN td_bill_relation tbr on tbr.profile_id = o.profile_id and tbr.source_vchcode = o.id and
                                                         tbr.target_vchtype in (4001, 4002)
                       left join td_bill_core tbc on tbc.profile_id = o.profile_id and tbc.vchcode = tbr.target_vchcode
                       left join td_bill_balance_detail tbbd
                                 on tbbd.profile_id = o.profile_id and tbbd.vchcode = tbc.vchcode and
                                    tbbd.business_vchcode = o.id
              where o.profile_id = #{profileId}
                and o.id = #{id}
              union
              select sum(tbbd.currency_payment_total) as bill_total
              from pl_eshop_sale_order o
                       LEFT JOIN td_bill_relation tbr on tbr.profile_id = o.profile_id and tbr.source_vchcode = o.id and
                                                         tbr.target_vchtype in (4001, 4002)
                       left join acc_bill_core abc on abc.profile_id = o.profile_id and abc.vchcode = tbr.target_vchcode
                       left join acc_bill_balance_detail tbbd
                                 on tbbd.profile_id = o.profile_id and tbbd.vchcode = abc.vchcode and
                                    tbbd.business_vchcode = o.id
              where o.profile_id = #{profileId}
                and o.id = #{id}) tmp
    </select>


    <select id="queryAdvanceReceiveTotal" resultType="java.math.BigDecimal">
        select sum(currency_dised_taxed_total)
        from (select SUM(-tbda.currency_dised_taxed_total) as currency_dised_taxed_total
              FROM acc_bill_detail_deliver deliverd
                       left join acc_bill_detail_assinfo_sale tbda
                                 ON tbda.detail_id = deliverd.detail_id AND tbda.profile_id = deliverd.profile_id
              WHERE deliverd.profile_id = #{profileId}
                and deliverd.deleted = 0
                and deliverd.trade_order_id = #{tradeId}
              GROUP BY deliverd.order_id) tmp
    </select>

    <select id="queryAccNeedReceiveTotal" resultType="java.math.BigDecimal">
        select currency_dised_taxed_total
        from (select SUM(-tbda.currency_dised_taxed_total) as currency_dised_taxed_total
              FROM acc_bill_detail_deliver deliverd
                       left join acc_bill_detail_assinfo_sale tbda
                                 ON tbda.detail_id = deliverd.detail_id AND tbda.profile_id = deliverd.profile_id
              WHERE deliverd.profile_id = #{profileId}
                and deliverd.deleted = 0
                and
                (deliverd.order_id = #{id} or deliverd.trade_order_id=#{tradeOrderId})
              GROUP BY deliverd.order_id) tmp limit 1
    </select>

    <select id="queryTdNeedReceiveTotal" resultType="java.math.BigDecimal">
        select currency_dised_taxed_total
        from (select SUM(-tbda.currency_dised_taxed_total) as currency_dised_taxed_total
              FROM td_bill_detail_deliver deliverd
                       left join td_bill_detail_assinfo tbda
                                 ON tbda.detail_id = deliverd.detail_id AND tbda.profile_id = deliverd.profile_id
              WHERE deliverd.profile_id = #{profileId}
                and deliverd.deleted = 0
                and deliverd.order_id = #{id}
              GROUP BY deliverd.order_id) tmp limit 1
    </select>

    <select id="queryDevliverOrder"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.FinanceCheckDeliverBillInfo">
        select abc.bill_number,tbwd.detail_id,deliverd.trade_order_id,abc.business_type,-tbda.currency_dised_taxed_total as billTotal,tbw.full_link_status
        FROM acc_bill_detail_deliver deliverd
                 left join acc_bill_detail_assinfo_sale tbda
                           ON tbda.detail_id = deliverd.detail_id AND tbda.profile_id = deliverd.profile_id
                 left join acc_bill_core abc on abc.profile_id = deliverd.profile_id and abc.vchcode = deliverd.vchcode
                 left join acc_bill_warehouse_task_detail tbwd on tbwd.profile_id = deliverd.profile_id and tbwd.detail_id = deliverd.detail_id
                 left join acc_bill_warehouse_task tbw on tbw.profile_id = tbwd.profile_id and tbw.warehouse_task_id = tbwd.warehouse_task_id
            left join pl_eshop_sale_order o on o.profile_id = deliverd.profile_id and o.id = deliverd.order_id
        WHERE deliverd.profile_id = #{profileId}
          and deliverd.deleted = 0
          and deliverd.trade_order_id = #{tradeOrderId}
          and abc.otype_id=#{otypeId}
        union
        select abc.bill_number,tbwd.detail_id,deliverd.trade_order_id,abc.business_type,-tbda.currency_dised_taxed_total as billTotal,tbw.full_link_status
        FROM acc_bill_detail_deliver deliverd
                 left join acc_bill_detail_assinfo_sale tbda
                           ON tbda.detail_id = deliverd.detail_id AND tbda.profile_id = deliverd.profile_id
                 left join acc_bill_core abc on abc.profile_id = deliverd.profile_id and abc.vchcode = deliverd.vchcode
                 left join acc_bill_warehouse_task_detail tbwd on tbwd.profile_id = deliverd.profile_id and tbwd.detail_id = deliverd.detail_id
                 left join acc_bill_warehouse_task tbw on tbw.profile_id = tbwd.profile_id and tbw.warehouse_task_id = tbwd.warehouse_task_id
                 left join pl_eshop_sale_order o on o.profile_id = deliverd.profile_id and o.id = deliverd.order_id
        WHERE deliverd.profile_id = #{profileId}
          and deliverd.deleted = 0
          and  deliverd.order_id=#{eshopOrderId}
          and abc.otype_id= #{otypeId}
        union
        select abc.bill_number,tbwd.detail_id,deliverd.trade_order_id,abc.business_type,-tbda.currency_dised_taxed_total as billTotal,tbw.full_link_status
        FROM td_bill_detail_deliver deliverd
                 left join td_bill_detail_assinfo tbda
                           ON tbda.detail_id = deliverd.detail_id AND tbda.profile_id = deliverd.profile_id
                 left join td_bill_core abc on abc.profile_id = deliverd.profile_id and abc.vchcode = deliverd.vchcode
                 left join td_bill_warehouse_task_detail tbwd on tbwd.profile_id = deliverd.profile_id and tbwd.detail_id = deliverd.detail_id
                 left join td_bill_warehouse_task tbw on tbw.profile_id = tbwd.profile_id and tbw.warehouse_task_id = tbwd.warehouse_task_id
            left join pl_eshop_sale_order o on o.profile_id = deliverd.profile_id and o.id = deliverd.order_id
        WHERE deliverd.profile_id = #{profileId}
          and deliverd.deleted = 0 and abc.deleted = 0
          and deliverd.trade_order_id = #{tradeOrderId}
          and abc.otype_id= #{otypeId}
        union
        select abc.bill_number,tbwd.detail_id,deliverd.trade_order_id,abc.business_type,-tbda.currency_dised_taxed_total as billTotal,tbw.full_link_status
        FROM td_bill_detail_deliver deliverd
                 left join td_bill_detail_assinfo tbda
                           ON tbda.detail_id = deliverd.detail_id AND tbda.profile_id = deliverd.profile_id
                 left join td_bill_core abc on abc.profile_id = deliverd.profile_id and abc.vchcode = deliverd.vchcode
                 left join td_bill_warehouse_task_detail tbwd on tbwd.profile_id = deliverd.profile_id and tbwd.detail_id = deliverd.detail_id
                 left join td_bill_warehouse_task tbw on tbw.profile_id = tbwd.profile_id and tbw.warehouse_task_id = tbwd.warehouse_task_id
                 left join pl_eshop_sale_order o on o.profile_id = deliverd.profile_id and o.id = deliverd.order_id
        WHERE deliverd.profile_id = #{profileId}
          and deliverd.deleted = 0 and abc.deleted = 0
          and deliverd.order_id=#{eshopOrderId}
          and abc.otype_id= #{otypeId}
    </select>


    <select id="queryFinanceCheck"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.PlatformFinanceCheckRequest"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.PlatformFinanceCheckPageData">
        SELECT 0 as selected,o.`id`,o.`trade_order_id`,o.`otype_id`,o.`profile_id`,o.`btype_id`, o.`dised_taxed_total`,
        o.`total`,o.`order_buyer_freight_fee`,o.`ptype_service_fee`,o.local_trade_state,o.pay_no,
        bo.fullname AS otypeName,b.fullname AS btypeName,esoe.mark_info,efcr.check_etype_id,rbe.fullname as check_name,
        efcr.check_time ,efcr.check_status,efcr.check_diff,efcr.bill_check_diff,efcr.fee_total,efcr.bill_check_status,0
        as sourceType,re.refund_type,aba.currency_order_buyer_freight_fee as
        deliverFreightFee,aba.currency_ptype_service_fee as deliverServiceFee,bo.atype_id,
        sum(re.refund_apply_freight_fee) as refund_apply_freight_fee,
        sum(re.refund_apply_service_fee) as refund_apply_service_fee,
        sum(re.refund_apply_taxed_total) as refundApplyTaxedTotal,
        sum(re.refund_apply_taxed_total) as refundMoney,
        sum(tbc.bill_total) as realrefundMoney,
        esoe.national_subsidy_total
        FROM ${tableName}
        LEFT JOIN acc_bill_deliver abd ON abd.profile_id = o.profile_id and abd.trade_order_id = o.trade_order_id
        LEFT JOIN acc_bill_assinfo aba ON aba.profile_id = o.profile_id and aba.vchcode = abd.vchcode
        LEFT JOIN pl_eshop_sale_order_extend esoe ON esoe.profile_id = o.profile_id and esoe.eshop_order_id = o.id
        LEFT JOIN pl_eshop_refund re on re.profile_id = o.profile_id and re.otype_id = o.otype_id and re.trade_order_id
        = o.trade_order_id and re.deleted=0 and re.refund_state in(1,2,3,4,5) and re.confirm_state in (1,0)
        LEFT JOIN `base_otype` bo ON bo.profile_id = o.profile_id AND bo.id = o.otype_id
        LEFT JOIN base_btype b ON b.profile_id = o.profile_id AND b.id = o.btype_id
        LEFT JOIN pl_eshop_finance_check_result efcr ON efcr.profile_id = o.profile_id and efcr.vchcode = o.id
        LEFT JOIN base_etype rbe ON rbe.profile_id = efcr.profile_id and efcr.check_etype_id = rbe.id
        LEFT JOIN td_bill_relation tbr on tbr.profile_id = o.profile_id and tbr.source_vchcode = re.id and
        tbr.target_vchtype in(4001,4002,4005)
        LEFT JOIN acc_bill_core tbc on tbc.profile_id = o.profile_id and tbc.vchcode = tbr.target_vchcode and
        tbc.vchtype
        in(4001,4002,4005)
        <if test="ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=`o`.profile_id and blsk.object_type=2 and
            `o`.ktype_id=blsk.object_id and blsk.etype_id=#{etypeId}
        </if>
        <if test="otypeLimited">
            inner join base_limit_scope blso on blso.profile_id=`o`.profile_id and blso.object_type=3 and
            `o`.otype_id=blso.object_id and blso.etype_id=#{etypeId}
        </if>
        WHERE o.profile_id = #{profileId} AND o.deleted = 0 AND bo.ocategory in (0,1) and o.create_type &lt;&gt; 2
        <if test="beginTime!=null and endTime!=null and timeType==0">
            and o.trade_finish_time BETWEEN #{beginTime} AND #{endTime}
        </if>
        <if test="beginTime!=null and endTime!=null and timeType==1">
            and esoe.platform_send_time BETWEEN #{beginTime} AND #{endTime}
        </if>
        <if test="beginTime!=null and endTime!=null and timeType==2">
            and o.trade_pay_time BETWEEN #{beginTime} AND #{endTime}
        </if>
        <if test="beginTime!=null and endTime!=null and timeType==3">
            and o.create_time BETWEEN #{beginTime} AND #{endTime}
        </if>
        <if test="otypeList != null and otypeList.size()>0">
            AND o.otype_id IN
            <foreach collection="otypeList" close=")" open="(" separator="," item="otypeId">
                #{otypeId}
            </foreach>
        </if>
        <if test="checkStatusFilter !=null and checkStatusFilter == 0 ">
            and (efcr.check_status is null or efcr.check_status = 0)
        </if>
        <if test="checkStatusFilter !=null and checkStatusFilter == 1 ">
            and efcr.check_status=1
        </if>
        <if test="billCheckStatusFilter !=null and billCheckStatusFilter == 0 ">
            and (efcr.bill_check_status is null or efcr.bill_check_status = 0)
        </if>
        <if test="billCheckStatusFilter !=null and billCheckStatusFilter == 1 ">
            and efcr.bill_check_status=1
        </if>

        <if test="checkStatus !=null and checkStatus == 0">
            and (efcr.bill_check_status is null or efcr.bill_check_diff != 0)
        </if>
        <if test="tradeStatusList != null and tradeStatusList.size() >0">
            AND o.local_trade_state in
            <foreach collection="tradeStatusList" item="status" close=")" open="(" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="checkStatus !=null and checkStatus == 1">
            and (efcr.bill_check_status = 1 and efcr.bill_check_diff = 0)
        </if>
        <if test="checkStatus !=null and checkStatus == 2">
            and (efcr.check_status is null or efcr.check_diff != 0)
        </if>
        <if test="checkStatus !=null and checkStatus == 3">
            and (efcr.check_status = 1 and efcr.check_diff = 0)
        </if>
        <if test="keyWords!=null and keyWords.size()>0 and filterKeyType==1 ">
            AND o.trade_order_id IN
            <foreach collection="keyWords" close=")" open="(" separator="," item="keyWord">
                #{keyWord}
            </foreach>
        </if>
        <if test="ids!=null and ids.size()>0">
            AND o.id IN
            <foreach collection="ids" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="keyWords!=null and keyWords.size()>0 and filterKeyType==2 ">
            AND re.trade_refund_order_number in
            <foreach collection="keyWords" close=")" open="(" separator="," item="keyWord">
                #{keyWord}
            </foreach>
            )
        </if>

        <if test="markInfo != '' and markInfo != null">
            and esoe.mark_info like CONCAT('%',#{markInfo},'%')
        </if>
        <if test="otypeId != null and otypeId != 0">
            AND o.otype_id = #{otypeId}
        </if>
        <if test="tradeStatus != null and tradeStatus.size() >0">
            AND o.local_trade_state in
            <foreach collection="tradeStatus" item="status" close=")" open="(" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="localTradeState != null and localTradeState != 8">
            AND o.local_trade_state = #{localTradeState}
        </if>
        <if test="filterTradeOrderId !=null and filterTradeOrderId !=  '' ">
            AND o.trade_order_id like CONCAT('%',#{filterTradeOrderId},'%')
        </if>
        <if test="btypeName !=null and btypeName !=  '' ">
            AND b.fullname like CONCAT('%',#{btypeName},'%')
        </if>
        <if test="checkName !=null and checkName !=  '' ">
            AND rbe.fullname like CONCAT('%',#{checkName},'%')
        </if>
        <if test="checkTimeStart != null and checkTimEnd != null">
            AND efcr.check_time BETWEEN #{checkTimeStart} AND #{checkTimEnd}
        </if>

        <if test="checkDiff1 != null">
            AND efcr.check_diff &gt;= #{checkDiff1}
        </if>
        <if test="checkDiff2 !=null">
            AND efcr.check_diff &lt;= #{checkDiff2}
        </if>

        <if test="disedTaxedTotal1 != null">
            AND o.`dised_taxed_total` &gt;= #{disedTaxedTotal1}
        </if>
        <if test="disedTaxedTotal2 !=null">
            AND o.`dised_taxed_total` &lt;= #{disedTaxedTotal2}
        </if>
        group by o.trade_order_id,o.otype_id
    </select>

    <select id="queryFinanceCheckMain"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.PlatformFinanceCheckRequest"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.PlatformFinanceCheckPageData">
        select * from (
        SELECT 0 as selected,o.`id`,o.`trade_order_id`,'' as refundOrderId ,o.`otype_id`,o.`profile_id`,o.`btype_id`,0
        as isRefund,0 as checkRange,
        o.`dised_taxed_total`,if(re.refund_type=5,re.refund_apply_taxed_total,0) as refundApplyTaxedTotal,0 as realrefundMoney,
        o.`total`,o.`order_buyer_freight_fee`,o.`ptype_service_fee`,if(re.refund_type = 5, re.refund_apply_service_fee, 0) as refundApplyServiceFee,
        if(re.refund_type = 5, re.refund_apply_freight_fee, 0) as refundApplyFreightFee,o.local_trade_state,o.pay_no,
        bo.fullname AS otypeName,b.fullname AS btypeName,esoe.mark_info,efcr.check_etype_id,rbe.fullname as check_name,
        efcr.check_time ,efcr.check_status,efcr.check_diff,efcr.bill_check_diff,efcr.fee_total,efcr.bill_check_status,0
        as sourceType,re.deleted,re.business_type,re.refund_state,re.refund_type,re.confirm_state,re.pay_state,bo.atype_id,ba.fullname as atypeName,esoe.national_subsidy_total
        FROM ${tableName}
        LEFT JOIN pl_eshop_sale_order_extend esoe ON esoe.profile_id = o.profile_id and esoe.eshop_order_id = o.id
        LEFT JOIN pl_eshop_refund re on re.profile_id = o.profile_id and re.otype_id = o.otype_id and re.trade_order_id
        = o.trade_order_id and re.deleted=0 and re.refund_state in(1,2,3,5) and re.confirm_state in (1,0)
        LEFT JOIN `base_otype` bo ON bo.profile_id = o.profile_id AND bo.id = o.otype_id
        LEFT JOIN `base_atype` ba ON ba.profile_id = o.profile_id AND ba.id = bo.atype_id
        LEFT JOIN base_btype b ON b.profile_id = o.profile_id AND b.id = o.btype_id
        LEFT JOIN pl_eshop_finance_check_result efcr ON efcr.profile_id = o.profile_id and efcr.vchcode = o.id
        LEFT JOIN base_etype rbe ON rbe.profile_id = efcr.profile_id and efcr.check_etype_id = rbe.id
        <if test="ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=`o`.profile_id and blsk.object_type=2 and
            `o`.ktype_id=blsk.object_id and blsk.etype_id=#{etypeId}
        </if>
        <if test="otypeLimited">
            inner join base_limit_scope blso on blso.profile_id=`o`.profile_id and blso.object_type=3 and
            `o`.otype_id=blso.object_id and blso.etype_id=#{etypeId}
        </if>
        WHERE o.profile_id = #{profileId} AND o.deleted = 0 AND bo.ocategory in (0,1) and o.create_type &lt;&gt; 2
        AND (`o`.order_sale_type  not in(3,6) OR (`o`.order_sale_type=3 AND `o`.platform_parent_order_id !=''))
        <if test="beginTime!=null and endTime!=null and timeType==0">
            and o.trade_finish_time BETWEEN #{beginTime} AND #{endTime}
        </if>
        <if test="beginTime!=null and endTime!=null and timeType==1">
            and esoe.platform_send_time BETWEEN #{beginTime} AND #{endTime}
        </if>
        <if test="beginTime!=null and endTime!=null and timeType==2">
            and o.trade_pay_time BETWEEN #{beginTime} AND #{endTime}
        </if>
        <if test="beginTime!=null and endTime!=null and timeType==3">
            and o.create_time BETWEEN #{beginTime} AND #{endTime}
        </if>
        <if test="otypeList != null and otypeList.size()>0">
            AND o.otype_id IN
            <foreach collection="otypeList" close=")" open="(" separator="," item="otypeId">
                #{otypeId}
            </foreach>
        </if>
        <if test="checkStatus !=null and checkStatus == 0">
            and (efcr.bill_check_status is null or efcr.bill_check_diff != 0)
        </if>
        <if test="tradeStatusList != null and tradeStatusList.size() >0">
            AND o.local_trade_state in
            <foreach collection="tradeStatusList" item="status" close=")" open="(" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="checkStatus !=null and checkStatus == 1">
            and (efcr.bill_check_status = 1 and efcr.bill_check_diff = 0)
        </if>
        <if test="checkStatus !=null and checkStatus == 2">
            and (efcr.check_status is null or efcr.check_diff != 0)
        </if>
        <if test="checkStatus !=null and checkStatus == 3">
            and (efcr.check_status = 1 and efcr.check_diff = 0)
        </if>
        <if test="keyWords!=null and keyWords.size()>0 and filterKeyType==1 ">
            AND o.trade_order_id IN
            <foreach collection="keyWords" close=")" open="(" separator="," item="keyWord">
                #{keyWord}
            </foreach>
        </if>
          <if test="keyWords!=null and keyWords.size()>0 and filterKeyType==2 ">
            AND re.trade_refund_order_number IN
            <foreach collection="keyWords" close=")" open="(" separator="," item="keyWord">
                #{keyWord}
            </foreach>
        </if>
        <if test="markInfo != '' and markInfo != null">
            and esoe.mark_info like CONCAT('%',#{markInfo},'%')
        </if>
        <if test="otypeId != null and otypeId != 0">
            AND o.otype_id = #{otypeId}
        </if>
        <if test="tradeStatus != null and tradeStatus.size() >0">
            AND o.local_trade_state in
            <foreach collection="tradeStatus" item="status" close=")" open="(" separator=",">
                #{status}
            </foreach>
        </if>
          <if test="ids != null and ids.size() >0">
            AND o.id in
            <foreach collection="ids" item="id" close=")" open="(" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="localTradeState != null and localTradeState != 8">
            AND o.local_trade_state = #{localTradeState}
        </if>
        <if test="filterTradeOrderId !=null and filterTradeOrderId !=  '' ">
            AND o.trade_order_id like CONCAT('%',#{filterTradeOrderId},'%')
        </if>
        <if test="btypeName !=null and btypeName !=  '' ">
            AND b.fullname like CONCAT('%',#{btypeName},'%')
        </if>
        <if test="checkName !=null and checkName !=  '' ">
            AND rbe.fullname like CONCAT('%',#{checkName},'%')
        </if>
        <if test="checkTimeStart != null and checkTimEnd != null">
            AND efcr.check_time BETWEEN #{checkTimeStart} AND #{checkTimEnd}
        </if>

        <if test="checkDiff1 != null">
            AND efcr.check_diff &gt;= #{checkDiff1}
        </if>
        <if test="checkDiff2 !=null">
            AND efcr.check_diff &lt;= #{checkDiff2}
        </if>

        <if test="disedTaxedTotal1 != null">
            AND o.`dised_taxed_total` &gt;= #{disedTaxedTotal1}
        </if>
        <if test="disedTaxedTotal2 !=null">
            AND o.`dised_taxed_total` &lt;= #{disedTaxedTotal2}
        </if>
        group by o.trade_order_id,o.otype_id
        union
        SELECT 0 as selected,re.`id`,o.`trade_order_id`,re.trade_refund_order_number as refundOrderId
        ,re.`otype_id`,re.`profile_id`,re.`btype_id`,1 as isRefund,1 as checkRange,0 AS disedTaxedTotal,
        re.`refund_apply_taxed_total`,if(re.pay_state=2,ifnull(tbc.bill_total, abc.bill_total), 0) as realrefundMoney,
        o.`total`,o.`order_buyer_freight_fee`,o.`ptype_service_fee`,re.`refund_apply_freight_fee`,re.`refund_apply_service_fee`,o.local_trade_state,o.pay_no,
        bo.fullname AS otypeName,b.fullname AS btypeName,esoe.mark_info,efcr.check_etype_id,rbe.fullname as check_name,
        efcr.check_time ,efcr.check_status,efcr.check_diff,efcr.bill_check_diff,efcr.fee_total,efcr.bill_check_status,0
        as sourceType,re.deleted,re.business_type,re.refund_state,re.refund_type,re.confirm_state,re.pay_state,bo.atype_id,ba.fullname as atypeName,esoe.national_subsidy_total
        FROM pl_eshop_refund re
        left join pl_eshop_sale_order o on o.profile_id = re.profile_id and o.id = re.eshop_order_id and o.otype_id =
        re.otype_id
        LEFT JOIN pl_eshop_sale_order_extend esoe ON esoe.profile_id = o.profile_id and esoe.eshop_order_id = o.id
        LEFT JOIN `base_otype` bo ON bo.profile_id = o.profile_id AND bo.id = o.otype_id
        LEFT JOIN `base_atype` ba ON ba.profile_id = o.profile_id AND ba.id = bo.atype_id
        LEFT JOIN base_btype b ON b.profile_id = o.profile_id AND b.id = o.btype_id
        LEFT JOIN pl_eshop_finance_check_result efcr ON efcr.profile_id = o.profile_id and efcr.vchcode = re.id
        LEFT JOIN base_etype rbe ON rbe.profile_id = efcr.profile_id and efcr.check_etype_id = rbe.id
        LEFT JOIN td_bill_relation tbr ON tbr.profile_id = re.profile_id and tbr.source_vchcode =
        re.id and tbr.target_vchtype in(4002,4005)
        LEFT JOIN td_bill_core tbc ON tbc.profile_id = re.profile_id and tbc.vchcode = tbr.target_vchcode
        LEFT JOIN acc_bill_core abc ON abc.profile_id = re.profile_id and abc.vchcode = tbr.target_vchcode
        <if test="ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=`o`.profile_id and blsk.object_type=2 and
            `o`.ktype_id=blsk.object_id and blsk.etype_id=#{etypeId}
        </if>
        <if test="otypeLimited">
            inner join base_limit_scope blso on blso.profile_id=`o`.profile_id and blso.object_type=3 and
            `o`.otype_id=blso.object_id and blso.etype_id=#{etypeId}
        </if>
        WHERE o.profile_id = #{profileId} AND re.deleted = 0 and re.refund_state in (1,2,3,5) and re.confirm_state
        in (1, 0) and re.refund_type in(0,1)
        <if test="beginTime!=null and endTime!=null and timeType==0">
            and o.trade_finish_time BETWEEN #{beginTime} AND #{endTime}
        </if>
        <if test="beginTime!=null and endTime!=null and timeType==1">
            and esoe.platform_send_time BETWEEN #{beginTime} AND #{endTime}
        </if>
        <if test="beginTime!=null and endTime!=null and timeType==2">
            and o.trade_pay_time BETWEEN #{beginTime} AND #{endTime}
        </if>
        <if test="beginTime!=null and endTime!=null and timeType==3">
            and o.create_time BETWEEN #{beginTime} AND #{endTime}
        </if>
        <if test="ids != null and ids.size() >0">
            AND re.id in
            <foreach collection="ids" item="id" close=")" open="(" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="otypeList != null and otypeList.size()>0">
            AND o.otype_id IN
            <foreach collection="otypeList" close=")" open="(" separator="," item="otypeId">
                #{otypeId}
            </foreach>
        </if>
        <if test="checkStatus !=null and checkStatus == 0">
            and (efcr.bill_check_status is null or efcr.bill_check_diff != 0)
        </if>
        <if test="tradeStatusList != null and tradeStatusList.size() >0">
            AND o.local_trade_state in
            <foreach collection="tradeStatusList" item="status" close=")" open="(" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="checkStatus !=null and checkStatus == 1">
            and (efcr.bill_check_status = 1 and efcr.bill_check_diff = 0)
        </if>
        <if test="checkStatus !=null and checkStatus == 2">
            and (efcr.check_status is null or efcr.check_diff != 0)
        </if>
        <if test="checkStatus !=null and checkStatus == 3">
            and (efcr.check_status = 1 and efcr.check_diff = 0)
        </if>
        <if test="keyWords!=null and keyWords.size()>0 and filterKeyType==1 ">
            AND o.trade_order_id IN
            <foreach collection="keyWords" close=")" open="(" separator="," item="keyWord">
                #{keyWord}
            </foreach>
        </if>
        <if test="keyWords!=null and keyWords.size()>0 and filterKeyType==2 ">
            AND re.trade_refund_order_number IN
            <foreach collection="keyWords" close=")" open="(" separator="," item="keyWord">
                #{keyWord}
            </foreach>
        </if>
        <if test="markInfo != '' and markInfo != null">
            and esoe.mark_info like CONCAT('%',#{markInfo},'%')
        </if>
        <if test="otypeId != null and otypeId != 0">
            AND o.otype_id = #{otypeId}
        </if>
        <if test="tradeStatus != null and tradeStatus.size() >0">
            AND o.local_trade_state in
            <foreach collection="tradeStatus" item="status" close=")" open="(" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="localTradeState != null and localTradeState != 8">
            AND o.local_trade_state = #{localTradeState}
        </if>
        <if test="filterTradeOrderId !=null and filterTradeOrderId !=  '' ">
            AND o.trade_order_id like CONCAT('%',#{filterTradeOrderId},'%')
        </if>
        <if test="btypeName !=null and btypeName !=  '' ">
            AND b.fullname like CONCAT('%',#{btypeName},'%')
        </if>
        <if test="checkName !=null and checkName !=  '' ">
            AND rbe.fullname like CONCAT('%',#{checkName},'%')
        </if>
        <if test="checkTimeStart != null and checkTimEnd != null">
            AND efcr.check_time BETWEEN #{checkTimeStart} AND #{checkTimEnd}
        </if>

        <if test="checkDiff1 != null">
            AND efcr.check_diff &gt;= #{checkDiff1}
        </if>
        <if test="checkDiff2 !=null">
            AND efcr.check_diff &lt;= #{checkDiff2}
        </if>

        <if test="disedTaxedTotal1 != null">
            AND o.`dised_taxed_total` &gt;= #{disedTaxedTotal1}
        </if>
        <if test="disedTaxedTotal2 !=null">
            AND o.`dised_taxed_total` &lt;= #{disedTaxedTotal2}
        </if>
        ) tmp where 1=1
        <if test="filterKeyType==2 ">
            and tmp.isRefund=1
        </if>


    </select>

    <sql id="financeCheckCalculateInJoin">
        LEFT JOIN pl_eshop_sale_order_extend esoe ON esoe.profile_id = o.profile_id and esoe.eshop_order_id = o.id
        LEFT JOIN `base_otype` bo ON bo.profile_id = o.profile_id AND bo.id = o.otype_id
        LEFT JOIN `base_atype` ba ON ba.profile_id = o.profile_id AND ba.id = bo.atype_id
        LEFT JOIN base_btype b ON b.profile_id = o.profile_id AND b.id = o.btype_id
        LEFT JOIN pl_eshop_finance_check_result efcr ON efcr.profile_id = o.profile_id and efcr.vchcode = o.id
        LEFT JOIN base_etype rbe ON rbe.profile_id = efcr.profile_id and efcr.check_etype_id = rbe.id
        <if test="ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=`o`.profile_id and blsk.object_type=2 and
            `o`.ktype_id=blsk.object_id and blsk.etype_id=#{etypeId}
        </if>
        <if test="otypeLimited">
            inner join base_limit_scope blso on blso.profile_id=`o`.profile_id and blso.object_type=3 and
            `o`.otype_id=blso.object_id and blso.etype_id=#{etypeId}
        </if>
    </sql>

    <sql id="financeCheckCalculateInWhere">
        WHERE o.profile_id = #{profileId} AND o.deleted = 0 AND bo.ocategory in (0,1) and o.create_type &lt;&gt; 2 AND
        `o`.order_sale_type != 6
        <if test="beginTime!=null and endTime!=null and timeType==0">
            and o.trade_finish_time BETWEEN #{beginTime} AND #{endTime}
        </if>
        <if test="beginTime!=null and endTime!=null and timeType==1">
            and esoe.platform_send_time BETWEEN #{beginTime} AND #{endTime}
        </if>
        <if test="beginTime!=null and endTime!=null and timeType==2">
            and o.trade_pay_time BETWEEN #{beginTime} AND #{endTime}
        </if>
        <if test="beginTime!=null and endTime!=null and timeType==3">
            and o.create_time BETWEEN #{beginTime} AND #{endTime}
        </if>
        <if test="otypeList != null and otypeList.size()>0">
            AND o.otype_id IN
            <foreach collection="otypeList" close=")" open="(" separator="," item="otypeId">
                #{otypeId}
            </foreach>
        </if>
        <if test="checkStatus !=null and checkStatus == 0">
            and (efcr.bill_check_status is null or efcr.bill_check_diff != 0)
        </if>
        <if test="tradeStatusList != null and tradeStatusList.size() >0">
            AND o.local_trade_state in
            <foreach collection="tradeStatusList" item="status" close=")" open="(" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="checkStatus !=null and checkStatus == 1">
            and (efcr.bill_check_status = 1 and efcr.bill_check_diff = 0)
        </if>
        <if test="checkStatus !=null and checkStatus == 2">
            and (efcr.check_status is null or efcr.check_diff != 0)
        </if>
        <if test="checkStatus !=null and checkStatus == 3">
            and (efcr.check_status = 1 and efcr.check_diff = 0)
        </if>
        <if test="keyWords!=null and keyWords.size()>0 and filterKeyType==1 ">
            AND o.trade_order_id IN
            <foreach collection="keyWords" close=")" open="(" separator="," item="keyWord">
                #{keyWord}
            </foreach>
        </if>
        <if test="markInfo != '' and markInfo != null">
            and esoe.mark_info like CONCAT('%',#{markInfo},'%')
        </if>
        <if test="otypeId != null and otypeId != 0">
            AND o.otype_id = #{otypeId}
        </if>
        <if test="tradeStatus != null and tradeStatus.size() >0">
            AND o.local_trade_state in
            <foreach collection="tradeStatus" item="status" close=")" open="(" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="ids != null and ids.size() >0">
            AND o.id in
            <foreach collection="ids" item="id" close=")" open="(" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="localTradeState != null and localTradeState != 8">
            AND o.local_trade_state = #{localTradeState}
        </if>
        <if test="filterTradeOrderId !=null and filterTradeOrderId !=  '' ">
            AND o.trade_order_id like CONCAT('%',#{filterTradeOrderId},'%')
        </if>
        <if test="btypeName !=null and btypeName !=  '' ">
            AND b.fullname like CONCAT('%',#{btypeName},'%')
        </if>
        <if test="checkName !=null and checkName !=  '' ">
            AND rbe.fullname like CONCAT('%',#{checkName},'%')
        </if>
        <if test="checkTimeStart != null and checkTimEnd != null">
            AND efcr.check_time BETWEEN #{checkTimeStart} AND #{checkTimEnd}
        </if>
        <if test="checkDiff1 != null">
            AND efcr.check_diff &gt;= #{checkDiff1}
        </if>
        <if test="checkDiff2 !=null">
            AND efcr.check_diff &lt;= #{checkDiff2}
        </if>
        <if test="disedTaxedTotal1 != null">
            AND o.`dised_taxed_total` &gt;= #{disedTaxedTotal1}
        </if>
        <if test="disedTaxedTotal2 !=null">
            AND o.`dised_taxed_total` &lt;= #{disedTaxedTotal2}
        </if>
    </sql>

    <sql id="financeCheckCalculateInGroup">
        group by o.trade_order_id, o.otype_id
    </sql>


    <select id="queryFinanceCheckCalculateIn"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.PlatformFinanceCheckRequest"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.PlatformFinanceCheckPageData">
        select SUM(tmp.`dised_taxed_total`) AS disedTaxedTotal,
        SUM(tmp.`order_buyer_freight_fee`) AS orderBuyerFreightFee,
        SUM(tmp.`ptype_service_fee`) AS ptypeServiceFee,
        SUM(if(tmp.refund_type = 5, tmp.refund_apply_service_fee, 0)) as refundApplyServiceFee,
        SUM(if(tmp.refund_type = 5, tmp.refund_apply_taxed_total, 0)) as refundApplyTaxedTotal,
        SUM(if(tmp.refund_type = 5, tmp.refund_apply_freight_fee, 0)) as refundApplyFreightFee,
        COUNT(1) as totalCount
        from (
        SELECT
        tmp1.dised_taxed_total,
        tmp1.order_buyer_freight_fee,
        tmp1.ptype_service_fee,
        tmp2.refund_apply_service_fee,
        tmp2.refund_apply_taxed_total,
        tmp2.refund_apply_freight_fee,
        tmp2.refund_type
        FROM (
        SELECT
        o.trade_order_id,o.otype_id,
        SUM(o.dised_taxed_total) AS dised_taxed_total,
        SUM(o.order_buyer_freight_fee) AS order_buyer_freight_fee,
        SUM(o.ptype_service_fee) AS ptype_service_fee
        FROM ${tableName}
       <include refid="financeCheckCalculateInJoin"/>
        <include refid="financeCheckCalculateInWhere"/>
        <include refid="financeCheckCalculateInGroup"/>
        ) tmp1
        LEFT JOIN (
        SELECT
        o.trade_order_id,o.otype_id,
        SUM(re.refund_apply_service_fee) AS refund_apply_service_fee,
        SUM(re.refund_apply_taxed_total) AS refund_apply_taxed_total,
        SUM(re.refund_apply_freight_fee) AS refund_apply_freight_fee,
        MAX(re.refund_type) AS refund_type
        FROM ${tableName}
        <include refid="financeCheckCalculateInJoin"/>
        LEFT JOIN pl_eshop_refund re on re.profile_id = o.profile_id and re.otype_id = o.otype_id and re.trade_order_id
        = o.trade_order_id and re.deleted=0 and re.refund_state in(1,2,3,5) and re.confirm_state in (1,0) and re.refund_type=5
        <include refid="financeCheckCalculateInWhere"/>
        <if test="keyWords!=null and keyWords.size()>0 and filterKeyType==2 ">
            AND re.trade_refund_order_number IN
            <foreach collection="keyWords" close=")" open="(" separator="," item="keyWord">
                #{keyWord}
            </foreach>
        </if>
        <include refid="financeCheckCalculateInGroup"/>
        )tmp2
        ON tmp1.trade_order_id = tmp2.trade_order_id and tmp1.otype_id = tmp2.otype_id
        )tmp where 1=1
    </select>

    <select id="queryFinanceCheckCalculateOut"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.PlatformFinanceCheckRequest"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.PlatformFinanceCheckPageData">
        select tmp.* from (
        SELECT sum(re.`refund_apply_taxed_total`) as refundApplyTaxedTotal,sum(re.`refund_apply_freight_fee`) as
        refundApplyFreightFee,sum(re.`refund_apply_service_fee`) as refundApplyServiceFee,count(1) as totalCount
        FROM pl_eshop_refund re
        left join pl_eshop_sale_order o on o.profile_id = re.profile_id and o.id = re.eshop_order_id and o.otype_id =
        re.otype_id
        LEFT JOIN pl_eshop_sale_order_extend esoe ON esoe.profile_id = o.profile_id and esoe.eshop_order_id = o.id
        LEFT JOIN `base_otype` bo ON bo.profile_id = o.profile_id AND bo.id = o.otype_id
        LEFT JOIN `base_atype` ba ON ba.profile_id = o.profile_id AND ba.id = bo.atype_id
        LEFT JOIN base_btype b ON b.profile_id = o.profile_id AND b.id = o.btype_id
        LEFT JOIN pl_eshop_finance_check_result efcr ON efcr.profile_id = o.profile_id and efcr.vchcode = re.id
        LEFT JOIN base_etype rbe ON rbe.profile_id = efcr.profile_id and efcr.check_etype_id = rbe.id
        LEFT JOIN td_bill_relation tbr ON tbr.profile_id = re.profile_id and tbr.source_vchcode =
        re.id and tbr.target_vchtype in(4002,4005)
        LEFT JOIN td_bill_core tbc ON tbc.profile_id = re.profile_id and tbc.vchcode = tbr.target_vchcode
        LEFT JOIN acc_bill_core abc ON abc.profile_id = re.profile_id and abc.vchcode = tbr.target_vchcode
        <if test="ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=`o`.profile_id and blsk.object_type=2 and
            `o`.ktype_id=blsk.object_id and blsk.etype_id=#{etypeId}
        </if>
        <if test="otypeLimited">
            inner join base_limit_scope blso on blso.profile_id=`o`.profile_id and blso.object_type=3 and
            `o`.otype_id=blso.object_id and blso.etype_id=#{etypeId}
        </if>
        WHERE o.profile_id = #{profileId} AND re.deleted = 0 and re.refund_state in (1,2,3,5) and re.confirm_state
        in (1, 0) and re.refund_type in(0,1)
        <if test="beginTime!=null and endTime!=null and timeType==0">
            and o.trade_finish_time BETWEEN #{beginTime} AND #{endTime}
        </if>
        <if test="beginTime!=null and endTime!=null and timeType==1">
            and esoe.platform_send_time BETWEEN #{beginTime} AND #{endTime}
        </if>
        <if test="beginTime!=null and endTime!=null and timeType==2">
            and o.trade_pay_time BETWEEN #{beginTime} AND #{endTime}
        </if>
        <if test="beginTime!=null and endTime!=null and timeType==3">
            and o.create_time BETWEEN #{beginTime} AND #{endTime}
        </if>
        <if test="ids != null and ids.size() >0">
            AND re.id in
            <foreach collection="ids" item="id" close=")" open="(" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="otypeList != null and otypeList.size()>0">
            AND o.otype_id IN
            <foreach collection="otypeList" close=")" open="(" separator="," item="otypeId">
                #{otypeId}
            </foreach>
        </if>
        <if test="checkStatus !=null and checkStatus == 0">
            and (efcr.bill_check_status is null or efcr.bill_check_diff != 0)
        </if>
        <if test="tradeStatusList != null and tradeStatusList.size() >0">
            AND o.local_trade_state in
            <foreach collection="tradeStatusList" item="status" close=")" open="(" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="checkStatus !=null and checkStatus == 1">
            and (efcr.bill_check_status = 1 and efcr.bill_check_diff = 0)
        </if>
        <if test="checkStatus !=null and checkStatus == 2">
            and (efcr.check_status is null or efcr.check_diff != 0)
        </if>
        <if test="checkStatus !=null and checkStatus == 3">
            and (efcr.check_status = 1 and efcr.check_diff = 0)
        </if>
        <if test="keyWords!=null and keyWords.size()>0 and filterKeyType==1 ">
            AND o.trade_order_id IN
            <foreach collection="keyWords" close=")" open="(" separator="," item="keyWord">
                #{keyWord}
            </foreach>
        </if>
        <if test="keyWords!=null and keyWords.size()>0 and filterKeyType==2 ">
            AND re.trade_refund_order_number IN
            <foreach collection="keyWords" close=")" open="(" separator="," item="keyWord">
                #{keyWord}
            </foreach>
        </if>
        <if test="markInfo != '' and markInfo != null">
            and esoe.mark_info like CONCAT('%',#{markInfo},'%')
        </if>
        <if test="otypeId != null and otypeId != 0">
            AND o.otype_id = #{otypeId}
        </if>
        <if test="tradeStatus != null and tradeStatus.size() >0">
            AND o.local_trade_state in
            <foreach collection="tradeStatus" item="status" close=")" open="(" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="localTradeState != null and localTradeState != 8">
            AND o.local_trade_state = #{localTradeState}
        </if>
        <if test="filterTradeOrderId !=null and filterTradeOrderId !=  '' ">
            AND o.trade_order_id like CONCAT('%',#{filterTradeOrderId},'%')
        </if>
        <if test="btypeName !=null and btypeName !=  '' ">
            AND b.fullname like CONCAT('%',#{btypeName},'%')
        </if>
        <if test="checkName !=null and checkName !=  '' ">
            AND rbe.fullname like CONCAT('%',#{checkName},'%')
        </if>
        <if test="checkTimeStart != null and checkTimEnd != null">
            AND efcr.check_time BETWEEN #{checkTimeStart} AND #{checkTimEnd}
        </if>

        <if test="checkDiff1 != null">
            AND efcr.check_diff &gt;= #{checkDiff1}
        </if>
        <if test="checkDiff2 !=null">
            AND efcr.check_diff &lt;= #{checkDiff2}
        </if>

        <if test="disedTaxedTotal1 != null">
            AND o.`dised_taxed_total` &gt;= #{disedTaxedTotal1}
        </if>
        <if test="disedTaxedTotal2 !=null">
            AND o.`dised_taxed_total` &lt;= #{disedTaxedTotal2}
        </if>
        ) tmp where 1=1
    </select>

    <select id="queryrefundByTradeId"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.PlatformFinanceCheckRequest"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.PlatformFinanceCheckPageData">
        SELECT
        sum(re.refund_apply_taxed_total) as refundApplyTaxedTotal,
        sum(re.refund_apply_service_fee) as refundApplyServiceFee,
        sum(re.refund_apply_freight_fee) as refundApplyFreightFee
        FROM ${tableName}
        LEFT JOIN pl_eshop_refund re on re.profile_id = o.profile_id and re.otype_id = o.otype_id and re.trade_order_id
        = o.trade_order_id and re.deleted=0 and re.refund_state in(1,2,3,5) and re.confirm_state in (1,0)
        LEFT JOIN `base_otype` bo ON bo.profile_id = o.profile_id AND bo.id = o.otype_id
        <if test="ktypeLimited">
            inner join base_limit_scope blsk on blsk.profile_id=`o`.profile_id and blsk.object_type=2 and
            `o`.ktype_id=blsk.object_id and blsk.etype_id=#{etypeId}
        </if>
        <if test="otypeLimited">
            inner join base_limit_scope blso on blso.profile_id=`o`.profile_id and blso.object_type=3 and
            `o`.otype_id=blso.object_id and blso.etype_id=#{etypeId}
        </if>
        WHERE o.profile_id = #{profileId} AND o.deleted = 0 AND bo.ocategory in (0,1) and o.create_type &lt;&gt; 2
        AND o.trade_order_id= #{keyWord} AND o.otype_id = #{otypeId} and re.refund_type=5
        group by o.trade_order_id
    </select>

    <update id="updatePaymentFlowBillInfo">
        update pl_eshop_payment_flow
        SET
        <if test="auditTime != null">
            `audit_time` = #{auditTime},
        </if>
        <if test="etypeId != null">
            `etype_id` = #{etypeId},
        </if>
        <if test="processState != null">
            `process_state` = #{processState},
        </if>
        `bill_vchcode` = #{billVchcode},
        `bill_vchtype` = #{billVchtype},
        `atype_id` = #{atypeId}
        where profile_id=#{profileId} and otype_id=#{otypeId} and unique_mark=#{uniqueMark}
    </update>

    <update id="batchUpdatePaymentFlowBillInfo">
        <foreach collection="flowList" close="" open="" index="index" separator=";" item="item">
            update pl_eshop_payment_flow
            <set>
                <if test="item.auditTime != null">
                    `audit_time` = #{item.auditTime},
                </if>
                <if test="item.etypeId != null">
                    `etype_id` = #{item.etypeId},
                </if>
                <if test="item.processState != null">
                    `process_state` = #{item.processState},
                </if>
                bill_vchcode=#{item.billVchcode},
                bill_vchtype=#{item.billVchtype},
                atype_id=#{item.atypeId}
            </set>
            where profile_id=#{item.profileId} and otype_id=#{item.otypeId} and unique_mark=#{item.uniqueMark}
        </foreach>
    </update>

    <update id="batchUpdateEntryState">
        update pl_eshop_finance_check_result
        set flow_entry_state = 1
        where profile_id=#{profileId} and vchcode in
        <foreach collection="vchcodes" close=")" open="(" separator="," item="vchcode">
            #{vchcode}
        </foreach>
    </update>

    <select id="querySaleOrderSettlementList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.SaleOrderSettlementEntity">

        SELECT
        `eshop_order_id`,`trade_order_id`,`total`,`taxd_total`,`service_fee`,`freight_fee`,`mall_fee`,`platform_payment_settled_total`,`settled_total`,
        `settled_out_fee`,`settle_status`,`create_time`,`update_time`,`settle_time`,`diff_total`,`etype_id`,`profile_id`,`otype_id`
        FROM `pl_eshop_sale_order_settlement`
        where profile_id = #{profileId}
        <if test="eshopOrderIds!=null and eshopOrderIds.size()>0">
            AND eshop_order_id IN
            <foreach collection="eshopOrderIds" close=")" open="(" separator="," item="eshopOrderId">
                #{eshopOrderId}
            </foreach>
        </if>
    </select>

    <insert id="insertSaleOrderSettlement">
        insert into pl_eshop_sale_order_settlement
        (`eshop_order_id`, `trade_order_id`, `total`, `taxd_total`, `service_fee`, `freight_fee`, `mall_fee`,
         `platform_payment_settled_total`, `settled_total`,
         `settled_out_fee`, `settle_status`, `create_time`, `update_time`, `settle_time`, `diff_total`,
         `etype_id`, `profile_id`, `otype_id`, `check_accounts_status`)
        values (#{eshopOrderId}, #{tradeOrderId}, #{total}, #{taxdTotal}, #{serviceFee}, #{freightFee}, #{mallFee},
                #{platformPaymentSettledTotal}, #{settledTotal}, #{settledOutFee}, #{settleStatus}, #{createTime},
                #{updateTime}, #{settleTime}, #{diffTotal}, #{etypeId}, #{profileId}, #{otypeId},
                #{checkAccountsStatus})
    </insert>

    <update id="updateSaleOrderSettlement">
        update pl_eshop_sale_order_settlement
        SET `total`                          =#{total},
            `taxd_total`                     =#{taxdTotal},
            `service_fee`                    =#{serviceFee},
            `freight_fee`                    =#{freightFee},
            `mall_fee`                       =#{mallFee},
            `platform_payment_settled_total` =#{platformPaymentSettledTotal},
            `settled_total`                  =#{settledTotal},
            `settled_out_fee`                =#{settledOutFee},
            `settle_status`                  =#{settleStatus},
            `create_time`                    =#{createTime},
            `update_time`                    =#{updateTime},
            `settle_time`                    =#{settleTime},
            `diff_total`                     =#{diffTotal}
        where profile_id = #{profileId}
          and eshop_order_id = #{eshopOrderId}
    </update>

    <select id="queryEshopSaleorderListByVchcode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity">
        SELECT `id`,`trade_order_id`,`otype_id`,`profile_id`,`platform_trade_state`,`order_source_type`,`pay_no`,
        `dised_taxed_total`,`total`,`ptype_service_fee`,`order_buyer_freight_fee`,`ktype_id`,`btype_id`,`create_time`
        FROM `pl_eshop_sale_order`
        WHERE profile_id = #{profileId}
        <if test="ids != null and ids.size()>0">
            and id in
            <foreach collection="ids" item="id" separator="," index="i" close=")" open="(">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="queryMallFeeAtypeId" resultType="java.math.BigInteger">
        SELECT id
        FROM `base_atype`
        WHERE profile_id = #{profileId}
          AND typeid = #{typeId}
    </select>


    <select id="queryRefundBillReleation" resultType="java.lang.Integer">
        SELECT per.refund_type
        FROM td_bill_relation perbr
                 left join pl_eshop_refund per on per.id = perbr.source_vchcode
        WHERE perbr.target_vchcode = #{billVchcodes}
          and per.profile_id = #{profileId} limit 1
    </select>

    <select id="queryTradeIdByVchcode" resultType="java.lang.String">
        SELECT trade_order_id
        FROM `pl_eshop_sale_order`
        WHERE profile_id = #{profileId}
          AND id = #{id}
    </select>

    <select id="queryVchcodeByTradeId" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.order.EshopSaleOrderEntity">
        SELECT id, trade_order_id
        FROM `pl_eshop_sale_order`
        WHERE profile_id = #{profileId}
          AND trade_order_id = #{tradeOrderId}
          and otype_id = #{eshopId}
    </select>

    <select id="getEtypeNameById" resultType="java.lang.String">
        select fullname
        from base_etype
        where profile_id = #{profileId}
          and id = #{etypeId} limit 1
    </select>

    <select id="querySaleOrderOutStore" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.response.BillDetailData">
        SELECT bdas.vchcode,
               bdas.detail_id,
               bdas.profile_id,
               bdas.currency_dised_total as total,
               bp.weight,
               bp.weight_unit,
               bdcs.qty,
               bdas.unit_qty             as qty,
               bdcs.business_type,
               bdcs.vchtype
        FROM acc_bill_detail_assinfo_sale bdas
                 LEFT JOIN acc_bill_deliver_state bds ON bds.vchcode = bdas.vchcode and bds.profile_id = bdas.profile_id
                 LEFT JOIN acc_bill_detail_core_sale bdcs
                           ON bdcs.detail_id = bdas.detail_id and bdcs.profile_id = bdas.profile_id
                 LEFT JOIN base_ptype bp ON bp.id = bdcs.ptype_id and bp.profile_id = bdcs.profile_id
        WHERE bdas.profile_id = #{profileId}
          AND bds.preset_mark NOT IN (13, 14, 15)
          AND bdas.vchcode IN (SELECT abd.vchcode
                               FROM acc_bill_detail_deliver abd
                               WHERE profile_id = #{profileId}
                                 AND order_id IN (SELECT DISTINCT eshop_order_id
                                                  FROM pl_eshop_payment_flow
                                                  WHERE profile_id = #{profileId} and bill_vchcode = #{vchcode})
                               union
                               SELECT tbd.vchcode
                               FROM td_bill_detail_deliver tbd
                               WHERE profile_id = #{profileId}
                                 AND order_id IN (SELECT DISTINCT eshop_order_id
                                                  FROM pl_eshop_payment_flow
                                                  WHERE profile_id = #{profileId} and bill_vchcode = #{vchcode}))
        union
        SELECT bdas.vchcode,
               bdas.detail_id,
               bdas.profile_id,
               bdas.currency_dised_total as total,
               bp.weight,
               bp.weight_unit,
               bdcs.qty,
               bdas.unit_qty             as qty,
               bdcs.business_type,
               bdcs.vchtype
        FROM acc_bill_detail_assinfo_sale bdas
                 LEFT JOIN td_bill_deliver_state bds ON bds.vchcode = bdas.vchcode and bds.profile_id = bdas.profile_id
                 LEFT JOIN acc_bill_detail_core_sale bdcs
                           ON bdcs.detail_id = bdas.detail_id and bdcs.profile_id = bdas.profile_id
                 LEFT JOIN base_ptype bp ON bp.id = bdcs.ptype_id and bp.profile_id = bdcs.profile_id
        WHERE bdas.profile_id = #{profileId}
          AND bds.preset_mark NOT IN (13, 14, 15)
          AND bdas.vchcode IN (SELECT tbd.vchcode
                               FROM td_bill_deliver tbd
                               WHERE profile_id = #{profileId}
                                 AND order_id IN (SELECT DISTINCT eshop_order_id
                                                  FROM pl_eshop_payment_flow
                                                  WHERE profile_id = #{profileId} and bill_vchcode = #{vchcode})
                               union
                               SELECT abd.vchcode
                               FROM acc_bill_deliver abd
                               WHERE profile_id = #{profileId}
                                 AND order_id IN (SELECT DISTINCT eshop_order_id
                                                  FROM pl_eshop_payment_flow
                                                  WHERE profile_id = #{profileId} and bill_vchcode = #{vchcode}))
    </select>

    <select id="querySaleOrderOutStoreByRefund"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.response.BillDetailData">
        SELECT bdas.vchcode,bdas.detail_id,bdas.profile_id,bdas.currency_dised_total as
        total,bp.weight,bp.weight_unit,bdcs.qty,bdas.unit_qty as qty,
        bdcs.business_type,bdcs.vchtype
        FROM acc_bill_detail_assinfo_sale bdas
        LEFT JOIN acc_bill_deliver_state bds ON bds.vchcode = bdas.vchcode and bds.profile_id = bdas.profile_id
        LEFT JOIN acc_bill_detail_core_sale bdcs ON bdcs.detail_id = bdas.detail_id and bdcs.profile_id =
        bdas.profile_id
        LEFT JOIN base_ptype bp ON bp.id = bdcs.ptype_id and bp.profile_id = bdcs.profile_id
        WHERE bdas.profile_id = #{profileId}
        AND bds.preset_mark NOT IN(13,14,15)
        AND bdas.vchcode IN(SELECT vchcode FROM acc_bill_deliver WHERE profile_id =#{profileId}
        <if test="tradeIds != null and tradeIds.size()>0">
            and trade_order_id in
            <foreach collection="tradeIds" item="tradeId" separator="," index="i" close=")" open="(">
                #{tradeId})
            </foreach>
        </if>
    </select>

    <select id="queryAtypeData" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.response.AtypeData">
        select atype_id, fee_total as atypeTotal
        from td_bill_balance_eshop_order_fee
        WHERE profile_id = #{profileId}
          and vchcode = #{vchcode}
    </select>

    <select id="queryBillVchcode" resultType="java.math.BigInteger">
        select c.vchcode
        from acc_bill_deliver d
                 left join acc_bill_core c on c.vchcode = d.vchcode
        WHERE d.profile_id = #{profileId}
          and d.order_id = #{vchcode} limit 1
    </select>

    <select id="queryAtypeDataByBillAccount" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.response.AtypeData">
        select atype_id, currency_total as atypeTotal
        from acc_bill_account
        WHERE profile_id = #{profileId}
          and vchcode = #{vchcode}
          and account_detail_type != 1
    </select>

    <insert id="saveBillBalanceEshopOrderFee">
        INSERT INTO `td_bill_balance_eshop_order_fee` (`id`,
                                                       `vchcode`,
                                                       `profile_id`,
                                                       `eshop_order_id`,
                                                       `atype_id`,
                                                       `fee_total`)
        VALUES
        <foreach collection="list" item="item" separator="," >
            (#{item.id},
                #{item.vchcode},
                #{item.profileId},
                #{item.orderId},
                #{item.atypeId},
                #{item.feeTotal})
        </foreach>
    </insert>


    <insert id="saveBillRelation">
        INSERT INTO `td_bill_relation` (`id`,
                                        `profile_id`,
                                        `source_vchcode`,
                                        `target_vchcode`,
                                        `target_vchtype`,
                                        `source_vchtype`,
                                        `target_business_type`,
                                        `source_business_type`)
        VALUES (#{id},
                #{profileId},
                #{sourceVchcode},
                #{targetVchcode},
                #{targetVchtype},
                #{sourceVchtype},
                #{targetBusinessType},
                #{sourceBusinessType})
    </insert>
    <insert id="saveBillRelationBatch">
        INSERT INTO `td_bill_relation` (`id`,
        `profile_id`,
        `source_vchcode`,
        `target_vchcode`,
        `target_vchtype`,
        `source_vchtype`,
        `target_business_type`,
        `source_business_type`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id},
            #{item.profileId},
            #{item.sourceVchcode},
            #{item.targetVchcode},
            #{item.targetVchtype},
            #{item.sourceVchtype},
            #{item.targetBusinessType},
            #{item.sourceBusinessType})
        </foreach>
    </insert>

    <select id="getBusinessType" resultType="java.math.BigInteger">
        select business_type
        from td_bill_core
        where profile_id = #{profileId}
          and vchcode = #{vchcode}
        union all
        select business_type
        from acc_bill_core
        where profile_id = #{profileId}
          and vchcode = #{vchcode}
    </select>

    <insert id="addOrderBillRelation">
        insert into order_bill_relation(id, profile_id, vchcode, billcode, balance)
        values (#{id}, #{profileId}, #{vchcode}, #{billId}, #{total})
    </insert>

    <delete id="DoDeletePaymentNumber">
        DELETE FROM pl_eshop_payment_flow WHERE
        profile_id = #{profileId} and id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="DoDeletecheckResult">
        DELETE FROM pl_eshop_finance_check_result WHERE
        profile_id = #{profileId} and vchcode in
        <foreach collection="vchcodes" item="vchcode" separator="," open="(" close=")">
            #{vchcode}
        </foreach>
    </delete>

    <select id="getCheckResultByWhere" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.EshopFinanceCheckResultEntity">
        select * FROM pl_eshop_finance_check_result WHERE
        profile_id = #{profileId} and check_status = 1 and check_diff &lt;&gt; 0
    </select>

    <update id="updateEshopPaymentFlowProcessState">
        update pl_eshop_payment_flow
        SET `process_state`=#{params.processState},`etype_id`= #{params.etypeId},`audit_time`= #{params.auditTime}
        where profile_id=#{params.profileId}
        and id in
        <foreach collection="params.ids" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </update>

    <update id="updatePaymentFlowProcessStateById">
        update pl_eshop_payment_flow
        SET `process_state`=#{processState}
        where profile_id=#{profileId}
        and id in
        <foreach collection="ids" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </update>

    <update id="updateFlowBillNumber">
        update pl_eshop_payment_flow
        SET `bill_number`=#{billNumber},
            vchcode      = #{billVchcode}
        where profile_id = #{profileId}
          and id = #{id}
    </update>

    <select id="querySaleOrderBillInfo"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.FinanceCheckBillInfo">
        <include refid="acc_bill_info"/>
        union
        <include refid="td_bill_info"/>
        union
        <include refid="refund_info"/>
    </select>

    <select id="queryRefundBillInfo"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.FinanceCheckRefundBillInfo">
        select re.trade_refund_order_number as refundNumber,
        re.refund_type,re.confirm_state,re.refund_state,
        re.create_type,
        sum(re.refund_apply_taxed_total+re.refund_apply_freight_fee+re.refund_apply_service_fee) as
        refundApplyTaxedTotal,
        re.refund_apply_freight_fee,
        re.refund_apply_service_fee
        from pl_eshop_sale_order o
        left join pl_eshop_refund re on re.profile_id = o.profile_id and re.otype_id = o.otype_id and
        re.trade_order_id = o.trade_order_id and re.deleted = 0
        where o.profile_id=#{profileId} and re.refund_state in(1,2,3,4,5) and re.confirm_state in (1,0) and
        re.refund_type &lt;&gt;5
        <if test="ids!=null and ids.size()>0">
            and o.id in
            <foreach collection="ids" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        group by re.trade_refund_order_number
    </select>


    <sql id="acc_bill_info">
        SELECT
        deliver.vchcode AS billVchcode,deliver.create_time AS bill_date,acc.bill_number,acc.vchtype,
        detail.business_type AS bill_business_type ,SUM(-bda.currency_dised_taxed_total) AS total,
        deliver.profile_id,detail.source_vchcode AS id
        FROM acc_bill_deliver deliver
        LEFT JOIN acc_bill_detail_deliver deliverd ON deliverd.vchcode = deliver.vchcode AND deliverd.profile_id =
        deliver.profile_id
        LEFT JOIN acc_bill_detail_core_sale detail ON detail.detail_id = deliverd.detail_id AND detail.profile_id =
        deliverd.profile_id
        LEFT JOIN acc_bill_core acc ON detail.vchcode = acc.vchcode AND detail.profile_id = acc.profile_id
        left join acc_bill_detail_assinfo_sale bda ON bda.detail_id=detail.detail_id AND detail.profile_id =
        bda.profile_id
        WHERE deliver.profile_id = #{profileId} and deliverd.deleted =0 and acc.deleted =0
        AND detail.source_vchcode IN
        <foreach collection="ids" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
        GROUP BY detail.source_vchcode,detail.vchcode
    </sql>

    <sql id="td_bill_info">
        SELECT
        deliver.vchcode AS billVchcode,deliver.create_time AS bill_date,td.bill_number,td.vchtype,
        detail.business_type AS bill_business_type ,SUM(-bda.currency_dised_taxed_total) AS total,
        deliver.profile_id,detail.source_vchcode AS id
        FROM td_bill_deliver deliver
        LEFT JOIN td_bill_detail_deliver deliverd ON deliverd.vchcode = deliver.vchcode AND deliverd.profile_id =
        deliver.profile_id
        LEFT JOIN td_bill_detail_core detail ON detail.detail_id = deliverd.detail_id AND detail.profile_id =
        deliverd.profile_id
        LEFT JOIN td_bill_core td ON detail.vchcode = td.vchcode AND detail.profile_id = td.profile_id
        left join td_bill_detail_assinfo bda ON bda.detail_id=detail.detail_id AND detail.profile_id = bda.profile_id
        WHERE deliver.profile_id = #{profileId} and deliverd.deleted =0 and td.deleted =0
        AND detail.source_vchcode IN
        <foreach collection="ids" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
        GROUP BY detail.source_vchcode,detail.vchcode
    </sql>

    <sql id="refund_info">
        SELECT refund.id as billVchcode,refund.create_time as bill_date,refund.trade_refund_order_number as bill_number,
        2100 as vchtype,
        case refund.business_type
        when 0 then 201
        when 1 then 204
        when 2 then 203
        when 3 then 205
        else refund.business_type
        end as bill_business_type
        ,-refund.refund_apply_taxed_total,
        refund.profile_id,refund.eshop_order_id as id
        FROM pl_eshop_refund refund
        WHERE refund.profile_id = #{profileId} and refund.refund_type in (0,1) and refund.confirm_State =1
        and refund.deleted=0
        AND refund.eshop_order_id in
        <foreach collection="ids" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </sql>


    <sql id="bill_info">
        SELECT detail.vchcode as billVchcode,detail.vchtype,detail.business_type AS
        billBusinessType,detail.dised_taxed_total AS total,
        detail.profile_id,detail.source_vchcode as id,bill.bill_date,bill.bill_number,bill.create_type
        FROM (SELECT vchcode,profile_id,source_vchcode,vchtype,business_type,SUM(-dised_taxed_total) AS
        dised_taxed_total
        FROM acc_bill_detail_core_sale WHERE vchtype in ('2000','2100','4005') AND profile_id = #{profileId}
        AND source_vchcode in
        <foreach collection="ids" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
        GROUP BY source_vchcode,vchtype) detail
        LEFT JOIN acc_bill_core bill ON detail.vchcode = bill.vchcode AND detail.profile_id = bill.profile_id
        union
        SELECT detail.vchcode as billVchcode,detail.vchtype,detail.business_type AS
        billBusinessType,detail.dised_taxed_total AS total,
        detail.profile_id,detail.source_vchcode as id,bill.bill_date,bill.bill_number,bill.create_type
        FROM (SELECT vchcode,profile_id,source_vchcode,vchtype,business_type,SUM(-dised_taxed_total) AS
        dised_taxed_total
        FROM td_bill_detail_core WHERE vchtype in ('2000','2100','4005') AND profile_id = #{profileId}
        AND source_vchcode in
        <foreach collection="ids" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
        GROUP BY source_vchcode,vchtype) detail
        LEFT JOIN td_bill_core bill ON detail.vchcode = bill.vchcode AND detail.profile_id = bill.profile_id
    </sql>


    <select id="querySaleOrderPaymentFlowInfo"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.FinanceCheckPaymentFlowInfo">
        SELECT o.`id`,flow.payment_number,if(flow.in_amount > 0,flow.in_amount,-flow.out_amount) as total,
        case flow.business_type
        when '4096' then 2
        when '16384' then 2
        else 1 end as inOutType,flow.create_time,flow.payment_remark,flow.business_name
        FROM `pl_eshop_sale_order` o
        LEFT JOIN pl_eshop_payment_flow flow ON flow.profile_id = o.profile_id AND flow.eshop_order_id = o.id
        WHERE o.profile_id = #{profileId} and flow.process_state =1
        and flow.business_type in ('4096','8192','8193','8194','9195','8196','16384')
        and o.id in
        <foreach collection="ids" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </select>

    <select id="queryBillInOutInfo"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.FinanceCheckInOutBillInfo">
        select * from (
        SELECT abc.bill_number,abc.vchtype,tbbd.currency_payment_total as
        billTotal,abc.bill_date,abc.memo,abc.summary,o.profile_id
        FROM `pl_eshop_sale_order` o
        LEFT JOIN td_bill_relation tbr on tbr.profile_id = o.profile_id and tbr.source_vchcode = o.id
        LEFT JOIN acc_bill_core abc on abc.profile_id = o.profile_id and abc.vchcode = tbr.target_vchcode
        left join acc_bill_balance_detail tbbd on tbbd.profile_id = o.profile_id and tbbd.vchcode = abc.vchcode and tbbd.business_vchcode = o.id
        WHERE o.profile_id = #{profileId} and abc.vchtype in(4001,4002)
        <if test="ids!=null and ids.size()>0">
            and o.id in
            <foreach collection="ids" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        union
        SELECT tbc.bill_number,tbc.vchtype,tbbd.currency_payment_total as
        billTotal,tbc.bill_date,tbc.memo,tbc.summary,o.profile_id
        FROM `pl_eshop_sale_order` o
        LEFT JOIN td_bill_relation tbr on tbr.profile_id = o.profile_id and tbr.source_vchcode = o.id
        LEFT JOIN td_bill_core tbc on tbc.profile_id = o.profile_id and tbc.vchcode = tbr.target_vchcode
        left join td_bill_balance_detail tbbd on tbbd.profile_id = o.profile_id and tbbd.vchcode = tbc.vchcode and tbbd.business_vchcode = o.id
        WHERE o.profile_id = #{profileId} and tbc.vchtype in(4001,4002)
        <if test="ids!=null and ids.size()>0">
            and o.id in
            <foreach collection="ids" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        union
        SELECT tbc.bill_number,tbc.vchtype,tbc.bill_total,tbc.bill_date,tbc.memo,tbc.summary,o.profile_id
        FROM `pl_eshop_sale_order` o
        LEFT JOIN pl_eshop_refund re on re.profile_id = o.profile_id and re.trade_order_id = o.trade_order_id and
        re.otype_id=o.otype_id
        LEFT JOIN td_bill_relation tbr on tbr.profile_id = o.profile_id and tbr.source_vchcode = re.id
        LEFT JOIN td_bill_core tbc on tbc.profile_id = o.profile_id and tbc.vchcode = tbr.target_vchcode
        WHERE o.profile_id = #{profileId} and tbc.vchtype in(4001,4002,4005)
        <if test="ids!=null and ids.size()>0">
            and o.id in
            <foreach collection="ids" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        union
        SELECT abc.bill_number,abc.vchtype,abc.bill_total,abc.bill_date,abc.memo,abc.summary,o.profile_id
        FROM `pl_eshop_sale_order` o
        LEFT JOIN pl_eshop_refund re on re.profile_id = o.profile_id and re.trade_order_id = o.trade_order_id and
        re.otype_id=o.otype_id
        LEFT JOIN td_bill_relation tbr on tbr.profile_id = o.profile_id and tbr.source_vchcode = re.id
        LEFT JOIN acc_bill_core abc on abc.profile_id = o.profile_id and abc.vchcode = tbr.target_vchcode
        WHERE o.profile_id = #{profileId} and abc.vchtype in(4001,4002,4005)
        <if test="ids!=null and ids.size()>0">
            and o.id in
            <foreach collection="ids" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
         union
        SELECT abc.bill_number,abc.vchtype,abc.bill_total,abc.bill_date,abc.memo,abc.summary,re.profile_id
        FROM pl_eshop_refund re
        LEFT JOIN td_bill_relation tbr on tbr.profile_id = re.profile_id and tbr.source_vchcode = re.id
        LEFT JOIN td_bill_core abc on abc.profile_id = re.profile_id and abc.vchcode = tbr.target_vchcode
        WHERE re.profile_id = #{profileId} and abc.vchtype in(4001,4002,4005)
        <if test="ids!=null and ids.size()>0">
            and re.id in
            <foreach collection="ids" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        union
        SELECT abc.bill_number,abc.vchtype,abc.bill_total,abc.bill_date,abc.memo,abc.summary,re.profile_id
        FROM pl_eshop_refund re
        LEFT JOIN td_bill_relation tbr on tbr.profile_id = re.profile_id and tbr.source_vchcode = re.id
        LEFT JOIN acc_bill_core abc on abc.profile_id = re.profile_id and abc.vchcode = tbr.target_vchcode
        WHERE re.profile_id = #{profileId} and abc.vchtype in(4001,4002,4005)
        <if test="ids!=null and ids.size()>0">
            and re.id in
            <foreach collection="ids" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        ) tmp group by tmp.bill_number
    </select>

    <insert id="insertEshopFinanceCheckResult">
        INSERT INTO pl_eshop_finance_check_result
        (`id`, `profile_id`, `otype_id`, `check_subject`, `vchcode`, `check_time`, `check_status`,
         `need_payment`, `has_payment`, `check_diff`, `fee_total`, `check_etype_id`)
        VALUES (#{id}, #{profileId}, #{otypeId}, #{checkSubject}, #{vchcode}, #{checkTime}, #{checkStatus},
                #{needPayment}, #{hasPayment}, #{checkDiff}, #{feeTotal}, #{checkEtypeId}) ON DUPLICATE KEY
        UPDATE `check_status`=#{checkStatus},
            `need_payment`=#{needPayment},
            `has_payment`=#{hasPayment},
            `check_diff`=#{checkDiff},
            `fee_total`=#{feeTotal},
            `check_etype_id`=#{checkEtypeId}
    </insert>

    <insert id="batchInsertEshopFinanceCheckResult">
        REPLACE INTO pl_eshop_finance_check_result
        (`id`, `profile_id`, `otype_id`, `check_subject`, `vchcode`,`check_time`, `check_status`,
        `need_payment`, `has_payment`, `check_diff`, `fee_total`,
        `check_etype_id`,bill_total,bill_check_diff,bill_check_status,flow_entry_state)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.profileId}, #{item.otypeId}, #{item.checkSubject}, #{item.vchcode},#{item.checkTime},
            #{item.checkStatus},
            #{item.needPayment}, #{item.hasPayment}, #{item.checkDiff}, #{item.feeTotal},
            #{item.checkEtypeId},#{item.billTotal}, #{item.billCheckDiff}, #{item.billCheckStatus},
            #{item.flowEntryState})
        </foreach>
    </insert>
    <insert id="columNameConfig" parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.SysData">
        insert into sys_data (id, profile_id, sub_name, sub_value, description)
        values (#{id}, #{profileId}, #{subName}, #{subValue}, #{description});
    </insert>
    <select id="queryEshopFinanceCheckResult"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.EshopFinanceCheckResultEntity">
        SELECT `id`, `profile_id`, `otype_id`, `check_subject`, `vchcode`,`check_time`, `check_status`,
        `need_payment`, `has_payment`, `check_diff`, `fee_total`, `check_etype_id`
        FROM `pl_eshop_finance_check_result`
        WHERE profile_id = #{profileId}
        and vchcode in
        <foreach collection="vchcodes" close=")" open="(" separator="," item="vchcode">
            #{vchcode}
        </foreach>
    </select>
    <update id="batchUpdateEshopFinanceCheckResult">
        <foreach collection="list" close="" open="" index="index" separator=";" item="item">
            update pl_eshop_finance_check_result
            <set>
                check_time=#{item.checkTime},
                check_status=#{item.checkStatus},
                bill_check_status=#{item.billCheckStatus},
                need_payment=#{item.needPayment},
                has_payment=#{item.hasPayment},
                <if test="item.checkDiff != null">
                    check_diff=#{item.checkDiff},
                </if>
                <if test="item.billCheckDiff != null">
                    bill_check_diff=#{item.billCheckDiff},
                </if>
                fee_total=#{item.feeTotal},
                check_etype_id=#{item.checkEtypeId}
            </set>
            where profile_id=#{item.profileId} and vchcode = #{item.vchcode}
        </foreach>
    </update>

    <update id="updateFlowEntryState">
        update pl_eshop_finance_check_result
        set flow_entry_state=#{flowEntryState}
        where profile_id = #{profileId}
          and vchcode = #{vchcode}
    </update>

    <update id="updateFlowCheckState">
            update pl_eshop_finance_check_result set check_status=#{checkStatus}
            where profile_id=#{profileId} and vchcode = #{vchcode}
    </update>

    <update id="batchUpdateFinanceChange">
        update pl_eshop_finance_check_change
        SET `finished` =#{finished},`finished_time`=now()
        where profile_id=#{profileId}
        and vchcode in
        <foreach collection="vchcodes" close=")" open="(" separator="," item="vchcode">
            #{vchcode}
        </foreach>
    </update>
    <update id="updateEshopPaymentFlowByPayNumber">
        update pl_eshop_payment_flow
        SET
        `eshop_order_id` =#{eshopOrderId},
        `trade_order_id` =#{tradeOrderId},
        `process_state` =#{processState},
        `merchant_order_number`=#{merchantOrderNumber},
        `merchant_payment_account`=#{merchantPaymentAccount},
        `opposite_payment_account`=#{oppositePaymentAccount},
        `platform_business_type`=#{platformBusinessType},
        `platform_business_sub_type`=#{platformBusinessSubType},
        `business_type`=#{businessType},
        `business_name`=#{businessName},
        `in_amount`=#{inAmount},
        `flow_create_time`=#{flowCreateTime},
        `out_amount`=#{outAmount},
        `payment_remark`=#{paymentRemark},
        `update_time`=#{updateTime},
        `bill_vchtype`=#{billVchtype},
        `balance_total`=#{balanceTotal},
        `etype_id` =#{etypeId},
        <if test="btypeId!=null and btypeId!=0">
            `btype_id` =#{btypeId},
        </if>
        `process_state`=#{processState},
        `audit_time`=#{auditTime},
        `payment_mode`=#{paymentMode},
        payway_id=#{paywayId},
        payment_name=#{paymentName},
        bill_check_time=#{billCheckTime},
        entry_state=#{entryState}
        where profile_id=#{profileId} and payment_number=#{paymentNumber};
    </update>
    <update id="updateEshopPaymentFlowForExport">
        update pl_eshop_payment_flow
        SET
        `eshop_order_id` =#{eshopOrderId},
        `trade_order_id` =#{tradeOrderId},
        `process_state` =#{processState},
        `merchant_order_number`=#{merchantOrderNumber},
        `merchant_payment_account`=#{merchantPaymentAccount},
        `opposite_payment_account`=#{oppositePaymentAccount},
        `platform_business_type`=#{platformBusinessType},
        `platform_business_sub_type`=#{platformBusinessSubType},
        `business_type`=#{businessType},
        `business_name`=#{businessName},
        `in_amount`=#{inAmount},
        `flow_create_time`=#{flowCreateTime},
        `out_amount`=#{outAmount},
        `payment_remark`=#{paymentRemark},
        `update_time`=#{updateTime},
        `bill_vchtype`=#{billVchtype},
        `balance_total`=#{balanceTotal},
        `etype_id` =#{etypeId},
        <if test="btypeId!=null and btypeId!=0">
            `btype_id` =#{btypeId},
        </if>
        `process_state`=#{processState},
        `audit_time`=#{auditTime},
        `payment_mode`=#{paymentMode},
        payway_id=#{paywayId},
        payment_name=#{paymentName},
        bill_check_time=#{billCheckTime},
        entry_state=#{entryState}
        where profile_id=#{profileId} and payment_number=#{paymentNumber}
        <if test="paywayId!=null">
            and payway_id=#{paywayId}
        </if>
        <if test="inOutType!=null">
            and in_out_type=#{inOutType}
        </if>
        <if test="inAmount!=null">
            and in_amount=#{inAmount}
        </if>
        <if test="outAmount!=null">
            and out_amount=#{outAmount}
        </if>
    </update>

    <select id="queryAccountsByEshopOrderId"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.PlatformCheckAccountsRequest"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.PlatformCheckAccountsPageData">
        SELECT flow.`id`,
               flow.`payment_number`,
               flow.`merchant_order_number`,
               flow.`business_type`,
               flow.`business_name`,
               flow.`in_amount`,
               flow.`out_amount`,
               flow.`payment_remark`,
               flow.`create_time`,
               flow.`eshop_order_id`,
               flow.trade_order_id,
               flow.`unique_mark`,
               flow.`process_state`,
               flow.`create_type`,
               flow.`audit_time`,
               flow.`annex_url`,
               flow.`etype_id`,
               etype.fullname as 'auditName', ifnull(acc.vchcode, bill.vchcode) as billVchcode,
               ifnull(acc.vchtype, bill.vchtype)                   as billVchtype,
               if(ifnull(acc.vchcode, bill.vchcode) is null, 0, 1) as billStatus,
               ifnull(acc.bill_number, bill.bill_number)           as billNumber,
               ifnull(acc.create_time, bill.create_time)           as billCreateTime
        FROM pl_eshop_payment_flow flow
                 LEFT JOIN base_etype etype ON flow.profile_id = etype.profile_id AND flow.etype_id = etype.id
                 LEFT JOIN acc_bill_core acc
                           ON acc.profile_id = flow.profile_id AND acc.vchcode = flow.bill_vchcode AND acc.deleted = 0
                 LEFT JOIN td_bill_core bill
                           ON bill.profile_id = flow.profile_id AND bill.vchcode = flow.bill_vchcode AND
                              bill.deleted = 0
        WHERE flow.profile_id = #{profileId}
          AND flow.eshop_order_id = #{eshopOrderId}
    </select>

    <select id="queryPaymentFlowCheckStatus"
            resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM pl_eshop_payment_flow flow
        LEFT JOIN acc_bill_core acc ON acc.profile_id = flow.profile_id AND acc.vchcode = flow.bill_vchcode AND
        acc.deleted =0
        LEFT JOIN td_bill_core bill ON bill.profile_id = flow.profile_id AND bill.vchcode = flow.bill_vchcode AND
        bill.deleted =0
        WHERE flow.profile_id = #{profileId} AND (bill.vchcode > 0 OR acc.vchcode > 0)
        AND flow.id IN
        <foreach collection="ids" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </select>

    <select id="queryPaymentFlowByBillNumbers"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.EshopPaymentFlowEntity">
        select flow.*,abc.memo,
        if(flow.in_amount > 0, if(flow.in_amount > flow.out_amount, 1, 0), 0) as inOutType,
        if(flow.in_amount > 0, flow.in_amount, flow.out_amount) as total
        from pl_eshop_payment_flow flow
        left join acc_bill_core abc on abc.profile_id = flow.profile_id and abc.bill_number = flow.bill_number
        where flow.profile_id = #{profileId} and flow.source_type=1
        <if test="billNumber!='' and billNumber!= null">
            and flow.bill_number = #{billNumber}
        </if>
        <if test="paymentNumber!='' and paymentNumber!= null">
            and flow.payment_number = #{paymentNumber}
        </if>
    </select>

    <select id="queryPaymentFlowByMoney"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.EshopPaymentFlowEntity">
        select flow.*
        from pl_eshop_payment_flow flow
        where flow.profile_id = #{profileId}
          and flow.source_type = 1
          and flow.in_amount = #{billTotal}
          and flow.out_amount = 0
          and flow.bill_number = ''
        union
        select flow.*
        from pl_eshop_payment_flow flow
        where flow.profile_id = #{profileId}
          and flow.source_type = 1
          and flow.out_amount = #{billTotal}
          and flow.in_amount = 0
          and flow.bill_number = ''
    </select>

    <select id="queryPaymentFlowById"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.EshopPaymentFlowEntity">
        select flow.*
        from pl_eshop_payment_flow flow
        where flow.profile_id = #{profileId}
          and flow.id = #{id}
    </select>

    <select id="queryhasPayment"
            resultType="java.math.BigDecimal">
        select SUM(de.has_payment)
        from pl_eshop_flow_bill_relation rel
                 left join pl_eshop_bill_receive_detail de
                           on de.profile_id = rel.profile_id and de.vchcode = rel.vchcode
        where rel.profile_id = #{profileId}
          and rel.flow_id = #{flowId}
    </select>

    <select id="queryPaymentFlowNotConanceCheck"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.EshopPaymentFlowEntity">
        select flow.*,abc.memo,
        if(flow.in_amount > 0, if(flow.in_amount > flow.out_amount, 1, 0), 0) as inOutType,
        if(flow.in_amount > 0, flow.in_amount, flow.out_amount) as total
        from pl_eshop_payment_flow flow
        left join acc_bill_core abc on abc.profile_id = flow.profile_id and abc.bill_number = flow.bill_number
        where flow.profile_id = #{profileId} and flow.source_type=1 and flow.bill_number=''
        <if test="billNumber!='' and billNumber!= null">
            and flow.bill_number = #{billNumber}
        </if>
        <if test="paymentNumber!='' and paymentNumber!= null">
            and flow.payment_number = #{paymentNumber}
        </if>
    </select>

    <select id="queryPaymentFlowInfoByPayNumbers"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.FinanceCheckPaymentFlowInfo">
        SELECT flow.`trade_order_id` as id,flow.payment_number,flow.in_amount,flow.out_amount,flow.payway_id,
        case flow.business_type
        when '4096' then 2
        when '16384' then 2
        else 1 end as inOutType,flow.create_time,flow.payment_remark,flow.business_name
        from pl_eshop_payment_flow flow
        WHERE flow.profile_id = #{profileId}
        and flow.payment_number in
        <foreach collection="payNumbers" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </select>

    <select id="checkFlowInfoIsExist"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.FinanceCheckPaymentFlowInfo">
        SELECT flow.id,flow.payment_number,flow.in_amount,flow.out_amount,flow.payway_id,
        case flow.business_type
        when '4096' then 2
        when '16384' then 2
        else 1 end as inOutType,flow.create_time,flow.payment_remark,flow.business_name
        from pl_eshop_payment_flow flow
        WHERE flow.profile_id = #{profileId} and flow.payway_id = #{paywayId} and flow.in_amount = #{total}
        and flow.business_type in ('4096','8192','8193','8194','9195','8196','16384')
        and flow.payment_number in
        <foreach collection="payNumbers" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </select>

    <select id="queryPaymentFlowByPayNumbers"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.FinanceCheckPaymentFlowInfo">
        SELECT flow.`id` as id,flow.payment_number,if(flow.in_out_type =1 ,flow.in_amount,flow.out_amount) as total,flow.entry_state,flow.in_amount,flow.out_amount,flow.bill_check_time,
        case flow.business_type
        when '4096' then 2
        when '16384' then 2
        else 1 end as inOutType,flow.create_time,flow.payment_remark,flow.business_name,flow.atype_id,flow.in_out_type,flow.payway_id
        from pl_eshop_payment_flow flow
        WHERE flow.profile_id = #{profileId}
        and flow.payment_number in
        <foreach collection="payNumbers" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </select>
    <select id="queryOrderBillPaymentFlowInfo"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.FinanceCheckPaymentFlowInfo">
        SELECT bill_number as flowId,(sum(flow.in_amount)-sum(flow.out_amount)) as
        total,flow.create_time,flow.payment_remark,flow.business_name,
        flow.payment_number as paymentNumber
        from pl_eshop_payment_flow flow
        WHERE flow.profile_id = #{profileId}
        <if test="billNumber != null and billNumber!=''">
            and flow.bill_number=#{billNumber}
        </if>
        <if test="paymentNumber != null and paymentNumber!=''">
            and flow.payment_number=#{paymentNumber}
        </if>
        and flow.business_type in ('8193')
    </select>

    <select id="nameDuplicated" resultType="java.lang.Boolean">
        select count(id) > 0
        from sys_data
        where profile_id = #{profileId}
          and sub_name = #{templateName}
    </select>
    <select id="listColumNameConfig" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.SysData"
            parameterType="java.math.BigInteger">
        select * from sys_data where profile_id = #{profileId} and description = '账单流水导入字段配置'
        <if test="id != null">
            and id = #{id}
        </if>
    </select>


    <update id="updateBindFlow">
        update pl_eshop_payment_flow
        set bill_vchcode=0,
            bill_number=''
        WHERE profile_id = #{profileId}
          and id = #{id}
    </update>
    <update id="updateFlowBtype">
        update pl_eshop_payment_flow
        set btype_id=#{billBtypeId}
        where profile_id = #{profileId}
          and id = #{id}
    </update>

    <update id="updateFlowMatchStatus">
        update pl_eshop_payment_flow
        set flow_match_status=#{flowMatchStatus}
        where profile_id = #{profileId}
          and id = #{id}
    </update>

    <update id="updateFlowMatchStatusByVchcode">
        update pl_eshop_payment_flow
        set flow_match_status=#{flowMatchStatus}
        where profile_id = #{profileId}
          and id in (select flow_id
                     from pl_eshop_flow_bill_relation
                     where profile_id = #{profileId}
                       and vchcode = #{vchcode})
    </update>

    <update id="modifyflowInfoByBillVchcode">
        update pl_eshop_payment_flow set btype_id=0,bill_vchcode=0,bill_number='' where  profile_id = #{profileId} and bill_vchcode =#{vchcode}
    </update>

    <delete id="cleanFlowBillInfoByBillVchcode">
        delete from pl_eshop_flow_bill_relation where  profile_id = #{profileId} and vchcode =#{vchcode}
    </delete>

    <select id="queryFlowBillRelationCountByVchcode" resultType="java.lang.Integer">
        select count(*) from pl_eshop_flow_bill_relation where  profile_id = #{profileId} and flow_id =#{flowId}
    </select>
    <select id="getBillAccountCount" resultType="java.lang.Integer">
        select count(*)
        from (select id
              from acc_bill_account
              where profile_id = #{profileId}
                and vchcode = #{vchcode} and pay_out_no !=''
              union
              select id
              from td_bill_account
              where profile_id = #{profileId}
                and vchcode = #{vchcode} and pay_out_no !='') tmp
    </select>

    <select id="queryBill" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.CapitalPaymetFlowResponse">
        select * from (select ac.account_detail_type,ac.id, ac.id as billAccountId,ac.pay_out_no,abc.bill_date as
        createTime,abc.vchtype,abc.business_type,abc.bill_number,abc.vchcode,info.btype_id,b.fullname as
        btypeName,abc.etype_id,e.fullname as etypeName ,ac.payway_id,bp.fullname as paywayName,ac.atype_id,a.fullname as
        atypeName,if(abc.vchtype in(2100,4002,4005,4014),-1 * ac.currency_total,ac.currency_total) as bill_total,abc.summary,ac.memo
        from acc_bill_core abc
        left join acc_bill_account ac on ac.profile_id=abc.profile_id and ac.vchcode=abc.vchcode
        left join acc_bill_balance_info info on info.vchcode=abc.vchcode and info.profile_id=abc.profile_id and
        balance_business_type=0
        left join base_btype b on b.profile_id=abc.profile_id and b.id=info.btype_id
        left join base_atype a on a.profile_id=ac.profile_id and a.id=ac.atype_id
        left join base_etype e on e.profile_id=abc.profile_id and e.id=abc.etype_id
        left join base_payways bp on bp.profile_id=abc.profile_id and bp.id=ac.payway_id
        where abc.profile_id=#{params.profileId} and abc.vchtype in(2000,2100,2200,4001,4002,4005,4014)
        <if test="params.needRelationData==0">
            and ac.id not in(select bill_account_id from pl_eshop_flow_bill_relation where
            profile_id=#{params.profileId})
        </if>
        and abc.vchcode not in(select vchcode from pl_eshop_finance_check_result where profile_id=#{params.profileId}
        and check_status=3)
        <if test="params.vchtypeList !=null and params.vchtypeList.size()>0">
            and abc.vchtype in
            <foreach collection="params.vchtypeList" close=")" open="(" separator="," item="vchtype">
                #{vchtype}
            </foreach>
        </if>
        <if test="params.paymentNumber !=null and params.paymentNumber !=''">
            and ac.pay_out_no=#{params.paymentNumber}
        </if>
        <if test="params.billTotal !=null">
            and ac.currency_total=#{params.billTotal}
        </if>
        <if test="params.paywayId !=null">
            and ac.payway_id=#{params.paywayId}
        </if>
        <if test="params.memo !=null and params.memo !=''">
            and abc.memo=#{params.memo}
        </if>
        <if test="params.billNumber !=null and params.billNumber != ''">
            and abc.bill_number=#{params.billNumber}
        </if>
        <if test="params.vchcode !=null">
            and abc.vchcode=#{params.vchcode}
        </if>
        <if test="params.startDate !=null">
            and abc.bill_date &gt;=#{params.startDate}
        </if>
        <if test="params.endDate !=null">
            and abc.bill_date &lt;=#{params.endDate}
        </if>
        <if test="params.btypeId !=null and params.btypeId !=0">
            and info.btype_id =#{params.btypeId}
        </if>
        <if test="params.atypeId !=null and params.atypeId !=0">
            and (ac.atype_id =#{params.atypeId} or ac.atype_id =0 or ac.atype_id IS NULL)
        </if>
        union
        select tac.account_detail_type, tac.id,tac.id as billAccountId,tac.pay_out_no,tbc.bill_date as
        createTime,tbc.vchtype,tbc.business_type,tbc.bill_number,tbc.vchcode,info.btype_id,b.fullname as
        btypeName,tbc.etype_id,e.fullname as etypeName,tac.payway_id,bp.fullname as paywayName,tac.atype_id,a.fullname
        as atypeName,if(tbc.vchtype in(2100,4002,4005,4014),-1 * tac.currency_total,tac.currency_total) as bill_total,tbc.summary,tac.memo
        from td_bill_core tbc
        left join td_bill_account tac on tac.profile_id=tbc.profile_id and tac.vchcode=tbc.vchcode
        left join acc_bill_balance_info info on info.vchcode=tbc.vchcode and info.profile_id=tbc.profile_id
        left join base_btype b on b.profile_id=tbc.profile_id and b.id=tbc.btype_id
        left join base_atype a on a.profile_id=tac.profile_id and a.id=tac.atype_id
        left join base_etype e on e.profile_id=tbc.profile_id and e.id=tbc.etype_id
        left join base_payways bp on bp.profile_id=tbc.profile_id and bp.id=tac.payway_id
        where tbc.profile_id=#{params.profileId} and tbc.vchtype in(2000,2100,2200,4001,4002,4005,4014)
        <if test="params.needRelationData==0">
            and tac.id not in(select bill_account_id from pl_eshop_flow_bill_relation where
            profile_id=#{params.profileId})
        </if>
        and tbc.vchcode not in(select vchcode from pl_eshop_finance_check_result where profile_id=#{params.profileId}
        and check_status=3)
        <if test="params.vchtypeList !=null and params.vchtypeList.size()>0">
            and tbc.vchtype in
            <foreach collection="params.vchtypeList" close=")" open="(" separator="," item="vchtype">
                #{vchtype}
            </foreach>
        </if>
        <if test="params.paymentNumber !=null and params.paymentNumber !=''">
            and tac.pay_out_no=#{params.paymentNumber}
        </if>
        <if test="params.paywayId !=null">
            and tac.payway_id=#{params.paywayId}
        </if>
        <if test="params.billTotal !=null">
            and tac.currency_total=#{params.billTotal}
        </if>
        <if test="params.memo !=null and params.memo !=''">
            and tbc.memo=#{params.memo}
        </if>
        <if test="params.billNumber !=null and params.billNumber !=''">
            and tbc.bill_number=#{params.billNumber}
        </if>
        <if test="params.vchcode !=null">
            and tbc.vchcode=#{params.vchcode}
        </if>
        <if test="params.startDate !=null">
            and tbc.bill_date &gt;=#{params.startDate}
        </if>
        <if test="params.endDate !=null">
            and tbc.bill_date &lt;=#{params.endDate}
        </if>
        <if test="params.btypeId !=null and params.btypeId !=0">
            and info.btype_id =#{params.btypeId}
        </if>
        <if test="params.atypeId !=null and params.atypeId !=0">
            and (tac.atype_id =#{params.atypeId} or tac.atype_id=0 or tac.atype_id IS NULL)
        </if>)tmp group by tmp.vchcode,tmp.billAccountId

    </select>

    <select id="queryCapitalPaymentFlow"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.CapitalPaymetFlowResponse">
        select * from (select flow.id,
        flow.create_type,
        flow.pay_platform,
        flow.user_pay_way,
        flow.origin_payment_number,
        flow.entry_state,
        flow.process_state,
        flow.profile_id,
        flow.create_time,
        flow.flow_create_time,
        flow.update_time,
        flow.payment_number,
        flow.btype_id,
        flow.otype_id,
        flow.atype_id,
        etype.fullname as etypeName,
        flow.payment_name,
        bp.fullname as payment_mode,
        flow.in_out_type,
        flow.payway_id,
        bat.id as atypeId,
        bat.fullname as atypeName,
        bbt.fullname as btypeName,
        flow.in_amount,
        flow.out_amount,
        flow.payment_remark,
        flow.flow_match_status,
        flow.bill_check_time ,
        '' as memo,
        ot.fullname as otypeName,
        ifnull(info.id,info1.id) as billAccountId,
        GROUP_CONCAT(ifnull(abc.bill_number, tbc.bill_number)) as billNumber,
        GROUP_CONCAT(ifnull(abc.vchtype,tbc.vchtype))as vchtypes,
        sum((ifnull(if(ifnull(abc.vchtype,tbc.vchtype)=2100,info.currency_total,info.currency_total), if(ifnull(abc.vchtype,tbc.vchtype)=2100,info1.currency_total,info1.currency_total)))) as billTotal,
        GROUP_CONCAT(ifnull(abc.vchcode,tbc.vchcode)) as vchcodes
        from pl_eshop_payment_flow flow
        left join base_etype etype on etype.profile_id= flow.profile_id and etype.id=flow.etype_id
        left join pl_eshop_flow_bill_relation rel
        on rel.profile_id = flow.profile_id and rel.flow_id = flow.id
        left join base_otype ot
        on ot.profile_id = flow.profile_id and ot.id = flow.otype_id
        left join base_payways bp on bp.profile_id = flow.profile_id and bp.id=flow.payway_id
        left join base_atype bat
        on bat.profile_id = flow.profile_id and bat.id = flow.atype_id
        left join base_btype bbt
        on bbt.profile_id = flow.profile_id and bbt.id = flow.btype_id
        left join acc_bill_core abc on abc.profile_id = flow.profile_id and abc.vchcode = rel.vchcode
        left join td_bill_core tbc on tbc.profile_id = flow.profile_id and tbc.vchcode = rel.vchcode
        left join acc_bill_account info on info.profile_id = flow.profile_id and info.vchcode = rel.vchcode and info.id
        = rel.bill_account_id
        left join td_bill_account info1 on info1.profile_id = flow.profile_id and info1.vchcode = rel.vchcode and
        info1.id = rel.bill_account_id
        where flow.profile_id = #{queryParams.profileId}
        and flow.source_type = 1  group by flow.id,flow.atype_id)tmp
        where tmp.profile_id = #{queryParams.profileId}
        <if test="queryParams.createTime !=null">
            and tmp.flow_create_time &gt;= #{queryParams.createTime} and tmp.flow_create_time &lt;= #{queryParams.createTimeEnd}
        </if>
        <if test="queryParams.billCheckTime !=null">
            and tmp.bill_check_time &gt;= #{queryParams.billCheckTime}
        </if>
        <if test="queryParams.billCheckTimeEnd !=null">
            and tmp.bill_check_time &lt;= #{queryParams.billCheckTimeEnd}
        </if>
        <if test="queryParams.entryState !=null">
            and tmp.entry_state= #{queryParams.entryState}
        </if>
        <if test="queryParams.flowMatchStatus !=null and queryParams.flowMatchStatus !=0 ">
            and tmp.flow_match_status= #{queryParams.flowMatchStatus}
        </if>
        <if test="queryParams.flowMatchStatusFilter !=null ">
            and tmp.flow_match_status= #{queryParams.flowMatchStatusFilter}
        </if>
        <if test="queryParams.etypeName !=null">
            and tmp.etypeName= #{queryParams.etypeName}
        </if>
        <if test="queryParams.filterentryState !=null">
            and tmp.entry_state= #{queryParams.filterentryState}
        </if>
        <if test="queryParams.paymentName !=null">
            and tmp.payment_name like CONCAT('%',#{queryParams.paymentName},'%')
        </if>
        <if test="queryParams.billTotalEnd !=null">
            and tmp.billTotal &lt;= #{queryParams.billTotalEnd}
        </if>
        <if test="queryParams.billTotal !=null">
            and tmp.billTotal &gt;= #{queryParams.billTotal}
        </if>
        <if test="queryParams.inAmountEnd !=null">
            and tmp.in_amount &lt;= #{queryParams.inAmountEnd}
        </if>
        <if test="queryParams.inAmount !=null">
            and tmp.in_amount &gt;= #{queryParams.inAmount}
        </if>
        <if test="queryParams.outAmountEnd !=null">
            and tmp.out_amount &lt;= #{queryParams.outAmountEnd}
        </if>
        <if test="queryParams.outAmount !=null">
            and tmp.out_amount &gt;= #{queryParams.outAmount}
        </if>
        <if test="queryParams.vchtype !=null">
            and tmp.vchtype= #{queryParams.vchtype}
        </if>
        <if test="queryParams.billNumber !=null and queryParams.billNumber !=''">
            and tmp.billNumber like CONCAT('%',#{queryParams.billNumber},'%')
        </if>
        <if test="queryParams.paymentNumber !=null">
            and tmp.payment_number like CONCAT('%',#{queryParams.paymentNumber},'%')
        </if>
        <if test="queryParams.paymentRemark !=null">
            and tmp.payment_remark like CONCAT('%',#{queryParams.paymentRemark},'%')
        </if>
        <if test="queryParams.startDate !=null">
            and tmp.create_time &gt;= #{queryParams.startDate}
        </if>
        <if test="queryParams.endDate !=null">
            and tmp.create_time &lt;= #{queryParams.endDate}
        </if>
        <if test="queryParams.atypeId !=null">
            and tmp.atypeId = #{queryParams.atypeId}
        </if>
        <if test="queryParams.filteratypeName !=null and queryParams.filteratypeName != ''">
            and tmp.atypeName like CONCAT('%',#{queryParams.filteratypeName},'%')
        </if>
        <if test="queryParams.paymentMode !=null">
            and tmp.payment_mode like CONCAT('%',#{queryParams.paymentMode},'%')
        </if>
        <if test="queryParams.paywayId !=null">
            and tmp.payway_id= #{queryParams.paywayId}
        </if>
        <if test="queryParams.btypeId !=null">
            and tmp.btype_id = #{queryParams.btypeId}
        </if>
        <if test="queryParams.btypeName !=null">
            and tmp.btypeName = #{queryParams.btypeName}
        </if>
        <if test="queryParams.otypeId !=null">
            and tmp.otype_id = #{queryParams.otypeId}
        </if>
        <if test="queryParams.processState !=null and queryParams.processState ==-1">
            and tmp.process_state = #{queryParams.processState}
        </if>
        <if test="queryParams.processState !=null and queryParams.processState !=-1">
            and tmp.process_state in (0,1)
        </if>
        <if test="queryParams.filterProcessState !=null and queryParams.filterProcessState ==-1">
            and tmp.process_state = #{queryParams.filterProcessState}
        </if>
        <if test="queryParams.processState !=null and queryParams.processState !=-1">
            and tmp.process_state in (0,1)
        </if>
        <if test="queryParams.accountingTypeName !=null">
            and tmp.in_out_type = #{queryParams.accountingTypeName}
        </if>
        <if test="queryParams.createType !=null">
            and tmp.create_type = #{queryParams.createType}
        </if>

    </select>

    <select id="queryFlowBytime"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.CapitalPaymetFlowResponse">
        select flow.id,
               flow.entry_state,
               flow.process_state,
               flow.create_time,
               flow.payment_number,
               flow.btype_id,
               abc.btype_id as billBtypeId,
               flow.otype_id,
               flow.atype_id,
               flow.payment_name,
               flow.payment_mode,
               flow.payway_id,
               flow.in_amount,
               flow.out_amount,
               flow.payment_remark,
               ''           as memo,
               abc.vchcode,
               abc.vchtype,
               abc.bill_total
        from pl_eshop_payment_flow flow
                 left join acc_bill_account aba
                           on aba.pay_order_no = flow.payment_number and aba.payway_id = flow.payway_id
                 left join acc_bill_core abc on abc.profile_id = flow.profile_id and abc.vchcode = aba.vchcode
        where flow.profile_id = #{profileId}
          and source_type = 1
        union
        select flowp.id,
               flowp.entry_state,
               flowp.process_state,
               flowp.create_time,
               flowp.payment_number,
               flowp.btype_id,
               tbc.btype_id as billBtypeId,
               flowp.otype_id,
               flowp.atype_id,
               flowp.payment_name,
               flowp.payment_mode,
               flowp.payway_id,
               flowp.in_amount,
               flowp.out_amount,
               flowp.payment_remark,
               ''           as memo,
               tbc.vchcode,
               tbc.vchtype,
               tbc.bill_total
        from pl_eshop_payment_flow flowp
                 left join td_bill_account tba
                           on tba.pay_order_no = flowp.payment_number and tba.payway_id = flowp.payway_id
                 left join td_bill_core tbc on tbc.profile_id = flowp.profile_id and tbc.vchcode = tba.vchcode
        where flowp.profile_id = #{profileId}
          and source_type = 1
    </select>

    <select id="queryAtypeIdByPaywayId" resultType="java.math.BigInteger">
        select atype_id
        from base_payways
        where profile_id = #{profileId}
          and id = #{paywayId}
    </select>
    <select id="queryPaymentFlowId" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.EshopPaymentFlowEntity">
        select flow.id,entry_state,out_amount,in_amount,flow.in_out_type,flow.payment_number
        FROM pl_eshop_payment_flow flow
        where flow.profile_id = #{profileId}
          and flow.atype_id = #{atypeId}
          and flow.payment_number = #{payOutNo}
          and flow.payway_id = #{paywayId}
          and flow.id not in (select flow_id from pl_eshop_flow_bill_relation where profile_id = #{profileId}) limit 1
    </select>


    <select id="queryPaymentFlowIdByNum"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.EshopPaymentFlowEntity">
        select flow.id, entry_state
        FROM pl_eshop_payment_flow flow
        where flow.profile_id = #{profileId}
          and flow.atype_id = #{atypeId}
          and flow.payment_number = #{payOutNo}
          and flow.id in (select flow_id from pl_eshop_flow_bill_relation where profile_id = #{profileId})
    </select>

    <select id="queryOpscapByVchcode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.CapitalPaymetFlowResponse">
        select * from (
        select
        GROUP_CONCAT(bd.fullname) as department,
        GROUP_CONCAT(oc.account_name) as accountName,
        GROUP_CONCAT(bp.fullname) as ptypeName
        FROM acc_bill_core abc
        left join acc_bill_assinfo aba on aba.profile_id= abc.profile_id and aba.vchcode = abc.vchcode
        left join ops_cpa oc on oc.profile_id= abc.profile_id and oc.id =(CASE aba.product_account_id WHEN 0 THEN
        aba.refund_account_id ELSE aba.product_account_id END )
        left join base_ptype bp on bp.profile_id = abc.profile_id and bp.id = oc.ptype_id
        left join base_dtype bd on bp.profile_id = abc.profile_id and bd.id = abc.dtype_id
        left join ops_customer_source ocs on ocs.profile_id = abc.profile_id and oc.otype_id = ocs.id
        where abc.profile_id = #{profileId}
        <if test="vchcodesList!=null and vchcodesList.size()>0">
            and abc.vchcode in
            <foreach collection="vchcodesList" item="vchcode" close=")" open="(" separator=",">
                #{vchcode}
            </foreach>
        </if>
        group by abc.profile_id
        union
        select
        GROUP_CONCAT(bd.fullname) as department,
        GROUP_CONCAT(oc.account_name) as accountName,
        GROUP_CONCAT(bp.fullname) as ptypeName
        FROM td_bill_core tbc
        left join td_bill_assinfo aba on aba.profile_id= tbc.profile_id and aba.vchcode = tbc.vchcode
        left join ops_cpa oc on oc.profile_id= tbc.profile_id and oc.id =(CASE aba.product_account_id WHEN 0 THEN
        aba.refund_account_id ELSE aba.product_account_id END )
        left join base_ptype bp on bp.profile_id = tbc.profile_id and bp.id = oc.ptype_id
        left join base_dtype bd on bp.profile_id = tbc.profile_id and bd.id = tbc.dtype_id
        left join ops_customer_source ocs on ocs.profile_id = tbc.profile_id and oc.otype_id = ocs.id
        where tbc.profile_id = #{profileId}
        <if test="vchcodesList!=null and vchcodesList.size()>0">
            and tbc.vchcode in
            <foreach collection="vchcodesList" item="vchcode" close=")" open="(" separator=",">
                #{vchcode}
            </foreach>
        </if>
        group by tbc.profile_id)tmp limit 1
    </select>
    <select id="querySKBillTotalMap" resultType="java.util.Map">
        select id,bill_total
        from (select o.id,sum(tbbd.currency_payment_total) as bill_total
              from pl_eshop_sale_order o
                       LEFT JOIN td_bill_relation tbr on tbr.profile_id = o.profile_id and tbr.source_vchcode = o.id and
                                                         tbr.target_vchtype in (4001, 4002)
                       left join td_bill_core tbc on tbc.profile_id = o.profile_id and tbc.vchcode = tbr.target_vchcode
                       left join td_bill_balance_detail tbbd
                                 on tbbd.profile_id = o.profile_id and tbbd.vchcode = tbc.vchcode and
                                    tbbd.business_vchcode = o.id
              where o.profile_id = #{profileId}
                and o.id in
              <foreach collection="ids" item="id" close=")" open="(" separator=",">
                    #{id}
                </foreach>
                group by o.id
              union
              select o.id,sum(tbbd.currency_payment_total) as bill_total
              from pl_eshop_sale_order o
                       LEFT JOIN td_bill_relation tbr on tbr.profile_id = o.profile_id and tbr.source_vchcode = o.id and
                                                         tbr.target_vchtype in (4001, 4002)
                       left join acc_bill_core abc on abc.profile_id = o.profile_id and abc.vchcode = tbr.target_vchcode
                       left join acc_bill_balance_detail tbbd
                                 on tbbd.profile_id = o.profile_id and tbbd.vchcode = abc.vchcode and
                                    tbbd.business_vchcode = o.id
              where o.profile_id = #{profileId}
                and o.id in
            <foreach collection="ids" item="id" close=")" open="(" separator=",">
                #{id}
            </foreach>
              group by o.id
              ) tmp
    </select>
    <select id="queryAdvanceReceiveTotalList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.BillTotalBaseEntity">
        select deliverd.trade_order_id as orderNumber,ifnull(SUM(-tbda.currency_dised_taxed_total),0)  as total,deliverd.order_id as order_id
        FROM acc_bill_detail_deliver deliverd
        left join acc_bill_detail_assinfo_sale tbda
        ON tbda.detail_id = deliverd.detail_id AND tbda.profile_id = deliverd.profile_id
        WHERE deliverd.profile_id = #{profileId}
        and deliverd.deleted = 0
        and deliverd.trade_order_id in
        <foreach collection="tradeIds" item="tradeOrderId" close=")" open="(" separator=",">
            #{tradeOrderId}
        </foreach>
        GROUP BY deliverd.trade_order_id,deliverd.order_id
    </select>

    <select id="queryAccNeedReceiveTotalList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.BillTotalBaseEntity">
        select tmp.order_id as orderId ,tmp.trade_order_id as orderNumber,ifnull(currency_dised_taxed_total,0) as total
        from (select SUM(-tbda.currency_dised_taxed_total) as currency_dised_taxed_total,deliverd.order_id,deliverd.trade_order_id
              FROM acc_bill_detail_deliver deliverd
                       left join acc_bill_detail_assinfo_sale tbda
                                 ON tbda.detail_id = deliverd.detail_id AND tbda.profile_id = deliverd.profile_id
              WHERE deliverd.profile_id = #{profileId}
                and deliverd.deleted = 0
                and
                  deliverd.order_id in
        <foreach collection="ids" item="orderId" close=")" open="(" separator=",">
            #{orderId}
        </foreach>

              GROUP BY deliverd.order_id
              union
        select SUM(-tbda.currency_dised_taxed_total) as currency_dised_taxed_total,deliverd.order_id,deliverd.trade_order_id
        FROM acc_bill_detail_deliver deliverd
        left join acc_bill_detail_assinfo_sale tbda
        ON tbda.detail_id = deliverd.detail_id AND tbda.profile_id = deliverd.profile_id
        WHERE deliverd.profile_id = #{profileId}
        and deliverd.deleted = 0
        and
        deliverd.trade_order_id in
        <foreach collection="tradeOrderIds" item="tradeOrderId" close=")" open="(" separator=",">
            #{tradeOrderId}
        </foreach>
              GROUP BY deliverd.order_id) tmp
    </select>
    <select id="queryrefundByTradeIdList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.PlatformFinanceCheckPageData">
        SELECT o.trade_order_id,o.otype_id,
        sum(re.refund_apply_taxed_total) as refundApplyTaxedTotal,
        sum(re.refund_apply_service_fee) as refundApplyServiceFee,
        sum(re.refund_apply_freight_fee) as refundApplyFreightFee
        FROM ${tableName}
        LEFT JOIN pl_eshop_refund re on re.profile_id = o.profile_id and re.otype_id = o.otype_id and re.trade_order_id
        = o.trade_order_id and re.deleted=0 and re.refund_state in(1,2,3,5) and re.confirm_state in (1,0)
        LEFT JOIN `base_otype` bo ON bo.profile_id = o.profile_id AND bo.id = o.otype_id
        WHERE o.profile_id = #{profileId} AND o.deleted = 0 AND bo.ocategory in (0,1) and o.create_type &lt;&gt; 2
        AND o.trade_order_id in
         <foreach collection="id" item="tradeOrderId" close=")" open="(" separator=",">
            #{tradeOrderId}
        </foreach>
         AND o.otype_id in
          <foreach collection="otypeList" item="otypeId" close=")" open="(" separator=",">
              #{otypeId}
          </foreach>
          and re.refund_type=5
        group by o.otype_id,o.trade_order_id
    </select>

    <select id="querySKBillTotalList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.platformcheck.BillTotalBaseEntity">
        select id as orderId,ifnull(bill_total,0) as total
        from (select o.id,sum(tbbd.currency_payment_total) as bill_total
        from pl_eshop_sale_order o
        LEFT JOIN td_bill_relation tbr on tbr.profile_id = o.profile_id and tbr.source_vchcode = o.id and
        tbr.target_vchtype in (4001, 4002)
        left join td_bill_core tbc on tbc.profile_id = o.profile_id and tbc.vchcode = tbr.target_vchcode
        left join td_bill_balance_detail tbbd
        on tbbd.profile_id = o.profile_id and tbbd.vchcode = tbc.vchcode and
        tbbd.business_vchcode = o.id
        where o.profile_id = #{profileId}
        and o.id in
        <foreach collection="ids" item="id" close=")" open="(" separator=",">
            #{id}
        </foreach>
        group by o.id
        union
        select o.id,sum(tbbd.currency_payment_total) as bill_total
        from pl_eshop_sale_order o
        LEFT JOIN td_bill_relation tbr on tbr.profile_id = o.profile_id and tbr.source_vchcode = o.id and
        tbr.target_vchtype in (4001, 4002)
        left join acc_bill_core abc on abc.profile_id = o.profile_id and abc.vchcode = tbr.target_vchcode
        left join acc_bill_balance_detail tbbd
        on tbbd.profile_id = o.profile_id and tbbd.vchcode = abc.vchcode and
        tbbd.business_vchcode = o.id
        where o.profile_id = #{profileId}
        and o.id in
        <foreach collection="ids" item="id" close=")" open="(" separator=",">
            #{id}
        </foreach>
        group by o.id
        ) tmp
    </select>
    <select id="listVchcodesByTradeIdsUnSettled" resultType="com.wsgjp.ct.bill.core.handle.entity.BillEntity">
        select d.vchcode,core.btype_id,core.pay_btype_id from
        acc_bill_detail_deliver d
        left join acc_bill_core core on core.vchcode=d.vchcode and d.profile_id=core.profile_id
        left join acc_bill_balance_detail ad  on d.profile_id=ad.profile_id and d.vchcode=ad.business_vchcode  and ad.balance_business_type=0
        where
        d.profile_id=#{profileId}
        and ad.vchcode is null
        and core.otype_id=#{otypeId}
        and d.trade_order_id in
        <foreach collection="tradeIds" item="tradeOrderId" close=")" open="(" separator=",">
            #{tradeOrderId}
        </foreach>
        group by d.vchcode
    </select>

    <select id="querySaleOrderGatherInfo"
            resultType="com.wsgjp.ct.sale.biz.jarvis.dto.deliverbillupdate.SaleOrderSelectDeliverBillResponse">
        select * from (
        SELECT abc.vchcode,o.id as OrderId,o.trade_order_id,abc.bill_number,abc.vchtype,tbbd.currency_payment_total as
        billTotal,abc.bill_date,abc.memo,abc.summary,o.profile_id,
        case
        when abc.post_state = 0 then '草稿'
        when abc.post_state = 100 then '待审核'
        when abc.post_state = 300 then '已审核'
        when abc.post_state = 500 then '待出入库'
        when abc.post_state = 550 then '部分出入库'
        when abc.post_state = 600 then '出入库完成'
        when abc.post_state = 650 then '财务核算失败'
        when abc.post_state = 651 then '财务核算失败已处理'
        when abc.post_state = 700 then '账面库存已核算'
        when abc.post_state = 800 then '财务核算完成'
        else '' end as fullLinkLinkShow
        FROM `pl_eshop_sale_order` o
        LEFT JOIN td_bill_relation tbr on tbr.profile_id = o.profile_id and tbr.source_vchcode = o.id
        LEFT JOIN acc_bill_core abc on abc.profile_id = o.profile_id and abc.vchcode = tbr.target_vchcode
        left join acc_bill_balance_detail tbbd on tbbd.profile_id = o.profile_id and tbbd.vchcode = abc.vchcode and tbbd.business_vchcode = o.id
        WHERE o.profile_id = #{profileId} and abc.vchtype in(4001,4002)
        <if test="ids!=null and ids.size()>0">
            and o.id in
            <foreach collection="ids" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        union
        SELECT tbc.vchcode,o.id as OrderId,o.trade_order_id,tbc.bill_number,tbc.vchtype,tbbd.currency_payment_total as
        billTotal,tbc.bill_date,tbc.memo,tbc.summary,o.profile_id,
        case
        when tbc.post_state = 0 then '草稿'
        when tbc.post_state = 100 then '待审核'
        when tbc.post_state = 300 then '已审核'
        when tbc.post_state = 500 then '待出入库'
        when tbc.post_state = 550 then '部分出入库'
        when tbc.post_state = 600 then '出入库完成'
        when tbc.post_state = 650 then '财务核算失败'
        when tbc.post_state = 651 then '财务核算失败已处理'
        when tbc.post_state = 700 then '账面库存已核算'
        when tbc.post_state = 800 then '财务核算完成'
        else '' end as fullLinkLinkShow
        FROM `pl_eshop_sale_order` o
        LEFT JOIN td_bill_relation tbr on tbr.profile_id = o.profile_id and tbr.source_vchcode = o.id
        LEFT JOIN td_bill_core tbc on tbc.profile_id = o.profile_id and tbc.vchcode = tbr.target_vchcode
        left join td_bill_balance_detail tbbd on tbbd.profile_id = o.profile_id and tbbd.vchcode = tbc.vchcode and tbbd.business_vchcode = o.id
        WHERE o.profile_id = #{profileId} and tbc.vchtype in(4001,4002)
        <if test="ids!=null and ids.size()>0">
            and o.id in
            <foreach collection="ids" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        ) tmp group by tmp.bill_number
    </select>
    <select id="getSaleOrderGatherInfoByImport" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.request.order.EshopSaleOrderGatherInfo">
        <if test="tradeIds!=null and tradeIds.size()>0">
            select o.id,o.trade_order_id,e.atype_id,o.otype_id,o.btype_id,o.dised_taxed_total,o.order_buyer_freight_fee,
                   o.ptype_service_fee,esoe.national_subsidy_total,esoe.gathered_total,o.pay_no,'' as refundNumber,o.deleted,
                   esoe.gather_status
            FROM `pl_eshop_sale_order` o
            LEFT JOIN pl_eshop_sale_order_extend esoe ON esoe.profile_id = o.profile_id and esoe.eshop_order_id = o.id
            left join base_otype e on o.profile_id = e.profile_id and  o.otype_id = e.id
            WHERE o.profile_id = #{profileId} and o.otype_id = #{otypeId}
            and o.trade_order_id in
            <foreach collection="tradeIds" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="payNos!=null and payNos.size()>0 ">
            <if test="tradeIds!=null and tradeIds.size()>0">
                union
            </if>
            select o.id,o.trade_order_id,e.atype_id,o.otype_id,o.btype_id,o.dised_taxed_total,o.order_buyer_freight_fee,
            o.ptype_service_fee,esoe.national_subsidy_total,esoe.gathered_total,o.pay_no,'' as refundNumber,o.deleted,
            esoe.gather_status
            FROM `pl_eshop_sale_order` o
            LEFT JOIN pl_eshop_sale_order_extend esoe ON esoe.profile_id = o.profile_id and esoe.eshop_order_id = o.id
            left join base_otype e on o.profile_id = e.profile_id and  o.otype_id = e.id
            WHERE o.profile_id = #{profileId} and o.otype_id = #{otypeId}
            and o.pay_no in
            <foreach collection="payNos" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
        <if test="refundNumbers!=null and refundNumbers.size()>0">
            <if test="(tradeIds!=null and tradeIds.size()>0) or (payNos!=null and payNos.size()>0)">
                union
            </if>
            select o.id,o.trade_order_id,e.atype_id,o.otype_id,o.btype_id,o.dised_taxed_total,o.order_buyer_freight_fee,
            o.ptype_service_fee,esoe.national_subsidy_total,esoe.gathered_total,'' as payNo,
            refund.trade_refund_order_number as refundNumber,o.deleted,
            esoe.gather_status
            FROM `pl_eshop_refund` refund
            LEFT JOIN pl_eshop_sale_order o ON refund.profile_id = o.profile_id and refund.eshop_order_id = o.id
            LEFT JOIN pl_eshop_sale_order_extend esoe ON esoe.profile_id = o.profile_id and esoe.eshop_order_id = o.id
            left join base_otype e on o.profile_id = e.profile_id and  o.otype_id = e.id
            WHERE refund.profile_id = #{profileId} and refund.otype_id = #{otypeId}
            and refund.trade_refund_order_number in
            <foreach collection="refundNumbers" close=")" open="(" separator="," item="id">
                #{id}
            </foreach>
        </if>
    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopOrderTestMapper">
    <select id="select" resultType="java.lang.Integer">select  1</select>
    <select id="getEmployee" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.Employee">
        SELECT id,profileid,fullname FROM employee order by  create_time DESC limit 1
    </select>
</mapper>
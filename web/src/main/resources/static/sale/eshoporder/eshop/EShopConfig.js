Type.registerNamespace('sale.eshoporder.eshop');
sale.eshoporder.eshop.EShopConfigAction = function () {
    sale.eshoporder.eshop.EShopConfigAction.initializeBase(this);
};

sale.eshoporder.eshop.EShopConfigAction.prototype = {
    _qualityResult: null,
    _qualityWarehouse: null,
    _stop: true,
    _eshopBranchList: [],
    importState: false,
    canEdit: false,
    _eshopListRowData: {},
    _ModifyAccountNeedReAuth: false,
    _pageEnabled:true,
    _ydhShop:false,
    controlVisible:{},

    context: function (cb) {
        var form = this.get_form();
        var startObj = null;
        if (window.$startTiming) {
            startObj = $startTiming('网店->网店->网店设置');
        }
        var pageParam = form.get_pageParams();

        this._eshopListRowData.eshopType = pageParam.eshopType;
        this._eshopListRowData.id = pageParam.id;
        this._eshopListRowData.auth = pageParam.auth;
        this._eshopListRowData.mutiSelectAppkey = pageParam.mutiSelectAppkey;
        this._eshopListRowData.authType = pageParam.authType;
        this._eshopListRowData.ocategory = pageParam.ocategory;
        this._eshopListRowData.stoped = pageParam.stoped;

        var powers = this._getSomePower();
        var params = {
            otypeId: pageParam.id,
            ocategory: pageParam.ocategory ? pageParam.ocategory : 0,
            typeId: pageParam.typeId ? pageParam.typeId : "00000",
            eshopType: pageParam.eshopType
        };
        var response =
            $common.ajaxSync({
                url: "sale/eshoporder/eshop/getEshopPageResponse",
                data: params,
                router: 'ngp'
            });
        if (response.code !== "200") {
            Sys.UI.MessageBox.alert("服务请求出错,请稍后重试[" + response.message + "]");
            return;
        }
        var configPageInfoResult =
            $common.ajaxSync({
                url: "sale/eshoporder/eshop/getEshopConfigPageInfo",
                data: params,
                router: 'ngp'
            });
        var configPageInfo = configPageInfoResult && configPageInfoResult.data ? configPageInfoResult.data : null;
        var autoShelfOnOrOff = configPageInfo && configPageInfo.supportAutoShelfOnOrOff;
        var autoCreateBtypeVisible = configPageInfo && configPageInfo.supportAutoCreateBtype;
        var customerBtypeVisible = autoCreateBtypeVisible;
        var btypeAutoUploadEnabled = configPageInfo && configPageInfo.supportUploadBtype;
        var invoiceUploadEnabled = configPageInfo && configPageInfo.allowUploadEshopInvoice;
        var storeProcessVisible = configPageInfo && configPageInfo.allowAoXiang && params.ocategory!=10;
        var agVisible = configPageInfo && configPageInfo.allowAg;
        var synFreightVisible = configPageInfo && configPageInfo.synFreightVisible;
        var supportDownloadSkuMemo = configPageInfo && configPageInfo.supportDownloadSkuMemo;

        var pickKtypeInfo = this._getAutoPickKtype(params);
        var eshopBranchList = this.getBranchInfos(response.data.pageInfo.groupId);
        var allowSetProductMemo = this.allowSetProductMemo(params);

        var data = response.data;
        var buyerparam = data.pageInfo;
        buyerparam.auth = pageParam.auth;
        buyerparam.skuMemoDesired = !supportDownloadSkuMemo ? 0 : data.pageInfo.skuMemoDesired;
        var kehugongying = buyerparam.ptypeAutoUploadEnabled || btypeAutoUploadEnabled || buyerparam.ktypeAutoUploadEnabled;
        kehugongying = kehugongying && params.ocategory!=10;
        buyerparam.allowProcessRefundOnline = configPageInfo && configPageInfo.allowProcessRefundOnline;
        this.buyerparam = buyerparam;
        buyerparam.btypeName = pageParam.btypeName;
        buyerparam.oldAgEnabled = buyerparam.agEnabled;
        buyerparam.oldShopAccount = buyerparam.eshopAccount;
        this._originData = data;
        var refundVisible = agVisible || (configPageInfo && configPageInfo.allowProcessRefundOnline && (this.buyerparam.eshopType == 0 || this.buyerparam.eshopType == 1)) || synFreightVisible;
        var result = !(storeProcessVisible || refundVisible || autoShelfOnOrOff || customerBtypeVisible || kehugongying || buyerparam.invoiceUploadEnabled || pickKtypeInfo.platformCanAutoPickKtype);
        this.mutiSelectAppkey = buyerparam.mutiSelectAppkey;
        var _this = this;
        var splitSendTagValue = 0;
        var isShowSelfDeliveryMode = params.ocategory == 2 ? false : true;
        var allowQicConfig = pageParam.eshopType == 115;
        var allowQicTitleConfig = pageParam.eshopType == 115 || pageParam.eshopType == 52;
        var allowBatsConfig = pageParam.eshopType == 52;
        var specUnitDownloadEnabledVisible = params.eshopType == 2;
        if (buyerparam.splitSendByFirstEnabled) {
            splitSendTagValue = 1;
        } else if (buyerparam.splitSendWhenAllEnabled) {
            splitSendTagValue = 2;
        } else if (buyerparam.splitSendAllWhenLastSync) {
            splitSendTagValue = 3;
        }
        var auth = pageParam.auth;
        var authText = "重新授权"
        if (!auth) {
            authText = "立即授权"
            //如果是新增肯定为undifine所以auth也是false
            auth = false;
        }
        var allowTaobao = (pageParam.eshopType == 0 || pageParam.eshopType == 1 || pageParam.eshopType == 38);
        var allowDD = (pageParam.eshopType == 52 || pageParam.eshopType == 48);
        var allowDistributionHandle = (pageParam.eshopType == 90 || pageParam.eshopType == 157 || pageParam.eshopType == 108 || pageParam.eshopType == 159 || pageParam.eshopType == 148);
        var downloadOrderTypelist = "";
        if (pageParam.shopType == 1) {
            downloadOrderTypelist = [{value: 0, text: '天猫订单'}, {value: 1, text: '家装分销订单'}];
        }
        if (pageParam.shopType == 0) {
            downloadOrderTypelist = [{value: 0, text: '淘宝订单'}, {value: 1, text: '家装分销订单'}];
        }
        if (pageParam.shopType == 38) {
            downloadOrderTypelist = [{value: 1, text: '厂直订单', eb: false}, {value: 2, text: '采购单'}];
        }
        var isDaiYunYing = pageParam.ocategory == 2 || pageParam.ocategory == 10 ? false : true;
        this.canEdit = configPageInfo && configPageInfo.needCheckShopAccount && pageParam.eshopAuthMark.indexOf("已授权") != -1;
        this._ModifyAccountNeedReAuth = configPageInfo && configPageInfo.modifyAccountNeedReAuth;
        if (startObj != null) {
            startObj.endTiming();// 触发上报
        }
        this._pageEnabled = params.ocategory != 10;
        var isYdh = (pageParam.eshopType == 888 || pageParam.eshopType == 889 || pageParam.eshopType == 890|| pageParam.eshopType == 891|| pageParam.eshopType == 892);
        this._ydhShop = isYdh;
        var openWaiQin = $msModule.checkPoint("DistributeFunc") || $msModule.checkPoint("SaleCarFunc")||$msModule.checkPoint("VisitFunc");
        var oldVersion= configPageInfo && configPageInfo.oldVersion;
        this.controlVisible.customerSupplierVisible=customerBtypeVisible;
        this.controlVisible.refundVisible=refundVisible;
        this.controlVisible.shopProductVisible=supportDownloadSkuMemo||specUnitDownloadEnabledVisible;
        this.controlVisible.shipVisible=data.allowSplitSendOrder || storeProcessVisible;
        var cb_data = {
            "partypes": buyerparam.parTypeList,
            "isDaiYunYing": isDaiYunYing,
            "datasource": buyerparam,
            "fieldInfos": buyerparam.fieldInfos,
            "eshopBranchList": eshopBranchList,
            "downloadOrderTypelist": downloadOrderTypelist,
            "platformEshopSnType": buyerparam.platformEshopSnType,
            "token": buyerparam.token,
            "refundVisible": refundVisible,
            "isShowSelfDeliveryMode": isShowSelfDeliveryMode && openWaiQin,
            "agVisible": agVisible,
            "allowSetProductMemo": allowSetProductMemo,
            "processRefundOnline": buyerparam.processRefundOnline,
            "allowProcessRefundOnline": configPageInfo && configPageInfo.allowProcessRefundOnline,
            "vendorId": buyerparam.vendorId,
            "btypeAutoUploadEnabled": btypeAutoUploadEnabled,
            "invoiceUploadEnabled": invoiceUploadEnabled,
            "platformEshopId": buyerparam.platformEshopId,
            "allowBuyerAccount": data.allowBuyerAccount,
            "assignAccountType": data.assignAccountType,
            "shopTypeValue": buyerparam ? buyerparam.eshopType : -1,
            "atypeSource": data.atypes,
            "allowQicConfig": allowQicConfig,
            "allowBatsConfig": allowBatsConfig,
            "allowQicTitleConfig": allowQicTitleConfig,
            "autoShelfOnOrOffVisible": autoShelfOnOrOff,
            "specUnitDownloadEnabledVisible": specUnitDownloadEnabledVisible,
            "customerBtypeVisible": customerBtypeVisible,
            "autoCreateBtypeVisible": autoCreateBtypeVisible,
            "kehugongyingVisible": kehugongying,
            "storeProcessVisible": storeProcessVisible,
            "btypeSource": data.btypes,
            "stockSource": data.stocks,
            "needAuth": buyerparam.needAuth,
            "needOrder": buyerparam.needOrder,
            "shopTypeSource": data.shopTypeSource ? data.shopTypeSource : "",
            "otypeId": pageParam.otypeId,
            "mode": pageParam.mode,
            "pageTitle": pageParam.pageTitle,
            // "buyerParams": _this._initBuyerParams(buyerparam),
            "platformSupport": data.platformSupport,
            "piFaplatformSource": data.piFaplatformSource,
            "piFaeshopTypeSource": data.piFaeshopTypeSource,
            "eshopTypeSupport": data ? data.eshopTypeSupport : null,
            "dropdownDataSource": buyerparam.fieldInfos ? buyerparam.fieldInfos[0] ? buyerparam.fieldInfos[0].dropdownDataSource : null : null,
            "typeId": pageParam.typeId ? pageParam.typeId : "00000",
            "productId": data.productId,
            "temppifaplatformSource": data.piFaplatformSource,
            "accountTypeTemp": buyerparam.accountType,
            "agEnabled": buyerparam.agEnabled ? buyerparam.agEnabled : true,
            "oldAgEnabled": buyerparam.agEnabled ? buyerparam.agEnabled : true,
            "pagein": buyerparam.deliverDuration,
            "showShopType": false,
            "autoPickKtypeVisible": pickKtypeInfo.platformCanAutoPickKtype,
            "platformKtypeList": pickKtypeInfo.platformKtypeList,
            "pickKtypeInfo": pickKtypeInfo,
            "hdSendProcessWay": buyerparam.sendProcessWay,
            splitSendEnabled: buyerparam.splitSendEnabled,
            splitSendTagValue: splitSendTagValue,
            allowSplitSendOrder: data.allowSplitSendOrder,
            allowTaobao: allowTaobao,
            allowDD: allowDD,
            allowDistributionHandle: allowDistributionHandle,
            "powers": powers,
            "auth": auth,
            "authText": authText,
            "html1": '如001订单付款时间为<font font-weight:bold>1月1日 14:00</font>  ，设置发货时效为<font font-weight:bold>48</font>小时。',
            "html2": '则001订单最晚发货时间为<font font-weight:bold>1月3日14:00</font> 。',
            "html3": '比如设置时效提醒为<font font-weight:bold>3</font>， 001订单在<font font-weight:bold>1月3号11:00</font>未完成发货，系统会进行标记提醒。',
            "html4": '如001售后单创建时间为<font font-weight:bold>7月1日 14:00</font>  ，设置线上同意退货时效为<font font-weight:bold>3</font>小时。',
            "html5": '则001售后单最晚同意/拒绝退货时间为<font font-weight:bold>7月1日17:00</font> 。',
            "showSkuMemo": supportDownloadSkuMemo,
            synFreightVisible: synFreightVisible,
            "manageModeVisible": this._pageEnabled || openWaiQin|| oldVersion,
            "orderManageModeVisible":this._pageEnabled && isYdh,
            "pageEnabled":this._pageEnabled,
            "allowShowEshopClass": configPageInfo && configPageInfo.allowShowEshopClass,
            "openWaiQin" :openWaiQin,
            "oldVersion":oldVersion,
        }
        cb(cb_data);
    },

    getBranchInfos: function (groupId) {
        var url = "sale/eshoporder/eshop/getBrachEshopByGroupId/" + groupId;
        var eshopBranch = $common.ajaxSync({
            url: url,
            data: null,
            type: 'get',
            router: 'ngp'
        });
        var eshopBranchList = eshopBranch.data ? eshopBranch.data : [];
        this._eshopBranchList = eshopBranchList;
        return eshopBranchList;
    },
    allowSetProductMemo: function (params) {
        if (params.eshopType == 0 || params.eshopType == 1 || params.eshopType == 79 || params.eshopType == 20) {
            return true;
        } else {
            return false;
        }
    },

    changeQualityStatus: function (sender) {
        if (!sender) {
            return;
        }
        var sendType = sender.get_value();
        if (sendType) {
            sender.get_form().eshopQicConfig.set_visible(true);
        } else {
            sender.get_form().eshopQicConfig.set_visible(false);
        }
    },

    _getAutoPickKtype: function (params) {
        var pickInfo = new Object();
        pickInfo.platformCanAutoPickKtype = false;
        pickInfo.platformKtypeList = new Array();
        pickInfo.defaultStrategyType = 0;
        if (!params || params.eshopType != 76) {
            return pickInfo;
        }
        var autoPickKtypeResult =
            $common.ajaxSync({
                url: "sale/eshoporder/eshop/getAutoPickKtypeInfo",
                data: params,
                router: 'ngp'
            });
        if (autoPickKtypeResult.code !== "200") {
            Sys.UI.MessageBox.alert("服务请求出错,请稍后重试[" + autoPickKtypeResult.message + "]");
            return;
        }
        return autoPickKtypeResult.data;
    },

    _initBuyerParams: function (buyer) {
        // if (!buyer || !buyer.province) {
        //     return null;
        // }
        // var arr = [];
        // if (buyer.province) arr.push(buyer.province);
        // if (buyer.city) arr.push(buyer.city);
        // if (buyer.district) arr.push(buyer.district);
        // if (buyer.street) arr.push(buyer.street);
        // var area = arr.join('/');
        // return {
        //     area: area,
        //     name: buyer.district,
        //     address: buyer.address,
        //     area_header: [{name: buyer.province}, {name: buyer.city}, {name: buyer.district}, {name: buyer.street}]
        // };
    },

    showMessageCheck: function (sender, args) {
        var form = sender.get_form();
        var pageParam = form.get_pageParams();
        var processRefundOnline = form.processRefundOnline.get_value()
        if (processRefundOnline && (pageParam.shopType == 0 || pageParam.shopType == 1)) {
            form.messageCheck.set_visible(true);
        } else {
            form.needMessageCheck.set_value(false);
            form.messageCheck.set_visible(false);
        }
    },


    doFormClose: function (sender, eventArgs) {
        $notify.emit('messageA');
    },

    supplierSelectorInit: function (sender) {
        var form = sender.get_form();
        var pageParams = new Object();
        pageParams.bcategory = 1;
        pageParams.cooperationType = 4;
        pageParams.cooperationTypeVisible = false;
        var url = "jxc/baseinfo/selector/BtypeSelector.gspx";
        sender.set_selectorPage(url);
        sender.set_selectorPageParams(pageParams);
    },

    selectSupplier: function (sender, eventArgs) {
        var data = eventArgs.get_form().selectedData;
        if (data == undefined) {
            return;
        }
        sender.set_value(data.id);
        sender.set_text(data.fullname);
    },
    doAutoOnChange: function (sender, args) {
        if (sender.get_value()) {
            $common.alert("该网店不支持开启对应功能!");
            args.set_cancel(true);
            return;
        }
    },

    checkSupportOpen: function () {
        var form = this.get_form();
        var param = form.saveData();
        param.eshopId = param.otypeId;
        var eshopSaveResult =
            $common.ajaxSync({
                url: "sale/eshoporder/eshop/checkSupportAutoShelfOnOrOff",
                data: param,
                router: 'ngp'
            });
        var respData = eshopSaveResult.data;
        if (!respData.success) {
            form.autoShelfOn.set_enabled(false);
            return;
        }
    },

    doAutoBtypeOnChange: function (sender, args) {
        var form = sender.get_form();
        var param = form.saveData();
        param.eshopId = param.otypeId;
        if (param.deliverProcessUsetype) {
            param.deliverProcessUsetype = 1;
        } else {
            param.deliverProcessUsetype = 0;
        }
        var eshopSaveResult =
            $common.ajaxSync({
                url: "sale/eshoporder/eshop/checkOpenEshopBtypeUploadConfig",
                data: param,
                router: 'ngp'
            });

        var respData = eshopSaveResult.data;
        if (!eshopSaveResult || !eshopSaveResult.data) {
            $common.alert("检查平台是否能开启配置出错：" + eshopSaveResult.message);
            args.set_cancel(true);
            return;
        }
        if (!respData.success) {
            $common.alert("检查平台是否能开启配置结果：" + respData.message);
            args.set_cancel(true);
        }
    },
    doInvoiceOnChange: function (sender, args) {
        var form = sender.get_form();
        var param = form.saveData();
        param.eshopId = param.otypeId;
        if (param.deliverProcessUsetype) {
            param.deliverProcessUsetype = 1;
        } else {
            param.deliverProcessUsetype = 0;
        }
        var eshopSaveResult =
            $common.ajaxSync({
                url: "sale/eshoporder/eshop/checkOpenEshopInvoiceConfig",
                data: param,
                router: 'ngp'
            });

        var respData = eshopSaveResult.data;
        if (!eshopSaveResult || !eshopSaveResult.data) {
            $common.alert("检查平台是否能开启配置出错：" + eshopSaveResult.message);
            args.set_cancel(true);
            return;
        }
        if (!respData.success) {
            $common.alert("检查平台是否能开启配置结果：" + respData.message);
            args.set_cancel(true);
        }
    },

    doAuthCheckByShoptype: function (sender, param) {
        var form = sender.get_form();
        if (form.radio2 && form.radio2.get_checked() == false) {
            return "";
        }
        var shopType = form.get_pageParams().shopType;
        var param = {shopType: shopType, queryVirtual: true, otypeId: param.otypeId};
        var result =
            $common.ajaxSync({
                url: "sale/eshoporder/eshop/authorizeCheckByShoptype",
                data: param,
                router: 'ngp'
            });
        var data = result ? result.data : null;
        if (data != null && !data.success) {
            return data.message;
        }

        return '';
    },
    doBtnSaveCheck: function (sender) {
        var form = this.get_form();
        var _this = this;
        var param = form.saveData();
        if (this.buyerparam.mainEshop && this.buyerparam.groupId != null) {
            var popForm = new Sys.UI.Form(sender, 0, 0);
            var eshop = new Object();
            eshop.id = param.otypeId
            eshop.eshopType = form.get_pageParams().shopType;
            var obj = new Object();
            obj.selectEshop = eshop;
            obj.oprateType = "eshopConfig";
            popForm.set_params(obj);
            popForm.showModal("sale/eshoporder/eshopproduct/PtypeSyncRelation.gspx", param);
            popForm.add_closed(function (pop) {
                _this.doBtnSave(sender);
            });
        } else {
            _this.doBtnSave(sender);
        }
    },

    doBtnSave: function (sender) {
        var startObj=this.logStartTime("资料->网店->网店设置->保存")
        var _this = this;
        var form = sender.get_form();
        var param = form.saveData();
        var downloadOrderType = form.downloadOrderType.get_value();
        var pageParam = form.get_pageParams();
        var msg = this.doAuthCheckByShoptype(sender, param);
        if (msg != '') {
            $common.showError(msg);
            return;
        }
        var oldAg = form.oldAgEnabled.get_value();
        if (oldAg == true && param.agEnabled == false && param.auth) {
            $common.alert("保存网店配置：不支持在ERP关闭极速退款");
            return;
        }
        if (param.autoPickKtype && param.pickKtypeStrategyType == 2
            && param.ktypeRangeStr != null && param.ktypeRangeStr.length > 1) {
            $common.alert("选择了“全部寻仓到指定仓库并且检查可发货库存” 的巡仓规则，巡仓范围 只能选择一个仓库");
            form.dpKtypeRangeStr.focus();
            return;
        }
        // this.checkMobile(sender);
        param.eshopId = param.otypeId;
        if (!param.deliverProcessUsetype) {
            param.deliverProcessUsetype = 0;
        }
        // var splitSendEnabled = form.sbSplitSendEnabled.get_value();
        var splitSendTag = form.rdSplitSendTag.get_value();
        param.splitSendEnabled = true;
        if (splitSendTag == 1) {
            param.splitSendByFirstEnabled = true;
            param.splitSendAllWhenLastSync = false;
            param.splitSendNormalEnabled = false;
            // param.splitSendEnabled = true;
        } else if (splitSendTag == 3) {
            param.splitSendByFirstEnabled = false;
            param.splitSendAllWhenLastSync = true;
            param.splitSendNormalEnabled = false;
            // param.splitSendEnabled = true;
        } else {
            param.splitSendNormalEnabled = true;
            param.splitSendAllWhenLastSync = false;
            param.splitSendByFirstEnabled = false;
        }
        if(!this._ydhShop){
            param.btypeGenerateType=0;
        }else{
            param.btypeGenerateType=1;
        }
        pageParam.authExpireNotifyDays=7;
        if (pageParam.shopType == 38) {
            if (downloadOrderType && downloadOrderType.length) {
                param.downloadOrderType = 0;
                for (var i = 0; i < downloadOrderType.length; i++) {
                    param.downloadOrderType = param.downloadOrderType | downloadOrderType[i];
                }
            } else {
                param.downloadOrderType = 0;
            }
        } else {
            if (downloadOrderType) {
                if (downloadOrderType.length > 1) {
                    param.downloadOrderType = 2;
                } else {
                    param.downloadOrderType = downloadOrderType[0];
                }
            } else {
                param.downloadOrderType = 0;
            }
        }
        if (form.selectBtn0 && form.selectBtn0.get_checked() == true) {
            param.mutiSelectAppkey = form.selectBtn0.get_tag();
        }
        if (form.selectBtn1 && form.selectBtn1.get_checked() == true) {
            param.mutiSelectAppkey = form.selectBtn1.get_tag();
        }
        if (form.platformQualityStatus.get_checked()) {
            this.buildQicParamas(param);
        }
        // if (form.platformBatsStatusDefaultInfo.get_checked()) {
        //     this.buildBatsQicParamas(param);
        // }
        if (form.radio2.get_checked() && param.eshopType == 90) {
            param.tmallSpecialSale = true;
            param.tokenExpireIn = Date.parse('2099-12-30 :00:00:00');
        }
        if (form.radio1.get_checked() && param.eshopType == 90) {
            param.tmallSpecialSale = false;
            param.tokenExpireIn = null;
            param.token = '';
        }
        if (form.BranchStorePanelMT.get_visible()) {
            if (form.MTgrid.get_dataSource() && form.MTgrid.get_dataSource()[0].eshopAccount) {
                param.eshopBranch = form.MTgrid.get_dataSource();
            } else if (form.MTgrid.get_allItems() && form.MTgrid.get_allItems().length > 0 && form.MTgrid.get_allItems()[0].eshopAccount) {
                param.eshopBranch = form.MTgrid.get_allItems();
            }
            var onValidTips = this.checkEshopBranchInfoValid(form, param.eshopBranch)
            if (onValidTips) {
                $common.alert(onValidTips);
                return;
            }
        }
        if (form.BranchStorePanel.get_visible()) {
            if (form.grid.get_dataSource() && form.grid.get_dataSource()[0].eshopAccount) {
                param.eshopBranch = form.grid.get_dataSource();
            } else if (form.grid.get_allItems() && form.grid.get_allItems().length > 0 && form.grid.get_allItems()[0].eshopAccount) {
                param.eshopBranch = form.grid.get_allItems();
            }
        }
        param.stoped= this._eshopListRowData && this._eshopListRowData.stoped;
        var needReAuth = this.doAuthCheckByShopAccount(sender, param);
        if (needReAuth) {
            var title = this.get_form().shopAccount.get_label();
            var tips = title + "修改后会取消授权再重新授权，请确认是否需要修改";
            $common.confirm(tips, function (r) {
                if (r) {
                    param.modifyAccountNeedReauth = true;
                } else {
                    param.eshopAccount = param.oldShopAccount;
                    param.modifyAccountNeedReauth = false;
                }
                _this.doSaveInfos(_this, param, form, sender);
            }, this);

        } else {
            this.doSaveInfos(this, param, form, sender);
        }
        this.logEndTime(startObj);
    },

    doSaveInfos: function (_this, param, form, sender) {
        var res = _this.saveOtypeInfo(param);
        if (!res) {
            return;
        }
        param.typeid = form.dpParTypeId.get_value();
        param.partypeid = form.dpParTypeId.get_value();
        var eshopSaveResult =
            $common.ajaxSync({
                url: "sale/eshoporder/eshop/saveEshopConfig",
                data: param,
                router: 'ngp'
            });

        if (!eshopSaveResult || !eshopSaveResult.data) {
            $common.alert("保存网店配置信息出错" + eshopSaveResult.message);
            form.set_enabled(true);
            return;
        }
        if (!eshopSaveResult.data.success) {
            $common.alert("保存网店配置信息出错: " + eshopSaveResult.data.message);
            form.set_enabled(true);
            return;
        }
        if (this.buyerparam.mainEshop && this.buyerparam.groupId != null) {
            var popForm = new Sys.UI.Form(sender, 0, 0);
            var eshop = new Object();
            eshop.id = param.otypeId
            eshop.eshopType = form.get_pageParams().shopType;
            var obj = new Object();
            obj.selectEshop = eshop;
            obj.oprateType = "eshopConfig";
            popForm.set_params(obj);
            popForm.showModal("sale/eshoporder/eshopproduct/PtypeSyncRelation.gspx", param);
            popForm.add_closed(function (pop) {
                $common.showOk("保存成功");
                _this.addLog(param);
                $notify.emit('otypeDataChanged', {});
                // _this.doReAuth(sender, param);
                $common.showPage(sender, "sale/eshoporder/eshop/EShopList.gspx", {}, SysConsts.CloseAll);
                form.close();
            });
        } else {
            $common.showOk("保存成功");
            _this.addLog(param);
            $notify.emit('otypeDataChanged', {});
            // _this.doReAuth(sender, param);
            $common.showPage(sender, "sale/eshoporder/eshop/EShopList.gspx", {}, SysConsts.CloseAll);
            form.close();
        }
    },

    showPlanSendTimeDuration: function (sender) {
        var form = sender.get_form();
        var hours = form.planSendTimeDuration.get_value();
        if (!hours && hours != 0) {
            return;
        }
        if (hours == 0) {
            form.planSendTimeDurationShow.set_text("---->" + 0 + " " + "天" + " " + 0 + " " + "小时");
            return;
        }
        var days = Math.floor(hours / 24); // 计算天数
        var remainingHours = hours % 24; // 计算剩余小时数
        var planSendTimeDurationShow = "---->" + days + " " + "天" + " " + remainingHours + " " + "小时"
        form.planSendTimeDurationShow.set_text(planSendTimeDurationShow);
    },

    checkEshopBranchInfoValid: function (form, eshopBranch) {
        if (!eshopBranch) {
            return null;
        }
        var onValidTips = null;
        eshopBranch.forEach(function (item) {
            if (!item.fullname) {
                form.MTgrid.focus();
                onValidTips = "子店名称不能为空，请填写子店名称！";
                return;
            }
        });
        return onValidTips;
    },
    OnEshopBranchFullNameChange: function (sender, args) {
        if (sender.get_value()) {
            return;
        }
        if (!args.get_oldValue()) {
            return;
        }
        var form = this.get_form();
        var grid = form.MTgrid;
        var index = grid.get_selectedRowIndex();
        var msg = "子店名称不能为空！";
        grid.modifyCellValue(index, sender.get_dataField(), args.get_oldValue());
        $common.alert(msg);
        form.MTgrid.focus();
    },
    buildQicParamas: function (param) {
        var form = this.get_form();
        param.eshopQicConfig.platformQualityOrgId = (param.eshopQicConfig.platformQualityOrgName ? ((param.eshopQicConfig.platformQualityOrgName.agencyId) ? (param.eshopQicConfig.platformQualityOrgName.agencyId) : (this.buyerparam.eshopQicConfig.platformQualityOrgId)) : "");
        param.eshopQicConfig.platformQualityOrgName = (param.eshopQicConfig.platformQualityOrgName ? ((param.eshopQicConfig.platformQualityOrgName.agencyName) ? (param.eshopQicConfig.platformQualityOrgName.agencyName) : (this.buyerparam.eshopQicConfig.platformQualityOrgName)) : "");
        param.eshopQicConfig.platformQualityWarehouseCode = (param.eshopQicConfig.platformQualityWarehouseName ? ((param.eshopQicConfig.platformQualityWarehouseName.warehouseCode) ? (param.eshopQicConfig.platformQualityWarehouseName.warehouseCode) : (this.buyerparam.eshopQicConfig.platformQualityWarehouseCode)) : "");
        param.eshopQicConfig.platformQualityWarehouseName = (param.eshopQicConfig.platformQualityWarehouseName ? ((param.eshopQicConfig.platformQualityWarehouseName.warehouseName) ? (param.eshopQicConfig.platformQualityWarehouseName.warehouseName) : (this.buyerparam.eshopQicConfig.platformQualityWarehouseName)) : "");
        param.eshopQicConfig.platformQualityReceiveAddress = form.address.get_text();
        param.eshopQicConfig.platformQualityComment = param.eshopQicConfig.platformQualityComment;
        param.eshopQicConfig.platformQualityBtypeNameBackup = form.qualityBtypeBackup.get_text();
        param.eshopQicConfig.platformQualityBtypeName = form.qualityBtype.get_text();
        param.eshopQicConfig.platformQualityBtypeInsure = form.qualityBtypeInsure.get_checked();
        param.eshopQicConfig.platformQualityBtypeBackupInsure = form.qualityBtypeBackupInsure.get_checked();
        if (!form.qualityBtypeInsure.get_checked()) {
            param.eshopQicConfig.platformQualityBtypeInsureType = "";
            param.eshopQicConfig.platformQualityBtypeInsureTotal = 0;
        }
        if (!form.qualityBtypeBackupInsure.get_checked()) {
            param.eshopQicConfig.platformQualityBtypeBackupInsureType = "";
            param.eshopQicConfig.platformQualityBtypeBackupInsureTotal = 0;
        }
        if (!form.qualityBtypeProduct.get_text()) {
            param.eshopQicConfig.platformQualityBtypeProduct = "";
        }
        if (!form.qualityBtypeBackupProduct.get_text()) {
            param.eshopQicConfig.platformQualityBtypeBackupProduct = "";
        }
        // this.doSaveBuyer(param);
    },

    // buildBatsQicParamas: function (param) {
    //     var form = this.get_form();
    //     param.eshopBatsConfig.platformQualityType = 1;
    // },
    doSaveBuyer: function (param) {
        var form = this.get_form();
        var warsehouse = this._qualityWarehouse;
        if (!warsehouse) {
            return;
        }
        var receiverInfo = warsehouse.receiverInfo;
        var pageParam = form.get_pageParams();
        var buyerInfo = new Object();
        buyerInfo.otypeId = param.eshopId;
        buyerInfo.eshopType = pageParam.eshopType;
        buyerInfo.customerReceiverCountry = receiverInfo.customerCountry;
        buyerInfo.customerReceiverProvince = receiverInfo.customerProvince;
        buyerInfo.customerReceiverCity = receiverInfo.customerCity;
        buyerInfo.customerReceiverDistrict = receiverInfo.customerDistrict;
        buyerInfo.customerReceiverTown = receiverInfo.customerTown;
        buyerInfo.customerReceiverAddress = receiverInfo.customerAddress;
        res = $common.ajaxSync({
            url: "sale/eshoporder/eshop/doSaveBuyer",
            data: buyerInfo,
            router: 'ngp'
        });
        var buyerId = res.data;
        param.eshopQicConfig.qicAddressId = buyerId;
        if (buyerId < 1) {
            param.eshopQicConfig.qicAddressId = null;
        }
    },
    saveOtypeInfo: function (param) {
        var otypeInfo = this.buildOtype(param);
        var otypeUrl = "jxc/baseinfo/otype/update";
        var otypeSaveResult =
            $common.ajaxSync({
                url: otypeUrl,
                data: otypeInfo,
                router: 'ngp'
            });
        if (!otypeSaveResult || !otypeSaveResult.data) {
            $common.alert("保存机构信息出错" + otypeSaveResult.message);
            return false;
        }
        return true;
    },
    buildOtype: function (param) {
        var form = this.get_form();
        var otypeInfo = {};
        var isnew = false;
        otypeInfo.id = param.otypeId;
        otypeInfo.usercode = '';
        otypeInfo.fullname = param.otypeFullname;
        otypeInfo.ktypeId = param.ktypeId;
        otypeInfo.btypeId = param.btypeId;
        otypeInfo.atypeId = param.atypeId ? param.atypeId : 0;
        otypeInfo.currencyId = param.currencyId ? param.currencyId : 0;
        otypeInfo.partypeId = form.dpParTypeId.get_value().typeid;
        if (param.btypeGenerateType == 0) {
            otypeInfo.accountType = 1;
        } else if (param.btypeGenerateType == 1) {
            otypeInfo.accountType = 3;
        }

        otypeInfo.checkAccountType = param.checkAccountType;
        otypeInfo.payAccount = "";//param.payAccount;
        otypeInfo.memo = param.memo;
        otypeInfo.deliverDuration = param.deliverDuration ? param.deliverDuration : -1;
        otypeInfo.people = param.senderName;
        otypeInfo.cellphone = param.mobile;
        otypeInfo.telephone = param.phone;
        otypeInfo.rowindex = param.rowindex;
        otypeInfo.independentCheck = param.independentCheck ? 1 : 0;

        otypeInfo.province =  "";
        otypeInfo.city = "";
        otypeInfo.district =  "";
        otypeInfo.street =  "";
        otypeInfo.address = "";
        otypeInfo.stoped = param.stoped;
        param.province = "";
        param.city = "";
        param.district = "";
        param.street = "";
        param.address = "";
        if (isnew) otypeInfo.createTime = new Date();
        else otypeInfo.updateTime = new Date();
        return otypeInfo;
    },

    showAddress: function () {
        var form = this.get_form();
        var addressEdit = form.buyerAddress.get_value();
        if (!addressEdit) {
            $common.showTips("请填写内容!")
        }
        var _this = this;
        this.get_service().post('shell/config/resolveAddress', {address: addressEdit}, function (rest) {
            if (!rest || rest.code != '200') {
                $common.showTips((data && data.message) ? data.message : '解析地址出错，请重试');
                return;
            }
            var data = rest.data
            if (data.address) {
                form.senderArea.popupArea._bindData(data.address.province, data.address.city, data.address.district, data.address.town);
                form.address.set_value(data.address.street);
            }
            if (data.receiverName) {
                form.senderName.set_value(data.receiverName)
            }
            if (data.telephone) {
                form.phone.set_value(data.telephone)
            }
        })
    },

    _doInitForm: function () {
        var form = this.get_form();
        var buyerParams = "";
        if (form.hdDefaultBuyerArea) {
            buyerParams = form.hdDefaultBuyerArea.get_value();
        }
        this._doInitAutoPickKtypeForm(form);
        var pageParam = form.get_pageParams();
        //sendProcessWay
        var list = [];
        var pageParam = form.get_pageParams();
        if (pageParam && pageParam.ocategory == 2) {
            list.push({id: 4, text: '不走发货流程-不记账'});
        } else {
            list = [
                {id: 4, text: '不走发货流程-不记账'}, {id: 0, text: '不走发货流程'}
            ];
            var hasWmsFunc = $msModule.checkPoint("WmsFunc");
            if (hasWmsFunc) {
                defaultValue = 1;
                list.push({id: 1, text: '仓储管理'});

            }
            var hasOfflineBillToDeliverFunc = $msModule.checkPoint("OfflineBillToDeliverFunc");
            if (hasOfflineBillToDeliverFunc) {
                defaultValue = 2;
                list.push({id: 2, text: '简易仓储'});
            }
        }
        var wayValue = form.hdSendProcessWay.get_value();
        form.sendProcessWay.set_items(list);
        form.sendProcessWay.set_value(wayValue);
        if (pageParam.otypeId == 0) {
            form.sendProcessWay.set_value(defaultValue);
        }
        //selfDeliveryMode
        var selfDeliveryModelist = [
            {id: 0, text: '简单配送'}
        ];
        var hasDistributeFunc = $msModule.checkPoint("DistributeFunc");
        if (hasDistributeFunc) {
            selfDeliveryModelist.push({id: 1, text: '外勤配送'});
        }
        form.selfDeliveryMode.set_items(selfDeliveryModelist);
        var params = {
            otypeId: 0,
            ocategory: pageParam.ocategory,
            shopType: pageParam.shopType,
            notQueryEshop: true
        };
        var response = $common.ajaxSync({
            url: "sale/eshoporder/eshop/getEshopInfoByEshopType",
            data: params,
            router: 'ngp'
        });
        var data = response ? response.data : null;
        var eshopInfo = data ? data.pageInfo : null;
        if (eshopInfo.subscribeApplications != null && eshopInfo.subscribeApplications.length > 0) {
            this._ShowSelectAppkeyView(form, eshopInfo.subscribeApplications);
        }
        if (this.buyerparam.tmallSpecialSale) {
            form.radio2.set_checked(true);
            form.token.set_visible(true);
            form.token.set_label("Token");
        }
        // if(this.buyerparam.platformBatsQualityStatus){
        //     form.batsQualityInfo.set_visible(true);
        // } if(this.buyerparam.platformBatsQualityDefaultInfoStatus){
        //     form.eshopBatsConfig.set_visible(true);
        // }
        this.showPlanSendTimeDuration(this);
    },

    _ShowSelectAppkeyView: function (form, data) {
        var _this = this;
        form.usualAuth.set_visible(false);
        form.threeStepAuth.set_visible(false);
        form.selectAppkey.set_visible(true);
        this.subscribeApplications = {};
        var checkedIndex = 0;

        if (form.appendBlock) {
            form.removeControlById("appendBlock");
        }
        var block = $createControl('Block', {ID: "appendBlock", CssClass: "dflex"}, form);


        for (var i = 0; i < data.length; i++) {
            var entity = new Object();
            entity.appName = data[i].appName;
            entity.description = data[i].description;
            entity.orderLinkUrl = data[i].orderLinkUrl;
            this.subscribeApplications[data[i].key] = entity;
            var panel = $createControl('RadioButton',
                {
                    ID: "selectBtn" + i,
                    GroupName: "selAppkey",
                    CssClass: "selBtn usuallyBtn SkinBorderHover",
                    Text: data[i].appName,
                    Tag: data[i].key,
                    OnChange: "doSelectAppkey",
                    Checked: data[i].key === _this.buyerparam.mutiSelectAppkey
                },
                form, block);
            if (this.mutiSelectAppkey == data[i].key) {
                checkedIndex = i;
            }
        }
        block.appendTo(form.selectBtns);
        this.initSelectAppKey(checkedIndex, form, data);

    },

    initSelectAppKey: function (index, form, data) {
        form.selectdescription.set_text(data[index].description);
        form.orderLink.set_value(data[index].orderLinkUrl);
        this.mutiSelectAppkey = data[index].key;
        var btnControl = $craba.findControl("selectBtn" + index, form)
        btnControl.set_checked(true);
        var btnEle = form.appendBlock._element.children[index];
        btnEle.classList.add("SkinColor");
        btnEle.classList.add("SkinBorder");
        btnEle.classList.remove("usuallyBtn");

        var btn = $common.getBounds(btnEle); // 按钮的位置
        var box = $common.getBounds(form.Box.get_element()); // 面板的位置
        var x = btn.x - box.x + 20; // 箭头相对面板的的x坐标。
        form.arrow2.get_element().style.left = x + "px";
        form.arrow1.get_element().style.left = x + 5 + "px";
    },
    doSelectAppkey: function (sender) {
        var form = this.get_form();
        var tag = sender.get_tag();
        var btns = $common.getElementsByClassName(sender.get_parent().get_element(), 'selBtn', false);
        for (var i = 0; i < btns.length; i++) {
            btns[i].classList.remove("SkinColor");
            btns[i].classList.remove("SkinBorder");
            btns[i].classList.remove("checked");
            //  btns[i].classList.remove("usuallyBtn");
            btns[i].classList.add("usuallyBtn");
        }

        var entity = this.subscribeApplications[tag];
        form.selectdescription.set_text(entity.description);
        form.orderLink.set_value(entity.orderLinkUrl);
        this.mutiSelectAppkey = tag;
        var btnEle = sender.get_element().parentElement;
        btnEle.classList.add("SkinColor");
        btnEle.classList.add("SkinBorder");
        btnEle.classList.remove("usuallyBtn");

        var btn = $common.getBounds(btnEle); // 按钮的位置
        var box = $common.getBounds(form.Box.get_element()); // 面板的位置

        var x = btn.x - box.x + 20; // 箭头相对面板的的x坐标。
        form.arrow2.get_element().style.left = x + "px";
        form.arrow1.get_element().style.left = x + 5 + "px";
    },

    initialize: function initialize() {
        var _this = this;
        var form = this.get_form();
        form.shopAccount.set_enabled((!_this.canEdit || this._ModifyAccountNeedReAuth) && this._pageEnabled);
        //移入弹窗绑定
        if (form.refundTimingExplain) {
            $common.addMouseEnterHandler(form.refundTimingExplain.get_element(), $createDelegate(this, this.showRefundTimingExplainView));
            $common.addMouseLeaveHandler(form.refundTimingExplain.get_element(), $createDelegate(this, this.hideRefundTimingExplainView));
        }
        if (form.orderTimingExplain) {
            $common.addMouseEnterHandler(form.orderTimingExplain.get_element(), $createDelegate(this, this.showOrderTimingExplainView));
            $common.addMouseLeaveHandler(form.orderTimingExplain.get_element(), $createDelegate(this, this.hideOrderTimingExplainView));
        }
        if(form.orderDeliverProcessExplain){
            $common.addMouseEnterHandler(form.orderDeliverProcessExplain.get_element(), $createDelegate(this, this.showOrderDeliverProcessExplainView));
            $common.addMouseLeaveHandler(form.orderDeliverProcessExplain.get_element(), $createDelegate(this, this.hideOrderDeliverProcessExplainView));
        }
        if (this.buyerparam.processRefundOnline && (this.buyerparam.eshopType == 0 || this.buyerparam.eshopType == 1)) {
            form.messageCheck.set_visible(true);
        } else {
            form.needMessageCheck.set_value(false);
            form.messageCheck.set_visible(false);
        }
        sale.eshoporder.eshop.EShopConfigAction.callBaseMethod(this, 'initialize');
        // this.checkSupportOpen();
        this.onEShopTypeChange()
        this._doInitForm();
        var pageParam = form.get_pageParams();
        var category = pageParam.ocategory;
        // if (category == 1) {
        //     var btypeList = [
        //         {id: 0, text: "分销商"}
        //     ];
        //     form.btypeGenerateType.set_items(btypeList);
        //     form.btypeGenerateType.set_selectedIndex(0);
        //     form.btypeGenerateType.set_enabled(false);
        //     form.BtypeId.set_label("默认分销商往来单位");
        // }else if(this._ydhShop){
        //     form.btypeGenerateType.set_selectedIndex(1);
        // }
        // else{
        //     form.btypeGenerateType.set_selectedIndex(0);
        // }
        // if (category == 2) {
        //     form.BaseInfo.set_visible(false);
        // }
        if (this.buyerparam.downloadOrderType == 2) {
            form.downloadOrderType.set_value([0, 1]);
        }
        if (pageParam.shopType == 38) {
            //京东订单类型:   0: 默认(默认全开) 1: 京东厂直订单  2: 采购单
            //1、2、4、8
            //京东自营：downloadOrderType 的值= 多个订单类枚举值型值用位或运算得到
            if (this.buyerparam.downloadOrderType == 0 || this.buyerparam.downloadOrderType == 3) {
                form.downloadOrderType.set_value([1, 2]);
            } else {
                form.downloadOrderType.set_value([1]);
            }
        }
        var pageParam = form.get_pageParams();
        if (form.topicCWGL18) {
            form.eshopQicConfig.set_visible(form.platformQualityStatus.get_checked());
            if (pageParam.shopType == 115) {
                _this._setControlVisibleAndDataSource(form);
            }
        }

        if (this._eshopListRowData.ocategory == 10) {
            form.flbMainBody.set_enabled(false);
            form.flbBottomBody.set_enabled(false);
        }
        this.setMoreInfoControlVisible(form, false);
    },

    _setControlVisibleAndDataSource: function (form, needFunc) {
        var _this = this;
        var pageParam = form.get_pageParams();
        eshopId = pageParam.id;
        $jarvisUtils.post("sale/jarvis/baseInfo/getPlatformQuality", eshopId, function (response) {
            try {
                if (response.code == 200 || response.data) {
                    if (pageParam.shopType == 115) {
                        _this._qualityResult = response.data;
                        _this._setControlVisible(form, _this._qualityResult, needFunc);
                        _this.qualityBtypeAndBackForReview(response.data);
                    }
                    return;
                }

            } catch (e) {
                console.log('刷新质检信息报错：' + e);
            }
        });
    },
    qualityBtypeAndBackForReview: function (qualityResult) {
        var form = this.get_form();
        var qualityBtypeItem = form.qualityBtype.get_value();
        var qualityBtypeBackupItem = form.qualityBtypeBackup.get_value();
        var deliveryList = qualityResult.deliveryList;
        var qualityBtypeProductItem = null;
        var qualityBtypeBackupProductItem = null;
        for (var i = 0; i < deliveryList.length; i++) {
            var item = deliveryList[i];
            if (item.id == qualityBtypeItem) {
                form.qualityBtypeProduct.set_dataSource(item.deliveryProducts);
                qualityBtypeProductItem = item.deliveryProducts;
            }
            if (item.id == qualityBtypeBackupItem) {
                form.qualityBtypeBackupProduct.set_dataSource(item.deliveryProducts);
                qualityBtypeBackupProductItem = item.deliveryProducts;
            }
        }

        var qualityBtypeProduct = form.qualityBtypeProduct.get_value();
        var qualityBtypeBackupProduct = form.qualityBtypeBackupProduct.get_value();
        for (var j = 0; j < qualityBtypeProductItem.length; j++) {
            var item1 = qualityBtypeProductItem[j];
            if (item1.id == qualityBtypeProduct) {
                form.qualityBtypeInsureType.set_dataSource(item1.insureTypeList);
                form.qualityBtypeInsure.set_visible(item1.enableInsure == "1");
            }
        }
        for (var j = 0; j < qualityBtypeBackupProductItem.length; j++) {
            var item2 = qualityBtypeBackupProductItem[j];
            if (item2.id == qualityBtypeBackupProduct) {
                form.qualityBtypeBackupInsureType.set_dataSource(item2.insureTypeList);
                form.qualityBtypeBackupInsure.set_visible(item2.enableInsure == "1");
            }
        }
        var qualityBtypeBackupInsure = form.qualityBtypeBackupInsure.get_value();
        form.qualityBtypeBackupInsureTotal.set_visible(qualityBtypeBackupInsure);
        form.qualityBtypeBackupInsureType.set_visible(qualityBtypeBackupInsure);
        var qualityBtypeInsure = form.qualityBtypeInsure.get_value();
        form.qualityBtypeInsureTotal.set_visible(qualityBtypeInsure);
        form.qualityBtypeInsureType.set_visible(qualityBtypeInsure);
    },
    _setControlVisible: function (form, qualityResult, needFunc) {
        var _this = this;
        form.qualityBtype.set_dataSource(qualityResult ? (qualityResult.deliveryList || []) : []);
        form.qualityBtypeBackup.set_visible(true);
        form.qualityBtypeBackup.set_dataSource(qualityResult ? (qualityResult.deliveryList || []) : []);
    },
    showdownloadConfigView: function (sender, e) {
        if (!e) e = sender; // hover的情况
        this.get_form().downloadConfigView.appendAt(e.target);
    },
    hidedownloadConfigView: function (sender, e) {
        this.get_form().downloadConfigView.close();
    },
    showOrderTimingExplainView: function (sender, e) {
        if (!e) e = sender; // hover的情况
        this.get_form().orderTimingExplainView.appendAt(e.target);
    },
    hideOrderTimingExplainView: function (sender, e) {
        this.get_form().orderTimingExplainView.close();
    },
    showRefundTimingExplainView: function (sender, e) {
        if (!e) e = sender; // hover的情况
        this.get_form().refundTimingExplainView.appendAt(e.target);
    },
    hideRefundTimingExplainView: function (sender, e) {
        this.get_form().refundTimingExplainView.close();
    },
    showOrderDeliverProcessExplainView: function (sender, e,viewId) {
        if (!e) e = sender; // hover的情况
        this.get_form().orderDeliverProcessExplainView.appendAt(e.target);
    },
    hideOrderDeliverProcessExplainView: function (sender, e) {
        this.get_form().orderDeliverProcessExplainView.close();
    },

    dispose: function () {
        sale.eshoporder.eshop.EShopConfigAction.callBaseMethod(this, 'dispose');
    },
    addLog: function (param) {
        var form = this.get_form();
        //新增日志调用api的时候有记录
        if (this.get_context('mode') == 1 || !this._originData) {
            return;
        }
        var message = "";
        var pageInfo = this._originData.pageInfo;
        if (pageInfo.otypeFullname != param.otypeFullname) {
            message += "修改网店的配置信息，修改【网店名称】为“" + param.otypeFullname + "”\n";
        }
        // if (pageInfo.partypeid != param.partypeid) {
        //     var classItem = form.dpParTypeId.get_text();
        //     message += "修改网店的配置信息，修改【选择分类】为“" + classItem + "”\n";
        // }
        // if (pageInfo.memo != param.memo) {
        //     message += "修改网店的配置信息，修改【网店备注】为“" + param.memo + "”\n";
        // }
        if (pageInfo.deliverDuration != param.deliverDuration) {
            if (param.deliverDuration != -1) {
                message += "修改网店的配置信息，修改【发货时效】为“" + param.deliverDuration + "小时”\n";
            } else {
                message += "修改网店的配置信息，修改【发货时效】为“无时效 ”\n";
            }
        }
        // if (pageInfo.promisedSyncFreightDuration != param.promisedSyncFreightDuration) {
        //     if (param.promisedSyncFreightDuration != -1) {
        //         message += "修改网店的配置信息，修改【同步单号时效】为“" + param.promisedSyncFreightDuration + "小时”\n";
        //     } else {
        //         message += "修改网店的配置信息，修改【同步单号时效】为“无时效 ”\n";
        //     }
        // }
        if (pageInfo.reissiueSyncFreight != param.reissiueSyncFreight) {
            if (param.reissiueSyncFreight == true) {
                message += "修改网店的配置信息，修改【手工创建的换货/补寄售后单支持同步物流信息】为【开启】\n";
            } else {
                message += "修改网店的配置信息，修改【手工创建的换货/补寄售后单支持同步物流信息】为【关闭】\n";
            }
        }
        if (pageInfo.promisedCollectDuration != param.promisedCollectDuration) {
            if (param.promisedCollectDuration != -1) {
                message += "修改网店的配置信息，修改【揽收时效】为“" + param.promisedCollectDuration + "小时”\n";
            } else {
                message += "修改网店的配置信息，修改【揽收时效】为“无时效 ”\n";
            }
        }
        if (pageInfo.promisedCollectDurationConfig != param.promisedCollectDurationConfig) {
            if (param.promisedCollectDurationConfig == 0) {
                message += "修改网店的配置信息，修改【揽收时效计算方式】为“以付款时间计算”\n";
            } else {
                message += "修改网店的配置信息，修改【揽收时效计算方式】为“以发货时间计算”\n";
            }
        }
        // if (pageInfo.promisedSignDuration != param.promisedSignDuration) {
        //     if (param.promisedSignDuration != -1) {
        //         message += "修改网店的配置信息，修改【签收时效】为“" + param.promisedSignDuration + "小时”\n";
        //     } else {
        //         message += "修改网店的配置信息，修改【签收时效】为“无时效 ”\n";
        //     }
        // }
        if (pageInfo.mentionDeliverDuration != param.mentionDeliverDuration) {
            if (param.mentionDeliverDuration != -1) {
                message += "修改网店的配置信息，修改【时效即将超时提前提醒】为“" + param.mentionDeliverDuration + "小时”\n";
            } else {
                message += "修改网店的配置信息，修改【时效即将超时提前提醒】为“无时效 ”\n";
            }
        }
        if (pageInfo.subscribeLogistics != param.subscribeLogistics) {
            var re = param.subscribeLogistics ? "开启" : "关闭";
            message += "修改网店的配置信息，修改【物流轨迹监控】为“" + re + "”\n";
        }


        if (pageInfo.refundPromisedAgreeDuration != param.refundPromisedAgreeDuration) {
            if (param.refundPromisedAgreeDuration != -1) {
                message += "修改网店的配置信息，修改【售后处理时效】为“" + param.refundPromisedAgreeDuration + "小时”\n";
            } else {
                message += "修改网店的配置信息，修改【售后处理时效】为“无时效 ”\n";
            }
        }

        if (pageInfo.ktypeId != param.ktypeId) {
            var ktype = form.edKType.get_text();
            message += "修改网店的配置信息，修改【发货仓库】为“" + ktype + "”\n";
        }

        if (pageInfo.eshopAccount != param.eshopAccount) {
            var shopAccountLable = form.shopAccount.get_label();
            message += "修改网店的配置信息，修改【" + shopAccountLable + "】为“" + param.eshopAccount + "”\n";
        }
        if (pageInfo.platformEshopSnType != param.platformEshopSnType) {
            var platformEshopSnType = form.platformEshopSnType.get_text();
            var platformEshopSnTypeLable = form.platformEshopSnType.get_label();
            message += "修改网店的配置信息，修改【" + platformEshopSnTypeLable + "】为“" + platformEshopSnType + "”\n";
        }
        if (pageInfo.token != param.token && form.token.get_visible()) {
            var token = form.token.get_label();
            message += "修改网店的配置信息，修改【" + token + "】为“" + param.token + "”\n";
        }
        if (pageInfo.vendorId != param.vendorId) {
            var vendorId = form.vendorId.get_label();
            message += "修改网店的配置信息，修改【" + vendorId + "】为“" + param.vendorId + "”\n";
        }
        if (pageInfo.platformEshopId != param.platformEshopId) {
            var platformEshopId = form.platformEshopId.get_label();
            message += "修改网店的配置信息，修改【" + platformEshopId + "】为“" + param.platformEshopId + "”\n";
        }
        if (pageInfo.appKey != param.appKey) {
            var platformEshopId = form.appKey.get_label();
            message += "修改网店的配置信息，修改【" + platformEshopId + "】为“" + param.appKey + "”\n";
        }
        if (pageInfo.appSecret != param.appSecret) {
            var appSecret = form.appSecret.get_label();
            message += "修改网店的配置信息，修改【" + appSecret + "】为“" + param.appSecret + "”\n";
        }

        if (pageInfo.ptypeAutoUploadEnabled != param.ptypeAutoUploadEnabled) {
            var re = param.ptypeAutoUploadEnabled ? "开启" : "关闭";
            message += "修改网店的配置信息，修改【自动同步本地商品到平台】为“" + re + "”\n";
        }
        if (pageInfo.btypeAutoUploadEnabled != param.btypeAutoUploadEnabled) {
            var re2 = param.btypeAutoUploadEnabled ? "开启" : "关闭";
            message += "修改网店的配置信息，修改【自动同步本地往来单位到平台】为“" + re2 + "”\n";
        }
        if (pageInfo.ktypeAutoUploadEnabled != param.ktypeAutoUploadEnabled) {
            var re3 = param.ktypeAutoUploadEnabled ? "开启" : "关闭";
            message += "修改网店的配置信息，修改【自动同步本地仓库到平台】为“" + re3 + "”\n";
        }
        if (pageInfo.invoiceUploadEnabled != param.invoiceUploadEnabled) {
            var re4 = param.invoiceUploadEnabled ? "开启" : "关闭";
            message += "修改网店的配置信息，修改【开票后上传发票到平台】为“" + re4 + "”\n";
        }
        // if (pageInfo.autoShelfOn != param.autoShelfOn) {
        //     var re4 = param.autoShelfOn ? "开启" : "关闭";
        //     message += "修改网店的配置信息，修改【开启网店商品自动上下架】为“" + re4 + "”\n";
        // }
        if (pageInfo.specUnitDownloadEnabled != param.specUnitDownloadEnabled) {
            var re4 = param.specUnitDownloadEnabled ? "开启" : "关闭";
            message += "修改网店的配置信息，修改【规格单位下载】为“" + re4 + "”\n";
        }
        if (pageInfo.autoCreateBtypeEnabled != param.autoCreateBtypeEnabled) {
            var re5 = param.autoCreateBtypeEnabled ? "开启" : "关闭";
            message += "修改网店的配置信息，修改【自动创建往来单位】为“" + re5 + "”\n";
        }
        if (typeof param.downloadOrderType === "undefined") {
            param.downloadOrderType = 0;
        }
        if (pageInfo.downloadOrderType != param.downloadOrderType) {
            var shoptypeStr = "淘宝";
            if (pageInfo.eshopType == 1) {
                shoptypeStr = "天猫";
            }
            var re5 = "";
            if (pageInfo.eshopType == 38) {
                if (param.downloadOrderType == 3) {
                    re5 = "厂直订单,采购单";
                } else {
                    re5 = "厂直订单";
                }
            } else {
                if (param.downloadOrderType == 2) {
                    re5 = shoptypeStr + "订单,家装分销订单";
                } else {
                    re5 = param.downloadOrderType == 0 ? shoptypeStr + "订单" : "家装分销订单";
                }
            }
            message += "修改网店的配置信息，修改【下单订单类型为】为“" + re5 + "”\n";
        }
        if (pageInfo.eshopQicConfig) {
            if (pageInfo.eshopQicConfig.platformQualityWarehouseName != param.eshopQicConfig.platformQualityWarehouseName) {
                var re5 = param.eshopQicConfig.platformQualityWarehouseName;
                message += "修改网店的配置信息，修改【质检仓库】为“" + re5 + "”\n";
            }
            if (pageInfo.eshopQicConfig.platformQualityOrgName != param.eshopQicConfig.platformQualityOrgName) {
                var re5 = param.eshopQicConfig.platformQualityOrgName;
                message += "修改网店的配置信息，修改【质检机构】为“" + re5 + "”\n";
            }
            if (pageInfo.eshopQicConfig.platformQualityRefundInterceptionCode != param.eshopQicConfig.platformQualityRefundInterceptionCode) {
                var re5 = form.refundInterception.get_text();
                message += "修改网店的配置信息，修改【售后拦截策略】为“" + re5 + "”\n";
            }
            if (pageInfo.eshopQicConfig.platformQualityReceiveAddress != param.eshopQicConfig.platformQualityReceiveAddress) {
                var re5 = param.eshopQicConfig.platformQualityReceiveAddress;
                message += "修改网店的配置信息，修改【质检机构收货地址】为“" + re5 + "”\n";
            }
            if (pageInfo.eshopQicConfig.platformQualityBtypeName != param.eshopQicConfig.platformQualityBtypeName) {
                var re5 = param.eshopQicConfig.platformQualityBtypeName;
                message += "修改网店的配置信息，修改【首发发货快递物流】为“" + re5 + "”\n";
            }
            if (pageInfo.eshopQicConfig.platformQualityBtypeProduct != param.eshopQicConfig.platformQualityBtypeProduct) {
                var re5 = form.qualityBtypeProduct.get_text();
                message += "修改网店的配置信息，修改【发货快递物流产品】为“" + re5 + "”\n";
            }
            if (pageInfo.eshopQicConfig.platformQualityBtypeInsureTotal != param.eshopQicConfig.platformQualityBtypeInsureTotal) {
                var re5 = param.eshopQicConfig.platformQualityBtypeInsureTotal;
                message += "修改网店的配置信息，修改【保价金额】为“" + re5 + "”\n";
            }
            if ((pageInfo.eshopQicConfig.platformQualityBtypeInsureType) != (param.eshopQicConfig.platformQualityBtypeInsureType == null ? "" : param.eshopQicConfig.platformQualityBtypeInsureType)) {
                var re5 = form.qualityBtypeInsureType.get_text();
                message += "修改网店的配置信息，修改【保价类型】为“" + re5 + "”\n";
            }
            if (pageInfo.eshopQicConfig.platformQualityBtypeNameBackup != param.eshopQicConfig.platformQualityBtypeNameBackup) {
                var re5 = param.eshopQicConfig.platformQualityBtypeNameBackup;
                message += "修改网店的配置信息，修改【备用发货快递物流】为“" + re5 + "”\n";
            }
            if (pageInfo.eshopQicConfig.platformQualityBtypeBackupProduct != param.eshopQicConfig.platformQualityBtypeBackupProduct) {
                var re5 = form.qualityBtypeBackupProduct.get_text();
                message += "修改网店的配置信息，修改【备用发货快递物流产品】为“" + re5 + "”\n";
            }
            if (pageInfo.eshopQicConfig.platformQualityBtypeBackupInsureTotal != param.eshopQicConfig.platformQualityBtypeBackupInsureTotal) {
                var re5 = param.eshopQicConfig.platformQualityBtypeBackupInsureTotal;
                message += "修改网店的配置信息，修改【保价金额】为“" + re5 + "”\n";
            }
            if ((pageInfo.eshopQicConfig.platformQualityBtypeBackupInsureType) != (param.eshopQicConfig.platformQualityBtypeBackupInsureType == null ? "" : param.eshopQicConfig.platformQualityBtypeBackupInsureType)) {
                var re5 = form.qualityBtypeBackupInsureType.get_text();
                message += "修改网店的配置信息，修改【保价类型】为“" + re5 + "”\n";
            }
            if (pageInfo.eshopQicConfig.platformQualityComment != param.eshopQicConfig.platformQualityComment) {
                var re5 = param.eshopQicConfig.platformQualityComment;
                message += "修改网店的配置信息，修改【备注】为“" + re5 + "”\n";
            }
            if (pageInfo.planSendTimeDuration != param.planSendTimeDuration) {
                var re5 = param.planSendTimeDuration;
                message += "修改网店的配置信息，修改【预约签收/送达的周期购订单预计提前时间】为“" + re5 + "”\n";
            }
        }


        if (message) {
            var params = {};
            params.objectType = "otype";
            params.body = message;
            params.objectId = pageInfo.otypeId;
            var url = "sale/eshoporder/eshoplist/saveChangeLog";
            $common.ajax({
                url: url,
                data: params,
                router: 'ngp'
            });
        }
    },

    doAutoPickKtypeChange: function (sender) {
        var form = sender.get_form();
        this._doSetAutoPickKtypeFormEnable(form);
    },

    _doInitAutoPickKtypeForm: function (form) {
        var autoPickData = form.hfAutoPickKtype.get_value();
        form.sbAutoPickKtype.set_checked(autoPickData.autoPickKtype);
        form.dpPickKtypeStrategyType.set_value(autoPickData.pickKtypeStrategyType);
        form.dpKtypeRangeStr.set_value(autoPickData.ktypeRangeStr);
        form.deDefaultStrategyType.set_value(autoPickData.defaultStrategyType);
        form.deDefaultKtype.set_value(autoPickData.defaultKtype);
        if (autoPickData.defaultStrategyType == 2) {
            form.deDefaultKtype.set_visible(true);
        } else {
            form.deDefaultKtype.set_visible(false);
        }
        this._doSetAutoPickKtypeFormEnable(form);
    },

    _doSetAutoPickKtypeFormEnable: function (form) {
        if (!form.sbAutoPickKtype.get_checked()) {
            form.dpPickKtypeStrategyType.set_visible(false);
            form.dpKtypeRangeStr.set_visible(false);
            form.deDefaultStrategyType.set_visible(false);
            form.deDefaultKtype.set_visible(false);
            return;
        }
        form.dpPickKtypeStrategyType.set_visible(true);
        form.dpKtypeRangeStr.set_visible(true);
        form.deDefaultStrategyType.set_visible(true);
        if (form.deDefaultStrategyType.get_value() == 2) {
            form.deDefaultKtype.set_visible(true);
        } else {
            form.deDefaultKtype.set_visible(false);
        }
    },

    onPickKtypeStrategyTypeChange: function (sender, args) {
        var form = sender.get_form();
        var pickType = sender.get_value();
    },
    onDefaultStrategyTypeChange: function (sender, args) {
        var form = sender.get_form();
        var defaultStrategyType = sender.get_value();
        if (defaultStrategyType == 2) {
            form.deDefaultKtype.set_visible(true);
        } else {
            form.deDefaultKtype.set_visible(false);
        }
    },
    doOpenStockMapping: function (sender) {
        var form = sender.get_form();
        var pageParam = {
            eshopId: form.otypeId.get_value()
        };
        $common.showPage(sender, "sale/eshoporder/eshop/PlatformWarehouseCorrespond.gspx", pageParam, SysConsts.CloseAll);
        form.close();
    },

    doPackageSplitSendTypeChange: function (sender) {
        if (!sender) {
            return;
        }
        var sendType = sender.get_value();
        if (sendType) {
            sender.get_form().rdSplitSendTag.set_visible(true);
        } else {
            sender.get_form().rdSplitSendTag.set_visible(false);
        }
    },
    _getSomePower: function () {
        var powers = {};
        //是否为批发产品
        powers.isWholesale = false;//$eshoppower.getSysdata("productType") == "WHOLESALE";
        return powers;
    },
    doSubscribeLogisticsChange: function (sender, args) {
        var form = this.get_form();
        var logisChecked = form.subscribeLogistics.get_value();
        if (logisChecked) {
            $common.confirm("开启物流轨迹监控，系统会自动订阅物流轨迹，请确认是否继续？", function (confirmRes) {
                if (!confirmRes) {
                    form.subscribeLogistics.set_value(false);
                }
            })
        }
    },

    supplierSelectorInit: function (sender) {
        var form = sender.get_form();
        var pageParams = new Object();
        pageParams.bcategory = 3;
        pageParams.cooperationType = 4;
        pageParams.cooperationTypeVisible = false;
        var url = "jxc/baseinfo/selector/BtypeSelector.gspx";
        sender.set_selectorPage(url);
        sender.set_selectorPageParams(pageParams);
    },
    onAtypeInit: function (sender) {
        var url = "/jxc/baseinfo/selector/AtypeSelector.gspx";
        var filterStr = sender.get_text();
        var parameter = {
            filterKey: 'quick',
            parTypeId: "00001",
            showadd: true,
            showstopfilter: true,
            pageTitle: '收支账户'
        };
        if (filterStr && sender.get_textChanged()) {
            parameter.filtervalue = filterStr;
        }
        sender.set_selectorPageParams(parameter);
        sender.set_selectorPage(url);

    },
    doEnterPress: function (sender, eventArgs) {
        if (sender.get_value() && !sender.get_textChanged()) {
            eventArgs.set_cancel(true);
        }
    },
    doBaseInfoSelect: function (sender, eventArgs) {
        var selector = eventArgs.get_form();
        var result = selector.selectedData;
        if (sender.get_value() === result.id) {
            return;
        }
        sender.set_value(result.id);
        sender.set_text(result.fullname);
    },
    selectSupplier: function (sender, eventArgs) {
        var data = eventArgs.get_form().selectedData;
        if (data == undefined) {
            return;
        }
        sender.set_value(data.id);
        sender.set_text(data.fullname);
    },
    onKtypeInit: function (sender) {
        var url = "/jxc/baseinfo/selector/KtypeSelector.gspx";
        var filterStr = sender.get_text();
        var parameter = {
            filtertype: 'quick',
            stockStates: '0',
            showadd: true
        };
        if (filterStr && sender.get_textChanged()) {
            parameter.filtervalue = filterStr;
        }
        sender.set_showMDI(true);
        sender.set_selectorPageParams(parameter);
        sender.set_selectorPage(url);
    },
    // checkMobile: function (sender) {
    //     var form = sender.get_form();
    //     var phone = form.mobile.get_value();
    //     var reg = /^\d{11}$/; //正规表达式对象
    //     if (!phone) return;
    //     $common.checkTips(!reg.test(phone), "手机号码格式不正确", form.mobile);
    // },
    doOrder: function (sender) {
        var form = this.get_form();
        var orderLink = form.orderLink.get_value();
        if (!orderLink) return;
        if (form.shopAccount.get_value() == '') {
            $common.alert("请输入网店账号");
            return;
        }
        window.open(orderLink, "_blank");
    },
    doAuth: function (sender) {
        var form = sender.get_form();
        var saveData = form.saveData();
        var needAuth = form.needAuth.get_value();
        if (!needAuth) return;
        if (!saveData.eshopAccount) {
            if (form.MTgrid) {
                var rowData = form.MTgrid.get_selectedRowData();
                saveData.eshopAccount = rowData.eshopAccount;
            }
        }
        if (form.shopAccount.get_value() == '' && saveData.eshopAccount == '') {
            $common.alert("请输入网店账号");
            return;
        }
        var msg = this.doAuthCheck(sender);
        if (msg) {
            $common.alert(msg);
            return;
        }
        this.doBtnSaveBeforeAuth(sender);
        form.btnSave.set_enabled(false);
    },
    doBtnSaveBeforeAuth: function (sender) {
        this.authorizeAction(sender);
    },

    authorizeAction: function (sender) {
        var form = sender.get_form();
        var authType = form.authType.get_value();
        var mode = this.get_context('mode');
        var shopType = form.shopType.get_value();
        var otypeId = form.otypeId.get_value();
        var fullName = form.orgName.get_value();
        var param = {otypeId: otypeId, shopType: shopType, mode: mode, fullName: fullName};
        if (this.mutiSelectAppkey != null) {
            param.mutiSelectAppkey = this.mutiSelectAppkey
        }

        var service = this.get_service();
        if (authType == 1 || authType == 5)//需要弹出授权页面
        {
            var authresult =
                $common.ajaxSync({
                    url: "sale/eshoporder/eshop/getAuthUrl",
                    data: param,
                    router: 'ngp'
                });
            if (!authresult.code || authresult.code != '200') {
                if (authresult.message != null || authresult.message != '') {
                    if (authresult.message.includes(':')) {
                        $common.alert(authresult.message.split(':')[1]);
                    }
                } else {
                    $common.alert("获取授权链接失败");
                }
                return;
            }
            var authUrl = authresult.data;
            this.openEShopAuthorizeURL(authUrl);
        } else if (authType == 2) {
            param.appSecret = form.appSecret.get_value();
            param.appKey = form.appKey.get_value();
            var result =
                $common.ajaxSync({
                    url: "sale/eshoporder/eshop/doAuth",
                    data: param,
                    router: 'ngp'
                });
            if (result.code != "200") {
                $common.alert(result.message);
                return;
            }
            var data = result ? result.data : null;
            if (data && data.success) {
                this.sendMessage("授权");
                $common.showOk("授权成功");
                return;
            }
            if (data && !data.success && data.message) {
                $common.alert(data.message);
            }
        }


    },
    openEShopAuthorizeURL: function (authUrl) {
        if (!authUrl) {
            return;
        }
        $common.openwin(authUrl);
    },
    doAuthCheck: function (sender) {
        var form = sender.get_form();
        var needAuth = form.needAuth.get_value();
        var shopType = form.shopType.get_value();
        var otypeId = form.otypeId.get_value();
        var eshopAccount = form.shopAccount.get_value();
        var param = {otypeId: otypeId, shopType: shopType, eshopAccount: eshopAccount};
        if (!needAuth) return;
        var result =
            $common.ajaxSync({
                url: "sale/eshoporder/eshop/authorizeCheck",
                data: param,
                router: 'ngp'
            });
        var data = result ? result.data : null;
        if (data != null && !data.success) {
            return data.message;
        }


        return null;
    },
    doInderectAuth: function (sender) {
        var form = sender.get_form();
        var authType = form.authType.get_value();
        var mode = this.get_context('mode');
        var shopType = form.shopType.get_value();
        var otypeId = form.otypeId.get_value();
        var fullName = form.orgName.get_value();
        var param = {otypeId: otypeId, shopType: shopType, mode: mode, fullName: fullName};
        if (this.mutiSelectAppkey != null) {
            param.mutiSelectAppkey = this.mutiSelectAppkey
        }

        if (authType == 2 || authType == 5) {
            param.appSecret = form.appSecret.get_value();
            param.appKey = form.appKey.get_value();
            var result =
                $common.ajaxSync({
                    url: "sale/eshoporder/eshop/doAuth",
                    data: param,
                    router: 'ngp'
                });
            if (result.code != "200") {
                $common.alert(result.message);
                return;
            }
            var data = result ? result.data : null;
            if (data && data.success) {
                this.authStatus.authorizedAuth = true;
                this.authStatus.appSecret = param.appSecret;
                this.sendMessage("授权");
                $common.showOk("授权成功");
                return;
            }
            if (data && !data.success && data.message) {
                $common.alert(data.message);
            }
        }
    },
    onEShopTypeChange: function () {
        var form = this.get_form();
        var pageParam = form.get_pageParams();
        var shopType = pageParam.eshopType;
        if (shopType == 889) {
            form.processType.set_enabled(true);
        } else {
            form.processType.set_value(1);
            form.processType.set_enabled(false);
        }
        if (shopType == 891) {
            form.edKType.set_enabled(false);
        }
        form.tmallSpecialSale.set_visible(false);
        if (shopType == 90) {
            form.tmallSpecialSale.set_visible(true);
        }
        this._shopType = shopType;
        this.createControls(form);
        //连锁网店处理
        if (shopType == 114 && this.buyerparam.mainEshop && this.buyerparam.groupId != null && this.buyerparam.groupId != "") {
            form.BranchStorePanel.set_visible(true);
            form.onlineEshopId.set_visible(false);
            form.platformauthType.set_visible(true);
            form.platformauthType.set_selectedIndex(1);
            this.getBranchEshop();
        }
        //单店处理
        if (shopType == 114 && this.buyerparam.mainEshop == false && this.buyerparam.groupId != null && this.buyerparam.groupId != "") {
            form.platformauthType.set_visible(true);
            form.platformauthType.set_selectedIndex(0);
        }
        //连锁网店处理
        if ((shopType == 116 || shopType == 134 || shopType == 164) && this.buyerparam.mainEshop && this.buyerparam.groupId != null) {
            form.BranchStorePanelMT.set_visible(true);
            form.shopAccount.set_visible(false);
            form.platformauthType.set_visible(true);
            form.platformauthType.set_selectedIndex(1);
        }
        //单店处理
        if ((shopType == 116 || shopType == 134 || shopType == 164) && this.buyerparam.mainEshop == false && this.buyerparam.groupId != null) {
            form.platformauthType.set_visible(true);
            form.platformauthType.set_selectedIndex(0);
        }
    },

    doEditEshopAccount: function (sender, args) {
        if (sender && !sender.get_dataField()) {
            return;
        }
        var row = args.get_rowData();
        if (row && row.eshopAccount && row.eshopAccount != '') {
            sender.set_enabled(false);
            return;
        }
        sender.set_enabled(true);
    },

    getBranchEshop: function () {
        var form = this.get_form();
        var pageParam = form.get_pageParams();
        var param = new Object();
        param.eshopAccount = form.shopAccount.get_value();
        param.shoptype = pageParam.eshopType;
        param.otypeId = pageParam.id;
        var res =
            $common.ajaxSync({
                url: "sale/eshoporder/eshop/getBranchEshop",
                data: param,
                router: 'ngp'
            });
        if (res.code == '200') {
            form.grid.dataBind(res.data);
        }
    },
    createControls: function (form) {
        var fields = form.fieldInfos.get_value();
        var shoptype = this._shopType;
        form.platformEshopSnType.set_visible(false);
        form.vendorId.set_visible(false);
        form.token.set_visible(false);
        form.platformEshopId.set_visible(false);
        form.onlineEshopId.set_visible(false);
        form.appKey.set_visible(false);
        form.appSecret.set_visible(false);
        if (!fields) {
            return;
        }
        form.shopAccount.set_label("网店账号");
        form.shopAccount.set_nullDisplayText('请填写网店账号');
        for (var i = 0; i < fields.length; i++) {
            var data = fields[i];
            var isRequired = data.required ? true : false;
            var title = data.title ? data.title : data.description;
            var hintText = "";
            var labelText = title;
            if (title.length > 8) {
                hintText = title;
                labelText = title.substring(0, 8) + "...";
            }
            if (data.controllerType == "TEXT" && data.field == "eshopAccount") {
                form.shopAccount.set_label(title);
                form.shopAccount.set_nullDisplayText('请填写' + title);
                form.shopAccount.set_required(isRequired);
                if (isRequired) {
                    form.shopAccount.set_labelCssClass("MustCharLeft");
                } else {
                    form.shopAccount.remove_labelCssClass("MustCharLeft");
                }
                if (data)
                    continue;
            }
            if (data.controllerType == "TEXT" && data.field == "appSecret") {
                form.appSecret.set_label(title);
                form.appSecret.set_nullDisplayText('请填写' + title);
                form.appSecret.set_required(isRequired);
                form.appSecret.set_visible(true);
                if (isRequired) {
                    form.appSecret.set_labelCssClass("MustCharLeft");
                }
                if (data)
                    continue;
            }

            if (data.controllerType == "SELECTOR" && data.field == "platformEshopSnType") {
                form.platformEshopSnType.set_label(title);
                // if (form.get_context("platformEshopSnType") == null) {
                form.platformEshopSnType.set_items(data.dropdownDataSource);
                // }
                var platformEshopSnType = this.get_context('platformEshopSnType');
                var containValue = false;
                if (data.dropdownDataSource != null) {
                    for (var index = 0; index < data.dropdownDataSource.length; index++) {
                        if (platformEshopSnType === data.dropdownDataSource[index].value) {
                            containValue = true;
                            break;
                        }
                    }
                }
                if (!containValue) {
                    if (data.defaultValue !== null && data.defaultValue !== '') {
                        form.platformEshopSnType.set_value(data.defaultValue);
                    }
                } else {
                    form.platformEshopSnType.set_value(platformEshopSnType);
                }
                form.platformEshopSnType.set_visible(true);
                form.platformEshopSnType.set_nullDisplayText("请填写" + title);
                form.platformEshopSnType.set_required(isRequired);
                if (isRequired) {
                    form.platformEshopSnType.set_labelCssClass("MustCharLeft");
                } else {
                    form.platformEshopSnType.remove_labelCssClass("MustCharLeft");
                }
                continue;
            }
            if (data.controllerType == "TEXT" && data.field == "vendorId") {
                form.vendorId.set_label(title);
                form.vendorId.set_visible(true);
                form.vendorId.set_nullDisplayText("请填写" + title);
                if (shoptype == 50) {
                    form.vendorId.set_nullDisplayText(data.hintText);
                }
                form.vendorId.set_required(isRequired);
                if (isRequired) {
                    form.vendorId.set_labelCssClass("MustCharLeft");
                } else {
                    form.vendorId.remove_labelCssClass("MustCharLeft");
                }
                continue;
            }
            if (data.controllerType == "TEXT" && data.field == "token") {
                form.token.set_label(title);
                form.token.set_visible(true);
                form.token.set_nullDisplayText("请填写" + title);
                form.token.set_required(isRequired);
                if (isRequired) {
                    form.token.set_labelCssClass("MustCharLeft");
                } else {
                    form.token.remove_labelCssClass("MustCharLeft");
                }
                continue;
            }
            if (data.controllerType == "TEXT" && data.field == "platformEshopId") {
                form.platformEshopId.set_label(title);
                form.platformEshopId.set_visible(true);
                form.platformEshopId.set_nullDisplayText("请填写" + title);
                form.platformEshopId.set_required(isRequired);
                if (isRequired) {
                    form.platformEshopId.set_labelCssClass("MustCharLeft");
                } else {
                    form.platformEshopId.remove_labelCssClass("MustCharLeft");
                }
                continue;
            }
            if (data.controllerType == "TEXT" && data.field == "onlineEshopId") {
                form.onlineEshopId.set_label(title);
                form.onlineEshopId.set_visible(true);
                form.onlineEshopId.set_nullDisplayText("请填写" + title);
                form.onlineEshopId.set_required(isRequired);
                if (isRequired) {
                    form.onlineEshopId.set_labelCssClass("MustCharLeft");
                } else {
                    form.onlineEshopId.remove_labelCssClass("MustCharLeft");
                }
                continue;
            }
            var elt = form[data.field];
            var controllerType = data.controllerType;
            if (elt && controllerType == "TEXT") {
                elt.set_visible(true);
                if (Sys.UI.Controls.TextEdit.isInstanceOfType(elt) && data.hintText) {
                    elt.set_nullDisplayText(data.hintText);
                }
                if (title) {
                    elt.set_nullDisplayText('请填写' + title);
                }
                if(labelText){
                    elt.set_label(labelText);
                }
                if(hintText){
                    elt.set_labelHint(hintText);
                }

                if (data.field == "shopAccount") {
                    form.shopAccountName.set_value(title);
                }
                form.shopAccountName.set_required(isRequired);
                if (isRequired) {
                    form.shopAccountName.set_labelCssClass("MustCharLeft");
                } else {
                    form.shopAccountName.remove_labelCssClass("MustCharLeft");
                }
                // else if (data.field == "appKey") {
                //     this.appKey = title;
                // } else if (data.field == "appSecret") {
                //     this.appSecret = title;
                // }
            }
        }
    },
    showToken: function () {
        var form = this.get_form();
        var chencked = form.radio2.get_checked();
        form.token.set_visible(false);
        if (chencked) {
            form.token.set_visible(true);
            form.token.set_label("Token:");
            form.usualAuth.set_visible(false);
        }
    },
    doBuyerResolved: function (sender, arg) {
        var form = this.get_form();
        var telephone = sender.telephone;
        var mobile = sender.phone;
        var nick = sender.receiverName;
        var oldNick = form.senderName.get_value();
        var oldMobile = form.mobile.get_value();
        var oldPhone = form.phone.get_value();
        if (nick) {
            form.senderName.set_value(nick);
        }
        if (telephone) {
            form.mobile.set_value(telephone);
        }
        if (mobile) {
            form.phone.set_value(mobile);
        }
    },
    loadQualityWarehouse: function (sender) {
        var form = this.get_form();
        var pageParam = form.get_pageParams();
        var otypeId = pageParam.id;
        sender.set_selectorPage('/sale/jarvis/DeliverBill/customer/QualityWarehouse.gspx');
        sender.set_selectorPageParams({eshopId: otypeId});
    },
    loadQualityOrg: function (sender) {
        var _this = this;
        var form = _this.get_form();
        var saveData = form.saveData();
        var warehouse = form.qualityWarehouse.get_value();
        if (typeof (form.qualityWarehouse.get_value()) != 'object') {
            warehouse = new Object();
            warehouse.warehouseCode = saveData.eshopQicConfig.platformQualityWarehouseCode;
        }
        var pageParam = form.get_pageParams();
        var otypeId = pageParam.id;
        sender.set_selectorPage('/sale/jarvis/DeliverBill/customer/QualityOrg.gspx');
        sender.set_selectorPageParams({eshopId: otypeId, warehouse: warehouse});
    },
    doChooseOrg: function (sender, args) {
        var form = sender.get_form();
        var selectForm = args.get_form();
        form.qualityOrg.set_value(selectForm.org);
        form.qualityOrg.set_text(selectForm.org.agencyName);
    },
    setProductDataSource: function (sender) {
        var _this = this;
        var form = _this.get_form();
        var qualityBtypeItem = form.qualityBtype.get_selectedItem();
        form.qualityBtypeProduct.set_dataSource(qualityBtypeItem.deliveryProducts || []);
        form.qualityBtypeProduct.set_value(null);
        _this.setInsureVisible(sender);
    },
    setInsureVisible: function (sender) {
        var _this = this;
        var form = _this.get_form();
        var qualityBtypeItem = form.qualityBtypeProduct.get_selectedItem();
        // form.qualityBtypeInsureTotal.set_visible(qualityBtypeItem&&qualityBtypeItem.enableInsure == "1");
        // form.qualityBtypeInsureType.set_visible(qualityBtypeItem&&qualityBtypeItem.enableInsure == "1");
        form.qualityBtypeInsureTotal.set_visible(false);
        form.qualityBtypeInsureType.set_visible(false);
        form.qualityBtypeInsure.set_visible(qualityBtypeItem && qualityBtypeItem.enableInsure == "1");
        if (!(qualityBtypeItem && qualityBtypeItem.enableInsure == "1")) {
            form.qualityBtypeInsure.set_checked(qualityBtypeItem && qualityBtypeItem.enableInsure == "1");
        }
        // form.qualityBtypeInsure.set_checked(qualityBtypeItem&&qualityBtypeItem.enableInsure == "1");
        if (qualityBtypeItem) {
            form.qualityBtypeInsureType.set_dataSource(qualityBtypeItem.insureTypeList || []);
        }
        _this.doQualityBtypeInsureChange(sender);
    },
    setBackupProductDataSource: function (sender) {
        var _this = this;
        var form = _this.get_form();
        var qualityBtypeBackupItem = form.qualityBtypeBackup.get_selectedItem();
        if (qualityBtypeBackupItem) {
            form.qualityBtypeBackupProduct.set_dataSource(qualityBtypeBackupItem.deliveryProducts || []);
        } else {
            form.qualityBtypeBackupProduct.set_dataSource([]);
        }
        form.qualityBtypeBackupProduct.set_value(null);
        _this.setBackupInsureVisible(sender);
    },
    setBackupInsureVisible: function (sender) {
        var _this = this;
        var form = _this.get_form();
        var qualityBtypeBackupItem = form.qualityBtypeBackupProduct.get_selectedItem();
        form.qualityBtypeBackupInsureTotal.set_visible(false);
        form.qualityBtypeBackupInsureType.set_visible(false);
        form.qualityBtypeBackupInsure.set_visible(qualityBtypeBackupItem && qualityBtypeBackupItem.enableInsure == "1");
        if (!(qualityBtypeBackupItem && qualityBtypeBackupItem.enableInsure == "1")) {
            form.qualityBtypeBackupInsure.set_checked(qualityBtypeBackupItem && qualityBtypeBackupItem.enableInsure == "1");
        }
        if (qualityBtypeBackupItem) {
            form.qualityBtypeBackupInsureType.set_dataSource(qualityBtypeBackupItem.insureTypeList || []);
        }
        _this.doQualityBtypeBackupInsureChange(sender);

    },
    doQualityBtypeInsureChange: function (sender) {
        var _this = this;
        var form = _this.get_form();
        var qualityBtypeInsure = form.qualityBtypeInsure.get_value();
        form.qualityBtypeInsureTotal.set_visible(qualityBtypeInsure);
        form.qualityBtypeInsureType.set_visible(qualityBtypeInsure);
        // form.qualityBtypeInsure.set_visible(qualityBtypeInsure);
    },
    doQualityBtypeBackupInsureChange: function (sender) {
        var _this = this;
        var form = _this.get_form();
        var qualityBtypeBackupInsure = form.qualityBtypeBackupInsure.get_value();
        form.qualityBtypeBackupInsureTotal.set_visible(qualityBtypeBackupInsure);
        form.qualityBtypeBackupInsureType.set_visible(qualityBtypeBackupInsure);
    },
    doChooseWarehouse: function (sender, args) {
        var form = sender.get_form();
        var selectForm = args.get_form();
        this._qualityWarehouse = selectForm.warehouse;
        form.qualityWarehouse.set_value(selectForm.warehouse);
        form.qualityWarehouse.set_text(selectForm.warehouse.warehouseName);
        form.address.set_text(selectForm.warehouse.fullAddress);
        form.qualityOrg.set_value(null);
        form.qualityOrg.set_text("");
    },
    doExport: function () {
        var form = this.get_form();
        var _this = this;
        this._childForm = form.import.showModal('导入子网店', {});
        this._childForm.add_closed(function (popup) {
            if (_this.exportState) {
                _this.getMeituanBrachEshop();
            }
        });
    },
    downloadRealtionModel: function (sender) {
        $common.download("/sale/eshoporder/template/" + encodeURI("子网店导入模板.xlsx"));
    },
    getMeituanBrachEshop: function () {
        var form = this.get_form();
        var _this = this;
        var res = $common.ajaxSync({
            url: "sale/eshoporder/eshop/getMeituanBrachEshop",
            data: null,
            router: 'ngp'
        });
        if (res.code == 200) {
            if (res.data && res.data.length > 0) {
                if (_this._eshopBranchList.length > 0) {
                    var rows = _this._eshopBranchList;
                    for (var i = 0; i < rows.length; i++) {
                        res.data.push(rows[i]);
                    }
                }
                form.MTgrid.dataBind(res.data);
            }
        }
    },
    doIconColumnClick: function (sender, args) {
        var form = this.get_form();
        var grid = this.get_form().MTgrid;
        if (args.get_buttonIndex() == 0) {
            var shopType = form.get_pageParams().shopType;
            // var platformauthType = form.platformauthType.get_value();
            if (shopType === 164)
                grid.insertRowData(args.get_rowIndex(), {
                    enabled: true
                });
            else {
                grid.insertRowData(args.get_rowIndex(), {
                    auth: "<font color='white' style='color:#2c57a0;border-radius:3px;padding:2px;padding-left:4px;padding-right:4px'>授权</font>",
                    enabled: true
                });
            }
            //grid.insertRow(args.rowIndex);
        } else if (args.get_buttonIndex() == 1) {
            $common.alert("确认删除？", function (res) {
                if (res) {
                    var eshopBranch = [];
                    if (form.MTgrid) {
                        if (form.MTgrid.get_dataSource()) {
                            eshopBranch = form.MTgrid.get_dataSource();
                        } else if (form.MTgrid.get_allItems() && form.MTgrid.get_allItems().length > 0) {
                            eshopBranch = form.MTgrid.get_allItems();
                        }
                    }
                    if (eshopBranch.length > 1) {
                        grid.deleteRow(args.get_rowIndex());
                    }
                }
            });
        }
    },
    doStopEshop: function (sender) {
        var form = sender.get_form();
        var grid = form.grid;
        var rowDatas = grid.get_selectedItems();

        if (!rowDatas || rowDatas.length == 0) {
            $common.showInfo("请至少选择一条网店！");
            return;
        }
        for (var i = 0; i < rowDatas.length; i++) {
            rowDatas[i].enabled = false;
            this.get_form().grid.modifyRowData(rowDatas[i].__rowIndex, rowDatas[i]);

        }

    },
    doOpenEshop: function (sender) {
        var form = sender.get_form();
        var grid = form.grid;
        var rowDatas = grid.get_selectedItems();
        if (!rowDatas || rowDatas.length == 0) {
            $common.showInfo("请至少选择一条网店！");
            return;
        }
        for (var i = 0; i < rowDatas.length; i++) {
            rowDatas[i].enabled = true;
            this.get_form().grid.modifyRowData(rowDatas[i].__rowIndex, rowDatas[i]);

        }
    },
    handlePlatformuthType: function (sender) {
        var form = this.get_form();
        var shopType = form.get_pageParams().shopType;
        if (shopType == 114) {
            form.onlineEshopId.set_visible(true);
            form.BranchStorePanel.set_visible(false);
            var platformauthType = form.platformauthType.get_value();
            if (platformauthType == 1) {
                form.onlineEshopId.set_visible(false);
                form.BranchStorePanel.set_visible(true);
            }
        }
        if (shopType == 116 || shopType == 134 || shopType == 164) {
            form.BranchStorePanelMT.set_visible(false);
            form.shopAccount.set_visible(true);
            form.usualAuth.set_visible(true);
            var platformauthType = form.platformauthType.get_value();
            if (platformauthType == 1) {
                form.BranchStorePanelMT.set_visible(true);
                form.shopAccount.set_visible(false);
                form.usualAuth.set_visible(false);
                if (form.MTgrid._newRowDatas && form.MTgrid._newRowDatas.length == 0) {
                    form.MTgrid.appendRowData({
                        auth: "<font color='white' style='color:#2c57a0;border-radius:3px;padding:2px;padding-left:4px;padding-right:4px'>授权</font>",
                        operation: "<font color='white' style='color:#2c57a0;border-radius:3px;padding:2px;padding-left:4px;padding-right:4px'>订购</font>"
                    });
                }
            }
        }

    },

    uploadFile: function (sender) {
        var form = sender.get_form();
        this._childForm.submitBtn.set_enabled(false);
        if (!form.importFile.get_text()) {
            this._childForm.submitBtn.set_enabled(true);
            $common.checkTips(!form.importFile.get_text(), "请选择要导入的文件！", form.importFile);
        }
        var headers = {}; // 自定义请求头，传了该参数，则使用平台新的提交上传文件..
        var panelID = sender.get_tag(); // 这样做的目的是平台内部会根据panel.saveData()局部读取数据，而不是form.saveData()会触发全局验证
        $uploader(form[panelID], // 只会当前控件对应容器的FileUpload触发panel的saveData
            $createDelegate(this, this.doImportSucceeded),
            $createDelegate(this, this.doImportFailed),
            function (data) { // 支持动态构造服务端需要的参数，因此不是必须再gspx写隐藏域<HiddenField>也可以传参
            }, headers); // 自定义请求头，传了该参数，则使用平台新的提交上传文件..
        this.exportState = true;
    }
    ,
    doImportSucceeded: function (sender, result) {
        if (result.code != '200') {
            $common.showError("导入失败，" + result.message);
            return;
        }
        var _this = this;
        _this._activeTimer = new Sys.Timer();
        this._stop = false;
        var intervalCount = 2000;
        _this._activeTimer.set_interval(intervalCount);
        _this._activeTimer.set_enabled(true);
        _this._activeTimer.add_tick(Function.createDelegate(this, function () {
            _this.getMessage(result.data);
        }));

    },
    doImportFailed: function (sender, result) {
        var form = this.get_form();
        if (result) {
            $common.alert(result);
        }
        _this._childForm.submitBtn.set_enabled(true);
    },
    getMessage: function (processId) {
        var form = this.get_form();
        if (null == form) {
            return;
        }
        var requestParam = new Object();
        requestParam.requestStr = processId;
        var _this = this;
        if (_this._childForm.processBar) {
            _this._childForm.processBar.set_value(80);
        } else {
            _this._activeTimer.set_enabled(false);
            return;
        }
        _this._childForm.processBar.set_value(80);
        this.get_service().post("sale/eshoporder/common/getProcessMsg", requestParam,
            function (res) {
                var obj = res.data;
                if (!obj) {
                    return;
                }
                if (obj.completed) { //已经结束
                    _this._childForm.processBar.set_value(100);
                    _this._activeTimer.set_enabled(false);
                    _this._childForm.submitBtn.set_enabled(true);
                    if (obj.message) {
                        _this._childForm.info.set_text(obj.message);
                        _this._stop = true;
                    }
                }
            }, function (error) {
                if (_this._activeTimer) {
                    _this._activeTimer.set_enabled(false);
                }
                _this._stop = true;
                $common.alert(error);
            }
            , false);
    },

    doAuthCheckByShopAccount: function (sender, param) {
        if (!param.oldShopAccount || param.oldShopAccount === param.eshopAccount) {
            return false;
        }
        if (!param.auth) {
            return false;
        }
        if (!this._ModifyAccountNeedReAuth) {
            return false;
        }
        return true;
    },

    doReAuth: function (sender, param) {
        if (!param.modifyAccountNeedReauth) {
            return;
        }
        var needAuth = form.needAuth.get_value();
        form.needAuth.set_value(true);
        var msg = this.doAuthCheck(sender);
        if (msg) {
            $common.alert(msg);
            return;
        }
        this.doBtnSaveBeforeAuth(sender);
        form.needAuth.set_value(needAuth);
    },

    logEndTime: function (startObj) {
        if (startObj != null) {
            startObj.endTiming();// 触发上报
        }
    },

    logStartTime: function (logObjMsg) {
        var startObj = null;
        if (window.$startTiming) {
            startObj = $startTiming(logObjMsg);
        }
        return startObj;
    },
    onExpandMore:function ( sender){
        var form = sender.get_form();
        this.setMoreInfoControlVisible(form, true);
    },

    setMoreInfoControlVisible:function (form, visible){
        form.orderRefundEffective.set_visible(visible);
        form.customerSupply.set_visible(visible);
        form.customerSupplier.set_visible(visible);
        form.shopProduct.set_visible(visible);
        form.topicOrder.set_visible(visible);
        form.topicRefund.set_visible(visible);
        form.topicShip.set_visible(visible);
        form.showLess.set_visible(visible);
        form.expandMore.set_visible(!visible);
        if (visible) {
            form.customerSupplier.set_visible(this.controlVisible.customerSupplierVisible);
            form.shopProduct.set_visible(this.controlVisible.shopProductVisible);
            form.topicRefund.set_visible(this.controlVisible.refundVisible);
            form.eshopQicConfig.set_visible(form.platformQualityStatus.get_visible() && form.platformQualityStatus.get_checked());
        }
    },

    onShowLess:function ( sender){
        var form = sender.get_form();
        this.setMoreInfoControlVisible(form, false);
    }
};
sale.eshoporder.eshop.EShopConfigAction.registerClass('sale.eshoporder.eshop.EShopConfigAction', Sys.UI.PageAction);
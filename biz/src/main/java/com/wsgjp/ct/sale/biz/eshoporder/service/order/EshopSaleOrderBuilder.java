
package com.wsgjp.ct.sale.biz.eshoporder.service.order;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wsgjp.ct.bill.core.handle.entity.enums.BillBusinessType;
import com.wsgjp.ct.bill.core.handle.entity.enums.BillDeliverType;
import com.wsgjp.ct.bill.core.handle.entity.enums.BillPostState;
import com.wsgjp.ct.bill.core.handle.entity.enums.DeliverProcessTypeEnum;
import com.wsgjp.ct.common.enums.core.entity.BaseMarkBigDataEntity;
import com.wsgjp.ct.common.enums.core.entity.LogisticsInfo;
import com.wsgjp.ct.common.enums.core.entity.MarkData;
import com.wsgjp.ct.common.enums.core.entity.QualificationInformation;
import com.wsgjp.ct.common.enums.core.enums.BaseOrderMarkEnum;
import com.wsgjp.ct.common.enums.core.enums.SelfDeliveryModeEnum;
import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.ngp.export.sdk.imports.dto.ValidResult;
import com.wsgjp.ct.sale.biz.bifrost.mapper.BifrostEshopMapper;
import com.wsgjp.ct.sale.biz.bifrost.mapper.EshopTmcOrderMsgMapper;
import com.wsgjp.ct.sale.biz.bifrost.service.BifrostEshopOrderService;
import com.wsgjp.ct.sale.biz.bifrost.util.FreightUtils;
import com.wsgjp.ct.sale.biz.common.util.CurrentProfileUtil;
import com.wsgjp.ct.sale.biz.common.util.VersionUtil;
import com.wsgjp.ct.sale.biz.eshoporder.api.PtypeApi;
import com.wsgjp.ct.sale.biz.eshoporder.api.request.BaseBtype;
import com.wsgjp.ct.sale.biz.eshoporder.api.request.BtypeQueryParameter;
import com.wsgjp.ct.sale.biz.eshoporder.api.request.BtypeQueryRequest;
import com.wsgjp.ct.sale.biz.eshoporder.api.response.BaseBtypeResponse;
import com.wsgjp.ct.sale.biz.eshoporder.api.response.SaveEtypeResponse;
import com.wsgjp.ct.sale.biz.eshoporder.config.SaleBizConfig;
import com.wsgjp.ct.sale.biz.eshoporder.config.sysdata.AutoSubmitConfig;
import com.wsgjp.ct.sale.biz.eshoporder.config.sysdata.EshopOrderSysDataConfig;
import com.wsgjp.ct.sale.biz.eshoporder.constant.eshop.PlatformStoreMappingConstants;
import com.wsgjp.ct.sale.biz.eshoporder.dto.order.BroadcastSessionDto;
import com.wsgjp.ct.sale.biz.eshoporder.entity.base.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.order.DownloadType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.order.OrderCreateType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.order.OrderDownloadCacheType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.enums.stock.StockRuleType;
import com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopConfig;
import com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopPlatformStoreMapping;
import com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopQicConfig;
import com.wsgjp.ct.sale.biz.eshoporder.entity.malldeduction.MallDeductionConfigEntity;
import com.wsgjp.ct.sale.biz.eshoporder.entity.order.*;
import com.wsgjp.ct.sale.biz.eshoporder.entity.orderImport.SaleOrderParam;
import com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductSkuMapping;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.EshopSaleOrderDownloadTask;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QueryBuyerRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.QuerySkuMappingRequest;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.base.QueryBaseTypeParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.standardApi.platformBtype.PlatformBtypeMapping;
import com.wsgjp.ct.sale.biz.eshoporder.entity.request.stock.QueryRecordDetailParam;
import com.wsgjp.ct.sale.biz.eshoporder.entity.stock.QueryStockRuleParameter;
import com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockRuleDetail;
import com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncRule;
import com.wsgjp.ct.sale.biz.eshoporder.entity.stock.StockSyncRuleBase;
import com.wsgjp.ct.sale.biz.eshoporder.entity.strategy.EshopOrderStrategyDto;
import com.wsgjp.ct.sale.biz.eshoporder.entity.strategy.EshopOrderStrategyPtypeDetailDto;
import com.wsgjp.ct.sale.biz.eshoporder.entity.strategy.StrategyPtypeDto;
import com.wsgjp.ct.sale.biz.eshoporder.mapper.*;
import com.wsgjp.ct.sale.biz.eshoporder.service.baseinfo.AddressResolution;
import com.wsgjp.ct.sale.biz.eshoporder.service.eshop.EshopService;
import com.wsgjp.ct.sale.biz.eshoporder.service.malldeduction.EshopMallDeductionRateSettingService;
import com.wsgjp.ct.sale.biz.eshoporder.service.orderimport.EShopSaleOrderImportManager;
import com.wsgjp.ct.sale.biz.eshoporder.service.product.EshopProductDataService;
import com.wsgjp.ct.sale.biz.eshoporder.service.receiver.EshopBuyerService;
import com.wsgjp.ct.sale.biz.eshoporder.service.stock.SaleQtyMarkService;
import com.wsgjp.ct.sale.biz.eshoporder.service.stock.StockRuleService;
import com.wsgjp.ct.sale.biz.eshoporder.service.strategy.EshopOrderStrategyService;
import com.wsgjp.ct.sale.biz.eshoporder.util.CommonUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.EShopOrderMarkUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.biz.eshoporder.util.SysLogUtil;
import com.wsgjp.ct.sale.biz.jarvis.dto.BillDeliverDTO;
import com.wsgjp.ct.sale.biz.shopsale.mapper.BaseInfoMapper;
import com.wsgjp.ct.sale.common.constant.KeyWordConstant;
import com.wsgjp.ct.sale.common.entity.EshopInfo;
import com.wsgjp.ct.sale.common.entity.freight.FreightMapping;
import com.wsgjp.ct.sale.common.entity.order.BtypeExtendFieldRequestDto;
import com.wsgjp.ct.sale.common.enums.eshoporder.*;
import com.wsgjp.ct.sale.common.syssecretinfo.BaseSysSecretInfo;
import com.wsgjp.ct.sale.common.syssecretinfo.SysSecretInfoService;
import com.wsgjp.ct.sale.platform.config.EshopSystemParams;
import com.wsgjp.ct.sale.platform.dto.order.EshopOrderDetailEntity;
import com.wsgjp.ct.sale.platform.dto.order.EshopOrderEntity;
import com.wsgjp.ct.sale.platform.dto.order.ProductGiftRelation;
import com.wsgjp.ct.sale.platform.dto.order.entity.*;
import com.wsgjp.ct.sale.platform.entity.entities.EshopNotifyChange;
import com.wsgjp.ct.sale.platform.entity.entities.FreightInfo;
import com.wsgjp.ct.sale.platform.entity.request.buyer.FillSearchDigestRequest;
import com.wsgjp.ct.sale.platform.entity.request.order.AuditCancelOrderRequest;
import com.wsgjp.ct.sale.platform.entity.response.freight.QueryFreightMappingParameter;
import com.wsgjp.ct.sale.platform.entity.response.order.AuditCancelOrderResponse;
import com.wsgjp.ct.sale.platform.enums.*;
import com.wsgjp.ct.sale.platform.exception.BusinessSupportException;
import com.wsgjp.ct.sale.platform.factory.EshopFactory;
import com.wsgjp.ct.sale.platform.factory.EshopFactoryManager;
import com.wsgjp.ct.sale.platform.feature.order.EshopAuditCancelOrderFeature;
import com.wsgjp.ct.sale.platform.sdk.util.PlatformCommonUtil;
import com.wsgjp.ct.sale.platform.utils.BeanUtils;
import com.wsgjp.ct.sale.sdk.stock.entity.StockRecordOrderDetail;
import com.wsgjp.ct.sis.client.entity.EncryptFullAdapter;
import com.wsgjp.ct.support.business.Money;
import com.wsgjp.ct.support.business.MoneyUtils;
import com.wsgjp.ct.support.business.pubsystemlog.PubSystemLogService;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.global.GlobalConfig;
import com.wsgjp.ct.support.global.entity.SysGlobalConfig;
import com.wsgjp.ct.support.redis.factory.CacheType;
import ngp.idgenerator.UId;
import ngp.redis.RedisPoolFactory;
import ngp.starter.web.base.GeneralResult;
import ngp.utils.*;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.connection.ReturnType;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.security.SecureRandom;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2020-01-06 15:17
 */
public class EshopSaleOrderBuilder {

    private final EshopSaleOrderRelationService relationService;
    private final EshopService eshopService;
    private final PtypeApi baseApi;
    private final EshopPlatformStoreMappingMapper platformStoreMappingMapper;
    private final EshopOrderBaseInfoMapper eshopOrderBaseInfoMapper;
    private final EshopBuyerService buyerService;
    private final SysSecretInfoService sysSecretInfoService;
    private final EshopBroadcastSessionMapper broadcastSessionMapper;
    private final PlatformBtypeMapper platformBtypeMapper;
    private final SaleQtyMarkService markService;
    private final SaleBizConfig bizConfig;
    private final CurrentProfileUtil profileUtil;
    private final EshopMallDeductionRateSettingService mallService;
    private final RedisPoolFactory redisPoolFactory;
    private final EshopOrderBuyerMapper mapper;
    private final EshopQicConfigMapper qicConfigMapper;
    private final BifrostEshopOrderService bifrostEshopOrderService;
    private final EshopOrderStrategyService strategyService;

    private static final Logger logger = LoggerFactory.getLogger(EshopSaleOrderBuilder.class);

    private EshopSaleOrderBuilder() {
        relationService = GetBeanUtil.getBean(EshopSaleOrderRelationService.class);
        sysSecretInfoService = GetBeanUtil.getBean(SysSecretInfoService.class);
        eshopService = GetBeanUtil.getBean(EshopService.class);
        baseApi = GetBeanUtil.getBean(PtypeApi.class);
        platformStoreMappingMapper = GetBeanUtil.getBean(EshopPlatformStoreMappingMapper.class);
        eshopOrderBaseInfoMapper = GetBeanUtil.getBean(EshopOrderBaseInfoMapper.class);
        broadcastSessionMapper = GetBeanUtil.getBean(EshopBroadcastSessionMapper.class);
        buyerService = GetBeanUtil.getBean(EshopBuyerService.class);
        markService = GetBeanUtil.getBean(SaleQtyMarkService.class);
        profileUtil = GetBeanUtil.getBean(CurrentProfileUtil.class);
        mallService = GetBeanUtil.getBean(EshopMallDeductionRateSettingService.class);
        bizConfig = GetBeanUtil.getBean(SaleBizConfig.class);
        platformBtypeMapper = GetBeanUtil.getBean(PlatformBtypeMapper.class);
        redisPoolFactory = GetBeanUtil.getBean(RedisPoolFactory.class);
        mapper = GetBeanUtil.getBean(EshopOrderBuyerMapper.class);
        qicConfigMapper = GetBeanUtil.getBean(EshopQicConfigMapper.class);
        bifrostEshopOrderService = GetBeanUtil.getBean(BifrostEshopOrderService.class);
        strategyService = GetBeanUtil.getBean(EshopOrderStrategyService.class);
    }

    public static EshopSaleOrderBuilder getSingleton() {
        return EshopSaleOrderBuilderSingleton.INSTANCE;
    }

    private static class EshopSaleOrderBuilderSingleton {
        private static final EshopSaleOrderBuilder INSTANCE = new EshopSaleOrderBuilder();
    }


    public EshopSaleOrderEntity build(EshopOrderEntity apiOrder, Otype otype, EshopSaleOrderDownloadTask task, Map<String, EshopBuyer> buyerMap) {
        try {
            EshopSaleOrderEntity order = new EshopSaleOrderEntity();
//            CommonUtil.doLogByEnable(logger,LogLevelEnum.INFO,String.format("profileId:%s, eshopId:%s, tradeOrderId:%s,type:%s,下载订单接受到的实体:%s",CurrentUser.getProfileId(),otype.getId(),apiOrder.getTradeId(),task.getDownloadType(),JsonUtils.toJson(apiOrder)));
            //检查线上订单是否有重复明细
            checkDetail(apiOrder, task);
            // 主体
            buildMainOrder(apiOrder, order, otype);
            // 买家信息
            buildBuyerInfo(apiOrder, order, buyerMap);
            //自提信息
            buildPickUpInfo(apiOrder, order);
            //礼物订单真实买家
            buildGiftOrderRealBuyer(apiOrder, order, task);
            //支付方式,根据返回的【商户收款账号】动态查找 base_payways 表中 编号与之相等的支付方式id，匹配到以后将该表的payway_id字段填充到原始订单上
            buildPayWay(order, task.getTaskId());
            //寄件信息，目前主要是元气在用
            buildSenderInfo(apiOrder, order);
            //门店信息
            buildLocalStoreInfo(apiOrder, order, task.getTaskId());
            //根据编码构建经手人
            getOrderEtype(order, task.getTaskId());
            //构建经手人信息，元气需求
            getOrCreateEtypeId(order, task.getTaskId());
            //往来单位信息
            buildBtypeId(apiOrder, order, otype, task);
            //往来单位扩展信息
            buildBtypeExtendInfo(apiOrder, order);
            // 整单优惠分摊
            doNewSharePreferential(apiOrder, PreferentialType.SELLER);
            doNewSharePreferential(apiOrder, PreferentialType.PLATFORM);
            doNewSharePreferential(apiOrder, PreferentialType.ANCHOR);
            doNewSharePreferential(apiOrder, PreferentialType.PLATFORM_SUBSIDIES);
            // 明细
            buildOrderDetails(apiOrder, order, otype, task);
            //业务类型
            buildDetailBusinessTypeWithOtype(apiOrder, order, otype);
            //构建明细的赠品关系
            buildOrderDetailGiftRelations(apiOrder, order);
            //二次构建主表仓库id
            secondBuildKtypeId(apiOrder, order);
            // 时效性
            buildOrderTerming(apiOrder, order, otype);
            //根据平台platform_batchno+仓库信息，匹配本地批次号
            buildBatchByPlatformBatchno(order);
            // 根据规则ID构建仓库和批次信息
            doBuildBatchInfoByRule(order);
            //插入预占明细
            markService.doMarkPreRecordOrder(order);
            //  订单金额重算
            buildOrderTotal(apiOrder, order);
            // 运费
            doShareBuyerFreightFee(order);
            // 发票信息
            buildInvoiceInfo(apiOrder, order);
            // 物流信息
            buildLocalFreightInfo(apiOrder, order);
            //标记构建 （mentionType 类型的标记目前已弃用，但是这个方法遗留了一些订单状态赋值，这个方法暂时不能去掉）
            buildOrderMentionType(order,otype);
            //发货方
            buildDeliverType(apiOrder.getDeliverType(), order);
            // 扩展信息
            buildOrderExtend(apiOrder, order);
            //qic默认质检地址
            buildQicDefaultAddress(order, otype, task);
            //分销信息
            buildOrderDistributionBuyer(apiOrder, order);
            // 标签提醒
            BuildOrderMainMark(apiOrder, order, otype);
            SysLogUtil.buildAndAddKeyLog(task.getDownloadType().getOperateType(), order, "buildOrderMark");
            //根据标记构建主表和明细的控制标记
            buildControllerMark(order, false);
            //构建 订单管理流程方式
            buildOrderDeliverProcessType(order);
            //构建指定物流信息
//            buildCustomerFreightInfo(apiOrder, order);
            SysLogUtil.buildAndAddKeyLog(task.getDownloadType().getOperateType(), order, "buildOrderControllerMark");
            //检查异常明细
            checkDetail(order, task, "订单build");
            SysLogUtil.buildAndAddKeyLog(task.getDownloadType().getOperateType(), order, "buildOrder");
            return order;
        } catch (Exception ex) {
            logger.error(String.format("buildOrder Exception profileid:%s tradeid:%s message:%s", CurrentUser.getProfileId(),
                    apiOrder.getTradeId(), ex.getMessage()), ex);
            throw new RuntimeException("构建订单报错" + ex.getMessage());
        }
    }

    public void buildOrderDeliverProcessType(EshopSaleOrderEntity order) {
        if (order.getBusinessType() == BillBusinessType.SaleProxy){
            order.setDeliverProcessType(DeliverProcessTypeEnum.NONE);
            return;
        }
        if (BillDeliverType.PLATFORM_SEND == order.getDeliverType()){
            order.setDeliverProcessType(DeliverProcessTypeEnum.NONE);
            return;
        }
        List<EshopSaleOrderDetail> details = order.getOrderDetails();
        //是否全部对应的虚拟商品
        boolean isAllVirtualPtype = details.stream().allMatch(x -> x.getPcategory() == 1);
        if (isAllVirtualPtype) {
            order.setDeliverProcessType(DeliverProcessTypeEnum.NONE);
        }
        BigInteger notSendKeepAccountCode = BigInteger.valueOf(BaseOrderMarkEnum.NOTSEND_KEEPACCOUNT.getCode());
        boolean allDetailMatchMark = order.getOrderDetails() != null
                && order.getOrderDetails().stream().allMatch(d -> {
            List<EshopOrderMarkEntity> marks = d.getOrderDetailMarks();
            return marks != null && marks.stream().anyMatch(m ->
                    notSendKeepAccountCode.equals(m.getMarkCode())
            );
        });
        if (allDetailMatchMark){
            order.setDeliverProcessType(DeliverProcessTypeEnum.NONE);
            return;
        }
        List<Stock> stocks = eshopService.getAllStockList(order.getProfileId(), Collections.singletonList(order.getKtypeId()));
        if (stocks == null || stocks.size() == 0) {
            return;
        }
        Stock stock = stocks.get(0);
        if (stock.getScategory() == 2){
            order.setDeliverProcessType(DeliverProcessTypeEnum.CLOUD_WAREHOUSE_DELIVERY);
            return;
        }
        order.setDeliverProcessType(stock.getDeliverProcessType());
    }

    private void buildGiftOrderRealBuyer(EshopOrderEntity apiOrder, EshopSaleOrderEntity order, EshopSaleOrderDownloadTask task) {
        EshopBuyer giftRealBuyer = new EshopBuyer();
        ReceiverInfo receiverInfo = apiOrder.getReceiverInfo();
        if (receiverInfo == null) {
            logger.error(String.format("profileid:%s tradeid:%s null receiverInfo", CurrentUser.getProfileId()));
            return;
        }
        if (StringUtils.isEmpty(receiverInfo.getRealPlatformBuyerId()) || StringUtils.isEmpty(receiverInfo.getRealPlatformBuyerNick())) {
            return;
        }
        BigInteger newId = UId.newId();
        giftRealBuyer.setBuyerId(newId);
        giftRealBuyer.setProfileId(apiOrder.getProfileId());
        giftRealBuyer.setOtypeId(apiOrder.getEshopId());
        giftRealBuyer.setEshopType(apiOrder.getShopType());
        giftRealBuyer.setCustomerShopAccount(receiverInfo.getRealPlatformBuyerNick());
        giftRealBuyer.setTradeId(StringUtils.isEmpty(receiverInfo.getTradeId()) ? apiOrder.getTradeId() : receiverInfo.getTradeId());
        giftRealBuyer.setOpenAddressId(receiverInfo.getRealPlatformBuyerId());
        List<EncryptFullAdapter> collect = Collections.singletonList(giftRealBuyer);
        if (BigInteger.ZERO.compareTo(giftRealBuyer.getOrgId()) == 0) {
            return;
        }
        buyerService.doEncrypt(collect);
        buyerService.saveEncryptedBuyer(giftRealBuyer);
        if (null == order.getExtend()) {
            return;
        }
        order.getExtend().setRealBuyer(giftRealBuyer);
        order.getExtend().setRealBuyerId(giftRealBuyer.getBuyerId());
        SysLogUtil.add(SysLogUtil.buildLogByOrderId(order.getId(), task.getDownloadType().getOperateType(),
                String.format("礼物订单，已确认真实买家信息：%s", receiverInfo.getRealPlatformBuyerNick())));
    }

    private void buildQicDefaultAddress(EshopSaleOrderEntity order, Otype otype, EshopSaleOrderDownloadTask task) {
        if (!ShopType.WeChatVideoShop.equals(otype.getShopType())) {
            return;
        }
        if (null == order || !QcResultType.NoQualityInspection.equals(order.getExtend().getPlatformQcResult())) {
            return;
        }
        if (null == otype.getEshopConfig() || !otype.getEshopConfig().isPlatformQualityStatus()) {
            return;
        }
        EshopQicConfig qicConfig = qicConfigMapper.getQualityOrgByOtypeIdByPlatformQualityType(otype.getProfileId(), otype.getId(), 0);
        if (null == qicConfig) {
            return;
        }
        BigInteger qicAddressId = qicConfig.getQicAddressId();
        if (null == qicAddressId || BigInteger.ZERO.compareTo(qicAddressId) == 0) {
            return;
        }
        EshopBuyer qicBuyer = mapper.queryBuyerById(order.getProfileId(), qicAddressId);
        if (null == qicBuyer) {
            return;
        }
        order.setEshopBuyer(qicBuyer);
        order.setBuyerId(qicAddressId);
        SysLogUtil.add(SysLogUtil.buildLog(order, task.getDownloadType().getOperateType(), String.format("获取默认质检地址：%s，替换原收货地址", qicBuyer.getFullBuyerInfo())));
    }

    private void buildBatchByPlatformBatchno(EshopSaleOrderEntity order) {
        if (null == order) {
            return;
        }
        List<EshopSaleOrderDetail> details = order.getOrderDetails();
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        for (EshopSaleOrderDetail detail : details) {
            if (null == detail || null == detail.getKtypeId() || BigInteger.ZERO.compareTo(detail.getKtypeId()) == 0 ||
                    null == detail.getExtend() || StringUtils.isEmpty(detail.getExtend().getPlatformBatchno()) ||
                    !detail.isMappingState() || null == detail.getPtypeId() || BigInteger.ZERO.compareTo(detail.getPtypeId()) == 0) {
                continue;
            }
            String platformBatchno = detail.getExtend().getPlatformBatchno();
            BigInteger ktypeId = detail.getKtypeId();
            BigInteger ptypeId = detail.getPtypeId();
            BigInteger skuId = detail.getSkuId();
            BigInteger profileId = detail.getProfileId();
            EshopSaleOrderDetailBatch batchFirst = eshopOrderBaseInfoMapper.getGoodStockBatchFirst(profileId, ktypeId, ptypeId, skuId, platformBatchno);
            if (null == batchFirst) {
                continue;
            }
            batchFirst.setId(UId.newId());
            batchFirst.setEshopOrderDetailId(detail.getId());
            batchFirst.setEshopOrderId(order.getId());
            batchFirst.setUnitQty(batchFirst.getQty());
            batchFirst.setCreateTime(new Date());
            batchFirst.setUpdateTime(new Date());
            detail.setBatch(batchFirst);
        }
    }

    private void buildOrderDetailGiftRelations(EshopOrderEntity apiOrder, EshopSaleOrderEntity order) {
        List<EshopSaleOrderDetail> details = order.getOrderDetails();
        List<EshopOrderDetailEntity> apiDetails = apiOrder.getOrderDetails();
        if (CollectionUtils.isEmpty(apiDetails) || CollectionUtils.isEmpty(details)) {
            return;
        }
        List<ProductGiftRelation> apiDetailGiftRelations = apiDetails.stream().map(EshopOrderDetailEntity::getGiftRelations).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        Map<EshopSaleOrderDetail, List<ProductGiftRelation>> map = new HashMap<>();
        if (CollectionUtils.isEmpty(apiDetailGiftRelations)) {
            return;
        }
        for (EshopSaleOrderDetail detail : details) {
            String tradeOrderDetailId = detail.getTradeOrderDetailId();
            if (StringUtils.isEmpty(tradeOrderDetailId)) {
                continue;
            }
            List<ProductGiftRelation> matchGiftRelations = apiDetailGiftRelations.stream().filter(a -> a.getSourceOid().equals(tradeOrderDetailId)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(matchGiftRelations)) {
                continue;
            }
            map.put(detail, matchGiftRelations);
        }
        for (Map.Entry<EshopSaleOrderDetail, List<ProductGiftRelation>> entry : map.entrySet()) {
            EshopSaleOrderDetail detail = entry.getKey();
            List<ProductGiftRelation> giftRelations = entry.getValue();
            if (CollectionUtils.isEmpty(giftRelations)) {
                continue;
            }
            List<EshopSaleOrderDetailGiftRelation> detailGiftRelationList = new ArrayList<>();
            for (ProductGiftRelation giftRelation : giftRelations) {
                String sourceOid = giftRelation.getSourceOid();
                String giftOid = giftRelation.getGiftOid();
                if (StringUtils.isEmpty(sourceOid) || StringUtils.isEmpty(giftOid)) {
                    continue;
                }
                List<BigInteger> giftEshopOrderDetailIds = details.stream().filter(d -> d.getTradeOrderDetailId().equals(giftOid)).map(EshopSaleOrderDetail::getId).distinct().collect(Collectors.toList());
                if (CollectionUtils.isEmpty(giftEshopOrderDetailIds)) {
                    continue;
                }
                for (BigInteger giftEshopOrderDetailId : giftEshopOrderDetailIds) {
                    EshopSaleOrderDetailGiftRelation detailGiftRelation = new EshopSaleOrderDetailGiftRelation();
                    detailGiftRelation.setId(UId.newId());
                    detailGiftRelation.setProfileId(order.getProfileId());
                    detailGiftRelation.setSourceEshopOrderId(order.getId());
                    detailGiftRelation.setEshopOrderId(order.getId());
                    detailGiftRelation.setSourceEshopOrderDetailId(detail.getId());
                    detailGiftRelation.setEshopOrderDetailId(giftEshopOrderDetailId);
                    detailGiftRelation.setCreateTime(new Date());
                    detailGiftRelation.setUpdateTime(new Date());
                    detailGiftRelationList.add(detailGiftRelation);
                }
            }
            detail.setGiftRelations(detailGiftRelationList);
        }
    }

    private void secondBuildKtypeId(EshopOrderEntity apiOrder, EshopSaleOrderEntity order) {
        //主表仓库默认采用第一行明细仓库（后续如果要增加明细行仓库筛选，可扩展表存储该订单关联的仓库列表）
        BigInteger ktypeId = order.getOrderDetails().get(0).getKtypeId();
        order.setKtypeId(ktypeId);
        Map<String, List<EshopSaleOrderDetail>> collect = order.getOrderDetails().stream().collect(Collectors.groupingBy(EshopSaleOrderDetail::getPlatformStockId));
        if (collect.size() == 1 && StringUtils.isEmpty(order.getPlatformStockId())) {
            String platformStockId = order.getOrderDetails().get(0).getPlatformStockId();
            String platformStockCode = order.getOrderDetails().get(0).getPlatformStockCode();
            order.setPlatformStockId(platformStockId);
            order.setPlatformStockCode(platformStockCode);
        }
    }

    private void buildPickUpInfo(EshopOrderEntity apiOrder, EshopSaleOrderEntity orderEntity) {
        EshopBuyer pickUpInfo = doEncryptPickUpAddress(apiOrder);
        //保存 然后查询ID
        if (null == orderEntity.getExtend()) {
            orderEntity.setExtend(new EshopSaleOrderExtendEntity(orderEntity));
        }
        orderEntity.getExtend().setSelfPickUpInfo(pickUpInfo);
        orderEntity.getExtend().setPickUpAddressId(pickUpInfo.getBuyerId());
    }

    /**
     * 将订单构建过程中的io缓存在redis中
     *
     * @param
     */
    private void cacheOrderDownloadIO(String taskId, String keyStr, Object obj) {
        if (StringUtils.isEmpty(taskId) || StringUtils.isEmpty(keyStr)) {
            return;
        }
        BigInteger profileId = CurrentUser.getProfileId();
        String key = String.format("%s_%s_%s_%s", "ORDER_DOWNLOAD_CACHE_", profileId, taskId, keyStr);
        StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
        String val = template.opsForValue().get(key);
        if (StringUtils.isEmpty(val)) {
            template.opsForValue().set(key, JsonUtils.toJson(obj), 5, TimeUnit.MINUTES);
        }
    }

    /**
     * 获取缓存数据，如果缓存中没有数据，则调用 supplier 获取数据并设置缓存。
     *
     * @param
     * @param clazz    对象类型或 List 的 TypeReference
     * @param supplier 获取数据的逻辑
     * @param <T>      泛型类型
     * @return 缓存数据
     * <p>
     * 如果返回值为集合，需要传入 clazz = new TypeReference<List<PayWay>>() {}
     */
    public <T> T getOrFetchCache(String taskId, OrderDownloadCacheType type, String keyStr, Object clazz, Supplier<T> supplier) {
        try {
            if (StringUtils.isEmpty(taskId) || StringUtils.isEmpty(keyStr) || null == type || null == clazz) {
                if (null == supplier) {
                    return null;
                }
                return supplier.get();
            }
            BigInteger profileId = CurrentUser.getProfileId();
            String key = String.format("%s_%s_%s_%s_%s", "ORDER_DOWNLOAD_CACHE_", profileId, taskId, type, keyStr);
            StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
            String cachedValue = template.opsForValue().get(key);
            if (cachedValue != null) {
                ObjectMapper objectMapper = new ObjectMapper();
                try {
                    if (clazz instanceof Class) {
                        return objectMapper.readValue(cachedValue, (Class<T>) clazz);
                    } else if (clazz instanceof TypeReference) {
                        return objectMapper.readValue(cachedValue, (TypeReference<T>) clazz);
                    } else {
                        CommonUtil.doLogByEnable(logger, LogLevelEnum.ERROR, String.format("ORDER_DOWNLOAD_CACHE---->profileId : %s ,unsupported type : %s", profileId, key));
                        throw new IllegalArgumentException("unsupported type");
                    }
                } catch (Exception e) {
                    // 处理反序列化失败的情况
                    CommonUtil.doLogByEnable(logger, LogLevelEnum.ERROR, String.format("ORDER_DOWNLOAD_CACHE---->profileId : %s ,failed to deserialize object from cache : %s", profileId, key));
                    if (null == supplier) {
                        return null;
                    }
                    return supplier.get();
                }
            } else {
                if (null == supplier) {
                    return null;
                }
                T result = supplier.get();
                setCache(taskId, type, keyStr, result);
                return result;
            }
        } catch (Exception e) {
            CommonUtil.doLogByEnable(logger, LogLevelEnum.ERROR, String.format("ORDER_DOWNLOAD_CACHE---->profileId : %s ,cache error", CurrentUser.getProfileId()), e);
            return null;
        }
    }

    /**
     * 设置缓存数据。
     *
     * @param
     * @param value 要缓存的对象
     */
    public void setCache(String taskId, OrderDownloadCacheType type, String keyStr, Object value) {
        if (StringUtils.isEmpty(taskId) || StringUtils.isEmpty(keyStr) || null == type || null == value) {
            return;
        }
        BigInteger profileId = CurrentUser.getProfileId();
        String key = String.format("%s_%s_%s_%s_%s", "ORDER_DOWNLOAD_CACHE_", profileId, taskId, type, keyStr);
        try {
            StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
            template.opsForValue().set(key, JsonUtils.toJson(value), 30, TimeUnit.MINUTES);
        } catch (Exception e) {
            // 处理序列化失败的情况
            CommonUtil.doLogByEnable(logger, LogLevelEnum.ERROR, String.format("ORDER_DOWNLOAD_CACHE---->profileId : %s ,failed to serialize object to cache : %s", profileId, key));
        }
    }


    /**
     * 获取缓存
     *
     * @param
     */
    private String getCacheOrderDownloadIO(String taskId, String keyStr) {
        if (StringUtils.isEmpty(taskId) || StringUtils.isEmpty(keyStr)) {
            return null;
        }
        BigInteger profileId = CurrentUser.getProfileId();
        String key = String.format("%s_%s_%s_%s", "ORDER_DOWNLOAD_CACHE_", profileId, taskId, keyStr);
        StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
        return template.opsForValue().get(key);
    }

    public void checkDetail(EshopOrderEntity order, EshopSaleOrderDownloadTask task) {
        try {
            if (null == order) {
                return;
            }
            List<EshopOrderDetailEntity> orderDetails = order.getOrderDetails();
            if (CollectionUtils.isEmpty(orderDetails)) {
                logger.error("下载订单，从接口返回的订单中存在错误明细，明细为空，order:{}，task:{}", JsonUtils.toJson(order), JsonUtils.toJson(task));
                return;
            }
            long count = orderDetails.stream().map(EshopOrderDetailEntity::getOid).distinct().count();

            if (orderDetails.size() > count) {
                logger.error("下载订单，从接口返回的订单中存在错误明细，order:{}，task:{}", JsonUtils.toJson(order), JsonUtils.toJson(task));
            }
        } catch (Exception e) {
            logger.error("下载订单，从接口返回的订单中存在错误明细检查出错，order:{}，task:{}", JsonUtils.toJson(order), JsonUtils.toJson(task));
        }
    }

    public void checkDetail(EshopSaleOrderEntity order, EshopSaleOrderDownloadTask task, String msg) {
        try {
            if (null == order) {
                return;
            }
            //商品解析整单发标记，不检查
            if (order.getOrderMarks().stream().anyMatch(m->m.getMarkCode().compareTo(BigInteger.valueOf(BaseOrderMarkEnum.FULL_SEND.getCode())) == 0)){
                return ;
            }
            List<EshopSaleOrderDetail> orderDetails = order.getOrderDetails();
            if (CollectionUtils.isEmpty(orderDetails)) {
                return;
            }
            boolean hasError = false;
            for (int i = 0; i < orderDetails.size(); i++) {
                if (hasError) {
                    break;
                }
                EshopSaleOrderDetail detail = orderDetails.get(i);
                for (EshopSaleOrderDetail tempDetail : orderDetails) {
                    if (detail.getId().compareTo(tempDetail.getId()) == 0) {
                        continue;
                    }
                    //oid相同
                    if (detail.getTradeOrderDetailId().equals(tempDetail.getTradeOrderDetailId()) && StringUtils.isNotEmpty(tempDetail.getTradeOrderDetailId())) {
                        //都是普通明细
                        if (detail.getComboRowId().compareTo(tempDetail.getComboRowId()) == 0 && detail.getComboRowId().compareTo(BigInteger.ZERO) == 0) {
                            hasError = true;
                            break;
                        }
                        //一个套餐明细，一个普通明细
                        if ((detail.getComboRowId().compareTo(BigInteger.ZERO) == 0 && tempDetail.getComboRowId().compareTo(BigInteger.ZERO) != 0) ||
                                (tempDetail.getComboRowId().compareTo(BigInteger.ZERO) == 0 && detail.getComboRowId().compareTo(BigInteger.ZERO) != 0)) {
                            hasError = true;
                            break;
                        }
                        //oid不同
                    } else {
                        //都是套餐明细
                        if (detail.getComboRowId().compareTo(tempDetail.getComboRowId()) == 0 && detail.getComboRowId().compareTo(BigInteger.ZERO) != 0) {
                            hasError = true;
                            break;
                        }
                    }
                }
            }

            if (hasError) {
                logger.error("下载订单，{}订单中存在错误明细，order:{}，task:{}", msg, JsonUtils.toJson(order), JsonUtils.toJson(task));
            }
        } catch (Exception e) {
            logger.error("下载订单，{}订单中存在错误明细检查出错，order:{}，task:{}", msg, JsonUtils.toJson(order), JsonUtils.toJson(task));
        }
    }

    private void buildPayWay(EshopSaleOrderEntity order, String taskId) {
        String merchantPaymentAccount = order.getMerchantPaymentAccount();
        if (StringUtils.isEmpty(merchantPaymentAccount)) {
            return;
        }
        PayWays payWaysByFullName = getOrFetchCache(taskId,
                OrderDownloadCacheType.PAY_WAY,
                merchantPaymentAccount,
                PayWays.class,
                () -> eshopOrderBaseInfoMapper.getPayWaysByFullName(order.getProfileId(), merchantPaymentAccount));
        if (null == payWaysByFullName) {
            logger.debug(String.format("没有匹配到对应的支付方式，profileid:%s tradeOrderId:%s ", order.getProfileId(), order.getTradeOrderId()));
            return;
        }
        order.setPaywayId(payWaysByFullName.getId());
    }

    public void buildControllerMark(EshopSaleOrderEntity order, boolean needBuildDetailControllerMark) {
        try {
            List<EshopOrderMarkEntity> orderMarks = order.getOrderMarks();
            List<EshopOrderMarkEntity> newOrderMarks = new ArrayList<>();
            List<EshopSaleOrderDetail> orderDetails = order.getOrderDetails();
            if (!orderMarks.isEmpty()) {
                for (EshopOrderMarkEntity orderMarkEntity : orderMarks) {
                    //生成的控制标记
                    List<BaseOrderMarkEnum> mainMark = BaseOrderMarkEnum.buildControllerMarks(orderMarkEntity.getMarkCode().intValue());
//                    CommonUtil.doLogByEnable(logger,LogLevelEnum.ERROR,String.format("profileId:%s,tradeOrderId:%s,mark:%s,buildControllerMark:%s",
//                            CurrentUser.getProfileId(),order.getTradeOrderId(),
//                            null == orderMarkEntity ? "" : orderMarkEntity.getMarkCode(),
//                            CollectionUtils.isEmpty(mainMark) ? "" : JsonUtils.toJson(mainMark)));
                    //构建控制标记的数据库实体
                    List<EshopOrderMarkEntity> newMainMarkEntity = EShopOrderMarkUtil.buildEshopOrderMarkEntity(orderMarkEntity, mainMark);
                    newMainMarkEntity.add(orderMarkEntity);
                    newOrderMarks.addAll(newMainMarkEntity);
                }
            }
            if (needBuildDetailControllerMark) {
                for (EshopSaleOrderDetail detail : orderDetails) {
                    List<EshopOrderMarkEntity> orderDetailMarks = detail.getOrderDetailMarks();
                    List<EshopOrderMarkEntity> newOrderDetailMarks = new ArrayList<>();
                    if (!orderDetailMarks.isEmpty()) {
                        for (EshopOrderMarkEntity detailMarkEntity : orderDetailMarks) {
                            List<BaseOrderMarkEnum> detailMark = BaseOrderMarkEnum.buildControllerMarks(detailMarkEntity.getMarkCode().intValue());
                            List<EshopOrderMarkEntity> newDetailMarkEntity = EShopOrderMarkUtil.buildEshopOrderMarkEntity(detailMarkEntity, detailMark);
                            newDetailMarkEntity.add(detailMarkEntity);
                            newOrderDetailMarks.addAll(newDetailMarkEntity);
                        }
                    }
                    detail.setOrderDetailMarks(newOrderDetailMarks);
                }
            }
            order.setOrderMarks(newOrderMarks);
            order.setOrderDetails(orderDetails);
        } catch (Exception e) {
            logger.error(String.format("构建控制标记报错:%s,账套：%s", e.getMessage(), order.getProfileId()));
            throw new RuntimeException();
        }
    }

    private void buildMainOrder(EshopOrderEntity apiOrder, EshopSaleOrderEntity orderEntity, Otype otype) {
        BigInteger newId = UId.newId();
        //TODO
        //徐星
        //com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopSaleOrderSaver.buildItemChange
        if (apiOrder.getShopType() == ShopType.Doudian || apiOrder.getShopType() == ShopType.DouDianInstantShopping) {
            try {
                EshopTmcOrderMsgMapper mapper = BeanUtils.getBean(EshopTmcOrderMsgMapper.class);
                List<EshopNotifyChange> changes = mapper.queryNotifyChangeByType(apiOrder.getProfileId(), Collections.singletonList(apiOrder.getTradeId()), apiOrder.getEshopId(), 27);
                if (CollectionUtils.isNotEmpty(changes)) {
                    boolean anyMatch = changes.stream().anyMatch(x -> x.getTradeOrderId().equals(apiOrder.getTradeId()));
                    if (anyMatch) {
                        apiOrder.getOrderMarks().add(BaseOrderMarkEnum.CUS_SERVICE_MODIFY_SKU);
                    }
                }
            } catch (Exception e) {
                logger.error(String.format("profileId:%s,eshopId:%s,tradeId:%s,抖店改sku报错:%s", apiOrder.getProfileId(), apiOrder.getEshopId(), apiOrder.getTradeId(), e.getMessage(), e));
            }
        }
        orderEntity.setId(apiOrder.getEshopOrderId());
        orderEntity.setId(null == apiOrder.getEshopOrderId() || BigInteger.ZERO.compareTo(apiOrder.getEshopOrderId()) == 0 ? UId.newId() : apiOrder.getEshopOrderId());
        orderEntity.setDeleted(apiOrder.getDeleted());
        orderEntity.setPlatformParentOrderId(null == apiOrder.getParentTradeId() ? "0" : apiOrder.getParentTradeId());
        orderEntity.setProfileId(CurrentUser.getProfileId());
        orderEntity.setCreateType(OrderCreateType.FROM_DOWNLOAD_ORDER);
        orderEntity.setOrderSourceType(OrderType.ESHOP_SELL);
        orderEntity.setOrderSaleType(apiOrder.getTradeType());
        orderEntity.setShopType(otype.getEshopInfo().getEshopType());
        orderEntity.setOrderDeliverRequired(apiOrder.getDeliverRequired());
        orderEntity.setPayTimeType(apiOrder.getPayType());
        orderEntity.setTradeOrderId(apiOrder.getTradeId());
        orderEntity.setOtypeId(otype.getEshopInfo().getOtypeId());
        orderEntity.setModifiedTime(new Date());
        orderEntity.setTradeCreateTime(apiOrder.getCreateTime());
        orderEntity.setTradeModifiedTime(apiOrder.getModifiedTime());
        orderEntity.setTradePayTime(apiOrder.getPayTime());
        orderEntity.setPlatformTradeState(apiOrder.getPlatformStatus());
        orderEntity.setLocalRefundProcessState(apiOrder.getPlatformRefund());
        orderEntity.setLocalTradeState(apiOrder.getTradeStatus());
        orderEntity.setTradeFinishTime(CommonUtil.isInvalidDate(apiOrder.getFinishTime()) &&  TradeStatus.TRADE_FINISHED == orderEntity.getLocalTradeState() ?
                orderEntity.getTradeModifiedTime() : apiOrder.getFinishTime());
        //TODO
        //徐星
        //com.wsgjp.ct.sale.biz.eshoporder.service.order.EshopSaleOrderSaver#doAfterSave
        try {
            if (apiOrder.isCancelAudit()) {
                LocalProcessStatus status = checkDeliverStatus(apiOrder);
                AuditCancelOrderRequest request = new AuditCancelOrderRequest();
                request.setTradeId(apiOrder.getTradeId());
                request.setStatus(status);
                request.setShopType(apiOrder.getShopType());
                request.setShopId(apiOrder.getEshopId());
                AuditCancelOrderResponse auditCancelOrderResponse = auditCancelOrder(request);
                if (auditCancelOrderResponse.getSuccess()) {
                    orderEntity.setLocalTradeState(TradeStatus.ALL_CLOSED);
                } else {
                    logger.error(String.format("profileId:%s,eshopId:%s,tradeId:%s,取消订单回告失败:%s", apiOrder.getProfileId(), apiOrder.getEshopId(), apiOrder.getTradeId(), auditCancelOrderResponse.getMessage()));
                }
            }
        } catch (Exception e) {
            logger.error(String.format("profileId:%s,eshopId:%s,tradeId:%s,取消订单回告报错:%s", apiOrder.getProfileId(), apiOrder.getEshopId(), apiOrder.getTradeId(), e.getMessage()), e);
        }
        orderEntity.setRefundState(apiOrder.getRefundStatus());
        orderEntity.setBuyerMessage(apiOrder.getBuyerMessage());
        orderEntity.setSellerMemo(apiOrder.getSellerMemo());
        orderEntity.setSellerFlag(buildOrderSellerFlag(apiOrder.getSellerFlag()));
        orderEntity.setPlatformStockId(apiOrder.getStoreId());
        orderEntity.setPlatformStockCode(apiOrder.getStockCode());
        orderEntity.setPayNo(apiOrder.getPayNo());
        orderEntity.setUniqueMark(apiOrder.getUniqueMark());
        orderEntity.setFuzzySensitiveData(apiOrder.isFuzzySensitiveData());
        orderEntity.setSelfDeliveryMode(buildSelfDeliveryMode(apiOrder.getSelfDeliveryMode().getCode()));
        buildSelfDeliveryModeByEshopConfig(orderEntity, otype);
        orderEntity.setSalesman(apiOrder.getSalesman());
        orderEntity.setSummary(apiOrder.getSummary());
        orderEntity.setPlatformOrderPreferentialTotal(apiOrder.getPlatformPreferentialTotal());
        orderEntity.setOrderPreferentialAllotTotal(apiOrder.getPreferenceTotal());
        orderEntity.setCustomerExpectedFreightName(apiOrder.getCustomerExpectedFreightName());
        orderEntity.setCustomerExpectedFreightCode(apiOrder.getCustomerExpectedFreightCode());
        orderEntity.setRemark(apiOrder.getRemark());
        orderEntity.setSellerMemo(apiOrder.getSellerMemo());
        orderEntity.setPlatformDistributorName(apiOrder.getPlatformDistributorName());
        orderEntity.setModeOfPayment(apiOrder.getPaymentMode());
        orderEntity.setMerchantPaymentAccount(apiOrder.getMerchantCollectionAccount());
        orderEntity.setPlatformDistributorId(apiOrder.getPlatformDistributorCode());
//        buildDeliverProcessType(orderEntity, otype);
        buildBaseInfoByOtype(orderEntity, otype);
//        orderEntity.setCreatorId(profileUtil.getAdminEtypeId());
        orderEntity.setPlatformEtypeCode(apiOrder.getPlatformEtypeUsercode());
        orderEntity.setOrderMainHashChange(apiOrder.isOrderMainHashChange());
        orderEntity.setOrderDetailHashChange(apiOrder.isOrderDetailHashChange());
        orderEntity.setOrderMarkHashChange(apiOrder.isOrderMarkHashChange());
        orderEntity.setApiDetailSize(apiOrder.getOrderDetails().size());
    }

    private static void buildSelfDeliveryModeByEshopConfig(EshopSaleOrderEntity orderEntity, Otype otype) {
//        Boolean distributeFunc = SysProfileModelService.getEnabledByAllKeys().get(SysProfileModelKeyEnum.DistributeFunc.getKey());
        if (orderEntity.getSelfDeliveryMode().equals(SelfDeliveryModeEnum.DELIVERY_BY_SELF)) {
            switch (otype.getEshopConfig().getSelfDeliveryMode()) {
                case 0:
                    orderEntity.setSelfDeliveryMode(SelfDeliveryModeEnum.SIMPLE_DELIVERY);
                    break;
                case 1:
                    orderEntity.setSelfDeliveryMode(SelfDeliveryModeEnum.DELIVERY_BY_SELF);
                    break;
                default:
                    orderEntity.setSelfDeliveryMode(orderEntity.getSelfDeliveryMode());
                    break;
            }
        }
    }

    public AuditCancelOrderResponse auditCancelOrder(AuditCancelOrderRequest request) {
        try {
            BifrostEshopMapper eshopMapper = BeanUtils.getBean(BifrostEshopMapper.class);
            EshopInfo eshopInfo = eshopMapper.getEshopInfoByShopId(com.wsgjp.ct.sis.client.common.CurrentUser.getProfileId(), request.getShopId());
            EshopSystemParams systemParams = com.wsgjp.ct.sale.biz.bifrost.util.CommonUtil.toSystemParams(eshopInfo);
            EshopFactory factory = EshopFactoryManager.create(request.getShopType(), systemParams);
            EshopAuditCancelOrderFeature feature = factory.getFeature(EshopAuditCancelOrderFeature.class);
            if (feature == null) {
                String msg = String.format("%s不支持取消订单回告平台！", request.getSystemParams().getShopType().getName());
                throw new BusinessSupportException(msg);
            }
            return feature.auditCancelOrder(request);
        } catch (Exception ex) {
            logger.error("取消订单失败，失败原因:{}，request:{}", ex.getMessage(), JsonUtils.toJson(request), ex);
            AuditCancelOrderResponse response = new AuditCancelOrderResponse();
            response.setSuccess(false);
            response.setMessage(ex.getMessage());
            return response;
        }
    }

    public LocalProcessStatus checkDeliverStatus(EshopOrderEntity apiOrder) {
        BigInteger profileId = CurrentUser.getProfileId();
        BigInteger otypeId = apiOrder.getEshopId();
        String tradeId = apiOrder.getTradeId();
        //判断交易单的类型，是否需要走仓储
        //如果不走仓储
        //仓储是否打印物流单
        EshopOrderEshopRefundMapper refundMapper = BeanUtils.getBean(EshopOrderEshopRefundMapper.class);
        List<BillDeliverDTO> deliverList = refundMapper.queryDeliverByTradeId(profileId, otypeId, tradeId);
        if (CollectionUtils.isEmpty(deliverList)) {
            return LocalProcessStatus.WAIT_AUDIT;
        }

        for (BillDeliverDTO billDeliverDTO : deliverList) {
            if (billDeliverDTO.getDeliverProcessType().equals(DeliverProcessTypeEnum.NONE_POST)) {
                return LocalProcessStatus.WAIT_AUDIT;
            }
            BillPostState postState = billDeliverDTO.getPostState();
            if (postState.getState() > 500) {
                return LocalProcessStatus.SENDED;
            }
            if (billDeliverDTO.getDeliverProcessType().equals(DeliverProcessTypeEnum.NONE)) {
                return LocalProcessStatus.WAIT_AUDIT;
            }
            if (billDeliverDTO.getDeliverProcessType().equals(DeliverProcessTypeEnum.CLOUD_WAREHOUSE_DELIVERY)) {
                return getDeliverStatusWithWms(billDeliverDTO, refundMapper);
            }
            return getDeliverStatusWithPost(billDeliverDTO, refundMapper);
        }
        return LocalProcessStatus.WAIT_AUDIT;
    }

    private LocalProcessStatus getDeliverStatusWithWms(BillDeliverDTO billDeliverDTO, EshopOrderEshopRefundMapper refundMapper) {
        boolean sendState = refundMapper.queryWmsSendState(billDeliverDTO.getProfileId(), billDeliverDTO.getVchcode());
        if (sendState) {
            return LocalProcessStatus.WAIT_SEND;
        } else {
            return LocalProcessStatus.WAIT_AUDIT;
        }
    }

    private LocalProcessStatus getDeliverStatusWithPost(BillDeliverDTO billDeliverDTO, EshopOrderEshopRefundMapper refundMapper) {
        List<Boolean> list = refundMapper.queryFrightPrintState(billDeliverDTO.getProfileId(), billDeliverDTO.getVchcode());
        if (CollectionUtils.isEmpty(list)) {
            return LocalProcessStatus.WAIT_AUDIT;
        }
        boolean match = list.stream().anyMatch(x -> x);
        if (match) {
            return LocalProcessStatus.WAIT_SEND;
        } else {
            return LocalProcessStatus.WAIT_AUDIT;
        }
    }

//    public void buildDeliverProcessType(EshopSaleOrderEntity orderEntity, Otype otype) {
//        DeliverProcessTypeEnum[] values = DeliverProcessTypeEnum.values();
//        for (DeliverProcessTypeEnum deliverProcessTypeEnum : values) {
//            if (deliverProcessTypeEnum.getCode() == otype.getDeliverProcessType()) {
//                orderEntity.setDeliverProcessType(deliverProcessTypeEnum);
//            }
//        }
//        if (null == orderEntity.getDeliverProcessType()) {
//            orderEntity.setDeliverProcessType(DeliverProcessTypeEnum.NONE);
//        }
//    }

    private OrderSellerFlag buildOrderSellerFlag(SellerFlag platformFlag) {
        if (platformFlag == null) {
            return OrderSellerFlag.WHITE;
        }
        switch (platformFlag) {
            case RED:
                return OrderSellerFlag.RED;
            case YELLOW:
                return OrderSellerFlag.YELLOW;
            case GREEN:
                return OrderSellerFlag.GREEN;
            case BLUE:
                return OrderSellerFlag.BLUE;
            case VIOLET:
                return OrderSellerFlag.VIOLET;
            case GRAY:
                return OrderSellerFlag.GRAY;
            case ORANGE:
                return OrderSellerFlag.ORANGE;
            case CHING:
                return OrderSellerFlag.CHING;
            case ROSERED:
                return OrderSellerFlag.ROSE_RED;
            case TENDERGREEN:
                return OrderSellerFlag.TENDER_GREEN;
            case VIOLETPURPLE:
                return OrderSellerFlag.PURPLE;
            default:
                return OrderSellerFlag.WHITE;
        }
    }

    private SelfDeliveryModeEnum buildSelfDeliveryMode(int selfDeliveryModeEnumCode) {
        switch (selfDeliveryModeEnumCode) {
            case 0:
                return SelfDeliveryModeEnum.NONE;
            case 1:
                return SelfDeliveryModeEnum.EXPRESS_LOGISTICS;
            case 2:
                return SelfDeliveryModeEnum.INTRA_CITY_DISTRIBUTION;
            case 3:
                return SelfDeliveryModeEnum.CUSTOMER_COLLECT;
            case 4:
                return SelfDeliveryModeEnum.DELIVERY_BY_SELF;
            case 5:
                return SelfDeliveryModeEnum.CAR_SALE;
            case 6:
                return SelfDeliveryModeEnum.DELIVERY_BY_MANUFACTURER;
            case 7:
                return SelfDeliveryModeEnum.SIMPLE_DELIVERY;
            default:
                return SelfDeliveryModeEnum.EXPRESS_LOGISTICS;
        }
    }

    private void buildOrderTerming(EshopOrderEntity apiOrder, EshopSaleOrderEntity orderEntity, Otype otype) {
        EshopSaleOrderTiming orderTiming = new EshopSaleOrderTiming();
        orderTiming.setEshopOrderId(orderEntity.getId());
        orderTiming.setProfileId(apiOrder.getProfileId());
        OrderTiming apiTiming = apiOrder.getOrderTiming();
        if (apiTiming == null) {
            apiTiming = new OrderTiming();
        }
        orderTiming.setCnService(apiTiming.getCnService());
        orderTiming.setPlanSendTime(apiTiming.getPlanSendTime());
        caculateMainSendTime(orderEntity, apiTiming, orderTiming);
        orderTiming.setPromisedCollectTime(apiTiming.getPromisedCollectTime());
        orderTiming.setPromisedSignTime(apiTiming.getPromisedSignTime());
        calculateMainPlatformSendTime(apiOrder, apiTiming, orderTiming);
        orderTiming.setPlanSignTime(apiTiming.getPlanSignTime());
        orderTiming.setSystemTiming(apiTiming.getSystemTiming());
        orderTiming.setTimingType(apiTiming.getTimingType());
        orderTiming.setSignTime(apiOrder.getSignTime());
        orderTiming.setPromisedSignStartTime(apiTiming.getPromisedSignStartTime());
        calculateOrderTimingDuration(apiOrder, otype, apiTiming, orderTiming);
        orderEntity.setTiming(orderTiming);
    }

    /**
     * 通过明细计算主表的最晚发货时间
     *
     * @param orderEntity
     * @param apiTiming
     * @param orderTiming
     */
    private static void caculateMainSendTime(EshopSaleOrderEntity orderEntity, OrderTiming apiTiming, EshopSaleOrderTiming orderTiming) {
        Date apiSendTime = apiTiming.getSendTime();
        Date minDetailPromisedSendTime = null;
        List<EshopSaleOrderDetailTiming> detailTimings = orderEntity.getOrderDetails().stream().map(EshopSaleOrderDetail::getTiming).collect(Collectors.toList());
        for (EshopSaleOrderDetailTiming detailTiming : detailTimings) {
            if (null != detailTiming.getPromisedSendTime()) {
                if (null == minDetailPromisedSendTime) {
                    minDetailPromisedSendTime = detailTiming.getPromisedSendTime();
                    continue;
                }
                int compare = DateUtils.compare(minDetailPromisedSendTime, detailTiming.getPromisedSendTime());
                if (1 == compare) {
                    minDetailPromisedSendTime = detailTiming.getPromisedSendTime();
                    continue;
                }
            }
        }
        if (null != apiSendTime && null != minDetailPromisedSendTime) {
            //对比明细返回预计发货时间^主表预计发货时间，取最小时间为当前订单预计发货时间
            orderTiming.setSendTime(minDetailPromisedSendTime);
            return;
        }
        if (null != apiSendTime && null == minDetailPromisedSendTime) {
            //取主表返回预计发货时间
            orderTiming.setSendTime(apiSendTime);
            return;
        }
        if (null == apiSendTime && null != minDetailPromisedSendTime) {
            //对比明细返回预计发货时间^主表预计发货时间，取最小时间为当前订单预计发货时间
            orderTiming.setSendTime(minDetailPromisedSendTime);
            return;
        }
    }

    private static void calculateOrderTimingDuration(EshopOrderEntity apiOrder, Otype otype, OrderTiming apiTiming, EshopSaleOrderTiming orderTiming) {
        if (apiOrder.getTradeStatus().getCode() >= TradeStatus.WAIT_SELLER_SEND_GOODS.getCode()) {
            if (apiTiming.getPromisedCollectTime() == null && otype.getPromisedCollectDuration() > 0 && otype.getEshopConfig().getPromisedCollectDurationConfig() == 0) {
                orderTiming.setPromisedCollectTime(CommonUtil.getAddDateByHours(apiOrder.getPayTime(), otype.getPromisedCollectDuration()));
            }
            if (apiTiming.getPromisedSignTime() == null && otype.getPromisedSignDuration() > 0) {
                orderTiming.setPromisedSignTime(CommonUtil.getAddDateByHours(apiOrder.getPayTime(), otype.getPromisedSignDuration()));
            }
            if (orderTiming.getSendTime() == null && apiTiming.getSendTime() == null && otype.getDeliverDuration() > 0) {
                orderTiming.setSendTime(CommonUtil.getAddDateByHours(apiOrder.getPayTime(), otype.getDeliverDuration()));
            }
            //平台不会返回最晚同步物流单号时间，直接从网店配置中读取计算
            if (otype.getPromisedSyncFreightDuration() > 0) {
                orderTiming.setPromisedSyncFreightTime(CommonUtil.getAddDateByHours(apiOrder.getPayTime(), otype.getPromisedSyncFreightDuration()));
            }
        }
    }

    /**
     * 计算主表的平台发货时间
     * 如果主表没有返回，用明细的最近时间
     * 如果都没返回为当前时间
     *
     * @param apiOrder
     * @param apiTiming
     * @param orderTiming
     */
    private static void calculateMainPlatformSendTime(EshopOrderEntity apiOrder, OrderTiming apiTiming, EshopSaleOrderTiming orderTiming) {
        Date mainPlatformSendTime = apiTiming.getPlatformSendTime();
        List<OrderTiming> detailOrderTimings = apiOrder.getOrderDetails().stream().map(EshopOrderDetailEntity::getOrderTiming).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(detailOrderTimings) && null == mainPlatformSendTime) {
            orderTiming.setPlatformSendTime(new Date());
            return;
        }
        List<Date> detailPlatformSendTimes = detailOrderTimings.stream().map(OrderTiming::getPlatformSendTime).filter(Objects::nonNull).collect(Collectors.toList());
        if (null == mainPlatformSendTime) {
            if (CollectionUtils.isNotEmpty(detailPlatformSendTimes)) {
                detailPlatformSendTimes.sort(Comparator.comparing(Date::getTime).reversed());
                orderTiming.setPlatformSendTime(detailPlatformSendTimes.get(0));
            }
        } else {
            orderTiming.setPlatformSendTime(mainPlatformSendTime);
        }
    }


    private void buildDetailBusinessTypeWithOtype(EshopOrderEntity apiOrder, EshopSaleOrderEntity orderEntity, Otype otype) {
        orderEntity.setBusinessType(BillBusinessType.SaleNormal);
        List<EshopSaleOrderDetail> details = orderEntity.getOrderDetails();
        if (CollectionUtils.isEmpty(details)){
            return;
        }
        Map<BillBusinessType, List<EshopSaleOrderDetail>> map = details.stream().collect(Collectors.groupingBy(EshopSaleOrderDetail::getBusinessType));
        if (CollectionUtils.isEmpty(map)){
            return;
        }
        if (map.size() == 1){
            for (Map.Entry<BillBusinessType, List<EshopSaleOrderDetail>> entry : map.entrySet()) {
                orderEntity.setBusinessType(entry.getKey());
                return;
            }
        }
        boolean hasNormal = details.stream().map(EshopSaleOrderDetail::getBusinessType).anyMatch(b -> BillBusinessType.SaleNormal == b);
        if(hasNormal){
            return;
        }
        if (null != apiOrder.getPlatformBusinessType()){
            switch (apiOrder.getPlatformBusinessType()){
                case JINGXIAO:
                    orderEntity.setBusinessType(BillBusinessType.SaleNormal);
                    return;
                case DAIXIAO:
                    orderEntity.setBusinessType(BillBusinessType.SaleDistribution);
                    return;
                case SALEPROXY:
                    orderEntity.setBusinessType(BillBusinessType.SaleProxy);
                    return;
                default:
                    orderEntity.setBusinessType(BillBusinessType.SaleNormal);
                    return;
            }
        }
    }

    private BillBusinessType buildDetailBusinessTypeWithOtype(PlatformBusinessType detailBusinessType,PlatformBusinessType mainBusinessType, Otype otype){
        if (null != detailBusinessType){
            switch (detailBusinessType){
                case JINGXIAO:
                    return BillBusinessType.SaleNormal;
                case DAIXIAO:
                    return BillBusinessType.SaleDistribution;
                case SALEPROXY:
                    return BillBusinessType.SaleProxy;
                default:
                    return BillBusinessType.SaleNormal;
            }
        } else if (null != mainBusinessType) {
            switch (mainBusinessType){
                case JINGXIAO:
                    return BillBusinessType.SaleNormal;
                case DAIXIAO:
                    return BillBusinessType.SaleDistribution;
                case SALEPROXY:
                    return BillBusinessType.SaleProxy;
                default:
                    return BillBusinessType.SaleNormal;
            }
        } else {
            switch (otype.getOcategory()){
                case DYY_SHOP:
                    return BillBusinessType.None;
                case FX_SHOP:
                    return BillBusinessType.SaleDistribution;
                default:
                    return BillBusinessType.SaleNormal;
            }
        }
    }

    public void buildDeliverType(BillDeliverType defaultDeliverType, EshopSaleOrderEntity orderEntity) {
        orderEntity.setDeliverType(defaultDeliverType);
        List<EshopSaleOrderDetail> details = orderEntity.getOrderDetails();
        //是否全部对应的虚拟商品
        boolean isAllVirtualPtype = details.stream().allMatch(x -> x.getPcategory() == 1);
        if (isAllVirtualPtype) {
            orderEntity.setDeliverType(BillDeliverType.NO_DELIVER);
        }
        List<Stock> stocks = eshopService.getAllStockList(orderEntity.getProfileId(), Collections.singletonList(orderEntity.getKtypeId()));
        if (stocks == null || stocks.size() == 0) {
            return;
        }
        if (stocks.get(0).getScategory() == 2) {
            orderEntity.setDeliverType(BillDeliverType.WMS);
        }
        //feat #30561
        if (stocks.get(0).getScategory() == 1 && eshopService.getSysData(CurrentUser.getProfileId(), "useStockDeliverType", "0").equals("1")) {
            orderEntity.setDeliverType(BillDeliverType.PLATFORM_SEND);
        }
        //是否是代销业务,代销默认厂家发货
        if (orderEntity.getBusinessType().equals(BillBusinessType.SaleProxy)) {
            orderEntity.setDeliverType(BillDeliverType.MANUFACTURER);
        }
        buildOrderDeliverTypeByStock(orderEntity, stocks);
    }

    /**
     * 根据仓库类型构建订单的发货方式和发货流程
     *
     * @param orderEntity
     * @param stocks
     */
    public void buildOrderDeliverTypeByStock(EshopSaleOrderEntity orderEntity, List<Stock> stocks) {
        //42547
        //当在构建订单时，订单仓库为云仓仓库，则需要把订单发货流程方式构建为云仓发货，发货方构建为云仓发货
        if (stocks.get(0).getScategory() == 2) {
            orderEntity.setDeliverType(BillDeliverType.WMS);
        } else {
            //当在构建订单时，订单仓库为非云仓仓库，订单发货流程方式构建为不走发货流程，需要把发货方处理为不发货
            if (DeliverProcessTypeEnum.NONE == orderEntity.getDeliverProcessType()) {
                orderEntity.setDeliverType(BillDeliverType.NO_DELIVER);
            }
        }
    }

    public void doGetOrCreateBtype(EshopSaleOrderEntity orderEntity, String name) {
        try {
            BtypeQueryRequest request = new BtypeQueryRequest();
            BtypeQueryParameter parameter = new BtypeQueryParameter(name);
            request.setQueryParams(parameter);
            GeneralResult<BaseBtypeResponse> result = baseApi.getBtype(request);
            if (result.getCode() != 200) {
                logger.error(String.format("根据【%s】匹配往来单位报错", name));
                return;
            }
            BaseBtypeResponse data = result.getData();
            if (data == null || data.getTotal() == 0) {
                doCreateBtype(orderEntity, name);
                return;
            }
            if (data.getTotal() > 1) {
                logger.error(String.format("根据【%s】匹配出多个往来单位", name));
                return;
            }
            BigInteger id = data.getList().get(0).getId();
            orderEntity.setBtypeId(id);
        } catch (Throwable ex) {
            logger.error(String.format("查询往来单位报错:%s", ex.getMessage()), ex);
        }
    }

    public void doGetOrCreateBtypeByImport(EshopSaleOrderEntity orderEntity, String name, SaleOrderParam param, ValidResult importResult) {
        EShopSaleOrderImportManager importManager = GetBeanUtil.getBean(EShopSaleOrderImportManager.class);
        try {
            BtypeQueryRequest request = new BtypeQueryRequest();
            BtypeQueryParameter parameter = new BtypeQueryParameter(name);
            request.setQueryParams(parameter);
            GeneralResult<BaseBtypeResponse> result = baseApi.getBtype(request);
            if (result.getCode() != 200) {
                String errorMsg = String.format("*订单导入错误，订单编号：%s，根据【%s】匹配往来单位报错", orderEntity.getTradeOrderId(), name);
                importManager.buildValidResultAndThrow(importResult, errorMsg, false);
                return;
            }
            BaseBtypeResponse data = result.getData();
            if ((data == null || data.getTotal() == 0) && 1 == param.getBtypeConfig()) {
                doCreateBtype(orderEntity, name);
                return;
            }
            if (data.getTotal() > 1) {
                String errorMsg = String.format("*订单导入错误，订单编号：%s，根据【%s】匹配出多个往来单位", orderEntity.getTradeOrderId(), name);
                importManager.buildValidResultAndThrow(importResult, errorMsg, false);
                return;
            }
            if (data != null && data.getTotal() != 0) {
                BigInteger id = data.getList().get(0).getId();
                orderEntity.setBtypeId(id);
            }
        } catch (Throwable ex) {
            String errorMsg = String.format("*订单导入错误，订单编号：%s，查询往来单位报错", orderEntity.getTradeOrderId());
            importManager.buildValidResultAndThrow(importResult, errorMsg, false);
        }
    }

    private void doCreateBtype(EshopSaleOrderEntity orderEntity, String name) {
        try {
            BaseBtype baseBtype = new BaseBtype(name, PlatformCommonUtil.toSafeString(name, 30));
            Integer cooperationType = BillBusinessType.SaleDistribution.equals(orderEntity.getBusinessType()) ? 1 : 0;
            baseBtype.setCooperationType(cooperationType);
            baseBtype.setBcategory(0);
            loopRetryGetBtypeByConfig(orderEntity, name, baseBtype, bizConfig.getCreateBtypeRetryCount() + 1);
        } catch (Exception ex) {
            logger.error(String.format("根据【%s】生成往来单位报错:%s", name, ex.getMessage()), ex);
        }
    }

    public void getOrCreateBtype(String shopAccount, EshopSaleOrderEntity orderEntity, Otype otype) {
        if (otype == null) {
            return;
        }
        if (otype.getEshopConfig() == null) {
            logger.error(String.format("%d:机构实体config为空", otype.getId()));
            return;
        }
        //分销店铺都是分销订单，分销订单的销售单位取值  = 店铺默认往来单位（btypeId）
        if (otype.getOcategory().equals(OrganizationType.FX_SHOP)) {
            orderEntity.setBtypeId(otype.getBtypeId());
            doBuildPayTypeId(orderEntity, otype);
            return;
        }
        //店铺不是分销店铺，订单为分销订单
        if (orderEntity.getBusinessType().equals(BillBusinessType.SaleDistribution)) {
            //   分销商名称不为空，根据平台返回的分销商名称（原始订单主表. platform_distributor_name）匹配往来单位（没有就自动创建）
            if (StringUtils.isNotEmpty(orderEntity.getPlatformDistributorName())) {
                doGetOrCreateBtype(orderEntity, orderEntity.getPlatformDistributorName());
            } else {
                //分销商名称为空，根据买家账号匹配往来单位（没有对应关系不要自动创建）
                BtypeQueryRequest request = new BtypeQueryRequest();
                BtypeQueryParameter parameter = new BtypeQueryParameter(shopAccount);
                request.setQueryParams(parameter);
                GeneralResult<BaseBtypeResponse> result = baseApi.getBtype(request);
                if (result.getCode() != 200 || result.getData() == null
                        || result.getData().getTotal() == 0 || result.getData().getTotal() > 1) {
                    logger.info(String.format("非分销网店的分销订单根据【%s】买家账号匹配往来单位为空或多个", shopAccount));
                    //  上面没有匹配到就去店铺默认往来单位
                    orderEntity.setBtypeId(otype.getBtypeId());
                } else {
                    BigInteger id = result.getData().getList().get(0).getId();
                    orderEntity.setBtypeId(id);
                }
            }
            doBuildPayTypeId(orderEntity, otype);
            return;
        }

        //当订单业务类型 !=分销（代发）时：
        //销售单位取值= 店铺默认往来单
        //结算单位取值 = 店铺默认往来单位
        orderEntity.setBtypeId(otype.getBtypeId());
        orderEntity.setPayBtypeId(otype.getBtypeId());
    }

    public void buildBtypeId(EshopOrderEntity apiOrder, EshopSaleOrderEntity orderEntity, Otype otype, EshopSaleOrderDownloadTask task) {
        try {
            orderEntity.setCommissionBtypeId(apiOrder.getCommissionBtypeId());
//            orderEntity.setBtypeExtendFieldRequestDto(apiOrder.getBtypeExtendFieldRequestDto());
            //优先从接口获取往来单位id
            if (apiOrder.getMainPlatformJsonEntity() != null && !StringUtils.isEmpty(apiOrder.getMainPlatformJsonEntity().getBtypeId())) {
                BigInteger btypeId = new BigInteger(apiOrder.getMainPlatformJsonEntity().getBtypeId());
                orderEntity.setBtypeId(btypeId);
                doBuildPayTypeId(orderEntity, otype);
                return;
            }
            getOrderBtype(orderEntity, otype, task);
        } catch (RuntimeException ex) {
            logger.error(String.format("构建往来单位出错 profileid:%s  tradeId:%s , message:%s", CurrentUser.getProfileId(),
                    apiOrder.getTradeId(), ex.getMessage()), ex);
        }
    }

    private static void checkPlatformDistributorInfoNeedIgnore(EshopSaleOrderEntity orderEntity) {
        EshopOrderSysDataConfig sysDataConfig = GlobalConfig.get(EshopOrderSysDataConfig.class);
        if (null == sysDataConfig || StringUtils.isEmpty(sysDataConfig.getIgnorePlatformDistributorInfoShopType())) {
            return;
        }
        List<String> shopTypeList = Arrays.asList(sysDataConfig.getIgnorePlatformDistributorInfoShopType().split(","));
        if (CollectionUtils.isEmpty(shopTypeList)) {
            return;
        }
        int code = orderEntity.getShopType().getCode();
        if (!shopTypeList.contains(String.valueOf(code))) {
            return;
        }
        if (null != orderEntity) {
            orderEntity.setPlatformDistributorName("");
            orderEntity.setPlatformDistributorId("");
        }
    }


    private void getOrCreateEtypeId(EshopSaleOrderEntity orderEntity, String taskId) {
        EshopSenderInfo senderInfo = orderEntity.getSenderInfo();
        //从平台接口 etypeInfo 获取
        if (senderInfo == null || StringUtils.isEmpty(senderInfo.getSenderMobile())) {
            return;
        }
        try {
            Etype etype = getOrFetchCache(taskId,
                    OrderDownloadCacheType.SENDER_ETYPE,
                    String.format("%s_%s", senderInfo.getSenderMobile(), senderInfo.getSenderName()),
                    Etype.class,
                    () -> GetBaseEtypeInfoByMobile(senderInfo.getSenderMobile(), senderInfo.getSenderName()));
            if (etype != null && etype.getId().compareTo(BigInteger.ZERO) > 0) {
                orderEntity.setEtypeId(etype.getId());
                orderEntity.setDtypeId(etype.getDtypeId());
                return;
            }
            BigInteger etypeId = CreateBaseEtypeInfo(senderInfo.getSenderMobile(), senderInfo.getSenderName(), orderEntity.getTradeOrderId());
            orderEntity.setEtypeId(etypeId);
        } catch (RuntimeException ex) {
            logger.error(String.format("构建经手人信息出错 profileid:%s  tradeId:%s ,senderInfo:%s message:%s",
                    CurrentUser.getProfileId(),
                    orderEntity.getTradeOrderId(),
                    JsonUtils.toJson(senderInfo),
                    ex.getMessage()), ex);
        }
    }

    /**
     * 根据平台提供的职员编码查询职员信息，处理经手人
     *
     * @param orderEntity
     */
    private void getOrderEtype(EshopSaleOrderEntity orderEntity, String taskId) {

        try {
            Etype etype = GetBaseEtypeInfoByCode(orderEntity.getPlatformEtypeCode(), taskId);
            if (etype != null && etype.getId().compareTo(BigInteger.ZERO) > 0) {
                orderEntity.setEtypeId(etype.getId());
                orderEntity.setDtypeId(etype.getDtypeId());
            }
        } catch (RuntimeException ex) {
            logger.error(String.format("构建经手人信息出错 profileid:%s  tradeId:%s ,userCode:%s message:%s",
                    CurrentUser.getProfileId(),
                    orderEntity.getTradeOrderId(),
                    orderEntity.getPlatformEtypeCode(),
                    ex.getMessage()), ex);
        }
    }

    private Etype GetBaseEtypeInfoByMobile(String mobile, String name) {
        EshopOrderBaseInfoMapper baseInfoMapper = GetBeanUtil.getBean(EshopOrderBaseInfoMapper.class);
        if (StringUtils.isEmpty(mobile)) {
            return null;
        }
        QueryBaseTypeParameter parameter = new QueryBaseTypeParameter();
        parameter.setProfileId(CurrentUser.getProfileId());
        parameter.setMobile(mobile);
        parameter.setFullname(name);
        parameter.setIgnoreDelete(true);
        return baseInfoMapper.getEtype(parameter);
    }

    private Etype GetBaseEtypeInfoByCode(String userCode, String taskId) {
        EshopOrderBaseInfoMapper baseInfoMapper = GetBeanUtil.getBean(EshopOrderBaseInfoMapper.class);
        if (StringUtils.isEmpty(userCode)) {
            return null;
        }
        QueryBaseTypeParameter parameter = new QueryBaseTypeParameter();
        parameter.setProfileId(CurrentUser.getProfileId());
        parameter.setUserCode(userCode);
        parameter.setIgnoreDelete(true);
        Etype etype = getOrFetchCache(taskId,
                OrderDownloadCacheType.ORDER_ETYPE,
                userCode,
                Etype.class,
                () -> baseInfoMapper.getEtype(parameter));
        return etype;
    }

    private BigInteger CreateBaseEtypeInfo(String mobile, String name, String tradeId) {
        if (StringUtils.isEmpty(mobile) || StringUtils.isEmpty(name)) {
            return BigInteger.ZERO;
        }
        Etype etype = new Etype();
        etype.setFullname(name);
        etype.setUsercode(mobile);
        etype.setMobile(mobile);
        etype.setMemo("");
        etype.setLoginUser(false);
        etype.setNamepy("");
        GeneralResult<SaveEtypeResponse> response = baseApi.saveEtype(etype);
        if (5001001 == response.getCode()) {
            etype = GetBaseEtypeInfoByMobile(mobile, name);
            return etype == null ? BigInteger.ZERO : etype.getId();
        }
        if (200 != response.getCode()) {
            PubSystemLogService.saveInfo(String.format("经手人创建失败，原因： %s  电话： %s 姓名：%s", response.getMessage(), mobile, name));
            logger.error(String.format("经手人创建失败，原因： %s  电话： %s 姓名：%s", response.getMessage(), mobile, name));
            return BigInteger.ZERO;
        }
        PubSystemLogService.saveInfo(String.format("接口创建经手人,名称：%s 电话：%s 订单编号：%s", name, mobile, tradeId));
        return response.getData().getEtypeId();
    }


    private void buildBaseInfoByOtype(EshopSaleOrderEntity orderEntity, Otype otype) {
        if (otype == null) {
            return;
        }
        orderEntity.setKtypeId(otype.getKtypeId());
        orderEntity.setBtypeId(otype.getBtypeId());
        if (otype.getOcategory().equals(OrganizationType.DYY_SHOP)) {
            orderEntity.setOrderDeliverRequired(false);
            orderEntity.setDeliverType(BillDeliverType.NO_DELIVER);
        }
    }

    public void getOrderBtype(EshopSaleOrderEntity orderEntity, Otype otype, EshopSaleOrderDownloadTask task) {
        checkPlatformDistributorInfoNeedIgnore(orderEntity);
        orderEntity.setBtypeId(otype.getBtypeId());
        //通过分销商名称匹配
        BigInteger idByDistributorName = getBtypeIdByDistributorName(orderEntity, otype, task);
        if (idByDistributorName != null && idByDistributorName.compareTo(BigInteger.ZERO) > 0) {
            orderEntity.setBtypeId(idByDistributorName);
            if (null == orderEntity.getCommissionBtypeId() || BigInteger.ZERO.compareTo(orderEntity.getCommissionBtypeId()) == 0) {
                orderEntity.setCommissionBtypeId(idByDistributorName);
            }
        } else {
            doGetBtypeIdByBuyer(orderEntity, otype, task);
        }
        //如果分销商返回的结果为0通过买家账号匹配
        doBuildPayTypeId(orderEntity, otype);
    }

    private void doSaveBtypeExpandInfo(EshopSaleOrderEntity orderEntity, BigInteger idByDistributorName) {
        if (null == orderEntity.getBtypeExtendFieldRequestDto()) {
            return;
        }
        boolean notNull = idByDistributorName != null && idByDistributorName.compareTo(BigInteger.ZERO) > 0;
        if (!notNull) {
            return;
        }

    }

    private BigInteger checkAndAutoCreateBtype(EshopSaleOrderEntity orderEntity, Otype otype, int matchMode, EshopSaleOrderDownloadTask task) {
        BigInteger btypeId = BigInteger.ZERO;
        try {
            String name = "";
            Boolean support = bizConfig.getAutoCreateBtypeAndPlaintextPlatformDistributorName().get(otype.getShopType().name().toLowerCase());
            //平台不支持
            if (null == support) {
                return btypeId;
            }
            //平台分销商名称匹配，且从接口返回的名称是密文
            if (0 == matchMode) {
                if (!support || StringUtils.isEmpty(orderEntity.getPlatformDistributorName())) {
                    return btypeId;
                }
                name = orderEntity.getPlatformDistributorName();
            }
            if (1 == matchMode) {
                if (null == orderEntity.getEshopBuyer()) {
                    return btypeId;
                }
                //系统加密
                boolean sysEncrypt = orderEntity.getEshopBuyer().getDi().contains("SYS:");
                if (!sysEncrypt) {
                    return btypeId;
                }
                //解密
                EshopBuyer newBuyer = buyerService.decryptBuyer(orderEntity.getProfileId(), orderEntity.getEshopBuyer().getBuyerId());
                if (StringUtils.isEmpty(newBuyer.getCustomerShopAccount())) {
                    return btypeId;
                }
                name = newBuyer.getCustomerShopAccount();
            }
            EshopConfig eshopConfig = otype.getEshopConfig();
            //网店配置为空
            if (null == eshopConfig) {
                return btypeId;
            }
            boolean autoCreateBtypeEnabled = eshopConfig.getAutoCreateBtypeEnabled();
            //没有开启网店配置开关
            if (!autoCreateBtypeEnabled) {
                return btypeId;
            }
            doCreateBtype(orderEntity, name);
            PlatformBtypeMapping mapping = new PlatformBtypeMapping();
            mapping.setProfileId(orderEntity.getProfileId());
            mapping.setEshopId(otype.getId());
            mapping.setId(UId.newId());
            mapping.setPlatformBtypeName(name);
            mapping.setDeleted(false);
            mapping.setCreateType(3);
            mapping.setBtypeId(orderEntity.getBtypeId());
            mapping.setPlatformBtypeCode(orderEntity.getPlatformDistributorId());
            mapping.setPlatformBtypeUniqueId(orderEntity.getPlatformDistributorId());
            platformBtypeMapper.updatePlatformBtypeMapping(mapping);
            String log = "";
            if (null == orderEntity.getBtypeId() || BigInteger.ZERO.compareTo(orderEntity.getBtypeId()) == 0 || otype.getBtypeId().compareTo(orderEntity.getBtypeId()) == 0){
                log = String.format("根据订单%s【%s】生成往来单位失败",0==matchMode ? "平台分销商名称":"买家账号",name);
            }else {
                log = String.format("根据订单%s【%s】生成往来单位，并绑定对应关系",0==matchMode ? "平台分销商名称":"买家账号",name);
            }
            SysLogUtil.add(SysLogUtil.buildLog(orderEntity, task.getDownloadType().getOperateType(), log));
            return orderEntity.getBtypeId();
        } catch (Exception e) {
            logger.error(String.format("tradeOrderid:%s,mode:%s,创建往来单位失败", orderEntity.getTradeOrderId(), matchMode), e);
            return orderEntity.getBtypeId();
        }
    }

    private BigInteger getBtypeIdByDistributorName(EshopSaleOrderEntity orderEntity, Otype otype, EshopSaleOrderDownloadTask task) {
        BigInteger profileId = orderEntity.getProfileId();
        String distributorName = orderEntity.getPlatformDistributorName();
        BigInteger btypeId = getOrFetchCache(null == task ? null : task.getTaskId(),
                OrderDownloadCacheType.BTYPE_BY_DISTRIBUTOR_NAME_FROM_MAPPING,
                distributorName,
                BigInteger.class,
                () -> eshopOrderBaseInfoMapper.getBtypeIdFromMappingByDistributorName(profileId, distributorName));
        if (btypeId != null && btypeId.compareTo(BigInteger.ZERO) > 0) {
            return btypeId;
        }
        BigInteger btypeIdByFullName = getOrFetchCache(null == task ? null : task.getTaskId(),
                OrderDownloadCacheType.BTYPE_BY_DISTRIBUTOR_NAME,
                distributorName,
                BigInteger.class,
                () -> eshopOrderBaseInfoMapper.getBtypeIdByFullName(profileId, distributorName));
        if (btypeIdByFullName != null && btypeIdByFullName.compareTo(BigInteger.ZERO) > 0) {
            return btypeIdByFullName;
        }
        return checkAndAutoCreateBtype(orderEntity, otype, 0, task);
    }

    private void doGetBtypeIdByBuyer(EshopSaleOrderEntity orderEntity, Otype otype, EshopSaleOrderDownloadTask task) {
        EshopBuyer eshopBuyer = orderEntity.getEshopBuyer();
        if (eshopBuyer == null) {
            return;
        }
        BigInteger profileId = orderEntity.getProfileId();
        String md5Ai = Md5Utils.md5(String.format("%s%s%s", profileId, orderEntity.getOtypeId(), eshopBuyer.getAi()));
        String platformDistributorId = orderEntity.getPlatformDistributorId();
        BigInteger btypeIdByAi = getOrFetchCache(null == task ? null : task.getTaskId(),
                OrderDownloadCacheType.BTYPE_BY_AI_FROM_MAPPING,
                StringUtils.isEmpty(platformDistributorId) ? md5Ai : platformDistributorId,
                BigInteger.class,
                () -> eshopOrderBaseInfoMapper.getBtypeIdFromMappingByAi(profileId, StringUtils.isEmpty(platformDistributorId) ? md5Ai : platformDistributorId));
        if (btypeIdByAi != null && btypeIdByAi.compareTo(BigInteger.ZERO) > 0) {
            orderEntity.setBtypeId(btypeIdByAi);
            return;
        }

        if (StringUtils.isNotEmpty(eshopBuyer.getCustomerShopAccount()) && eshopBuyer.getDi().contains("SYS:")
                && null != otype && null != otype.getEshopConfig()
                && otype.getEshopConfig().isMatchLocalSameBtypeEnable()) {
            //解密
            EshopBuyer newBuyer = buyerService.decryptBuyer(orderEntity.getProfileId(), eshopBuyer.getBuyerId());
            if (null != newBuyer && StringUtils.isNotEmpty(newBuyer.getCustomerShopAccount())) {
                BigInteger btypeIdByFullName = getOrFetchCache(null == task ? null : task.getTaskId(),
                        OrderDownloadCacheType.BTYPE_BY_SHOP_ACCOUNT_NAME,
                        newBuyer.getCustomerShopAccount(),
                        BigInteger.class,
                        () -> eshopOrderBaseInfoMapper.getBtypeIdByFullName(profileId, newBuyer.getCustomerShopAccount()));
                if (btypeIdByFullName != null && btypeIdByFullName.compareTo(BigInteger.ZERO) > 0) {
                    orderEntity.setBtypeId(btypeIdByFullName);
                }
            }
        }
        checkAndAutoCreateBtype(orderEntity, otype, 1, task);
    }

    //按照网店设置 对 结算单位取值（payTypeId）
    //如果网店设置页面，客户配置的是“跟平台结算，取值店铺默认往来单位”时，取店铺默认往来单位  。
    // 网店设置页面，客户配置的是“跟销售方结算，取值销售往来单位时”，取销售单位
    public void doBuildPayTypeId(EshopSaleOrderEntity orderEntity, Otype otype) {
        if (null == orderEntity || null == otype) {
            return;
        }
        int btypeGenerateType = otype.getBtypeGenerateType();
        //按网店记账,跟平台结算，取值店铺默认往来单位
        if (0 == btypeGenerateType) {
            orderEntity.setPayBtypeId(otype.getBtypeId());
        }
        //按分销商记账，跟销售方结算，取值销售往来单位时
        if (1 == btypeGenerateType) {
            orderEntity.setPayBtypeId(orderEntity.getBtypeId());
        }
    }

    private void loopRetryGetBtypeByConfig(EshopSaleOrderEntity orderEntity, String name, BaseBtype baseBtype, int retryCount) {
        if (null == baseBtype || null == orderEntity || StringUtils.isEmpty(name)) {
            return;
        }
        if (1 > retryCount) {
            return;
        }
        try {
            Thread.sleep(200 * RandomUtils.randomInt(1, 25));
        } catch (Exception e) {
            CommonUtil.doLogByEnable(logger, LogLevelEnum.ERROR, String.format("retryCreateBtype，sleepError : req【%s】", JsonUtils.toJson(baseBtype)), e);
        }
        GeneralResult<Object> generalResult = baseApi.saveBtype(baseBtype);
        String log = String.format("KEYLOG,createBtype: req【%s】,rsp【%s】,retryCount【%s】", JsonUtils.toJson(baseBtype), JsonUtils.toJson(generalResult), retryCount);
        SysLogUtil.buildAndAddKeyLog(orderEntity, OrderOpreateType.AUTO_DOWNLOAD, log);
        if (generalResult.getCode() == 200) {
            BigInteger id = JsonUtils.toObject(JsonUtils.toJson(generalResult.getData()), BigInteger.class);
            orderEntity.setBtypeId(id);
            return;
        }
        if (generalResult.getCode() != 200 && 1 == retryCount) {
            BtypeQueryRequest request = new BtypeQueryRequest();
            BtypeQueryParameter parameter = new BtypeQueryParameter(name);
            request.setQueryParams(parameter);
            GeneralResult<BaseBtypeResponse> result = baseApi.getBtype(request);
            if (result.getCode() == 200 &&
                    null != result.getData() &&
                    1 == result.getData().getTotal()) {
                BigInteger id = result.getData().getList().get(0).getId();
                orderEntity.setBtypeId(id);
            }
        }
        retryCount--;
        loopRetryGetBtypeByConfig(orderEntity, name, baseBtype, retryCount);
    }

    private void retryGetBtype(EshopSaleOrderEntity orderEntity, String name) {
        BtypeQueryRequest request = new BtypeQueryRequest();
        BtypeQueryParameter parameter = new BtypeQueryParameter(name);
        request.setQueryParams(parameter);
        GeneralResult<BaseBtypeResponse> result = baseApi.getBtype(request);
        String log = String.format("KEYLOG,retryCreateBtype: req【%s】,rsp【%s】", JsonUtils.toJson(request), JsonUtils.toJson(result));
        SysLogUtil.buildAndAddKeyLog(orderEntity, OrderOpreateType.AUTO_DOWNLOAD, log);
        if (result.getCode() != 200) {
            return;
        }
        BaseBtypeResponse data = result.getData();
        if (data == null || data.getTotal() != 1) {
            return;
        }
        BigInteger id = data.getList().get(0).getId();
        orderEntity.setBtypeId(id);
    }

    private void buildLocalFreightInfo(EshopOrderEntity apiOrder, EshopSaleOrderEntity orderEntity) {
        buildOrderBuyerFreightFee(apiOrder, orderEntity);
        List<EshopOrderDetailEntity> apiDetails = apiOrder.getOrderDetails();
        //判断明细是否有物流信息
        boolean hasDetailFreightInfo = apiDetails.stream().map(EshopOrderDetailEntity::getFreightInfoList).flatMap(List::stream).filter(f -> null != f && (StringUtils.isNotEmpty(f.getFreightBillNo()) || StringUtils.isNotEmpty(f.getFreightCode()) || StringUtils.isNotEmpty(f.getFreightName()))).count() > 0;
        //明细有，以明细为准，汇总到主表
        if (hasDetailFreightInfo){
            List<EshopOrderDetailFreight> mainFreightList = new ArrayList<>();
            for (EshopSaleOrderDetail orderDetail : orderEntity.getOrderDetails()) {
                Optional<EshopOrderDetailEntity> first = apiDetails.stream().filter(a -> a.getOid().equals(orderDetail.getTradeOrderDetailId())).findFirst();
                if (!first.isPresent()){
                    continue;
                }
                EshopOrderDetailEntity apiDetail = first.get();
                List<FreightInfo> detailFreightInfoList = apiDetail.getFreightInfoList();
                if (CollectionUtils.isEmpty(detailFreightInfoList)){
                    continue;
                }
                List<EshopOrderDetailFreight> orderDetailFreightList = new ArrayList<>();
                for (FreightInfo freightInfo : detailFreightInfoList) {
                    EshopOrderDetailFreight detailFreight = new EshopOrderDetailFreight();
                    detailFreight.setId(UId.newId());
                    detailFreight.setProfileId(orderEntity.getProfileId());
                    detailFreight.setOtypeId(orderEntity.getOtypeId());
                    detailFreight.setTradeOrderId(orderEntity.getTradeOrderId());
                    detailFreight.setTradeOrderDetailId(orderDetail.getTradeOrderDetailId());
                    detailFreight.setCreateTime(new Date());
                    detailFreight.setCreateType(0);
                    detailFreight.setFreightCode(freightInfo.getFreightCode());
                    detailFreight.setFreightName(freightInfo.getFreightName());
                    detailFreight.setFreightBillNo(freightInfo.getFreightBillNo());
                    if (StringUtils.isNotEmpty(detailFreight.getFreightCode())){
                        QueryFreightMappingParameter parameter = new QueryFreightMappingParameter();
                        parameter.setShopType(orderEntity.getShopType());
                        parameter.setCode(detailFreight.getFreightCode());
                        parameter.setQueryLocal(true);
                        FreightMapping freightMapping = FreightUtils.getFreightMapping(parameter);
                        if (freightMapping != null) {
                            detailFreight.setFreightCode(freightMapping.getLocalCode());
                            detailFreight.setFreightName(freightMapping.getLocalName());
                        }
                    }
                    orderDetailFreightList.add(detailFreight);
                }
                orderDetail.setFreightList(orderDetailFreightList);
                mainFreightList.addAll(orderDetailFreightList);
                List<EshopSaleOrderFreight> freightList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(mainFreightList)){
                    for (EshopOrderDetailFreight mainFreight : mainFreightList) {
                        //去重
                        boolean match = freightList.stream().anyMatch(f -> f.getFreightCode().equals(mainFreight.getFreightCode()) && f.getFreightName().equals(mainFreight.getFreightName()) && f.getFreightNo().equals(mainFreight.getFreightBillNo()));
                        if (match){
                            continue;
                        }
                        EshopSaleOrderFreight freight = new EshopSaleOrderFreight();
                        freight.setId(UId.newId());
                        freight.setProfileId(orderEntity.getProfileId());
                        freight.setEshopOrderId(orderEntity.getId());
                        freight.setFreightCode(mainFreight.getFreightCode());
                        freight.setFreightNo(mainFreight.getFreightBillNo());
                        freight.setFreightName(mainFreight.getFreightName());
                        freight.setCreateTime(new Date());
                        freightList.add(freight);
                    }
                    orderEntity.setFreights(freightList);
                }
            }
            return;
        }
        //明细没有，以主表为准，所有明细物流信息等于主表物流信息
        //构建主表的物流信息
        buildOrderMainFreightInfo(apiOrder, orderEntity);
        List<EshopSaleOrderFreight> freights = orderEntity.getFreights();
        for (EshopSaleOrderDetail orderDetail : orderEntity.getOrderDetails()) {
            if (CollectionUtils.isEmpty(freights)){
                continue;
            }
            List<EshopOrderDetailFreight> orderDetailFreightList = new ArrayList<>();
            for (EshopSaleOrderFreight freight : freights) {
                EshopOrderDetailFreight detailFreight = new EshopOrderDetailFreight();
                detailFreight.setId(UId.newId());
                detailFreight.setProfileId(orderEntity.getProfileId());
                detailFreight.setOtypeId(orderEntity.getOtypeId());
                detailFreight.setTradeOrderId(orderEntity.getTradeOrderId());
                detailFreight.setTradeOrderDetailId(orderDetail.getTradeOrderDetailId());
                detailFreight.setCreateTime(new Date());
                detailFreight.setCreateType(0);
                detailFreight.setFreightCode(freight.getFreightCode());
                detailFreight.setFreightName(freight.getFreightName());
                detailFreight.setFreightBillNo(freight.getFreightNo());
                orderDetailFreightList.add(detailFreight);
            }
            if (CollectionUtils.isNotEmpty(orderDetailFreightList)){
                orderDetail.setFreightList(orderDetailFreightList);
            }
        }
    }

    private void buildOrderMainFreightInfo(EshopOrderEntity apiOrder, EshopSaleOrderEntity orderEntity) {
        String code = apiOrder.getFreightCode();
        orderEntity.setPlatformFreightCode(code);
        orderEntity.setPlatformFreightName(apiOrder.getFreightName());
        orderEntity.setLocalFreightName(apiOrder.getFreightName());
        orderEntity.setLocalFreightCode(code);
        orderEntity.setLocalFreightBillNo(apiOrder.getFreightBillNo());
        QueryFreightMappingParameter parameter = new QueryFreightMappingParameter();
        parameter.setShopType(orderEntity.getShopType());
        parameter.setCode(code);
        parameter.setQueryLocal(true);
        FreightMapping freightMapping = FreightUtils.getFreightMapping(parameter);
        if (freightMapping != null) {
            orderEntity.setLocalFreightName(freightMapping.getLocalName());
            orderEntity.setLocalFreightCode(freightMapping.getLocalCode());
        }
        List<EshopSaleOrderFreight> freightList = apiOrder.getFreightList();
        if (CollectionUtils.isNotEmpty(freightList)) {
            for (EshopSaleOrderFreight apiFreight : freightList) {
                apiFreight.setId(UId.newId());
                apiFreight.setEshopOrderId(orderEntity.getId());
                apiFreight.setCreateTime(new Date());
                apiFreight.setProfileId(orderEntity.getProfileId());
            }
        } else if (StringUtils.isNotEmpty(orderEntity.getLocalFreightCode()) ||
                StringUtils.isNotEmpty(orderEntity.getLocalFreightBillNo()) ||
                StringUtils.isNotEmpty(orderEntity.getLocalFreightName())) {
            EshopSaleOrderFreight freight = new EshopSaleOrderFreight();
            freight.setId(UId.newId());
            freight.setProfileId(orderEntity.getProfileId());
            freight.setEshopOrderId(orderEntity.getId());
            freight.setFreightCode(orderEntity.getLocalFreightCode());
            freight.setFreightNo(orderEntity.getLocalFreightBillNo());
            freight.setFreightName(orderEntity.getLocalFreightName());
            freight.setCreateTime(new Date());
            freightList.add(freight);
        }
        orderEntity.setFreights(freightList);
    }

    private void buildCustomerFreightInfo(EshopOrderEntity apiOrder, EshopSaleOrderEntity orderEntity) {
        QueryFreightMappingParameter customParam = new QueryFreightMappingParameter();
        customParam.setShopType(orderEntity.getShopType());
        customParam.setCode(orderEntity.getCustomerExpectedFreightCode());
        customParam.setQueryLocal(true);
        FreightMapping customFreightMapping = FreightUtils.getFreightMapping(customParam);
        if (customFreightMapping == null) {
            return;
        }
        orderEntity.setCustomerExpectedFreightName(customFreightMapping.getLocalName());
        orderEntity.setCustomerExpectedFreightCode(customFreightMapping.getLocalCode());
        buildCustomerFreightMarkData(apiOrder, customFreightMapping, orderEntity.getOrderMarks());
        for (EshopSaleOrderDetail detail : orderEntity.getOrderDetails()) {
            buildCustomerFreightMarkData(apiOrder, customFreightMapping,detail.getOrderDetailMarks());
        }
    }

    private void buildCustomerFreightMarkData(EshopOrderEntity apiOrder, FreightMapping customFreightMapping, List<EshopOrderMarkEntity> marks) {
        if (CollectionUtils.isEmpty(marks)){
            return;
        }
        for (EshopOrderMarkEntity mark : marks) {
            if (mark.getMarkCode().compareTo(BigInteger.valueOf(BaseOrderMarkEnum.BUYER_DESIGNATED_LOGISTICS.getCode())) == 0 ||
                    mark.getMarkCode().compareTo(BigInteger.valueOf(BaseOrderMarkEnum.DESIGNATED_LOGISTICS.getCode())) == 0){
                mark.setBubble(mark.getBubble().replace(apiOrder.getCustomerExpectedFreightName(), customFreightMapping.getLocalName()));
                BaseMarkBigDataEntity bigData = JsonUtils.toObject(mark.getBigData(),BaseMarkBigDataEntity.class);
                List<LogisticsInfo> logisticsInfo = bigData.getWhiteLogisticsInfo();
                if (null == bigData || CollectionUtils.isEmpty(logisticsInfo)){
                    continue;
                }
                for (LogisticsInfo info : logisticsInfo) {
                    info.setLogisticsCompanyName(customFreightMapping.getLocalName());
                    info.setLogisticsCompanyCode(customFreightMapping.getLocalCode());
                }
            }
        }
    }

    private static void buildOrderBuyerFreightFee(EshopOrderEntity apiOrder, EshopSaleOrderEntity orderEntity) {
        orderEntity.setOrderBuyerFreightFee(apiOrder.getBuyerFreightFee());
        if (orderEntity.getBusinessType().equals(BillBusinessType.SaleDistribution)) {
            //分销业务将买家运费加入到分销表中
            orderEntity.setOrderBuyerFreightFee(apiOrder.getDistributionFreightFee());
        }
    }

    private void buildLocalStoreInfo(EshopOrderEntity apiOrder, EshopSaleOrderEntity order, String taskId) {
        if (StringUtils.isEmpty(apiOrder.getStoreId())) {
            return;
        }
        EshopPlatformStoreMapping storeMapping = getOrFetchCache(taskId,
                OrderDownloadCacheType.STORE_MAPPING_BY_CODE,
                String.format("%s_%s_%s", order.getOtypeId(), CurrentUser.getProfileId(), apiOrder.getStoreId()),
                EshopPlatformStoreMapping.class,
                () -> platformStoreMappingMapper.getMappingKtypeIdByStoreCode(order.getOtypeId(), CurrentUser.getProfileId(), apiOrder.getStoreId()));
        if (storeMapping == null) {
            order.setKtypeId(BigInteger.ZERO);
            insertStoreMapping(apiOrder, order);
            return;
        }
        order.setKtypeId(storeMapping.getKtypeId());
    }

    private void insertStoreMapping(EshopOrderEntity apiOrder, EshopSaleOrderEntity order) {
        try {
            // 如果storeMapping == null 说明不存在此仓库
            if (VersionUtil.isNewVersion()) {
                EshopPlatformStoreMapping inDTO = buildPlatformStore(apiOrder);
                if (StringUtils.isNotEmpty(apiOrder.getStockCode())) {
                    BaseInfoMapper baseInfoMapper = BeanUtils.getBean(BaseInfoMapper.class);
                    List<com.wsgjp.ct.baseinfo.core.dao.entity.Ktype> ktypeList = baseInfoMapper.getKtypeList(CurrentUser.getProfileId());
                    Map<String, com.wsgjp.ct.baseinfo.core.dao.entity.Ktype> ktypeNameMap = ktypeList.stream()
                            .collect(Collectors.toMap(
                                    com.wsgjp.ct.baseinfo.core.dao.entity.Ktype::getFullname,
                                    Function.identity(),
                                    (existing, replacement) -> existing));
                    com.wsgjp.ct.baseinfo.core.dao.entity.Ktype ktype = ktypeNameMap.get(apiOrder.getStockCode());
                    if (ktype != null) {
                        inDTO.setKtypeId(ktype.getId());
                        inDTO.setCorrespondFlag(true);
                    }
                }

                try {
                    platformStoreMappingMapper.addPlatformStoreMappingByAdd(inDTO);
                    order.setKtypeId(inDTO.getKtypeId());
                } catch (Exception insertException) {
                    // 插入失败可能是并发导致，再次查询一次
                    EshopPlatformStoreMapping platformStoreMapping = new EshopPlatformStoreMapping();
                    platformStoreMapping.setProfileId(CurrentUser.getProfileId());
                    platformStoreMapping.setPlatformStoreStockId(apiOrder.getStoreId());
                    platformStoreMapping.setEshopId(order.getOtypeId());
                    EshopPlatformStoreMapping existingMapping = platformStoreMappingMapper.queryPlatformStoreByEshopId(platformStoreMapping);
                    if (existingMapping != null) {
                        order.setKtypeId(existingMapping.getKtypeId());
                    } else {
                        // 如果仍然查不到，记录错误但不抛出异常
                        logger.error("无法创建或查询到网店仓库映射，storeId: {}, eshopId: {}",
                                apiOrder.getStoreId(), apiOrder.getEshopId());
                        order.setKtypeId(BigInteger.ZERO);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("根据仓库id自动生成网店仓库失败: " + e.getMessage(), e);
            // 设置默认值避免影响订单构建流程
            order.setKtypeId(BigInteger.ZERO);
        }
    }

    @NotNull
    private static EshopPlatformStoreMapping buildPlatformStore(EshopOrderEntity apiOrder) {
        EshopPlatformStoreMapping inDTO = new EshopPlatformStoreMapping();
        inDTO.setId(UId.newId());
        inDTO.setProfileId(CurrentUser.getProfileId());
        inDTO.setCorrespondFlag(false);
        inDTO.setPlatformStoreStockId(apiOrder.getStoreId());
        inDTO.setPlatformStoreName(StringUtils.isNotEmpty(apiOrder.getStockCode()) ? apiOrder.getStockCode() : "");
        inDTO.setBusinessLabel("");
        inDTO.setPlatformStoreAddress("");
        inDTO.setPlatformStoreNew(true);
        inDTO.setType(66);
        inDTO.setCreateTime(new Date());
        inDTO.setOtypeName("");
        inDTO.setEshopId(apiOrder.getEshopId());
        inDTO.setKtypeId(BigInteger.ZERO);
        inDTO.setPlatformStoreType(1);
        inDTO.setDeleted(false);
        inDTO.setSource(PlatformStoreMappingConstants.MANUAL_SOURCE);
        return inDTO;
    }

    private EshopBuyer doEncryptBuyer(EshopOrderEntity apiOrder) {
        EshopBuyer eshopBuyer = buildBuyerInfo(apiOrder);
        if (eshopBuyer.getBuyerId() != null) {
            List<EncryptFullAdapter> collect = Collections.singletonList(eshopBuyer);
            buyerService.doEncrypt(collect);
        }
        if (eshopBuyer.getBuyerId() == null || eshopBuyer.getBuyerId().compareTo(BigInteger.ZERO) == 0) {
            SysLogUtil.add(SysLogUtil.buildLog(apiOrder.getTradeId(), apiOrder.getEshopOrderId(), apiOrder.getEshopId(),
                    apiOrder.getTradeStatus(), ProcessState.NoSubmit,
                    CommonUtil.substringStr(String.format("buyerId为空：%s", JsonUtils.toJson(eshopBuyer)), 4900),
                    RefundStatus.NONE, OrderOpreateType.DOWNLOAD));
        }
        buyerService.saveEncryptedBuyer(eshopBuyer);
        return eshopBuyer;
    }

    /**
     * 批量构建订单的收货信息
     *
     * @param apiOrderList
     * @return {@link Map}<{@link String},{@link EshopBuyer}>
     */
    public Map<String, EshopBuyer> doEncryptBuyer(List<EshopOrderEntity> apiOrderList, Otype otype) {
        Map<String, EshopBuyer> map = new HashMap<>();
        if (CollectionUtils.isEmpty(apiOrderList) || null == otype) {
            return map;
        }
        List<List<EshopOrderEntity>> splitList = CommonUtil.splitList(apiOrderList, bizConfig.getOrderDownLoadBatchBuildBuyer());
        for (List<EshopOrderEntity> orderEntityList : splitList) {
            if (CollectionUtils.isNotEmpty(orderEntityList)) {
                batchBuildBuyer(orderEntityList, map, otype);
            }
        }
        return map;
    }

    @Nullable
    private void batchBuildBuyer(List<EshopOrderEntity> apiOrderList, Map<String, EshopBuyer> map, Otype otype) {
        try {
            List<EshopBuyer> buyerList = new ArrayList<>();
            for (EshopOrderEntity apiOrder : apiOrderList) {
                EshopBuyer eshopBuyer = buildBuyerInfo(apiOrder);
                if (eshopBuyer.getBuyerId() != null) {
                    buyerList.add(eshopBuyer);
                }
                map.put(apiOrder.getTradeId(), eshopBuyer);
            }
            List<EncryptFullAdapter> collect = new ArrayList<>(buyerList);
            buyerService.doEncrypt(collect);
            List<EshopBuyer> needInsert = new ArrayList<>();
            //有赞订单索引串升级
            FillSearchDigestRequest fillSearchDigestRequest = new FillSearchDigestRequest();
            fillSearchDigestRequest.setShopId(otype.getId());
            fillSearchDigestRequest.setShopType(otype.getShopType());
            for (EshopBuyer buyer : buyerList) {
                fillSearchDigestRequest.setBuyerInfo(buyer);
                bifrostEshopOrderService.fillBuyerInfoSearchDigest(fillSearchDigestRequest);
                if (buyer.getBuyerId() == null || buyer.getBuyerId().compareTo(BigInteger.ZERO) == 0) {
                    buyer.setBuyerId(UId.newId());
                }
                if (buyer.getProfileId() == null || buyer.getProfileId().compareTo(BigInteger.ZERO) == 0) {
                    buyer.setProfileId(CurrentUser.getProfileId());
                }
                EshopBuyer exists = buyerService.queryBuyerIdByHashMark(buyer);
                if (exists != null) {
                    QueryBuyerRequest request = new QueryBuyerRequest();
                    request.setBuyerId(exists.getBuyerId());
                    EshopBuyer localBuyer = mapper.queryBuyerOnlyId(request);
                    if (null != localBuyer && null != localBuyer.getBuyerId() && BigInteger.ZERO.compareTo(localBuyer.getBuyerId()) != 0) {
                        buyer.setBuyerId(localBuyer.getBuyerId());
                        continue;
                    }
                    CommonUtil.doLogByEnable(logger, LogLevelEnum.ERROR, String.format("error buyer cache, key:%s, buyerId :%s", buyer.getHashKey(), exists.getBuyerId()));
                }
                needInsert.add(buyer);
            }
            if (CollectionUtils.isNotEmpty(needInsert)) {
                buyerService.batchInsertEshopBuyer(needInsert);
                for (EshopBuyer buyer : buyerList) {
                    String key = String.format("pl_buyer_save_%s_%s", buyer.getProfileId(), buyer.getHashKey());
                    buyerService.doCacheBuyer(key, buyer);
                }
            }
        } catch (Exception e) {
            CommonUtil.doLogByEnable(logger, LogLevelEnum.ERROR, "order download batch build buyer error", e);
            map.clear();
        }
    }

    private EshopBuyer doEncryptPickUpAddress(EshopOrderEntity apiOrder) {
        EshopBuyer selfPickUpInfo = buildPickUpInfo(apiOrder);
        List<EncryptFullAdapter> collect = Collections.singletonList(selfPickUpInfo);
        if (BigInteger.ZERO.compareTo(selfPickUpInfo.getOrgId()) == 0) {
            return selfPickUpInfo;
        }
        buyerService.doEncrypt(collect);
        buyerService.saveEncryptedBuyer(selfPickUpInfo);
        return selfPickUpInfo;
    }

    private EshopBuyer buildBuyerInfo(EshopOrderEntity apiOrder) {

        EshopBuyer eshopBuyer = new EshopBuyer();
        ReceiverInfo receiverInfo = apiOrder.getReceiverInfo();
        if (receiverInfo == null) {
            logger.error(String.format("profileid:%s tradeid:%s null receiverInfo", CurrentUser.getProfileId()));
            return eshopBuyer;
        }
        BigInteger newId = UId.newId();
        eshopBuyer.setBuyerId(newId);
        eshopBuyer.setProfileId(apiOrder.getProfileId());
        eshopBuyer.setOtypeId(apiOrder.getEshopId());
        eshopBuyer.setEshopType(apiOrder.getShopType());
        eshopBuyer.setCustomerReceiver(receiverInfo.getReceiver());
        eshopBuyer.setCustomerIdCardName(receiverInfo.getCustomerIdCardName());
        eshopBuyer.setCustomerIdCard(receiverInfo.getCustomerIdCardNumber());

        eshopBuyer.setCustomerEmail(receiverInfo.getCustomerEmail());
        eshopBuyer.setCustomerShopAccount(receiverInfo.getCustomerShopAccount());
        eshopBuyer.setCustomerPayAccount(receiverInfo.getCustomerPayAccount());
        eshopBuyer.setCustomerReceiverAddress(receiverInfo.getCustomerAddress());
        eshopBuyer.setCustomerReceiverFullAddress(String.format("%s%s%s%s%s",
                receiverInfo.getCustomerProvince(),
                receiverInfo.getCustomerCity(),
                receiverInfo.getCustomerDistrict(),
                receiverInfo.getCustomerTown(),
                receiverInfo.getCustomerAddress()));
        eshopBuyer.setCustomerReceiverCity(receiverInfo.getCustomerCity());
        eshopBuyer.setCustomerReceiverCountry(receiverInfo.getCustomerCountry());
        eshopBuyer.setCustomerReceiverDistrict(receiverInfo.getCustomerDistrict());
        eshopBuyer.setCustomerReceiverMobile(receiverInfo.getCustomerMobile());
        eshopBuyer.setCustomerReceiverPhone(receiverInfo.getCustomerTel());
        eshopBuyer.setCustomerReceiverTown(receiverInfo.getCustomerTown());
        eshopBuyer.setCustomerReceiverProvince(receiverInfo.getCustomerProvince());
        eshopBuyer.setCustomerReceiverZipCode((StringUtils.isEmpty(receiverInfo.getCustomerZipCode()) || "000000".equals(receiverInfo.getCustomerZipCode())) ? "" : receiverInfo.getCustomerZipCode());
        eshopBuyer.setDi(receiverInfo.getDi());
        eshopBuyer.setRi(receiverInfo.getReceiverDicId());
        eshopBuyer.setAi(receiverInfo.getAccountDicId());
        eshopBuyer.setMi(receiverInfo.getMobileDicId());
        eshopBuyer.setPi(receiverInfo.getPhoneDicId());
        eshopBuyer.setPai(receiverInfo.getPayAccountDicId());
        eshopBuyer.setAddri(receiverInfo.getAddressId());
        eshopBuyer.setIci(receiverInfo.getIdCardId());
        eshopBuyer.setIni(receiverInfo.getIdCardNameId());
        if (TradeTypeEnum.CYCLE_PURCHASE == apiOrder.getTradeType() && StringUtils.isNotEmpty(apiOrder.getParentTradeId())) {
            eshopBuyer.setTradeId(StringUtils.isEmpty(receiverInfo.getTradeId()) ?
                            (StringUtils.isEmpty(apiOrder.getParentTradeId()) ? apiOrder.getTradeId() : apiOrder.getParentTradeId()) :
                            receiverInfo.getTradeId());
        } else {
            eshopBuyer.setTradeId(StringUtils.isEmpty(receiverInfo.getTradeId()) ? apiOrder.getTradeId() : receiverInfo.getTradeId());
        }
        if (receiverInfo.isNeedPlatformDecrypt() || apiOrder.isFuzzySensitiveData()) {
            eshopBuyer.setNeedPlatformDecrypt(true);
        }
        eshopBuyer.setOpenReceiverId(receiverInfo.getUniqueMark());
        eshopBuyer.setOpenAddressId(receiverInfo.getPlatformOpenId());
        eshopBuyer.setPlatformProvinceCode(receiverInfo.getProvinceCode());
        eshopBuyer.setPlatformCityCode(receiverInfo.getCityCode());
        eshopBuyer.setPlatformDistrictCode(receiverInfo.getDistrictCode());
        eshopBuyer.setPlatformStreetCode(receiverInfo.getTownCode());
        eshopBuyer.setOpenAddressId2(receiverInfo.getPlatformOpenId2());
        if (StringUtils.isEmpty(eshopBuyer.getCustomerReceiverDistrict())){
            if (bizConfig.getEnableFillDistrictByTown().contains(eshopBuyer.getCustomerReceiverCity())){
                eshopBuyer.setCustomerReceiverDistrict(eshopBuyer.getCustomerReceiverTown());
            }
        }
        return eshopBuyer;
    }

    private EshopBuyer buildPickUpInfo(EshopOrderEntity apiOrder) {
        SelfPickupInfo selfPickup = apiOrder.getSelfPickUpInfo();
        EshopBuyer selfPickUpInfo = new EshopBuyer();
        if (selfPickup == null) {
            return selfPickUpInfo;
        }
        BigInteger newId = UId.newId();
        selfPickUpInfo.setBuyerId(newId);
        selfPickUpInfo.setProfileId(apiOrder.getProfileId());
        selfPickUpInfo.setOtypeId(apiOrder.getEshopId());

        selfPickUpInfo.setEshopType(apiOrder.getShopType());
        selfPickUpInfo.setCustomerReceiverProvince(selfPickup.getSelfPickupSiteProvince());
        selfPickUpInfo.setCustomerReceiverCity(selfPickup.getSelfPickupSiteCity());
        selfPickUpInfo.setCustomerReceiverDistrict(selfPickup.getSelfPickupSiteDistrict());
        selfPickUpInfo.setCustomerReceiverAddress(selfPickup.getSelfPickupSiteAddress());
        selfPickUpInfo.setCustomerReceiverFullAddress(String.format("%s%s%s%s",
                selfPickup.getSelfPickupSiteProvince(),
                selfPickup.getSelfPickupSiteCity(),
                selfPickup.getSelfPickupSiteDistrict(),
                selfPickup.getSelfPickupSiteAddress()));
        selfPickUpInfo.setCustomerReceiverMobile(selfPickup.getSelfPickupSiteMobile());
        selfPickUpInfo.setCustomerReceiver(selfPickup.getSelfPickupSiteUserName());
        selfPickUpInfo.setCustomerShopAccount(selfPickup.getSelfPickupSiteName());
        selfPickUpInfo.setCustomerIdCard(selfPickup.getIdCard());
        selfPickUpInfo.setCustomerIdCardName(selfPickup.getIdCardName());
        return selfPickUpInfo;
    }

    private void buildBuyerInfo(EshopOrderEntity apiOrder, EshopSaleOrderEntity orderEntity, Map<String, EshopBuyer> buyerMap) {
        EshopBuyer buyer = new EshopBuyer();
        if (CollectionUtils.isNotEmpty(buyerMap) &&
                null != buyerMap.get(orderEntity.getTradeOrderId()) &&
                null != buyerMap.get(orderEntity.getTradeOrderId()).getBuyerId() &&
                BigInteger.ZERO.compareTo(buyerMap.get(orderEntity.getTradeOrderId()).getBuyerId()) != 0) {
            buyer = buyerMap.get(orderEntity.getTradeOrderId());
        } else {
            buyer = doEncryptBuyer(apiOrder);
        }
        boolean selfPickUpOrder = apiOrder.getOrderMarks().stream().anyMatch(m -> BaseOrderMarkEnum.KTT_SELF_FETCH.getCode() == m.getCode());
        if (selfPickUpOrder) {
            EshopBuyer pickUpInfo = doEncryptPickUpAddress(apiOrder);
            orderEntity.setEshopBuyer(pickUpInfo);
            orderEntity.setBuyerId(pickUpInfo.getBuyerId());
            orderEntity.setReceiveAddressId(pickUpInfo.getBuyerId());
            orderEntity.setExtend(new EshopSaleOrderExtendEntity(orderEntity));
            //买家信息为付款人信息
            orderEntity.getExtend().setRealBuyerId(buyer.getBuyerId());
            return;
        }
        orderEntity.setEshopBuyer(buyer);
        orderEntity.setBuyerId(buyer.getBuyerId());
        orderEntity.setReceiveAddressId(buyer.getBuyerId());
        orderEntity.setExtend(new EshopSaleOrderExtendEntity(orderEntity));
        orderEntity.getExtend().setRealBuyerId(buyer.getBuyerId());
    }

    private void buildSenderInfo(EshopOrderEntity apiOrder, EshopSaleOrderEntity orderEntity) {
        EshopSenderInfo delivererInfo = new EshopSenderInfo();
        delivererInfo.setId(UId.newId());
        delivererInfo.setProfileId(orderEntity.getProfileId());
        orderEntity.setSenderInfo(delivererInfo);
        ReceiverInfo etypeInfo = apiOrder.getEtypeInfo();
        if (etypeInfo == null) {
            return;
        }
        delivererInfo.setSenderMobile(StringUtils.isEmpty(etypeInfo.getCustomerMobile()) ? etypeInfo.getCustomerTel() : etypeInfo.getCustomerMobile());
        delivererInfo.setSenderName(etypeInfo.getReceiver());
    }

    private void buildInvoiceInfo(EshopOrderEntity apiOrder, EshopSaleOrderEntity orderEntity) {
        EshopSaleOrderInvoiceInfo invoiceInfo = new EshopSaleOrderInvoiceInfo();
        BigInteger newId = UId.newId();
        invoiceInfo.setId(newId);
        invoiceInfo.setEshopOrderId(orderEntity.getId());
        invoiceInfo.setProfileId(orderEntity.getProfileId());
        invoiceInfo.setInvoiceType(InvoiceType.ENTERPRISE);
        OrderInvoice apiOrderOrderInvoice = apiOrder.getOrderInvoice();
        if (apiOrderOrderInvoice != null) {
            invoiceInfo.setInvoiceCategory(apiOrderOrderInvoice.getInvoiceCategory());
            invoiceInfo.setInvoiceType(apiOrderOrderInvoice.getInvoiceType());
            invoiceInfo.setInvoiceCode(apiOrderOrderInvoice.getInvoiceCode());
            invoiceInfo.setInvoiceTitle(apiOrderOrderInvoice.getInvoiceTitle());
            invoiceInfo.setInvoiceState(apiOrderOrderInvoice.getInvoiceState());
            invoiceInfo.setInvoiceCompany(apiOrderOrderInvoice.getInvoiceCompany());
            invoiceInfo.setInvoiceRegisterAddr(apiOrderOrderInvoice.getInvoiceRegisterAddr());
            invoiceInfo.setInvoiceRegisterPhone(apiOrderOrderInvoice.getInvoiceRegisterPhone());
            invoiceInfo.setInvoiceBank(apiOrderOrderInvoice.getInvoiceBank());
            invoiceInfo.setInvoiceBankAccount(apiOrderOrderInvoice.getInvoiceBankAccount());
            invoiceInfo.setInvoiceRequired(apiOrderOrderInvoice.isNeeInvoice());
            invoiceInfo.setInvoiceRemark(apiOrderOrderInvoice.getInvoiceRemark());
        }
        doEncryptInvoice(invoiceInfo);
        orderEntity.setInvoiceInfo(invoiceInfo);
    }

    public void doEncryptInvoice(EshopSaleOrderInvoiceInfo invoiceInfo) {
        if (null == invoiceInfo) {
            return;
        }
        BaseSysSecretInfo invoiceSysInfo = new BaseSysSecretInfo();
        invoiceSysInfo.setId(UId.newId());
        invoiceSysInfo.setProfileId(invoiceInfo.getProfileId());
        invoiceSysInfo.setPhone(invoiceInfo.getInvoiceRegisterPhone());
        invoiceSysInfo.setAddress(invoiceInfo.getInvoiceRegisterAddr());
        invoiceSysInfo.setSourceTable("pl_eshop_sale_order_invoice");
        invoiceSysInfo.setCreateTime(new Date());
        if (invoiceSysInfo.getId() != null && BigInteger.ZERO.compareTo(invoiceSysInfo.getId()) != 0) {
            sysSecretInfoService.encryptAndSaveSysSecretInfo(invoiceSysInfo);
        }
        //模糊化数据回填
        invoiceInfo.setSecretId(invoiceSysInfo.getId());
        invoiceInfo.setInvoiceRegisterPhone(invoiceSysInfo.getPhone());
        invoiceInfo.setInvoiceRegisterAddr(invoiceSysInfo.getAddress());
    }

    private void buildOrderDetails(EshopOrderEntity apiOrder, EshopSaleOrderEntity orderEntity, Otype otype, EshopSaleOrderDownloadTask task) {
        if (apiOrder.getOrderDetails() == null || apiOrder.getOrderDetails().isEmpty()) {
            return;
        }
        List<EshopOrderDetailEntity> apiDetails = apiOrder.getOrderDetails();
        String platformJson = apiOrder.getPlatformSpecialJson();
        if (apiDetails == null || apiDetails.isEmpty()) {
            throw new RuntimeException(String.format("订单号：%s,订单明细为空，请检查", apiOrder.getTradeId()));
        }
        List<ProductGiftRelation> giftRelationList = apiDetails.stream().map(EshopOrderDetailEntity::getGiftRelations).flatMap(List::stream).collect(Collectors.toList());
        List<EshopSaleOrderDetail> tempDetails = new ArrayList<>();
        for (EshopOrderDetailEntity apiDetail : apiDetails) {
            BigInteger newId = UId.newId();
            EshopSaleOrderDetail tempDetail = new EshopSaleOrderDetail();
            tempDetail.setId(newId);
            tempDetail.setDeleted(orderEntity.getDeleted());
            tempDetail.setDeliverRequired(apiDetail.isCanSendGoods());
            tempDetail.setNoShippingBookkeeping(apiDetail.isNoShippingBookkeeping());
            tempDetail.setBtypeId(orderEntity.getBtypeId());
            tempDetail.setProfileId(orderEntity.getProfileId());
            tempDetail.setOtypeId(orderEntity.getOtypeId());
            tempDetail.setEshopOrderId(orderEntity.getId());
            buildDetailKtypeId(orderEntity, apiDetail, tempDetail);
            tempDetail.setTradeOrderDetailId(apiDetail.getOid());
            tempDetail.setPlatformPtypePicUrl(apiDetail.getPicUrl());
            tempDetail.setPlatformPtypeId(apiDetail.getNumId());
            //对于返回的网店商品信息没有numid的情况，根据返回的网店商品名字自动md5生成numid
            if (StringUtils.isEmpty(tempDetail.getPlatformPtypeId())) {
                tempDetail.setPlatformPtypeId(Md5Utils.md5(apiDetail.getTitle()));
            }
            tempDetail.setPlatformSkuId(apiDetail.getSkuId());
            tempDetail.setPlatformPtypeName(CommonUtil.checkSpecialCharacters(CommonUtil.substringStr(apiDetail.getTitle(),490)));
            tempDetail.setPlatformPropertiesName(apiDetail.getPropertiesValue(bizConfig.getShopTypeList().contains(String.valueOf(otype.getShopType().getCode())) ? 0 : 1));
            if(otype.getEshopConfig()!=null&&otype.getEshopConfig().isUsePlatformSkuidAsXcode()) {
                tempDetail.setPlatformPtypeXcode(CommonUtil.checkSpecialCharacters(apiDetail.getSkuId()));
            } else {
                tempDetail.setPlatformPtypeXcode(CommonUtil.checkSpecialCharacters(apiDetail.getXcode()));
            }
            tempDetail.setPlatformDetailTradeState(apiDetail.getTradeStatus().equals(TradeStatus.ABNORMAL) ? apiOrder.getTradeStatus() : apiDetail.getTradeStatus());
            tempDetail.setLocalRefundProcessState(apiDetail.getRefundStatus());
            buildSupplierAndDistributorDetailInfo(apiDetail, tempDetail);
            //主表标记预售，整单明细都标记预售
            if (TradeTypeEnum.ADVANCE_FORWARD_SALE.equals(orderEntity.getOrderSaleType()) ||
                    TradeTypeEnum.ADVANCE_FORWARD_SALE_BY_STOCK.equals(orderEntity.getOrderSaleType()) ||
                    TradeTypeEnum.CYCLE_PURCHASE.equals(orderEntity.getOrderSaleType())) {
                tempDetail.setOrderSaleType(orderEntity.getOrderSaleType());
            } else {
                tempDetail.setOrderSaleType(apiDetail.getTradeType());
            }
            tempDetail.setBusinessType(buildDetailBusinessTypeWithOtype(apiDetail.getPlatformBusinessType(),apiOrder.getPlatformBusinessType(),otype));
            tempDetail.setPlatformStockId(StringUtils.isEmpty(apiDetail.getStockId()) ? apiOrder.getStoreId() : apiDetail.getStockId());
            tempDetail.setPlatformStockCode(StringUtils.isEmpty(apiDetail.getStockCode()) ? apiOrder.getStockCode() : apiDetail.getStockCode());
            tempDetail.setVerifyCode(apiDetail.getVerifyCode());
            tempDetail.setFlowChannel(StringUtils.isEmpty(apiDetail.getFlowChannel()) ? apiOrder.getFlowChannel() : apiDetail.getFlowChannel());
            if (apiDetail.getDetailMarks().stream().map(BaseOrderMarkEnum::getCode).collect(Collectors.toList()).stream().anyMatch(m -> BaseOrderMarkEnum.KTT_PARTIAL_REFUND.getCode() == m)) {
                tempDetail.setKttComment(apiDetail.getPlatformSpecialJson());
            }
            buildDetailReturnState(apiDetail, tempDetail);
            buildDetailPreferentialTotal(apiOrder, tempDetail);
            buildDetailPrice(apiDetail, tempDetail);
            if (eshopService.ShopTypesSupport(otype.getShopType(), bizConfig.getAbnormalPriceShopTypeList())) {
                buildAbnormalPrice(tempDetail);
            }
            if (BillBusinessType.SaleDistribution.equals(tempDetail.getBusinessType())) {
                buildDetailPriceForDistribution(apiDetail, tempDetail);
            }
            tempDetail.setDisplayCustomInfo(apiDetail.getDisplayCustomInfo());
            if (apiDetail.getQualityControlInfo() != null) {
                tempDetail.setPlatformQcResult(apiDetail.getQualityControlInfo().getStatus());
                tempDetail.setPlatformQcResultDesc(apiDetail.getQualityControlInfo().getDescription());
            }
            if (apiDetail.getIdentifyInfo() != null) {
                tempDetail.setPlatformIdentifyResult(apiDetail.getIdentifyInfo().getStatus());
                tempDetail.setPlatformIdentifyResultDesc(apiDetail.getIdentifyInfo().getDescription());
            }
            buildOrderDetailExtend(apiDetail, tempDetail, giftRelationList);
            buildOrderDetailTiming(apiDetail, tempDetail);
            buildMallFee(otype, tempDetail, apiDetail);
            //构建直播信息
            buildOrderDetailLiveBroadCast(apiDetail, tempDetail, apiOrder.getPayTime(), task.getTaskId(), orderEntity);
            BuildOrderDetailMark(apiDetail, tempDetail, platformJson);
            tempDetail.setPlatformGift(checkDetailPlatformGift(tempDetail, giftRelationList));
            tempDetails.add(tempDetail);
        }
        orderEntity.setOrderDetails(tempDetails);
        EshopSaleOrderEntity strategyOrder = doEshopOrderStrategy(orderEntity, otype, task);
        doRelationAfterStrategy(orderEntity,strategyOrder, otype, task, apiDetails);
    }

    private void doRelationAfterStrategy(EshopSaleOrderEntity origin,EshopSaleOrderEntity orderEntity, Otype otype, EshopSaleOrderDownloadTask task, List<EshopOrderDetailEntity> apiDetails) {
        EshopSaleOrderEntity copyOrder = CommonUtil.deepCopy(orderEntity, EshopSaleOrderEntity.class);
        orderEntity.setOrderDetails(new ArrayList<>());
        for (EshopSaleOrderDetail orderDetail : copyOrder.getOrderDetails()) {
            if (!orderDetail.isMappingState()){
                relationService.buildOrderRelation(orderEntity, orderDetail, otype, task, apiDetails.size());
            }else {
                orderEntity.getOrderDetails().add(orderDetail);
            }
        }
        origin.setOrderDetails(orderEntity.getOrderDetails());
        origin.setDetailCombos(orderEntity.getDetailCombos());
    }

    private EshopSaleOrderEntity doEshopOrderStrategy(EshopSaleOrderEntity orderEntity, Otype otype, EshopSaleOrderDownloadTask task) {
        try {
            if (null == orderEntity || CollectionUtils.isEmpty(orderEntity.getOrderDetails()) || null == otype){
                return orderEntity;
            }
            EshopOrderStrategyDto mainStrategy = strategyService.getEshopOrderStrategy();
            if (null == mainStrategy || CollectionUtils.isEmpty(mainStrategy.getDtoList())){
                return orderEntity;
            }
            List<EshopOrderStrategyPtypeDetailDto> dtoList = mainStrategy.getDtoList();
            EshopSaleOrderEntity tragetOrder = CommonUtil.deepCopy(orderEntity, EshopSaleOrderEntity.class);
            List<EshopSaleOrderDetail> tragetDetail = new ArrayList<>();
            EshopProductDataService productDataService = GetBeanUtil.getBean(EshopProductDataService.class);
            Optional<EshopOrderStrategyPtypeDetailDto> first = dtoList.stream().filter(d -> CollectionUtils.isEmpty(d.getOtypeIds()) || d.getOtypeIds().contains(otype.getId())).findFirst();
            if (!first.isPresent()){
                return orderEntity;
            }
            EshopOrderStrategyPtypeDetailDto strategy = first.get();
            if(!checkAvailableStrategy(otype, strategy)){
                return orderEntity;
            }
            EshopOrderStrategyMatchType matchType = strategy.getMatchType();
            int target = matchType.getTarget();
            boolean useKeyWord = matchType.isUseKeyWord();
            EshopSaleOrderEntity strategyResult = null;
            switch (target){
                case 0:
                    strategyResult = executeStrategyByOrder(orderEntity, otype, task, tragetOrder, matchType, strategy, useKeyWord,  productDataService, tragetDetail);
                    break;
                case 1:
                    strategyResult = executeStrategyByOrderDetail(orderEntity, otype, task, tragetOrder, matchType, tragetDetail, productDataService);
                    break;
            }
            if (strategyResult != null){
                return orderEntity;
            }
            if (CollectionUtils.isNotEmpty(tragetDetail)){
                tragetOrder.setOrderDetails(tragetDetail);
                Map<String,BigDecimal> bigData = new HashMap<>();
                for (EshopSaleOrderDetail orderDetail : orderEntity.getOrderDetails()) {
                    bigData.put(orderDetail.getTradeOrderDetailId(),orderDetail.getQty());
                }
                EShopOrderMarkUtil.doBuildAndAddToMarkList(tragetOrder.getOrderMarks(), tragetOrder.getId(), BigInteger.ZERO,
                        tragetOrder.getProfileId(),
                        BaseOrderMarkEnum.FULL_SEND, JsonUtils.toJson(bigData),
                        BaseOrderMarkEnum.FULL_SEND.getBubble());
            }
            return tragetOrder;
        }catch (Exception e){
            CommonUtil.doLogByEnable(logger,LogLevelEnum.ERROR,String.format("账套%s,网店%s,订单号%s,匹配新版商品解析策略出错",orderEntity.getProfileId(), otype.getId(), orderEntity.getTradeOrderId()),e);
            return orderEntity;
        }
    }

    @Nullable
    private EshopSaleOrderEntity executeStrategyByOrder(EshopSaleOrderEntity orderEntity, Otype otype, EshopSaleOrderDownloadTask task,
                                                        EshopSaleOrderEntity tragetOrder, EshopOrderStrategyMatchType matchType, EshopOrderStrategyPtypeDetailDto strategy,
                                                        boolean useKeyWord, EshopProductDataService productDataService,
                                                        List<EshopSaleOrderDetail> tragetDetail) throws Exception {
        Map<String,BigDecimal> orderStrMap = checkAvailableOrderStr(tragetOrder, matchType);
        if (CollectionUtils.isEmpty(orderStrMap)){
            return orderEntity;
        }
        BigDecimal allQty = orderStrMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        int index = 0;
        for (Map.Entry<String, BigDecimal> entry : orderStrMap.entrySet()) {
            String orderKeyWord = entry.getKey();
            BigDecimal orderQty = entry.getValue();
            StrategyPtypeDto matchPtype = matchKeyWord(strategy, orderKeyWord);
            if (null == matchPtype && useKeyWord){
                EShopOrderMarkUtil.doBuildAndAddToMarkList(orderEntity.getOrderMarks(), orderEntity.getId(), BigInteger.ZERO,
                        orderEntity.getProfileId(), BaseOrderMarkEnum.GOODS_PARSING_FAIL, "",
                        String.format("按%s未匹配",matchType.getName()));
                return orderEntity;
            }
            EshopSaleOrderDetail originDetail = new EshopSaleOrderDetail();
            if (index > orderEntity.getOrderDetails().size()-1){
                originDetail = caclOrderDetailAllTotal(orderEntity,orderEntity.getOrderDetails().get(0));
                originDetail.setId(UId.newId());
                originDetail.getExtend().setId(UId.newId());
                originDetail.getExtend().setDetailId(originDetail.getId());
                originDetail.getDistribution().setEshopOrderDetailId(originDetail.getId());
                originDetail.getPurchase().setDetailId(originDetail.getId());
                originDetail.getTiming().setId(UId.newId());
                originDetail.getTiming().setEshopOrderDetailId(originDetail.getId());
            }else {
                originDetail = caclOrderDetailAllTotal(orderEntity,orderEntity.getOrderDetails().get(index));
            }
            EshopSaleOrderDetail temp = caclOrderDetailTempOrder(originDetail, orderQty, allQty);
            EshopProductSkuMapping skuMapping = new EshopProductSkuMapping();
            if (useKeyWord){
                skuMapping.setPtypeId(matchPtype.getPtypeId());
                skuMapping.setSkuId(matchPtype.getSkuId());
                skuMapping.setUnitId(matchPtype.getUnitId());
                skuMapping.setBind(true);
            }else {
                skuMapping = getEshopProductSkuMappingByXcode(orderKeyWord, productDataService);
                if (null == skuMapping || !skuMapping.isBind()){
                    EShopOrderMarkUtil.doBuildAndAddToMarkList(orderEntity.getOrderMarks(), orderEntity.getId(), BigInteger.ZERO,
                            orderEntity.getProfileId(), BaseOrderMarkEnum.GOODS_PARSING_FAIL, "",
                            String.format("按%s未匹配",matchType.getName()));
                    return orderEntity;
                }
                skuMapping.setSaleProxyLabel(false);
            }
            if ((null != matchPtype && matchPtype.isComboRow()) ||
                    (null != skuMapping && skuMapping.getPcategory() == Pcategory.Combo)){
                doBuildComboAndDetailByStrategy(otype, task, skuMapping, tragetOrder, temp, tragetDetail);
            }else {
                doBuildDetailByStrategy(otype, task, temp, skuMapping, tragetOrder, tragetDetail);
            }
            index++;
        }
        return null;
    }

    @Nullable
    private EshopSaleOrderEntity executeStrategyByOrderDetail(EshopSaleOrderEntity orderEntity, Otype otype, EshopSaleOrderDownloadTask task,
                                                              EshopSaleOrderEntity tragetOrder, EshopOrderStrategyMatchType matchType, List<EshopSaleOrderDetail> tragetDetail,
                                                              EshopProductDataService productDataService) throws Exception {
        for (EshopSaleOrderDetail orderDetail : tragetOrder.getOrderDetails()) {
            Map<String,BigDecimal> detailStrMap = checkAvailableOrderStr(orderDetail, matchType);
            if (CollectionUtils.isEmpty(detailStrMap)){
                tragetDetail.add(orderDetail);
                continue;
            }
            BigDecimal allQty = detailStrMap.values().stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            for (Map.Entry<String, BigDecimal> entry : detailStrMap.entrySet()) {
                String orderKeyWord = entry.getKey();
                BigDecimal orderQty = entry.getValue();
                EshopSaleOrderDetail temp = caclOrderDetailTempOrder(orderDetail, orderQty, allQty);
                EshopProductSkuMapping skuMapping = getEshopProductSkuMappingByXcode(orderKeyWord, productDataService);
                if (null == skuMapping || !skuMapping.isBind()){
                    EShopOrderMarkUtil.doBuildAndAddToMarkList(orderEntity.getOrderMarks(), orderEntity.getId(), BigInteger.ZERO,
                            orderEntity.getProfileId(), BaseOrderMarkEnum.GOODS_PARSING_FAIL, "",
                            String.format("按%s未匹配",matchType.getName()));
                    return orderEntity;
                }
                skuMapping.setSaleProxyLabel(false);
                if (skuMapping.getPcategory() == Pcategory.Combo){
                    doBuildComboAndDetailByStrategy(otype, task, skuMapping, tragetOrder, temp, tragetDetail);
                }else {
                    doBuildDetailByStrategy(otype, task, temp, skuMapping, tragetOrder, tragetDetail);
                }
            }
        }
        return null;
    }

    private EshopProductSkuMapping getEshopProductSkuMappingByXcode(String orderKeyWord, EshopProductDataService productDataService) {
        QuerySkuMappingRequest request = new QuerySkuMappingRequest();
        request.setXcode(orderKeyWord);
        request.setProfileId(CurrentUser.getProfileId());
        return productDataService.getSingleSkuMappingOpenXcodeForOrder(request);
    }

    private void doBuildDetailByStrategy(Otype otype, EshopSaleOrderDownloadTask task, EshopSaleOrderDetail temp, EshopProductSkuMapping skuMapping, EshopSaleOrderEntity tragetOrder, List<EshopSaleOrderDetail> tragetDetail) {
        relationService.buildOrderRelationBySku(temp, skuMapping, tragetOrder, task.getDownloadType().getOperateType(), otype);
        tragetDetail.add(temp);
    }

    private void doBuildComboAndDetailByStrategy(Otype otype, EshopSaleOrderDownloadTask task, EshopProductSkuMapping skuMapping, EshopSaleOrderEntity tragetOrder, EshopSaleOrderDetail temp, List<EshopSaleOrderDetail> tragetDetail) {
        skuMapping.setPcategory(Pcategory.Combo);
        EshopSaleDetailCombo eshopSaleDetailCombo = relationService.buildComboRow(tragetOrder, temp, skuMapping);
        List<EshopSaleOrderDetail> comboDetails = relationService.doBuildComboDetails(temp, skuMapping, otype);
        relationService.doLocalDistributionPriceCalc(eshopSaleDetailCombo, comboDetails, tragetOrder, task.getDownloadType().getOperateType(), skuMapping.getSaleType());
        BigDecimal comboTaxTotal = comboDetails.stream().filter(d -> d.getComboRowId().compareTo(eshopSaleDetailCombo.getEshopOrderDetailComboRowId()) == 0).
                map(EshopSaleOrderDetail::getTaxTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
        eshopSaleDetailCombo.setTaxTotal(comboTaxTotal);
        eshopSaleDetailCombo.setDisedTotal(MoneyUtils.subtract(eshopSaleDetailCombo.getDisedTaxedTotal(), eshopSaleDetailCombo.getTaxTotal(), Money.Total));
        eshopSaleDetailCombo.setDisedPrice(MoneyUtils.divide(eshopSaleDetailCombo.getDisedTotal(), eshopSaleDetailCombo.getQty(), Money.Price));
        tragetDetail.addAll(comboDetails);
    }

    @NotNull
    private EshopSaleOrderDetail caclOrderDetailTempOrder(EshopSaleOrderDetail originDetail, BigDecimal orderQty, BigDecimal allQty) {
        EshopSaleOrderDetail temp = CommonUtil.deepCopy(originDetail, EshopSaleOrderDetail.class);
        temp.setQty(orderQty);
        temp.setUnitQty(orderQty);
        //金额
        temp.setPtypePreferentialTotal(MoneyUtils.divide(temp.getPtypePreferentialTotal().multiply(orderQty), allQty,Money.Total));
        temp.setOrderPreferentialAllotTotal(MoneyUtils.divide(temp.getOrderPreferentialAllotTotal().multiply(orderQty), allQty,Money.Total));
        temp.setPreferentialTotal(MoneyUtils.divide(temp.getPreferentialTotal().multiply(orderQty), allQty,Money.Total));
        temp.setPlatformPtypePreferentialTotal(MoneyUtils.divide(temp.getPlatformPtypePreferentialTotal().multiply(orderQty), allQty,Money.Total));
        temp.setPlatformOrderPreferentialTotal(MoneyUtils.divide(temp.getPlatformOrderPreferentialTotal().multiply(orderQty), allQty,Money.Total));
        temp.setTradeTotal(MoneyUtils.divide(temp.getTradeTotal().multiply(orderQty), allQty,Money.Total));
        temp.setTotal(MoneyUtils.divide(temp.getTotal().multiply(orderQty), allQty,Money.Total));
        temp.setDisedInitialTotal(MoneyUtils.divide(temp.getDisedInitialTotal().multiply(orderQty), allQty,Money.Total));
        temp.setDisedTaxedTotal(MoneyUtils.divide(temp.getDisedTaxedTotal().multiply(orderQty), allQty,Money.Total));
        temp.setTaxTotal(MoneyUtils.divide(temp.getTaxTotal().multiply(orderQty), allQty,Money.Total));
        temp.setDisedTotal(MoneyUtils.divide(temp.getDisedTotal().multiply(orderQty), allQty,Money.Total));
        temp.setPtypeServiceFee(MoneyUtils.divide(temp.getPtypeServiceFee().multiply(orderQty), allQty,Money.Total));
        temp.setPtypeCommissionTotal(MoneyUtils.divide(temp.getPtypeCommissionTotal().multiply(orderQty), allQty,Money.Total));

        temp.getDistribution().setBuyerTotal(MoneyUtils.divide(temp.getDistribution().getBuyerTotal().multiply(orderQty), allQty,Money.Total));
        temp.getDistribution().setBuyerDisedInitialTotal(MoneyUtils.divide(temp.getDistribution().getBuyerDisedInitialTotal().multiply(orderQty), allQty,Money.Total));
        temp.getDistribution().setBuyerPtypePreferentialTotal(MoneyUtils.divide(temp.getDistribution().getBuyerPtypePreferentialTotal().multiply(orderQty), allQty,Money.Total));
        temp.getDistribution().setBuyerOrderPreferentialAllotTotal(MoneyUtils.divide(temp.getDistribution().getBuyerOrderPreferentialAllotTotal().multiply(orderQty), allQty,Money.Total));
        temp.getDistribution().setBuyerDisedTaxedTotal(MoneyUtils.divide(temp.getDistribution().getBuyerDisedTaxedTotal().multiply(orderQty), allQty,Money.Total));
        temp.getDistribution().setBuyerTaxTotal(MoneyUtils.divide(temp.getDistribution().getBuyerTaxTotal().multiply(orderQty), allQty,Money.Total));
        temp.getDistribution().setBuyerDisedTotal(MoneyUtils.divide(temp.getDistribution().getBuyerDisedTotal().multiply(orderQty), allQty,Money.Total));

        temp.getExtend().setAnchorPtypePreferentialTotal(MoneyUtils.divide(temp.getExtend().getAnchorPtypePreferentialTotal().multiply(orderQty), allQty,Money.Total));
        temp.getExtend().setAnchorOrderPreferentialTotal(MoneyUtils.divide(temp.getExtend().getAnchorOrderPreferentialTotal().multiply(orderQty), allQty,Money.Total));
        temp.getExtend().setPlatformPtypeSubsidyTotal(MoneyUtils.divide(temp.getExtend().getPlatformPtypeSubsidyTotal().multiply(orderQty), allQty,Money.Total));
        temp.getExtend().setPlatformOrderSubsidyTotal(MoneyUtils.divide(temp.getExtend().getPlatformOrderSubsidyTotal().multiply(orderQty), allQty,Money.Total));
        temp.getExtend().setPlatformPtypeSubsidyTotal(MoneyUtils.divide(temp.getExtend().getPlatformPtypeSubsidyTotal().multiply(orderQty), allQty,Money.Total));
        temp.getExtend().setNationalSubsidyTotal(MoneyUtils.divide(temp.getExtend().getNationalSubsidyTotal().multiply(orderQty), allQty,Money.Total));
        temp.getExtend().setMallDeductionFee(MoneyUtils.divide(temp.getExtend().getMallDeductionFee().multiply(orderQty), allQty,Money.Total));

        //单价
        temp.setPrice(MoneyUtils.divide(temp.getTotal(),temp.getQty(), Money.Price));
        temp.setDisedInitialPrice(MoneyUtils.divide(temp.getDisedInitialTotal(),temp.getQty(), Money.Price));
        temp.setDisedTaxedPrice(MoneyUtils.divide(temp.getDisedTaxedTotal(),temp.getQty(), Money.Price));
        temp.setDisedPrice(MoneyUtils.divide(temp.getDisedPrice(),temp.getQty(), Money.Price));

        temp.getDistribution().setBuyerPrice(MoneyUtils.divide(temp.getDistribution().getBuyerTotal(),temp.getQty(), Money.Price));
        temp.getDistribution().setBuyerDisedInitialPrice(MoneyUtils.divide(temp.getDistribution().getBuyerDisedInitialTotal(),temp.getQty(), Money.Price));
        temp.getDistribution().setBuyerDisedTaxedPrice(MoneyUtils.divide(temp.getDistribution().getBuyerDisedTaxedTotal(),temp.getQty(), Money.Price));
        temp.getDistribution().setBuyerDisedPrice(MoneyUtils.divide(temp.getDistribution().getBuyerDisedTotal(),temp.getQty(), Money.Price));

        return temp;
    }

    @NotNull
    private EshopSaleOrderDetail caclOrderDetailAllTotal(EshopSaleOrderEntity orderEntity,EshopSaleOrderDetail originDetail) {
        List<EshopSaleOrderDetail> orderDetails = orderEntity.getOrderDetails();
        originDetail.setPtypePreferentialTotal(orderDetails.stream().map(EshopSaleOrderDetail::getPtypePreferentialTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        originDetail.setOrderPreferentialAllotTotal(orderDetails.stream().map(EshopSaleOrderDetail::getOrderPreferentialAllotTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        originDetail.setPreferentialTotal(orderDetails.stream().map(EshopSaleOrderDetail::getPreferentialTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        originDetail.setPlatformPtypePreferentialTotal(orderDetails.stream().map(EshopSaleOrderDetail::getPlatformPtypePreferentialTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        originDetail.setPlatformOrderPreferentialTotal(orderDetails.stream().map(EshopSaleOrderDetail::getPlatformOrderPreferentialTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        originDetail.setTradeTotal(orderDetails.stream().map(EshopSaleOrderDetail::getTradeTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        originDetail.setTotal(orderDetails.stream().map(EshopSaleOrderDetail::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        originDetail.setDisedInitialTotal(orderDetails.stream().map(EshopSaleOrderDetail::getDisedInitialTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        originDetail.setDisedTaxedTotal(orderDetails.stream().map(EshopSaleOrderDetail::getDisedTaxedTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        originDetail.setTaxTotal(orderDetails.stream().map(EshopSaleOrderDetail::getTaxTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        originDetail.setDisedTotal(orderDetails.stream().map(EshopSaleOrderDetail::getDisedTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        originDetail.setPtypeServiceFee(orderDetails.stream().map(EshopSaleOrderDetail::getPtypeServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add));
        originDetail.setPtypeCommissionTotal(orderDetails.stream().map(EshopSaleOrderDetail::getPtypeCommissionTotal).reduce(BigDecimal.ZERO, BigDecimal::add));

        originDetail.getDistribution().setBuyerTotal(orderDetails.stream().map(d->d.getDistribution()).map(EshopSaleOrderDetailDistribution::getBuyerTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        originDetail.getDistribution().setBuyerDisedInitialTotal(orderDetails.stream().map(d->d.getDistribution()).map(EshopSaleOrderDetailDistribution::getBuyerDisedInitialTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        originDetail.getDistribution().setBuyerPtypePreferentialTotal(orderDetails.stream().map(d->d.getDistribution()).map(EshopSaleOrderDetailDistribution::getBuyerPtypePreferentialTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        originDetail.getDistribution().setBuyerOrderPreferentialAllotTotal(orderDetails.stream().map(d->d.getDistribution()).map(EshopSaleOrderDetailDistribution::getBuyerOrderPreferentialAllotTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        originDetail.getDistribution().setBuyerDisedTaxedTotal(orderDetails.stream().map(d->d.getDistribution()).map(EshopSaleOrderDetailDistribution::getBuyerDisedTaxedTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        originDetail.getDistribution().setBuyerTaxTotal(orderDetails.stream().map(d->d.getDistribution()).map(EshopSaleOrderDetailDistribution::getBuyerTaxTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        originDetail.getDistribution().setBuyerDisedTotal(orderDetails.stream().map(d->d.getDistribution()).map(EshopSaleOrderDetailDistribution::getBuyerDisedTotal).reduce(BigDecimal.ZERO, BigDecimal::add));

        originDetail.getExtend().setAnchorPtypePreferentialTotal(orderDetails.stream().map(d->d.getExtend()).map(EshopSaleOrderDetailExtend::getAnchorPtypePreferentialTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        originDetail.getExtend().setAnchorOrderPreferentialTotal(orderDetails.stream().map(d->d.getExtend()).map(EshopSaleOrderDetailExtend::getAnchorOrderPreferentialTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        originDetail.getExtend().setPlatformPtypeSubsidyTotal(orderDetails.stream().map(d->d.getExtend()).map(EshopSaleOrderDetailExtend::getPlatformPtypeSubsidyTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        originDetail.getExtend().setPlatformOrderSubsidyTotal(orderDetails.stream().map(d->d.getExtend()).map(EshopSaleOrderDetailExtend::getPlatformOrderSubsidyTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        originDetail.getExtend().setPlatformPtypeSubsidyTotal(orderDetails.stream().map(d->d.getExtend()).map(EshopSaleOrderDetailExtend::getPlatformPtypeSubsidyTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        originDetail.getExtend().setNationalSubsidyTotal(orderDetails.stream().map(d->d.getExtend()).map(EshopSaleOrderDetailExtend::getNationalSubsidyTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        originDetail.getExtend().setMallDeductionFee(orderDetails.stream().map(d->d.getExtend()).map(EshopSaleOrderDetailExtend::getMallDeductionFee).reduce(BigDecimal.ZERO, BigDecimal::add));
        return originDetail;
    }

    private StrategyPtypeDto matchKeyWord(EshopOrderStrategyPtypeDetailDto strategy, String orderKeyWord) {
        List<StrategyPtypeDto> ptypeDtoList = strategy.getPtypeDtoList();
        Optional<StrategyPtypeDto> first = ptypeDtoList.stream().filter(p -> p.getKeyWord().equals(orderKeyWord)).findFirst();
        if (first.isPresent()){
            return first.get();
        }
        return null;
    }

    private Map<String,BigDecimal> checkAvailableOrderStr(Object obj, EshopOrderStrategyMatchType matchType) throws Exception {
        Method getMethod = obj.getClass().getMethod(matchType.getMethod());
        String strategyStr = getMethod.invoke(obj).toString();
        return getAvailableKeyWordMap(strategyStr);
    }

    @Nullable
    private Map<String, BigDecimal> getAvailableKeyWordMap(String strategyStr) {
        int index = strategyStr.indexOf("@");
        if (index == -1){
            return null;
        }
        String resultStr = strategyStr.substring(index + 1);
        Map<String,BigDecimal> result = new HashMap<>();
        String[] split = resultStr.split("\\+");
        if (null == split || split.length == 0){
            return null;
        }
        for (String splitStr : split) {
            if (StringUtils.isEmpty(splitStr)){
                return null;
            }
            String[] keyAndQty = splitStr.split("\\*");
            if (null == keyAndQty || keyAndQty.length != 2){
                return null;
            }
            if (StringUtils.isEmpty(keyAndQty[0]) || !StringUtils.isNumeric(keyAndQty[1])){
                return null;
            }
            if (null != result.get(keyAndQty[0])){
                return null;
            }
            result.put(keyAndQty[0],new BigDecimal(keyAndQty[1]));
        }
        return result;
    }

    private Map<String,BigDecimal> checkAvailableDetailStr(EshopSaleOrderDetail detail, EshopOrderStrategyMatchType matchType) throws Exception {
        Method getMethod = EshopSaleOrderDetail.class.getMethod(matchType.getMethod());
        String strategyStr = getMethod.invoke(detail).toString();
        return getAvailableKeyWordMap(strategyStr);
    }


    private boolean checkAvailableStrategy(Otype otype, EshopOrderStrategyPtypeDetailDto strategy) {
        EshopOrderStrategyMatchType matchType = strategy.getMatchType();
        if (null == matchType){
            return false;
        }
        if (matchType.isUseKeyWord() && CollectionUtils.isEmpty(strategy.getPtypeDtoList())){
            return false;
        }
        return true;
    }

    private boolean checkDetailPlatformGift(EshopSaleOrderDetail tempDetail, List<ProductGiftRelation> giftRelationList) {
        if (null == tempDetail || CollectionUtils.isEmpty(giftRelationList)) {
            return false;
        }
        String tradeOrderDetailId = tempDetail.getTradeOrderDetailId();
        if (StringUtils.isEmpty(tradeOrderDetailId)) {
            return false;
        }
        return giftRelationList.stream().anyMatch(g -> tradeOrderDetailId.equals(g.getGiftOid()));
    }

    private static void buildSupplierAndDistributorDetailInfo(EshopOrderDetailEntity apiDetail, EshopSaleOrderDetail tempDetail) {
        tempDetail.setCombo(apiDetail.isCombo());
        tempDetail.setSupplierPtypeId(apiDetail.getSupplierPtypeId());
        tempDetail.setSupplierSkuId(apiDetail.getSupplierSkuId());
        tempDetail.setSupplierUnitId(apiDetail.getSupplierUnitId());
        tempDetail.setDistributorPtypeId(apiDetail.getDistributorPtypeId());
        tempDetail.setDistributorSkuId(apiDetail.getDistributorSkuId());
        tempDetail.setDistributorUnitId(apiDetail.getDistributorUnitId());
    }

    private void buildDetailKtypeId(EshopSaleOrderEntity orderEntity, EshopOrderDetailEntity apiDetail, EshopSaleOrderDetail tempDetail) {
        tempDetail.setKtypeId(orderEntity.getKtypeId());
        if (StringUtils.isNotEmpty(apiDetail.getStockId())) {
            EshopPlatformStoreMapping storeMapping = platformStoreMappingMapper.getMappingKtypeIdByStoreCode(tempDetail.getOtypeId(), tempDetail.getProfileId(),
                    apiDetail.getStockId());
            if (storeMapping == null) {
                tempDetail.setKtypeId(BigInteger.ZERO);
            } else {
                tempDetail.setKtypeId(storeMapping.getKtypeId());
            }
        }
    }

    private static void buildOrderDetailTiming(EshopOrderDetailEntity apiDetail, EshopSaleOrderDetail tempDetail) {
        EshopSaleOrderDetailTiming timing = new EshopSaleOrderDetailTiming(tempDetail);
        timing.setId(UId.newId());
        timing.setPromisedSendTime(apiDetail.getPromisedSendTime());
        tempDetail.setTiming(timing);
    }

    private void buildMallFee(Otype otype, EshopSaleOrderDetail tempDetail, EshopOrderDetailEntity apiDetail) {
        if (tempDetail.getExtend().getMallDeductionFee().compareTo(BigDecimal.ZERO) > 0) {
            if (tempDetail.getExtend().getMallDeductionRate().compareTo(BigDecimal.ZERO) == 0) {
                if (null == tempDetail.getDisedTaxedTotal() || BigDecimal.ZERO.compareTo(tempDetail.getDisedTaxedTotal()) == 0) {
                    tempDetail.getExtend().setMallDeductionRate(BigDecimal.ZERO);
                } else {
                    BigDecimal rate = MoneyUtils.divide(tempDetail.getExtend().getMallDeductionFee(), tempDetail.getDisedTaxedTotal(), Money.Tax);
                    tempDetail.getExtend().setMallDeductionRate(rate);
                }
            }
            return;
        }
        if (StringUtils.isEmpty(apiDetail.getClassId())) {
            return;
        }
        List<String> cid = Collections.singletonList(apiDetail.getClassId());
        List<MallDeductionConfigEntity> configList = mallService.queryMallDeductionRateByEshopIdAndCategoryIds(otype.getId(), cid);
        if (CollectionUtils.isEmpty(configList)) {
            return;
        }
        MallDeductionConfigEntity deductionConfig = configList.get(0);
        if (deductionConfig == null) {
            return;
        }
        BigDecimal rate = deductionConfig.getMallDeductionRate();
        BigDecimal mallFee = MoneyUtils.multiply(tempDetail.getDisedTaxedTotal(), rate, Money.Total);
        tempDetail.getExtend().setMallDeductionFee(mallFee);
        tempDetail.getExtend().setMallDeductionRate(rate);
    }

    private void buildAbnormalPrice(EshopSaleOrderDetail tempDetail) {
        if (null == tempDetail) {
            return;
        }
        //特殊处理，客户问题106928，优惠金额为负数的情况
        //将优惠金额置0
        if (tempDetail.getPtypePreferentialTotal().compareTo(BigDecimal.ZERO) < 0) {
            tempDetail.setPtypePreferentialTotal(BigDecimal.ZERO);
        }
        if (tempDetail.getOrderPreferentialAllotTotal().compareTo(BigDecimal.ZERO) < 0) {
            tempDetail.setOrderPreferentialAllotTotal(BigDecimal.ZERO);
        }
    }

    private void buildOrderDetailLiveBroadCast(EshopOrderDetailEntity apiDetail, EshopSaleOrderDetail tempDetail, Date payTime, String taskId, EshopSaleOrderEntity orderEntity) {
        BigInteger profileId = CurrentUser.getProfileId();
        EshopSaleOrderDetailLiveBroadcast liveBroadcast = new EshopSaleOrderDetailLiveBroadcast();
        liveBroadcast.setEshopId(tempDetail.getOtypeId());
        liveBroadcast.setProfileId(profileId);
        liveBroadcast.setEshopOrderId(tempDetail.getEshopOrderId());
        liveBroadcast.setDetailId(tempDetail.getId());
        liveBroadcast.setPlatformAnchorName(apiDetail.getDaRen());
        liveBroadcast.setPlatformAnchorId(apiDetail.getDaRenId());
        liveBroadcast.setPlatformLiveRoomId(apiDetail.getRoomId());
        liveBroadcast.setCreateTime(new Date());
        liveBroadcast.setUpdateTime(new Date());
        //时间+直播间ID+主播ID>时间+主播ID>时间+直播间ID
        if(StringUtils.isEmpty(liveBroadcast.getPlatformLiveRoomId()) && StringUtils.isEmpty(liveBroadcast.getPlatformAnchorId())){
            tempDetail.setLiveBroadcast(liveBroadcast);
            return;
        }
        BroadcastSessionDto dto = null;
        if (StringUtils.isNotEmpty(liveBroadcast.getPlatformLiveRoomId()) && StringUtils.isNotEmpty(liveBroadcast.getPlatformAnchorId())){
            dto = broadcastSessionMapper.queryBroadcastSessionsBySaleOrder(profileId, liveBroadcast.getPlatformLiveRoomId(), liveBroadcast.getPlatformAnchorId(), payTime);
        } else if (StringUtils.isNotEmpty(liveBroadcast.getPlatformAnchorId())) {
            dto = broadcastSessionMapper.queryBroadcastSessionsBySaleOrder(profileId, null, liveBroadcast.getPlatformAnchorId(), payTime);
        }else if (StringUtils.isNotEmpty(liveBroadcast.getPlatformLiveRoomId())) {
            dto = broadcastSessionMapper.queryBroadcastSessionsBySaleOrder(profileId, liveBroadcast.getPlatformLiveRoomId(), null, payTime);
        }
        if (null == dto || CommonUtil.isInvalidBigInteger(dto.getId())){
            tempDetail.setLiveBroadcast(liveBroadcast);
            return;
        }
        liveBroadcast.setLiveBrodcastSessionId(dto.getId());
        orderEntity.setEtypeId(CommonUtil.isInvalidBigInteger(dto.getEtypeHandler()) ? BigInteger.ZERO : dto.getEtypeHandler());
        tempDetail.setLiveBroadcast(liveBroadcast);
    }

    private void buildDetailPriceForDistribution(EshopOrderDetailEntity apiDetail, EshopSaleOrderDetail detail) {
        EshopSaleOrderDetailDistribution distribution = new EshopSaleOrderDetailDistribution();
        distribution.setEshopOrderDetailId(detail.getId());
        distribution.setProfileId(detail.getProfileId());
        distribution.setEshopOrderId(detail.getEshopOrderId());
        distribution.setEshopId(detail.getOtypeId());
        distribution.setBuyerPrice(MoneyUtils.round(detail.getPrice(), Money.Price));
        distribution.setBuyerTotal(MoneyUtils.round(detail.getTotal(), Money.Total));
        distribution.setBuyerDiscount(detail.getDiscount());
        distribution.setBuyerDisedInitialPrice(MoneyUtils.round(detail.getDisedInitialPrice(), Money.Price));
        distribution.setBuyerDisedInitialTotal(MoneyUtils.round(detail.getDisedInitialTotal(), Money.Total));
        distribution.setBuyerPtypePreferentialTotal(MoneyUtils.round(detail.getPtypePreferentialTotal(), Money.Total));
        distribution.setBuyerOrderPreferentialAllotTotal(MoneyUtils.round(detail.getOrderPreferentialAllotTotal(), Money.Total));
        distribution.setBuyerDisedTaxedPrice(MoneyUtils.round(detail.getDisedTaxedPrice(), Money.Price));
        distribution.setBuyerDisedTaxedTotal(MoneyUtils.round(detail.getDisedTaxedTotal(), Money.Total));
        distribution.setBuyerTaxRate(MoneyUtils.round(detail.getTaxRate(), Money.Tax));
        distribution.setBuyerTaxTotal(MoneyUtils.round(detail.getTaxTotal(), Money.Total));
        distribution.setBuyerDisedPrice(MoneyUtils.round(detail.getDisedPrice(), Money.Price));
        distribution.setBuyerDisedTotal(MoneyUtils.round(detail.getDisedTotal(), Money.Total));
        distribution.setDistributionBuyerTradeDetailId(apiDetail.getDistributionBuyerTradeDetailId());
        detail.setDistribution(distribution);
        BigDecimal distriPrice = MoneyUtils.round(apiDetail.getDistributionBalanceTaxedPrice(), Money.Price);
        BigDecimal distriTotal = MoneyUtils.round(apiDetail.getDistributionBalanceTaxedTotal(), Money.Total);
        if (apiDetail.getDistributionCommissionTotal().compareTo(BigDecimal.ZERO) == 0) {
            detail.setPtypeCommissionTotal(MoneyUtils.subtract(detail.getDisedTaxedTotal(), distriTotal, Money.Total));
        }
        detail.setTradePrice(distriPrice);
        detail.setTradeTotal(distriTotal);
        detail.setPrice(distriPrice);
        detail.setTotal(distriTotal);
        detail.setDiscount(BigDecimal.ONE);
        detail.setDisedInitialPrice(distriPrice);
        detail.setDisedInitialTotal(distriTotal);
        detail.setPtypePreferentialTotal(BigDecimal.ZERO);
        detail.setOrderPreferentialAllotTotal(BigDecimal.ZERO);
        detail.setDisedTaxedPrice(distriPrice);
        detail.setDisedTaxedTotal(distriTotal);
        detail.setTaxTotal(BigDecimal.ZERO);
        detail.setTaxRate(BigDecimal.ZERO);
        detail.setDisedPrice(distriPrice);
        detail.setDisedTotal(distriTotal);
    }

    public void BuildOrderMainMark(EshopOrderEntity apiOrder, EshopSaleOrderEntity orderEntity, Otype otype) {
        List<EshopOrderMarkEntity> Marks = orderEntity.getOrderMarks();
        AddressResolution addressResolution = CommonUtil.doubleAddressJudgment(orderEntity.getEshopBuyer());
        if (addressResolution.isException()) {
            EshopOrderMarkEntity item = CommonUtil.buildEshopOrderMarkEntity(orderEntity.getId(), BigInteger.ZERO, orderEntity.getProfileId(), 91210003, addressResolution.getMsg());
            Marks.add(item);
        }
        if (orderEntity.getPayTimeType().equals(PayType.MONTH_SETTLEMENT) && TradeStatus.WAIT_BUYER_PAY.equals(orderEntity.getLocalTradeState())) {
            EShopOrderMarkUtil.doBuildAndAddToMarkList(Marks, orderEntity.getId(), BigInteger.ZERO, orderEntity.getProfileId(), BaseOrderMarkEnum.NO_PAY_SEND, "", "");
        }
        if (orderEntity.getPayTimeType().equals(PayType.CASH_ON_DELIVERY)) {
            EShopOrderMarkUtil.doBuildAndAddToMarkList(Marks, orderEntity.getId(), BigInteger.ZERO, orderEntity.getProfileId(), BaseOrderMarkEnum.COD, "", "");
        }
        if (orderEntity.getOrderSaleType().equals(TradeTypeEnum.ADVANCE_FORWARD_SALE) || orderEntity.getOrderSaleType().equals(TradeTypeEnum.ADVANCE_FORWARD_SALE_BY_STOCK)) {
            //明细全部都是【预售-按计划发】
            //主表标记【预售-按计划发】
            buildOrderMarkByAllDetail(orderEntity, Marks, orderEntity.getOrderDetails(), BaseOrderMarkEnum.FORWARD_SALE);
            //主表没有标记【预售-按计划发】
            //说明明细中含有【预售-有货就发】
            //一张订单存在两个预售标记时，【预售-有货就发】优先级高
            //主表标记【预售-有货就发】
            if (Marks.stream().noneMatch(m -> m.getMarkCode().compareTo(BigInteger.valueOf(BaseOrderMarkEnum.FORWARD_SALE.getCode())) == 0)) {
                EShopOrderMarkUtil.doBuildAndAddToMarkList(Marks, orderEntity.getId(), BigInteger.ZERO, orderEntity.getProfileId(), BaseOrderMarkEnum.FORWARD_SALE_BY_STOCK);
            }
        }
//        if (orderEntity.getOrderSaleType().equals(TradeTypeEnum.CYCLE_PURCHASE)) {
//            EShopOrderMarkUtil.doBuildAndAddToMarkList(Marks, orderEntity.getId(), BigInteger.ZERO, orderEntity.getProfileId(), BaseOrderMarkEnum.CYCLE_PURCHASE);
//        }
        if (orderEntity.getLocalTradeState().equals(TradeStatus.WAIT_BUYER_PAY)) {
            EShopOrderMarkUtil.doBuildAndAddToMarkList(Marks, orderEntity.getId(), BigInteger.ZERO, orderEntity.getProfileId(), BaseOrderMarkEnum.NO_PAY);
        }
        if (orderEntity.getExtend().getPaymentMode().equals(PaymentMode.COD)) {
            EShopOrderMarkUtil.doBuildAndAddToMarkList(Marks, orderEntity.getId(), BigInteger.ZERO, orderEntity.getProfileId(), BaseOrderMarkEnum.COD);
        }
        if (!orderEntity.isMappingState()) {
            EShopOrderMarkUtil.doBuildAndAddToMarkList(Marks, orderEntity.getId(), BigInteger.ZERO, orderEntity.getProfileId(), BaseOrderMarkEnum.UN_RELATION);
        }
        if (!orderEntity.isOrderDeliverRequired()) {
            EShopOrderMarkUtil.doBuildAndAddToMarkList(Marks, orderEntity.getId(), BigInteger.ZERO, orderEntity.getProfileId(), BaseOrderMarkEnum.CANT_SEND_GOODS);
        }
        List<EshopSaleOrderDetail> orderDetails = orderEntity.getOrderDetails();
        //明细是否全是服务商品
        boolean isVirtual = orderDetails.stream().allMatch(d -> d.getPcategory() == 1);
        if (isVirtual) {
            EShopOrderMarkUtil.doBuildAndAddToMarkList(Marks, orderEntity.getId(), BigInteger.ZERO, orderEntity.getProfileId(), BaseOrderMarkEnum.VIRTUAL);
            logger.error("下载订单，从接口返回的订单中存在错误明细，构建出服务商品，order:{}", JsonUtils.toJson(orderEntity));
        }

        //仓库id=0
        if (BigInteger.ZERO.compareTo(orderEntity.getKtypeId()) == 0) {
            String platformName = null == otype ? "" : otype.getEshopInfo().getEshopType().getPlatformName();
            EShopOrderMarkUtil.doBuildAndAddToMarkList(Marks, orderEntity.getId(), BigInteger.ZERO, orderEntity.getProfileId(),
                    BaseOrderMarkEnum.KTYPE_UN_RELATION, "", String.format("%s%s%s%s%s", platformName, apiOrder.getStoreType().getDesc(), "与系统发货仓库", BaseOrderMarkEnum.KTYPE_UN_RELATION.getBubble(), apiOrder.getStoreType().getDesc()));
        }

        if (BillBusinessType.SaleDistribution.equals(orderEntity.getBusinessType())) {
            if (OrderCreateType.FROM_DOWNLOAD_ORDER.equals(orderEntity.getCreateType())) {
                EShopOrderMarkUtil.doBuildAndAddToMarkList(Marks, orderEntity.getId(), BigInteger.ZERO, orderEntity.getProfileId(), BaseOrderMarkEnum.PLATFORM_DESIGNATED);
            } else {
                EShopOrderMarkUtil.doBuildAndAddToMarkList(Marks, orderEntity.getId(), BigInteger.ZERO, orderEntity.getProfileId(), BaseOrderMarkEnum.LOCAL_DESIGNATED);
            }
        }

        buildOrderMarkByAllDetail(orderEntity, Marks, orderDetails, BaseOrderMarkEnum.GOODS_PARSING_FAIL);
        buildOrderMarkByAllDetail(orderEntity, Marks, orderDetails, BaseOrderMarkEnum.CANT_SEND_GOODS);
        buildOrderMarkByAllDetail(orderEntity, Marks, orderDetails, BaseOrderMarkEnum.NOTSEND_KEEPACCOUNT);
        buildOrderMarkByAllDetail(orderEntity, Marks, orderDetails, BaseOrderMarkEnum.NO_PAY);
        buildOrderMarkByAllDetail(orderEntity, Marks, orderDetails, BaseOrderMarkEnum.DD_ZHIBO);

        buildOrderMarkByAnyDetail(orderEntity, Marks, orderDetails, BaseOrderMarkEnum.SN);
        buildOrderMarkByAnyDetail(orderEntity, Marks, orderDetails, BaseOrderMarkEnum.IMEI);
        buildOrderMarkByAnyDetail(orderEntity, Marks, orderDetails, BaseOrderMarkEnum.TB_ICCID);
        buildOrderMarkByAnyDetail(orderEntity, Marks, orderDetails, BaseOrderMarkEnum.UNSEAL_CODE);
        buildOrderMarkByAnyDetail(orderEntity, Marks, orderDetails, BaseOrderMarkEnum.ORGANIC_CODE);
        buildOrderMarkByAnyDetail(orderEntity, Marks, orderDetails, BaseOrderMarkEnum.BARCODE);

        if (apiOrder == null || ((apiOrder.getOrderMarks() == null || apiOrder.getOrderMarks().size() == 0) && (apiOrder.getMarkDataList() == null || apiOrder.getMarkDataList().size() == 0))) {
            return;
        }
        for (BaseOrderMarkEnum orderMark : apiOrder.getOrderMarks()) {
            EShopOrderMarkUtil.doBuildAndAddToMarkList(Marks, orderEntity.getId(), BigInteger.ZERO, orderEntity.getProfileId(), orderMark);
        }
        if (CollectionUtils.isNotEmpty(apiOrder.getMarkDataList())) {
            for (MarkData markData : apiOrder.getMarkDataList()) {
                EshopOrderMarkEntity orderMark = Marks.stream().filter(x -> x.getMarkCode().compareTo(BigInteger.valueOf(markData.getOrderMarkEnum().getCode())) == 0).findFirst().orElse(null);
                //兼容标记和标记data，重复的更新bubble，不同的插入数据
                if (null != orderMark) {
                    orderMark.setBubble(StringUtils.isEmpty(markData.getBubble()) ? markData.getOrderMarkEnum().getBubble() : markData.getBubble());
                    orderMark.setBigData(markData.getBigDataJson());
                    orderMark.setMarkDataId(UId.newId());
                } else {
                    EShopOrderMarkUtil.doBuildAndAddToMarkList(Marks, orderEntity.getId(), BigInteger.ZERO, orderEntity.getProfileId(), markData.getOrderMarkEnum(), markData.getBigDataJson(), StringUtils.isEmpty(markData.getBubble()) ? markData.getOrderMarkEnum().getBubble() : markData.getBubble());
                }
            }
        }
    }

    /**
     * 如果任一明细都存在标记，则主表也会标记
     *
     * @param orderEntity  订单实体
     * @param Marks        标志着
     * @param orderDetails 订单细节
     */
    public void buildOrderMarkByAnyDetail(EshopSaleOrderEntity orderEntity, List<EshopOrderMarkEntity> Marks, List<EshopSaleOrderDetail> orderDetails, BaseOrderMarkEnum mark) {
        //任一明细有标记，主表就会打上标记
        for (EshopSaleOrderDetail detail : orderDetails) {
            boolean match = detail.getOrderDetailMarks().stream().anyMatch(m -> m.getMarkCode().compareTo(BigInteger.valueOf(mark.getCode())) == 0);
            if (match) {
                EShopOrderMarkUtil.doBuildAndAddToMarkList(Marks, orderEntity.getId(), BigInteger.ZERO, orderEntity.getProfileId(), mark);
                return;
            }
        }
    }

    /**
     * 如果全部明细都存在标记，则主表也会标记
     *
     * @param orderEntity  订单实体
     * @param Marks        标志着
     * @param orderDetails 订单细节
     */
    public void buildOrderMarkByAllDetail(EshopSaleOrderEntity orderEntity, List<EshopOrderMarkEntity> Marks, List<EshopSaleOrderDetail> orderDetails, BaseOrderMarkEnum mark) {
        //全部明细有标记，主表就会打上标记
        AtomicBoolean allDetail = new AtomicBoolean(true);
        orderDetails.forEach(o -> {
            //明细是否全部匹配
            boolean noAllDetail = o.getOrderDetailMarks().stream().noneMatch(m -> m.getMarkCode().compareTo(BigInteger.valueOf(mark.getCode())) == 0);
            if (noAllDetail) {
                allDetail.set(false);
            }
        });
        if (allDetail.get()) {
            EShopOrderMarkUtil.doBuildAndAddToMarkList(Marks, orderEntity.getId(), BigInteger.ZERO, orderEntity.getProfileId(), mark);
        }
    }

    public void BuildOrderDetailMark(EshopOrderDetailEntity apiDetail, EshopSaleOrderDetail detail, String platformJson) {
        List<EshopOrderMarkEntity> detailMarks = new ArrayList<>();
        detail.setOrderDetailMarks(detailMarks);
        if (!detail.isDeliverRequired()) {
            EShopOrderMarkUtil.doBuildAndAddToMarkList(detailMarks, detail.getEshopOrderId(), detail.getId(), detail.getProfileId(), BaseOrderMarkEnum.CANT_SEND_GOODS, platformJson);
        }
        if (detail.isNoShippingBookkeeping()) {
            EShopOrderMarkUtil.doBuildAndAddToMarkList(detailMarks, detail.getEshopOrderId(), detail.getId(), detail.getProfileId(), BaseOrderMarkEnum.NOTSEND_KEEPACCOUNT, platformJson);
        }
        if (detail.getPcategory() == 1) {
            EShopOrderMarkUtil.doBuildAndAddToMarkList(detailMarks, detail.getEshopOrderId(), detail.getId(), detail.getProfileId(), BaseOrderMarkEnum.VIRTUAL, platformJson);
            logger.error("下载订单，从接口返回的订单中存在错误明细，构建出服务商品，order:{}", JsonUtils.toJson(detail));
        }
        if (BigInteger.ZERO.compareTo(detail.getKtypeId()) == 0) {
            EShopOrderMarkUtil.doBuildAndAddToMarkList(detailMarks, detail.getEshopOrderId(), detail.getId(), detail.getProfileId(),
                    BaseOrderMarkEnum.KTYPE_UN_RELATION, "");
        }
        if (apiDetail == null || (CollectionUtils.isEmpty(apiDetail.getDetailMarks()) && CollectionUtils.isEmpty(apiDetail.getMarkDataList()))) {
            return;
        }
        for (BaseOrderMarkEnum detailMark : apiDetail.getDetailMarks()) {
            EShopOrderMarkUtil.doBuildAndAddToMarkList(detailMarks, detail.getEshopOrderId(), detail.getId(), detail.getProfileId(), detailMark, platformJson);
        }

        for (MarkData markData : apiDetail.getMarkDataList()) {
            EshopOrderMarkEntity orderMark = detailMarks.stream().filter(x -> x.getMarkCode().compareTo(BigInteger.valueOf(markData.getOrderMarkEnum().getCode())) == 0).findFirst().orElse(null);
            //兼容标记和标记data，重复的更新bubble，不同的插入数据
            if (null != orderMark) {
                orderMark.setBubble(StringUtils.isEmpty(markData.getBubble()) ? markData.getOrderMarkEnum().getBubble() : markData.getBubble());
                orderMark.setBigData(markData.getBigDataJson());
                orderMark.setMarkDataId(UId.newId());
            } else {
                EShopOrderMarkUtil.doBuildAndAddToMarkList(detailMarks, detail.getEshopOrderId(), detail.getId(), detail.getProfileId(), markData.getOrderMarkEnum(), markData.getBigDataJson(), StringUtils.isEmpty(markData.getBubble()) ? markData.getOrderMarkEnum().getBubble() : markData.getBubble());
            }

        }
    }

    private void buildDetailReturnState(EshopOrderDetailEntity apiDetail, EshopSaleOrderDetail tempDetail) {
        boolean noReturn = apiDetail.getRefundStatus().equals(RefundStatus.NONE) ||
                apiDetail.getRefundStatus().equals(RefundStatus.CANCEL) ||
                apiDetail.getRefundStatus().equals(RefundStatus.SELLER_REFUSE);
        boolean success = apiDetail.getRefundStatus().equals(RefundStatus.SUCCESS);
        if (noReturn) {
            tempDetail.setLocalRefundState(ReturnState.NONE);
        } else if (success) {
            tempDetail.setLocalRefundState(ReturnState.SUCCESS);
        } else {
            tempDetail.setLocalRefundState(ReturnState.REFUNDING);
        }
        //售后状态不为空，则优先使用
        if (null != apiDetail.getRefundState()){
            tempDetail.setLocalRefundState(apiDetail.getRefundState());
        }
    }

    private void buildDetailPrice(EshopOrderDetailEntity apiDetail, EshopSaleOrderDetail detail) {
        calculateApiDetailPrice(apiDetail);
        detail.setTradePrice(MoneyUtils.round(apiDetail.getPrice(), Money.Price));
        detail.setTradeTotal(MoneyUtils.round(apiDetail.getTotal(), Money.Total));
        detail.setPrice(MoneyUtils.round(apiDetail.getPrice(), Money.Price));
        detail.setTotal(MoneyUtils.round(apiDetail.getTotal(), Money.Total));
        detail.setUnitQty(MoneyUtils.round(apiDetail.getQty(), Money.Qty));
        detail.setQty(MoneyUtils.round(apiDetail.getQty(), Money.Qty));
//        detail.setSubQty(MoneyUtils.round(apiDetail.getQty(), Money.Qty));
        detail.setDiscount(BigDecimal.ONE);
        detail.setDisedInitialPrice(MoneyUtils.round(detail.getPrice(), Money.Price));
        detail.setDisedInitialTotal(MoneyUtils.round(detail.getTotal(), Money.Total));
        BigDecimal preferentialTotal = MoneyUtils.add(detail.getPtypePreferentialTotal(), detail.getOrderPreferentialAllotTotal(), Money.Total);
        detail.setDisedTaxedTotal(MoneyUtils.subtract(MoneyUtils.round(apiDetail.getTotal(), Money.Total), preferentialTotal, Money.Total));
        if (detail.getUnitQty().compareTo(BigDecimal.ZERO) > 0) {
            detail.setDisedTaxedPrice(MoneyUtils.divide(detail.getDisedTaxedTotal(), detail.getUnitQty(), Money.Price));
        }
        calcaulateDetailTax(detail, apiDetail.getTaxFee(), apiDetail.getTaxRate(), GlobalConfig.get(SysGlobalConfig.class));
        detail.setPtypeServiceFee(MoneyUtils.multiply(apiDetail.getServiceFee(), BigDecimal.ONE, Money.Total));
        detail.setPtypeCommissionTotal(MoneyUtils.multiply(apiDetail.getDistributionCommissionTotal(), BigDecimal.ONE, Money.Total));
    }

    /**
     * calcaulate计算明细税相关
     *
     * @param detail
     * @param taxFee
     * @param taxRate
     * @param config
     */
    public void calcaulateDetailTax(EshopSaleOrderDetail detail, BigDecimal taxFee, BigDecimal taxRate, SysGlobalConfig config) {
        //未开启税率
        //税额和税率都为0
        if (!config.isEnabledTax() ||
                (null == taxFee && null == taxRate) ||
                (null == taxFee && taxRate.compareTo(BigDecimal.ZERO) == 0) ||
                (null == taxRate && taxFee.compareTo(BigDecimal.ZERO) == 0)) {
            detail.setTaxTotal(BigDecimal.ZERO);
            detail.setTaxRate(BigDecimal.ZERO);
            detail.setDisedTotal(detail.getDisedTaxedTotal());
            detail.setDisedPrice(MoneyUtils.divide(detail.getDisedTotal(), detail.getUnitQty(), Money.Price));
            return;
        }
        //税额为0
        //通过税率，计算税额
        //      disedPrice = disedTaxedPrice/(1+taxRate)
        //      taxTotal = disedTaxedTotal - (disedPrice * qty)
        if (null == taxFee || taxFee.compareTo(BigDecimal.ZERO) == 0) {
            BigDecimal disedPrice = MoneyUtils.divide(detail.getDisedTaxedPrice(), taxRate.divide(BigDecimal.valueOf(100)).add(BigDecimal.ONE), Money.Price);
            BigDecimal taxTotal = MoneyUtils.subtract(detail.getDisedTaxedTotal(), disedPrice.multiply(detail.getUnitQty()), Money.Tax);
            if (taxTotal.compareTo(BigDecimal.ZERO) < 0) {
                taxTotal = BigDecimal.ZERO;
            }
            detail.setTaxTotal(taxTotal);
            detail.setTaxRate(taxRate);
            detail.setDisedTotal(MoneyUtils.subtract(detail.getDisedTaxedTotal(), detail.getTaxTotal(), Money.Tax));
            detail.setDisedPrice(disedPrice);
            return;
        }
        //税率为0
        //通过税额，计算税率
        if (null == taxRate || taxRate.compareTo(BigDecimal.ZERO) == 0) {
            BigDecimal val = MoneyUtils.subtract(detail.getDisedTaxedTotal(), taxFee, Money.Tax);
            if (val.compareTo(BigDecimal.ZERO) == 0) {
                detail.setDisedTotal(val);
                detail.setDisedPrice(MoneyUtils.divide(val, detail.getUnitQty(), Money.Price));
                return;
            }
            detail.setTaxRate(MoneyUtils.divide(taxFee.multiply(BigDecimal.valueOf(100)), val, Money.Tax));
            detail.setTaxTotal(taxFee);
            detail.setDisedTotal(val);
            detail.setDisedPrice(MoneyUtils.divide(val, detail.getUnitQty(), Money.Price));
        }
    }

    /// 明细优惠金额赋值
    private void buildDetailPreferentialTotal(EshopOrderEntity apiOrder, EshopSaleOrderDetail detail) {
        List<EshopPreferentialDetail> preferentialDetails = apiOrder.getPreferentialDetails();
        List<EshopPreferentialDetail> SellerPreferentialDetails = preferentialDetails.stream()
                .filter(x -> x.getType().equals(PreferentialType.SELLER) && x.getOid().equals(detail.getTradeOrderDetailId())).collect(Collectors.toList());
        if (!SellerPreferentialDetails.isEmpty()) {
            //商家单品优惠
            BigDecimal sellerPtypePreferentialTotal = SellerPreferentialDetails.stream().map(EshopPreferentialDetail::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
            //商家整单优惠分摊
            BigDecimal preferentialAllotTotal = SellerPreferentialDetails.stream().map(EshopPreferentialDetail::getTotalShare).reduce(BigDecimal.ZERO, BigDecimal::add);
            detail.setPtypePreferentialTotal(sellerPtypePreferentialTotal);
            detail.setOrderPreferentialAllotTotal(preferentialAllotTotal);
            detail.setPreferentialTotal(MoneyUtils.add(sellerPtypePreferentialTotal, preferentialAllotTotal, Money.Total));
        }

        //平台优惠
        List<EshopPreferentialDetail> platformPreferentialDetails = preferentialDetails.stream()
                .filter(x -> x.getType().equals(PreferentialType.PLATFORM) && x.getOid().equals(detail.getTradeOrderDetailId())).collect(Collectors.toList());
        if (!platformPreferentialDetails.isEmpty()) {
            //平台单品优惠
            BigDecimal platformDiscount = platformPreferentialDetails.stream().map(EshopPreferentialDetail::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
            //平台整单优惠分摊
            BigDecimal platformDiscountShare = platformPreferentialDetails.stream().map(EshopPreferentialDetail::getTotalShare).reduce(BigDecimal.ZERO, BigDecimal::add);
            detail.setPlatformPtypePreferentialTotal(platformDiscount);
            detail.setPlatformOrderPreferentialTotal(platformDiscountShare);
        }

        //主播优惠
        List<EshopPreferentialDetail> anchorPreferentialDetails = preferentialDetails.stream()
                .filter(x -> x.getType().equals(PreferentialType.ANCHOR) && x.getOid().equals(detail.getTradeOrderDetailId())).collect(Collectors.toList());
        if (!anchorPreferentialDetails.isEmpty()) {
            //主播单品优惠
            BigDecimal anchorDiscount = anchorPreferentialDetails.stream().map(EshopPreferentialDetail::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
            //主播整单优惠分摊
            BigDecimal anchorDiscountShare = anchorPreferentialDetails.stream().map(EshopPreferentialDetail::getTotalShare).reduce(BigDecimal.ZERO, BigDecimal::add);
            detail.getExtend().setAnchorPtypePreferentialTotal(anchorDiscount);
            detail.getExtend().setAnchorOrderPreferentialTotal(anchorDiscountShare);
        }

        //平台补贴
        List<EshopPreferentialDetail> platformOrderSubsidyDetails = preferentialDetails.stream()
                .filter(x -> x.getType().equals(PreferentialType.PLATFORM_SUBSIDIES) && x.getOid().equals(detail.getTradeOrderDetailId())).collect(Collectors.toList());
        if (!platformOrderSubsidyDetails.isEmpty()) {
            //平台补贴单品优惠
            BigDecimal platformPtypeSubsidyDiscount = platformOrderSubsidyDetails.stream().map(EshopPreferentialDetail::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
            //平台补贴整单优惠分摊
            BigDecimal platformOrderSubsidyDiscountShare = platformOrderSubsidyDetails.stream().map(EshopPreferentialDetail::getTotalShare).reduce(BigDecimal.ZERO, BigDecimal::add);
            detail.getExtend().setPlatformPtypeSubsidyTotal(platformPtypeSubsidyDiscount);
            detail.getExtend().setPlatformOrderSubsidyTotal(platformOrderSubsidyDiscountShare);
        }
    }

    private void calculateApiDetailPrice(EshopOrderDetailEntity apiDetail) {
        if (apiDetail.getPrice().compareTo(BigDecimal.ZERO) == 0
                && apiDetail.getTotal().compareTo(BigDecimal.ZERO) > 0) {
            apiDetail.setPrice(MoneyUtils.divide(apiDetail.getTotal(), apiDetail.getQty(), Money.Price));
        }
        if (apiDetail.getTotal().compareTo(BigDecimal.ZERO) == 0
                && apiDetail.getPrice().compareTo(BigDecimal.ZERO) > 0) {
            apiDetail.setTotal(MoneyUtils.multiply(apiDetail.getPrice(), apiDetail.getQty(), Money.Total));
        }
    }

    private void buildOrderTotal(EshopOrderEntity apiOrder, @NotNull EshopSaleOrderEntity orderEntity) {
        List<EshopPreferentialDetail> preferentialDetails = apiOrder.getPreferentialDetails();
        List<EshopSaleOrderDetail> details = orderEntity.getOrderDetails();
        //商家单品优惠
        BigDecimal detailPreferential = BigDecimal.ZERO;
        //平台单品优惠
        BigDecimal platformPreferentialTotal = BigDecimal.ZERO;
        if (preferentialDetails != null && !preferentialDetails.isEmpty()) {
            detailPreferential = preferentialDetails.stream().filter(x -> x.getType().equals(PreferentialType.SELLER))
                    .map(EshopPreferentialDetail::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
            platformPreferentialTotal = preferentialDetails.stream().filter(x -> x.getType().equals(PreferentialType.PLATFORM))
                    .map(EshopPreferentialDetail::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        BigDecimal tradeTotal = details.stream().map(EshopSaleOrderDetail::getTradeTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
        orderEntity.setTradeTotal(MoneyUtils.round(tradeTotal, Money.Total));
        //平台金额
        orderEntity.setTotal(MoneyUtils.round(apiOrder.getTotal(), Money.Total));
        //商家整单优惠
        orderEntity.setOrderPreferentialAllotTotal(MoneyUtils.round(apiOrder.getPreferenceTotal(), Money.Total));
        //商家优惠总金额
        BigDecimal ptypePreferentialTotal = details.stream().map(EshopSaleOrderDetail::getPtypePreferentialTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
        orderEntity.setPtypePreferentialTotal(MoneyUtils.round(ptypePreferentialTotal, Money.Total));
        //折后含税金额
        BigDecimal disedTaxedTotal = details.stream().map(EshopSaleOrderDetail::getDisedTaxedTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
        orderEntity.setDisedTaxedTotal(MoneyUtils.round(disedTaxedTotal, Money.Total));
        //税额
        orderEntity.setTaxTotal(details.stream().map(EshopSaleOrderDetail::getTaxTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        //分佣金额
        BigDecimal distributionCommissionTotal = details.stream().map(EshopSaleOrderDetail::getPtypeCommissionTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        orderEntity.setPtypeCommissionTotal(MoneyUtils.round(distributionCommissionTotal, Money.Total));
        //分销买家金额
        if (BillBusinessType.SaleDistribution.equals(orderEntity.getBusinessType())) {
            BigDecimal distriTotal = details.stream().map(EshopSaleOrderDetail::getDistribution)
                    .map(EshopSaleOrderDetailDistribution::getBuyerDisedTaxedTotal)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            orderEntity.setDistributionDisedTaxedTotal(MoneyUtils.round(distriTotal, Money.Total));
            //税额
            BigDecimal taxTotal = details.stream().map(EshopSaleOrderDetail::getDistribution)
                    .map(EshopSaleOrderDetailDistribution::getBuyerTaxTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
            orderEntity.setTaxTotal(MoneyUtils.round(taxTotal, Money.Tax));
        }

        BigDecimal serviceFee = details.stream().map(EshopSaleOrderDetail::getPtypeServiceFee).reduce(BigDecimal.ZERO, BigDecimal::add);
        orderEntity.setPtypeServiceFee(MoneyUtils.round(serviceFee, Money.Tax));
        orderEntity.setOrderBuyerFreightFee(MoneyUtils.round(apiOrder.getBuyerFreightFee(), Money.Tax));
        orderEntity.setPlatformOrderPreferentialTotal(MoneyUtils.round(apiOrder.getPlatformPreferentialTotal(), Money.Tax));
        BigDecimal platfromAllDiscount = MoneyUtils.add(platformPreferentialTotal, orderEntity.getPlatformOrderPreferentialTotal(), Money.Total);
        orderEntity.setBuyerTradeTotal(MoneyUtils.subtract(orderEntity.getCustomerPayment(), platfromAllDiscount, Money.Total));
        orderEntity.setBuyerPaidTotal(MoneyUtils.round(apiOrder.getByuerPaidTotal(), Money.Tax));
        orderEntity.setBuyerUnpaidTotal(MoneyUtils.subtract(orderEntity.getBuyerTradeTotal(), apiOrder.getByuerPaidTotal(), Money.Total));
        EshopSaleOrderExtendEntity extend = orderEntity.getExtend();
        if (null == extend) {
            orderEntity.setExtend(new EshopSaleOrderExtendEntity(orderEntity));
        }
        if (apiOrder.getMallFee().compareTo(BigDecimal.ZERO) > 0) {
            orderEntity.getExtend().setMallDeductionFee(MoneyUtils.round(apiOrder.getMallFee(), Money.Total));
        } else {
            double sum = details.stream().mapToDouble(x -> x.getExtend().getMallDeductionFee().doubleValue()).sum();
            orderEntity.getExtend().setMallDeductionFee(MoneyUtils.round(BigDecimal.valueOf(sum), Money.Total));
        }
        BigDecimal detailAnchorPtypePreferentialTotal = details.stream().map(EshopSaleOrderDetail::getExtend).map(EshopSaleOrderDetailExtend::getAnchorPtypePreferentialTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal detailPlatformPtypeSubsidyTotal = details.stream().map(EshopSaleOrderDetail::getExtend).map(EshopSaleOrderDetailExtend::getPlatformPtypeSubsidyTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
        orderEntity.getExtend().setAnchorOrderPreferentialTotal(MoneyUtils.add(apiOrder.getAnchorOrderPreferentialTotal(), detailAnchorPtypePreferentialTotal, Money.Total));
        orderEntity.getExtend().setPlatformOrderSubsidyTotal(MoneyUtils.add(apiOrder.getPlatformSubsidyPreferentialTotal(), detailPlatformPtypeSubsidyTotal, Money.Total));
    }

    private void doNewSharePreferential(@NotNull EshopOrderEntity apiOrder, PreferentialType type) {
        List<EshopOrderDetailEntity> details = apiOrder.getOrderDetails();
        BigDecimal orderTotal = apiOrder.getTotal();
        if (orderTotal.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        BigDecimal sharePreferential = BigDecimal.ZERO;
        switch (type) {
            case SELLER:
                sharePreferential = apiOrder.getPreferenceTotal();
                break;
            case PLATFORM:
                sharePreferential = apiOrder.getPlatformPreferentialTotal();
                break;
            case ANCHOR:
                sharePreferential = apiOrder.getAnchorOrderPreferentialTotal();
                break;
            case PLATFORM_SUBSIDIES:
                sharePreferential = apiOrder.getPlatformSubsidyPreferentialTotal();
                break;
            default:
                sharePreferential = BigDecimal.ZERO;
                break;
        }
        if (!checkNeedShare(apiOrder, sharePreferential)) {
            return;
        }
        BigDecimal usedPreferential = BigDecimal.ZERO;
        for (EshopOrderDetailEntity detail : details) {
            if (usedPreferential.compareTo(sharePreferential) >= 0) {
                continue;
            }
            BigDecimal total = detail.getTotal();
            //分摊比例先乘以100 算分摊金额 防止四舍五入小数位丢失
            BigDecimal percent = MoneyUtils.divide(total, orderTotal, Money.Total);
            percent = MoneyUtils.multiply(percent, 100, Money.Total);
            BigDecimal shared = MoneyUtils.multiply(sharePreferential, percent, Money.Total);
            shared = MoneyUtils.divide(shared, 100, Money.Total);
            EshopPreferentialDetail PtypePreferentialDetail = getPtypePeferentialDetail(type, detail, apiOrder);
            BigDecimal tempPreferentialAll = MoneyUtils.add(PtypePreferentialDetail.getTotal(), shared, Money.Total);
            BigDecimal less = BigDecimal.ZERO;
            if (tempPreferentialAll.compareTo(total) > 0) {
                less = MoneyUtils.subtract(tempPreferentialAll, total, Money.Total);
            }
            shared = MoneyUtils.subtract(shared, less, Money.Total);
            PtypePreferentialDetail.setTotalShare(shared);
            usedPreferential = MoneyUtils.add(usedPreferential, shared, Money.Total);

            BigDecimal preferentialAll = MoneyUtils.add(PtypePreferentialDetail.getTotal(), shared, Money.Total);
            detail.setTradeTotal(MoneyUtils.subtract(total, preferentialAll, Money.Total));
        }
        BigDecimal lastTotal = MoneyUtils.subtract(sharePreferential, usedPreferential, Money.Total);
        //没有分完的金额做二次分摊
        if (lastTotal.compareTo(BigDecimal.ZERO) != 0) {
            ShareDiffPeferentialTotal(type, details, apiOrder, lastTotal);
        }
    }

    private EshopPreferentialDetail getPtypePeferentialDetail(PreferentialType type, EshopOrderDetailEntity detail,
                                                              EshopOrderEntity apiOrder) {
        if (apiOrder.getPreferentialDetails() == null) {
            apiOrder.setPreferentialDetails(new ArrayList<>());
        }
        List<EshopPreferentialDetail> preferentialDetails = apiOrder.getPreferentialDetails();
        Optional<EshopPreferentialDetail> first = preferentialDetails.stream().filter(x -> x.getOid().equals(detail.getOid())
                && x.getType().equals(type)).findFirst();
        EshopPreferentialDetail preferentialDetail = new EshopPreferentialDetail();
        if (!first.isPresent()) {
            preferentialDetail = new EshopPreferentialDetail();
            preferentialDetail.setOid(detail.getOid());
            preferentialDetail.setType(type);
            preferentialDetail.setTotal(BigDecimal.ZERO);
            preferentialDetails.add(preferentialDetail);
        } else {
            preferentialDetail = first.get();
        }
        return preferentialDetail;
    }

    private void ShareDiffPeferentialTotal(PreferentialType type, List<EshopOrderDetailEntity> details,
                                           EshopOrderEntity apiOrder, BigDecimal lastTotal) {

        BigDecimal needShareTotal = lastTotal;
        for (EshopOrderDetailEntity detail : details) {
            EshopPreferentialDetail preferentialDetail = getPtypePeferentialDetail(type, detail, apiOrder);
            BigDecimal PreferentialAll = MoneyUtils.add(preferentialDetail.getTotal(), preferentialDetail.getTotalShare(), Money.Total);
            if (detail.getTotal().compareTo(PreferentialAll) > 0) {
                BigDecimal canShareTotal = MoneyUtils.subtract(detail.getTotal(), PreferentialAll, Money.Total);
                if (canShareTotal.compareTo(needShareTotal) >= 0) {
                    preferentialDetail.setTotalShare(MoneyUtils.add(preferentialDetail.getTotalShare(), needShareTotal, Money.Total));
                    return;
                }
                preferentialDetail.setTotalShare(MoneyUtils.add(preferentialDetail.getTotalShare(), canShareTotal, Money.Total));
                needShareTotal = MoneyUtils.subtract(needShareTotal, canShareTotal, Money.Total);
            }
        }
    }

    private boolean checkNeedShare(EshopOrderEntity apiOrder, BigDecimal shareTotal) {
        boolean result = true;
        List<EshopOrderDetailEntity> details = apiOrder.getOrderDetails();
        BigDecimal orderTotal = apiOrder.getTotal();
        if (details == null || details.size() == 0) {
            result = false;
        } else if (orderTotal.doubleValue() <= 0) {
            result = false;
        }
        return result;
    }

    public void buildOrderMentionType(EshopSaleOrderEntity orderEntity, Otype otype) {
        List<EshopSaleOrderDetail> details = orderEntity.getOrderDetails();
        if (details == null || details.size() == 0) {
            return;
        }
        boolean advance = details.stream().allMatch(x -> x.getOrderSaleType().compareTo(TradeTypeEnum.ADVANCE_FORWARD_SALE) == 0
                || x.getOrderSaleType().compareTo(TradeTypeEnum.ADVANCE_FORWARD_SALE_BY_STOCK) == 0)
                || orderEntity.getOrderSaleType().equals(TradeTypeEnum.ADVANCE_FORWARD_SALE)
                || orderEntity.getOrderSaleType().equals(TradeTypeEnum.ADVANCE_FORWARD_SALE_BY_STOCK);
        //明细未对应，但是有【不发货不记账】，则应该认定为已对应的明细
        boolean has_mapping = details.stream().allMatch(d -> d.isMappingState() || !d.isDeliverRequired());
        boolean notSendGoods = details.stream().noneMatch(EshopSaleOrderDetail::isDeliverRequired);
        boolean cyclePurchase = details.stream().allMatch(x -> x.getOrderSaleType().compareTo(TradeTypeEnum.CYCLE_PURCHASE) == 0);
        if (advance) {
            orderEntity.setOrderSaleType(TradeTypeEnum.ADVANCE_FORWARD_SALE);
        }
        if (cyclePurchase) {
            orderEntity.setOrderSaleType(TradeTypeEnum.CYCLE_PURCHASE);
        }
        if (advance || details.stream().anyMatch(x -> (
                TradeTypeEnum.ADVANCE_FORWARD_SALE.equals(x.getOrderSaleType())
                        || TradeTypeEnum.ADVANCE_FORWARD_SALE_BY_STOCK.equals(x.getOrderSaleType())
        ))) {
            //计算预计发货时间
            orderEntity.getOrderDetails().stream().map(EshopSaleOrderDetail::getExtend)
                    .map(EshopSaleOrderDetailExtend::getPlanSendTime).filter(Objects::nonNull)
                    .min(Comparator.naturalOrder()).ifPresent(planSendTime -> orderEntity.getTiming().setPlanSendTime(planSendTime));
        }
        orderEntity.setMappingState(has_mapping);
        orderEntity.setOrderDeliverRequired(!notSendGoods);
        buildOrderRefundMentionType(orderEntity);
        buildOrderPlanSendTimeByEshopConfig(orderEntity,otype);
    }

    private void buildOrderPlanSendTimeByEshopConfig(EshopSaleOrderEntity orderEntity, Otype otype) {
        if (null == orderEntity || null == orderEntity.getTiming() || null != orderEntity.getTiming().getPlanSendTime() ||
                 null == otype || null == otype.getPlanSendTimeDuration() || null == orderEntity.getTiming().getPlanSignTime()){
            return;
        }
        if (TradeTypeEnum.ADVANCE_FORWARD_SALE != orderEntity.getOrderSaleType() &&
                TradeTypeEnum.ADVANCE_FORWARD_SALE_BY_STOCK != orderEntity.getOrderSaleType() &&
                TradeTypeEnum.CYCLE_PURCHASE != orderEntity.getOrderSaleType()){
            return;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(orderEntity.getTiming().getPlanSignTime());
        calendar.add(Calendar.HOUR, -otype.getPlanSendTimeDuration());
        orderEntity.getTiming().setPlanSendTime(calendar.getTime());
        orderEntity.getOrderDetails().forEach(d->d.getExtend().setPlanSendTime(calendar.getTime()));
    }

    public void buildOrderRefundMentionType(EshopSaleOrderEntity orderEntity) {
        List<EshopSaleOrderDetail> details = orderEntity.getOrderDetails();
        if (details == null || details.size() == 0) {
            return;
        }
        // true：所有明细都没有售后 或者只有部分明细产生售后     false：所有明细都产生售后
        boolean existNoRefund = details.stream().anyMatch(x -> x.getLocalRefundState().equals(ReturnState.NONE));

        //所有明细都产生售后  有任意一条明细退款成功
        boolean refunded = details.stream().anyMatch(x -> x.getLocalRefundState().equals(ReturnState.SUCCESS)) && !existNoRefund;
        //只有部分明细产生售后  有任意一条明细退款成功
        boolean part_refunded = (details.stream().anyMatch(x -> x.getLocalRefundState().equals(ReturnState.SUCCESS) || x.getLocalRefundState().equals(ReturnState.SECTION_REFUND_SUCCESS)) && existNoRefund) || (details.stream().anyMatch(x -> x.getLocalRefundState().equals(ReturnState.SECTION_REFUND_SUCCESS)));
        //所有明细都产生售后  并且有退款中的明细
        boolean refunding = details.stream().anyMatch(x -> x.getLocalRefundState().equals(ReturnState.REFUNDING)) && !existNoRefund;
        //所有明细都产生售后  并且有退款中的明细
        boolean part_refunding_first = details.stream().anyMatch(x -> x.getLocalRefundState().equals(ReturnState.SECTION_REFUNDING)) && !existNoRefund;
        //只有部分明细产生售后 并且有退款中的明细
        boolean part_refunding_second = details.stream().anyMatch(x -> x.getLocalRefundState().equals(ReturnState.REFUNDING) || x.getLocalRefundState().equals(ReturnState.SECTION_REFUNDING)) && existNoRefund;
        //任意一条明细是部分退款中，m
        boolean part_refunding = part_refunding_first || part_refunding_second;

        orderEntity.setLocalRefundState(ReturnState.NONE);
        /*
        总结：
            明细上的部分退款优先级要高点
            一条明细部分退中或部分退成，主表只能是部分退中或者部分退成
        */
        buildOrderRefundState(refunded, ReturnState.SUCCESS, orderEntity);
        buildOrderRefundState(part_refunded, ReturnState.SECTION_REFUND_SUCCESS, orderEntity);
        buildOrderRefundState(part_refunding, ReturnState.SECTION_REFUNDING, orderEntity);
        buildOrderRefundState(refunding, ReturnState.REFUNDING, orderEntity);

        buildOrderReSendState(details, orderEntity);
    }

    //计算【换/补状态】
    private void buildOrderReSendState(List<EshopSaleOrderDetail> details, EshopSaleOrderEntity orderEntity) {
        // true：所有明细都没有换补
        boolean existNoReSend = details.stream().allMatch(x -> x.getReSendState().equals(ReSendStateEnum.NOMARL));
        //任意明细产生换补，且换补中
        boolean exchangingReplenishing = details.stream().anyMatch(x -> x.getReSendState().equals(ReSendStateEnum.EXCHANGING_REPLENISHING)) && !existNoReSend;
        //任意明细产生换补，且这些明细全部为换补完成
        boolean exchangedReplenished = details.stream().allMatch(x -> x.getReSendState().equals(ReSendStateEnum.EXCHANGED_REPLENISHED) || x.getReSendState().equals(ReSendStateEnum.NOMARL)) && !existNoReSend;

       /*
        总结：
            明细上的换补中优先级要高点
            一条明细换补中，主表只能是换补中
        */
        buildOrderReSendState(exchangingReplenishing, ReSendStateEnum.EXCHANGING_REPLENISHING, orderEntity);
        buildOrderReSendState(exchangedReplenished, ReSendStateEnum.EXCHANGED_REPLENISHED, orderEntity);
        buildOrderReSendState(existNoReSend, ReSendStateEnum.NOMARL, orderEntity);
    }

    private void buildOrderRefundState(boolean condition, ReturnState state, EshopSaleOrderEntity order) {
        if (!condition) {
            return;
        }
        order.setLocalRefundState(state);
    }

    private void buildOrderReSendState(boolean condition, ReSendStateEnum resendState, EshopSaleOrderEntity order) {
        if (!condition) {
            return;
        }
        order.setReSendState(resendState);
    }

    public void rebuildOrder(EshopSaleOrderEntity newOrder, @NotNull EshopSaleOrderEntity localOrder, DownloadType downloadType) {
        //重算订单基本信息
        rebuildOrderMainInfo(newOrder, localOrder);
        //通过线上明细oid以及numid + 属性，确定明细的changeType
        buildOrderDetailOnlineChangeType(newOrder, localOrder);
        //分销订单，客户价格本金额重算
        rebuildLocalDistributionPriceCalc(newOrder, localOrder, downloadType);
        //税额税率重新赋值
        reBuildDetailTax(newOrder, localOrder, downloadType);
        //自动下单，保持本地订单对应关系到线上订单
        rebuildOrderDetailByChangeType(newOrder, localOrder, downloadType);
        //重算仓库
        rebuildOrderKtype(newOrder, localOrder);
        //对应关系重算之后，确定明细的changeType
        buildOrderDetailLocalChangeType(newOrder, localOrder);
        //重算对应状态，无需发货状态
        rebuildMentionType(newOrder, localOrder);
        //重算标记
        rebuildOrderMainMark(newOrder);
        //重算提交状态
        boolean hasAddDetail = newOrder.getOrderDetails().stream().map(EshopSaleOrderDetail::getDetailChangeTypes).flatMap(List::stream)
                .anyMatch(c -> OrderDetailChangeType.ADD_DETAIL == c);
        if (hasAddDetail){
            newOrder.setProcessState(rebuildOrderProcessType(newOrder.getOrderDetails()));
        }
    }

    private void rebuildOrderKtype(EshopSaleOrderEntity newOrder, @NotNull EshopSaleOrderEntity localOrder) {
        Optional<EshopSaleOrderDetail> firstDetail = newOrder.getOrderDetails().stream().findFirst();
        newOrder.setKtypeId(firstDetail.isPresent() ? firstDetail.get().getKtypeId() : newOrder.getKtypeId());
    }

    public ProcessState rebuildOrderProcessType(List<EshopSaleOrderDetail> details) {
        if (CollectionUtils.isEmpty(details)) {
            throw new RuntimeException("提交状态重算失败，明细为空");
        }
        boolean orderHasSubmit = details.stream().allMatch(d -> d.getProcessState().equals(DetailProcessState.Submit) || d.getProcessState().equals(DetailProcessState.SubmitAdvance));
        boolean orderHasNoSubmit = details.stream().allMatch(d -> d.getProcessState().equals(DetailProcessState.NoSubmit));
        if (orderHasSubmit) {
            return ProcessState.Submit;
        }
        if (orderHasNoSubmit) {
            return ProcessState.NoSubmit;
        }
        return ProcessState.SubmitAdvance;
    }

    private static void rebuildOrderMainInfo(EshopSaleOrderEntity newOrder, @NotNull EshopSaleOrderEntity localOrder) {
        newOrder.setId(localOrder.getId());
        newOrder.setProcessState(localOrder.getProcessState());
        newOrder.setOldTradeStatus(localOrder.getLocalTradeState());
        newOrder.setRemark((!newOrder.getRemark().isEmpty() && !localOrder.getRemark().contains(newOrder.getRemark()))
                ? String.format("%s  %s", localOrder.getRemark(), newOrder.getRemark()) : localOrder.getRemark());
        newOrder.setDeleted(localOrder.getDeleted());
        newOrder.setSummary(localOrder.getSummary());
        newOrder.getExtend().setEshopOrderId(localOrder.getId());
        newOrder.getExtend().setGatherStatus(localOrder.getExtend().getGatherStatus());
        newOrder.getTiming().setEshopOrderId(localOrder.getId());
        newOrder.getInvoiceInfo().setEshopOrderId(localOrder.getId());
        if (newOrder.getFuzzySensitiveData()) {
            newOrder.setBuyerId(localOrder.getBuyerId());
            newOrder.setReceiveAddressId(localOrder.getBuyerId());
        }
        newOrder.setCreateTime(localOrder.getCreateTime());
        newOrder.setUpdateTime(localOrder.getUpdateTime());
        newOrder.getExtend().setIsDraft(localOrder.getExtend().getIsDraft());
        newOrder.setTradeFinishTime(CommonUtil.isInvalidDate(localOrder.getTradeFinishTime()) ?  newOrder.getTradeFinishTime() : localOrder.getTradeFinishTime());
    }

    private void rebuildLocalDistributionPriceCalc(EshopSaleOrderEntity newOrder, @NotNull EshopSaleOrderEntity localOrder, DownloadType downloadType) {
        if (!(downloadType.equals(DownloadType.BY_MODIFY_ALL_DATA) || downloadType.equals(DownloadType.BY_MUL_EDIT) ) && localOrder.isMappingState()) {
            newOrder.setBtypeId(localOrder.getBtypeId());
            newOrder.setPayBtypeId(localOrder.getPayBtypeId());
            rebuildLocalDistributionPriceCalc(newOrder, localOrder, downloadType.getOperateType());
            if (localOrder.getDisedTaxedTotal().compareTo(newOrder.getDisedTaxedTotal()) != 0) {
                logger.error(String.format("profileId:%s,tradeOrderId:%s,客户价格本发生变化导致折后含税金额变更,原%s------->新%s", newOrder.getProfileId(), newOrder.getTradeOrderId(), localOrder.getDisedTaxedTotal().stripTrailingZeros(), newOrder.getDisedTaxedTotal().stripTrailingZeros()));
            }
        }
    }

    /**
     * 通过oid比对线上明细和本地明细
     *
     * @param newOrder
     * @param localOrder
     */
    private void buildOrderDetailOnlineChangeType(EshopSaleOrderEntity newOrder, @NotNull EshopSaleOrderEntity localOrder) {
        List<String> shopTypeList = bizConfig.getRebuildOrderByOidAndPtypeShopTypeList();
        List<EshopSaleOrderDetail> onlineDetails = newOrder.getOrderDetails();
        List<EshopSaleOrderDetail> localDetails = localOrder.getOrderDetails();
        Map<String, List<EshopSaleOrderDetail>> onlineDetailsMap = onlineDetails.stream().collect(Collectors.groupingBy(EshopSaleOrderDetail::getTradeOrderDetailId));
        Map<String, List<EshopSaleOrderDetail>> localDetailsMap = localDetails.stream().collect(Collectors.groupingBy(EshopSaleOrderDetail::getTradeOrderDetailId));
        //匹配完后剩余的本地明细，就是线上删除的明细
        Map<String, Object> surplusLocalDetailsMap = JsonUtils.toHashMap(JsonUtils.toJson(localDetailsMap));

        //线上为主进行匹配，筛选线上新增明细
        for (String onlineOid : onlineDetailsMap.keySet()) {
            List<EshopSaleOrderDetail> onlineItems = onlineDetailsMap.get(onlineOid);
            List<EshopSaleOrderDetail> matchLocalByOid = localDetails.stream().filter(x -> x.getTradeOrderDetailId().equals(onlineOid)).collect(Collectors.toList());
            EshopSaleOrderDetail onlineItem = onlineItems.get(0);
            if (CollectionUtils.isEmpty(matchLocalByOid)) {
                //通过oid匹配不到明细，就需要通过 numId + 属性名称来进行匹配
                //默认只有小红书需要这样匹配
                if (shopTypeList.contains(String.valueOf(newOrder.getShopType().getCode()))) {
                    String platformPtypeId = onlineItem.getPlatformPtypeId();
                    String platformPropertiesName = onlineItem.getPlatformPropertiesName();
                    List<EshopSaleOrderDetail> matchLocalByNumId = localDetails.stream().filter(x -> x.getPlatformPtypeId().equals(platformPtypeId) && x.getPlatformPropertiesName().equals(platformPropertiesName)).collect(Collectors.toList());
                    //通过 numId + 属性名称 匹配不到，就认为 该线上明细 为新增明细
                    if (CollectionUtils.isEmpty(matchLocalByNumId)) {
                        onlineItems.forEach(x -> {
                            x.getDetailChangeTypes().add(OrderDetailChangeType.ADD_DETAIL);
                        });
                    } else {
                        onlineItems.forEach(o -> o.setLocalTradeOrderDetailId(matchLocalByNumId.get(0).getTradeOrderDetailId()));
                        surplusLocalDetailsMap.remove(matchLocalByNumId.get(0).getTradeOrderDetailId());
                        buildDetailChangeType(matchLocalByNumId, onlineItems, newOrder, localOrder, false);
                    }
                    continue;
                }
                //只通过oid匹配，但是匹配不到的情况下，就认为是新增明细
                onlineItems.forEach(x -> {
                    x.getDetailChangeTypes().add(OrderDetailChangeType.ADD_DETAIL);
                });
                continue;
            }
            //通过oid匹配到了
            //判断明细变更情况
            onlineItems.forEach(o -> o.setLocalTradeOrderDetailId(onlineOid));
            surplusLocalDetailsMap.remove(onlineOid);
            buildDetailChangeType(matchLocalByOid, onlineItems, newOrder, localOrder, true);
        }

        for (String localOid : surplusLocalDetailsMap.keySet()) {
            //线上已删除的oid
            //应该将该明细加入newOrder中
            //同时增加标记取消，不发货不记账
            List<EshopSaleOrderDetail> deleteDetails = JsonUtils.toList(JsonUtils.toJson(surplusLocalDetailsMap.get(localOid)), EshopSaleOrderDetail.class);
            List<EshopSaleDetailCombo> deleteCombos = localOrder.getDetailCombos().stream().filter(c -> c.getTradeOrderDetailId().equals(localOid)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(deleteDetails)) {
                deleteDetails.forEach(d -> {
                    EShopOrderMarkUtil.doBuildAndAddToMarkList(d.getOrderDetailMarks(), newOrder.getId(), d.getId(), newOrder.getProfileId(), BaseOrderMarkEnum.ORDER_DETAIL_DELETE, null, "线上明细被删除");
                    d.setPlatformDetailTradeState(TradeStatus.ALL_CLOSED);
                    d.setDeliverRequired(false);
                    d.getDetailChangeTypes().add(OrderDetailChangeType.DELETE_DETAIL);
                });
                newOrder.getOrderDetails().addAll(deleteDetails);
            }
            if (CollectionUtils.isNotEmpty(deleteCombos)) {
                newOrder.getDetailCombos().addAll(deleteCombos);
            }
        }
    }

    private void buildDetailChangeType(List<EshopSaleOrderDetail> localDetails, List<EshopSaleOrderDetail> onlineItems,
                                       EshopSaleOrderEntity newOrder, EshopSaleOrderEntity localOrder, boolean matchOid) {
        EshopSaleOrderDetail localItem = localDetails.get(0);
        EshopSaleOrderDetail onlineItem = onlineItems.get(0);
        String localDetailKey = getDetailKeyByDetailOrCombo(localOrder, localItem, matchOid);
        String onlineDetailKey = getDetailKeyByDetailOrCombo(newOrder, onlineItem, matchOid);
        // 需要判断明细信息是否发生变更
        if (!localDetailKey.equals(onlineDetailKey)) {
            onlineItems.forEach(x -> {
                x.setLocalTradeOrderDetailId(localItem.getTradeOrderDetailId());
                x.getDetailChangeTypes().add(OrderDetailChangeType.PTYPE_CHANGE);
            });
        }
        // 金额发生变更
        if (checkDetailPriceChange(localDetails,onlineItems)) {
            onlineItems.forEach(x -> {
                x.getDetailChangeTypes().add(OrderDetailChangeType.PRICE_CHANGE);
            });
        }
        long localGiftCount = localDetails.stream().filter(l -> null != l.getExtend() && l.getExtend().isGift()).count();
        long onlineGiftCount = onlineItems.stream().filter(l -> null != l.getExtend() && l.getExtend().isGift()).count();
        if (localGiftCount != onlineGiftCount) {
            onlineItems.forEach(x -> {
                x.getDetailChangeTypes().add(OrderDetailChangeType.GIFT_CHANGE);
            });
        }
        doLogBySysDataConfig(localOrder,newOrder);
    }

    private double getDetailPriceChangeTotal(List<EshopSaleOrderDetail> localDetails) {
        double localSum = localDetails.stream().mapToDouble(x -> x.getDisedTaxedTotal() //折后含税金额
                .add(x.getPlatformOrderPreferentialTotal()) //平台整单优惠分摊
                .add(x.getPlatformPtypePreferentialTotal())//平台单品优惠金额
                .add(x.getPtypePreferentialTotal())//商家单品优惠金额
                .add(x.getOrderPreferentialAllotTotal())//商家整单优惠分摊
                .add(x.getExtend().getAnchorOrderPreferentialTotal())//主播整单优惠分摊
                .add(x.getExtend().getAnchorPtypePreferentialTotal())//主播单品优惠金额
                .add(x.getExtend().getPlatformOrderSubsidyTotal())//平台补贴商家整单金额分摊
                .add(x.getExtend().getPlatformPtypeSubsidyTotal())//平台补贴商家单品金额
                .add(x.getExtend().getNationalSubsidyTotal())//国补金额
                .add(x.getPtypeServiceFee())//服务费
                .doubleValue()).sum();
        return localSum;
    }

    private boolean checkDetailPriceChange(List<EshopSaleOrderDetail> localDetails,List<EshopSaleOrderDetail> onlineDetails){
        if (CollectionUtils.isEmpty(localDetails) && CollectionUtils.isEmpty(onlineDetails)){
            return false;
        }
        return compareSum(localDetails, onlineDetails, EshopSaleOrderDetail::getDisedTaxedTotal) ||//折后含税金额
                compareSum(localDetails, onlineDetails, EshopSaleOrderDetail::getPlatformOrderPreferentialTotal) ||//平台整单优惠分摊
                compareSum(localDetails, onlineDetails, EshopSaleOrderDetail::getPlatformPtypePreferentialTotal) ||//平台单品优惠金额
                compareSum(localDetails, onlineDetails, EshopSaleOrderDetail::getPtypePreferentialTotal) ||//商家单品优惠金额
                compareSum(localDetails, onlineDetails, EshopSaleOrderDetail::getOrderPreferentialAllotTotal) ||//商家整单优惠分摊
                compareSum(localDetails, onlineDetails,  detail -> detail.getExtend().getAnchorOrderPreferentialTotal()) ||//主播整单优惠分摊
                compareSum(localDetails, onlineDetails,  detail -> detail.getExtend().getAnchorPtypePreferentialTotal()) ||//主播单品优惠金额
                compareSum(localDetails, onlineDetails,  detail -> detail.getExtend().getPlatformOrderSubsidyTotal()) ||//平台补贴商家整单金额分摊
                compareSum(localDetails, onlineDetails,  detail -> detail.getExtend().getPlatformPtypeSubsidyTotal()) ||//平台补贴商家单品金额
                compareSum(localDetails, onlineDetails,  detail -> detail.getExtend().getNationalSubsidyTotal()) ||//国补金额
                compareSum(localDetails, onlineDetails, EshopSaleOrderDetail::getPtypeServiceFee);//服务费
    }

    private boolean compareSum(List<EshopSaleOrderDetail> list1, List<EshopSaleOrderDetail> list2, Function<EshopSaleOrderDetail, BigDecimal> mapper) {
        double sum1 = list1 == null ? 0 : list1.stream()
                .mapToDouble(x -> mapper.apply(x).doubleValue())
                .sum();
        double sum2 = list2 == null ? 0 : list2.stream()
                .mapToDouble(x -> mapper.apply(x).doubleValue())
                .sum();
        return Double.compare(sum1, sum2) != 0;
    }

    public void doLogBySysDataConfig(EshopSaleOrderEntity local,EshopSaleOrderEntity online) {
        if (null == local){
            return;
        }
        EshopOrderSysDataConfig sysDataConfig = GlobalConfig.get(EshopOrderSysDataConfig.class);
        if (null != sysDataConfig && sysDataConfig.getInsertOrderLog() == 1) {
            CommonUtil.doLogByEnable(logger,LogLevelEnum.ERROR,String.format("profileId:%s,tradeOrderId:%s,localOrder:%s,onlineOrder:%s",
                    local.getProfileId(), local.getTradeOrderId(),JsonUtils.toJson(local),
                    null == online ? "" : JsonUtils.toJson(online)));
        }
    }

    private String getDetailKeyByDetailOrCombo(EshopSaleOrderEntity localOrder, EshopSaleOrderDetail localItem, boolean matchOid) {
        boolean localCombow = BigInteger.ZERO.compareTo(localItem.getComboRowId()) != 0;
        String localDetailKey = "";
        if (localCombow) {
            Optional<EshopSaleDetailCombo> localCombo = localOrder.getDetailCombos().stream().filter(c -> c.getEshopOrderDetailComboRowId().compareTo(localItem.getComboRowId()) == 0).findFirst();
            if (localCombo.isPresent()) {
                localDetailKey = getComboKey(localCombo.get(), localItem, matchOid);
            }
        } else {
            localDetailKey = getDetailKey(localItem, matchOid);
        }
        return localDetailKey;
    }

    //如果原来就有税额和税率，更新订单不进行覆盖
    private void reBuildDetailTax(EshopSaleOrderEntity newOrder, EshopSaleOrderEntity localOrder, DownloadType downloadType) {
        Map<String, List<EshopSaleOrderDetail>> onlineDetailsMap = newOrder.getOrderDetails().stream().collect(Collectors.groupingBy(EshopSaleOrderDetail::getTradeOrderDetailId));
        List<EshopSaleOrderDetail> localDetails = localOrder.getOrderDetails();
        if (!(downloadType.equals(DownloadType.BY_MODIFY_ALL_DATA) || downloadType.equals(DownloadType.BY_MUL_EDIT)) && localOrder.isMappingState()) {
            return;
        }
        for (String onlineOid : onlineDetailsMap.keySet()) {
            List<EshopSaleOrderDetail> onlineItems = onlineDetailsMap.get(onlineOid);
            List<EshopSaleOrderDetail> localCollect = localDetails.stream().filter(x -> x.getTradeOrderDetailId().equals(onlineOid)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(onlineItems) || CollectionUtils.isEmpty(localCollect)) {
                continue;
            }
            EshopSaleOrderDetail onlineItem = onlineItems.get(0);
            EshopSaleOrderDetail localItem = localCollect.get(0);
            if (localItem.getTaxRate().compareTo(BigDecimal.ZERO) == 0 && localItem.getTaxTotal().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            //新：套餐，旧：套餐
            if (onlineItem.getComboRowId().compareTo(BigInteger.ZERO) != 0 && localItem.getComboRowId().compareTo(BigInteger.ZERO) != 0) {
                //套餐行
                Optional<EshopSaleDetailCombo> first = localOrder.getDetailCombos().stream().filter(c -> c.getEshopOrderDetailComboRowId().compareTo(localItem.getComboRowId()) == 0).findFirst();
                if (first.isPresent()) {
                    BigDecimal localTaxTotal = first.get().getTaxTotal();
                    newOrder.getDetailCombos().forEach(c -> {
                        if (c.getEshopOrderDetailComboRowId().compareTo(onlineItem.getComboRowId()) == 0) {
                            c.setTaxTotal(localTaxTotal);
                        }
                    });
                    buildComboDetailTaxFormCombo(onlineItems, localTaxTotal);
                }
                continue;
            }
            //新：套餐，旧：商品
            if (onlineItem.getComboRowId().compareTo(BigInteger.ZERO) != 0 && localItem.getComboRowId().compareTo(BigInteger.ZERO) == 0) {
                newOrder.getDetailCombos().forEach(c -> {
                    if (c.getEshopOrderDetailComboRowId().compareTo(onlineItem.getComboRowId()) == 0) {
                        c.setTaxTotal(localItem.getTaxTotal());
                    }
                });
                buildComboDetailTaxFormCombo(onlineItems, localItem.getTaxTotal());
                continue;
            }
            //新：商品，旧：商品
            if (localItem.getTaxRate().compareTo(BigDecimal.ZERO) != 0 && localItem.getTaxTotal().compareTo(BigDecimal.ZERO) != 0 && localItem.getComboRowId().compareTo(BigInteger.ZERO) == 0) {
                onlineItem.setTaxTotal(localItem.getTaxTotal());
                onlineItem.setTaxRate(localItem.getTaxRate());
                onlineItem.setDisedTotal(MoneyUtils.subtract(onlineItem.getDisedTaxedTotal(),onlineItem.getTaxTotal(),Money.Total));
                continue;
            }
            //新：商品，旧：套餐
            if (onlineItem.getComboRowId().compareTo(BigInteger.ZERO) == 0 && localItem.getComboRowId().compareTo(BigInteger.ZERO) != 0) {
                Optional<EshopSaleDetailCombo> first = localOrder.getDetailCombos().stream().filter(c -> c.getEshopOrderDetailComboRowId().compareTo(localItem.getComboRowId()) == 0).findFirst();
                if (first.isPresent()) {
                    BigDecimal localTaxTotal = first.get().getTaxTotal();
                    onlineItem.setTaxTotal(localTaxTotal);
                    calcaulateDetailTax(onlineItem, localTaxTotal, null, GlobalConfig.get(SysGlobalConfig.class));
                }
            }
        }
    }

    //从套餐行税额，税率，计算套餐明细的税额，税率
    private void buildComboDetailTaxFormCombo(List<EshopSaleOrderDetail> onlineItems, BigDecimal localTaxTotal) {
        onlineItems.forEach(o -> {
            calcaulateDetailTax(o, null, o.getTaxRate(), GlobalConfig.get(SysGlobalConfig.class));
        });
    }

    private void rebuildLocalDistributionPriceCalc(EshopSaleOrderEntity newOrder, EshopSaleOrderEntity localOrder, OrderOpreateType orderOpreateType) {
        //如果是按商家编码对应，sku_mapping表里不会存储对应关系
        //分销订单
        //在除更新全部外的操作时，会出现没有对应关系，取不到价格本的数据
        //导致折后金额为0的情况
        //将套餐和普通商品进行分组
        if (!newOrder.getBusinessType().equals(BillBusinessType.SaleDistribution)) {
            return;
        }
        List<EshopSaleOrderDetail> orderDetails = newOrder.getOrderDetails();
        List<EshopSaleDetailCombo> detailCombos = newOrder.getDetailCombos();
        List<EshopSaleOrderDetail> localOrderDetails = localOrder.getOrderDetails();
        List<EshopSaleDetailCombo> localDetailCombos = localOrder.getDetailCombos();
        //新订单没有对应关系
        List<EshopSaleOrderDetail> needAddDetails = new ArrayList<>();
        List<EshopSaleDetailCombo> needAddDetailCombos = new ArrayList<>();
        if (!newOrder.isMappingState()) {
            Iterator<EshopSaleOrderDetail> iterator = orderDetails.iterator();
            while (iterator.hasNext()) {
                EshopSaleOrderDetail next = iterator.next();
                if (!next.isMappingState()) {
                    List<EshopSaleOrderDetail> localMappingDetails = localOrderDetails.stream().filter(l -> l.getTradeOrderDetailId().equals(next.getTradeOrderDetailId()) && l.isMappingState()).collect(Collectors.toList());
                    localMappingDetails.forEach(l -> doRebuildDetailBaseInfo(newOrder, next, l, true));
                    needAddDetails.addAll(localMappingDetails);
                    iterator.remove();
                }
            }
            needAddDetails.forEach(n -> n.setEshopOrderId(newOrder.getId()));
            needAddDetails.stream().map(EshopSaleOrderDetail::getTradeOrderDetailId).distinct().forEach(o -> {
                List<EshopSaleDetailCombo> modifyCombos = rebuildComboRowsForUpdate(o, localOrder.getDetailCombos(), needAddDetails);
                needAddDetailCombos.addAll(modifyCombos);
            });
            needAddDetailCombos.forEach(n -> n.setEshopOrderId(newOrder.getId()));
            orderDetails.addAll(needAddDetails);
            detailCombos.addAll(needAddDetailCombos);

            List<EshopSaleOrderDetail> noComboRowDetails = orderDetails.stream().filter(o -> !o.isComboRow()).collect(Collectors.toList());
            newOrder.setOrderDetails(orderDetails);
            newOrder.setDetailCombos(detailCombos);
            newOrder.setTradeTotal(orderDetails.stream().map(EshopSaleOrderDetail::getTradeTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
            newOrder.setTotal(orderDetails.stream().map(EshopSaleOrderDetail::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
            newOrder.setDisedTaxedTotal(orderDetails.stream().map(EshopSaleOrderDetail::getDisedTaxedTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
            String log = String.format("分销订单，线上订单没有对应关系，取本地订单信息，折后含税金额取价格本金额:%s", newOrder.getDisedTaxedTotal().stripTrailingZeros());
            SysLogUtil.add(SysLogUtil.buildLog(newOrder, orderOpreateType, log));
            return;
        }

        Map<EshopSaleDetailCombo, List<EshopSaleOrderDetail>> comboListMap = new HashMap<>();
        if (orderDetails == null || orderDetails.size() == 0) {
            return;
        }
        if (!detailCombos.isEmpty()) {
            for (EshopSaleDetailCombo comboRow : detailCombos) {
                comboRow.setBusinessType(BillBusinessType.SaleDistribution);
                comboRow.setBtypeId(newOrder.getBtypeId());
                List<EshopSaleOrderDetail> comboDetailList = orderDetails.stream().filter(o -> o.getComboRowId().equals(comboRow.getEshopOrderDetailComboRowId())).collect(Collectors.toList());
                comboListMap.put(comboRow, comboDetailList);
            }
        }
        //普通明细取价格本
        for (EshopSaleOrderDetail detail : orderDetails) {
            detail.setBusinessType(BillBusinessType.SaleDistribution);
            if (detail.getComboRowId().equals(BigInteger.ZERO)) {
                relationService.doLocalDistributionPriceCalc(detail, newOrder, orderOpreateType);
            }
        }
        //套餐明细取价格本
        if (comboListMap.isEmpty()) {
            return;
        }
        for (Map.Entry<EshopSaleDetailCombo, List<EshopSaleOrderDetail>> entry : comboListMap.entrySet()) {
            relationService.doLocalDistributionPriceCalc(entry.getKey(), entry.getValue(), newOrder, orderOpreateType, entry.getKey().getSaleType());
        }
        newOrder.setTradeTotal(orderDetails.stream().map(EshopSaleOrderDetail::getTradeTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        newOrder.setTotal(orderDetails.stream().map(EshopSaleOrderDetail::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        newOrder.setDisedTaxedTotal(orderDetails.stream().map(EshopSaleOrderDetail::getDisedTaxedTotal).reduce(BigDecimal.ZERO, BigDecimal::add));
        String log = String.format("分销订单，重取客户价格本，折后含税金额变更,原%s------->新%s", localOrder.getDisedTaxedTotal().stripTrailingZeros(), newOrder.getDisedTaxedTotal().stripTrailingZeros());
        SysLogUtil.add(SysLogUtil.buildLog(newOrder, orderOpreateType, log));
    }

    public void buildOrderDetailLocalChangeType(EshopSaleOrderEntity newOrder, @NotNull EshopSaleOrderEntity localOrder) {
        List<EshopSaleDetailCombo> onlineCombos = newOrder.getDetailCombos();
        List<EshopSaleDetailCombo> localCombos = localOrder.getDetailCombos();
        Map<String, List<EshopSaleOrderDetail>> onlineDetailsMap = newOrder.getOrderDetails().stream().collect(Collectors.groupingBy(EshopSaleOrderDetail::getTradeOrderDetailId));
        List<EshopSaleOrderDetail> localDetails = localOrder.getOrderDetails();
        for (String onlineOid : onlineDetailsMap.keySet()) {
            List<EshopSaleOrderDetail> onlineItems = onlineDetailsMap.get(onlineOid);
            List<EshopSaleOrderDetail> localCollect = localDetails.stream().filter(x -> x.getTradeOrderDetailId().equals(onlineOid)).collect(Collectors.toList());
            if (localCollect.size() == 0) {
                continue;
            }
            EshopSaleOrderDetail onlineItem = onlineItems.get(0);
            EshopSaleOrderDetail localItem = localCollect.get(0);

            //判断新明细是否为套餐
            Optional<EshopSaleOrderDetail> onlineComboRow = onlineItems.stream().filter(o -> o.getComboRowId().compareTo(BigInteger.ZERO) > 0).findFirst();
            //判断旧明细是否为套餐
            Optional<EshopSaleOrderDetail> localComboRow = localCollect.stream().filter(o -> o.getComboRowId().compareTo(BigInteger.ZERO) > 0).findFirst();
            if (CollectionUtils.isNotEmpty(onlineItem.getDetailChangeTypes())) {
                continue;
            }
            //对应关系变更：
            if ((onlineItem.isMappingState() != localItem.isMappingState()) ||
                    //新明细为套餐，旧的普通明细
                    (onlineComboRow.isPresent() && !localComboRow.isPresent()) ||
                    //新明细为套餐，旧明细也是套餐，但是套餐行的comboId不匹配
                    (onlineComboRow.isPresent() && localComboRow.isPresent() && onlineCombos.stream().filter(o -> o.getEshopOrderDetailComboRowId().compareTo(onlineComboRow.get().getComboRowId()) == 0).findFirst().get().getComboId().compareTo(localCombos.stream().filter(l -> l.getEshopOrderDetailComboRowId().compareTo(localComboRow.get().getComboRowId()) == 0).findFirst().get().getComboId()) != 0) ||
                    //新明细为普通明细，旧明细为套餐
                    (!onlineComboRow.isPresent() && localComboRow.isPresent()) ||
                    //新明细为普通明细，旧明细为普通明细，但是ptypeId,skuId,unitId不匹配
                    (!onlineComboRow.isPresent() && !localComboRow.isPresent() && onlineItem.getPtypeId().compareTo(localItem.getPtypeId()) != 0 && onlineItem.getSkuId().compareTo(localItem.getSkuId()) != 0 && onlineItem.getUnit().compareTo(localItem.getUnit()) != 0)) {
                onlineItems.forEach(x -> {
                    x.getDetailChangeTypes().add(OrderDetailChangeType.MAPPING_CHANGE);
                });
                continue;
            }
            if (onlineItem.getOrderSaleType() != localItem.getOrderSaleType()) {
                onlineItems.forEach(x -> x.getDetailChangeTypes().add(OrderDetailChangeType.TRADE_TYPE_CHANGE));
            }
            if (onlineItem.isDeliverRequired() != localItem.isDeliverRequired()) {
                onlineItems.forEach(x -> x.getDetailChangeTypes().add(OrderDetailChangeType.DELIVER_REQUIRE));
            }
            if (onlineItem.getKtypeId().compareTo(localItem.getKtypeId()) != 0) {
                onlineItems.forEach(x -> x.getDetailChangeTypes().add(OrderDetailChangeType.KTYPE_CHANGE));
            }
        }
    }

    public void rebuildOrderDetailByChangeType(EshopSaleOrderEntity newOrder, @NotNull EshopSaleOrderEntity localOrder, DownloadType downloadType) {
        List<String> shopTypeList = bizConfig.getRebuildOrderByOidAndPtypeShopTypeList();
        Map<String, List<EshopSaleOrderDetail>> onlineDetailsMap = newOrder.getOrderDetails().stream().collect(Collectors.groupingBy(EshopSaleOrderDetail::getTradeOrderDetailId, LinkedHashMap::new, Collectors.toList()));
        List<EshopSaleDetailCombo> onlineComboRows = newOrder.getDetailCombos();
        List<EshopSaleOrderDetail> localDetails = localOrder.getOrderDetails();
        List<EshopSaleOrderDetail> orderDetails = new ArrayList<>();
        List<EshopSaleDetailCombo> combos = new ArrayList<>();
        Map<BigInteger, BigInteger> detailIdsMap = new HashMap<>();
        for (String onlineOid : onlineDetailsMap.keySet()) {
            List<EshopSaleOrderDetail> onlineItems = onlineDetailsMap.get(onlineOid);
            EshopSaleOrderDetail onlineItem = onlineItems.get(0);
            List<EshopSaleOrderDetail> localCollect = localDetails.stream().filter(
                    x -> x.getTradeOrderDetailId().equals(onlineOid)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(localCollect) && shopTypeList.contains(String.valueOf(newOrder.getShopType().getCode()))) {
                String onlinePlatformPtypeId = onlineItem.getPlatformPtypeId();
                String onlinePlatformPropertiesName = onlineItem.getPlatformPropertiesName();
                localCollect = localDetails.stream().filter(x -> x.getPlatformPtypeId().equals(onlinePlatformPtypeId)
                        && x.getPlatformPropertiesName().equals(onlinePlatformPropertiesName)).collect(Collectors.toList());
            }

            //本地找不到这个明细，并且这条线上明细被标记了删除
            //之前已经从本地明细中添加了这条明细到线上，所以这里就不进行构建
            if (onlineItem.getDetailChangeTypes().contains(OrderDetailChangeType.DELETE_DETAIL)) {
                orderDetails.addAll(onlineItems);
                Optional<EshopSaleDetailCombo> first = onlineComboRows.stream().filter(o -> o.getTradeOrderDetailId().equals(onlineOid)).findFirst();
                if (first.isPresent()) {
                    combos.add(first.get());
                }
                continue;
            }

            //明细信息进行全部更新 （包含线上信息和本地状态）
            if (0 == localCollect.size() ||
                    onlineItem.getDetailChangeTypes().contains(OrderDetailChangeType.ADD_DETAIL) ||
                    downloadType.equals(DownloadType.BY_MODIFY_ALL_DATA) ||
                    downloadType.equals(DownloadType.BY_MUL_EDIT) ||
                    !localCollect.get(0).isMappingState() ||
                    onlineItem.getDetailChangeTypes().contains(OrderDetailChangeType.PTYPE_CHANGE)) {
                orderDetails.addAll(rebuildOrderDetailList(onlineItems, localCollect.size() == 0 ? null : localCollect.get(0), newOrder, detailIdsMap));
                EshopSaleDetailCombo comboRow = rebuildOrderComboRows(onlineOid, onlineComboRows, onlineItems.get(0));
                if (comboRow != null) {
                    combos.add(comboRow);
                }
                continue;
            }


            //只进行线上信息修改的更新（本地状态不进行更新）

            //只构建单品的明细（新明细是单品），以本地商品明细为基准，更新线上信息，对应关系和标签的重新应用
            List<EshopSaleOrderDetail> detailsForUpdate = rebuildDetailForUpdate(newOrder, onlineItems, localCollect);
            //只构建套餐的明细（新明细是套餐），以本地商品明细为基准，更新线上信息，对应关系和标签的重新应用
            List<EshopSaleOrderDetail> tempComboDetails = doRebuildComboDetailsForUpdate(newOrder, onlineOid, onlineItems, onlineComboRows, localCollect);
            if (tempComboDetails.size() > 0) {
                detailsForUpdate.addAll(tempComboDetails);
            }
            orderDetails.addAll(detailsForUpdate);
            //重新构建套餐行 以本地套餐行为基准，更新线上信息
            List<EshopSaleDetailCombo> modifyCombos = rebuildComboRowsForUpdate(onlineOid, localOrder.getDetailCombos(), detailsForUpdate);
            combos.addAll(modifyCombos);
        }
        newOrder.setOrderDetails(orderDetails);
        newOrder.setDetailCombos(combos);
        //主表的税额永远都是从明细上来的，不应该受其他信息影响
        BigDecimal taxTotal = newOrder.getOrderDetails().stream().filter(d -> !d.isComboRow()).map(EshopSaleOrderDetail::getTaxTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
        reBuildOrderGiftRelations(newOrder, detailIdsMap);
        newOrder.setTaxTotal(taxTotal);
    }

    private static void reBuildOrderGiftRelations(EshopSaleOrderEntity order, Map<BigInteger, BigInteger> detailIdsMap) {
        if (null == order || detailIdsMap.size() == 0) {
            return;
        }
        List<EshopSaleOrderDetail> details = order.getOrderDetails();
        if (ngp.utils.CollectionUtils.isEmpty(details)) {
            return;
        }
        for (EshopSaleOrderDetail detail : details) {
            List<EshopSaleOrderDetailGiftRelation> giftRelations = detail.getGiftRelations();
            if (ngp.utils.CollectionUtils.isEmpty(giftRelations)) {
                continue;
            }
            for (EshopSaleOrderDetailGiftRelation giftRelation : giftRelations) {
                BigInteger eshopOrderDetailId = giftRelation.getEshopOrderDetailId();
                BigInteger sourceEshopOrderDetailId = giftRelation.getSourceEshopOrderDetailId();
                BigInteger newEshopOrderDetailId = detailIdsMap.get(eshopOrderDetailId);
                BigInteger newSourceEshopOrderDetailId = detailIdsMap.get(sourceEshopOrderDetailId);
                if (null == newEshopOrderDetailId || null == newSourceEshopOrderDetailId) {
                    continue;
                }
                if (BigInteger.ZERO.compareTo(newEshopOrderDetailId) == 0 || BigInteger.ZERO.compareTo(newSourceEshopOrderDetailId) == 0) {
                    continue;
                }
                giftRelation.setEshopOrderDetailId(newEshopOrderDetailId);
                giftRelation.setSourceEshopOrderDetailId(newSourceEshopOrderDetailId);
            }
        }
    }

    private String getDetailKey(EshopSaleOrderDetail detail, boolean matchOid) {
        String sb = (matchOid ? detail.getTradeOrderDetailId() : "") +
                detail.getPlatformPtypeId() +
                detail.getPlatformPtypeXcode() +
                detail.getPlatformSkuId() +
                getPlatformPropertiesNameKey(detail.getPlatformPropertiesName()) +
                detail.getUnitQty().stripTrailingZeros() +
                detail.getQty().stripTrailingZeros() +
                detail.getDisplayCustomInfo();
        return Md5Utils.md5(sb);
    }

    private String getComboKey(EshopSaleDetailCombo combo, EshopSaleOrderDetail detail, boolean matchOid) {
        String sb = (matchOid ? detail.getTradeOrderDetailId() : "") +
                detail.getPlatformPtypeId() +
                detail.getPlatformPtypeXcode() +
                detail.getPlatformSkuId() +
                getPlatformPropertiesNameKey(detail.getPlatformPropertiesName()) +
                combo.getQty().stripTrailingZeros() +
                combo.getQty().stripTrailingZeros() +
                detail.getDisplayCustomInfo();
        return Md5Utils.md5(sb);
    }

    private String getPlatformPropertiesNameKey(String propertiesName){
        if (StringUtils.isEmpty(propertiesName)){
            return propertiesName;
        }
        if (!propertiesName.contains("_")){
            return propertiesName;
        }
        List<String> split = Arrays.asList(propertiesName.split("_")).stream().sorted().collect(Collectors.toList());
        return Md5Utils.md5(String.join("_",split));
    }


    private List<EshopSaleOrderDetail> rebuildDetailForUpdate(EshopSaleOrderEntity newOrder,
                                                              List<EshopSaleOrderDetail> newDetails,
                                                              List<EshopSaleOrderDetail> oldDetails) {
        List<EshopSaleOrderDetail> detailsForUpdate = new ArrayList<>();
        if (newDetails == null || oldDetails == null || oldDetails.size() == 0) {
            return detailsForUpdate;
        }
        for (EshopSaleOrderDetail detail : newDetails) {
            if (detail.getComboRowId().compareTo(BigInteger.ZERO) > 0) {
                //新的明细是餐行 先不处理
                continue;
            }
            EshopSaleOrderDetail oldDetail = oldDetails.get(0);
            //老的明细是套餐
            if (oldDetails.size() > 1 || oldDetail.getComboRowId().compareTo(BigInteger.ZERO) > 0) {
                //如果是多个明细 则原来是对应的套餐，就构建原来的套餐明细信息
                rebuildComboDetailForUpdate(newOrder, detail, oldDetails, newDetails);
                detailsForUpdate.addAll(oldDetails);
                continue;
            }
            //老的明细是单品
            doRebuildDetailForUpdate(newOrder, detail, oldDetail);
            detailsForUpdate.add(oldDetail);
        }
        return detailsForUpdate;
    }

    private List<EshopSaleOrderDetail> doRebuildComboDetailsForUpdate(EshopSaleOrderEntity newOrder, String onlineOid,
                                                                      List<EshopSaleOrderDetail> newDetails,
                                                                      List<EshopSaleDetailCombo> newComboRows,
                                                                      List<EshopSaleOrderDetail> oldDetails) {
        List<EshopSaleOrderDetail> tempDetails = new ArrayList<>();
        if (newComboRows == null || newComboRows.size() == 0 ||
                newDetails == null || newDetails.size() == 0 ||
                oldDetails == null || oldDetails.size() == 0) {
            return tempDetails;
        }
        Optional<EshopSaleDetailCombo> comboOptional = newComboRows.stream().filter(x -> x.getTradeOrderDetailId().equals(onlineOid)).findAny();
        if (!comboOptional.isPresent()) {
            return tempDetails;
        }
        //新套餐行
        EshopSaleDetailCombo combo = comboOptional.get();
        EshopSaleOrderDetail detail = combo.toOrderDetail();
        doRebuildDetailBaseInfo(newOrder, newDetails.get(0), detail, true);
        detail.setMappingState(newDetails.get(0).isMappingState());
        if (oldDetails.size() == 1) {
            //可以理解成把newDetails的第一个明细相关值赋值到detail的第一个明细上
            EshopSaleOrderDetail oldDetail = oldDetails.get(0);
            doRebuildDetailForUpdate(newOrder, detail, oldDetail);
            rebuildDetailFreight(oldDetail, newDetails.get(0));
            tempDetails.add(oldDetail);
            return tempDetails;
        }
        rebuildComboDetailForUpdate(newOrder, detail, oldDetails, newDetails);
        tempDetails.addAll(oldDetails);
        return tempDetails;
    }

    private List<EshopSaleDetailCombo> rebuildComboRowsForUpdate(String onlineOid, List<EshopSaleDetailCombo> oldCombos,
                                                                 List<EshopSaleOrderDetail> comboDetails) {
        List<EshopSaleDetailCombo> modifyCombos = new ArrayList<>();
        if (oldCombos == null || oldCombos.size() == 0 || comboDetails == null || comboDetails.size() == 0) {
            return modifyCombos;
        }
        Optional<EshopSaleDetailCombo> comboOptional = oldCombos.stream().filter(x -> x.getTradeOrderDetailId().equals(onlineOid)).findAny();
        if (!comboOptional.isPresent()) {
            return modifyCombos;
        }
        EshopSaleDetailCombo combo = comboOptional.get();
        combo.setTradeTotal(comboDetails.stream().map(EshopSaleOrderDetail::getTradeTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        BigDecimal total = comboDetails.stream().map(EshopSaleOrderDetail::getTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        combo.setTotal(total);
        combo.setDiscount(BigDecimal.ONE);
        combo.setDisedInitialTotal(total);
        combo.setDisedInitialPrice(MoneyUtils.divide(combo.getDisedInitialTotal(), combo.getQty(), Money.Price));
        combo.setPreferentialTotal(comboDetails.stream().map(EshopSaleOrderDetail::getPreferentialTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        combo.setOrderPreferentialAllotTotal(comboDetails.stream().map(EshopSaleOrderDetail::getOrderPreferentialAllotTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        combo.setPtypePreferentialTotal(comboDetails.stream().map(EshopSaleOrderDetail::getPtypePreferentialTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        combo.setDisedTaxedTotal(comboDetails.stream().map(EshopSaleOrderDetail::getDisedTaxedTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        combo.setTaxTotal(comboDetails.stream().map(EshopSaleOrderDetail::getTaxTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        combo.setDisedTotal(MoneyUtils.subtract(combo.getDisedTaxedTotal(),combo.getTaxTotal(),Money.Total));
        combo.setMallDeductionFee(comboDetails.stream().map(EshopSaleOrderDetail::getExtend).map(EshopSaleOrderDetailExtend::getMallDeductionFee)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        combo.setMallDeductionRate(BigDecimal.ZERO.compareTo(combo.getDisedTaxedTotal()) == 0 ? BigDecimal.ZERO :
                MoneyUtils.divide(combo.getMallDeductionFee(), combo.getDisedTaxedTotal(), Money.Tax));

        BigDecimal val = MoneyUtils.subtract(combo.getDisedTaxedTotal(), combo.getTaxTotal(), Money.Tax);
        if (val.compareTo(BigDecimal.ZERO) > 0) {
            combo.setTaxRate(MoneyUtils.divide(combo.getTaxTotal().multiply(BigDecimal.valueOf(100)), val, Money.Tax));
        }

        if (combo.getQty().compareTo(BigDecimal.ZERO) > 0) {
            combo.setTradePrice(MoneyUtils.divide(combo.getTradeTotal(), combo.getQty(), Money.Price));
            combo.setPrice(MoneyUtils.divide(combo.getTotal(), combo.getQty(), Money.Price));
            combo.setDisedInitialPrice(MoneyUtils.divide(combo.getDisedInitialTotal(), combo.getQty(), Money.Price));
            combo.setDisedTaxedPrice(MoneyUtils.divide(combo.getDisedTaxedTotal(), combo.getQty(), Money.Price));
            combo.setDisedPrice(MoneyUtils.divide(combo.getDisedTotal(), combo.getQty(), Money.Price));
        }
        combo.setPtypeServiceFee(comboDetails.stream().map(EshopSaleOrderDetail::getPtypeServiceFee)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        combo.setPlatformOrderPreferentialTotal(comboDetails.stream().map(EshopSaleOrderDetail::getPlatformOrderPreferentialTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        combo.setPlatformPtypePreferentialTotal(comboDetails.stream().map(EshopSaleOrderDetail::getPlatformPtypePreferentialTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        combo.setAnchorOrderPreferentialTotal(comboDetails.stream().map(EshopSaleOrderDetail::getExtend).map(EshopSaleOrderDetailExtend::getAnchorOrderPreferentialTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        combo.setAnchorPtypePreferentialTotal(comboDetails.stream().map(EshopSaleOrderDetail::getExtend).map(EshopSaleOrderDetailExtend::getAnchorPtypePreferentialTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        combo.setPlatformPtypeSubsidyTotal(comboDetails.stream().map(EshopSaleOrderDetail::getExtend).map(EshopSaleOrderDetailExtend::getPlatformPtypeSubsidyTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        combo.setPlatformOrderSubsidyTotal(comboDetails.stream().map(EshopSaleOrderDetail::getExtend).map(EshopSaleOrderDetailExtend::getPlatformOrderSubsidyTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        combo.setNationalSubsidyTotal(comboDetails.stream().map(EshopSaleOrderDetail::getExtend).map(EshopSaleOrderDetailExtend::getNationalSubsidyTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        if (null != comboDetails.get(0).getDistribution() && null != comboDetails.get(0).getDistribution().getEshopOrderId()) {
            BigDecimal distrTotal = comboDetails.stream().map(EshopSaleOrderDetail::getDistribution).
                    map(EshopSaleOrderDetailDistribution::getBuyerTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
            combo.getDistribution().setBuyerTotal(distrTotal);

            BigDecimal disedInitialTotal = comboDetails.stream().map(EshopSaleOrderDetail::getDistribution).
                    map(EshopSaleOrderDetailDistribution::getBuyerDisedInitialTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
            combo.getDistribution().setBuyerDiscount(BigDecimal.ONE);
            combo.getDistribution().setBuyerDisedInitialTotal(disedInitialTotal);

            BigDecimal preferentialTotal = comboDetails.stream().map(EshopSaleOrderDetail::getDistribution).
                    map(EshopSaleOrderDetailDistribution::getBuyerPtypePreferentialTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
            combo.getDistribution().setBuyerPtypePreferentialTotal(preferentialTotal);
            BigDecimal preferentialAllotTotal = comboDetails.stream().map(EshopSaleOrderDetail::getDistribution).
                    map(EshopSaleOrderDetailDistribution::getBuyerOrderPreferentialAllotTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
            combo.getDistribution().setBuyerOrderPreferentialAllotTotal(preferentialAllotTotal);

            BigDecimal buyerDisedTaxedTotal = comboDetails.stream().map(EshopSaleOrderDetail::getDistribution).
                    map(EshopSaleOrderDetailDistribution::getBuyerDisedTaxedTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
            combo.getDistribution().setBuyerDisedTaxedTotal(buyerDisedTaxedTotal);

            BigDecimal buyerTaxTotal = comboDetails.stream().map(EshopSaleOrderDetail::getDistribution).
                    map(EshopSaleOrderDetailDistribution::getBuyerTaxTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
            combo.getDistribution().setBuyerTaxTotal(buyerTaxTotal);
            BigDecimal camboVal = MoneyUtils.subtract(combo.getDistribution().getBuyerDisedTaxedTotal(),
                    combo.getDistribution().getBuyerTaxTotal(), Money.Tax);
            if (camboVal.compareTo(BigDecimal.ZERO) > 0) {
                combo.getDistribution().setBuyerTaxRate(MoneyUtils.divide(combo.getDistribution().getBuyerTaxTotal().
                        multiply(BigDecimal.valueOf(100)), camboVal, Money.Tax));
            }
            BigDecimal buyerDisedTotal = comboDetails.stream().map(EshopSaleOrderDetail::getDistribution).
                    map(EshopSaleOrderDetailDistribution::getBuyerDisedTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
            combo.getDistribution().setBuyerDisedTotal(buyerDisedTotal);
            if (combo.getQty().compareTo(BigDecimal.ZERO) > 0) {
                combo.getDistribution().setBuyerPrice(MoneyUtils.divide(distrTotal, combo.getQty(), Money.Price));
                combo.getDistribution().setBuyerDisedInitialPrice(MoneyUtils.divide(disedInitialTotal, combo.getQty(), Money.Price));
                combo.getDistribution().setBuyerDisedTaxedPrice(MoneyUtils.divide(buyerDisedTaxedTotal, combo.getQty(), Money.Price));
                combo.getDistribution().setBuyerDisedPrice(MoneyUtils.divide(buyerDisedTotal, combo.getQty(), Money.Price));
            }
        }
        modifyCombos.add(combo);
        return modifyCombos;
    }

    private void rebuildComboDetailForUpdate(EshopSaleOrderEntity newOrder, EshopSaleOrderDetail detail,
                                             List<EshopSaleOrderDetail> collectDetails, List<EshopSaleOrderDetail> newDetails) {
        EshopSaleOrderDetail shareDetail = JsonUtils.toObject(JsonUtils.toJson(detail), EshopSaleOrderDetail.class);
        Map<BigInteger, EshopSaleOrderDetail> hasMatchedOldDetail = new HashMap<>();
        collectDetails.stream().sorted(Comparator.comparing(EshopSaleOrderDetail::getDisedTaxedTotal));
        newDetails.stream().sorted(Comparator.comparing(EshopSaleOrderDetail::getDisedTaxedTotal));
        //这里为了解决套餐明细的存在相同sku的情况，导致匹配的时候都是匹配到第一条明细上的
        //将老套餐明细赋值到新套餐明细
        for (EshopSaleOrderDetail oldDetail : collectDetails) {
            //如果新明细没有对应关系，那么应该直接获取老明细的数据，并且将状态等赋值
            if (!detail.isMappingState()) {
                doRebuildDetailBaseInfo(newOrder, detail, oldDetail, false);
                relationService.calculateComboPrice(shareDetail, detail, oldDetail, oldDetail.getComboShareScale());
                continue;
            }
            if (CollectionUtils.isNotEmpty(newDetails)) {
                List<EshopSaleOrderDetail> matchOldDetails = newDetails.stream().filter(n -> {
                    return (n.getPtypeId().compareTo(oldDetail.getPtypeId()) == 0) &&
                            (n.getSkuId().compareTo(oldDetail.getSkuId()) == 0) &&
                            (n.getUnit().compareTo(oldDetail.getUnit()) == 0) &&
                            (n.getExtend().isGift() == oldDetail.getExtend().isGift());
                }).collect(Collectors.toList());
                //通过ptypeID+SKUiD+unitId匹配到了套餐明细
                if (CollectionUtils.isNotEmpty(matchOldDetails)) {
                    //已匹配的map中为空
                    if (CollectionUtils.isEmpty(hasMatchedOldDetail)) {
                        //直接取匹配到的第一条
                        doRebuildDetailBaseInfo(newOrder, matchOldDetails.get(0), oldDetail, false);
                        oldDetail.setQty(matchOldDetails.get(0).getQty());
                        oldDetail.setUnitQty(matchOldDetails.get(0).getUnitQty());
                        oldDetail.setTaxRate(matchOldDetails.get(0).getTaxRate());
                        rebuildDetailExtend(oldDetail, matchOldDetails.get(0));
                        rebuildDetailLiveBroadcast(oldDetail, matchOldDetails.get(0));
                        rebuildDetailTiming(oldDetail, matchOldDetails.get(0));
                        rebuildDetailBatch(oldDetail, matchOldDetails.get(0));
                        rebuildDetailFreight(oldDetail, matchOldDetails.get(0));
                        //将匹配到的id放入已匹配的map
                        hasMatchedOldDetail.put(matchOldDetails.get(0).getId(), matchOldDetails.get(0));
                    } else {
                        //循环寻找到的套擦明细
                        for (EshopSaleOrderDetail matchOldDetail : matchOldDetails) {
                            //套餐明细的id在mqp中能找到，说明是已经经过匹配的，需要舍弃
                            if (hasMatchedOldDetail.containsKey(matchOldDetail.getId())) {
                                continue;
                            }
                            doRebuildDetailBaseInfo(newOrder, matchOldDetail, oldDetail, false);
                            oldDetail.setQty(matchOldDetail.getQty());
                            oldDetail.setUnitQty(matchOldDetail.getUnitQty());
                            oldDetail.setTaxRate(matchOldDetail.getTaxRate());
                            rebuildDetailExtend(oldDetail, matchOldDetail);
                            rebuildDetailLiveBroadcast(oldDetail, matchOldDetail);
                            rebuildDetailTiming(oldDetail, matchOldDetail);
                            rebuildDetailBatch(oldDetail, matchOldDetail);
                            rebuildDetailFreight(oldDetail, matchOldDetail);
                            //将匹配到的id放入已匹配的map
                            hasMatchedOldDetail.put(matchOldDetail.getId(), matchOldDetail);
                            break;
                        }
                    }
                }
            }
            relationService.calculateComboPrice(shareDetail, detail, oldDetail, oldDetail.getComboShareScale());
        }
        relationService.calculateComboPriceAdd(collectDetails, shareDetail);
    }

    private static void rebuildDetailLiveBroadcast(EshopSaleOrderDetail oldDetail, EshopSaleOrderDetail first) {
        EshopSaleOrderDetailLiveBroadcast liveBroadcast = first.getLiveBroadcast();
        if (null != liveBroadcast && liveBroadcast.getDetailId().compareTo(BigInteger.ZERO) != 0) {
            EshopSaleOrderDetailLiveBroadcast newDetailLiveBroadcast = JsonUtils.toObject(JsonUtils.toJson(liveBroadcast), EshopSaleOrderDetailLiveBroadcast.class);
            newDetailLiveBroadcast.setDetailId(oldDetail.getId());
            oldDetail.setLiveBroadcast(newDetailLiveBroadcast);
        }
    }

    private static void rebuildDetailBatch(EshopSaleOrderDetail oldDetail, EshopSaleOrderDetail first) {
        EshopSaleOrderDetailBatch batch = first.getBatch();
        if (null != batch && batch.getEshopOrderDetailId().compareTo(BigInteger.ZERO) != 0) {
            EshopSaleOrderDetailBatch newDetailBatch = JsonUtils.toObject(JsonUtils.toJson(batch), EshopSaleOrderDetailBatch.class);
            newDetailBatch.setId(UId.newId());
            newDetailBatch.setEshopOrderDetailId(oldDetail.getId());
            oldDetail.setBatch(newDetailBatch);
        }
    }

    private static void rebuildDetailFreight(EshopSaleOrderDetail oldDetail, EshopSaleOrderDetail first) {
        List<EshopOrderDetailFreight> freights = first.getFreightList();
        if (CollectionUtils.isNotEmpty(freights)) {
            List<EshopOrderDetailFreight> newFreights = JsonUtils.toList(JsonUtils.toJson(freights), EshopOrderDetailFreight.class);
            for (EshopOrderDetailFreight newFreight : newFreights) {
                newFreight.setId(UId.newId());
            }
            oldDetail.setFreightList(newFreights);
        }
    }

    private static void rebuildDetailTiming(EshopSaleOrderDetail oldDetail, EshopSaleOrderDetail first) {
        EshopSaleOrderDetailTiming timing = first.getTiming();
        if (null != timing && timing.getEshopOrderDetailId().compareTo(BigInteger.ZERO) != 0) {
            EshopSaleOrderDetailTiming newDetailTiming = JsonUtils.toObject(JsonUtils.toJson(timing), EshopSaleOrderDetailTiming.class);
            newDetailTiming.setId(UId.newId());
            newDetailTiming.setEshopOrderDetailId(oldDetail.getId());
            oldDetail.setTiming(newDetailTiming);
        }
    }

    private static void rebuildDetailExtend(EshopSaleOrderDetail oldDetail, EshopSaleOrderDetail first) {
        EshopSaleOrderDetailExtend extend = first.getExtend();
        if (null != extend && extend.getDetailId().compareTo(BigInteger.ZERO) != 0) {
            EshopSaleOrderDetailExtend newDetailExtend = JsonUtils.toObject(JsonUtils.toJson(extend), EshopSaleOrderDetailExtend.class);
            newDetailExtend.setId(UId.newId());
            newDetailExtend.setDetailId(oldDetail.getId());
            newDetailExtend.setOtypeId(oldDetail.getOtypeId());
            oldDetail.setExtend(newDetailExtend);
        }
    }

    //detail : 新套餐行转明细之后的detail
    //oldDetail：老明细
    private void doRebuildDetailForUpdate(EshopSaleOrderEntity newOrder, EshopSaleOrderDetail detail, EshopSaleOrderDetail oldDetail) {
        doRebuildDetailBaseInfo(newOrder, detail, oldDetail, true);
        oldDetail.setTradeTotal(detail.getTradeTotal());
        oldDetail.setTradePrice(MoneyUtils.divide(oldDetail.getTradeTotal(), oldDetail.getUnitQty(), Money.Price));
        oldDetail.setPlatformOrderPreferentialTotal(detail.getPlatformOrderPreferentialTotal());
        oldDetail.setPlatformPtypePreferentialTotal(detail.getPlatformPtypePreferentialTotal());
        oldDetail.setPtypeServiceFee(detail.getPtypeServiceFee());
        oldDetail.setBuyerFreightFee(detail.getBuyerFreightFee());

        oldDetail.setTotal(detail.getTotal());
        oldDetail.setPrice(MoneyUtils.divide(oldDetail.getTotal(), oldDetail.getUnitQty(), Money.Price));

        oldDetail.setDiscount(detail.getDiscount());
        oldDetail.setDisedInitialTotal(detail.getDisedInitialTotal());
        oldDetail.setDisedInitialPrice(detail.getDisedInitialPrice());

        oldDetail.setPreferentialTotal(detail.getPreferentialTotal());
        oldDetail.setPtypePreferentialTotal(detail.getPtypePreferentialTotal());
        oldDetail.setOrderPreferentialAllotTotal(detail.getOrderPreferentialAllotTotal());
        oldDetail.setDisedTaxedTotal(detail.getDisedTaxedTotal());
        oldDetail.setDisedTaxedPrice(MoneyUtils.divide(oldDetail.getDisedTaxedTotal(), oldDetail.getUnitQty(), Money.Price));

        oldDetail.setDisedTotal(MoneyUtils.subtract(oldDetail.getDisedTaxedTotal(), oldDetail.getTaxTotal(), Money.Total));
        oldDetail.setDisedPrice(MoneyUtils.divide(oldDetail.getDisedTotal(), oldDetail.getUnitQty(), Money.Price));
        oldDetail.setPtypeCommissionTotal(detail.getPtypeCommissionTotal());
        if (null != detail.getDistribution() && null != detail.getDistribution().getEshopOrderId()) {
            oldDetail.getDistribution().setBuyerTotal(detail.getDistribution().getBuyerTotal());
            oldDetail.getDistribution().setBuyerPrice(detail.getDistribution().getBuyerPrice());
            oldDetail.getDistribution().setBuyerDiscount(detail.getDistribution().getBuyerDiscount());
            oldDetail.getDistribution().setBuyerDisedInitialTotal(detail.getDistribution().getBuyerDisedInitialTotal());
            oldDetail.getDistribution().setBuyerDisedInitialPrice(detail.getDistribution().getBuyerDisedInitialPrice());
            oldDetail.getDistribution().setBuyerOrderPreferentialAllotTotal(detail.getDistribution()
                    .getBuyerOrderPreferentialAllotTotal());
            oldDetail.getDistribution().setBuyerPtypePreferentialTotal(detail.getDistribution()
                    .getBuyerPtypePreferentialTotal());
            oldDetail.getDistribution().setBuyerDisedTaxedTotal(detail.getDistribution().getBuyerDisedTaxedTotal());
            oldDetail.getDistribution().setBuyerDisedTaxedPrice(detail.getDistribution().getBuyerDisedTaxedPrice());
            oldDetail.getDistribution().setBuyerDisedTotal(detail.getDistribution().getBuyerDisedTotal());
            oldDetail.getDistribution().setBuyerDisedPrice(detail.getDistribution().getBuyerDisedPrice());
        }
        if (null != detail.getPurchase() && null != detail.getPurchase().getEshopOrderId()) {
            oldDetail.getPurchase().setPurchaseTotal(detail.getPurchase().getPurchaseTotal());
            oldDetail.getPurchase().setPurchasePrice(detail.getPurchase().getPurchasePrice());
            oldDetail.getPurchase().setPurchaseDiscount(detail.getPurchase().getPurchaseDiscount());
            oldDetail.getPurchase().setPurchaseDisedTaxedTotal(detail.getPurchase().getPurchaseDisedTaxedTotal());
            oldDetail.getPurchase().setPurchaseDisedTaxedPrice(detail.getPurchase().getPurchaseDisedTaxedPrice());
            oldDetail.getPurchase().setPurchaseDisedTotal(detail.getPurchase().getPurchaseDisedTotal());
            oldDetail.getPurchase().setPurchaseDisedPrice(detail.getPurchase().getPurchaseDisedPrice());
        }
        rebuildDetailExtend(oldDetail, detail);
        rebuildDetailLiveBroadcast(oldDetail, detail);
        rebuildDetailTiming(oldDetail, detail);
        rebuildDetailBatch(oldDetail, detail);
        rebuildDetailFreight(oldDetail, detail);
    }

    private void doRebuildDetailBaseInfo(EshopSaleOrderEntity newOrder, EshopSaleOrderDetail detail, EshopSaleOrderDetail oldDetail, boolean needQty) {
        oldDetail.setPlatformDetailTradeState(detail.getPlatformDetailTradeState());
        oldDetail.setLocalRefundState(detail.getLocalRefundState());
        oldDetail.setLocalRefundProcessState(detail.getLocalRefundProcessState());
        oldDetail.setPlatformPtypeId(detail.getPlatformPtypeId());
        oldDetail.setPlatformSkuId(detail.getPlatformSkuId());
        oldDetail.setPlatformPtypeXcode(detail.getPlatformPtypeXcode());
        oldDetail.setPlatformStockCode(detail.getPlatformStockCode());
        oldDetail.setPlatformStockId(detail.getPlatformStockId());
        oldDetail.setPlatformPtypeName(detail.getPlatformPtypeName());
        oldDetail.setPlatformPropertiesName(detail.getPlatformPropertiesName());
        oldDetail.setPlatformPtypePicUrl(detail.getPlatformPtypePicUrl());
        oldDetail.getExtend().setPlanSendTime(detail.getExtend().getPlanSendTime());
        oldDetail.setPlatformDetailTradeState(detail.getPlatformDetailTradeState());
        oldDetail.setDetailChangeTypes(detail.getDetailChangeTypes());
        if (DetailProcessState.NoSubmit.equals(oldDetail.getProcessState())) {
            oldDetail.setKtypeId(detail.getKtypeId());
        }
//        oldDetail.setKtypeId(detail.getKtypeId());
        if (needQty) {
            oldDetail.setQty(detail.getQty());
            oldDetail.setUnitQty(detail.getUnitQty());
//            oldDetail.setSubQty(detail.getSubQty());
        }
        oldDetail.setTaxTotal(detail.getTaxTotal());
        oldDetail.setTaxRate(detail.getTaxRate());
        oldDetail.setDisplayCustomInfo(detail.getDisplayCustomInfo());
        oldDetail.setNoShippingBookkeeping(detail.isNoShippingBookkeeping());
        rebuildDetailMark(oldDetail, detail.getOrderDetailMarks());
    }

    private void rebuildComboMark(EshopSaleDetailCombo detail, List<EshopOrderMarkEntity> comboMarks) {
        if (comboMarks == null || comboMarks.size() == 0) {
            return;
        }
        List<EshopOrderMarkEntity> remarks = new ArrayList<>();
        for (EshopOrderMarkEntity itemMark : comboMarks
        ) {
            itemMark.setMarkTarget(OrderMarkType.Detail);
            itemMark.setOrderId(detail.getEshopOrderId());
            itemMark.setDetailId(detail.getEshopOrderDetailComboRowId());
            remarks.add(itemMark);
        }
        detail.setMarks(remarks);
    }

    private void rebuildDetailMark(EshopSaleOrderDetail detail, List<EshopOrderMarkEntity> detailMarks) {
        List<EshopOrderMarkEntity> remarks = new ArrayList<>();
        for (EshopOrderMarkEntity mark : detailMarks) {
            if (mark.getMarkCode().compareTo(BigInteger.valueOf(BaseOrderMarkEnum.UN_RELATION.getCode())) == 0 ||
                    mark.getMarkCode().compareTo(BigInteger.valueOf(BaseOrderMarkEnum.CANT_SEND_GOODS.getCode())) == 0 ||
                    mark.getMarkCode().compareTo(BigInteger.valueOf(BaseOrderMarkEnum.FORWARD_SALE.getCode())) == 0 ||
                    mark.getMarkCode().compareTo(BigInteger.valueOf(BaseOrderMarkEnum.FORWARD_SALE_BY_STOCK.getCode())) == 0 ||
                    mark.getMarkCode().compareTo(BigInteger.valueOf(BaseOrderMarkEnum.SALE_PROXY.getCode())) == 0 ||
                    mark.getMarkCode().compareTo(BigInteger.valueOf(BaseOrderMarkEnum.NOTSEND_KEEPACCOUNT.getCode())) == 0) {
                //本地标记不进行更新
                continue;
            }
            EshopOrderMarkEntity itemMark = new EshopOrderMarkEntity();
            itemMark.setMarkTarget(OrderMarkType.Detail);
            itemMark.setOrderId(detail.getEshopOrderId());
            itemMark.setDetailId(detail.getId());
            itemMark.setId(UId.newId());
            itemMark.setProfileId(mark.getProfileId());
            itemMark.setOrderType(mark.getOrderType());
            itemMark.setMarkCode(mark.getMarkCode());
            itemMark.setBubble(mark.getBubble());
            itemMark.setShowType(mark.getShowType());
            itemMark.setReason(mark.getReason());
            itemMark.setMarkDataId(mark.getMarkDataId());
            itemMark.setBigData(mark.getBigData());
            //itemMark.setPlatformJson(mark.getPlatformJson());
            remarks.add(itemMark);
        }

        //本地标记重新计算
        if (!detail.isDeliverRequired()) {
            EShopOrderMarkUtil.doBuildAndAddToMarkList(remarks, detail.getEshopOrderId(), detail.getId(), detail.getProfileId(), BaseOrderMarkEnum.CANT_SEND_GOODS);
        }
        if (detail.isNoShippingBookkeeping()) {
            EShopOrderMarkUtil.doBuildAndAddToMarkList(remarks, detail.getEshopOrderId(), detail.getId(), detail.getProfileId(), BaseOrderMarkEnum.NOTSEND_KEEPACCOUNT);
        }
        if (!detail.isMappingState()) {
            EShopOrderMarkUtil.doBuildAndAddToMarkList(remarks, detail.getEshopOrderId(), detail.getId(), detail.getProfileId(), BaseOrderMarkEnum.UN_RELATION);
        }
        TradeTypeEnum tradeType = detail.getOrderSaleType();
        //【预售】标记状态 交易类型
        //时效预售
        //缺货预售
        if (tradeType.equals(TradeTypeEnum.ADVANCE_FORWARD_SALE)) {
            EShopOrderMarkUtil.doBuildAndAddToMarkList(remarks, detail.getEshopOrderId(), detail.getId(),
                    detail.getProfileId(), BaseOrderMarkEnum.FORWARD_SALE);
        }
        if (tradeType.equals(TradeTypeEnum.ADVANCE_FORWARD_SALE_BY_STOCK)) {
            EShopOrderMarkUtil.doBuildAndAddToMarkList(remarks, detail.getEshopOrderId(), detail.getId(),
                    detail.getProfileId(), BaseOrderMarkEnum.FORWARD_SALE_BY_STOCK);
        }
//        if (tradeType.equals(TradeTypeEnum.CYCLE_PURCHASE)) {
//            EShopOrderMarkUtil.doBuildAndAddToMarkList(remarks, detail.getEshopOrderId(), detail.getId(),
//                    detail.getProfileId(), BaseOrderMarkEnum.CYCLE_PURCHASE);
//        }
        //重算本地明细标记
        rebuildLocalDetailMarks(detail, remarks, BaseOrderMarkEnum.SALE_PROXY);
        rebuildLocalDetailMarks(detail, remarks, BaseOrderMarkEnum.CANT_SEND_GOODS);
        rebuildLocalDetailMarks(detail, remarks, BaseOrderMarkEnum.NOTSEND_KEEPACCOUNT);
        detail.setOrderDetailMarks(remarks);
    }

    private static void rebuildLocalDetailMarks(EshopSaleOrderDetail detail, List<EshopOrderMarkEntity> remarks, BaseOrderMarkEnum markEnum) {
        boolean hasMark = detail.getOrderDetailMarks().stream().anyMatch(m -> m.getMarkCode().intValue() == markEnum.getCode());
        if (hasMark) {
            EShopOrderMarkUtil.doBuildAndAddToMarkList(remarks, detail.getEshopOrderId(), detail.getId(),
                    detail.getProfileId(), markEnum);
        }
    }

    private void rebuildOrderMainMark(EshopSaleOrderEntity newOrder) {
        List<EshopOrderMarkEntity> remarks = new ArrayList<>();
        for (EshopOrderMarkEntity itemMark : newOrder.getOrderMarks()) {
            if (itemMark.getMarkCode().compareTo(BigInteger.valueOf(BaseOrderMarkEnum.UN_RELATION.getCode())) == 0 ||
                    itemMark.getMarkCode().compareTo(BigInteger.valueOf(BaseOrderMarkEnum.CANT_SEND_GOODS.getCode())) == 0 ||
                    itemMark.getMarkCode().compareTo(BigInteger.valueOf(BaseOrderMarkEnum.FORWARD_SALE.getCode())) == 0 ||
                    itemMark.getMarkCode().compareTo(BigInteger.valueOf(BaseOrderMarkEnum.NOTSEND_KEEPACCOUNT.getCode())) == 0 ||
                    itemMark.getMarkCode().compareTo(BigInteger.valueOf(BaseOrderMarkEnum.DELETED_LOGIC.getCode())) == 0 ||
                    itemMark.getMarkCode().compareTo(BigInteger.valueOf(BaseOrderMarkEnum.FORWARD_SALE_BY_STOCK.getCode())) == 0
            ) {
                continue;
            }
            itemMark.setOrderId(newOrder.getId());
            remarks.add(itemMark);
        }
        if (!newOrder.isOrderDeliverRequired()) {
            EShopOrderMarkUtil.doBuildAndAddToMarkList(remarks, newOrder.getId(), BigInteger.ZERO, newOrder.getProfileId(), BaseOrderMarkEnum.CANT_SEND_GOODS);
        }
        if (newOrder.getDeleted() == 1) {
            EShopOrderMarkUtil.doBuildAndAddToMarkList(remarks, newOrder.getId(), BigInteger.ZERO, newOrder.getProfileId(), BaseOrderMarkEnum.DELETED_LOGIC, "", "操作删除了的原始订单");
        }
        if (!newOrder.isMappingState()) {
            EShopOrderMarkUtil.doBuildAndAddToMarkList(remarks, newOrder.getId(), BigInteger.ZERO, newOrder.getProfileId(), BaseOrderMarkEnum.UN_RELATION);
        }
        TradeTypeEnum tradeType = newOrder.getOrderSaleType();
        if (tradeType.equals(TradeTypeEnum.ADVANCE_FORWARD_SALE) || tradeType.equals(TradeTypeEnum.ADVANCE_FORWARD_SALE_BY_STOCK)) {
            //明细全部都是【预售-按计划发】
            //主表标记【预售-按计划发】
            buildOrderMarkByAllDetail(newOrder, remarks, newOrder.getOrderDetails(), BaseOrderMarkEnum.FORWARD_SALE);
            //主表没有标记【预售-按计划发】
            //说明明细中含有【预售-有货就发】
            //一张订单存在两个预售标记时，【预售-有货就发】优先级高
            //主表标记【预售-有货就发】
            if (remarks.stream().noneMatch(m -> m.getMarkCode().compareTo(BigInteger.valueOf(BaseOrderMarkEnum.FORWARD_SALE.getCode())) == 0)) {
                EShopOrderMarkUtil.doBuildAndAddToMarkList(remarks, newOrder.getId(), BigInteger.ZERO, newOrder.getProfileId(), BaseOrderMarkEnum.FORWARD_SALE_BY_STOCK);
            }
        }
//        if (tradeType.equals(TradeTypeEnum.CYCLE_PURCHASE)) {
//            EShopOrderMarkUtil.doBuildAndAddToMarkList(remarks, newOrder.getId(), BigInteger.ZERO, newOrder.getProfileId(), BaseOrderMarkEnum.CYCLE_PURCHASE);
//        }
        buildOrderMarkByAllDetail(newOrder, remarks, newOrder.getOrderDetails(), BaseOrderMarkEnum.CANT_SEND_GOODS);
        buildOrderMarkByAllDetail(newOrder, remarks, newOrder.getOrderDetails(), BaseOrderMarkEnum.NOTSEND_KEEPACCOUNT);
        buildOrderMarkByAllDetail(newOrder, remarks, newOrder.getOrderDetails(), BaseOrderMarkEnum.ORDER_DETAIL_DELETE);
        buildOrderMarkByAllDetail(newOrder, remarks, newOrder.getOrderDetails(), BaseOrderMarkEnum.LOCKED_ORDER);
        newOrder.setOrderMarks(remarks);
    }

    private void rebuildMentionType(EshopSaleOrderEntity newOrder, EshopSaleOrderEntity localOrder) {
        List<EshopSaleOrderDetail> details = newOrder.getOrderDetails();
        if (details == null) {
            return;
        }
        boolean advance = details.stream().allMatch(x -> x.getOrderSaleType().compareTo(TradeTypeEnum.ADVANCE_FORWARD_SALE) == 0
                || x.getOrderSaleType().compareTo(TradeTypeEnum.ADVANCE_FORWARD_SALE_BY_STOCK) == 0)
                || newOrder.getOrderSaleType().equals(TradeTypeEnum.ADVANCE_FORWARD_SALE)
                || newOrder.getOrderSaleType().equals(TradeTypeEnum.ADVANCE_FORWARD_SALE_BY_STOCK);
        boolean cyclePurchase = details.stream().allMatch(x -> x.getOrderSaleType().compareTo(TradeTypeEnum.CYCLE_PURCHASE) == 0);
        //明细未对应，但是有【不发货不记账】，则应该认定为已对应的明细
        boolean has_mapping = details.stream().allMatch(d -> d.isMappingState() || !d.isDeliverRequired());
        boolean notSendGoods = details.stream().noneMatch(EshopSaleOrderDetail::isDeliverRequired);
        if (advance) {
            newOrder.setOrderSaleType(TradeTypeEnum.ADVANCE_FORWARD_SALE);
        }
        if (cyclePurchase) {
            newOrder.setOrderSaleType(TradeTypeEnum.CYCLE_PURCHASE);
        }
        newOrder.setMappingState(has_mapping);
        newOrder.setOrderDeliverRequired(!notSendGoods);
//        newOrder.getTiming().setPlanSendTime((localOrder.getTiming().getPlanSendTime() == null || newOrder.getOrderSaleType().equals(TradeTypeEnum.NORMAL)) ?
//                newOrder.getTiming().getPlanSendTime() : localOrder.getTiming().getPlanSendTime());
    }

    private List<EshopSaleOrderDetail> rebuildOrderDetailList(List<EshopSaleOrderDetail> orderDetails, EshopSaleOrderDetail localdetail,
                                                              EshopSaleOrderEntity newOrder, Map<BigInteger, BigInteger> detailIdsMap) {
        if (orderDetails == null) {
            return null;
        }
        if (localdetail == null) {
            return orderDetails;
        }
        for (EshopSaleOrderDetail detail : orderDetails) {
            detail.setEshopOrderId(newOrder.getId());
            if (localdetail != null && localdetail.getId().compareTo(BigInteger.ZERO) > 0
                    && detail.getComboRowId().compareTo(BigInteger.ZERO) == 0) {
                if (!detailIdsMap.values().contains(localdetail.getId())
                        ||  newOrder.getOrderMarks().stream().noneMatch(m->m.getMarkCode().compareTo(BigInteger.valueOf(BaseOrderMarkEnum.FULL_SEND.getCode())) == 0)){
                    detail.setId(localdetail.getId());
                }
            }
            detailIdsMap.put(detail.getId(), localdetail.getId());
            if (detail.getExtend() != null) {
                detail.getExtend().setEshopOrderId(detail.getEshopOrderId());
                detail.getExtend().setDetailId(detail.getId());
                detail.setProcessState(
                        localdetail == null ? DetailProcessState.NoSubmit :
                                localdetail.getProcessState());
            }
            //明细标记
            List<EshopOrderMarkEntity> detailMarks = detail.getOrderDetailMarks();
            if (detailMarks != null && detailMarks.size() > 0) {
                rebuildDetailMark(detail, detailMarks);
            }
            //采购实体
            if (detail.getDistribution() != null && null != detail.getDistribution().getEshopOrderId()) {
                detail.getDistribution().setEshopOrderId(detail.getEshopOrderId());
                detail.getDistribution().setEshopOrderDetailId(detail.getId());
            }
            //分销实体
            if (detail.getPurchase() != null && null != detail.getPurchase().getEshopOrderId()) {
                detail.getPurchase().setEshopOrderId(detail.getEshopOrderId());
                detail.getPurchase().setEshopOrderDetailId(detail.getId());
            }
            //直播实体
            if (detail.getLiveBroadcast() != null && null != detail.getLiveBroadcast().getEshopOrderId()) {
                detail.getLiveBroadcast().setEshopOrderId(detail.getEshopOrderId());
                detail.getLiveBroadcast().setDetailId(detail.getId());
            }
            //直播实体
            if (detail.getTiming() != null && null != detail.getTiming().getEshopOrderId()) {
                detail.getTiming().setEshopOrderId(detail.getEshopOrderId());
                detail.getTiming().setEshopOrderDetailId(detail.getId());
            }
            //批次实体
            if (detail.getBatch() != null && null != detail.getBatch().getEshopOrderId()) {
                detail.getBatch().setEshopOrderId(detail.getEshopOrderId());
                detail.getBatch().setEshopOrderDetailId(detail.getId());
            }
        }
        return orderDetails;
    }

    private EshopSaleDetailCombo rebuildOrderComboRows(String onlineOid, List<EshopSaleDetailCombo> newCombos, EshopSaleOrderDetail detail) {
        if (newCombos == null || newCombos.size() == 0 || detail.getComboRowId().compareTo(BigInteger.ZERO) == 0) {
            return null;
        }
        Optional<EshopSaleDetailCombo> comboOptional = newCombos.stream().filter(x -> x.getTradeOrderDetailId().equals(onlineOid)).findAny();
        if (!comboOptional.isPresent()) {
            return null;
        }
        EshopSaleDetailCombo combo = comboOptional.get();
        combo.setEshopOrderId(detail.getEshopOrderId());
        combo.setEshopOrderDetailComboRowId(detail.getComboRowId());
        if (null != combo.getDistribution() && null != combo.getDistribution().getEshopOrderId()) {
            combo.getDistribution().setEshopOrderId(detail.getEshopOrderId());
            combo.getDistribution().setEshopOrderComboRowId(detail.getComboRowId());
        }
        if (null != combo.getPurchase() && null != combo.getPurchase().getEshopOrderId()) {
            combo.getPurchase().setEshopOrderId(detail.getEshopOrderId());
            combo.getPurchase().setEshopOrderDetailId(detail.getComboRowId());
        }
        List<EshopOrderMarkEntity> comboMarks = combo.getMarks();
        rebuildComboMark(combo, comboMarks);
        return combo;
    }

    private void buildOrderExtend(EshopOrderEntity apiOrder, EshopSaleOrderEntity orderEntity) {
        EshopSaleOrderExtendEntity extend = orderEntity.getExtend();
        if (null == extend) {
            extend = new EshopSaleOrderExtendEntity(orderEntity);
        }
        extend.setInstallationServiceProvider(apiOrder.getInstallationServiceProvider());
        extend.setCollectCustomer(apiOrder.getCollectCustomer());
//        extend.setCollectTime(apiOrder.getCollectTime() == null ? extend.getDefaultCollectTime() : apiOrder.getCollectTime());
        extend.setActivityNo(apiOrder.getActivityNo());
        extend.setActivityName(apiOrder.getActivityName());
        extend.setActivityType(apiOrder.getActivityType());
        extend.setPlatformOperateType(apiOrder.getPlatformOperateType());
        extend.setPaymentMode(apiOrder.getPaymentMode());
        // 团长名
        extend.setGroupHeaderName(apiOrder.getGroupHeaderName());
        extend.setFlowChannel(apiOrder.getFlowChannel());
        buildOrderExtendHoldTime(apiOrder, extend);
        extend.setPlatformRequired(apiOrder.getPlatformRequired());
        extend.setPlatformJson(apiOrder.getMainPlatformJsonEntity() == null ? "" : JsonUtils.toJson(apiOrder.getMainPlatformJsonEntity()));
        extend.setPlatformSendTime(orderEntity.getTiming().getPlatformSendTime());
        extend.setTradeSummaryMd5(apiOrder.getHashMark());
        extend.setBuyerUniqueMark(Md5Utils.md5(apiOrder.getReceiverInfo().getUniqueMark()));
        extend.setAdvanceTotal(MoneyUtils.round(apiOrder.getAdvanceTotal(),Money.Total));
        if (null != apiOrder.getDeliveryInfo()) {
            extend.setPickupCode(apiOrder.getDeliveryInfo().getPickupCode());
            extend.setPlatformDispatcherName(CommonUtil.substringStr(apiOrder.getDeliveryInfo().getCourierName(), 40));
            extend.setPlatformDispatherMobile(CommonUtil.substringStr(apiOrder.getDeliveryInfo().getCourierPhone(), 40));
            extend.setTakeGoodsCode(apiOrder.getDeliveryInfo().getReceiverPickCode());
        }
        extend.setLogisticsStatus(apiOrder.getLogisticsStatus());
        extend.setConfirmStatus(apiOrder.getConfirmStatus());
        buildOrderExtendQcResult(orderEntity, extend);
        buildOrderExtendIndetifyResult(orderEntity, extend);
        extend.setSellerFlagMemo(apiOrder.getSellerFlagMemo());
        extend.setGroupTitle(apiOrder.getGroupTitle());
        extend.setGroupNo(apiOrder.getGroupNo());
        extend.setPlatformHashOrderMain(apiOrder.getPlatformHashOrderMain());
        extend.setPlatformHashOrderDetail(apiOrder.getPlatformHashOrderDetail());
        extend.setPlatformHashMarkList(apiOrder.getPlatformHashMarkList());
        extend.setPlatformQualityOrgId(apiOrder.getPlatformQualityOrgId());
        extend.setPlatformQualityOrgName(apiOrder.getPlatformQualityOrgName());
        extend.setPlatformQualityWarehouseCode(apiOrder.getPlatformQualityWarehouseCode());
        extend.setPlatformQualityWarehouseName(apiOrder.getPlatformQualityWarehouseName());
        extend.setIsDraft(0);
        extend.setSalePeriodNum(apiOrder.getSalePeriodNum());
        extend.setCurrentPeriodNum(apiOrder.getCurrentPeriodNum());
        extend.setNationalSubsidyTotal(apiOrder.getNationalSubsidyTotal());
        orderEntity.setExtend(extend);
    }

    private static void buildOrderExtendHoldTime(EshopOrderEntity apiOrder, EshopSaleOrderExtendEntity extend) {
        AutoSubmitConfig config = GlobalConfig.get(AutoSubmitConfig.class);
        if (apiOrder.isNeedHoldOrder()) {
            Date holdDate = new Date();
            Calendar calendar = Calendar.getInstance();
            if (null == apiOrder.getHoldToDate()) {
                calendar.setTime(apiOrder.getPayTime() == null ? apiOrder.getCreateTime() : apiOrder.getPayTime());
            } else {
                calendar.setTime(apiOrder.getHoldToDate());
            }
            calendar.set(Calendar.MINUTE, calendar.get(Calendar.MINUTE) + config.getHoldTime());
            extend.setHoldTime(calendar.getTime());
        } else {
            extend.setHoldTime(new Date());
        }
    }

    private void buildOrderDistributionBuyer(EshopOrderEntity apiOrder, EshopSaleOrderEntity orderEntity) {
        EshopSaleOrderDistributionBuyer distributionBuyer = orderEntity.getDistribution();
        if (null == distributionBuyer) {
            distributionBuyer = new EshopSaleOrderDistributionBuyer();
        }
        distributionBuyer.setProfileId(orderEntity.getProfileId());
        distributionBuyer.setEshopOrderId(orderEntity.getId());
        distributionBuyer.setDistributionBuyerTradeId(apiOrder.getDistributionBuyerTradeId());
        distributionBuyer.setCreateTime(new Date());
        distributionBuyer.setUpdateTime(new Date());
        orderEntity.setDistribution(distributionBuyer);
    }

    private static void buildOrderExtendQcResult(EshopSaleOrderEntity orderEntity, EshopSaleOrderExtendEntity extend) {
        extend.setPlatformQcResult(QcResultType.NONE);
        //当明细有一个为质检未通过，则整单质检为未通过
        boolean failsToPassQualityInspection = orderEntity.getOrderDetails().stream().anyMatch(d -> QcResultType.FailsToPassQualityInspection.equals(d.getPlatformQcResult()));
        if (failsToPassQualityInspection) {
            extend.setPlatformQcResult(QcResultType.FailsToPassQualityInspection);
            return;
        }
        //当明细有一个为质检有瑕疵，则整单为质检有瑕疵
        boolean thereAreFlaws = orderEntity.getOrderDetails().stream().anyMatch(d -> QcResultType.ThereAreFlaws.equals(d.getPlatformQcResult()));
        if (thereAreFlaws) {
            extend.setPlatformQcResult(QcResultType.ThereAreFlaws);
            return;
        }
        //当明细有一个未质检，则主单为未质检
        boolean noQualityInspection = orderEntity.getOrderDetails().stream().anyMatch(d -> QcResultType.NoQualityInspection.equals(d.getPlatformQcResult()));
        if (noQualityInspection) {
            extend.setPlatformQcResult(QcResultType.NoQualityInspection);
            return;
        }
        //全部质检通过，才显示质检通过
        boolean qualityInspectionPassed = orderEntity.getOrderDetails().stream().allMatch(d -> QcResultType.QualityInspectionPassed.equals(d.getPlatformQcResult()));
        if (qualityInspectionPassed) {
            extend.setPlatformQcResult(QcResultType.QualityInspectionPassed);
            return;
        }
    }

    private static void buildOrderExtendIndetifyResult(EshopSaleOrderEntity orderEntity, EshopSaleOrderExtendEntity extend) {
        extend.setPlatformIdentifyResult(IdentifyResultType.NONE);
        //当明细有一个为为假，则整单鉴定为假
        boolean falseIdentify = orderEntity.getOrderDetails().stream().anyMatch(d -> IdentifyResultType.False.equals(d.getPlatformIdentifyResult()));
        if (falseIdentify) {
            extend.setPlatformIdentifyResult(IdentifyResultType.False);
            return;
        }
        //当明细有一个鉴定有瑕疵，则主单鉴定有瑕疵
        boolean thereAreFlaws = orderEntity.getOrderDetails().stream().anyMatch(d -> IdentifyResultType.ThereAreFlaws.equals(d.getPlatformIdentifyResult()));
        if (thereAreFlaws) {
            extend.setPlatformIdentifyResult(IdentifyResultType.ThereAreFlaws);
            return;
        }
        //当明细有一个未鉴定，则主单为未鉴定
        boolean notIdentified = orderEntity.getOrderDetails().stream().anyMatch(d -> IdentifyResultType.NotIdentified.equals(d.getPlatformIdentifyResult()));
        if (notIdentified) {
            extend.setPlatformIdentifyResult(IdentifyResultType.NotIdentified);
            return;
        }
        //全部鉴定为真，才显示鉴定为真
        boolean really = orderEntity.getOrderDetails().stream().anyMatch(d -> IdentifyResultType.Really.equals(d.getPlatformIdentifyResult()));
        if (really) {
            extend.setPlatformIdentifyResult(IdentifyResultType.Really);
            return;
        }
    }

    private void buildOrderDetailExtend(EshopOrderDetailEntity apiOrderDetail, EshopSaleOrderDetail orderDetail, List<ProductGiftRelation> giftRelationList) {
        EshopSaleOrderDetailExtend extend = new EshopSaleOrderDetailExtend(orderDetail);
        extend.setId(UId.newId());
        extend.setActivityNo(apiOrderDetail.getActivityNo());
        extend.setActivityName(apiOrderDetail.getActivityName());
        extend.setActivityType(apiOrderDetail.getActivityType());
//        extend.setPlatformOperateType(apiOrderDetail.getPlatformOperateType());
        extend.setPlatformOperateType(apiOrderDetail.getPlatformOperateType());
        extend.setAttributeType(apiOrderDetail.getAttributeType());
        extend.setMallDeductionFee(MoneyUtils.round(apiOrderDetail.getMallFee(), Money.Total));
        extend.setPlatformBatchno(apiOrderDetail.getBatchno());
        extend.setGift(checkDetailPlatformGift(orderDetail, giftRelationList) ||
                null == orderDetail.getDisedTaxedTotal() || BigDecimal.ZERO.compareTo(orderDetail.getDisedTaxedTotal()) == 0);
        extend.setNationalSubsidyTotal(apiOrderDetail.getNationalSubsidyTotal());
        orderDetail.setExtend(extend);
    }

    public void doShareBuyerFreightFee(@NotNull EshopSaleOrderEntity order) {
        BigDecimal buyerFreightFee = order.getOrderBuyerFreightFee();
        if (buyerFreightFee.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        List<EshopSaleOrderDetail> details = getNeedShareBuyerFreightFeeDetail(order);
        if (details == null || details.size() == 0) {
            return;
        }
        BigDecimal taxedTotal = details.stream().map(EshopSaleOrderDetail::getTotal).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (taxedTotal.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        BigDecimal currentBuyerFreightFee = buyerFreightFee;

        for (EshopSaleOrderDetail detail : details) {
            if (currentBuyerFreightFee.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            BigDecimal percent = MoneyUtils.divide(detail.getTotal(), taxedTotal, Money.Price);
            BigDecimal shared = MoneyUtils.multiply(buyerFreightFee, percent, Money.Total);
            detail.setBuyerFreightFee(shared);
            currentBuyerFreightFee = MoneyUtils.subtract(currentBuyerFreightFee, shared, Money.Total);
        }
        //误差二次分摊
        if (currentBuyerFreightFee.doubleValue() != 0) {
            EshopSaleOrderDetail firstDetail = details.get(0);
            BigDecimal adjustBuyerFreightFee = MoneyUtils.add(firstDetail.getBuyerFreightFee(), currentBuyerFreightFee, Money.Total);
            firstDetail.setBuyerFreightFee(adjustBuyerFreightFee);
        }
        buildComboRowBuyerFreightFee(order);
    }

    private void buildComboRowBuyerFreightFee(EshopSaleOrderEntity order) {
        List<EshopSaleDetailCombo> comboRows = order.getDetailCombos();
        if (comboRows == null || comboRows.size() == 0) {
            return;
        }
        List<EshopSaleOrderDetail> details = order.getOrderDetails();
        if (details == null || details.size() == 0) {
            return;
        }
        Map<BigInteger, List<EshopSaleOrderDetail>> comboDetailsMap = details.stream().filter(x -> x.getComboRowId().compareTo(BigInteger.ZERO) > 0).collect(Collectors.groupingBy(EshopSaleOrderDetail::getComboRowId));

        comboRows.forEach(x -> {
            BigInteger comboRowId = x.getEshopOrderDetailComboRowId();
            if (!comboDetailsMap.containsKey(comboRowId)) {
                return;
            }
            BigDecimal buyerFreightFee = comboDetailsMap.get(comboRowId).stream().map(EshopSaleOrderDetail::getBuyerFreightFee)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            x.setBuyerFreightFee(buyerFreightFee);
        });
    }

    private List<EshopSaleOrderDetail> getNeedShareBuyerFreightFeeDetail(EshopSaleOrderEntity order) {
        List<EshopSaleOrderDetail> details = order.getOrderDetails();
        if (details == null || details.size() == 0) {
            return details;
        }
        List<EshopSaleOrderDetail> retDetails = new ArrayList<>(details);
        if (retDetails.size() == 0) {
            details.get(0).setBuyerFreightFee(order.getOrderBuyerFreightFee());
        }
        return retDetails;
    }


    /**
     * 分摊套餐明细金额
     *
     * @param details   套餐明细
     * @param total     套餐行金额，如果不传，默认取第一行明细金额
     * @param getMethod 需要分摊的字段get方法
     * @param setMethod 需要分摊的字段set方法
     */
    public void doShareComBoDetail(List<EshopSaleOrderDetail> details, BigDecimal total, String getMethod, String setMethod) {
        if (StringUtils.isEmpty(getMethod) || StringUtils.isEmpty(setMethod) || null == details || details.size() == 0) {
            return;
        }
        try {
            //已分摊金额
            final BigDecimal[] hasSharedTotal = {BigDecimal.ZERO};
            //分摊字段
            Method getM = EshopSaleOrderDetail.class.getMethod(getMethod);
            Method setM = EshopSaleOrderDetail.class.getMethod(setMethod, BigDecimal.class);
            //总金额
            for (EshopSaleOrderDetail detail : details) {
                if (detail.getComboRowId().compareTo(BigInteger.ZERO) == 0 || detail.getComboShareScale().compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                try {
                    if (null == total || total.compareTo(BigDecimal.ZERO) == 0) {
                        //套餐行金额为空，取明细金额
                        total = (BigDecimal) getM.invoke(detail);
                    }
                    if (null == total || total.compareTo(BigDecimal.ZERO) == 0) {
                        return;
                    }
                    BigDecimal val = MoneyUtils.divide(total.multiply(detail.getComboShareScale()), 100, Money.Total);
                    setM.invoke(detail, val);
                    hasSharedTotal[0] = hasSharedTotal[0].add(val);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
            if (null == total || total.compareTo(BigDecimal.ZERO) == 0) {
                return;
            }
            //误差消除
            BigDecimal subtract = total.subtract(hasSharedTotal[0]);
            if (subtract.compareTo(BigDecimal.ZERO) != 0) {
                Optional<EshopSaleOrderDetail> first = details.stream().filter(o -> {
                    try {
                        return ((BigDecimal) getM.invoke(o)).add(subtract).compareTo(BigDecimal.ZERO) > 0 && o.getComboShareScale().compareTo(BigDecimal.ZERO) != 0;
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }).findFirst();
                if (first.isPresent()) {
                    EshopSaleOrderDetail detail = first.get();
                    setM.invoke(detail, ((BigDecimal) getM.invoke(detail)).add(subtract));
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(String.format("套餐明细分摊金额错误，订单id：%s，方法名：%s", details.get(0).getEshopOrderId(), getMethod));
        }
    }

    public void doShareComBoDetail(List<Object> details, BigDecimal total, String getMethod, String setMethod, BigDecimal comboShareScale, Class clazz) {
        if (StringUtils.isEmpty(getMethod) || StringUtils.isEmpty(setMethod) ||
                null == details || details.size() == 0 ||
                BigDecimal.ZERO.compareTo(comboShareScale) == 0) {
            return;
        }
        try {
            //已分摊金额
            final BigDecimal[] hasSharedTotal = {BigDecimal.ZERO};
            //分摊字段
            Method getM = clazz.getMethod(getMethod);
            Method setM = clazz.getMethod(setMethod, BigDecimal.class);
            //总金额
            for (Object detail : details) {
                try {
                    if (null == total || total.compareTo(BigDecimal.ZERO) == 0) {
                        return;
                    }
                    BigDecimal val = MoneyUtils.divide(total.multiply(comboShareScale), 100, Money.Total);
                    setM.invoke(detail, val);
                    hasSharedTotal[0] = hasSharedTotal[0].add(val);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
            if (null == total || total.compareTo(BigDecimal.ZERO) == 0) {
                return;
            }
            //误差消除
            BigDecimal subtract = total.subtract(hasSharedTotal[0]);
            if (subtract.compareTo(BigDecimal.ZERO) != 0) {
                Optional<Object> first = details.stream().filter(o -> {
                    try {
                        return ((BigDecimal) getM.invoke(o)).add(subtract).compareTo(BigDecimal.ZERO) > 0 && comboShareScale.compareTo(BigDecimal.ZERO) != 0;
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }).findFirst();
                if (first.isPresent()) {
                    Object detail = first.get();
                    setM.invoke(detail, ((BigDecimal) getM.invoke(detail)).add(subtract));
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(String.format("套餐明细分摊金额错误，方法名：%s", getMethod));
        }
    }

    public void doBuildBatchInfoByRule(EshopSaleOrderEntity order) {
        try {
            List<EshopSaleOrderDetail> orderDetails = order.getOrderDetails();
            if (CollectionUtils.isEmpty(orderDetails)) {
                return;
            }
            List<BigInteger> ruleIds = getRuleIdListByDetails(orderDetails);
            if (CollectionUtils.isEmpty(ruleIds)) {
                return;
            }
            StockRuleService ruleService = GetBeanUtil.getBean(StockRuleService.class);
            QueryStockRuleParameter parameter = new QueryStockRuleParameter();
            parameter.setRuleIds(ruleIds);
            List<StockSyncRule> ruleList = ruleService.queryRuleWithDetails(parameter);
            if (CollectionUtils.isEmpty(ruleList)) {
                return;
            }
            doBuildRuleSaleQty(ruleService, ruleList);
            Map<BigInteger, List<StockSyncRule>> listMap = ruleList.stream().collect(Collectors.groupingBy(StockSyncRuleBase::getId));
            for (EshopSaleOrderDetail detail : orderDetails) {
                if (null != detail.getBatch() && StringUtils.isNotEmpty(detail.getBatch().getBatchno())) {
                    continue;
                }
                BigInteger ruleId = detail.getStockSyncRuleId();
                if (!listMap.containsKey(ruleId)) {
                    continue;
                }
                List<StockSyncRule> rules = listMap.get(ruleId);
                if (CollectionUtils.isEmpty(rules)) {
                    continue;
                }
                StockSyncRule rule = rules.get(0);
                List<StockRuleDetail> ruleDetailList = rule.getStockRuleDetailList();
                if (CollectionUtils.isEmpty(ruleDetailList)) {
                    continue;
                }
                if (!rule.getRuleType().equals(StockRuleType.FrozenStock)) {
                    List<StockRuleDetail> batchDetails = ruleDetailList.stream().filter(x -> StringUtils.isNotEmpty(x.getBatchInfoString())).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(batchDetails)) {
                        continue;
                    }
                    StockRuleDetail ruleDetail = batchDetails.get(0);
                    if (batchDetails.size() > 1) {
                        SecureRandom random = SecureRandom.getInstanceStrong();
                        int batchIndex = random.nextInt(batchDetails.size() - 1);
                        ruleDetail = batchDetails.get(batchIndex);
                    }
                    doFilterDetailByRuleDetail(detail, ruleDetail);
                    continue;
                }
                if (doBuildByOldRecord(order, detail, rule)) {
                    continue;
                }
                doCheckRuleLock(detail, ruleDetailList);
            }
            doBuildOrderKtypeByDetail(order);
        } catch (Exception ex) {
            logger.error("账套{}订单{}根据规则构建批次信息报错{}", order.getProfileId(), order.getTradeOrderId(), ex.getMessage(), ex);
        }
    }

    private boolean doBuildByOldRecord(EshopSaleOrderEntity order, EshopSaleOrderDetail detail, StockSyncRule rule) {
        StockRuleMapper ruleMapper = GetBeanUtil.getBean(StockRuleMapper.class);
        QueryRecordDetailParam parameter = new QueryRecordDetailParam();
        parameter.setTradeId(order.getTradeOrderId());
        parameter.setRuleId(rule.getId());
        List<StockRecordOrderDetail> recordDetails = ruleMapper.queryRecordDetails(parameter);
        if (CollectionUtils.isEmpty(recordDetails)) {
            return false;
        }
        StockRecordOrderDetail oldRecord = recordDetails.get(0);
        List<StockRuleDetail> ruleDetailList = rule.getStockRuleDetailList();
        Optional<StockRuleDetail> first = ruleDetailList.stream().filter(x -> x.getKtypeId().equals(oldRecord.getKtypeId())).findFirst();
        if (!first.isPresent()) {
            return false;
        }
        StockRuleDetail ruleDetail = first.get();
        doFilterDetailByRuleDetail(detail, ruleDetail);
        return true;
    }

    private void doFilterDetailByRuleDetail(EshopSaleOrderDetail detail, StockRuleDetail ruleDetail) {
        EshopSaleOrderDetailBatch detailBatch = new EshopSaleOrderDetailBatch();
        detailBatch.setId(UId.newId());
        detailBatch.setEshopOrderId(detail.getEshopOrderId());
        detailBatch.setEshopOrderDetailId(detail.getId());
        detailBatch.setProfileId(detail.getProfileId());
        detailBatch.setBatchno(ruleDetail.getBatchno());
        detailBatch.setProduceDate(ruleDetail.getProduceDate());
        detailBatch.setExpireDate(ruleDetail.getExpireDate());
        detailBatch.setQty(detail.getQty());
        detailBatch.setSubQty(detail.getSubQty());
        detailBatch.setUnitQty(detail.getUnitQty());
        detailBatch.setBatchPrice(ruleDetail.getBatchPrice());
        detailBatch.setCreateTime(new Date());
        detailBatch.setUpdateTime(new Date());
        detail.setBatch(detailBatch);
    }

    private void doCheckRuleLock(EshopSaleOrderDetail detail, List<StockRuleDetail> ruleDetailList) {
        List<StockRuleDetail> sortedList = ruleDetailList.stream().sorted(Comparator.comparing(StockRuleDetail::getBatchInfoString).reversed()).collect(Collectors.toList());
        doCheckByLockDetails(detail, sortedList);
    }

    private void doCheckByLockDetails(EshopSaleOrderDetail detail, List<StockRuleDetail> ruleDetailList) {
        for (StockRuleDetail ruleDetail : ruleDetailList) {
            boolean cacheLockRecord = getCacheLockRecord(detail, ruleDetail);
            if (cacheLockRecord) {
                doFilterDetailByRuleDetail(detail, ruleDetail);
                break;
            }
        }
    }

    private boolean getCacheLockRecord(EshopSaleOrderDetail detail, StockRuleDetail ruleDetail) {
        BigDecimal saleQty = ruleDetail.getFrozenSaleQty();
        String redisKey = String.format("%s_%s_%s", KeyWordConstant.ORDER_BUILD_PRE_KEY, ruleDetail.getRuleId(), ruleDetail.getKtypeId());
        RedisPoolFactory redisPoolFactory = GetBeanUtil.getBean(RedisPoolFactory.class);
        StringRedisTemplate template = redisPoolFactory.getTemplate(CacheType.BIZ.getKey());
        String oldVal = template.opsForValue().get(redisKey);
        BigDecimal cacheRecord = BigDecimal.ZERO;
        if (StringUtils.isNotEmpty(oldVal)) {
            cacheRecord = new BigDecimal(oldVal);
        }
        BigDecimal newRecord = cacheRecord.add(detail.getQty());
        boolean hasFullStock = saleQty.compareTo(newRecord) >= 0;
        if (hasFullStock) {
            template.opsForValue().set(redisKey, newRecord.toString(), 10, TimeUnit.MINUTES);
        }
        return hasFullStock;
    }

    private void doBuildRuleSaleQty(StockRuleService ruleService, List<StockSyncRule> ruleList) {
        for (StockSyncRule rule : ruleList) {
            if (!rule.getRuleType().equals(StockRuleType.FrozenStock)) {
                continue;
            }
            if (rule.getPcategory().equals(Pcategory.Combo)) {
                ruleService.buildComboLockQty(rule);
                continue;
            }
            ruleService.buildSkuLockQty(rule);
        }
    }

    private List<BigInteger> getRuleIdListByDetails(List<EshopSaleOrderDetail> orderDetails) {
        List<BigInteger> ruleIds = new ArrayList<>();
        for (EshopSaleOrderDetail detail : orderDetails) {
            if (detail.getSkuId().compareTo(BigInteger.ZERO) == 0) {
                continue;
            }
            if (detail.getStockSyncRuleId().compareTo(BigInteger.ZERO) == 0) {
                continue;
            }
            if (detail.getStockSyncRuleId().compareTo(BigInteger.TEN) == 0) {
                continue;
            }
            ruleIds.add(detail.getStockSyncRuleId());
        }
        return ruleIds;
    }

    private void doBuildOrderKtypeByDetail(EshopSaleOrderEntity order) {
        List<EshopSaleOrderDetail> orderDetails = order.getOrderDetails();
        if (CollectionUtils.isEmpty(orderDetails)) {
            return;
        }
        BigInteger ktypeId = order.getKtypeId();
        Optional<EshopSaleOrderDetail> first = orderDetails.stream()
                .filter(x -> x.getKtypeId().equals(ktypeId) && (StringUtils.isNotEmpty(x.getBatchno()) || x.getProduceDate() != null || x.getExpireDate() != null))
                .findFirst();
        if (first.isPresent()) {
            doFilterNoneKtypeDetails(order);
            return;
        }
        Optional<EshopSaleOrderDetail> optional = orderDetails.stream().filter(x -> x.getKtypeId().compareTo(BigInteger.ZERO) > 0).findFirst();
        if (optional.isPresent()) {
            BigInteger firstDetailKtypeId = optional.get().getKtypeId();
            order.setKtypeId(firstDetailKtypeId);
        }
        doFilterNoneKtypeDetails(order);
    }

    private void doFilterNoneKtypeDetails(EshopSaleOrderEntity order) {
        List<EshopSaleOrderDetail> orderDetails = order.getOrderDetails();
        for (EshopSaleOrderDetail detail : orderDetails) {
            if (detail.getKtypeId().compareTo(BigInteger.ZERO) > 0) {
                continue;
            }
            detail.setKtypeId(order.getKtypeId());
        }
    }

    private void buildBtypeExtendInfo(EshopOrderEntity apiOrder, EshopSaleOrderEntity orderEntity) {
        try {
            if (CollectionUtils.isEmpty(apiOrder.getMarkDataList()) || null == orderEntity.getBtypeId() || orderEntity.getBtypeId().compareTo(BigInteger.ZERO) == 0) {
                return;
            }
            Optional<MarkData> markDataOpt = apiOrder.getMarkDataList().stream().filter(x -> x.getOrderMarkEnum().equals(BaseOrderMarkEnum.YBM_HIDE_TAG)).findFirst();
            if (!markDataOpt.isPresent()) {
                return;
            }
            if (null == markDataOpt.get().getBigData()) {
                return;
            }
            QualificationInformation bigData = (QualificationInformation) markDataOpt.get().getBigData();
            Map<String, Object> info = JsonUtils.toHashMap(JsonUtils.toJson(bigData));
            List<BtypeExtendFieldRequestDto> list = CommonUtil.convertToExtendInfoList(null, info, orderEntity.getBtypeId());
            if (CollectionUtils.isEmpty(list)) {
                logger.error("账套id：{}，网店id：{}，往来单位扩展信息：{} 往来单位扩展信息保存失败：扩展信息list为空", CurrentUser.getProfileId(), orderEntity.getOtypeId(), markDataOpt.get().getBigDataJson());
                return;
            }
            GeneralResult<List<String>> result = baseApi.batchSaveBtypeExtendFieldInfo(list);
            if (result != null && org.apache.commons.collections4.CollectionUtils.isNotEmpty(result.getData())) {
                logger.error("账套id：{}，网店id：{}，往来单位扩展信息：{} 往来单位扩展信息保存失败：{}", CurrentUser.getProfileId(), orderEntity.getOtypeId(), JsonUtils.toJson(list), String.join(",", result.getData()));
            }
        } catch (RuntimeException ex) {
            logger.error(String.format("构建往来单位扩展信息出错 profileid:%s  tradeId:%s , message:%s", CurrentUser.getProfileId(),
                    apiOrder.getTradeId(), ex.getMessage()), ex);
        }
    }
}

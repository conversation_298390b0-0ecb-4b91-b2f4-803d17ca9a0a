Type.registerNamespace('sale.product');

sale.product.markAction = function () {
    sale.product.markAction.initializeBase(this);
};

sale.product.markAction.prototype = {
    context: function (cb) {
        var form = this.get_form();
        var pageParams = this.get_pageParams();
        var data = pageParams.rowDataList;
        if (!data || data.length == 0) {
            $common.showError('请选择需要打标的网店商品');
            form.doCancel();
            return;
        }
        var markEditEnable = $saleUtils.getPower('eshoporder.product.mark.edit');
        if (!markEditEnable) {
            $common.showError('请先配置打标权限');
            form.doCancel();
            return;
        }
        cb({
            isSingle: data.length == 1
        });
    },
    initialize: function markAction$initialize() {
        sale.product.markAction.callBaseMethod(this, 'initialize');
        var form = this.get_form();
        form.fpRdAdvance.set_enabled(false);
        var pageParams = this.get_pageParams();
        var data = pageParams.rowDataList[0];
        if (!data.markList || data.markList.length == 0) {
            return;
        }
        var serialTypeList = [];
        for (var i = 0; i < data.markList.length; i++) {
            var mark = data.markList[i];
            if (!mark.markCode) {
                continue;
            }
            var serialValues = [1000, 1001, 1002, 1003, 1004, 1005, 1006, 1010, 1017];
            if (mark.markCode == 1000) {
                //商家预售
                form.fpRdAdvance.set_enabled(true);
                form.chkPre.set_checked(true);
                var bigData = mark.productMarkBigDataEntity;
                if (bigData) {
                    if (bigData.sendAfterPaiedDays) {
                        data.sendAfterPaiedDays = bigData.sendAfterPaiedDays;
                        form.sendType.set_value(0);
                        form.txtInDate.set_value(data.sendAfterPaiedDays);
                    } else if (bigData.sendOnDate) {
                        data.sendOnDate = bigData.sendOnDate;
                        form.sendType.set_value(1);
                        form.deOnTime.set_value(data.sendOnDate);
                    }
                    this.doSendTypeChanged();
                }
            } else if (mark.markCode == 1001 || mark.markCode == 1002) {
                //1001不发货不记账
                //1002不走发货流程只记账
                form.chkSend.set_checked(true);
                form.sendOrKeepAccounts_0.set_enabled(true);
                form.sendOrKeepAccounts_1.set_enabled(true);
                if (mark.markCode == 1001) {
                    form.sendOrKeepAccounts_0.set_checked(true);
                } else if (mark.markCode == 1002) {
                    form.sendOrKeepAccounts_1.set_checked(true);
                }
            } else {
                for (var j = 0; j < serialValues.length; j++) {
                    if (mark.markCode == serialValues[j]) {
                        serialTypeList.push(serialValues[j]);
                        break;
                    }
                }
            }
        }
        if (serialTypeList.length > 0) {
            form.chkSn.set_checked(true);
            form.chkSnList.set_enabled(true);
            form.chkSnList.set_value(serialTypeList);
        }
    },
    doChkPreChanged: function (sender) {
        var form = this.get_form();
        var value = sender.get_checked();
        form.fpRdAdvance.set_enabled(value);
    },
    doSendTypeChanged: function () {
        var form = this.get_form();
        var value = form.sendType.get_value();
        if (value == null) {
            value = 0;
        }
        form.txtInDate.set_visible(value != 1);
        form.info.set_visible(value != 1);
        form.deOnTime.set_visible(value == 1);
    },
    doChkSendChanged: function (sender) {
        var form = this.get_form();
        var value = sender.get_checked();
        form.sendOrKeepAccounts_0.set_enabled(value);
        form.sendOrKeepAccounts_1.set_enabled(value);
    },
    doChkSnChanged: function (sender) {
        var form = this.get_form();
        var value = sender.get_checked();
        form.chkSnList.set_enabled(value);
    },
    doSave: function (sender) {
        var form = this.get_form();
        var saveData = form.saveData();
        var startObj = $saleUtils._getStartTiming('网店->网店商品管理->打标');

        //预售
        saveData.advance = form.chkPre.get_checked();
        if (saveData.advance) {
            if (saveData.sendType == 0) {
                if (!saveData.sendAfterPaiedDays || saveData.sendAfterPaiedDays < 1) {
                    $common.showError('请配置最晚发货时间');
                    return;
                }
            } else {
                if (!saveData.sendOnDate) {
                    $common.showError('请配置最晚发货时间');
                    return;
                }
            }
        } else {
            saveData.sendAfterPaiedDays = 0;
            saveData.sendOnDate = null;
        }
        //发货
        if (form.chkSend.get_checked()) {
            var sendConfigRadio = form.sendOrKeepAccounts_0.get_selected();
            if (sendConfigRadio) {
                saveData.sendOrKeepAccounts = sendConfigRadio.get_tag();
            } else {
                $common.showError('请配置发货/记账方式');
                return;
            }
        } else {
            saveData.sendOrKeepAccounts = null;
        }
        //sn
        if (!form.chkSn.get_checked()) {
            saveData.serialType = [];
        }

        //覆盖追加
        var modifyType = form.chkModify_0.get_selected();
        if (modifyType) {
            saveData.modifyType = modifyType.get_tag();
        } else {
            saveData.modifyType = 1;
        }

        var skuList = this.get_pageParams().rowDataList;
        var params = {
            markConfig: saveData,
            skuList: skuList,
            async: skuList.length > 100
        };
        var url = "/sale/eshoporder/product/saveProductMark";
        var response = $common.ajaxSync({
            url: url,
            data: params,
            type: 'post',
            router: 'ngp'
        });

        if (response.code != 200) {
            $common.showError(response.message);
            return;
        }

        if (params.async) {
            var pageParams = {};
            pageParams.tittle = '打标';
            pageParams.processId = response.data;
            pageParams.startObj = startObj;
            pageParams.tipsType = 'mark';
            pageParams.data = skuList;
            var popForm = new Sys.UI.Form(sender, 0, 0);
            popForm.add_closed(function (pop) {
                form.doOk();
            });
            popForm.showModal("/sale/eshoporder/product/process.gspx", pageParams);
        } else {
            //不开启进度框,提示后关闭弹窗
            $saleUtils._logEndTiming(startObj);
            $common.showOk("商品打标成功");
            form.doOk();
        }

    },
    dispose: function () {
        sale.product.markAction.callBaseMethod(this, 'dispose');
    }
};
sale.product.markAction.registerClass('sale.product.markAction', Sys.UI.PageAction);

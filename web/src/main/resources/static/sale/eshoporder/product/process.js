Type.registerNamespace('sale.product');

sale.product.processAction = function () {
    sale.product.processAction.initializeBase(this);
};

sale.product.processAction.prototype = {
    _activeTimer: null,
    _isStop: false,
    _isDownloaded: false,

    context: function (cb) {
        var params = this.get_pageParams();
        var tittle = params.tittle;
        cb({
            tittle: tittle
        });
    },
    initialize: function processAction$initialize() {
        sale.product.processAction.callBaseMethod(this, 'initialize');
        var params = this.get_pageParams();
        if (params && params.data && params.data.length > 0) {
            this.startProgress();
        } else if (params && params.createPtypeParams) {
            this.startProgress();
        }
    },
    startProgress: function () {
        var params = this.get_pageParams();
        var startObj = params.startObj
            ? params.startObj
            : $saleUtils._getStartTiming('网店->网店商品管理->' + params.tittle);
        var form = this.get_form();
        if (params.tipsType && "uploadxcode" === params.tipsType) {
            this._doUpdateXcode(form, startObj);
        } else if (params.tipsType && "createPtype" === params.tipsType) {
            this._doCreatePtype(form, startObj);
        } else if (params.tipsType && "mark" === params.tipsType) {
            this._doMark(form, startObj);
        } else if (params.tipsType && "stockSync" === params.tipsType) {
            this._doStockSync(form);
        }
    },
    _doCreatePtype: function (form, startObj) {
        var params = this.get_pageParams();
        var createPtypeParams = params.createPtypeParams;
        var totalSku = params.totalSku + " SKU商品";
        form.lbAll.set_text(totalSku);
        form.lbSuccess.set_text("0 SKU商品");
        form.lbFail.set_text("0 SKU商品");
        var processResult = $common.ajaxSync({
            url: "sale/eshoporder/product/createPtype",
            data: createPtypeParams,
            type: "post",
            router: 'ngp'
        });
        this._isStop = false;
        this._isDownloaded = true;
        this._activeTimer = new Sys.Timer();
        this._activeTimer.set_interval(500);
        this._activeTimer.set_enabled(true);
        var _this = this;
        this._activeTimer.add_tick(Function.createDelegate(this, function (timeData) {
            _this.getMessage(processResult, startObj);
        }));
        form.result = true;
        form.result = this._isDownloaded;
    },
    _doUpdateXcode: function (form, startObj) {
        var service = this.get_service();
        var params = this.get_pageParams();
        var _this = this;
        var totalSku = (params.data ? params.data.length : 0) + " SKU";
        for (var i = 0; i < params.data.length; i++) {
            params.data[i].selected = 1;
        }
        form.lbAll.set_text(totalSku);
        form.lbSuccess.set_text("0 SKU");
        form.lbFail.set_text("0 SKU");
        service.post("/sale/eshoporder/product/generateProductXcode", params, function (procesResult) {
            _this._isStop = false;
            _this._isDownloaded = true;
            _this._activeTimer = new Sys.Timer();
            var intervalCount = 500;
            _this._activeTimer.set_interval(intervalCount);
            _this._activeTimer.set_enabled(true);
            _this._activeTimer.add_tick(Function.createDelegate(this, function (timeData) {
                _this.getMessage(procesResult, startObj);
            }));
            form.result = true;
            form.result = _this._isDownloaded;
        });
    },

    getMessage: function (processId, startObj) {
        var form = this.get_form();
        if (null == form) {
            return;
        }
        var reqParam = new Object();
        reqParam.requestStr = processId.data;
        var _this = this;
        var service = this.get_service();
        service.post("sale/eshoporder/common/getProcessMsg", reqParam, function (res) {
            var obj = res.data;
            if (!obj) {
                return;
            }
            if (obj.message) {
                form.processMemo.set_text(obj.message);
            }

            if (obj.percent) {
                form.proBar.set_value(obj.percent);
            }
            var successCount = (obj.successCount ? obj.successCount : 0) + " SKU";
            var failedCount = (obj.failedCount ? obj.failedCount : 0) + " SKU";

            form.lbSuccess.set_text(successCount);
            form.lbFail.set_text(failedCount);
            if (obj.completed) { //已经结束
                if (_this._activeTimer) {
                    _this._activeTimer.set_enabled(false);
                }
                form.proBar.set_value(100);
                _this._isStop = true
                $saleUtils._logEndTiming(startObj)
            }
        });
    },
    doFormClose: function (sender, eventArgs) {
        var form = this.get_form();
        if (!this._isStop) {
            eventArgs.set_canClose(false);
            $common.confirm("操作尚未完成，是否关闭？", function (res) {
                if (!res) {
                    return;
                }
                form.close(true)
                this._isDownloaded = true;
                form.result = this._isDownloaded
            });
        }
    },
    _doMark: function (form, startObj) {
        var params = this.get_pageParams();
        var totalSku = (params.data ? params.data.length : 0) + " SKU";
        form.lbAll.set_text(totalSku);
        form.lbSuccess.set_text("0 SKU");
        form.lbFail.set_text("0 SKU");
        if (params && params.processId && params.processId.length > 0) {
            this.recurrentGetMessage(params.processId, startObj);
        }
    },

    _doStockSync: function (form) {
        var startObj = $saleUtils._getStartTiming('网店->网店商品管理->' + "手工同步库存");
        var service = this.get_service();
        var params = this.get_pageParams();
        var _this = this;
        form.lbAll.set_visible(false);
        form.lbSuccess.set_visible(false);
        form.lbFail.set_visible(false);
        service.post("/sale/eshoporder/stock/syncStockMulti", params.data, function (procesResult) {
            _this._isStop = false;
            _this._isDownloaded = true;
            _this._activeTimer = new Sys.Timer();
            var intervalCount = 500;
            _this._activeTimer.set_interval(intervalCount);
            _this._activeTimer.set_enabled(true);
            _this._activeTimer.add_tick(Function.createDelegate(this, function () {
                startObj.batchCount = params.data.length;
                _this.getMessage(procesResult, startObj);
            }));
            form.result = true;
            form.result = _this._isDownloaded;
        });
    },
    recurrentGetMessage: function (processId, startObj) {
        var form = this.get_form();
        var _this = this;
        this._isStop = false;
        this._isDownloaded = true;
        this._activeTimer = new Sys.Timer();
        var intervalCount = 500;
        this._activeTimer.set_interval(intervalCount);
        this._activeTimer.set_enabled(true);
        this._activeTimer.add_tick(Function.createDelegate(this, function (timeData) {
            _this.getMessage({data: processId}, startObj);
        }));
        form.result = true;
        form.result = this._isDownloaded;
    },

    doShowError: function () {
        //todo 之后的版本做这个功能，展示错误的列表记录
    },

    dispose: function () {
        sale.product.processAction.callBaseMethod(this, 'dispose');
    }
};
sale.product.processAction.registerClass('sale.product.processAction', Sys.UI.PageAction);

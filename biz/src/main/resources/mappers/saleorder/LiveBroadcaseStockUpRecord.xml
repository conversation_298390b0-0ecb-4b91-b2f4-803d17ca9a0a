<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.LiveBroadcastStockUpRecordMapper">
    <select id="query"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.broadcast.LiveBroadcastStockUpRecordEntity">
        select plbsur.*,
                    plbs.platform_session_name as platformSessionName,
                    plbs.id as liveBroadcastSessionId,
                    CONCAT(DATE_FORMAT(plbs.start_time, '%Y-%m-%d %H:%i:%S'),' 至 ',DATE_FORMAT(plbs.end_time, '%Y-%m-%d %H:%i:%S')) as liveTime,
                    be.fullname as etypeName
        from pl_live_broadcast_stock_up_record plbsur
        left join pl_live_broadcast_session plbs on plbs.profile_id = plbsur.profile_id and plbs.id=plbsur.live_broadcast_session_id and plbs.stopped=0
        left join base_etype be on be.profile_id = plbsur.profile_id and be.id=plbsur.etype_id and be.deleted=0 and be.stoped=0
        where plbsur.profile_id=#{profileId} and plbsur.deleted=0
        <if test="liveBroadcastId != '' and liveBroadcastId != null">
            and plbsur.live_broadcast_session_id = #{liveBroadcastId}
        </if>
        <if test="anchor != '' and anchor != null">
            and plbs.platform_anchor_name = #{anchor}
        </if>
        <if test="summary != '' and summary != null">
            and plbsur.summary like CONCAT('%',#{summary},'%')
        </if>
        <if test="recordCode != '' and recordCode != null">
            and plbsur.record_code like CONCAT('%',#{recordCode},'%')
        </if>
        <if test="id != '' and id != null">
            and plbsur.id = #{id}
        </if>
    </select>

    <select id="queryDetail"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.broadcast.LiveBroadcastStockUpRecordDetailEntity">
            select d.*,bp.fullname as ptypeName,(CASE WHEN bps.pic_url='' THEN pic.pic_url ELSE bps.pic_url END) pic_url,bp.usercode as ptypeCode,bp.standard as ptypeStandard,bp.ptype_type as ptypeType,
                unit.unit_name as unitName,ifnull(bar.fullbarcode,bp.barcode) as barcode,xcode.xcode as xcode
            from pl_live_broadcast_stock_up_record_detail d
            left join base_ptype bp on bp.profile_id=d.profile_id and d.ptype_id=bp.id
            left join base_ptype_sku bps on bps.profile_id=d.profile_id and bps.id=d.sku_id
            left join base_ptype_unit unit on unit.profile_id=d.profile_id and unit.id=d.unit_id
            left join base_ptype_xcode xcode on xcode.profile_id=d.profile_id and xcode.ptype_id=d.ptype_id
            left join base_ptype_pic pic on pic.profile_id = d.profile_id and d.ptype_id = pic.ptype_id and pic.rowindex = 1
            left join base_ptype_fullbarcode bar on bar.profile_id = d.profile_id and d.ptype_id = bar.ptype_id and bps.id=bar.sku_id and unit.id=bar.unit_id
            where d.profile_id=#{profileId} and d.record_id=#{recordId} GROUP BY  d.profile_id,d.id
    </select>

    <update id="delete">
            update pl_live_broadcast_stock_up_record set deleted = 1
            where profile_id = #{profileId} and id= #{id} and deleted = 0
    </update>

    <select id="queryTodayRecordNum" resultType="java.lang.String">
        select record_code
        from pl_live_broadcast_stock_up_record
        where profile_id=#{profileId} and  deleted=0 and create_time between #{startTime} and #{endTime} order by record_code desc limit 1
    </select>

    <select id="queryPtypeType" resultType="java.util.Map">
        select id,partypeid
        from base_ptype
        where profile_id=#{profileId}  and classed=0 and id =#{ptypeId}
    </select>

    <select id="queryPartypeById" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.Ptype">
        select id,fullname
        from base_ptype
        where profile_id=#{profileId}  and classed=1 and typeid=#{typeid}
    </select>

    <insert id="insertLiveBroadcastStockUpRecord">
        insert into pl_live_broadcast_stock_up_record(id, profile_id, record_code, live_broadcast_session_id, summary,
                                                      create_time, update_time, etype_id, deleted, qty,
                                                      ptype_category_count) VALUE (#{id},#{profileId},#{recordCode},#{liveBroadcastSessionId},#{summary},
                                                                                   #{createTime},#{updateTime},#{etypeId},#{deleted},#{qty},
                                                                                   #{ptypeCategoryCount})
    </insert>

    <insert id="insertLiveBroadcastStockUpRecordDetail">
        insert into pl_live_broadcast_stock_up_record_detail(record_id, id, ptype_id, sku_id, unit_id, platform_xcode,
                                                             create_time, update_time, print_status, qty, profile_id) VALUES
        <foreach item="item" index="index" collection="detailEntityList" separator=",">
            (#{item.recordId},
            #{item.id},
            #{item.ptypeId},
            #{item.skuId},
            #{item.unitId},
            #{item.platformXcode},
            #{item.createTime},
            #{item.updateTime},
            #{item.printStatus},
            #{item.qty},
            #{item.profileId}            )
        </foreach>
    </insert>

    <delete id="deleteLiveBroadcastStockUpRecord">
        delete from pl_live_broadcast_stock_up_record where profile_id=#{profileId} and id=#{id}
    </delete>

    <delete id="deleteLiveBroadcastStockUpRecordDetail">
        delete from pl_live_broadcast_stock_up_record_detail where profile_id=#{profileId} and record_id=#{recordId}
    </delete>

    <select id="checkPtypeHasDeleted" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.Ptype">
        select *
        from base_ptype
        where profile_id=#{profileId}  and  id =#{ptypeId};
    </select>
</mapper>
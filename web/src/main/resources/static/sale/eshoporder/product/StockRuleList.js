Type.registerNamespace('sale.product');

sale.product.StockRuleListAction = function () {
    sale.product.StockRuleListAction.initializeBase(this);
};

sale.product.StockRuleListAction.prototype = {
    context: function (cb) {

        var pageParams = this.get_pageParams();
        if (!pageParams) {
            pageParams = {};
        }

        var isEdit = false;
        if (pageParams && pageParams.mode) {
            var mode = pageParams.mode;
            if (mode && "edit" == mode) {
                isEdit = true;
            }
        }

        var title = "自定义库存同步规则选择";
        if (pageParams && pageParams.title && pageParams.title.length > 0) {
            title = pageParams.title;
        } else if (isEdit) {
            title = '自定义库存同步规则设置'
        }

        var ktypeList = pageParams.ktypeList;
        if (!ktypeList || ktypeList.length == 0) {
            var response = $common.ajaxSync({
                url: 'sale/eshoporder/basic/ktypeList',
                type: 'post',
                router: 'ngp'
            });
            ktypeList = response.data;
        }
        cb({
            title: title,
            ktypeList: ktypeList,
            isEdit: isEdit,
            cancelText: isEdit ? '关闭' : '取消'
        });
    },
    initialize: function StockRuleListAction$initialize() {
        sale.product.StockRuleListAction.callBaseMethod(this, 'initialize');
        this.get_form().ruleGrid.get_pager().refresh();
    },
    doBindPagerDataSource: function (path, params, binData) {
        var url = '/sale/eshoporder/product/queryCustomRule';
        params.queryParams = this.buildQueryParams(params);
        var startObj = $saleUtils._getStartTiming('网店->网店商品管理->自定义库存同步规则查询');
        var that = this;
        $common.ajax({
            url: url,
            data: params,
            router: 'ngp',
            waiting: '正在查询数据,请稍等',
            success: function (response) {
                $saleUtils._logEndTiming(startObj);
                if (response.code != 200) {
                    $common.alert(response.message);
                    binData({itemList: [], itemCount: 0});
                    return;
                }
                var data = response.data;
                if (!data || data.total == 0) {
                    binData({itemList: [], itemCount: 0});
                } else {
                    that._doFillKtypeNames(data.list);
                    that._rebuildFormula(data.list);
                    binData({itemList: data.list, itemCount: data.total});
                }
            }
        });
    },
    buildQueryParams: function (params) {
        var queryParams = {};
        if (!params || !params.queryParams || !params.queryParams.gridFilter) {
            return queryParams;
        }
        for (var i = 0; i < params.queryParams.gridFilter.length; i++) {
            var item = params.queryParams.gridFilter[i];
            var dataField = item.dataField;
            var dataValue = item.value;
            if (dataField === 'ruleName') {
                dataField = 'filterRuleName';
            }
            if ((!dataValue || dataValue == '') && false !== dataValue) {
                continue;
            }

            queryParams[dataField] = dataValue;
        }
        return queryParams;
    },
    _doFillKtypeNames: function (list) {
        var ktypeList = this.get_context("ktypeList");
        if (!ktypeList || !list || list.length == 0) {
            return;
        }
        for (var i = 0; i < list.length; i++) {
            var item = list[i];
            if (!item.ktypeIds || item.ktypeIds.length == 0) {
                continue;
            }
            var ktypeNames = "";
            for (var j = 0; j < ktypeList.length; j++) {
                if (item.ktypeIds.indexOf(ktypeList[j].id) > -1) {
                    ktypeNames += ktypeList[j].fullname + ",";
                }
            }
            if (ktypeNames.length > 0) {
                item.ktypeNames = ktypeNames.substr(0, ktypeNames.length - 1);
            }
        }
    },
    _rebuildFormula: function (list) {
        if (!list || list.length == 0) {
            return;
        }
        for (var i = 0; i < list.length; i++) {
            var item = list[i];
            if (!item.calculate) {
                continue;
            }
            item.formula = this.buildFormulaFromCalculate(item.calculate);
        }
    },
    buildFormulaFromCalculate: function (calculate) {
        if (!calculate) {
            return;
        }
        return "可销售库存*" + math.round(calculate.percentage, 2) + "%"
            + this._getMode(calculate.calculateMode) + math.round(calculate.ruleCalQty, 2);
    },
    _getMode: function (mode) {
        var result;
        switch (mode + "") {
            case "1":
                result = "-";
                break;
            case "2":
                result = "*";
                break;
            case "3":
                result = "/";
                break;
            default:
                result = "+";
                break;
        }
        return result;
    },
    doCellRender: function (sender, args) {

    },

    doDblClick: function (sender) {
        var form = sender.get_form();
        if (!this.get_context('isEdit')) {
            this.doRuleSelected(sender);
        } else {
            var rowData = form.ruleGrid.get_selectedRowData();
            this._showEditRuleForm(form, rowData, true);
        }
    },
    doChangeFilter: function (sender) {
        var grid = sender.get_form().ruleGrid;
        var allowed = grid.get_allowFilter();
        grid.set_allowFilter(!allowed);
    },
    doOperationClick: function (sender, args) {
        var form = sender.get_form();
        var rowData = form.ruleGrid.get_selectedRowData();
        if (args.get_buttonIndex() == 0) {
            this._showEditRuleForm(form, rowData, true);
        } else if (args.get_buttonIndex() == 1) {
            var _this = this;
            $common.confirm("确定要删除自定义库存同步规则" + rowData.ruleName + "吗？", function (isok) {
                if (!isok) {
                    return;
                }
                var startObj = $saleUtils._getStartTiming('网店->网店商品管理->删除自定义库存同步规则');
                var ruleIds = [];
                ruleIds.push(rowData.id);
                var url = '/sale/eshoporder/product/batchDeleteCustomRule';
                var param = {
                    ruleIdList: ruleIds
                };
                var response = $common.ajaxSync({
                    url: url,
                    type: 'post',
                    router: 'ngp',
                    data: param
                });
                $saleUtils._logEndTiming(startObj);
                if (response.code != 200) {
                    $common.showError(response.message);
                    return;
                }

                if (response.data && response.data.length > 0) {
                    $common.showError(response.data);
                    return;
                }
                $common.showOk("库存自定义规则删除成功");
                form.ruleGrid.get_pager().refresh();
            });

        }
    },
    doAddCustomRule: function (sender) {
        var form = sender.get_form();
        this._showEditRuleForm(form, null, false);
    },
    doRuleSelected: function (sender) {
        var form = this.get_form();
        var rootForm = this.get_root().get_form();
        if (rootForm && rootForm.get_action() && rootForm.get_action().doCustomRuleSelected) {
            var selectedRowData = form.ruleGrid.get_selectedRowData();
            rootForm.get_action().doCustomRuleSelected(selectedRowData);
        } else {
            form.doOk();
        }
    },
    doCancel: function (sender) {
        var form = sender.get_form();
        if (form.doCancel) {
            form.doCancel();
        } else {
            var rootForm = this.get_root().get_form();
            if (rootForm && rootForm.get_action() && rootForm.get_action().closePopup) {
                rootForm.get_action().closePopup();
            }
        }

    },
    _showEditRuleForm: function (form, rowData, isEdit) {
        var params = {
            title: isEdit ? '编辑自定义库存同步规则' : '新增自定义库存同步规则',
            ktypeList: this.get_context("ktypeList"),
            mode: isEdit ? 'custom_edit' : 'custom_add',
            rule: rowData
        };
        var modal = new Sys.UI.Form(form);
        modal.showModal("sale/eshoporder/product/EditRuleForm.gspx", params);
        modal.add_close(function (sender, args) {
            form.ruleGrid.get_pager().refresh();
        });
    },
    doRuleGridSelectionChanged: function (sender) {
        var form = sender.get_form();
        var grid = form.ruleGrid;
        var data = grid.get_selectedRowData();
        if (!data) {
            return;
        }
        var queryParams = {
            rowData: data,
            objectId: data.id
        }
        form.rule_log_ctrl.logGrid.get_pager().refresh(queryParams);
    },
    dispose: function () {
        sale.product.StockRuleListAction.callBaseMethod(this, 'dispose');
    }
};
sale.product.StockRuleListAction.registerClass('sale.product.StockRuleListAction', Sys.UI.PageAction);

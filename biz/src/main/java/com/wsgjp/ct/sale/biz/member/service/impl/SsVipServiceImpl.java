package com.wsgjp.ct.sale.biz.member.service.impl;

import bf.datasource.page.PageRequest;
import bf.datasource.page.PageResponse;
import bf.datasource.page.Sort;
import cn.hutool.core.date.DateTime;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wsgjp.ct.bill.core.handle.entity.enums.BillPostState;
import com.wsgjp.ct.framework.enums.AssertsChangeType;
import com.wsgjp.ct.framework.enums.AssertsSourceOperation;
import com.wsgjp.ct.framework.enums.AssertsSourceType;
import com.wsgjp.ct.pm.service.PermissionValiateService;
import com.wsgjp.ct.sale.biz.bill.mapper.TdBillCoreMapper;
import com.wsgjp.ct.sale.biz.bill.service.BillInterface;
import com.wsgjp.ct.sale.biz.bill.service.impl.GoodsBillServiceImpl;
import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.biz.member.common.*;
import com.wsgjp.ct.sale.biz.member.config.PosRedisLockTimeConfig;
import com.wsgjp.ct.sale.biz.member.mapper.SsCardAssertBillMapper;
import com.wsgjp.ct.sale.biz.member.mapper.SsVipLevelMapper;
import com.wsgjp.ct.sale.biz.member.mapper.SsVipMapper;
import com.wsgjp.ct.sale.biz.member.model.dto.card.GiveCardToVipRequest;
import com.wsgjp.ct.sale.biz.member.model.dto.recharge.VipRechargeDTO;
import com.wsgjp.ct.sale.biz.member.model.dto.rights.GetRightsCardByNamePage;
import com.wsgjp.ct.sale.biz.member.model.dto.vip.*;
import com.wsgjp.ct.sale.biz.member.model.entity.card.SsCard;
import com.wsgjp.ct.sale.biz.member.model.entity.card.SsCardTemplate;
import com.wsgjp.ct.sale.biz.member.model.entity.rights.MemberEquityValue;
import com.wsgjp.ct.sale.biz.member.model.entity.store.SsVipAsserts;
import com.wsgjp.ct.sale.biz.member.model.entity.vip.*;
import com.wsgjp.ct.sale.biz.member.model.vo.rights.CardForVipIdVo;
import com.wsgjp.ct.sale.biz.member.model.vo.rights.RightsVo;
import com.wsgjp.ct.sale.biz.member.model.vo.vip.*;
import com.wsgjp.ct.sale.biz.member.service.*;
import com.wsgjp.ct.sale.biz.member.utils.*;
import com.wsgjp.ct.sale.biz.shopsale.common.ResultCode;
import com.wsgjp.ct.sale.biz.shopsale.constanst.BillSaveResultTypeEnum;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.base.BaseInfoLog;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.BillSaveResultDTO;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.PayMentDTO;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.base.BaseOtype;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.base.PosBuyer;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.base.ScoreQuickReason;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.bill.PayResultInfo;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.store.StorePayway;
import com.wsgjp.ct.sale.biz.shopsale.service.PosSisClientService;
import com.wsgjp.ct.sale.biz.shopsale.service.StoreService;
import com.wsgjp.ct.sale.biz.shopsale.service.SystemConfigService;
import com.wsgjp.ct.sale.biz.shopsale.service.TaotaoguLogService;
import com.wsgjp.ct.sale.biz.wx.common.WxCommonFieldIdEnum;
import com.wsgjp.ct.sale.biz.wx.entity.SsWxVipCardTemplate;
import com.wsgjp.ct.sale.biz.wx.entity.WxVipAssert;
import com.wsgjp.ct.sale.biz.wx.service.NgpWxOpenService;
import com.wsgjp.ct.sale.biz.wx.utils.AssertUtil;
import com.wsgjp.ct.sale.monitor.MonitorService;
import com.wsgjp.ct.sale.monitor.entity.MonitorTypeEnum;
import com.wsgjp.ct.sale.sdk.payment.biz.AggregatePaymentService;
import com.wsgjp.ct.sale.sdk.payment.entity.request.AggregatePaymentRequest;
import com.wsgjp.ct.sale.sdk.payment.entity.response.PaymentInfo;
import com.wsgjp.ct.sale.sdk.payment.entity.response.PaymentResponse;
import com.wsgjp.ct.sale.sdk.payment.enums.PayStatusEnum;
import com.wsgjp.ct.sale.sdk.mapper.MemberAssertsMapper;
import com.wsgjp.ct.sale.sdk.pos.biz.MemberAssertsChangeService;
import com.wsgjp.ct.sale.sdk.pos.entity.asserts.*;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.thread.ThreadPool;
import com.wsgjp.ct.support.thread.ThreadPoolFactory;
import com.wsgjp.ct.support.utils.QiniuUtils;
import com.wsgjp.ct.support.utils.RedisBizUtils;
import me.chanjar.weixin.mp.bean.card.membercard.MemberCardUserInfo;
import me.chanjar.weixin.mp.bean.card.membercard.NameValues;
import me.chanjar.weixin.mp.bean.card.membercard.WxMpMemberCardUserInfoResult;
import ngp.idgenerator.UId;
import ngp.loadbalancer.context.RouteContext;
import ngp.loadbalancer.context.RouteThreadLocal;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.Md5Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import utils.StringUtil;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.wsgjp.ct.sale.biz.member.utils.SVIPUtils.*;


/**
 * <p>
 * 会员表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-15
 */
@Service
public class SsVipServiceImpl implements ISsVipService {

    @Autowired
    GoodsBillServiceImpl goodsBillService;

    @Autowired
    private SsVipMapper ssVipMapper;

    @Autowired
    private SsVipLevelMapper ssVipLevelMapper;

    @Autowired
    private MemberAssertsMapper memberAssertsMapper;

    @Autowired
    private ApplicationContext applicationContext;

    /**
     * 卡劵、权益卡
     */
    @Autowired
    private ISsCardService cardService;

    @Autowired
    private ISsCardTemplateService ssCardTemplateService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private MemberAssertsChangeService memberAssertsChangeService;

    @Autowired
    private ISsVipAssertsChangeService vipAssertsChangeService;

    @Autowired
    private AggregatePaymentService aggregatePaymentService;

    @Autowired
    private TaotaoguLogService taotaoguLogService;

    @Autowired
    private BillInterface billInterface;


    private static final Logger LOGGER = LoggerFactory.getLogger(ISsVipService.class);

    /**
     * 会员和门店中间表
     */
    private final ISsVipOtypeService vipOtypeService;
    /**
     * 会员和权益中间表
     */
    private final ISsVipRightsCardService vipRightsCardService;

    @Autowired
    private SsCardAssertBillMapper vipAssertChangeMapper;

    /**
     * 会员等级表
     */
    private final ISsVipLevelService levelService;

    /**
     * 收款单据
     */
    @Autowired
    ReceiptBillService receiptBillService;

    @Autowired
    private PosRedisLockTimeConfig posRedisLockTimeConfig;

    private final NgpWxOpenService wxOpenService;

    @Autowired
    private ISsVipRechargeRecordListService rechargeRecordListService;

    @Autowired
    private PosSisClientService sisClientService;

    public static final String markStyle = "background: %s;text-align: center;font-size: 12px;color: #FFFFFF;" +
            "margin-left: 1px;margin-top: 4px;max-width: 130px;border-radius:2px; line-height: 20px;float: left; " +
            "padding" +
            ": 0 3px;";

    private Logger log = LoggerFactory.getLogger(SsVipServiceImpl.class);

    public SsVipServiceImpl(ISsVipOtypeService vipOtypeService, ISsVipRightsCardService vipRightsCardService,
                            @Lazy ISsVipLevelService levelService,
                            @Lazy NgpWxOpenService wxOpenService) {
        this.vipOtypeService = vipOtypeService;
        this.vipRightsCardService = vipRightsCardService;
        this.levelService = levelService;
        this.wxOpenService = wxOpenService;
    }

    @Override
    public BigInteger addOrEditVip(AddOrEditVipDTO dto) throws Exception {
        // 增加分布式锁，防止网络卡顿时重复操作会员
        boolean aTrue =
                PosRedisLockerUtils.setLock("addOrEditVip:" + CurrentUser.getProfileId() + " " + dto.getPhone(), "true",
                        posRedisLockTimeConfig.getHalfMin());
        if (!aTrue) {
            throw new RuntimeException(String.format("请等待30秒,会员%s正在%s中", dto.getPhone(), dto.getId() == null ? "新增" :
                    "编辑"));
        }
        Map<String, Object> extra = new HashMap<>();
        BigInteger vipId;
        try {
            vipId = GetBeanUtil.getBean(SsVipServiceImpl.class).addOrEditVipWithoutPost(dto, extra);
            BigInteger receiptVchcode = (BigInteger) extra.get("receiptVchcode");
            if (receiptVchcode != null && !receiptVchcode.equals(BigInteger.ZERO)) {
                try {
                    goodsBillService.postBill(receiptVchcode);
                } catch (Exception e) {
                    log.error("receiptVchcode:" + receiptVchcode + "核算时发生异常：" + e.getMessage());
                }
            }
            BigInteger levelVchcode = (BigInteger) extra.get("levelVchcode");
            if (levelVchcode != null && !levelVchcode.equals(BigInteger.ZERO)) {
                try {
                    goodsBillService.postBill(levelVchcode);
                } catch (Exception e) {
                    log.error("levelVchcode:" + levelVchcode + "核算时发生异常：" + e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("新增会员报错：" + JsonUtils.toJson(e));
            throw new RuntimeException(e.getMessage());
        } finally {
            PosRedisLockerUtils.unLock("addOrEditVip:" + CurrentUser.getProfileId() + " " + dto.getPhone());
        }
        return vipId;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public BigInteger addOrEditVipWithoutPost(AddOrEditVipDTO dto, Map<String, Object> extra) throws Exception {
        // 微信会员来源的不用验证权限
        if (dto.getSourceType() != 1) {
            if (dto.getId() != null) {
                //修改
                if (!PermissionValiateService.validate(PermissionShopSale.MEMBER_VIP_EDIT)) {
                    throw new RuntimeException("您无编辑会员的权限，请联系管理员授权");
                }
            } else {
                if (!PermissionValiateService.validate(PermissionShopSale.MEMBER_VIP_ADD)) {
                    throw new RuntimeException("您无新增会员的权限，请联系管理员授权");
                }
            }
        }

        if (!dto.isFromExcel()) {
            if (dto.isRequiredPhone() && (dto.getPhone() == null || dto.getPhone().isEmpty())) {
                throw new Exception("电话号码不能为空!");
            }
        }

        // 操作来源校验
        if (dto.getOperationSource() == null) {
            throw new RuntimeException("操作来源不能为空");
        }
        PosSysData sys = new PosSysData();
        sys.setKey(MemberSettingEnum.POS_VIP_NAME_REQUIRED);
        String vipNameRequired = systemConfigService.getConfigByKey(sys);
        if ("1".equals(vipNameRequired)) {
            AssertUtil.trueTrw(StringUtils.isBlank(dto.getName()), "会员名称不能为空");
        }
        // 详细地址校验
        if (StringUtils.isNotBlank(dto.getAddress())) {
            AssertUtil.trueTrw(dto.getAddress().length() > 200, "详细地址不能超过200字符");
        }
        // 可用门店选项校验
//        AssertUtil.nullTrw(dto.getApplyStoreType(), "可用门店选项不能为空");
        //根据是否有vipId来判断是新增还是修改
        BigInteger vipId = dto.getId();
        MemberAssertsChange memberAssertsChange = new MemberAssertsChange();
        List<MemberAssert> vipAsserts = new ArrayList<>();
        //会员表
        SsVip vip = BeanUtils.copyProperties(dto, SsVip.class);
        SsVipLevel ssVipLevel = null;
        SsVipLevelRule levelRule = null;
        if (vipId != null) {
            SsVip ssVip = ssVipMapper.selectVipById(vipId, CurrentUser.getProfileId());
            //不可修改 顾客信息，等级，成长值
            vip.setCustomerId(ssVip.getCustomerId());
            vip.setLevelId(ssVip.getLevelId());
            vip.setGrowthValue(ssVip.getGrowthValue());
            ssVipLevel = levelService.getVipLevelByLevelId(vip.getLevelId());
        } else {
            if (dto.getVipLevelId() == null) {
                ssVipLevel = getDefaultVipLevel();
            } else {
                ssVipLevel = levelService.getVipLevelByLevelId(dto.getVipLevelId());
            }
            vip.setLevelId(ssVipLevel.getId());
            vip.setGrowthValue(dto.getGrowthValue() == null ? 0 : dto.getGrowthValue());
            if (dto.getGrowthValue() != null && dto.getGrowthValue() != 0) {
                vipAsserts.add(MemberAssert.createData(3, new BigDecimal(dto.getGrowthValue()), "新增会员", null, null, AssertsChangeType.NEW_VIP));
            }
        }
        List<PayMentDTO> paymentStore = new ArrayList<>();
        List<PayMentDTO> paymentLevel = new ArrayList<>();
        Date now = new Date();

        //只有新增会员可以修改会员等级
        if (dto.getId() == null) {
            if (ssVipLevel == null || ssVipLevel.getId() == null) {
                throw new RuntimeException("系统找不到匹配的会员等级");
            }
            if (ssVipLevel.getStoped()) {
                throw new RuntimeException("该会员等级已停用");
            }
            //校验会员等级和价格
            if (ssVipLevel.getVipType() == 1) {
                if (dto.getLevelRuleId() == null || BigInteger.ZERO.equals(dto.getLevelRuleId())) {
                    throw new RuntimeException("请选择付费会员定价规则");
                }
                if (dto.getBtypeId() == null || BigInteger.ZERO.equals(dto.getBtypeId())) {
                    throw new RuntimeException("结算单位不能为空");
                }
                List<SsVipLevelRule> levelRules = levelService.selectVipLevelRuleByLevelId(ssVipLevel.getId());
                levelRule =
                        levelRules.stream().filter(rule -> rule.getId().equals(dto.getLevelRuleId())).findAny().orElse(null);
                if (levelRule == null) {
                    throw new RuntimeException("当前的会员等级不包含此定价规则");
                }
                //计算会员过期时间
                vip.setValidDate(getValidDate(levelRule.getValidity(), now));
                //会员等级支付方式
                if (levelRule.getLevelPrice().compareTo(BigDecimal.ZERO) > 0) {
                    if ((dto.getAtypeId() == null || dto.getAtypeId().equals(BigInteger.ZERO))) {
                        if (dto.getPayment() == null || dto.getPayment().isEmpty()) {
                            throw new RuntimeException("请选择支付方式");
                        }
                        checkValidPaymentList(dto.getPayment());
                        paymentLevel.addAll(dto.getPayment());
                    } else {
                        PayMentDTO payment = new PayMentDTO();
                        payment.setAtypeId(dto.getAtypeId());
                        payment.setPaywayId(dto.getPaywayId());
                        payment.setCurrencyAtypeTotal(levelRule.getLevelPrice());
                        paymentLevel.add(payment);
                    }
                }
            }
            //储值需要支付方式
            if (dto.getStore() != null && dto.getStore().compareTo(BigDecimal.ZERO) > 0) {
                if (dto.getBtypeId() == null || BigInteger.ZERO.equals(dto.getBtypeId())) {
                    throw new RuntimeException("结算单位不能为空");
                }
                if ((dto.getAtypeId() == null || dto.getAtypeId().equals(BigInteger.ZERO)) && (null == dto.getPayment() || dto.getPayment().size() == 0)) {
                    throw new RuntimeException("请选择支付方式");
                }
                PayMentDTO payment = new PayMentDTO();
                payment.setAtypeId(dto.getAtypeId());
                payment.setPaywayId(dto.getPaywayId());
                payment.setCurrencyAtypeTotal(dto.getStore());
                paymentStore.add(payment);
            }
        }
        if (StringUtils.isNotBlank(dto.getPhone())) {
            checkPhone(vip.getPhone(), vip.getCustomerId(), vip.getApplyStoreType(),
                    dto.getApplyStoreType() == 1 ? dto.getOtypeIds() : null);
        }

        //标签
        if (dto.getVipTag() != null) {
            if (dto.getVipTag().size() > 10) {
                throw new RuntimeException("会员最多能加10个标签");
            }
            vip.setTags(JSONObject.toJSONString(dto.getVipTag()));
        } else {
            vip.setTags("[]");
        }
        // 储值密码
        if (StringUtils.isNotBlank(dto.getPassword())) {
            // 储值支付密码只能是6位，不能多也不能少
            Assert.isTrue(dto.getPassword().length() == 6, "储值支付密码只能是6位数字");
            String key = CurrentUser.getProfileId() + dto.getPassword();
            String val = Md5Utils.md5(key);
            vip.setPassword(val);
        } else {
            vip.setPassword("");
        }
        vip.setPhone(dto.getPhone());
        vip.setBirthday(DateUtils.parse(dto.getBirthday()));
        //付费会员无需开启
        if (ssVipLevel.getVipType() == 0) {
            // 开没开等级评估周期
            SsVipLevelAssessPeriod levelAssessPeriod = levelService.getLevelAssessPeriod();
            if (levelAssessPeriod != null && levelAssessPeriod.getLevelAssessPeriod() == 1) {
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.DATE, levelAssessPeriod.getPeriod());
                vip.setNextLevelAssessTime(calendar.getTime());
            }
        }
        // 客户id
        if (vip.getCustomerId() == null) {
            addCustomer(vip, dto);
        }
        // 会员地址处理
        buildAddress(vip, dto);

        //新增会员
        addOrEditVip(vip, dto.getOtypeIds());
        //资产表 （积分和储值）
        if (vipId == null) {
            addVipAsserts(vip, dto, memberAssertsChange, vipAsserts, paymentStore, extra,
                    dto.getBtypeId(), dto.getMemo());
        }
        //付费等级
        if (vipId == null && ssVipLevel.getVipType() == 1) {
            assert levelRule != null;
            if (levelRule.getLevelPrice().compareTo(BigDecimal.ZERO) > 0) {
                BigInteger levelVchcode = UId.newId();
                if (dto.getVchcode() == null) {
                    String levelName = ssVipLevel.getVipName();
                    if (levelName != null) {
                        levelName = "(" + levelName + ")";
                    } else {
                        levelName = "";
                    }
                    String summary = "会员(" + dto.getPhone() + ")开通付费会员" + levelName;
                    submitVipLevelBill(levelVchcode, levelRule.getLevelPrice(), summary, dto.getMemo(), paymentLevel,
                            dto.getEtypeId(), dto.getBtypeId(), dto.getOtypeId(), vip.getId());
                } else {
                    levelVchcode = dto.getVchcode();
                }

                extra.put("levelVchcode", levelVchcode);
            }
            assert vip.getValidDate() != null;
            //记录会员等级付费记录
            BigInteger vchcode = (BigInteger) extra.get("levelVchcode");
            ssVipMapper.insertVipFeeRecord(Collections.singletonList(new SsVipFeeRecord(UId.newId(), vchcode == null
                    ? BigInteger.ZERO : vchcode, CurrentUser.getProfileId(), vip.getId(), levelRule.getLevelPrice(),
                    1, now,
                    vip.getValidDate())));
        }
        // 新增会员的情况下，添加权益卡
        if (vipId == null) {
            // 普通权益卡
            addCard(dto.getRightsCardModelIds(), vipAsserts, "普通权益卡");
            // 优惠券
            addCard(dto.getCardIds(), vipAsserts, "优惠券");
            // 处理开卡赠送（积分和卡券）
            processOpenCardGift(vip, ssVipLevel, vipAsserts);
            // 创建空数据
            saveNullData(Collections.singletonList(vip));
            // 等级权益卡
            addLevelCard(vip);
        }
        // 保存资产变动
        if (!vipAsserts.isEmpty()) {
            try {
                LOGGER.info("开始处理会员资产变动，vipId: {}, 资产变动数量: {}", vip.getId(), vipAsserts.size());
                // 新增会员不算消费
                memberAssertsChange.setSourceOperation(AssertsSourceOperation.NEW_VIP);
                memberAssertsChange.setOperationSource(dto.getOperationSource());
                memberAssertsChange.setMemo(StringUtils.isNotBlank(dto.getMemo()) ? dto.getMemo() : "新增会员");
                memberAssertsChange.setSaveVipAsserts(true);
                List<VipAsserts> assertsList = new ArrayList<>();
                VipAsserts vipAssert = new VipAsserts();
                vipAssert.setVipId(vip.getId());
                vipAssert.setVipAssert(vipAsserts);
                assertsList.add(vipAssert);
                memberAssertsChange.setVipAsserts(assertsList);
                memberAssertsChangeService.vipAssertsChange(memberAssertsChange);
                LOGGER.info("会员资产变动处理完成，vipId: {}", vip.getId());
            } catch (Exception e) {
                LOGGER.error("会员资产变动处理失败，vipId: {}, error: {}", vip.getId(), e.getMessage(), e);
                throw new RuntimeException("会员资产变动处理失败: " + e.getMessage(), e);
            }
        }

        return vip.getId();
    }

    private void addLevelCard(SsVip vip) {
        try {
            LOGGER.info("开始处理等级权益卡，vipId: {}, levelId: {}", vip.getId(), vip.getLevelId());
            // 按照以前的代码，送等级权益卡是不记录资产变动日志的，所以这里还是单独处理
            List<BigInteger> vipIds = new ArrayList<>();
            vipIds.add(vip.getId());
            SsVipLevel vipLevelByLevelId = levelService.getVipLevelByLevelId(vip.getLevelId());
            if (vipLevelByLevelId != null && vipLevelByLevelId.getCardTemplateId() != null) {
                List<BigInteger> modelIds = new ArrayList<>();
                modelIds.add(vipLevelByLevelId.getCardTemplateId());
                GiveCardToVipRequest cardToVipRequest = new GiveCardToVipRequest();
                cardToVipRequest.setCardIds(modelIds);
                cardToVipRequest.setVipIds(vipIds);
                cardToVipRequest.setComment("新增会员等级权益卡");
                cardToVipRequest.setThrowException(false);
                ssCardTemplateService.giveCardToVip(cardToVipRequest);
                LOGGER.info("等级权益卡处理完成，vipId: {}", vip.getId());
            } else {
                LOGGER.warn("会员等级或权益卡模板为空，跳过权益卡赠送，vipId: {}, levelId: {}", vip.getId(), vip.getLevelId());
            }
        } catch (Exception e) {
            LOGGER.error("等级权益卡处理失败，vipId: {}, error: {}", vip.getId(), e.getMessage(), e);
            // 权益卡赠送失败不应该影响会员创建，所以这里只记录日志，不抛出异常
        }
    }

    private void addOtype(SsVip vip, List<BigInteger> otypeIds) {
        if (vip.getApplyStoreType() == 1) {
            // 部分可用门店才需要保存
            AssertUtil.emptyTrw(otypeIds, "可用门店不能为空");
            List<SsVipStore> storeList = new ArrayList<>();
            for (BigInteger otypeId : otypeIds) {
                SsVipStore store = new SsVipStore();
                store.setId(UId.newId());
                store.setProfileId(CurrentUser.getProfileId());
                store.setVipId(vip.getId());
                store.setStoreId(otypeId);
                storeList.add(store);
            }
            ssVipMapper.insertVipStore(storeList);
        }
    }


    /**
     * 添加或修改一个会员
     *
     * @param vip      会员
     * @param otypeIds
     */
    private void addOrEditVip(SsVip vip, List<BigInteger> otypeIds) {
        vip.setProfileId(CurrentUser.getProfileId());
        if (vip.getId() == null) {
            vip.setId(UId.newId());
            //默认开启
            vip.setEnableStore(true);
            vip.setEnableScore(true);
            ssVipMapper.insertVip(vip);
            // 门店处理
            addOtype(vip, otypeIds);
        } else {
            EditCustomerBaseInfoDTO editCustomerBaseInfoDTO = BeanUtils.copyProperties(vip,
                    EditCustomerBaseInfoDTO.class);
            editCustomerBaseInfoDTO.setVipId(vip.getId());
            editCustomerBaseInfoDTO.setChangeType(EditCustomerType.all);
            editCustomerBaseInfoDTO.setBirthday(vip.getBirthday() == null ? "" :
                    DateUtils.formatDate(vip.getBirthday()));
            editCustomerBaseInfoDTO.setOtypeIds(otypeIds);
            GetBeanUtil.getBean(SsVipServiceImpl.class).editCustomerBaseInfo(editCustomerBaseInfoDTO);
            // 保存微信相关信息
            ssVipMapper.updateVipWxData(vip);
        }
    }

    @Override
    public void updateVipWxData(SsVip vip) {
        ssVipMapper.updateVipWxData(vip);
    }

    @Override
    public SsVip getOpenIdByVipId(BigInteger profileId, BigInteger vipId) {
        return ssVipMapper.getOpenIdByVipId(profileId, vipId);
    }

    @Override
    public List<SsVip> getOpenIdByVipIds(BigInteger profileId, List<BigInteger> vipIds) {
        return ssVipMapper.getOpenIdByVipIds(profileId, vipIds);
    }

    @Override
    public List<PlShopVipCard> getPlShopVipCards(BigInteger profileId, BigInteger eshopId) {
        return ssVipMapper.getPlShopVipCards(profileId, eshopId);
    }

    @Override
    public void updatePlShopVipCard(List<PlShopVipCard> updateList) {
        if (updateList.isEmpty()) {
            return;
        }
        ssVipMapper.updatePlShopVipCard(updateList);
    }

    @Override
    public void checkValidPaymentList(List<PayMentDTO> list) {


        //无效的支付方式
        //条件：非预存款类型且未绑定现金银行的支付方式
        String unValidList =
                list.stream().filter(item -> (null == item.getAtypeId() || item.getAtypeId().equals(BigInteger.ZERO)) && item.getPaywayType() != 3).map(PayMentDTO::getPaywayFullname).collect(Collectors.joining(","));
        if (StringUtils.isNotEmpty(unValidList)) {
            throw new RuntimeException(String.format("支付方式【%s】绑定的现金银行为空，请在资料-科目-支付方式 中进行编辑", unValidList));
        }

    }

    @Override
    public PaidVip getPaidVipById(BigInteger vipId) {
        PaidVip paidVipById = ssVipMapper.getPaidVipById(CurrentUser.getProfileId(), vipId);
        // 手机号解密
        List<PosBuyer> buyers =
                sisClientService.batchDecryptBuyers(Collections.singletonList(paidVipById.getBuyerId()));
        if (buyers != null && !buyers.isEmpty()) {
            paidVipById.setPhone(buyers.get(0).getCustomerReceiverPhone());
        }
        return paidVipById;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public BigInteger paidVipRefund(PaidVipRefundDTO dto) {
        // 校验数据
        AssertUtil.falseTrw(dto.getVipId() != null && !dto.getVipId().equals(BigInteger.ZERO), "会员id不能为空");
        AssertUtil.falseTrw(dto.getOtypeId() != null && !dto.getOtypeId().equals(BigInteger.ZERO), "门店信息不能为空");
        AssertUtil.falseTrw(dto.getRefundMoney() != null && BigDecimal.ZERO.compareTo(dto.getRefundMoney()) < 0,
                "退款金额必须大于0元");
        PaidVip paidVipById = getPaidVipById(dto.getVipId());
        AssertUtil.falseTrw(paidVipById.getTotal().compareTo(dto.getRefundMoney()) >= 0,
                "退款金额最大值不能超过" + paidVipById.getTotal().stripTrailingZeros().toPlainString());
        SsVip ssVip = selectVipById(dto.getVipId());
        // 会员手机号解密
        List<BigInteger> buyerIds = new ArrayList<>();
        buyerIds.add(ssVip.getBuyerId());
        List<PosBuyer> buyers = sisClientService.batchDecryptBuyers(buyerIds);
        if (buyers != null && !buyers.isEmpty()) {
            ssVip.setPhone(buyers.get(0).getCustomerReceiverPhone());
        }
        // 生成负值收款单
        BigInteger vchcode = UId.newId();
        // 处理支付方式，确保金额都为负数
        for (PayMentDTO paymentDTO : dto.getPayment()) {
            paymentDTO.setCurrencyAtypeTotal(paymentDTO.getCurrencyAtypeTotal().abs().negate());
        }
        String summary = "会员[" + ssVip.getPhone() + "]的会员卡付费会员退款" + dto.getRefundMoney().abs() + "元";
        submitVipLevelBill(vchcode, dto.getRefundMoney().abs().negate(), summary, null, dto.getPayment(),
                CurrentUser.getEmployeeId(), dto.getBtypeId(), dto.getOtypeId(), dto.getVipId());

        // 会员降级
        // 先判断有没有免费会员等级，没有就不做处理
        Integer freeLevel = levelService.getFreeLevel();
        if (freeLevel != null && freeLevel != 0) {
            SsVipLevelAssessPeriod levelAssessPeriod = levelService.getLevelAssessPeriod();
            levelService.changeVipLevel(Collections.singletonList(ssVip), levelAssessPeriod, "付费会员退款");
        }

        // 生成充值记录
        // 先将之前的充值记录作废
        ssVipMapper.setVipFeeRecordExpired(CurrentUser.getProfileId(), dto.getVipId());
        SsVipFeeRecord record = new SsVipFeeRecord(UId.newId(), vchcode, CurrentUser.getProfileId(), dto.getVipId(),
                dto.getRefundMoney().abs().negate(), 2, null, null);
        ssVipMapper.insertVipFeeRecord(Collections.singletonList(record));
        // 微信会员同步
        vipAssertsChangeService.wxVipAsync(dto.getVipId(), CurrentUser.getProfileId());
        return vchcode;
    }

    private void buildAddress(SsVip vip, AddOrEditVipDTO dto) {
        vip.setProvince(dto.getProvince() == null ? "" : dto.getProvince());
        vip.setCity(dto.getCity() == null ? "" : dto.getCity());
        vip.setDistrict(dto.getDistrict() == null ? "" : dto.getDistrict());
        vip.setStreet(dto.getStreet() == null ? "" : dto.getStreet());
        vip.setAddress(dto.getAddress() == null ? "" : dto.getAddress());
        vip.setFullAddress(vip.getProvince() + vip.getCity() + vip.getDistrict() + vip.getStreet() + vip.getAddress());
    }

    private void addCustomer(SsVip vip, AddOrEditVipDTO dto) {
        // 新建客户
        BigInteger customerId = UId.newId();

        SsVip customer = new SsVip();
        customer.setCustomerId(customerId);
        customer.setProfileId(CurrentUser.getProfileId());
        customer.setName(dto.getName() == null ? "" : dto.getName());
        customer.setPhone(dto.getPhone());
        customer.setEmail(dto.getEmail() == null ? "" : dto.getEmail());
        customer.setSex(dto.getSex() == null ? 1 : dto.getSex());
        customer.setBirthday(DateUtils.parse(dto.getBirthday()));
        buildAddress(customer, dto);
        int success = ssVipMapper.insertCustomer(customer);
        AssertUtil.trueTrw(success != 1, "添加客户信息错误");
        // 联系方式、地址
        BigInteger deliveryId = UId.newId();
        // 加密
        PosBuyer buyerByPhone = sisClientService.getBuyerByPhone(customer.getPhone());
        customer.setPhone(buyerByPhone.getCustomerReceiverPhone());
        customer.setDi(buyerByPhone.getDi());
        customer.setPi(buyerByPhone.getPi());
        success = ssVipMapper.insertPlBuyer(deliveryId, customer);
        AssertUtil.trueTrw(success != 1, "添加客户信息错误");
        // 性别、生日
        BigInteger buyerExtendId = UId.newId();
        success = ssVipMapper.insertPlBuyerExtend(buyerExtendId, deliveryId, customer);
        AssertUtil.trueTrw(success != 1, "添加客户信息错误");
        //顾客和联系方式中间表
        success = ssVipMapper.insertCustomerWithDelivery(UId.newId(), customerId, deliveryId,
                CurrentUser.getProfileId());
        AssertUtil.trueTrw(success != 1, "添加客户信息错误");
        vip.setCustomerId(customerId);
    }

    private void addVipAsserts(SsVip vip, AddOrEditVipDTO dto, MemberAssertsChange memberAssertsChange,
                               List<MemberAssert> vipAsserts, List<PayMentDTO> paymentStore,
                               Map<String, Object> extra, BigInteger btypeId, String memo) {
        List<VipRechargeDTO> rechargeDTOS = new ArrayList<>();
        buildVipAsserts(vip, dto, vipAsserts, rechargeDTOS);
        // 生成收款单
        if (dto.getStore() != null && dto.getStore().compareTo(BigDecimal.ZERO) > 0 && extra != null && !paymentStore.isEmpty()) {
            BigInteger vchcode = UId.newId();
            memberAssertsChange.setVchcode(vchcode);
            String billNumber = submitRechargeBill(vchcode, dto.getStore(), "新增会员" + dto.getPhone() + "时充值" + dto.getStore() + "元", memo,
                    paymentStore, btypeId, vip.getId());
            memberAssertsChange.setVchcode(vchcode);
            memberAssertsChange.setVchtype(com.wsgjp.ct.framework.enums.Vchtypes.ReceiptBill);
            memberAssertsChange.setBillNumber(billNumber);
            extra.put("receiptVchcode", vchcode);
            if (!rechargeDTOS.isEmpty()) {
                rechargeRecordListService.insertRechargeRecordList(rechargeDTOS, vchcode, RechargeChargeType.RECHARGE);
            }
        }
    }

    private void addCard(List<BigInteger> cardModelIds,
                         List<MemberAssert> vipAsserts, String detailMemo) {
        if (cardModelIds != null && !cardModelIds.isEmpty()) {
            for (BigInteger cardModelId : cardModelIds) {
                // 会员初始卡券
                vipAsserts.add(MemberAssert.createData(4, BigDecimal.ONE, "初始" + detailMemo, null, cardModelId, AssertsChangeType.NEW_VIP));
            }
        }
    }

    private void addDefaultVipLevelCard(List<BigInteger> vipIds) {
        SsVipLevel defaultVipLevel = getDefaultVipLevel();
        List<BigInteger> modelIds = new ArrayList<>();
        modelIds.add(defaultVipLevel.getCardTemplateId());
        GiveCardToVipRequest cardToVipRequest = new GiveCardToVipRequest();
        cardToVipRequest.setCardIds(modelIds);
        cardToVipRequest.setVipIds(vipIds);
        cardToVipRequest.setComment("新增会员等级权益卡");
        cardToVipRequest.setThrowException(false);
        ssCardTemplateService.giveCardToVip(cardToVipRequest);
    }

    /**
     * 根据id查找vip信息
     *
     * @param id 会员id
     */
    @Override
    public SsVip selectVipById(BigInteger id) {
        return ssVipMapper.selectVipById(id, CurrentUser.getProfileId());
    }

    @Override
    public PageInfo<CardForVipIdVo> cardGetVipList(GetRightsCardByNamePage page) {
        BigInteger profileId = CurrentUser.getProfileId();
        PageHelper.startPage(page.getPageIndex() == 0 ? 1 : page.getPageIndex(), page.getPageSize() == 0 ? 10 :
                page.getPageSize());
        if (StringUtils.isNotBlank(page.getPhone()) && page.getPhone().length() > 4) {
            // 长度大于4的都当做手机号加密一下
            PosBuyer buyerByPhone = sisClientService.getBuyerByPhone(page.getPhone());
            page.setEncryptPhone(buyerByPhone.getPi());
        }
        List<CardForVipIdVo> vipList = ssVipMapper.getVipList(page, profileId);
        PageInfo<CardForVipIdVo> pageInfos = new PageInfo<>(vipList);
        if (CollectionUtils.isEmpty(vipList)) {
            return pageInfos;
        }
        // 手机号解密
        List<BigInteger> buyerIds = new ArrayList<>();
        for (CardForVipIdVo cardForVipIdVO : vipList) {
            buyerIds.add(cardForVipIdVO.getBuyerId());
        }
        List<PosBuyer> buyers = sisClientService.batchDecryptBuyers(buyerIds);
        if (buyers != null && !buyers.isEmpty()) {
            for (CardForVipIdVo cardForVipIdVO : vipList) {
                for (PosBuyer buyer : buyers) {
                    if (buyer.getBuyerId().equals(cardForVipIdVO.getBuyerId())) {
                        cardForVipIdVO.setPhone(buyer.getCustomerReceiverPhone());
                    }
                }
            }
        }
        // 查询优惠券
        List<BigInteger> ids = vipList.stream().map(CardForVipIdVo::getVipId).collect(Collectors.toList());
        List<Integer> cardTypes = new ArrayList<>();
        cardTypes.add(2);
        cardTypes.add(3);
        cardTypes.add(4);
        List<RightsVo> cardNames = ssVipMapper.getCardByVipId(ids, profileId, cardTypes);
        for (CardForVipIdVo vo : vipList) {
            for (RightsVo cardName : cardNames) {
                if (Objects.equals(cardName.getEquityCardId(), vo.getVipId())) {
                    if (!cardName.getNames().isEmpty()) {
                        vo.setCardName(cardName.getNames());
                    }
                }
            }
        }
        return pageInfos;
    }

    @Override
    public PageInfo<CardForVipIdVo> getVipList(GetRightsCardByNamePage page) {
        BigInteger profileId = CurrentUser.getProfileId();
        PageHelper.startPage(page.getPageIndex() == 0 ? 1 : page.getPageIndex(), page.getPageSize() == 0 ? 10 :
                page.getPageSize());
        //这里还是不能写一对多
        if (StringUtils.isNotBlank(page.getPhone()) && page.getPhone().length() > 4) {
            // 长度大于4的都当做手机号加密一下
            PosBuyer buyerByPhone = sisClientService.getBuyerByPhone(page.getPhone());
            page.setEncryptPhone(buyerByPhone.getPi());
        }
        List<CardForVipIdVo> vipList = ssVipMapper.getVipList(page, profileId);
        PageInfo<CardForVipIdVo> pageInfos = new PageInfo<>(vipList);
        if (CollectionUtils.isEmpty(vipList)) {
            return pageInfos;
        }
        // 手机号解密
        List<BigInteger> buyerIds = new ArrayList<>();
        for (CardForVipIdVo vipIdVo : vipList) {
            buyerIds.add(vipIdVo.getBuyerId());
        }
        List<PosBuyer> buyers = sisClientService.batchDecryptBuyers(buyerIds);
        if (buyers != null && !buyers.isEmpty()) {
            for (CardForVipIdVo vipIdVo : vipList) {
                for (PosBuyer buyer : buyers) {
                    if (buyer.getBuyerId().equals(vipIdVo.getBuyerId())) {
                        vipIdVo.setPhone(buyer.getCustomerReceiverPhone());
                    }
                }
            }
        }
        //一对多分页问题还是不能解决 考虑到 巨量数据还是不一对多
        List<BigInteger> ids = vipList.stream().map(CardForVipIdVo::getVipId).collect(Collectors.toList());
        //这里需要数据补充 权益卡名字
        //这里可以一对多
        List<RightsVo> rightsCardName = ssVipMapper.getRightsCardName(ids, profileId, new Date());
        Map<BigInteger, List<String>> collect =
                rightsCardName.stream().collect(Collectors.toMap(RightsVo::getEquityCardId, RightsVo::getNames));
        for (CardForVipIdVo vo : vipList) {
            List<String> strings = collect.get(vo.getVipId());
            if (!CollectionUtils.isEmpty(strings)) {
                vo.setCardName(strings);
            }
        }

        return pageInfos;
    }

    @Override
    public PageInfo<SelectVipListResponseDTO> selectVipList(PageRequest<SelectVipListRequestDTO> request) {
        if (request.getPageIndex() > 0 && request.getPageSize() > 0) {
            PageHelper.startPage(request.getPageIndex(), request.getPageSize());
        }
        SelectVipListRequestDTO dto = request.getQueryParams();
        dto.setProfileId(CurrentUser.getProfileId());
        changeSorts(request.getSorts());

        //查询条件tag处理
        if (dto.getTag() != null && !dto.getTag().isEmpty()) {
            String tagString = "";
            String jsonString = JSONObject.toJSONString(dto.getTag());
            tagString = jsonString.replace("[", "").replace("]", "").replaceAll(",", "|").replaceAll("\"", "");
            dto.setTags(tagString);
        }
        // 查询手机号处理
        if (StringUtils.isNotBlank(dto.getPhone()) && dto.getPhone().length() > 4) {
            PosBuyer buyerByPhone = sisClientService.getBuyerByPhone(dto.getPhone());
            dto.setEncryptPhone(buyerByPhone.getPi());
        }
        List<SelectVipListResponseDTO> pageList = ssVipMapper.selectVipList(dto, request.getSorts());
        List<SsVipTags> tagsList = ssVipMapper.getTags(CurrentUser.getProfileId());
        // 手机号解密
        if (pageList != null && !pageList.isEmpty()) {
            List<BigInteger> buyerIds =
                    pageList.stream().map(SelectVipListResponseDTO::getBuyerId).collect(Collectors.toList());
            List<PosBuyer> buyers = sisClientService.batchDecryptBuyers(buyerIds);
            Date dateNow = new Date();
            for (SelectVipListResponseDTO responseDTO : pageList) {
                for (PosBuyer buyer : buyers) {
                    if (buyer.getBuyerId().equals(responseDTO.getBuyerId())) {
                        responseDTO.setPhone(buyer.getCustomerReceiverPhone());
                        break;
                    }
                }
                // 付费会员是否过期
                responseDTO.setExpired(SVIPUtils.isVipLevelExpired(responseDTO.getVipLevelType(),
                        responseDTO.getValidDate()));
                if (responseDTO.getSex() == null) {
                    responseDTO.setSexName("");
                } else {
                    responseDTO.setSexName(responseDTO.getSex() == 1 ? "男" : "女");
                }
                if (Objects.equals(responseDTO.getVipLevelType(), 1)) {
                    responseDTO.setVipLevelTypeName("付费会员");
                    if (isForever(responseDTO.getValidDate())) {
                        responseDTO.setExpireDate("一直有效");
                        responseDTO.setExpireState("生效中");
                    } else {
                        Instant instant = responseDTO.getValidDate().toInstant();
                        ZoneId zoneId = ZoneId.of("Asia/Shanghai");
                        ZonedDateTime zonedDateTime = instant.atZone(zoneId);
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX"); //
                        // 使用'XXX'来表示时区偏移
                        String formattedDateTime = zonedDateTime.format(formatter);
                        responseDTO.setExpireDate(formattedDateTime);
                        if (responseDTO.getValidDate().before(dateNow)) {
                            responseDTO.setExpireState("已过期");
                        } else {
                            responseDTO.setExpireState("生效中");
                        }
                    }
                } else {
                    responseDTO.setVipLevelTypeName("免费会员");
                    responseDTO.setExpireDate("");
                    responseDTO.setExpireState("");
                }
                // tags处理
                LinkedList<HashMap<BigInteger, String>> tagButtons = new LinkedList<>();
                ArrayList<String> tags = JsonUtils.toList(responseDTO.getTags(), String.class);
                responseDTO.setTagIds(tags);
                if (tags != null && !tags.isEmpty()) {
                    for (String tag : tags) {
                        HashMap<BigInteger, String> tagButton = new HashMap<>();
                        tagsList.forEach(tags1 -> {
                            if (tags1.getId().toString().equals(tag)) {
                                tagButton.put(tags1.getId(),
                                        String.format("<font class=\"status\" style=\"" + markStyle + "\" " +
                                                        "onclick=\"return false;\" title=\"%s\">%s</font>",
                                                tags1.getColour(), tags1.getText(), tags1.getText()));
                                tagButtons.add(tagButton);
                            }
                        });
                    }
                    responseDTO.setTags(JsonUtils.toJson(tagButtons));
                }
            }
        }
        return new PageInfo<>(pageList);
    }

    @Override
    public PageInfo<VipHistoryConsumeDto> vipHistoryConsume(PageRequest<VipQuery> request) {
        if (request.getPageIndex() > 0 && request.getPageSize() > 0) {
            PageHelper.startPage(request.getPageIndex(), request.getPageSize());
        }
        List<VipHistoryConsumeDto> pageList = ssVipMapper.selectVipHistoryConsume(CurrentUser.getProfileId(),
                request.getQueryParams().getVipId(), DateUtils.addYears(DateTime.now(), -3),
                request.getSorts());

        for (VipHistoryConsumeDto item : pageList) {
            if (item.getPicUrl() != null && item.getPicUrl() != "" && !item.getPicUrl().contains("http")) {
                item.setPicUrl(QiniuUtils.getThumbnail(item.getPicUrl()));
            }
        }

        return new PageInfo<>(pageList);
    }

    @Override
    public VipWithLevelScoreRightsCardDTO getVipWithRightsAndOtypeById(GetVipWithLevelScoreRightsCardByPhoneDTO request) throws Exception {
        BigInteger vipId = request.getVipId();
        if (vipId == null) {
            throw new Exception("vipId不能为空");
        }
        //会员信息
        SsVip vip = selectVipById(vipId);
        if (vip == null || vip.getId() == null) {
            throw new Exception("没有该会员信息！");
        }
        long sTime = System.currentTimeMillis();
        MonitorService monitorService = com.wsgjp.ct.sale.platform.utils.BeanUtils.getBean(MonitorService.class);
        // 会员手机号解密
        List<BigInteger> buyerIds = new ArrayList<>();
        buyerIds.add(vip.getBuyerId());
        List<PosBuyer> buyers = sisClientService.batchDecryptBuyers(buyerIds);
        if (buyers != null && !buyers.isEmpty()) {
            vip.setPhone(buyers.get(0).getCustomerReceiverPhone());
        }
        //会员消费统计
        SsVipConsume consume = ssVipMapper.getVipCousumer(vipId, CurrentUser.getProfileId());
        //会员资产
        SsVipAsserts ssVipAsserts = ssVipMapper.getSsVipAsserts(vipId, CurrentUser.getProfileId());
        VipWithLevelScoreRightsCardDTO dto = new VipWithLevelScoreRightsCardDTO();
        //会员等级
        SsVipLevel vipLevel = levelService.selectVipLevelById(vip.getLevelId());
        if (vipLevel == null) {
            throw new Exception("该会员未绑定任何会员等级！");
        }
        // 付费会员是否过期
        vip.setExpired(SVIPUtils.isVipLevelExpired(vipLevel.getVipType(), vip.getValidDate()));
        VipSimple.Level level = new VipSimple.Level(vipLevel);
        //权益卡
        List<SsCard> rightsCardList = cardService.getCardListByVipIdAndCardType(vipId, 1);
        //优惠券
        List<Integer> cardTypes = new ArrayList<>();
        cardTypes.add(2);
        cardTypes.add(3);
        cardTypes.add(4);
        List<BigInteger> vipIds = Collections.singletonList(vipId);
        List<SsCard> cardList = cardService.getCardListByVipIdAndCardTypeYH(vipIds, cardTypes, request.getOtypeId());
        //等级权益卡
        List<SsCard> levelCardList = cardService.getCardListByVipIdAndCardType(vipId, 0);
        //门店
        List<VipSimple.Otype> otypeList = vipOtypeService.selectOtypeListByVipId(vipId);
        // 门店名称
        if (otypeList.isEmpty()) {
            vip.setOtypeNames("全部");
        } else {
            String otypeNames = otypeList.stream().map(VipSimple.Otype::getOtypeName).collect(Collectors.joining("、"));
            vip.setOtypeNames(otypeNames);
        }
        // 门店id
        List<BigInteger> otypeIds = otypeList.stream().map(VipSimple.Otype::getOtypeId).collect(Collectors.toList());
        vip.setOtypeIds(otypeIds);
        //tags
        List<String> list = JsonUtils.toList(vip.getTags(), String.class);
        List<BigInteger> tagIds = list.stream().map(BigInteger::new).collect(Collectors.toList());
        List<SsVipTags> tags = new ArrayList<>();
        if (!tagIds.isEmpty()) {
            tags = ssVipMapper.getTagByIds(tagIds, CurrentUser.getProfileId());
        }

        dto.setTagList(tags);
        dto.setVipTag(list);
        dto.setVip(vip);
        dto.setSsVipAsserts(ssVipAsserts);
        dto.setLevel(level);
        dto.setRightCardList(rightsCardList);
        dto.setCardList(cardList);
        dto.setLevelCardList(levelCardList);
        dto.setOtypeList(otypeList);
        dto.setConsume(consume);
        monitorService.recordTP(MonitorTypeEnum.PL_API_SALE_POS_GET_VIP_DETAIL_TP_TIME.getTopic(), "shopType", "pos",
                (System.currentTimeMillis() - sTime));
        return dto;
    }

    @Override
    public List<SsVip> getAllCustomerPhone(List<BigInteger> otypeIds) {
        List<SsVip> allCustomerPhone = ssVipMapper.getAllCustomerPhone(CurrentUser.getProfileId(), otypeIds);
        if (allCustomerPhone.isEmpty()) {
            return new ArrayList<>();
        }
        List<BigInteger> buyerIds = allCustomerPhone.stream().map(SsVip::getBuyerId).collect(Collectors.toList());
        List<PosBuyer> buyers = sisClientService.batchDecryptBuyers(buyerIds);
        if (!buyers.isEmpty()) {
            for (SsVip ssVip : allCustomerPhone) {
                for (PosBuyer buyer : buyers) {
                    if (ssVip.getBuyerId().equals(buyer.getBuyerId())) {
                        ssVip.setPhone(buyer.getCustomerReceiverPhone());
                    }
                }
            }
        }
        return allCustomerPhone;
    }

    @Override
    public String importByExcel(MultipartFile file, BigInteger btypeId) {
        if (btypeId == null || BigInteger.ZERO.equals(btypeId)) {
            throw new RuntimeException("结算单位不能为空");
        }
        checkFilePrefix(file, "xls", "xlsx");
        RouteContext routeContext = RouteThreadLocal.getRoute();
        if (routeContext == null) {
            throw new RuntimeException("请设置routeContext");
        }
        List<SsVip> customerList = getAllCustomerPhone(null);
        List<SelectLevelResponseDTO> levelList = levelService.getVipLevelList(new SelectLevelRequestDTO());
        ArrayList<SsCardTemplate> cardTemplateList =
                ssCardTemplateService.getAllCardTemplate(CurrentUser.getProfileId(), 1);
        Map<String, Boolean> permissions = systemConfigService.getAll().getData().getPermissions();
        List<BaseOtype> otypes = vipOtypeService.getAllOtype();
        HashMap<String, List<Integer>> paymentQuery = new HashMap<>();
        paymentQuery.put("paywayTypeList", Arrays.asList(0, 1, 4));
        List<StorePayway> paymentList =
                GetBeanUtil.getBean(StoreService.class).getStorePaymentList(paymentQuery);
        PosSysData sys = new PosSysData();
        sys.setKey(MemberSettingEnum.POS_VIP_NAME_REQUIRED);
        String vipNameRequired = systemConfigService.getConfigByKey(sys);
        String localProcessKey = RedisBizUtils.getRequestId();
        ThreadPool threadPool = ThreadPoolFactory.build("shopsale");
        threadPool.executeAsync(processKey -> {
            if (org.apache.commons.lang.StringUtils.isEmpty(processKey)) {
                LOGGER.error("currentProcessKey不能为空");
            }
            //设置进度ProcessId值
            ProcessUtil.setLocalProcessKey(processKey);
            try {
                EasyExcel.read(file.getInputStream(), AddVipByExcelDTO.class,
                        new VipImportListener(applicationContext.getBean(SsVipServiceImpl.class), goodsBillService,
                                customerList, levelList, cardTemplateList, permissions, paymentList, otypes, btypeId,
                                vipNameRequired)).sheet().doRead();
            } catch (FileNotFoundException e) {
                ProcessUtil.appendError("文件上传失败,请重新上传");
            } catch (Exception e) {
                LOGGER.error("未处理异常", e);
                ProcessUtil.appendError(e.getMessage());
            } finally {
                if (ProcessUtil.canComplete()) {
                    ProcessUtil.complete(null);
                }
                RouteThreadLocal.remove();
            }
        }, localProcessKey);
        return localProcessKey;
    }

    @Override
    public void addVipExcelToList(@Valid AddVipByExcelDTO dto, List<AddVipByExcelDTO> list) {
        list.add(dto);
    }

    @Override
    public Map<String, List<OtypeWithVipPhoneDO>> getOtypeWithVipPhoneListByOtypeNames(List<String> otypeNames) {
        List<OtypeWithVipPhoneDO> list = ssVipMapper.getOtypeWithVipPhoneListByOtypeNames(otypeNames,
                CurrentUser.getProfileId());
        return list.stream().collect(Collectors.groupingBy(item -> item.getOtypeName()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void addVipListFromExcel(List<AddVipByExcelDTO> list, Map<String, Object> extra, BigInteger btypeId) {
        //记录付费等级付款方式
        Map<BigInteger, PayMentDTO> levelPaymentMap = new HashMap<>();
        BigDecimal levelTotal = BigDecimal.ZERO;
        //记录储值付款方式
        Map<BigInteger, PayMentDTO> storePaymentMap = new HashMap<>();
        BigDecimal storeTotal = BigDecimal.ZERO;
        //依次批量插入 会员，客户表、客户拓展表、客户联系方式表、会员资产，会员消费记录、会员门店，会员权益卡,其中权益卡需要先查询权益卡的id
        SsVipLevel defaultVipLevel = getDefaultVipLevel();
        //遍历，拿到插入的vip信息，vip关联的门店id列表，vip关联的权益卡
        List<AddOrEditVipDTO> dtos = new ArrayList<>();
        for (AddVipByExcelDTO excelDTO : list) {
            AddOrEditVipDTO addOrEditVipDTO = new AddOrEditVipDTO();
            addOrEditVipDTO.setVipLevelId(excelDTO.getVipLevelId() == null ? defaultVipLevel.getId() :
                    excelDTO.getVipLevelId());
            addOrEditVipDTO.setName(excelDTO.getName());
            addOrEditVipDTO.setPhone(excelDTO.getPhone());
            addOrEditVipDTO.setSex(excelDTO.getSex());
            addOrEditVipDTO.setBirthday(excelDTO.getBirthday());
            addOrEditVipDTO.setEmail(excelDTO.getEmail());
            addOrEditVipDTO.setGrowthValue(StringUtils.isBlank(excelDTO.getGrowthValue()) ? 0 :
                    Integer.parseInt(excelDTO.getGrowthValue()));
            addOrEditVipDTO.setScore(Integer.parseInt(excelDTO.getScore()));
            addOrEditVipDTO.setStore(new BigDecimal(excelDTO.getStore()));
            addOrEditVipDTO.setAtypeId(excelDTO.getAtypeId());
            addOrEditVipDTO.setPaywayId(excelDTO.getPaywayId());
            addOrEditVipDTO.setLevelRuleId(excelDTO.getLevelRuleId());
            addOrEditVipDTO.setValidity(excelDTO.getValidity());
            addOrEditVipDTO.setLevelPrice(excelDTO.getPrice());
            addOrEditVipDTO.setOtypeIds(excelDTO.getOtypeIds());
            //记录储值付款方式
            if (addOrEditVipDTO.getStore() != null && addOrEditVipDTO.getStore().compareTo(BigDecimal.ZERO) > 0) {
                storeTotal = storeTotal.add(addOrEditVipDTO.getStore());
                addPayment(storePaymentMap, addOrEditVipDTO.getAtypeId(), addOrEditVipDTO.getPaywayId(),
                        addOrEditVipDTO.getStore());
            }
            //记录付费等级付款方式
            if (excelDTO.getVipType() == 1) {
                BigDecimal levelPrice = new BigDecimal(excelDTO.getPrice());
                levelTotal = levelTotal.add(levelPrice);
                addPayment(levelPaymentMap, addOrEditVipDTO.getAtypeId(), addOrEditVipDTO.getPaywayId(), levelPrice);
            }
            // 权益卡
            //权益卡名称
            if (excelDTO.getRightsCardNames() != null && !excelDTO.getRightsCardNames().isEmpty()) {
                List<String> rightsCardNameList =
                        excelDTO.getRightsCardNames().stream().distinct().collect(Collectors.toList());
                //将权益卡转换成id
                List<SsCardTemplate> rightsCards = vipRightsCardService.getRightsCardIdByNames(rightsCardNameList);
                List<BigInteger> rightsCardModelIds = new ArrayList<>();
                for (SsCardTemplate rightsCard : rightsCards) {
                    rightsCardModelIds.add(rightsCard.getId());
                }
                addOrEditVipDTO.setRightsCardModelIds(rightsCardModelIds);
            }
            dtos.add(addOrEditVipDTO);
        }
        MemberAssertsChange memberAssertsChange = new MemberAssertsChange();
        List<VipAsserts> assertsList = new ArrayList<>();
        List<SsVip> vips = new ArrayList<>();
        List<SsVipFeeRecord> feeRecords = new ArrayList<>();
        List<SsVip> customers = new ArrayList<>();
        List<GiveCardToVipRequest> levelCardToVipList = new ArrayList<>();
        List<VipRechargeDTO> rechargeDTOS = new ArrayList<>();
        List<SsVipStore> stores = new ArrayList<>();
        BigInteger vchcode = UId.newId();
        BigInteger levelVchcode = UId.newId();
        buildVip(dtos, vips, stores, customers, assertsList,
                levelCardToVipList, rechargeDTOS, feeRecords, levelVchcode);
        ProcessUtil.appendInfo("构建完成，开始导入会员，当前进度：" + 30 + "%", 30);
        // 批量插入
        addVipList(vips, stores, customers);
        //充值生成收款单
        if (storeTotal.compareTo(BigDecimal.ZERO) > 0) {
            String billNumber = submitRechargeBill(vchcode, storeTotal, "批量导入会员时充值", null, new ArrayList<>(storePaymentMap.values()),
                    btypeId, null);
            extra.put("receiptVchcode", vchcode);
            if (!rechargeDTOS.isEmpty()) {
                rechargeRecordListService.insertRechargeRecordList(rechargeDTOS, vchcode, RechargeChargeType.RECHARGE);
            }
            memberAssertsChange.setVchcode(vchcode);
            memberAssertsChange.setVchtype(com.wsgjp.ct.framework.enums.Vchtypes.ReceiptBill);
            memberAssertsChange.setBillNumber(billNumber);
        }
        ProcessUtil.appendInfo("客户导入完成，开始导入资产，当前进度：" + 60 + "%", 60);
        // 资产变动
        if (!assertsList.isEmpty()) {
            memberAssertsChange.setSourceOperation(AssertsSourceOperation.NEW_VIP);
            memberAssertsChange.setOperationSource(AssertsSourceType.PC);
            memberAssertsChange.setMemo("会员导入");
            memberAssertsChange.setVipAsserts(assertsList);
            memberAssertsChangeService.vipAssertsChange(memberAssertsChange);
        }
        ProcessUtil.appendInfo("资产导入完成，开始导入等级权益卡，当前进度：" + 80 + "%", 80);
        ssCardTemplateService.giveCardToVipList(levelCardToVipList, null, false);
        //付费等级生成其他收入单
        ProcessUtil.appendInfo("等级权益卡导入完成，开始生成会员等级收入单，当前进度：" + 90 + "%", 90);
        if (levelTotal.compareTo(BigDecimal.ZERO) > 0) {
            submitVipLevelBill(levelVchcode, levelTotal, "会员会费收入", null, new ArrayList<>(levelPaymentMap.values()),
                    null, btypeId, null, null);
            extra.put("levelVchcode", levelVchcode);
            if (!feeRecords.isEmpty()) {
                ssVipMapper.insertVipFeeRecord(feeRecords);
            }
        }
    }

    private void addPayment(Map<BigInteger, PayMentDTO> paymentMap, BigInteger atypeId, BigInteger paywayId,
                            BigDecimal money) {
        PayMentDTO payment = paymentMap.get(paywayId);
        if (payment == null) {
            payment = new PayMentDTO();
            payment.setAtypeId(atypeId);
            payment.setPaywayId(paywayId);
            payment.setCurrencyAtypeTotal(BigDecimal.ZERO);
        }
        BigDecimal total = payment.getCurrencyAtypeTotal();
        total = total.add(money);
        payment.setCurrencyAtypeTotal(total);
        paymentMap.put(paywayId, payment);
    }

    public void buildVip(List<AddOrEditVipDTO> dtos, List<SsVip> vips, List<SsVipStore> stores, List<SsVip> customers,
                         List<VipAsserts> assertsList,
                         List<GiveCardToVipRequest> levelCardToVipList, List<VipRechargeDTO> rechargeDTOS,
                         List<SsVipFeeRecord> feeRecords, BigInteger levelVchcode) {
        SsVipLevelAssessPeriod levelAssessPeriod = levelService.getLevelAssessPeriod();
        Date now = new Date();
        List<PosBuyer> buyers = new ArrayList<>();

        for (AddOrEditVipDTO dto : dtos) {
            VipAsserts asserts = new VipAsserts();
            List<MemberAssert> vipAsserts = new ArrayList<>();
            //会员表
            SsVip vip = BeanUtils.copyProperties(dto, SsVip.class);
            vip.setTags("[]");
            vip.setLevelId(dto.getVipLevelId() == null ? getDefaultVipLevel().getId() : dto.getVipLevelId());
            vip.setGrowthValue(dto.getGrowthValue() == null ? 0 : dto.getGrowthValue());
            if (dto.getOtypeIds() == null || dto.getOtypeIds().isEmpty()) {
                vip.setApplyStoreType(0);
            } else {
                vip.setApplyStoreType(1);
            }
            if (dto.getGrowthValue() != null && dto.getGrowthValue() != 0) {
                vipAsserts.add(MemberAssert.createData(3, new BigDecimal(dto.getGrowthValue()), "新增会员", null, null, AssertsChangeType.NEW_VIP));
            }
            vip.setId(UId.newId());
            //计算付费会员过期时间
            if (dto.getValidity() != null && dto.getValidity() >= 0) {
                vip.setValidDate(SVIPUtils.getValidDate(dto.getValidity(), now));
                feeRecords.add(new SsVipFeeRecord(UId.newId(), levelVchcode, CurrentUser.getProfileId(), vip.getId(),
                        new BigDecimal(dto.getLevelPrice()), 1, now, vip.getValidDate()));
            }
            vip.setProfileId(CurrentUser.getProfileId());
            vip.setPhone(dto.getPhone());
            vip.setBirthday(DateUtils.parse(dto.getBirthday()));
            //默认开启
            vip.setEnableStore(true);
            vip.setEnableScore(true);
            //付费会员无需开启
            if (dto.getLevelRuleId() == null) {
                // 开没开等级评估周期
                if (levelAssessPeriod != null && levelAssessPeriod.getLevelAssessPeriod() == 1) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.add(Calendar.DATE, levelAssessPeriod.getPeriod());
                    vip.setNextLevelAssessTime(calendar.getTime());
                }
            }
            // 可用门店
            if (vip.getApplyStoreType() == 1 && !vip.getOtypeIds().isEmpty()) {
                for (BigInteger otypeId : vip.getOtypeIds()) {
                    SsVipStore store = new SsVipStore();
                    store.setId(UId.newId());
                    store.setProfileId(CurrentUser.getProfileId());
                    store.setVipId(vip.getId());
                    store.setStoreId(otypeId);
                    stores.add(store);
                }
            }
            // 客户id
            buildCustomer(vip, dto, customers, buyers);
            // 资产
            buildVipAsserts(vip, dto, vipAsserts, rechargeDTOS);
            SsVipLevel vipLevelByLevelId = levelService.getVipLevelByLevelId(vip.getLevelId());
            //开卡赠送
            buildOpenCardAsserts(vip, vipLevelByLevelId, vipAsserts);

            //普通权益卡
            BuildRightsCard(dto.getRightsCardModelIds(), vipAsserts, "普通权益卡");


            // 保存资产变动表
            if (!vipAsserts.isEmpty()) {
                asserts.setVipId(vip.getId());
                asserts.setVipAssert(vipAsserts);
                assertsList.add(asserts);
            }
            // 等级权益卡
            List<BigInteger> vipIds = new ArrayList<>();
            vipIds.add(vip.getId());
            List<BigInteger> modelIds = new ArrayList<>();
            modelIds.add(vipLevelByLevelId.getCardTemplateId());
            GiveCardToVipRequest cardToVipRequest = new GiveCardToVipRequest();
            cardToVipRequest.setCardIds(modelIds);
            cardToVipRequest.setVipIds(vipIds);
            cardToVipRequest.setComment("新增会员等级权益卡");
            cardToVipRequest.setThrowException(false);
            levelCardToVipList.add(cardToVipRequest);
            vips.add(vip);
        }
        // 手机号加密
        if (!buyers.isEmpty()) {
            sisClientService.batchEncryptBuyers(buyers);
            for (SsVip customer : customers) {
                for (PosBuyer buyer : buyers) {
                    if (customer.getDeliveryId().equals(buyer.getBuyerId())) {
                        customer.setPhone(buyer.getCustomerReceiverPhone());
                        customer.setPi(buyer.getPi());
                        customer.setDi(buyer.getDi());
                    }
                }
            }
        }
    }

    private void buildCustomer(SsVip vip, AddOrEditVipDTO dto, List<SsVip> customers, List<PosBuyer> buyers) {
        // 新建客户
        BigInteger customerId = UId.newId();
        BigInteger buyerId = UId.newId();
        SsVip customer = new SsVip();
        customer.setCustomerId(customerId);
        customer.setProfileId(CurrentUser.getProfileId());
        customer.setName(dto.getName() == null ? "" : dto.getName());
        customer.setPhone(dto.getPhone());
        customer.setEmail(dto.getEmail() == null ? "" : dto.getEmail());
        customer.setIdCode(StringUtils.isEmpty(dto.getIdCode()) ? "" : dto.getIdCode());
        customer.setSex(dto.getSex());
        customer.setBirthday(DateUtils.parse(dto.getBirthday()));
        customer.setDeliveryId(buyerId);
        customer.setExtendId(UId.newId());
        customer.setDeliveryInfoId(UId.newId());
        customers.add(customer);
        vip.setCustomerId(customerId);

        // 用于手机号加密
        PosBuyer buyer = new PosBuyer();
        buyer.setCustomerReceiverPhone(dto.getPhone());
        buyer.setBuyerId(buyerId);
        buyers.add(buyer);
    }

    /**
     * 会员资产变动
     * 包含积分和储值
     * 需要修改会员资产，以及记录会员资产变动
     */
    private void buildVipAsserts(SsVip vip, AddOrEditVipDTO dto,
                                 List<MemberAssert> vipAsserts,
                                 List<VipRechargeDTO> rechargeDTOS) {
        //记录会员资产变动
        //储值
        if (dto.getStore() != null && dto.getStore().compareTo(BigDecimal.ZERO) > 0) {
            vipAsserts.add(MemberAssert.createData(
                    1,
                    dto.getStore(),
                    "新增会员",
                    null,
                    null,
                    AssertsChangeType.NEW_VIP
            ));
            // 充值记录
            VipRechargeDTO rechargeDTO = new VipRechargeDTO();
            rechargeDTO.setVipId(vip.getId());
            rechargeDTO.setRechargeMoney(dto.getStore());
            rechargeDTO.setStoreBalance(dto.getStore());
            rechargeDTOS.add(rechargeDTO);
        }
        //积分
        if (dto.getScore() != null && dto.getScore() > 0) {
            vipAsserts.add(MemberAssert.createData(
                    0,
                    new BigDecimal(dto.getScore()),
                    "新增会员",
                    null,
                    null,
                    AssertsChangeType.NEW_VIP
            ));
        }
    }

    private void BuildRightsCard(List<BigInteger> cardModelIds,
                                 List<MemberAssert> vipAsserts, String detailMemo) {
        if (cardModelIds != null && !cardModelIds.isEmpty()) {
            for (BigInteger cardModelId : cardModelIds) {
                // 会员初始卡券
                vipAsserts.add(MemberAssert.createData(4, BigDecimal.ONE, "初始" + detailMemo, null, cardModelId, AssertsChangeType.NEW_VIP));
            }
        }
    }

    private void addVipList(List<SsVip> vips, List<SsVipStore> stores, List<SsVip> customers) {
        // 保存会员资料
        ssVipMapper.insertVipList(vips);
        if (stores != null && !stores.isEmpty()) {
            ssVipMapper.insertVipStore(stores);
        }
        ProcessUtil.appendInfo("会员导入完成，开始导入客户，当前进度：" + 50 + "%", 50);
        ssVipMapper.insertCustomerList(customers);
        // 联系方式
        ssVipMapper.insertPlBuyerList(customers);
        // 性别、生日
        ssVipMapper.insertPlBuyerExtendList(customers);
        //顾客和联系方式中间表
        ssVipMapper.insertCustomerWithDeliveryList(customers);
        saveNullData(vips);
    }

    public void downloadVipList(List<SsVip> vips, List<SsVipStore> stores, List<SsVip> customers, List<VipAsserts> assertsList,
                                List<GiveCardToVipRequest> levelCardToVipList) {
        ssVipMapper.insertVipList(vips);
        if (stores != null && !stores.isEmpty()) {
            ssVipMapper.insertVipStore(stores);
        }
        ssVipMapper.insertCustomerList(customers);
        // 联系方式、身份证号
        ssVipMapper.insertPlBuyerList(customers);
        // 性别、生日
        ssVipMapper.insertPlBuyerExtendList(customers);
        //顾客和联系方式中间表
        ssVipMapper.insertCustomerWithDeliveryList(customers);
        saveNullData(vips);

        if (assertsList != null && !assertsList.isEmpty()) {
            MemberAssertsChange memberAssertsChange = new MemberAssertsChange();
            memberAssertsChange.setSourceOperation(AssertsSourceOperation.NEW_VIP);
            memberAssertsChange.setOperationSource(AssertsSourceType.PC);
            memberAssertsChange.setMemo("会员下载");
            memberAssertsChange.setVipAsserts(assertsList);
            memberAssertsChangeService.vipAssertsChange(memberAssertsChange);
        }

        ssCardTemplateService.giveCardToVipList(levelCardToVipList, null, false);


    }

    private void saveNullData(List<SsVip> vips) {
        // 应给所有会员构建资产表、消费记录表、消费信息表的初始数据
        List<SsMemberAsserts> memberAsserts = new ArrayList<>();
        List<MemberConsume> memberConsumes = new ArrayList<>();
        List<MemberConsumptionRecord> memberConsumptionRecords = new ArrayList<>();
        for (SsVip vip : vips) {
            memberAsserts.add(SsMemberAsserts.createNullData(vip.getId()));
            memberConsumes.add(MemberConsume.createNullData(vip.getId()));
            memberConsumptionRecords.add(MemberConsumptionRecord.createNullData(vip.getId()));
        }
        // 资产表
        memberAssertsMapper.insertVipAsserts(memberAsserts);
        // 消费记录表
        memberAssertsMapper.insertVipConsumes(memberConsumes);
        // 消费信息表
        memberAssertsMapper.insertConsumptionRecords(memberConsumptionRecords);
    }

    @Override
    public List<VipWithLevelScoreRightsCardDTO> getVipWithLevelScoreRightsCardByPhone(String phone,
                                                                                      BigInteger otypeId,
                                                                                      Integer type,
                                                                                      Boolean fuzzyQuery) throws Exception {
        if (phone == null || type == null) {
            return null;
        }
        List<VipWithLevelScoreRightsCardDTO> result = ssVipMapper.getVipByPhone(phone, otypeId, type, fuzzyQuery,
                CurrentUser.getProfileId());
        // 将会员信息、等级权益卡查询出来
        // 将权益卡、优惠券分别查询出来
        List<BigInteger> vipIds = new ArrayList<>();
        for (VipWithLevelScoreRightsCardDTO vip : result) {
            if (vip != null && vip.getVip() != null) {
                vipIds.add(vip.getVip().getId());
                // 付费会员是否过期
                vip.getVip().setExpired(SVIPUtils.isVipLevelExpired(vip.getLevel().isVipType() ? 1 : 0,
                        vip.getVip().getValidDate()));
            }
        }

        // 将优惠券查询出来
        if (!vipIds.isEmpty()) {
            List<Integer> cardTypes = new ArrayList<>();
            cardTypes.add(2);
            cardTypes.add(3);
            cardTypes.add(4);
            List<SsCard> cardList = cardService.getCardListByVipIdAndCardTypeYH(vipIds, cardTypes, otypeId);
            if (cardList != null && !cardList.isEmpty()) {
                for (VipWithLevelScoreRightsCardDTO vipWithLevelScoreRightsCardDTO : result) {
                    List<SsCard> cards =
                            cardList.stream().filter(ssCard -> ssCard.getVipId().equals(vipWithLevelScoreRightsCardDTO.getVip().getId())).collect(Collectors.toList());
                    if (!cards.isEmpty()) {
                        vipWithLevelScoreRightsCardDTO.setCardList(cards);
                    }
                }
            }
        }
        return result;
    }

    @Override
    public List<SsVipTags> getTagsList() {
        List<SsVipTags> tags = ssVipMapper.getTags(CurrentUser.getProfileId());
        for (SsVipTags tag : tags) {
            tag.setOperate("[{\"delete\":\"<font class=\\\"aicon-shanchu clickIco\\\" style=\\\"color:#2196f3;" +
                    "padding-left:10px\\\" title=\\\"删除\\\"></font>\"}]");
        }
        return tags;
    }

    @Override
    public ResultCode deleteTag(BigInteger id) {
        // 删除标签  正在使用的标签不可以删除
        // 判断有没有会员使用这个标签
        String tag = ssVipMapper.selectVipCardTags(id, CurrentUser.getProfileId());
        if (!StringUtil.isNullOrEmpty(tag)) {
            return new ResultCode(500, "标签已使用，不能删除");
        }
        int delete = ssVipMapper.deleteTag(id, CurrentUser.getProfileId());
        if (delete == 1) {
            return new ResultCode(200, "删除成功");
        } else {
            return new ResultCode(500, "删除失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultCode insertTags(List<SsVipTags> tags) {
        for (SsVipTags tag : tags) {
            tag.setProfileId(CurrentUser.getProfileId());
            if (BigInteger.ZERO.equals(tag.getId())) {
                //id为0的是新增
                tag.setId(UId.newId());
                tag.setProfileId(CurrentUser.getProfileId());
                int i = ssVipMapper.insertTag(tag);
                if (i != 1) {
                    throw new RuntimeException("标签新增失败");
                }
            } else {
                // id不为0的是修改
                int j = ssVipMapper.updateTag(tag);
                if (j != 1) {
                    throw new RuntimeException("标签修改失败");
                }
            }
        }
        return new ResultCode(200, "成功");
    }

    @Override
    public Map<String, Object> getAllTags(BigInteger id) {
        Map<String, Object> map = new HashMap<>();
        map.put("code", 200);
        map.put("values", null);
        // 如果没有id就是新增
        if (id != null) {
            SsVip ssVip = ssVipMapper.selectVipById(id, CurrentUser.getProfileId());
            if (!StringUtil.isNullOrEmpty(ssVip.getTags())) {
                List<String> list = JSONObject.parseObject(ssVip.getTags(), List.class);
                map.put("values", list);
            }
        }
        List<SsVipTags> tags = ssVipMapper.getTags(CurrentUser.getProfileId());
        map.put("tags", tags);
        return map;
    }

    @Override
    public List<SsVip> getCustomerByPhone(String phone, BigInteger customerId) {
        List<SsVip> vip = new ArrayList<>();
        // 手机号校验
        if (Pattern.compile("^[1-9]\\d*$").matcher(phone).matches()) {
            if (phone.length() < 11) {
                throw new RuntimeException("手机号不能小于11位");
            }
            // 手机号加密
            PosBuyer buyerByPhone = sisClientService.getBuyerByPhone(phone);
            String encryptPhone = buyerByPhone.getPi();
            vip = ssVipMapper.getCustomerByPhone(phone, encryptPhone, CurrentUser.getProfileId(), customerId, null);
        } else {
            throw new RuntimeException("手机号格式不正确");
        }
        return vip;
    }

    @Override
    public void checkPhone(String phone, BigInteger customerId, Integer applyStoreType, List<BigInteger> otypeIds) {
        // 手机号格式校验
        if (Pattern.compile("^[1-9]\\d*$").matcher(phone).matches()) {
            if (phone.length() < 11) {
                throw new RuntimeException("手机号不能小于11位");
            }
            // 手机号重复校验
            // 手机号加密
            PosBuyer buyerByPhone = sisClientService.getBuyerByPhone(phone);
            String encryptPhone = buyerByPhone.getPi();
            // 根据customerId是否为空判断是否排除会员自己，customerId不为空，则代表要排除自己
            List<SsVip> vip = ssVipMapper.getCustomerByPhone(phone, encryptPhone, CurrentUser.getProfileId(),
                    customerId, otypeIds);
            // 根据方法传值，customerId决定是否排除自己，otypeIds 决定是否排除所传门店和 "全部"可用门店
            if (!vip.isEmpty()) {
                // 手机号解密
                List<BigInteger> buyerIds = vip.stream().map(SsVip::getBuyerId).collect(Collectors.toList());
                List<PosBuyer> buyers = sisClientService.batchDecryptBuyers(buyerIds);
                if (!buyers.isEmpty()) {
                    for (SsVip ssVip : vip) {
                        for (PosBuyer buyer : buyers) {
                            if (buyer.getBuyerId().equals(ssVip.getBuyerId())) {
                                ssVip.setPhone(buyer.getCustomerReceiverPhone());
                                break;
                            }
                        }
                    }
                }
                phone = vip.get(0).getPhone();
                // 不会出现重复会员可用门店既有全部又有部分的数据
                if (vip.size() == 1 && vip.get(0).getApplyStoreType() == 0) {
                    // 只有一条可用门店为全部的数据
                    String message = String.format("手机号%s在所有门店下已有会员", phone);
                    throw new RuntimeException(message);
                } else {
                    // 重复会员可用门店为部分
                    // 查询重复会员所在的门店
                    List<BigInteger> vipIds = vip.stream().map(SsVip::getId).collect(Collectors.toList());
                    List<BaseOtype> otypes = ssVipMapper.getRepeatVipStore(vipIds, CurrentUser.getProfileId());
                    StringBuilder otypeNames = new StringBuilder();

                    // 当新会员可用门店为全部时
                    if (applyStoreType == 0) {
                        // 只用展示重复会员的门店名称
                        for (BaseOtype otype : otypes) {
                            otypeNames.append(otype.getFullname()).append("、");
                        }
                    } else {
                        AssertUtil.emptyTrw(otypeIds, "请填写会员可用门店");
                        // 取重复会员所在门店 与 传来的otypeId的交集，取门店名称展示
                        AssertUtil.emptyTrw(otypes, "未查询到会员所在门店");
                        for (BaseOtype otype : otypes) {
                            for (BigInteger otypeId : otypeIds) {
                                if (otypeId.equals(otype.getId())) {
                                    otypeNames.append(otype.getFullname()).append("、");
                                }
                            }
                        }
                    }
                    otypeNames.deleteCharAt(otypeNames.length() - 1);
                    String message = String.format("手机号%s在%s下已有会员", phone, otypeNames);
                    throw new RuntimeException(message);
                }
            }
        } else {
            throw new RuntimeException("手机号格式不正确");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultCode updateVipTags(List<Map<String, String>> list) {
        for (Map<String, String> stringStringMap : list) {
            if (stringStringMap.get("tagIds").length() > 220) {
                throw new RuntimeException("会员最多能加10个标签");
            }
            int i = ssVipMapper.updateVipTags(stringStringMap.get("vipId"), stringStringMap.get("tagIds"),
                    CurrentUser.getProfileId());
            if (i != 1) {
                throw new RuntimeException("保存失败");
            }
        }
        return new ResultCode(200, "保存成功");
    }


    /**
     * 获取默认会员等级
     */
    private SelectLevelResponseDTO getDefaultVipLevel() {
        SelectLevelRequestDTO request = new SelectLevelRequestDTO();
        request.setPage(1);
        request.setPageSize(1);
        request.setVipType(0);
        PageInfo<SelectLevelResponseDTO> vipLevelList = levelService.getAllSsVipLevelList(request);
        List<SelectLevelResponseDTO> list = vipLevelList.getList();
        if (list == null || list.isEmpty()) {
            throw new RuntimeException("还未创建任何会员等级");
        }
        return list.get(0);
    }

    private static void checkFilePrefix(MultipartFile file, String... prefixs) {
        if (file == null || file.getSize() == 0) {
            throw new RuntimeException("请选择导入文件");
        }
        String filename = file.getOriginalFilename();
        String prefix = filename.substring(filename.lastIndexOf(".") + 1);
        if (!Arrays.asList(prefixs).contains(prefix)) {
            throw new RuntimeException("文件格式必须为：" + String.join(",", prefixs));
        }
    }


    /**
     * @param ssVipBill 会员和单据关联信息
     * @return
     */
    @Override
    public CustomResult insertOrUpdateVipBill(SsVipBill ssVipBill) {
        boolean alreadyHas = ssVipMapper.checkRepeatVipBill(ssVipBill.getVchcode(),
                CurrentUser.getProfileId().toString());
        ssVipBill.setProfileId(CurrentUser.getProfileId());
        boolean isSuccess = true;
        String message = "新增";
        if (!alreadyHas) {
            ssVipBill.setId(UId.newId());
            isSuccess = ssVipMapper.insertVipBill(ssVipBill);
        } else {
            message = "更新";
            isSuccess = ssVipMapper.updateVipBill(ssVipBill);
        }
        if (isSuccess) {
            return new CustomResult(ngp.starter.web.base.ResultCode.SUCCESS.getCode(), "");
        } else {
            return new CustomResult(ngp.starter.web.base.ResultCode.FAILED.getCode(), message + "会员和单据关联失败");
        }

    }

    @Override
    public SsVip getVipByCustomerId(BigInteger customerId) {
        return ssVipMapper.getVipByCustomerId(customerId, CurrentUser.getProfileId());
    }

    @Override
    public Integer selectVipNum() {
        return ssVipMapper.selectVipNum(CurrentUser.getProfileId());
//        return 0;
    }

    @Override
    public PageInfo<VipAssertChange> selectVipAssertChangeByVipId(PageRequest<BigInteger> request) {
        if (request == null || request.getQueryParams() == null) {
            throw new RuntimeException("会员id不能为空");
        }

        if (request.getPageIndex() > 0 && request.getPageSize() > 0) {
            PageHelper.startPage(request.getPageIndex(), request.getPageSize());
        }
        List<VipAssertChange> list = vipAssertChangeMapper.selectVipAssertChangeByVipId(CurrentUser.getProfileId(),
                request.getQueryParams());
        return new PageInfo<>(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void editCustomerBaseInfo(EditCustomerBaseInfoDTO request) {
        if (request == null || request.getCustomerId() == null) {
            throw new RuntimeException("顾客id不能为空");
        }
        if (request.getChangeType() == null) {
            return;
        }
        request.setProfileId(CurrentUser.getProfileId());
        // 获取原会员信息
        SsVip customer = ssVipMapper.getCustomerById(request.getProfileId(), request.getCustomerId());
        // 手机号解密
        List<PosBuyer> buyers = sisClientService.batchDecryptBuyers(Collections.singletonList(customer.getBuyerId()));
        if (!buyers.isEmpty()) {
            customer.setPhone(buyers.get(0).getCustomerReceiverPhone());
        }

        List<String> messages = new ArrayList<>();
        String message = "";
        // 手机号校验
        if (request.getChangeType() == EditCustomerType.all || request.getChangeType() == EditCustomerType.phone) {
            if (StringUtils.isBlank(request.getPhone())) {
                throw new RuntimeException("手机号码不能为空");
            }
            checkPhone(request.getPhone(), request.getCustomerId(), request.getApplyStoreType(),
                    request.getApplyStoreType() == 1 ? request.getOtypeIds() : null);
            String phone = request.getPhone();
            // 手机号加密
            PosBuyer buyerByPhone = sisClientService.getBuyerByPhone(request.getPhone());
            request.setPhone(buyerByPhone.getCustomerReceiverPhone());
            request.setDi(buyerByPhone.getDi());
            request.setPi(buyerByPhone.getPi());
            ssVipMapper.updatePhone(request);
            if (!customer.getPhone().equals(request.getPhone())) {
                message = String.format("会员手机号由【%s】改为【%s】", customer.getPhone(), phone);
                messages.add(message);
            }
        }
        // 地址
        if (request.getChangeType() == EditCustomerType.all || request.getChangeType() == EditCustomerType.address) {
            if (StringUtils.isNotBlank(request.getAddress())) {
                AssertUtil.trueTrw(request.getAddress().length() > 200, "详细地址不能超过200字符");
            }
            ssVipMapper.updateAddress(request);
            if (!customer.getFullAddress().equals(request.getFullAddress())) {
                message = String.format("会员地址由【%s】改为【%s】", customer.getFullAddress(), request.getFullAddress());
                messages.add(message);
            }
        }
        // 姓名、邮箱
        if (request.getChangeType() == EditCustomerType.all || request.getChangeType() == EditCustomerType.name || request.getChangeType() == EditCustomerType.email) {
            PosSysData sys = new PosSysData();
            sys.setKey(MemberSettingEnum.POS_VIP_NAME_REQUIRED);
            String vipNameRequired = systemConfigService.getConfigByKey(sys);
            if ("1".equals(vipNameRequired)) {
                AssertUtil.trueTrw(StringUtils.isBlank(request.getName()), "会员名称不能为空");
            }
            ssVipMapper.updateCustomerEmailAndName(request);
            if (!customer.getName().equals(request.getName())) {
                message = String.format("会员姓名由【%s】改为【%s】", customer.getName(), request.getName());
                messages.add(message);
            }
            if (!customer.getEmail().equals(request.getEmail())) {
                message = String.format("会员邮箱由【%s】改为【%s】", customer.getEmail(), request.getEmail());
                messages.add(message);
            }
        }
        // 生日、性别
        if (request.getChangeType() == EditCustomerType.all || request.getChangeType() == EditCustomerType.birthday || request.getChangeType() == EditCustomerType.sex) {
            if (StringUtils.isBlank(request.getBirthday())) {
                request.setBirthday("0001-01-01 00:00:00.000");
            }
            ssVipMapper.updateCustomerBirthdayAndSex(request);
            Date nowBirthday = StringUtils.isBlank(request.getBirthday()) ? null :
                    DateUtils.parse(request.getBirthday());
            if ((nowBirthday == null && customer.getBirthday() != null) || (nowBirthday != null && customer.getBirthday() == null)) {
                message = String.format("会员生日由【%s】改为【%s】", customer.getBirthday() == null ? "" :
                                DateUtils.formatDate(customer.getBirthday()),
                        "0001-01-01 00:00:00.000".equals(request.getBirthday()) ? "" : request.getBirthday());
                messages.add(message);
            } else if (DateUtils.compare(customer.getBirthday(), nowBirthday) != 0) {
                message = String.format("会员生日由【%s】改为【%s】", DateUtils.formatDate(customer.getBirthday()), ("0001-01-01" +
                        " " +
                        "00:00:00.000").equals(request.getBirthday()) ? "" : request.getBirthday());
                messages.add(message);
            }
            if (!Objects.equals(request.getSex(), customer.getSex())) {
                message = String.format("会员性别由【%s】改为【%s】", getSexString(customer.getSex()),
                        getSexString(request.getSex()));
                messages.add(message);
            }
        }
        // 标签
        if (request.getChangeType() == EditCustomerType.all || request.getChangeType() == EditCustomerType.tags) {
            if (request.getVipId() == null) {
                throw new RuntimeException("会员id不能为空");
            }
            ssVipMapper.updateVipTags(request.getVipId().toString(), request.getTags(), request.getProfileId());
        }
        // 可用门店
        if (request.getChangeType() == EditCustomerType.all || request.getChangeType() == EditCustomerType.store) {
            AssertUtil.nullTrw(request.getApplyStoreType(), "可用门店选项不能为空");
            // 先验证手机号是否重复
            if (request.getChangeType() == EditCustomerType.store) {
                // 只有单据修改可用门店时要验证，其他情况前面已经验证过了
                checkPhone(customer.getPhone(), request.getCustomerId(), request.getApplyStoreType(),
                        request.getApplyStoreType() == 1 ? request.getOtypeIds() : null);
            }
            // 获取原可用门店
            List<BaseOtype> oldOtype = ssVipMapper.getRepeatVipStore(Collections.singletonList(request.getVipId()),
                    request.getProfileId());
            List<BigInteger> oldOtypeIds = oldOtype.stream().map(BaseOtype::getId).collect(Collectors.toList());
            String oldOtypeNames = oldOtype.stream().map(BaseOtype::getFullname).collect(Collectors.joining("、"));
            // 清除原门店关系
            ssVipMapper.deleteVipStore(request.getVipId(), request.getProfileId());
            // 修改会员可用门店类型
            ssVipMapper.updateVipStoreType(request.getVipId(), request.getApplyStoreType(), request.getProfileId());
            if (request.getApplyStoreType() == 1) {
                // 部分
                AssertUtil.emptyTrw(request.getOtypeIds(), "可用门店不能为空");
                // 新增门店关系
                List<SsVipStore> list = new ArrayList<>();
                for (BigInteger otypeId : request.getOtypeIds()) {
                    SsVipStore store = new SsVipStore();
                    store.setId(UId.newId());
                    store.setProfileId(request.getProfileId());
                    store.setVipId(request.getVipId());
                    store.setStoreId(otypeId);
                    list.add(store);
                }
                ssVipMapper.insertVipStore(list);
            }
            // 获取会员现在的可用门店
            List<BaseOtype> newOtype = ssVipMapper.getRepeatVipStore(Collections.singletonList(request.getVipId()),
                    request.getProfileId());
            List<BigInteger> newOtypeIds = newOtype.stream().map(BaseOtype::getId).collect(Collectors.toList());
            String newOtypeNames = newOtype.stream().map(BaseOtype::getFullname).collect(Collectors.joining("、"));
            if (!customer.getApplyStoreType().equals(request.getApplyStoreType())) {
                if (customer.getApplyStoreType() == 0) {
                    // 由全部改为部分
                    message = String.format("会员可用门店由【全部】改为【部分：%s】", newOtypeNames);
                    messages.add(message);
                } else {
                    // 由部分改为全部
                    message = String.format("会员可用门店由【部分：%s】改为【全部】", oldOtypeNames);
                    messages.add(message);
                }
            }
            // 部分中改了可用门店
            if (request.getApplyStoreType() == 1 && customer.getApplyStoreType().equals(request.getApplyStoreType())
                    && !new HashSet<>(newOtypeIds).equals(new HashSet<>(oldOtypeIds))) {
                message = String.format("会员可用门店由【部分：%s】改为【部分：%s】", oldOtypeNames, newOtypeNames);
                messages.add(message);
            }
        }
        // 记录变更日志
        saveLog(request.getVipId(), messages);
    }

    private String getSexString(Integer sex) {
        if (sex == null) {
            return "";
        } else if (sex == 1) {
            return "男";
        } else if (sex == 2) {
            return "女";
        }
        return "";
    }

    public void saveLog(BigInteger vipId, List<String> messages) {
        messages.forEach(message -> {
            BaseInfoLog log = new BaseInfoLog();
            log.setObjectType("vip");
            log.setObjectId(vipId);
            log.setBody(message);
            GetBeanUtil.getBean(StoreService.class).saveLog(log);
        });
    }


    private void changeSorts(List<Sort> sorts) {
        if (sorts != null) {
            for (Sort sort : sorts) {
                if ("createTime".equals(sort.getDataField())) {
                    sort.setDataField("create_time");
                    break;
                }
                if ("payTimeCount".equals(sort.getDataField())) {
                    sort.setDataField("pay_time_count");
                    break;
                }
                if ("payMoneyCount".equals(sort.getDataField())) {
                    sort.setDataField("pay_money_count");
                    break;
                }
                if ("protectScore".equals(sort.getDataField())) {
                    sort.setDataField("protect_score");
                    break;
                }
                if ("availableScore".equals(sort.getDataField())) {
                    sort.setDataField("available_score");
                    break;
                }
                if ("growthValue".equals(sort.getDataField())) {
                    sort.setDataField("growth_value");
                    break;
                }
            }
        }
    }

    @Override
    public void getImportTemplate(HttpServletResponse response) throws IOException {
        HashMap<String, List<Integer>> params = new HashMap<>();
        // 过滤掉聚合支付
        List<Integer> list = Arrays.asList(0, 1, 4);
        params.put("paywayTypeList", list);
        List<StorePayway> paymentList =
                GetBeanUtil.getBean(StoreService.class).getStorePaymentList(params);
        PosSysData sys = new PosSysData();
        sys.setKey(MemberSettingEnum.POS_VIP_NAME_REQUIRED);
        String vipNameRequired = systemConfigService.getConfigByKey(sys);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("会员导入模板", "UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), VipImportExcelTemplateDTO.class).sheet().registerWriteHandler(new VipImportTemplateSheetWriteHandler(paymentList.stream().map(StorePayway::getPaywayName).toArray(String[]::new), vipNameRequired)).doWrite(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public ResultCode deleteVip(DeleteVipDto dto) {
        Assert.isTrue(!dto.getVipIds().isEmpty(), "未选择会员");
        // 查出已经被引用的会员
        List<BigInteger> hasBill = ssVipMapper.getVipIdInBill(CurrentUser.getProfileId(), dto.getVipIds());
        // 有储值的会员、付费会员也不能删
        List<BigInteger> hasTotal = ssVipMapper.getVipTotalByVipId(CurrentUser.getProfileId(), dto.getVipIds());
        // 只判断会员当前状态是不是付费会员，是就不能删，若之前是付费会员，现在降到了免费会员，理论上可以删
        List<BigInteger> hasLevel = ssVipMapper.getVipTypeByVipId(CurrentUser.getProfileId(), dto.getVipIds());
        // 是否是微信会员
        List<BigInteger> hasWx = ssVipMapper.getWxVipByVipIds(CurrentUser.getProfileId(), dto.getVipIds());
        hasBill.addAll(hasTotal);
        hasBill.addAll(hasLevel);
        List<BigInteger> noDelete = hasBill.stream().distinct().collect(Collectors.toList());
        List<BigInteger> needDelete =
                dto.getVipIds().stream().filter(vipId -> !noDelete.contains(vipId)).collect(Collectors.toList());
        // 需要删的会员里是否还有微信会员，有的话弹窗提示，没有的话直接删除
        List<BigInteger> needDeleteWx = needDelete.stream().filter(hasWx::contains).collect(Collectors.toList());
        if (dto.getVipIds().size() == 1) {
            // 如果只有一条会员
            AssertUtil.trueTrw(needDelete.isEmpty(), "该会员已被引用，不支持删除");
        } else {
            // 批量删除
            AssertUtil.trueTrw(needDelete.isEmpty(), "所选会员已全部被引用");
            if (!noDelete.isEmpty() && !dto.isDecidedDelete()) {
                return new ResultCode(DeleteVipResultCode.QUOTE_DELETE.getCode(),
                        DeleteVipResultCode.QUOTE_DELETE.getMessage());
            }
        }
        if (!needDeleteWx.isEmpty() && dto.isDeleteWx() == null) {
            return new ResultCode(DeleteVipResultCode.WX_DELETE.getCode(), DeleteVipResultCode.WX_DELETE.getMessage());
        }
        // 过滤微信会员
        if (!needDeleteWx.isEmpty() && !dto.isDeleteWx()) {
            needDelete =
                    needDelete.stream().filter(vipId -> !needDeleteWx.contains(vipId)).collect(Collectors.toList());
        }
        if (!needDelete.isEmpty()) {
            // 删除会员
            ssVipMapper.deleteVipList(CurrentUser.getProfileId(), needDelete);
            // 删除会员下已发放的卡券
            ssVipMapper.deleteVipCard(CurrentUser.getProfileId(), needDelete);
            // 删除会员下的积分有效期、保护期数据
            ssVipMapper.deleteScoreExpire(CurrentUser.getProfileId(), needDelete);
            ssVipMapper.deleteScoreProtect(CurrentUser.getProfileId(), needDelete);
        } else {
            return new ResultCode(DeleteVipResultCode.SUCCESS.getCode(), "未删除任何会员");
        }
        return new ResultCode(DeleteVipResultCode.SUCCESS.getCode(), "操作成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public MemberResult addVip(AddVipDTO dto) {
        if (dto.getPhone() == null || dto.getPhone().isEmpty()) {
            return new MemberResult(500, "手机号码不能为空!");
        }
        if (dto.getSex() != null && (dto.getSex() != 1 && dto.getSex() != 2)) {
            return new MemberResult(500, "性别只能为1或2!");
        }
        checkPhone(dto.getPhone(), null, dto.getApplyStoreType(), 1 == dto.getApplyStoreType() ? dto.getOtypeIds() :
                null);
        // 会员表
        SsVip vip = new SsVip();
        vip.setTags("[]");
        // 获取默认会员等级
        SsVipLevel level = getDefaultVipLevel();
        vip.setLevelId(level.getId());
        vip.setGrowthValue(0);
        vip.setPhone(dto.getPhone());
        // 开没开等级评估周期
        SsVipLevelAssessPeriod levelAssessPeriod = levelService.getLevelAssessPeriod();
        if (levelAssessPeriod != null && levelAssessPeriod.getLevelAssessPeriod() == 1) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE, levelAssessPeriod.getPeriod());
            vip.setNextLevelAssessTime(calendar.getTime());
        }

        // 新建客户
        AddOrEditVipDTO vipDTO = BeanUtils.copyProperties(dto, AddOrEditVipDTO.class);
        addCustomer(vip, vipDTO);

        addOrEditVip(vip, null);
        MemberAssertsChange memberAssertsChange = new MemberAssertsChange();
        List<MemberAssert> vipAsserts = new ArrayList<>();
        //资产表
        addVipAsserts(vip, vipDTO, memberAssertsChange, vipAsserts, null, null, null, null);

        // 普通权益卡
        addCard(dto.getRightsCardIds(), vipAsserts, "普通权益卡");

        //优惠券
        addCard(dto.getCardTemplateIds(), vipAsserts, "优惠券");

        // 等级权益卡
        List<BigInteger> vipIds = new ArrayList<>();
        vipIds.add(vip.getId());
        addDefaultVipLevelCard(vipIds);

        // 保存资产变动表
        if (!vipAsserts.isEmpty()) {
            // 新增会员不算消费
            memberAssertsChange.setSourceOperation(AssertsSourceOperation.NEW_VIP);
            memberAssertsChange.setOperationSource(AssertsSourceType.PC);
            memberAssertsChange.setMemo("新增会员");
            memberAssertsChange.setSaveVipAsserts(true);
            List<VipAsserts> assertsList = new ArrayList<>();
            VipAsserts vipAssert = new VipAsserts();
            vipAssert.setVipId(vip.getId());
            vipAssert.setVipAssert(vipAsserts);
            assertsList.add(vipAssert);
            memberAssertsChange.setVipAsserts(assertsList);
            memberAssertsChangeService.vipAssertsChange(memberAssertsChange);
        }
        MemberResult result = new MemberResult(200, "创建成功");
        result.setData(vip.getId());
        return result;
    }


    @Override
    public List<SelectVipListResponseDTO> selectVipList(SelectVipListRequestDTO dto) {
        return ssVipMapper.selectVipList(dto, null);
    }

    /**
     * 提交会员充值单据，收款单
     *
     * @param vchcode
     * @param billTotal
     * @param summary
     * @param payment
     * @param btypeId   收款单结算单位
     */
    private String submitRechargeBill(BigInteger vchcode, BigDecimal billTotal, String summary, String memo,
                                      List<PayMentDTO> payment, BigInteger btypeId, BigInteger vipId) {
        //生成收款单据
        String billNumber = "";
        try {
            billNumber = receiptBillService.submitRechargeBill(null, vchcode, billTotal, BigInteger.ZERO, BigInteger.ZERO,
                    BigInteger.ZERO, btypeId, summary, memo, payment, vipId, null);
        } catch (Exception e) {
            throw new RuntimeException("开具收款单失败," + e.getMessage());
        }
        return billNumber;
    }

    /**
     * 提交付费会员单据，其他收入单
     *
     * @param vchcode
     * @param billTotal
     * @param memo
     * @param payment
     * @param etypeId
     * @param btypeId   收款单结算单位
     */
    private void submitVipLevelBill(BigInteger vchcode, BigDecimal billTotal, String summary, String memo,
                                    List<PayMentDTO> payment, BigInteger etypeId, BigInteger btypeId,
                                    BigInteger otypeId, BigInteger vipId) {
        //生成收款单据
        try {
            receiptBillService.submitVipLevelBill(vchcode, billTotal, otypeId, etypeId, etypeId, btypeId, summary,
                    memo, payment, vipId, null);
        } catch (Exception e) {
            throw new RuntimeException("开具其他收入单失败," + e.getMessage());
        }
    }

    public void addPlShopVipCard(List<PlShopVipCard> list) {
        if (list.isEmpty()) {
            return;
        }
        ssVipMapper.addPlShopVipCard(list);
    }

    @Override
    public void submitVipLevelBill(RenewOrUpgradeSVIPDTO dto) throws Exception {
        //支付金额
        BigDecimal paidMoney = BigDecimal.ZERO;
        for (PayMentDTO atypeModel : dto.getPayment()) {
            if (atypeModel.getCurrencyAtypeTotal().compareTo(BigDecimal.ZERO) < 0) {
                throw new RuntimeException("付款方式中支付金额不能为负数");
            }
            paidMoney = paidMoney.add(atypeModel.getCurrencyAtypeTotal());
        }
        try {
            receiptBillService.submitVipLevelBill(dto.getVchcode(), paidMoney, dto.getOtypeId(), dto.getEtypeId(),
                    dto.getEtypeId(), dto.getBtypeId(), "会员升级/续费其他收款单草稿",
                    "会员升级/续费其他收款单草稿", dto.getPayment(), dto.getVipId(), BillPostState.UNCONFIRMED);
        } catch (Exception e) {
            log.error("会员升级/续费其他收款单草稿创建失败", e);
            throw new RuntimeException("会员升级/续费其他收款单草稿创建失败," + e.getMessage());
        }
    }

    /**
     * 续费或升级付费会员
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public BigInteger renewOrUpgradeSVIP(RenewOrUpgradeSVIPDTO dto) {
        if (dto.getVipId() == null) {
            throw new RuntimeException("会员id不能为空");
        }
        if (dto.getLevelId() == null) {
            throw new RuntimeException("等级id不能为空");
        }
        if (dto.getLevelRuleId() == null) {
            throw new RuntimeException("等级规则id不能为空");
        }
        if (dto.getPayment() == null || dto.getPayment().isEmpty()) {
            throw new RuntimeException("支付金额不能为空");
        }
        //支付金额
        BigDecimal paidMoney = BigDecimal.ZERO;
        for (PayMentDTO atypeModel : dto.getPayment()) {
            if (atypeModel.getCurrencyAtypeTotal().compareTo(BigDecimal.ZERO) < 0) {
                throw new RuntimeException("付款方式中支付金额不能为负数");
            }
            paidMoney = paidMoney.add(atypeModel.getCurrencyAtypeTotal());
        }
        checkValidPaymentList(dto.getPayment());
        if (dto.getBtypeId() == null || BigInteger.ZERO.equals(dto.getBtypeId())) {
            throw new RuntimeException("结算单位不能为空");
        }
        BigInteger profileId = CurrentUser.getProfileId();
        SsVip ssVip = ssVipMapper.selectVipById(dto.getVipId(), profileId);
        if (ssVip == null) {
            throw new RuntimeException("找不到该会员");
        }
        // 会员手机号解密
        List<PosBuyer> buyers = sisClientService.batchDecryptBuyers(Collections.singletonList(ssVip.getBuyerId()));
        if (!buyers.isEmpty()) {
            ssVip.setPhone(buyers.get(0).getCustomerReceiverPhone());
        }
        SsVipLevel level = levelService.getVipLevelByLevelId(dto.getLevelId());
        if (level == null) {
            throw new RuntimeException("找不到该会员等级");
        }
        if (level.getStoped()) {
            throw new RuntimeException("该会员等级已停用");
        }
        if (level.getVipType() != 1) {
            throw new RuntimeException("该会员等级非付费");
        }
        List<SsVipLevelRule> levelRules = levelService.selectVipLevelRuleByLevelId(level.getId());
        SsVipLevelRule levelRule =
                levelRules.stream().filter(rule -> rule.getId().equals(dto.getLevelRuleId())).findAny().orElse(null);
        if (levelRule == null) {
            throw new RuntimeException("当前的会员等级不包含此定价规则");
        }
        //找到续费或升级前的会员等级
        SsVipLevel oldLevel = levelService.getVipLevelByLevelId(ssVip.getLevelId());
        //是否是由免费升级到付费(付费已过期也算)
        boolean isFreeToPaid = oldLevel.getVipType() != 1 || isVipLevelExpired(oldLevel.getVipType(),
                ssVip.getValidDate());
//        if (!isFreeToPaid && level.getVipLevel() < oldLevel.getVipLevel()) {
//            throw new RuntimeException("不支持降级");
//        }
        //是否是升级
        boolean isUpgrade = !dto.getLevelId().equals(oldLevel.getId());
        //是否将之前的购买记录作废，只有平级续费，且续费时长不是永久，才会保留之前的记录
        boolean setBeforeExpired = true;
        //之前有效期内会员抵扣的金额
        BigDecimal discountMoney = BigDecimal.ZERO;
        Date now = new Date();
        //消费记录开始时间
        Date startDate = now;
        BigDecimal levelPrice = levelRule.getLevelPrice();
        if (!isFreeToPaid) {
            Date oldValidDate = ssVip.getValidDate();
            if (!isUpgrade && isForever(oldValidDate)) {
                throw new RuntimeException("当前的会员等级已经是永久有效，无需续费该等级");
            }
            /*
            平级续费，且续费时长不是永久，那么：
            1.直接在原来的记录后面插一条记录
            2.新的记录开始时间为之前有效期的过期时间
            3.无需计算抵扣
             */
            if (!isUpgrade && levelRule.getValidity() > 0) {
                setBeforeExpired = false;
                startDate = oldValidDate;
            }
            /*
            分两种情况：
            1.平级续费到永久
            2.升级续费
            均需要：
            1.将之前的记录作废
            2.插入一条新的记录，开始时间为现在
            3.需要计算抵扣金额
             */
            else {
                List<SsVipFeeRecord> feeRecords = selectFeeRecordByVipId(ssVip.getId());
                SsVipFeeRecord foreverRecord =
                        feeRecords.stream().filter(record -> isForever(record.getEndDate())).findFirst().orElse(null);
                //如果之前就是永久会员，则抵扣金额就是该等级支付的钱
                if (foreverRecord != null) {
                    discountMoney = foreverRecord.getTotal();
                } else {
                    for (SsVipFeeRecord record : feeRecords) {
                        BigDecimal total = record.getTotal();
                        //如果该有效期金额为0，则无需计算，直接抵扣金额就是0
                        if (!BigDecimal.ZERO.equals(total)) {
                            BigDecimal discount;
                            Date start = record.getStartDate();
                            Date end = record.getEndDate();
                            //该有效期已经开始
                            if (start.before(now)) {
                                //该记录一共多少天
                                int period = getDaysBetween(start, end);
                                //剩余多少天
                                int remainDays = getDaysBetween(now, end);
                                discount = new BigDecimal(remainDays).multiply(total).divide(new BigDecimal(period),
                                        2, RoundingMode.HALF_UP);
                            }
                            //未开始，全额抵扣
                            else {
                                discount = total;
                            }
                            discountMoney = discountMoney.add(discount);
                        }
                    }
                    discountMoney = discountMoney.setScale(2, RoundingMode.HALF_UP);
                    //如果花费小于抵扣，则抵扣金额就是花费金额，避免支付金额为负数
                    if (levelPrice.compareTo(discountMoney) <= 0) {
                        discountMoney = levelPrice;
                    }
                }
            }
        }
        //总价值
        BigDecimal totalAll = discountMoney.add(paidMoney);
        if (levelPrice.compareTo(totalAll) > 0) {
            throw new RuntimeException("支付金额不足");
        }
        //计算过期时间
        Date validDate = getValidDate(levelRule.getValidity(), startDate);
        //将之前的记录设置为作废
        if (setBeforeExpired) {
            ssVipMapper.setVipFeeRecordExpired(profileId, ssVip.getId());
        }
        //插入升级/续费记录
        BigInteger vchcode = UId.newId();

        if (dto.getVchcode() != null) {
            vchcode = dto.getVchcode();
        }
        SsVipFeeRecord record = new SsVipFeeRecord(UId.newId(), vchcode, profileId, ssVip.getId(), totalAll, 1,
                startDate,
                validDate);
        ssVipMapper.insertVipFeeRecord(Collections.singletonList(record));
        //修改会员等级
        ssVipMapper.changeVipLevel(profileId, ssVip.getId(), level.getId(), validDate);
        String levelName = level.getVipName();
        if (levelName != null) {
            levelName = "会员等级(" + levelName + ")";
        } else {
            levelName = "";
        }
        String summary = "会员(" + ssVip.getPhone() + ")升级/续费" + levelName;
        //更改会员等级权益
        levelService.sendLevelCard(ssVip.getId(), ssVip.getProfileId(), level.getCardTemplateId(), level.getId(),
                summary);

        //生成其他收入单
        submitVipLevelBill(vchcode, paidMoney, summary, dto.getMemo(), dto.getPayment(), dto.getEtypeId(),
                dto.getBtypeId(), dto.getOtypeId(), dto.getVipId());

        // 微信会员同步
        vipAssertsChangeService.wxVipAsync(ssVip.getId(), CurrentUser.getProfileId());
        return vchcode;
    }

    /**
     * 根据会员id查询未过期的会员等级付费记录
     */
    @Override
    public List<SsVipFeeRecord> selectFeeRecordByVipId(BigInteger vipId) {
        return ssVipMapper.selectFeeRecordByVipId(CurrentUser.getProfileId(), vipId, ZERO_POINT_DATE);
    }

    @Override
    public BigInteger addWxVip(WxMpMemberCardUserInfoResult userInfo, SsWxVipCardTemplate cardTemplate) throws Exception {
        // 先查库里有没有使用该手机号的会员，如果有，则是老会员，如果没有，则是新会员
        MemberCardUserInfo info = userInfo.getUserInfo();
        NameValues[] commonFieldList = info.getCommonFieldList();
        AddOrEditVipDTO dto = new AddOrEditVipDTO();
        dto.setVipLevelId(cardTemplate.getLevelId());
        // 来源：微信
        dto.setSourceType(1);
        // 可用门店默认全部
        dto.setApplyStoreType(0);
        // 微信来源的会员默认操作来源为系统后台
        dto.setOperationSource(AssertsSourceType.PC);
        dto.setTemplateId(cardTemplate.getId());
        dto.setOpenId(userInfo.getOpenId());
        dto.setUserCardCode(userInfo.getMembershipNumber());
        Assert.isTrue(commonFieldList != null && commonFieldList.length != 0, "未获取到会员信息");
        // 手机号、会员姓名、性别、生日、邮箱
        Arrays.stream(commonFieldList).forEach(nameValues -> {
            if (WxCommonFieldIdEnum.USER_FORM_INFO_FLAG_MOBILE.getWxType().equals(nameValues.getName())) {
                dto.setPhone(nameValues.getValue());
            }
            if (WxCommonFieldIdEnum.USER_FORM_INFO_FLAG_SEX.getWxType().equals(nameValues.getName())) {
                dto.setSex("男".equals(nameValues.getValue()) ? 1 : 2);
            }
            if (WxCommonFieldIdEnum.USER_FORM_INFO_FLAG_BIRTHDAY.getWxType().equals(nameValues.getName())) {
                dto.setBirthday(nameValues.getValue());
            }
            if (WxCommonFieldIdEnum.USER_FORM_INFO_FLAG_NAME.getWxType().equals(nameValues.getName())) {
                dto.setName(nameValues.getValue());
            }
            if (WxCommonFieldIdEnum.USER_FORM_INFO_FLAG_EMAIL.getWxType().equals(nameValues.getValue())) {
                dto.setEmail(nameValues.getValue());
            }
        });
        List<SsVip> customerByPhone = getCustomerByPhone(dto.getPhone(), null);
        AssertUtil.trueTrw(customerByPhone.size() > 1, "系统中存在多个该手机号");
        if (!customerByPhone.isEmpty()) {
            // 是老会员，保存完微信相关参数后，直接把系统内的资产同步过去就好
            // 保存微信相关信息
            SsVip vip = BeanUtils.copyProperties(dto, SsVip.class);
            vip.setId(customerByPhone.get(0).getId());
            vip.setProfileId(CurrentUser.getProfileId());
            ssVipMapper.updateVipWxData(vip);
            return customerByPhone.get(0).getId();
        }
        return addOrEditVip(dto);
    }

    @Override
    @Deprecated
    public List<VipWithLevelScoreRightsCardDTO> getVipByCode(GetVipByCodeDTO request) throws Exception {
        Assert.isTrue(StringUtils.isNotEmpty(request.getCode()), "查询Code不能为空");
        long sTime = System.currentTimeMillis();
        MonitorService monitorService = com.wsgjp.ct.sale.platform.utils.BeanUtils.getBean(MonitorService.class);
        // 先拿code当微信卡券code查询，看能不能查出手机号，如果能查出就拿这个手机号查会员，如果查不出，就拿code当手机号查
        String phone = ssVipMapper.getPhoneByUserCardCode(request.getCode(), request.getOtypeId(),
                CurrentUser.getProfileId());
        if (StringUtils.isEmpty(phone)) {
            phone = request.getCode();
        }
        List<VipWithLevelScoreRightsCardDTO> result = getVipWithLevelScoreRightsCardByPhone(phone,
                request.getOtypeId(), request.getType(),
                request.getFuzzyQuery());
        if (!result.isEmpty()) {
            monitorService.recordTP(MonitorTypeEnum.PL_API_SALE_POS_GET_VIP_TP_TIME.getTopic(), "shopType", "pos",
                    (System.currentTimeMillis() - sTime) / result.size());
        }
        return result;
    }

    @Override
    public List<SsVip> getVipByCodeNew(GetVipByCodeDTO request) {
        Assert.isTrue(StringUtils.isNotEmpty(request.getCode()), "查询Code不能为空");
        Assert.isTrue(request.getOtypeId() != null && !BigInteger.ZERO.equals(request.getOtypeId()), "门店id不能为空");
        // 这个接口只返回会员手机号和会员姓名，如需要具体信息，请调getVipWithRightsAndOtypeById接口
        // 查询参数共有4种可能：微信卡券code、完整手机号、手机号后4位、会员姓名
        // 满足任何一种可能就把数据查出来
        String code = request.getCode();
        // pi 或 未加密的手机号
        String phone = "";
        // 加密后的pi
        String encryptedPhone = "";
        // 先拿code当微信卡券code查询，看能不能查出手机号，如果能查出就拿这个手机号查会员，如果查不出，就拿code当手机号查
        // 这里返回的phone是加密后的pi 或 未加密数据的手机号
        long sTime = System.currentTimeMillis();
        MonitorService monitorService = com.wsgjp.ct.sale.platform.utils.BeanUtils.getBean(MonitorService.class);
        // 这里返回的phone是加密后的pi 或 未加密数据的手机号
        phone = ssVipMapper.getPhoneByUserCardCode(request.getCode(), request.getOtypeId(), CurrentUser.getProfileId());
        // 手机号加密
        if (StringUtils.isNotBlank(code) && code.length() > 4 && StringUtils.isBlank(phone)) {
            // 手机号大于4位的就认为是输入了完整手机号，使用密文查
            PosBuyer buyerByPhone = sisClientService.getBuyerByPhone(code);
            encryptedPhone = buyerByPhone.getPi();
        }
        List<SsVip> phoneAndNameByPhone = ssVipMapper.getPhoneAndNameByPhone(code, phone, encryptedPhone, request.getOtypeId(), request.getType(),
                CurrentUser.getProfileId());
        if (!phoneAndNameByPhone.isEmpty()) {
            List<BigInteger> buyerIds =
                    phoneAndNameByPhone.stream().map(SsVip::getBuyerId).collect(Collectors.toList());
            List<PosBuyer> buyers = sisClientService.batchDecryptBuyers(buyerIds);
            if (!buyers.isEmpty()) {
                Map<BigInteger, String> buyerMap = buyers.stream()
                        .filter(b -> Objects.nonNull(b.getBuyerId()))
                        .collect(Collectors.toMap(
                                PosBuyer::getBuyerId,
                                PosBuyer::getCustomerReceiverPhone,
                                (existing, replacement) -> existing));
                for (SsVip ssVip : phoneAndNameByPhone) {
                    if (ssVip.getBuyerId() != null && !Objects.equals(ssVip.getBuyerId(), BigInteger.ZERO)) {
                        ssVip.setPhone(buyerMap.get(ssVip.getBuyerId()));
                    }
                }
            }
        }
        monitorService.recordTP(MonitorTypeEnum.PL_API_SALE_POS_GET_VIP_TP_TIME.getTopic(), "shopType", "pos",
                (System.currentTimeMillis() - sTime));
        return phoneAndNameByPhone;
    }

    @Override
    public PageResponse<SsVip> getVipByCodeNewPage(GetVipByCodeDTO request) throws Exception {
        Assert.isTrue(StringUtils.isNotEmpty(request.getCode()), "查询Code不能为空");
        Assert.isTrue(request.getOtypeId() != null && !BigInteger.ZERO.equals(request.getOtypeId()), "门店id不能为空");

        // 设置分页参数，默认值
        int pageIndex = request.getPageIndex() != null && request.getPageIndex() > 0 ? request.getPageIndex() : 1;
        int pageSize = request.getPageSize() != null && request.getPageSize() > 0 ? request.getPageSize() : 10;

        // 这个接口只返回会员手机号和会员姓名，如需要具体信息，请调getVipWithRightsAndOtypeById接口
        // 查询参数共有4种可能：微信卡券code、完整手机号、手机号后4位、会员姓名
        // 满足任何一种可能就把数据查出来
        String code = request.getCode();
        // pi 或 未加密的手机号
        String phone = "";
        // 加密后的pi
        String encryptedPhone = "";
        // 先拿code当微信卡券code查询，看能不能查出手机号，如果能查出就拿这个手机号查会员，如果查不出，就拿code当手机号查
        // 这里返回的phone是加密后的pi 或 未加密数据的手机号
        long sTime = System.currentTimeMillis();
        MonitorService monitorService = com.wsgjp.ct.sale.platform.utils.BeanUtils.getBean(MonitorService.class);
        // 这里返回的phone是加密后的pi 或 未加密数据的手机号
        phone = ssVipMapper.getPhoneByUserCardCode(request.getCode(), request.getOtypeId(), CurrentUser.getProfileId());
        // 手机号加密
        if (StringUtils.isNotBlank(code) && code.length() > 4 && StringUtils.isBlank(phone)) {
            // 手机号大于4位的就认为是输入了完整手机号，使用密文查
            PosBuyer buyerByPhone = sisClientService.getBuyerByPhone(code);
            encryptedPhone = buyerByPhone.getPi();
        }

        // 启动分页 - 必须在实际查询前立即调用
        Page<Object> page = PageHelper.startPage(pageIndex, pageSize);

        List<SsVip> phoneAndNameByPhone = ssVipMapper.getPhoneAndNameByPhone(code, phone, encryptedPhone, request.getOtypeId(), request.getType(),
                CurrentUser.getProfileId());

        // 创建分页信息
        PageResponse<SsVip> pageResponse = new PageResponse<>();
        pageResponse.setList(phoneAndNameByPhone);
        pageResponse.setTotal(page.getTotal());
        pageResponse.setPageIndex(pageIndex);
        pageResponse.setPageSize(pageSize);

        if (!phoneAndNameByPhone.isEmpty()) {
            List<BigInteger> buyerIds =
                    phoneAndNameByPhone.stream().map(SsVip::getBuyerId).collect(Collectors.toList());
            List<PosBuyer> buyers = sisClientService.batchDecryptBuyers(buyerIds);
            if (!buyers.isEmpty()) {
                Map<BigInteger, String> buyerMap = buyers.stream()
                        .filter(b -> Objects.nonNull(b.getBuyerId()))
                        .collect(Collectors.toMap(
                                PosBuyer::getBuyerId,
                                PosBuyer::getCustomerReceiverPhone,
                                (existing, replacement) -> existing));
                for (SsVip ssVip : phoneAndNameByPhone) {
                    if (ssVip.getBuyerId() != null && !Objects.equals(ssVip.getBuyerId(), BigInteger.ZERO)) {
                        ssVip.setPhone(buyerMap.get(ssVip.getBuyerId()));
                    }
                }
            }
        }
        monitorService.recordTP(MonitorTypeEnum.PL_API_SALE_POS_GET_VIP_TP_TIME.getTopic(), "shopType", "pos",
                (System.currentTimeMillis() - sTime));
        return pageResponse;
    }

    @Override
    public WxVipAssert getVipAssertByVipId(BigInteger vipId, BigInteger profileId) {
        return ssVipMapper.getVipAssertByVipId(vipId, profileId);
    }

    @Override
    public List<WxVipAssert> getVipAssertByVipIds(List<BigInteger> vipIds, BigInteger profileId) {
        return ssVipMapper.getVipAssertByVipIds(vipIds, profileId);
    }

    @Override
    public void saveScoreQuickReason(ScoreQuickReason reason) {
        ssVipMapper.saveScoreQuickReason(reason);
    }

    @Override
    public void updateScoreQuickReason(ScoreQuickReason reason) {
        ssVipMapper.updateScoreQuickReason(reason);
    }

    @Override
    public List<ScoreQuickReason> getScoreQuickReasonList() {
        return ssVipMapper.getScoreQuickReasonList(CurrentUser.getProfileId());
    }

    @Override
    public void deleteScoreQuickReason(ScoreQuickReason reason) {
        ssVipMapper.deleteScoreQuickReason(reason);
    }

    @Override
    public void deleteAllScoreQuickReason() {
        ssVipMapper.deleteAllScoreQuickReason(CurrentUser.getProfileId());
    }

    @Override
    public void saveScoreQuickReasonList(List<ScoreQuickReason> reasonList) {
        ssVipMapper.saveScoreQuickReasonList(reasonList);
    }

    @Override
    public void vipLevelUpgrade(BigInteger vipId) {
        levelService.vipLevelUpdate(vipId);
    }


    private void buildOpenCardAsserts(SsVip vip, SsVipLevel vipLevel,
                                      List<MemberAssert> vipAsserts) {
        if (vipLevel == null || vip == null) {
            return;
        }

        // 获取等级对应的权益卡模板
        SsCardTemplate vipLevelCard = ssCardTemplateService.getCardTemplate(vipLevel.getCardTemplateId());
        if (vipLevelCard == null || vipLevelCard.getMemberEquityValues() == null || vipLevelCard.getMemberEquityValues().isEmpty()) {
            return;
        }

        // 记录开卡赠送的卡券列表
        List<SsCardTemplate> sendCards = new ArrayList<>();

        // 遍历权益设置，查找开卡赠送的积分和卡券
        boolean hasGiftConfig = false;
        for (MemberEquityValue memberEquityValue : vipLevelCard.getMemberEquityValues()) {
            if (memberEquityValue.getValueType().equals(EquityValueType.openCardGift.name())) {
                // 开卡赠送卡券
                if (memberEquityValue.getDetailList() != null && !memberEquityValue.getDetailList().isEmpty() &&
                        StringUtils.isNotBlank(memberEquityValue.getDetailList().get(0).getValueString())) {
                    sendCards = JsonUtils.toList(memberEquityValue.getDetailList().get(0).getValueString(), SsCardTemplate.class);
                    hasGiftConfig = true;
                }
            } else if (memberEquityValue.getValueType().equals(EquityValueType.openCardScoreGift.name())) {
                // 开卡赠送积分
                if (memberEquityValue.getDetailList() != null && !memberEquityValue.getDetailList().isEmpty() &&
                        memberEquityValue.getDetailList().get(0).getValueDetail() != null &&
                        memberEquityValue.getDetailList().get(0).getValueDetail().compareTo(BigDecimal.ZERO) > 0) {

                    vipAsserts.add(MemberAssert.createData(0,
                            memberEquityValue.getDetailList().get(0).getValueDetail(),
                            "开卡赠送积分",
                            null,
                            null,
                            AssertsChangeType.CARD_OPENING_GIFT));

                    hasGiftConfig = true;
                }
            }
        }

        // 如果没有配置开卡赠送，则直接返回
        if (!hasGiftConfig) {
            return;
        }

        // 处理卡券赠送
        if (!sendCards.isEmpty()) {
            List<BigInteger> cardTemplateIds = sendCards.stream()
                    .map(SsCardTemplate::getId)
                    .collect(Collectors.toList());

            if (!cardTemplateIds.isEmpty()) {
                for (BigInteger cardTemplateId : cardTemplateIds) {
                    vipAsserts.add(MemberAssert.createData(4,
                            BigDecimal.ONE,
                            "开卡赠送优惠券",
                            null,
                            cardTemplateId,
                            AssertsChangeType.CARD_OPENING_GIFT));
                }
            }
        }
    }

    /**
     * 处理开卡赠送（积分和卡券）
     *
     * @param vip      会员信息
     * @param vipLevel 会员等级信息
     */
    private void processOpenCardGift(SsVip vip, SsVipLevel vipLevel, List<MemberAssert> vipAsserts) {
        if (vipLevel == null || vip == null) {
            return;
        }

        // 获取等级对应的权益卡模板
        SsCardTemplate vipLevelCard = ssCardTemplateService.getCardTemplate(vipLevel.getCardTemplateId());
        if (vipLevelCard == null || vipLevelCard.getMemberEquityValues() == null || vipLevelCard.getMemberEquityValues().isEmpty()) {
            return;
        }

        List<SsCardTemplate> sendCards = new ArrayList<>();

        // 遍历权益设置，查找开卡赠送的积分和卡券
        boolean hasGiftConfig = false;
        for (MemberEquityValue memberEquityValue : vipLevelCard.getMemberEquityValues()) {
            if (memberEquityValue.getValueType().equals(EquityValueType.openCardGift.name())) {
                // 开卡赠送卡券
                if (memberEquityValue.getDetailList() != null && !memberEquityValue.getDetailList().isEmpty() &&
                        StringUtils.isNotBlank(memberEquityValue.getDetailList().get(0).getValueString())) {
                    sendCards = JsonUtils.toList(memberEquityValue.getDetailList().get(0).getValueString(), SsCardTemplate.class);
                    hasGiftConfig = true;
                }
            } else if (memberEquityValue.getValueType().equals(EquityValueType.openCardScoreGift.name())) {
                // 开卡赠送积分
                if (memberEquityValue.getDetailList() != null && !memberEquityValue.getDetailList().isEmpty() &&
                        memberEquityValue.getDetailList().get(0).getValueDetail() != null &&
                        memberEquityValue.getDetailList().get(0).getValueDetail().compareTo(BigDecimal.ZERO) > 0) {
                    vipAsserts.add(MemberAssert.createData(0, memberEquityValue.getDetailList().get(0).getValueDetail(), AssertsChangeType.CARD_OPENING_GIFT));
                    hasGiftConfig = true;
                }
            }
        }

        // 如果没有配置开卡赠送，则直接返回
        if (!hasGiftConfig) {
            return;
        }

        // 处理卡券赠送
        for (SsCardTemplate map : sendCards) {
            vipAsserts.add(MemberAssert.createData(4,
                    BigDecimal.ONE,
                    VipAssertsBillDetailDto.getTypedMemo(4, BigDecimal.ONE),
                    null,
                    map.getId(),
                    AssertsChangeType.CARD_OPENING_GIFT));
        }

    }

    /**
     * 付费会员升级聚合支付
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PayResultInfo vipLevelPayOrderAndSaveBill(VipLevelPaymentRequest request) {
        long sTime = System.currentTimeMillis();
        MonitorService monitorService = com.wsgjp.ct.sale.platform.utils.BeanUtils.getBean(MonitorService.class);

        RenewOrUpgradeSVIPDTO upgradeInfo = request.getUpgradeInfo();
        AggregatePaymentRequest payRequestDto = request.getPayInfo();
        payRequestDto.setServerName("sale");
        payRequestDto.setNotifyUrl("/sale/shopsale/bill/payResultCallback");

        // 关闭轮询
        payRequestDto.setEnablePollingQuery(false);

        PayResultInfo resultInfo = new PayResultInfo();
        BigInteger vchcode = null;
        String billNumber = null;

        try {
            // 1. 完整的参数验证（与原有renewOrUpgradeSVIP一致）
            if (upgradeInfo.getVipId() == null) {
                throw new RuntimeException("会员id不能为空");
            }
            if (upgradeInfo.getLevelId() == null) {
                throw new RuntimeException("等级id不能为空");
            }
            if (upgradeInfo.getLevelRuleId() == null) {
                throw new RuntimeException("等级规则id不能为空");
            }
            if (upgradeInfo.getPayment() == null || upgradeInfo.getPayment().isEmpty()) {
                throw new RuntimeException("支付金额不能为空");
            }

            // 支付金额验证
            BigDecimal paidMoney = BigDecimal.ZERO;
            for (PayMentDTO atypeModel : upgradeInfo.getPayment()) {
                if (atypeModel.getCurrencyAtypeTotal().compareTo(BigDecimal.ZERO) < 0) {
                    throw new RuntimeException("付款方式中支付金额不能为负数");
                }
                paidMoney = paidMoney.add(atypeModel.getCurrencyAtypeTotal());
            }

            // 验证支付方式
            checkValidPaymentList(upgradeInfo.getPayment());

            if (upgradeInfo.getBtypeId() == null || BigInteger.ZERO.equals(upgradeInfo.getBtypeId())) {
                throw new RuntimeException("结算单位不能为空");
            }

            // 2. 先生成草稿其他收款单据（不核算）
            vchcode = UId.newId();
            if (upgradeInfo.getVchcode() != null) {
                vchcode = upgradeInfo.getVchcode();
            }
            upgradeInfo.setVchcode(vchcode);

            // 生成其他收款单据（草稿状态）
            SsVipLevel level = levelService.getVipLevelByLevelId(upgradeInfo.getLevelId());
            String levelName = level.getVipName();
            if (levelName != null) {
                levelName = "会员等级(" + levelName + ")";
            } else {
                levelName = "";
            }

            SsVip ssVip = ssVipMapper.selectVipById(upgradeInfo.getVipId(), CurrentUser.getProfileId());
            // 手机号解密
            if (ssVip.getBuyerId() != null) {
                List<BigInteger> buyerIds = new ArrayList<>();
                buyerIds.add(ssVip.getBuyerId());
                List<PosBuyer> buyers = sisClientService.batchDecryptBuyers(buyerIds);
                if (buyers != null && !buyers.isEmpty()) {
                    ssVip.setPhone(buyers.get(0).getCustomerReceiverPhone());
                }
            }
            String summary = "会员(" + ssVip.getPhone() + ")升级/续费" + levelName;

            // 生成其他收款单据（草稿状态）
            receiptBillService.submitVipLevelBill(vchcode, paidMoney, upgradeInfo.getOtypeId(),
                    upgradeInfo.getEtypeId(), upgradeInfo.getEtypeId(), upgradeInfo.getBtypeId(),
                    summary, upgradeInfo.getMemo(), upgradeInfo.getPayment(), upgradeInfo.getVipId(),
                    BillPostState.PROCESS_COMPLETED);
            billNumber = vchcode.toString(); // 使用vchcode作为单据号

            // 设置支付请求参数
            payRequestDto.setVchcode(vchcode);
            payRequestDto.setBillTotal(paidMoney);
            payRequestDto.setOutNo(vchcode.toString());

            if (payRequestDto.getGoodsDesc() == null || payRequestDto.getGoodsDesc().isEmpty()) {
                payRequestDto.setGoodsDesc("付费会员升级");
            }
            if (payRequestDto.getGoodsTitle() == null || payRequestDto.getGoodsTitle().isEmpty()) {
                payRequestDto.setGoodsTitle("付费会员升级");
            }

            // 2. 调用聚合支付
            PaymentResponse<PaymentInfo> response = aggregatePaymentService.payOrder(payRequestDto);
            resultInfo.setMessage(response.getMessage());
            if (response.getData() != null) {
                org.springframework.beans.BeanUtils.copyProperties(response.getData(), resultInfo);
            }

            // 订单状态处理，把淘淘谷的等待输入密码换成等待
            if (resultInfo.getStatus() == PayStatusEnum.WAITING_PASSWORD) {
                resultInfo.setStatus(PayStatusEnum.PENDING);
            }

            // 记录日志
            taotaoguLogService.saveLog(vchcode, payRequestDto.getOutNo(), "vipLevelPayOrderAndSaveBill",
                    JsonUtils.toJson(request), JsonUtils.toJson(resultInfo), false, "0", "付费会员升级聚合支付请求结果");

            // 3. 如果支付成功，立即处理会员升级和核算
            if (resultInfo.getStatus() == PayStatusEnum.SUCCEEDED) {
                processVipLevelUpgradeSuccess(upgradeInfo, vchcode, level);
                // 核算单据
                try {
                    goodsBillService.postBill(vchcode);
                } catch (Exception e) {
                    String info = "付费会员升级聚合支付单据核算失败[" + vchcode + "]" + e.getMessage();
                    LOGGER.warn(info);
                }
            }

            // 设置单据信息到结果中
            BillSaveResultDTO billSaveResult = new BillSaveResultDTO();
            billSaveResult.setVchcode(vchcode);
            billSaveResult.setBillNumber(billNumber);
            billSaveResult.setResultType(BillSaveResultTypeEnum.SUCCESS);
            resultInfo.setResultDTO(billSaveResult);

            monitorService.recordTP(MonitorTypeEnum.PL_API_SALE_POS_PAY_TP_TIME.getTopic(), "shopType", "pos",
                    (System.currentTimeMillis() - sTime));

            return resultInfo;

        } catch (Exception e) {
            LOGGER.error("付费会员升级聚合支付失败，profileId: {}, error: {}", CurrentUser.getProfileId(), e.getMessage());
            if (vchcode != null) {
                taotaoguLogService.saveLog(vchcode, payRequestDto.getOutNo(), "vipLevelPayOrderAndSaveBill",
                        JsonUtils.toJson(request), "", true, "-3", "付费会员升级聚合支付失败: " + e.getMessage());
            }

            resultInfo.setStatus(PayStatusEnum.CANCELLED);
            resultInfo.setMessage("升级支付失败: " + e.getMessage());

            // 设置错误的单据信息
            if (vchcode != null) {
                BillSaveResultDTO billSaveResult = new BillSaveResultDTO();
                billSaveResult.setVchcode(vchcode);
                billSaveResult.setBillNumber(billNumber);
                billSaveResult.setResultType(BillSaveResultTypeEnum.ERROR);
                billSaveResult.setMessage(e.getMessage());
                resultInfo.setResultDTO(billSaveResult);
            }

            return resultInfo;
        }
    }

    /**
     * 处理付费会员升级支付成功后的业务逻辑
     * 完整实现原有renewOrUpgradeSVIP方法的所有功能
     */
    private void processVipLevelUpgradeSuccess(RenewOrUpgradeSVIPDTO upgradeInfo, BigInteger vchcode, SsVipLevel level) {
        try {
            // 直接调用原有的renewOrUpgradeSVIP核心逻辑
            executeRenewOrUpgradeLogic(upgradeInfo, vchcode, level);

        } catch (Exception e) {
            LOGGER.error("处理付费会员升级支付成功后的业务逻辑失败: {}", e.getMessage());
            throw new RuntimeException("会员升级处理失败: " + e.getMessage());
        }
    }

    /**
     * 执行续费或升级的核心逻辑
     * 完整复制原有renewOrUpgradeSVIP方法的所有逻辑，不遗漏任何验证和处理
     */
    private void executeRenewOrUpgradeLogic(RenewOrUpgradeSVIPDTO dto, BigInteger vchcode, SsVipLevel level) {
        BigInteger profileId = CurrentUser.getProfileId();
        SsVip ssVip = ssVipMapper.selectVipById(dto.getVipId(), profileId);
        if (ssVip == null) {
            throw new RuntimeException("找不到该会员");
        }

        // 会员手机号解密
        List<PosBuyer> buyers = sisClientService.batchDecryptBuyers(Collections.singletonList(ssVip.getBuyerId()));
        if (!buyers.isEmpty()) {
            ssVip.setPhone(buyers.get(0).getCustomerReceiverPhone());
        }

        // 等级验证
        if (level == null) {
            throw new RuntimeException("找不到该会员等级");
        }
        if (level.getStoped()) {
            throw new RuntimeException("该会员等级已停用");
        }
        if (level.getVipType() != 1) {
            throw new RuntimeException("该会员等级非付费");
        }

        // 获取等级规则
        List<SsVipLevelRule> levelRules = levelService.selectVipLevelRuleByLevelId(level.getId());
        if (levelRules == null || levelRules.isEmpty()) {
            throw new RuntimeException("等级规则不存在");
        }

        // 找到对应的规则
        SsVipLevelRule levelRule = levelRules.stream()
                .filter(rule -> rule.getId().equals(dto.getLevelRuleId()))
                .findFirst()
                .orElse(null);
        if (levelRule == null) {
            throw new RuntimeException("当前的会员等级不包含此定价规则");
        }

        // 计算支付金额
        BigDecimal paidMoney = dto.getPayment().stream()
                .map(PayMentDTO::getCurrencyAtypeTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 找到续费或升级前的会员等级
        SsVipLevel oldLevel = levelService.getVipLevelByLevelId(ssVip.getLevelId());

        // 是否是由免费升级到付费(付费已过期也算)
        boolean isFreeToPaid = oldLevel.getVipType() != 1 || isVipLevelExpired(oldLevel.getVipType(), ssVip.getValidDate());

        // 是否是升级
        boolean isUpgrade = !dto.getLevelId().equals(oldLevel.getId());

        // 是否将之前的购买记录作废，只有平级续费，且续费时长不是永久，才会保留之前的记录
        boolean setBeforeExpired = true;

        // 之前有效期内会员抵扣的金额
        BigDecimal discountMoney = BigDecimal.ZERO;
        Date now = new Date();

        // 消费记录开始时间
        Date startDate = now;
        BigDecimal levelPrice = levelRule.getLevelPrice();

        if (!isFreeToPaid) {
            Date oldValidDate = ssVip.getValidDate();
            // 永久有效判断
            if (!isUpgrade && isForever(oldValidDate)) {
                throw new RuntimeException("当前的会员等级已经是永久有效，无需续费该等级");
            }

            // 如果是平级续费，且续费时长不是永久
            if (!isUpgrade && levelRule.getValidity() != -1) {
                setBeforeExpired = false;
                startDate = oldValidDate;

                // 计算之前有效期内会员抵扣的金额
                int daysBetween = getDaysBetween(now, oldValidDate);
                if (daysBetween > 0) {
                    BigDecimal dayPrice = levelPrice.divide(new BigDecimal(levelRule.getValidity()), 2, RoundingMode.HALF_UP);
                    discountMoney = dayPrice.multiply(new BigDecimal(daysBetween));
                }
            }
            /*
            分两种情况：
            1.平级续费到永久
            2.升级续费
            均需要：
            1.将之前的记录作废
            2.插入一条新的记录，开始时间为现在
            3.需要计算抵扣金额
             */
            else {
                List<SsVipFeeRecord> feeRecords = selectFeeRecordByVipIdForUpgrade(ssVip.getId());
                SsVipFeeRecord foreverRecord = feeRecords.stream()
                        .filter(record -> isForever(record.getEndDate()))
                        .findFirst()
                        .orElse(null);

                // 如果之前就是永久会员，则抵扣金额就是该等级支付的钱
                if (foreverRecord != null) {
                    discountMoney = foreverRecord.getTotal();
                } else {
                    for (SsVipFeeRecord record : feeRecords) {
                        BigDecimal total = record.getTotal();
                        // 如果该有效期金额为0，则无需计算，直接抵扣金额就是0
                        if (!BigDecimal.ZERO.equals(total)) {
                            BigDecimal discount;
                            Date start = record.getStartDate();
                            Date end = record.getEndDate();
                            // 该有效期已经开始
                            if (start.before(now)) {
                                // 该记录一共多少天
                                int period = getDaysBetween(start, end);
                                // 剩余多少天
                                int remainDays = getDaysBetween(now, end);
                                discount = new BigDecimal(remainDays).multiply(total)
                                        .divide(new BigDecimal(period), 2, RoundingMode.HALF_UP);
                            }
                            // 未开始，全额抵扣
                            else {
                                discount = total;
                            }
                            discountMoney = discountMoney.add(discount);
                        }
                    }
                    discountMoney = discountMoney.setScale(2, RoundingMode.HALF_UP);
                    // 如果花费小于抵扣，则抵扣金额就是花费金额，避免支付金额为负数
                    if (levelPrice.compareTo(discountMoney) <= 0) {
                        discountMoney = levelPrice;
                    }
                }
            }
        }

        // 总价值
        BigDecimal totalAll = discountMoney.add(paidMoney);
        if (levelPrice.compareTo(totalAll) > 0) {
            throw new RuntimeException("支付金额不足");
        }

        // 计算过期时间
        Date validDate = SVIPUtils.getValidDate(levelRule.getValidity(), startDate);

        // 将之前的记录设置为作废
        if (setBeforeExpired) {
            ssVipMapper.setVipFeeRecordExpired(profileId, ssVip.getId());
        }

        // 插入升级/续费记录
        SsVipFeeRecord record = new SsVipFeeRecord(UId.newId(), vchcode, profileId, ssVip.getId(),
                totalAll, 1, startDate, validDate);
        ssVipMapper.insertVipFeeRecord(Collections.singletonList(record));

        // 修改会员等级
        ssVipMapper.changeVipLevel(profileId, ssVip.getId(), level.getId(), validDate);

        String levelName = level.getVipName();
        if (levelName != null) {
            levelName = "会员等级(" + levelName + ")";
        } else {
            levelName = "";
        }
        String summary = "会员(" + ssVip.getPhone() + ")升级/续费" + levelName;

        // 更改会员等级权益
        levelService.sendLevelCard(ssVip.getId(), ssVip.getProfileId(), level.getCardTemplateId(), level.getId(), summary);

        // 微信会员同步
        vipAssertsChangeService.wxVipAsync(ssVip.getId(), ssVip.getProfileId());
    }

    /**
     * 付费会员升级支付确认处理
     * 用于POS端轮询或手动确认收款时调用，处理会员升级和单据核算
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmVipLevelPayment(VipLevelPaymentRequest request) {
        try {
            RenewOrUpgradeSVIPDTO upgradeInfo = request.getUpgradeInfo();
            BigInteger vchcode = upgradeInfo.getVchcode();

            // 1. 验证参数
            if (vchcode == null) {
                throw new RuntimeException("单据号不能为空");
            }

            LOGGER.info("开始处理付费会员升级支付确认，vchcode: {}", vchcode);

            // 2. 查询会员等级信息
            SsVipLevel level = getVipLevelById(upgradeInfo.getLevelId());
            if (level == null) {
                throw new RuntimeException("未找到会员等级信息，levelId: " + upgradeInfo.getLevelId());
            }

            // 3. 处理会员升级等业务逻辑（使用与聚合支付相同的逻辑）
            executeRenewOrUpgradeLogic(upgradeInfo, vchcode, level);

            // 4. 核算单据
            try {
                goodsBillService.postBill(vchcode);
            } catch (Exception e) {
                String info = "付费会员升级支付确认单据核算失败[" + vchcode + "]" + e.getMessage();
                LOGGER.warn(info);
            }

            LOGGER.info("付费会员升级支付确认处理完成，vchcode: {}", vchcode);

        } catch (Exception e) {
            LOGGER.error("处理付费会员升级支付确认失败: {}", e.getMessage());
            throw new RuntimeException("会员升级支付确认处理失败: " + e.getMessage());
        }
    }


    /**
     * 根据等级ID查询会员等级信息
     */
    private SsVipLevel getVipLevelById(BigInteger levelId) {
        try {
            return levelService.getVipLevelByLevelId(levelId);
        } catch (Exception e) {
            LOGGER.error("查询会员等级信息失败，levelId: {}, error: {}", levelId, e.getMessage());
            return null;
        }
    }

    /**
     * 判断日期是否为永久有效
     */
    private boolean isForever(Date date) {
        if (date == null) {
            return false;
        }
        // 2100-01-01 00:00:00
        return date.getTime() >= 4102444800000L;
    }

    /**
     * 计算两个日期之间的天数
     */
    private int getDaysBetween(Date startDate, Date endDate) {
        if (startDate == null || endDate == null) {
            return 0;
        }
        long diff = endDate.getTime() - startDate.getTime();
        return (int) (diff / (1000 * 60 * 60 * 24));
    }


    /**
     * 根据会员ID查询付费记录（私有方法，用于升级逻辑）
     */
    private List<SsVipFeeRecord> selectFeeRecordByVipIdForUpgrade(BigInteger vipId) {
        return ssVipMapper.selectFeeRecordByVipId(CurrentUser.getProfileId(), vipId, ZERO_POINT_DATE);
    }

    /**
     * 会员开通聚合支付
     */
    @Override
    public PayResultInfo vipCreatePayOrderAndSaveBill(VipCreatePaymentRequest request) {
        long sTime = System.currentTimeMillis();
        MonitorService monitorService = com.wsgjp.ct.sale.platform.utils.BeanUtils.getBean(MonitorService.class);

        AddOrEditVipDTO vipInfo = request.getVipInfo();
        vipInfo.setOperationSource(AssertsSourceType.POS);
        AggregatePaymentRequest payRequestDto = request.getPayInfo();
        payRequestDto.setServerName("sale");
        payRequestDto.setNotifyUrl("/sale/shopsale/bill/payResultCallback");

        // 关闭轮询
        payRequestDto.setEnablePollingQuery(false);

        PayResultInfo resultInfo = new PayResultInfo();
        BigInteger vchcode = null;
        String billNumber = null;

        try {
            // 1. 参数验证
            if (vipInfo == null) {
                throw new RuntimeException("会员信息不能为空");
            }
            if (vipInfo.getVipLevelId() == null) {
                throw new RuntimeException("会员等级ID不能为空");
            }
            // 支付金额从聚合支付信息中获取
            if (payRequestDto.getTradeAmount() == null || payRequestDto.getTradeAmount().compareTo(BigDecimal.ZERO) <= 0) {
                throw new RuntimeException("支付金额必须大于0");
            }

            // 验证支付方式
            checkValidPaymentList(vipInfo.getPayment());

            // 获取会员等级信息
            SsVipLevel level = ssVipLevelMapper.selectVipLevelById(CurrentUser.getProfileId(), vipInfo.getVipLevelId());
            if (level == null) {
                throw new RuntimeException("会员等级不存在");
            }
            if (level.getStoped() != null && level.getStoped()) {
                throw new RuntimeException("会员等级已停用");
            }

            String levelName = level.getVipName();
            String summary = "会员开通-" + levelName;

            // 生成单据号
            vchcode = UId.newId();

            // 生成其他收款单据（待出入库状态）
            receiptBillService.submitVipLevelBill(vchcode, payRequestDto.getBillTotal(), vipInfo.getOtypeId(),
                    vipInfo.getEtypeId(), vipInfo.getEtypeId(), vipInfo.getBtypeId(),
                    summary, "会员开通", vipInfo.getPayment(), null,
                    BillPostState.PROCESS_COMPLETED);
            billNumber = vchcode.toString(); // 使用vchcode作为单据号

            // 设置支付请求参数
            payRequestDto.setVchcode(vchcode);
            payRequestDto.setOutNo(vchcode.toString());

            if (payRequestDto.getGoodsDesc() == null || payRequestDto.getGoodsDesc().isEmpty()) {
                payRequestDto.setGoodsDesc("会员开通");
            }
            if (payRequestDto.getGoodsTitle() == null || payRequestDto.getGoodsTitle().isEmpty()) {
                payRequestDto.setGoodsTitle("会员开通");
            }

            // 2. 调用聚合支付
            PaymentResponse<PaymentInfo> response = aggregatePaymentService.payOrder(payRequestDto);
            resultInfo.setMessage(response.getMessage());
            if (response.getData() != null) {
                org.springframework.beans.BeanUtils.copyProperties(response.getData(), resultInfo);
            }

            // 订单状态处理，把淘淘谷的等待输入密码换成等待
            if (resultInfo.getStatus() == PayStatusEnum.WAITING_PASSWORD) {
                resultInfo.setStatus(PayStatusEnum.PENDING);
            }

            // 记录日志
            taotaoguLogService.saveLog(vchcode, payRequestDto.getOutNo(), "vipCreatePayOrderAndSaveBill",
                    JsonUtils.toJson(request), JsonUtils.toJson(resultInfo), false, "0", "会员开通聚合支付请求结果");

            // 3. 如果支付成功，立即创建会员
            if (resultInfo.getStatus() == PayStatusEnum.SUCCEEDED) {
                BigInteger vipId = processVipCreateSuccess(vipInfo, vchcode, level);
                resultInfo.setVipId(vipId);
                // 核算单据
                try {
                    goodsBillService.postBill(vchcode);
                } catch (Exception e) {
                    String info = "会员开通聚合支付单据核算失败[" + vchcode + "]" + e.getMessage();
                    LOGGER.warn(info);
                }
            }

            // 设置单据信息
            BillSaveResultDTO billSaveResult = new BillSaveResultDTO();
            billSaveResult.setVchcode(vchcode);
            billSaveResult.setBillNumber(billNumber);
            billSaveResult.setResultType(BillSaveResultTypeEnum.SUCCESS);
            resultInfo.setResultDTO(billSaveResult);

            monitorService.recordTP(MonitorTypeEnum.PL_API_SALE_POS_GET_RECHARGE_TIME.getTopic(), "shopType", "pos",
                    (System.currentTimeMillis() - sTime));

            return resultInfo;

        } catch (Exception e) {
            LOGGER.error("会员开通聚合支付失败，profileId: {}, error: {}", CurrentUser.getProfileId(), e.getMessage());
            if (vchcode != null) {
                taotaoguLogService.saveLog(vchcode, payRequestDto.getOutNo(), "vipCreatePayOrderAndSaveBill",
                        JsonUtils.toJson(request), "", true, "-3", "会员开通聚合支付失败: " + e.getMessage());
            }

            resultInfo.setStatus(PayStatusEnum.CANCELLED);
            resultInfo.setMessage("会员开通支付失败: " + e.getMessage());

            // 设置错误的单据信息
            if (vchcode != null) {
                BillSaveResultDTO billSaveResult = new BillSaveResultDTO();
                billSaveResult.setVchcode(vchcode);
                billSaveResult.setBillNumber(billNumber);
                billSaveResult.setResultType(BillSaveResultTypeEnum.ERROR);
                billSaveResult.setMessage(e.getMessage());
                resultInfo.setResultDTO(billSaveResult);
            }

            return resultInfo;
        }
    }

    /**
     * 处理会员开通支付成功后的业务逻辑
     */
    private BigInteger processVipCreateSuccess(AddOrEditVipDTO vipInfo, BigInteger vchcode, SsVipLevel level) {
        try {
            // 如果是付费会员，设置到期时间
            if (level.getVipType() == 1) {
                // 获取等级规则
                List<SsVipLevelRule> levelRules = ssVipLevelMapper.selectVipLevelRuleByLevelId(vipInfo.getVipLevelId(), CurrentUser.getProfileId());
                if (levelRules != null && !levelRules.isEmpty()) {
                    SsVipLevelRule levelRule = levelRules.get(0);
                    // 设置有效期（天数）
                    vipInfo.setValidity(levelRule.getValidity());
                }
            }

            // 调用addOrEditVip创建会员
            BigInteger vipId = addOrEditVip(vipInfo);

            LOGGER.info("会员开通成功，vipId: {}, vchcode: {}", vipId, vchcode);
            return vipId;

        } catch (Exception e) {
            LOGGER.error("处理会员开通支付成功后的业务逻辑失败: {}", e.getMessage(), e);
            // 重新抛出异常，确保事务能够正确回滚
            throw new RuntimeException("会员创建失败: " + e.getMessage(), e);
        }
    }


}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopPlatformStoreMappingMapper">
    <insert id="addPlatformStoreMappings">
        insert into pl_eshop_platform_store_mapping(id,profile_id,eshop_id,platform_store_stock_id,ktype_id
        ,platform_store_name, platform_store_address, platform_store_type,
        business_label,correspond_flag,type,deleted,source)
        values
        <foreach collection="mappings" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.profileId},
            #{item.eshopId},
            #{item.platformStoreStockId},
            #{item.ktypeId},
            #{item.platformStoreName},
            #{item.platformStoreAddress},
            #{item.platformStoreType},
            #{item.businessLabel},
            #{item.correspondFlag},
            #{item.type},
            #{item.deleted},
            #{item.source}
            )
        </foreach>
        on duplicate key update
        platform_store_name=VALUES(platform_store_name),
        platform_store_address=VALUES(platform_store_address),
        platform_store_type=VALUES(platform_store_type),
        business_label=VALUES(business_label),
        correspond_flag=VALUES(correspond_flag),
        type=VALUES(type),
        deleted=VALUES(deleted),
        source=VALUES(source)
    </insert>

    <insert id="addPlatformStoreMapping">
        insert into pl_eshop_platform_store_mapping(id, profile_id, eshop_id, platform_store_stock_id, ktype_id)
        values (#{item.id},
                #{item.profileId},
                #{item.eshopId},
                #{item.platformStoreStockId},
                #{item.ktypeId})
    </insert>

    <insert id="addPlatformStoreMappingByAdd">
        insert into pl_eshop_platform_store_mapping(id, profile_id, eshop_id, platform_store_stock_id, ktype_id,
                                                    platform_store_name, platform_store_address, platform_store_type,
                                                    correspond_flag, source,type)
        values (#{item.id},
                #{item.profileId},
                #{item.eshopId},
                #{item.platformStoreStockId},
                #{item.ktypeId},
                #{item.platformStoreName},
                #{item.platformStoreAddress},
                #{item.platformStoreType},
                #{item.correspondFlag},
                #{item.source},#{item.type})
    </insert>
    <delete id="deletePlatformStoreMapping" parameterType="java.math.BigInteger">
        delete
        from pl_eshop_platform_store_mapping
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and id = #{id}
    </delete>

    <delete id="deleteAllPlatformStoreMappingByEShopId" parameterType="java.math.BigInteger">
        delete
        from pl_eshop_platform_store_mapping
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
    </delete>
    <select id="getPlatformStoreMappingByEshopId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopPlatformStoreMapping">
        select *, 0 as pid, ktype_id as preKtypeId, platform_store_stock_id as prePlatformStoreId
        from pl_eshop_platform_store_mapping
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
    </select>

    <select id="getPlatformStoreMappingById"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopPlatformStoreMapping">
        select *
        from pl_eshop_platform_store_mapping
        where profile_id = #{profileId}
          and id = #{id}
    </select>
    <update id="updatePlatformStoreMappingById"
            parameterType="com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopPlatformStoreMapping">
        update pl_eshop_platform_store_mapping
        set ktype_id=#{mapping.ktypeId},
            platform_store_stock_id=#{mapping.platformStoreStockId}
        where profile_id = #{mapping.profileId}
          and id = #{mapping.id}
    </update>

    <!--    <update id="updatePlatformStoreMappings" parameterType="java.util.List">-->
    <!--        <foreach collection="mappings" item="item" index="index" separator=";">-->
    <!--            update pl_eshop_platform_store_mapping-->
    <!--            <set>-->
    <!--                <if test="item.platformStoreId != null">-->
    <!--                    platform_store_id = #{item.platformStoreId},-->
    <!--                </if>-->
    <!--                <if test="item.ktypeId != null and item.ktypeId>0">-->
    <!--                    ktype_id = #{item.ktypeId},-->
    <!--                </if>-->
    <!--            </set>-->
    <!--            where profile_id=#{item.profileId} and id=#{item.id}-->
    <!--        </foreach>-->
    <!--    </update>-->


    <!--查询列表-->
    <select id="queryListByEshopId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopPlatformStoreMapping">
        select
        pepsm.eshop_id, pepsm.id,pepsm.profile_id, pepsm.platform_store_stock_id, pepsm.ktype_id, pepsm.create_time,
        pepsm.update_time,pepsm.platform_store_name,pepsm.source,pepsm.deleted,pepsm.type,
        pepsm.platform_store_address, pepsm.platform_store_type, pepsm.business_label,pepsm.correspond_flag,
        bo.fullname as 'otypeName',bk.fullname as 'btypeName',bk.usercode as 'btypeUserCode',
        CONCAT(bs.sender_province,bs.sender_city,bs.sender_district,bs.sender_town,bs.sender_address) as 'btypeAddress'
        from pl_eshop_platform_store_mapping pepsm
        left join base_otype bo on pepsm.eshop_id = bo.id and bo.profile_id=pepsm.profile_id
        LEFT JOIN base_ktype bk on bk.id = pepsm.ktype_id and bk.profile_id=pepsm.profile_id
        LEFT JOIN base_ktype_deliveryinfo bkd on bkd.ktype_id = pepsm.ktype_id and bkd.deliverytype = 1 and
        bkd.profile_id=pepsm.profile_id and bkd.defaulted=1
        LEFT JOIN pl_sender bs on bs.id = bkd.delivery_id and bs.profile_id=bkd.profile_id
        <where>
            <if test="profileId != null">
                and pepsm.profile_id = #{profileId}
            </if>
            <if test="eshopId != null and eshopId != 0">
                and pepsm.eshop_id = #{eshopId}
            </if>
            <if test="otypeFilter != null and otypeFilter != 0">
                and pepsm.eshop_id = #{otypeFilter}
            </if>
            <if test="id != null">
                and pepsm.id = #{id}
            </if>
            <if test="platformStoreStockId != null and platformStoreStockId != ''">
                and pepsm.platform_store_stock_id like CONCAT('%',#{platformStoreStockId},'%')
            </if>
            <if test="storeIdFilter != null and storeIdFilter != ''">
                and pepsm.platform_store_stock_id like CONCAT('%',#{storeIdFilter},'%')
            </if>
            <if test="ktypeId != null">
                and pepsm.ktype_id = #{ktypeId}
            </if>
            <if test="ktypeFilter != null">
                and pepsm.ktype_id = #{ktypeFilter}
            </if>
            <if test="createTime != null">
                and pepsm.create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and pepsm.update_time = #{updateTime}
            </if>
            <if test="platformStoreName != null and platformStoreName != ''">
                and pepsm.platform_store_name like CONCAT('%',#{platformStoreName},'%')
            </if>
            <if test="storeNameFilter != null and storeNameFilter != ''">
                and pepsm.platform_store_name like CONCAT('%',#{storeNameFilter},'%')
            </if>
            <if test="platformStoreAddress != null and platformStoreAddress != ''">
                and pepsm.platform_store_address like CONCAT('%',#{platformStoreAddress},'%')
            </if>
            <if test="storeAddressFilter != null and storeAddressFilter != ''">
                and pepsm.platform_store_address like CONCAT('%',#{storeAddressFilter},'%')
            </if>
            <if test="platformStoreType != null and platformStoreType != 0">
                and pepsm.platform_store_type = #{platformStoreType}
            </if>
            <if test="businessLabel != null and businessLabel != ''">
                and pepsm.business_label = #{businessLabel}
            </if>
            <if test="correspondFlag != null and correspondFlag != 2">
                and pepsm.correspond_flag = #{correspondFlag}
            </if>
            <if test="storeFlagFilter != null and storeFlagFilter != 2">
                and pepsm.correspond_flag = #{storeFlagFilter}
            </if>
            <if test="deleted != null">
                and pepsm.deleted = #{deleted}
            </if>
            <if test="type != null">
                and pepsm.type = #{type}
            </if>
            <if test="source != null">
                and pepsm.source = #{source}
            </if>
            <if test="btypeUserCode != null and btypeUserCode != ''">
                and bk.usercode like CONCAT('%',#{btypeUserCode},'%')
            </if>
            <if test="btypeAddress != null and btypeAddress != ''">
                and bd.address like CONCAT('%',#{btypeAddress},'%')
            </if>
            <if test="platformStoreTypeFilter != null">
                and pepsm.platform_store_type = #{platformStoreTypeFilter}
            </if>
            <if test="quickfilter != null">
                and (pepsm.platform_store_stock_id like CONCAT('%',#{quickfilter},'%')
                or pepsm.platform_store_name like CONCAT('%',#{quickfilter},'%'))
            </if>
            <if test="otypeIds!=null and otypeIds.size()>0">
                and pepsm.eshop_id in
                <foreach collection="otypeIds" close=")" open="(" separator="," item="item">
                #{item}
                </foreach>
            </if>
        </where>
    </select>
    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into pl_eshop_platform_store_mapping(id, profile_id, eshop_id, platform_store_stock_id, ktype_id,
                                                    create_time,
                                                    update_time,
                                                    platform_store_name, platform_store_address, platform_store_type,
                                                    business_label, correspond_flag, type, deleted, source)
        values (#{id}, #{profileId}, #{eshopId}, #{platformStoreStockId}, #{ktypeId}, #{createTime}, #{updateTime},
                #{platformStoreName},
                #{platformStoreAddress}, #{platformStoreType}, #{businessLabel}, #{correspondFlag}, #{type}, #{deleted},
                #{source})
    </insert>

    <insert id="insertPlatformArtesianRotation" keyProperty="id" useGeneratedKeys="true">
        insert into pl_eshop_platform_store_mapping(id, profile_id, eshop_id, platform_store_stock_id, ktype_id,
                                                    create_time,
                                                    platform_store_name, platform_store_address, platform_store_type,
                                                    business_label, correspond_flag, type, deleted, source)
        values (#{id}, #{profileId}, #{eshopId}, #{platformStoreStockId}, #{ktypeId}, #{createTime},
                #{platformStoreName},
                #{platformStoreAddress}, #{platformStoreType}, #{businessLabel}, #{correspondFlag}, #{type}, #{deleted},
                #{source})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update pl_eshop_platform_store_mapping
        <set>
            <if test="profileId != null">
                profile_id = #{profileId},
            </if>
            <if test="eshopId != null">
                eshop_id = #{eshopId},
            </if>
            <if test="platformStoreStockId != null ">
                platform_store_stock_id = #{platformStoreStockId},
            </if>
            <if test="ktypeId != null">
                ktype_id = #{ktypeId},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="platformStoreName != null and platformStoreName != ''">
                platform_store_name = #{platformStoreName},
            </if>
            <if test="platformStoreAddress != null ">
                platform_store_address = #{platformStoreAddress},
            </if>
            <if test="platformStoreType != null">
                platform_store_type = #{platformStoreType},
            </if>
            <if test="businessLabel != null and businessLabel != ''">
                business_label = #{businessLabel},
            </if>
            <if test="correspondFlag != null">
                correspond_flag = #{correspondFlag},
            </if>
            <if test="deleted != null">
                deleted = #{deleted},
            </if>
            <if test="type != null">
                `type` = #{type},
            </if>
            <if test="source != null">
                source = #{source}
            </if>
        </set>
        where id = #{id}
        and profile_id = #{profileId}
    </update>


    <update id="updateStoreInfo">
        update pl_eshop_platform_store_mapping
        <set>
            <if test="platformStoreStockId != null ">
                platform_store_stock_id = #{platformStoreStockId},
            </if>

            <if test="platformStoreName != null and platformStoreName != ''">
                platform_store_name = #{platformStoreName},
            </if>

            <if test="platformStoreAddress != null ">
                platform_store_address = #{platformStoreAddress},
            </if>
        </set>
        where id = #{id}
        and profile_id = #{profileId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from pl_eshop_platform_store_mapping
        where id = #{id}
          and profile_id = #{profileId}
    </delete>

    <delete id="deleteByIds">
        delete from pl_eshop_platform_store_mapping
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and profile_id = #{profileId}
    </delete>

    <update id="updatePlatformStoreMappings">
        <foreach collection="mappings" item="item" index="index" separator=";">
            update pl_eshop_platform_store_mapping
            <set>
                <if test="item.profileId != null">
                    profile_id = #{item.profileId},
                </if>
                <if test="item.eshopId != null">
                    eshop_id = #{item.eshopId},
                </if>
                <if test="item.platformStoreStockId != null and item.platformStoreStockId != ''">
                    platform_store_stock_id = #{item.platformStoreStockId},
                </if>
                <if test="item.ktypeId != null">
                    ktype_id = #{item.ktypeId},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime},
                </if>
                <if test="item.platformStoreName != null and item.platformStoreName != ''">
                    platform_store_name = #{item.platformStoreName},
                </if>
                <if test="item.platformStoreAddress != null and item.platformStoreAddress != ''">
                    platform_store_address = #{item.platformStoreAddress},
                </if>
                <if test="item.platformStoreType != null">
                    platform_store_type = #{item.platformStoreType},
                </if>
                <if test="item.businessLabel != null and item.businessLabel != ''">
                    business_label = #{item.businessLabel},
                </if>
                <if test="item.correspondFlag != null">
                    correspond_flag = #{item.correspondFlag},
                </if>
                <if test="item.deleted != null">
                    deleted = #{item.deleted},
                </if>
                <if test="item.type != null">
                    `type` = #{item.type},
                </if>
                <if test="item.source != null">
                    source = #{item.source},
                </if>
            </set>
            where profile_id=#{item.profileId} and id=#{item.id}
        </foreach>
    </update>

    <select id="queryPlatformStoreByUnique"
            resultType="java.lang.Integer">
        select count(*)
        from pl_eshop_platform_store_mapping
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and platform_store_stock_id = #{platformStoreStockId}
    </select>


    <select id="queryPlatformStoreByEshopId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopPlatformStoreMapping">
        select
        pepsm.eshop_id, pepsm.id, pepsm.profile_id,pepsm.platform_store_stock_id, pepsm.ktype_id, pepsm.create_time,
        pepsm.update_time,pepsm.platform_store_name,pepsm.source,pepsm.deleted,
        pepsm.platform_store_address, pepsm.platform_store_type, pepsm.business_label,pepsm.correspond_flag,
        bo.fullname as 'otypeName'
        from pl_eshop_platform_store_mapping pepsm
        left join base_otype bo on pepsm.eshop_id = bo.id
        <where>

            <if test="profileId != null">
                and pepsm.profile_id = #{profileId}
            </if>
            <if test="eshopId != null  and eshopId != 0">
                and pepsm.eshop_id = #{eshopId}
            </if>
            <if test="id != null">
                and pepsm.id = #{id}
            </if>
            <if test="platformStoreStockId != null ">
                and pepsm.platform_store_stock_id = #{platformStoreStockId}
            </if>
            <if test="ktypeId != null">
                and pepsm.ktype_id = #{ktypeId}
            </if>
            <if test="createTime != null">
                and pepsm.create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and pepsm.update_time = #{updateTime}
            </if>
            <if test="platformStoreName != null and platformStoreName != ''">
                and pepsm.platform_store_name like CONCAT('%',#{platformStoreName},'%')
            </if>
            <if test="platformStoreAddress != null ">
                and pepsm.platform_store_address like CONCAT('%',#{platformStoreAddress},'%')
            </if>
            <if test="platformStoreType != null and platformStoreType != 0">
                and pepsm.platform_store_type = #{platformStoreType}
            </if>
            <if test="businessLabel != null and businessLabel != ''">
                and pepsm.business_label = #{businessLabel}
            </if>
            <if test="correspondFlag != null and correspondFlag != 2">
                and pepsm.correspond_flag = #{correspondFlag}
            </if>
            <if test="deleted != null">
                and pepsm.deleted = #{deleted}
            </if>
            <if test="type != null">
                and pepsm.type = #{type}
            </if>
            <if test="source != null">
                and pepsm.source = #{source}
            </if>
            Limit 1
        </where>
    </select>

    <select id="getMappingKtypeIdByStoreCode"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopPlatformStoreMapping">
        SELECT bk.id as ktypeId, bk.stoped as ktypeStoped, bk.deleted as ktypeDeleted, pepsm.platform_store_name
        FROM pl_eshop_platform_store_mapping pepsm
                 LEFT JOIN base_ktype bk ON bk.id = pepsm.ktype_id
        WHERE pepsm.profile_id = #{profileId}
          AND pepsm.platform_store_stock_id = #{storeCode}
          AND pepsm.eshop_id = #{otypeId}
          AND pepsm.deleted = 0 Limit 1
    </select>
    <select id="getplatformStoreStockId" resultType="java.lang.String">
        select platform_store_stock_id
        from pl_eshop_platform_store_mapping
        where profile_id = #{profileId}
          and ktype_id = #{ktypeId}
          and eshop_id = #{otypeId}
    </select>

    <select id="getEshopAutoPickKtypeConfig"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.AutoPickKtypeInfoEntity">
        select eshop_id,
               profile_id,
               is_auto_pick_ktype,
               pick_ktype_strategy_type,
               ktype_range_str,
               default_strategy_type,
               default_ktype
        from pl_eshop_auto_pick_ktype_config
        where profile_id = #{profileId}
          and eshop_id = #{eshopId} limit 1
    </select>

    <select id="getEshopAutoPickKtypeConfigByEshopIds"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.AutoPickKtypeInfoEntity">
        select eshop_id, profile_id, is_auto_pick_ktype, pick_ktype_strategy_type, ktype_range_str,
        default_strategy_type, default_ktype
        from pl_eshop_auto_pick_ktype_config
        where profile_id=#{profileId} and eshop_id in
        <foreach collection="eshopIds" item="eshopId" separator="," open="(" close=")">
            #{eshopId}
        </foreach>
    </select>
    <select id="checkEshopPlatformStoreMappingExist"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.eshop.EshopPlatformStoreMapping">
        select id from pl_eshop_platform_store_mapping where profile_id=#{profileId} and deleted=0 and correspond_flag=1 limit 1
    </select>

    <insert id="insertEshopAutoPickKtypeConfig">
        INSERT INTO pl_eshop_auto_pick_ktype_config (eshop_id, profile_id, is_auto_pick_ktype, pick_ktype_strategy_type,
                                                     ktype_range_str, default_strategy_type, default_ktype)
        VALUES ( #{eshopId}, #{profileId}, #{isAutoPickKtype}, #{pickKtypeStrategyType}, #{ktypeRangeStr}
               , #{defaultStrategyType}
               , #{defaultKtype}) ON DUPLICATE KEY
        UPDATE `is_auto_pick_ktype`= #{isAutoPickKtype},
            `pick_ktype_strategy_type`=#{pickKtypeStrategyType},
            `ktype_range_str`=#{ktypeRangeStr},
            `default_strategy_type`=#{defaultStrategyType},
            `default_ktype`=#{defaultKtype};
    </insert>

    <delete id="deletePlatformStoreMappingByPlatformId">
        DELETE FROM pl_eshop_platform_store_mapping
        WHERE profile_id = #{profileId}
          AND eshop_id = #{eshopId}
          AND platform_store_stock_id = #{platformStoreId}
    </delete>
</mapper>
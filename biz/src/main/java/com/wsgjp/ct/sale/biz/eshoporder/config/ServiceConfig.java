package com.wsgjp.ct.sale.biz.eshoporder.config;

import com.wsgjp.ct.common.enums.core.enums.ShopType;
import com.wsgjp.ct.sale.biz.member.utils.StringUtils;
import com.wsgjp.ct.support.annotation.DefaultValue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "eshop-order.common")
public class ServiceConfig {
    private static final Logger logger = LoggerFactory.getLogger(ServiceConfig.class);
    public ServiceConfig() {
        this.saleOrderPageSize = 10;
        this.orderPollSize = 10;
        this.maxPollSize = 20;
        this.threadKeepAliveTime = 10L;
        this.allowRepeatAuth = false;
    }

    private boolean encrypttokenInfo;

    //网店商品对应判断对应是否是只支持种对应方
    private String OnlyOneModeToRelation = "5,7,19,29,10,16,31,25,37,50,51,54,55,56,64,65,77,78,86,95";
    private String authCheckByOnlineEshopId = "79,154";
    private String needCheckShopAccountShoptype = "0,1,82,4,90,91,135,130,2,38,69,70,132,133,149,20,87,83,52,103,113,146,148,40,79,119,134,114";
    //允许网店商品上下架的网店类型
    private String allowEshopProductUpDown = "0,1,2,3,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,61,62,63,64,65,66,67,72,73,74,75,76,77,78,79,80,81,83,84,85,86,88,89,90,91,92,94,95,96,97,98,99,100,107,108,114,113,115,119,124,131,130,134,145";
    private String supportCreateInitStockShopType = "0,1,2,4,20,38,40,52,79";
    private String authHelperUrlList = "";
    private String notSupportCreateEshopCorrespondShopType = "889";
    private String checkDoAuthByWriteInfo = "90,85,62";
    //允许网店商品发布的网店类型
    private String allowEshopProductPublish = "0,2,20,38,52,79,126";
    //帮助中心地址
    private String helpUrlConfig;
    // 不支持增】功能的店铺类型列表
    private String notSupportCreateShopTypes = "110";
    private String supportProductIncreamentDownload = "0,1,20,40,38,145";
    private String supportTmcProductSyncShopType = "20,52";
    private String newEshopSign = "";
    private String getShoptypeHasSort = "";
    //根据货号刷新商品
    private String supportProductByArticleNumber = "52,95,164";
    private int saleOrderPageSize;
    private int orderPollSize;
    private int maxPollSize;
    private Long threadKeepAliveTime;
    private String appName = "NGP零售";
    private String refundUrl = "http://www.wsgjp.com.cn/Apply.aspx";

    private String refundUrlNew = "http://www.wsgjp.com.cn/Apply.aspx";

    private Boolean useNewRefundUrlEnabled = false;
    private String rsaKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCo7Sg4kD6QTpy8TmoJ0Br+18uochxdugkZvbc7CF4+ZxEbNmHJWpPHs1DMxZIhfToFEHYNcWpSZpX0W1pnMqrNzLaYoMmdnzXaVfynoM55/ux6fuelUpCJgR4bhXvJbSgR8QAepB41ArBAABuZ02lIqb+Z7iXc77CSHqDGRSUcKwIDAQAB";
    private boolean allowRepeatAuth;
    //部署级别配置 执行检查授权书
    private boolean openRealStockQty = false;
    private int relationSizeByOnce = 100;
    /**
     * 锁过期时间，30s
     */
    @DefaultValue(value = "30")
    private int checkProductPublishLockTimeout;
    private String needCooperationShopTypes = "";
    private String supportTmcOrderShopTypes = "0,1,2,20,49,52,59,71,79,85,90,91,110,113,114,116,119,120,124,130,148,162";
    private String supportOrderSingleSyncShopTypes = "82";
    private String unSupportAutoSyncStockShopTypes = "123";
    private Long lockOrderSingleSyncBeforeSendSeconds = 3600L;
    private Long lockOrderSingleSyncAfterSendSeconds = 7200L;

    private String refundNoOidShopTypes = "90,91,108,148,152,157,158,159,162";
    private String refundOnlyOidShopTypes = "40";
    private String cantDecryptAfterSendShopTypes = "20,79";
    private String cantDecryptByCloseOrderShopTypes = "0,1";
    private boolean orderManualSubmitUseThreadPool = true;
    private String stockCalcVersion = "53";
    private String syncStockAllowShowMark = "1001,1002,1007,1012,1013,1014,1016";

    //库存同步支持时效的平台允许的时效类型
    private String stockSyncTimingTypes = "1,3,5,7,9";
    private String needCheckQicShopType = "52";

    //不需要通知授权过期提前提醒的网店类型
    private String notNotifyAuthBeforeExpireShopTypes = "52,146";
    /**
     *提交增加明细提交唯一性保证数据提交不重复开关
     */
    private Boolean enableSubmitDetailPrimaryKey;

    /**
     *订单下载使用新通知方案
     */
    private boolean useNewOrderNotify = false;
    private String noNeedCheckAuthShopccountShopTypes = "";

    private int autoRepairProductRelationIntervalHour = 20;

    private String supportCheckSubscribeShopTypes = "0,1,3,20,90,91,108,144";


    public boolean isUseNewOrderNotify() {
        return useNewOrderNotify;
    }

    public void setUseNewOrderNotify(boolean useNewOrderNotify) {
        this.useNewOrderNotify = useNewOrderNotify;
    }

    public Boolean getEnableSubmitDetailPrimaryKey() {
        if (null == enableSubmitDetailPrimaryKey){
            return true;
        }
        return enableSubmitDetailPrimaryKey;
    }
    public void setEnableSubmitDetailPrimaryKey(Boolean enableSubmitDetailPrimaryKey) {
        this.enableSubmitDetailPrimaryKey = enableSubmitDetailPrimaryKey;
    }

    public String getNeedCheckShopAccountShoptype() {
        return needCheckShopAccountShoptype;
    }

    public void setNeedCheckShopAccountShoptype(String needCheckShopAccountShoptype) {
        this.needCheckShopAccountShoptype = needCheckShopAccountShoptype;
    }

    public String getNeedCheckQicShopType() {
        return needCheckQicShopType;
    }

    public void setNeedCheckQicShopType(String needCheckQicShopType) {
        this.needCheckQicShopType = needCheckQicShopType;
    }

    public boolean isEncrypttokenInfo() {
        return encrypttokenInfo;
    }

    public void setEncrypttokenInfo(boolean encrypttokenInfo) {
        this.encrypttokenInfo = encrypttokenInfo;
    }

    public String getSyncStockAllowShowMark() {
        return syncStockAllowShowMark;
    }

    public void setSyncStockAllowShowMark(String syncStockAllowShowMark) {
        this.syncStockAllowShowMark = syncStockAllowShowMark;
    }

    public String getRefundOnlyOidShopTypes() {
        return refundOnlyOidShopTypes;
    }

    public void setRefundOnlyOidShopTypes(String refundOnlyOidShopTypes) {
        this.refundOnlyOidShopTypes = refundOnlyOidShopTypes;
    }

    public String getAuthHelperUrlList() {
        return authHelperUrlList;
    }

    public void setAuthHelperUrlList(String authHelperUrlList) {
        this.authHelperUrlList = authHelperUrlList;
    }

    public String getGetShoptypeHasSort() {
        return getShoptypeHasSort;
    }

    public void setGetShoptypeHasSort(String getShoptypeHasSort) {
        this.getShoptypeHasSort = getShoptypeHasSort;
    }

    public String getNewEshopSign() {
        return newEshopSign;
    }

    public void setNewEshopSign(String newEshopSign) {
        this.newEshopSign = newEshopSign;
    }

    public String getAuthCheckByOnlineEshopId() {
        return authCheckByOnlineEshopId;
    }

    public void setAuthCheckByOnlineEshopId(String authCheckByOnlineEshopId) {
        this.authCheckByOnlineEshopId = authCheckByOnlineEshopId;
    }

    public boolean isOrderManualSubmitUseThreadPool() {
        return orderManualSubmitUseThreadPool;
    }

    public void setOrderManualSubmitUseThreadPool(boolean orderManualSubmitUseThreadPool) {
        this.orderManualSubmitUseThreadPool = orderManualSubmitUseThreadPool;
    }

    /**
     * 只通过OpenAddressId来判断地址变更的平台
     */
    private String buyerInfoNotifyByOpenAddressIdShopTypes = "52";
    /**
     * 矫正订单金额开关
     */
    private boolean correctOrderTotal = false;

    public boolean isOpenRealStockQty() {
        return openRealStockQty;
    }

    public void setOpenRealStockQty(boolean openRealStockQty) {
        this.openRealStockQty = openRealStockQty;
    }

    public boolean getCorrectOrderTotal() {
        return correctOrderTotal;
    }

    public void setCorrectOrderTotal(boolean correctOrderTotal) {
        this.correctOrderTotal = correctOrderTotal;
    }

    public String getBuyerInfoNotifyByOpenAddressIdShopTypes() {
        return buyerInfoNotifyByOpenAddressIdShopTypes;
    }

    public void setBuyerInfoNotifyByOpenAddressIdShopTypes(String buyerInfoNotifyByOpenAddressIdShopTypes) {
        this.buyerInfoNotifyByOpenAddressIdShopTypes = buyerInfoNotifyByOpenAddressIdShopTypes;
    }

    public String getCantDecryptByCloseOrderShopTypes() {
        return cantDecryptByCloseOrderShopTypes;
    }

    public void setCantDecryptByCloseOrderShopTypes(String cantDecryptByCloseOrderShopTypes) {
        this.cantDecryptByCloseOrderShopTypes = cantDecryptByCloseOrderShopTypes;
    }

    private Boolean qmNotifyDirectConsumerEnabled = true;

    public String getSupportProductByArticleNumber() {
        return supportProductByArticleNumber;
    }

    public void setSupportProductByArticleNumber(String supportProductByArticleNumber) {
        this.supportProductByArticleNumber = supportProductByArticleNumber;
    }

    public int getCheckProductPublishLockTimeout() {
        return checkProductPublishLockTimeout;
    }

    public void setCheckProductPublishLockTimeout(int checkProductPublishLockTimeout) {
        this.checkProductPublishLockTimeout = checkProductPublishLockTimeout;
    }

    /**
     * 对应使用缓存
     */
    private boolean openRelationRedisCache = false;

    /**
     * 缓存过期时间 单位分钟
     */
    private int relationRedisCacheExpireTime = 30;
    /**
     * 未对应缓存过期时间 单位秒
     */
    private int unRelationRedisCacheExpireTime = 180;

    private String stockStatisticsIgnoreVchtypes = "3100";

    private String noNeedNotifyOrderMarks = "91210003,10000016,200046,90200002,10000017,90790003,90790004,200081,90790007";
    private String noNeedSubmitOrderMarks = "90790007";

    public String getSupportCreateInitStockShopType() {
        return supportCreateInitStockShopType;
    }

    public void setSupportCreateInitStockShopType(String supportCreateInitStockShopType) {
        this.supportCreateInitStockShopType = supportCreateInitStockShopType;
    }

    private boolean closeIncreaseDownload = false;
    private boolean closeCompensateDownload = false;

    private int anaSendQtyCheck = 10;
    private boolean useAnaSendQtyCheck = false;

    /**
     * 单次提交最大数量
     */
    private int submitOrderCount = 500;

    private String shopTypeFilesRedisVersion="V1.0";


    public String getNotSupportCreateEshopCorrespondShopType() {
        return notSupportCreateEshopCorrespondShopType;
    }

    public void setNotSupportCreateEshopCorrespondShopType(String notSupportCreateEshopCorrespondShopType) {
        this.notSupportCreateEshopCorrespondShopType = notSupportCreateEshopCorrespondShopType;
    }

    public int getSubmitOrderCount() {
        return submitOrderCount;
    }

    public void setSubmitOrderCount(int submitOrderCount) {
        this.submitOrderCount = submitOrderCount;
    }

    public int getUnRelationRedisCacheExpireTime() {
        return unRelationRedisCacheExpireTime;
    }

    public void setUnRelationRedisCacheExpireTime(int unRelationRedisCacheExpireTime) {
        this.unRelationRedisCacheExpireTime = unRelationRedisCacheExpireTime;
    }

    public String getHelpUrlConfig() {
        return helpUrlConfig;
    }

    public void setHelpUrlConfig(String helpUrlConfig) {
        this.helpUrlConfig = helpUrlConfig;
    }

    public String getAllowEshopProductPublish() {
        return allowEshopProductPublish;
    }

    public void setAllowEshopProductPublish(String allowEshopProductPublish) {
        this.allowEshopProductPublish = allowEshopProductPublish;
    }

    //小时
    private int syncOrderSingleUpdateTimeLeftOffset = 2;
    //天
    private int syncOrderSingleCreateTimeLeftOffset = 7;

    //小时
    private int syncOrderSingleCreateTimeRightOffset = 12;

    public int getSyncOrderSingleUpdateTimeLeftOffset() {
        return syncOrderSingleUpdateTimeLeftOffset;
    }

    public void setSyncOrderSingleUpdateTimeLeftOffset(int syncOrderSingleUpdateTimeLeftOffset) {
        this.syncOrderSingleUpdateTimeLeftOffset = syncOrderSingleUpdateTimeLeftOffset;
    }

    public String getNoNeedNotifyOrderMarks() {
        return noNeedNotifyOrderMarks;
    }

    public void setNoNeedNotifyOrderMarks(String noNeedNotifyOrderMarks) {
        this.noNeedNotifyOrderMarks = noNeedNotifyOrderMarks;
    }

    public String getSupportProductIncreamentDownload() {
        return supportProductIncreamentDownload;
    }

    public void setSupportProductIncreamentDownload(String supportProductIncreamentDownload) {
        this.supportProductIncreamentDownload = supportProductIncreamentDownload;
    }

    public String getOnlyOneModeToRelation() {
        return OnlyOneModeToRelation;
    }

    public void setOnlyOneModeToRelation(String onlyOneModeToRelation) {
        OnlyOneModeToRelation = onlyOneModeToRelation;
    }

    public String getAllowEshopProductUpDown() {
        return allowEshopProductUpDown;
    }

    public void setAllowEshopProductUpDown(String allowEshopProductUpDown) {
        this.allowEshopProductUpDown = allowEshopProductUpDown;
    }

    public String getRefundUrl() {
        return refundUrl;
    }

    public void setRefundUrl(String refundUrl) {
        this.refundUrl = refundUrl;
    }

    public String getRsaKey() {
        return rsaKey;
    }

    public void setRsaKey(String rsaKey) {
        this.rsaKey = rsaKey;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public int getSaleOrderPageSize() {
        return saleOrderPageSize;
    }

    public void setSaleOrderPageSize(int saleOrderPageSize) {
        this.saleOrderPageSize = saleOrderPageSize;
    }

    public int getOrderPollSize() {
        return orderPollSize;
    }

    public void setOrderPollSize(int orderPollSize) {
        this.orderPollSize = orderPollSize;
    }

    public int getMaxPollSize() {
        return maxPollSize;
    }

    public void setMaxPollSize(int maxPollSize) {
        this.maxPollSize = maxPollSize;
    }

    public Long getThreadKeepAliveTime() {
        return threadKeepAliveTime;
    }

    public void setThreadKeepAliveTime(Long threadKeepAliveTime) {
        this.threadKeepAliveTime = threadKeepAliveTime;
    }

    public boolean isAllowRepeatAuth() {
        return allowRepeatAuth;
    }

    public void setAllowRepeatAuth(boolean allowRepeatAuth) {
        this.allowRepeatAuth = allowRepeatAuth;
    }

    public boolean isOpenRelationRedisCache() {
        return openRelationRedisCache;
    }

    public void setOpenRelationRedisCache(boolean openRelationRedisCache) {
        this.openRelationRedisCache = openRelationRedisCache;
    }

    public int getRelationRedisCacheExpireTime() {
        return relationRedisCacheExpireTime;
    }

    public void setRelationRedisCacheExpireTime(int relationRedisCacheExpireTime) {
        this.relationRedisCacheExpireTime = relationRedisCacheExpireTime;
    }

    public String getNotSupportCreateShopTypes() {
        return notSupportCreateShopTypes;
    }

    public void setNotSupportCreateShopTypes(String notSupportCreateShopTypes) {
        this.notSupportCreateShopTypes = notSupportCreateShopTypes;
    }

    public int getRelationSizeByOnce() {
        return relationSizeByOnce;
    }

    public void setRelationSizeByOnce(int relationSizeByOnce) {
        this.relationSizeByOnce = relationSizeByOnce;
    }

    public String getStockStatisticsIgnoreVchtypes() {
        if (stockStatisticsIgnoreVchtypes == null) {
            stockStatisticsIgnoreVchtypes = "3100";
        }
        return stockStatisticsIgnoreVchtypes;
    }

    public void setStockStatisticsIgnoreVchtypes(String stockStatisticsIgnoreVchtypes) {
        this.stockStatisticsIgnoreVchtypes = stockStatisticsIgnoreVchtypes;
    }

    public List<Integer> getIgnoreVchtypes() {
        List<Integer> result = new ArrayList<>();
        String[] split = stockStatisticsIgnoreVchtypes.split(",");
        for (String item : split) {
            result.add(Integer.parseInt(item));
        }
        return result;
    }

    public String getNeedCooperationShopTypes() {
        return needCooperationShopTypes;
    }

    public void setNeedCooperationShopTypes(String needCooperationShopTypes) {
        this.needCooperationShopTypes = needCooperationShopTypes;
    }

    public String getSupportTmcOrderShopTypes() {
        return supportTmcOrderShopTypes;
    }

    public void setSupportTmcOrderShopTypes(String supportTmcOrderShopTypes) {
        this.supportTmcOrderShopTypes = supportTmcOrderShopTypes;
    }

    public String getRefundNoOidShopTypes() {
        return refundNoOidShopTypes;
    }

    public void setRefundNoOidShopTypes(String refundNoOidShopTypes) {
        this.refundNoOidShopTypes = refundNoOidShopTypes;
    }

    public String getCantDecryptAfterSendShopTypes() {
        return cantDecryptAfterSendShopTypes;
    }

    public void setCantDecryptAfterSendShopTypes(String cantDecryptAfterSendShopTypes) {
        this.cantDecryptAfterSendShopTypes = cantDecryptAfterSendShopTypes;
    }

    public String getSupportOrderSingleSyncShopTypes() {
        return supportOrderSingleSyncShopTypes;
    }

    public void setSupportOrderSingleSyncShopTypes(String supportOrderSingleSyncShopTypes) {
        this.supportOrderSingleSyncShopTypes = supportOrderSingleSyncShopTypes;
    }

    public Long getLockOrderSingleSyncBeforeSendSeconds() {
        return lockOrderSingleSyncBeforeSendSeconds;
    }

    public void setLockOrderSingleSyncBeforeSendSeconds(Long lockOrderSingleSyncBeforeSendSeconds) {
        this.lockOrderSingleSyncBeforeSendSeconds = lockOrderSingleSyncBeforeSendSeconds;
    }

    public Long getLockOrderSingleSyncAfterSendSeconds() {
        return lockOrderSingleSyncAfterSendSeconds;
    }

    public void setLockOrderSingleSyncAfterSendSeconds(Long lockOrderSingleSyncAfterSendSeconds) {
        this.lockOrderSingleSyncAfterSendSeconds = lockOrderSingleSyncAfterSendSeconds;
    }

    public String getUnSupportAutoSyncStockShopTypes() {
        return unSupportAutoSyncStockShopTypes;
    }

    public void setUnSupportAutoSyncStockShopTypes(String unSupportAutoSyncStockShopTypes) {
        this.unSupportAutoSyncStockShopTypes = unSupportAutoSyncStockShopTypes;
    }

    public boolean isCloseIncreaseDownload() {
        return closeIncreaseDownload;
    }

    public void setCloseIncreaseDownload(boolean closeIncreaseDownload) {
        this.closeIncreaseDownload = closeIncreaseDownload;
    }

    public boolean isCloseCompensateDownload() {
        return closeCompensateDownload;
    }

    public void setCloseCompensateDownload(boolean closeCompensateDownload) {
        this.closeCompensateDownload = closeCompensateDownload;
    }

    public int getAnaSendQtyCheck() {
        return anaSendQtyCheck;
    }

    public void setAnaSendQtyCheck(int anaSendQtyCheck) {
        this.anaSendQtyCheck = anaSendQtyCheck;
    }

    public boolean isUseAnaSendQtyCheck() {
        return useAnaSendQtyCheck;
    }

    public void setUseAnaSendQtyCheck(boolean useAnaSendQtyCheck) {
        this.useAnaSendQtyCheck = useAnaSendQtyCheck;
    }

    public Boolean getQmNotifyDirectConsumerEnabled() {
        return qmNotifyDirectConsumerEnabled;
    }

    public void setQmNotifyDirectConsumerEnabled(Boolean qmNotifyDirectConsumerEnabled) {
        this.qmNotifyDirectConsumerEnabled = qmNotifyDirectConsumerEnabled;
    }

    public String getRefundUrlNew() {
        return refundUrlNew;
    }

    public void setRefundUrlNew(String refundUrlNew) {
        this.refundUrlNew = refundUrlNew;
    }

    public Boolean getUseNewRefundUrlEnabled() {
        return useNewRefundUrlEnabled;
    }

    public void setUseNewRefundUrlEnabled(Boolean useNewRefundUrlEnabled) {
        this.useNewRefundUrlEnabled = useNewRefundUrlEnabled;
    }

    public String getSupportTmcProductSyncShopType() {
        return supportTmcProductSyncShopType;
    }

    public void setSupportTmcProductSyncShopType(String supportTmcProductSyncShopType) {
        this.supportTmcProductSyncShopType = supportTmcProductSyncShopType;
    }

    public String getStockCalcVersion() {
        return stockCalcVersion;
    }

    public void setStockCalcVersion(String stockCalcVersion) {
        this.stockCalcVersion = stockCalcVersion;
    }

    public int getSyncOrderSingleCreateTimeLeftOffset() {
        return syncOrderSingleCreateTimeLeftOffset;
    }

    public void setSyncOrderSingleCreateTimeLeftOffset(int syncOrderSingleCreateTimeLeftOffset) {
        this.syncOrderSingleCreateTimeLeftOffset = syncOrderSingleCreateTimeLeftOffset;
    }

    public String getStockSyncTimingTypes() {
        return stockSyncTimingTypes;
    }

    public void setStockSyncTimingTypes(String stockSyncTimingTypes) {
        this.stockSyncTimingTypes = stockSyncTimingTypes;
    }

    public String getCheckDoAuthByWriteInfo() {
        return checkDoAuthByWriteInfo;
    }

    public void setCheckDoAuthByWriteInfo(String checkDoAuthByWriteInfo) {
        this.checkDoAuthByWriteInfo = checkDoAuthByWriteInfo;
    }

    public int getSyncOrderSingleCreateTimeRightOffset() {
        return syncOrderSingleCreateTimeRightOffset;
    }

    public void setSyncOrderSingleCreateTimeRightOffset(int syncOrderSingleCreateTimeRightOffset) {
        this.syncOrderSingleCreateTimeRightOffset = syncOrderSingleCreateTimeRightOffset;
    }


    public List<ShopType> getNotNotifyAuthBeforeAuthExpireShopTypes() {
        if (StringUtils.isBlank(notNotifyAuthBeforeExpireShopTypes)) {
            return null;
        }
        List<ShopType> shopTypes = new ArrayList<>();
        for (String item : notNotifyAuthBeforeExpireShopTypes.split(",")) {
            if (StringUtils.isEmpty(item)) {
                continue;
            }
            try {
                shopTypes.add(ShopType.valueOf(Integer.parseInt(item)));
            } catch (Exception e) {
                logger.error("notNotifyAuthBeforeExpireShopTypes error", e);
            }
        }
        return shopTypes;
    }

    public String getNotNotifyAuthBeforeExpireShopTypes() {
        return notNotifyAuthBeforeExpireShopTypes;
    }

    public void setNotNotifyAuthBeforeExpireShopTypes(String notNotifyAuthBeforeExpireShopTypes) {
        this.notNotifyAuthBeforeExpireShopTypes = notNotifyAuthBeforeExpireShopTypes;
    }

    public int getAutoRepairProductRelationIntervalHour() {
        return autoRepairProductRelationIntervalHour;
    }

    public void setAutoRepairProductRelationIntervalHour(int autoRepairProductRelationIntervalHour) {
        this.autoRepairProductRelationIntervalHour = autoRepairProductRelationIntervalHour;
    }

    public String getNoNeedCheckAuthShopccountShopTypes() {
        return noNeedCheckAuthShopccountShopTypes;
    }

    public void setNoNeedCheckAuthShopccountShopTypes(String noNeedCheckAuthShopccountShopTypes) {
        this.noNeedCheckAuthShopccountShopTypes = noNeedCheckAuthShopccountShopTypes;
    }

    public String getSupportCheckSubscribeShopTypes() {
        return supportCheckSubscribeShopTypes;
    }

    public void setSupportCheckSubscribeShopTypes(String supportCheckSubscribeShopTypes) {
        this.supportCheckSubscribeShopTypes = supportCheckSubscribeShopTypes;
    }

    public String getNoNeedSubmitOrderMarks() {
        return noNeedSubmitOrderMarks;
    }

    public void setNoNeedSubmitOrderMarks(String noNeedSubmitOrderMarks) {
        this.noNeedSubmitOrderMarks = noNeedSubmitOrderMarks;
    }

    public String getShopTypeFilesRedisVersion() {
        if(StringUtils.isEmpty(shopTypeFilesRedisVersion)){
            return "";
        }
        return shopTypeFilesRedisVersion;
    }

    public void setShopTypeFilesRedisVersion(String shopTypeFilesRedisVersion) {
        this.shopTypeFilesRedisVersion = shopTypeFilesRedisVersion;
    }
}

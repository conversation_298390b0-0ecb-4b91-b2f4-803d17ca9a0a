<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.wsgjp.ct</groupId>
    <artifactId>sale</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>
    <parent>
        <artifactId>ngp-project</artifactId>
        <groupId>ngp</groupId>
        <version>2.0.5.5-RELEASE</version>
        <relativePath/>
    </parent>
    <properties>
        <common.version>6.0.20250723.01-RELEASE</common.version>
        <common.enum.core.version>6.0.20250731.01-RELEASE</common.enum.core.version>
        <platfrom.version>6.0.20250723.01-RELEASE</platfrom.version>
        <sale.sdk.version>6.0.20250730.01-RELEASE</sale.sdk.version>
        <bill.core.version>5.9.0.20-RELEASE</bill.core.version>
        <wms.sdk.version>6.0.20250725.01-RELEASE</wms.sdk.version>
        <sys.support.version>5.6.4.6-RELEASE</sys.support.version>
        <ngp.mq.version>2.0.5.5-RELEASE</ngp.mq.version>
        <sis.client.version>5.9.20250721.01-RELEASE</sis.client.version>
    </properties>
    <modules>
        <module>common</module>
        <module>monitor</module>
        <module>platform</module>
        <module>platform-sdk</module>
        <module>biz</module>
        <module>tool</module>
        <module>web</module>
        <module>sale-bus</module>
        <!--        <module>sale-sdk</module>-->
        <!--        <module>common-enum-core</module>-->
    </modules>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>ngp</groupId>
                <artifactId>ngp-datasource</artifactId>
                <version>2.0.5-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.12.0</version>
            </dependency>
            <dependency>
                <groupId>com.wsgjp.ct</groupId>
                <artifactId>sale-sdk</artifactId>
                <version>${sale.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wsgjp.ct.sale</groupId>
                <artifactId>platform</artifactId>
                <version>${platfrom.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wsgjp.ct.sale</groupId>
                <artifactId>platform-sdk</artifactId>
                <version>${platfrom.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.wsgjp.ct.sale</groupId>
                        <artifactId>platform</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.wsgjp.ct</groupId>
                <artifactId>common</artifactId>
                <version>${common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.wsgjp.ct</groupId>
                        <artifactId>bill-core-handle</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.wsgjp.ct</groupId>
                <artifactId>monitor</artifactId>
                <version>${common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.wsgjp.ct</groupId>
                        <artifactId>common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>com.wsgjp.ct</groupId>
            <artifactId>sys-support</artifactId>
            <version>${sys.support.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wsgjp.ct</groupId>
                    <artifactId>menu-permission</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wsgjp.ct</groupId>
            <artifactId>menu-permission</artifactId>
            <version>6.0.7-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.wsgjp.ct</groupId>
            <artifactId>business-datasource</artifactId>
            <version>2.0.5-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>ngpd</groupId>
                    <artifactId>ngpd-route</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>ngp</groupId>
            <artifactId>ngp-monitor</artifactId>
        </dependency>

        <dependency>
            <groupId>ngp</groupId>
            <artifactId>ngp-utils</artifactId>
            <version>2.0.5.6-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>ngp</groupId>
            <artifactId>ngp-mq</artifactId>
            <version>${ngp.mq.version}</version>
        </dependency>
        <dependency>
            <groupId>ngp-service-component</groupId>
            <artifactId>ngp-service-component-job</artifactId>
            <version>2.0.5.5-RELEASE</version>
        </dependency>

    </dependencies>

    <repositories>
        <repository>
            <id>local</id>
            <name>local</name>
            <url>http://172.17.0.241:8081/repository/maven-public/</url>
        </repository>

        <repository>
            <id>remote</id>
            <name>remote</name>
            <url>http://nexus.mygjp.com.cn/repository/maven-public/</url>
        </repository>
    </repositories>

    <profiles>

        <profile>
            <id>local</id>
            <distributionManagement>
                <repository>
                    <id>local-releases</id>
                    <name>local-releases</name>
                    <url>http://172.17.0.241:8081/repository/maven-releases/</url>
                </repository>
                <snapshotRepository>
                    <id>local-snapshots</id>
                    <name>local-snapshots</name>
                    <url>http://172.17.0.241:8081/repository/maven-snapshots/</url>
                </snapshotRepository>
            </distributionManagement>
        </profile>

        <profile>
            <id>remote</id>
            <distributionManagement>
                <repository>
                    <id>remote-releases</id>
                    <name>remote-releases</name>
                    <url>http://nexus.mygjp.com.cn/repository/maven-releases/</url>
                </repository>
                <snapshotRepository>
                    <id>remote-snapshots</id>
                    <name>remote-snapshots</name>
                    <url>http://nexus.mygjp.com.cn/repository/maven-snapshots/</url>
                </snapshotRepository>
            </distributionManagement>
        </profile>
    </profiles>
</project>

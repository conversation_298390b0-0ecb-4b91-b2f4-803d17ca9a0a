<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopSenderMapper">
    <insert id="insertSender">
        insert into pl_sender
        (id, profile_id, sender_name, sender_mobile, sender_phone, sender_country, sender_province, sender_city, sender_district,
         sender_address, sender_full_address, hash_key,sender_zip_code,sender_town,pi,mi,di,ri,addri)
        value
        (#{id},#{profileId},#{senderName},#{senderMobile},#{senderPhone},#{senderCountry},#{senderProvince},#{senderCity},#{senderDistrict},
                #{senderAddress},#{senderFullAddress},#{hashKey},#{senderZipCode},#{senderTown},#{pi},#{mi},#{di},#{ri},#{addri})
    </insert>

    <update id="modifySender">
        update pl_sender
            set sender_mobile=#{senderMobile},
            sender_phone=#{senderPhone},
            sender_country=#{senderCountry},
            sender_province=#{senderProvince},
            sender_city=#{senderCity},
            sender_district=#{senderDistrict},
            sender_address=#{senderAddress},
            sender_full_address=#{senderFullAddress},
            sender_zip_code=#{senderZipCode},
            hash_key=#{hashKey},
            sender_town=#{senderTown}
        where profile_id=#{profileId} and id=#{id}
    </update>

    <select id="getSenderById" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.EshopSenderInfo">
        select * from pl_sender where profile_id=#{profileId} and id=#{id}
    </select>

    <select id="getSenderByKey" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.EshopSenderInfo">
        select * from pl_sender where profile_id=#{profileId} and hash_key =#{hashKey} limit 1
    </select>

    <select id="getSender" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.base.EshopSenderInfo">
        select * from pl_sender where profile_id=#{profileId}
        <if test="senderName!=null and senderName!=''">
            and sender_name like CONCAT('%',#{senderName},'%')
        </if>
        <if test="senderPhone!=null and senderPhone!=''">
            and sender_phone like CONCAT('%',#{senderPhone},'%')
        </if>
    </select>
</mapper>
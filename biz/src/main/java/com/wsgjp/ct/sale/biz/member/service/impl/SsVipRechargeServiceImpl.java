package com.wsgjp.ct.sale.biz.member.service.impl;

import bf.datasource.page.PageRequest;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wsgjp.ct.bill.core.handle.entity.dao.BillRelationDao;
import com.wsgjp.ct.bill.core.handle.entity.enums.BillBusinessType;
import com.wsgjp.ct.bill.core.handle.entity.enums.BillPostState;
import com.wsgjp.ct.bill.core.handle.entity.enums.Vchtypes;
import com.wsgjp.ct.framework.enums.AssertsChangeType;
import com.wsgjp.ct.framework.enums.AssertsSourceOperation;
import com.wsgjp.ct.framework.enums.AssertsSourceType;
import com.wsgjp.ct.pm.service.PermissionValiateService;
import com.wsgjp.ct.sale.biz.bill.service.BillInterface;
import com.wsgjp.ct.sale.biz.bill.service.impl.GoodsBillServiceImpl;
import com.wsgjp.ct.sale.biz.bill.mapper.TdBillCoreMapper;

import com.wsgjp.ct.sale.biz.eshoporder.util.GetBeanUtil;
import com.wsgjp.ct.sale.biz.member.common.PermissionShopSale;
import com.wsgjp.ct.sale.biz.member.common.RechargeChargeType;
import com.wsgjp.ct.sale.biz.member.common.ScoreStrategyType;
import com.wsgjp.ct.sale.biz.member.mapper.*;
import com.wsgjp.ct.sale.biz.member.model.dto.recharge.*;
import com.wsgjp.ct.sale.biz.member.model.dto.vip.ComparisonResult;
import com.wsgjp.ct.sale.biz.member.model.dto.vip.SelectLevelResponseDTO;
import com.wsgjp.ct.sale.biz.member.model.dto.vip.VipAssertsBillDetailDto;
import com.wsgjp.ct.sale.biz.member.model.entity.recharge.*;
import com.wsgjp.ct.sale.biz.member.model.entity.store.SsVipAsserts;
import com.wsgjp.ct.sale.biz.member.model.entity.vip.*;
import com.wsgjp.ct.sale.biz.member.service.*;
import com.wsgjp.ct.sale.biz.member.utils.CompareUtils;
import com.wsgjp.ct.sale.biz.member.utils.SVIPUtils;
import com.wsgjp.ct.sale.biz.shopsale.constanst.BillSaveResultTypeEnum;
import com.wsgjp.ct.sale.biz.shopsale.enums.BillSourceTypeEnum;
import com.wsgjp.ct.sale.biz.shopsale.mapper.BillMapper;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.base.BaseInfoLog;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.bill.*;
import com.wsgjp.ct.sale.biz.shopsale.model.dto.cashbox.CashBoxPaymentDTO;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.bill.PayResultInfo;
import com.wsgjp.ct.sale.biz.shopsale.model.entity.bill.PosBill;
import com.wsgjp.ct.sale.biz.shopsale.model.vo.baseinfo.Etype;
import com.wsgjp.ct.sale.biz.shopsale.service.BillService;
import com.wsgjp.ct.sale.biz.shopsale.service.CashBoxService;
import com.wsgjp.ct.sale.biz.shopsale.service.ShopSaleBaseInfoService;
import com.wsgjp.ct.sale.monitor.MonitorService;
import com.wsgjp.ct.sale.monitor.entity.MonitorTypeEnum;
import com.wsgjp.ct.sale.biz.shopsale.service.TaotaoguLogService;
import com.wsgjp.ct.sale.platform.utils.BeanUtils;
import com.wsgjp.ct.sale.sdk.payment.biz.AggregatePaymentService;
import com.wsgjp.ct.sale.sdk.payment.entity.request.AggregatePaymentRequest;
import com.wsgjp.ct.sale.sdk.payment.entity.request.PayOrderQueryRequest;
import com.wsgjp.ct.sale.sdk.payment.entity.response.PaymentInfo;
import com.wsgjp.ct.sale.sdk.payment.entity.response.PaymentResponse;
import com.wsgjp.ct.sale.sdk.payment.enums.PayStatusEnum;
import com.wsgjp.ct.sale.sdk.payment.enums.PaymentQueryTypeEnum;
import com.wsgjp.ct.sale.sdk.pos.biz.MemberAssertsChangeService;
import com.wsgjp.ct.sale.sdk.pos.entity.asserts.MemberAssert;
import com.wsgjp.ct.sale.sdk.pos.entity.asserts.MemberAssertsChange;
import com.wsgjp.ct.sale.sdk.pos.entity.asserts.VipAsserts;
import com.wsgjp.ct.support.context.CurrentUser;
import com.wsgjp.ct.support.log.service.LogService;
import ngp.idgenerator.UId;
import ngp.utils.DateUtils;
import ngp.utils.JsonUtils;
import ngp.utils.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class SsVipRechargeServiceImpl implements ISsVipRechargeService {

    @Autowired
    GoodsBillServiceImpl goodsBillService;
    ISsVipService vipService;

    @Autowired
    BillService billService;

    @Autowired
    ShopSaleBaseInfoService baseInfoService;

    @Autowired
    private SsVipRechargeMapper ssVipRechargeMapper;

    @Autowired
    private SsVipRechargeLevelMapper ssVipRechargeLevelMapper;

    @Autowired
    private SsVipRechargeOtypeMapper ssVipRechargeOtypeMapper;

    @Autowired
    private SsVipRechargeGearMapper ssVipRechargeGearMapper;

    @Autowired
    private SsVipRechargeGearDetailMapper ssVipRechargeGearDetailMapper;

    @Autowired
    private SsVipRechargeRuleMapper ssVipRechargeRuleMapper;

    @Autowired
    private SsVipRechargeRecordMapper recordMapper;

    @Autowired
    private BillInterface billInterface;

    @Autowired
    ReceiptBillService receiptBillService;

    @Autowired
    private SsVipMapper vipMapper;

    @Autowired
    private ISsVipLevelService levelService;

    @Autowired
    private ShopSaleBaseInfoService shopSaleBaseInfoService;

    @Autowired
    BillMapper billMapper;


    @Autowired
    CashBoxService cashBoxService;

    @Autowired
    private ISsVipRechargeRecordListService rechargeRecordListService;

    @Autowired
    private ISsVipLevelService vipLevelService;

    @Autowired
    private ISsVipAssertsChangeService vipAssertsChangeService;

    @Autowired
    private MemberAssertsChangeService memberAssertsChangeService;

    @Autowired
    private AggregatePaymentService aggregatePaymentService;

    @Autowired
    private TaotaoguLogService taotaoguLogService;

    @Autowired
    private TdBillCoreMapper tdBillCoreMapper;

    private final Logger logger = LoggerFactory.getLogger(getClass());

    public SsVipRechargeServiceImpl(ISsVipService vipService) {
        this.vipService = vipService;
    }

    @Override
    public RechargeDTO getRechargeById(BigInteger id) {
        List<RechargeDTO> rechargeList =
                GetBeanUtil.getBean(SsVipRechargeServiceImpl.class).getRechargeList(new GetRechargeListRequestDTO(id));
        if (rechargeList != null && !rechargeList.isEmpty()) {
            return rechargeList.get(0);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PageInfo<RechargeDTO> getRechargeListWithLevelNamesAndOtypeNames(PageRequest<GetRechargeListRequestDTO> request) {
        GetRechargeListRequestDTO params = request.getQueryParams();
        params.setProfileId(CurrentUser.getProfileId());
        Page<Object> page = PageHelper.startPage(request.getPageIndex(), request.getPageSize());
        List<RechargeDTO> list = ssVipRechargeMapper.getRechargeListWithLevelNamesAndOtypeNames(params);
        GetBeanUtil.getBean(SsVipRechargeServiceImpl.class).handleRechargeList(list, false);
        PageInfo<RechargeDTO> pageInfo = new PageInfo<>(list);
        pageInfo.setTotal(page.getTotal());
        return pageInfo;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public List<RechargeDTO> getRechargeList(GetRechargeListRequestDTO params) {
        if (params == null) {
            throw new RuntimeException("请检查请求参数");
        }
        BigInteger profileId = CurrentUser.getProfileId();
        params.setProfileId(profileId);
        List<RechargeDTO> list = ssVipRechargeMapper.getRechargeList(params);
        GetBeanUtil.getBean(SsVipRechargeServiceImpl.class).handleRechargeList(list, true);
        return list;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void handleRechargeList(List<RechargeDTO> list, boolean hasGearsAndDetails) {
        if (list != null && !list.isEmpty()) {
            List<BigInteger> rechargeIds = new ArrayList<>();
            Date now = new Date();
            String formatDate = DateUtils.formatDate(now, "yyyy-MM-dd ");
            for (RechargeDTO rechargeDTO : list) {
                if (rechargeDTO.getActivityState() == null) {
                    String timePattern = "yyyy-MM-dd HH:mm:ss";
                    //结束日期小于现在，说明已经结束
                    //这里需要处理一下结束日期，因为数据库中是date字段，会抹掉时间，再取出会变成00:00:00
                    if (DateUtils.parse(DateUtils.formatDate(rechargeDTO.getEndDate(), "yyyy-MM-dd 23:59:59"),
                            timePattern).compareTo(now) < 0) {
                        rechargeDTO.setActivityState(2);
                    } else if (rechargeDTO.getStartDate().compareTo(now) > 0) {
                        rechargeDTO.setActivityState(0);
                    } else {
                        if (now.compareTo(DateUtils.parse(formatDate + rechargeDTO.getEndTime(), timePattern)) >= 0 || now.compareTo(DateUtils.parse(formatDate + rechargeDTO.getStartTime(), timePattern)) < 0) {
                            rechargeDTO.setActivityState(3);
                        } else {
                            rechargeDTO.setActivityState(1);
                        }
                    }
                }
                rechargeDTO.setCanStop(rechargeDTO.getActivityState() != 2);
                //活动日期
                String activityDate =
                        DateUtils.formatDate(rechargeDTO.getStartDate(), "yyyy.MM.dd") + " ~ " + DateUtils.formatDate(rechargeDTO.getEndDate(), "yyyy.MM.dd");
                rechargeDTO.setActivityDate(activityDate);
                //活动时间
                String activityTime = rechargeDTO.getStartTime() + " ~ " + rechargeDTO.getEndTime();
                rechargeDTO.setActivityTime(activityTime);
                rechargeIds.add(rechargeDTO.getId());
            }
            if (hasGearsAndDetails) {
                Map<BigInteger, List<RechargeGearDTO>> groupBy =
                        ssVipRechargeMapper.getRechargeGearsByRechargeIds(CurrentUser.getProfileId(), rechargeIds).stream().collect(Collectors.groupingBy(SsVipRechargeGear::getRechargeId));
                if (!groupBy.isEmpty()) {
                    for (RechargeDTO rechargeDTO : list) {
                        rechargeDTO.setGears(groupBy.get(rechargeDTO.getId()));
                    }
                } else {
                    list.forEach(dto -> dto.setGears(new ArrayList<>()));
                }
            }
        }
    }

    @Override
    public void changeRechargeStoped(BigInteger id) {
        if (id == null) {
            throw new RuntimeException("充值策略id不能为空");
        }
        RechargeDTO oldRecharge = getRechargeById(id);
        ssVipRechargeMapper.changeRechargeStoped(id);

        BaseInfoLog log = new BaseInfoLog();
        log.setObjectId(id);
        String oldStop = oldRecharge.getStoped() == 0 ? "启用" : "停用";
        String newStop = "启用".equals(oldStop) ? "停用" : "启用";
        log.setBody(newStop + "充值策略  ");
        saveLog(log);
    }

    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> checkBeforeRecharge(VipRechargeDTO request, BigInteger vchcode) {
        long sTime = System.currentTimeMillis();
        if (request.getVipId() == null) {
            throw new RuntimeException("会员id不能为空");
        }
        if (request.getRechargeMoney() == null || request.getRechargeMoney().compareTo(BigDecimal.ZERO) < 0) {
            throw new RuntimeException("充值金额必须大于0");
        }
        if (request.getPayment() == null || request.getPayment().isEmpty()) {
            throw new RuntimeException("付款方式不能为空");
        }
        ///验证支付方式的合理性（因前期进销存的bug导致部分非预存款的支付方式存在现金银行为空的数据，避免影响单据数据异常，该部分操作需中断单据提交）
        vipService.checkValidPaymentList(request.getPayment());
        RechargeDTO recharge = null;
        List<SsVipRechargeRecord> records = null;
        BigInteger rechargeId = request.getRechargeId();
        //商品，积分，卡券只有充值活动才会送
        if (request.getCardId() != null || (request.getScore() != null && request.getScore() > 0) || request.getGoodsBill() != null) {
            if (rechargeId == null) {
                throw new RuntimeException("充值活动id不能为空");
            }
        }
        if (rechargeId != null) {
            boolean check = false;
            GetRechargeListRequestDTO params = new GetRechargeListRequestDTO(rechargeId);
            params.setStop(0);
            params.setActivityState(1);
            List<RechargeDTO> rechargeList =
                    GetBeanUtil.getBean(SsVipRechargeServiceImpl.class).getRechargeList(params);
            if (rechargeList != null && !rechargeList.isEmpty()) {
                recharge = rechargeList.get(0);
                if (recharge.getStoped() == 0
                        && recharge.getActivityState() == 1
                        && recharge.getOtypeIds().contains(request.getOtypeId())
                        && recharge.getVipLevelIds().contains(request.getVipLevelId())) {
                    records = recordMapper.getRechargeRecordByVipIdAndRechargeIds(CurrentUser.getProfileId(),
                            request.getVipId(), null);
                    if (checkRechargeEnabled(records, recharge)) {
                        check = true;
                    }
                }
            }
            if (!check) {
                throw new RuntimeException("当前充值活动无法使用");
            }
        }
        MonitorService monitorService = BeanUtils.getBean(MonitorService.class);
        monitorService.recordTP(MonitorTypeEnum.PL_API_SALE_POS_GET_RECHARGE_CHECK_TIME.getTopic(), "shopType", "pos",
                (System.currentTimeMillis() - sTime));

        Map<String, Object> result = new HashMap<>();
        result.put("recharge", recharge);
        String message = GetBeanUtil.getBean(SsVipRechargeServiceImpl.class).rechargeWithoutCheck(request, recharge,
                records,
                vchcode);
        result.put("message", message);
        return result;
    }

    @Override
    public void submitRechargeBill(VipRechargeDTO request) {
        try {
            receiptBillService.submitRechargeBill(null, request.getVchcode(), request.getRechargeMoney(),
                    request.getOtypeId(),
                    request.getEtypeId(), request.getCreateEtypeId(), request.getBtypeId(), request.getSummary(),
                    request.getMemo(), request.getPayment(), request.getVipId(), BillPostState.UNCONFIRMED);
        } catch (Exception e) {
            logger.error("充值草稿收款单提交失败", e);
            throw new RuntimeException("充值草稿收款单提交失败:" + e.getMessage());
        }
    }

    @Override
    public String recharge(VipRechargeDTO request) {
        BigInteger vchcode = UId.newId();
        if (request.getVchcode() != null) {
            vchcode = request.getVchcode();
        }
        MonitorService monitorService = BeanUtils.getBean(MonitorService.class);
        Map<String, Object> recharegeResult =
                GetBeanUtil.getBean(SsVipRechargeServiceImpl.class).checkBeforeRecharge(request, vchcode);
        String message = (String) recharegeResult.get("message");
        RechargeDTO recharge = (RechargeDTO) recharegeResult.get("recharge");
        if (message == null) {
            message = "";
        }
        try {
            Map<String, Object> goodsBill = request.getGoodsBill();
            if (goodsBill != null) {
                long sTime = System.currentTimeMillis();
                BillSourceTypeEnum sourceTypeEnum = BillSourceTypeEnum.RECHARGE_GIFTS;
                if (recharge.getRechargeType() == 1) {
                    sourceTypeEnum = BillSourceTypeEnum.RECHARGE_EXCHANGE;
                }
                goodsBill.put("sourceType", sourceTypeEnum.toString());
                GoodsBillDTO goodsBillDTO = JsonUtils.toObject(JSONObject.toJSONString(goodsBill),
                        GoodsBillDTO.class);
                PosBill posBill = new PosBill();
                posBill.setSaveVipAsserts(false);
                posBill.setGoodsBill(goodsBillDTO);
                BillSaveResultDTO orderbill = billInterface.submitBill(goodsBillDTO, posBill);
                if (orderbill.getResultType() != BillSaveResultTypeEnum.SUCCESS) {
                    BillSaveExceptionDTO resultDTO = orderbill.getExceptionInfo().get(0);
                    String info = "赠品出库单开单失败" + resultDTO.getMessage();
                    logger.info(info);
                } else {
                    //将收款单和赠品的出库单关联起来
                    BillRelationDao relation = new BillRelationDao();
                    relation.setId(UId.newId());
                    relation.setProfileId(CurrentUser.getProfileId());
                    relation.setSourceVchcode(vchcode);
                    relation.setSourceVchtype(Vchtypes.ReceiptBill);
                    relation.setSourceBusinessType(BillBusinessType.VipValue.getBusinessTypeId());
                    relation.setTargetVchcode(orderbill.getVchcode());
                    Vchtypes vchtypes = orderbill.getVchtype();
                    if (vchtypes == null) {
                        vchtypes = Vchtypes.SaleBill;
                    }
                    relation.setTargetVchtype(vchtypes);
                    relation.setTargetBusinessType(BillBusinessType.SaleNormal.getBusinessTypeId());
                    billMapper.saveBillRelation(relation);
//                    vipAssertsService.insertVipBill(null, request.getVipId().toString(), request.getCashierId()
//                    .toString(),
//                            (String) goodsBill.get("vchtype"), (String) goodsBill.get("vchcode"));
                }
                monitorService.recordTP(MonitorTypeEnum.PL_API_SALE_POS_GET_RECHARGE_GFIT_TIME.getTopic(), "shopType"
                        , "pos",
                        (System.currentTimeMillis() - sTime));
            }
        } catch (Exception e) {
            String info = "赠品出库单开单失败" + e.getMessage();
            logger.info(info);
            message = message + info;
        }
        message = postBill(vchcode, message, "会员充值单据核算失败");
        return message;
    }

    /**
     * 核算单据
     */
    @Override
    public String postBill(BigInteger vchcode, String message, String errorInfo) {
        try {
            goodsBillService.postBill(vchcode);
        } catch (Exception e) {
            String info = errorInfo + "[" + vchcode + "]" + e.getMessage();
            logger.info(info);
            message = message + ";" + info;
            //这里核算失败不阻断流程
//            throw new RuntimeException(message);
        }
        return message;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<RechargeDTO> getRechargeListByVip(GetRechargeByVipDTO request) {
        if (request == null) {
            throw new RuntimeException("未传入任何参数");
        }
        if (request.getVipId() == null) {
            throw new RuntimeException("会员id不能为空");
        }
        if (request.getOtypeId() == null) {
            throw new RuntimeException("门店id不能为空");
        }
        if (request.getVipLevelId() == null) {
            throw new RuntimeException("会员等级id不能为空");
        }
        BigInteger profileId = CurrentUser.getProfileId();
        SsVip vip = vipMapper.selectVipById(request.getVipId(), profileId);
        if (vip == null) {
            throw new RuntimeException("该会员不存在");
        }
        SelectLevelResponseDTO level = levelService.getVipLevelByLevelId(request.getVipLevelId());
        //如果是付费会员且过期，则无法使用充值活动
        if (SVIPUtils.isVipLevelExpired(level.getVipType(), vip.getValidDate())) {
            return new ArrayList<>();
        }
        //先查出满足门店和会员等级的所有活动
        GetRechargeListRequestDTO getRechargeListRequestDTO = new GetRechargeListRequestDTO();
        getRechargeListRequestDTO.setOtypeIds(Collections.singletonList(request.getOtypeId()));
        getRechargeListRequestDTO.setVipLevelIds(Collections.singletonList(request.getVipLevelId()));
        getRechargeListRequestDTO.setStop(0);
        getRechargeListRequestDTO.setEndDate(new Date());
        getRechargeListRequestDTO.setActivityState(1);
        //排除非进行中的活动和脏数据
        List<RechargeDTO> rechargeList =
                GetBeanUtil.getBean(SsVipRechargeServiceImpl.class).getRechargeList(getRechargeListRequestDTO);
        //根据vipId拿到充值记录
        List<SsVipRechargeRecord> rechargeRecords = recordMapper.getRechargeRecordByVipIdAndRechargeIds(profileId,
                request.getVipId(), null);
        rechargeList =
                rechargeList.stream().filter(rechargeDTO -> checkRechargeEnabled(rechargeRecords, rechargeDTO)).collect(Collectors.toList());
        return rechargeList;
    }

    /**
     * 充值退款
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BigInteger rechargeRefund(RechargeRefundDTO request) {
        if (request.getVipId() == null) {
            throw new RuntimeException("会员id不能为空");
        }
        if (request.getRefundMoney() == null) {
            throw new RuntimeException("退款金额不能为空");
        }
        if (request.getRefundMoney().equals(BigDecimal.ZERO)) {
            throw new RuntimeException("退款金额不能为0");
        }

        //这里先把金额取正
        BigDecimal refundMoney = request.getRefundMoney().abs();
        vipService.checkValidPaymentList(request.getPayment());
        //根据会员id获取会员的资产，退款金额不得大于储值本金
        SsVipAsserts asserts = vipMapper.getSsVipAsserts(request.getVipId(), CurrentUser.getProfileId());
        if (asserts == null) {
            throw new RuntimeException("找不到会员资产");
        }
        BigDecimal storeBalance = asserts.getChargeTotal().add(asserts.getGiftTotal());
        BigDecimal chargeTotal = asserts.getChargeTotal();
        if (chargeTotal.compareTo(refundMoney) < 0) {
            throw new RuntimeException("退款金额不得大于充值本金");
        }
        BigInteger vchcode = UId.newId();
        //生成收款单,单据金额为负数
        //处理支付方式，确保金额都为负数
        for (PayMentDTO paymentDTO : request.getPayment()) {
            paymentDTO.setCurrencyAtypeTotal(paymentDTO.getCurrencyAtypeTotal().abs().negate());
        }
        String billNumber = "";
        try {
            billNumber = receiptBillService.submitRechargeBill(null, vchcode, refundMoney.negate(), request.getOtypeId(),
                    request.getEtypeId(), request.getCreateEtypeId(), request.getBtypeId(), request.getSummary(),
                    null, request.getPayment(), request.getVipId(), null);
        } catch (Exception e) {
            throw new RuntimeException("开具收款单失败," + e.getMessage());
        }

        //会员资产变动
        MemberAssertsChange assertsChangeDto = new MemberAssertsChange();
        assertsChangeDto.setVchcode(vchcode);
        assertsChangeDto.setVchtype(com.wsgjp.ct.framework.enums.Vchtypes.codeOf(Vchtypes.ReceiptBill.getCode()));
        assertsChangeDto.setBillNumber(billNumber);
        List<VipAsserts> vipAssertsList = new ArrayList<>();
        VipAsserts vipAssert = new VipAsserts();
        vipAssert.setVipId(request.getVipId());
        List<MemberAssert> vipAsserts = new ArrayList<>();
        //退款金额
        vipAsserts.add(MemberAssert.createData(1, refundMoney.negate(), AssertsChangeType.STORED_VALUE_REFUND));
        //赠送金额
        BigDecimal giftRefund = null;
        if (asserts.getGiftTotal().compareTo(BigDecimal.ZERO) > 0) {
            //等比计算赠金
            giftRefund = asserts.getGiftTotal().multiply(refundMoney.divide(asserts.getChargeTotal(), 2,
                    RoundingMode.HALF_UP)).setScale(2, RoundingMode.HALF_UP);
            if (giftRefund.compareTo(asserts.getGiftTotal()) > 0) {
                giftRefund = asserts.getGiftTotal();
            }
            vipAsserts.add(MemberAssert.createData(2, giftRefund.negate(), AssertsChangeType.STORED_VALUE_REFUND));
        }
        vipAssert.setVipAssert(vipAsserts);
        vipAssertsList.add(vipAssert);
        assertsChangeDto.setVipAsserts(vipAssertsList);
        assertsChangeDto.setMemo(request.getSummary());
        assertsChangeDto.setSourceOperation(AssertsSourceOperation.RECHARGE_REFUND);
        // 充值退款只有PC后台有
        assertsChangeDto.setOperationSource(AssertsSourceType.PC);
        memberAssertsChangeService.vipAssertsChange(assertsChangeDto);

        // 增加充值记录
        VipRechargeDTO dto = new VipRechargeDTO();
        dto.setVipId(request.getVipId());
        dto.setOtypeId(request.getOtypeId());
        storeBalance = storeBalance.add(refundMoney.abs().negate());
        // 退费记录负值
        dto.setRechargeMoney(refundMoney.abs().negate());
        if (giftRefund != null) {
            dto.setGiveMoney(giftRefund.abs().negate());
            storeBalance = storeBalance.add(giftRefund.abs().negate());
        }
        dto.setStoreBalance(storeBalance);
        rechargeRecordListService.insertRechargeRecordList(dto, vchcode, RechargeChargeType.REFUND, null);
        return vchcode;
    }

    /**
     * 校验当前此活动是否可以充值
     *
     * @param rechargeRecords 会员的充值记录
     * @param rechargeDTO     当前活动
     * @return 是否可以充值
     */
    private boolean checkRechargeEnabled(List<SsVipRechargeRecord> rechargeRecords, RechargeDTO rechargeDTO) {
        //排除正常活动
        if (rechargeDTO.getActivityState() != 1 || !checkRecharge(rechargeDTO)) {
            return false;
        }
        //如果限制首充，且有充值记录，则不能再充值
        if (rechargeDTO.getFirstCharge() == 1 && rechargeRecords != null && !rechargeRecords.isEmpty()) {
            return false;
        }
        //无周期，活动不限制充值
        if (rechargeDTO.getCycleType() == 0) {
            return true;
        }
        Calendar calendar = null;
        //间隔类型：0=无 1=天 2=周 3=月 4=年
        Integer spaceType = rechargeDTO.getSpaceType();
        //非首充限制
        String frequency = rechargeDTO.getFrequency();
        //周期为固定日期，判断今天是否是对应的固定日期，如果不是，直接pass
        if (rechargeDTO.getFirstCharge() == 0 && rechargeDTO.getCycleType() == 2) {
            calendar = Calendar.getInstance();
            int day;
            if (spaceType == 2) {
                day = calendar.get(Calendar.DAY_OF_WEEK) - 1;
                //星期天为1，1-0=0
                if (day == 0) {
                    day = 7;
                }
            } else {
                day = calendar.get(Calendar.DAY_OF_MONTH);
            }
            //固定日期时，frequency存的日期以","分割
            List<String> split = Arrays.asList(frequency.split(","));
            if (!split.contains(String.valueOf(day))) {
                return false;
            } else if (rechargeDTO.getCycleSpace() == 1) {
                //如果周期间隔为1，则每个周期内，只要日期符合，都能充值
                return true;
            }
        }
        //没有充值记录，则任何活动都可用
        if (rechargeRecords == null || rechargeRecords.isEmpty()) {
            return true;
        }
        //获取该活动的充值记录
        SsVipRechargeRecord record = null;
        for (SsVipRechargeRecord r : rechargeRecords) {
            if (r.getRechargeId().equals(rechargeDTO.getId())) {
                record = r;
                break;
            }
        }
        //没有充值过，允许充值
        if (record == null) {
            return true;
        }
        //这里经过之前的判断,必定是不限制首充，且若是有周期，且固定日期匹配
        if (calendar == null) {
            calendar = Calendar.getInstance();
        }
        Calendar lastChargeCycleBegin = Calendar.getInstance();
        lastChargeCycleBegin.setTime(record.getCycleFirstDate());
        //和上次充值不在一个活动周期，则允许充值
        if (checkSameCycle(calendar, lastChargeCycleBegin, spaceType, rechargeDTO.getCycleSpace()) == -1) {
            return true;
        }
        if (rechargeDTO.getCycleType() == 2) {//固定日期
            //判断今天和之前是否在一个[自然周期]内,例如，每2周充值2次，则第一周允许充值，第二周不允充值
            //这里cycleSpace传入1,则判断的是[自然周期]
            return checkSameCycle(calendar, lastChargeCycleBegin, spaceType, 1) == 1;
        }
        //次数判断
        try {
            return Integer.parseUnsignedInt(frequency) > record.getCycleTimes();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * [周期] 自然周期，例如，天，周，月，年
     * [大周期] 为充值的重复周期，例如2周2次,则[大周期]为 2*[周期]=2周
     * 进行比较,将上个[大周期]开始时间+[周期]*周期间隔数量，和现在比较，判断和现在时间是否在一个[大周期]内
     *
     * @param now                  现在的时间
     * @param lastChargeCycleBegin 上次周期开始时间
     * @param spaceType            自然周期类型 0=无 1=天 2=周 3=月 4=年
     * @param cycleSpace           周期间隔
     * @return 当为-1，则说明不在一个[大周期]内，若是0，则说明无周期类型，若是1，则说明在一个[大周期]
     */
    private int checkSameCycle(Calendar now, Calendar lastChargeCycleBegin, int spaceType, int cycleSpace) {
        //间隔类型：0=无  1=天 2=周 ;3=月;4=年
        //将天数设置为周期内的第一天,方便比较
        switch (spaceType) {
            case 1:
                spaceType = Calendar.DATE;
                break;
            case 2:
                spaceType = Calendar.WEEK_OF_MONTH;
                now.setFirstDayOfWeek(Calendar.MONDAY);
                now.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                lastChargeCycleBegin.setFirstDayOfWeek(Calendar.MONDAY);
                lastChargeCycleBegin.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                break;
            case 3:
                spaceType = Calendar.MONTH;
                now.set(Calendar.DAY_OF_MONTH, 1);
                lastChargeCycleBegin.set(Calendar.DAY_OF_MONTH, 1);
                break;
            case 4:
                spaceType = Calendar.YEAR;
                now.set(Calendar.DAY_OF_YEAR, 1);
                lastChargeCycleBegin.set(Calendar.DAY_OF_YEAR, 1);
                break;
            default:
                return 0;
        }
        //重置时间为 00:00:00.000,方便比较
        now.set(Calendar.HOUR, 0);
        now.set(Calendar.MINUTE, 0);
        now.set(Calendar.SECOND, 0);
        now.set(Calendar.MILLISECOND, 0);
        lastChargeCycleBegin.set(Calendar.HOUR, 0);
        lastChargeCycleBegin.set(Calendar.MINUTE, 0);
        lastChargeCycleBegin.set(Calendar.SECOND, 0);
        lastChargeCycleBegin.set(Calendar.MILLISECOND, 0);
        //进行比较,将上个大周期开始时间+周期*周期间隔数量，和现在比较
        lastChargeCycleBegin.add(spaceType, cycleSpace);
        if (lastChargeCycleBegin.before(now)) {
            return -1;
        }
        return 1;
    }

    /**
     * 会员充值（除开最后的核算步骤）
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public String rechargeWithoutCheck(VipRechargeDTO request, RechargeDTO recharge,
                                       List<SsVipRechargeRecord> records, BigInteger vchcode) {
        BigInteger profileId = CurrentUser.getProfileId();
        BigInteger vipId = request.getVipId();
        long sTime = System.currentTimeMillis();
        //记录会员充值信息
        if (recharge != null) {
            BigInteger rechargeId = recharge.getId();
            //先查出会员所有充值记录
            SsVipRechargeRecord record = null;
            if (records != null && !records.isEmpty()) {
                record = records.stream().filter(r -> rechargeId.equals(r.getRechargeId())).findFirst().orElse(null);
            }
            if (record == null) {
                record = new SsVipRechargeRecord();
                record.setId(UId.newId());
                record.setProfileId(profileId);
                record.setRechargeId(rechargeId);
                record.setVipId(vipId);
                record.setCycleTimes(1);
                record.setCycleFirstDate(new Date());
                recordMapper.insertRecord(record);
            } else {
                //活动周期类型：0=无周期 1=次数 2=固定日期
                Integer cycleType = recharge.getCycleType();
                //间隔类型：0=无  1=天 2=周 ;3=月;4=年
                Integer spaceType = recharge.getSpaceType();
                //是否重置次数和周期开始时间
                boolean reset = true;
                if (record.getCycleFirstDate() != null && record.getCycleTimes() > 0 && cycleType != 0) {
                    //这里经过之前的判断,必定是不限制首充，且若是有周期，且固定日期匹配
                    Calendar calendar = Calendar.getInstance();
                    Calendar lastChargeCycleBegin = Calendar.getInstance();
                    lastChargeCycleBegin.setTime(record.getCycleFirstDate());
                    if (checkSameCycle(calendar, lastChargeCycleBegin, spaceType, recharge.getCycleSpace()) == 1) {
                        //由于前面已经确认可以充值，又在一个周期内，则只需要增加次数
                        reset = false;
                    }
                }
                if (reset) {
                    //和上次充值不在一个活动周期，则次数和周期开始时间都重置
                    record.setCycleTimes(1);
                    record.setCycleFirstDate(new Date());
                } else {
                    record.setCycleTimes(record.getCycleTimes() + 1);
                }
                recordMapper.updateRecord(record);
            }
        }
        //生成收款单据
        String billNumber = "";
        try {
            billNumber = receiptBillService.submitRechargeBill(null, vchcode, request.getRechargeMoney(), request.getOtypeId(),
                    request.getEtypeId(), request.getCreateEtypeId(), request.getBtypeId(), request.getSummary(),
                    request.getMemo(), request.getPayment(), request.getVipId(), null);
        } catch (Exception e) {
            throw new RuntimeException("开具收款单失败," + e.getMessage());
        }
        //充值记录
        SsVipAsserts asserts = vipMapper.getSsVipAsserts(request.getVipId(), CurrentUser.getProfileId());
        BigDecimal storeBalance = asserts.getChargeTotal().add(asserts.getGiftTotal()).add(request.getRechargeMoney());
        // 退费记录负值
        if (request.getGiveMoney() != null && request.getGiveMoney().compareTo(BigDecimal.ZERO) > 0) {
            storeBalance = storeBalance.add(request.getGiveMoney());
        }
        request.setStoreBalance(storeBalance);
        rechargeRecordListService.insertRechargeRecordList(request, vchcode, RechargeChargeType.RECHARGE, recharge);
        //会员资产变动
        MemberAssertsChange assertsChangeDto = new MemberAssertsChange();
        assertsChangeDto.setVchcode(vchcode);
        assertsChangeDto.setVchtype(com.wsgjp.ct.framework.enums.Vchtypes.codeOf(Vchtypes.ReceiptBill.getCode()));
        assertsChangeDto.setBillNumber(billNumber);
        List<VipAsserts> vipAssertsList = new ArrayList<>();
        VipAsserts vipAssert = new VipAsserts();
        vipAssert.setVipId(vipId);
        List<MemberAssert> vipAsserts = new ArrayList<>();
        //充值金额
        vipAsserts.add(MemberAssert.createData(1, request.getRechargeMoney(), AssertsChangeType.VIP_RECHARGE));
        //积分赠送
        if (request.getScore() != null && request.getScore() > 0) {
            vipAsserts.add(MemberAssert.createData(0, new BigDecimal(request.getScore()), AssertsChangeType.RECHARGE_GIFT));
        }
        //赠送金额
        if (request.getGiveMoney() != null && request.getGiveMoney().compareTo(BigDecimal.ZERO) > 0) {
            vipAsserts.add(MemberAssert.createData(2, request.getGiveMoney(), AssertsChangeType.RECHARGE_GIFT));
        }
        //赠送优惠券
        if (request.getCardId() != null && request.getCardQty() > 0) {
            vipAsserts.add(MemberAssert.createData(4,
                    new BigDecimal(request.getCardQty()),
                    VipAssertsBillDetailDto.getTypedMemo(4, new BigDecimal(request.getCardQty())),
                    null,
                    request.getCardId(),
                    AssertsChangeType.RECHARGE_GIFT));
        }
        // 成长值
        Integer givenGrowth = vipAssertsChangeService.growthCompute(vipId,
                request.getRechargeMoney(), ScoreStrategyType.recharge);
        if (givenGrowth != null && givenGrowth != 0) {
            vipAsserts.add(MemberAssert.createData(3, new BigDecimal(givenGrowth), AssertsChangeType.RECHARGE_GIFT));
        }
        vipAssert.setVipAssert(vipAsserts);
        vipAssertsList.add(vipAssert);
        assertsChangeDto.setVipAsserts(vipAssertsList);
        assertsChangeDto.setMemo(request.getSummary());
        assertsChangeDto.setSourceOperation(AssertsSourceOperation.VIP_RECHARGE);
        // 会员充值只有pos有
        assertsChangeDto.setOperationSource(AssertsSourceType.POS);
        memberAssertsChangeService.vipAssertsChange(assertsChangeDto);
        //关联ss_vip_bill这张表，记录下收款单vchcode和vipId和充值后金额
        insertVipBill(vipId, request.getCashierId(), vchcode, profileId);
        // 会员升级--充值可能送成长值，有可能触发会员升级
        vipLevelService.vipLevelUpdate(vipId);
        MonitorService monitorService = BeanUtils.getBean(MonitorService.class);
        monitorService.recordTP(MonitorTypeEnum.PL_API_SALE_POS_GET_RECHARGE_TIME.getTopic(), "shopType", "pos",
                (System.currentTimeMillis() - sTime));
        return null;
    }

    //记录ss_vip_bill 记录会员id，充值收款单id，和充值后储值余额
    private void insertVipBill(BigInteger vipId, BigInteger cashierId, BigInteger vchcode, BigInteger profileId) {
        if (cashierId == null) {
            cashierId = BigInteger.ZERO;
        }
        SsVipAsserts asserts = vipMapper.getSsVipAsserts(vipId, profileId);
        BigDecimal assertTotal = asserts.getChargeTotal().add(asserts.getGiftTotal());
        SsVipBill ssVipBill = new SsVipBill();
        ssVipBill.setVipId(vipId);
        ssVipBill.setId(UId.newId());
        ssVipBill.setProfileId(profileId);
        ssVipBill.setVchcode(vchcode.toString());
        ssVipBill.setVchtype(Vchtypes.ReceiptBill.getName());
        ssVipBill.setCashierId(cashierId.toString());
        ssVipBill.setAssertTotal(assertTotal);
        vipMapper.insertVipBill(ssVipBill);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateRecharge(RechargeDTO dto) throws Exception {
        notNull(dto);
        RechargeDTO oldRecharge = null;
        // 根据有没有id判断是新增还是修改
        if (dto.getId() != null) {
            checkRechargeName(dto);
            // 获取旧值
            oldRecharge = getRechargeById(dto.getId());
            // 修改活动
            boolean updateRechargeResult = ssVipRechargeMapper.updateRecharge(dto);
            if (!updateRechargeResult) {
                throw new RuntimeException("更新充值活动失败");
            }
            //先删除活动内的全部内容，再重新添加
            //删除绑定的门店
            ssVipRechargeOtypeMapper.deleteRechargeOtypeByRechargeId(dto.getId());
            //删除绑定的会员等级
            ssVipRechargeLevelMapper.deleteRechargeLevelByRechargeId(dto.getId());
            //获取之前的档位id，用来删除绑定档位的详情
            List<BigInteger> gearIds = ssVipRechargeGearMapper.getIdByRechargeId(dto.getId());
            //根据档位id获取之前档位详情的id
            List<BigInteger> gearDetailIds = ssVipRechargeGearDetailMapper.getIdByGearId(gearIds);
            //删除档位
            ssVipRechargeGearMapper.deleteRechargeGearByRechargeId(dto.getId());
            //删除档位详情
            ssVipRechargeGearDetailMapper.deleteRechargeGearDetailById(gearDetailIds);
            //删除档位详情对应的信息
            ssVipRechargeRuleMapper.deleteRechargeRuleByGearDetailId(gearDetailIds);
        } else {
            //新增
            dto.setId(UId.newId());
            dto.setProfileId(CurrentUser.getProfileId());
            checkRechargeName(dto);
            boolean insertRecharge = ssVipRechargeMapper.insertRecharge(dto);
            if (!insertRecharge) {
                throw new RuntimeException("新增充值活动失败");
            }
        }
        saveRechargeLog(dto, oldRecharge);
        insertRechargeGearsAndRules(dto);

    }

    private void saveRechargeLog(RechargeDTO dto, RechargeDTO oldRecharge) throws Exception {
        BaseInfoLog baseInfoLog = new BaseInfoLog();
        baseInfoLog.setObjectId(dto.getId());
        if (oldRecharge != null) {
            // 活动结束时间特殊处理
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(oldRecharge.getEndDate());
            calendar.add(Calendar.HOUR_OF_DAY, 23);
            calendar.add(Calendar.MINUTE, 59);
            calendar.add(Calendar.SECOND, 59);
            oldRecharge.setEndDate(calendar.getTime());
            // List排序
            listSort(oldRecharge);
            listSort(dto);
            List<ComparisonResult> comparisonResults = CompareUtils.compareFields(oldRecharge, dto, RechargeDTO.class);
            StringBuilder body = new StringBuilder();
            for (ComparisonResult comparisonResult : comparisonResults) {
                buildDifference(body, comparisonResult, dto);
            }

            if (!Objects.equals(oldRecharge.getCycleType(), dto.getCycleType())) {
                // 活动周期变了
                String oldRepetitionPeriod = buildRepetitionPeriod(oldRecharge);
                String newRepetitionPeriod = buildRepetitionPeriod(dto);
                body.append(String.format("活动周期：由【%s】修改为【%s】，重复周期由【%s】修改为【%s】",
                        oldRecharge.getCycleTypeStr(oldRecharge.getCycleType().toString()),
                        dto.getCycleTypeStr(dto.getCycleType().toString()), oldRepetitionPeriod, newRepetitionPeriod));
            } else if (!Objects.equals(oldRecharge.getCycleSpace(), dto.getCycleSpace()) || !Objects.equals(oldRecharge.getSpaceType(), dto.getSpaceType()) || !Objects.equals(oldRecharge.getFrequency(), dto.getFrequency())) {
                // 活动周期没变，重复周期变了
                String oldRepetitionPeriod = buildRepetitionPeriod(oldRecharge);
                String newRepetitionPeriod = buildRepetitionPeriod(dto);
                body.append(String.format("重复周期：由【%s】修改为【%s】", oldRepetitionPeriod, newRepetitionPeriod));
            }
            if (StringUtils.isEmpty(body)) {
                return;
            }
            baseInfoLog.setBody(body.toString());
        } else {
            baseInfoLog.setBody("新增充值策略");
        }
        saveLog(baseInfoLog);
    }

    private String buildRepetitionPeriod(RechargeDTO dto) {
        String repetitionPeriod = "";
        if (dto.getCycleType() == 0) {
            repetitionPeriod = "无";
        }
        if (dto.getCycleType() == 1) {
            repetitionPeriod = String.format("每%s%s重复%s次", dto.getCycleSpace(),
                    dto.getSpaceTypeStr(dto.getSpaceType().toString()), dto.getFrequency());
        }
        if (dto.getCycleType() == 2) {
            String frequency = "";
            if (dto.getSpaceType() == 2) {
                frequency = dto.getFrequencyWeekStr(dto.getFrequency());
            }
            if (dto.getSpaceType() == 3) {
                frequency = dto.getFrequency().replace(",", "、") + "日";
            }
            repetitionPeriod = String.format("每%s%s%s重复", dto.getCycleSpace(),
                    dto.getSpaceTypeStr(dto.getSpaceType().toString()), frequency);
        }
        return repetitionPeriod;
    }

    private void buildDifference(StringBuilder body, ComparisonResult comparisonResult, RechargeDTO dto) {
        String before;
        String after;
        if ("vipLevelIds".equals(comparisonResult.getKey())) {
            // 适用会员
            List<String> previous = levelService.getLevelNameList(JsonUtils.toList(comparisonResult.getPrevious(),
                    BigInteger.class), CurrentUser.getProfileId());
            before = StringUtils.join(previous, ",");
            List<String> later = levelService.getLevelNameList(JsonUtils.toList(comparisonResult.getLater(),
                    BigInteger.class), CurrentUser.getProfileId());
            after = StringUtils.join(later, ",");
        } else if ("otypeIds".equals(comparisonResult.getKey())) {
            List<String> previous = baseInfoService.getOtypeNameList(JsonUtils.toList(comparisonResult.getPrevious(),
                    BigInteger.class), CurrentUser.getProfileId());
            before = StringUtils.join(previous, ",");
            List<String> later = baseInfoService.getOtypeNameList(JsonUtils.toList(comparisonResult.getLater(),
                    BigInteger.class), CurrentUser.getProfileId());
            after = StringUtils.join(later, ",");
        } else if ("startDate".equals(comparisonResult.getKey()) || "endDate".equals(comparisonResult.getKey())) {
            before = DateUtils.formatDate(DateUtils.parse(comparisonResult.getPrevious()), "yyyy-MM-dd");
            after = DateUtils.formatDate(DateUtils.parse(comparisonResult.getLater()), "yyyy-MM-dd");
        } else if ("firstCharge".equals(comparisonResult.getKey())) {
            before = "0".equals(comparisonResult.getPrevious()) ? "否" : "是";
            after = "0".equals(comparisonResult.getLater()) ? "否" : "是";
        } else {
            before = comparisonResult.getPrevious();
            after = comparisonResult.getLater();
        }
        if ("gears".equals(comparisonResult.getKey())) {
            // 充值规则
            body.append("变更了充值规则").append("\n");
        } else {
            body.append(String.format("%s：由【%s】修改为【%s】", comparisonResult.getLabel(), StringUtils.isBlank(before) ?
                            "无" : before,
                    StringUtils.isBlank(after) ? "无" : after)).append("\n");
        }
    }

    public void saveLog(BaseInfoLog baseInfoLog) {
        baseInfoLog.setProfileId(CurrentUser.getProfileId());
        baseInfoLog.setEtypeId(CurrentUser.getEmployeeId());
        baseInfoLog.setId(UId.newId());
        Etype etype = shopSaleBaseInfoService.getEtypeById();
        baseInfoLog.setEfullname(etype.getFullname());
        baseInfoLog.setLogTime(DateUtils.getDate());
        baseInfoLog.setIp(CurrentUser.getClientIp());
        baseInfoLog.setObjectType("vipRecharge");
        LogService.add(baseInfoLog);
    }

    private void notNull(RechargeDTO dto) {
        if (dto.getStartDate() == null) {
            throw new RuntimeException("活动开始时间不能为空");
        }
        if (dto.getEndDate() == null) {
            throw new RuntimeException("活动结束时间不能为空");
        }
        if (dto.getVipLevelIds() == null || dto.getVipLevelIds().isEmpty()) {
            throw new RuntimeException("适用会员不能为空");
        }
        if (dto.getOtypeIds() == null || dto.getOtypeIds().isEmpty()) {
            throw new RuntimeException("适用门店不能为空");
        }
        if (dto.getGears() == null || dto.getGears().isEmpty()) {
            throw new RuntimeException("充值档位不能为空");
        }
    }

    private void checkRechargeName(RechargeDTO dto) throws Exception {
        ///检查名称是否重复
        boolean isExist = recordMapper.checkRechargeName(CurrentUser.getProfileId(), dto.getRechargeName(),
                dto.getId());
        if (isExist) {
            throw new RuntimeException("充值活动名称重复");
        }
    }

    private void listSort(RechargeDTO dto) {
        List<BigInteger> vipLevelIds = dto.getVipLevelIds().stream().sorted(BigInteger::compareTo)
                .collect(Collectors.toList());
        dto.setVipLevelIds(vipLevelIds);
        List<BigInteger> otypeIds = dto.getOtypeIds().stream().sorted(BigInteger::compareTo)
                .collect(Collectors.toList());
        dto.setOtypeIds(otypeIds);
    }

    /**
     * 添加档位以及档位下的福利
     */
    private void insertRechargeGearsAndRules(RechargeDTO dto) {
        //todo 数据校验？
        BigInteger profileId = CurrentUser.getProfileId();
        // 充值活动适用会员表
        List<SsVipRechargeLevel> ssVipRechargeLevels = new ArrayList<>();
        for (BigInteger vipLevelId : dto.getVipLevelIds()) {
            SsVipRechargeLevel ssVipRechargeLevel = new SsVipRechargeLevel();
            ssVipRechargeLevel.setId(UId.newId());
            ssVipRechargeLevel.setProfileId(profileId);
            ssVipRechargeLevel.setRechargeId(dto.getId());
            ssVipRechargeLevel.setVipLevelId(vipLevelId);
            ssVipRechargeLevels.add(ssVipRechargeLevel);
        }
        boolean insertRechargeLevel = ssVipRechargeLevelMapper.insertRechargeLevel(ssVipRechargeLevels);
        if (!insertRechargeLevel) {
            throw new RuntimeException("新增充值活动适用会员失败");
        }
        // 充值活动适用门店表
        List<SsVipRechargeOtype> ssVipRechargeOtypes = new ArrayList<>();
        for (BigInteger otypeId : dto.getOtypeIds()) {
            SsVipRechargeOtype ssVipRechargeOtype = new SsVipRechargeOtype();
            ssVipRechargeOtype.setId(UId.newId());
            ssVipRechargeOtype.setProfileId(profileId);
            ssVipRechargeOtype.setRechargeId(dto.getId());
            ssVipRechargeOtype.setOtypeId(otypeId);
            ssVipRechargeOtypes.add(ssVipRechargeOtype);
        }
        boolean insertRechargeOtype = ssVipRechargeOtypeMapper.insertRechargeOtype(ssVipRechargeOtypes);
        if (!insertRechargeOtype) {
            throw new RuntimeException("新增充值活动适用门店失败");
        }
        // 充值活动档位表
        List<RechargeGearDTO> ssVipRechargeGears = dto.getGears();
        // 活动档位详情
        List<SsVipRechargeGearDetail> ssVipRechargeGearDetails = new ArrayList<>();
        // 活动档位详情关联具体赠品信息
        List<SsVipRechargeRule> ssVipRechargeRules = new ArrayList<>();
        for (int i = 0; i < ssVipRechargeGears.size(); i++) {
            RechargeGearDTO gear = ssVipRechargeGears.get(i);
            BigInteger rechargeGearId = UId.newId();
            gear.setId(rechargeGearId);
            gear.setProfileId(profileId);
            gear.setRechargeId(dto.getId());
            gear.setRowIndex(i);
            //每个档位下的福利(detail和rule)
            List<SsVipRechargeRule> rules = new ArrayList<>();
            List<RechargeGearDetailDTO> details = gear.getDetails();
            for (int j = 0; j < details.size(); j++) {
                RechargeGearDetailDTO detail = details.get(j);
                BigInteger detailId = UId.newId();
                detail.setId(detailId);
                detail.setProfileId(profileId);
                detail.setRechargeGearId(rechargeGearId);
                detail.setRowIndex(j);
                if (detail.getDetailType() == null) {
                    if (dto.getRechargeType() == 1) {
                        ///换购活动 固定写死 POS端需要
                        detail.setDetailType(5);
                    } else {
                        detail.setDetailType(0);
                    }
                }
                if (detail.getGiveMode() == null) {
                    if (dto.getRechargeType() == 1) {
                        ///换购活动 固定写死 POS端需要
                        detail.setGiveMode(1);
                    } else {
                        detail.setGiveMode(0);
                    }
                }

                //rule和detail是一对多
                List<RechargeRuleDTO> ruleList = detail.getRules();
                for (RechargeRuleDTO rule : ruleList) {
                    if (rule.getGiveType() == null) {
                        rule.setGiveType(0);
                    }
                    if (rule.getGiveType() == 0) {
                        rule.setExchangePrice(BigDecimal.valueOf(0));
                        rule.setExchangeDiscount(BigDecimal.valueOf(0));
                    }
                    rule.setId(UId.newId());
                    rule.setProfileId(profileId);
                    rule.setRechargeGearDetailId(detailId);
                    rules.add(rule);
                }

            }
            if (details.isEmpty()) {
                throw new RuntimeException("请完善充值规则信息");
            }
            //批量处理
            ssVipRechargeGearDetails.addAll(details);
            ssVipRechargeRules.addAll(rules);
        }
        boolean insertRechargeGear = ssVipRechargeGearMapper.insertRechargeGear(new ArrayList<>(ssVipRechargeGears));
        if (!insertRechargeGear) {
            throw new RuntimeException("新增充值活动档位失败");
        }
        boolean insertRechargeGearDetail =
                ssVipRechargeGearDetailMapper.insertRechargeGearDetail(ssVipRechargeGearDetails);
        if (!insertRechargeGearDetail) {
            throw new RuntimeException("新增充值活动档位福利失败");
        }
        boolean insertRechargeRule = ssVipRechargeRuleMapper.insertRechargeRule(ssVipRechargeRules);
        if (!insertRechargeRule) {
            throw new RuntimeException("新增充值活动档位福利失败");
        }
    }

    /**
     * 校验充值活动数据是否合规
     *
     * @param recharge 充值活动
     */
    private boolean checkRecharge(SsVipRecharge recharge) {
        Integer cycleType = recharge.getCycleType();
        Integer spaceType = recharge.getSpaceType();
        String frequency = recharge.getFrequency();
        Integer cycleSpace = recharge.getCycleSpace();
        if (recharge.getFirstCharge() == 1) {
            if (cycleType != 0) {
                return false;
            }
        } else if (recharge.getFirstCharge() != 0) {
            return false;
        }
        if (cycleType < 0 || cycleType > 2) {
            return false;
        }
        if (cycleType != 0) {
            if (StringUtils.isEmpty(frequency)) {
                return false;
            }
            if (cycleSpace <= 0) {
                return false;
            }
            if (cycleType == 2) {
                if (spaceType != 2 && spaceType != 3) {
                    return false;
                }
            } else if (spaceType <= 0 || spaceType > 4) {
                return false;
            }
        }
        if (recharge instanceof RechargeDTO) {
            RechargeDTO rechargeDTO = (RechargeDTO) recharge;
            return rechargeDTO.getGears() != null && !rechargeDTO.getGears().isEmpty();
        }
        return true;
    }

    /**
     * 充值作废
     *
     * @return
     */
    @Override
    public String invalidateRechargeRecord(InvalidateRechargeRequestDTO request) {
        BigInteger newVchcode = UId.newId();
        BigInteger vchcode =
                GetBeanUtil.getBean(SsVipRechargeServiceImpl.class).invalidateRechargeWithoutGiftBill(request,
                        newVchcode);
        String message = postBill(newVchcode, "", "会员储值退款单据核算失败");
        //将赠品单据做逻辑删除
        try {
            deleteGiftGoodsOrder(vchcode);
        } catch (Exception e) {
            message = message + ";" + e.getMessage();
        }
        return message;

    }

    @Override
    public void batchRecharge(VipBatchRechargeDTO request) {
        BigInteger vchcode = UId.newId();
        GetBeanUtil.getBean(SsVipRechargeServiceImpl.class).batchRechargeWithOutPost(vchcode, request);
        postBill(vchcode, "", "");
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void batchRechargeWithOutPost(BigInteger vchcode, VipBatchRechargeDTO request) {
        // 数据校验
        if (request.getVipIds() == null || request.getVipIds().isEmpty()) {
            throw new RuntimeException("会员id不能为空");
        }
        if (request.getRechargeMoney() == null || request.getRechargeMoney().compareTo(BigDecimal.ZERO) < 0) {
            throw new RuntimeException("充值金额必须大于0");
        }
        if (request.getPayment() == null || request.getPayment().isEmpty()) {
            throw new RuntimeException("付款方式不能为空");
        }
        // 验证支付方式的合理性（因前期进销存的bug导致部分非预存款的支付方式存在现金银行为空的数据，避免影响单据数据异常，该部分操作需中断单据提交）
        vipService.checkValidPaymentList(request.getPayment());
        // 排除掉已过期的付费会员
        List<BigInteger> expiredVip = vipMapper.getExpiredVip(CurrentUser.getProfileId(), request.getVipIds());
        request.getVipIds().removeIf(expiredVip::contains);
        if (request.getVipIds().isEmpty()) {
            throw new RuntimeException("没有可充值的会员");
        }
        // 生成收款单据
        String billNumber = "";
        try {
            String summary = String.format("批量充值%s个会员，每个会员充值%s元", request.getVipIds().size(),
                    request.getRechargeMoney());
            BigDecimal billTotal = request.getRechargeMoney().multiply(new BigDecimal(request.getVipIds().size()));
            BigInteger etypeId = request.getEtypeId() == null ? CurrentUser.getEmployeeId() : request.getEtypeId();
            BigInteger createEtypeId = request.getCreateEtypeId() == null ? CurrentUser.getEmployeeId() : request.getCreateEtypeId();
            billNumber = receiptBillService.submitRechargeBill(null, vchcode, billTotal, request.getOtypeId(),
                    etypeId, createEtypeId, request.getBtypeId(), summary,
                    request.getMemo(), request.getPayment(), BigInteger.ZERO, null);
        } catch (Exception e) {
            throw new RuntimeException("开具收款单失败," + e.getMessage());
        }

        MemberAssertsChange memberAssertsChange = new MemberAssertsChange();
        memberAssertsChange.setVchcode(vchcode);
        memberAssertsChange.setVchtype(com.wsgjp.ct.framework.enums.Vchtypes.codeOf(Vchtypes.ReceiptBill.getCode()));
        memberAssertsChange.setBillNumber(billNumber);
        memberAssertsChange.setMemo(AssertsSourceOperation.BATCH_RECHARGE.getName());
        memberAssertsChange.setSourceOperation(AssertsSourceOperation.BATCH_RECHARGE);
        memberAssertsChange.setSaveVipAsserts(true);
        memberAssertsChange.setOperationSource(AssertsSourceType.PC);
        List<VipAsserts> vipAsserts = new ArrayList<>();
        // 构建数据
        Map<BigInteger, SsVipAsserts> memberAssertsMap = getMemberAssertsMap(request.getVipIds());
        List<SsVipBill> vipBills = new ArrayList<>();
        List<VipRechargeDTO> rechargeDTOS = new ArrayList<>();
        for (BigInteger vipId : request.getVipIds()) {
            // 批量处理会员资产时，要一个会员构建一个资产变动
            buildBatchRechargeData(vipId, vchcode, request.getOtypeId(), request.getRechargeMoney(), request.getGiveMoney(), memberAssertsMap.get(vipId), vipAsserts, vipBills, rechargeDTOS);
        }
        memberAssertsChange.setVipAsserts(vipAsserts);
        memberAssertsChangeService.vipAssertsChange(memberAssertsChange);

        // 充值记录
        rechargeRecordListService.insertRechargeRecordList(rechargeDTOS, vchcode, RechargeChargeType.BATCH_RECHARGE);
        //关联ss_vip_bill这张表，记录下收款单vchcode和vipId和充值后金额
        vipMapper.insertVipBillList(vipBills);
    }

    private void buildBatchRechargeData(BigInteger vipId, BigInteger vchcode, BigInteger otypeId, BigDecimal rechargeMoney, BigDecimal giveMoney, SsVipAsserts ssVipAsserts,
                                        List<VipAsserts> vipAsserts, List<SsVipBill> vipBills, List<VipRechargeDTO> rechargeDTOS) {

        VipAsserts vipAssert = new VipAsserts();
        vipAssert.setVipId(vipId);
        List<MemberAssert> memberAssertList = new ArrayList<>();
        memberAssertList.add(MemberAssert.createData(1, rechargeMoney, null, null, null, AssertsChangeType.VIP_RECHARGE));
        if (giveMoney != null && giveMoney.compareTo(BigDecimal.ZERO) > 0) {
            memberAssertList.add(MemberAssert.createData(2, giveMoney, null, null, null, AssertsChangeType.RECHARGE_GIFT));
            ssVipAsserts.setGiftTotal(ssVipAsserts.getGiftTotal().add(giveMoney));
        }
        vipAssert.setVipAssert(memberAssertList);
        vipAsserts.add(vipAssert);
        ssVipAsserts.setChargeTotal(ssVipAsserts.getChargeTotal().add(rechargeMoney));


        // 充值记录
        VipRechargeDTO rechargeDTO = new VipRechargeDTO();
        rechargeDTO.setVipId(vipId);
        rechargeDTO.setOtypeId(otypeId);
        rechargeDTO.setRechargeMoney(rechargeMoney);
        rechargeDTO.setGiveMoney(giveMoney);
        // vipAsserts 中的本金和赠金已经加过新充值的了，这里直接用就好
        BigDecimal storeBalance = ssVipAsserts.getChargeTotal().add(ssVipAsserts.getGiftTotal());
        rechargeDTO.setStoreBalance(storeBalance);
        rechargeDTOS.add(rechargeDTO);

        SsVipBill ssVipBill = new SsVipBill();
        ssVipBill.setVipId(vipId);
        ssVipBill.setId(UId.newId());
        ssVipBill.setProfileId(CurrentUser.getProfileId());
        ssVipBill.setVchcode(vchcode.toString());
        ssVipBill.setVchtype(Vchtypes.ReceiptBill.getName());
        ssVipBill.setCashierId("0");
        ssVipBill.setAssertTotal(ssVipAsserts.getChargeTotal().add(ssVipAsserts.getGiftTotal()));
        vipBills.add(ssVipBill);
    }

    @Transactional(rollbackFor = Exception.class)
    public BigInteger invalidateRechargeWithoutGiftBill(InvalidateRechargeRequestDTO request, BigInteger newVchcode) {
        if (!PermissionValiateService.validate(PermissionShopSale.RECHARGE_RECORD_INVALIDATE)) {
            throw new RuntimeException("您没有储值作废的权限,请联系管理员开通");
        }
        BigInteger recordId = request.getRecordId();
        BigInteger otypeId = request.getOtypeId();
        if (recordId == null) {
            throw new RuntimeException("充值记录id不能为空");
        }
        if (otypeId == null) {
            throw new RuntimeException("门店id不能为空");
        }
        BigInteger profileId = CurrentUser.getProfileId();
        SsVipRechargeRecordList log = recordMapper.getRechargeRecordDetail(recordId, profileId);
        if (log == null || log.getDeleted() == 1) {
            throw new RuntimeException("该充值记录不存在");
        }
        if (log.getChargeType() == RechargeChargeType.INVALID) {
            throw new RuntimeException("该充值已经作废");
        }
        //校验是否可以作废，查询从充值到现在是否有为负的储值变动（充值作废除外）
        PageRequest<BigInteger> r = new PageRequest<>();
        BigInteger vipId = log.getVipId();
        BigInteger vchcode = log.getVchcode();
        r.setQueryParams(vipId);

        //todo 新增储值赠金校验
        //根据会员id获取会员的资产
        SsVipAsserts asserts = vipMapper.getSsVipAsserts(vipId, CurrentUser.getProfileId());
        List<MemberAssert> vipAsserts = new ArrayList<>();
        //查询充值金额和赠金
        List<VipAssertChange> billAssertChanges = recordMapper.getRechargeRecordDetailAssertChange(vchcode, profileId);
        //判断当前储值本金和赠金是否足够
        //将资产退回，增加操作来源sourceType=13，储值作废
        BigDecimal refundQty = BigDecimal.ZERO;
        for (VipAssertChange assertChange : billAssertChanges) {
            BigDecimal qty = assertChange.getQty().setScale(2, RoundingMode.HALF_UP);
            switch (assertChange.getTyped()) {
                case 0:
                    //积分减少
                    vipAsserts.add(MemberAssert.createData(0, qty.negate(), AssertsChangeType.RECHARGE_INVALIDATE));
                    break;
                case 1:
                    //本金减少
                    refundQty = refundQty.add(qty);
                    if (qty.compareTo(asserts.getChargeTotal()) > 0)
                        throw new RuntimeException("当前储值余额中本金不足，无法作废");
                    vipAsserts.add(MemberAssert.createData(1, qty.negate(), AssertsChangeType.RECHARGE_INVALIDATE));
                    break;
                case 2:
                    //赠金减少
                    refundQty = refundQty.add(qty);
                    if (qty.compareTo(asserts.getGiftTotal()) > 0)
                        throw new RuntimeException("当前储值余额中赠金不足，无法作废");
                    vipAsserts.add(MemberAssert.createData(2, qty.negate(), AssertsChangeType.RECHARGE_INVALIDATE));
                    break;
                case 3:
                    //成长值减少
                    vipAsserts.add(MemberAssert.createData(3, qty.negate(), AssertsChangeType.RECHARGE_INVALIDATE));
                    break;
                case 4:
                    //卡券，储值目前只送优惠券
                    if (assertChange.getCardType() == 2 || assertChange.getCardType() == 3 || assertChange.getCardType() == 4) {
                        if (qty.compareTo(BigDecimal.ZERO) > 0) {
                            vipAsserts.add(MemberAssert.createData(4,
                                    qty.negate(),
                                    VipAssertsBillDetailDto.getTypedMemo(4, qty),
                                    assertChange.getAssertId(),
                                    null,
                                    AssertsChangeType.RECHARGE_INVALIDATE));
                        }
                    }
                    break;
                default:
                    //5为服务商品，目前充值不送
                    break;
            }
        }

        //已作废，不再使用充值后是否开单来判断是否可以作废
//        PageInfo<VipAssertChange> assertChanges = vipService.selectVipAssertChangeByVipId(r);
//        if (assertChanges.getList().stream()
//                .anyMatch(assertChange -> assertChange.getTyped() == 1
//                        && !SourceOperationType.rechargeInvalidate.getValue().equals(assertChange
//                        .getSourceOperation())
//                        && assertChange.getQty().compareTo(BigDecimal.ZERO) < 0
//                        && assertChange.getCreateTime().after(log.getCreateTime()))) {
//            throw new RuntimeException("该会员储值已经消费，无法作废");
//        }
        //查询收款单，充值和充值作废操作的门店必须一致
        Map<String, Object> receiptBillInfo = recordMapper.getReceiptBillInfo(vchcode);
        if (receiptBillInfo == null) {
            throw new RuntimeException("未找到对应的收款单");
        }
        Object billOtypeId = receiptBillInfo.get("otype_id");
        if (billOtypeId == null || !otypeId.equals(new BigInteger(billOtypeId.toString()))) {
            throw new RuntimeException("充值和作废的门店不一致");
        }
        //查询三方流水号,三方流水号退款
        BigInteger newPayOutNo = UId.newId();
        //淘谷谷支付方式
        PayMentDTO tggPayment = null;
        //现金支付方式
        PayMentDTO cashPayment = null;
        List<PayMentDTO> atypeInfo = recordMapper.getAtypeInfo(vchcode, profileId);
        //新的负金额收款单中的支付方式，原路退
        List<PayMentDTO> paymentList = new ArrayList<>();
        for (PayMentDTO payMentDTO : atypeInfo) {
            PayMentDTO payment = new PayMentDTO();
            payment.setAtypeId(payMentDTO.getAtypeId());
            payment.setPaywayId(payMentDTO.getPaywayId());
            payment.setCurrencyAtypeTotal(payMentDTO.getCurrencyAtypeTotal().negate());
            paymentList.add(payment);
            //找出之前的流水号
            if (tggPayment == null && payMentDTO.getOutNo() != null && !payMentDTO.getOutNo().isEmpty()) {
                tggPayment = payMentDTO;
                payment.setOutNo(newPayOutNo.toString());
                if (payMentDTO.getPayOrderNo() != null && !payMentDTO.getPayOrderNo().isEmpty()) {
                    payment.setPayOrderNo(payMentDTO.getPayOrderNo());
                }
            }
            //找出现金支付方式，记录钱箱
            if (cashPayment == null && payMentDTO.getPaywayType() == 0) {
                cashPayment = payMentDTO;
            }
        }
        //生成金额为负数的收款单 这个收款单的支付方式为会员储值
        Object billTotal = receiptBillInfo.get("bill_total");
        if (billTotal == null) {
            throw new RuntimeException("单据金额为空");
        }
        if (tggPayment != null) {
            AggregatePaymentRequest aggregatePaymentRequest = getAggregatePaymentRequest(tggPayment, newPayOutNo);
            try {
                aggregatePaymentRequest.setVchcode(vchcode);
                aggregatePaymentRequest.setBillTotal(new BigDecimal(billTotal.toString()));
                PayResultInfo resultInfo = billService.payRefundNew(aggregatePaymentRequest);
                if (resultInfo.getStatus() == PayStatusEnum.PENDING) {
                    ///需轮询
                    PayOrderQueryRequest queryRequest = new PayOrderQueryRequest();
                    queryRequest.setVchcode(vchcode);
                    queryRequest.setPaywayId(tggPayment.getPaywayId());
                    queryRequest.setQueryType(PaymentQueryTypeEnum.REFUND);
                    queryRequest.setOutNo(newPayOutNo.toString());
                    try {
                        PayResultInfo payResultInfo = billService.queryPayStatusNew(queryRequest);
                        if (payResultInfo.getStatus() != PayStatusEnum.SUCCEEDED) {
                            throw new RuntimeException("退款失败");
                        }
                    } catch (Exception e) {
                        throw new RuntimeException("退款失败：" + e.getMessage());
                    }
                } else if (resultInfo.getStatus() != PayStatusEnum.SUCCEEDED) {
                    throw new RuntimeException("退款失败");
                }
            } catch (Exception e) {
                throw new RuntimeException("退款失败：" + e.getMessage());
            }
        }
        String billNumber = "";
        try {
            billNumber = receiptBillService.submitRechargeBill(vchcode, newVchcode, new BigDecimal(billTotal.toString()).negate(), otypeId,
                    request.getEtypeId(), request.getCreateEtypeId(), request.getBtypeId(), "储值记录作废", null,
                    paymentList, vipId, null);
        } catch (Exception e) {
            throw new RuntimeException("开具退款收款单失败," + e.getMessage());
        }

        //会员资产变动
        if (asserts == null) {
            throw new RuntimeException("找不到会员资产");
        }
        MemberAssertsChange assertsChangeDto = new MemberAssertsChange();
        assertsChangeDto.setVchcode(newVchcode);
        assertsChangeDto.setVchtype(com.wsgjp.ct.framework.enums.Vchtypes.codeOf(Vchtypes.ReceiptBill.getCode()));
        assertsChangeDto.setBillNumber(billNumber);
        List<VipAsserts> vipAssertsList = new ArrayList<>();
        VipAsserts vipAssert = new VipAsserts();
        vipAssert.setVipId(vipId);
        vipAssert.setVipAssert(vipAsserts);
        vipAssertsList.add(vipAssert);
        assertsChangeDto.setVipAsserts(vipAssertsList);
        assertsChangeDto.setMemo("储值作废");
        assertsChangeDto.setSourceOperation(AssertsSourceOperation.RECHARGE_INVALIDATE);
        // 储值作废只有POS有
        assertsChangeDto.setOperationSource(AssertsSourceType.POS);
        // 充值会送积分，作废会扣积分，作废不存在赠送积分的场景
        memberAssertsChangeService.vipAssertsChange(assertsChangeDto);
        //钱箱记录
        if (cashPayment != null && cashPayment.getCurrencyAtypeTotal() != null && !BigDecimal.ZERO.equals(cashPayment.getCurrencyAtypeTotal())) {
            CashBoxPaymentDTO cashBoxPaymentDTO = new CashBoxPaymentDTO();
            cashBoxPaymentDTO.setEtypeId(request.getCreateEtypeId());
            cashBoxPaymentDTO.setOtypeId(request.getOtypeId());
            cashBoxPaymentDTO.setCashierId(request.getCashierId());
            cashBoxPaymentDTO.setPaymentType(BigInteger.valueOf(3));
            cashBoxPaymentDTO.setAmount(cashPayment.getCurrencyAtypeTotal());
            cashBoxPaymentDTO.setId(UId.newId());
            try {
                cashBoxService.insertPayment(cashBoxPaymentDTO);
            } catch (Exception e) {
                throw new RuntimeException(String.format("新增钱箱记录失败:" + e.getMessage()));
            }
        }

        // 充值记录标记作废
        rechargeRecordListService.updateRechargeListInvalid(recordId, asserts.getTotal().subtract(refundQty));
        // 充值作废后有可能会有成长值的变动，需要重新计算等级
        levelService.vipLevelUpdate(vipId);
        return vchcode;
    }

    @NotNull
    private static AggregatePaymentRequest getAggregatePaymentRequest(PayMentDTO tggPayment, BigInteger newPayOutNo) {
        AggregatePaymentRequest aggregatePaymentRequest = new AggregatePaymentRequest();
        aggregatePaymentRequest.setTradeAmount(tggPayment.getCurrencyAtypeTotal());
        aggregatePaymentRequest.setOutNo(tggPayment.getOutNo());
        aggregatePaymentRequest.setOrderNo(tggPayment.getPayOrderNo());
        aggregatePaymentRequest.setPaywayId(tggPayment.getPaywayId());
        aggregatePaymentRequest.setRefundOutNo(newPayOutNo.toString());
        aggregatePaymentRequest.setGoodsTitle("会员储值退款");
        aggregatePaymentRequest.setGoodsDesc("会员储值退款");
        return aggregatePaymentRequest;
    }

    /**
     * 删除充值赠品出库单
     */
    private void deleteGiftGoodsOrder(BigInteger vchcode) throws Exception {
        //查询赠品信息
        BillRelationDao relationDao = billMapper.searchBillRelationBySourceVchcode(CurrentUser.getProfileId(), vchcode);
        if (relationDao != null && relationDao.getTargetVchcode() != null && !BigInteger.ZERO.equals(relationDao.getTargetVchcode())) {
            BigInteger targetVchcode = relationDao.getTargetVchcode();
            billService.logicalDeleteBill(targetVchcode, "充值作废删除单据", AssertsSourceType.POS);
        }
    }

    /**
     * 会员储值聚合支付
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PayResultInfo rechargePayOrderAndSaveBill(VipRechargePaymentRequest request) {
        long sTime = System.currentTimeMillis();
        MonitorService monitorService = BeanUtils.getBean(MonitorService.class);

        VipRechargeDTO rechargeInfo = request.getRechargeInfo();
        AggregatePaymentRequest payRequestDto = request.getPayInfo();
        payRequestDto.setServerName("sale");
        payRequestDto.setNotifyUrl("/sale/shopsale/bill/payResultCallback");

        // 关闭轮询
        payRequestDto.setEnablePollingQuery(false);

        PayResultInfo resultInfo = new PayResultInfo();
        BigInteger vchcode = null;
        String billNumber = null;

        try {
            // 1. 完整的参数验证（与原有checkBeforeRecharge一致）
            if (rechargeInfo.getVipId() == null) {
                throw new RuntimeException("会员id不能为空");
            }
            if (rechargeInfo.getRechargeMoney() == null || rechargeInfo.getRechargeMoney().compareTo(BigDecimal.ZERO) < 0) {
                throw new RuntimeException("充值金额必须大于0");
            }
            if (rechargeInfo.getPayment() == null || rechargeInfo.getPayment().isEmpty()) {
                throw new RuntimeException("付款方式不能为空");
            }

            // 验证支付方式
            vipService.checkValidPaymentList(rechargeInfo.getPayment());

            // 商品，积分，卡券只有充值活动才会送
            if (rechargeInfo.getCardId() != null || (rechargeInfo.getScore() != null && rechargeInfo.getScore() > 0) || rechargeInfo.getGoodsBill() != null) {
                if (rechargeInfo.getRechargeId() == null) {
                    throw new RuntimeException("充值活动id不能为空");
                }
            }

            // 充值活动验证
            if (rechargeInfo.getRechargeId() != null) {
                boolean check = false;
                GetRechargeListRequestDTO params = new GetRechargeListRequestDTO(rechargeInfo.getRechargeId());
                params.setStop(0);
                params.setActivityState(1);
                List<RechargeDTO> rechargeList = getRechargeList(params);
                if (rechargeList != null && !rechargeList.isEmpty()) {
                    RechargeDTO recharge = rechargeList.get(0);
                    if (recharge.getStoped() == 0
                            && recharge.getActivityState() == 1
                            && recharge.getOtypeIds().contains(rechargeInfo.getOtypeId())
                            && recharge.getVipLevelIds().contains(rechargeInfo.getVipLevelId())) {
                        List<SsVipRechargeRecord> records = recordMapper.getRechargeRecordByVipIdAndRechargeIds(CurrentUser.getProfileId(),
                                rechargeInfo.getVipId(), null);
                        if (checkRechargeEnabled(records, recharge)) {
                            check = true;
                        }
                    }
                }
                if (!check) {
                    throw new RuntimeException("当前充值活动无法使用");
                }
            }

            // 2. 先生成草稿收款单据（不核算）
            vchcode = UId.newId();
            if (rechargeInfo.getVchcode() != null) {
                vchcode = rechargeInfo.getVchcode();
            }
            rechargeInfo.setVchcode(vchcode);

            // 生成收款单据（草稿状态）
            billNumber = receiptBillService.submitRechargeBill(null, vchcode, rechargeInfo.getRechargeMoney(),
                    rechargeInfo.getOtypeId(), rechargeInfo.getEtypeId(), rechargeInfo.getCreateEtypeId(),
                    rechargeInfo.getBtypeId(), rechargeInfo.getSummary(), rechargeInfo.getMemo(),
                    rechargeInfo.getPayment(), rechargeInfo.getVipId(), BillPostState.PROCESS_COMPLETED);

            // 设置支付请求参数
            payRequestDto.setVchcode(vchcode);
            payRequestDto.setBillTotal(rechargeInfo.getRechargeMoney());
            payRequestDto.setOutNo(vchcode.toString());

            if (payRequestDto.getGoodsDesc() == null || payRequestDto.getGoodsDesc().isEmpty()) {
                payRequestDto.setGoodsDesc("会员储值");
            }
            if (payRequestDto.getGoodsTitle() == null || payRequestDto.getGoodsTitle().isEmpty()) {
                payRequestDto.setGoodsTitle("会员储值");
            }

            // 2. 调用聚合支付
            PaymentResponse<PaymentInfo> response = aggregatePaymentService.payOrder(payRequestDto);
            resultInfo.setMessage(response.getMessage());
            if (response.getData() != null) {
                org.springframework.beans.BeanUtils.copyProperties(response.getData(), resultInfo);
            }

            // 订单状态处理，把淘淘谷的等待输入密码换成等待
            if (resultInfo.getStatus() == PayStatusEnum.WAITING_PASSWORD) {
                resultInfo.setStatus(PayStatusEnum.PENDING);
            }

            // 记录日志
            taotaoguLogService.saveLog(vchcode, payRequestDto.getOutNo(), "rechargePayOrderAndSaveBill",
                    JsonUtils.toJson(request), JsonUtils.toJson(resultInfo), false, "0", "会员储值聚合支付请求结果");

            // 3. 如果支付成功，立即处理资产变动和核算
            if (resultInfo.getStatus() == PayStatusEnum.SUCCEEDED) {
                processRechargeSuccess(rechargeInfo, vchcode, billNumber);
                // 核算单据
                String message = postBill(vchcode, "", "会员储值聚合支付单据核算失败");
                if (!message.isEmpty()) {
                    logger.warn("会员储值聚合支付单据核算警告: {}", message);
                }
            }

            // 设置单据信息到结果中
            BillSaveResultDTO billSaveResult = new BillSaveResultDTO();
            billSaveResult.setVchcode(vchcode);
            billSaveResult.setBillNumber(billNumber);
            billSaveResult.setResultType(BillSaveResultTypeEnum.SUCCESS);
            resultInfo.setResultDTO(billSaveResult);

            monitorService.recordTP(MonitorTypeEnum.PL_API_SALE_POS_PAY_TP_TIME.getTopic(), "shopType", "pos",
                    (System.currentTimeMillis() - sTime));

            return resultInfo;

        } catch (Exception e) {
            logger.error("会员储值聚合支付失败，profileId: {}, error: {}", CurrentUser.getProfileId(), e.getMessage());
            if (vchcode != null) {
                taotaoguLogService.saveLog(vchcode, payRequestDto.getOutNo(), "rechargePayOrderAndSaveBill",
                        JsonUtils.toJson(request), "", true, "-3", "会员储值聚合支付失败: " + e.getMessage());
            }

            resultInfo.setStatus(PayStatusEnum.CANCELLED);
            resultInfo.setMessage("储值支付失败: " + e.getMessage());

            // 设置错误的单据信息
            if (vchcode != null) {
                BillSaveResultDTO billSaveResult = new BillSaveResultDTO();
                billSaveResult.setVchcode(vchcode);
                billSaveResult.setBillNumber(billNumber);
                billSaveResult.setResultType(BillSaveResultTypeEnum.ERROR);
                billSaveResult.setMessage(e.getMessage());
                resultInfo.setResultDTO(billSaveResult);
            }

            return resultInfo;
        }
    }

    /**
     * 处理储值支付成功后的资产变动
     * 完整复制原有recharge方法的所有逻辑，不遗漏任何验证和处理
     */
    private void processRechargeSuccess(VipRechargeDTO request, BigInteger vchcode, String billNumber) {
        try {
            // 1. 完整的参数验证（与原有checkBeforeRecharge一致）
            if (request.getVipId() == null) {
                throw new RuntimeException("会员id不能为空");
            }
            if (request.getRechargeMoney() == null || request.getRechargeMoney().compareTo(BigDecimal.ZERO) < 0) {
                throw new RuntimeException("充值金额必须大于0");
            }
            if (request.getPayment() == null || request.getPayment().isEmpty()) {
                throw new RuntimeException("付款方式不能为空");
            }

            // 验证支付方式
            vipService.checkValidPaymentList(request.getPayment());

            // 2. 充值活动验证和处理
            RechargeDTO recharge = null;
            List<SsVipRechargeRecord> records = null;
            BigInteger rechargeId = request.getRechargeId();

            // 商品，积分，卡券只有充值活动才会送
            if (request.getCardId() != null || (request.getScore() != null && request.getScore() > 0) || request.getGoodsBill() != null) {
                if (rechargeId == null) {
                    throw new RuntimeException("充值活动id不能为空");
                }
            }

            if (rechargeId != null) {
                boolean check = false;
                GetRechargeListRequestDTO params = new GetRechargeListRequestDTO(rechargeId);
                params.setStop(0);
                params.setActivityState(1);
                List<RechargeDTO> rechargeList = getRechargeList(params);
                if (rechargeList != null && !rechargeList.isEmpty()) {
                    recharge = rechargeList.get(0);
                    if (recharge.getStoped() == 0
                            && recharge.getActivityState() == 1
                            && recharge.getOtypeIds().contains(request.getOtypeId())
                            && recharge.getVipLevelIds().contains(request.getVipLevelId())) {
                        records = recordMapper.getRechargeRecordByVipIdAndRechargeIds(CurrentUser.getProfileId(),
                                request.getVipId(), null);
                        if (checkRechargeEnabled(records, recharge)) {
                            check = true;
                        }
                    }
                }
                if (!check) {
                    throw new RuntimeException("当前充值活动无法使用");
                }
            }

            // 3. 执行完整的rechargeWithoutCheck逻辑
            executeRechargeWithoutCheckLogic(request, recharge, records, vchcode, billNumber);

            // 4. 处理充值活动记录
            processRechargeActivityRecord(request, vchcode);

        } catch (Exception e) {
            logger.error("处理储值支付成功后的资产变动失败: {}", e.getMessage());
            throw new RuntimeException("储值资产变动处理失败: " + e.getMessage());
        }
    }

    /**
     * 执行rechargeWithoutCheck的完整逻辑
     * 完整复制原有rechargeWithoutCheck方法的所有逻辑
     */
    private void executeRechargeWithoutCheckLogic(VipRechargeDTO request, RechargeDTO recharge,
                                                  List<SsVipRechargeRecord> records, BigInteger vchcode, String billNumber) {
        BigInteger profileId = CurrentUser.getProfileId();
        BigInteger vipId = request.getVipId();

        // 充值活动记录处理已移至processRechargeActivityRecord方法中统一处理

        // 充值记录
        SsVipAsserts asserts = vipMapper.getSsVipAsserts(request.getVipId(), CurrentUser.getProfileId());
        BigDecimal storeBalance = asserts.getChargeTotal().add(asserts.getGiftTotal()).add(request.getRechargeMoney());
        // 退费记录负值
        if (request.getGiveMoney() != null && request.getGiveMoney().compareTo(BigDecimal.ZERO) > 0) {
            storeBalance = storeBalance.add(request.getGiveMoney());
        }
        request.setStoreBalance(storeBalance);
        rechargeRecordListService.insertRechargeRecordList(request, vchcode, RechargeChargeType.RECHARGE, recharge);

        // 会员资产变动
        MemberAssertsChange assertsChangeDto = new MemberAssertsChange();
        assertsChangeDto.setVchcode(vchcode);
        assertsChangeDto.setVchtype(com.wsgjp.ct.framework.enums.Vchtypes.ReceiptBill);
        assertsChangeDto.setMemo(request.getSummary());
        assertsChangeDto.setBillNumber(billNumber);
        assertsChangeDto.setSourceOperation(AssertsSourceOperation.VIP_RECHARGE);
        assertsChangeDto.setSaveVipAsserts(true);

        List<VipAsserts> vipAsserts = new ArrayList<>();
        VipAsserts vipAssertsDto = new VipAsserts();
        vipAssertsDto.setVipId(vipId);
        vipAssertsDto.setMoney(request.getRechargeMoney());

        List<MemberAssert> memberAsserts = new ArrayList<>();
        // 充值金额
        memberAsserts.add(MemberAssert.createData(1, request.getRechargeMoney(),
                VipAssertsBillDetailDto.getTypedMemo(1, request.getRechargeMoney()), null, null, AssertsChangeType.VIP_RECHARGE));
        // 积分赠送
        if (request.getScore() != null && request.getScore() > 0) {
            memberAsserts.add(MemberAssert.createData(0, new BigDecimal(request.getScore()),
                    VipAssertsBillDetailDto.getTypedMemo(0, new BigDecimal(request.getScore())), null, null, AssertsChangeType.RECHARGE_GIFT));
        }
        // 赠送金额
        if (request.getGiveMoney() != null && request.getGiveMoney().compareTo(BigDecimal.ZERO) > 0) {
            memberAsserts.add(MemberAssert.createData(2, request.getGiveMoney(),
                    VipAssertsBillDetailDto.getTypedMemo(2, request.getGiveMoney()), null, null, AssertsChangeType.RECHARGE_GIFT));
        }
        // 赠送优惠券
        if (request.getCardId() != null && request.getCardQty() > 0) {
            memberAsserts.add(MemberAssert.createData(4,
                    new BigDecimal(request.getCardQty()),
                    VipAssertsBillDetailDto.getTypedMemo(4, new BigDecimal(request.getCardQty())),
                    null,
                    request.getCardId(),
                    AssertsChangeType.RECHARGE_GIFT));
        }
        // 成长值
        Integer givenGrowth = vipAssertsChangeService.growthCompute(vipId,
                request.getRechargeMoney(), ScoreStrategyType.recharge);
        if (givenGrowth != null && givenGrowth != 0) {
            memberAsserts.add(MemberAssert.createData(3, new BigDecimal(givenGrowth),
                    VipAssertsBillDetailDto.getTypedMemo(3, new BigDecimal(givenGrowth)), null, null, AssertsChangeType.RECHARGE_GIFT));
        }

        vipAssertsDto.setVipAssert(memberAsserts);
        vipAsserts.add(vipAssertsDto);
        assertsChangeDto.setVipAsserts(vipAsserts);
        assertsChangeDto.setOperationSource(AssertsSourceType.POS);
        assertsChangeDto.setSourceOperation(AssertsSourceOperation.VIP_RECHARGE);

        memberAssertsChangeService.vipAssertsChange(assertsChangeDto);

        // 注意：新的memberAssertsChangeService.vipAssertsChange方法返回void，不返回错误信息
        // 如果需要错误处理，需要通过其他方式获取

        // 关联ss_vip_bill这张表，记录下收款单vchcode和vipId和充值后金额
        insertVipBill(vipId, request.getCashierId(), vchcode, profileId);

        // 处理赠品出库单逻辑（使用独立事务，失败不影响主事务）
        processRechargeGiftsInSeparateTransaction(request, vchcode);

        // 会员升级--充值可能送成长值，有可能触发会员升级
        vipLevelService.vipLevelUpdate(vipId);
    }

    /**
     * 处理储值支付确认的资产变动
     * 简化版本，只包含核心的资产变动逻辑，不包含验证
     */
    private void processRechargeConfirm(VipRechargeDTO request, BigInteger vchcode, String billNumber) {
        try {
            // 直接执行核心的资产变动逻辑，跳过验证
            RechargeDTO recharge = null;
            List<SsVipRechargeRecord> records = null;

            // 如果有充值活动，获取活动信息
            if (request.getRechargeId() != null) {
                GetRechargeListRequestDTO params = new GetRechargeListRequestDTO(request.getRechargeId());
                params.setStop(0);
                params.setActivityState(1);
                List<RechargeDTO> rechargeList = getRechargeList(params);
                if (rechargeList != null && !rechargeList.isEmpty()) {
                    recharge = rechargeList.get(0);
                    records = recordMapper.getRechargeRecordByVipIdAndRechargeIds(CurrentUser.getProfileId(),
                            request.getVipId(), null);
                }
            }

            // 执行核心的资产变动逻辑
            executeRechargeWithoutCheckLogic(request, recharge, records, vchcode, billNumber);

            // 处理充值活动记录
            processRechargeActivityRecord(request, vchcode);

        } catch (Exception e) {
            logger.error("处理储值支付确认资产变动失败: {}", e.getMessage());
            throw new RuntimeException("储值确认资产变动处理失败: " + e.getMessage());
        }
    }

    /**
     * 处理充值活动记录
     */
    private void processRechargeActivityRecord(VipRechargeDTO rechargeInfo, BigInteger vchcode) {
        try {
            if (rechargeInfo.getRechargeId() == null) {
                return; // 没有充值活动，直接返回
            }

            BigInteger profileId = CurrentUser.getProfileId();
            BigInteger vipId = rechargeInfo.getVipId();
            BigInteger rechargeId = rechargeInfo.getRechargeId();

            RechargeDTO recharge = getRechargeById(rechargeId);
            if (recharge == null) {
                return;
            }

            // 查询会员的充值记录
            List<SsVipRechargeRecord> records = recordMapper.getRechargeRecordByVipIdAndRechargeIds(profileId, vipId, null);
            SsVipRechargeRecord record = null;
            if (records != null && !records.isEmpty()) {
                record = records.stream().filter(r -> rechargeId.equals(r.getRechargeId())).findFirst().orElse(null);
            }

            if (record == null) {
                // 新建充值记录
                record = new SsVipRechargeRecord();
                record.setId(UId.newId());
                record.setProfileId(profileId);
                record.setRechargeId(rechargeId);
                record.setVipId(vipId);
                record.setCycleTimes(1);
                record.setCycleFirstDate(new Date());
                recordMapper.insertRecord(record);
            } else {
                // 更新充值记录
                Integer cycleType = recharge.getCycleType();
                Integer spaceType = recharge.getSpaceType();
                boolean reset = true;

                if (record.getCycleFirstDate() != null && record.getCycleTimes() > 0 && cycleType != 0) {
                    Calendar calendar = Calendar.getInstance();
                    Calendar lastChargeCycleBegin = Calendar.getInstance();
                    lastChargeCycleBegin.setTime(record.getCycleFirstDate());
                    if (checkSameCycle(calendar, lastChargeCycleBegin, spaceType, recharge.getCycleSpace()) == 1) {
                        reset = false;
                    }
                }

                if (reset) {
                    record.setCycleTimes(1);
                    record.setCycleFirstDate(new Date());
                } else {
                    record.setCycleTimes(record.getCycleTimes() + 1);
                }
                recordMapper.updateRecord(record);
            }
        } catch (Exception e) {
            logger.warn("处理充值活动记录失败: {}", e.getMessage());
            // 充值活动记录失败不影响主流程
        }
    }

    /**
     * 会员储值支付确认处理
     * 用于POS端轮询或手动确认收款时调用，处理资产变动和单据核算
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirmRechargePayment(VipRechargePaymentRequest request) {
        try {
            VipRechargeDTO rechargeInfo = request.getRechargeInfo();
            BigInteger vchcode = rechargeInfo.getVchcode();
            // 1. 验证参数
            if (vchcode == null) {
                throw new RuntimeException("单据号不能为空");
            }

            logger.info("开始处理会员储值支付确认，vchcode: {}", vchcode);

            // 2. 处理储值资产变动（使用与聚合支付相同的逻辑）
            String billNumber = getBillNumberByVchcode(vchcode);
            processRechargeConfirm(rechargeInfo, vchcode, billNumber);

            // 3. 核算单据
            String message = postBill(vchcode, "支付确认成功", "会员储值支付确认单据核算失败");
            if (!message.isEmpty()) {
                logger.warn("会员储值支付确认单据核算警告: {}", message);
            }

            logger.info("会员储值支付确认处理完成，vchcode: {}", vchcode);

        } catch (Exception e) {
            logger.error("处理会员储值支付确认失败: {}", e.getMessage());
            throw new RuntimeException("储值支付确认处理失败: " + e.getMessage());
        }
    }


    /**
     * 在独立事务中处理储值赠品出库单，失败不影响主事务
     */
    private void processRechargeGiftsInSeparateTransaction(VipRechargeDTO rechargeInfo, BigInteger vchcode) {
        try {
            // 使用GetBeanUtil获取自身代理对象，以便使用独立事务
            SsVipRechargeServiceImpl self = GetBeanUtil.getBean(SsVipRechargeServiceImpl.class);
            self.processRechargeGiftsWithNewTransaction(rechargeInfo, vchcode);
        } catch (Exception e) {
            logger.error("独立事务处理赠品出库单失败，vchcode: {}, error: {}", vchcode, e.getMessage(), e);

            // 检查是否是配置错误，记录详细信息
            if (e.getMessage() != null && e.getMessage().contains("找不到skuId")) {
                logger.error("赠品配置错误：充值活动中配置的skuId在数据库中不存在，请检查充值活动配置，vchcode: {}", vchcode);
            }

            // 赠品处理失败不影响主流程，在独立事务中处理避免了污染主事务
        }
    }

    /**
     * 使用新事务处理储值赠品出库单
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void processRechargeGiftsWithNewTransaction(VipRechargeDTO rechargeInfo, BigInteger vchcode) {
        logger.info("开始在新事务中处理储值赠品出库单，vchcode: {}", vchcode);

        try {
            processRechargeGifts(rechargeInfo, vchcode);
            logger.info("新事务中赠品处理成功，vchcode: {}", vchcode);
        } catch (Exception e) {
            logger.error("新事务中赠品处理失败，vchcode: {}, error: {}", vchcode, e.getMessage(), e);
            // 在新事务中，如果出现异常，让这个事务回滚，但不影响主事务
            throw e;
        }
    }

    /**
     * 处理储值赠品出库单的核心逻辑
     */
    private void processRechargeGifts(VipRechargeDTO rechargeInfo, BigInteger vchcode) {
        Map<String, Object> goodsBill = rechargeInfo.getGoodsBill();
        if (goodsBill == null) {
            logger.info("无赠品需要处理，vchcode: {}", vchcode);
            return;
        }

        logger.info("开始处理储值赠品出库单，vchcode: {}, goodsBill: {}", vchcode, JsonUtils.toJson(goodsBill));

        long sTime = System.currentTimeMillis();
        MonitorService monitorService = com.wsgjp.ct.sale.platform.utils.BeanUtils.getBean(MonitorService.class);

        try {
            // 获取充值活动信息来确定赠品类型
            RechargeDTO recharge = null;
            if (rechargeInfo.getRechargeId() != null) {
                recharge = getRechargeById(rechargeInfo.getRechargeId());
                logger.info("获取到充值活动信息，rechargeId: {}, rechargeType: {}",
                        rechargeInfo.getRechargeId(), recharge != null ? recharge.getRechargeType() : "null");
            }

            BillSourceTypeEnum sourceTypeEnum = BillSourceTypeEnum.RECHARGE_GIFTS;
            if (recharge != null && recharge.getRechargeType() == 1) {
                sourceTypeEnum = BillSourceTypeEnum.RECHARGE_EXCHANGE;
            }
            goodsBill.put("sourceType", sourceTypeEnum.toString());

            GoodsBillDTO goodsBillDTO = JsonUtils.toObject(JSONObject.toJSONString(goodsBill), GoodsBillDTO.class);
            PosBill posBill = new PosBill();
            posBill.setSaveVipAsserts(false);
            posBill.setGoodsBill(goodsBillDTO);

            logger.info("准备调用billInterface.submitBill，vchcode: {}", vchcode);

            BillSaveResultDTO orderbill = billInterface.submitBill(goodsBillDTO, posBill);

            if (orderbill.getResultType() != BillSaveResultTypeEnum.SUCCESS) {
                BillSaveExceptionDTO resultDTO = orderbill.getExceptionInfo().get(0);
                String errorMsg = "赠品出库单开单失败: " + resultDTO.getMessage();
                logger.error("赠品出库单开单失败，vchcode: {}, error: {}", vchcode, errorMsg);

                // 在独立事务中，赠品处理失败只回滚当前事务，不影响主事务
                throw new RuntimeException(errorMsg);
            } else {
                logger.info("赠品出库单开单成功，vchcode: {}, targetVchcode: {}", vchcode, orderbill.getVchcode());

                // 将收款单和赠品的出库单关联起来
                BillRelationDao relation = new BillRelationDao();
                relation.setId(UId.newId());
                relation.setProfileId(CurrentUser.getProfileId());
                relation.setSourceVchcode(vchcode);
                relation.setSourceVchtype(Vchtypes.ReceiptBill);
                relation.setSourceBusinessType(BillBusinessType.VipValue.getBusinessTypeId());
                relation.setTargetVchcode(orderbill.getVchcode());
                Vchtypes vchtypes = orderbill.getVchtype();
                if (vchtypes == null) {
                    vchtypes = Vchtypes.SaleBill;
                }
                relation.setTargetVchtype(vchtypes);
                relation.setTargetBusinessType(BillBusinessType.SaleNormal.getBusinessTypeId());
                billMapper.saveBillRelation(relation);

                logger.info("赠品出库单关联关系保存成功，vchcode: {}", vchcode);
            }

            monitorService.recordTP(MonitorTypeEnum.PL_API_SALE_POS_GET_RECHARGE_GFIT_TIME.getTopic(), "shopType", "pos",
                    (System.currentTimeMillis() - sTime));

            logger.info("储值赠品出库单处理成功，vchcode: {}", vchcode);

        } catch (Exception e) {
            String errorMsg = "赠品出库单处理失败: " + e.getMessage();
            logger.error("赠品出库单处理异常，vchcode: {}, error: {}", vchcode, errorMsg, e);
            // 在独立事务中，赠品处理失败只回滚当前事务，不影响主事务
            throw new RuntimeException(errorMsg, e);
        }
    }


    private Map<BigInteger, SsVipAsserts> getMemberAssertsMap(List<BigInteger> vipIds) {
        List<SsVipAsserts> ssVipAsserts = vipMapper.getSsVipAssertsList(vipIds, CurrentUser.getProfileId());
        // 将ssVipAsserts转换成vipId为key,单个SsMemberAsserts为value的Map
        // 创建临时映射存储查询结果
        Map<BigInteger, SsVipAsserts> resultMap = new HashMap<>();

        // 处理查询结果：过滤空值并处理重复键
        Optional.ofNullable(ssVipAsserts)
                .orElseGet(Collections::emptyList)
                .stream()
                .filter(Objects::nonNull)
                .forEach(asserts ->
                        resultMap.put(asserts.getVipId(), asserts)
                );

        // 确保所有vipId都有对应条目（缺失的创建空数据）
        for (BigInteger vipId : vipIds) {
            resultMap.computeIfAbsent(vipId, k -> SsVipAsserts.createNullData(vipId));
        }

        return resultMap;
    }

    /**
     * 根据vchcode获取单据编号
     *
     * @param vchcode 单据编号
     * @return 单据编号字符串，如果未找到则返回vchcode的字符串形式
     */
    private String getBillNumberByVchcode(BigInteger vchcode) {
        try {
            BigInteger profileId = CurrentUser.getProfileId();
            String billNumber = tdBillCoreMapper.getBillNumberByVchcode(vchcode, profileId);
            if (billNumber != null) {
                return billNumber;
            }
            // 如果在td_bill_core中没有找到，可能在acc_bill_core中，但这里先返回vchcode的字符串形式
            logger.warn("未找到vchcode对应的单据编号，使用vchcode作为billNumber: {}", vchcode);
            return vchcode.toString();
        } catch (Exception e) {
            logger.error("查询单据编号失败，vchcode: {}, error: {}", vchcode, e.getMessage());
            return vchcode.toString();
        }
    }


}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.EshopProductMarkMapper">
    <select id="queryProductMarkList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMark">
        select mark.*,bigdata.big_data as productMarkBigData from pl_eshop_product_mark mark
        left join pl_eshop_product_mark_data bigdata on bigdata.profile_id= mark.profile_id and bigdata.mark_id = mark.id
        where mark.profile_id=#{profileId}
        <if test="eshopId!=null ">
            and mark.eshop_id=#{eshopId}
        </if>
        <if test="skuIds!=null and skuIds.size()>0">
            and mark.platform_sku_id in
            <foreach collection="skuIds" open="(" close=")" index="i" separator="," item="skuId">
                #{skuId}
            </foreach>
        </if>
        <if test="numids!=null and numids.size()>0">
            and mark.platform_num_id in
            <foreach collection="numids" open="(" close=")" index="i" separator="," item="numId">
                #{numId}
            </foreach>
        </if>
        <if test="uniqueIds!=null and uniqueIds.size()>0">
            and mark.unique_id in
            <foreach collection="uniqueIds" open="(" close=")" index="i" separator="," item="uniqueId">
                #{uniqueId}
            </foreach>
        </if>
    </select>

    <select id="queryProductMarkByNumidandPropertie"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMark">
        select mark.*,bigdata.big_data as productMarkBigData from pl_eshop_product_mark mark
        left join pl_eshop_product_mark_data bigdata on bigdata.profile_id= mark.profile_id and bigdata.mark_id = mark.id
        left join pl_eshop_product_sku sku on sku.profile_id= mark.profile_id and sku.unique_id = mark.unique_id and sku.eshop_id=mark.eshop_id
        where mark.profile_id=#{profileId} and mark.eshop_id=#{eshopId} and sku.unique_id IS NOT null
        <if test="numid!=null ">
            and mark.platform_num_id = #{numid}
        </if>
        <if test="uniqueId!=null ">
            and mark.unique_id = #{uniqueId}
        </if>
        <if test="platformPropertiesName!=null">
            and mark.platform_properties_name = #{platformPropertiesName}
        </if>
        <if test="uniqueIds!=null and uniqueIds.size()>0">
            and mark.unique_id in
            <foreach collection="uniqueIds" open="(" close=")" index="i" separator="," item="uniqueId">
                #{uniqueId}
            </foreach>
        </if>
    </select>
    <select id="queryProductMarkByNumids"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMark">
        select mark.*,bigdata.big_data as productMarkBigData from pl_eshop_product_mark mark
        left join pl_eshop_product_mark_data bigdata on bigdata.profile_id= mark.profile_id and bigdata.mark_id =
        mark.id
        where mark.profile_id=#{profileId} and mark.eshop_id=#{eshopId}
        <if test="numids!=null and numids.size()>0">
            and mark.platform_num_id in
            <foreach collection="numids" open="(" close=")" index="i" separator="," item="numId">
                #{numId}
            </foreach>
        </if>
    </select>

    <select id="queryByprofiledIdAndEshopId"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMark">
        select * from pl_eshop_product_mark where profile_id=#{profileId} and eshop_id=#{otypeId}
    </select>

    <insert id="save">
        INSERT INTO pl_eshop_product_mark (id, platform_sku_id, eshop_id, profile_id, is_send_by_days, pre_send_by_days,
                                           is_send_on_date, pre_send_on_date, frequency_type, frequency_blank_num,
                                           frequency_blank_cycle, frequency_blank_count, frequency_assign_cycle,
                                           frequency_assign_count, frequency_assign_days, frequency_phase, memo,
                                           create_time, update_time, platform_num_id, is_sale_qty_need_to_occupy,
                                           mark_code, bubble, show_type, platform_properties_name, unique_id)
        VALUES (#{id}, #{platformSkuId}, #{eshopId}, #{profileId}, #{isSendByDays}, #{preSendByDays}, #{isSendOnDate},
                #{preSendOnDate}, #{frequencyType},
                #{frequencyBlankNum}, #{frequencyBlankCycle}, #{frequencyBlankCount}, #{frequencyAssignCycle},
                #{frequencyAssignCount}, #{frequencyAssignDays}, #{frequencyPhase}, #{memo}, #{createTime},
                #{updateTime}, #{platformNumId}, #{isSaleQtyNeedToOccupy}, #{markCode}, #{bubble}, #{showType},
                #{platformPropertiesName}, #{uniqueId});
    </insert>
    <insert id="bulkInsertProductMark">
        INSERT INTO pl_eshop_product_mark (id, platform_sku_id, eshop_id, profile_id, is_send_by_days, pre_send_by_days,
                                           is_send_on_date, pre_send_on_date, frequency_type, frequency_blank_num,
                                           frequency_blank_cycle, frequency_blank_count, frequency_assign_cycle,
                                           frequency_assign_count, frequency_assign_days, frequency_phase, memo,
                                           create_time, update_time, platform_num_id, is_sale_qty_need_to_occupy,
                                           mark_code, bubble, show_type, platform_properties_name, unique_id)
        VALUES
        <foreach item="item" index="index" collection="productMarkList" separator=",">
            (#{item.id}, #{item.platformSkuId}, #{item.eshopId}, #{item.profileId}, #{item.isSendByDays}, #{item.preSendByDays}, #{item.isSendOnDate},
            #{item.preSendOnDate}, #{item.frequencyType},
            #{item.frequencyBlankNum}, #{item.frequencyBlankCycle}, #{item.frequencyBlankCount}, #{item.frequencyAssignCycle},
            #{item.frequencyAssignCount}, #{item.frequencyAssignDays}, #{item.frequencyPhase}, #{item.memo}, #{item.createTime},
            #{item.updateTime}, #{item.platformNumId}, #{item.isSaleQtyNeedToOccupy}, #{item.markCode}, #{item.bubble}, #{item.showType},
            #{item.platformPropertiesName}, #{item.uniqueId})
        </foreach>
            ON DUPLICATE KEY
        UPDATE mark_code  = values(mark_code),
               bubble = values(bubble),
               show_type = values(show_type)
    </insert>

    <delete id="deleteProductMark">
        delete from pl_eshop_product_mark where profile_id=#{profileId} and eshop_id=#{eshopId} and unique_id=#{uniqueId} and mark_code in(1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1017)
    </delete>

    <delete id="deleteProductMarkByMarkCode">
        delete from pl_eshop_product_mark where profile_id=#{profileId} and eshop_id=#{eshopId} and unique_id=#{uniqueId} and mark_code=#{markCode}
    </delete>
    <delete id="deleteProductMarkByEshopId">
        delete from pl_eshop_product_mark where profile_id=#{profileId} and eshop_id=#{eshopId}
    </delete>

    <update id="update">
        UPDATE`pl_eshop_product_mark`
        SET
           platform_sku_id=#{platformSkuId},
           eshop_id=#{eshopId},
           profile_id=#{profileId},
           is_send_by_days=#{isSendByDays},
           pre_send_by_days=#{preSendByDays},
           is_send_on_date= #{isSendOnDate},
           pre_send_on_date=#{preSendOnDate},
           frequency_type=#{frequencyType},
           frequency_blank_num=#{frequencyBlankNum},
           frequency_blank_cycle=#{frequencyBlankCycle},
           frequency_blank_count=#{frequencyBlankCount},
           frequency_assign_cycle=#{frequencyAssignCycle},
           frequency_assign_count=#{frequencyAssignCount},
           frequency_assign_days=#{frequencyAssignDays},
           frequency_phase=#{frequencyPhase},
           memo=#{memo},
           is_sale_qty_need_to_occupy=#{isSaleQtyNeedToOccupy}
        WHERE profile_id=#{profileId} and id = #{id}
    </update>

    <select id="queryProductMarkListByUniqueId" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMark">
        select id, platform_sku_id, eshop_id, profile_id, is_send_by_days, pre_send_by_days, is_send_on_date, pre_send_on_date, frequency_type, frequency_blank_num, frequency_blank_cycle, frequency_blank_count, frequency_assign_cycle, frequency_assign_count, frequency_assign_days, frequency_phase, memo,  platform_num_id, is_sale_qty_need_to_occupy, mark_code, bubble, show_type, platform_properties_name, unique_id
        from pl_eshop_product_mark where profile_id=#{profileId} and eshop_id=#{eshopId} and unique_id=#{uniqueId}
    </select>

    <select id="queryProductMarkListByUniqueIdFormOrder"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMark">
        select id,
               platform_sku_id,
               eshop_id,
               profile_id,
               is_send_by_days,
               pre_send_by_days,
               is_send_on_date,
               pre_send_on_date,
               frequency_type,
               frequency_blank_num,
               frequency_blank_cycle,
               frequency_blank_count,
               frequency_assign_cycle,
               frequency_assign_count,
               frequency_assign_days,
               frequency_phase,
               memo,
               platform_num_id,
               is_sale_qty_need_to_occupy,
               mark_code,
               bubble,
               show_type,
               platform_properties_name,
               unique_id
        from pl_eshop_product_mark
        where profile_id = #{profileId}
          and eshop_id = #{eshopId}
          and unique_id = #{uniqueId}
    </select>

    <select id="queryProductMarkByMarkCode" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMark">
        select id, platform_sku_id, eshop_id, profile_id, is_send_by_days, pre_send_by_days, is_send_on_date, pre_send_on_date, frequency_type, frequency_blank_num, frequency_blank_cycle, frequency_blank_count, frequency_assign_cycle, frequency_assign_count, frequency_assign_days, frequency_phase, memo,  platform_num_id, is_sale_qty_need_to_occupy, mark_code, bubble, show_type, platform_properties_name, unique_id
        from pl_eshop_product_mark
        where profile_id=#{profileId} and eshop_id=#{eshopId} and unique_id=#{uniqueId} and mark_code=#{markCode}
        limit 1
    </select>

    <select id="getProductsMarkByMarkCode" resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMark">
        select id, platform_sku_id, eshop_id, profile_id, is_send_by_days, pre_send_by_days, is_send_on_date, pre_send_on_date, frequency_type, frequency_blank_num, frequency_blank_cycle, frequency_blank_count, frequency_assign_cycle, frequency_assign_count, frequency_assign_days, frequency_phase, memo,  platform_num_id, is_sale_qty_need_to_occupy, mark_code, bubble, show_type, platform_properties_name, unique_id
        from pl_eshop_product_mark
        where profile_id=#{profileId} and mark_code=#{markCode}
        <if test="eshopIds!=null and eshopIds.size()>0">
            and eshop_id in
            <foreach collection="eshopIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="uniqueIds!=null and uniqueIds.size()>0">
            and unique_id in
            <foreach collection="uniqueIds" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="checkProductHasMark" resultType="java.lang.Integer">
        select count(1) from pl_eshop_product_mark
        where profile_id=#{profileId} and eshop_id=#{eshopId} and unique_id=#{uniqueId} and mark_code=#{markCode}
    </select>
    <select id="queryProductMarkListByUniqueIdList"
            resultType="com.wsgjp.ct.sale.biz.eshoporder.entity.product.EshopProductMark">
        select id,
        platform_sku_id,
        eshop_id,
        profile_id,
        is_send_by_days,
        pre_send_by_days,
        is_send_on_date,
        pre_send_on_date,
        frequency_type,
        frequency_blank_num,
        frequency_blank_cycle,
        frequency_blank_count,
        frequency_assign_cycle,
        frequency_assign_count,
        frequency_assign_days,
        frequency_phase,
        memo,
        platform_num_id,
        is_sale_qty_need_to_occupy,
        mark_code,
        bubble,
        show_type,
        platform_properties_name,
        unique_id
        from pl_eshop_product_mark
        where profile_id = #{profileId}
        and unique_id in
        <foreach collection="uniqueIdList" close=")" open="(" separator="," item="uniqueId">
            #{uniqueId}
        </foreach>
    </select>


    <delete id="deleteProductMarkAndMarkData">
        delete pdm,pmd
        from pl_eshop_product_mark pdm
                 left join pl_eshop_product_mark_data pmd on pmd.mark_id = pdm.id and pmd.profile_id = pdm.profile_id
        where pdm.profile_id = #{profileId}
          and pdm.eshop_id = #{eshopId}
          and pdm.unique_id = #{uniqueId}
          and pdm.mark_code in(1000,1001,1002,1003,1004,1005,1006,1007,1008,1009,1010,1017);
    </delete>
    <delete id="cleanProductMarkByUniqueId">
        delete pdm,pmd
        from pl_eshop_product_mark pdm
        left join pl_eshop_product_mark_data pmd on pmd.mark_id = pdm.id and pmd.profile_id = pdm.profile_id
        where pdm.profile_id = #{profileId}
        And pdm.unique_id IN
        <foreach collection="uniqueIdList" close=")" open="(" separator="," item="uniqueId">
            #{uniqueId}
        </foreach>
        AND pdm.mark_code in
        <foreach collection="markCodeList" close=")" open="(" separator="," item="markCode">
            #{markCode}
        </foreach>
    </delete>
</mapper>
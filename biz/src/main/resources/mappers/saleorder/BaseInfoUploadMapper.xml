<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wsgjp.ct.sale.biz.eshoporder.mapper.BaseInfoUploadMapper">
    <insert id="saveBaseInfoUploadRecord">
        insert into pl_eshop_baseinfo_upload_record
        (id, profile_id, eshop_id, create_time, update_time, upload_time, info_type, info_id, upload_Status)
        values (#{id}, #{profileId}, #{eshopId}, #{createTime}, #{updateTime}, #{uploadTime}, #{infoType}, #{infoId},
                #{uploadStatus})
        on duplicate key update upload_time=#{uploadTime},
                                upload_status=#{uploadStatus}
    </insert>
    <insert id="saveBaseInfoUploadRecordBatch">
        insert into pl_eshop_baseinfo_upload_record
        (id,profile_id,eshop_id,create_time,update_time,upload_time,info_type,info_id,upload_Status)
        values
        <foreach collection="records" item="record" separator=",">
            (#{record.id},#{record.profileId},#{record.eshopId},#{record.createTime},#{record.updateTime},#{record.uploadTime},#{record.infoType},#{infoId},#{record.uploadStatus})
        </foreach>
        on duplicate key update upload_time=values(#{uploadTime}）,upload_Status=values(#{uploadStatus})
    </insert>

    <select id="getPtypeListForRelation" resultType="com.wsgjp.ct.sale.biz.shopsale.model.dto.ptype.PtypePo">
    </select>

    <select id="queryWarehouseList" resultType="com.wsgjp.ct.sale.platform.dto.stock.BaseWareHouseInfo">
        select k.fullname as storeName,k.usercode as storeCode,k.id as ktypeId, k.shortname as aliasName, stoped as
        isStopped
        from base_ktype k
        where k.profile_id=#{profileId}
        <if test="modifyDate!=null">
            and k.update_time>=#{modifyDate}
        </if>
    </select>

    <select id="queryKtypeContactor" resultType="com.wsgjp.ct.sale.platform.entity.entities.upload.Contactor">
        select bd.address as detailAddress, district,province,city,bd.street as town, bd.cellphone as tel,bd.people as
        name,
        bd.telephone as mobile,bkd.ktype_id,bd.zipcode
        from base_ktype_deliveryinfo bkd
        left join base_deliveryinfo bd on bkd.profile_id = bd.profile_id and bkd.delivery_id=bd.id
        where bkd.profile_id=#{profileId}
        and bkd.ktype_id in
        <foreach collection="ktypeIds" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
        and bkd.defaulted=1
    </select>

    <select id="queryLogisticsList" resultType="com.wsgjp.ct.sale.platform.entity.entities.upload.LogisticsCompanyInfo">
        select bb.fullname as erpLogisticsCompanyName, bb.usercode as erpLogisticsCompanyCode,bb.stoped as stopped
        from base_btype bb
        left join base_btype_bcategory bbb on bb.profile_id = bbb.profile_id and bb.id=bbb.btype_id
        where bb.profile_id=#{profileId}
        and bbb.bcategory=2
        <if test="modifyDate!=null">
            and bb.update_time>=#{modifyDate}
        </if>
    </select>

    <select id="queryPtypeUpload" resultType="com.wsgjp.ct.sale.platform.entity.entities.upload.PtypeUploadEntity">
        select bps.id as skuId, bpu.id as unitId, bps.ptype_id,
        bp.fullname as ptypeName, bpx.xcode,
        bpf.fullbarcode as barCode, bps.deleted, bps.stoped as stop
        from base_ptype_sku bps
        left join base_ptype_xcode bpx on bpx.profile_id = bps.profile_id and bpx.sku_id=bps.id
        left join base_ptype_fullbarcode bpf on bpf.profile_id=bps.profile_id and bpf.sku_id=bps.id and bpf.defaulted=1
        left join base_ptype bp on bps.profile_id=bp.profile_id and bps.ptype_id=bp.id
        left join base_ptype_unit bpu on bp.profile_id=bpu.profile_id and bp.id=bpu.ptype_id and
        bpu.id=ifnull(bpx.unit_id, ifnull(bpf.unit_id,bpu.id))
        where bps.profile_id=#{profileId}
        and bp.pcategory=0
        <if test="modifyDate!=null">
            and bps.update_time>=#{modifyDate}
        </if>
    </select>

    <select id="queryKtypeRelation" resultType="com.wsgjp.ct.sale.platform.entity.entities.upload.ItemKtypeRelation">
        select ptype_id,ktype_id, bk.usercode as storeCode
        from acc_inigoodsstock_detail acc
        left join base_ktype bk on acc.profile_id=bk.profile_id and acc.ktype_id=bk.id
        where acc.profile_id=#{profileId}
        and acc.ptype_id in
        <foreach collection="ptypeIds" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </select>

    <select id="queryInventoryList" resultType="com.wsgjp.ct.sale.platform.entity.entities.upload.InventoryEntity">
        select acc.ktype_id,acc.ptype_id,acc.sku_id,bpu.id as unitId,
        (bpu.unit_rate * acc.qty) as totalQty,
        (bpu.unit_rate * (acc.qty - ifnull(r.qty,0))) as saleQty
        from acc_inventory_detail acc
        left join stock_record_qty_sale r on acc.profile_id=r.profile_id and acc.sku_id=r.sku_id and acc.ktype_id=r.ktype_id
        left join base_ptype_unit bpu on bpu.profile_id=acc.profile_id and bpu.ptype_id=acc.ptype_id
        where acc.profile_id=#{profileId}
        <if test="modifyDate!=null">
            and acc.update_time>=#{modifyDate}
        </if>
    </select>

    <select id="queryComboList" resultType="com.wsgjp.ct.sale.platform.entity.entities.upload.ComboInfo">
        select fullname as  comboName, usercode as comboCode, id as comboId, deleted, stoped
        from base_ptype
        where profile_id=#{profileId}
        and pcategory=2
        <if test="modifyDate!=null">
            and update_time>=#{modifyDate}
        </if>
    </select>

    <select id="queryComboDetails" resultType="com.wsgjp.ct.sale.platform.entity.entities.upload.ComboDetail">
        select sku_id,unit_id,qty,combo_id
        from base_ptype_combo_detail
        where profile_id=#{profileId}
        and combo_id in
        <foreach collection="comboIds" close=")" open="(" separator="," item="id">
            #{id}
        </foreach>
    </select>

    <select id="queryPtypeRelation" resultType="com.wsgjp.ct.sale.platform.entity.entities.upload.PtypeRelationMapping">
        select peps.platform_num_id,peps.platform_sku_id,map.sku_id,map.unit_id,if(map.pcategory=2, map.ptype_id, 0) as comboId
        from pl_eshop_product_sku peps
        left join pl_eshop_product_sku_mapping map on peps.profile_id=map.profile_id and peps.eshop_id=map.eshop_id and peps.unique_id=map.unique_id
        left join pl_eshop_product_sku_expand exp on peps.profile_id=exp.profile_id and peps.eshop_id=exp.eshop_id and peps.unique_id=exp.unique_id
        where peps.profile_id=#{profileId}
        and peps.eshop_id=#{eshopId}
        and exp.mapping_type=0
        <if test="modifyDate!=null">
            and peps.update_time>=#{modifyDate}
        </if>
        limit #{pageSize} offset #{pageIndex}
    </select>

    <select id="queryPtypeRelationCount" resultType="int">
        select count(1)
        from pl_eshop_product_sku peps
        left join pl_eshop_product_sku_mapping map on peps.profile_id=map.profile_id and peps.eshop_id=map.eshop_id and peps.unique_id=map.unique_id
        left join pl_eshop_product_sku_expand exp on peps.profile_id=exp.profile_id and peps.eshop_id=exp.eshop_id and peps.unique_id=exp.unique_id
        where peps.profile_id=#{profileId}
        and peps.eshop_id=#{eshopId}
        and exp.mapping_type=0
        <if test="modifyDate!=null">
            and peps.update_time>=#{modifyDate}
        </if>
    </select>

    <select id="queryPtypeRelationForComboCount" resultType="int">
 select count(1) from(
        select peps.platform_num_id,peps.platform_sku_id,bpx.sku_id,bpx.unit_id,ifnull(p.id, 0) as comboId
        from pl_eshop_product_sku peps
        left join base_ptype p on peps.profile_id=p.profile_id and p.usercode=peps.platform_xcode and p.pcategory=2
        left join base_ptype_xcode bpx on bpx.profile_id=peps.profile_id and bpx.xcode=peps.platform_xcode
        left join pl_eshop_product_sku_expand exp on peps.profile_id=exp.profile_id and peps.eshop_id=exp.eshop_id and peps.unique_id=exp.unique_id
        where peps.profile_id=#{profileId}
        and peps.eshop_id=#{eshopId}
        and exp.mapping_type=1
        <if test="modifyDate!=null">
            and peps.update_time>=#{modifyDate}
            union
            select peps.platform_num_id,peps.platform_sku_id,bpx.sku_id,bpx.unit_id,ifnull(p.id, 0) as comboId
            from pl_eshop_product_sku peps
            left join base_ptype p on peps.profile_id=p.profile_id and p.usercode=peps.platform_xcode and p.pcategory=2
            left join base_ptype_xcode bpx on bpx.profile_id=peps.profile_id and bpx.xcode=peps.platform_xcode
            left join pl_eshop_product_sku_expand exp on peps.profile_id=exp.profile_id and peps.eshop_id=exp.eshop_id
            and peps.unique_id=exp.unique_id
            where peps.profile_id=#{profileId}
            and peps.eshop_id=#{eshopId}
            and exp.mapping_type=1
            and bpx.update_time>=#{modifyDate}
        </if>)a
    </select>

    <select id="queryPtypeRelationForCombo"
            resultType="com.wsgjp.ct.sale.platform.entity.entities.upload.PtypeRelationMapping">
        select * from (
        select peps.platform_num_id,peps.platform_sku_id,bpx.sku_id,bpx.unit_id,ifnull(p.id, 0) as comboId
        from pl_eshop_product_sku peps
        left join base_ptype p on peps.profile_id=p.profile_id and p.usercode=peps.platform_xcode and p.pcategory=2
        left join base_ptype_xcode bpx on bpx.profile_id=peps.profile_id and bpx.xcode=peps.platform_xcode
        left join pl_eshop_product_sku_expand exp on peps.profile_id=exp.profile_id and peps.eshop_id=exp.eshop_id and
        peps.unique_id=exp.unique_id
        where peps.profile_id=#{profileId}
        and peps.eshop_id=#{eshopId}
        and exp.mapping_type=1
        <if test="modifyDate!=null">
            and peps.update_time>=#{modifyDate}
            union
            select peps.platform_num_id,peps.platform_sku_id,bpx.sku_id,bpx.unit_id,ifnull(p.id, 0) as comboId
            from pl_eshop_product_sku peps
            left join base_ptype p on peps.profile_id=p.profile_id and p.usercode=peps.platform_xcode and p.pcategory=2
            left join base_ptype_xcode bpx on bpx.profile_id=peps.profile_id and bpx.xcode=peps.platform_xcode
            left join pl_eshop_product_sku_expand exp on peps.profile_id=exp.profile_id and peps.eshop_id=exp.eshop_id
            and peps.unique_id=exp.unique_id
            where peps.profile_id=#{profileId}
            and peps.eshop_id=#{eshopId}
            and exp.mapping_type=1
            and bpx.update_time>=#{modifyDate}
        </if>
        ) a
        limit #{pageSize} offset #{pageIndex}
    </select>
</mapper>